// Start of Selection
{
  "makefile.extensionOutputFolder": "./.vscode",
  "go.lintTool": "golangci-lint",
  "go.lintFlags": ["--fast"],
  "go.lintOnSave": "workspace",
  "go.testEnvFile": "${workspaceFolder}/config/local.env",
  "go.coverOnSave": false,
  "go.coverOnSingleTestFile": true,
  "go.coverOnSingleTest": true,
  "go.testFlags": ["-v", "--timeout=3m"],
  "gopls": {
    "formatting.local": "goimports",
    "staticcheck": true,
    "ui.semanticTokens": true,
    "completionDocumentation": true
  },
  "[go]": {
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": "always",
      "source.sortMembers": "always"
    }
  }
}
