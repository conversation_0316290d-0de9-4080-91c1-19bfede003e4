package main

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/sms"
)

// usage:
// copy content.example to content.txt
// copy phone.example to phone.txt
// go run cmd/sms/main.go

func main() {
	content, err := os.ReadFile("cmd/sms/content.txt")
	if err != nil {
		panic(err)
	}
	phoneNumbers, err := os.Open("cmd/sms/phone.txt")
	if err != nil {
		panic(err)
	}
	defer phoneNumbers.Close()

	fileScanner := bufio.NewScanner(phoneNumbers)

	fileScanner.Split(bufio.ScanLines)

	for fileScanner.Scan() {
		phoneNumber := fileScanner.Text()
		kgErr := sms.Send(context.Background(), phoneNumber, string(content))
		if kgErr != nil {
			fmt.Println("Send SMS to", phoneNumber, "failed:", kgErr)
			continue
		}
		fmt.Println("Send SMS to", phoneNumber)
		time.Sleep(500 * time.Millisecond)
	}

}
