package main

// Usage: go run main.go -input=input.csv -output=output.csv
// input file format: first column is uid, other columns are data
// output file format: first column is phone number, other columns are data

import (
	"context"
	"encoding/csv"
	"flag"
	"os"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

func main() {
	inputPath := flag.String("input", "", "Input csv file path")
	outputPath := flag.String("output", "", "Output csv file path")
	flag.Parse()
	// read uid from file
	inputData := readDataFromCsv(*inputPath)

	outputData := [][]string{}
	for idx := range inputData {
		uid := inputData[idx][0]
		userData, _ := rdb.GormRepo().GetUser(context.Background(), uid, "", false, nil)
		if userData == nil {
			continue
		}
		phoneNumber := userData.PhoneNumber
		outputData = append(outputData, append([]string{phoneNumber}, inputData[idx][1:]...))
	}

	writeDataToCsv(*outputPath, outputData)
}

func readDataFromCsv(filePath string) [][]string {
	file, err := os.Open(filePath)
	if err != nil {
		panic(err)
	}
	defer file.Close()

	// 解析 CSV 檔案
	reader := csv.NewReader(file)

	data := [][]string{}
	// 迭代每一筆資料
	for {
		record, err := reader.Read()
		if err != nil {
			break
		}
		data = append(data, record)
	}
	return data
}

func writeDataToCsv(filePath string, data [][]string) {
	file, err := os.Create(filePath)
	if err != nil {
		panic(err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	for _, record := range data {
		err := writer.Write(record)
		if err != nil {
			panic(err)
		}
	}
}
