package main

import (
	"context"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/compliance"
	restcountriesapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/restcountries-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	complianceSvc "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/compliance"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// setup: modify database,redis,COMPLIANCE_API_KEY in config
// usgae: go run main.go

func init() {
	rdb.Init()

	// setup studio organization package
	organization.Init(organization.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
		SendgridClient:      sendgrid.NewClient(),
	})
	// setup comply flow package
	complianceSvc.Init(rdb.GormRepo())
}

func main() {
	ctx := context.Background()
	kglog.InfoCtx(ctx, "migrating compliance form submissions start")
	client := compliance.NewAdminAPIClient()

	// get all the studio organization
	organizations := []model.StudioOrganization{}
	rdb.Get().Find(&organizations)

	for _, org := range organizations {
		kglog.InfofCtx(ctx, "kg organization id: %d", org.ID)
		if org.ComplianceOrganizationID == 0 {
			continue
		}
		// get all the compliance form submissions
		formSubmissionAndTasks, _, err := client.GetFormSubmissionsAndTasks(ctx, &compliance.GetTasksParam{
			ComplianceOrgID: org.ComplianceOrganizationID,
		})
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "get form submissions error", map[string]interface{}{
				"org_id":            org.ID,
				"compliance_org_id": org.ComplianceOrganizationID,
				"error":             err.Error(),
			})
			panic(err)
		}
		kglog.InfofCtx(ctx, "form submissions count: %d", len(formSubmissionAndTasks))

		// start from the oldest form submission
		for i := len(formSubmissionAndTasks) - 1; i >= 0; i-- {
			kglog.InfofCtx(ctx, "processing form submission: %+v", formSubmissionAndTasks[i])
			formSubmissionAndTask := formSubmissionAndTasks[i]
			if formSubmissionAndTask.KgUID == "" {
				kglog.WarningfCtx(ctx, "kgUID is empty, form submission: %+v", formSubmissionAndTask)
				continue
			}

			err := updateCustomerAndCaseSubmission(ctx, org.ID, formSubmissionAndTask)
			if err != nil {
				kglog.ErrorWithDataCtx(ctx, "update customer and case submission error", map[string]interface{}{
					"org_id": org.ID,
					"kg_uid": formSubmissionAndTask.KgUID,
					"error":  err.Error(),
				})
				panic(err)
			}
		}
	}
	kglog.InfoCtx(ctx, "migrating compliance form submissions end")
}

func updateCustomerAndCaseSubmission(ctx context.Context, orgID int, data *compliance.FormSubmissionAndTasks) error {
	caseSubmission := model.CaseSubmission{
		OrganizationID: orgID,
		UID:            data.KgUID,
		FormID:         &data.FormID,
	}
	updateColumns := []string{}
	if data.FormSubmittedAt != 0 {
		caseSubmission.SubmittedAt = time.Unix(data.FormSubmittedAt, 0)
		updateColumns = append(updateColumns, "submitted_at")
	}
	if data.FormUpdatedAt != 0 {
		caseSubmission.UpdatedAt = time.Unix(data.FormUpdatedAt, 0)
		updateColumns = append(updateColumns, "updated_at")
	}
	if data.IdvID != 0 {
		caseSubmission.IdvID = &data.IdvID
		updateColumns = append(updateColumns, "idv_id")
	}
	if data.CddID != 0 {
		caseSubmission.CddID = &data.CddID
		updateColumns = append(updateColumns, "cdd_id")
	}

	customer := map[string]interface{}{}
	customer["kyc_status"] = convertAuditToKycStatus(&data.FormAuditStatus, data.CddAuditStatus, data.IdvAuditStatus, util.Val(data.SearchAutoCreate), util.Val(data.IdvAutoCreate))
	if data.IdvStatus != nil && data.IdvStatus.String() != "" {
		customer["idv_status"] = *data.IdvStatus
	}
	if data.CddRiskScore != nil {
		customer["cdd_risk_score"] = *data.CddRiskScore
	}
	if data.SanctionMatched != nil {
		customer["sanction_matched"] = *data.SanctionMatched
	}
	if data.Email != nil && *data.Email != "" {
		customer["email"] = cleanEmail(*data.Email)
	}
	if data.PhoneNumber != nil && *data.PhoneNumber != "" {
		customer["phone"] = cleanPhone(*data.PhoneNumber)
	}
	if data.RealName != nil {
		customer["legal_name"] = *data.RealName
	}
	if data.Country != nil {
		customer["country"] = restcountriesapi.GetCountryNameByCode(ctx, "en_US", *data.Country)
	}
	if data.IDNumber != nil {
		customer["national_id"] = *data.IDNumber
	}
	if data.Birthday != nil {
		customer["birthday"] = *data.Birthday
	}
	if data.BankName != nil {
		customer["bank_name"] = *data.BankName
	}
	if data.BranchName != nil {
		customer["branch_name"] = *data.BranchName
	}
	if data.AccountNumber != nil {
		customer["account_number"] = *data.AccountNumber
	}
	if data.AccountHolderName != nil {
		customer["account_holder_name"] = *data.AccountHolderName
	}

	return rdb.GetWith(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "form_id"},
			},
			DoUpdates: clause.AssignmentColumns(updateColumns),
		}).Create(&caseSubmission).Error; err != nil {
			return err
		}

		if err := tx.Model(&model.Customer{}).
			Where("uid = ? AND organization_id = ?", data.KgUID, orgID).
			Updates(&customer).Error; err != nil {
			return err
		}

		return nil
	})
}

func convertAuditToKycStatus(FromCmp, cddStatusFromCmp, idvStatusFromCmp *compliance.AuditStatus, searchAutoCreate, idvAutoCreate bool) domain.KycStatus {
	formStatus := getKycStatus(FromCmp)
	cddStatus := getKycStatus(cddStatusFromCmp)
	idvStatus := getKycStatus(idvStatusFromCmp)
	kglog.Infof("formStatus: %s, cddStatus: %s, idvStatus: %s, searchAutoCreate: %t, idvAutoCreate: %t\n", formStatus, cddStatus, idvStatus, searchAutoCreate, idvAutoCreate)

	if formStatus == domain.KycStatusUnverified &&
		((!searchAutoCreate) || cddStatus == domain.KycStatusUnverified) &&
		((!idvAutoCreate) || idvStatus == domain.KycStatusUnverified) {
		// all 3 are not found, then unverified
		return domain.KycStatusUnverified
	}

	// form config may be nil if user is not from complyflow

	// force to pass if auto create is disabled
	if !idvAutoCreate {
		idvStatus = domain.KycStatusVerified
	}

	// force to pass if auto create is disabled
	if !searchAutoCreate {
		cddStatus = domain.KycStatusVerified
	}

	if formStatus == domain.KycStatusRejected ||
		cddStatus == domain.KycStatusRejected ||
		idvStatus == domain.KycStatusRejected {
		// any of the 3 is not verified, then rejected
		return domain.KycStatusRejected
	} else if formStatus == domain.KycStatusVerified &&
		cddStatus == domain.KycStatusVerified &&
		idvStatus == domain.KycStatusVerified {
		// all 3 are verified, then verified
		return domain.KycStatusVerified
	}

	// default to pending, it mean at least 1 of the 3 is not audited
	return domain.KycStatusPending
}

func cleanPhone(phone string) string {
	phone = strings.Replace(phone, "(deprecated)", "", -1)
	return phone
}

func cleanEmail(email string) string {
	email = strings.Replace(email, "(deprecated)", "", -1)
	return email
}

func getKycStatus(auditStatus *compliance.AuditStatus) domain.KycStatus {
	if auditStatus == nil || *auditStatus == compliance.AuditStatusUndecided {
		return domain.KycStatusPending
	}

	if *auditStatus == compliance.AuditStatusRejected {
		return domain.KycStatusRejected
	}

	return domain.KycStatusVerified
}
