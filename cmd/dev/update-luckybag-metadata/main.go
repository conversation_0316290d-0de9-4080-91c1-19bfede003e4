package main

import (
	"context"
	"encoding/json"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/mitchellh/mapstructure"
)

/**
 * Usage: ENV=prod go run cmd/dev/update-luckybag-metadata/main.go
 */
func main() {
	chainID := "polygon"
	contractAddress := "******************************************"
	slug := "kryptogo-demiverse-lucky-bag-2022"
	schemaName := "ERC721"
	collectionName := "KryptoGO DemiVerse Lucky Bag 2022"
	collectionImageURL := "https://lh3.googleusercontent.com/VBNjpyeiEtffTnsICu2byPqtyr8bbx42pcCrxOxqKZnMopPM7YgjuxcxaHzu2HXAxAzVtVPBwumVxKoUsIsouuerOfvyPbcM__a2pQ=s0"
	nftDocID := chainID + "-" + contractAddress

	for i := 1; i <= 6666; i++ {
		kglog.Debugf("i: %d", i)
		tokenID := strconv.Itoa(i)
		itemSS, err := db.OneDoc("nfts", nftDocID).
			Ref.Collection("items").Doc(tokenID).Get(context.Background())
		if err != nil {
			kglog.Errorf("fail to get nft item: %v", err)
			continue
		}
		if itemSS == nil {
			kglog.Errorf("nft item is nil")
			continue
		}
		nftItem := &db.NftItem{}
		if err = mapstructure.Decode(itemSS.Data(), nftItem); err != nil {
			kglog.Errorf("fail to decode nft item: %v", err)
			continue
		}
		traits, _ := json.Marshal(nftItem.ItemDetail.Traits)
		traitsJSONStr := string(traits)

		nftAsset := &model.NftAsset{
			Name:               util.UnifyAssetName(schemaName, nftItem.ItemDetail.ItemName, tokenID),
			CollectionName:     &collectionName,
			CollectionImageURL: &collectionImageURL,
			ChainID:            chainID,
			ContractAddress:    contractAddress,
			TokenID:            tokenID,
			CollectionSlug:     &slug,
			ContractSchemaName: &schemaName,
			ImageURL:           nftItem.ItemDetail.ImageURL,
			ImagePreviewURL:    nftItem.ItemDetail.ImageURL,
			LastPrice:          0,
			LastPriceSymbol:    "ETH",
			Traits:             &traitsJSONStr,
		}
		if err := rdb.SaveNftAsset(context.Background(), nftAsset); err != nil {
			kglog.Errorf("fail to save nft asset: %v", err)
		}
	}
}
