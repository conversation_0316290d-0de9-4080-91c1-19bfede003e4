// usage: go run cmd/dev/withdraw-org-solana/main.go -amount 0.001 -recipient ******************************************** -org 1
package main

import (
	"context"
	"flag"
	"log"
	"os"
	"time"

	solanaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/referral"
	"github.com/shopspring/decimal"
)

func main() {
	amountStr := flag.String("amount", "", "Amount of SOL to send (e.g. 0.01)")
	recipient := flag.String("recipient", "", "Recipient Solana address (base58)")
	orgID := flag.Int("org", 1, "Organization ID (default 1)")
	flag.Parse()

	if *amountStr == "" || *recipient == "" {
		kglog.ErrorCtx(context.Background(), "amount and recipient are required")
		flag.Usage()
		os.Exit(1)
	}

	referral.Init(repo.Unified())
	solanaapi.InitDefault()

	amountDec, err := decimal.NewFromString(*amountStr)
	if err != nil {
		kglog.ErrorWithDataCtx(context.Background(), "Invalid amount", map[string]interface{}{"error": err.Error()})
		os.Exit(1)
	}
	amountLamports := amountDec.Mul(decimal.NewFromInt(1_000_000_000)).BigInt() // 1 SOL = 1e9 lamports

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	txHash, kgErr := referral.SendOrgSolana(ctx, *orgID, amountLamports, *recipient)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to send SOL", map[string]interface{}{"error": kgErr.Error})
		os.Exit(1)
	}
	kglog.InfoWithDataCtx(ctx, "Successfully sent SOL", map[string]interface{}{"txHash": txHash})
	log.Printf("Transaction hash: %s\n", txHash)
}
