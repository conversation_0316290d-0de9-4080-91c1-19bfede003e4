package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"

	firebase "firebase.google.com/go/v4"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"google.golang.org/api/option"
)

// usage: API_KEY=xxx SERVICE_ACCOUNT_FILE=/path/to/json USER_PHONE_NUMBER=+8869xxxxxxxx go run main.go

const (
	verifyCustomTokenURL = "https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyCustomToken?key=%s"
)

var (
	apiKey             = os.Getenv("API_KEY")
	serviceAccountFile = os.Getenv("SERVICE_ACCOUNT_FILE")
	phoneNumber        = os.Getenv("USER_PHONE_NUMBER")
)

func main() {
	testUserIDToken()
}

func testUserIDToken() {
	ctx := context.Background()
	opt := option.WithCredentialsFile(serviceAccountFile)
	app, err := firebase.NewApp(ctx, nil, opt)
	if err != nil {
		kglog.Errorf("error initializing firebase app: %v", err)
		return
	}
	client, err := app.Auth(ctx)
	if err != nil {
		kglog.Errorf("error initializing auth client: %v", err)
		return
	}

	user, err := client.GetUserByPhoneNumber(ctx, phoneNumber)
	if err != nil {
		kglog.Errorf("error getting user by phone number: %v", err)
		return
	}

	tokenString, err := client.CustomToken(ctx, user.UID)
	if err != nil {
		kglog.Errorf("error creating custom token: %v", err)
		return
	}

	idToken, err := signInWithCustomToken(tokenString)
	if err != nil {
		kglog.Errorf("error signing in with custom token: %v", err)
		return
	}

	fmt.Printf("token: %s\n\n", idToken)

	token, err := client.VerifyIDToken(ctx, idToken)
	if err != nil {
		kglog.Errorf("error verifying ID token: %v", err)
		return
	}

	fmt.Printf("%+v\n", token)
}

func signInWithCustomToken(token string) (string, error) {
	req, err := json.Marshal(map[string]interface{}{
		"token":             token,
		"returnSecureToken": true,
	})
	if err != nil {
		return "", err
	}

	resp, err := postRequest(fmt.Sprintf(verifyCustomTokenURL, apiKey), req)
	if err != nil {
		return "", err
	}
	var respBody struct {
		IDToken string `json:"idToken"`
	}
	if err := json.Unmarshal(resp, &respBody); err != nil {
		return "", err
	}
	return respBody.IDToken, err
}

func postRequest(url string, req []byte) ([]byte, error) {
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(req))
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected http status code: %d", resp.StatusCode)
	}
	return io.ReadAll(resp.Body)
}
