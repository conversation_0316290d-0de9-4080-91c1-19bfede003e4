package main

import (
	"context"
	"fmt"
	"os"

	firebase "firebase.google.com/go/v4"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"google.golang.org/api/option"
)

// usage: SERVICE_ACCOUNT_FILE=/path/to/json USER_PHONE_NUMBER=+8869xxxxxxxx go run main.go

var (
	serviceAccountFile = os.Getenv("SERVICE_ACCOUNT_FILE")
	phoneNumber        = os.Getenv("USER_PHONE_NUMBER")
)

func main() {
	revokeUserIDToken()
}

func revokeUserIDToken() {
	ctx := context.Background()
	opt := option.WithCredentialsFile(serviceAccountFile)
	app, err := firebase.NewApp(ctx, nil, opt)
	if err != nil {
		kglog.Errorf("error initializing app: %v", err)
		return
	}
	client, err := app.Auth(ctx)
	if err != nil {
		kglog.Errorf("error initializing auth client: %v", err)
		return
	}

	user, err := client.GetUserByPhoneNumber(ctx, phoneNumber)
	if err != nil {
		kglog.Errorf("error getting user by phone number: %v", err)
		return
	}
	fmt.Println("user.UID:", user.UID)

	err = client.RevokeRefreshTokens(ctx, user.UID)
	if err != nil {
		kglog.Errorf("error revoking refresh tokens: %v", err)
		return
	}
	kglog.Info("refresh token revoked")
}
