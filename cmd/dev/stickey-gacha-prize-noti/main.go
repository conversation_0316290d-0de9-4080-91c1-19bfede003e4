package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

var (
	titleN       = "Stickeyリリース記念キャンペーン コラボNFT N 抽選結果のお知らせ"
	summaryN     = "Stickeyリリース記念キャンペーンにてアマゾンギフト券300円分に当選したことをお知らせします"
	msgTemplateN = "<p><img style=\"max-width: 100%; display: block; margin-left: auto; margin-right: auto;\" src=\"https://wallet-static.kryptogo.com/public/announcement/stickey_gacha_result/gacha_n.png\" /></p><p><b>Stickeyリリース記念キャンペーンにてアマゾンギフト券300円分に当選したことをお知らせします</b></p><p>下記がアマゾンギフト券コードになります</p><p>{{.prize_code}}</p><p>キャンペーン詳細</p><p><strong>応募期間</strong></p><p><strong>2023年7月10日（月）～7月28日（金）23:59</strong></p><p>・Amazonギフトカード3,000円、300円の当選者の発表はアプリ内での賞品コードの発送をもって代えさせていただきます。</p><p>・Amazonギフトカードコードの発送は8月中を予定しております。</p><p><strong>■ 応募条件・方法（Amazonギフトカード3,000円、300円）</strong></p><ul><li>・Stickeyアプリをダウンロードしていること</li><li>・Stickeyアプリでアカウント作成していること</li><li>・応募期間内にコラボNFT RもしくはコラボNFT Nを保有していること（自動応募・抽選）</li></ul><p><strong>注意点</strong></p><p>※下記に該当する場合は、当選対象外となります。</p><ul><li>・キャンペーン終了時点で、Stickeyアカウントを削除している場合</li><li>・キャンペーン終了時点で、Stickeyをアンインストールしている場合</li><li>・不正な情報で会員登録を行っている場合</li><li>・応募内容に不備がある場合</li><li>・アプリ内で通知される応募フォームに必要事項を入力し、応募すること</li></ul><p>※こちらは自動応募ではありません</p><p><strong>■注意事項</strong></p><ul><li>・応募内容は変更できませんので、ご注意ください。</li><li>・プレゼントの対象となった方にのみ、アプリ内でご連絡をさせていただきます。</li><li>・いたずら、虚偽の内容、不正な申請など、弊社が無効な応募と判断した場合には、プレゼントの対象となりません。</li><li>・本キャンペーンは予告なく中止、終了または期間や内容の変更を行う場合があります。</li><li>・応募内容や受付完了の確認に関するお問い合わせには一切お答えできません。</li><li>・本キャンペーンの応募要項の解釈・適用に関しましては、日本国法に準拠するものといたします。 また、本キャンペーンに関する紛争については、東京地方裁判所を第一審の専属的合意管轄裁判所とします。 本キャンペーンはStickeyGO Inc.より委託を受け、株式会社ForNが運営しております。</li></ul>"
	imgN         = "https://wallet-static.kryptogo.com/public/announcement/stickey_gacha_result/gacha_n.png"

	titleR       = "Stickeyリリース記念キャンペーン コラボNFT R 抽選結果のお知らせ"
	summaryR     = "Stickeyリリース記念キャンペーンにてアマゾンギフト券3000円分に当選したことをお知らせします"
	msgTemplateR = "<p><img style=\"max-width: 100%; display: block; margin-left: auto; margin-right: auto;\" src=\"https://wallet-static.kryptogo.com/public/announcement/stickey_gacha_result/gacha_r.png\" /></p><p><b>Stickeyリリース記念キャンペーンにてアマゾンギフト券3000円分に当選したことをお知らせします</b></p><p>下記がアマゾンギフト券コードになります</p><p>{{.prize_code}}</p><p>キャンペーン詳細</p><p><strong>応募期間</strong></p><p><strong>2023年7月10日（月）～7月28日（金）23:59</strong></p><p>・Amazonギフトカード3,000円、300円の当選者の発表はアプリ内での賞品コードの発送をもって代えさせていただきます。</p><p>・Amazonギフトカードコードの発送は8月中を予定しております。</p><p><strong>■ 応募条件・方法（Amazonギフトカード3,000円、300円）</strong></p><ul><li>・Stickeyアプリをダウンロードしていること</li><li>・Stickeyアプリでアカウント作成していること</li><li>・応募期間内にコラボNFT RもしくはコラボNFT Nを保有していること（自動応募・抽選）</li></ul><p><strong>注意点</strong></p><p>※下記に該当する場合は、当選対象外となります。</p><ul><li>・キャンペーン終了時点で、Stickeyアカウントを削除している場合</li><li>・キャンペーン終了時点で、Stickeyをアンインストールしている場合</li><li>・不正な情報で会員登録を行っている場合</li><li>・応募内容に不備がある場合</li><li>・アプリ内で通知される応募フォームに必要事項を入力し、応募すること</li></ul><p>※こちらは自動応募ではありません</p><p><strong>■注意事項</strong></p><ul><li>・応募内容は変更できませんので、ご注意ください。</li><li>・プレゼントの対象となった方にのみ、アプリ内でご連絡をさせていただきます。</li><li>・いたずら、虚偽の内容、不正な申請など、弊社が無効な応募と判断した場合には、プレゼントの対象となりません。</li><li>・本キャンペーンは予告なく中止、終了または期間や内容の変更を行う場合があります。</li><li>・応募内容や受付完了の確認に関するお問い合わせには一切お答えできません。</li><li>・本キャンペーンの応募要項の解釈・適用に関しましては、日本国法に準拠するものといたします。 また、本キャンペーンに関する紛争については、東京地方裁判所を第一審の専属的合意管轄裁判所とします。 本キャンペーンはStickeyGO Inc.より委託を受け、株式会社ForNが運営しております。</li></ul>"
	imgR         = "https://wallet-static.kryptogo.com/public/announcement/stickey_gacha_result/gacha_r.png"

	httpClient *resty.Client
)

/**
 * Usage: TOKEN=$(gcloud auth print-identity-token) go run cmd/dev/stickey-gacha-prize-noti/main.go
 */

func main() {
	// read csv file
	records, err := util.ReadCsvFile("res/stickey-winner-300.csv")
	if err != nil {
		panic(err)
	}

	httpClient = resty.New().
		SetBaseURL(config.GetString("SELF_INTERNAL_HOST"))
	clientID, kgErr := application.GetStickeyClientID(context.Background())
	if kgErr != nil {
		panic(kgErr.Error)
	}

	var successCount, failCount int
	for i, row := range records {
		if i == 0 {
			// skip header
			continue
		}
		// row[0] is address
		// row[2] is prize code
		address := row[0]
		prizeCode := row[2]
		msg := util.TempToStr(msgTemplateN, map[string]interface{}{
			"prize_code": prizeCode,
		})
		req := &api.SendNotificationsReq{
			WalletAddresses: []string{address},
			MessageType:     "system",
			ContentType:     "html",
			MultiLocaleTitle: map[string]string{
				"default": titleN,
			},
			MultiLocaleSummary: map[string]string{
				"default": summaryN,
			},
			MultiLocaleMessage: map[string]string{
				"default": msg,
			},
			MultiLocalePrimaryText: map[string]string{},
			PreviewImageURL: map[string]string{
				"default": imgN,
			},
			ClientID: clientID,
		}
		// send notification
		resp, err := httpClient.R().
			SetHeader("KG-INTERNAL-TOKEN", os.Getenv("TOKEN")).
			SetBody(req).
			Post("/_v/notifications")
		if err != nil {
			fmt.Println("error sending notification: ", err.Error())
			failCount++
			continue
		}
		if resp.StatusCode() != http.StatusOK {
			fmt.Println("error sending notification: ", resp.Status())
			failCount++
			continue
		}
		successCount++

		if i%5 == 0 {
			time.Sleep(1 * time.Second)
		}
	}
	fmt.Println("N success count: ", successCount, ", fail count: ", failCount)

	// read csv file
	records, err = util.ReadCsvFile("res/stickey-winner-3000.csv")
	if err != nil {
		panic(err)
	}

	successCount = 0
	failCount = 0
	for i, row := range records {
		if i == 0 {
			// skip header
			continue
		}
		// row[0] is address
		// row[2] is prize code
		address := row[0]
		prizeCode := row[2]
		msg := util.TempToStr(msgTemplateR, map[string]interface{}{
			"prize_code": prizeCode,
		})
		req := &api.SendNotificationsReq{
			WalletAddresses: []string{address},
			MessageType:     "system",
			ContentType:     "html",
			MultiLocaleTitle: map[string]string{
				"default": titleR,
			},
			MultiLocaleSummary: map[string]string{
				"default": summaryR,
			},
			MultiLocaleMessage: map[string]string{
				"default": msg,
			},
			MultiLocalePrimaryText: map[string]string{},
			PreviewImageURL: map[string]string{
				"default": imgR,
			},
			ClientID: clientID,
		}
		// send notification
		resp, err := httpClient.R().
			SetHeader("KG-INTERNAL-TOKEN", os.Getenv("TOKEN")).
			SetBody(req).
			Post("/_v/notifications")
		if err != nil {
			fmt.Println("error sending notification: ", err.Error())
			failCount++
			continue
		}
		if resp.StatusCode() != http.StatusOK {
			fmt.Println("error sending notification: ", resp.Status())
			failCount++
			continue
		}
		successCount++

		if i%5 == 0 {
			time.Sleep(1 * time.Second)
		}
	}
	fmt.Println("R success count: ", successCount, ", fail count: ", failCount)
}
