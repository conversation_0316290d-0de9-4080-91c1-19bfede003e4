package main

import (
	"bytes"
	"context"
	"encoding/json"
	"flag"
	"net/http"
	"os"
	"time"

	solanago "github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

const (
	targetAddress    = "GNSdzR1xxpALxDw98hqsRKToCufRpLHvnmYQLi71PDFR"
	tokenAnalysisAPI = "https://api-q4nbern77q-de.a.run.app/_v/token_analysis/record"
	referenceHash    = "2EfJLsE6WhwvWMEFAeXVEdd3wcKEq4E7JZR85brkkvimJYuAiTkNiUxjKVG7R2MihHGCXZ1A8kpxGt6JVJxs2tgP"
)

type TokenAnalysisRequest struct {
	TxHash string `json:"tx_hash"`
}

type TokenAnalysisResponse struct {
	// Add response fields as needed based on the API response
	Success bool `json:"success"`
	// Add other fields that come back from the API
}

func main() {
	ctx := context.Background()

	// Parse command line flags
	internalToken := flag.String("token", "", "KG Internal Token for API authentication")
	flag.Parse()

	if *internalToken == "" {
		kglog.FatalCtx(ctx, "KG Internal Token is required. Please provide it using the -token flag")
		os.Exit(1)
	}

	// Initialize RPC client
	rpcClient := rpc.New("https://solana-mainnet.g.alchemy.com/v2/********************************")

	// Get reference transaction details
	refSignature := solanago.MustSignatureFromBase58(referenceHash)
	refTx, err := rpcClient.GetTransaction(ctx, refSignature, &rpc.GetTransactionOpts{
		MaxSupportedTransactionVersion: util.Ptr(uint64(0)),
	})
	if err != nil {
		kglog.FatalWithDataCtx(ctx, "Failed to fetch reference transaction", map[string]interface{}{
			"error": err.Error(),
			"hash":  referenceHash,
		})
		os.Exit(1)
	}

	refTimestamp := refTx.BlockTime.Time()
	kglog.InfoWithDataCtx(ctx, "Reference transaction timestamp fetched", map[string]interface{}{
		"timestamp": refTimestamp,
	})

	// Get signatures with pagination
	var allSignatures []*rpc.TransactionSignature
	var beforeSignature solanago.Signature
	const paginationLimit = 1000
	var hasMorePages = true

	kglog.InfoCtx(ctx, "Starting to fetch Solana transaction signatures")

	for hasMorePages {
		opts := &rpc.GetSignaturesForAddressOpts{
			Limit: util.Ptr(paginationLimit),
		}
		if !beforeSignature.IsZero() {
			opts.Before = beforeSignature
		}

		signatures, err := rpcClient.GetSignaturesForAddressWithOpts(ctx, solanago.MustPublicKeyFromBase58(targetAddress), opts)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "Failed to get signatures", map[string]interface{}{
				"error": err.Error(),
			})
			os.Exit(1)
		}

		kglog.InfoWithDataCtx(ctx, "Fetched signatures batch", map[string]interface{}{
			"count": len(signatures),
		})

		// Filter signatures based on timestamp
		for _, sig := range signatures {
			if sig.BlockTime.Time().After(refTimestamp) {
				allSignatures = append(allSignatures, sig)
			}
		}

		if len(signatures) < paginationLimit {
			hasMorePages = false
		} else {
			beforeSignature = signatures[len(signatures)-1].Signature
		}
	}

	kglog.InfoWithDataCtx(ctx, "Total signatures fetched after filtering", map[string]interface{}{
		"count": len(allSignatures),
	})

	// Create HTTP client with timeout
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Process each transaction
	for i, sig := range allSignatures {
		txHash := sig.Signature.String()

		// Skip if it's the reference transaction
		if txHash == referenceHash {
			continue
		}

		// Prepare request body
		reqBody := TokenAnalysisRequest{
			TxHash: txHash,
		}
		jsonBody, err := json.Marshal(reqBody)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "Failed to marshal request body", map[string]interface{}{
				"error":   err.Error(),
				"tx_hash": txHash,
			})
			continue
		}

		// Create request
		req, err := http.NewRequestWithContext(ctx, "POST", tokenAnalysisAPI, bytes.NewBuffer(jsonBody))
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "Failed to create request", map[string]interface{}{
				"error":   err.Error(),
				"tx_hash": txHash,
			})
			continue
		}

		// Set headers
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("KG-INTERNAL-TOKEN", *internalToken)

		// Make request
		resp, err := httpClient.Do(req)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "Failed to make request", map[string]interface{}{
				"error":   err.Error(),
				"tx_hash": txHash,
			})
			continue
		}

		// Process response
		var result TokenAnalysisResponse
		if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
			resp.Body.Close()
			kglog.ErrorWithDataCtx(ctx, "Failed to decode response", map[string]interface{}{
				"error":   err.Error(),
				"tx_hash": txHash,
			})
			continue
		}
		resp.Body.Close()

		// Log progress periodically
		if (i+1)%100 == 0 {
			kglog.InfoWithDataCtx(ctx, "Processing progress", map[string]interface{}{
				"processed": i + 1,
				"total":     len(allSignatures),
			})
		}

		// Add a small delay to avoid overwhelming the API
		time.Sleep(100 * time.Millisecond)
	}

	kglog.InfoCtx(ctx, "Completed processing all transactions")
}
