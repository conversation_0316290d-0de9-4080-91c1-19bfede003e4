package main

import (
	"context"
	"fmt"
	"log"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

/**
 * Usage: source config/dev.sh && go run cmd/dev/update-firestore-user-hideSpamNft/main.go
 */

func main() {
	users, kgErr := rdb.GormRepo().UserAll(context.Background(), "", false, nil)
	if kgErr != nil {
		log.Println(kgErr)
		return
	}
	cnt := 0
	for _, user := range users {
		err := rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:         user.UID,
				HideSpamNft: util.Ptr(true),
			},
		})

		if err != nil {
			log.Println(err)
			break
		}
		cnt++
	}
	fmt.Println("total users cnt: ", cnt)
}
