package main

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

func main() {
	clientID := application.GetDefaultClientID(context.Background())

	// range over firestore user
	var users []*domain.UserData
	var kgErr *code.KGError
	var lastUserID *string
	for {
		users, kgErr = rdb.GormRepo().UserListAfterWithLimit(context.Background(), lastUserID, domain.BatchSize, clientID, false, &domain.UserPreloads{
			WithLocale: true,
		})
		if kgErr != nil {
			kglog.Error(kgErr.String())
			return
		}
		for _, user := range users {
			localeMap := user.LocaleMap
			if localeMap == nil {
				localeMap = map[string]string{}
			}
			localeMap[clientID] = user.Locale

			// update field fcm_token_map & read_all_timestamp_map
			err := rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
				UserInfo: domain.UserInfo{
					UID:       user.UID,
					LocaleMap: localeMap,
				},
			})
			if err != nil {
				kglog.ErrorWithData("set user error: "+err.Error(), user)
				continue
			}
		}
		lastUserID = &users[len(users)-1].UID
		if len(users) < domain.BatchSize {
			break
		}
	}
}
