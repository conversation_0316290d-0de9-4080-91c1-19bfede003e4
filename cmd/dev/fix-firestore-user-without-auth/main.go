package main

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

func main() {
	fixDuplicateUsers()
	fixUnlinkUsers()
}

func fixDuplicateUsers() {
	// range over firestore user
	// filter out user with duplicate docs
	// delete duplicate docs
	// delete duplicate user auth
	userSet := map[string][]*domain.UserData{}
	duplicatedPhoneNumbers := []string{}
	var lastUserID *string
	var users []*domain.UserData
	var kgErr *code.KGError
	for {
		users, kgErr = rdb.GormRepo().UserListAfterWithLimit(context.Background(), lastUserID, domain.BatchSize, "", false, nil)
		if kgErr != nil {
			kglog.Error(kgErr.String())
			return
		}
		for _, user := range users {
			if prevUsers, ok := userSet[user.PhoneNumber]; ok {
				userSet[user.PhoneNumber] = append(prevUsers, user)
			} else {
				userSet[user.PhoneNumber] = []*domain.UserData{user}
			}
		}
		lastUserID = &users[len(users)-1].UID
		if len(users) < domain.BatchSize {
			break
		}
	}

	for phoneNumber, users := range userSet {
		if len(phoneNumber) == 0 {
			continue
		}
		if len(users) > 1 {
			// record phone number
			duplicatedPhoneNumbers = append(duplicatedPhoneNumbers, phoneNumber)
			// shorterUser := users[0]
			// longerUser := users[1]
			// if len(longerUser.UID) < len(shorterUser.UID) {
			// 	shorterUser, longerUser = longerUser, shorterUser
			// }
			// // delete longer user's document
			// err := db.DeleteUser(longerUser.UID)
			// if err != nil {
			// 	kglog.ErrorWithData("delete user error: "+err.Error(), longerUser)
			// }
			// // delete longer user's firebase user
			// err = firebase.DeleteUser(longerUser.UID)
			// if err != nil {
			// 	kglog.ErrorWithData("delete firebase user error: "+err.Error(), longerUser)
			// }
		}
	}
	kglog.DebugWithData("duplicated phone numbers", duplicatedPhoneNumbers)
}

func fixUnlinkUsers() {
	// range over firebase user
	// filter out user without phone number
	// update user with phone number
	users := firebase.Users()
	for _, user := range users {
		if user.PhoneNumber == "" {
			userData, _ := rdb.GormRepo().GetUser(context.Background(), user.UID, "", false, nil)
			if userData == nil {
				kglog.Debug("no user data for uid: " + user.UID)
				continue
			}
			kglog.Debug("update user with phone number: " + userData.PhoneNumber + " uid: " + user.UID)
			// update user
			_, err := firebase.UpdateUserPhoneNumber(context.Background(), user.UID, userData.PhoneNumber)
			if err != nil {
				kglog.ErrorWithData("update user error: "+err.Error(), user)
			}
		}
	}
}
