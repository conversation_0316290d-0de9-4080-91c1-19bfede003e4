package main

import (
	"context"
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/samber/lo"
)

/**
 * Usage: source config/{env}.sh && go run cmd/dev/sync-alchemy-notify/main.go
 */
func main() {
	apis.InitDefault()
	var users []*domain.UserData
	var kgErr *code.KGError
	var lastUserID *string
	for {
		alchemyUsers := make([]*alchemyUser, 0)
		users, kgErr = rdb.GormRepo().UserListAfterWithLimit(context.Background(), lastUserID, domain.BatchSize, "", true, &domain.UserPreloads{
			WithWallets: true,
		})
		if kgErr != nil {
			kglog.Error(kgErr.String())
			return
		}
		for _, user := range users {
			walletAddressesByChainID := user.AddressesByChains("", []string{model.ChainIDEthereum}, false)
			for chainID, walletAddresses := range walletAddressesByChainID {
				if len(walletAddresses) == 0 {
					continue
				}
				alchemyUsers = append(alchemyUsers, &alchemyUser{
					UID:       user.UID,
					ChainID:   chainID,
					Addresses: walletAddresses,
				})
			}
		}
		if len(alchemyUsers) >= 0 {
			syncAlchemyWebhook(alchemyUsers)
		}
		lastUserID = &users[len(users)-1].UID
		if len(users) < domain.BatchSize {
			break
		}
	}
}

type alchemyUser struct {
	UID       string
	ChainID   string
	Addresses []string
}

func syncAlchemyWebhook(alchemyUsers []*alchemyUser) {
	alchemyapi.InitDefault()

	mWebhookID := config.GetStringMap("ALCHEMY_WEBHOOK_ID_MAP")

	var countWebhookID int
	for _, webhookID := range mWebhookID {
		if webhookID != "" {
			countWebhookID++
		}
	}
	if countWebhookID == 0 || countWebhookID != len(mWebhookID) {
		kglog.Errorf("missing webhook id")
		return
	}

	addresses := lo.FlatMap(alchemyUsers, func(a *alchemyUser, _ int) []string {
		return a.Addresses
	})

	// sync alchemy webhook MaxWebhookAddresses per request
	for i := 0; i < len(addresses); i += alchemyapi.MaxWebhookAddresses {
		end := i + alchemyapi.MaxWebhookAddresses
		if end > len(addresses) {
			end = len(addresses)
		}

		for _, webhookID := range mWebhookID {
			alchemyapi.Get().AddSingleWebhookAddresses(context.Background(), webhookID, addresses[i:end])
		}
	}

	fmt.Println("update users cnt: ", len(alchemyUsers))
}
