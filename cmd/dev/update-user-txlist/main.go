package main

import (
	"context"
	"log"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
)

/**
 * Usage: ENV=dev FLUSH_ALL=true UIDS=ycZFiYxcelZvJFSo9wR2fQV81MC3,47YaaBsEXyUTG6r972PSWvrW5JP2 CHAIN_ID=all go run cmd/dev/update-user-txlist/main.go
 *
 * CHAIN_ID=,all,eth,matic,btc,bsc,sol,tron,kcc,arb
 */
func main() {
	uids := config.GetString("UIDS")
	if uids == "" {
		log.Println("uids is empty")
		return
	}

	updateChainID := config.GetString("CHAIN_ID")
	if updateChainID == "" {
		updateChainID = "all"
	}

	// viper.SetDefault("FLUSH_ALL", false)
	// flushAll := config.GetBool("FLUSH_ALL")

	for _, uid := range strings.Split(uids, ",") {
		tx.Get().UpdateTxByUID(context.Background(), uid, updateChainID, false)
	}
}
