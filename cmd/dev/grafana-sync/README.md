Grafana sync

This is a simple script to sync Grafana dashboards between two Grafana instances.

## Requirements

- [grafana service account token](https://www.notion.so/kryptogo/AssetPro-Data-Design-Doc-df4275bb7e774361a592cd15e0b8f374?pvs=4#3b611e10c82b40458c398b211ac68fdf) of both source and destination Grafana instances.

## Configuration

### Senerio 1: Sync dashboard between two environment (e.g. dev and prod)

The constraint between frontend and backend is that the dashboard embed url must be `https://{{grafana-bask-url}}/d/{{dashboard-uid}}/{{dashboard-slug}}`. So we need to make sure that the dashboard slug is the same in both source and destination Grafana instances.

Make sure source.env and destination.env be set up correctly at `config.yaml` file. And source.dashboardUID and destination.dashboardUID is the same.

### Senerio 2: Sync between at the same environment, but different dashboard

You can set the source.dashboardUID and destination.dashboardUID to be different.

## How To Use

Run `go run`, you can see the output file `dashboard-source.jaon` and `dashboard-destination.json`.

## Debugging

You can set the dryRun to `true` in the `config.yaml` when you don't want to make any changes to the destination Grafana instance.
