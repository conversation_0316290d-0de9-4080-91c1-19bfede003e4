package main

import (
	"embed"
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"strings"

	"github.com/go-openapi/strfmt"
	goapi "github.com/grafana/grafana-openapi-client-go/client"
	"github.com/grafana/grafana-openapi-client-go/client/dashboards"
	"github.com/grafana/grafana-openapi-client-go/models"
	"gopkg.in/yaml.v3"
)

type env string

const (
	defaultOrgID = 1

	envDev        = env("dev")
	envStaging    = env("staging")
	envProduction = env("production")
)

var mProject = map[env]string{
	envDev:        "kryptogo-wallet-app-dev",
	envStaging:    "kryptogo-wallet-app-staging",
	envProduction: "kryptogo-wallet-app",
}

var mBigqueryUID = map[env]string{
	envDev:        "edp7uom6fi800e",
	envStaging:    "edrtihecyud4wf",
	envProduction: "eds3vmjvorpxce",
}

var mGrafanaHost = map[env]string{
	envDev:        "https://grafana-dev.kryptogo.com",
	envStaging:    "https://grafana-staging.kryptogo.com",
	envProduction: "https://grafana.kryptogo.com",
}

var allowedEnv = map[env]struct{}{
	envDev:        {},
	envStaging:    {},
	envProduction: {},
}

type cfg struct {
	Source struct {
		Env                 env    `yaml:"env"`
		DashboardUID        string `yaml:"dashboardUID"`
		ServiceAccountToken string `yaml:"serviceAccountToken"`
	} `yaml:"source"`
	Destination struct {
		Env                 env    `yaml:"env"`
		DashboardUID        string `yaml:"dashboardUID"`
		ServiceAccountToken string `yaml:"serviceAccountToken"`
		Title               string `yaml:"title"`
	} `yaml:"destination"`
	DryRun bool `yaml:"dryRun"`
}

func main() {
	if err := wrapMain(); err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("done")
}

//go:embed config.yaml
var embedFS embed.FS

func readConfig() (*cfg, error) {
	var c cfg
	config, err := embedFS.ReadFile("config.yaml")
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}
	if err := yaml.Unmarshal(config, &c); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config file: %v", err)
	}

	if _, ok := allowedEnv[c.Source.Env]; !ok {
		return nil, fmt.Errorf("invalid source env: %v", c.Source.Env)
	}

	if _, ok := allowedEnv[c.Destination.Env]; !ok {
		return nil, fmt.Errorf("invalid destination env: %v", c.Destination.Env)
	}

	if c.Source.DashboardUID == "" {
		return nil, fmt.Errorf("source dashboard UID is required")
	}

	if c.Source.ServiceAccountToken == "" {
		return nil, fmt.Errorf("source service account token is required")
	}

	if c.Destination.DashboardUID == "" {
		return nil, fmt.Errorf("destination dashboard UID is required")
	}

	if c.Destination.ServiceAccountToken == "" {
		return nil, fmt.Errorf("destination service account token is required")
	}

	if c.Destination.Title == "" {
		return nil, fmt.Errorf("destination dashboard title is required")
	}

	return &c, nil
}

func wrapMain() error {
	c, err := readConfig()
	if err != nil {
		return fmt.Errorf("error reading config: %v", err)
	}

	dashboard, err := getSourceDashboard(mGrafanaHost[c.Source.Env], c.Source.ServiceAccountToken, c.Source.DashboardUID)
	if err != nil {
		return fmt.Errorf("error getting dashboard: %v", err)
	}

	req := dashboardMigrateRequest{
		title:         c.Destination.Title,
		uid:           c.Destination.DashboardUID,
		dataSourceUID: mBigqueryUID[c.Destination.Env],
		srcProject:    mProject[c.Source.Env],
		dstProject:    mProject[c.Destination.Env],
	}
	if err := dashboard.migrate(req); err != nil {
		return fmt.Errorf("error migrating dashboard: %v", err)
	}

	if err := upsertDashboard(mGrafanaHost[c.Destination.Env], c.Destination.ServiceAccountToken, dashboard, c.DryRun); err != nil {
		return fmt.Errorf("error upserting dashboard: %v", err)
	}

	return nil
}

func createGrafanaOAPIClient(apiURL, apiKey string) (*goapi.GrafanaHTTPAPI, error) {
	u, err := url.Parse(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse API url: %v", err.Error())
	}

	cfg := goapi.TransportConfig{
		Host:      u.Host,
		BasePath:  "/api",
		Schemes:   []string{u.Scheme},
		TLSConfig: nil,
		BasicAuth: nil,
		OrgID:     defaultOrgID,
		APIKey:    apiKey,
	}

	return goapi.NewHTTPClientWithConfig(strfmt.Default, &cfg), nil
}

func getSourceDashboard(host, token, uid string) (*dashboard, error) {
	client, err := createGrafanaOAPIClient(host, token)
	if err != nil {
		return nil, fmt.Errorf("error creating source client: %v", err)
	}

	resp, err := client.Dashboards.GetDashboardByUID(uid)
	if err != nil {
		return nil, fmt.Errorf("error getting source dashboard: %v", err)
	}

	if !resp.IsSuccess() {
		return nil, fmt.Errorf("error getting source dashboard")
	}

	if err := saveToJSON(resp.Payload, "dashboard-source.json"); err != nil {
		fmt.Println("save source dashboard to json error:", err)
	}

	bs, err := json.Marshal(resp.Payload.Dashboard)
	if err != nil {
		return nil, fmt.Errorf("error marshaling dashboard: %v", err)
	}

	var d dashboard
	if err := json.Unmarshal(bs, &d); err != nil {
		return nil, fmt.Errorf("error unmarshaling dashboard: %v", err)
	}

	return &d, nil
}

func upsertDashboard(host, token string, d *dashboard, dryRun bool) error {
	client, err := createGrafanaOAPIClient(host, token)
	if err != nil {
		return fmt.Errorf("error creating source client: %v", err)
	}

	bs, err := json.Marshal(d)
	if err != nil {
		return fmt.Errorf("error marshaling dashboard: %v", err)
	}

	req := models.SaveDashboardCommand{}
	if err := json.Unmarshal(bs, &req.Dashboard); err != nil {
		return fmt.Errorf("error unmarshaling dashboard: %v", err)
	}

	if err := saveToJSON(req, "dashboard-destination.json"); err != nil {
		fmt.Println("save source dashboard to json error:", err)
	}

	if dryRun {
		return nil
	}

	{ // check if dashboard exists and delete it to avoid `version-mismatch`
		v := *d
		uid := v["uid"].(string)

		resp, err := client.Dashboards.DeleteDashboardByUID(uid)
		if err != nil {
			if _, ok := err.(*dashboards.DeleteDashboardByUIDNotFound); !ok {
				return fmt.Errorf("error deleting dashboard: %v", err)
			}
		}

		if !resp.IsSuccess() {
			return fmt.Errorf("error deleting dashboard")
		}

	}

	{ // create dashboard
		resp, err := client.Dashboards.PostDashboard(&req)
		if err != nil {
			return fmt.Errorf("error saving dashboard: %v", err)
		}

		if !resp.IsSuccess() {
			return fmt.Errorf("error saving dashboard")
		}
	}

	return nil
}

func saveToJSON(v any, filename string) error {
	bs, err := json.MarshalIndent(v, "", "  ")
	if err != nil {
		return fmt.Errorf("error marshaling dashboard: %v", err)
	}

	return os.WriteFile(filename, bs, 0644)
}

type dashboard map[string]any

type dashboardMigrateRequest struct {
	title         string
	uid           string
	dataSourceUID string
	srcProject    string
	dstProject    string
}

func (d *dashboard) migrate(req dashboardMigrateRequest) error {
	type j = map[string]any
	v := *d

	v["id"] = nil
	v["title"] = req.title
	v["uid"] = req.uid

	for panelIndex, panel := range v["panels"].([]any) {
		if panel.(j)["type"].(string) == "row" {
			continue
		}
		if panel.(j)["datasource"].(j)["type"].(string) == "grafana-bigquery-datasource" {
			v["panels"].([]any)[panelIndex].(j)["datasource"].(j)["uid"] = req.dataSourceUID
		}

		for targetIndex, target := range panel.(j)["targets"].([]any) {
			if target.(j)["datasource"].(j)["type"].(string) == "grafana-bigquery-datasource" {
				v["panels"].([]any)[panelIndex].(j)["targets"].([]any)[targetIndex].(j)["datasource"].(j)["uid"] = req.dataSourceUID
			}

			v["panels"].([]any)[panelIndex].(j)["targets"].([]any)[targetIndex].(j)["project"] = req.dstProject
			v["panels"].([]any)[panelIndex].(j)["targets"].([]any)[targetIndex].(j)["rawSql"] =
				strings.ReplaceAll(v["panels"].([]any)[panelIndex].(j)["targets"].([]any)[targetIndex].(j)["rawSql"].(string), req.srcProject, req.dstProject)
		}

	}

	for listIndex, list := range v["templating"].(j)["list"].([]any) {
		// case 1: dashboard variable is queried from datasource
		if list.(j)["datasource"] != nil && list.(j)["datasource"].(j)["type"] != nil {
			if list.(j)["datasource"].(j)["type"].(string) == "grafana-bigquery-datasource" {
				v["templating"].(j)["list"].([]any)[listIndex].(j)["datasource"].(j)["uid"] = req.dataSourceUID
			}

			v["templating"].(j)["list"].([]any)[listIndex].(j)["query"].(j)["rawSql"] =
				strings.ReplaceAll(v["templating"].(j)["list"].([]any)[listIndex].(j)["query"].(j)["rawSql"].(string), req.srcProject, req.dstProject)
		}

		// case 2: dashboard is set to a constant value
		// should be manually set to the correct value
	}

	return nil
}
