package main_test

import (
	"os"
	"strconv"
	"testing"

	poc "github.com/kryptogo/kg-wallet-backend/cmd/dev/payment-poc"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestQuoteBridge(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	okxClient := poc.NewClient(os.Getenv("OKX_ACCESS_KEY"), os.Getenv("OKX_SECRET_KEY"), os.<PERSON>env("OKX_PASSPHRASE"))
	resp, err := okxClient.GetQuoteBridge(map[string]string{
		"amount":           "941701975248680605",
		"fromTokenAddress": "******************************************",
		"fromChainId":      "56",
		"toTokenAddress":   "******************************************",
		"toChainId":        "42161",
	})
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	estimateTime := resp.Data[0].RouterList[0].EstimateTime
	bridgeName := resp.Data[0].RouterList[0].Router.BridgeName
	t.Logf("Estimate time: %s", estimateTime)
	t.Logf("Bridge name: %s", bridgeName)
}

func TestGetSupportedChains(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	okxClient := poc.NewClient(os.Getenv("OKX_ACCESS_KEY"), os.Getenv("OKX_SECRET_KEY"), os.Getenv("OKX_PASSPHRASE"))
	resp, err := okxClient.GetSupportedChains("")
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	t.Log(resp)
}

func TestQuoteBridgeSolana(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	okxClient := poc.NewClient(os.Getenv("OKX_ACCESS_KEY"), os.Getenv("OKX_SECRET_KEY"), os.Getenv("OKX_PASSPHRASE"))
	resp, err := okxClient.GetQuoteBridge(map[string]string{
		"amount":           "1000000000",
		"fromTokenAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",
		"fromChainId":      "501",
		"toTokenAddress":   "******************************************",
		"toChainId":        "42161",
	})
	assert.NoError(t, err)
	assert.NotNil(t, resp)

	estimateTime := resp.Data[0].RouterList[0].EstimateTime
	bridgeName := resp.Data[0].RouterList[0].Router.BridgeName
	bridgeId := resp.Data[0].RouterList[0].Router.BridgeID
	t.Logf("Estimate time: %s", estimateTime)
	t.Logf("Bridge name: %s", bridgeName)

	resp2, err := okxClient.GetBuildBridgeTx(map[string]string{
		"fromChainId":       "501",
		"toChainId":         "42161",
		"fromTokenAddress":  "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB",
		"toTokenAddress":    "******************************************",
		"amount":            "100000",
		"bridgeId":          strconv.Itoa(bridgeId),
		"userWalletAddress": "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
		"receiveAddress":    "******************************************",
	})
	assert.NoError(t, err)
	assert.NotNil(t, resp2)
	// t.Log(resp2)
}
