package main

import (
	"fmt"
	"net/url"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
)

const lifiBaseURL = "https://li.quest/v1"

var lifiChainId = map[domain.Chain]string{
	domain.Ethereum:  "eth",
	domain.Polygon:   "pol",
	domain.Arbitrum:  "arb",
	domain.BNBChain:  "bsc",
	domain.BaseChain: "bas",
	domain.Solana:    "sol",
}

type LiFiToken struct {
	Address  string `json:"address"`
	ChainId  int64  `json:"chainId"`
	Symbol   string `json:"symbol"`
	Decimals int    `json:"decimals"`
	Name     string `json:"name"`
	CoinKey  string `json:"coinKey"`
	LogoURI  string `json:"logoURI"`
	PriceUSD string `json:"priceUSD"`
}

type LiFiAction struct {
	FromChainId               int64     `json:"fromChainId"`
	FromAmount                string    `json:"fromAmount"`
	FromToken                 LiFiToken `json:"fromToken"`
	FromAddress               string    `json:"fromAddress"`
	ToChainId                 int64     `json:"toChainId"`
	ToToken                   LiFiToken `json:"toToken"`
	ToAddress                 string    `json:"toAddress"`
	Slippage                  float64   `json:"slippage"`
	DestinationGasConsumption string    `json:"destinationGasConsumption,omitempty"`
}

type LiFiGasCost struct {
	Amount    string    `json:"amount"`
	AmountUSD string    `json:"amountUSD"`
	Token     LiFiToken `json:"token"`
	Estimate  string    `json:"estimate"`
	Limit     string    `json:"limit"`
	Price     string    `json:"price"`
	Type      string    `json:"type"`
}

type LiFiFeeCost struct {
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Token       LiFiToken `json:"token"`
	Amount      string    `json:"amount"`
	AmountUSD   string    `json:"amountUSD"`
	Percentage  string    `json:"percentage"`
	Included    bool      `json:"included"`
}

type LiFiEstimate struct {
	FromAmount        string        `json:"fromAmount"`
	ToAmount          string        `json:"toAmount"`
	ToAmountMin       string        `json:"toAmountMin"`
	ApprovalAddress   string        `json:"approvalAddress"`
	GasCosts          []LiFiGasCost `json:"gasCosts"`
	FeeCosts          []LiFiFeeCost `json:"feeCosts"`
	ExecutionDuration int           `json:"executionDuration"`
	FromAmountUSD     string        `json:"fromAmountUSD"`
	ToAmountUSD       string        `json:"toAmountUSD"`
	Tool              string        `json:"tool,omitempty"`
}

type LiFiToolDetails struct {
	Key     string `json:"key"`
	Name    string `json:"name"`
	LogoURI string `json:"logoURI"`
}

type LiFiStep struct {
	Id          string          `json:"id"`
	Type        string          `json:"type"`
	Tool        string          `json:"tool"`
	Action      LiFiAction      `json:"action"`
	Estimate    LiFiEstimate    `json:"estimate"`
	ToolDetails LiFiToolDetails `json:"toolDetails"`
}

type LiFiQuote struct {
	Id                 string          `json:"id"`
	Type               string          `json:"type"`
	Tool               string          `json:"tool"`
	ToolDetails        LiFiToolDetails `json:"toolDetails"`
	Action             LiFiAction      `json:"action"`
	Estimate           LiFiEstimate    `json:"estimate"`
	IncludedSteps      []LiFiStep      `json:"includedSteps"`
	Integrator         string          `json:"integrator"`
	TransactionRequest struct {
		Data string `json:"data"`
		// Below fields are EVM only
		To       string `json:"to"`
		Value    string `json:"value"`
		From     string `json:"from"`
		ChainId  int    `json:"chainId"`
		GasPrice string `json:"gasPrice"`
		GasLimit string `json:"gasLimit"`
	} `json:"transactionRequest"`
	Message string `json:"message"`
}

type LiFiClient struct {
	httpClient *resty.Client
}

func NewLiFiClient() *LiFiClient {
	return &LiFiClient{
		httpClient: resty.New().
			SetBaseURL(lifiBaseURL).
			SetHeader("Content-Type", "application/json"),
	}
}

func (c *LiFiClient) GetQuote(fromChain domain.Chain, fromAddress, fromToken, fromAmount string, toChain domain.Chain, toToken, toAddress string) (*LiFiQuote, error) {
	params := url.Values{
		"fromChain":      {lifiChainId[fromChain]},
		"toChain":        {lifiChainId[toChain]},
		"fromToken":      {fromToken},
		"toToken":        {toToken},
		"toAddress":      {toAddress},
		"fromAmount":     {fromAmount},
		"fromAddress":    {fromAddress},
		"skipSimulation": {"true"},
	}

	var result LiFiQuote
	resp, err := c.httpClient.R().
		SetQueryParamsFromValues(params).
		SetResult(&result).
		Get("/quote")

	if err != nil {
		return nil, fmt.Errorf("failed to get Li.fi quote: %v", err)
	}

	// fmt.Printf("Li.fi quote resp body: %s\n", resp.Body())
	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("li.fi API returned status %d", resp.StatusCode())
	}

	return &result, nil
}
