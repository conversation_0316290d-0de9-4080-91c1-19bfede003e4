package main

import (
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
)

func handleTest() {
	// quote, err := lifiClient.GetQuote(domain.Arbitrum, "******************************************", "******************************************", "100000000", domain.Solana, "SOL", "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7")
	// quote, err := lifiClient.GetQuote(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7", "5qsZAnWuR7vMJbiznrJEY2ddVqZXjNYmFvR3eXSxpump", "1000000000", domain.Arbitrum, "******************************************", "******************************************")
	quote, err := lifiClient.GetQuote(domain.BaseChain, "******************************************", "******************************************", "10000000000000000000000", domain.Solana, "SOL", "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7")
	if err != nil {
		fmt.Printf("Failed to get quote: %v\n", err)
	}
	fmt.Printf("Quote: %+v\n", quote)
}
