package main

import (
	"context"
	"crypto/ecdsa"
	"encoding/binary"
	"encoding/hex"
	"flag"
	"fmt"
	"math/big"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/kg-wallet-backend/chain/evm"
	abibinding "github.com/kryptogo/kg-wallet-backend/chain/evm/abi-binding"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/mr-tron/base58"
	"github.com/shopspring/decimal"
)

var (
	ArbitrumUSDC            = "******************************************"
	EVMNativeToken          = "******************************************"
	SolanaUSDC              = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
	AccountFactory          = "******************************************"
	AccountFactoryAddress   = common.HexToAddress(AccountFactory)
	Paymaster               = "******************************************"
	PaymasterAddress        = common.HexToAddress(Paymaster)
	Entrypoint              = "******************************************"
	SimpleAccountFactoryABI = `[{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"uint256","name":"salt","type":"uint256"}],"name":"createAccount","outputs":[{"internalType":"address","name":"account","type":"address"}],"stateMutability":"payable","type":"function"}]`
	SimpleAccountABI        = `[{"inputs":[{"internalType":"address","name":"dest","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"},{"internalType":"bytes","name":"func","type":"bytes"}],"name":"execute","outputs":[],"stateMutability":"nonpayable","type":"function"}]`
	ERC20ABI                = `[{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"}]`
)

var (
	privateKeyHex = os.Getenv("PRIVATE_KEY")
	privateKey    *ecdsa.PrivateKey
	ownerAddress  common.Address
	okxClient     *Client
)

var (
	createCmd  = flag.NewFlagSet("create", flag.ExitOnError)
	collectCmd = flag.NewFlagSet("collect", flag.ExitOnError)

	// collect flags
	saltFlag = collectCmd.Int("salt", 0, "Salt value used to generate the address")
	// user's address to receive native token
	targetAddrFlag = collectCmd.String("target", "", "Target address to collect funds")
	// target chain can be base or solana
	targetChainId = collectCmd.String("chain", "", "Target chain to collect funds")
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Expected 'create' or 'collect' subcommands")
		os.Exit(1)
	}

	// setup variables
	var err error
	if privateKeyHex == "" {
		fmt.Println("PRIVATE_KEY environment variable not set")
		os.Exit(1)
	}
	privateKey, err = crypto.HexToECDSA(privateKeyHex)
	if err != nil {
		fmt.Printf("Failed to parse private key: %v\n", err)
		os.Exit(1)
	}
	ownerAddress = crypto.PubkeyToAddress(privateKey.PublicKey)
	fmt.Printf("Owner address: %s\n", ownerAddress.Hex())
	okxClient = NewClient(os.Getenv("OKX_ACCESS_KEY"), os.Getenv("OKX_SECRET_KEY"), os.Getenv("OKX_PASSPHRASE"))

	switch os.Args[1] {
	case "create":
		err := createCmd.Parse(os.Args[2:])
		if err != nil {
			fmt.Println("Failed to parse create command:", err)
			os.Exit(1)
		}
		handleCreate()
	case "collect":
		err := collectCmd.Parse(os.Args[2:])
		if err != nil {
			fmt.Println("Failed to parse collect command:", err)
			os.Exit(1)
		}
		handleCollect()
	case "test":
		handleTest()
	default:
		fmt.Println("Expected 'create' or 'collect' subcommands")
		os.Exit(1)
	}
}

func handleCreate() {
	// Generate random salt
	// r := rand.New(rand.NewSource(time.Now().UnixNano()))
	// salt := r.Intn(100000)
	salt := 59405
	saltBig := big.NewInt(int64(salt))

	// Calculate counterfactual addresses for each chain
	chains := []struct {
		name    string
		chainId string
	}{
		{"Ethereum", "eth"},
		{"Arbitrum", "arb"},
		{"Polygon", "matic"},
		{"BNBChain", "bsc"},
	}

	fmt.Printf("Generated salt: %d\n", salt)
	fmt.Println("Payment addresses:")

	_, solPublicKey := generateSolanaKeyPair(salt)
	fmt.Printf("Solana: %s\n", base58.Encode(solPublicKey))

	for _, chain := range chains {
		addr, err := getCounterfactualAddress(
			domain.IDToChain(chain.chainId),
			domain.NewEvmAddress(AccountFactory),
			domain.NewEvmAddress(ownerAddress.Hex()),
			saltBig,
		)
		if err != nil {
			fmt.Printf("Failed to calculate address for %s: %v\n", chain.name, err)
			continue
		}
		fmt.Printf("%s: %s\n", chain.name, addr)
	}

	fmt.Println("\nPlease transfer $5 USD worth of any token to one of these addresses.")
}

func handleCollect() {
	if *saltFlag == 0 || *targetAddrFlag == "" || *targetChainId == "" {
		fmt.Println("Salt, target address, and target chain are required")
		collectCmd.PrintDefaults()
		os.Exit(1)
	}

	// Calculate counterfactual address
	saltBig := big.NewInt(int64(*saltFlag))
	chains := []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Polygon, domain.BNBChain, domain.Solana}
	targetChain := domain.IDToChain(*targetChainId)
	for _, chain := range chains {
		if chain.IsEVM() {
			sender, err := getCounterfactualAddress(chain, domain.NewEvmAddress(AccountFactory), domain.NewEvmAddress(ownerAddress.Hex()), saltBig)
			if err != nil {
				fmt.Printf("Failed to calculate address for %s: %v\n", chain.ID(), err)
				continue
			}
			fmt.Printf("Checking balances for address on %s: %s\n", chain.ID(), sender)
			processFundsEVM(saltBig, chain, sender, *targetAddrFlag, targetChain)
		} else if chain == domain.Solana {
			solPrivateKey, solPublicKey := generateSolanaKeyPair(*saltFlag)
			sender := base58.Encode(solPublicKey)
			fmt.Printf("Checking balances for address on %s: %s\n", chain.ID(), sender)
			processFundsSolana(solPrivateKey, sender, *targetAddrFlag, targetChain)
		} else {
			panic("unsupported chain")
		}
	}
}

func processFundsEVM(salt *big.Int, chain domain.Chain, sender domain.EvmAddress, targetAddr string, targetChain domain.Chain) {
	client := evm.GetClient(chain)
	ctx := context.Background()

	nativeBalance, err := client.NativeBalance(ctx, sender)
	if err != nil {
		fmt.Printf("Failed to get native balance on %s: %v\n", chain.ID(), err)
		return
	}

	var userOps []abibinding.PackedUserOperation
	var nextAccountNonce *big.Int

	if nativeBalance.Sign() > 0 {
		fmt.Printf("Native token balance on %s: %s\n", chain.ID(), nativeBalance.String())
		if chain == targetChain {
			// 1) If native token is on the same chain, direct transfer
			userOp, err := createSignedUserOp(chain, sender, salt, nextAccountNonce, 60_000, common.HexToAddress(targetAddr), nativeBalance, nil)
			if err != nil {
				fmt.Printf("Failed to create user op on %s: %v\n", chain.ID(), err)
			} else {
				userOps = append(userOps, userOp)
				nextAccountNonce = incrementNonce(nextAccountNonce, userOp.Nonce)
			}
		} else if targetChain.IsEVM() {
			// 2) Handle native balance from EVM to EVM
			uops, newNonce, err := handleNativeBridgeEVMtoEVM(chain, sender, targetAddr, targetChain, nativeBalance, salt, nextAccountNonce)
			if err != nil {
				fmt.Printf("Failed to handle native balance on %s: %v\n", chain.ID(), err)
			} else {
				userOps = append(userOps, uops...)
				nextAccountNonce = newNonce
			}
		} else if targetChain == domain.Solana {
			// 3) Handle native balance from EVM to Solana
			uops, newNonce, err := handleNativeBridgeEVMtoSolana(chain, sender, targetAddr, nativeBalance, salt, nextAccountNonce)
			if err != nil {
				fmt.Printf("Failed to handle native balance on %s: %v\n", chain.ID(), err)
			} else {
				userOps = append(userOps, uops...)
				nextAccountNonce = newNonce
			}
		} else {
			panic("unsupported chain")
		}
	}

	// 4) Handle ERC20 tokens from EVM to EVM
	tokenUserOps, _, err := collectAndHandleERC20Tokens(ctx, client, chain, sender, targetAddr, targetChain, salt, nextAccountNonce)
	if err != nil {
		fmt.Printf("Failed to handle ERC20 tokens on %s: %v\n", chain.ID(), err)
	} else {
		userOps = append(userOps, tokenUserOps...)
	}

	// 5) Submit userOps if any
	if len(userOps) == 0 {
		fmt.Printf("No user ops found on %s\n", chain.ID())
		return
	}
	entrypoint, _ := abibinding.NewEntrypoint(common.HexToAddress(Entrypoint), client.GetRawClient())
	txHash, err := submitUserOps(chain, entrypoint, userOps, privateKey)
	if err != nil {
		fmt.Printf("Failed to submit user op on %s: %v\n", chain.ID(), err)
		return
	}
	fmt.Printf("Transaction hash on %s: %s\n", chain.ID(), txHash)
}

// handleNativeBridgeEVMtoEVM is used for bridging native token from EVM to EVM
func handleNativeBridgeEVMtoEVM(
	chain domain.Chain,
	sender domain.EvmAddress,
	targetAddr string,
	targetChain domain.Chain,
	nativeBalance *big.Int,
	salt *big.Int,
	givenNonce *big.Int,
) ([]abibinding.PackedUserOperation, *big.Int, error) {
	if chain == targetChain {
		panic("invalid parameters")
	}

	var userOps []abibinding.PackedUserOperation
	var nextAccountNonce = givenNonce

	// 1) Get bridging quote & build bridging tx
	//    We try bridging the *entire* nativeBalance at first.
	txResp, gasLimit, bridgingValue, err := buildBridgeTxOKX(
		chain, sender,
		"******************************************", // fromToken = native
		targetChain,
		"******************************************", // toToken = native
		nativeBalance, // amount
		targetAddr,
	)
	if err != nil {
		return nil, nextAccountNonce, fmt.Errorf("failed to build bridging tx on %s: %v", chain.ID(), err)
	}

	// 2) Check if the bridging "value" is greater than the actual native balance in the account.
	//    If bridgingValue > nativeBalance, we reduce the bridging amount accordingly
	if bridgingValue.Cmp(nativeBalance) > 0 {
		// bridgingValue is bigger than the entire account's native balance
		diff := new(big.Int).Sub(bridgingValue, nativeBalance)
		reducedAmount := new(big.Int).Sub(nativeBalance, diff)
		if reducedAmount.Sign() < 1 {
			return userOps, nextAccountNonce, fmt.Errorf("cannot build bridging tx on %s: bridgingValue is too large, and no leftover for bridging", chain.ID())
		}

		// 2a) Re-call to build bridging tx with leftover
		txResp2, gasLimit2, bridgingValue2, err2 := buildBridgeTxOKX(
			chain, sender,
			"******************************************",
			targetChain,
			"******************************************",
			reducedAmount,
			targetAddr,
		)
		if err2 != nil {
			return userOps, nextAccountNonce, fmt.Errorf("failed to build bridging tx on %s with reduced amount: %v", chain.ID(), err2)
		}
		if bridgingValue2.Cmp(nativeBalance) > 0 {
			return nil, nextAccountNonce, fmt.Errorf("bridgingValue is still greater than nativeBalance on %s", chain.ID())
		}
		// Use the updated bridging data
		txResp = txResp2
		gasLimit = gasLimit2
		bridgingValue = bridgingValue2
	}
	fmt.Printf("Using bridge %s\n", txResp.Data[0].Router.BridgeName)

	// 3) Create userOp
	bridgingTo := common.HexToAddress(txResp.Data[0].Tx.To)
	bridgingData := mustDecodeHex(txResp.Data[0].Tx.Data)
	bridgingUop, err := createSignedUserOp(
		chain,
		sender,
		salt,
		nextAccountNonce,
		gasLimit*2, // doubling the on-chain estimate
		bridgingTo,
		bridgingValue, // bridgingValue is the 'value' in the transaction
		bridgingData,
	)
	if err != nil {
		return nil, nextAccountNonce, fmt.Errorf("failed to create bridging userOp: %v", err)
	}
	userOps = append(userOps, bridgingUop)
	nextAccountNonce = incrementNonce(nextAccountNonce, bridgingUop.Nonce)

	return userOps, nextAccountNonce, nil
}

// handleNativeBridgeEVMtoSolana is used for bridging native token from EVM to Solana
func handleNativeBridgeEVMtoSolana(
	chain domain.Chain,
	sender domain.EvmAddress,
	targetAddr string,
	nativeBalance *big.Int,
	salt *big.Int,
	givenNonce *big.Int,
) ([]abibinding.PackedUserOperation, *big.Int, error) {
	var userOps []abibinding.PackedUserOperation
	var nextAccountNonce = givenNonce

	quote, err := lifiClient.GetQuote(chain, sender.Hex(), "******************************************", nativeBalance.String(), domain.Solana, "SOL", targetAddr)
	if err != nil {
		return nil, nextAccountNonce, fmt.Errorf("failed to get quote on %s: %v", chain.ID(), err)
	}
	txTo := quote.TransactionRequest.To
	txData := mustDecodeHex(quote.TransactionRequest.Data)

	// calculate amount of native token needed
	value, ok := new(big.Int).SetString(quote.TransactionRequest.Value[2:], 16)
	if !ok {
		return nil, nextAccountNonce, fmt.Errorf("failed to parse value from quote: %s", quote.TransactionRequest.Value)
	}
	gasLimit, err := strconv.ParseUint(quote.TransactionRequest.GasLimit[2:], 16, 64)
	if err != nil {
		return nil, nextAccountNonce, fmt.Errorf("failed to parse gas limit from quote")
	}
	if value.Cmp(nativeBalance) > 0 {
		// If value is greater than nativeBalance, we need to reserve some native token for it to avoid tx revert
		diff := new(big.Int).Sub(value, nativeBalance)
		newBridgeAmount := new(big.Int).Sub(nativeBalance, diff)
		if newBridgeAmount.Sign() < 1 {
			return nil, nextAccountNonce, fmt.Errorf("cannot build bridging tx on %s: bridgingValue is too large, and no leftover for bridging", chain.ID())
		}
		quote, err = lifiClient.GetQuote(chain, sender.Hex(), "******************************************", newBridgeAmount.String(), domain.Solana, "SOL", targetAddr)
		if err != nil {
			return nil, nextAccountNonce, fmt.Errorf("failed to get quote on %s: %v", chain.ID(), err)
		}
		txTo = quote.TransactionRequest.To
		txData = mustDecodeHex(quote.TransactionRequest.Data)
		value, ok = new(big.Int).SetString(quote.TransactionRequest.Value[2:], 16)
		if !ok {
			return nil, nextAccountNonce, fmt.Errorf("failed to parse value from quote")
		}
		gasLimit, err = strconv.ParseUint(quote.TransactionRequest.GasLimit[2:], 16, 64)
		if err != nil {
			return nil, nextAccountNonce, fmt.Errorf("failed to parse gas limit from quote")
		}
	}

	// 3) Create userOp
	bridgingUop, err := createSignedUserOp(
		chain,
		sender,
		salt,
		nextAccountNonce,
		gasLimit*2, // doubling the on-chain estimate
		common.HexToAddress(txTo),
		value,
		txData,
	)
	if err != nil {
		return nil, nextAccountNonce, fmt.Errorf("failed to create bridging userOp: %v", err)
	}
	userOps = append(userOps, bridgingUop)
	nextAccountNonce = incrementNonce(nextAccountNonce, bridgingUop.Nonce)

	return userOps, nextAccountNonce, nil
}

// collectAndHandleERC20Tokens retrieves token logs for `sender` and then calls handleERC20TokenBalance.
func collectAndHandleERC20Tokens(
	ctx context.Context,
	client evm.IClient,
	chain domain.Chain,
	sender domain.EvmAddress,
	targetAddr string,
	targetChain domain.Chain,
	salt *big.Int,
	givenNonce *big.Int,
) ([]abibinding.PackedUserOperation, *big.Int, error) {

	var userOps []abibinding.PackedUserOperation
	var nextAccountNonce = givenNonce

	transferTopic := common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef")
	receiverTopic := common.HexToHash(sender.Hex())
	logs, err := client.GetRawClient().FilterLogs(ctx, ethereum.FilterQuery{
		Topics: [][]common.Hash{{transferTopic}, {}, {receiverTopic}},
	})
	if err != nil {
		return nil, nextAccountNonce, fmt.Errorf("failed to get token balances on %s: %v", chain.ID(), err)
	}

	// Get unique contract addresses from transfer logs
	contractAddresses := make(map[common.Address]bool)
	for _, log := range logs {
		contractAddresses[log.Address] = true
	}

	for contractAddress := range contractAddresses {
		tokenBalance, err := client.TokenBalance(ctx, sender, contractAddress.Hex())
		if err != nil {
			fmt.Printf("Failed to get token balance of %s on %s: %v\n", contractAddress.Hex(), chain.ID(), err)
			continue
		}
		if tokenBalance.Sign() == 0 {
			continue
		}

		// 3) For each token, handle it
		if targetChain.IsEVM() {
			uops, newNonce, err := handleERC20TokenToEVM(chain, sender, targetAddr, targetChain, contractAddress, tokenBalance, salt, nextAccountNonce)
			if err != nil {
				fmt.Printf("handleERC20TokenBalance failed on %s for token %s: %v\n", chain.ID(), contractAddress.Hex(), err)
				continue
			}
			userOps = append(userOps, uops...)
			nextAccountNonce = newNonce
		} else if targetChain == domain.Solana {
			uops, newNonce, err := handleERC20TokenToSolana(chain, sender, targetAddr, contractAddress, tokenBalance, salt, nextAccountNonce)
			if err != nil {
				fmt.Printf("handleERC20TokenBalance failed on %s for token %s: %v\n", chain.ID(), contractAddress.Hex(), err)
				continue
			}
			userOps = append(userOps, uops...)
			nextAccountNonce = newNonce
		} else {
			panic("unsupported chain")
		}
	}
	return userOps, nextAccountNonce, nil
}

// handleERC20TokenToEVM deals with bridging or swapping for non-native tokens.
func handleERC20TokenToEVM(
	chain domain.Chain,
	sender domain.EvmAddress,
	targetAddr string,
	targetChain domain.Chain,
	contractAddress common.Address,
	tokenBalance *big.Int,
	salt *big.Int,
	givenNonce *big.Int,
) ([]abibinding.PackedUserOperation, *big.Int, error) {

	var userOps []abibinding.PackedUserOperation
	var nextAccountNonce = givenNonce

	// 1) If on the same chain, approve + swap to native token
	if chain == targetChain {
		// Approve
		approveUop, newNonce, err := approveTokenOKX(chain, sender, contractAddress, tokenBalance, salt, nextAccountNonce)
		if err != nil {
			return nil, nextAccountNonce, err
		}
		userOps = append(userOps, approveUop)
		nextAccountNonce = newNonce

		// Swap
		swapUop, newNonce, err := swapTokenOKX(chain, sender, contractAddress, EVMNativeToken, tokenBalance, targetAddr, salt, nextAccountNonce)
		if err != nil {
			return nil, nextAccountNonce, err
		}
		userOps = append(userOps, swapUop)
		nextAccountNonce = newNonce

		return userOps, nextAccountNonce, nil
	}

	// 2) On other chain => bridging flow
	// Approve -> swap to native or bridging in multiple steps
	uops, newNonce, err := handleCrossChainTokenBridging(chain, sender, contractAddress, tokenBalance, targetAddr, targetChain, salt, nextAccountNonce)
	if err != nil {
		return nil, nextAccountNonce, err
	}
	userOps = append(userOps, uops...)
	nextAccountNonce = newNonce

	return userOps, nextAccountNonce, nil
}

// handleERC20TokenToEVM deals with bridging or swapping for non-native tokens.
func handleERC20TokenToSolana(
	chain domain.Chain,
	sender domain.EvmAddress,
	targetAddr string,
	contractAddress common.Address,
	tokenBalance *big.Int,
	salt *big.Int,
	givenNonce *big.Int,
) ([]abibinding.PackedUserOperation, *big.Int, error) {
	var userOps []abibinding.PackedUserOperation
	var nextAccountNonce = givenNonce

	quote, err := lifiClient.GetQuote(chain, sender.Hex(), contractAddress.String(), tokenBalance.String(), domain.Solana, "SOL", targetAddr)
	if err != nil {
		return nil, nextAccountNonce, fmt.Errorf("failed to get quote on %s: %v", chain.ID(), err)
	}

	// 1) Pack approve call data and create approve token userOp
	parsedAbi, err := abi.JSON(strings.NewReader(ERC20ABI))
	if err != nil {
		return nil, givenNonce, fmt.Errorf("failed to parse ERC20 ABI: %v", err)
	}
	approveData, err := parsedAbi.Pack("approve", common.HexToAddress(quote.Estimate.ApprovalAddress), tokenBalance)
	if err != nil {
		return nil, givenNonce, fmt.Errorf("failed to pack approve data: %v", err)
	}
	userOp, err := createSignedUserOp(chain, sender, salt, givenNonce, 200_000, contractAddress, big.NewInt(0), approveData)
	if err != nil {
		return nil, givenNonce, err
	}
	userOps = append(userOps, userOp)
	nextAccountNonce = incrementNonce(givenNonce, userOp.Nonce)

	// 2) Create bridging userOp
	txTo := quote.TransactionRequest.To
	txData := mustDecodeHex(quote.TransactionRequest.Data)
	value, ok := new(big.Int).SetString(quote.TransactionRequest.Value[2:], 16)
	if !ok {
		return nil, nextAccountNonce, fmt.Errorf("failed to parse value from quote: %s", quote.TransactionRequest.Value)
	}
	gasLimit, err := strconv.ParseUint(quote.TransactionRequest.GasLimit[2:], 16, 64)
	if err != nil {
		return nil, nextAccountNonce, fmt.Errorf("failed to parse gas limit from quote")
	}

	bridgingUop, err := createSignedUserOp(
		chain,
		sender,
		salt,
		nextAccountNonce,
		gasLimit*2, // doubling the on-chain estimate
		common.HexToAddress(txTo),
		value,
		txData,
	)
	if err != nil {
		return nil, nextAccountNonce, fmt.Errorf("failed to create bridging userOp: %v", err)
	}
	userOps = append(userOps, bridgingUop)
	nextAccountNonce = incrementNonce(nextAccountNonce, bridgingUop.Nonce)

	return userOps, nextAccountNonce, nil
}

// buildSwapTxOKX builds the swap tx for your native->token or token->token scenario
func buildSwapTxOKX(
	chain domain.Chain,
	sender domain.EvmAddress,
	fromToken string,
	toToken string,
	amount *big.Int,
	targetAddr string,
) (common.Address, *big.Int, []byte, uint64, error) {

	resp, err := okxClient.GetBuildSwapTx(map[string]string{
		"chainId":             strconv.Itoa(int(chain.Number())),
		"fromTokenAddress":    fromToken,
		"toTokenAddress":      toToken,
		"amount":              amount.String(),
		"userWalletAddress":   sender.Hex(),
		"swapReceiverAddress": targetAddr,
	})
	if err != nil {
		return common.Address{}, nil, nil, 0, err
	}

	gasLimit, err := strconv.ParseUint(resp.Data[0].Tx.Gas, 10, 64)
	if err != nil {
		return common.Address{}, nil, nil, 0, err
	}
	toAddress := common.HexToAddress(resp.Data[0].Tx.To)
	value, ok := new(big.Int).SetString(resp.Data[0].Tx.Value, 10)
	if !ok {
		return common.Address{}, nil, nil, 0, fmt.Errorf("failed to parse value")
	}
	data, err := hex.DecodeString(resp.Data[0].Tx.Data[2:])
	if err != nil {
		return common.Address{}, nil, nil, 0, err
	}

	return toAddress, value, data, gasLimit, nil
}

// buildBridgeTxOKX builds the bridging tx for native/erc20 bridging
func buildBridgeTxOKX(
	chain domain.Chain,
	sender domain.EvmAddress,
	fromToken string,
	toChain domain.Chain,
	toToken string,
	amount *big.Int,
	targetAddr string,
) (*APIResponse[BuildTxResponse], uint64, *big.Int, error) {

	quoteResp, err := okxClient.GetQuoteBridge(map[string]string{
		"amount":           amount.String(),
		"fromTokenAddress": fromToken,
		"fromChainId":      strconv.Itoa(int(chain.Number())),
		"toTokenAddress":   toToken,
		"toChainId":        strconv.Itoa(int(toChain.Number())),
	})
	if err != nil {
		return nil, 0, nil, err
	}
	bridgeId := quoteResp.Data[0].RouterList[0].Router.BridgeID
	fmt.Printf("Got bridge %s, estimate time: %s\n", quoteResp.Data[0].RouterList[0].Router.BridgeName, quoteResp.Data[0].RouterList[0].EstimateTime)

	// Build bridging tx
	txResp, err := okxClient.GetBuildBridgeTx(map[string]string{
		"amount":            amount.String(),
		"fromTokenAddress":  fromToken,
		"fromChainId":       strconv.Itoa(int(chain.Number())),
		"toTokenAddress":    toToken,
		"toChainId":         strconv.Itoa(int(toChain.Number())),
		"bridgeId":          strconv.Itoa(bridgeId),
		"userWalletAddress": sender.Hex(),
		"receiveAddress":    targetAddr,
	})
	if err != nil {
		return nil, 0, nil, err
	}

	// parse results
	value, ok := new(big.Int).SetString(txResp.Data[0].Tx.Value, 10)
	if !ok {
		return nil, 0, nil, fmt.Errorf("failed to parse value from bridging tx")
	}
	gasLimit, err := strconv.ParseUint(txResp.Data[0].Tx.GasLimit, 10, 64)
	if err != nil {
		return nil, 0, nil, err
	}

	return txResp, gasLimit, value, nil
}

// approveTokenOKX builds and returns the userOp that approves full tokenBalance to OKX router
func approveTokenOKX(
	chain domain.Chain,
	sender domain.EvmAddress,
	contractAddress common.Address,
	tokenBalance *big.Int,
	salt *big.Int,
	givenNonce *big.Int,
) (abibinding.PackedUserOperation, *big.Int, error) {

	approveResp, err := okxClient.GetApproveTransaction(chain, contractAddress.Hex(), tokenBalance.String())
	if err != nil {
		return abibinding.PackedUserOperation{}, givenNonce, err
	}
	approveData, err := hex.DecodeString(approveResp.Data[0].Data[2:])
	if err != nil {
		return abibinding.PackedUserOperation{}, givenNonce, err
	}

	userOp, err := createSignedUserOp(chain, sender, salt, givenNonce, 200_000, contractAddress, big.NewInt(0), approveData)
	if err != nil {
		return abibinding.PackedUserOperation{}, givenNonce, err
	}
	return userOp, incrementNonce(givenNonce, userOp.Nonce), nil
}

// swapTokenOKX builds and returns the userOp that swaps token->token
func swapTokenOKX(
	chain domain.Chain,
	sender domain.EvmAddress,
	fromToken common.Address,
	toToken string,
	amount *big.Int,
	targetAddr string,
	salt *big.Int,
	givenNonce *big.Int,
) (abibinding.PackedUserOperation, *big.Int, error) {

	toAddress, value, data, gasLimit, err := buildSwapTxOKX(
		chain, sender, fromToken.Hex(), toToken, amount, targetAddr,
	)
	if err != nil {
		return abibinding.PackedUserOperation{}, givenNonce, err
	}

	userOp, err := createSignedUserOp(chain, sender, salt, givenNonce, gasLimit*2, toAddress, value, data)
	if err != nil {
		return abibinding.PackedUserOperation{}, givenNonce, err
	}
	return userOp, incrementNonce(givenNonce, userOp.Nonce), nil
}

func handleCrossChainTokenBridging(
	chain domain.Chain,
	sender domain.EvmAddress,
	contractAddress common.Address,
	tokenBalance *big.Int,
	targetAddr string,
	targetChain domain.Chain,
	salt *big.Int,
	givenNonce *big.Int,
) ([]abibinding.PackedUserOperation, *big.Int, error) {

	var (
		userOps          []abibinding.PackedUserOperation
		nextAccountNonce = givenNonce
	)

	// 1) Approve the entire tokenBalance so that OKX's router can swap/bridge it.
	approveUop, newNonce, err := approveTokenOKX(chain, sender, contractAddress, tokenBalance, salt, nextAccountNonce)
	if err != nil {
		return nil, nextAccountNonce, fmt.Errorf("failed to build approve userOp: %v", err)
	}
	userOps = append(userOps, approveUop)
	nextAccountNonce = newNonce

	// 2) Get a bridging quote from OKX to see bridging routes, fees, etc.
	quoteResp, err := okxClient.GetQuoteBridge(map[string]string{
		"amount":           tokenBalance.String(),
		"fromTokenAddress": contractAddress.Hex(),
		"fromChainId":      strconv.Itoa(int(chain.Number())),
		"toTokenAddress":   EVMNativeToken,
		"toChainId":        strconv.Itoa(int(targetChain.Number())),
	})
	if err != nil {
		return userOps, nextAccountNonce, fmt.Errorf("failed to get bridging quote on %s: %v", chain.ID(), err)
	}
	fmt.Printf("Got bridge %s, estimate time: %s\n", quoteResp.Data[0].RouterList[0].Router.BridgeName, quoteResp.Data[0].RouterList[0].EstimateTime)
	bridgeId := quoteResp.Data[0].RouterList[0].Router.BridgeID

	// 3) Build the bridging tx for the full tokenBalance (we’ll possibly adjust if bridging cost > 0)
	txResp, err := okxClient.GetBuildBridgeTx(map[string]string{
		"amount":            tokenBalance.String(),
		"fromTokenAddress":  contractAddress.Hex(),
		"fromChainId":       strconv.Itoa(int(chain.Number())),
		"toTokenAddress":    EVMNativeToken,
		"toChainId":         strconv.Itoa(int(targetChain.Number())),
		"bridgeId":          strconv.Itoa(bridgeId),
		"userWalletAddress": sender.Hex(),
		"receiveAddress":    targetAddr,
	})
	if err != nil {
		return userOps, nextAccountNonce, fmt.Errorf("failed to build bridging tx on %s: %v", chain.ID(), err)
	}

	// The bridging transaction might require some native gas (in addition to normal fees)
	// so sometimes you need to do a partial swap of your ERC20 -> native to cover bridging if you're short on gas.
	// We'll show how to do that by building a swap transaction for the portion of the token that’s needed.

	// For demonstration: get how many native tokens you'd get by swapping the full `tokenBalance`.
	swapQuoteResp, err := okxClient.GetQuoteSwap(map[string]string{
		"chainId":           strconv.Itoa(int(chain.Number())),
		"fromTokenAddress":  contractAddress.Hex(),
		"amount":            tokenBalance.String(),
		"toTokenAddress":    "******************************************", // native
		"userWalletAddress": sender.Hex(),
	})
	if err != nil {
		return userOps, nextAccountNonce, fmt.Errorf("failed to get swap quote on %s: %v", chain.ID(), err)
	}

	// We won't do a full swap, only partial if needed. For example, bridging might require some native fees.
	fullToTokenAmount, _ := new(big.Int).SetString(swapQuoteResp.Data[0].ToTokenAmount, 10)

	// For simplicity, let's assume you want to swap enough to get bridging cost covered.
	// The bridging transaction has "value" that might need coverage. Let's parse it:
	bridgingValue, ok := new(big.Int).SetString(txResp.Data[0].Tx.Value, 10)
	if !ok {
		return userOps, nextAccountNonce, fmt.Errorf("failed to parse bridging tx value")
	}

	// If bridgingValue is > 0, you might need bridgingValue in native tokens.
	// Let's find out how many tokens that bridgingValue corresponds to.
	// ratio = bridgingValue / fullToTokenAmount * tokenBalance
	// because bridgingValue is how many native tokens you need to produce from your ERC20,
	// out of the fullToTokenAmount you could get if you swapped everything.
	tokenToSwapAmount := decimal.
		NewFromBigInt(bridgingValue, 0).
		Div(decimal.NewFromBigInt(fullToTokenAmount, 0)). // bridgingValue / fullToTokenAmount
		Mul(decimal.NewFromBigInt(tokenBalance, 0)).      // * tokenBalance
		Mul(decimal.NewFromFloat(1.05)).                  // add some buffer
		BigInt()

	if tokenToSwapAmount.Cmp(tokenBalance) > 0 {
		return userOps, nextAccountNonce, fmt.Errorf("tokenToSwapAmount is greater than tokenBalance")
	}

	// 4) Build userOp for the partial swap (token -> native) to get bridgingValue
	if tokenToSwapAmount.Sign() > 0 {
		swapUop, newNonce, err := swapTokenOKX(
			chain,
			sender,
			contractAddress, // fromToken
			"******************************************", // toToken (native)
			tokenToSwapAmount,
			sender.Hex(), // We want the native coins to come back to the same sender for bridging
			salt,
			nextAccountNonce,
		)
		if err != nil {
			return userOps, nextAccountNonce, fmt.Errorf("failed to create partial swap userOp: %v", err)
		}
		userOps = append(userOps, swapUop)
		nextAccountNonce = newNonce
	}

	// 5) Now build bridging userOp with the leftover tokens (tokenBalance - tokenToSwapAmount).
	leftoverTokens := new(big.Int).Sub(tokenBalance, tokenToSwapAmount)

	if leftoverTokens.Sign() > 0 {
		// Re-build bridging tx for leftoverTokens
		txResp, err = okxClient.GetBuildBridgeTx(map[string]string{
			"amount":            leftoverTokens.String(),
			"fromTokenAddress":  contractAddress.Hex(),
			"fromChainId":       strconv.Itoa(int(chain.Number())),
			"toTokenAddress":    EVMNativeToken,
			"toChainId":         strconv.Itoa(int(targetChain.Number())),
			"bridgeId":          strconv.Itoa(bridgeId),
			"userWalletAddress": sender.Hex(),
			"receiveAddress":    targetAddr,
		})
		if err != nil {
			return userOps, nextAccountNonce, fmt.Errorf("failed to build bridging tx for leftover tokens on %s: %v", chain.ID(), err)
		}

		// Parse bridging tx data
		bridgingValue, ok := new(big.Int).SetString(txResp.Data[0].Tx.Value, 10)
		if !ok {
			return userOps, nextAccountNonce, fmt.Errorf("failed to parse bridging value on leftover bridging tx")
		}
		bridgingGasLimit, err := strconv.ParseUint(txResp.Data[0].Tx.GasLimit, 10, 64)
		if err != nil {
			return userOps, nextAccountNonce, fmt.Errorf("failed to parse bridging gas limit: %v", err)
		}
		bridgingData := mustDecodeHex(txResp.Data[0].Tx.Data)
		bridgingTo := common.HexToAddress(txResp.Data[0].Tx.To)

		// Build bridging userOp
		bridgingUop, err := createSignedUserOp(
			chain,
			sender,
			salt,
			nextAccountNonce,
			bridgingGasLimit*2,
			bridgingTo,
			bridgingValue,
			bridgingData,
		)
		if err != nil {
			return userOps, nextAccountNonce, fmt.Errorf("failed to create bridging userOp: %v", err)
		}
		userOps = append(userOps, bridgingUop)
		nextAccountNonce = incrementNonce(nextAccountNonce, bridgingUop.Nonce)
	}

	return userOps, nextAccountNonce, nil
}

// incrementNonce is a small helper to handle nil or existing nextAccountNonce
func incrementNonce(currentNonce, usedNonce *big.Int) *big.Int {
	if currentNonce == nil {
		return new(big.Int).Add(usedNonce, big.NewInt(1))
	}
	return new(big.Int).Add(currentNonce, big.NewInt(1))
}

func mustDecodeHex(input string) []byte {
	data, _ := hex.DecodeString(input[2:])
	return data
}

func createSignedUserOp(chain domain.Chain, sender domain.EvmAddress, senderSalt *big.Int, givenNonce *big.Int, callGasLimit uint64, target common.Address, value *big.Int, data []byte) (abibinding.PackedUserOperation, error) {
	fmt.Printf("Creating user op on %s with sender: %s, senderSalt: %s, givenNonce: %s, callGasLimit: %d, target: %s, value: %s, data: %s\n", chain.ID(), sender.Hex(), senderSalt.String(), givenNonce.String(), callGasLimit, target.Hex(), value.String(), hex.EncodeToString(data))

	client := evm.GetClient(chain)
	ctx := context.Background()

	entrypoint, err := abibinding.NewEntrypoint(common.HexToAddress(Entrypoint), client.GetRawClient())
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to create entrypoint contract: %v", err)
	}

	var nonce *big.Int
	if givenNonce != nil {
		nonce = givenNonce
	} else {
		var err error
		nonce, err = entrypoint.GetNonce(nil, sender.Address, big.NewInt(int64(0)))
		if err != nil {
			return abibinding.PackedUserOperation{}, fmt.Errorf("failed to get nonce: %v", err)
		}
	}

	initCode := []byte{}
	codeSize, err := client.GetCodeSize(ctx, sender)
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to get code size: %v", err)
	}
	if codeSize == 0 && (givenNonce == nil || givenNonce.Cmp(big.NewInt(0)) == 0) {
		initCode = append(initCode, AccountFactoryAddress.Bytes()...)
		contractABI, err := abi.JSON(strings.NewReader(SimpleAccountFactoryABI))
		if err != nil {
			return abibinding.PackedUserOperation{}, fmt.Errorf("failed to parse factory ABI: %v", err)
		}

		calldata, err := contractABI.Pack("createAccount", ownerAddress, senderSalt)
		if err != nil {
			return abibinding.PackedUserOperation{}, fmt.Errorf("failed to pack factory function call: %v", err)
		}
		initCode = append(initCode, calldata...)
	}

	contractABI, err := abi.JSON(strings.NewReader(SimpleAccountABI))
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to parse account ABI: %v", err)
	}
	calldata, err := contractABI.Pack("execute", target, value, data)
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to pack execute function call: %v", err)
	}

	paymasterValidationGasLimit := big.NewInt(150000)
	postOpGasLimit := big.NewInt(35000)
	paymasterAndData, paymasterData, err := createPaymasterData(paymasterValidationGasLimit, postOpGasLimit)
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to create paymaster data: %v", err)
	}

	gasPrice, err := client.GetRawClient().SuggestGasPrice(ctx)
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to get gas price: %v", err)
	}

	userOp := abibinding.PackedUserOperation{
		Sender:             sender.Address,
		Nonce:              nonce,
		InitCode:           initCode,
		CallData:           calldata,
		AccountGasLimits:   packGasLimits(400_000, callGasLimit),
		PreVerificationGas: big.NewInt(35000),
		GasFees:            packGasFees(gasPrice, gasPrice),
		PaymasterAndData:   paymasterAndData,
		Signature:          []byte{},
	}

	paymaster, err := abibinding.NewPaymaster(PaymasterAddress, client.GetRawClient())
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to create paymaster contract: %v", err)
	}

	paymentHashBytes, err := paymaster.GetHash(nil, userOp, *paymasterData, paymasterValidationGasLimit, postOpGasLimit)
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to get paymaster hash: %v", err)
	}

	evmWallet := domain.NewEvmWallet("", privateKey)
	paymasterSig, err := evmWallet.SignMessage(paymentHashBytes[:])
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to sign paymaster hash: %v", err)
	}

	paymasterSigBytes, err := hex.DecodeString(paymasterSig)
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to decode paymaster signature: %v", err)
	}

	copy(userOp.PaymasterAndData[161:161+65], paymasterSigBytes)

	userOpHashBytes, err := entrypoint.GetUserOpHash(nil, userOp)
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to get user op hash: %v", err)
	}

	userOpSig, err := evmWallet.SignMessage(userOpHashBytes[:])
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to sign user op hash: %v", err)
	}

	userOpSigBytes, err := hex.DecodeString(userOpSig)
	if err != nil {
		return abibinding.PackedUserOperation{}, fmt.Errorf("failed to decode user op signature: %v", err)
	}
	userOp.Signature = userOpSigBytes

	return userOp, nil
}

func getCounterfactualAddress(chain domain.Chain, factoryAddress, ownerAddress domain.EvmAddress, salt *big.Int) (domain.EvmAddress, error) {
	if !chain.IsEVM() {
		return domain.NewEvmAddress(""), fmt.Errorf("chain is not evm but %s", chain.ID())
	}
	client := evm.GetClient(chain)

	factory, err := abibinding.NewSimpleaccountfactory(factoryAddress.Address, client.GetRawClient())
	if err != nil {
		return domain.NewEvmAddress(""), err
	}
	addr, err := factory.GetAddress(nil, ownerAddress.Address, salt)
	if err != nil {
		return domain.NewEvmAddress(""), err
	}
	return domain.EvmAddress{Address: addr}, nil
}

func createPaymasterData(paymasterValidationGasLimit, postOpGasLimit *big.Int) ([]byte, *abibinding.VerifyingPaymasterPaymasterData, error) {
	now := uint64(time.Now().Unix())
	paymasterData := &abibinding.VerifyingPaymasterPaymasterData{
		ValidUntil:         big.NewInt(int64(now + 3600)), // 1 hour from now
		ValidAfter:         big.NewInt(int64(now)),
		SponsorUUID:        big.NewInt(int64(1)),
		AllowAnyBundler:    true,
		PrecheckBalance:    false,
		PrepaymentRequired: false,
		Token:              common.Address{},
		Receiver:           common.Address{},
		ExchangeRate:       big.NewInt(0), // Free sponsorship
		PostOpGas:          big.NewInt(0), // Only used for token transfer from AA wallet
	}

	// Create paymaster data
	// Format: address(20) + validationGas(16) + postOpGas(16) + paymasterData(109) + signature(65)
	// Paymaster data: validUntil(6) + validAfter(6) + sponsorUUID(16) + flags(3) + token(20) + receiver(20) + exchangeRate(32) + postOpGas(6)
	paymasterAndData := make([]byte, 20+16+16+109+65)
	copy(paymasterAndData[:20], PaymasterAddress.Bytes())
	validationGasBytes := make([]byte, 16)
	binary.BigEndian.PutUint64(validationGasBytes[8:], paymasterValidationGasLimit.Uint64())
	copy(paymasterAndData[20:36], validationGasBytes)
	postOpGasBytes := make([]byte, 16)
	binary.BigEndian.PutUint64(postOpGasBytes[8:], postOpGasLimit.Uint64())
	copy(paymasterAndData[36:52], postOpGasBytes)

	// Paymaster data starts at 52
	offset := 52

	// Copy validUntil (6 bytes)
	validUntilBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(validUntilBytes, paymasterData.ValidUntil.Uint64())
	copy(paymasterAndData[offset:offset+6], validUntilBytes[2:])
	offset += 6

	// Copy validAfter (6 bytes)
	validAfterBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(validAfterBytes, paymasterData.ValidAfter.Uint64())
	copy(paymasterAndData[offset:offset+6], validAfterBytes[2:])
	offset += 6

	// Copy sponsorUUID (16 bytes)
	sponsorUUIDBytes := make([]byte, 16)
	binary.BigEndian.PutUint64(sponsorUUIDBytes[8:], paymasterData.SponsorUUID.Uint64())
	copy(paymasterAndData[offset:offset+16], sponsorUUIDBytes)
	offset += 16

	// Copy boolean flags (3 bytes)
	flags := []byte{
		boolToByte(paymasterData.AllowAnyBundler),
		boolToByte(paymasterData.PrecheckBalance),
		boolToByte(paymasterData.PrepaymentRequired),
	}
	copy(paymasterAndData[offset:offset+3], flags)
	offset += 3

	// Copy token address (20 bytes)
	copy(paymasterAndData[offset:offset+20], paymasterData.Token.Bytes())
	offset += 20

	// Copy receiver address (20 bytes)
	copy(paymasterAndData[offset:offset+20], paymasterData.Receiver.Bytes())
	offset += 20

	// Copy exchange rate (32 bytes)
	exchangeRateBytes := make([]byte, 32)
	paymasterData.ExchangeRate.FillBytes(exchangeRateBytes)
	copy(paymasterAndData[offset:offset+32], exchangeRateBytes)
	offset += 32

	// Copy postOpGas (6 bytes)
	postOpGasBytes = make([]byte, 8)
	binary.BigEndian.PutUint64(postOpGasBytes, paymasterData.PostOpGas.Uint64())
	copy(paymasterAndData[offset:offset+6], postOpGasBytes[2:])

	return paymasterAndData, paymasterData, nil
}

// Helper function to convert bool to byte
func boolToByte(b bool) byte {
	if b {
		return 1
	}
	return 0
}

func packGasLimits(validateGasLimit, callGasLimit uint64) [32]byte {
	var packed [32]byte

	// Pack the values into a bytes32 format
	validateGasBytes := new(big.Int).SetUint64(validateGasLimit).Bytes()
	callGasBytes := new(big.Int).SetUint64(callGasLimit).Bytes()

	// Copy with proper padding
	copy(packed[16-len(validateGasBytes):16], validateGasBytes)
	copy(packed[32-len(callGasBytes):], callGasBytes)
	return packed
}

// Helper function to pack gas fees with validation
func packGasFees(maxPriorityFeePerGas, maxFeePerGas *big.Int) [32]byte {
	var packed [32]byte
	// Ensure priority fee doesn't exceed max fee
	if maxPriorityFeePerGas.Cmp(maxFeePerGas) > 0 {
		maxPriorityFeePerGas = maxFeePerGas
	}

	// Pack the values into a bytes32 format
	maxFeeBytes := maxFeePerGas.Bytes()
	priorityFeeBytes := maxPriorityFeePerGas.Bytes()

	// Copy with proper padding
	copy(packed[16-len(priorityFeeBytes):16], priorityFeeBytes)
	copy(packed[32-len(maxFeeBytes):], maxFeeBytes)
	return packed
}

func submitUserOps(chain domain.Chain, entrypoint *abibinding.Entrypoint, userOps []abibinding.PackedUserOperation, privateKey *ecdsa.PrivateKey) (string, error) {
	ownerAddress := crypto.PubkeyToAddress(privateKey.PublicKey)
	totalGasLimit := uint64(300_000)
	for _, userOp := range userOps {
		validateGasLimit := binary.BigEndian.Uint64(userOp.AccountGasLimits[16-8 : 16])
		callGasLimit := binary.BigEndian.Uint64(userOp.AccountGasLimits[32-8:])
		totalGasLimit += validateGasLimit + callGasLimit
	}
	gasPrice, err := evm.GetClient(chain).GetRawClient().SuggestGasPrice(context.Background())
	if err != nil {
		return "", fmt.Errorf("failed to get gas price: %v", err)
	}
	if chain == domain.Polygon {
		gasPrice = new(big.Int).Add(gasPrice, big.NewInt(300_000_000_000))
	}
	if chain == domain.Arbitrum {
		totalGasLimit = totalGasLimit * 3 // workaround for arbitrum gas limit
	}
	opts := &bind.TransactOpts{
		GasFeeCap: gasPrice,
		GasLimit:  totalGasLimit,
		From:      ownerAddress,
		Signer: func(address common.Address, tx *types.Transaction) (*types.Transaction, error) {
			return types.SignTx(tx, types.NewLondonSigner(big.NewInt(chain.Number())), privateKey)
		},
	}
	if chain == domain.Polygon {
		opts.GasFeeCap = big.NewInt(300_000_000_000)
	}

	tx, err := entrypoint.HandleOps(opts, userOps, ownerAddress)
	if err != nil {
		return "", fmt.Errorf("failed to execute handleOps: %w", err)
	}

	return tx.Hash().String(), nil
}
