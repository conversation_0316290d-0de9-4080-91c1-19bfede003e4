package main

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
)

const (
	apiBaseURL            = "https://www.okx.com/api/v5/dex"
	okxNativeTokenAddress = "******************************************"
)

var ChainNameMapForToken = map[int]string{
	42161: "arb",
	137:   "matic",
	195:   "tron",
	56:    "bsc",
	1:     "eth",
}

var ChainNames = map[string]interface{}{
	"arb":   nil,
	"matic": nil,
	"tron":  nil,
	"bsc":   nil,
	"eth":   nil,
}

type Token struct {
	Decimals             int    `json:"decimals"`
	TokenContractAddress string `json:"tokenContractAddress"`
	TokenSymbol          string `json:"tokenSymbol"`
}

type DexRouterList struct {
	// Add dex router fields as needed
}

type QuoteCompareList struct {
	// Add quote compare fields as needed
}

type RouterResult struct {
	ChainID          string             `json:"chainId"`
	DexRouterList    []DexRouterList    `json:"dexRouterList"`
	EstimateGasFee   string             `json:"estimateGasFee"`
	FromToken        Token              `json:"fromToken"`
	FromTokenAmount  string             `json:"fromTokenAmount"`
	QuoteCompareList []QuoteCompareList `json:"quoteCompareList"`
	ToToken          Token              `json:"toToken"`
	ToTokenAmount    string             `json:"toTokenAmount"`
}

type Client struct {
	httpClient *resty.Client
	accessKey  string
	secretKey  string
	passphrase string
}

// Response types matching TypeScript interfaces
type ApproveTransactionResponse struct {
	Data               string `json:"data"`
	DexContractAddress string `json:"dexContractAddress"`
	GasLimit           string `json:"gasLimit"`
	GasPrice           string `json:"gasPrice"`
}

type BuildTxResponse struct {
	FromTokenAmount string `json:"fromTokenAmount"`
	MinimumReceived string `json:"minimumReceived"`
	Router          Router `json:"router"`
	ToTokenAmount   string `json:"toTokenAmount"`
	Tx              Tx     `json:"tx"`
}

type Router struct {
	BridgeID                  int    `json:"bridgeId"`
	BridgeName                string `json:"bridgeName"`
	CrossChainFee             string `json:"crossChainFee"`
	CrossChainFeeTokenAddress string `json:"crossChainFeeTokenAddress"`
	OtherNativeFee            string `json:"otherNativeFee"`
}

type Tx struct {
	Data                 string `json:"data"`
	From                 string `json:"from"`
	Gas                  string `json:"gas"`
	GasLimit             string `json:"gasLimit"`
	GasPrice             string `json:"gasPrice"`
	MaxPriorityFeePerGas string `json:"maxPriorityFeePerGas"`
	RandomKeyAccount     []any  `json:"randomKeyAccount"`
	To                   string `json:"to"`
	Value                string `json:"value"`
}

type SwapDataResponse struct {
	RouterResult RouterResult `json:"routerResult"`
	Tx           Tx           `json:"tx"`
}

type APIResponse[T any] struct {
	Code string `json:"code"`
	Data []T    `json:"data"`
	Msg  string `json:"msg"`
}

// NewClient creates a new OKX API client
func NewClient(accessKey, secretKey, passphrase string) *Client {
	return &Client{
		httpClient: resty.New(),
		accessKey:  accessKey,
		secretKey:  secretKey,
		passphrase: passphrase,
	}
}

func (c *Client) getHeaders(pathWithParams, method string) map[string]string {
	timestamp := time.Now().UTC().Format("2006-01-02T15:04:05.000Z07:00")
	message := timestamp + method + pathWithParams[19:] // Remove apiBaseURL from path

	h := hmac.New(sha256.New, []byte(c.secretKey))
	h.Write([]byte(message))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return map[string]string{
		"Content-Type":         "application/json",
		"OK-ACCESS-KEY":        c.accessKey,
		"OK-ACCESS-SIGN":       signature,
		"OK-ACCESS-TIMESTAMP":  timestamp,
		"OK-ACCESS-PASSPHRASE": c.passphrase,
	}
}

// GetApproveTransaction gets approval transaction data
func (c *Client) GetApproveTransaction(chain domain.Chain, tokenContractAddress string, approveAmount string) (*APIResponse[ApproveTransactionResponse], error) {
	params := url.Values{
		"chainId":              []string{strconv.Itoa(int(chain.Number()))},
		"tokenContractAddress": []string{tokenContractAddress},
		"approveAmount":        []string{approveAmount},
	}
	pathWithParams := fmt.Sprintf("%s/aggregator/approve-transaction?%s", apiBaseURL, url.Values(params).Encode())

	resp := &APIResponse[ApproveTransactionResponse]{}

	_, err := c.httpClient.R().
		SetHeaders(c.getHeaders(pathWithParams, "GET")).
		SetResult(resp).
		Get(pathWithParams)

	return resp, err
}

// GetBuildSwapTx gets swap transaction data
func (c *Client) GetBuildSwapTx(params map[string]string) (*APIResponse[SwapDataResponse], error) {
	// Handle native token address conversion
	if _, exists := ChainNames[params["fromTokenAddress"]]; exists {
		params["fromTokenAddress"] = okxNativeTokenAddress
	}
	if _, exists := ChainNames[params["toTokenAddress"]]; exists {
		params["toTokenAddress"] = okxNativeTokenAddress
	}

	params["slippage"] = "0.03"
	urlParams := url.Values{}
	for k, v := range params {
		urlParams.Add(k, v)
	}
	pathWithParams := fmt.Sprintf("%s/aggregator/swap?%s", apiBaseURL, urlParams.Encode())

	resp := &APIResponse[SwapDataResponse]{}

	_, err := c.httpClient.R().
		SetHeaders(c.getHeaders(pathWithParams, "GET")).
		SetResult(resp).
		Get(pathWithParams)

	// fmt.Printf("GetBuildSwapTx response: %s\n", r.String())
	return resp, err
}

// GetBuildBridgeTx gets cross-chain bridge transaction data
func (c *Client) GetBuildBridgeTx(params map[string]string) (*APIResponse[BuildTxResponse], error) {
	// Handle native token address conversion
	if _, exists := ChainNames[params["fromTokenAddress"]]; exists {
		params["fromTokenAddress"] = okxNativeTokenAddress
	}
	if _, exists := ChainNames[params["toTokenAddress"]]; exists {
		params["toTokenAddress"] = okxNativeTokenAddress
	}
	params["slippage"] = "0.03"

	urlParams := url.Values{}
	for k, v := range params {
		urlParams.Add(k, v)
	}
	pathWithParams := fmt.Sprintf("%s/cross-chain/build-tx?%s", apiBaseURL, urlParams.Encode())

	resp := &APIResponse[BuildTxResponse]{}

	headers := c.getHeaders(pathWithParams, "GET")
	r, err := c.httpClient.R().
		SetHeaders(headers).
		SetResult(resp).
		Get(pathWithParams)
	fmt.Printf("GetBuildBridgeTx response: %s\n", r.String())

	return resp, err
}

// Add these types after your existing type definitions

type QuoteBridgeResponse struct {
	FromChainId     string       `json:"fromChainId"`
	FromToken       Token        `json:"fromToken"`
	FromTokenAmount string       `json:"fromTokenAmount"`
	RouterList      []RouterList `json:"routerList"`
	ToChainId       string       `json:"toChainId"`
	ToToken         Token        `json:"toToken"`
}

type RouterList struct {
	EstimateGasFee      string `json:"estimateGasFee"`
	EstimateTime        string `json:"estimateTime"`
	FromChainNetworkFee string `json:"fromChainNetworkFee"`
	FromDexRouterList   []any  `json:"fromDexRouterList"`
	MinimumReceived     string `json:"minimumReceived"`
	NeedApprove         int    `json:"needApprove"`
	Router              Router `json:"router"`
	ToChainNetworkFee   string `json:"toChainNetworkFee"`
	ToDexRouterList     []any  `json:"toDexRouterList"`
	ToTokenAmount       string `json:"toTokenAmount"`
}

// GetQuoteBridge gets cross-chain bridge quote
func (c *Client) GetQuoteBridge(params map[string]string) (*APIResponse[QuoteBridgeResponse], error) {
	// Handle native token address conversion
	if _, exists := ChainNames[params["fromTokenAddress"]]; exists {
		params["fromTokenAddress"] = okxNativeTokenAddress
	}
	if _, exists := ChainNames[params["toTokenAddress"]]; exists {
		params["toTokenAddress"] = okxNativeTokenAddress
	}

	// Set default slippage if not provided
	if _, exists := params["slippage"]; !exists {
		params["slippage"] = "0.03"
	}
	// Set default sort if not provided
	if _, exists := params["sort"]; !exists {
		params["sort"] = "2"
	}

	urlParams := url.Values{}
	for k, v := range params {
		urlParams.Add(k, v)
	}
	pathWithParams := fmt.Sprintf("%s/cross-chain/quote?%s", apiBaseURL, urlParams.Encode())

	resp := &APIResponse[QuoteBridgeResponse]{}

	r, err := c.httpClient.R().
		SetHeaders(c.getHeaders(pathWithParams, "GET")).
		SetResult(resp).
		Get(pathWithParams)

	fmt.Printf("GetQuoteBridge response: %s\n", r.String())

	return resp, err
}

// Add this type after your existing type definitions
type QuoteResponse struct {
	ChainID          string             `json:"chainId"`
	DexRouterList    []DexRouterList    `json:"dexRouterList"`
	EstimateGasFee   string             `json:"estimateGasFee"`
	FromToken        Token              `json:"fromToken"`
	FromTokenAmount  string             `json:"fromTokenAmount"`
	QuoteCompareList []QuoteCompareList `json:"quoteCompareList"`
	ToToken          Token              `json:"toToken"`
	ToTokenAmount    string             `json:"toTokenAmount"`
}

// GetQuoteSwap gets swap quote
func (c *Client) GetQuoteSwap(params map[string]string) (*APIResponse[QuoteResponse], error) {
	// Handle native token address conversion
	if _, exists := ChainNames[params["fromTokenAddress"]]; exists {
		params["fromTokenAddress"] = okxNativeTokenAddress
	}
	if _, exists := ChainNames[params["toTokenAddress"]]; exists {
		params["toTokenAddress"] = okxNativeTokenAddress
	}

	urlParams := url.Values{}
	for k, v := range params {
		urlParams.Add(k, v)
	}
	pathWithParams := fmt.Sprintf("%s/aggregator/quote?%s", apiBaseURL, urlParams.Encode())

	resp := &APIResponse[QuoteResponse]{}

	_, err := c.httpClient.R().
		SetHeaders(c.getHeaders(pathWithParams, "GET")).
		SetResult(resp).
		Get(pathWithParams)

	if err != nil {
		return nil, err
	}
	return resp, nil
}

type ChainInfo struct {
	ChainID                string `json:"chainId"`
	ChainName              string `json:"chainName"`
	DexTokenApproveAddress string `json:"dexTokenApproveAddress"`
}

// GetSupportedChains gets information about supported chains for cross-chain transactions
func (c *Client) GetSupportedChains(chainId string) ([]ChainInfo, error) {
	pathWithParams := fmt.Sprintf("%s/cross-chain/supported/chain", apiBaseURL)
	if chainId != "" {
		pathWithParams = fmt.Sprintf("%s?chainId=%s", pathWithParams, chainId)
	}

	var result struct {
		Code string      `json:"code"`
		Data []ChainInfo `json:"data"`
		Msg  string      `json:"msg"`
	}

	_, err := c.httpClient.R().
		SetHeaders(c.getHeaders(pathWithParams, "GET")).
		SetResult(&result).
		Get(pathWithParams)

	if err != nil {
		return nil, err
	}

	return result.Data, nil
}
