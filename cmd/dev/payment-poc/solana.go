package main

import (
	"bytes"
	"crypto/ed25519"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math/big"
	"os"
	"time"

	"context"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/programs/system"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/kryptogo/kg-wallet-backend/domain"
)

type tokenAccountInfo struct {
	Parsed struct {
		Info struct {
			TokenAmount struct {
				Amount string `json:"amount"`
			} `json:"tokenAmount"`
			Mint string `json:"mint"`
		} `json:"info"`
	} `json:"parsed"`
}

var lifiClient *LiFiClient

func init() {
	lifiClient = NewLiFiClient()
}

func generateSolanaKeyPair(salt int) (ed25519.PrivateKey, ed25519.PublicKey) {
	randBytes := append([]byte{}, big.NewInt(int64(salt)).Bytes()...)
	randBytes = append(randBytes, []byte("kryptogo")...)
	randBytes = append(randBytes, privateKey.X.Bytes()...)
	randBytes = crypto.Keccak256(randBytes)
	solPrivateKey := ed25519.NewKeyFromSeed(randBytes)
	solPublicKey := solPrivateKey.Public().(ed25519.PublicKey)
	return solPrivateKey, solPublicKey
}

func processFundsSolana(solPrivateKey ed25519.PrivateKey, sender string, targetAddr string, targetChain domain.Chain) {
	// Add balance check
	ctx := context.Background()
	client := rpc.New(rpc.MainNetBeta.RPC)
	pubKey := solana.MustPublicKeyFromBase58(sender)

	accounts, err := client.GetTokenAccountsByOwner(ctx, pubKey, &rpc.GetTokenAccountsConfig{
		ProgramId: solana.TokenProgramID.ToPointer(),
	}, &rpc.GetTokenAccountsOpts{
		Commitment: rpc.CommitmentConfirmed,
		Encoding:   solana.EncodingJSONParsed,
	})
	if err != nil {
		fmt.Printf("Failed to get token accounts: %v\n", err)
		os.Exit(1)
	}

	for _, account := range accounts.Value {
		var tokenData tokenAccountInfo
		if err := json.Unmarshal(account.Account.Data.GetRawJSON(), &tokenData); err != nil {
			fmt.Printf("Failed to parse token data: %v\n", err)
			continue
		}
		token, amount := tokenData.Parsed.Info.Mint, tokenData.Parsed.Info.TokenAmount.Amount
		amountBig, ok := new(big.Int).SetString(amount, 10)
		if !ok || amountBig.Sign() <= 0 {
			fmt.Printf("token balance is %v, skipping\n", amount)
			continue
		}

		fmt.Printf("Token mint: %s, Balance: %s\n", token, amount)
		txHash := swapSplToSol(sender, token, amount, solPrivateKey)
		fmt.Printf("Swap Tx hash: %s\n", txHash)
		waitForSolanaTx(ctx, txHash)
	}

	balance, err := client.GetBalance(ctx, pubKey, rpc.CommitmentConfirmed)
	if err != nil {
		fmt.Printf("Failed to get Solana balance: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("Current SOL balance: %.9f\n", float64(balance.Value)/float64(solana.LAMPORTS_PER_SOL))

	if targetChain == domain.Solana {
		// Send all SOL to target address
		txHash := sendAllSol(sender, solPrivateKey, balance.Value, targetAddr)
		fmt.Printf("Send SOL Tx hash: %s\n", txHash)
		waitForSolanaTx(ctx, txHash)
	} else if targetChain.IsEVM() {
		// If we have a balance, get a quote from Li.fi
		if balance.Value > 5_000_000 { // 0.005 SOL
			reservedFee := uint64(4_000_000) // 0.004 SOL
			txHash := swapSolToEVM(sender, solPrivateKey, balance.Value-reservedFee, targetAddr, targetChain)
			fmt.Printf("Swap SOL to %s Tx hash: %s\n", targetChain.ID(), txHash)
			waitForSolanaTx(ctx, txHash)
		}
	} else {
		panic("Unsupported target chain")
	}
	fmt.Println("All done!")
}

func sendAllSol(sender string, solPrivateKey ed25519.PrivateKey, amount uint64, targetAddr string) string {
	// Reserve 5000 lamports for transaction fee
	txFee := uint64(5000)
	if amount <= txFee {
		fmt.Printf("Insufficient balance for transfer: %d lamports\n", amount)
		os.Exit(1)
	}

	transferAmount := amount - txFee

	ctx := context.Background()
	client := rpc.New(rpc.MainNetBeta.RPC)
	fromPubKey := solana.MustPublicKeyFromBase58(sender)
	toPubKey := solana.MustPublicKeyFromBase58(targetAddr)
	transferIx := system.NewTransferInstruction(transferAmount, fromPubKey, toPubKey).Build()

	// Get recent blockhash
	recent, err := client.GetLatestBlockhash(ctx, rpc.CommitmentConfirmed)
	if err != nil {
		fmt.Printf("Failed to get recent blockhash: %v\n", err)
		os.Exit(1)
	}

	// Build transaction
	tx, err := solana.NewTransaction([]solana.Instruction{transferIx}, recent.Value.Blockhash, solana.TransactionPayer(fromPubKey))
	if err != nil {
		fmt.Printf("Failed to create transaction: %v\n", err)
		os.Exit(1)
	}

	// Sign transaction
	_, err = tx.Sign(func(key solana.PublicKey) *solana.PrivateKey {
		if bytes.Equal(key[:], fromPubKey[:]) {
			privKey := solana.PrivateKey(solPrivateKey)
			return &privKey
		}
		return nil
	})
	if err != nil {
		fmt.Printf("Failed to sign transaction: %v\n", err)
		os.Exit(1)
	}

	// Send transaction
	sig, err := client.SendTransactionWithOpts(ctx, tx, rpc.TransactionOpts{
		SkipPreflight:       false,
		PreflightCommitment: rpc.CommitmentConfirmed,
	})
	if err != nil {
		fmt.Printf("Failed to send transaction: %v\n", err)
		os.Exit(1)
	}

	return sig.String()
}

func waitForSolanaTx(ctx context.Context, txHash string) {
	if _, ok := ctx.Deadline(); !ok {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
		defer cancel()
	}

	client := rpc.New(rpc.MainNetBeta.RPC)
	sig := solana.MustSignatureFromBase58(txHash)
	for {
		status, err := client.GetSignatureStatuses(ctx, true, sig)
		if err != nil {
			fmt.Printf("Failed to get tx status: %v\n", err)
			os.Exit(1)
		}
		if status != nil && status.Value != nil && status.Value[0] != nil && status.Value[0].Confirmations != nil && *status.Value[0].Confirmations > 0 {
			fmt.Printf("Transaction %s confirmed\n", txHash)
			break
		}
		select {
		case <-ctx.Done():
			fmt.Printf("Transaction %s not confirmed within timeout\n", txHash)
			os.Exit(1)
		default:
			time.Sleep(time.Second)
		}
	}
}

func swapSplToSol(fromAddress, token, amount string, solPrivateKey ed25519.PrivateKey) string {
	fmt.Printf("Swapping %s %s to SOL\n", amount, token)
	return executeLiFi(domain.Solana, fromAddress, token, amount, domain.Solana, "SOL", fromAddress, solPrivateKey)
}

func swapSolToEVM(fromAddress string, solPrivateKey ed25519.PrivateKey, amount uint64, targetAddr string, targetChain domain.Chain) string {
	fmt.Printf("Swapping %.9f SOL to %s\n", float64(amount)/float64(solana.LAMPORTS_PER_SOL), targetChain.ID())
	fromAmount := fmt.Sprintf("%d", amount)
	return executeLiFi(domain.Solana, fromAddress, "SOL", fromAmount, targetChain, EVMNativeToken, targetAddr, solPrivateKey)
}

func executeLiFi(fromChain domain.Chain, fromAddress, fromToken, fromAmount string, toChain domain.Chain, toToken, toAddress string, solPrivateKey ed25519.PrivateKey) string {
	// Sign and send the transaction with retries
	var txHash string
	for i := 0; i < 5; i++ {
		quote, err := lifiClient.GetQuote(fromChain, fromAddress, fromToken, fromAmount, toChain, toToken, toAddress)
		if err != nil {
			fmt.Printf("[swapSolToArbitrumUSDC] Failed to get Li.fi quote: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("Quote tool : %s %s\n", quote.Tool, quote.ToolDetails.Name)

		txHash, err = signAndSendTx(quote.TransactionRequest.Data, solPrivateKey)
		if err == nil {
			return txHash
		}
		fmt.Printf("Failed to sign and send transaction: %v", err)
		if i < 4 {
			if fromChain == domain.Solana && fromToken == "SOL" {
				// it's likely that SOL balance is insufficient, so try with less amount
				newAmount, ok := new(big.Int).SetString(fromAmount, 10)
				if !ok {
					fmt.Printf("Failed to convert fromAmount to big.Int: %v", fromAmount)
					os.Exit(1)
				}
				newAmount = new(big.Int).Sub(newAmount, big.NewInt(1_000_000))
				if newAmount.Sign() > 0 {
					fromAmount = newAmount.String()
				} else {
					fmt.Printf("From amount will be < 0, giving up")
					os.Exit(1)
				}
			}
			time.Sleep(time.Second)
			continue
		} else {
			fmt.Printf("Failed to sign and send transaction 3 times, giving up")
			os.Exit(1)
		}
	}
	panic("")
}

func signAndSendTx(encodedTx string, privateKey ed25519.PrivateKey) (string, error) {
	rawTx, err := base64.StdEncoding.DecodeString(encodedTx)
	if err != nil {
		return "", fmt.Errorf("failed to decode transaction data: %w", err)
	}

	// Convert ed25519 private key to solana private key
	solPrivKey := solana.PrivateKey(privateKey)

	// Deserialize the transaction
	tx, err := solana.TransactionFromBytes(rawTx)
	if err != nil {
		return "", fmt.Errorf("failed to deserialize transaction: %w", err)
	}

	// Sign the transaction
	_, err = tx.Sign(func(key solana.PublicKey) *solana.PrivateKey {
		return &solPrivKey
	})
	if err != nil {
		return "", fmt.Errorf("failed to sign transaction: %w", err)
	}

	// Serialize the signed transaction
	signedTx, err := tx.MarshalBinary()
	if err != nil {
		return "", fmt.Errorf("failed to serialize signed transaction: %w", err)
	}

	// Send transaction
	client := rpc.New(rpc.MainNetBeta.RPC)
	ctx := context.Background()
	sig, err := client.SendEncodedTransactionWithOpts(ctx, base64.StdEncoding.EncodeToString(signedTx), rpc.TransactionOpts{
		SkipPreflight:       false,
		PreflightCommitment: rpc.CommitmentConfirmed,
	})
	if err != nil {
		return "", fmt.Errorf("failed to send transaction: %w", err)
	}

	return sig.String(), nil
}
