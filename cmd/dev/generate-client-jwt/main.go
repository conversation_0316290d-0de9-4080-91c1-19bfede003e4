package main

import (
	"fmt"
	"os"
	"time"

	"github.com/golang-jwt/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

var (
	encryptKey      string
	subjectName     string
	contractAddress string
)

type authClaims struct {
	jwt.StandardClaims
	ContractAddressList []string `json:"contract_address_list"`
}

func init() {
	subjectName = os.Getenv("SUBJECT_NAME")
	contractAddress = os.Getenv("CONTRACT_ADDRESS")
	encryptKey = config.GetString("ENCRYPTION_KEY")
}

/**
 * Usage: ENV=prod SUBJECT_NAME="https://www.91app.com" CONTRACT_ADDRESS="******************************************" go run cmd/dev/generate-client-jwt/main.go
 * Output: client token
 */

func main() {
	jwtToken := generateToken(subjectName, contractAddress)
	fmt.Println(jwtToken)
}

func generateToken(userName, contractAddress string) string {
	expiresAt := time.Now().Add(30 * 24 * time.Hour).Unix()
	time.Sleep(1 * time.Second)
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, authClaims{
		StandardClaims: jwt.StandardClaims{
			Audience:  "https://kryptogo.com",
			ExpiresAt: expiresAt,
			Id:        "",
			IssuedAt:  time.Now().Unix(),
			Issuer:    "https://www.kryptogo.com",
			NotBefore: time.Now().Unix(),
			Subject:   subjectName,
		},
		ContractAddressList: []string{contractAddress},
	},
	)

	// Sign and get the complete encoded token as a string using the secret
	tokenString, err := token.SignedString([]byte(encryptKey))
	if err != nil {
		fmt.Println(err)
		return ""
	}
	return tokenString
}
