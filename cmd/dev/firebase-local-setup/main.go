package main

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"

	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
)

func main() {
	rdb.Reset()
	users, _ := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	if err != nil {
		panic(err)
	}
}
