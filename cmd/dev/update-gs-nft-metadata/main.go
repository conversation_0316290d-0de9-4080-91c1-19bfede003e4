package main

// disable it for now, because we don't use it. Tickey: KW-1279
/**
 * Usage: ENV=prod TOKEN_ID=154 EVENTID=fortunego-by-kryptogo go run cmd/dev/update-gs-nft-metadata/main.go
 */
// func main() {
// 	endTokenID, err := strconv.Atoi(config.GetString("TOKEN_ID"))
// 	if err != nil {
// 		kglog.Fatal("wrong token id")
// 		return
// 	}
// 	eventID := config.GetString("EVENTID")

// 	event, errCode, err := rdb.GetAirdropEvent(eventID, "")
// 	if err != nil {
// 		kglog.FatalWithData("GetAirdropEvent failed", map[string]interface{}{
// 			"errCode": errCode,
// 			"err":     err.Error(),
// 			"eventID": eventID,
// 		})
// 		return
// 	}

// 	for tokenID := 1; tokenID <= endTokenID; tokenID++ {
// 		traitID, err := getTraitID(eventID, tokenID)
// 		if err != nil {
// 			kglog.Error("getTraitID failed")
// 		}
// 		trait, _, err := rdb.GetAirdropNFTTrait(eventID, traitID)
// 		kglog.DebugWithData("update gs nft metadata", map[string]interface{}{
// 			"tokenID": tokenID,
// 			"traitID": traitID,
// 			"trait":   trait,
// 		})
// 		// airdrop.SetNFTMetadataWithTrait(event, int32(tokenID), trait)
// 	}
// }

// func getTraitID(eventID string, tokenID int) (string, error) {
// 	objectPath := fmt.Sprintf("public/contracts/%s/meta/%d", eventID, tokenID)

// 	// get nft metadata from gcp storage
// 	resp, err := resty.New().
// 		SetTimeout(10*time.Second).R().
// 		SetHeader("Content-Type", "application/json").
// 		Get(fmt.Sprintf("%s%s", config.GetString("CDN_HOST"), objectPath))

// 	if err != nil {
// 		kglog.FatalWithData("get nftMetadata failed", err)
// 		return "", err
// 	}
// 	if resp.StatusCode() >= 400 {
// 		kglog.WarningWithData("91app mint - get nftMetadata status code >=400", resp)
// 		return "", fmt.Errorf("get nftMetadata status code >=400")
// 	}
// nftMetadata := airdrop.NftMetadata{}

// json.Unmarshal(resp.Body(), &nftMetadata)

// // get trait id from image
// traitID, err := parseTraitIDFromImage(nftMetadata.Image)
// return traitID, err
// }

// func parseTraitIDFromImage(imageURL string) (string, error) {
// 	// https://wallet-static.kryptogo.com/public/assets/images/fortunego-by-kryptogo/trait_1.jpg
// 	s := strings.Split(imageURL, "/")

// 	if len(s) < 2 {
// 		return "", fmt.Errorf("wrong image url")
// 	}

// 	s = strings.Split(s[len(s)-1], "_")
// 	if len(s) < 2 {
// 		return "", fmt.Errorf("wrong image url")
// 	}
// 	s = strings.Split(s[1], ".")
// 	return s[0], nil
// }
