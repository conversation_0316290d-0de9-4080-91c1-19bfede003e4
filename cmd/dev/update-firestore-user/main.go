package main

import (
	"context"
	"encoding/json"
	"log"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

func main() {
	wallets := &domain.Wallets{
		DefaultReceiveWallets: map[domain.Chain]domain.Address{
			domain.Arbitrum: domain.NewEvmAddress("******************************************"),
			domain.BNBChain: domain.NewEvmAddress("******************************************"),
			domain.Bitcoin:  domain.NewStrAddress("******************************************"),
			domain.Ethereum: domain.NewEvmAddress("******************************************"),
			domain.Kcc:      domain.NewEvmAddress("******************************************"),
			domain.Polygon:  domain.NewEvmAddress("******************************************"),
			domain.Solana:   domain.NewStrAddress("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"),
			domain.Tron:     domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
		},
		EvmWallets: []*domain.UserWallet{
			{
				Address:      "******************************************",
				AddressIndex: 0,
			},
			{
				Address:      "******************************************",
				AddressIndex: 0,
			},
		},
		WalletGroups: []*domain.WalletGroup{
			{
				BtcWallets: []*domain.UserWallet{
					{
						Address:      "******************************************",
						AddressIndex: 0,
					},
				},
				EvmWallets: []*domain.UserWallet{
					{
						Address:      "******************************************",
						AddressIndex: 0,
					},
				},
				SolanaWallets: []*domain.UserWallet{
					{
						Address:      "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
						AddressIndex: 0,
					},
				},
				TronWallets: []*domain.UserWallet{
					{
						Address:      "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn",
						AddressIndex: 0,
					},
				},
			},
		},
		Wallets: []*domain.UserWallet{
			{
				Address:      "BfYQnrZCojWELhKS7cNTH28rwzousmBJAb3TBKH8hTJ8",
				AddressIndex: 0,
				Chain:        "sol",
			},
		},
	}

	data := map[string]interface{}{}

	encoded, _ := json.Marshal(wallets)
	_ = json.Unmarshal(encoded, &data)

	err := rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID: "004-dev",
		},
		Wallets: wallets,
	})
	if err != nil {
		log.Println(err)
	}
}
