package main

import (
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

func main() {
	dsn := config.GetString("MYSQL_DSN")
	walletDB, err := gorm.Open(
		mysql.Open(dsn),
	)
	if err != nil {
		panic(err)
	}

	g := gen.NewGenerator(gen.Config{
		OutPath:           "pkg/db/dao",
		Mode:              gen.WithoutContext,
		FieldNullable:     true,
		FieldCoverable:    true,
		FieldWithTypeTag:  true,
		FieldWithIndexTag: true,
	})

	g.UseDB(walletDB)

	g.GenerateAllTable()

	g.ApplyBasic(
		model.DashboardUser{},
		model.DashboardProject{},
		model.DashboardPrizeRedeemed{},
		model.DashboardPrizeToken{},
		model.DashboardPrize{},
		model.DashboardRedeemLog{},
		model.DataContractMetadata{},
		model.NftAsset{},
		model.NftAssetTag{},
		model.NftCollection{},
		model.NftSyncOwner{},
		model.NftUserAmount{},
		model.NotificationsRead{},
		model.Notification{},
		model.Asset{},
		model.AssetPrice{},
		model.AssetPriceHistory{},
		model.HistoricalBalance{},
		model.TokenMetadata{},
		model.TxBlockTimestamp{},
		model.TxDetail{},
		model.TxJob{},
		model.TxJobLog{},
		model.TxList{},
		model.TxUpdate{},
		model.AirdropEvent{},
		model.AirdropLog{},
		model.SmsLog{},
		model.AirdropWhitelist{},
		model.IdvTaskLog{},
		model.KycResult{},
		model.AirdropNftTrait{},
		model.WsEvent{},
		model.Order{},
		model.APIKey{},
		model.CryptoTosLog{},
		model.Order{},
		model.StudioLinebotConfig{},
		model.LinebotUserMap{},
	)

	g.Execute()
}
