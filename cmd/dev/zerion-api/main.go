package main

import (
	"encoding/json"
	"fmt"
	"log"
	"runtime"
	"strings"
	"time"

	gosocketio "github.com/kryptogo/golang-socketio"
	"github.com/kryptogo/golang-socketio/transport"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

/**
 * Usage: ENV=dev ADDRESS=****************************************** go run cmd/dev/zerion-api/main.go
 */

func main() {
	apiKey := config.GetString("API_KEY")
	if apiKey == "" {
		apiKey = config.GetString("ZERION_API_KEY")
	}

	runtime.GOMAXPROCS(runtime.NumCPU())

	url := "wss://api-v4.zerion.io/socket.io/?EIO=3&transport=websocket&api_token=" + apiKey
	fmt.Println(url)

	transport := transport.GetDefaultWebsocketTransport()
	namespace := "address"
	c, err := gosocketio.Dial(url, "/"+namespace, transport)
	if err != nil {
		kglog.Fatalf("error dialing zerion api: %v", err)
	}

	scopes := []string{"positions"}
	for _, scope := range scopes {
		events := []string{"received", namespace, scope}
		event := strings.Join(events, " ")
		_ = c.On(event, func(h *gosocketio.Channel, msg interface{}) {
			data, err := json.Marshal(msg)
			if err != nil {
				kglog.Errorf("[Zerion] marshal error: %s", err.Error())
			}
			kglog.DebugWithData("[Zerion] received data", string(data))
		})
	}

	err = c.On(gosocketio.OnDisconnection, func(h *gosocketio.Channel) {
		kglog.Fatal("Disconnected")
	})
	if err != nil {
		kglog.Fatalf("error setting disconnection handler: %v", err)
	}

	err = c.On(gosocketio.OnConnection, func(h *gosocketio.Channel) {
		kglog.Debug("Connected")
	})
	if err != nil {
		kglog.Fatalf("error setting connection handler: %v", err)
	}

	err = c.Emit("get", map[string]interface{}{
		"scope": scopes,
		"payload": map[string]interface{}{
			"address":  config.GetString("ADDRESS"),
			"currency": "usd",
		},
	})
	if err != nil {
		kglog.Fatalf("error emitting get: %v", err)
	}
	time.Sleep(60 * time.Second)
	c.Close()

	log.Println(" [x] Complete")
}
