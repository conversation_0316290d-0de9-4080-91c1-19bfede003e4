package main

import (
	"context"
	"log"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// usage 1: ENV=dev UID=ycZFiYxcelZvJFSo9wR2fQV81MC3 go run cmd/dev/clear-kyc-state/main.go
// usage 2: ENV=dev PHONE=+************ go run cmd/dev/clear-kyc-state/main.go
func main() {
	uid := config.GetString("UID")
	phone := config.GetString("PHONE")
	if uid == "" && phone == "" {
		log.Println("Both uid and phone are empty")
		return
	}
	if uid != "" && phone != "" {
		log.Println("Both uid and phone are set")
		return
	}
	if phone != "" {
		userData, _ := rdb.GormRepo().GetUserByPhoneNumber(context.Background(), phone, "", false, nil)
		if userData == nil {
			log.Printf("user not found: %s\n", phone)
			return
		}
		uid = userData.UID
	}

	err := rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID: uid,
		},
		KycState: util.Ptr(domain.KycStatusUnverified.String()),
	})
	if err != nil {
		log.Printf("set user failed: %v\n", map[string]interface{}{
			"err": err.Error(),
		})
		return
	}

	err = rdb.DeleteKycResult(context.Background(), uid)
	if err != nil {
		log.Printf("delete kyc result failed: %v\n", err)
	}
}
