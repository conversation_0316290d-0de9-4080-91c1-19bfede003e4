package main

import (
	"time"

	"github.com/golang-jwt/jwt"
	"github.com/google/uuid"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

// create a token for api service to call signing server
func main() {
	now := time.Now()
	expiresAt := now.Add(time.Duration(time.Hour) * 24 * 3650)
	tokenClaims := jwt.StandardClaims{
		Issuer:    "KryptoGO Wallet",
		Subject:   "kg-backend-api-server",
		Audience:  "https://kryptogo.com",
		ExpiresAt: expiresAt.Unix(),
		NotBefore: now.Unix(),
		IssuedAt:  now.Unix(),
		Id:        uuid.New().String(),
	}
	jwtClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, tokenClaims)
	result, err := jwtClaims.SignedString([]byte(config.GetString("KG_TOKEN_SECRET")))
	if err != nil {
		panic(err)
	}
	println(result)
}
