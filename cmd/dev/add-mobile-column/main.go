package main

import (
	"bufio"
	"encoding/csv"
	"os"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

func main() {
	// Read mobile transactions into a map for O(1) lookup
	mobileTxs := make(map[string]bool)
	mobileFile, err := os.Open("./mobile-txs.txt")
	if err != nil {
		kglog.FatalWithData("Failed to open mobile-txs.txt", map[string]interface{}{
			"error": err.Error(),
		})
	}
	defer mobileFile.Close()

	// Read mobile transactions line by line
	scanner := bufio.NewScanner(mobileFile)
	for scanner.Scan() {
		txHash := strings.TrimSpace(scanner.Text())
		if txHash != "" {
			mobileTxs[txHash] = true
		}
	}

	if err := scanner.Err(); err != nil {
		kglog.FatalWithData("Failed to read mobile-txs.txt", map[string]interface{}{
			"error": err.<PERSON><PERSON><PERSON>(),
		})
	}

	// Open input CSV
	inputFile, err := os.Open("solana_transactions.csv")
	if err != nil {
		kglog.FatalWithData("Failed to open solana_transactions.csv", map[string]interface{}{
			"error": err.Error(),
		})
	}
	defer inputFile.Close()

	// Create output CSV
	outputFile, err := os.Create("solana_transactions_with_mobile.csv")
	if err != nil {
		kglog.FatalWithData("Failed to create output CSV", map[string]interface{}{
			"error": err.Error(),
		})
	}
	defer outputFile.Close()

	// Create CSV readers and writers
	reader := csv.NewReader(inputFile)
	writer := csv.NewWriter(outputFile)
	defer writer.Flush()

	// Read header
	header, err := reader.Read()
	if err != nil {
		kglog.FatalWithData("Failed to read CSV header", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// Add new column to header
	header = append(header, "is_mobile")
	if err := writer.Write(header); err != nil {
		kglog.FatalWithData("Failed to write CSV header", map[string]interface{}{
			"error": err.Error(),
		})
	}

	// Process each record
	for {
		record, err := reader.Read()
		if err != nil {
			break // End of file
		}

		// Parse and reformat timestamp
		timestamp, err := time.Parse(time.RFC3339, record[0])
		if err != nil {
			kglog.ErrorWithData("Failed to parse timestamp", map[string]interface{}{
				"error":     err.Error(),
				"timestamp": record[0],
			})
			continue
		}

		// Format timestamp in the expected format
		record[0] = timestamp.Format("2006-01-02 15:04:05")

		// Check if transaction hash exists in mobile transactions
		isMobile := "0"
		if mobileTxs[record[1]] { // record[1] is tx_hash
			isMobile = "1"
		}

		// Add is_mobile column
		record = append(record, isMobile)
		if err := writer.Write(record); err != nil {
			kglog.FatalWithData("Failed to write CSV record", map[string]interface{}{
				"error": err.Error(),
			})
		}
	}

	kglog.Info("Successfully created solana_transactions_with_mobile.csv")
}
