package main

import (
	"encoding/json"
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/pkg/verifier"
	"github.com/lestrrat-go/jwx/jwk"
)

func main() {
	privateKeyPEM, privateKey, err := verifier.GenerateRSAPrivateKeyPEM()
	if err != nil {
		panic(err)
	}
	jwkKey, err := jwk.New(privateKey.Public())
	if err != nil {
		panic(err)
	}
	if err := jwkKey.Set(jwk.KeyIDKey, "demo-key"); err != nil {
		panic(err)
	}
	if err := jwkKey.Set(jwk.AlgorithmKey, "RS256"); err != nil {
		panic(err)
	}

	set := jwk.NewSet()
	set.Add(jwkKey)

	if setBytes, err := json.Marshal(set); err != nil {
		panic(err)
	} else {
		fmt.Printf("JWK Set: %s\n", string(setBytes))
	}
	fmt.Printf("Private key PEM: %s\n", string(privateKeyPEM))
}
