package main

import (
	"context"
	"sync"
	"time"

	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/nft"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

/**
* Usage: source config/dev.sh && go run cmd/dev/update-nft-is-spam/main.go
  Replace the db info & redis info
*/

const (
	batchSize = 999
	cacheTTL  = time.Hour * 24
)

func main() {
	ctx := context.Background()

	// gett start index from redis
	cacheKey := "nft:is-spam:start-index"
	startIndexP, err := cache.Int(cacheKey)
	if err != nil {
		panic(err)
	} else if startIndexP == nil {
		startIndex := 1
		startIndexP = &startIndex
	}

	startIndex := *startIndexP

	kglog.InfofCtx(ctx, "update nft isSpam ,start index: %d", startIndex)
	maxID, err := rdb.GetNftMaxID(ctx)
	kglog.InfofCtx(ctx, "update nft isSpam, max id: %d", maxID)

	if err != nil {
		panic(err)
	}

	for {
		assets, err := rdb.GetNftAssetsByIds(ctx, int32(startIndex), int32(startIndex+batchSize))
		if err != nil {
			panic(err)
		}

		if len(assets) == 0 && startIndex > int(maxID) {
			kglog.InfoCtx(ctx, "update nft isSpam, no more assets, done!")
			break
		}

		updatedAssets := make([]*model.NftAsset, len(assets))

		// Use WaitGroup for synchronization
		var wg sync.WaitGroup

		// Control the number of concurrent goroutines
		semaphore := make(chan struct{}, 20) // 20 assets processed in parallel

		for i, asset := range assets {
			wg.Add(1)

			// Acquire a token
			semaphore <- struct{}{}

			go func(asset *model.NftAsset, index int) {
				defer wg.Done()

				var alchemyapiIsSpam alchemyapi.IsSpam
				if asset.IsSpam != nil && *asset.IsSpam {
					alchemyapiIsSpam = alchemyapi.IsSpamTrue
				} else {
					alchemyapiIsSpam = alchemyapi.IsSpamFalse
				}

				asset.IsSpam = nft.IsSpamNft(ctx, alchemyapiIsSpam, &nft.DetectSpamNftParams{
					ChainID:         asset.ChainID,
					ContractAddress: asset.ContractAddress,
					Name:            asset.Name,
					Symbol:          "",
					ImageURL:        asset.ImageURL,
				})
				updatedAssets[index] = &model.NftAsset{
					ChainID:         asset.ChainID,
					ContractAddress: asset.ContractAddress,
					TokenID:         asset.TokenID,
					IsSpam:          asset.IsSpam,
				}

				// Release a token
				<-semaphore

			}(asset, i)
		}

		wg.Wait() // Wait for all goroutines to finish

		err = rdb.SaveNftAsset(ctx, updatedAssets)
		if err != nil {
			panic(err)
		}
		kglog.InfofCtx(ctx, "update nft isSpam, save assets, start index: %d", startIndex)
		startIndex += batchSize + 1
		cache.Set(ctx, cacheKey, startIndex, cacheTTL)
	}
}
