package main

import (
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/kms"
)

func main() {
	mnemonic, err := kms.GenerateMnemonic()
	if err != nil {
		fmt.Println("error: ", err)
		return
	}
	fmt.Println("mnemonic: ", mnemonic)

	for chainName, derivationPath := range kms.WalletDerivationPath {
		account, pk, err := kms.GenerateAddress(mnemonic, chainName, derivationPath)
		if err != nil {
			fmt.Println("error: ", err)
			return
		}
		fmt.Printf("account, chain= %s , address= %s , privateKey= %s\n", chainName, account, pk)
	}
}
