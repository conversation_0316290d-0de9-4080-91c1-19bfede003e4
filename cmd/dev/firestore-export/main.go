package main

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"
	"google.golang.org/api/iterator"
)

/**
 * Usage: ENV=local COLLECTION_NAME=users EXPORT_PATH=dev go run cmd/dev/firestore-export/main.go
 */
func main() {
	viper.SetDefault("COLLECTION_NAME", "users")
	viper.SetDefault("EXPORT_PATH", "default")
	collectionName := config.GetString("COLLECTION_NAME")
	subpath := config.GetString("EXPORT_PATH")
	exportCollections(collectionName, subpath)
}

func exportCollections(collection, subpath string) {
	iter := db.All(collection)
	for {
		item, err := iter.Next()
		if err == iterator.Done {
			fmt.Println("done")
			break
		}
		if err != nil {
			kglog.Errorf("iterator error: %v", err)
			break
		}
		if item == nil {
			kglog.Errorf("item is nil")
			continue
		}
		fmt.Println(item.Ref.ID)

		d := new(map[string]interface{})
		_ = mapstructure.Decode(item.Data(), d)

		dataStr, err := json.Marshal(d)
		if err != nil {
			kglog.Errorf("marshal error: %v", err)
			continue
		}
		err = os.WriteFile("res/"+collection+"/"+subpath+"/"+item.Ref.ID+".json", dataStr, 0644)
		if err != nil {
			fmt.Println(err.Error())
		}
	}
}
