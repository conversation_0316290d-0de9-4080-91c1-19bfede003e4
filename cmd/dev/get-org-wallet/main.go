package main

import (
	"context"
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/kms"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
)

func main() {
	rdb.Init()
	repo := rdb.GormRepo()
	kmsKeyName := kms.GetKmsKeyName("mnemonic", "key")
	encryptor := server.NewKMSPrivateKeyEncryptor(kmsKeyName)
	server.Init(repo, &stub.DefaultAlertSender{}, encryptor)
	signer, err := repo.GetSignerByOrganizationId(context.Background(), 1, encryptor)
	if err != nil {
		panic(err)
	}
	fmt.Println(signer)
}
