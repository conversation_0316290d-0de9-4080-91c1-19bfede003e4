dryRun: false

compliance:
  gofactToken: "AAA"
  org:
    # existingOrgID: 55688
    code: "test"
    name: "test"
    contact:
      name: "test"
      email: "<EMAIL>"
      phone: "1234567890"
    plan: 4
    subscriptionRange:
      startDate: "2024-07-01"
      endDate: "2025-06-30"
      points: 10000

studio:
  internalToken: ""
  org:
    admin:
      # existingAdminUID: "vNxjm0HEnDQ60wJ8oh0y60e6V2u1"
      email: "<EMAIL>"
      # phone: "0912345678"
    users:
      - email: "<EMAIL>"
        name: "ashleylin"
        memberID: "<PERSON>"
        roles:
          - "owner"

    adminEmail: "<EMAIL>"
    adminName: "estelle"
    name: "test"
    modules:
      admin:
        - "billing"
      user360:
        - "data"
        - "engage"
        - "audience"
      assetPro:
        - "treasury"
        - "send_token"
        - "transaction_history"
        - "operators"
        - "market"
      nftBoost:
        - "campaign"
        - "reward"
      compliance:
        - "create_a_task"
        - "case_management"
        - "all_tasks"
      walletBuilder: []
    wallet:
      oauthClientID: "test-wallet"
      oauthClientName: "test-wallet"
      oauthClientDomain: "https://wallet.kryptogo.app"
    complyflow:
      oauthClientID: "test"
      oauthClientName: "test-complyflow"
      oauthClientDomain: "https://wallet.kryptogo.app"
    market:
      marketCode: "test"
      title: "Kryptogo marketplace"
      logo: "https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png"
      introduction: "KryptoGO marketplace"
      paymentMethod: "bank_transfer"
      paymentCurrency: "TWD"
      bankName: "Mega Bank"
      branchName: "XinYi"
      bankAccount: "55688"
      paymentExpirationSec: 3600
      bankAccountHolderName: "KryptoGO Owner"
      oauthClientID: "test-market"
      oauthClientName: "test-market"
