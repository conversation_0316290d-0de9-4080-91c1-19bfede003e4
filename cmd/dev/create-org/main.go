package main

import (
	"embed"
	"fmt"
	"strconv"
	"strings"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/samber/lo"
	"gopkg.in/yaml.v3"
)

/*
  Ref: https://www.notion.so/kryptogo/Studio-OTC-b36f774c8620466ebe4b338fbdc6a520
*/

const (
	defaultSignAlertThreshold    = 10
	defaultOauthClientMainLogo   = "https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png"
	defaultOauthClientSquareLogo = "https://wallet-static.kryptogo.com/public/icon/KryptoGO-24.png"
)

func wrapMain() error {
	type event struct {
		name string
		fn   func() error
	}

	events := make([]event, 0)

	param, err := readConfig()
	if err != nil {
		return fmt.Errorf("failed to read config: %v", err)
	}

	studioHelper := studioOrganizationHelper{
		host:          config.GetString("SELF_INTERNAL_HOST"),
		storeHost:     config.GetString("KGSTORE_BASE_URL"),
		internalToken: param.Studio.InternalToken,
	}
	if err := studioHelper.validate(); err != nil {
		return fmt.Errorf("failed to validate studio helper: %v", err)
	}

	var cmpOrgID *int
	if param.Compliance.Org.ExistingOrgID != nil && *param.Compliance.Org.ExistingOrgID != 0 {
		cmpOrgID = param.Compliance.Org.ExistingOrgID
	} else {
		cmpHelper := complianceOrganizationHelper{
			host:        config.GetString("COMPLIANCE_HOST"),
			gofactToken: param.Compliance.GofactToken,
		}
		if err := cmpHelper.validate(); err != nil {
			return fmt.Errorf("failed to validate compliance organization: %v", err)
		}

		// create compliance organization
		{
			req := createComplianceOrganizationRequest{
				OrgCode:      param.Compliance.Org.Code,
				Name:         param.Compliance.Org.Name,
				EmailAddress: param.Compliance.Org.Contact.Email,
				Contact: struct {
					Name         string `json:"name"`
					PhoneNumber  string `json:"phone_number"`
					EmailAddress string `json:"email_address"`
				}{
					Name:         param.Compliance.Org.Contact.Name,
					PhoneNumber:  param.Compliance.Org.Contact.Phone,
					EmailAddress: param.Compliance.Org.Contact.Email,
				},
				Plan: param.Compliance.Org.Plan,
				SubscriptionRange: struct {
					StartDate string `json:"startDate"`
					EndDate   string `json:"endDate"`
					Points    string `json:"points"`
				}{
					StartDate: param.Compliance.Org.SubscriptionRange.StartDate,
					EndDate:   param.Compliance.Org.SubscriptionRange.EndDate,
					Points:    strconv.Itoa(param.Compliance.Org.SubscriptionRange.Points),
				},
			}
			if err := req.validate(); err != nil {
				return fmt.Errorf("failed to validate compliance organization: %v", err)
			}

			events = append(events, event{
				name: "create compliance organization",
				fn: func() error {
					orgID, err := cmpHelper.createOrganization(req)
					if err != nil {
						return fmt.Errorf("failed to create compliance organization: %v", err)
					}

					cmpOrgID = &orgID
					return nil
				},
			})
		}

		// start subscription
		{
			req := createComplianceContractRequest{
				Plan:           param.Compliance.Org.Plan,
				StartDate:      param.Compliance.Org.SubscriptionRange.StartDate,
				EndDate:        param.Compliance.Org.SubscriptionRange.EndDate,
				NumberOfPoints: param.Compliance.Org.SubscriptionRange.Points,
			}

			if err := req.validate(); err != nil {
				return fmt.Errorf("failed to validate compliance contract: %v", err)
			}

			events = append(events, event{
				name: "create compliance contract",
				fn: func() error {
					return cmpHelper.createContract(cmpOrgID, req)
				},
			})
		}

	}

	// studio admin
	var adminUID *string
	{
		if param.Studio.Org.Admin.ExistingAdminUID != nil && *param.Studio.Org.Admin.ExistingAdminUID != "" {
			adminUID = param.Studio.Org.Admin.ExistingAdminUID
		} else {
			req := createUserRequest{
				Email: param.Studio.Org.Admin.Email,
				Phone: param.Studio.Org.Admin.Phone,
			}

			events = append(events, event{
				name: "create admin user",
				fn: func() error {
					uid, err := studioHelper.createUser(req)
					if err != nil {
						return fmt.Errorf("failed to create user: %v", err)
					}
					adminUID = &uid
					return nil
				},
			})
		}
	}
	// studio org
	var studioOrgID *int
	{
		req := createStudioOrganizationRequest{
			Name:      param.Studio.Org.Name,
			AdminName: param.Studio.Org.AdminName,
			Email:     param.Studio.Org.AdminEmail,
		}
		if err := req.validate(); err != nil {
			return fmt.Errorf("failed to validate create studio organization request: %v", err)
		}
		events = append(events, event{
			name: "create studio org",
			fn: func() error {
				orgID, err := studioHelper.createOrganization(cmpOrgID, adminUID, req)
				if err != nil {
					return fmt.Errorf("failed to create organization: %v", err)
				}
				studioOrgID = &orgID
				return nil
			},
		})
	}

	// update org
	var isComplyflowEnabled, isMarketEnabled bool
	{
		req := updateStudioOrganizationRequest{
			SignAlertThreshold: defaultSignAlertThreshold,
			Module: struct {
				User360       []string `json:"user_360"`
				WalletBuilder []string `json:"wallet_builder"`
				AssetPro      []string `json:"asset_pro"`
				NFTBoost      []string `json:"nft_boost"`
				Compliance    []string `json:"compliance"`
				Admin         []string `json:"admin"`
			}{
				User360:       param.Studio.Org.Modules["user360"],
				WalletBuilder: param.Studio.Org.Modules["walletBuilder"],
				AssetPro:      param.Studio.Org.Modules["assetPro"],
				NFTBoost:      param.Studio.Org.Modules["nftBoost"],
				Compliance:    param.Studio.Org.Modules["compliance"],
				Admin:         param.Studio.Org.Modules["admin"],
			},
		}

		isMarketEnabled = lo.Contains(param.Studio.Org.Modules["assetPro"], "market")
		isComplyflowEnabled = len(param.Studio.Org.Modules["compliance"]) > 0 && param.Studio.Org.Complyflow != nil

		if err := req.validate(); err != nil {
			return fmt.Errorf("failed to validate update studio organization request: %v", err)
		}

		events = append(events, event{
			name: "update org",
			fn: func() error {
				return studioHelper.updateStudioOrganization(studioOrgID, req)
			},
		})
	}

	// create oauth client for wallet
	{
		req := createOAuthClientRequest{
			ID:              param.Studio.Org.Wallet.OauthClientID,
			Domain:          param.Studio.Org.Wallet.OauthClientDomain,
			IsPrivileged:    true,
			Name:            param.Studio.Org.Wallet.OauthClientName,
			MainLogo:        defaultOauthClientMainLogo,
			SupportAddress:  param.Studio.Org.AdminEmail,
			SquareLogo:      defaultOauthClientSquareLogo,
			ApplicationType: "mobile_wallet",
		}
		if err := req.validate(); err != nil {
			return fmt.Errorf("failed to validate create oauth client for wallet request: %v", err)
		}
		events = append(events, event{
			name: "create oauth client for wallet",
			fn: func() error {
				return studioHelper.createOAuthClient(studioOrgID, req)
			},
		})
	}

	// invite user to org
	{
		if len(param.Studio.Org.Users) > 0 {
			events = append(events, event{
				name: "studio login",
				fn: func() error {
					return studioHelper.loginV2(adminUID)
				},
			})
		}
		for index, u := range param.Studio.Org.Users {
			req := inviteUserRequest{
				InviteeEmail:    u.Email,
				InviteeName:     u.Name,
				InviteeMemberID: u.MemberID,
				Roles:           u.Roles,
			}

			if err := req.validate(); err != nil {
				return fmt.Errorf("failed to validate invite user request: %v", err)
			}
			events = append(events, event{
				name: fmt.Sprintf("invite user[%d] %s to org", index, u.Name),
				fn: func() error {
					return studioHelper.inviteUser(studioOrgID, req)
				},
			})
		}
	}

	{
		if isComplyflowEnabled {
			// oauth client for complyflow
			{
				req := createOAuthClientRequest{
					ID:              param.Studio.Org.Complyflow.OauthClientID,
					Domain:          param.Studio.Org.Complyflow.OauthClientDomain,
					IsPrivileged:    true,
					Name:            param.Studio.Org.Complyflow.OauthClientName,
					MainLogo:        defaultOauthClientMainLogo,
					SupportAddress:  param.Studio.Org.AdminEmail,
					SquareLogo:      defaultOauthClientSquareLogo,
					ApplicationType: "complyflow",
				}
				if err := req.validate(); err != nil {
					return fmt.Errorf("failed to validate create oauth client for comyflow request: %v", err)
				}
				events = append(events, event{
					name: "create oauth client for comyflow",
					fn: func() error {
						return studioHelper.createOAuthClient(studioOrgID, req)
					},
				})
			}
		}
	}

	{
		if isMarketEnabled {
			// oauth client for market
			{
				req := createOAuthClientRequest{
					ID:              param.Studio.Org.Market.OauthClientID,
					Domain:          studioHelper.storeHost,
					IsPrivileged:    true,
					Name:            param.Studio.Org.Market.OauthClientName,
					MainLogo:        defaultOauthClientMainLogo,
					SupportAddress:  param.Studio.Org.AdminEmail,
					SquareLogo:      defaultOauthClientSquareLogo,
					ApplicationType: "market",
				}
				if err := req.validate(); err != nil {
					return fmt.Errorf("failed to validate create oauth client for market request: %v", err)
				}
				events = append(events, event{
					name: "create oauth client for market",
					fn: func() error {
						return studioHelper.createOAuthClient(studioOrgID, req)
					},
				})
			}

			// market
			{
				req := createMarketplaceRequest{
					OauthClientID:         param.Studio.Org.Market.OauthClientID,
					Title:                 param.Studio.Org.Market.Title,
					MarketCode:            param.Studio.Org.Market.MarketCode,
					Logo:                  param.Studio.Org.Market.Logo,
					Introduction:          param.Studio.Org.Market.Introduction,
					PaymentMethod:         param.Studio.Org.Market.PaymentMethod,
					PaymentCurrency:       param.Studio.Org.Market.PaymentCurrency,
					BankName:              param.Studio.Org.Market.BankName,
					BranchName:            param.Studio.Org.Market.BranchName,
					BankAccount:           param.Studio.Org.Market.BankAccount,
					BankAccountHolderName: param.Studio.Org.Market.BankAccountHolderName,
					PaymentExpirationSec:  param.Studio.Org.Market.PaymentExpirationSec,
				}
				if err := req.validate(); err != nil {
					return fmt.Errorf("failed to validate create marketplace request: %v", err)
				}

				events = append(events, event{
					name: "create marketplace",
					fn: func() error {
						return studioHelper.createMarketplace(studioOrgID, req)
					},
				})
			}
		}
	}

	for _, e := range events {
		fmt.Println(strings.Repeat("=", 10), "running event:", e.name)
		if param.DryRun {
			continue
		}
		if err := e.fn(); err != nil {
			return err
		}
	}

	if param.DryRun {
		fmt.Println("dry run finished")
	}

	return nil
}

func main() {
	if err := wrapMain(); err != nil {
		fmt.Printf("failed to create organization: %v\n", err)
		return
	}

	fmt.Println("OK")
}

type cfg struct {
	DryRun     bool `yaml:"dryRun"`
	Compliance struct {
		GofactToken string `yaml:"gofactToken"`
		Org         struct {
			ExistingOrgID *int   `yaml:"existingOrgID"`
			Code          string `yaml:"code"`
			Name          string `yaml:"name"`
			Contact       struct {
				Name  string `yaml:"name"`
				Email string `yaml:"email"`
				Phone string `yaml:"phone"`
			} `yaml:"contact"`
			Plan              int `yaml:"plan"`
			SubscriptionRange struct {
				StartDate string `yaml:"startDate"`
				EndDate   string `yaml:"endDate"`
				Points    int    `yaml:"points"`
			} `yaml:"subscriptionRange"`
		} `yaml:"org"`
	} `yaml:"compliance"`
	Studio struct {
		InternalToken string `yaml:"internalToken"`
		Org           struct {
			Admin struct {
				ExistingAdminUID *string `yaml:"existingAdminUID"`
				Email            string  `yaml:"email"`
				Phone            string  `yaml:"phone"`
			}
			Users []struct {
				Email    string   `yaml:"email"`
				Name     string   `yaml:"name"`
				MemberID string   `yaml:"memberID"`
				Roles    []string `yaml:"roles"`
			} `yaml:"users"`
			AdminEmail string              `yaml:"adminEmail"`
			AdminName  string              `yaml:"adminName"`
			Name       string              `yaml:"name"`
			Modules    map[string][]string `yaml:"modules"`
			Wallet     struct {
				OauthClientID     string `yaml:"oauthClientID"`
				OauthClientName   string `yaml:"oauthClientName"`
				OauthClientDomain string `yaml:"oauthClientDomain"`
			} `yaml:"wallet"`
			Complyflow *struct {
				OauthClientID     string `yaml:"oauthClientID"`
				OauthClientName   string `yaml:"oauthClientName"`
				OauthClientDomain string `yaml:"oauthClientDomain"`
			} `yaml:"complyflow"`
			Market struct {
				MarketCode            string `yaml:"marketCode"`
				Title                 string `yaml:"title"`
				Logo                  string `yaml:"logo"`
				Introduction          string `yaml:"introduction"`
				PaymentMethod         string `yaml:"paymentMethod"`
				PaymentCurrency       string `yaml:"paymentCurrency"`
				BankName              string `yaml:"bankName"`
				BranchName            string `yaml:"branchName"`
				BankAccount           string `yaml:"bankAccount"`
				PaymentExpirationSec  int    `yaml:"paymentExpirationSec"`
				BankAccountHolderName string `yaml:"bankAccountHolderName"`
				OauthClientID         string `yaml:"oauthClientID"`
				OauthClientName       string `yaml:"oauthClientName"`
			} `yaml:"market"`
		} `yaml:"org"`
	} `yaml:"studio"`
}

//go:embed config.yaml
var embedFS embed.FS

func readConfig() (*cfg, error) {
	var c cfg
	config, err := embedFS.ReadFile("config.yaml")
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %v", err)
	}
	if err := yaml.Unmarshal(config, &c); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config file: %v", err)
	}

	return &c, nil
}

type complianceOrganizationHelper struct {
	host        string
	gofactToken string
}

func (c complianceOrganizationHelper) validate() error {
	if c.host == "" {
		return fmt.Errorf("missing compliance host")
	}
	if c.gofactToken == "" {
		return fmt.Errorf("missing compliance gofact token")
	}
	return nil
}

type createComplianceOrganizationRequest struct {
	OrgCode      string `json:"org_code"`
	Name         string `json:"name"`
	EmailAddress string `json:"email_address"`
	Contact      struct {
		Name         string `json:"name"`
		PhoneNumber  string `json:"phone_number"`
		EmailAddress string `json:"email_address"`
	} `json:"contact"`
	Plan              int `json:"plan"`
	SubscriptionRange struct {
		StartDate string `json:"startDate"`
		EndDate   string `json:"endDate"`
		Points    string `json:"points"`
	} `json:"subscriptionRange"`
}

func (req createComplianceOrganizationRequest) validate() error {
	if req.OrgCode == "" {
		return fmt.Errorf("org code is required")
	}
	if req.Name == "" {
		return fmt.Errorf("name is required")
	}
	if req.EmailAddress == "" {
		return fmt.Errorf("contact email is required")
	}
	if req.Plan == 0 {
		return fmt.Errorf("plan is required")
	}
	return nil
}

func (c complianceOrganizationHelper) createOrganization(
	req createComplianceOrganizationRequest) (orgID int, err error) {
	var resp struct {
		ID int `json:"id"`
	}

	restyResp, err := resty.New().
		SetBaseURL(c.host).NewRequest().
		SetHeader("accept", "application/vnd.kryptogo.v2+json").
		SetHeader("content-type", "application/json").
		SetHeader("gofact-token", c.gofactToken).
		SetBody(req).
		SetResult(&resp).Post("/auth/organization")
	if err != nil {
		return 0, fmt.Errorf("failed to create compliance organization: %v\n resp: %v", err, restyResp.String())
	}

	if restyResp.StatusCode() >= 300 {
		return 0, fmt.Errorf("failed to create compliance organization: %v\n resp: %v", err, restyResp.String())
	}

	return resp.ID, nil
}

type createComplianceContractRequest struct {
	Plan           int    `json:"plan"`
	StartDate      string `json:"start_date"`
	EndDate        string `json:"end_date"`
	NumberOfPoints int    `json:"points"`
}

func (req createComplianceContractRequest) validate() error {
	if req.Plan == 0 {
		return fmt.Errorf("plan is required")
	}
	if req.StartDate == "" {
		return fmt.Errorf("start date is required")
	}
	if req.EndDate == "" {
		return fmt.Errorf("end date is required")
	}
	if req.NumberOfPoints == 0 {
		return fmt.Errorf("number of points is required")
	}
	return nil
}

func (c complianceOrganizationHelper) createContract(orgID *int,
	req createComplianceContractRequest) (err error) {
	restyResp, err := resty.New().
		SetBaseURL(c.host).NewRequest().
		SetHeader("accept", "application/vnd.kryptogo.v2+json").
		SetHeader("content-type", "application/json").
		SetHeader("gofact-token", c.gofactToken).
		SetBody(req).
		Post(fmt.Sprintf("/auth/organization/%d/contract", *orgID))
	if err != nil {
		return fmt.Errorf("failed to create contract: %v\n resp: %v", err, restyResp.String())
	}

	if restyResp.StatusCode() >= 300 {
		return fmt.Errorf("failed to create contract: %v\n resp: %v", err, restyResp.String())
	}

	return nil
}

// type sendComplianceAdminResetPasswordEmailRequest struct {
// }

// not implemented
// func (c complianceOrganizationHelper) sendAdminResetPasswordEmail(
// 	req sendComplianceAdminResetPasswordEmailRequest) (err error) {
// 	return nil
// }

// not implemented
// func (c complianceOrganizationHelper) createFromConfig() (err error) {
// 	return nil
// }

type studioOrganizationHelper struct {
	host          string
	storeHost     string
	internalToken string
	studioTokenV2 string
}

func (s studioOrganizationHelper) validate() error {
	if s.host == "" {
		return fmt.Errorf("missing kg host")
	}
	if s.storeHost == "" {
		return fmt.Errorf("missing kg store host")
	}
	if s.internalToken == "" {
		return fmt.Errorf("missing kg internal token")
	}
	return nil
}

type createUserRequest struct {
	Email string `json:"email"`
	Phone string `json:"phone"`
}

func (req createUserRequest) validate() error {
	if req.Email == "" && req.Phone == "" {
		return fmt.Errorf("missing email or phone")
	}
	if req.Email != "" && req.Phone != "" {
		return fmt.Errorf("only one of email or phone can be set")
	}
	return nil
}

func (s studioOrganizationHelper) createUser(
	req createUserRequest) (uid string, err error) {
	var resp struct {
		Data struct {
			UID string `json:"uid"`
		} `json:"data"`
	}

	restyResp, err := resty.New().
		SetBaseURL(s.host).NewRequest().
		SetHeader("KG-INTERNAL-TOKEN", s.internalToken).
		SetBody(req).
		SetResult(&resp).Post("/_v/users")
	if err != nil {
		return "", fmt.Errorf("failed to create user: %v\n resp: %v", err, restyResp.String())
	}

	if restyResp.StatusCode() >= 300 {
		return "", fmt.Errorf("failed to create user: %v\n resp: %v", err, restyResp.String())
	}

	return resp.Data.UID, nil
}

type createStudioOrganizationRequest struct {
	Name                     string `json:"name"`
	AdminUID                 string `json:"uid"`
	AdminName                string `json:"admin_name"`
	Email                    string `json:"email"`
	ComplianceOrganizationID int    `json:"compliance_organization_id"`
}

func (req createStudioOrganizationRequest) validate() error {
	if req.Name == "" {
		return fmt.Errorf("missing name")
	}
	if req.AdminName == "" {
		return fmt.Errorf("missing admin name")
	}
	if req.Email == "" {
		return fmt.Errorf("missing email")
	}
	return nil
}

func (s studioOrganizationHelper) createOrganization(cmpOrgID *int, adminUID *string,
	req createStudioOrganizationRequest) (orgID int, err error) {

	req.ComplianceOrganizationID = *cmpOrgID
	req.AdminUID = *adminUID

	var resp struct {
		Data struct {
			ID int `json:"id"`
		} `json:"data"`
	}

	restyResp, err := resty.New().
		SetBaseURL(s.host).NewRequest().
		SetHeader("KG-INTERNAL-TOKEN", s.internalToken).
		SetBody(req).
		SetResult(&resp).Post("/_v/studio/organizations")
	if err != nil {
		return 0, fmt.Errorf("failed to create organization: %v\n resp: %v", err, restyResp.String())
	}

	if restyResp.StatusCode() >= 300 {
		return 0, fmt.Errorf("failed to create organization: %v\n resp: %v", err, restyResp.String())
	}

	return resp.Data.ID, nil
}

type createOAuthClientRequest struct {
	ID              string `json:"id"`
	Domain          string `json:"domain"`
	IsPrivileged    bool   `json:"is_privileged"`
	Name            string `json:"name"`
	MainLogo        string `json:"main_logo"`
	SupportAddress  string `json:"support_address"`
	OrganizationID  int    `json:"organization_id"`
	SquareLogo      string `json:"square_logo"`
	ApplicationType string `json:"application_type"`
}

func (req createOAuthClientRequest) validate() error {
	if req.ID == "" {
		return fmt.Errorf("missing id")
	}
	if req.Domain == "" {
		return fmt.Errorf("missing domain")
	}
	if req.Name == "" {
		return fmt.Errorf("missing name")
	}
	if req.MainLogo == "" {
		return fmt.Errorf("missing main logo")
	}
	if req.SupportAddress == "" {
		return fmt.Errorf("missing support address")
	}
	if req.SquareLogo == "" {
		return fmt.Errorf("missing square logo")
	}
	if req.ApplicationType == "" {
		return fmt.Errorf("missing application type")
	}

	return nil
}

func (s studioOrganizationHelper) createOAuthClient(orgID *int,
	req createOAuthClientRequest) (err error) {
	req.OrganizationID = *orgID
	restyResp, err := resty.New().
		SetBaseURL(s.host).NewRequest().
		SetHeader("KG-INTERNAL-TOKEN", s.internalToken).
		SetBody(req).
		Put("/_v/oauth/configs")
	if err != nil {
		return fmt.Errorf("failed to create oauth client: %v\n resp: %v", err, restyResp.String())
	}

	if restyResp.StatusCode() >= 300 {
		return fmt.Errorf("failed to create oauth client: %v\n resp: %v", err, restyResp.String())
	}

	return nil
}

type updateStudioOrganizationRequest struct {
	SignAlertThreshold float64 `json:"sign_alert_threshold"`
	Module             struct {
		User360       []string `json:"user_360"`
		WalletBuilder []string `json:"wallet_builder"`
		AssetPro      []string `json:"asset_pro"`
		NFTBoost      []string `json:"nft_boost"`
		Compliance    []string `json:"compliance"`
		Admin         []string `json:"admin"`
	} `json:"module"`
	// AssetProApprovalConfig string `json:"asset_pro_approval_type"`
	// IconURL                string `json:"icon_url"`
}

func (s *studioOrganizationHelper) loginV2(
	adminUID *string) (err error) {
	var resp struct {
		Data struct {
			StudioToken string `json:"studio_token"`
		} `json:"data"`
	}

	restyResp, err := resty.New().
		SetBaseURL(s.host).NewRequest().
		SetHeader("KG-INTERNAL-TOKEN", s.internalToken).
		SetHeader("KG-DEV-UID", *adminUID).
		SetResult(&resp).Post("/v1/studio/login_v2")
	if err != nil {
		return fmt.Errorf("failed to create organization: %v\n resp: %v", err, restyResp.String())
	}

	if restyResp.StatusCode() >= 300 {
		return fmt.Errorf("failed to create organization: %v\n resp: %v", err, restyResp.String())
	}

	s.studioTokenV2 = resp.Data.StudioToken

	return nil
}

func (req updateStudioOrganizationRequest) validate() error {
	if req.SignAlertThreshold == 0 {
		return fmt.Errorf("missing sign alert threshold")
	}
	return nil
}

func (s studioOrganizationHelper) updateStudioOrganization(orgID *int,
	req updateStudioOrganizationRequest) (err error) {

	restyResp, err := resty.New().
		SetBaseURL(s.host).NewRequest().
		SetHeader("KG-INTERNAL-TOKEN", s.internalToken).
		SetBody(req).
		Put(fmt.Sprintf("/_v/studio/organization/%d", *orgID))
	if err != nil {
		return fmt.Errorf("failed to update organization: %v\n resp: %v", err, restyResp.String())
	}

	if restyResp.StatusCode() >= 300 {
		return fmt.Errorf("failed to update organization: %v\n resp: %v", err, restyResp.String())
	}

	return nil
}

type inviteUserRequest struct {
	InviteeEmail    string   `json:"email"`
	InviteeName     string   `json:"name"`
	InviteeMemberID string   `json:"member_id"`
	Roles           []string `json:"roles"`
}

func (req inviteUserRequest) validate() error {
	if req.InviteeEmail == "" {
		return fmt.Errorf("missing invitee email")
	}
	if req.InviteeName == "" {
		return fmt.Errorf("missing invitee name")
	}
	if req.InviteeMemberID == "" {
		return fmt.Errorf("missing invitee member id")
	}
	if len(req.Roles) == 0 {
		return fmt.Errorf("missing roles")
	}
	return nil
}

func (s studioOrganizationHelper) inviteUser(orgID *int,
	req inviteUserRequest) (err error) {
	restyResp, err := resty.New().
		SetBaseURL(s.host).NewRequest().
		SetHeader("KG-STUDIO-TOKEN-V2", s.studioTokenV2).
		SetBody(req).
		Post(fmt.Sprintf("/v1/studio/organization/%d/users", *orgID))
	if err != nil {
		return fmt.Errorf("failed to invite user: %v\n resp: %v", err, restyResp.String())
	}

	if restyResp.StatusCode() >= 300 {
		return fmt.Errorf("failed to invite user: %v\n resp: %v", err, restyResp.String())
	}

	return nil
}

type createMarketplaceRequest struct {
	OauthClientID         string `json:"oauth_client_id"`
	Title                 string `json:"title"`
	MarketCode            string `json:"market_code"`
	Logo                  string `json:"logo"`
	Introduction          string `json:"introduction"`
	PaymentMethod         string `json:"payment_method"`
	PaymentCurrency       string `json:"payment_currency"`
	BankName              string `json:"bank_name"`
	BranchName            string `json:"branch_name"`
	BankAccount           string `json:"bank_account"`
	BankAccountHolderName string `json:"bank_account_holder_name"`
	PaymentExpirationSec  int    `json:"payment_expiration_sec"`
}

func (req createMarketplaceRequest) validate() error {
	if req.OauthClientID == "" {
		return fmt.Errorf("missing oauth client id")
	}
	if req.Title == "" {
		return fmt.Errorf("missing title")
	}
	if req.MarketCode == "" {
		return fmt.Errorf("missing market code")
	}
	if req.Logo == "" {
		return fmt.Errorf("missing logo")
	}
	if req.Introduction == "" {
		return fmt.Errorf("missing introduction")
	}
	if req.PaymentMethod == "" {
		return fmt.Errorf("missing payment method")
	}
	if req.PaymentCurrency == "" {
		return fmt.Errorf("missing payment currency")
	}
	if req.BankName == "" {
		return fmt.Errorf("missing bank name")
	}
	if req.BranchName == "" {
		return fmt.Errorf("missing branch name")
	}
	if req.BankAccount == "" {
		return fmt.Errorf("missing bank account")
	}
	if req.BankAccountHolderName == "" {
		return fmt.Errorf("missing bank account holder name")
	}
	if req.PaymentExpirationSec == 0 {
		return fmt.Errorf("missing payment expiration sec")
	}
	return nil
}

func (s studioOrganizationHelper) createMarketplace(orgID *int,
	req createMarketplaceRequest) (err error) {
	restyResp, err := resty.New().
		SetBaseURL(s.host).NewRequest().
		SetHeader("KG-INTERNAL-TOKEN", s.internalToken).
		SetBody(req).
		Put(fmt.Sprintf("/_v/studio/organization/%d/asset_pro/market", *orgID))
	if err != nil {
		return fmt.Errorf("failed to create marketplace: %v\n resp: %v", err, restyResp.String())
	}

	if restyResp.StatusCode() >= 300 {
		return fmt.Errorf("failed to create marketplace: %v\n resp: %v", err, restyResp.String())
	}

	return nil
}
