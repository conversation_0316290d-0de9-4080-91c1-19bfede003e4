package main

import (
	"context"
	"log"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/nft"
)

/**
 * Usage: ENV=dev \
 * 	CHAIN_ID=ethereum \
 * 	CONTRACT_ADDRESS=****************************************** \
 * 	go run cmd/dev/update-metadata/main.go
 */
func main() {
	chainID := config.GetString("CHAIN_ID")
	contractAddress := config.GetString("CONTRACT_ADDRESS")
	tokenID := config.GetString("TOKEN_ID")

	if chainID == "" || contractAddress == "" {
		log.Println("CHAIN_ID & CONTRACT_ADDRESS cannot empty")
		return
	}
	nft.UpdateMetadataFromOpenseaBatch(context.Background(), chainID, contractAddress, tokenID)
}
