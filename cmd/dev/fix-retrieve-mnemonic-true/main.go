package main

import (
	"context"
	"fmt"
	"regexp"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

func main() {
	errorCnt, emptyCnt := 0, 0
	errorUserIds := make([]string, 0)
	users, kgErr := rdb.GormRepo().UserAll(context.Background(), "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if kgErr != nil {
		fmt.Printf("rdb.UserAll() error: %v\n", kgErr)
		return
	}
	var cnt int
	for _, user := range users {
		if cnt%1000 == 0 {
			fmt.Println("Processed: ", cnt)
		}
		wallets := user.Wallets
		if wallets == nil {
			continue
		}

		if !wallets.RetrieveMnemonic {
			continue
		}

		group := wallets.WalletGroups[0]
		seed := group.EncryptedSeedPhrase
		hexRegex := regexp.MustCompile(`^[0-9a-f]+$`)
		if (seed == "") || hexRegex.MatchString(seed) {
			errorUserIds = append(errorUserIds, user.UID)
			errorCnt++
			if seed == "" {
				emptyCnt++
			}
		}
	}
	fmt.Println("errorCnt: ", errorCnt)
	fmt.Println("emptyCnt: ", emptyCnt)

	// set retrieve_mnemonic to false for error users
	for _, userID := range errorUserIds {
		data, _ := rdb.GormRepo().GetUser(context.Background(), userID, "", true, &domain.UserPreloads{
			WithWallets: true,
		})
		data.Wallets.RetrieveMnemonic = false
		_ = rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID: userID,
			},
			Wallets: data.Wallets,
		})
	}
}
