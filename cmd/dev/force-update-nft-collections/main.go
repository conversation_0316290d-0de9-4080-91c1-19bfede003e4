package main

import (
	"context"
	"encoding/json"
	"log"

	"github.com/kryptogo/kg-wallet-backend/pkg/apis"
	openseaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/opensea-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/dao"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

func main() {
	apis.InitDefault()
	walletDB := rdb.Get()
	query := dao.Use(walletDB).NftCollection
	nfts, err := query.
		Where(query.TotalVolume.Gt(0)).
		Where(query.ThirtyDayVolume.Eq(0)).
		Order(query.TotalVolume.Desc()).
		Find()
	if err != nil {
		log.Println(err.Error())
		return
	}

	dataToUpdate := make([]model.NftCollection, 0)
	for _, v := range nfts {
		if v.Slug == "" {
			continue
		}
		log.Println(v.Slug)

		stats, err := openseaapi.Get().GetStats(context.Background(), v.Slug)
		if err != nil {
			log.Println(err.Error())
			continue
		}
		statsBytes, _ := json.Marshal(stats)
		statsStr := string(statsBytes)
		nftCollection := model.NftCollection{
			Slug:             v.Slug,
			FloorPriceSymbol: "ETH",
			TotalVolume:      stats.TotalVolume,
			ThirtyDayVolume:  stats.ThirtyDayVolume,
			Stats:            &statsStr,
		}
		nftCollection.SetFloorPrice(stats.FloorPrice)
		dataToUpdate = append(dataToUpdate, nftCollection)
		if len(dataToUpdate) >= 10 {
			if err := rdb.BatchUpdateNftFloorPrice(context.Background(), &dataToUpdate); err != nil {
				kglog.Errorf("fail to batch update nft floor price: %v", err)
			}
			dataToUpdate = make([]model.NftCollection, 0)
		}
	}
	if len(dataToUpdate) > 0 {
		if err := rdb.BatchUpdateNftFloorPrice(context.Background(), &dataToUpdate); err != nil {
			kglog.Errorf("fail to batch update nft floor price: %v", err)
		}
	}
}
