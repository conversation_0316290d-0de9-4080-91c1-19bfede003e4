package main

// Usage: go run cmd/dev/encrypt-by-kms/main.go -data signer_key
// Result: Encrypted data by KMS, and encoded by base64
import (
	"context"
	"encoding/base64"
	"flag"
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/kms"
)

func main() {
	// 透過參數拿到明文
	plaintext := flag.String("data", "test", "Data that needs to be encrypted")
	flag.Parse()

	kmsKeyName := kms.GetKmsKeyName("mnemonic", "key")
	encrypedData, err := kms.EncryptSymmetric(context.Background(), kmsKeyName, []byte(*plaintext))
	if err != nil {
		panic(err)
	}
	encData := base64.StdEncoding.EncodeToString(encrypedData)
	fmt.Println("encoded encrypted data:", encData)

}
