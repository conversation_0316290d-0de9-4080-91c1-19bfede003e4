package main

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

func main() {
	c := context.Background()
	clientID := application.GetDefaultClientID(c)
	applicationID, apiTokenName, _ := application.GetSendbirdAppIDAndAPIToken(c, clientID)

	sendbirdClient := sendbirdapi.NewClient(applicationID, apiTokenName)
	chatroomService := chatroom.GetService(sendbirdClient)

	// range over firestore user
	var lastUserID *string
	var users []*domain.UserData
	var kgErr *code.KGError
	for {
		users, kgErr = rdb.GormRepo().UserListAfterWithLimit(context.Background(), lastUserID, domain.BatchSize, "", false, &domain.UserPreloads{
			WithAvatar: true,
		})
		if kgErr != nil {
			kglog.Error(kgErr.String())
			return
		}
		for _, user := range users {
			_, _, err := chatroomService.CreateAUser(c, user)
			if err != nil {
				kglog.ErrorWithData("chatroom create a user error", map[string]interface{}{
					"uid": user.UID,
					"err": err.Error(),
				})
				continue
			}
		}
		lastUserID = &users[len(users)-1].UID
		if len(users) < domain.BatchSize {
			break
		}
	}
}
