package main

import (
	"context"
	"log"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/nft"
)

var syncTTL = time.Duration(0)

/**
 * Usage: ENV=dev ETH_ADDRESS=****************************************** go run cmd/dev/force-update-user-nft-asset/main.go
 */
func main() {
	ethAddress := config.GetString("ETH_ADDRESS")
	if ethAddress == "" {
		log.Println("ETH_ADDRESS is empty")
		return
	}

	nft.UpdateNftAssetsByAddress(context.Background(), ethAddress, syncTTL, nft.UpdatePriorityHigh, []string{})
}
