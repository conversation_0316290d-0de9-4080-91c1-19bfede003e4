package main

import (
	"context"
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	airdropclient "github.com/kryptogo/kg-wallet-backend/pkg/apis/airdrop-client"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/airdrop/module"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"gorm.io/gorm"
)

// setup
// 1. export GOOGLE_APPLICATION_CREDENTIALS={replace with firebase admin credential}
// 2. redis port forward
// 3. set env: alchemy api key, database, redis

var (
	chainID    = "matic"
	grantRole  = "MINTER_ROLE"
	walletType = "evm"
	fromOrgID  = 1

	// setting
	gasLimit = 200000
)

func init() {
	rdb.Init()

	organization.Init(organization.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})
}

func main() {
	ctx := context.Background()
	orgNftBoostMap := map[int][]string{
		4:  {"PUDU is coming test", "Single Doge", "Ultraman from Secret Santa", "Estelle Shiba Sad Swimming"},
		10: {"Estelle from KryptoGO", "Maggie from KryptoGO", "Jacko from KryptoGO", "Fred from KryptoGO", "Jean from KryptoGO", "Harry from KryptoGO", "Kordan from KryptoGO", "Estelle from KryptoGO TEST", "thevaultcode", "thevaultcodetest", "KG Studio Launch", "Estelle Production", "KG test Shiba"},
	}

	// get from organization info
	fromOrgWallet, err := organization.GetOrgWallet(ctx, fromOrgID, walletType)
	if err != nil {
		panic(err)
	}

	// run transfer
	for toOrgID, collectionNames := range orgNftBoostMap {
		kglog.InfofCtx(ctx, "transfer to org: %d", toOrgID)

		toOrgWallet, err := organization.GetOrgWallet(ctx, toOrgID, walletType)
		if err != nil {
			panic(err)
		}
		toOrg, kgErr := rdb.GormRepo().GetOrganizationByID(ctx, toOrgID)
		if kgErr != nil {
			panic(kgErr)
		}

		for _, name := range collectionNames {
			kglog.InfofCtx(ctx, "transfer collection: %s", name)
			var contractAddress string
			err = rdb.GetWith(ctx).Table("studio_nft_projects").Select("contract_address").Where("organization_id = ?", fromOrgID).Where("collection_name = ?", name).Row().Scan(&contractAddress)
			if err != nil {
				panic(err)
			}
			kglog.InfofCtx(ctx, "contract address: %s, from: %s, to: %s", contractAddress, fromOrgWallet.WalletAddress, toOrgWallet.WalletAddress)
			err = transferOwnership(fromOrgID, fromOrgWallet.WalletAddress, toOrgWallet.WalletAddress, contractAddress)
			if err != nil {
				panic(err)
			}
			transferNftBoostProject(ctx, toOrgID, toOrg.Name, toOrgWallet.WalletAddress, contractAddress)
		}
	}

}

func transferOwnership(fromOrgID int, fromAddress, toAddress, contractAddress string) error {
	var err error
	var txOpts *bind.TransactOpts
	defer func() {
		if err != nil && txOpts != nil {
			// return nonce to pool
			kglog.Error(err)

			nonceSvc := tx.NewNonceService(chainID, fromAddress)
			err = nonceSvc.ReturnToPool(int32(txOpts.Nonce.Uint64()))
			if err != nil {
				kglog.Error(err)
			}
		}
	}()
	chainClient, err := airdropclient.CreateChainClient(chainID)
	if err != nil {
		return err
	}
	url := chainClient.GetAPIURL()
	conn, err := ethclient.Dial(url)
	if err != nil {
		return err
	}

	// transfer ownership
	txOpts = composeTxOpts(context.Background(), fromOrgID, chainID, fromAddress)
	session, err := module.CreateAirdropSession(common.HexToAddress(contractAddress), conn, txOpts)
	if err != nil {
		return err
	}
	_, err = session.TransferOwnership(common.HexToAddress(toAddress))
	if err != nil {
		return err
	}

	// grant roles
	txOpts = composeTxOpts(context.Background(), fromOrgID, chainID, fromAddress)
	session, err = module.CreateAirdropSession(common.HexToAddress(contractAddress), conn, txOpts)
	if err != nil {
		return err
	}
	var role [32]byte
	copy(role[:], crypto.Keccak256Hash([]byte(grantRole)).Bytes())
	_, err = session.GrantRole(role, common.HexToAddress(toAddress))
	if err != nil {
		return err
	}

	return nil
}

func transferNftBoostProject(ctx context.Context, toOrgID int, toOrgName, toAddress, contractAddress string) {

	err := rdb.GetWith(ctx).Transaction(func(tx *gorm.DB) error {
		// update studio nft project
		updates := map[string]interface{}{
			"organization_id": toOrgID,
			"organization":    toOrgName,
		}
		err := tx.Model(&model.StudioNftProject{}).
			Where("contract_address = ?", contractAddress).
			Updates(updates).Error
		if err != nil {
			return err
		}

		// update airdrop event
		updates = map[string]interface{}{
			"from_address": toAddress,
		}
		err = tx.Model(&model.AirdropEvent{}).
			Where("contract_address = ?", contractAddress).
			Updates(updates).Error
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		panic(err)
	}
}

func composeTxOpts(ctx context.Context, fromOrgID int, chainID, fromAddress string) *bind.TransactOpts {
	gasPrice := util.HexToBigInt(module.GetGasPrice(ctx, chainID))
	nonce, err := getNonce(ctx, chainID, fromAddress)
	if err != nil {
		panic(err)
	}
	opts := &bind.TransactOpts{
		From:     common.HexToAddress(fromAddress),
		Value:    big.NewInt(0),
		GasPrice: gasPrice,
		GasLimit: uint64(gasLimit),
		Signer: func(address common.Address, tx *types.Transaction) (*types.Transaction, error) {
			return client.SignEVMTransaction(ctx, fromOrgID, chainID, tx) // use signing v2
		},
		Nonce: util.HexToBigInt(nonce),
	}
	return opts
}

func getNonce(ctx context.Context, chainID, fromAddress string) (string, error) {
	nonceSvc := tx.NewNonceService(chainID, fromAddress)
	nonce, err := nonceSvc.RetrieveMin(ctx)
	if err != nil {
		return "", err
	}

	nonceHex := fmt.Sprintf("0x%x", nonce)
	return nonceHex, nil
}
