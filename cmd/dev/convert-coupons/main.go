package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/fatih/color"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis"
	openseaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/opensea-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/mitchellh/mapstructure"
	"google.golang.org/api/iterator"
)

var defaultPublishTime = time.Unix(1640995200, 0) // 2022-01-01

/**
 * Usage: ENV=prod DOC_ID= SLUG= go run cmd/dev/convert-coupons/main.go
 */
/*
# luckybag
ENV=prod DOC_ID=polygon-****************************************** SLUG=kryptogo-demiverse-lucky-bag-2022 go run cmd/dev/convert-coupons/main.go

# luckybag alpha
ENV=prod DOC_ID=polygon-****************************************** SLUG=kryptogo-x-demiverse-lucky-bag-2022 go run cmd/dev/convert-coupons/main.go

# rendart
ENV=prod DOC_ID=ethereum-****************************************** SLUG=trac go run cmd/dev/convert-coupons/main.go

# MEKU
ENV=prod DOC_ID=polygon-****************************************** SLUG=meku-candy go run cmd/dev/convert-coupons/main.go

# (DEV) rendart x SVNA
ENV=dev DOC_ID=polygon-****************************************** SLUG=savannanft-alpha go run cmd/dev/convert-coupons/main.go

# (DEV) rendart x hunnft
ENV=dev DOC_ID=polygon-****************************************** SLUG=hunnft-alpha go run cmd/dev/convert-coupons/main.go
*/
func main() {
	apis.InitDefault()
	docID := config.GetString("DOC_ID")
	slug := config.GetString("SLUG")
	if docID == "" || slug == "" {
		color.Red("DOC_ID and SLUG are required")
		return
	}

	// project
	log.Println(color.BlueString("Converting Project..."))
	projectInfo := strings.Split(docID, "-")
	chainID := projectInfo[0]
	contractAddress := projectInfo[1]
	project, _, _ := rdb.ProjectByAddress(context.Background(), chainID, contractAddress)
	if project == nil {
		collection, err := openseaapi.Get().GetCollection(context.Background(), slug)
		if err != nil {
			log.Println(color.RedString("Cannot get collection:"), err)
			return
		}

		project = &model.DashboardProject{
			ChainID:         chainID,
			ContractAddress: contractAddress,
			CollectionSlug:  slug,
			Name:            &collection.Name,
			Description:     &collection.Description,
			ImageURL:        collection.ImageURL,
			LargeImageURL:   collection.LargeImageURL,
			OwnerAddress:    "0x0",
			Verified:        true,
		}
		_, err = rdb.CreateProject(context.Background(), project)
		if err != nil {
			log.Println(color.RedString("Cannot add project:"), err)
			return
		}
		log.Println(color.GreenString("Added project:"), project.ID)

	} else {
		log.Println(color.GreenString("Found project:"), project.ID)
	}

	// prizes (coupons)
	log.Println(color.BlueString("Converting Prizes..."))
	couponPath := fmt.Sprintf("nfts/%s/coupons", docID)
	iter := db.All(couponPath)
	couponToPrizeIDMap := map[string]int32{}
	traitValueToCouponMap := map[string]string{}
	for {
		docSS, err := iter.Next()
		if err == iterator.Done {
			log.Println("done")
			break
		}
		if err != nil {
			log.Println(color.RedString(err.Error()))
			continue
		}
		if docSS == nil {
			log.Println(color.RedString("docSS is nil"))
			continue
		}
		// skip if coupon id start with pid-
		if strings.Index(docSS.Ref.ID, "pid-") == 0 {
			log.Println("skip", docSS.Ref.ID)
			continue
		}

		couponID := docSS.Ref.ID
		coupon := &db.Coupon{}
		if err = mapstructure.Decode(docSS.Data(), coupon); err != nil {
			log.Println(color.RedString("Cannot decode coupon:"), err)
			continue
		}
		// if coupon.EndTime.Before(time.Now()) {
		// 	log.Println("Skip expired coupon:", couponID)
		// 	continue
		// }

		coupon.ID = couponID
		coupon.StartTimestamp = coupon.StartTime.Unix()
		coupon.EndTimestamp = coupon.EndTime.Unix()

		prize, _, err := rdb.PrizeByCustomID(context.Background(), project.ID, couponID)
		if err != nil {
			log.Println(color.RedString("Get prize err:"), err)
			continue
		}

		matchedTraitStr := ""
		if coupon.MatchedTrait.Value != nil {
			matchedBytes, _ := json.Marshal(coupon.MatchedTrait)
			matchedTraitStr = string(matchedBytes)
			traitValueToCouponMap[*coupon.MatchedTrait.Value] = couponID
		}

		if prize == nil {
			fixedDesc := strings.ReplaceAll(coupon.Description, " \\n", "\n")
			prize = &model.DashboardPrize{
				Title:           &coupon.Title,
				Detail:          &fixedDesc,
				Amount:          1,
				StartTime:       coupon.StartTime,
				EndTime:         cutEndTime(coupon.EndTime),
				ChainID:         project.ChainID,
				ContractAddress: project.ContractAddress,
				ProjectID:       project.ID,
				OwnerAddress:    "0x0",
				ImageURL:        &coupon.ImageURL,
				PublishTime:     &defaultPublishTime,
				ProjectName:     project.Name,
				CustomID:        couponID,
			}
			if matchedTraitStr != "" {
				prize.MatchedTrait = &matchedTraitStr
			}
			if coupon.URL != "" {
				prize.RedeemURL = &coupon.URL
			}
			if coupon.SameCode != "" {
				prize.SameCode = &coupon.SameCode
			}
			_, err = rdb.AddPrize(context.Background(), prize)
			if err != nil {
				log.Println(color.RedString("Cannot add prize:"), err)
				continue
			}

			log.Println(color.GreenString("Added prize:"), prize.ID, project.ID, couponID)
		} else {
			log.Println("Exist prize:", prize.ID, project.ID, couponID)
		}

		// for all tokens
		if coupon.MatchedTrait.Value == nil {
			// check if exists
			if rdb.HasPrizeToken(context.Background(), prize.ID, "all", project.ChainID, project.ContractAddress) {
				log.Println("Exist price for all tokens:", couponID)
				continue
			}
			// add prize->token table
			err := rdb.AddPrizeToken(context.Background(), prize.ID, "all", project.ChainID, project.ContractAddress)
			if err != nil {
				log.Println(color.RedString("Cannot add prize for all tokens:"), err)
				continue
			}
			log.Println(color.GreenString("Add prize for all tokens:"), couponID)
		}

		couponToPrizeIDMap[couponID] = prize.ID
	}

	log.Println(color.BlueString("Converting Token list and Logs..."))
	itemsPath := fmt.Sprintf("nfts/%s/items", docID)
	docs, err := db.All(itemsPath).GetAll()
	if err != nil {
		log.Println(color.RedString("Cannot get items:"), err)
		return
	}
	for _, docSS := range docs {
		if docSS == nil {
			log.Println(color.RedString("docSS is nil"))
			continue
		}
		tokenIDStr := docSS.Ref.ID
		log.Println(color.BlueString("TokenID:" + tokenIDStr))
		tokenID, err := strconv.ParseInt(tokenIDStr, 10, 64)
		if err != nil {
			log.Println(color.RedString("Cannot parse tokenID:"), err)
			continue
		}

		item := &db.NftItem{}
		_ = mapstructure.Decode(docSS.Data(), item)

		// 展開 prize -> token
		for _, trait := range item.ItemDetail.Traits {
			if trait.Value == nil {
				continue
			}
			couponID, exists := traitValueToCouponMap[*trait.Value]
			if !exists {
				log.Println(color.YellowString("Skip prize -> token, cause no couponID found:"), *trait.Value, tokenID)
				continue
			}
			prizeID, ok := couponToPrizeIDMap[couponID]
			if !ok {
				log.Println(color.YellowString("Skip prize -> token, cause no prizeID found:"), *trait.Value, tokenID)
				continue
			}
			// log.Println(color.GreenString("Save prize -> token:"), *trait.Value, tokenID, couponID, prizeID)
			// check if exists
			if rdb.HasPrizeToken(context.Background(), prizeID, strconv.Itoa(int(tokenID)), project.ChainID, project.ContractAddress) {
				log.Println("Exist price token:", couponID, tokenID)
				continue
			}
			// add prize->token table
			err := rdb.AddPrizeToken(context.Background(), prizeID, strconv.Itoa(int(tokenID)), project.ChainID, project.ContractAddress)
			if err != nil {
				log.Println(color.RedString("Cannot add prize token:"), err)
				continue
			}
		}

		// redeem logs
		if item.RedeemStatus == nil || len(*item.RedeemStatus) == 0 {
			continue
		}
		for couponID, redeemStatus := range *item.RedeemStatus {
			// skip if coupon id start with pid-
			if strings.Index(couponID, "pid-") == 0 {
				log.Println("skip", couponID)
				continue
			}
			// get prize id
			prizeID, ok := couponToPrizeIDMap[couponID]
			if !ok {
				log.Println(color.YellowString("Skip redeem log, cause no prizeID found:"), couponID, tokenID, redeemStatus)
				continue
			}
			// check if exists
			if rdb.HasRedeemLog(context.Background(), project.ID, prizeID, strconv.Itoa(int(tokenID))) {
				log.Println("Skip log:", couponID, tokenID, redeemStatus)
				continue
			}
			// add new log
			err = rdb.SaveRedeemLog(context.Background(), project.ID, int32(prizeID), 1, strconv.Itoa(int(tokenID)), defaultPublishTime)
			if err != nil {
				log.Println("Add log err:", err)
				continue
			}
			log.Println(color.GreenString("Add log:"), couponID, tokenID, redeemStatus)
		}
	}
}

func cutEndTime(endTime time.Time) time.Time {
	maxTime, _ := time.Parse("2006-01-02", "2038-01-19")
	if endTime.After(maxTime) {
		return maxTime
	}
	return endTime
}
