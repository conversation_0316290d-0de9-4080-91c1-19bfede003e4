package main

import (
	"bufio"
	"context"
	"encoding/base64"
	"encoding/csv"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/dashboard"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

const (
	chainID = "polygon"
)

var (
	csvFilePath     string
	contractAddress string
	ownerAddress    string
	imageFolder     string
	projectName     = "washinmura-nextt-partner"
	// projectDesc     = "Hi 和心村 NextT Partner，首先恭喜我們共同入選！誠心邀請您成為和心村的聯盟夥伴，讓我們在未來一同努力成長吧！"
)

func init() {
	// 使用方法 -o *
	flag.StringVar(&csvFilePath, "f", "data.csv", "the prize csv file path")
	flag.StringVar(&contractAddress, "c", "******************************************", "contractAddress")
	flag.StringVar(&ownerAddress, "o", "******************************************", "ownerAddress")
	flag.StringVar(&imageFolder, "i", "images", "prize images folder path")

	flag.Usage = flag.PrintDefaults
}

func main() {
	flag.Parse()

	colIndex := map[string]int{
		"merchant_contact": -1,
		"merchant_name":    -1,
		"title":            -1,
		"amount":           -1,
		"start_time":       -1,
		"end_time":         -1,
		"detail":           -1,
		"image_url":        -1,
	}

	// 1. Load csv file
	records := loadData(csvFilePath)
	header := records[0]
	records = records[1:]
	for i, col := range header {
		if _, ok := colIndex[col]; ok {
			colIndex[col] = i
		}
	}
	publishTime := time.Now()
	projectID := int32(52) //createProject("https://wallet-static.kryptogo.com/public/assets/images/WNP.png")

	for _, record := range records {
		startTime, _ := time.Parse("2006/1/2", record[colIndex["start_time"]])
		endTime, _ := time.Parse("2006/1/2", record[colIndex["end_time"]])
		amount, _ := strconv.Atoi(record[colIndex["amount"]])
		imageURL, _ := saveImage(projectID, record[colIndex["title"]])
		limitation := ""
		prize := model.DashboardPrize{
			ChainID:         chainID,
			ContractAddress: contractAddress,
			OwnerAddress:    ownerAddress,
			ProjectID:       projectID,
			ProjectName:     &projectName,
			PublishTime:     &publishTime,
			Title:           &record[colIndex["title"]],
			Amount:          int32(amount),
			StartTime:       startTime,
			EndTime:         endTime,
			Detail:          &record[colIndex["detail"]],
			ImageURL:        &imageURL,
			MerchantName:    &record[colIndex["merchant_name"]],
			MerchantContact: &record[colIndex["merchant_contact"]],
			Limitation:      &limitation,
		}
		insertPrize(&prize)
		fmt.Println(prize.ID)
		_, err := db.SavePrizeData(&prize)
		if err != nil {
			kglog.Errorf("save prize data error: %v", err)
			continue
		}
		err = rdb.UpdatePrizePublishTime(context.Background(), int32(prize.ID), time.Now())
		if err != nil {
			kglog.Errorf("update prize publish time error: %v", err)
			continue
		}
	}

}

func loadData(filePath string) [][]string {
	f, err := os.Open(filePath)
	if err != nil {
		kglog.Fatalf("open file error: %v", err)
	}
	defer f.Close()

	csvReader := csv.NewReader(f)
	records, err := csvReader.ReadAll()
	if err != nil {
		kglog.Fatalf("read csv file error: %v", err)
	}
	return records
}

// func createProject(imageURL string) int32 {
// 	now := time.Now()
// 	project := model.DashboardProject{
// 		ChainID:         chainID,
// 		ContractAddress: contractAddress,
// 		OwnerAddress:    ownerAddress,
// 		CollectionSlug:  projectName,
// 		Name:            &projectName,
// 		Description:     &projectDesc,
// 		ImageURL:        imageURL,
// 		LargeImageURL:   imageURL,
// 		CreatedAt:       &now,
// 	}

// 	result := rdb.Get().Create(&project)
// 	if result.Error != nil {
// 		kglog.Fatalf("create project error: %v", result.Error)
// 	}
// 	return project.ID
// }

func insertPrize(prize *model.DashboardPrize) {
	result := rdb.Get().Create(prize)
	if result.Error != nil {
		kglog.Fatalf("create prize error: %v", result.Error)
	}
}

func saveImage(projectID int32, title string) (string, error) {
	pathMapping := map[string]string{
		"東京和心村 住宿 9 折優惠券 ":            "和心村01 - Ren Yee Quek.jpg",
		"每日精選紐西蘭酒款 Happy hour":        "紐西蘭酒款Happy hour - Ren Yee Quek.jpg",
		"紐西蘭 Tip Top冰淇淋":              "紐西蘭Tip Top冰淇淋 - Ren Yee Quek.jpg",
		"路易奇電力公司贊助紐西蘭頂級牛菲力佐香蒜奶油醬 1 份": "luigi - Ren Yee Quek.png",
		"招牌臺灣脆皮豬五花 兌換券":               "招牌臺灣脆皮豬五花 - Ren Yee Quek.jpg",
		"侯黑鍋物 手工川丸子 兌換券":              "手工川丸子 - Ren Yee Quek.jpg",
	}
	bytes, _ := getImageFromFilePath(fmt.Sprintf("%s/%s", imageFolder, pathMapping[title]))
	mimeType := http.DetectContentType(bytes)
	encoded := base64.StdEncoding.EncodeToString(bytes)
	fmt.Println("Uploading image:", title, pathMapping[title])
	return dashboard.StoragePrizeImage(projectID, string(encoded), mimeType)
}

func getImageFromFilePath(filePath string) ([]byte, error) {
	f, _ := os.Open(filePath)
	reader := bufio.NewReader(f)
	return io.ReadAll(reader)
}
