package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/kms"
	"github.com/kryptogo/kg-wallet-backend/service/user"
)

var (
	filePath string
	chainID  string
	logFile  *os.File
)

func init() {
	flag.StringVar(&filePath, "file", "", "Users' phone number file")
	flag.StringVar(&chainID, "chainID", "polygon", "blockchain chain ID")
	flag.Usage = usage
	flag.Parse()

	if chainID != "polygon" && chainID != "ethereum" {
		fmt.Println("Invalid chain ID: " + chainID)
		os.Exit(1)
	}
	var err error
	logFile, err = os.OpenFile("create_user.log", os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		kglog.Fatalf("error opening file: %v", err)
	}
	kglog.SetOutput(logFile)
}

func usage() {
	fmt.Fprintf(os.Stderr, "Usage: airdrop [options]\n")
	flag.PrintDefaults()
}

func createUser() []string {
	content, err := os.ReadFile(filePath)
	if err != nil {
		kglog.Fatal("Failed to read file: " + err.Error())
	}
	lines := strings.Split(string(content), "\n")

	kglog.Debugf("Total %d users in the list\n", len(lines))

	success := 0
	existing := 0
	ctx := context.Background()
	for _, phone := range lines {
		_, errCode, err := user.CreateUserIfNotExists(ctx, &firebase.UserParams{
			Phone: phone,
		}, true)
		if errCode == code.UserAlreadyExists {
			existing++
			continue
		} else if err != nil {
			log.Println("[Warning] Failed to create user with mnemonic: " + err.Error())
			continue
		}
		success++
	}
	kglog.Infof("[Creating mnemonic and addresses] Success: %d \n", success)
	kglog.Infof("[Creating mnemonic and addresses] Existing: %d \n", existing)
	kglog.Infof("[Creating mnemonic and addresses] Failure: %d \n", len(lines)-success-existing)
	return lines
}

func getUserAddressList(phones []string) []string {
	var userAddressList []string
	for _, phone := range phones {
		addresses, err := kms.GetAddressesByPhone(context.Background(), phone, "")
		if err != nil {
			kglog.Errorf("[warning] Failed to get user address. Phone: " + phone + " ,Err: " + err.String())
			continue
		}
		switch chainID {
		case "polygon":
			userAddressList = append(userAddressList, addresses[domain.Polygon].String())
		case "ethereum":
			userAddressList = append(userAddressList, addresses[domain.Ethereum].String())
		}
	}
	return userAddressList
}

func saveUserAddressList(userAddressList []string) {
	file, err := os.OpenFile("user_address_list.txt", os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		kglog.Fatalf("error opening file: %v", err)
	}
	content := strings.Join(userAddressList, "\n") + "\n"
	_, err = file.Write([]byte(content))
	if err != nil {
		kglog.Fatal("Failed to write file: " + err.Error())
	}
	// combine all the addresses into one array
	content = "[\"" + strings.Join(userAddressList, "\", \"") + "\"]\n"
	_, err = file.Write([]byte(content))
	if err != nil {
		kglog.Fatal("Failed to write file: " + err.Error())
	}
}

func main() {
	log.Println("Users creating...")
	phones := createUser()
	userAddressList := getUserAddressList(phones)
	saveUserAddressList(userAddressList)
}
