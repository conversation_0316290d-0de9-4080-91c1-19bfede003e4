package main

import (
	"context"
	"encoding/json"
	"errors"
	"log"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/apis"
	openseaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/opensea-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/dao"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

func main() {
	apis.InitDefault()

	walletDB := rdb.Get()
	query := dao.Use(walletDB).NftCollection
	nfts, err := query.
		Where(query.History.Eq(0)).
		Where(query.ModifiedAt.Lt(time.Now().Add(-48*time.Hour))).
		Order(
			query.TotalVolume.Desc(),
			query.FloorPrice.Desc(),
		).
		Find()
	if err != nil {
		log.Println(err.Error())
		return
	}
	log.Println("Count:", len(nfts))

	dataToUpdate := make([]model.NftCollection, 0)
	for _, v := range nfts {
		if v.Slug == "" {
			continue
		}
		log.Println(v.Slug)

		stats, err := openseaapi.Get().GetStats(context.Background(), v.Slug)
		if err != nil {
			if errors.Is(err, openseaapi.Err404) {
				log.Println("[UpdateOtherNFTCollections] collection not found:", v.Slug)
				_ = rdb.DeleteNftDataByCollection(context.Background(), v.Slug)
				time.Sleep(10 * time.Second)
				continue
			}
			log.Println("[UpdateOtherNFTCollections]", err.Error())
			continue
		}
		statsBytes, _ := json.Marshal(stats)
		statsStr := string(statsBytes)
		nftCollection := model.NftCollection{
			Slug:             v.Slug,
			FloorPriceSymbol: "ETH",
			TotalVolume:      stats.TotalVolume,
			ThirtyDayVolume:  stats.ThirtyDayVolume,
			Stats:            &statsStr,
		}
		nftCollection.SetFloorPrice(stats.FloorPrice)
		dataToUpdate = append(dataToUpdate, nftCollection)
		if len(dataToUpdate) >= 10 {
			_ = rdb.BatchUpdateNftFloorPrice(context.Background(), &dataToUpdate)
			dataToUpdate = make([]model.NftCollection, 0)
		}
	}
	if len(dataToUpdate) > 0 {
		_ = rdb.BatchUpdateNftFloorPrice(context.Background(), &dataToUpdate)
	}
}
