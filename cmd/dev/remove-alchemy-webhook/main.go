package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/apis"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

// please setup db connection
// usage: go run cmd/dev/remove-alchemy-webhook/main.go --address=****************************************** --address=******************************************
func main() {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	apis.InitDefault()
	mWebhookID := config.GetStringMap("ALCHEMY_WEBHOOK_ID_MAP")
	alchemyToken := config.GetString("ALCHEMY_TOKEN")

	var countWebhookID int
	for _, webhookID := range mWebhookID {
		if webhookID != "" {
			countWebhookID++
		}
	}
	if countWebhookID == 0 || countWebhookID != len(mWebhookID) || alchemyToken == "" || alchemyToken == "REDACTED" {
		panic("missing env")
	}

	// get address from cmd arg
	addresses := []string{}
	for _, arg := range os.Args {
		if strings.HasPrefix(arg, "--address") {
			addresses = append(addresses, strings.Split(arg, "=")[1])
		}
	}

	// remove webhook addresses
	for _, webhookID := range mWebhookID {
		if webhookID == "" {
			continue
		}

		if err := alchemyapi.Get().RemoveSingleWebhookAddresses(ctx,
			webhookID, addresses); err != nil {
			fmt.Println("remove webhook addresses error:", err.Error())
			panic(err)
		}
	}
}
