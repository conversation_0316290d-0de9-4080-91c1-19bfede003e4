package main

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	"github.com/spf13/viper"
)

/**
 * Usage: ENV=local COLLECTION_NAME=users IMPORT_PATH=dev go run cmd/dev/firestore-import/main.go
 */
func main() {
	viper.SetDefault("COLLECTION_NAME", "users")
	viper.SetDefault("IMPORT_PATH", "default")
	collectionName := config.GetString("COLLECTION_NAME")
	subpath := config.GetString("IMPORT_PATH")
	importCollections(collectionName, subpath)
}

func importCollections(collection, subpath string) {
	files, err := os.ReadDir("res/" + collection + "/" + subpath)
	if err != nil {
		kglog.Errorf("read dir error: %v", err)
	}
	allDocs := map[string]interface{}{}
	cnt := 0
	for _, file := range files {
		if file.IsDir() {
			continue
		}
		fileName := file.Name()
		dataStr, err := os.ReadFile("res/" + collection + "/" + subpath + "/" + fileName)
		if err != nil {
			kglog.Errorf("read file error: %v", err)
			continue
		}

		docID := strings.Replace(fileName, ".json", "", -1)
		docData := new(map[string]interface{})
		err = json.Unmarshal(dataStr, docData)
		if err != nil {
			kglog.Errorf("unmarshal error: %v", err)
			continue
		}
		allDocs[docID] = docData
		cnt++
	}
	fmt.Println("Total item read:", cnt)
	db.BatchSetDoc(collection, &allDocs)
}
