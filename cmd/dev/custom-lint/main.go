package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"strings"
)

func parseFile(filename string) (*ast.File, *token.FileSet, error) {
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, filename, nil, parser.ParseComments)
	if err != nil {
		return nil, nil, err
	}
	return node, fset, nil
}

func detectLoopVarMisuseByAST(node *ast.File, fset *token.FileSet) bool {
	isOk := true
	ast.Inspect(node, func(n ast.Node) bool {
		if loop, ok := n.(*ast.RangeStmt); ok {
			var loopVarName string
			if loopVar, ok := loop.Value.(*ast.Ident); ok {
				loopVarName = loopVar.Name
			}

			ast.Inspect(loop.Body, func(inner ast.Node) bool {
				// Case 1: reference loop variable inside function param of tracing.AsyncOp()
				// Check for function calls
				if callExpr, ok := inner.(*ast.CallExpr); ok {
					// Check if the function is tracing.AsyncOp
					if selExpr, ok := callExpr.Fun.(*ast.SelectorExpr); ok {
						if ident, ok := selExpr.X.(*ast.Ident); ok && ident.Name == "tracing" && selExpr.Sel.Name == "AsyncOp" {
							// Inspect the lambda function argument
							if len(callExpr.Args) == 3 {
								if funcLit, ok := callExpr.Args[2].(*ast.FuncLit); ok {
									ast.Inspect(funcLit, func(funcLitInner ast.Node) bool {
										if ident, ok := funcLitInner.(*ast.Ident); ok && ident.Name == loopVarName {
											fmt.Println("Potential misuse of loop variable reference inside tracing.AsyncOp detected at position:", fset.Position(funcLitInner.Pos()))
											isOk = false
										}
										return true
									})
								}
							}
						}
					}
				}

				// Case 2: Reference loop variable inside a goroutine. Except for the case where the loop variable is passed as an argument to the goroutine
				// Check for goroutines
				if goStmt, ok := inner.(*ast.GoStmt); ok {
					if funcLit, ok := goStmt.Call.Fun.(*ast.FuncLit); ok {
						captured := true
						// Check if the loop variable is passed as an argument to the goroutine
						for _, arg := range goStmt.Call.Args {
							if ident, ok := arg.(*ast.Ident); ok && ident.Name == loopVarName {
								captured = false
								break
							}
						}
						if captured {
							// Inspect the body of the goroutine for misuse
							ast.Inspect(funcLit.Body, func(innerFuncLit ast.Node) bool {
								if ident, ok := innerFuncLit.(*ast.Ident); ok && ident.Name == loopVarName {
									fmt.Println("Potential misuse of loop variable reference inside goroutine detected at position:", fset.Position(innerFuncLit.Pos()))
									isOk = false
								}
								return true
							})
						}
					}
				}

				// Case 3: Reference loop variable using &
				// Existing check for unary expressions
				if unaryExpr, ok := inner.(*ast.UnaryExpr); ok && unaryExpr.Op == token.AND {
					if ident, ok := unaryExpr.X.(*ast.Ident); ok && ident.Name == loopVarName {
						fmt.Println("Potential misuse of loop variable reference detected at position:", fset.Position(inner.Pos()))
						isOk = false
					}
				}
				return true
			})
		}
		return true
	})
	return isOk
}

func detectLoopVarMisuse(filename string) bool {
	node, fset, err := parseFile(filename)
	if err != nil {
		fmt.Println("Error parsing file:", err)
		return false
	}
	return detectLoopVarMisuseByAST(node, fset)
}

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage:", os.Args[0], "<path>")
		return
	}

	path := os.Args[1]
	if path == "./..." {
		err := filepath.Walk(".", func(path string, info os.FileInfo, err error) error {
			if err != nil {
				fmt.Printf("prevent panic by handling failure accessing a path %q: %v\n", path, err)
				return err
			}
			if !info.IsDir() && strings.HasSuffix(path, ".go") {
				ok := detectLoopVarMisuse(path)
				if !ok {
					fmt.Printf("Issues detected in file: %s\n", path)
					os.Exit(1)
				}
			}
			return nil
		})
		if err != nil {
			fmt.Printf("error walking the path %v: %v\n", path, err)
			os.Exit(1)
		}
	} else {
		ok := detectLoopVarMisuse(path)
		if !ok {
			fmt.Printf("Issues detected in file: %s\n", path)
			os.Exit(1)
		}
	}
}
