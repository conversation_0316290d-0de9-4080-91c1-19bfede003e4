package main

import (
	"go/parser"
	"go/token"
	"testing"
)

func TestProcessAST(t *testing.T) {
	// test case: a correctly handled loop variable
	srcNormal := `
    package test
    func testFunc() {
			for _, v := range values {
				go func(val string) {
					fmt.Println(val)
				}(v)
			}
    }
  `
	testAST(t, srcNormal, false, "Normal loop var handling")

	// Test misuse in tracing.AsyncOp
	srcAsyncOp := `
    package test
    import "context"
    func testFunc() {
			for _, v := range values {
				tracing.AsyncOp(context.Background(), "testOp", func(ctx context.Context) {
					fmt.Println(v) // Incorrect use of loop variable 'v'
				})
			}
    }
  `
	testAST(t, srcAsyncOp, true, "misuse of loop var in tracing.AsyncOp")

	// Test misuse in goroutine
	srcGoroutine := `
    package test
    func testFunc() {
			for _, v := range values {
				go func() {
					fmt.Println(v) // Incorrect use of loop variable 'v'
				}()
			}
    }
  `
	testAST(t, srcGoroutine, true, "misuse of loop var in go")

	// Test misuse of & unary expressions
	srcUnary := `
    package test
    func testFunc() {
			for _, v := range values {
				ptr := &v // Incorrect use of '&' with loop variable 'v'
				fmt.Println(ptr)
			}
    }
  `
	testAST(t, srcUnary, true, "misuse of & unary expressions of loop variable")
}

func testAST(t *testing.T, src string, expectError bool, testName string) {
	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, "", src, parser.ParseComments)
	if err != nil {
		t.Fatalf("Failed to parse source for %s: %v", testName, err)
	}

	result := detectLoopVarMisuseByAST(node, fset)
	if expectError && result {
		t.Errorf("Expected misuse of loop variable in %s, but none was found", testName)
	} else if !expectError && !result {
		t.Errorf("Expected no misuse of loop variable in %s, but misuse was found", testName)
	}
}
