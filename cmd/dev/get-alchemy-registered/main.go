package main

import (
	"context"
	"fmt"
	"os"
	"strings"

	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

// setup: export ALCHEMY_TOKEN="$(gcloud secrets versions access latest --secret=ALCHEMY_TOKEN)"
// usage: go run cmd/dev/get-alchemy-registered/main.go --address=****************************************** --address=****************************************** --address=****************************************** --address=******************************************
func main() {
	mWebhookID := config.GetStringMap("ALCHEMY_WEBHOOK_ID_MAP")
	alchemyToken := config.GetString("ALCHEMY_TOKEN")

	var countWebhookID int
	for _, webhookID := range mWebhookID {
		if webhookID != "" {
			countWebhookID++
		}
	}
	if countWebhookID == 0 || countWebhookID != len(mWebhookID) || alchemyToken == "" || alchemyToken == "REDACTED" {
		panic("missing env")
	}

	alchemyapi.InitDefault()
	// get address and webhook_id from cmd arg
	isAddressesRegistered := make(map[string]bool)
	// webhookID := ""
	for _, arg := range os.Args {
		if strings.HasPrefix(arg, "--address") {
			address := strings.ToLower(strings.Split(arg, "=")[1])
			isAddressesRegistered[address] = false
		}
	}
	for _, webhookID := range mWebhookID {
		ith := 0
		for {
			webhookAddresses, err := alchemyapi.Get().GetAllWebhookAddresses(context.Background(), webhookID, 100, ith)
			if err != nil {
				fmt.Println("remove webhook addresses error:", err.Error())
				panic(err)
			}

			for _, webhookAddress := range webhookAddresses {
				address := strings.ToLower(webhookAddress)
				if _, ok := isAddressesRegistered[address]; ok {
					isAddressesRegistered[address] = true
				}
			}
			ith += 1
			if len(webhookAddresses) < 100 {
				break
			}
		}
	}
	fmt.Printf("isAddressesRegistered: %+v\n", isAddressesRegistered)
}
