package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"sort"
	"time"

	solanago "github.com/gagliardetto/solana-go"
	"github.com/kryptogo/kg-wallet-backend/chain/solana"
	"github.com/kryptogo/kg-wallet-backend/domain"
	solanaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"

	"github.com/gagliardetto/solana-go/rpc"
)

const targetAddress = "GNSdzR1xxpALxDw98hqsRKToCufRpLHvnmYQLi71PDFR"
const startDate = "2025-04-24 02:54:52 UTC"

type TraderStats struct {
	Address            string
	TxCount            int
	TxCountOver10U     int
	TotalVolume        decimal.Decimal
	TotalVolumeOver10U decimal.Decimal
}

type DailyStats struct {
	Date    time.Time
	Traders map[string]*TraderStats
}

// TransactionRecord holds data for a single transaction
type TransactionRecord struct {
	Timestamp time.Time
	TxHash    string
	Trader    string
	Volume    decimal.Decimal
	Profit    decimal.Decimal // Note: This will be calculated if available
}

func main() {
	ctx := context.Background()
	solanaapi.InitDefault()
	rpcClient := rpc.New("https://solana-mainnet.g.alchemy.com/v2/********************************")

	// Parse start date
	startTime, err := time.Parse("2006-01-02 15:04:05 MST", startDate)
	if err != nil {
		log.Fatalf("Failed to parse start date: %v", err)
	}

	// Map to store daily statistics
	dailyStats := make(map[string]*DailyStats) // key: YYYY-MM-DD

	// Slice to store transaction records for CSV export
	var txRecords []TransactionRecord

	// Get signatures with pagination to handle more than 1000 transactions
	var allSignatures []*rpc.TransactionSignature
	var beforeSignature solanago.Signature
	const paginationLimit = 50
	var hasMorePages = true
	for hasMorePages {
		opts := &rpc.GetSignaturesForAddressOpts{
			Limit: util.Ptr(paginationLimit),
		}
		if !beforeSignature.IsZero() {
			opts.Before = beforeSignature
		}

		signatures, err := rpcClient.GetSignaturesForAddressWithOpts(ctx, solanago.MustPublicKeyFromBase58(targetAddress), opts)
		if err != nil {
			log.Fatalf("Failed to get signatures: %v", err)
		}

		log.Printf("Fetched %d signatures in this batch", len(signatures))

		// Check for specific signature to break
		for _, sig := range signatures {
			if sig.Signature.String() == "V1yqstPbENZkQ5C47QW9wNj3bwokA8uMALfzqoRsifS3Bt1kz3Tr5mZKYywVGWPxP7nLhs7XQ49Z6M9D6Eu1ixi" {
				log.Printf("Found target signature, stopping pagination")
				hasMorePages = false
				break
			}
		}

		allSignatures = append(allSignatures, signatures...)

		// If we got fewer signatures than the limit, we've reached the end
		if len(signatures) < paginationLimit {
			hasMorePages = false
		} else if hasMorePages {
			// Set the before parameter to the oldest signature in this batch for the next request
			beforeSignature = signatures[len(signatures)-1].Signature
		}
	}

	log.Printf("Total signatures fetched: %d", len(allSignatures))

	// Process each transaction
	for _, sig := range allSignatures {
		txHash := sig.Signature.String()

		// Get transaction details
		txDetail, err := solana.GetClient(domain.Solana).TransactionDetail(ctx, txHash)
		if err != nil {
			log.Printf("Failed to get transaction %s: %v", txHash, err)
			continue
		}

		// Skip if transaction failed
		if txDetail.IsError {
			continue
		}

		// Skip if transaction is before start date
		if txDetail.Timestamp.Before(startTime) {
			continue
		}

		// Get date in UTC+8
		txTime := txDetail.Timestamp.In(time.FixedZone("UTC+8", 8*60*60))
		dateKey := txTime.Format("2006-01-02")

		// Initialize daily stats if not exists
		if _, exists := dailyStats[dateKey]; !exists {
			dailyStats[dateKey] = &DailyStats{
				Date:    txTime,
				Traders: make(map[string]*TraderStats),
			}
		}

		from := txDetail.From
		for _, transfer := range txDetail.InternalTransfers {
			if transfer.To.String() == "HF6pTvspvSCNxSCR1bh1Pbh5tQDUa6AiZwNYoQBS6oHR" {
				// Calculate USD value: lamports * 1e-9 * 200 * 180
				lamports := decimal.NewFromBigInt(transfer.Amount, 0)
				profitUsd := lamports.
					Mul(decimal.NewFromFloat(1e-9)). // Convert to SOL
					Mul(decimal.NewFromInt(180))     // Multiply by 180 for USD
				volumeUsd := profitUsd.Mul(decimal.NewFromInt(200))

				// Initialize trader stats if not exists
				if _, exists := dailyStats[dateKey].Traders[from.String()]; !exists {
					dailyStats[dateKey].Traders[from.String()] = &TraderStats{
						Address:            from.String(),
						TotalVolume:        decimal.Zero,
						TotalVolumeOver10U: decimal.Zero,
					}
				}

				trader := dailyStats[dateKey].Traders[from.String()]
				trader.TxCount++
				trader.TotalVolume = trader.TotalVolume.Add(volumeUsd)

				// Track >10U transactions separately
				if volumeUsd.GreaterThanOrEqual(decimal.NewFromInt(10)) {
					trader.TxCountOver10U++
					trader.TotalVolumeOver10U = trader.TotalVolumeOver10U.Add(volumeUsd)
				}

				// Create transaction record for CSV
				txRecords = append(txRecords, TransactionRecord{
					Timestamp: txTime,
					TxHash:    txHash,
					Trader:    from.String(),
					Volume:    volumeUsd,
					Profit:    profitUsd,
				})

				break
			}
		}
	}

	// Sort dates
	var dates []string
	for date := range dailyStats {
		dates = append(dates, date)
	}
	sort.Strings(dates)

	// Print results for each day
	for _, date := range dates {
		stats := dailyStats[date]

		// Convert map to slice for sorting
		var traderList []*TraderStats
		for _, trader := range stats.Traders {
			traderList = append(traderList, trader)
		}

		// Sort by total volume
		sort.Slice(traderList, func(i, j int) bool {
			return traderList[i].TotalVolume.GreaterThan(traderList[j].TotalVolume)
		})

		fmt.Printf("\nTrading Statistics for %s\n", date)
		fmt.Println("Address                                      | All Tx  | >10U Tx | Total Volume   | >10U Volume")
		fmt.Println("---------------------------------------------|---------|---------|----------------|----------------")
		for _, trader := range traderList {
			fmt.Printf("%-44s | %7d | %7d | %14.2f | %13.2f\n",
				trader.Address,
				trader.TxCount,
				trader.TxCountOver10U,
				trader.TotalVolume.InexactFloat64(),
				trader.TotalVolumeOver10U.InexactFloat64())
		}

		// Print daily totals
		var totalTx, totalTxOver10U int
		var totalVolume, totalVolumeOver10U decimal.Decimal
		for _, trader := range traderList {
			totalTx += trader.TxCount
			totalTxOver10U += trader.TxCountOver10U
			totalVolume = totalVolume.Add(trader.TotalVolume)
			totalVolumeOver10U = totalVolumeOver10U.Add(trader.TotalVolumeOver10U)
		}
		fmt.Println("---------------------------------------------|---------|---------|----------------|----------------")
		fmt.Printf("DAILY TOTAL                                  | %7d | %7d | %14.2f | %13.2f\n",
			totalTx,
			totalTxOver10U,
			totalVolume.InexactFloat64(),
			totalVolumeOver10U.InexactFloat64())
	}

	// Calculate last 14 days unique wallets and volumes
	fmt.Printf("\nLast 14 Days Unique Wallets and Volumes\n")
	fmt.Println("Address                                      | Total Volume")
	fmt.Println("---------------------------------------------|----------------")

	// Get most recent date
	if len(dates) == 0 {
		fmt.Println("No data available")
		return
	}
	mostRecentDate := dates[len(dates)-1]
	mostRecentTime, _ := time.Parse("2006-01-02", mostRecentDate)

	// Create map for unique wallets
	uniqueWallets := make(map[string]decimal.Decimal)

	// Process last 14 days
	for _, date := range dates {
		dateTime, _ := time.Parse("2006-01-02", date)
		daysDiff := int(mostRecentTime.Sub(dateTime).Hours() / 24)
		if daysDiff >= 14 {
			continue
		}

		stats := dailyStats[date]
		for _, trader := range stats.Traders {
			uniqueWallets[trader.Address] = uniqueWallets[trader.Address].Add(trader.TotalVolume)
		}
	}

	// Convert to slice for sorting
	var walletList []struct {
		address string
		volume  decimal.Decimal
	}
	for addr, vol := range uniqueWallets {
		walletList = append(walletList, struct {
			address string
			volume  decimal.Decimal
		}{addr, vol})
	}

	// Sort by volume
	sort.Slice(walletList, func(i, j int) bool {
		return walletList[i].volume.GreaterThan(walletList[j].volume)
	})

	// Print results
	for _, wallet := range walletList {
		fmt.Printf("%-44s | %14.2f\n",
			wallet.address,
			wallet.volume.InexactFloat64())
	}

	// Print total
	var totalVolume decimal.Decimal
	for _, wallet := range walletList {
		totalVolume = totalVolume.Add(wallet.volume)
	}
	fmt.Println("---------------------------------------------|----------------")
	fmt.Printf("TOTAL UNIQUE WALLETS: %d\n", len(walletList))
	fmt.Printf("TOTAL VOLUME: %14.2f\n", totalVolume.InexactFloat64())

	// Write transaction records to CSV
	writeTransactionCSV(txRecords)
}

// writeTransactionCSV writes transaction records to a CSV file
func writeTransactionCSV(records []TransactionRecord) {
	// Create CSV file
	file, err := os.Create("solana_transactions.csv")
	if err != nil {
		log.Fatalf("Failed to create CSV file: %v", err)
	}
	defer file.Close()

	// Create CSV writer
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write header
	header := []string{"Timestamp", "Transaction Hash", "Trader", "Volume", "Profit"}
	if err := writer.Write(header); err != nil {
		log.Fatalf("Failed to write CSV header: %v", err)
	}

	// Write records
	for _, record := range records {
		row := []string{
			record.Timestamp.Format(time.RFC3339),
			record.TxHash,
			record.Trader,
			record.Volume.String(),
			record.Profit.String(),
		}
		if err := writer.Write(row); err != nil {
			log.Fatalf("Failed to write CSV record: %v", err)
		}
	}

	log.Printf("Successfully wrote %d transaction records to solana_transactions.csv", len(records))
}
