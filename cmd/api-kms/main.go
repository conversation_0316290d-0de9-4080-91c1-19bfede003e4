package main

import (
	"context"

	"github.com/gin-contrib/requestid"
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/common"
	"github.com/kryptogo/kg-wallet-backend/api/kms"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

func main() {
	if !config.IsLocal() {
		defer func() {
			if err := tracing.Shutdown(context.Background()); err != nil {
				kglog.Errorf("shutdown tracing error: %v", err)
			}
		}()

		// setup GCP profiler
		profiler := tracing.NewGcpProfiler()
		if err := profiler.Start(); err != nil {
			kglog.Fatal(err)
		}
		defer profiler.Stop()
	}

	appPort := config.GetString("APP_PORT")
	r := gin.New()
	r.Use(tracing.Middleware(), requestid.New(), middleware.RequestLatency(), middleware.HandlePanic, middleware.AddAuditLog)

	registerCommonAPI(r)
	registerWalletAPI(r)
	registerV1API(r)
	_ = r.Run(":" + appPort)
}

func registerCommonAPI(r *gin.Engine) {
	r.GET("/ok", common.GetOk)
	r.GET("/favicon.ico", common.FavIcon)
	r.GET("/ip", common.ServerIP)
	r.GET("/health", common.Health)
}

// firebase hosting rewrite apis
func registerV1API(r *gin.Engine) {
	v1 := r.Group("/v1")

	v1.GET("/ok", common.GetOk)
	v1.GET("/auth/ok", auth.AuthorizeByToken(), common.GetOk)
}

func registerWalletAPI(r *gin.Engine) {
	wallet := r.Group("/v1/kms")
	wallet.GET("/ok", common.GetOk)
	wallet.POST("/generateMnemonicAddresses", kms.GenerateMnemonicAddresses)
	wallet.POST("/retrieveMnemonic", kms.RetrieveMnemonic)
}
