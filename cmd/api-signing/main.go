package main

import (
	"context"

	"github.com/gin-contrib/requestid"
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/common"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/kms"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/alert"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
)

func main() {
	if !config.IsLocal() {
		defer func() {
			if err := tracing.Shutdown(context.Background()); err != nil {
				kglog.Errorf("shutdown tracing error: %v", err)
			}
		}()

		// setup GCP profiler
		profiler := tracing.NewGcpProfiler()

		if err := profiler.Start(); err != nil {
			kglog.Fatal(err)
		}
		defer profiler.Stop()
	}

	// setup sign server package
	rdb.Init()
	kmsKeyName := kms.GetKmsKeyName("mnemonic", "key")
	server.Init(rdb.GormRepo(), &alert.SlackAlertSender{}, server.NewKMSPrivateKeyEncryptor(kmsKeyName))

	appPort := config.GetString("APP_PORT")
	r := gin.New()
	r.Use(tracing.Middleware(), requestid.New(), middleware.RequestLatency(), middleware.HandlePanic, middleware.AddAuditLog)

	registerCommonAPI(r)
	registerV1API(r)
	_ = r.Run(":" + appPort)
}

func registerCommonAPI(r *gin.Engine) {
	r.GET("/ok", common.GetOk)
	r.GET("/favicon.ico", common.FavIcon)
	r.GET("/ip", common.ServerIP)
	r.GET("/health", common.Health)
}

// firebase hosting rewrite apis
func registerV1API(r *gin.Engine) {
	v1 := r.Group("/v1")
	v1.GET("/ok", common.GetOk)

	v1.GET("/wallets", signing.GetWalletsByOrganization)
	v1.POST("/wallets", signing.CreateOrganizationWallet)
	v1.POST("/sign/evm/message", signing.AuthorizeSigningServer, signing.SignEvmMessage)
	v1.POST("/sign/evm", signing.AuthorizeSigningServer, signing.SignEvmTransaction)
	v1.POST("/sign/evm_by_salt", signing.AuthorizeSigningServer, signing.SignEvmTransactionWithSalt)
	v1.GET("/bridging_address", signing.GetAddressesBySalt)
	v1.POST("/sign/tron", signing.AuthorizeSigningServer, signing.SignTronTransaction)
	v1.POST("/sign/solana", signing.AuthorizeSigningServer, signing.SignSolanaTransaction)
	v1.GET("/alert_threshold", signing.GetAlertThreshold)
	v1.POST("/alert_threshold", signing.UpdateAlertThreshold)
	v1.GET("/deposit_address", signing.GetDepositAddress)
	v1.GET("/deposit_address_by_salt", signing.GetDepositAddressesBySalt)
}
