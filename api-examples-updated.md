# Bridge External Account API Examples - Updated

根據實際的 Bridge API 使用方式，以下是所有 account type 的正確 API 請求範例：

## API 端點
```
POST /v1/studio/bridge/external_accounts
Content-Type: application/json
Authorization: Bearer <your_token>
```

## 1. US Account Type (`"us"`)

```json
{
  "account_type": "us",
  "account_owner_name": "<PERSON>",
  "account_owner_type": "individual",
  "bank_name": "Chase Bank",
  "currency": "usd",
  "us": {
    "account": {
      "account_number": "************",
      "routing_number": "*********"
    }
  },
  "address": {
    "street_line_1": "270 Park Ave",
    "street_line_2": "Floor 10",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "USA"
  }
}
```

**特點：**
- 必須提供 `us.account.account_number` 和 `us.account.routing_number`
- 必須提供完整的 `address` 資訊，包含嚴格的長度驗證
- `state` 欄位是可選的，但建議提供

## 2. IBAN Account Type (`"iban"`)

```json
{
  "account_type": "iban",
  "account_owner_name": "KryptoGO Co., Ltd.",
  "account_owner_type": "business",
  "bank_name": "Mega International Commercial Bank",
  "currency": "usd",
  "business_name": "KryptoGO Co., Ltd.",
  "swift": {
    "account": {
      "account_number": "************",
      "bic": "ICBCTWTP216"
    },
    "address": {
      "country": "TWN",
      "city": "Taipei City",
      "street_line_1": "No. 333, Section 1",
      "street_line_2": "Keelung Rd, Xinyi District",
      "postal_code": "110"
    },
    "purpose_of_funds": ["invoice_for_goods_and_services"],
    "category": "supplier",
    "short_business_description": "operation"
  },
  "address": {
    "country": "TWN",
    "city": "Taipei City",
    "street_line_1": "12F., No. 161, Songde Rd.",
    "street_line_2": "Xinyi Dist.",
    "postal_code": "110"
  }
}
```

**特點：**
- 必須提供完整的 `swift` 資訊
- 包含 SWIFT 帳戶詳細資訊和地址
- 適用於國際銀行轉帳

## 3. Unknown Account Type (`"unknown"`) - **根據實際 Bridge API**

```json
{
  "account_type": "unknown",
  "account_owner_name": "KryptoGO Co., Ltd.",
  "account_owner_type": "business",
  "bank_name": "Mega International Commercial Bank",
  "currency": "usd",
  "business_name": "KryptoGO Co., Ltd.",
  "swift": {
    "account": {
      "account_number": "***********",
      "bic": "ICBCTWTP216"
    },
    "address": {
      "country": "TWN",
      "city": "Taipei City",
      "street_line_1": "No. 333, Section 1",
      "street_line_2": "Keelung Rd, Xinyi District",
      "postal_code": "110"
    },
    "purpose_of_funds": ["invoice_for_goods_and_services"],
    "category": "supplier",
    "short_business_description": "operation"
  },
  "address": {
    "country": "TWN",
    "city": "Taipei City",
    "street_line_1": "12F., No. 161, Songde Rd.",
    "street_line_2": "Xinyi Dist.",
    "postal_code": "110"
  }
}
```

**特點：**
- 必須提供完整的 `swift` 資訊（與 IBAN 相同）
- 系統會自動將 `swift.account.account_number` 複製到根層級的 `account_number` 欄位
- 適用於不符合標準 US、IBAN 或 CLABE 類型的特殊帳戶
- 通常用於特殊情況或非標準銀行帳戶

## 4. CLABE Account Type (`"clabe"`)

```json
{
  "account_type": "clabe",
  "account_owner_name": "María García",
  "account_owner_type": "individual",
  "bank_name": "Banco Santander México",
  "currency": "mxn",
  "address": {
    "country": "MEX",
    "city": "Mexico City",
    "street_line_1": "Av. Insurgentes Sur 1234",
    "street_line_2": "Col. Del Valle",
    "postal_code": "03100"
  }
}
```

**特點：**
- 主要用於墨西哥銀行帳戶
- 不需要特殊的帳戶結構驗證
- 通常使用 MXN 貨幣

## cURL 範例

### Unknown Account (基於實際 Bridge API)
```bash
curl --request POST \
     --url "https://api-dev.kryptogo.com/v1/studio/bridge/external_accounts" \
     --header "Content-Type: application/json" \
     --header "Authorization: Bearer YOUR_TOKEN" \
     --data '{
  "account_type": "unknown",
  "account_owner_name": "KryptoGO Co., Ltd.",
  "account_owner_type": "business",
  "bank_name": "Mega International Commercial Bank",
  "currency": "usd",
  "business_name": "KryptoGO Co., Ltd.",
  "swift": {
    "account": {
      "account_number": "***********",
      "bic": "ICBCTWTP216"
    },
    "address": {
      "country": "TWN",
      "city": "Taipei City",
      "street_line_1": "No. 333, Section 1",
      "street_line_2": "Keelung Rd, Xinyi District",
      "postal_code": "110"
    },
    "purpose_of_funds": ["invoice_for_goods_and_services"],
    "category": "supplier",
    "short_business_description": "operation"
  },
  "address": {
    "country": "TWN",
    "city": "Taipei City",
    "street_line_1": "12F., No. 161, Songde Rd.",
    "street_line_2": "Xinyi Dist.",
    "postal_code": "110"
  }
}'
```

## 驗證要求總結

| Account Type | 必要欄位                    | 特殊驗證                                | 備註                                |
| ------------ | --------------------------- | --------------------------------------- | ----------------------------------- |
| `us`         | `us.account.*`, `address.*` | 嚴格的美國地址長度驗證                  | -                                   |
| `iban`       | `swift.*`                   | SWIFT 詳細資訊驗證                      | -                                   |
| `unknown`    | `swift.*`                   | SWIFT 詳細資訊驗證 + SWIFT 帳戶詳細資訊 | 系統自動設置根層級 `account_number` |
| `clabe`      | 基本欄位                    | 無特殊驗證                              | -                                   |

## 重要注意事項

1. **Unknown Account Type**: 根據實際的 Bridge API 使用方式，`unknown` 類型需要完整的 `swift` 資訊，包括 `account` 詳細資訊。

2. **自動欄位設置**: 對於 `unknown` 類型，我們的 API 會自動將 `swift.account.account_number` 的值設置到根層級的 `account_number` 欄位，符合 Bridge API 的要求。

3. **驗證錯誤**: 如果缺少必要的欄位，會收到相應的錯誤訊息：
   - `"swift details are required when account_type is 'unknown'"`
   - `"swift account details are required when account_type is 'unknown'"`

這個更新確保了我們的 API 與實際的 Bridge API 使用方式完全一致。 