package ronin

import (
	"context"
	"math/big"

	"github.com/kryptogo/kg-wallet-backend/domain"
)

func (c *clientImpl) BlockNumber(ctx context.Context) (uint64, error) {
	panic("not implemented")
}

func (c *clientImpl) NativeBalance(ctx context.Context, address domain.Address) (*big.Int, error) {
	panic("not implemented")
}

func (c *clientImpl) TokenBalance(ctx context.Context, address domain.Address, tokenID string) (*big.Int, error) {
	panic("not implemented")
}

func (c *clientImpl) GetTransactionStatus(ctx context.Context, txHash string) (domain.TransactionStatus, error) {
	panic("not implemented")
}

func (c *clientImpl) WaitUntilTransactionConfirmed(ctx context.Context, txHash string) (domain.TransactionStatus, error) {
	panic("not implemented")
}

// TransactionDetail fetches transaction details for a given transaction hash. TODO: consider NFT tx
func (c *clientImpl) TransactionDetail(ctx context.Context, txHash string) (*domain.TransactionDetail, error) {
	panic("not implemented")
}

func (c *clientImpl) BroadcastRawTransaction(ctx context.Context, rawTx string) (string, error) {
	panic("not implemented")
}
