package evm

import (
	"context"
	"math/big"
	"time"

	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/kryptogo/kg-wallet-backend/domain"
)

type IService interface {
	domain.TokenAmountsFetcher
}

type serviceImpl struct{}

func Get() IService {
	return &serviceImpl{}
}

type IClient interface {
	domain.ChainClient
	domain.ChainSyncFetcher
	BroadcastTransaction(ctx context.Context, transaction *types.Transaction) (string, error)
	BlockTimeByNumber(ctx context.Context, blockNumber *big.Int) (time.Time, error)
	GetTokenNameSymbol(ctx context.Context, tokenID string) (string, string, error)
	GetNonce(ctx context.Context, address domain.EvmAddress) (uint64, error)
	GetCodeSize(ctx context.Context, address domain.EvmAddress) (int, error)
	CreateNativeTransfer(ctx context.Context, from, to domain.EvmAddress, amount *big.Int) (*types.Transaction, error)
	GetRawClient() *ethclient.Client
	GetAlchemyClient() *ethclient.Client
	GetFirstTokenTransferTx(ctx context.Context, address domain.EvmAddress, tokenAddress string) (string, error)
}

type clientImpl struct {
	chain  domain.Chain
	client *ethclient.Client
}

func GetClient(chain domain.Chain) IClient {
	if !chain.IsEVM() {
		panic("chain is not evm")
	}

	client, err := ethclient.Dial(RpcURL(chain))
	if err != nil {
		panic(err)
	}
	return &clientImpl{chain: chain, client: client}
}

func (c *clientImpl) GetRawClient() *ethclient.Client {
	return c.client
}

func (c *clientImpl) GetAlchemyClient() *ethclient.Client {
	client, err := ethclient.Dial(AlchemyRpcURL(c.chain))
	if err != nil {
		panic(err)
	}
	return client
}
