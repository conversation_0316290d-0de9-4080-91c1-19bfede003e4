package evm

import (
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

func RpcURL(chain domain.Chain) string {
	quickNodeKey := config.GetString("QUICKNODE_API_KEY_V2")
	if quickNodeKey != "" {
		baseURL := chain.QuicknodeRpcBase()
		if baseURL != "" {
			return fmt.Sprintf("%s/%s", baseURL, quickNodeKey)
		}
	}

	alchemyKey := config.GetString("ALCHEMY_API_KEY")
	if alchemyKey != "" {
		baseURL := chain.AlchemyRpcBase()
		if baseURL != "" {
			return fmt.Sprintf("%s/%s", baseURL, alchemyKey)
		}
	}

	// use public rpc
	rpcURL := chain.DefaultRpcURL()
	if rpcURL != "" {
		return rpcURL
	}

	panic("unsupported chain")
}

func AlchemyRpcURL(chain domain.Chain) string {
	alchemyKey := config.GetString("ALCHEMY_API_KEY")
	if alchemyKey != "" {
		baseURL := chain.AlchemyRpcBase()
		if baseURL != "" {
			return fmt.Sprintf("%s/%s", baseURL, alchemyKey)
		}
	}
	return ""
}
