// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package abibinding

import (
	"errors"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

// VerifyingPaymasterPaymasterData is an auto generated low-level Go binding around an user-defined struct.
type VerifyingPaymasterPaymasterData struct {
	ValidUntil         *big.Int
	ValidAfter         *big.Int
	SponsorUUID        *big.Int
	AllowAnyBundler    bool
	PrecheckBalance    bool
	PrepaymentRequired bool
	Token              common.Address
	Receiver           common.Address
	ExchangeRate       *big.Int
	PostOpGas          *big.Int
}

// PaymasterMetaData contains all meta data concerning the Paymaster contract.
var PaymasterMetaData = &bind.MetaData{
	ABI: "[{\"inputs\":[{\"internalType\":\"contractIEntryPoint\",\"name\":\"entryPoint\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"initialVerifyingSigner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"initialOwner\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"bundler\",\"type\":\"address\"}],\"name\":\"BundlerNotAllowed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"DespositFailed\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ECDSAInvalidSignature\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"length\",\"type\":\"uint256\"}],\"name\":\"ECDSAInvalidSignatureLength\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"s\",\"type\":\"bytes32\"}],\"name\":\"ECDSAInvalidSignatureS\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"InvalidSignatureLength\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"NoPendingSigner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"PostOpGasLimitExceeded\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"RenouceOwnershipDisabled\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"balance\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"maxTokenCost\",\"type\":\"uint256\"}],\"name\":\"SenderTokenBalanceTooLow\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"bundler\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"bool\",\"name\":\"allowed\",\"type\":\"bool\"}],\"name\":\"BundlerAllowlistUpdated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferStarted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"}],\"name\":\"PendingVerifyingSignerSet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"userOperationHash\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"uint128\",\"name\":\"sponsorUUID\",\"type\":\"uint128\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"UserOperationSponsored\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"userOperationHash\",\"type\":\"bytes32\"},{\"indexed\":true,\"internalType\":\"uint128\",\"name\":\"sponsorUUID\",\"type\":\"uint128\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"UserOperationSponsoredWithERC20\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"oldSigner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newSigner\",\"type\":\"address\"}],\"name\":\"VerifyingSignerRotated\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"acceptOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint32\",\"name\":\"unstakeDelaySec\",\"type\":\"uint32\"}],\"name\":\"addStake\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"deposit\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"entryPoint\",\"outputs\":[{\"internalType\":\"contractIEntryPoint\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getDeposit\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"initCode\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"},{\"internalType\":\"bytes32\",\"name\":\"accountGasLimits\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"preVerificationGas\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"gasFees\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"paymasterAndData\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"internalType\":\"structPackedUserOperation\",\"name\":\"userOp\",\"type\":\"tuple\"},{\"components\":[{\"internalType\":\"uint48\",\"name\":\"validUntil\",\"type\":\"uint48\"},{\"internalType\":\"uint48\",\"name\":\"validAfter\",\"type\":\"uint48\"},{\"internalType\":\"uint128\",\"name\":\"sponsorUUID\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"allowAnyBundler\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"precheckBalance\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"prepaymentRequired\",\"type\":\"bool\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"exchangeRate\",\"type\":\"uint256\"},{\"internalType\":\"uint48\",\"name\":\"postOpGas\",\"type\":\"uint48\"}],\"internalType\":\"structVerifyingPaymaster.PaymasterData\",\"name\":\"paymasterData\",\"type\":\"tuple\"},{\"internalType\":\"uint256\",\"name\":\"paymasterValidationGasLimit\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"postOpGasLimit\",\"type\":\"uint256\"}],\"name\":\"getHash\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"bundler\",\"type\":\"address\"}],\"name\":\"isBundlerAllowed\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"allowed\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"asset\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"ownerWithdrawERC20\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes\",\"name\":\"paymasterAndData\",\"type\":\"bytes\"}],\"name\":\"parsePaymasterData\",\"outputs\":[{\"components\":[{\"internalType\":\"uint48\",\"name\":\"validUntil\",\"type\":\"uint48\"},{\"internalType\":\"uint48\",\"name\":\"validAfter\",\"type\":\"uint48\"},{\"internalType\":\"uint128\",\"name\":\"sponsorUUID\",\"type\":\"uint128\"},{\"internalType\":\"bool\",\"name\":\"allowAnyBundler\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"precheckBalance\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"prepaymentRequired\",\"type\":\"bool\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"receiver\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"exchangeRate\",\"type\":\"uint256\"},{\"internalType\":\"uint48\",\"name\":\"postOpGas\",\"type\":\"uint48\"}],\"internalType\":\"structVerifyingPaymaster.PaymasterData\",\"name\":\"paymasterData\",\"type\":\"tuple\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingOwner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pendingVerifyingSigner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"enumIPaymaster.PostOpMode\",\"name\":\"mode\",\"type\":\"uint8\"},{\"internalType\":\"bytes\",\"name\":\"context\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"actualGasCost\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"actualUserOpFeePerGas\",\"type\":\"uint256\"}],\"name\":\"postOp\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"rotateVerifyingSigner\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"}],\"name\":\"setPendingVerifyingSigner\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unlockStake\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"bundlers\",\"type\":\"address[]\"},{\"internalType\":\"bool\",\"name\":\"allowed\",\"type\":\"bool\"}],\"name\":\"updateBundlerAllowlist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"components\":[{\"internalType\":\"address\",\"name\":\"sender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"nonce\",\"type\":\"uint256\"},{\"internalType\":\"bytes\",\"name\":\"initCode\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"callData\",\"type\":\"bytes\"},{\"internalType\":\"bytes32\",\"name\":\"accountGasLimits\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"preVerificationGas\",\"type\":\"uint256\"},{\"internalType\":\"bytes32\",\"name\":\"gasFees\",\"type\":\"bytes32\"},{\"internalType\":\"bytes\",\"name\":\"paymasterAndData\",\"type\":\"bytes\"},{\"internalType\":\"bytes\",\"name\":\"signature\",\"type\":\"bytes\"}],\"internalType\":\"structPackedUserOperation\",\"name\":\"userOp\",\"type\":\"tuple\"},{\"internalType\":\"bytes32\",\"name\":\"userOpHash\",\"type\":\"bytes32\"},{\"internalType\":\"uint256\",\"name\":\"maxCost\",\"type\":\"uint256\"}],\"name\":\"validatePaymasterUserOp\",\"outputs\":[{\"internalType\":\"bytes\",\"name\":\"context\",\"type\":\"bytes\"},{\"internalType\":\"uint256\",\"name\":\"validationData\",\"type\":\"uint256\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"verifyingSigner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"addresspayable\",\"name\":\"withdrawAddress\",\"type\":\"address\"}],\"name\":\"withdrawStake\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"addresspayable\",\"name\":\"withdrawAddress\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"withdrawTo\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}]",
}

// PaymasterABI is the input ABI used to generate the binding from.
// Deprecated: Use PaymasterMetaData.ABI instead.
var PaymasterABI = PaymasterMetaData.ABI

// Paymaster is an auto generated Go binding around an Ethereum contract.
type Paymaster struct {
	PaymasterCaller     // Read-only binding to the contract
	PaymasterTransactor // Write-only binding to the contract
	PaymasterFilterer   // Log filterer for contract events
}

// PaymasterCaller is an auto generated read-only Go binding around an Ethereum contract.
type PaymasterCaller struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// PaymasterTransactor is an auto generated write-only Go binding around an Ethereum contract.
type PaymasterTransactor struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// PaymasterFilterer is an auto generated log filtering Go binding around an Ethereum contract events.
type PaymasterFilterer struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// PaymasterSession is an auto generated Go binding around an Ethereum contract,
// with pre-set call and transact options.
type PaymasterSession struct {
	Contract     *Paymaster        // Generic contract binding to set the session for
	CallOpts     bind.CallOpts     // Call options to use throughout this session
	TransactOpts bind.TransactOpts // Transaction auth options to use throughout this session
}

// PaymasterCallerSession is an auto generated read-only Go binding around an Ethereum contract,
// with pre-set call options.
type PaymasterCallerSession struct {
	Contract *PaymasterCaller // Generic contract caller binding to set the session for
	CallOpts bind.CallOpts    // Call options to use throughout this session
}

// PaymasterTransactorSession is an auto generated write-only Go binding around an Ethereum contract,
// with pre-set transact options.
type PaymasterTransactorSession struct {
	Contract     *PaymasterTransactor // Generic contract transactor binding to set the session for
	TransactOpts bind.TransactOpts    // Transaction auth options to use throughout this session
}

// PaymasterRaw is an auto generated low-level Go binding around an Ethereum contract.
type PaymasterRaw struct {
	Contract *Paymaster // Generic contract binding to access the raw methods on
}

// PaymasterCallerRaw is an auto generated low-level read-only Go binding around an Ethereum contract.
type PaymasterCallerRaw struct {
	Contract *PaymasterCaller // Generic read-only contract binding to access the raw methods on
}

// PaymasterTransactorRaw is an auto generated low-level write-only Go binding around an Ethereum contract.
type PaymasterTransactorRaw struct {
	Contract *PaymasterTransactor // Generic write-only contract binding to access the raw methods on
}

// NewPaymaster creates a new instance of Paymaster, bound to a specific deployed contract.
func NewPaymaster(address common.Address, backend bind.ContractBackend) (*Paymaster, error) {
	contract, err := bindPaymaster(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &Paymaster{PaymasterCaller: PaymasterCaller{contract: contract}, PaymasterTransactor: PaymasterTransactor{contract: contract}, PaymasterFilterer: PaymasterFilterer{contract: contract}}, nil
}

// NewPaymasterCaller creates a new read-only instance of Paymaster, bound to a specific deployed contract.
func NewPaymasterCaller(address common.Address, caller bind.ContractCaller) (*PaymasterCaller, error) {
	contract, err := bindPaymaster(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &PaymasterCaller{contract: contract}, nil
}

// NewPaymasterTransactor creates a new write-only instance of Paymaster, bound to a specific deployed contract.
func NewPaymasterTransactor(address common.Address, transactor bind.ContractTransactor) (*PaymasterTransactor, error) {
	contract, err := bindPaymaster(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &PaymasterTransactor{contract: contract}, nil
}

// NewPaymasterFilterer creates a new log filterer instance of Paymaster, bound to a specific deployed contract.
func NewPaymasterFilterer(address common.Address, filterer bind.ContractFilterer) (*PaymasterFilterer, error) {
	contract, err := bindPaymaster(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &PaymasterFilterer{contract: contract}, nil
}

// bindPaymaster binds a generic wrapper to an already deployed contract.
func bindPaymaster(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := PaymasterMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_Paymaster *PaymasterRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _Paymaster.Contract.PaymasterCaller.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_Paymaster *PaymasterRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Paymaster.Contract.PaymasterTransactor.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_Paymaster *PaymasterRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _Paymaster.Contract.PaymasterTransactor.contract.Transact(opts, method, params...)
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_Paymaster *PaymasterCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _Paymaster.Contract.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_Paymaster *PaymasterTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Paymaster.Contract.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_Paymaster *PaymasterTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _Paymaster.Contract.contract.Transact(opts, method, params...)
}

// EntryPoint is a free data retrieval call binding the contract method 0xb0d691fe.
//
// Solidity: function entryPoint() view returns(address)
func (_Paymaster *PaymasterCaller) EntryPoint(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _Paymaster.contract.Call(opts, &out, "entryPoint")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// EntryPoint is a free data retrieval call binding the contract method 0xb0d691fe.
//
// Solidity: function entryPoint() view returns(address)
func (_Paymaster *PaymasterSession) EntryPoint() (common.Address, error) {
	return _Paymaster.Contract.EntryPoint(&_Paymaster.CallOpts)
}

// EntryPoint is a free data retrieval call binding the contract method 0xb0d691fe.
//
// Solidity: function entryPoint() view returns(address)
func (_Paymaster *PaymasterCallerSession) EntryPoint() (common.Address, error) {
	return _Paymaster.Contract.EntryPoint(&_Paymaster.CallOpts)
}

// GetDeposit is a free data retrieval call binding the contract method 0xc399ec88.
//
// Solidity: function getDeposit() view returns(uint256)
func (_Paymaster *PaymasterCaller) GetDeposit(opts *bind.CallOpts) (*big.Int, error) {
	var out []interface{}
	err := _Paymaster.contract.Call(opts, &out, "getDeposit")

	if err != nil {
		return *new(*big.Int), err
	}

	out0 := *abi.ConvertType(out[0], new(*big.Int)).(**big.Int)

	return out0, err

}

// GetDeposit is a free data retrieval call binding the contract method 0xc399ec88.
//
// Solidity: function getDeposit() view returns(uint256)
func (_Paymaster *PaymasterSession) GetDeposit() (*big.Int, error) {
	return _Paymaster.Contract.GetDeposit(&_Paymaster.CallOpts)
}

// GetDeposit is a free data retrieval call binding the contract method 0xc399ec88.
//
// Solidity: function getDeposit() view returns(uint256)
func (_Paymaster *PaymasterCallerSession) GetDeposit() (*big.Int, error) {
	return _Paymaster.Contract.GetDeposit(&_Paymaster.CallOpts)
}

// GetHash is a free data retrieval call binding the contract method 0x38dcc981.
//
// Solidity: function getHash((address,uint256,bytes,bytes,bytes32,uint256,bytes32,bytes,bytes) userOp, (uint48,uint48,uint128,bool,bool,bool,address,address,uint256,uint48) paymasterData, uint256 paymasterValidationGasLimit, uint256 postOpGasLimit) view returns(bytes32)
func (_Paymaster *PaymasterCaller) GetHash(opts *bind.CallOpts, userOp PackedUserOperation, paymasterData VerifyingPaymasterPaymasterData, paymasterValidationGasLimit *big.Int, postOpGasLimit *big.Int) ([32]byte, error) {
	var out []interface{}
	err := _Paymaster.contract.Call(opts, &out, "getHash", userOp, paymasterData, paymasterValidationGasLimit, postOpGasLimit)

	if err != nil {
		return *new([32]byte), err
	}

	out0 := *abi.ConvertType(out[0], new([32]byte)).(*[32]byte)

	return out0, err

}

// GetHash is a free data retrieval call binding the contract method 0x38dcc981.
//
// Solidity: function getHash((address,uint256,bytes,bytes,bytes32,uint256,bytes32,bytes,bytes) userOp, (uint48,uint48,uint128,bool,bool,bool,address,address,uint256,uint48) paymasterData, uint256 paymasterValidationGasLimit, uint256 postOpGasLimit) view returns(bytes32)
func (_Paymaster *PaymasterSession) GetHash(userOp PackedUserOperation, paymasterData VerifyingPaymasterPaymasterData, paymasterValidationGasLimit *big.Int, postOpGasLimit *big.Int) ([32]byte, error) {
	return _Paymaster.Contract.GetHash(&_Paymaster.CallOpts, userOp, paymasterData, paymasterValidationGasLimit, postOpGasLimit)
}

// GetHash is a free data retrieval call binding the contract method 0x38dcc981.
//
// Solidity: function getHash((address,uint256,bytes,bytes,bytes32,uint256,bytes32,bytes,bytes) userOp, (uint48,uint48,uint128,bool,bool,bool,address,address,uint256,uint48) paymasterData, uint256 paymasterValidationGasLimit, uint256 postOpGasLimit) view returns(bytes32)
func (_Paymaster *PaymasterCallerSession) GetHash(userOp PackedUserOperation, paymasterData VerifyingPaymasterPaymasterData, paymasterValidationGasLimit *big.Int, postOpGasLimit *big.Int) ([32]byte, error) {
	return _Paymaster.Contract.GetHash(&_Paymaster.CallOpts, userOp, paymasterData, paymasterValidationGasLimit, postOpGasLimit)
}

// IsBundlerAllowed is a free data retrieval call binding the contract method 0x4031c20e.
//
// Solidity: function isBundlerAllowed(address bundler) view returns(bool allowed)
func (_Paymaster *PaymasterCaller) IsBundlerAllowed(opts *bind.CallOpts, bundler common.Address) (bool, error) {
	var out []interface{}
	err := _Paymaster.contract.Call(opts, &out, "isBundlerAllowed", bundler)

	if err != nil {
		return *new(bool), err
	}

	out0 := *abi.ConvertType(out[0], new(bool)).(*bool)

	return out0, err

}

// IsBundlerAllowed is a free data retrieval call binding the contract method 0x4031c20e.
//
// Solidity: function isBundlerAllowed(address bundler) view returns(bool allowed)
func (_Paymaster *PaymasterSession) IsBundlerAllowed(bundler common.Address) (bool, error) {
	return _Paymaster.Contract.IsBundlerAllowed(&_Paymaster.CallOpts, bundler)
}

// IsBundlerAllowed is a free data retrieval call binding the contract method 0x4031c20e.
//
// Solidity: function isBundlerAllowed(address bundler) view returns(bool allowed)
func (_Paymaster *PaymasterCallerSession) IsBundlerAllowed(bundler common.Address) (bool, error) {
	return _Paymaster.Contract.IsBundlerAllowed(&_Paymaster.CallOpts, bundler)
}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_Paymaster *PaymasterCaller) Owner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _Paymaster.contract.Call(opts, &out, "owner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_Paymaster *PaymasterSession) Owner() (common.Address, error) {
	return _Paymaster.Contract.Owner(&_Paymaster.CallOpts)
}

// Owner is a free data retrieval call binding the contract method 0x8da5cb5b.
//
// Solidity: function owner() view returns(address)
func (_Paymaster *PaymasterCallerSession) Owner() (common.Address, error) {
	return _Paymaster.Contract.Owner(&_Paymaster.CallOpts)
}

// ParsePaymasterData is a free data retrieval call binding the contract method 0x81e4d81d.
//
// Solidity: function parsePaymasterData(bytes paymasterAndData) pure returns((uint48,uint48,uint128,bool,bool,bool,address,address,uint256,uint48) paymasterData, bytes signature)
func (_Paymaster *PaymasterCaller) ParsePaymasterData(opts *bind.CallOpts, paymasterAndData []byte) (struct {
	PaymasterData VerifyingPaymasterPaymasterData
	Signature     []byte
}, error) {
	var out []interface{}
	err := _Paymaster.contract.Call(opts, &out, "parsePaymasterData", paymasterAndData)

	outstruct := new(struct {
		PaymasterData VerifyingPaymasterPaymasterData
		Signature     []byte
	})
	if err != nil {
		return *outstruct, err
	}

	outstruct.PaymasterData = *abi.ConvertType(out[0], new(VerifyingPaymasterPaymasterData)).(*VerifyingPaymasterPaymasterData)
	outstruct.Signature = *abi.ConvertType(out[1], new([]byte)).(*[]byte)

	return *outstruct, err

}

// ParsePaymasterData is a free data retrieval call binding the contract method 0x81e4d81d.
//
// Solidity: function parsePaymasterData(bytes paymasterAndData) pure returns((uint48,uint48,uint128,bool,bool,bool,address,address,uint256,uint48) paymasterData, bytes signature)
func (_Paymaster *PaymasterSession) ParsePaymasterData(paymasterAndData []byte) (struct {
	PaymasterData VerifyingPaymasterPaymasterData
	Signature     []byte
}, error) {
	return _Paymaster.Contract.ParsePaymasterData(&_Paymaster.CallOpts, paymasterAndData)
}

// ParsePaymasterData is a free data retrieval call binding the contract method 0x81e4d81d.
//
// Solidity: function parsePaymasterData(bytes paymasterAndData) pure returns((uint48,uint48,uint128,bool,bool,bool,address,address,uint256,uint48) paymasterData, bytes signature)
func (_Paymaster *PaymasterCallerSession) ParsePaymasterData(paymasterAndData []byte) (struct {
	PaymasterData VerifyingPaymasterPaymasterData
	Signature     []byte
}, error) {
	return _Paymaster.Contract.ParsePaymasterData(&_Paymaster.CallOpts, paymasterAndData)
}

// PendingOwner is a free data retrieval call binding the contract method 0xe30c3978.
//
// Solidity: function pendingOwner() view returns(address)
func (_Paymaster *PaymasterCaller) PendingOwner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _Paymaster.contract.Call(opts, &out, "pendingOwner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// PendingOwner is a free data retrieval call binding the contract method 0xe30c3978.
//
// Solidity: function pendingOwner() view returns(address)
func (_Paymaster *PaymasterSession) PendingOwner() (common.Address, error) {
	return _Paymaster.Contract.PendingOwner(&_Paymaster.CallOpts)
}

// PendingOwner is a free data retrieval call binding the contract method 0xe30c3978.
//
// Solidity: function pendingOwner() view returns(address)
func (_Paymaster *PaymasterCallerSession) PendingOwner() (common.Address, error) {
	return _Paymaster.Contract.PendingOwner(&_Paymaster.CallOpts)
}

// PendingVerifyingSigner is a free data retrieval call binding the contract method 0xff1ff13a.
//
// Solidity: function pendingVerifyingSigner() view returns(address)
func (_Paymaster *PaymasterCaller) PendingVerifyingSigner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _Paymaster.contract.Call(opts, &out, "pendingVerifyingSigner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// PendingVerifyingSigner is a free data retrieval call binding the contract method 0xff1ff13a.
//
// Solidity: function pendingVerifyingSigner() view returns(address)
func (_Paymaster *PaymasterSession) PendingVerifyingSigner() (common.Address, error) {
	return _Paymaster.Contract.PendingVerifyingSigner(&_Paymaster.CallOpts)
}

// PendingVerifyingSigner is a free data retrieval call binding the contract method 0xff1ff13a.
//
// Solidity: function pendingVerifyingSigner() view returns(address)
func (_Paymaster *PaymasterCallerSession) PendingVerifyingSigner() (common.Address, error) {
	return _Paymaster.Contract.PendingVerifyingSigner(&_Paymaster.CallOpts)
}

// RenounceOwnership is a free data retrieval call binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() view returns()
func (_Paymaster *PaymasterCaller) RenounceOwnership(opts *bind.CallOpts) error {
	var out []interface{}
	err := _Paymaster.contract.Call(opts, &out, "renounceOwnership")

	if err != nil {
		return err
	}

	return err

}

// RenounceOwnership is a free data retrieval call binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() view returns()
func (_Paymaster *PaymasterSession) RenounceOwnership() error {
	return _Paymaster.Contract.RenounceOwnership(&_Paymaster.CallOpts)
}

// RenounceOwnership is a free data retrieval call binding the contract method 0x715018a6.
//
// Solidity: function renounceOwnership() view returns()
func (_Paymaster *PaymasterCallerSession) RenounceOwnership() error {
	return _Paymaster.Contract.RenounceOwnership(&_Paymaster.CallOpts)
}

// VerifyingSigner is a free data retrieval call binding the contract method 0x23d9ac9b.
//
// Solidity: function verifyingSigner() view returns(address)
func (_Paymaster *PaymasterCaller) VerifyingSigner(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _Paymaster.contract.Call(opts, &out, "verifyingSigner")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// VerifyingSigner is a free data retrieval call binding the contract method 0x23d9ac9b.
//
// Solidity: function verifyingSigner() view returns(address)
func (_Paymaster *PaymasterSession) VerifyingSigner() (common.Address, error) {
	return _Paymaster.Contract.VerifyingSigner(&_Paymaster.CallOpts)
}

// VerifyingSigner is a free data retrieval call binding the contract method 0x23d9ac9b.
//
// Solidity: function verifyingSigner() view returns(address)
func (_Paymaster *PaymasterCallerSession) VerifyingSigner() (common.Address, error) {
	return _Paymaster.Contract.VerifyingSigner(&_Paymaster.CallOpts)
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_Paymaster *PaymasterTransactor) AcceptOwnership(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "acceptOwnership")
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_Paymaster *PaymasterSession) AcceptOwnership() (*types.Transaction, error) {
	return _Paymaster.Contract.AcceptOwnership(&_Paymaster.TransactOpts)
}

// AcceptOwnership is a paid mutator transaction binding the contract method 0x79ba5097.
//
// Solidity: function acceptOwnership() returns()
func (_Paymaster *PaymasterTransactorSession) AcceptOwnership() (*types.Transaction, error) {
	return _Paymaster.Contract.AcceptOwnership(&_Paymaster.TransactOpts)
}

// AddStake is a paid mutator transaction binding the contract method 0x0396cb60.
//
// Solidity: function addStake(uint32 unstakeDelaySec) payable returns()
func (_Paymaster *PaymasterTransactor) AddStake(opts *bind.TransactOpts, unstakeDelaySec uint32) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "addStake", unstakeDelaySec)
}

// AddStake is a paid mutator transaction binding the contract method 0x0396cb60.
//
// Solidity: function addStake(uint32 unstakeDelaySec) payable returns()
func (_Paymaster *PaymasterSession) AddStake(unstakeDelaySec uint32) (*types.Transaction, error) {
	return _Paymaster.Contract.AddStake(&_Paymaster.TransactOpts, unstakeDelaySec)
}

// AddStake is a paid mutator transaction binding the contract method 0x0396cb60.
//
// Solidity: function addStake(uint32 unstakeDelaySec) payable returns()
func (_Paymaster *PaymasterTransactorSession) AddStake(unstakeDelaySec uint32) (*types.Transaction, error) {
	return _Paymaster.Contract.AddStake(&_Paymaster.TransactOpts, unstakeDelaySec)
}

// Deposit is a paid mutator transaction binding the contract method 0xd0e30db0.
//
// Solidity: function deposit() payable returns()
func (_Paymaster *PaymasterTransactor) Deposit(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "deposit")
}

// Deposit is a paid mutator transaction binding the contract method 0xd0e30db0.
//
// Solidity: function deposit() payable returns()
func (_Paymaster *PaymasterSession) Deposit() (*types.Transaction, error) {
	return _Paymaster.Contract.Deposit(&_Paymaster.TransactOpts)
}

// Deposit is a paid mutator transaction binding the contract method 0xd0e30db0.
//
// Solidity: function deposit() payable returns()
func (_Paymaster *PaymasterTransactorSession) Deposit() (*types.Transaction, error) {
	return _Paymaster.Contract.Deposit(&_Paymaster.TransactOpts)
}

// OwnerWithdrawERC20 is a paid mutator transaction binding the contract method 0x93563a95.
//
// Solidity: function ownerWithdrawERC20(address asset, address to, uint256 amount) returns()
func (_Paymaster *PaymasterTransactor) OwnerWithdrawERC20(opts *bind.TransactOpts, asset common.Address, to common.Address, amount *big.Int) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "ownerWithdrawERC20", asset, to, amount)
}

// OwnerWithdrawERC20 is a paid mutator transaction binding the contract method 0x93563a95.
//
// Solidity: function ownerWithdrawERC20(address asset, address to, uint256 amount) returns()
func (_Paymaster *PaymasterSession) OwnerWithdrawERC20(asset common.Address, to common.Address, amount *big.Int) (*types.Transaction, error) {
	return _Paymaster.Contract.OwnerWithdrawERC20(&_Paymaster.TransactOpts, asset, to, amount)
}

// OwnerWithdrawERC20 is a paid mutator transaction binding the contract method 0x93563a95.
//
// Solidity: function ownerWithdrawERC20(address asset, address to, uint256 amount) returns()
func (_Paymaster *PaymasterTransactorSession) OwnerWithdrawERC20(asset common.Address, to common.Address, amount *big.Int) (*types.Transaction, error) {
	return _Paymaster.Contract.OwnerWithdrawERC20(&_Paymaster.TransactOpts, asset, to, amount)
}

// PostOp is a paid mutator transaction binding the contract method 0x7c627b21.
//
// Solidity: function postOp(uint8 mode, bytes context, uint256 actualGasCost, uint256 actualUserOpFeePerGas) returns()
func (_Paymaster *PaymasterTransactor) PostOp(opts *bind.TransactOpts, mode uint8, context []byte, actualGasCost *big.Int, actualUserOpFeePerGas *big.Int) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "postOp", mode, context, actualGasCost, actualUserOpFeePerGas)
}

// PostOp is a paid mutator transaction binding the contract method 0x7c627b21.
//
// Solidity: function postOp(uint8 mode, bytes context, uint256 actualGasCost, uint256 actualUserOpFeePerGas) returns()
func (_Paymaster *PaymasterSession) PostOp(mode uint8, context []byte, actualGasCost *big.Int, actualUserOpFeePerGas *big.Int) (*types.Transaction, error) {
	return _Paymaster.Contract.PostOp(&_Paymaster.TransactOpts, mode, context, actualGasCost, actualUserOpFeePerGas)
}

// PostOp is a paid mutator transaction binding the contract method 0x7c627b21.
//
// Solidity: function postOp(uint8 mode, bytes context, uint256 actualGasCost, uint256 actualUserOpFeePerGas) returns()
func (_Paymaster *PaymasterTransactorSession) PostOp(mode uint8, context []byte, actualGasCost *big.Int, actualUserOpFeePerGas *big.Int) (*types.Transaction, error) {
	return _Paymaster.Contract.PostOp(&_Paymaster.TransactOpts, mode, context, actualGasCost, actualUserOpFeePerGas)
}

// RotateVerifyingSigner is a paid mutator transaction binding the contract method 0x1f338ed8.
//
// Solidity: function rotateVerifyingSigner() returns()
func (_Paymaster *PaymasterTransactor) RotateVerifyingSigner(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "rotateVerifyingSigner")
}

// RotateVerifyingSigner is a paid mutator transaction binding the contract method 0x1f338ed8.
//
// Solidity: function rotateVerifyingSigner() returns()
func (_Paymaster *PaymasterSession) RotateVerifyingSigner() (*types.Transaction, error) {
	return _Paymaster.Contract.RotateVerifyingSigner(&_Paymaster.TransactOpts)
}

// RotateVerifyingSigner is a paid mutator transaction binding the contract method 0x1f338ed8.
//
// Solidity: function rotateVerifyingSigner() returns()
func (_Paymaster *PaymasterTransactorSession) RotateVerifyingSigner() (*types.Transaction, error) {
	return _Paymaster.Contract.RotateVerifyingSigner(&_Paymaster.TransactOpts)
}

// SetPendingVerifyingSigner is a paid mutator transaction binding the contract method 0x6fc45f4a.
//
// Solidity: function setPendingVerifyingSigner(address signer) returns()
func (_Paymaster *PaymasterTransactor) SetPendingVerifyingSigner(opts *bind.TransactOpts, signer common.Address) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "setPendingVerifyingSigner", signer)
}

// SetPendingVerifyingSigner is a paid mutator transaction binding the contract method 0x6fc45f4a.
//
// Solidity: function setPendingVerifyingSigner(address signer) returns()
func (_Paymaster *PaymasterSession) SetPendingVerifyingSigner(signer common.Address) (*types.Transaction, error) {
	return _Paymaster.Contract.SetPendingVerifyingSigner(&_Paymaster.TransactOpts, signer)
}

// SetPendingVerifyingSigner is a paid mutator transaction binding the contract method 0x6fc45f4a.
//
// Solidity: function setPendingVerifyingSigner(address signer) returns()
func (_Paymaster *PaymasterTransactorSession) SetPendingVerifyingSigner(signer common.Address) (*types.Transaction, error) {
	return _Paymaster.Contract.SetPendingVerifyingSigner(&_Paymaster.TransactOpts, signer)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_Paymaster *PaymasterTransactor) TransferOwnership(opts *bind.TransactOpts, newOwner common.Address) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "transferOwnership", newOwner)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_Paymaster *PaymasterSession) TransferOwnership(newOwner common.Address) (*types.Transaction, error) {
	return _Paymaster.Contract.TransferOwnership(&_Paymaster.TransactOpts, newOwner)
}

// TransferOwnership is a paid mutator transaction binding the contract method 0xf2fde38b.
//
// Solidity: function transferOwnership(address newOwner) returns()
func (_Paymaster *PaymasterTransactorSession) TransferOwnership(newOwner common.Address) (*types.Transaction, error) {
	return _Paymaster.Contract.TransferOwnership(&_Paymaster.TransactOpts, newOwner)
}

// UnlockStake is a paid mutator transaction binding the contract method 0xbb9fe6bf.
//
// Solidity: function unlockStake() returns()
func (_Paymaster *PaymasterTransactor) UnlockStake(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "unlockStake")
}

// UnlockStake is a paid mutator transaction binding the contract method 0xbb9fe6bf.
//
// Solidity: function unlockStake() returns()
func (_Paymaster *PaymasterSession) UnlockStake() (*types.Transaction, error) {
	return _Paymaster.Contract.UnlockStake(&_Paymaster.TransactOpts)
}

// UnlockStake is a paid mutator transaction binding the contract method 0xbb9fe6bf.
//
// Solidity: function unlockStake() returns()
func (_Paymaster *PaymasterTransactorSession) UnlockStake() (*types.Transaction, error) {
	return _Paymaster.Contract.UnlockStake(&_Paymaster.TransactOpts)
}

// UpdateBundlerAllowlist is a paid mutator transaction binding the contract method 0x7dd345cb.
//
// Solidity: function updateBundlerAllowlist(address[] bundlers, bool allowed) returns()
func (_Paymaster *PaymasterTransactor) UpdateBundlerAllowlist(opts *bind.TransactOpts, bundlers []common.Address, allowed bool) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "updateBundlerAllowlist", bundlers, allowed)
}

// UpdateBundlerAllowlist is a paid mutator transaction binding the contract method 0x7dd345cb.
//
// Solidity: function updateBundlerAllowlist(address[] bundlers, bool allowed) returns()
func (_Paymaster *PaymasterSession) UpdateBundlerAllowlist(bundlers []common.Address, allowed bool) (*types.Transaction, error) {
	return _Paymaster.Contract.UpdateBundlerAllowlist(&_Paymaster.TransactOpts, bundlers, allowed)
}

// UpdateBundlerAllowlist is a paid mutator transaction binding the contract method 0x7dd345cb.
//
// Solidity: function updateBundlerAllowlist(address[] bundlers, bool allowed) returns()
func (_Paymaster *PaymasterTransactorSession) UpdateBundlerAllowlist(bundlers []common.Address, allowed bool) (*types.Transaction, error) {
	return _Paymaster.Contract.UpdateBundlerAllowlist(&_Paymaster.TransactOpts, bundlers, allowed)
}

// ValidatePaymasterUserOp is a paid mutator transaction binding the contract method 0x52b7512c.
//
// Solidity: function validatePaymasterUserOp((address,uint256,bytes,bytes,bytes32,uint256,bytes32,bytes,bytes) userOp, bytes32 userOpHash, uint256 maxCost) returns(bytes context, uint256 validationData)
func (_Paymaster *PaymasterTransactor) ValidatePaymasterUserOp(opts *bind.TransactOpts, userOp PackedUserOperation, userOpHash [32]byte, maxCost *big.Int) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "validatePaymasterUserOp", userOp, userOpHash, maxCost)
}

// ValidatePaymasterUserOp is a paid mutator transaction binding the contract method 0x52b7512c.
//
// Solidity: function validatePaymasterUserOp((address,uint256,bytes,bytes,bytes32,uint256,bytes32,bytes,bytes) userOp, bytes32 userOpHash, uint256 maxCost) returns(bytes context, uint256 validationData)
func (_Paymaster *PaymasterSession) ValidatePaymasterUserOp(userOp PackedUserOperation, userOpHash [32]byte, maxCost *big.Int) (*types.Transaction, error) {
	return _Paymaster.Contract.ValidatePaymasterUserOp(&_Paymaster.TransactOpts, userOp, userOpHash, maxCost)
}

// ValidatePaymasterUserOp is a paid mutator transaction binding the contract method 0x52b7512c.
//
// Solidity: function validatePaymasterUserOp((address,uint256,bytes,bytes,bytes32,uint256,bytes32,bytes,bytes) userOp, bytes32 userOpHash, uint256 maxCost) returns(bytes context, uint256 validationData)
func (_Paymaster *PaymasterTransactorSession) ValidatePaymasterUserOp(userOp PackedUserOperation, userOpHash [32]byte, maxCost *big.Int) (*types.Transaction, error) {
	return _Paymaster.Contract.ValidatePaymasterUserOp(&_Paymaster.TransactOpts, userOp, userOpHash, maxCost)
}

// WithdrawStake is a paid mutator transaction binding the contract method 0xc23a5cea.
//
// Solidity: function withdrawStake(address withdrawAddress) returns()
func (_Paymaster *PaymasterTransactor) WithdrawStake(opts *bind.TransactOpts, withdrawAddress common.Address) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "withdrawStake", withdrawAddress)
}

// WithdrawStake is a paid mutator transaction binding the contract method 0xc23a5cea.
//
// Solidity: function withdrawStake(address withdrawAddress) returns()
func (_Paymaster *PaymasterSession) WithdrawStake(withdrawAddress common.Address) (*types.Transaction, error) {
	return _Paymaster.Contract.WithdrawStake(&_Paymaster.TransactOpts, withdrawAddress)
}

// WithdrawStake is a paid mutator transaction binding the contract method 0xc23a5cea.
//
// Solidity: function withdrawStake(address withdrawAddress) returns()
func (_Paymaster *PaymasterTransactorSession) WithdrawStake(withdrawAddress common.Address) (*types.Transaction, error) {
	return _Paymaster.Contract.WithdrawStake(&_Paymaster.TransactOpts, withdrawAddress)
}

// WithdrawTo is a paid mutator transaction binding the contract method 0x205c2878.
//
// Solidity: function withdrawTo(address withdrawAddress, uint256 amount) returns()
func (_Paymaster *PaymasterTransactor) WithdrawTo(opts *bind.TransactOpts, withdrawAddress common.Address, amount *big.Int) (*types.Transaction, error) {
	return _Paymaster.contract.Transact(opts, "withdrawTo", withdrawAddress, amount)
}

// WithdrawTo is a paid mutator transaction binding the contract method 0x205c2878.
//
// Solidity: function withdrawTo(address withdrawAddress, uint256 amount) returns()
func (_Paymaster *PaymasterSession) WithdrawTo(withdrawAddress common.Address, amount *big.Int) (*types.Transaction, error) {
	return _Paymaster.Contract.WithdrawTo(&_Paymaster.TransactOpts, withdrawAddress, amount)
}

// WithdrawTo is a paid mutator transaction binding the contract method 0x205c2878.
//
// Solidity: function withdrawTo(address withdrawAddress, uint256 amount) returns()
func (_Paymaster *PaymasterTransactorSession) WithdrawTo(withdrawAddress common.Address, amount *big.Int) (*types.Transaction, error) {
	return _Paymaster.Contract.WithdrawTo(&_Paymaster.TransactOpts, withdrawAddress, amount)
}

// Receive is a paid mutator transaction binding the contract receive function.
//
// Solidity: receive() payable returns()
func (_Paymaster *PaymasterTransactor) Receive(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Paymaster.contract.RawTransact(opts, nil) // calldata is disallowed for receive function
}

// Receive is a paid mutator transaction binding the contract receive function.
//
// Solidity: receive() payable returns()
func (_Paymaster *PaymasterSession) Receive() (*types.Transaction, error) {
	return _Paymaster.Contract.Receive(&_Paymaster.TransactOpts)
}

// Receive is a paid mutator transaction binding the contract receive function.
//
// Solidity: receive() payable returns()
func (_Paymaster *PaymasterTransactorSession) Receive() (*types.Transaction, error) {
	return _Paymaster.Contract.Receive(&_Paymaster.TransactOpts)
}

// PaymasterBundlerAllowlistUpdatedIterator is returned from FilterBundlerAllowlistUpdated and is used to iterate over the raw logs and unpacked data for BundlerAllowlistUpdated events raised by the Paymaster contract.
type PaymasterBundlerAllowlistUpdatedIterator struct {
	Event *PaymasterBundlerAllowlistUpdated // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *PaymasterBundlerAllowlistUpdatedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(PaymasterBundlerAllowlistUpdated)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(PaymasterBundlerAllowlistUpdated)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *PaymasterBundlerAllowlistUpdatedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *PaymasterBundlerAllowlistUpdatedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// PaymasterBundlerAllowlistUpdated represents a BundlerAllowlistUpdated event raised by the Paymaster contract.
type PaymasterBundlerAllowlistUpdated struct {
	Bundler common.Address
	Allowed bool
	Raw     types.Log // Blockchain specific contextual infos
}

// FilterBundlerAllowlistUpdated is a free log retrieval operation binding the contract event 0x8ff8c5211f68ef53b4bdd15ab2ea6d87be8a3dbf58865bd8325c984057e4fcb4.
//
// Solidity: event BundlerAllowlistUpdated(address bundler, bool allowed)
func (_Paymaster *PaymasterFilterer) FilterBundlerAllowlistUpdated(opts *bind.FilterOpts) (*PaymasterBundlerAllowlistUpdatedIterator, error) {

	logs, sub, err := _Paymaster.contract.FilterLogs(opts, "BundlerAllowlistUpdated")
	if err != nil {
		return nil, err
	}
	return &PaymasterBundlerAllowlistUpdatedIterator{contract: _Paymaster.contract, event: "BundlerAllowlistUpdated", logs: logs, sub: sub}, nil
}

// WatchBundlerAllowlistUpdated is a free log subscription operation binding the contract event 0x8ff8c5211f68ef53b4bdd15ab2ea6d87be8a3dbf58865bd8325c984057e4fcb4.
//
// Solidity: event BundlerAllowlistUpdated(address bundler, bool allowed)
func (_Paymaster *PaymasterFilterer) WatchBundlerAllowlistUpdated(opts *bind.WatchOpts, sink chan<- *PaymasterBundlerAllowlistUpdated) (event.Subscription, error) {

	logs, sub, err := _Paymaster.contract.WatchLogs(opts, "BundlerAllowlistUpdated")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(PaymasterBundlerAllowlistUpdated)
				if err := _Paymaster.contract.UnpackLog(event, "BundlerAllowlistUpdated", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseBundlerAllowlistUpdated is a log parse operation binding the contract event 0x8ff8c5211f68ef53b4bdd15ab2ea6d87be8a3dbf58865bd8325c984057e4fcb4.
//
// Solidity: event BundlerAllowlistUpdated(address bundler, bool allowed)
func (_Paymaster *PaymasterFilterer) ParseBundlerAllowlistUpdated(log types.Log) (*PaymasterBundlerAllowlistUpdated, error) {
	event := new(PaymasterBundlerAllowlistUpdated)
	if err := _Paymaster.contract.UnpackLog(event, "BundlerAllowlistUpdated", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// PaymasterOwnershipTransferStartedIterator is returned from FilterOwnershipTransferStarted and is used to iterate over the raw logs and unpacked data for OwnershipTransferStarted events raised by the Paymaster contract.
type PaymasterOwnershipTransferStartedIterator struct {
	Event *PaymasterOwnershipTransferStarted // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *PaymasterOwnershipTransferStartedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(PaymasterOwnershipTransferStarted)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(PaymasterOwnershipTransferStarted)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *PaymasterOwnershipTransferStartedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *PaymasterOwnershipTransferStartedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// PaymasterOwnershipTransferStarted represents a OwnershipTransferStarted event raised by the Paymaster contract.
type PaymasterOwnershipTransferStarted struct {
	PreviousOwner common.Address
	NewOwner      common.Address
	Raw           types.Log // Blockchain specific contextual infos
}

// FilterOwnershipTransferStarted is a free log retrieval operation binding the contract event 0x38d16b8cac22d99fc7c124b9cd0de2d3fa1faef420bfe791d8c362d765e22700.
//
// Solidity: event OwnershipTransferStarted(address indexed previousOwner, address indexed newOwner)
func (_Paymaster *PaymasterFilterer) FilterOwnershipTransferStarted(opts *bind.FilterOpts, previousOwner []common.Address, newOwner []common.Address) (*PaymasterOwnershipTransferStartedIterator, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _Paymaster.contract.FilterLogs(opts, "OwnershipTransferStarted", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return &PaymasterOwnershipTransferStartedIterator{contract: _Paymaster.contract, event: "OwnershipTransferStarted", logs: logs, sub: sub}, nil
}

// WatchOwnershipTransferStarted is a free log subscription operation binding the contract event 0x38d16b8cac22d99fc7c124b9cd0de2d3fa1faef420bfe791d8c362d765e22700.
//
// Solidity: event OwnershipTransferStarted(address indexed previousOwner, address indexed newOwner)
func (_Paymaster *PaymasterFilterer) WatchOwnershipTransferStarted(opts *bind.WatchOpts, sink chan<- *PaymasterOwnershipTransferStarted, previousOwner []common.Address, newOwner []common.Address) (event.Subscription, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _Paymaster.contract.WatchLogs(opts, "OwnershipTransferStarted", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(PaymasterOwnershipTransferStarted)
				if err := _Paymaster.contract.UnpackLog(event, "OwnershipTransferStarted", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseOwnershipTransferStarted is a log parse operation binding the contract event 0x38d16b8cac22d99fc7c124b9cd0de2d3fa1faef420bfe791d8c362d765e22700.
//
// Solidity: event OwnershipTransferStarted(address indexed previousOwner, address indexed newOwner)
func (_Paymaster *PaymasterFilterer) ParseOwnershipTransferStarted(log types.Log) (*PaymasterOwnershipTransferStarted, error) {
	event := new(PaymasterOwnershipTransferStarted)
	if err := _Paymaster.contract.UnpackLog(event, "OwnershipTransferStarted", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// PaymasterOwnershipTransferredIterator is returned from FilterOwnershipTransferred and is used to iterate over the raw logs and unpacked data for OwnershipTransferred events raised by the Paymaster contract.
type PaymasterOwnershipTransferredIterator struct {
	Event *PaymasterOwnershipTransferred // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *PaymasterOwnershipTransferredIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(PaymasterOwnershipTransferred)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(PaymasterOwnershipTransferred)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *PaymasterOwnershipTransferredIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *PaymasterOwnershipTransferredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// PaymasterOwnershipTransferred represents a OwnershipTransferred event raised by the Paymaster contract.
type PaymasterOwnershipTransferred struct {
	PreviousOwner common.Address
	NewOwner      common.Address
	Raw           types.Log // Blockchain specific contextual infos
}

// FilterOwnershipTransferred is a free log retrieval operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_Paymaster *PaymasterFilterer) FilterOwnershipTransferred(opts *bind.FilterOpts, previousOwner []common.Address, newOwner []common.Address) (*PaymasterOwnershipTransferredIterator, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _Paymaster.contract.FilterLogs(opts, "OwnershipTransferred", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return &PaymasterOwnershipTransferredIterator{contract: _Paymaster.contract, event: "OwnershipTransferred", logs: logs, sub: sub}, nil
}

// WatchOwnershipTransferred is a free log subscription operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_Paymaster *PaymasterFilterer) WatchOwnershipTransferred(opts *bind.WatchOpts, sink chan<- *PaymasterOwnershipTransferred, previousOwner []common.Address, newOwner []common.Address) (event.Subscription, error) {

	var previousOwnerRule []interface{}
	for _, previousOwnerItem := range previousOwner {
		previousOwnerRule = append(previousOwnerRule, previousOwnerItem)
	}
	var newOwnerRule []interface{}
	for _, newOwnerItem := range newOwner {
		newOwnerRule = append(newOwnerRule, newOwnerItem)
	}

	logs, sub, err := _Paymaster.contract.WatchLogs(opts, "OwnershipTransferred", previousOwnerRule, newOwnerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(PaymasterOwnershipTransferred)
				if err := _Paymaster.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseOwnershipTransferred is a log parse operation binding the contract event 0x8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0.
//
// Solidity: event OwnershipTransferred(address indexed previousOwner, address indexed newOwner)
func (_Paymaster *PaymasterFilterer) ParseOwnershipTransferred(log types.Log) (*PaymasterOwnershipTransferred, error) {
	event := new(PaymasterOwnershipTransferred)
	if err := _Paymaster.contract.UnpackLog(event, "OwnershipTransferred", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// PaymasterPendingVerifyingSignerSetIterator is returned from FilterPendingVerifyingSignerSet and is used to iterate over the raw logs and unpacked data for PendingVerifyingSignerSet events raised by the Paymaster contract.
type PaymasterPendingVerifyingSignerSetIterator struct {
	Event *PaymasterPendingVerifyingSignerSet // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *PaymasterPendingVerifyingSignerSetIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(PaymasterPendingVerifyingSignerSet)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(PaymasterPendingVerifyingSignerSet)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *PaymasterPendingVerifyingSignerSetIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *PaymasterPendingVerifyingSignerSetIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// PaymasterPendingVerifyingSignerSet represents a PendingVerifyingSignerSet event raised by the Paymaster contract.
type PaymasterPendingVerifyingSignerSet struct {
	Signer common.Address
	Raw    types.Log // Blockchain specific contextual infos
}

// FilterPendingVerifyingSignerSet is a free log retrieval operation binding the contract event 0xe6846361b77f46dd08e26fd167e64b6166b0fe77d822b2178bd06cf629c5dd93.
//
// Solidity: event PendingVerifyingSignerSet(address signer)
func (_Paymaster *PaymasterFilterer) FilterPendingVerifyingSignerSet(opts *bind.FilterOpts) (*PaymasterPendingVerifyingSignerSetIterator, error) {

	logs, sub, err := _Paymaster.contract.FilterLogs(opts, "PendingVerifyingSignerSet")
	if err != nil {
		return nil, err
	}
	return &PaymasterPendingVerifyingSignerSetIterator{contract: _Paymaster.contract, event: "PendingVerifyingSignerSet", logs: logs, sub: sub}, nil
}

// WatchPendingVerifyingSignerSet is a free log subscription operation binding the contract event 0xe6846361b77f46dd08e26fd167e64b6166b0fe77d822b2178bd06cf629c5dd93.
//
// Solidity: event PendingVerifyingSignerSet(address signer)
func (_Paymaster *PaymasterFilterer) WatchPendingVerifyingSignerSet(opts *bind.WatchOpts, sink chan<- *PaymasterPendingVerifyingSignerSet) (event.Subscription, error) {

	logs, sub, err := _Paymaster.contract.WatchLogs(opts, "PendingVerifyingSignerSet")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(PaymasterPendingVerifyingSignerSet)
				if err := _Paymaster.contract.UnpackLog(event, "PendingVerifyingSignerSet", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParsePendingVerifyingSignerSet is a log parse operation binding the contract event 0xe6846361b77f46dd08e26fd167e64b6166b0fe77d822b2178bd06cf629c5dd93.
//
// Solidity: event PendingVerifyingSignerSet(address signer)
func (_Paymaster *PaymasterFilterer) ParsePendingVerifyingSignerSet(log types.Log) (*PaymasterPendingVerifyingSignerSet, error) {
	event := new(PaymasterPendingVerifyingSignerSet)
	if err := _Paymaster.contract.UnpackLog(event, "PendingVerifyingSignerSet", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// PaymasterUserOperationSponsoredIterator is returned from FilterUserOperationSponsored and is used to iterate over the raw logs and unpacked data for UserOperationSponsored events raised by the Paymaster contract.
type PaymasterUserOperationSponsoredIterator struct {
	Event *PaymasterUserOperationSponsored // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *PaymasterUserOperationSponsoredIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(PaymasterUserOperationSponsored)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(PaymasterUserOperationSponsored)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *PaymasterUserOperationSponsoredIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *PaymasterUserOperationSponsoredIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// PaymasterUserOperationSponsored represents a UserOperationSponsored event raised by the Paymaster contract.
type PaymasterUserOperationSponsored struct {
	UserOperationHash [32]byte
	SponsorUUID       *big.Int
	Token             common.Address
	Raw               types.Log // Blockchain specific contextual infos
}

// FilterUserOperationSponsored is a free log retrieval operation binding the contract event 0xfc6d279aa22eb9d6ba081f1bd2443d1e1c8eb3bcf089906d78064616798fbad0.
//
// Solidity: event UserOperationSponsored(bytes32 indexed userOperationHash, uint128 indexed sponsorUUID, address token)
func (_Paymaster *PaymasterFilterer) FilterUserOperationSponsored(opts *bind.FilterOpts, userOperationHash [][32]byte, sponsorUUID []*big.Int) (*PaymasterUserOperationSponsoredIterator, error) {

	var userOperationHashRule []interface{}
	for _, userOperationHashItem := range userOperationHash {
		userOperationHashRule = append(userOperationHashRule, userOperationHashItem)
	}
	var sponsorUUIDRule []interface{}
	for _, sponsorUUIDItem := range sponsorUUID {
		sponsorUUIDRule = append(sponsorUUIDRule, sponsorUUIDItem)
	}

	logs, sub, err := _Paymaster.contract.FilterLogs(opts, "UserOperationSponsored", userOperationHashRule, sponsorUUIDRule)
	if err != nil {
		return nil, err
	}
	return &PaymasterUserOperationSponsoredIterator{contract: _Paymaster.contract, event: "UserOperationSponsored", logs: logs, sub: sub}, nil
}

// WatchUserOperationSponsored is a free log subscription operation binding the contract event 0xfc6d279aa22eb9d6ba081f1bd2443d1e1c8eb3bcf089906d78064616798fbad0.
//
// Solidity: event UserOperationSponsored(bytes32 indexed userOperationHash, uint128 indexed sponsorUUID, address token)
func (_Paymaster *PaymasterFilterer) WatchUserOperationSponsored(opts *bind.WatchOpts, sink chan<- *PaymasterUserOperationSponsored, userOperationHash [][32]byte, sponsorUUID []*big.Int) (event.Subscription, error) {

	var userOperationHashRule []interface{}
	for _, userOperationHashItem := range userOperationHash {
		userOperationHashRule = append(userOperationHashRule, userOperationHashItem)
	}
	var sponsorUUIDRule []interface{}
	for _, sponsorUUIDItem := range sponsorUUID {
		sponsorUUIDRule = append(sponsorUUIDRule, sponsorUUIDItem)
	}

	logs, sub, err := _Paymaster.contract.WatchLogs(opts, "UserOperationSponsored", userOperationHashRule, sponsorUUIDRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(PaymasterUserOperationSponsored)
				if err := _Paymaster.contract.UnpackLog(event, "UserOperationSponsored", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseUserOperationSponsored is a log parse operation binding the contract event 0xfc6d279aa22eb9d6ba081f1bd2443d1e1c8eb3bcf089906d78064616798fbad0.
//
// Solidity: event UserOperationSponsored(bytes32 indexed userOperationHash, uint128 indexed sponsorUUID, address token)
func (_Paymaster *PaymasterFilterer) ParseUserOperationSponsored(log types.Log) (*PaymasterUserOperationSponsored, error) {
	event := new(PaymasterUserOperationSponsored)
	if err := _Paymaster.contract.UnpackLog(event, "UserOperationSponsored", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// PaymasterUserOperationSponsoredWithERC20Iterator is returned from FilterUserOperationSponsoredWithERC20 and is used to iterate over the raw logs and unpacked data for UserOperationSponsoredWithERC20 events raised by the Paymaster contract.
type PaymasterUserOperationSponsoredWithERC20Iterator struct {
	Event *PaymasterUserOperationSponsoredWithERC20 // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *PaymasterUserOperationSponsoredWithERC20Iterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(PaymasterUserOperationSponsoredWithERC20)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(PaymasterUserOperationSponsoredWithERC20)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *PaymasterUserOperationSponsoredWithERC20Iterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *PaymasterUserOperationSponsoredWithERC20Iterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// PaymasterUserOperationSponsoredWithERC20 represents a UserOperationSponsoredWithERC20 event raised by the Paymaster contract.
type PaymasterUserOperationSponsoredWithERC20 struct {
	UserOperationHash [32]byte
	SponsorUUID       *big.Int
	Token             common.Address
	Receiver          common.Address
	Amount            *big.Int
	Raw               types.Log // Blockchain specific contextual infos
}

// FilterUserOperationSponsoredWithERC20 is a free log retrieval operation binding the contract event 0xe2459e4c2a3c2f358a092e76af1f3b2337c91875a1d49fe8afd6126342444f6f.
//
// Solidity: event UserOperationSponsoredWithERC20(bytes32 indexed userOperationHash, uint128 indexed sponsorUUID, address indexed token, address receiver, uint256 amount)
func (_Paymaster *PaymasterFilterer) FilterUserOperationSponsoredWithERC20(opts *bind.FilterOpts, userOperationHash [][32]byte, sponsorUUID []*big.Int, token []common.Address) (*PaymasterUserOperationSponsoredWithERC20Iterator, error) {

	var userOperationHashRule []interface{}
	for _, userOperationHashItem := range userOperationHash {
		userOperationHashRule = append(userOperationHashRule, userOperationHashItem)
	}
	var sponsorUUIDRule []interface{}
	for _, sponsorUUIDItem := range sponsorUUID {
		sponsorUUIDRule = append(sponsorUUIDRule, sponsorUUIDItem)
	}
	var tokenRule []interface{}
	for _, tokenItem := range token {
		tokenRule = append(tokenRule, tokenItem)
	}

	logs, sub, err := _Paymaster.contract.FilterLogs(opts, "UserOperationSponsoredWithERC20", userOperationHashRule, sponsorUUIDRule, tokenRule)
	if err != nil {
		return nil, err
	}
	return &PaymasterUserOperationSponsoredWithERC20Iterator{contract: _Paymaster.contract, event: "UserOperationSponsoredWithERC20", logs: logs, sub: sub}, nil
}

// WatchUserOperationSponsoredWithERC20 is a free log subscription operation binding the contract event 0xe2459e4c2a3c2f358a092e76af1f3b2337c91875a1d49fe8afd6126342444f6f.
//
// Solidity: event UserOperationSponsoredWithERC20(bytes32 indexed userOperationHash, uint128 indexed sponsorUUID, address indexed token, address receiver, uint256 amount)
func (_Paymaster *PaymasterFilterer) WatchUserOperationSponsoredWithERC20(opts *bind.WatchOpts, sink chan<- *PaymasterUserOperationSponsoredWithERC20, userOperationHash [][32]byte, sponsorUUID []*big.Int, token []common.Address) (event.Subscription, error) {

	var userOperationHashRule []interface{}
	for _, userOperationHashItem := range userOperationHash {
		userOperationHashRule = append(userOperationHashRule, userOperationHashItem)
	}
	var sponsorUUIDRule []interface{}
	for _, sponsorUUIDItem := range sponsorUUID {
		sponsorUUIDRule = append(sponsorUUIDRule, sponsorUUIDItem)
	}
	var tokenRule []interface{}
	for _, tokenItem := range token {
		tokenRule = append(tokenRule, tokenItem)
	}

	logs, sub, err := _Paymaster.contract.WatchLogs(opts, "UserOperationSponsoredWithERC20", userOperationHashRule, sponsorUUIDRule, tokenRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(PaymasterUserOperationSponsoredWithERC20)
				if err := _Paymaster.contract.UnpackLog(event, "UserOperationSponsoredWithERC20", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseUserOperationSponsoredWithERC20 is a log parse operation binding the contract event 0xe2459e4c2a3c2f358a092e76af1f3b2337c91875a1d49fe8afd6126342444f6f.
//
// Solidity: event UserOperationSponsoredWithERC20(bytes32 indexed userOperationHash, uint128 indexed sponsorUUID, address indexed token, address receiver, uint256 amount)
func (_Paymaster *PaymasterFilterer) ParseUserOperationSponsoredWithERC20(log types.Log) (*PaymasterUserOperationSponsoredWithERC20, error) {
	event := new(PaymasterUserOperationSponsoredWithERC20)
	if err := _Paymaster.contract.UnpackLog(event, "UserOperationSponsoredWithERC20", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// PaymasterVerifyingSignerRotatedIterator is returned from FilterVerifyingSignerRotated and is used to iterate over the raw logs and unpacked data for VerifyingSignerRotated events raised by the Paymaster contract.
type PaymasterVerifyingSignerRotatedIterator struct {
	Event *PaymasterVerifyingSignerRotated // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *PaymasterVerifyingSignerRotatedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(PaymasterVerifyingSignerRotated)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(PaymasterVerifyingSignerRotated)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *PaymasterVerifyingSignerRotatedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *PaymasterVerifyingSignerRotatedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// PaymasterVerifyingSignerRotated represents a VerifyingSignerRotated event raised by the Paymaster contract.
type PaymasterVerifyingSignerRotated struct {
	OldSigner common.Address
	NewSigner common.Address
	Raw       types.Log // Blockchain specific contextual infos
}

// FilterVerifyingSignerRotated is a free log retrieval operation binding the contract event 0x25beca4c8409103108ee57a3f82258e57ef286914874cd10638d85c37d1427f7.
//
// Solidity: event VerifyingSignerRotated(address oldSigner, address newSigner)
func (_Paymaster *PaymasterFilterer) FilterVerifyingSignerRotated(opts *bind.FilterOpts) (*PaymasterVerifyingSignerRotatedIterator, error) {

	logs, sub, err := _Paymaster.contract.FilterLogs(opts, "VerifyingSignerRotated")
	if err != nil {
		return nil, err
	}
	return &PaymasterVerifyingSignerRotatedIterator{contract: _Paymaster.contract, event: "VerifyingSignerRotated", logs: logs, sub: sub}, nil
}

// WatchVerifyingSignerRotated is a free log subscription operation binding the contract event 0x25beca4c8409103108ee57a3f82258e57ef286914874cd10638d85c37d1427f7.
//
// Solidity: event VerifyingSignerRotated(address oldSigner, address newSigner)
func (_Paymaster *PaymasterFilterer) WatchVerifyingSignerRotated(opts *bind.WatchOpts, sink chan<- *PaymasterVerifyingSignerRotated) (event.Subscription, error) {

	logs, sub, err := _Paymaster.contract.WatchLogs(opts, "VerifyingSignerRotated")
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(PaymasterVerifyingSignerRotated)
				if err := _Paymaster.contract.UnpackLog(event, "VerifyingSignerRotated", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseVerifyingSignerRotated is a log parse operation binding the contract event 0x25beca4c8409103108ee57a3f82258e57ef286914874cd10638d85c37d1427f7.
//
// Solidity: event VerifyingSignerRotated(address oldSigner, address newSigner)
func (_Paymaster *PaymasterFilterer) ParseVerifyingSignerRotated(log types.Log) (*PaymasterVerifyingSignerRotated, error) {
	event := new(PaymasterVerifyingSignerRotated)
	if err := _Paymaster.contract.UnpackLog(event, "VerifyingSignerRotated", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}
