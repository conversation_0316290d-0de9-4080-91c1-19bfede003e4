// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package abibinding

import (
	"errors"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

// SimpleaccountfactoryMetaData contains all meta data concerning the Simpleaccountfactory contract.
var SimpleaccountfactoryMetaData = &bind.MetaData{
	ABI: "[{\"type\":\"constructor\",\"inputs\":[{\"name\":\"_entryPoint\",\"type\":\"address\",\"internalType\":\"contractIEntryPoint\"}],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"accountImplementation\",\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"contractSimpleAccount\"}],\"stateMutability\":\"view\"},{\"type\":\"function\",\"name\":\"createAccount\",\"inputs\":[{\"name\":\"owner\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"salt\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"outputs\":[{\"name\":\"ret\",\"type\":\"address\",\"internalType\":\"contractSimpleAccount\"}],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"getAddress\",\"inputs\":[{\"name\":\"owner\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"salt\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"outputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"stateMutability\":\"view\"}]",
}

// SimpleaccountfactoryABI is the input ABI used to generate the binding from.
// Deprecated: Use SimpleaccountfactoryMetaData.ABI instead.
var SimpleaccountfactoryABI = SimpleaccountfactoryMetaData.ABI

// Simpleaccountfactory is an auto generated Go binding around an Ethereum contract.
type Simpleaccountfactory struct {
	SimpleaccountfactoryCaller     // Read-only binding to the contract
	SimpleaccountfactoryTransactor // Write-only binding to the contract
	SimpleaccountfactoryFilterer   // Log filterer for contract events
}

// SimpleaccountfactoryCaller is an auto generated read-only Go binding around an Ethereum contract.
type SimpleaccountfactoryCaller struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// SimpleaccountfactoryTransactor is an auto generated write-only Go binding around an Ethereum contract.
type SimpleaccountfactoryTransactor struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// SimpleaccountfactoryFilterer is an auto generated log filtering Go binding around an Ethereum contract events.
type SimpleaccountfactoryFilterer struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// SimpleaccountfactorySession is an auto generated Go binding around an Ethereum contract,
// with pre-set call and transact options.
type SimpleaccountfactorySession struct {
	Contract     *Simpleaccountfactory // Generic contract binding to set the session for
	CallOpts     bind.CallOpts         // Call options to use throughout this session
	TransactOpts bind.TransactOpts     // Transaction auth options to use throughout this session
}

// SimpleaccountfactoryCallerSession is an auto generated read-only Go binding around an Ethereum contract,
// with pre-set call options.
type SimpleaccountfactoryCallerSession struct {
	Contract *SimpleaccountfactoryCaller // Generic contract caller binding to set the session for
	CallOpts bind.CallOpts               // Call options to use throughout this session
}

// SimpleaccountfactoryTransactorSession is an auto generated write-only Go binding around an Ethereum contract,
// with pre-set transact options.
type SimpleaccountfactoryTransactorSession struct {
	Contract     *SimpleaccountfactoryTransactor // Generic contract transactor binding to set the session for
	TransactOpts bind.TransactOpts               // Transaction auth options to use throughout this session
}

// SimpleaccountfactoryRaw is an auto generated low-level Go binding around an Ethereum contract.
type SimpleaccountfactoryRaw struct {
	Contract *Simpleaccountfactory // Generic contract binding to access the raw methods on
}

// SimpleaccountfactoryCallerRaw is an auto generated low-level read-only Go binding around an Ethereum contract.
type SimpleaccountfactoryCallerRaw struct {
	Contract *SimpleaccountfactoryCaller // Generic read-only contract binding to access the raw methods on
}

// SimpleaccountfactoryTransactorRaw is an auto generated low-level write-only Go binding around an Ethereum contract.
type SimpleaccountfactoryTransactorRaw struct {
	Contract *SimpleaccountfactoryTransactor // Generic write-only contract binding to access the raw methods on
}

// NewSimpleaccountfactory creates a new instance of Simpleaccountfactory, bound to a specific deployed contract.
func NewSimpleaccountfactory(address common.Address, backend bind.ContractBackend) (*Simpleaccountfactory, error) {
	contract, err := bindSimpleaccountfactory(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &Simpleaccountfactory{SimpleaccountfactoryCaller: SimpleaccountfactoryCaller{contract: contract}, SimpleaccountfactoryTransactor: SimpleaccountfactoryTransactor{contract: contract}, SimpleaccountfactoryFilterer: SimpleaccountfactoryFilterer{contract: contract}}, nil
}

// NewSimpleaccountfactoryCaller creates a new read-only instance of Simpleaccountfactory, bound to a specific deployed contract.
func NewSimpleaccountfactoryCaller(address common.Address, caller bind.ContractCaller) (*SimpleaccountfactoryCaller, error) {
	contract, err := bindSimpleaccountfactory(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &SimpleaccountfactoryCaller{contract: contract}, nil
}

// NewSimpleaccountfactoryTransactor creates a new write-only instance of Simpleaccountfactory, bound to a specific deployed contract.
func NewSimpleaccountfactoryTransactor(address common.Address, transactor bind.ContractTransactor) (*SimpleaccountfactoryTransactor, error) {
	contract, err := bindSimpleaccountfactory(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &SimpleaccountfactoryTransactor{contract: contract}, nil
}

// NewSimpleaccountfactoryFilterer creates a new log filterer instance of Simpleaccountfactory, bound to a specific deployed contract.
func NewSimpleaccountfactoryFilterer(address common.Address, filterer bind.ContractFilterer) (*SimpleaccountfactoryFilterer, error) {
	contract, err := bindSimpleaccountfactory(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &SimpleaccountfactoryFilterer{contract: contract}, nil
}

// bindSimpleaccountfactory binds a generic wrapper to an already deployed contract.
func bindSimpleaccountfactory(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := SimpleaccountfactoryMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_Simpleaccountfactory *SimpleaccountfactoryRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _Simpleaccountfactory.Contract.SimpleaccountfactoryCaller.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_Simpleaccountfactory *SimpleaccountfactoryRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Simpleaccountfactory.Contract.SimpleaccountfactoryTransactor.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_Simpleaccountfactory *SimpleaccountfactoryRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _Simpleaccountfactory.Contract.SimpleaccountfactoryTransactor.contract.Transact(opts, method, params...)
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_Simpleaccountfactory *SimpleaccountfactoryCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _Simpleaccountfactory.Contract.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_Simpleaccountfactory *SimpleaccountfactoryTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _Simpleaccountfactory.Contract.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_Simpleaccountfactory *SimpleaccountfactoryTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _Simpleaccountfactory.Contract.contract.Transact(opts, method, params...)
}

// AccountImplementation is a free data retrieval call binding the contract method 0x11464fbe.
//
// Solidity: function accountImplementation() view returns(address)
func (_Simpleaccountfactory *SimpleaccountfactoryCaller) AccountImplementation(opts *bind.CallOpts) (common.Address, error) {
	var out []interface{}
	err := _Simpleaccountfactory.contract.Call(opts, &out, "accountImplementation")

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// AccountImplementation is a free data retrieval call binding the contract method 0x11464fbe.
//
// Solidity: function accountImplementation() view returns(address)
func (_Simpleaccountfactory *SimpleaccountfactorySession) AccountImplementation() (common.Address, error) {
	return _Simpleaccountfactory.Contract.AccountImplementation(&_Simpleaccountfactory.CallOpts)
}

// AccountImplementation is a free data retrieval call binding the contract method 0x11464fbe.
//
// Solidity: function accountImplementation() view returns(address)
func (_Simpleaccountfactory *SimpleaccountfactoryCallerSession) AccountImplementation() (common.Address, error) {
	return _Simpleaccountfactory.Contract.AccountImplementation(&_Simpleaccountfactory.CallOpts)
}

// GetAddress is a free data retrieval call binding the contract method 0x8cb84e18.
//
// Solidity: function getAddress(address owner, uint256 salt) view returns(address)
func (_Simpleaccountfactory *SimpleaccountfactoryCaller) GetAddress(opts *bind.CallOpts, owner common.Address, salt *big.Int) (common.Address, error) {
	var out []interface{}
	err := _Simpleaccountfactory.contract.Call(opts, &out, "getAddress", owner, salt)

	if err != nil {
		return *new(common.Address), err
	}

	out0 := *abi.ConvertType(out[0], new(common.Address)).(*common.Address)

	return out0, err

}

// GetAddress is a free data retrieval call binding the contract method 0x8cb84e18.
//
// Solidity: function getAddress(address owner, uint256 salt) view returns(address)
func (_Simpleaccountfactory *SimpleaccountfactorySession) GetAddress(owner common.Address, salt *big.Int) (common.Address, error) {
	return _Simpleaccountfactory.Contract.GetAddress(&_Simpleaccountfactory.CallOpts, owner, salt)
}

// GetAddress is a free data retrieval call binding the contract method 0x8cb84e18.
//
// Solidity: function getAddress(address owner, uint256 salt) view returns(address)
func (_Simpleaccountfactory *SimpleaccountfactoryCallerSession) GetAddress(owner common.Address, salt *big.Int) (common.Address, error) {
	return _Simpleaccountfactory.Contract.GetAddress(&_Simpleaccountfactory.CallOpts, owner, salt)
}

// CreateAccount is a paid mutator transaction binding the contract method 0x5fbfb9cf.
//
// Solidity: function createAccount(address owner, uint256 salt) returns(address ret)
func (_Simpleaccountfactory *SimpleaccountfactoryTransactor) CreateAccount(opts *bind.TransactOpts, owner common.Address, salt *big.Int) (*types.Transaction, error) {
	return _Simpleaccountfactory.contract.Transact(opts, "createAccount", owner, salt)
}

// CreateAccount is a paid mutator transaction binding the contract method 0x5fbfb9cf.
//
// Solidity: function createAccount(address owner, uint256 salt) returns(address ret)
func (_Simpleaccountfactory *SimpleaccountfactorySession) CreateAccount(owner common.Address, salt *big.Int) (*types.Transaction, error) {
	return _Simpleaccountfactory.Contract.CreateAccount(&_Simpleaccountfactory.TransactOpts, owner, salt)
}

// CreateAccount is a paid mutator transaction binding the contract method 0x5fbfb9cf.
//
// Solidity: function createAccount(address owner, uint256 salt) returns(address ret)
func (_Simpleaccountfactory *SimpleaccountfactoryTransactorSession) CreateAccount(owner common.Address, salt *big.Int) (*types.Transaction, error) {
	return _Simpleaccountfactory.Contract.CreateAccount(&_Simpleaccountfactory.TransactOpts, owner, salt)
}
