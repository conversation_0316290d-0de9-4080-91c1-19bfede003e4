// Code generated - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package abibinding

import (
	"errors"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/event"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = errors.New
	_ = big.NewInt
	_ = strings.NewReader
	_ = ethereum.NotFound
	_ = bind.Bind
	_ = common.Big1
	_ = types.BloomLookup
	_ = event.NewSubscription
	_ = abi.ConvertType
)

// EphemeralNotesMetaData contains all meta data concerning the EphemeralNotes contract.
var EphemeralNotesMetaData = &bind.MetaData{
	ABI: "[{\"type\":\"receive\",\"stateMutability\":\"payable\"},{\"type\":\"function\",\"name\":\"claimNoteOther\",\"inputs\":[{\"name\":\"_ephemeralOwner\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_recipient\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_signature\",\"type\":\"bytes\",\"internalType\":\"bytes\"},{\"name\":\"_fee\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"claimNoteRecipient\",\"inputs\":[{\"name\":\"_ephemeralOwner\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_signature\",\"type\":\"bytes\",\"internalType\":\"bytes\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"claimNoteSelf\",\"inputs\":[{\"name\":\"_ephemeralOwner\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"createNote\",\"inputs\":[{\"name\":\"_ephemeralOwner\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_token\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_amount\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"outputs\":[],\"stateMutability\":\"payable\"},{\"type\":\"function\",\"name\":\"createNoteWithPermit\",\"inputs\":[{\"name\":\"_ephemeralOwner\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_token\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"_amount\",\"type\":\"uint256\",\"internalType\":\"uint256\"},{\"name\":\"deadline\",\"type\":\"uint256\",\"internalType\":\"uint256\"},{\"name\":\"v\",\"type\":\"uint8\",\"internalType\":\"uint8\"},{\"name\":\"r\",\"type\":\"bytes32\",\"internalType\":\"bytes32\"},{\"name\":\"s\",\"type\":\"bytes32\",\"internalType\":\"bytes32\"}],\"outputs\":[],\"stateMutability\":\"nonpayable\"},{\"type\":\"function\",\"name\":\"notes\",\"inputs\":[{\"name\":\"\",\"type\":\"address\",\"internalType\":\"address\"}],\"outputs\":[{\"name\":\"ephemeralOwner\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"from\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"token\",\"type\":\"address\",\"internalType\":\"address\"},{\"name\":\"amount\",\"type\":\"uint256\",\"internalType\":\"uint256\"}],\"stateMutability\":\"view\"},{\"type\":\"event\",\"name\":\"NoteCreated\",\"inputs\":[{\"name\":\"ephemeralOwner\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"from\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"token\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"amount\",\"type\":\"uint256\",\"indexed\":false,\"internalType\":\"uint256\"}],\"anonymous\":false},{\"type\":\"event\",\"name\":\"NoteRedeemed\",\"inputs\":[{\"name\":\"ephemeralOwner\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"redeemer\",\"type\":\"address\",\"indexed\":true,\"internalType\":\"address\"},{\"name\":\"amount\",\"type\":\"uint256\",\"indexed\":false,\"internalType\":\"uint256\"},{\"name\":\"fee\",\"type\":\"uint256\",\"indexed\":false,\"internalType\":\"uint256\"}],\"anonymous\":false},{\"type\":\"error\",\"name\":\"AddressEmptyCode\",\"inputs\":[{\"name\":\"target\",\"type\":\"address\",\"internalType\":\"address\"}]},{\"type\":\"error\",\"name\":\"AddressInsufficientBalance\",\"inputs\":[{\"name\":\"account\",\"type\":\"address\",\"internalType\":\"address\"}]},{\"type\":\"error\",\"name\":\"ECDSAInvalidSignature\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"ECDSAInvalidSignatureLength\",\"inputs\":[{\"name\":\"length\",\"type\":\"uint256\",\"internalType\":\"uint256\"}]},{\"type\":\"error\",\"name\":\"ECDSAInvalidSignatureS\",\"inputs\":[{\"name\":\"s\",\"type\":\"bytes32\",\"internalType\":\"bytes32\"}]},{\"type\":\"error\",\"name\":\"FailedInnerCall\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"ReentrancyGuardReentrantCall\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"SafeERC20FailedOperation\",\"inputs\":[{\"name\":\"token\",\"type\":\"address\",\"internalType\":\"address\"}]}]",
}

// EphemeralNotesABI is the input ABI used to generate the binding from.
// Deprecated: Use EphemeralNotesMetaData.ABI instead.
var EphemeralNotesABI = EphemeralNotesMetaData.ABI

// EphemeralNotes is an auto generated Go binding around an Ethereum contract.
type EphemeralNotes struct {
	EphemeralNotesCaller     // Read-only binding to the contract
	EphemeralNotesTransactor // Write-only binding to the contract
	EphemeralNotesFilterer   // Log filterer for contract events
}

// EphemeralNotesCaller is an auto generated read-only Go binding around an Ethereum contract.
type EphemeralNotesCaller struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// EphemeralNotesTransactor is an auto generated write-only Go binding around an Ethereum contract.
type EphemeralNotesTransactor struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// EphemeralNotesFilterer is an auto generated log filtering Go binding around an Ethereum contract events.
type EphemeralNotesFilterer struct {
	contract *bind.BoundContract // Generic contract wrapper for the low level calls
}

// EphemeralNotesSession is an auto generated Go binding around an Ethereum contract,
// with pre-set call and transact options.
type EphemeralNotesSession struct {
	Contract     *EphemeralNotes   // Generic contract binding to set the session for
	CallOpts     bind.CallOpts     // Call options to use throughout this session
	TransactOpts bind.TransactOpts // Transaction auth options to use throughout this session
}

// EphemeralNotesCallerSession is an auto generated read-only Go binding around an Ethereum contract,
// with pre-set call options.
type EphemeralNotesCallerSession struct {
	Contract *EphemeralNotesCaller // Generic contract caller binding to set the session for
	CallOpts bind.CallOpts         // Call options to use throughout this session
}

// EphemeralNotesTransactorSession is an auto generated write-only Go binding around an Ethereum contract,
// with pre-set transact options.
type EphemeralNotesTransactorSession struct {
	Contract     *EphemeralNotesTransactor // Generic contract transactor binding to set the session for
	TransactOpts bind.TransactOpts         // Transaction auth options to use throughout this session
}

// EphemeralNotesRaw is an auto generated low-level Go binding around an Ethereum contract.
type EphemeralNotesRaw struct {
	Contract *EphemeralNotes // Generic contract binding to access the raw methods on
}

// EphemeralNotesCallerRaw is an auto generated low-level read-only Go binding around an Ethereum contract.
type EphemeralNotesCallerRaw struct {
	Contract *EphemeralNotesCaller // Generic read-only contract binding to access the raw methods on
}

// EphemeralNotesTransactorRaw is an auto generated low-level write-only Go binding around an Ethereum contract.
type EphemeralNotesTransactorRaw struct {
	Contract *EphemeralNotesTransactor // Generic write-only contract binding to access the raw methods on
}

// NewEphemeralNotes creates a new instance of EphemeralNotes, bound to a specific deployed contract.
func NewEphemeralNotes(address common.Address, backend bind.ContractBackend) (*EphemeralNotes, error) {
	contract, err := bindEphemeralNotes(address, backend, backend, backend)
	if err != nil {
		return nil, err
	}
	return &EphemeralNotes{EphemeralNotesCaller: EphemeralNotesCaller{contract: contract}, EphemeralNotesTransactor: EphemeralNotesTransactor{contract: contract}, EphemeralNotesFilterer: EphemeralNotesFilterer{contract: contract}}, nil
}

// NewEphemeralNotesCaller creates a new read-only instance of EphemeralNotes, bound to a specific deployed contract.
func NewEphemeralNotesCaller(address common.Address, caller bind.ContractCaller) (*EphemeralNotesCaller, error) {
	contract, err := bindEphemeralNotes(address, caller, nil, nil)
	if err != nil {
		return nil, err
	}
	return &EphemeralNotesCaller{contract: contract}, nil
}

// NewEphemeralNotesTransactor creates a new write-only instance of EphemeralNotes, bound to a specific deployed contract.
func NewEphemeralNotesTransactor(address common.Address, transactor bind.ContractTransactor) (*EphemeralNotesTransactor, error) {
	contract, err := bindEphemeralNotes(address, nil, transactor, nil)
	if err != nil {
		return nil, err
	}
	return &EphemeralNotesTransactor{contract: contract}, nil
}

// NewEphemeralNotesFilterer creates a new log filterer instance of EphemeralNotes, bound to a specific deployed contract.
func NewEphemeralNotesFilterer(address common.Address, filterer bind.ContractFilterer) (*EphemeralNotesFilterer, error) {
	contract, err := bindEphemeralNotes(address, nil, nil, filterer)
	if err != nil {
		return nil, err
	}
	return &EphemeralNotesFilterer{contract: contract}, nil
}

// bindEphemeralNotes binds a generic wrapper to an already deployed contract.
func bindEphemeralNotes(address common.Address, caller bind.ContractCaller, transactor bind.ContractTransactor, filterer bind.ContractFilterer) (*bind.BoundContract, error) {
	parsed, err := EphemeralNotesMetaData.GetAbi()
	if err != nil {
		return nil, err
	}
	return bind.NewBoundContract(address, *parsed, caller, transactor, filterer), nil
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_EphemeralNotes *EphemeralNotesRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _EphemeralNotes.Contract.EphemeralNotesCaller.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_EphemeralNotes *EphemeralNotesRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.EphemeralNotesTransactor.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_EphemeralNotes *EphemeralNotesRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.EphemeralNotesTransactor.contract.Transact(opts, method, params...)
}

// Call invokes the (constant) contract method with params as input values and
// sets the output to result. The result type might be a single field for simple
// returns, a slice of interfaces for anonymous returns and a struct for named
// returns.
func (_EphemeralNotes *EphemeralNotesCallerRaw) Call(opts *bind.CallOpts, result *[]interface{}, method string, params ...interface{}) error {
	return _EphemeralNotes.Contract.contract.Call(opts, result, method, params...)
}

// Transfer initiates a plain transaction to move funds to the contract, calling
// its default method if one is available.
func (_EphemeralNotes *EphemeralNotesTransactorRaw) Transfer(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.contract.Transfer(opts)
}

// Transact invokes the (paid) contract method with params as input values.
func (_EphemeralNotes *EphemeralNotesTransactorRaw) Transact(opts *bind.TransactOpts, method string, params ...interface{}) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.contract.Transact(opts, method, params...)
}

// Notes is a free data retrieval call binding the contract method 0xbd93644a.
//
// Solidity: function notes(address ) view returns(address ephemeralOwner, address from, address token, uint256 amount)
func (_EphemeralNotes *EphemeralNotesCaller) Notes(opts *bind.CallOpts, arg0 common.Address) (struct {
	EphemeralOwner common.Address
	From           common.Address
	Token          common.Address
	Amount         *big.Int
}, error) {
	var out []interface{}
	err := _EphemeralNotes.contract.Call(opts, &out, "notes", arg0)

	outstruct := new(struct {
		EphemeralOwner common.Address
		From           common.Address
		Token          common.Address
		Amount         *big.Int
	})
	if err != nil {
		return *outstruct, err
	}

	outstruct.EphemeralOwner = *abi.ConvertType(out[0], new(common.Address)).(*common.Address)
	outstruct.From = *abi.ConvertType(out[1], new(common.Address)).(*common.Address)
	outstruct.Token = *abi.ConvertType(out[2], new(common.Address)).(*common.Address)
	outstruct.Amount = *abi.ConvertType(out[3], new(*big.Int)).(**big.Int)

	return *outstruct, err

}

// Notes is a free data retrieval call binding the contract method 0xbd93644a.
//
// Solidity: function notes(address ) view returns(address ephemeralOwner, address from, address token, uint256 amount)
func (_EphemeralNotes *EphemeralNotesSession) Notes(arg0 common.Address) (struct {
	EphemeralOwner common.Address
	From           common.Address
	Token          common.Address
	Amount         *big.Int
}, error) {
	return _EphemeralNotes.Contract.Notes(&_EphemeralNotes.CallOpts, arg0)
}

// Notes is a free data retrieval call binding the contract method 0xbd93644a.
//
// Solidity: function notes(address ) view returns(address ephemeralOwner, address from, address token, uint256 amount)
func (_EphemeralNotes *EphemeralNotesCallerSession) Notes(arg0 common.Address) (struct {
	EphemeralOwner common.Address
	From           common.Address
	Token          common.Address
	Amount         *big.Int
}, error) {
	return _EphemeralNotes.Contract.Notes(&_EphemeralNotes.CallOpts, arg0)
}

// ClaimNoteOther is a paid mutator transaction binding the contract method 0x692103e8.
//
// Solidity: function claimNoteOther(address _ephemeralOwner, address _recipient, bytes _signature, uint256 _fee) returns()
func (_EphemeralNotes *EphemeralNotesTransactor) ClaimNoteOther(opts *bind.TransactOpts, _ephemeralOwner common.Address, _recipient common.Address, _signature []byte, _fee *big.Int) (*types.Transaction, error) {
	return _EphemeralNotes.contract.Transact(opts, "claimNoteOther", _ephemeralOwner, _recipient, _signature, _fee)
}

// ClaimNoteOther is a paid mutator transaction binding the contract method 0x692103e8.
//
// Solidity: function claimNoteOther(address _ephemeralOwner, address _recipient, bytes _signature, uint256 _fee) returns()
func (_EphemeralNotes *EphemeralNotesSession) ClaimNoteOther(_ephemeralOwner common.Address, _recipient common.Address, _signature []byte, _fee *big.Int) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.ClaimNoteOther(&_EphemeralNotes.TransactOpts, _ephemeralOwner, _recipient, _signature, _fee)
}

// ClaimNoteOther is a paid mutator transaction binding the contract method 0x692103e8.
//
// Solidity: function claimNoteOther(address _ephemeralOwner, address _recipient, bytes _signature, uint256 _fee) returns()
func (_EphemeralNotes *EphemeralNotesTransactorSession) ClaimNoteOther(_ephemeralOwner common.Address, _recipient common.Address, _signature []byte, _fee *big.Int) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.ClaimNoteOther(&_EphemeralNotes.TransactOpts, _ephemeralOwner, _recipient, _signature, _fee)
}

// ClaimNoteRecipient is a paid mutator transaction binding the contract method 0x02a32532.
//
// Solidity: function claimNoteRecipient(address _ephemeralOwner, bytes _signature) returns()
func (_EphemeralNotes *EphemeralNotesTransactor) ClaimNoteRecipient(opts *bind.TransactOpts, _ephemeralOwner common.Address, _signature []byte) (*types.Transaction, error) {
	return _EphemeralNotes.contract.Transact(opts, "claimNoteRecipient", _ephemeralOwner, _signature)
}

// ClaimNoteRecipient is a paid mutator transaction binding the contract method 0x02a32532.
//
// Solidity: function claimNoteRecipient(address _ephemeralOwner, bytes _signature) returns()
func (_EphemeralNotes *EphemeralNotesSession) ClaimNoteRecipient(_ephemeralOwner common.Address, _signature []byte) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.ClaimNoteRecipient(&_EphemeralNotes.TransactOpts, _ephemeralOwner, _signature)
}

// ClaimNoteRecipient is a paid mutator transaction binding the contract method 0x02a32532.
//
// Solidity: function claimNoteRecipient(address _ephemeralOwner, bytes _signature) returns()
func (_EphemeralNotes *EphemeralNotesTransactorSession) ClaimNoteRecipient(_ephemeralOwner common.Address, _signature []byte) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.ClaimNoteRecipient(&_EphemeralNotes.TransactOpts, _ephemeralOwner, _signature)
}

// ClaimNoteSelf is a paid mutator transaction binding the contract method 0x500d5d7c.
//
// Solidity: function claimNoteSelf(address _ephemeralOwner) returns()
func (_EphemeralNotes *EphemeralNotesTransactor) ClaimNoteSelf(opts *bind.TransactOpts, _ephemeralOwner common.Address) (*types.Transaction, error) {
	return _EphemeralNotes.contract.Transact(opts, "claimNoteSelf", _ephemeralOwner)
}

// ClaimNoteSelf is a paid mutator transaction binding the contract method 0x500d5d7c.
//
// Solidity: function claimNoteSelf(address _ephemeralOwner) returns()
func (_EphemeralNotes *EphemeralNotesSession) ClaimNoteSelf(_ephemeralOwner common.Address) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.ClaimNoteSelf(&_EphemeralNotes.TransactOpts, _ephemeralOwner)
}

// ClaimNoteSelf is a paid mutator transaction binding the contract method 0x500d5d7c.
//
// Solidity: function claimNoteSelf(address _ephemeralOwner) returns()
func (_EphemeralNotes *EphemeralNotesTransactorSession) ClaimNoteSelf(_ephemeralOwner common.Address) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.ClaimNoteSelf(&_EphemeralNotes.TransactOpts, _ephemeralOwner)
}

// CreateNote is a paid mutator transaction binding the contract method 0x437b91bb.
//
// Solidity: function createNote(address _ephemeralOwner, address _token, uint256 _amount) payable returns()
func (_EphemeralNotes *EphemeralNotesTransactor) CreateNote(opts *bind.TransactOpts, _ephemeralOwner common.Address, _token common.Address, _amount *big.Int) (*types.Transaction, error) {
	return _EphemeralNotes.contract.Transact(opts, "createNote", _ephemeralOwner, _token, _amount)
}

// CreateNote is a paid mutator transaction binding the contract method 0x437b91bb.
//
// Solidity: function createNote(address _ephemeralOwner, address _token, uint256 _amount) payable returns()
func (_EphemeralNotes *EphemeralNotesSession) CreateNote(_ephemeralOwner common.Address, _token common.Address, _amount *big.Int) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.CreateNote(&_EphemeralNotes.TransactOpts, _ephemeralOwner, _token, _amount)
}

// CreateNote is a paid mutator transaction binding the contract method 0x437b91bb.
//
// Solidity: function createNote(address _ephemeralOwner, address _token, uint256 _amount) payable returns()
func (_EphemeralNotes *EphemeralNotesTransactorSession) CreateNote(_ephemeralOwner common.Address, _token common.Address, _amount *big.Int) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.CreateNote(&_EphemeralNotes.TransactOpts, _ephemeralOwner, _token, _amount)
}

// CreateNoteWithPermit is a paid mutator transaction binding the contract method 0x693a7bca.
//
// Solidity: function createNoteWithPermit(address _ephemeralOwner, address _token, uint256 _amount, uint256 deadline, uint8 v, bytes32 r, bytes32 s) returns()
func (_EphemeralNotes *EphemeralNotesTransactor) CreateNoteWithPermit(opts *bind.TransactOpts, _ephemeralOwner common.Address, _token common.Address, _amount *big.Int, deadline *big.Int, v uint8, r [32]byte, s [32]byte) (*types.Transaction, error) {
	return _EphemeralNotes.contract.Transact(opts, "createNoteWithPermit", _ephemeralOwner, _token, _amount, deadline, v, r, s)
}

// CreateNoteWithPermit is a paid mutator transaction binding the contract method 0x693a7bca.
//
// Solidity: function createNoteWithPermit(address _ephemeralOwner, address _token, uint256 _amount, uint256 deadline, uint8 v, bytes32 r, bytes32 s) returns()
func (_EphemeralNotes *EphemeralNotesSession) CreateNoteWithPermit(_ephemeralOwner common.Address, _token common.Address, _amount *big.Int, deadline *big.Int, v uint8, r [32]byte, s [32]byte) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.CreateNoteWithPermit(&_EphemeralNotes.TransactOpts, _ephemeralOwner, _token, _amount, deadline, v, r, s)
}

// CreateNoteWithPermit is a paid mutator transaction binding the contract method 0x693a7bca.
//
// Solidity: function createNoteWithPermit(address _ephemeralOwner, address _token, uint256 _amount, uint256 deadline, uint8 v, bytes32 r, bytes32 s) returns()
func (_EphemeralNotes *EphemeralNotesTransactorSession) CreateNoteWithPermit(_ephemeralOwner common.Address, _token common.Address, _amount *big.Int, deadline *big.Int, v uint8, r [32]byte, s [32]byte) (*types.Transaction, error) {
	return _EphemeralNotes.Contract.CreateNoteWithPermit(&_EphemeralNotes.TransactOpts, _ephemeralOwner, _token, _amount, deadline, v, r, s)
}

// Receive is a paid mutator transaction binding the contract receive function.
//
// Solidity: receive() payable returns()
func (_EphemeralNotes *EphemeralNotesTransactor) Receive(opts *bind.TransactOpts) (*types.Transaction, error) {
	return _EphemeralNotes.contract.RawTransact(opts, nil) // calldata is disallowed for receive function
}

// Receive is a paid mutator transaction binding the contract receive function.
//
// Solidity: receive() payable returns()
func (_EphemeralNotes *EphemeralNotesSession) Receive() (*types.Transaction, error) {
	return _EphemeralNotes.Contract.Receive(&_EphemeralNotes.TransactOpts)
}

// Receive is a paid mutator transaction binding the contract receive function.
//
// Solidity: receive() payable returns()
func (_EphemeralNotes *EphemeralNotesTransactorSession) Receive() (*types.Transaction, error) {
	return _EphemeralNotes.Contract.Receive(&_EphemeralNotes.TransactOpts)
}

// EphemeralNotesNoteCreatedIterator is returned from FilterNoteCreated and is used to iterate over the raw logs and unpacked data for NoteCreated events raised by the EphemeralNotes contract.
type EphemeralNotesNoteCreatedIterator struct {
	Event *EphemeralNotesNoteCreated // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *EphemeralNotesNoteCreatedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(EphemeralNotesNoteCreated)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(EphemeralNotesNoteCreated)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *EphemeralNotesNoteCreatedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *EphemeralNotesNoteCreatedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// EphemeralNotesNoteCreated represents a NoteCreated event raised by the EphemeralNotes contract.
type EphemeralNotesNoteCreated struct {
	EphemeralOwner common.Address
	From           common.Address
	Token          common.Address
	Amount         *big.Int
	Raw            types.Log // Blockchain specific contextual infos
}

// FilterNoteCreated is a free log retrieval operation binding the contract event 0x4737d8c4bcb7771dd7f844199dad3424f65bbc7a704c435bc8651f82c1fedccf.
//
// Solidity: event NoteCreated(address indexed ephemeralOwner, address indexed from, address indexed token, uint256 amount)
func (_EphemeralNotes *EphemeralNotesFilterer) FilterNoteCreated(opts *bind.FilterOpts, ephemeralOwner []common.Address, from []common.Address, token []common.Address) (*EphemeralNotesNoteCreatedIterator, error) {

	var ephemeralOwnerRule []interface{}
	for _, ephemeralOwnerItem := range ephemeralOwner {
		ephemeralOwnerRule = append(ephemeralOwnerRule, ephemeralOwnerItem)
	}
	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var tokenRule []interface{}
	for _, tokenItem := range token {
		tokenRule = append(tokenRule, tokenItem)
	}

	logs, sub, err := _EphemeralNotes.contract.FilterLogs(opts, "NoteCreated", ephemeralOwnerRule, fromRule, tokenRule)
	if err != nil {
		return nil, err
	}
	return &EphemeralNotesNoteCreatedIterator{contract: _EphemeralNotes.contract, event: "NoteCreated", logs: logs, sub: sub}, nil
}

// WatchNoteCreated is a free log subscription operation binding the contract event 0x4737d8c4bcb7771dd7f844199dad3424f65bbc7a704c435bc8651f82c1fedccf.
//
// Solidity: event NoteCreated(address indexed ephemeralOwner, address indexed from, address indexed token, uint256 amount)
func (_EphemeralNotes *EphemeralNotesFilterer) WatchNoteCreated(opts *bind.WatchOpts, sink chan<- *EphemeralNotesNoteCreated, ephemeralOwner []common.Address, from []common.Address, token []common.Address) (event.Subscription, error) {

	var ephemeralOwnerRule []interface{}
	for _, ephemeralOwnerItem := range ephemeralOwner {
		ephemeralOwnerRule = append(ephemeralOwnerRule, ephemeralOwnerItem)
	}
	var fromRule []interface{}
	for _, fromItem := range from {
		fromRule = append(fromRule, fromItem)
	}
	var tokenRule []interface{}
	for _, tokenItem := range token {
		tokenRule = append(tokenRule, tokenItem)
	}

	logs, sub, err := _EphemeralNotes.contract.WatchLogs(opts, "NoteCreated", ephemeralOwnerRule, fromRule, tokenRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(EphemeralNotesNoteCreated)
				if err := _EphemeralNotes.contract.UnpackLog(event, "NoteCreated", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseNoteCreated is a log parse operation binding the contract event 0x4737d8c4bcb7771dd7f844199dad3424f65bbc7a704c435bc8651f82c1fedccf.
//
// Solidity: event NoteCreated(address indexed ephemeralOwner, address indexed from, address indexed token, uint256 amount)
func (_EphemeralNotes *EphemeralNotesFilterer) ParseNoteCreated(log types.Log) (*EphemeralNotesNoteCreated, error) {
	event := new(EphemeralNotesNoteCreated)
	if err := _EphemeralNotes.contract.UnpackLog(event, "NoteCreated", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}

// EphemeralNotesNoteRedeemedIterator is returned from FilterNoteRedeemed and is used to iterate over the raw logs and unpacked data for NoteRedeemed events raised by the EphemeralNotes contract.
type EphemeralNotesNoteRedeemedIterator struct {
	Event *EphemeralNotesNoteRedeemed // Event containing the contract specifics and raw log

	contract *bind.BoundContract // Generic contract to use for unpacking event data
	event    string              // Event name to use for unpacking event data

	logs chan types.Log        // Log channel receiving the found contract events
	sub  ethereum.Subscription // Subscription for errors, completion and termination
	done bool                  // Whether the subscription completed delivering logs
	fail error                 // Occurred error to stop iteration
}

// Next advances the iterator to the subsequent event, returning whether there
// are any more events found. In case of a retrieval or parsing error, false is
// returned and Error() can be queried for the exact failure.
func (it *EphemeralNotesNoteRedeemedIterator) Next() bool {
	// If the iterator failed, stop iterating
	if it.fail != nil {
		return false
	}
	// If the iterator completed, deliver directly whatever's available
	if it.done {
		select {
		case log := <-it.logs:
			it.Event = new(EphemeralNotesNoteRedeemed)
			if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
				it.fail = err
				return false
			}
			it.Event.Raw = log
			return true

		default:
			return false
		}
	}
	// Iterator still in progress, wait for either a data or an error event
	select {
	case log := <-it.logs:
		it.Event = new(EphemeralNotesNoteRedeemed)
		if err := it.contract.UnpackLog(it.Event, it.event, log); err != nil {
			it.fail = err
			return false
		}
		it.Event.Raw = log
		return true

	case err := <-it.sub.Err():
		it.done = true
		it.fail = err
		return it.Next()
	}
}

// Error returns any retrieval or parsing error occurred during filtering.
func (it *EphemeralNotesNoteRedeemedIterator) Error() error {
	return it.fail
}

// Close terminates the iteration process, releasing any pending underlying
// resources.
func (it *EphemeralNotesNoteRedeemedIterator) Close() error {
	it.sub.Unsubscribe()
	return nil
}

// EphemeralNotesNoteRedeemed represents a NoteRedeemed event raised by the EphemeralNotes contract.
type EphemeralNotesNoteRedeemed struct {
	EphemeralOwner common.Address
	Redeemer       common.Address
	Amount         *big.Int
	Fee            *big.Int
	Raw            types.Log // Blockchain specific contextual infos
}

// FilterNoteRedeemed is a free log retrieval operation binding the contract event 0x146aedbe80faa2cde4e5d6c01e9491e67d9e24b01bbf497fc4d0e06c33dccece.
//
// Solidity: event NoteRedeemed(address indexed ephemeralOwner, address indexed redeemer, uint256 amount, uint256 fee)
func (_EphemeralNotes *EphemeralNotesFilterer) FilterNoteRedeemed(opts *bind.FilterOpts, ephemeralOwner []common.Address, redeemer []common.Address) (*EphemeralNotesNoteRedeemedIterator, error) {

	var ephemeralOwnerRule []interface{}
	for _, ephemeralOwnerItem := range ephemeralOwner {
		ephemeralOwnerRule = append(ephemeralOwnerRule, ephemeralOwnerItem)
	}
	var redeemerRule []interface{}
	for _, redeemerItem := range redeemer {
		redeemerRule = append(redeemerRule, redeemerItem)
	}

	logs, sub, err := _EphemeralNotes.contract.FilterLogs(opts, "NoteRedeemed", ephemeralOwnerRule, redeemerRule)
	if err != nil {
		return nil, err
	}
	return &EphemeralNotesNoteRedeemedIterator{contract: _EphemeralNotes.contract, event: "NoteRedeemed", logs: logs, sub: sub}, nil
}

// WatchNoteRedeemed is a free log subscription operation binding the contract event 0x146aedbe80faa2cde4e5d6c01e9491e67d9e24b01bbf497fc4d0e06c33dccece.
//
// Solidity: event NoteRedeemed(address indexed ephemeralOwner, address indexed redeemer, uint256 amount, uint256 fee)
func (_EphemeralNotes *EphemeralNotesFilterer) WatchNoteRedeemed(opts *bind.WatchOpts, sink chan<- *EphemeralNotesNoteRedeemed, ephemeralOwner []common.Address, redeemer []common.Address) (event.Subscription, error) {

	var ephemeralOwnerRule []interface{}
	for _, ephemeralOwnerItem := range ephemeralOwner {
		ephemeralOwnerRule = append(ephemeralOwnerRule, ephemeralOwnerItem)
	}
	var redeemerRule []interface{}
	for _, redeemerItem := range redeemer {
		redeemerRule = append(redeemerRule, redeemerItem)
	}

	logs, sub, err := _EphemeralNotes.contract.WatchLogs(opts, "NoteRedeemed", ephemeralOwnerRule, redeemerRule)
	if err != nil {
		return nil, err
	}
	return event.NewSubscription(func(quit <-chan struct{}) error {
		defer sub.Unsubscribe()
		for {
			select {
			case log := <-logs:
				// New log arrived, parse the event and forward to the user
				event := new(EphemeralNotesNoteRedeemed)
				if err := _EphemeralNotes.contract.UnpackLog(event, "NoteRedeemed", log); err != nil {
					return err
				}
				event.Raw = log

				select {
				case sink <- event:
				case err := <-sub.Err():
					return err
				case <-quit:
					return nil
				}
			case err := <-sub.Err():
				return err
			case <-quit:
				return nil
			}
		}
	}), nil
}

// ParseNoteRedeemed is a log parse operation binding the contract event 0x146aedbe80faa2cde4e5d6c01e9491e67d9e24b01bbf497fc4d0e06c33dccece.
//
// Solidity: event NoteRedeemed(address indexed ephemeralOwner, address indexed redeemer, uint256 amount, uint256 fee)
func (_EphemeralNotes *EphemeralNotesFilterer) ParseNoteRedeemed(log types.Log) (*EphemeralNotesNoteRedeemed, error) {
	event := new(EphemeralNotesNoteRedeemed)
	if err := _EphemeralNotes.contract.UnpackLog(event, "NoteRedeemed", log); err != nil {
		return nil, err
	}
	event.Raw = log
	return event, nil
}
