package evm

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestFetchAmounts(t *testing.T) {
	testCases := []struct {
		name  string
		chain domain.Chain
		usdc  domain.Token
		usdt  domain.Token
	}{
		{
			name:  "<PERSON><PERSON>",
			chain: domain.Sepolia,
			usdc:  domain.NewToken(domain.Sepolia, "******************************************", "USD Coin", "USDC", "", 18, true),
			usdt:  domain.NewToken(domain.Sepolia, "******************************************", "USD Tether", "USDT", "", 18, true),
		},
		{
			name:  "Ethereum",
			chain: domain.Ethereum,
			usdc:  domain.NewToken(domain.Ethereum, "******************************************", "USD Coin", "USDC", "", 6, true),
			usdt:  domain.NewToken(domain.Ethereum, "******************************************", "Tether USD", "USDT", "", 6, true),
		},
		{
			name:  "BNBChain",
			chain: domain.BNBChain,
			usdc:  domain.NewToken(domain.BNBChain, "******************************************", "USD Coin", "USDC", "", 18, true),
			usdt:  domain.NewToken(domain.BNBChain, "******************************************", "Tether USD", "USDT", "", 18, true),
		},
		{
			name:  "Arbitrum",
			chain: domain.Arbitrum,
			usdc:  domain.NewToken(domain.Arbitrum, "******************************************", "USD Coin", "USDC", "", 6, true),
			usdt:  domain.NewToken(domain.Arbitrum, "******************************************", "Tether USD", "USDT", "", 6, true),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Define test context
			ctx := context.Background()

			// Define test tokens
			tokens := []*domain.WalletToken{
				{
					Wallet: domain.NewEvmAddress("******************************************"),
					Token:  tc.usdc,
				},
				{
					Wallet: domain.NewEvmAddress("******************************************"),
					Token:  tc.usdt,
				},
				{
					Wallet: domain.NewEvmAddress("******************************************"),
					Token:  tc.usdc,
				},
				{
					Wallet: domain.NewEvmAddress("******************************************"),
					Token:  tc.usdt,
				},
			}

			// Call FetchAmounts
			amounts, err := Get().FetchAmounts(ctx, tc.chain, tokens)

			// Assertions
			assert.NoError(t, err)
			assert.NotNil(t, amounts)
			assert.Contains(t, amounts, domain.NewEvmAddress("******************************************"))
			assert.Contains(t, amounts, domain.NewEvmAddress("******************************************"))
			amount := amounts[domain.NewEvmAddress("******************************************")][tc.usdc.ID()]
			if tc.chain == domain.Sepolia {
				assert.True(t, amount.GreaterThan(decimal.NewFromInt(0)))
				amount = amounts[domain.NewEvmAddress("******************************************")][tc.usdc.ID()]
				assert.True(t, amount.Equal(decimal.NewFromInt(0)))
				amount = amounts[domain.NewEvmAddress("******************************************")][tc.usdt.ID()]
				assert.True(t, amount.Equal(decimal.NewFromInt(0)))
			}
		})
	}

	t.Run("Fetch Failed Case", func(t *testing.T) {
		ctx := context.Background()
		nonExistentToken := domain.NewToken(domain.Ethereum, "******************************************", "Non-Existent Token", "NET", "", 18, true)
		tokens := []*domain.WalletToken{
			{
				Wallet: domain.NewEvmAddress("******************************************"),
				Token:  nonExistentToken,
			},
		}

		amounts, err := Get().FetchAmounts(ctx, domain.Ethereum, tokens)

		assert.Error(t, err)
		assert.Nil(t, amounts)
		assert.Contains(t, err.Error(), "failed to get batch balance of")
	})

	t.Run("Fetch Main Token", func(t *testing.T) {
		ctx := context.Background()
		eth := domain.Ethereum.MainToken()
		tokens := []*domain.WalletToken{
			{
				Wallet: domain.NewEvmAddress("******************************************"),
				Token:  eth,
			},
		}

		amounts, err := Get().FetchAmounts(ctx, domain.Ethereum, tokens)

		assert.NoError(t, err)
		assert.NotNil(t, amounts)
		assert.Contains(t, amounts, domain.NewEvmAddress("******************************************"))
		amount := amounts[domain.NewEvmAddress("******************************************")][eth.ID()]
		assert.True(t, amount.GreaterThan(decimal.NewFromInt(0)))
	})
}
