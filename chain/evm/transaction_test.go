package evm

import (
	"context"
	"math/big"
	"strings"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetTransactionStatus(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Ethereum)

	testCases := []struct {
		name     string
		txHash   string
		expected domain.TransactionStatus
	}{
		{
			name:     "Successful Transaction",
			txHash:   "0x5a63de8bc1a7979e52ab184be4bf26b86a83421c280993237eda5ca59ec80b6e",
			expected: domain.TransactionStatusSuccess,
		},
		{
			name:     "Failed Transaction",
			txHash:   "0xf9c8514fad47eb54a414930563aabfeceb465c9f308f5f294a37edd0d669243c",
			expected: domain.TransactionStatusFailed,
		},
		{
			name:     "Not exist Transaction",
			txHash:   "0xf9c8514fad47eb54a414930563aabfeceb465c9f308f5f294a37edd0d669243a",
			expected: domain.TransactionStatusUnknown,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			status, err := client.GetTransactionStatus(context.Background(), tc.txHash)
			if tc.expected == domain.TransactionStatusUnknown {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expected, status)
			}
		})
	}
}

func TestWaitUntilTransactionConfirmed(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Ethereum)

	testCases := []struct {
		name        string
		txHash      string
		expectError error
		expected    domain.TransactionStatus
	}{
		{
			name:     "Successful Transaction",
			txHash:   "0x5a63de8bc1a7979e52ab184be4bf26b86a83421c280993237eda5ca59ec80b6e",
			expected: domain.TransactionStatusSuccess,
		},
		{
			name:     "Failed Transaction",
			txHash:   "0xf9c8514fad47eb54a414930563aabfeceb465c9f308f5f294a37edd0d669243c",
			expected: domain.TransactionStatusFailed,
		},
		{
			name:        "Not exist Transaction",
			txHash:      "0xf9c8514fad47eb54a414930563aabfeceb465c9f308f5f294a37edd0d669243a",
			expectError: context.DeadlineExceeded,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()

			status, err := client.WaitUntilTransactionConfirmed(ctx, tc.txHash)
			if tc.expectError != nil {
				assert.Error(t, err)
				assert.Equal(t, tc.expectError, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expected, status)
			}
		})
	}
}

func TestUsdcTransferTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Ethereum)

	txHash := "0x63279a446e55ce9399c3792a93de997c44cb244555ea693a7c566f136af6dfc2"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Data: %s", detail.Data)
	t.Logf("Method ID: %s", detail.MethodID)
	t.Logf("Value: %s", detail.Value.String())
	t.Logf("Gas Price: %s", detail.GasPrice.String())
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(20991636), detail.BlockNum)
	assert.Equal(t, big.NewInt(27996069993), detail.GasPrice)
	assert.Equal(t, big.NewInt(45160), detail.GasUsed)
	assert.Equal(t, "0xa9059cbb000000000000000000000000580c076a0561ab494231c2fd37f9c396845e30a10000000000000000000000000000000000000000000000000000000012836140", detail.Data)
	assert.Equal(t, "0xa9059cbb", detail.MethodID)
	assert.Equal(t, time.Unix(1729244723, 0), detail.Timestamp)

	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.To)
	assert.Equal(t, big.NewInt(0), detail.Value)

	assert.Equal(t, 0, len(detail.InternalTransfers))

	assert.Equal(t, 1, len(detail.TokenTransfers))
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].Contract)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].To)
	assert.Equal(t, big.NewInt(310600000), detail.TokenTransfers[0].Amount)
}

func TestEthTransferTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Ethereum)

	txHash := "0x7672ce999aa7d8da1fc31f077da6c325d9f2598ff66cad63f1fbab7132722260"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Data: %s", detail.Data)
	t.Logf("Method ID: %s", detail.MethodID)
	t.Logf("Value: %s", detail.Value.String())
	t.Logf("Gas Price: %s", detail.GasPrice.String())
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(20991636), detail.BlockNum)
	assert.Equal(t, big.NewInt(29657492305), detail.GasPrice)
	assert.Equal(t, big.NewInt(21000), detail.GasUsed)
	assert.Equal(t, "0x", detail.Data)
	assert.Equal(t, "", detail.MethodID)
	assert.Equal(t, time.Unix(1729244723, 0), detail.Timestamp)

	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.To)
	assert.Equal(t, big.NewInt(54500000000000000), detail.Value)

	assert.Equal(t, 0, len(detail.TokenTransfers))
	assert.Equal(t, 0, len(detail.InternalTransfers))
}

func TestComplicatedTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Ethereum)

	txHash := "0x72ed70380bfced8d0bebd1a80e46b166dcd4c33634b4b31fc4e838375d89a1f0"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Data: %s", detail.Data)
	t.Logf("Method ID: %s", detail.MethodID)
	t.Logf("Value: %s", detail.Value.String())
	t.Logf("Gas Price: %s", detail.GasPrice.String())
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(20991636), detail.BlockNum)
	assert.Equal(t, big.NewInt(28469534175), detail.GasPrice)
	assert.Equal(t, big.NewInt(190236), detail.GasUsed)
	assert.Equal(t, "0x3593564c", detail.MethodID)
	assert.Equal(t, time.Unix(1729244723, 0), detail.Timestamp)

	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.To)
	assert.Equal(t, big.NewInt(100000000000000000), detail.Value)

	for _, tokenTransfer := range detail.TokenTransfers {
		t.Logf("Token Transfer: %v", tokenTransfer)
	}
	assert.Equal(t, 4, len(detail.TokenTransfers))
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].Contract)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].To)
	amount := new(big.Int)
	amount, _ = amount.SetString("6841605288777517353305", 10)
	assert.Equal(t, amount, detail.TokenTransfers[0].Amount)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[3].Contract)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[3].From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[3].To)
	amount, _ = amount.SetString("100000000000000000", 10)
	assert.Equal(t, amount, detail.TokenTransfers[3].Amount)

	for _, internalTransfers := range detail.InternalTransfers {
		t.Logf("internalTransfer : %v", internalTransfers)
	}
	assert.Equal(t, 1, len(detail.InternalTransfers))
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.InternalTransfers[0].From)
	assert.Equal(t, domain.NewEvmAddress("0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2"), detail.InternalTransfers[0].To)
	assert.Equal(t, big.NewInt(100000000000000000), detail.InternalTransfers[0].Amount)
}

func TestArbitrumTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Arbitrum)

	txHash := "0x37556fcd45ba2d13992d881929bee9d106599673cfab5a102d0e93eb610ea6bd"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Data: %s", detail.Data)
	t.Logf("Method ID: %s", detail.MethodID)
	t.Logf("Value: %s", detail.Value.String())
	t.Logf("Gas Price: %s", detail.GasPrice.String())
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(264674613), detail.BlockNum)
	assert.Equal(t, int64(1729148131), detail.Timestamp.Unix())
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.To)
	assert.Equal(t, big.NewInt(0), detail.Value)

	assert.Equal(t, 9, len(detail.TokenTransfers))

	// Check WETH transfer
	wethTransfer := detail.TokenTransfers[0]
	assert.Equal(t, domain.NewEvmAddress("******************************************"), wethTransfer.Contract)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), wethTransfer.From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), wethTransfer.To)
	wethAmount, _ := new(big.Int).SetString("11291443802970328", 10)
	assert.Equal(t, wethAmount, wethTransfer.Amount)

	// Check DAI transfer
	daiTransfer := detail.TokenTransfers[5]
	assert.Equal(t, domain.NewEvmAddress("******************************************"), daiTransfer.Contract)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), daiTransfer.From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), daiTransfer.To)
	daiAmount, _ := new(big.Int).SetString("29700000000000000000", 10)
	assert.Equal(t, daiAmount, daiTransfer.Amount)

	assert.Equal(t, 3, len(detail.InternalTransfers))
	internalTransfer := detail.InternalTransfers[0]
	assert.Equal(t, domain.NewEvmAddress("******************************************"), internalTransfer.From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), internalTransfer.To)
	internalAmount, _ := new(big.Int).SetString("11291443802970328", 10)
	assert.Equal(t, internalAmount, internalTransfer.Amount)
}

func TestOpUsdcTransferTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Optimism)

	txHash := "0x1ccb9e8793d8df5efcc1fea15184807197db0d730b21f99d74a30b07d9336a5e"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Data: %s", detail.Data)
	t.Logf("Method ID: %s", detail.MethodID)
	t.Logf("Value: %s", detail.Value.String())
	t.Logf("Gas Price: %s", detail.GasPrice.String())
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(130070996), detail.BlockNum)
	assert.Equal(t, big.NewInt(1000397), detail.GasPrice)
	assert.Equal(t, big.NewInt(62159), detail.GasUsed)
	assert.Equal(t, "0xa9059cbb00000000000000000000000031d1c7751eaa6374d4138597e8c7b5a1605cc43c00000000000000000000000000000000000000000000000000000000000186a0", detail.Data)
	assert.Equal(t, "0xa9059cbb", detail.MethodID)
	assert.Equal(t, time.Unix(1735740769, 0), detail.Timestamp)

	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.To)
	assert.Equal(t, big.NewInt(0), detail.Value)

	assert.Equal(t, 0, len(detail.InternalTransfers))

	assert.Equal(t, 1, len(detail.TokenTransfers))
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].Contract)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].To)
	assert.Equal(t, big.NewInt(100000), detail.TokenTransfers[0].Amount)
}

func TestFetchTxsInRange(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Ethereum)

	fromBlock := uint64(20830045)
	toBlock := uint64(20830050)

	usdtTransactionWithAddress := domain.TransactionWithAddresses{
		Chain: domain.Ethereum,
		Hash:  "0x99dd2847e3298f1742cab784daf17146c0b21bf30bb30f81c4700ec0ec229788",
		Addresses: []domain.Address{
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("******************************************"),
		},
	}
	ethTransactionWithAddress := domain.TransactionWithAddresses{
		Chain: domain.Ethereum,
		Hash:  "0x66af5ae1c051fe0ef48546c52b9a26b18888a43eba8b7444095f4ad0f6758611",
		Addresses: []domain.Address{
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("******************************************"),
		},
	}
	// TODO: add nft transaction with address
	// nftTransactionWithAddress := domain.TransactionWithAddresses{
	// 	Chain: domain.Ethereum,
	// 	Hash: "0x9a37070c8f8e9f17e6c69b5c0f471ae6c5478c98a240eb8100d72efc1caf6ff0",
	// 	Addresses: []domain.Address{
	// 	},
	// }
	smartContractCallTransactionWithAddress := domain.TransactionWithAddresses{
		Chain: domain.Ethereum,
		Hash:  "0x1134ed4ca7239da0e84e5782efe81671ab9c31976e7b647cf85af5b04558cd7e",
		Addresses: []domain.Address{
			domain.NewEvmAddress("******************************************"),
		},
	}
	ethInternalTransactionWithAddress := domain.TransactionWithAddresses{
		Chain: domain.Ethereum,
		Hash:  "0x38cc92b110434c4194d04467fada11e60cb496000f4893779e50d3db7fb3752d",
		Addresses: []domain.Address{
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("******************************************"),
		},
	}
	deployContractTransactionWithAddress := domain.TransactionWithAddresses{
		Chain: domain.Ethereum,
		Hash:  "0xc377212b8ec758c6f8999657b46b47ce84fe2e1169c01984471b2174afc460e2",
		Addresses: []domain.Address{
			domain.NewEvmAddress("******************************************"),
		},
	}

	lastBlockNo, transactions, err := client.FetchTxsInRange(context.Background(), domain.Ethereum, fromBlock, toBlock)

	assert.NoError(t, err)
	assert.NotNil(t, transactions)
	assert.NotEmpty(t, transactions, "Expected to find transactions in the specified block range")
	assert.Equal(t, toBlock, lastBlockNo)

	assert.Equal(t, 894, len(transactions))

	usdtTxFound := false
	ethTxFound := false
	smartContractCallTxFound := false
	deployContractTxFound := false
	ethInternalTxFound := false
	for _, tx := range transactions {
		if tx.Hash == usdtTransactionWithAddress.Hash {
			usdtTxFound = true
			assert.Equal(t, usdtTransactionWithAddress.Addresses, tx.Addresses)
			assert.Equal(t, usdtTransactionWithAddress.Chain, tx.Chain)
		}
		if tx.Hash == ethTransactionWithAddress.Hash {
			ethTxFound = true
			assert.Equal(t, ethTransactionWithAddress.Addresses, tx.Addresses)
			assert.Equal(t, ethTransactionWithAddress.Chain, tx.Chain)
		}
		if tx.Hash == smartContractCallTransactionWithAddress.Hash {
			smartContractCallTxFound = true
			assert.Equal(t, smartContractCallTransactionWithAddress.Addresses, tx.Addresses)
			assert.Equal(t, smartContractCallTransactionWithAddress.Chain, tx.Chain)
		}
		if tx.Hash == deployContractTransactionWithAddress.Hash {
			deployContractTxFound = true
			assert.Equal(t, deployContractTransactionWithAddress.Addresses, tx.Addresses)
			assert.Equal(t, deployContractTransactionWithAddress.Chain, tx.Chain)
		}
		if tx.Hash == ethInternalTransactionWithAddress.Hash {
			ethInternalTxFound = true
			assert.Equal(t, ethInternalTransactionWithAddress.Addresses, tx.Addresses)
			assert.Equal(t, ethInternalTransactionWithAddress.Chain, tx.Chain)
		}
	}
	assert.True(t, usdtTxFound)
	assert.True(t, ethTxFound)
	assert.True(t, smartContractCallTxFound)
	assert.True(t, deployContractTxFound)
	assert.True(t, ethInternalTxFound)
}

func TestFetchTxsInRangeBNB(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.BNBChain)

	fromBlock := uint64(43345132)
	toBlock := uint64(43345133)

	lastBlockNo, transactions, err := client.FetchTxsInRange(context.Background(), domain.BNBChain, fromBlock, toBlock)

	assert.NoError(t, err)
	assert.NotNil(t, transactions)
	assert.Equal(t, 286, len(transactions))
	assert.Equal(t, toBlock, lastBlockNo)
}

func TestFetchTxsInRangeArbitrum(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Arbitrum)

	fromBlock := uint64(264674613)
	toBlock := uint64(264674613)

	lastBlockNo, transactions, err := client.FetchTxsInRange(context.Background(), domain.Arbitrum, fromBlock, toBlock)

	assert.NoError(t, err)
	assert.NotNil(t, transactions)
	assert.Equal(t, 5, len(transactions))
	assert.Equal(t, toBlock, lastBlockNo)

	swapTransactionWithAddress := domain.TransactionWithAddresses{
		Chain: domain.Arbitrum,
		Hash:  "0x37556fcd45ba2d13992d881929bee9d106599673cfab5a102d0e93eb610ea6bd",
		Addresses: []domain.Address{
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("0x33e98fDcF6Fb3a5142374cAc5Bde06Ab1bcdb7DF"),
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("0x58095979B412a366687cA05CbE85fF56241bE21f"),
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("******************************************"),
			domain.NewEvmAddress("******************************************"),
		},
	}
	swapTxFound := false
	for _, tx := range transactions {
		if tx.Hash == swapTransactionWithAddress.Hash {
			swapTxFound = true
			assert.ElementsMatch(t, swapTransactionWithAddress.Addresses, tx.Addresses)
			assert.Equal(t, swapTransactionWithAddress.Chain, tx.Chain)
		}
	}
	assert.True(t, swapTxFound)
}

func TestGetExternalTransfers(t *testing.T) {
	t.Parallel()
	c, err := ethclient.Dial(RpcURL(domain.Arbitrum))
	if err != nil {
		panic(err)
	}
	client := &clientImpl{chain: domain.Arbitrum, client: c}

	blockNumber := uint64(266536113)
	nativeTransfers, err := client.getExternalTransfers(context.Background(), blockNumber)
	assert.NoError(t, err)
	assert.NotNil(t, nativeTransfers)
	t.Logf("nativeTransfers length: %d", len(nativeTransfers))
}

func TestPolygonTradeTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Polygon)

	txHash := "0x1b7c4d4f6cb2ed59675faab2bf89db1daf7a701e172c817dc7328f8d15068a16"
	detail, err := client.TransactionDetail(context.Background(), txHash)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Data: %s", detail.Data)
	t.Logf("Method ID: %s", detail.MethodID)
	t.Logf("Value: %s", detail.Value.String())
	t.Logf("Gas Price: %s", detail.GasPrice.String())
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(64396659), detail.BlockNum)
	assert.Equal(t, time.Unix(1731852297, 0), detail.Timestamp)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.To)
	assert.Equal(t, big.NewInt(0), detail.Value)

	for _, tokenTransfer := range detail.TokenTransfers {
		t.Logf("Token Transfer: %v", tokenTransfer)
	}
	assert.Equal(t, 2, len(detail.TokenTransfers))
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].Contract)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[0].To)
	amount, _ := new(big.Int).SetString("10701690000000000000000", 10)
	assert.Equal(t, amount, detail.TokenTransfers[0].Amount)

	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[1].Contract)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[1].From)
	assert.Equal(t, domain.NewEvmAddress("******************************************"), detail.TokenTransfers[1].To)
	assert.Equal(t, amount, detail.TokenTransfers[1].Amount)

	assert.Equal(t, 0, len(detail.InternalTransfers))
}

func TestBroadcastTransactions(t *testing.T) {
	t.Run("ETH Transfer", func(t *testing.T) {
		t.Parallel()
		client := GetClient(domain.Hoodi)
		fromAddr := domain.NewEvmAddress("******************************************")
		receiveAddr := common.HexToAddress("******************************************")
		nonce, err := client.GetNonce(context.Background(), fromAddr)
		t.Logf("nonce: %d", nonce)

		assert.NoError(t, err)
		tx := types.NewTx(&types.LegacyTx{
			Nonce: nonce,
			To:    &receiveAddr,
			Value: big.NewInt(1000000),
			Gas:   21000,
			// Use 1 Gwei as gas price
			GasPrice: big.NewInt(1000000000),
			Data:     []byte{},
		})

		// private key for ******************************************
		privateKey, err := crypto.HexToECDSA("afb24289e9e06060d6a291698c783404f3d92dafcc69de63297bd3a6ad35832b")
		assert.NoError(t, err)
		chainID := big.NewInt(domain.Hoodi.Number()) // Use actual Hoodi chain ID
		signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
		assert.NoError(t, err)

		hash, err := client.BroadcastTransaction(context.Background(), signedTx)
		assert.NoError(t, err)
		assert.NotEmpty(t, hash)
		t.Logf("Broadcasted transaction hash: %s", hash)
	})

	t.Run("ERC20 Token Transfer", func(t *testing.T) {
		t.Parallel()
		client := GetClient(domain.Sepolia)

		// Define transaction parameters
		fromAddr := domain.NewEvmAddress("******************************************")
		nonce, err := client.GetNonce(context.Background(), fromAddr)
		assert.NoError(t, err)
		usdcContractAddress := common.HexToAddress("******************************************")
		toAddress := common.HexToAddress("******************************************")
		amount := big.NewInt(100000)

		// Encode the transfer function call
		usdtABI, err := abi.JSON(strings.NewReader(`[{"constant":false,"inputs":[{"name":"_to","type":"address"},{"name":"_value","type":"uint256"}],"name":"transfer","outputs":[{"name":"","type":"bool"}],"type":"function"}]`))
		assert.NoError(t, err)

		data, err := usdtABI.Pack("transfer", toAddress, amount)
		assert.NoError(t, err)

		// Create a new transaction
		tx := types.NewTx(&types.LegacyTx{
			Nonce:    nonce,
			To:       &usdcContractAddress,
			Value:    big.NewInt(0),
			Gas:      87753,
			GasPrice: big.NewInt(55000000000),
			Data:     data,
		})

		// Sign the transaction (replace with actual private key)
		// private key for ******************************************
		privateKey, err := crypto.HexToECDSA("afb24289e9e06060d6a291698c783404f3d92dafcc69de63297bd3a6ad35832b")
		assert.NoError(t, err)
		chainNum := big.NewInt(domain.Sepolia.Number())
		signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainNum), privateKey)
		assert.NoError(t, err)

		// Broadcast the transaction
		hash, err := client.BroadcastTransaction(context.Background(), signedTx)
		assert.NoError(t, err)
		assert.NotEmpty(t, hash)
		t.Logf("Broadcasted transaction hash: %s", hash)
	})
}

func TestGetFirstTokenTransferTx(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	alchemyapi.InitDefault()

	// Use Arbitrum for the test since the USDT address is on Arbitrum
	client := GetClient(domain.Arbitrum)

	// Define test data
	receivingAddress := domain.NewEvmAddress("******************************************")
	expectedTxHash := "0x1930e47441d875ffd5954b159b0208d49f81a4f570aa85fcddf7405574586a70"

	// USDT token address on Arbitrum
	usdtTokenAddress := "0xFd086bC7CD5C481DCC9C85ebE478A1C0b69FCbb9"

	t.Run("Get First USDT Transfer", func(t *testing.T) {
		txHash, err := client.GetFirstTokenTransferTx(context.Background(), receivingAddress, usdtTokenAddress)

		// Log results for debugging
		t.Logf("Querying first token transfer for address: %s, token: %s", receivingAddress.String(), usdtTokenAddress)
		if err != nil {
			t.Logf("Error: %v", err)
		} else {
			t.Logf("Found transaction hash: %s", txHash)
		}

		// Assert results
		assert.NoError(t, err)
		assert.Equal(t, expectedTxHash, txHash, "Transaction hash does not match expected value")
	})

	t.Run("Invalid Token Address", func(t *testing.T) {
		invalidTokenAddress := "******************************************"

		_, err := client.GetFirstTokenTransferTx(context.Background(), receivingAddress, invalidTokenAddress)

		// Should return an error for invalid token
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no token transfers found to address", "Error message should include 'no token transfers found to address'")
	})

	t.Run("Valid Token But No Tx To Address", func(t *testing.T) {
		// Address with no USDT transfers
		noTxAddress := domain.NewEvmAddress("0x8896Fc7ba9855780589E812b2D3481C9AFb5c6B2")

		// Use USDT token address
		_, err := client.GetFirstTokenTransferTx(context.Background(), noTxAddress, usdtTokenAddress)

		// Log results for debugging
		t.Logf("Querying first token transfer for address with no transactions: %s, token: %s", noTxAddress.String(), usdtTokenAddress)
		if err != nil {
			t.Logf("Error: %v", err)
		}

		// Should return an error indicating no transfers to this address
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no token transfers found to address", "Error message should indicate no transfers found to this address")
	})
}
