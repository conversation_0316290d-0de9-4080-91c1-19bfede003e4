package evm

import (
	"context"
	"encoding/hex"
	"fmt"
	"math"
	"math/big"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/jpillora/backoff"
	abibinding "github.com/kryptogo/kg-wallet-backend/chain/evm/abi-binding"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/samber/lo"
)

// GetTransactionStatus retrieves the status of a transaction.
func (c *clientImpl) GetTransactionStatus(ctx context.Context, txHash string) (domain.TransactionStatus, error) {
	receipt, err := c.client.TransactionReceipt(ctx, common.HexToHash(txHash))
	if err != nil {
		return domain.TransactionStatusUnknown, err
	}
	switch receipt.Status {
	case types.ReceiptStatusSuccessful:
		return domain.TransactionStatusSuccess, nil
	case types.ReceiptStatusFailed:
		return domain.TransactionStatusFailed, nil
	default:
		return domain.TransactionStatusUnknown, fmt.Errorf("unknown transaction status")
	}
}

// WaitUntilTransactionConfirmed waits until a transaction is confirmed with exponential backoff.
func (c *clientImpl) WaitUntilTransactionConfirmed(ctx context.Context, txHash string) (domain.TransactionStatus, error) {
	backoffConfig := c.initializeBackoff()
	for {
		select {
		case <-ctx.Done():
			return domain.TransactionStatusUnknown, ctx.Err()
		default:
			status, err := c.GetTransactionStatus(ctx, txHash)
			if err != nil {
				if ctx.Err() != nil {
					return domain.TransactionStatusUnknown, ctx.Err()
				}
				// Non-context error, continue looping
			} else if status != domain.TransactionStatusUnknown {
				return status, nil
			}

			timer := time.NewTimer(backoffConfig.Duration())
			select {
			case <-ctx.Done():
				timer.Stop()
				return domain.TransactionStatusUnknown, ctx.Err()
			case <-timer.C:
				// Continue to next iteration
			}
		}
	}
}

// initializeBackoff configures backoff parameters based on chain's block time.
func (c *clientImpl) initializeBackoff() *backoff.Backoff {
	minWait := c.chain.BlockTime() / 2
	maxWait := c.chain.BlockTime() * 2
	if maxWait > 10*time.Second {
		maxWait = 10 * time.Second
	}
	return &backoff.Backoff{
		Min:    minWait,
		Max:    maxWait,
		Factor: 2,
		Jitter: true,
	}
}

// TransactionDetail fetches transaction details for a given transaction hash.
// TODO: consider NFT tx
func (c *clientImpl) TransactionDetail(ctx context.Context, txHash string) (*domain.TransactionDetail, error) {
	hash := common.HexToHash(txHash)
	tx, isPending, err := c.client.TransactionByHash(ctx, hash)
	if err != nil {
		return nil, err
	}
	if isPending {
		return nil, fmt.Errorf("transaction is pending")
	}

	receipt, err := c.client.TransactionReceipt(ctx, hash)
	if err != nil {
		return nil, err
	}

	internalTransfers, err := c.getInternalTransfers(ctx, txHash)
	if err != nil {
		return nil, err
	}

	tokenTransfers, err := c.getTokenTransfers(receipt)
	if err != nil {
		return nil, err
	}

	blockTime, err := c.blockTimeByNumber(ctx, receipt.BlockNumber)
	if err != nil {
		return nil, err
	}

	return c.parseTransactionDetail(tx, receipt, blockTime, internalTransfers, tokenTransfers)
}

// blockTimeByNumber retrieves the block time based on block number.
func (c *clientImpl) blockTimeByNumber(ctx context.Context, blockNumber *big.Int) (time.Time, error) {
	blockNumberHex := hexutil.EncodeUint64(blockNumber.Uint64())
	var block struct {
		Timestamp string `json:"timestamp"`
	}
	err := c.client.Client().CallContext(ctx, &block, "eth_getBlockByNumber", blockNumberHex, false)
	if err != nil {
		return time.Time{}, err
	}
	parsedTimestamp, err := strconv.ParseInt(block.Timestamp, 0, 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("invalid timestamp format: %w", err)
	}
	return time.Unix(parsedTimestamp, 0), nil
}

// getInternalTransfers retrieves internal token transfers based on the chain.
func (c *clientImpl) getInternalTransfers(ctx context.Context, txHash string) ([]*domain.NativeTokenTransfer, error) {
	if c.chain == domain.Arbitrum {
		return c.getArbitrumInternalTransfers(ctx, txHash)
	}
	return c.getStandardInternalTransfers(ctx, txHash)
}

// getStandardInternalTransfers retrieves internal transfers for standard chains.
func (c *clientImpl) getStandardInternalTransfers(ctx context.Context, txHash string) ([]*domain.NativeTokenTransfer, error) {
	var internalTxs []transactionTrace
	err := c.client.Client().CallContext(ctx, &internalTxs, "trace_transaction", common.HexToHash(txHash))
	if err != nil {
		return nil, err
	}
	kglog.DebugfCtx(ctx, "internalTxs: %v", internalTxs)

	var internalTransfers []*domain.NativeTokenTransfer
	for _, tx := range internalTxs {
		if tx.Action.CallType != "call" || len(tx.TraceAddress) == 0 {
			continue
		}
		value, err := hexutil.DecodeBig(tx.Action.Value)
		if err != nil || value.Cmp(big.NewInt(0)) == 0 {
			continue
		}
		internalTransfers = append(internalTransfers, &domain.NativeTokenTransfer{
			From:   domain.NewEvmAddress(tx.Action.From),
			To:     domain.NewEvmAddress(tx.Action.To),
			Amount: value,
		})
	}
	sort.SliceStable(internalTransfers, func(i, j int) bool {
		return internalTransfers[i].Compare(*internalTransfers[j]) < 0
	})
	return internalTransfers, nil
}

// getArbitrumInternalTransfers retrieves internal transfers specifically for Arbitrum chain.
func (c *clientImpl) getArbitrumInternalTransfers(ctx context.Context, txHash string) ([]*domain.NativeTokenTransfer, error) {
	var result struct {
		Calls []any `json:"calls"`
	}
	err := c.client.Client().CallContext(ctx, &result, "debug_traceTransaction", txHash, map[string]string{"tracer": "callTracer"})
	if err != nil {
		return nil, err
	}

	var internalTransfers []*domain.NativeTokenTransfer
	for _, call := range result.Calls {
		if callMap, ok := call.(map[string]interface{}); ok {
			internalTransfers = append(internalTransfers, c.processArbitrumCall(callMap)...)
		}
	}
	sort.SliceStable(internalTransfers, func(i, j int) bool {
		return internalTransfers[i].Amount.Cmp(internalTransfers[j].Amount) < 0
	})
	return internalTransfers, nil
}

// processArbitrumCall recursively processes calls to extract internal transfers.
func (c *clientImpl) processArbitrumCall(callMap map[string]interface{}) []*domain.NativeTokenTransfer {
	var transfers []*domain.NativeTokenTransfer
	if callMap["type"] == "CALL" {
		from, _ := callMap["from"].(string)
		to, _ := callMap["to"].(string)
		valueHex, _ := callMap["value"].(string)
		value, err := hexutil.DecodeBig(valueHex)
		if err == nil && value.Cmp(big.NewInt(0)) > 0 {
			transfers = append(transfers, &domain.NativeTokenTransfer{
				From:   domain.NewEvmAddress(from),
				To:     domain.NewEvmAddress(to),
				Amount: value,
			})
		}
	}

	if subCalls, ok := callMap["calls"].([]interface{}); ok {
		for _, subCall := range subCalls {
			if subCallMap, ok := subCall.(map[string]interface{}); ok {
				transfers = append(transfers, c.processArbitrumCall(subCallMap)...)
			}
		}
	}
	return transfers
}

// getTokenTransfers extracts token transfers from transaction receipt.
func (c *clientImpl) getTokenTransfers(receipt *types.Receipt) ([]*domain.TokenTransfer, error) {
	erc20, err := abibinding.NewERC20(common.Address{}, c.client)
	if err != nil {
		return nil, err
	}

	var tokenTransfers []*domain.TokenTransfer
	for _, log := range receipt.Logs {
		transfer, err := erc20.ParseTransfer(*log)
		if err != nil {
			continue // Not an ERC20 transfer
		}
		tokenTransfers = append(tokenTransfers, &domain.TokenTransfer{
			Contract: domain.EvmAddress{Address: transfer.Raw.Address},
			From:     domain.EvmAddress{Address: transfer.From},
			To:       domain.EvmAddress{Address: transfer.To},
			Amount:   transfer.Value,
		})
	}
	sort.SliceStable(tokenTransfers, func(i, j int) bool {
		return tokenTransfers[i].Compare(*tokenTransfers[j]) < 0
	})
	return tokenTransfers, nil
}

// parseTransactionDetail constructs the TransactionDetail struct from transaction data.
func (c *clientImpl) parseTransactionDetail(tx *types.Transaction, receipt *types.Receipt, blockTime time.Time, internalTransfers []*domain.NativeTokenTransfer, tokenTransfers []*domain.TokenTransfer) (*domain.TransactionDetail, error) {
	from, err := types.Sender(types.LatestSignerForChainID(tx.ChainId()), tx)
	if err != nil {
		return nil, err
	}
	to := tx.To()
	if to == nil {
		to = &common.Address{}
	}
	methodID := ""
	if len(tx.Data()) >= 4 {
		methodID = hexutil.Encode(tx.Data()[:4])
	}

	return &domain.TransactionDetail{
		Chain:            c.chain,
		Hash:             tx.Hash().Hex(),
		BlockNum:         uint32(receipt.BlockNumber.Uint64()),
		IsError:          receipt.Status == types.ReceiptStatusFailed,
		From:             domain.EvmAddress{Address: from},
		To:               domain.EvmAddress{Address: *to},
		DeployedContract: domain.EvmAddress{Address: receipt.ContractAddress},
		Value:            tx.Value(),
		Data:             hexutil.Encode(tx.Data()),
		GasPrice:         tx.GasPrice(),
		GasUsed:          new(big.Int).SetUint64(receipt.GasUsed),
		MethodID:         methodID,
		FunctionName:     "", // TODO: implement common function name
		Timestamp:        blockTime,
		TransactionTransfers: domain.TransactionTransfers{
			InternalTransfers: internalTransfers,
			TokenTransfers:    tokenTransfers,
			NftTransfers:      []*domain.NftTransfer{}, // TODO: implement NFT transfer
		},
	}, nil
}

// fetchBlock retrieves block data by number or "latest"
func (c *clientImpl) fetchBlock(ctx context.Context, blockNumber string) (*blockResponse, error) {
	var block blockResponse
	err := c.client.Client().CallContext(ctx, &block, "eth_getBlockByNumber", blockNumber, true)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch block: %w", err)
	}
	if block.Hash == "" {
		return nil, domain.ErrBlockNotFound
	}
	return &block, nil
}

// getLatestBlock gets the latest block
func (c *clientImpl) getLatestBlock(ctx context.Context) (*blockResponse, error) {
	return c.fetchBlock(ctx, "latest")
}

// getExternalTransfers retrieves external native transfers from a specific block
func (c *clientImpl) getExternalTransfers(ctx context.Context, blockNumber uint64) (map[string][]domain.Address, error) {
	block, err := c.fetchBlock(ctx, hexutil.EncodeUint64(blockNumber))
	if err != nil {
		return nil, err
	}

	nativeTransfers := make(map[string][]domain.Address)
	for _, tx := range block.Transactions {
		if tx.From == "" {
			continue
		}
		hash := tx.Hash
		nativeTransfers[hash] = append(nativeTransfers[hash], domain.NewEvmAddress(tx.From))
		if tx.To != "" && tx.Value != "0x0" {
			nativeTransfers[hash] = append(nativeTransfers[hash], domain.NewEvmAddress(tx.To))
		}
	}
	return nativeTransfers, nil
}

// FetchTxsInRange fetches all transactions in a given block range, including internal, external, and token transfers.
// TODO: consider NFT tx
func (c *clientImpl) FetchTxsInRange(ctx context.Context, chain domain.Chain, fromBlock, toBlock uint64) (uint64, []*domain.TransactionWithAddresses, error) {
	if c.chain != chain {
		return 0, nil, fmt.Errorf("chain mismatch")
	}

	// Get latest block first to avoid querying non-existent blocks
	latestBlock, err := c.getLatestBlock(ctx)
	if err != nil {
		return 0, nil, err
	}
	latestBlockNum, err := strconv.ParseUint(latestBlock.Number[2:], 16, 64)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to parse latest block number: %w", err)
	}

	// Adjust toBlock if it exceeds the latest block
	if toBlock > latestBlockNum {
		toBlock = latestBlockNum
	}

	hashToAddresses := make(map[string][]domain.Address)

	// Get external and internal transfers
	for blockNum := fromBlock; blockNum <= toBlock; blockNum++ {
		var externalTransfers map[string][]domain.Address
		var err error

		if blockNum == latestBlockNum {
			// Process the latest block we already have
			externalTransfers = make(map[string][]domain.Address)
			for _, tx := range latestBlock.Transactions {
				if tx.From == "" {
					continue
				}
				hash := tx.Hash
				externalTransfers[hash] = append(externalTransfers[hash], domain.NewEvmAddress(tx.From))
				if tx.To != "" && tx.Value != "0x0" {
					externalTransfers[hash] = append(externalTransfers[hash], domain.NewEvmAddress(tx.To))
				}
			}
		} else {
			externalTransfers, err = c.getExternalTransfers(ctx, blockNum)
			if err != nil {
				return 0, nil, fmt.Errorf("failed to get external transfers: %w", err)
			}
		}
		c.appendAddresses(hashToAddresses, externalTransfers)

		internalTransfers, err := c.getBlockInternalTransactions(ctx, hexutil.EncodeUint64(blockNum))
		if err != nil {
			return 0, nil, fmt.Errorf("failed to fetch internal transactions: %w", err)
		}
		c.appendAddresses(hashToAddresses, internalTransfers)
	}

	// Get token transfers
	logs, err := c.fetchTokenTransferLogs(ctx, fromBlock, toBlock)
	if err != nil {
		return 0, nil, err
	}
	c.appendAddresses(hashToAddresses, c.mapHashesToAddresses(logs))

	return toBlock, lo.MapToSlice(hashToAddresses, func(txHash string, addresses []domain.Address) *domain.TransactionWithAddresses {
		uniqueAddresses := lo.Uniq(addresses)
		uniqueAddresses = lo.Filter(uniqueAddresses, func(addr domain.Address, _ int) bool {
			return addr != (domain.EvmAddress{Address: common.Address{}})
		})
		sort.Slice(uniqueAddresses, func(i, j int) bool {
			return uniqueAddresses[i].String() < uniqueAddresses[j].String()
		})
		return &domain.TransactionWithAddresses{
			Chain:     c.chain,
			Hash:      txHash,
			Addresses: uniqueAddresses,
		}
	}), nil
}

// fetchTokenTransferLogs retrieves ERC20 transfer logs within the specified block range.
func (c *clientImpl) fetchTokenTransferLogs(ctx context.Context, fromBlock, toBlock uint64) ([]types.Log, error) {
	query := ethereum.FilterQuery{
		FromBlock: new(big.Int).SetUint64(fromBlock),
		ToBlock:   new(big.Int).SetUint64(toBlock),
		Topics: [][]common.Hash{
			{common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef")},
			{},
			{},
		},
	}
	return c.client.FilterLogs(ctx, query)
}

// mapHashesToAddresses maps transaction hashes to their associated addresses from logs.
func (c *clientImpl) mapHashesToAddresses(logs []types.Log) map[string][]domain.Address {
	erc20, err := abibinding.NewERC20(common.Address{}, c.client)
	if err != nil {
		// Handle error appropriately, possibly log and return empty map
		return map[string][]domain.Address{}
	}

	hashToAddresses := make(map[string][]domain.Address)
	for _, log := range logs {
		transfer, err := erc20.ParseTransfer(log)
		if err != nil {
			continue // Not an ERC20 transfer
		}
		hash := log.TxHash.Hex()
		hashToAddresses[hash] = append(hashToAddresses[hash], domain.EvmAddress{Address: transfer.From}, domain.EvmAddress{Address: transfer.To})
	}
	return hashToAddresses
}

func (c *clientImpl) BroadcastTransaction(ctx context.Context, tx *types.Transaction) (string, error) {
	err := c.client.SendTransaction(ctx, tx)
	if err != nil {
		return "", err
	}
	return tx.Hash().Hex(), nil
}

func (c *clientImpl) BroadcastRawTransaction(ctx context.Context, signedTx string) (string, error) {
	signedTx = strings.TrimPrefix(signedTx, "0x")
	txBytes, err := hex.DecodeString(signedTx)
	if err != nil {
		return "", fmt.Errorf("failed to decode transaction: %w", err)
	}
	tx := &types.Transaction{}
	err = tx.UnmarshalBinary(txBytes)
	if err != nil {
		return "", err
	}
	return c.BroadcastTransaction(ctx, tx)
}

func (c *clientImpl) GetNonce(ctx context.Context, address domain.EvmAddress) (uint64, error) {
	return c.client.PendingNonceAt(ctx, address.Address)
}

// blockTimeByNumber retrieves the block time based on block number.
func (c *clientImpl) BlockTimeByNumber(ctx context.Context, blockNumber *big.Int) (time.Time, error) {
	blockNumberHex := hexutil.EncodeUint64(blockNumber.Uint64())
	var block struct {
		Timestamp string `json:"timestamp"`
	}
	err := c.client.Client().CallContext(ctx, &block, "eth_getBlockByNumber", blockNumberHex, false)
	if err != nil {
		return time.Time{}, err
	}
	parsedTimestamp, err := strconv.ParseInt(block.Timestamp, 0, 64)
	if err != nil {
		return time.Time{}, fmt.Errorf("invalid timestamp format: %w", err)
	}
	return time.Unix(parsedTimestamp, 0), nil
}

type transactionTrace struct {
	Action struct {
		CallType string `json:"callType"`
		From     string `json:"from"`
		To       string `json:"to"`
		Value    string `json:"value"`
	} `json:"action"`
	TransactionHash string `json:"transactionHash"`
	TraceAddress    []int  `json:"traceAddress"`
}

// blockResponse represents the structure of an eth_getBlockByNumber response
type blockResponse struct {
	Hash         string `json:"hash"`
	Transactions []struct {
		Hash  string `json:"hash"`
		From  string `json:"from"`
		To    string `json:"to"`
		Value string `json:"value"`
	} `json:"transactions"`
	Number string `json:"number"`
}

// appendAddresses appends addresses from source map to the target map.
func (c *clientImpl) appendAddresses(target map[string][]domain.Address, source map[string][]domain.Address) {
	for hash, addrs := range source {
		target[hash] = append(target[hash], addrs...)
	}
}

// getBlockInternalTransactions retrieves internal transaction addresses for a specific block.
func (c *clientImpl) getBlockInternalTransactions(ctx context.Context, blockNumberHex string) (map[string][]domain.Address, error) {
	if c.chain == domain.Arbitrum {
		return c.getArbitrumBlockInternalTransactions(ctx, blockNumberHex)
	}
	return c.getStandardBlockInternalTransactions(ctx, blockNumberHex)
}

// getStandardBlockInternalTransactions retrieves internal transfers for standard chains.
func (c *clientImpl) getStandardBlockInternalTransactions(ctx context.Context, blockNumberHex string) (map[string][]domain.Address, error) {
	var internalTxs []transactionTrace
	err := c.client.Client().CallContext(ctx, &internalTxs, "trace_block", blockNumberHex)
	if err != nil {
		return nil, err
	}

	internalTransfers := make(map[string][]domain.Address)
	for _, tx := range internalTxs {
		if tx.Action.CallType != "call" {
			continue
		}
		value, err := hexutil.DecodeBig(tx.Action.Value)
		if err != nil || value.Cmp(big.NewInt(0)) == 0 {
			continue
		}
		hash := tx.TransactionHash
		internalTransfers[hash] = append(internalTransfers[hash], domain.NewEvmAddress(tx.Action.From), domain.NewEvmAddress(tx.Action.To))
	}
	return internalTransfers, nil
}

// debugTraceCall represents the structure of a traced call in Arbitrum.
type debugTraceCall struct {
	From  string `json:"from"`
	To    string `json:"to"`
	Value string `json:"value"`
	Calls []any  `json:"calls"`
	Type  string `json:"type"`
}

// getArbitrumBlockInternalTransactions retrieves internal transfers for Arbitrum chain.
func (c *clientImpl) getArbitrumBlockInternalTransactions(ctx context.Context, blockNumberHex string) (map[string][]domain.Address, error) {
	var result []struct {
		TxHash string         `json:"txHash"`
		Result debugTraceCall `json:"result"`
	}
	err := c.client.Client().CallContext(ctx, &result, "debug_traceBlockByNumber", blockNumberHex, map[string]string{"tracer": "callTracer"})
	if err != nil {
		return nil, err
	}

	internalTransfers := make(map[string][]domain.Address)
	for _, tx := range result {
		transfers := c.extractArbitrumTransfers(tx.Result)
		for _, transfer := range transfers {
			internalTransfers[tx.TxHash] = append(internalTransfers[tx.TxHash], transfer.From, transfer.To)
		}
	}
	return internalTransfers, nil
}

// extractArbitrumTransfers extracts transfer addresses from a debugTraceCall.
func (c *clientImpl) extractArbitrumTransfers(call debugTraceCall) []*domain.NativeTokenTransfer {
	var transfers []*domain.NativeTokenTransfer
	for _, subCall := range call.Calls {
		if subCallMap, ok := subCall.(map[string]interface{}); ok {
			subTransfer := c.processArbitrumCall(subCallMap)
			transfers = append(transfers, subTransfer...)
		}
	}
	return transfers
}

// GetFirstTokenTransferTx retrieves the transaction hash of the first token transfer where the specified address received the token.
// It uses the Alchemy API to efficiently find the earliest Transfer event where the address is the receiver.
func (c *clientImpl) GetFirstTokenTransferTx(ctx context.Context, address domain.EvmAddress, tokenAddress string) (string, error) {
	kglog.InfoWithDataCtx(ctx, "Searching for first token transfer received by address", map[string]interface{}{
		"address": address.String(),
		"token":   tokenAddress,
		"chain":   c.chain.ID(),
	})

	// Prepare parameters for Alchemy API call optimized for finding the first received transfer
	params := &alchemyapi.GetAssetTransfersParams{
		FromBlockHex: "0x0",             // Start from genesis block
		Category:     []string{"erc20"}, // Only query ERC20 token transfers
		MaxCountHex:  "0x64",            // Hex for 100 - reasonable limit to process
		ToAddress:    address.String(),  // Only transfers TO this address
	}

	// Call Alchemy API to get token transfers sent TO the address
	resp, _, err := alchemyapi.Get().GetAssetTransfers(ctx, c.chain.ID(), params)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get token transfers TO address", map[string]interface{}{
			"error":   err.Error(),
			"chain":   c.chain.ID(),
			"token":   tokenAddress,
			"address": address.String(),
		})
		return "", fmt.Errorf("failed to get token transfers: %w", err)
	}

	// Check if we found any transactions
	if resp == nil || len(resp.Result.Transfers) == 0 {
		kglog.InfoWithDataCtx(ctx, "No token transfers found to the address", map[string]interface{}{
			"address": address.String(),
			"chain":   c.chain.ID(),
		})
		return "", fmt.Errorf("no token transfers found to address %s", address.String())
	}

	// Filter and find the earliest transaction involving this specific token
	var firstTxHash string
	var earliestBlockNum int64 = math.MaxInt64

	for _, transfer := range resp.Result.Transfers {
		// Check if this transfer involves our target token
		if strings.EqualFold(transfer.RawContract.Address, tokenAddress) {
			// Convert block number from hex to int for comparison
			blockNum, err := strconv.ParseInt(transfer.BlockNum, 0, 64)
			if err != nil {
				continue
			}

			// Check if this is the earliest transaction we've found so far
			if blockNum < earliestBlockNum {
				earliestBlockNum = blockNum
				firstTxHash = transfer.Hash
			}
		}
	}

	// Check if we found a matching transaction for the specific token
	if firstTxHash == "" {
		kglog.InfoWithDataCtx(ctx, "No token transfers found for the specific token to this address", map[string]interface{}{
			"address": address.String(),
			"token":   tokenAddress,
			"chain":   c.chain.ID(),
		})
		return "", fmt.Errorf("no transfers of token %s to address %s found", tokenAddress, address.String())
	}

	kglog.InfoWithDataCtx(ctx, "Found first token transfer transaction to address", map[string]interface{}{
		"address":  address.String(),
		"token":    tokenAddress,
		"chain":    c.chain.ID(),
		"txHash":   firstTxHash,
		"blockNum": earliestBlockNum,
	})

	return firstTxHash, nil
}
