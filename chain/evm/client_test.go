package evm

import (
	"context"
	"math/big"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestNativeBalance(t *testing.T) {
	t.<PERSON>llel()
	client := GetClient(domain.Sepolia)
	address := domain.NewEvmAddress("******************************************")
	balance, err := client.NativeBalance(context.Background(), address)
	assert.NoError(t, err)
	assert.NotNil(t, balance)
	assert.True(t, balance.Cmp(big.NewInt(100)) >= 0, "Balance should be at least 0.0001 ETH")
	t.Logf("Balance: %s", balance.String())
}

func TestTokenBalance(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Ethereum)
	address := domain.NewEvmAddress("******************************************")
	balance, err := client.TokenBalance(context.Background(), address, "******************************************")
	assert.NoError(t, err)
	assert.NotNil(t, balance)
	assert.True(t, balance.Cmp(big.NewInt(100)) >= 0, "Balance should be at least 0.0001 USDT Token")
	t.Logf("Balance: %s", balance.String())
}

func TestBlockNumber(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Sepolia)
	blockNumber, err := client.BlockNumber(context.Background())
	assert.NoError(t, err)
	t.Logf("Block number: %v", blockNumber)
	assert.True(t, blockNumber > 6914919, "Block number should be greater than 6914919. Captured at 2024-10-21.")
}

func TestGetNonce(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Sepolia)
	nonce, err := client.GetNonce(context.Background(), domain.NewEvmAddress("******************************************"))
	assert.NoError(t, err)
	t.Logf("Nonce: %v", nonce)
}
