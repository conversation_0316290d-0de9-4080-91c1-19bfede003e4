package evm

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	abibinding "github.com/kryptogo/kg-wallet-backend/chain/evm/abi-binding"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// BlockNumber retrieves the latest block number.
func (c *clientImpl) BlockNumber(ctx context.Context) (uint64, error) {
	return c.client.BlockNumber(ctx)
}

// NativeBalance retrieves the native balance of an address.
func (c *clientImpl) NativeBalance(ctx context.Context, address domain.Address) (*big.Int, error) {
	addr, ok := address.(domain.EvmAddress)
	if !ok {
		return nil, fmt.Errorf("invalid address")
	}
	return c.client.BalanceAt(ctx, addr.Address, nil)
}

// TokenBalance retrieves the token balance for a specific token ID.
func (c *clientImpl) TokenBalance(ctx context.Context, address domain.Address, tokenID string) (*big.Int, error) {
	if tokenID == c.chain.MainToken().ID() {
		return c.NativeBalance(ctx, address)
	}
	addr, ok := address.(domain.EvmAddress)
	if !ok {
		return nil, fmt.Errorf("invalid address")
	}
	contract, err := abibinding.NewERC20(common.HexToAddress(tokenID), c.client)
	if err != nil {
		return nil, err
	}
	return contract.BalanceOf(nil, addr.Address)
}

func (c *clientImpl) GetTokenNameSymbol(ctx context.Context, tokenID string) (string, string, error) {
	tokenContract, err := abibinding.NewERC20(common.HexToAddress(tokenID), c.client)
	if err != nil {
		return "", "", err
	}

	// Default values in case of errors
	name := ""
	symbol := ""

	// Try to get name, use empty string if fails
	nameVal, err := tokenContract.Name(nil)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "get token name failed", map[string]interface{}{
			"token": tokenID,
			"error": err,
		})
	} else {
		name = nameVal
	}

	// Try to get symbol, use empty string if fails
	symbolVal, err := tokenContract.Symbol(nil)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "get token symbol failed", map[string]interface{}{
			"token": tokenID,
			"error": err,
		})
	} else {
		symbol = symbolVal
	}

	return name, symbol, err
}

// GetCodeSize retrieves the size of the code at a specific address.
func (c *clientImpl) GetCodeSize(ctx context.Context, address domain.EvmAddress) (int, error) {
	code, err := c.client.CodeAt(ctx, address.Address, nil)
	if err != nil {
		return 0, err
	}
	return len(code), nil
}

// Parse method signature and create input arguments
func parseMethodSignature(method string) (string, error) {
	// Find the opening and closing parentheses
	openParenIndex := strings.Index(method, "(")
	closeParenIndex := strings.LastIndex(method, ")")

	if openParenIndex == -1 || closeParenIndex == -1 || openParenIndex >= closeParenIndex {
		return "", fmt.Errorf("invalid method signature format")
	}

	// Extract the method name
	methodName := method[:openParenIndex]

	// You can remove paramTypes if not used
	// paramTypes := strings.Split(method[openParenIndex+1:closeParenIndex], ",")

	return methodName, nil
}

// EstimateGas estimates the gas needed for a transaction
func (c *clientImpl) EstimateGas(ctx context.Context, msg ethereum.CallMsg) (uint64, error) {
	// Add some buffer (10%) to the estimated gas as safety margin
	estimatedGas, err := c.client.EstimateGas(ctx, msg)
	if err != nil {
		return 0, fmt.Errorf("failed to estimate gas: %w", err)
	}

	// Add 10% buffer
	gasLimit := estimatedGas + (estimatedGas / 10)
	return gasLimit, nil
}

// CreateNativeTransfer creates an unsigned transaction to transfer native token
func (c *clientImpl) CreateNativeTransfer(ctx context.Context, from, to domain.EvmAddress, amount *big.Int) (*types.Transaction, error) {
	// Get nonce for the sender
	nonce, err := c.GetNonce(ctx, from)
	if err != nil {
		return nil, fmt.Errorf("failed to get nonce: %w", err)
	}

	// Get gas price
	gasPrice, err := c.client.SuggestGasPrice(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get gas price: %w", err)
	}
	// Increase gas price by 50% or 3x
	if c.chain.IsTestnet() {
		gasPrice = new(big.Int).Mul(gasPrice, big.NewInt(3))
	} else {
		gasPrice = new(big.Int).Mul(gasPrice, big.NewInt(15))
		gasPrice = gasPrice.Div(gasPrice, big.NewInt(10))
	}

	// Create call message for gas estimation
	msg := ethereum.CallMsg{
		From:  from.Address,
		To:    &to.Address,
		Value: amount,
		Data:  nil,
	}
	kglog.InfoWithDataCtx(ctx, "estimate gas", map[string]interface{}{
		"from":     from.String(),
		"to":       to.String(),
		"amount":   amount.String(),
		"gasPrice": gasPrice.String(),
	})

	// Estimate gas with buffer
	gasLimit, err := c.EstimateGas(ctx, msg)
	if err != nil {
		return nil, err
	}

	// Create transaction with estimated gas limit
	tx := types.NewTransaction(nonce, to.Address, amount, gasLimit, gasPrice, nil)

	return tx, nil
}
