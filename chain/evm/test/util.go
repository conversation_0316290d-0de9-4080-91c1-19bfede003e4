package evmutil

import (
	"context"
	"crypto/ecdsa"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/kg-wallet-backend/chain/evm"
	abibinding "github.com/kryptogo/kg-wallet-backend/chain/evm/abi-binding"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/shopspring/decimal"
)

func TransferToken(t *testing.T, chain domain.Chain, privateKey *ecdsa.PrivateKey, to domain.EvmAddress, tokenAddress domain.EvmAddress, amount decimal.Decimal) string {
	ctx := context.Background()
	client := evm.GetClient(chain)

	// Convert amount to big.Int
	amountBigInt := amount.BigInt()

	// Create a new instance of the ERC20 contract
	erc20Token, err := abibinding.NewERC20(tokenAddress.Address, client.GetRawClient())
	if err != nil {
		t.Fatalf("failed to instantiate ERC20 contract: %v", err)
	}

	// Prepare the transaction options
	auth, err := bind.NewKeyedTransactorWithChainID(privateKey, big.NewInt(chain.Number()))
	if err != nil {
		t.Fatalf("failed to create transaction options: %v", err)
	}

	// Execute the transfer
	tx, err := erc20Token.Transfer(auth, to.Address, amountBigInt)
	if err != nil {
		t.Fatalf("failed to transfer token: %v, to: %s, amount: %s", err, to.Address, amount)
	}

	t.Logf("tx: %s\n", tx.Hash().Hex())

	// Wait for the transaction to be mined
	receipt, err := bind.WaitMined(ctx, client.GetRawClient(), tx)
	if err != nil {
		t.Fatalf("failed to wait for transaction to be mined: %v", err)
	}

	return receipt.TxHash.Hex()
}

func TransferNativeToken(t *testing.T, chain domain.Chain, privateKey *ecdsa.PrivateKey, to domain.EvmAddress, amount decimal.Decimal) string {
	ctx := context.Background()
	client := evm.GetClient(chain)

	// Get the sender's address
	fromAddress := crypto.PubkeyToAddress(privateKey.PublicKey)
	from := domain.NewEvmAddress(fromAddress.Hex())

	// Create transaction
	// Convert amount to big.Int
	amountBigInt := amount.BigInt()
	t.Logf("amountBigInt: %s\n", amountBigInt.String())
	balance, err := client.NativeBalance(ctx, from)
	if err != nil {
		t.Fatalf("failed to get balance: %v", err)
	}
	t.Logf("balance: %s\n", balance.String())

	tx, err := client.CreateNativeTransfer(ctx, from, to, amountBigInt)
	if err != nil {
		t.Fatalf("failed to create native transfer: %v", err)
	}

	// Sign the transaction
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(big.NewInt(chain.Number())), privateKey)
	if err != nil {
		t.Fatalf("failed to sign transaction: %v", err)
	}

	// Send the transaction
	_, err = client.BroadcastTransaction(ctx, signedTx)
	if err != nil {
		t.Fatalf("failed to send transaction: %v", err)
	}

	// Wait for the transaction to be mined
	receipt, err := bind.WaitMined(ctx, client.GetRawClient(), signedTx)
	if err != nil {
		t.Fatalf("failed to wait for transaction to be mined: %v", err)
	}

	return receipt.TxHash.Hex()
}
