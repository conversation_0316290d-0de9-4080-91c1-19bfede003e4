package evmutil

import (
	"context"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/kg-wallet-backend/chain/evm"
	"github.com/kryptogo/kg-wallet-backend/domain"
)

func TestEvmTransactionReplacement(t *testing.T) {
	t.Skip("This test is only used when transaction is stuck.")

	// !! Select the chain to go with !!
	// chain := domain.Holesky
	chain := domain.Hoodi
	client := evm.GetClient(chain)

	ctx := context.Background()

	address := "******************************************"
	privateKey := "afb24289e9e06060d6a291698c783404f3d92dafcc69de63297bd3a6ad35832b"

	// 獲取當前的 nonce
	startNonce, err := client.GetRawClient().NonceAt(ctx, common.HexToAddress(address), nil)
	if err != nil {
		t.Fatalf("failed to get pending nonce: %v", err)
	}

	t.Logf("startNonce: %d", startNonce)

	endNonce, err := client.GetRawClient().PendingNonceAt(ctx, common.HexToAddress(address))
	if err != nil {
		t.Fatalf("failed to get pending nonce: %v", err)
	}

	t.Logf("endNonce: %d", endNonce)

	for i := startNonce; i <= endNonce+1; i++ {
		t.Logf("nonce: %d", i)
		// 設置較高的 gas price
		gasPrice, err := client.GetRawClient().SuggestGasPrice(ctx)
		if err != nil {
			t.Fatalf("failed to suggest gas price: %v", err)
		}
		gasPrice = new(big.Int).Mul(gasPrice, big.NewInt(100))

		// 構建交易
		toAddress := common.HexToAddress("******************************************")
		value := big.NewInt(1000000000) // 發送少量的 ETH，例如 1 Gwei
		gasLimit := uint64(21000)       // 標準轉帳的 gas limit

		tx := types.NewTransaction(i, toAddress, value, gasLimit, gasPrice, nil)

		// 獲取私鑰
		privateKeyECDSA, err := crypto.HexToECDSA(privateKey)
		if err != nil {
			t.Fatalf("failed to convert private key: %v", err)
		}

		// 簽名交易
		number := big.NewInt(chain.Number())
		signedTx, err := types.SignTx(tx, types.LatestSignerForChainID(number), privateKeyECDSA)
		if err != nil {
			t.Fatalf("failed to sign transaction: %v", err)
		}

		// 發送交易
		err = client.GetRawClient().SendTransaction(ctx, signedTx)
		if err != nil {
			t.Fatalf("failed to send transaction: %v", err)
		}

		t.Logf("Nonce: %d, replacement transaction sent: %s", i, signedTx.Hash().Hex())
	}
}
