package evm

import (
	"context"
	"fmt"
	"math/big"
	"net/http"

	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
	abibinding "github.com/kryptogo/kg-wallet-backend/chain/evm/abi-binding"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

var walletBalanceProviderContract = map[domain.Chain]string{
	domain.Ethereum:  "******************************************",
	domain.Polygon:   "******************************************",
	domain.BNBChain:  "******************************************",
	domain.Arbitrum:  "******************************************",
	domain.Optimism:  "******************************************",
	domain.BaseChain: "******************************************",
	domain.Sepolia:   "******************************************",
}

func (s *serviceImpl) SupportedChains() []domain.Chain {
	return lo.Keys(walletBalanceProviderContract)
}

func (s *serviceImpl) FetchAmounts(ctx context.Context, chain domain.Chain, tokens []*domain.WalletToken) (map[domain.Address]map[string]decimal.Decimal, error) {
	if !lo.Contains(s.SupportedChains(), chain) {
		return nil, fmt.Errorf("chain not supported: %s", chain)
	}
	for _, token := range tokens {
		if _, ok := token.Wallet.(domain.EvmAddress); !ok {
			return nil, fmt.Errorf("wallet address is not an evm address")
		}
	}

	wallets := lo.Map(tokens, func(t *domain.WalletToken, _ int) domain.EvmAddress {
		return t.Wallet.(domain.EvmAddress)
	})
	wallets = lo.Uniq(wallets)
	ethAddress := "******************************************" // mock eth address for aave contract
	tokenIds := lo.Map(tokens, func(t *domain.WalletToken, _ int) string {
		if t.IsMainToken() {
			return ethAddress
		}
		return t.ID()
	})
	tokenIds = lo.Uniq(tokenIds)
	balances, err := BatchBalanceOf(ctx, chain, wallets, tokenIds)
	if err != nil {
		return nil, err.Error
	}

	amountsMap := make(map[domain.Address]map[string]decimal.Decimal)
	for _, token := range tokens {
		wallet, tokenId := token.Wallet.(domain.EvmAddress), token.ID()
		if _, ok := amountsMap[wallet]; !ok {
			amountsMap[wallet] = make(map[string]decimal.Decimal)
		}
		rawBalance := balances[wallet][tokenId]
		if token.IsMainToken() {
			rawBalance = balances[wallet][ethAddress]
		}
		decimals := token.Decimals()
		amountsMap[wallet][tokenId] = decimal.NewFromBigInt(rawBalance, -int32(decimals))
	}
	return amountsMap, nil
}

func BatchBalanceOf(ctx context.Context, chain domain.Chain, wallets []domain.EvmAddress, tokens []string) (map[domain.EvmAddress]map[string]*big.Int, *code.KGError) {
	client, err := ethclient.Dial(RpcURL(chain))
	if err != nil {
		kglog.ErrorfCtx(ctx, "failed to connect to Ethereum client: %v", err)
		return nil, code.NewKGError(code.ThirdPartyAPIError, http.StatusInternalServerError, fmt.Errorf("failed to connect to Ethereum client"), nil)
	}
	contractAddress, ok := walletBalanceProviderContract[chain]
	if !ok {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("invalid chain id"), nil)
	}
	contract, err := abibinding.NewWalletBalanceProviderCaller(common.HexToAddress(contractAddress), client)
	if err != nil {
		kglog.ErrorfCtx(ctx, "failed to create contract instance: %v", err)
		return nil, code.NewKGError(code.ThirdPartyAPIError, http.StatusInternalServerError, fmt.Errorf("failed to create contract instance"), nil)
	}

	wallets = lo.Uniq(wallets)
	tokens = lo.Uniq(tokens)

	// convert wallets and tokens to []common.Address and call batch balance of
	walletAddresses := lo.Map(wallets, func(wallet domain.EvmAddress, _ int) common.Address {
		return wallet.Address
	})
	tokenAddresses := lo.Map(tokens, func(token string, _ int) common.Address {
		if token == chain.MainToken().ID() {
			return common.HexToAddress("******************************************")
		}
		return common.HexToAddress(token)
	})
	balances, err := contract.BatchBalanceOf(&bind.CallOpts{Context: ctx}, walletAddresses, tokenAddresses)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "[evm_amounts_fetcher] failed to get batch balance", map[string]any{
			"error":           err.Error(),
			"walletAddresses": walletAddresses,
			"tokenAddresses":  tokenAddresses,
		})
		return nil, code.NewKGError(code.ThirdPartyAPIError, http.StatusInternalServerError, fmt.Errorf("[evm_amounts_fetcher] failed to get batch balance of: %v", err), nil)
	}

	// the order of balances is: (w[0],t[0]), (w[0],t[1]), ..., (w[0],t[10]), (w[1],t[0]), ...
	result := make(map[domain.EvmAddress]map[string]*big.Int)
	for i, wallet := range wallets {
		result[wallet] = make(map[string]*big.Int)
		for j, token := range tokens {
			result[wallet][token] = balances[i*len(tokens)+j]
		}
	}
	return result, nil
}
