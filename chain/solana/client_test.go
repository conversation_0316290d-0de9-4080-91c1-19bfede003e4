package solana

import (
	"context"
	"math/big"
	"os"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	solanaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api"
	"github.com/stretchr/testify/assert"
)

func TestMain(m *testing.M) {
	solanaapi.InitDefault()
	os.Exit(m.Run())
}

func TestNativeBalance(t *testing.T) {
	t.<PERSON>()
	client := GetClient(domain.Solana)
	address := domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7")
	balance, err := client.NativeBalance(context.Background(), address)
	assert.NoError(t, err)
	assert.NotNil(t, balance)
	t.Logf("Balance: %s", balance.String())
	assert.True(t, balance.Cmp(big.NewInt(10000)) >= 0, "Balance should be at least 0.00001 SOL")
}

func TestTokenBalance(t *testing.T) {
	t.<PERSON>()
	client := GetClient(domain.Solana)
	address := domain.NewAddressByChain(domain.Solana, "Q6XprfkF8RQQKoQVG33xT88H7wi8Uk1B1CC7YAs69Gi")
	balance, err := client.TokenBalance(context.Background(), address, "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB")
	assert.NoError(t, err)
	assert.NotNil(t, balance)
	t.Logf("Balance: %s", balance.String())
	assert.True(t, balance.Cmp(big.NewInt(1000000)) >= 0, "Balance should be at least 1 USDT Token")
}

func TestBlockNumber(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)
	blockNumber, err := client.BlockNumber(context.Background())
	assert.NoError(t, err)
	t.Logf("Block number: %v", blockNumber)
	assert.True(t, blockNumber > 296827223, "Block number should be greater than 296827223. Captured at 2024-10-21.")
}

func TestGetTransactionStatus(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)

	testCases := []struct {
		name     string
		txHash   string
		expected domain.TransactionStatus
	}{
		{
			name:     "Successful Transaction",
			txHash:   "58EE9BktqVFgmmvUTTMYavwxnsnvEHHQujbZnKj8qsP9twmsgnuwi45G4AZY2ri56gz9xFHK3u7ZzPjZqEcKdStU",
			expected: domain.TransactionStatusSuccess,
		},
		{
			name:     "Failed Transaction",
			txHash:   "4VKozDdmAB5dTwLBtooRwwoAAvNXxvMt1DAGWiXaWqPxeZZbH6QUJhysQxu7id1wC1Jj87KZTJygtjiwpjCAGGrC",
			expected: domain.TransactionStatusFailed,
		},
		{
			name:     "Not exist Transaction",
			txHash:   "4VKozDdmAB5dTwLBtooRwwoAAvNXxvMt1DAGWiXaWqPxeZZbH6QUJhysQxu7id1wC1Jj87KZTJygtjiwpjCAGGrB",
			expected: domain.TransactionStatusUnknown,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			status, err := client.GetTransactionStatus(context.Background(), tc.txHash)
			if tc.expected == domain.TransactionStatusUnknown {
				t.Logf("err: %v", err)
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expected, status)
			}
		})
	}
}

func TestWaitUntilTransactionConfirmed(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)

	testCases := []struct {
		name        string
		txHash      string
		expectError error
		expected    domain.TransactionStatus
	}{
		{
			name:     "Successful Transaction",
			txHash:   "58EE9BktqVFgmmvUTTMYavwxnsnvEHHQujbZnKj8qsP9twmsgnuwi45G4AZY2ri56gz9xFHK3u7ZzPjZqEcKdStU",
			expected: domain.TransactionStatusSuccess,
		},
		{
			name:     "Failed Transaction",
			txHash:   "4VKozDdmAB5dTwLBtooRwwoAAvNXxvMt1DAGWiXaWqPxeZZbH6QUJhysQxu7id1wC1Jj87KZTJygtjiwpjCAGGrC",
			expected: domain.TransactionStatusFailed,
		},
		{
			name:        "Not exist Transaction",
			txHash:      "4VKozDdmAB5dTwLBtooRwwoAAvNXxvMt1DAGWiXaWqPxeZZbH6QUJhysQxu7id1wC1Jj87KZTJygtjiwpjCAGGrB",
			expectError: context.DeadlineExceeded,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			status, err := client.WaitUntilTransactionConfirmed(ctx, tc.txHash)
			if tc.expectError != nil {
				assert.Error(t, err)
				assert.Equal(t, tc.expectError, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expected, status)
			}
		})
	}
}

func TestUsdtTransferTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)

	txHash := "sF7pGcQzeF2vtWuqp7bVzP9K2vjTcNem7TuGGu9BSvPTGoqLn4RryAXhKTpaqZwd3BNrEZyfGUFJ2V7M6GE5EYT"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Data: %s", detail.Data)
	t.Logf("Method ID: %s", detail.MethodID)
	t.Logf("Value: %s", detail.Value.String())
	t.Logf("Gas Price: %s", detail.GasPrice.String())
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(296856360), detail.BlockNum)
	assert.Equal(t, big.NewInt(0), detail.GasPrice)
	assert.Equal(t, big.NewInt(410000), detail.GasUsed)
	assert.Equal(t, "", detail.Data)
	assert.Equal(t, "", detail.MethodID)
	assert.Equal(t, time.Unix(1729508489, 0), detail.Timestamp)

	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "FudCz5etrke6SiwppNULMuDfDCYrQkHawsodSLt3To4C"), detail.From)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"), detail.To)
	assert.Nil(t, detail.Value)

	assert.Equal(t, 0, len(detail.InternalTransfers))

	assert.Equal(t, 1, len(detail.TokenTransfers))
	for _, transfer := range detail.TokenTransfers {
		t.Logf("Token Transfer: %+v", transfer)
	}
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"), detail.TokenTransfers[0].Contract)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "FudCz5etrke6SiwppNULMuDfDCYrQkHawsodSLt3To4C"), detail.TokenTransfers[0].From)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "AC5RDfQFmDS1deWZos921JfqscXdByf8BKHs5ACWjtW2"), detail.TokenTransfers[0].To)
	assert.Equal(t, big.NewInt(108286703), detail.TokenTransfers[0].Amount)

	assert.Equal(t, 0, len(detail.NftTransfers))
}

func TestSolTransferTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)
	txHash := "5KWvC68Zf8jqH6t1wADh8ocdPWbhHua5SQkGr7b8pi412fVB5NVViFuJ2XBcrMkZ9GhXL2rDyqYaV1V4ZvkGgXH1"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Data: %s", detail.Data)
	t.Logf("Method ID: %s", detail.MethodID)
	t.Logf("Value: %s", detail.Value.String())
	t.Logf("Gas Price: %s", detail.GasPrice.String())
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(301095652), detail.BlockNum)
	assert.Equal(t, big.NewInt(0), detail.GasPrice)
	assert.Equal(t, big.NewInt(5000), detail.GasUsed)
	assert.Equal(t, "", detail.Data)
	assert.Equal(t, "", detail.MethodID)
	assert.Equal(t, time.Unix(1731477853, 0), detail.Timestamp)

	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "EJtaDztfm9UNKZPJyKyFhPJRuFgiA6ebEStEjr59xLx6"), detail.From)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "********************************"), detail.To)
	assert.Nil(t, detail.Value)

	assert.Equal(t, 1, len(detail.InternalTransfers))
	assert.Equal(t, big.NewInt(490000000), detail.InternalTransfers[0].Amount)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "EJtaDztfm9UNKZPJyKyFhPJRuFgiA6ebEStEjr59xLx6"), detail.InternalTransfers[0].From)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "FWBjyjcsoQw4d1xupzkV3QaLvxwxxLzNAdkcvXopReyz"), detail.InternalTransfers[0].To)

	assert.Equal(t, 0, len(detail.TokenTransfers))

	assert.Equal(t, 0, len(detail.NftTransfers))
}

func TestComplicatedTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)

	t.Run("18 native token transfers", func(t *testing.T) {
		t.Parallel()
		txHash := "57maFHBGdkfCq2SrWS6VsG5YPeZa9SpD1tFzKg4TRHdTo1CBPaFTHRxubTv3HTBBAm9GDdcS3DxwakLbWkcrfG8r"

		detail, err := client.TransactionDetail(context.Background(), txHash)
		t.Logf("err: %v", err)
		assert.NoError(t, err)
		assert.NotNil(t, detail)

		t.Logf("Transaction Hash: %s", detail.Hash)
		t.Logf("Block Number: %d", detail.BlockNum)
		t.Logf("From: %s", detail.From)
		t.Logf("To: %s", detail.To)
		t.Logf("Data: %s", detail.Data)
		t.Logf("Method ID: %s", detail.MethodID)
		t.Logf("Value: %s", detail.Value.String())
		t.Logf("Gas Price: %s", detail.GasPrice.String())
		t.Logf("Gas Used: %s", detail.GasUsed.String())
		t.Logf("Timestamp: %v", detail.Timestamp.Unix())

		assert.Equal(t, txHash, detail.Hash)
		assert.Equal(t, uint32(296851700), detail.BlockNum)
		assert.Equal(t, big.NewInt(0), detail.GasPrice)
		assert.Equal(t, big.NewInt(5661), detail.GasUsed)
		assert.Equal(t, time.Unix(1729506340, 0), detail.Timestamp)

		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "fLiPgg2yTvmgfhiPkKriAHkDmmXGP6CdeFX9UF5o7Zc"), detail.From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "********************************"), detail.To)
		assert.Nil(t, detail.Value)

		assert.Equal(t, 0, len(detail.TokenTransfers))

		for _, internalTransfers := range detail.InternalTransfers {
			t.Logf("internalTransfer : %v", internalTransfers)
		}
		assert.Equal(t, 18, len(detail.InternalTransfers))
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "fLiPgg2yTvmgfhiPkKriAHkDmmXGP6CdeFX9UF5o7Zc"), detail.InternalTransfers[0].From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "C5Wc4RRja3ZSJVBKpbtHtmzyQyJT4CXnyNmE5MzB4648"), detail.InternalTransfers[0].To)
		assert.Equal(t, big.NewInt(100), detail.InternalTransfers[0].Amount)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "fLiPgg2yTvmgfhiPkKriAHkDmmXGP6CdeFX9UF5o7Zc"), detail.InternalTransfers[17].From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "99q4g7RHKh1eD2w95HFSQdakYC6RqmAqEyzptCLN74E4"), detail.InternalTransfers[17].To)
		assert.Equal(t, big.NewInt(100), detail.InternalTransfers[0].Amount)
	})

	t.Run("Swap", func(t *testing.T) {
		t.Parallel()
		txHash := "5m5apho5GDz9mjPJtMGhzbhRW7LiwcfFJxnbcsmo4xCV1JDxDUiyuoafDa3BEjCwsDbyDwC5LMkjSrzeAFyo5Tpz"
		detail, err := client.TransactionDetail(context.Background(), txHash)
		t.Logf("err: %v", err)
		assert.NoError(t, err)
		assert.NotNil(t, detail)

		t.Logf("Transaction Hash: %s", detail.Hash)
		t.Logf("Block Number: %d", detail.BlockNum)
		t.Logf("From: %s", detail.From)
		t.Logf("To: %s", detail.To)
		t.Logf("Data: %s", detail.Data)
		t.Logf("Method ID: %s", detail.MethodID)
		t.Logf("Value: %s", detail.Value.String())
		t.Logf("Gas Price: %s", detail.GasPrice.String())
		t.Logf("Gas Used: %s", detail.GasUsed.String())
		t.Logf("Timestamp: %v", detail.Timestamp.Unix())

		assert.Equal(t, txHash, detail.Hash)
		assert.Equal(t, uint32(299660822), detail.BlockNum)
		assert.Equal(t, big.NewInt(0), detail.GasPrice)
		assert.Equal(t, big.NewInt(5000), detail.GasUsed)
		assert.Equal(t, time.Unix(1730818273, 0), detail.Timestamp)

		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "A4gdUUiLvXGKP4qkgcN7fqVkA66G9rjS2TksMEjXmBoC"), detail.From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8"), detail.To)
		assert.Nil(t, detail.Value)

		for _, tokenTransfer := range detail.TokenTransfers {
			t.Logf("tokenTransfer : %v", tokenTransfer)
		}
		assert.Equal(t, 1, len(detail.TokenTransfers))

		// First transfer: Elections token from user to Raydium
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "9QbGn8h666P2bz1uSJz63GK2F2ytEujvn5c96VJzpump"), detail.TokenTransfers[0].Contract) // Elections token
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "A4gdUUiLvXGKP4qkgcN7fqVkA66G9rjS2TksMEjXmBoC"), detail.TokenTransfers[0].From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"), detail.TokenTransfers[0].To) // Raydium (SOL-Elections) Pool 2
		assert.Equal(t, big.NewInt(1000000000), detail.TokenTransfers[0].Amount)

		for _, internalTransfer := range detail.InternalTransfers {
			t.Logf("internalTransfer : %v", internalTransfer)
		}
		assert.Equal(t, 2, len(detail.InternalTransfers))
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "A4gdUUiLvXGKP4qkgcN7fqVkA66G9rjS2TksMEjXmBoC"), detail.InternalTransfers[0].From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "E7TvPotQWJ6WN9vWQHJLqNK6VgFKqDHFwEXhMZr1cSWM"), detail.InternalTransfers[0].To)
		assert.Equal(t, big.NewInt(1950720), detail.InternalTransfers[0].Amount)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"), detail.InternalTransfers[1].From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "A4gdUUiLvXGKP4qkgcN7fqVkA66G9rjS2TksMEjXmBoC"), detail.InternalTransfers[1].To)
		assert.Equal(t, big.NewInt(672208), detail.InternalTransfers[1].Amount)
	})

	t.Run("Swap SOL to Token", func(t *testing.T) {
		t.Parallel()
		txHash := "2hZwhKwrgheJQuFcvujujqgBUsWvBRp4TLspfwa3rf71cdzWDCcbzudTSWxHDzaAR9gCT3cD4xNegPTtJrG51RdU"
		detail, err := client.TransactionDetail(context.Background(), txHash)
		t.Logf("err: %v", err)
		assert.NoError(t, err)
		assert.NotNil(t, detail)

		t.Logf("Transaction Hash: %s", detail.Hash)
		t.Logf("Block Number: %d", detail.BlockNum)
		t.Logf("From: %s", detail.From)
		t.Logf("To: %s", detail.To)
		t.Logf("Data: %s", detail.Data)
		t.Logf("Method ID: %s", detail.MethodID)
		t.Logf("Value: %s", detail.Value.String())
		t.Logf("Gas Price: %s", detail.GasPrice.String())
		t.Logf("Gas Used: %s", detail.GasUsed.String())
		t.Logf("Timestamp: %v", detail.Timestamp.Unix())

		assert.Equal(t, txHash, detail.Hash)
		assert.Equal(t, uint32(311566546), detail.BlockNum)
		assert.Equal(t, big.NewInt(0), detail.GasPrice)
		assert.Equal(t, big.NewInt(240014), detail.GasUsed)
		assert.Equal(t, time.Unix(1735888649, 0), detail.Timestamp)

		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "6m2CDdhRgxpH4WjvdzxAYbGxwdGUz5MziiL5jek2kBma"), detail.To)
		assert.Nil(t, detail.Value)

		for _, tokenTransfer := range detail.TokenTransfers {
			t.Logf("tokenTransfer : %v", tokenTransfer)
		}
		assert.Equal(t, 1, len(detail.TokenTransfers))

		// Token transfer
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "2FCuCToyXnrq2Lx6DKttpi72oNx77bqtK2Adgo52pMUh"), detail.TokenTransfers[0].From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.TokenTransfers[0].To)
		assert.Equal(t, big.NewInt(74814050354), detail.TokenTransfers[0].Amount)

		for _, internalTransfer := range detail.InternalTransfers {
			t.Logf("internalTransfer : %v", internalTransfer)
		}
		assert.Equal(t, 3, len(detail.InternalTransfers))

		// Internal transfers
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.InternalTransfers[0].From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "FERjPVNEa7Udq8CEv68h6tPL46Tq7ieE49HrE2wea3XT"), detail.InternalTransfers[0].To)
		assert.Equal(t, big.NewInt(953399630), detail.InternalTransfers[0].Amount)

		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.InternalTransfers[1].From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "25mYnjJ2MXHZH6NvTTdA63JvjgRVcuiaj6MRiEQNs1Dq"), detail.InternalTransfers[1].To)
		assert.Equal(t, big.NewInt(8189750), detail.InternalTransfers[1].Amount)

		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.InternalTransfers[2].From)
		assert.Equal(t, domain.NewAddressByChain(domain.Solana, "2EEAcuqsJUpQXgNKAe1YtmHmFkPn2G5YyQbMzSZXdMPS"), detail.InternalTransfers[2].To)
		assert.Equal(t, big.NewInt(1910620), detail.InternalTransfers[2].Amount)
	})
}

func TestSwapToSolTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)

	txHash := "5qBGFNZVzAHHgjcNREqHQNtR8NKtXw98wquMmxPgM16mm9jLnpSm8NxqCzCjhDsjnzGW8Sd6z24ca6iaKCZA1ABk"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	// Log all details for debugging
	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	// Verify basic transaction details
	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(313661692), detail.BlockNum)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.From)
	assert.Equal(t, big.NewInt(37144), detail.GasUsed)
	assert.Equal(t, time.Unix(1736745408, 0), detail.Timestamp)

	// Log token transfers
	for _, tokenTransfer := range detail.TokenTransfers {
		t.Logf("Token Transfer: %+v", tokenTransfer)
	}
	assert.Equal(t, 1, len(detail.TokenTransfers))

	// Verify token transfers - should only be Joni token out
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "GiaXSMzr7rkUVZh1GWqrCPYmaaSAccVrTCce4pLmpump"), detail.TokenTransfers[0].Contract)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.TokenTransfers[0].From)
	joniAmount, _ := new(big.Int).SetString("44077701293", 10)
	assert.Equal(t, joniAmount, detail.TokenTransfers[0].Amount)

	// Log internal transfers
	for _, internalTransfer := range detail.InternalTransfers {
		t.Logf("Internal Transfer: %+v", internalTransfer)
	}
	assert.Equal(t, 1, len(detail.InternalTransfers))

	// Verify internal transfer - receiving SOL
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.InternalTransfers[0].To)
	solAmount, _ := new(big.Int).SetString("12557360", 10)
	assert.Equal(t, solAmount, detail.InternalTransfers[0].Amount)
}

func TestTwoInternalTransfersTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)

	txHash := "5BA4zAycuc2XWSk3d1wg5wChVJcRXx4yRpm1UgKEoYBxkuJP1PPYKoJHv9FmPozkuCUsmWFpFkvnBSYfw4aiwUJ6"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	// Log all details for debugging
	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	// Verify basic transaction details
	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(313662100), detail.BlockNum)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.From)
	assert.Equal(t, big.NewInt(79293), detail.GasUsed)
	assert.Equal(t, time.Unix(1736745576, 0), detail.Timestamp)

	// Log internal transfers
	for _, internalTransfer := range detail.InternalTransfers {
		t.Logf("Internal Transfer: %+v", internalTransfer)
	}

	// Verify internal transfers
	assert.Equal(t, 2, len(detail.InternalTransfers))
	// First transfer
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.InternalTransfers[0].From)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "98NwTZayK6wvqgdVV4ThX2ffytr1QsaaAWpbpdvzLFAv"), detail.InternalTransfers[0].To)
	wsolAmount, _ := new(big.Int).SetString("1400000000", 10)
	assert.Equal(t, wsolAmount, detail.InternalTransfers[0].Amount)

	// Second transfer
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.InternalTransfers[1].From)
	assert.Equal(t, domain.NewAddressByChain(domain.Solana, "98NwTZayK6wvqgdVV4ThX2ffytr1QsaaAWpbpdvzLFAv"), detail.InternalTransfers[1].To)
	solAmount, _ := new(big.Int).SetString("7039280", 10)
	assert.Equal(t, solAmount, detail.InternalTransfers[1].Amount)

	// Verify no token transfers
	assert.Equal(t, 0, len(detail.TokenTransfers))
}

func TestJupiterSwapWithJitoTipTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)

	txHash := "w7XUUktbfAMD66rfJ5ggfnnQJZSzMRQCbYeAZakSVfSMGYZv6iToWjMk2eSZyNo73EesHPt1PgdsWNZxJopCwrf"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	// Log all details for debugging
	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(318420417), detail.BlockNum)
	assert.Equal(t, domain.NewStrAddress("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), detail.From)

	// Verify token transfers
	assert.Len(t, detail.TokenTransfers, 1)
	tokenTransfer := detail.TokenTransfers[0]
	assert.Equal(t, "6NcdiK8B5KK2DzKvzvCfqi8EHaEqu48fyEzC8Mm9pump", tokenTransfer.Contract.String())
	assert.Equal(t, domain.NewStrAddress("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), tokenTransfer.From)
	assert.Equal(t, domain.NewStrAddress("Fp7f3hXw5X2oMtnZCok39qTygqA9Q8xHZ8EKhrRXKd2r"), tokenTransfer.To)
	assert.Equal(t, big.NewInt(530558008), tokenTransfer.Amount)

	// Verify internal transfers
	assert.Len(t, detail.InternalTransfers, 3)

	// Jupiter SOL receive
	jupiterTransfer := detail.InternalTransfers[0]
	assert.Equal(t, domain.NewStrAddress("Fp7f3hXw5X2oMtnZCok39qTygqA9Q8xHZ8EKhrRXKd2r"), jupiterTransfer.From)
	assert.Equal(t, domain.NewStrAddress("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), jupiterTransfer.To)
	assert.Equal(t, big.NewInt(121178145), jupiterTransfer.Amount)

	// Jito tip transfer
	jitoTipTransfer := detail.InternalTransfers[1]
	assert.Equal(t, domain.NewStrAddress("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), jitoTipTransfer.From)
	assert.Equal(t, domain.NewStrAddress("ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt"), jitoTipTransfer.To)
	assert.Equal(t, big.NewInt(3974641), jitoTipTransfer.Amount)

	// Fee transfer
	feeTransfer := detail.InternalTransfers[2]
	assert.Equal(t, domain.NewStrAddress("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"), feeTransfer.From)
	assert.Equal(t, domain.NewStrAddress("GsTx64KUeHpAsZcTtLSyAuZTrtFHqWtJbVc36XZtiZpr"), feeTransfer.To)
	assert.Equal(t, big.NewInt(121178), feeTransfer.Amount)

}

func TestPumpSwapTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)

	txHash := "3ehUXx58s7GAbUEUbqJXpC5kVJjjeCEGuyYatAvbJnfmRiyMUhn69cknc86R2AWDSvPzzjSZNiYCuXjtMZ1f4fDe"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	// Log all details for debugging
	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	// Verify token transfers
	assert.Len(t, detail.TokenTransfers, 1)
	tokenTransfer := detail.TokenTransfers[0]
	assert.Equal(t, "Bqom9JXLXkPsuKV8cksQiVPGbJv72fmb1gT6K58Lpump", tokenTransfer.Contract.String())
	assert.Equal(t, domain.NewStrAddress("3NgUNnz3dbhNayWvtFwTxS3fZsxR9gqAWxcymbWb2zcS"), tokenTransfer.From)
	assert.Equal(t, domain.NewStrAddress("BgRHBEydZnfwer561tWf7hovrXmxZ4z6n9h9tzqroAte"), tokenTransfer.To)
	assert.Equal(t, big.NewInt(5927270294605), tokenTransfer.Amount)

	// Verify internal transfers
	assert.Len(t, detail.InternalTransfers, 3)

	// Pump fun transfer
	pumpFunTransfer := detail.InternalTransfers[0]
	assert.Equal(t, domain.NewStrAddress("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P"), pumpFunTransfer.From)
	assert.Equal(t, domain.NewStrAddress("3NgUNnz3dbhNayWvtFwTxS3fZsxR9gqAWxcymbWb2zcS"), pumpFunTransfer.To)
	assert.Equal(t, big.NewInt(358732920), pumpFunTransfer.Amount)

	// Fee transfer 1
	feeTransfer1 := detail.InternalTransfers[1]
	assert.Equal(t, domain.NewStrAddress("3NgUNnz3dbhNayWvtFwTxS3fZsxR9gqAWxcymbWb2zcS"), feeTransfer1.From)
	assert.Equal(t, domain.NewStrAddress("7HeD6sLLqAnKVRuSfc1Ko3BSPMNKWgGTiWLKXJF31vKM"), feeTransfer1.To)
	assert.Equal(t, big.NewInt(3228596), feeTransfer1.Amount)

	// Fee transfer 2
	feeTransfer2 := detail.InternalTransfers[2]
	assert.Equal(t, domain.NewStrAddress("3NgUNnz3dbhNayWvtFwTxS3fZsxR9gqAWxcymbWb2zcS"), feeTransfer2.From)
	assert.Equal(t, domain.NewStrAddress("nextBLoCkPMgmG8ZgJtABeScP35qLa2AMCNKntAP7Xc"), feeTransfer2.To)
	assert.Equal(t, big.NewInt(1200000), feeTransfer2.Amount)
}

func TestUSDCSwapTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Solana)

	txHash := "3emqMsp4WgSZYv6cQZQVwqgFEbNPm1XCY7uCJJgqxFWwCnnBAJ6sxuusSAuBPNPEk94bqhGZFG37UMhR6aReivvL"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	// Verify token transfers
	assert.Len(t, detail.TokenTransfers, 3)

	// First token transfer - BALL token
	assert.Equal(t, "BALLrveijbhu42QaS2XW1pRBYfMji73bGeYJghUvQs6y", detail.TokenTransfers[0].Contract.String())
	assert.Equal(t, domain.NewStrAddress("C93mwmaUmZ2cgf2Bed2GdGGQvtEFGWAMdXP7gqimASqR"), detail.TokenTransfers[0].From)
	assert.Equal(t, domain.NewStrAddress("2dukQMrp7aR1Kkk2zmYurWHEcQxaYpZyUDV1Pm7ETFgz"), detail.TokenTransfers[0].To)
	assert.Equal(t, big.NewInt(64763668451), detail.TokenTransfers[0].Amount)

	// Second token transfer - USDC
	assert.Equal(t, "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", detail.TokenTransfers[1].Contract.String())
	assert.Equal(t, domain.NewStrAddress("2dukQMrp7aR1Kkk2zmYurWHEcQxaYpZyUDV1Pm7ETFgz"), detail.TokenTransfers[1].From)
	assert.Equal(t, domain.NewStrAddress("GsTx64KUeHpAsZcTtLSyAuZTrtFHqWtJbVc36XZtiZpr"), detail.TokenTransfers[1].To)
	assert.Equal(t, big.NewInt(100), detail.TokenTransfers[1].Amount)

	// Third token transfer - USDC
	assert.Equal(t, "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", detail.TokenTransfers[2].Contract.String())
	assert.Equal(t, domain.NewStrAddress("2dukQMrp7aR1Kkk2zmYurWHEcQxaYpZyUDV1Pm7ETFgz"), detail.TokenTransfers[2].From)
	assert.Equal(t, domain.NewStrAddress("6YJWm3nhHXGPvgAHErWcNmqPQtSSHZhvtmE4U9Adwb3g"), detail.TokenTransfers[2].To)
	assert.Equal(t, big.NewInt(99900), detail.TokenTransfers[2].Amount)
}
