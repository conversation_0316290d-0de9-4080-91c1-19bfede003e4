package solana

import (
	"context"

	solanago "github.com/gagliardetto/solana-go"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/kryptogo/kg-wallet-backend/domain"
)

type IClient interface {
	domain.ChainClient
	BroadcastTransaction(ctx context.Context, tx *solanago.Transaction) (string, error)
}

type clientImpl struct {
	chain domain.Chain
}

func GetClient(chain domain.Chain) IClient {
	return &clientImpl{chain: chain}
}

func GetRawClient() *rpc.Client {
	return rpc.New(rpc.MainNetBeta.RPC)
}
