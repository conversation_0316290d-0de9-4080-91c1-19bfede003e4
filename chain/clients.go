package chain

import (
	"errors"

	"github.com/kryptogo/kg-wallet-backend/chain/bitcoin"
	"github.com/kryptogo/kg-wallet-backend/chain/evm"
	"github.com/kryptogo/kg-wallet-backend/chain/solana"
	"github.com/kryptogo/kg-wallet-backend/chain/tron"
	"github.com/kryptogo/kg-wallet-backend/domain"
)

func GetChainClient(chain domain.Chain) (domain.ChainClient, error) {
	if chain.IsTVM() {
		return tron.GetClient(chain), nil
	}
	if chain.IsEVM() {
		return evm.GetClient(chain), nil
	}

	switch chain {
	case domain.Bitcoin:
		return bitcoin.GetClient(chain), nil
	case domain.Solana:
		return solana.GetClient(chain), nil
	default:
		return nil, errors.New("chain not supported")
	}
}
