package tron

import (
	"context"
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
)

var ErrAccountNotActivated = fmt.Erro<PERSON>("account not activated")

func (c *clientImpl) GetOwnerAddress(ctx context.Context, addr domain.TronAddress) (domain.TronAddress, error) {
	// Prepare the payload for the HTTP request
	payload := map[string]interface{}{
		"address": addr.String(),
		"visible": true,
	}

	// Define the response structure
	var response struct {
		Address         string `json:"address"`
		OwnerPermission struct {
			Threshold int `json:"threshold"`
			Keys      []struct {
				Address string `json:"address"`
				Weight  int    `json:"weight"`
			} `json:"keys"`
		} `json:"owner_permission"`
	}

	// Make the HTTP request
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&response).
		Post("/wallet/getaccount")
	if err != nil {
		return domain.TronAddress{}, fmt.<PERSON>rf("HTTP call failed: %w", err)
	}

	if resp.<PERSON>r() {
		return domain.TronAddress{}, fmt.<PERSON>rf("failed to get account, response: %v", string(resp.Body()))
	}
	if response.Address == "" {
		return domain.TronAddress{}, ErrAccountNotActivated
	}

	// Check if owner permission exists and has keys
	if len(response.OwnerPermission.Keys) == 1 {
		ownerAddress := response.OwnerPermission.Keys[0].Address
		if ownerAddress != "" {
			return domain.NewTronAddress(ownerAddress), nil
		}
	}

	// Check if it's a multi sig address
	if len(response.OwnerPermission.Keys) > 1 {
		for _, key := range response.OwnerPermission.Keys {
			if key.Weight >= response.OwnerPermission.Threshold {
				return domain.NewTronAddress(key.Address), nil
			}
		}
		return domain.TronAddress{}, fmt.Errorf("multi sig address not supported")
	}

	return domain.TronAddress{}, fmt.Errorf("no owner key found")
}
