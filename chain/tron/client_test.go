package tron

import (
	"context"
	"math/big"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestNativeBalance(t *testing.T) {
	t.<PERSON>llel()
	client := GetClient(domain.Shasta)
	address := domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	balance, err := client.NativeBalance(context.Background(), address)
	assert.NoError(t, err)
	assert.NotNil(t, balance)
	assert.True(t, balance.Cmp(big.NewInt(100)) >= 0, "Balance should be at least 0.0001 TRX")
	t.Logf("Balance: %s", balance.String())
}

func TestTokenBalance(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Shasta)
	address := domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	balance, err := client.TokenBalance(context.Background(), address, "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs")
	assert.NoError(t, err)
	assert.NotNil(t, balance)
	assert.True(t, balance.Cmp(big.NewInt(100)) >= 0, "Balance should be at least 0.0001 USDT")
	t.Logf("Balance: %s", balance.String())
}

func TestBlockNumber(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Shasta)
	blockNumber, err := client.BlockNumber(context.Background())
	assert.NoError(t, err)
	assert.True(t, blockNumber > 48473086, "Block number should be greater than 48473086. Captured at 2024-10-16.")
	t.Logf("Block number: %v", blockNumber)
}

func TestGetTransactionStatus(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)

	testCases := []struct {
		name     string
		txHash   string
		expected domain.TransactionStatus
	}{
		{
			name:     "Successful Transaction",
			txHash:   "def820e9e6ca7857130e5815030c48de2e6f356d898fdbcf54acd8fe6d9ce9c1",
			expected: domain.TransactionStatusSuccess,
		},
		{
			name:     "Failed Transaction",
			txHash:   "427e928bb3b18ae35ddfb414e7e6ef4156480545455217ed9f4fb8415a88d925",
			expected: domain.TransactionStatusFailed,
		},
		{
			name:     "Not exist Transaction",
			txHash:   "0x427e928bb3b18ae35ddfb414e7e6ef4156480545455217ed9f4fb8415a88d924",
			expected: domain.TransactionStatusUnknown,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			status, err := client.GetTransactionStatus(context.Background(), tc.txHash)
			if tc.expected == domain.TransactionStatusUnknown {
				t.Logf("err: %v", err)
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expected, status)
			}
		})
	}
}

func TestWaitUntilTransactionConfirmed(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)

	// Define test cases with known transaction hashes
	testCases := []struct {
		name        string
		txHash      string
		expectError error
		expected    domain.TransactionStatus
	}{
		{
			name:     "Successful Transaction",
			txHash:   "0xdef820e9e6ca7857130e5815030c48de2e6f356d898fdbcf54acd8fe6d9ce9c1",
			expected: domain.TransactionStatusSuccess,
		},
		{
			name:     "Failed Transaction",
			txHash:   "0x427e928bb3b18ae35ddfb414e7e6ef4156480545455217ed9f4fb8415a88d925",
			expected: domain.TransactionStatusFailed,
		},
		{
			name:        "Not exist Transaction",
			txHash:      "0x427e928bb3b18ae35ddfb414e7e6ef4156480545455217ed9f4fb8415a88d924",
			expectError: context.DeadlineExceeded,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()

			status, err := client.WaitUntilTransactionConfirmed(ctx, tc.txHash)
			if tc.expectError != nil {
				assert.Error(t, err)
				assert.Equal(t, tc.expectError, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expected, status)
			}
		})
	}
}

func TestUsdtTransferTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)

	txHash := "0x3324c3eac2816eed0ede2b38ecb7a62189c053e65430714739f794d601fe95ec"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Data: %s", detail.Data)
	t.Logf("Method ID: %s", detail.MethodID)
	t.Logf("Value: %s", detail.Value.String())
	t.Logf("Gas Price: %s", detail.GasPrice.String())
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(66143644), detail.BlockNum)
	assert.Equal(t, big.NewInt(1), detail.GasPrice)
	assert.Equal(t, big.NewInt(401280), detail.GasUsed)
	assert.Equal(t, "0xa9059cbb0000000000000000000000002f6d11a081a41a4d981fc5caf83065f7e4db52da000000000000000000000000000000000000000000000000000000001dd26eb6", detail.Data)
	assert.Equal(t, "0xa9059cbb", detail.MethodID)
	assert.Equal(t, time.Unix(1729079850, 0), detail.Timestamp)

	assert.Equal(t, domain.NewTronAddress("THaxMpsK32gyzvakTpYRraMKiLqm6R9td4"), detail.From)
	assert.Equal(t, domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"), detail.To)
	assert.Equal(t, big.NewInt(0), detail.Value)

	assert.Equal(t, 0, len(detail.InternalTransfers))

	assert.Equal(t, 1, len(detail.TokenTransfers))
	assert.Equal(t, domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"), detail.TokenTransfers[0].Contract)
	assert.Equal(t, domain.NewTronAddress("THaxMpsK32gyzvakTpYRraMKiLqm6R9td4"), detail.TokenTransfers[0].From)
	assert.Equal(t, domain.NewTronAddress("TEHyRK3g8gMZnoKxjW62CZMbowZ2AeDdLp"), detail.TokenTransfers[0].To)
	assert.Equal(t, big.NewInt(500330166), detail.TokenTransfers[0].Amount)
}

func TestTrxTransferTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)
	txHash := "0xf4c8182035c3645cdf2d62e5f731d069f29557321b7e58a5127747c40b4f902b"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	t.Logf("Transaction Hash: %s", detail.Hash)
	t.Logf("Block Number: %d", detail.BlockNum)
	t.Logf("From: %s", detail.From)
	t.Logf("To: %s", detail.To)
	t.Logf("Data: %s", detail.Data)
	t.Logf("Method ID: %s", detail.MethodID)
	t.Logf("Value: %s", detail.Value.String())
	t.Logf("Gas Price: %s", detail.GasPrice.String())
	t.Logf("Gas Used: %s", detail.GasUsed.String())
	t.Logf("Timestamp: %v", detail.Timestamp.Unix())

	assert.Equal(t, txHash, detail.Hash)
	assert.Equal(t, uint32(66142342), detail.BlockNum)
	assert.Equal(t, big.NewInt(1), detail.GasPrice)
	assert.Equal(t, big.NewInt(268000), detail.GasUsed)
	assert.Equal(t, "0x", detail.Data)
	assert.Equal(t, "", detail.MethodID)
	assert.Equal(t, time.Unix(1729075944, 0), detail.Timestamp)

	assert.Equal(t, domain.NewTronAddress("TRtS87Qvb4EHPC4QnzM7NUTK4UGzZ12345"), detail.From)
	assert.Equal(t, domain.NewTronAddress("TCwgq6NNJWCGzBCgbnUkw6q6N6jejnNCMV"), detail.To)
	assert.Equal(t, big.NewInt(23400000), detail.Value)

	assert.Equal(t, 0, len(detail.InternalTransfers))
	assert.Equal(t, 0, len(detail.TokenTransfers))
}

func TestFetchTxsInRange_BadRange(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)

	// Define the block range for the test
	fromBlock := uint64(66142342) // Replace with a real block number
	toBlock := uint64(66142340)   // Replace with a real block number

	_, transactions, err := client.FetchTxsInRange(context.Background(), domain.Tron, fromBlock, toBlock)

	assert.NoError(t, err)
	assert.Empty(t, transactions)
}

func TestFetchTxsInRange(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)

	// Define the block range for the test
	fromBlock := uint64(64988944) // Replace with a real block number
	toBlock := uint64(64988947)   // Replace with a real block number

	lastBlockNo, transactions, err := client.FetchTxsInRange(context.Background(), domain.Tron, fromBlock, toBlock)

	testCases := []struct {
		name string
		tx   domain.TransactionWithAddresses
	}{
		{
			name: "delegate resource",
			tx: domain.TransactionWithAddresses{
				Chain: domain.Tron,
				Hash:  "0x1cae6c339cd3833a2e61a9c3c7e324c99a1076bdcea382dd78ce20454e79bb0b",
				Addresses: []domain.Address{
					domain.NewTronAddress("TNPdqto8HiuMzoG7Vv9wyyYhWzCojLeHAF"),
				},
			},
		},
		{
			name: "usdt transfer",
			tx: domain.TransactionWithAddresses{
				Chain: domain.Tron,
				Hash:  "0x1c6a20cb8d0f12157eacd998254af5112016282b003ec65a9763a003439cd14f",
				Addresses: []domain.Address{
					domain.NewTronAddress("TDqSquXBgUCLYvYC4XZgrprLK589dkhSCf"),
					domain.NewTronAddress("TNH6rW5794pZaBFwzdactNUthRZFgTYw5Q"),
				},
			},
		},
		{
			name: "trx transfer",
			tx: domain.TransactionWithAddresses{
				Chain: domain.Tron,
				Hash:  "0x82457bdaa7f71a1f3e27bade075d2b617695fb114312eba1fab074cc7b97b109",
				Addresses: []domain.Address{
					domain.NewTronAddress("THMdcLeg99e6yoBcYeTFURkQJJnRAmvXM5"),
					domain.NewTronAddress("TTvDaNWWGRWUa4nEwnaM88bPvPiF4RuR4T"),
				},
			},
		},
		{
			name: "smart contract call",
			tx: domain.TransactionWithAddresses{
				Chain: domain.Tron,
				Hash:  "0x6b22e8fbb1705c3850fd7acd414a4a00fcc0d2b4f3be29b2fddd4ea06c06fa01",
				Addresses: []domain.Address{
					domain.NewTronAddress("TG9nDZMUtC4LBmrWSdNXNi8xrKzXTMMSKT"),
					domain.NewTronAddress("TM9UumDkpeR6kYWveSpqJD5hJownQGzgUv"),
					domain.NewTronAddress("TTfvyrAz86hbZk5iDpKD78pqLGgi8C7AAw"),
				},
			},
		},
		{
			name: "smart contract deploy",
			tx: domain.TransactionWithAddresses{
				Chain: domain.Tron,
				Hash:  "0x0ce5a7b5604af3a3e902b214cf75b30a469a299c8069eaf4eef034bcaed29cd8",
				Addresses: []domain.Address{
					domain.NewTronAddress("TU3rMHyWHzeEJBReWLzHko728hbz6MtfNg"),
				},
			},
		},
		{
			name: "with internal tx",
			tx: domain.TransactionWithAddresses{
				Chain: domain.Tron,
				Hash:  "0x1e248c314deaaea65387c0c07d07740d122a63938855768f693fa90507ed5834",
				Addresses: []domain.Address{
					domain.NewTronAddress("TFVisXFaijZfeyeSjCEVkHfex7HGdTxzF9"),
					domain.NewTronAddress("TNUC9Qb1rRpS5CbWLmNMxXBjyFoydXjWFR"),
					domain.NewTronAddress("TSUUVjysXV8YqHytSNjfkNXnnB49QDvZpx"),
					domain.NewTronAddress("TTsmjJoxhKnq4X5LfdUT99N13ANuU7UTvC"),
				},
			},
		},
	}

	assert.NoError(t, err)
	assert.NotNil(t, transactions)
	assert.NotEmpty(t, transactions, "Expected to find transactions in the specified block range")

	assert.Equal(t, 1588, len(transactions))
	assert.Equal(t, toBlock, lastBlockNo)

	for _, tc := range testCases {
		found := false
		for _, tx := range transactions {
			if tx.Hash == tc.tx.Hash {
				found = true
				assert.Equalf(t, tc.tx.Addresses, tx.Addresses, "tx hash: %s", tc.tx.Hash)
				assert.Equalf(t, tc.tx.Chain, tx.Chain, "tx hash: %s", tc.tx.Hash)
				break
			}
		}
		assert.True(t, found, "Transaction %s not found", tc.name)
	}
}

func TestFetchTxsInRangeLatest(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	client := GetClient(domain.Tron)

	// Define the block range for the test
	fromBlock, err := client.BlockNumber(ctx)
	assert.NoError(t, err)

	lastBlockNo, transactions, err := client.FetchTxsInRange(ctx, domain.Tron, fromBlock, fromBlock)

	assert.NoError(t, err)
	assert.NotNil(t, transactions)
	assert.NotEmpty(t, transactions, "Expected to find transactions in the specified block range")

	assert.Greater(t, len(transactions), 1)
	assert.Equal(t, fromBlock, lastBlockNo)
	assert.Greater(t, len((*transactions[0]).Addresses), 0)
}

func TestInternalTransferTransactionDetail(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)
	txHash := "0x3884cbfd98765063442c215695377f276c6ed12a574aec224d769db6f7bef66c"

	detail, err := client.TransactionDetail(context.Background(), txHash)
	assert.NoError(t, err)
	assert.NotNil(t, detail)

	// Verify internal transfers
	assert.Equal(t, 2, len(detail.InternalTransfers))

	// First internal transfer
	assert.Equal(t, domain.NewTronAddress("TCxUypP7WpQYsCHEj6k3eXB2mL5ToFf1R7"), detail.InternalTransfers[0].From)
	assert.Equal(t, domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"), detail.InternalTransfers[0].To)
	assert.Equal(t, big.NewInt(4000000), detail.InternalTransfers[0].Amount)

	// Second internal transfer
	assert.Equal(t, domain.NewTronAddress("TCxUypP7WpQYsCHEj6k3eXB2mL5ToFf1R7"), detail.InternalTransfers[1].From)
	assert.Equal(t, domain.NewTronAddress("TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb"), detail.InternalTransfers[1].To)
	assert.Equal(t, big.NewInt(791538), detail.InternalTransfers[1].Amount)

	// Verify fee calculation
	assert.Equal(t, big.NewInt(1), detail.GasPrice)
	assert.Equal(t, big.NewInt(3483690), detail.GasUsed) // energy_fee from the example
}
