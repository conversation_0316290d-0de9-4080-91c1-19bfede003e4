package tron

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strings"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/gotron-sdk/pkg/address"
	"github.com/kryptogo/gotron-sdk/pkg/proto/core"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"
)

type TransactionExtension struct {
	ConstantResult [][]byte
	EnergyUsed     int64
	Transaction    *Transaction
}

type Transaction struct {
	RawData    RawData  `json:"raw_data"`
	Signature  []string `json:"signature"`
	Visible    bool     `json:"visible"`
	TxID       string   `json:"txid"`
	RawDataHex string   `json:"raw_data_hex"`
}

type RawData struct {
	Contract      []Contract `json:"contract"`
	RefBlockBytes string     `json:"ref_block_bytes"`
	RefBlockHash  string     `json:"ref_block_hash"`
	Expiration    int64      `json:"expiration"`
	FeeLimit      int64      `json:"fee_limit"`
	Timestamp     int64      `json:"timestamp"`
}

type Contract struct {
	Parameter Parameter `json:"parameter"`
	Type      string    `json:"type"`
}

type Parameter struct {
	Value   Value  `json:"value"`
	TypeURL string `json:"type_url"`
}

type Value struct {
	Data            string `json:"data,omitempty"`
	OwnerAddress    string `json:"owner_address,omitempty"`
	ToAddress       string `json:"to_address,omitempty"`
	ContractAddress string `json:"contract_address,omitempty"`
	CallValue       int64  `json:"call_value,omitempty"`
	CallTokenValue  int64  `json:"call_token_value,omitempty"`
	TokenID         int64  `json:"token_id,omitempty"`
	Amount          int64  `json:"amount,omitempty"`
}

// ToProto transforms a Transaction to tron's core.Transaction.
func (t *Transaction) ToProto() *core.Transaction {
	tx := &core.Transaction{
		RawData: &core.TransactionRaw{
			RefBlockBytes: decodeHex(t.RawData.RefBlockBytes),
			RefBlockHash:  decodeHex(t.RawData.RefBlockHash),
			Expiration:    t.RawData.Expiration,
			FeeLimit:      t.RawData.FeeLimit,
			Timestamp:     t.RawData.Timestamp,
			Contract:      make([]*core.Transaction_Contract, len(t.RawData.Contract)),
		},
		Signature: make([][]byte, len(t.Signature)),
	}
	for i, contract := range t.RawData.Contract {
		switch contract.Type {
		case "TriggerSmartContract":
			owner := domain.NewTronAddress(contract.Parameter.Value.OwnerAddress)
			contractAddress := domain.NewTronAddress(contract.Parameter.Value.ContractAddress)
			sc := &core.TriggerSmartContract{
				OwnerAddress:    decodeHex(owner.Hex()),
				ContractAddress: decodeHex(contractAddress.Hex()),
				Data:            decodeHex(contract.Parameter.Value.Data),
				CallValue:       contract.Parameter.Value.CallValue,
				CallTokenValue:  contract.Parameter.Value.CallTokenValue,
				TokenId:         contract.Parameter.Value.TokenID,
			}
			param, err := anypb.New(sc)
			if err != nil {
				fmt.Printf("Failed to create anypb: %v\n", err)
				return nil
			}
			pbContract := &core.Transaction_Contract{
				Type:      core.Transaction_Contract_ContractType(core.Transaction_Contract_ContractType_value[contract.Type]),
				Parameter: param,
			}
			tx.RawData.Contract[i] = pbContract
		case "TransferContract":
			owner := domain.NewTronAddress(contract.Parameter.Value.OwnerAddress)
			to := domain.NewTronAddress(contract.Parameter.Value.ToAddress)
			transfer := &core.TransferContract{
				OwnerAddress: decodeHex(owner.Hex()),
				ToAddress:    decodeHex(to.Hex()),
				Amount:       contract.Parameter.Value.Amount,
			}
			param, err := anypb.New(transfer)
			if err != nil {
				fmt.Printf("Failed to create anypb: %v\n", err)
				return nil
			}
			pbContract := &core.Transaction_Contract{
				Type:      core.Transaction_Contract_ContractType(core.Transaction_Contract_ContractType_value[contract.Type]),
				Parameter: param,
			}
			tx.RawData.Contract[i] = pbContract
		default:
			return nil
		}
	}
	for i, signature := range t.Signature {
		tx.Signature[i] = decodeHex(signature)
	}
	return tx
}

// SignerAddress returns the address of the signer of the transaction
func (t *Transaction) SignerAddress() (address.Address, error) {
	txProto := t.ToProto()
	if txProto == nil {
		return nil, fmt.Errorf("failed to convert tx to proto")
	}
	txBytes, err := proto.Marshal(txProto.GetRawData())
	if err != nil {
		return nil, fmt.Errorf("failed to marshal raw data: %v", err)
	}
	h256h := sha256.New()
	h256h.Write(txBytes)
	hashBytes := h256h.Sum(nil)
	if len(txProto.Signature) == 0 {
		return nil, fmt.Errorf("no signatures found")
	}
	sig := txProto.Signature[0]
	if len(sig) != 65 {
		return nil, fmt.Errorf("signature should be 65 bytes")
	}
	if sig[64] == 27 || sig[64] == 28 {
		// In original tron signature the V value may not be added 27
		sig[64] -= 27
	}
	rpk, err := crypto.Ecrecover(hashBytes, sig)
	if err != nil {
		return nil, fmt.Errorf("failed to recover public key: %v", err)
	}
	pubKey, err := crypto.UnmarshalPubkey(rpk)
	if err != nil || pubKey == nil {
		return nil, fmt.Errorf("failed to decompress public key: %v", err)
	}
	tronAddr := address.PubkeyToAddress(*pubKey)
	return tronAddr, nil
}

// Helper function to decode hex strings to byte slices
func decodeHex(s string) []byte {
	if strings.HasPrefix(s, "0x") {
		s = s[2:]
		s = "41" + s
	}
	bytes, err := hex.DecodeString(s)
	if err != nil {
		fmt.Printf("Failed to decode hex string: %s, error: %v\n", s, err)
		return nil
	}
	return bytes
}

// GetTxHash get tron tx hash
func GetTxHash(tx *core.Transaction) (string, error) {
	rawData, err := proto.Marshal(tx.GetRawData())
	if err != nil {
		return "", err
	}
	h256h := sha256.New()
	h256h.Write(rawData)
	return hex.EncodeToString(h256h.Sum(nil)), nil
}
