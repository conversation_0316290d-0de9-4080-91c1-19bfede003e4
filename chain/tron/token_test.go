package tron

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestToRawTokenAmount(t *testing.T) {
	t.<PERSON>llel()
	client := GetClient(domain.Tron)
	t.Run("USDT decimal", func(t *testing.T) {
		amount, err := client.ToRawTokenAmount(context.Background(),
			domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"),
			"1536")
		assert.NoError(t, err)
		assert.Equal(t, "1536000000", amount.String())
	})

	t.Run("TRX decimal", func(t *testing.T) {
		amount, err := client.ToRawTokenAmount(context.Background(),
			domain.NewTronAddress("TCFLL5dx5ZJdKnWuesXxi1VPwjLVmWZZy9"),
			"1536")
		assert.NoError(t, err)
		assert.Equal(t, "1536000000000000000000", amount.String())
	})
}
