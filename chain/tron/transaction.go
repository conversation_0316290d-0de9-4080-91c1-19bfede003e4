package tron

import (
	"context"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/jpillora/backoff"
	"github.com/kryptogo/gotron-sdk/pkg/proto/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"google.golang.org/protobuf/proto"
)

func (c *clientImpl) BroadcastTransactionHex(ctx context.Context, txHex string) (string, error) {
	payload := map[string]interface{}{
		"transaction": txHex,
	}

	b := &backoff.Backoff{
		Min:    time.Second,
		Max:    2 * time.Second,
		Factor: 2,
		Jitter: false,
	}
	var response struct {
		Result      bool        `json:"result"`
		Code        string      `json:"code"`
		TxID        string      `json:"txid"`
		Message     string      `json:"message,omitempty"`
		Transaction interface{} `json:"transaction,omitempty"`
	}
	var lastErr error
	for retries := 0; retries < 3; retries++ {
		resp, err := c.httpClient.R().
			SetContext(ctx).
			SetBody(payload).
			SetResult(&response).
			Post("/wallet/broadcasthex")

		if err != nil {
			lastErr = fmt.Errorf("HTTP call failed: %w", err)
		} else {
			kglog.DebugWithDataCtx(ctx, "Broadcast transaction hex response", response)

			if !resp.IsError() && response.Result {
				return response.TxID, nil
			}

			if response.Message != "" {
				lastErr = fmt.Errorf("failed to broadcast transaction, error: %s", response.Message)
			} else {
				lastErr = fmt.Errorf("failed to broadcast transaction, response: %v", response)
			}
		}
		if retries == 2 {
			return "", lastErr
		}

		select {
		case <-ctx.Done():
			return "", ctx.Err()
		case <-time.After(b.Duration()):
		}
	}

	return "", lastErr
}

// serializeTransaction serializes the signed transaction into a raw transaction string
func serializeTransaction(tx *core.Transaction) (string, error) {
	// Serialize the transaction using protobuf
	rawTx, err := proto.Marshal(tx)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(rawTx), nil
}

func (c *clientImpl) BroadcastTransaction(ctx context.Context, transaction *core.Transaction) (string, error) {
	rawTx, err := serializeTransaction(transaction)
	if err != nil {
		return "", err
	}

	txID, err := c.BroadcastTransactionHex(ctx, rawTx)
	if err != nil {
		return "", err
	}
	return txID, nil
}

func (c *clientImpl) BroadcastRawTransaction(ctx context.Context, rawTx string) (string, error) {
	return c.BroadcastTransactionHex(ctx, rawTx)
}
