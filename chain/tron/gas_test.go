package tron

import (
	"context"
	"math/big"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestGetEnergyFee(t *testing.T) {
	t.<PERSON>llel()
	client := GetClient(domain.Tron)
	chainParams, err := client.GetChainParameters(context.Background())
	assert.Nil(t, err)
	assert.NotNil(t, chainParams)
	t.Logf("Energy fee: %d", chainParams.EnergyFee)
	assert.True(t, chainParams.EnergyFee == 210, "energy fee should be 210")
}

func TestGetAccountResource(t *testing.T) {
	t.<PERSON>llel()
	client := GetClient(domain.Tron)
	resource, err := client.GetAccountResource(context.Background(), domain.NewTronAddress("TDSEUjDdfKtEAoWL387hi433T4Fanwip9Z"))
	assert.Nil(t, err)
	t.Logf("Resource: %+v", resource)
	assert.True(t, resource.FreeNetLimit > 0, "free net limit should be greater than 0")
}

func TestWaitUntilHavingEnoughEnergy(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)
	err := client.WaitUntilHavingEnoughEnergy(context.Background(), domain.NewTronAddress("TDSEUjDdfKtEAoWL387hi433T4Fanwip9Z"), 0)
	assert.Nil(t, err)

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	err = client.WaitUntilHavingEnoughEnergy(ctx, domain.NewTronAddress("TDSEUjDdfKtEAoWL387hi433T4Fanwip9Z"), ********)
	assert.Error(t, err)
	assert.Equal(t, context.DeadlineExceeded, err)
}

func TestEstimateTrxTransferGas(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)
	fromWithFreeNet := domain.NewTronAddress("TVZG1rmshiLcwYSczDxGB4mr2eLLjPJiAt")
	fromWithoutFreeNet := domain.NewTronAddress("TDqSquXBgUCLYvYC4XZgrprLK589dkhSCf") // binance-hot-7

	inactivatedAddr := domain.NewTronAddress("TDqJKbkhbxhuSMHzN1syeEiL76S9MMzXSw") // beans-inactivated
	activatedAddr := domain.NewTronAddress("TRSXRWudzfzY4jH7AaMowdMNUXDkHisbcd")

	t.Run("from with free net", func(t *testing.T) {
		gas1, err := client.EstimateTrxTransferGas(context.Background(), fromWithFreeNet, inactivatedAddr)
		assert.Nil(t, err)
		t.Logf("Gas 1: %+v", gas1)
		assert.True(t, gas1.TotalCost > 0)
		assert.Equal(t, int64(0), gas1.EnergyToRent)
		assert.Equal(t, gas1.TxSize, int64(268))

		gas2, err := client.EstimateTrxTransferGas(context.Background(), fromWithFreeNet, activatedAddr)
		assert.Nil(t, err)
		t.Logf("Gas 2: %+v", gas2)
		assert.Equal(t, gas2.TxSize, int64(268))

		assert.Equal(t, int64(1000000), gas1.TotalCost-gas2.TotalCost)
	})

	t.Run("from without free net", func(t *testing.T) {
		gas1, err := client.EstimateTrxTransferGas(context.Background(), fromWithoutFreeNet, inactivatedAddr)
		assert.Nil(t, err)
		t.Logf("Gas 1: %+v", gas1)
		assert.True(t, gas1.TotalCost > 0)
		assert.Equal(t, int64(0), gas1.EnergyToRent)
		assert.Equal(t, gas1.TxSize, int64(268))

		gas2, err := client.EstimateTrxTransferGas(context.Background(), fromWithoutFreeNet, activatedAddr)
		assert.Nil(t, err)
		t.Logf("Gas 2: %+v", gas2)
		assert.True(t, gas2.TotalCost > 50000)
		assert.Equal(t, gas2.TxSize, int64(268))

		assert.Equal(t, int64(1000000), gas1.TotalCost-gas2.TotalCost)
	})
}

func TestEstimateContractGas(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)
	fromWithNoEnergy := domain.NewTronAddress("TVZG1rmshiLcwYSczDxGB4mr2eLLjPJiAt")
	usdtContractAddress := domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t") // USDT contract address
	inactivatedAddr := domain.NewTronAddress("TDqJKbkhbxhuSMHzN1syeEiL76S9MMzXSw")     // beans-inactivated
	activatedAddr := domain.NewTronAddress("TRSXRWudzfzY4jH7AaMowdMNUXDkHisbcd")

	t.Run("estimate USDT transfer gas to inactivated address", func(t *testing.T) {
		amount := big.NewInt(1)
		params := []map[string]interface{}{
			{"address": inactivatedAddr.String()},
			{"uint256": amount.String()},
		}

		gas, err := client.EstimateContractGas(context.Background(), fromWithNoEnergy, usdtContractAddress, "transfer(address,uint256)", params)
		assert.Nil(t, err)
		t.Logf("Estimated Gas for USDT transfer 1: %+v", gas)
		assert.Greater(t, gas.EnergyUsed, int64(130000))
		assert.Equal(t, gas.EnergyUsed, gas.EnergyToRent)
		assert.Greater(t, gas.TotalCost, int64(25000000))
		assert.GreaterOrEqual(t, gas.TxSize, int64(330))
	})

	t.Run("estimate USDT transfer gas to activated address", func(t *testing.T) {
		amount := big.NewInt(100000) // 0.1 USDT
		params := []map[string]interface{}{
			{"address": activatedAddr.String()},
			{"uint256": amount.String()},
		}

		gas, err := client.EstimateContractGas(context.Background(), fromWithNoEnergy, usdtContractAddress, "transfer(address,uint256)", params)
		assert.Nil(t, err)
		t.Logf("Estimated Gas for USDT transfer 2: %+v", gas)
		assert.Greater(t, gas.EnergyUsed, int64(0))
		assert.Less(t, gas.EnergyUsed, int64(65000))
		assert.Equal(t, gas.EnergyUsed, gas.EnergyToRent)
		assert.Greater(t, gas.TotalCost, int64(13000000))
		assert.Less(t, gas.TotalCost, int64(25000000))
		assert.GreaterOrEqual(t, gas.TxSize, int64(330))
	})
}
