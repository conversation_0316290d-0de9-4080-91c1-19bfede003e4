package tron

import (
	"context"
	"fmt"
	"testing"

	"github.com/kryptogo/gotron-sdk/pkg/client"
	"github.com/kryptogo/gotron-sdk/pkg/proto/core"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func newGrpcClient(chain domain.Chain) (*client.GrpcClient, error) {
	var apiUrl string
	switch chain {
	case domain.Tron:
		apiUrl = "grpc.trongrid.io:50051"
	case domain.Shasta:
		apiUrl = "grpc.shasta.trongrid.io:50051"
	default:
		return nil, fmt.Errorf("invalid chain: %v", chain)
	}
	grpcClient := client.NewGrpcClient(apiUrl)
	err := grpcClient.Start(grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, err
	}
	return grpcClient, nil
}

// createTransaction creates a new transaction object
func createTransaction(grpcClient *client.GrpcClient, from, to string, amount int64) (*core.Transaction, error) {
	// Ensure the from address is activated and valid
	txExt, err := grpcClient.Transfer(from, to, amount)
	if err != nil {
		return nil, err
	}
	return txExt.Transaction, nil
}

func TestBroadcastRawTransaction(t *testing.T) {
	client := GetClient(domain.Shasta)
	grpcClient, err := newGrpcClient(domain.Shasta)
	if err != nil {
		t.Fatalf("Failed to create grpc client: %v", err)
	}

	// Example transaction details
	fromAddress := domain.NewTronAddress("TLYm7Ls8eY36bcwQqB1wiyGJ6z3tUMwNNN")
	toAddress := domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	amount := int64(1000000) // Amount in SUN (1 TRX = 1,000,000 SUN)

	// Create the transaction
	tx, err := createTransaction(grpcClient, fromAddress.String(), toAddress.String(), amount)
	if err != nil {
		t.Fatalf("Failed to create transaction: %v", err)
	}

	// Initialize TronWallet with a private key
	privateKey := "EEFD177A0C2BC825E7A30CCE741004E0DA1B0E9BC7EFC882F317266D29595E35"
	privKey, err := util.ParsePrivateKey(privateKey)
	if err != nil {
		t.Fatalf("Invalid private key: %v", err)
	}
	wallet := domain.NewTronWallet("", privKey)

	// Sign the transaction using TronWallet
	signedTx, err := wallet.SignTransaction(tx)
	if err != nil {
		t.Fatalf("Failed to sign transaction: %v", err)
	}

	t.Logf("Signed transaction: %v", signedTx)

	// Serialize the signed transaction
	txHex, err := serializeTransaction(signedTx)
	if err != nil {
		t.Fatalf("Failed to serialize transaction: %v", err)
	}

	t.Logf("Raw transaction: %s", txHex)

	// Broadcast the raw transaction
	txID, kgErr := client.BroadcastTransactionHex(context.Background(), txHex)
	if kgErr != nil {
		t.Fatalf("Failed to broadcast raw transaction: %v", kgErr)
	}

	t.Logf("Transaction ID: %s", txID)
}

func TestBroadcastTransaction(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Shasta)
	grpcClient, err := newGrpcClient(domain.Shasta)
	if err != nil {
		t.Fatalf("Failed to create grpc client: %v", err)
	}

	// Example transaction details
	fromAddress := domain.NewTronAddress("TLYm7Ls8eY36bcwQqB1wiyGJ6z3tUMwNNN")
	toAddress := domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	amount := int64(1000000) // Amount in SUN (1 TRX = 1,000,000 SUN)

	// Create the transaction
	tx, err := createTransaction(grpcClient, fromAddress.String(), toAddress.String(), amount)
	if err != nil {
		t.Fatalf("Failed to create transaction: %v", err)
	}

	// Initialize TronWallet with a private key
	privateKey := "EEFD177A0C2BC825E7A30CCE741004E0DA1B0E9BC7EFC882F317266D29595E35"
	privKey, err := util.ParsePrivateKey(privateKey)
	if err != nil {
		t.Fatalf("Invalid private key: %v", err)
	}
	wallet := domain.NewTronWallet("", privKey)

	// Sign the transaction using TronWallet
	signedTx, err := wallet.SignTransaction(tx)
	if err != nil {
		t.Fatalf("Failed to sign transaction: %v", err)
	}

	t.Logf("Signed transaction: %v", signedTx)

	// Broadcast the raw transaction
	txID, kgErr := client.BroadcastTransaction(context.Background(), signedTx)
	if kgErr != nil {
		t.Fatalf("Failed to broadcast raw transaction: %v", kgErr)
	}

	t.Logf("Transaction ID: %s", txID)
}
