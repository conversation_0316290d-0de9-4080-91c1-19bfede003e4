package tron

import (
	"context"
	"fmt"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/domain"
)

func (c *clientImpl) SupportedChains() []domain.Chain {
	return []domain.Chain{c.chain}
}

func isNotFoundError(err error) bool {
	errMsg := strings.ToLower(err.Error())
	return strings.Contains(errMsg, "revert opcode executed") ||
		strings.Contains(errMsg, "smart contract is not exist")
}

func (c *clientImpl) FetchMetadata(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
	if chain != c.chain {
		return nil, fmt.Errorf("unsupported chain")
	}

	address := domain.NewTronAddress(tokenID)
	decimals, err := c.GetTRC20Decimals(ctx, address)
	if err != nil {
		if isNotFoundError(err) {
			return nil, domain.ErrRecordNotFound
		}
		return nil, err
	}
	name, err := c.ReadContractString(ctx, tokenID, "name")
	if err != nil {
		if isNotFoundError(err) {
			return nil, domain.ErrRecordNotFound
		}
		return nil, err
	}
	symbol, err := c.ReadContractString(ctx, tokenID, "symbol")
	if err != nil {
		if isNotFoundError(err) {
			return nil, domain.ErrRecordNotFound
		}
		return nil, err
	}
	return &domain.TokenMetadata{
		Name:     name,
		Symbol:   symbol,
		Decimals: uint(decimals.Uint64()),
	}, nil
}
