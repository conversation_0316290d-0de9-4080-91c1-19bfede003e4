package tron

import (
	"context"
	"fmt"
	"math/big"
	"strings"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/shopspring/decimal"
)

func (c *clientImpl) GetTRC20Decimals(ctx context.Context, contractAddress domain.TronAddress) (*big.Int, error) {
	// Define the ABI for the decimals function
	const decimalsABI = `[{"constant":true,"inputs":[],"name":"decimals","outputs":[{"name":"","type":"uint8"}],"payable":false,"stateMutability":"view","type":"function"}]`

	// Parse the ABI
	tokenABI, err := abi.JSON(strings.NewReader(decimalsABI))
	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to parse ABI: %w", err)
	}

	// Pack the function call data
	data, err := tokenABI.Pack("decimals")
	if err != nil {
		return nil, fmt.Errorf("failed to pack data: %w", err)
	}

	// Prepare the eth_call payload
	payload := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_call",
		"params": []interface{}{
			map[string]interface{}{
				"to":   contractAddress.Hex(),
				"data": common.Bytes2Hex(data),
			},
			"latest",
		},
		"id": 1,
	}

	var response struct {
		Result string `json:"result"`
		Error  struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		} `json:"error,omitempty"`
	}
	resp, err := c.rpcClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&response).
		Post("")
	if err != nil {
		return nil, fmt.Errorf("RPC call failed: %w", err)
	}

	if resp.IsError() || response.Result == "" {
		if response.Error.Message != "" {
			return nil, fmt.Errorf("failed to get decimals, error: %s", response.Error.Message)
		}
		return nil, fmt.Errorf("failed to get decimals, response: %v", response)
	}

	// Convert the result from hex to big.Int
	decimals := new(big.Int)
	decimals.SetString(response.Result[2:], 16)

	return decimals, nil
}

func (c *clientImpl) ToRawTokenAmount(ctx context.Context, token domain.TronAddress, amount string) (*big.Int, error) {
	// Fetch the token decimals using the JSON-RPC client
	tokenDecimals, err := c.GetTRC20Decimals(ctx, token)
	if err != nil {
		return nil, fmt.Errorf("failed to get token decimals: %v", err)
	}

	// Parse the amount string into a decimal
	amountDec, err := decimal.NewFromString(amount)
	if err != nil {
		return nil, fmt.Errorf("failed to parse amount: %v", err)
	}

	// Calculate the scale factor (10^decimals)
	scale := decimal.NewFromInt(10).Pow(decimal.NewFromBigInt(tokenDecimals, 0))

	// Multiply the amount by the scale factor to get the raw amount
	rawAmount := amountDec.Mul(scale)

	// Convert the result to a big.Int
	return rawAmount.BigInt(), nil
}
