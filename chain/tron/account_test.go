package tron

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestGetOwnerAddress(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)

	testCases := []struct {
		name          string
		address       string
		expectError   bool
		expectAddress string
	}{
		{
			name:          "Valid address with owner",
			address:       "TZ4UXDV5ZhNW7fb2AMSbgfAEZ7hWsnYS2g",
			expectError:   false,
			expectAddress: "TZ4UXDV5ZhNW7fb2AMSbgfAEZ7hWsnYS2g",
		},
		{
			name:        "Invalid address",
			address:     "InvalidAddress",
			expectError: true,
		},
		{
			name:        "Multi sig address",
			address:     "TYPLXWeYnUNXvwDFPsMhvbrWtrnRZ7XBYh",
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ownerAddr, err := client.GetOwnerAddress(context.Background(), domain.NewTronAddress(tc.address))

			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectAddress, ownerAddr.String())
			}
		})
	}

	// address not activated
	_, err := client.GetOwnerAddress(context.Background(), domain.NewTronAddress("TKjKQSDK5fEN1rYCcJZmAGTECiJyfS9Vj5"))
	assert.Equal(t, ErrAccountNotActivated, err)
}
