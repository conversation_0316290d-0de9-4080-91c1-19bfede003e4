package tron

import (
	"context"
	"math/big"
	"time"

	troncore "github.com/kryptogo/gotron-sdk/pkg/proto/core"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

type IClient interface {
	GetChainParameters(ctx context.Context) (*chainParameters, error)
	EstimateTrxTransferGas(ctx context.Context, from, to domain.TronAddress) (*domain.TronGasInfo, error)
	EstimateContractGas(ctx context.Context, from, contractAddress domain.TronAddress, method string, params []map[string]interface{}) (*domain.TronGasInfo, error)
	GetTrc20Allowance(ctx context.Context, owner, spender, contractAddress domain.TronAddress) (*big.Int, error)
	GetTRC20Decimals(ctx context.Context, contractAddress domain.TronAddress) (*big.Int, error)
	ToRawTokenAmount(ctx context.Context, token domain.TronAddress, amount string) (*big.Int, error)
	GetAccountResource(ctx context.Context, addr domain.TronAddress) (*domain.TronAccountResource, error)
	WaitUntilHavingEnoughEnergy(ctx context.Context, addr domain.TronAddress, energy int64) error
	TriggerConstantContract(ctx context.Context, from, contractAddress domain.TronAddress, method string, params []map[string]interface{}, callValue int64) (*TransactionExtension, error)
	BroadcastTransaction(ctx context.Context, transaction *troncore.Transaction) (string, error)
	BroadcastTransactionHex(ctx context.Context, txHex string) (string, error)
	ReadContract(ctx context.Context, from, contractAddress domain.TronAddress, method string, params []map[string]interface{}) (*TransactionExtension, error)
	TriggerContract(ctx context.Context, from, contractAddress domain.TronAddress, method string, params []map[string]interface{}, defaultFeeLimit, tAmount int64, tTokenID string, tTokenAmount int64) (*TransactionExtension, error)
	TransferTRX(ctx context.Context, from, to domain.TronAddress, amount int64) (*TransactionExtension, error)
	// TODO: remove this and use EstimateContractGas instead
	EstimateEnergy(ctx context.Context, from, contractAddress domain.TronAddress, method string, params []map[string]interface{}) (int64, error)
	ReadContractString(ctx context.Context, tokenID, method string) (string, error)
	GetOwnerAddress(ctx context.Context, addr domain.TronAddress) (domain.TronAddress, error)
	domain.ChainClient
	domain.ChainSyncFetcher
	domain.TokenMetadataFetcher
}

type clientImpl struct {
	chain      domain.Chain
	rpcClient  resty.Client
	httpClient resty.Client
}

func GetClient(chain domain.Chain) IClient {
	if !chain.IsTVM() {
		panic("chain is not tron like")
	}
	return &clientImpl{
		chain: chain,
		rpcClient: resty.NewRestyClient().
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(JsonRpcURL(chain)).
			SetTimeout(time.Second * 5),
		httpClient: resty.NewRestyClient().
			OnBeforeRequest(core.RestyReqURLInjector).
			OnAfterResponse(core.RestyRespLogger()).
			SetBaseURL(HttpRpcURL(chain)).
			SetTimeout(time.Second * 5),
	}
}
