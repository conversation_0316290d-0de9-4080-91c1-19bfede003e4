package tron

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/big"
	"strings"

	ethabi "github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/kryptogo/gotron-sdk/pkg/abi"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

func (c *clientImpl) GetTrc20Allowance(ctx context.Context, owner, spender, contractAddress domain.TronAddress) (*big.Int, error) {
	// Define the ABI for the allowance function
	const allowanceABI = `[{"constant":true,"inputs":[{"name":"_owner","type":"address"},{"name":"_spender","type":"address"}],"name":"allowance","outputs":[{"name":"remaining","type":"uint256"}],"payable":false,"stateMutability":"view","type":"function"}]`

	// Parse the ABI
	tokenABI, err := ethabi.JSON(strings.NewReader(allowanceABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ABI: %w", err)
	}

	// Convert domain.Address to common.Address
	addrOwner := common.HexToAddress(owner.Hex())
	addrSpender := common.HexToAddress(spender.Hex())

	// Pack the function call data
	data, err := tokenABI.Pack("allowance", addrOwner, addrSpender)
	if err != nil {
		return nil, fmt.Errorf("failed to pack data: %w", err)
	}

	// Prepare the eth_call payload
	payload := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_call",
		"params": []interface{}{
			map[string]interface{}{
				"to":   contractAddress.Hex(),
				"data": common.Bytes2Hex(data),
			},
			"latest",
		},
		"id": 1,
	}

	var response struct {
		Result string `json:"result"`
		Error  struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		} `json:"error,omitempty"`
	}
	resp, err := c.rpcClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&response).
		Post("")
	if err != nil {
		return nil, fmt.Errorf("RPC call failed: %w", err)
	}

	if resp.IsError() || response.Result == "" {
		if response.Error.Message != "" {
			return nil, fmt.Errorf("failed to get allowance, error: %s", response.Error.Message)
		}
		return nil, fmt.Errorf("failed to get allowance, response: %v", response)
	}

	// Unpack the result
	allowance := new(big.Int)
	err = tokenABI.UnpackIntoInterface(&allowance, "allowance", common.FromHex(response.Result))
	if err != nil {
		return nil, fmt.Errorf("failed to unpack allowance: %w", err)
	}

	return allowance, nil
}

type contractTriggerResponse struct {
	Result struct {
		Result  bool   `json:"result"`
		Message string `json:"message"`
	} `json:"result"`
	EnergyUsed     int64    `json:"energy_used"`
	ConstantResult []string `json:"constant_result"`
	Error          struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	} `json:"error,omitempty"`
	Transaction Transaction `json:"transaction"`
}

func (c *clientImpl) TriggerConstantContract(ctx context.Context, from, contractAddress domain.TronAddress, method string, params []map[string]interface{}, callValue int64) (*TransactionExtension, error) {
	// Convert params to JSON string
	paramJSON, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal parameters: %w", err)
	}

	// Load ABI from JSON
	param, err := abi.LoadFromJSON(string(paramJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to load ABI from JSON: %w", err)
	}

	// Pack the function call data
	dataBytes, err := abi.Pack(method, param)
	if err != nil {
		return nil, fmt.Errorf("failed to pack data: %w", err)
	}

	// Prepare the payload for the triggerconstantcontract endpoint
	payload := map[string]interface{}{
		"owner_address":     from.String(),
		"contract_address":  contractAddress.String(),
		"function_selector": method,
		"parameter":         hex.EncodeToString(dataBytes)[8:],
		"call_value":        callValue,
		"visible":           true,
	}

	var response contractTriggerResponse
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&response).
		Post("/wallet/triggerconstantcontract")
	if err != nil {
		return nil, fmt.Errorf("HTTP call failed: %w", err)
	}

	kglog.DebugWithDataCtx(ctx, "Trigger constant contract response", response)

	if resp.IsError() || !response.Result.Result {
		return nil, fmt.Errorf("failed to trigger constant contract, response: %v", response)
	}
	if strings.Contains(response.Result.Message, "REVERT") {
		return nil, fmt.Errorf("contract call reverted")
	}

	// Parse the constant result
	constantResult := make([][]byte, len(response.ConstantResult))
	for i, res := range response.ConstantResult {
		constantResult[i] = common.FromHex(res)
	}

	// Create and return the result struct
	return &TransactionExtension{
		ConstantResult: constantResult,
		EnergyUsed:     response.EnergyUsed,
		Transaction:    &response.Transaction,
	}, nil
}

func (c *clientImpl) EstimateEnergy(ctx context.Context, from, contractAddress domain.TronAddress, method string, params []map[string]interface{}) (int64, error) {
	// it's more precise to use TriggerConstantContract to estimate energy
	txExt, err := c.TriggerConstantContract(ctx, from, contractAddress, method, params, 0)
	if err != nil {
		return 0, fmt.Errorf("failed to trigger constant contract: %w", err)
	}
	return txExt.EnergyUsed, nil
}

func (c *clientImpl) ReadContract(ctx context.Context, from, contractAddress domain.TronAddress, method string, params []map[string]interface{}) (*TransactionExtension, error) {
	// Call the TriggerConstantContract method
	txExt, err := c.TriggerConstantContract(ctx, from, contractAddress, method, params, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to trigger constant contract: %w", err)
	}

	// Log the transaction extension details
	kglog.DebugWithDataCtx(ctx, "Read contract transaction extension", txExt)

	return txExt, nil
}

func (c *clientImpl) TriggerContract(ctx context.Context, from, contractAddress domain.TronAddress, method string, params []map[string]interface{}, defaultFeeLimit, tAmount int64, tTokenID string, tTokenAmount int64) (*TransactionExtension, error) {
	// Prepare the ABI and pack the parameters
	paramJSON, err := json.Marshal(params)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal parameters: %w", err)
	}

	// Load ABI from JSON
	param, err := abi.LoadFromJSON(string(paramJSON))
	if err != nil {
		return nil, fmt.Errorf("failed to load ABI from JSON: %w", err)
	}

	// Pack the function call data
	dataBytes, err := abi.Pack(method, param)
	if err != nil {
		return nil, fmt.Errorf("failed to pack data: %w", err)
	}

	// Prepare the payload for the triggersmartcontract endpoint
	payload := map[string]interface{}{
		"owner_address":     from.String(),
		"contract_address":  contractAddress.String(),
		"function_selector": method,
		"parameter":         hex.EncodeToString(dataBytes)[8:],
		"fee_limit":         defaultFeeLimit,
		"call_value":        tAmount,
		"visible":           true,
	}

	var response contractTriggerResponse
	// Make the HTTP request
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&response).
		Post("/wallet/triggersmartcontract")
	if err != nil {
		return nil, fmt.Errorf("HTTP call failed: %w", err)
	}

	kglog.DebugWithDataCtx(ctx, "Trigger contract response", response)

	if resp.IsError() || !response.Result.Result {
		if response.Error.Message != "" {
			return nil, fmt.Errorf("failed to trigger contract, error: %s", response.Error.Message)
		}
		return nil, fmt.Errorf("failed to trigger contract, response: %v", response)
	}

	// Parse the constant result
	constantResult := make([][]byte, len(response.ConstantResult))
	for i, res := range response.ConstantResult {
		constantResult[i] = common.FromHex(res)
	}

	// Create and return the result struct
	return &TransactionExtension{
		ConstantResult: constantResult,
		EnergyUsed:     response.EnergyUsed,
		Transaction:    &response.Transaction,
	}, nil
}

func (c *clientImpl) TransferTRX(ctx context.Context, from, to domain.TronAddress, amount int64) (*TransactionExtension, error) {
	payload := map[string]interface{}{
		"owner_address": from.String(),
		"to_address":    to.String(),
		"amount":        amount,
		"visible":       true,
	}

	response := Transaction{}
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&response).
		Post("/wallet/createtransaction")
	if err != nil {
		return nil, fmt.Errorf("failed to create TRX transfer transaction: %w", err)
	}

	if resp.IsError() {
		return nil, fmt.Errorf("failed to create TRX transfer transaction, response: %v", string(resp.Body()))
	}

	// Create and return the TransactionExtension
	return &TransactionExtension{
		Transaction: &response,
	}, nil
}

// ReadContractString reads a contract method without param.
func (c *clientImpl) ReadContractString(ctx context.Context, tokenID, method string) (string, error) {
	abiString := fmt.Sprintf(`[
		{"name":"%s","type":"function","inputs":[],"outputs":[{"name":"%s","type":"string"}],"constant":true,"payable":false}
	]`, method, method)
	tokenABI, err := ethabi.JSON(strings.NewReader(abiString))
	if err != nil {
		return "", fmt.Errorf("failed to parse ABI: %v", err)
	}

	// Pack the function call data
	data, err := tokenABI.Pack(method)
	if err != nil {
		return "", fmt.Errorf("failed to pack data: %v", err)
	}

	var response struct {
		Result string `json:"result"`
	}

	params := []interface{}{
		map[string]interface{}{
			"to":   domain.NewTronAddress(tokenID).Hex(),
			"data": common.Bytes2Hex(data),
		},
		"latest",
	}

	if err := c.makeRPCCall(ctx, "eth_call", params, &response); err != nil {
		return "", err
	}
	if response.Result == "" {
		kglog.WarningfCtx(ctx, "Tron ReadContractString response body empty")
		return "", fmt.Errorf("failed to read contract string: response body empty")
	}

	// Unpack the result
	result := ""
	err = tokenABI.UnpackIntoInterface(&result, method, common.FromHex(response.Result))
	if err != nil {
		return "", fmt.Errorf("failed to unpack result: %v", err)
	}
	return result, nil
}
