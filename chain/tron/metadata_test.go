package tron

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestFetchMetadata(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)
	metadata, err := client.FetchMetadata(context.Background(), domain.Tron, "TNUC9Qb1rRpS5CbWLmNMxXBjyFoydXjWFR")
	assert.NoError(t, err)
	assert.NotNil(t, metadata)
	assert.Equal(t, "WTRX", metadata.Symbol)
	assert.Equal(t, "Wrapped TRX", metadata.Name)
	assert.Equal(t, uint(6), metadata.Decimals)
}

func TestFetchMetadataNotFound(t *testing.T) {
	client := GetClient(domain.Tron)
	t.Run("REVERT opcode executed", func(t *testing.T) {
		t.Parallel()
		metadata, err := client.FetchMetadata(context.Background(), domain.Tron, "TNURfT4XG9hoBiz6r95Z5pemDeuxzaeCTJ")
		assert.Error(t, err)
		assert.ErrorIs(t, err, domain.ErrRecordNotFound)
		assert.Nil(t, metadata)
	})

	t.Run("smart contract is not exist", func(t *testing.T) {
		t.Parallel()
		metadata, err := client.FetchMetadata(context.Background(), domain.Tron, "1005047") // TRC10 token ID
		assert.Error(t, err)
		assert.ErrorIs(t, err, domain.ErrRecordNotFound)
		assert.Nil(t, metadata)
	})
}
