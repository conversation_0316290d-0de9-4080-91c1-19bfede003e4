package tron

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/jpillora/backoff"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

type chainParameters struct {
	EnergyFee          int64
	BandwidthFee       int64
	AccountCreationFee int64
}

func (c *clientImpl) GetChainParameters(ctx context.Context) (*chainParameters, error) {
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetHeader("accept", "application/json").
		Get("/wallet/getchainparameters")
	if err != nil {
		return nil, fmt.Errorf("failed to get chain parameters: %w", err)
	}
	if resp.IsError() {
		return nil, fmt.Errorf("failed to get chain parameters")
	}

	var response struct {
		ChainParameter []struct {
			Key   string `json:"key"`
			Value int64  `json:"value"`
		} `json:"chainParameter"`
	}

	if err := json.Unmarshal(resp.Body(), &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	params := &chainParameters{}
	for _, param := range response.ChainParameter {
		switch param.Key {
		case "getEnergyFee":
			params.EnergyFee = param.Value
		case "getTransactionFee":
			params.BandwidthFee = param.Value
		case "getCreateNewAccountFeeInSystemContract":
			params.AccountCreationFee = param.Value
		}
	}

	return params, nil
}

func (c *clientImpl) GetAccountResource(ctx context.Context, addr domain.TronAddress) (*domain.TronAccountResource, error) {
	// Prepare the payload for the HTTP call to get account resources
	payload := map[string]interface{}{
		"address": addr.String(),
		"visible": true,
	}

	var response struct {
		FreeNetLimit int64  `json:"freeNetLimit"`
		NetUsed      int64  `json:"NetUsed"`
		NetLimit     int64  `json:"NetLimit"`
		EnergyLimit  int64  `json:"EnergyLimit"`
		EnergyUsed   int64  `json:"EnergyUsed"`
		Error        string `json:"Error,omitempty"`
	}
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&response).
		Post("/wallet/getaccountresource")
	if err != nil {
		return nil, fmt.Errorf("HTTP call failed: %w", err)
	}

	if resp.IsError() {
		if response.Error != "" {
			return nil, fmt.Errorf("failed to get account resource, error: %s", response.Error)
		}
		return nil, fmt.Errorf("failed to get account resource, response: %v", response)
	}

	return &domain.TronAccountResource{
		FreeNetLimit: response.FreeNetLimit,
		NetUsed:      response.NetUsed,
		NetLimit:     response.NetLimit,
		EnergyLimit:  response.EnergyLimit,
		EnergyUsed:   response.EnergyUsed,
	}, nil
}

func (c *clientImpl) WaitUntilHavingEnoughEnergy(ctx context.Context, addr domain.TronAddress, energy int64) error {
	b := &backoff.Backoff{
		Min:    100 * time.Millisecond,
		Max:    1 * time.Second,
		Factor: 2,
		Jitter: true,
	}

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			resource, err := c.GetAccountResource(ctx, addr)
			if err != nil {
				kglog.WarningfCtx(ctx, "failed to get account resource: %v", err)
				return fmt.Errorf("failed to get account resource: %w", err)
			}

			if (resource.EnergyLimit - resource.EnergyUsed) >= energy {
				return nil
			}

			timer := time.NewTimer(b.Duration())
			select {
			case <-ctx.Done():
				timer.Stop()
				return ctx.Err()
			case <-timer.C:
				// Continue with the next iteration
			}
		}
	}
}

func (c *clientImpl) addressExists(ctx context.Context, addr domain.TronAddress) (bool, error) {
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetBody(map[string]interface{}{
			"address": addr.String(),
			"visible": true,
		}).
		Post("/wallet/getaccount")
	if err != nil {
		return false, fmt.Errorf("failed to check address: %w", err)
	}

	// In Tron, if the account has no data (not activated),
	// the response will be empty or contain minimal data
	var account struct {
		CreateTime int64  `json:"create_time"` // Account creation timestamp
		Address    string `json:"address"`     // Base58 address
		Balance    int64  `json:"balance"`     // Balance in SUN
		Error      string `json:"Error"`
	}
	if err := json.Unmarshal(resp.Body(), &account); err != nil {
		return false, nil // Treat unmarshal error as non-existent account
	}

	// An account exists if it has a creation time or balance
	// The Error field being empty means the request was successful
	return (account.CreateTime > 0 || account.Balance > 0) && account.Error == "", nil
}

// EstimateTrxTransferGas estimates the gas cost for TRX transfer
func (c *clientImpl) EstimateTrxTransferGas(ctx context.Context, from, to domain.TronAddress) (*domain.TronGasInfo, error) {
	// Get chain parameters
	chainParams, err := c.GetChainParameters(ctx)
	if err != nil {
		return nil, err
	}

	// Check if target address exists
	exists, err := c.addressExists(ctx, to)
	if err != nil {
		return nil, fmt.Errorf("failed to check address existence: %w", err)
	}

	txSize := int64(268) // TRX transfer fixed to 268 bandwidth
	bandwidthCost := txSize * chainParams.BandwidthFee

	// Check for free bandwidth
	resource, err := c.GetAccountResource(ctx, from)
	if err != nil {
		return nil, fmt.Errorf("failed to get account resource: %w", err)
	}

	// If freeNetRemaining is not enough, actually TRON does not use them at all
	// So it's either 0 or the actual txSize
	freeNetRemaining := resource.FreeNetLimit - resource.NetUsed
	if freeNetRemaining >= txSize {
		bandwidthCost = 0
	}

	// Add account creation fee if needed
	var accountCreationFee int64
	if !exists {
		accountCreationFee = chainParams.AccountCreationFee
	}

	// Total cost in Sun
	return &domain.TronGasInfo{
		AccountExists:      &exists,
		AccountCreationFee: &accountCreationFee,
		TxSize:             txSize,
		BandwidthFee:       chainParams.BandwidthFee,
		BandwidthCost:      bandwidthCost,
		TotalCost:          bandwidthCost + accountCreationFee,
	}, nil
}

// EstimateContractGas estimates the gas cost for contract interactions (e.g., TRC20 transfers)
func (c *clientImpl) EstimateContractGas(ctx context.Context, from, contractAddress domain.TronAddress, method string, params []map[string]interface{}) (*domain.TronGasInfo, error) {
	// Get chain parameters
	chainParams, err := c.GetChainParameters(ctx)
	if err != nil {
		return nil, err
	}

	// Simulate the transaction to estimate energy usage
	txExt, err := c.TriggerConstantContract(ctx, from, contractAddress, method, params, 0)
	if err != nil {
		kglog.DebugWithDataCtx(ctx, "failed to simulate transaction", map[string]interface{}{
			"method": method,
			"params": params,
			"error":  err.Error(),
		})
		return nil, fmt.Errorf("failed to simulate transaction: %w", err)
	}

	// Calculate the energy cost
	energyUsed := txExt.EnergyUsed
	var energyCost int64

	// Check the account's current energy
	resource, err := c.GetAccountResource(ctx, from)
	if err != nil {
		return nil, fmt.Errorf("failed to get account resource: %w", err)
	}

	// Calculate the remaining energy cost after using available energy
	availableEnergy := resource.EnergyLimit - resource.EnergyUsed
	energyToRent := energyUsed - availableEnergy
	if availableEnergy >= energyUsed {
		energyToRent = 0
		energyCost = 0
	} else {
		energyCost = energyToRent * chainParams.EnergyFee
	}

	// Calculate the actual transaction size
	// https://developers.tron.network/docs/faq#5-how-to-calculate-the-bandwidth-and-energy-consumed-when-callingdeploying-a-contract
	txSize := int64(len(txExt.Transaction.RawDataHex)/2 + 3 + 64 + 67)
	bandwidthCost := txSize * chainParams.BandwidthFee
	kglog.DebugWithDataCtx(ctx, "bandwidthCost", map[string]interface{}{
		"txSize":        txSize,
		"bandwidth":     chainParams.BandwidthFee,
		"bandwidthCost": bandwidthCost,
	})

	// Check for free bandwidth
	// If freeNetRemaining is not enough, actually TRON does not use them at all
	// So it's either 0 or the actual txSize
	freeNetRemaining := resource.FreeNetLimit - resource.NetUsed
	if freeNetRemaining >= txSize {
		bandwidthCost = 0
	}

	return &domain.TronGasInfo{
		// account creation fee is not applicable since it will be on energy
		// 64,285 for activated account
		// 130,285 for non-activated account
		EnergyUsed:    energyUsed,
		EnergyToRent:  energyToRent,
		EnergyFee:     chainParams.EnergyFee,
		TxSize:        txSize,
		BandwidthFee:  chainParams.BandwidthFee,
		BandwidthCost: bandwidthCost,
		TotalCost:     energyCost + bandwidthCost,
	}, nil
}
