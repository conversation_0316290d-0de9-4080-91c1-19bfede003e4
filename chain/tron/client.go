package tron

import (
	"context"
	"encoding/json"
	"fmt"
	"math/big"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/jpillora/backoff"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/samber/lo"
)

func (c *clientImpl) makeRPCCall(ctx context.Context, method string, params []interface{}, result interface{}) error {
	payload := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  method,
		"params":  params,
		"id":      1,
	}

	resp, err := c.rpcClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(result).
		Post("")
	if err != nil {
		return err
	}
	if resp.Is<PERSON>rror() {
		return fmt.Errorf("RPC call failed")
	}
	return nil
}

func (c *clientImpl) BlockNumber(ctx context.Context) (uint64, error) {
	var respData struct {
		Result string `json:"result"`
	}

	if err := c.makeRPCCall(ctx, "eth_blockNumber", []interface{}{}, &respData); err != nil {
		return 0, err
	}
	if respData.Result == "" {
		return 0, fmt.Errorf("failed to get block number")
	}

	blockNumber, err := strconv.ParseInt(respData.Result[2:], 16, 64)
	if err != nil {
		return 0, err
	}
	return uint64(blockNumber), nil
}

func (c *clientImpl) NativeBalance(ctx context.Context, address domain.Address) (*big.Int, error) {
	addr, ok := address.(domain.TronAddress)
	if !ok {
		return nil, fmt.Errorf("address is not a tron address")
	}

	var response struct {
		Result string `json:"result"`
	}

	if err := c.makeRPCCall(ctx, "eth_getBalance", []interface{}{addr.Hex(), "latest"}, &response); err != nil {
		return nil, err
	}
	if response.Result == "" {
		return nil, fmt.Errorf("failed to get balance")
	}

	balance, ok := new(big.Int).SetString(response.Result[2:], 16)
	if !ok {
		return nil, fmt.Errorf("failed to parse balance")
	}
	return balance, nil
}

func (c *clientImpl) TokenBalance(ctx context.Context, address domain.Address, tokenID string) (*big.Int, error) {
	addr, ok := address.(domain.TronAddress)
	if !ok {
		return nil, fmt.Errorf("address is not a tron address")
	}

	// Create the ABI for the balanceOf function
	tokenABI, err := abi.JSON(strings.NewReader(`[{"name":"balanceOf","type":"function","inputs":[{"name":"_owner","type":"address"}],"outputs":[{"name":"balance","type":"uint256"}],"constant":true,"payable":false}]`))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ABI: %v", err)
	}

	// Pack the function call data
	data, err := tokenABI.Pack("balanceOf", addr.Address)
	if err != nil {
		return nil, fmt.Errorf("failed to pack data: %v", err)
	}

	var response struct {
		Result string `json:"result"`
	}

	params := []interface{}{
		map[string]interface{}{
			"to":   domain.NewTronAddress(tokenID).Address.Hex(),
			"data": common.Bytes2Hex(data),
		},
		"latest",
	}

	if err := c.makeRPCCall(ctx, "eth_call", params, &response); err != nil {
		return nil, err
	}
	if response.Result == "" {
		kglog.WarningfCtx(ctx, "Tron TokenBalance response body empty")
		return nil, fmt.Errorf("failed to get token balance: response body empty")
	}

	// Unpack the result
	balance := new(big.Int)
	err = tokenABI.UnpackIntoInterface(&balance, "balanceOf", common.FromHex(response.Result))
	if err != nil {
		return nil, fmt.Errorf("failed to unpack balance: %v", err)
	}

	return balance, nil
}

func (c *clientImpl) GetTransactionStatus(ctx context.Context, txHash string) (domain.TransactionStatus, error) {
	txHash = txWith0xPrefix(txHash)
	payload := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getTransactionReceipt",
		"params":  []interface{}{txHash},
		"id":      1,
	}

	// Define the response structure
	var response struct {
		Result struct {
			Status string `json:"status"`
		} `json:"result"`
	}

	// Make the RPC call
	resp, err := c.rpcClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&response).
		Post("")
	if err != nil {
		return domain.TransactionStatusUnknown, err
	}
	if resp.IsError() || response.Result.Status == "" {
		return domain.TransactionStatusUnknown, fmt.Errorf("failed to get transaction status")
	}

	// Parse the status
	status, err := strconv.ParseUint(response.Result.Status[2:], 16, 64)
	if err != nil {
		return domain.TransactionStatusUnknown, fmt.Errorf("failed to parse transaction status")
	}

	// Determine the transaction status
	switch status {
	case 1:
		return domain.TransactionStatusSuccess, nil
	case 0:
		return domain.TransactionStatusFailed, nil
	default:
		return domain.TransactionStatusUnknown, fmt.Errorf("unknown transaction status")
	}
}

func txWith0xPrefix(txHash string) string {
	if len(txHash) >= 2 && txHash[:2] == "0x" {
		return txHash
	}
	return "0x" + txHash
}

func (c *clientImpl) WaitUntilTransactionConfirmed(ctx context.Context, txHash string) (domain.TransactionStatus, error) {
	// Configure backoff parameters based on the average block time.
	minWait := c.chain.BlockTime() / 2
	maxWait := c.chain.BlockTime() * 2
	if maxWait > 10*time.Second {
		maxWait = 10 * time.Second
	}
	b := &backoff.Backoff{
		Min:    minWait,
		Max:    maxWait,
		Factor: 2,
		Jitter: true,
	}

	for {
		select {
		case <-ctx.Done():
			return domain.TransactionStatusUnknown, ctx.Err()
		default:
			status, err := c.GetTransactionStatus(ctx, txHash)
			if err != nil {
				if ctx.Err() != nil {
					return domain.TransactionStatusUnknown, ctx.Err()
				}
				// If it's not a context error, continue with the loop
			} else if status != domain.TransactionStatusUnknown {
				return status, nil
			}

			timer := time.NewTimer(b.Duration())
			select {
			case <-ctx.Done():
				// return as soon as the context is done
				timer.Stop()
				return domain.TransactionStatusUnknown, ctx.Err()
			case <-timer.C:
				// Continue with the next iteration
			}
		}
	}
}

func (c *clientImpl) TransactionDetail(ctx context.Context, txHash string) (*domain.TransactionDetail, error) {
	txHash = txWith0xPrefix(txHash)

	txResponse, err := c.fetchTransactionByHash(ctx, txHash)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction details: %w", err)
	}

	receiptResponse, err := c.fetchTransactionReceipt(ctx, txHash)
	if err != nil {
		return nil, fmt.Errorf("failed to get transaction receipt: %w", err)
	}

	block, err := c.fetchBlockByNumber(ctx, receiptResponse.Result.BlockNumber, false)
	if err != nil {
		return nil, fmt.Errorf("failed to get block details: %w", err)
	}

	detail, err := c.parseTransactionDetail(txResponse, receiptResponse, txHash, block)
	if err != nil {
		return nil, fmt.Errorf("failed to parse transaction detail: %w", err)
	}

	return detail, nil
}

func (c *clientImpl) fetchTransactionByHash(ctx context.Context, txHash string) (*transactionResponse, error) {
	payload := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getTransactionByHash",
		"params":  []interface{}{txHash},
		"id":      1,
	}

	var txResponse transactionResponse
	resp, err := c.rpcClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&txResponse).
		Post("")
	if err != nil || resp.IsError() || txResponse.Result.BlockHash == "" {
		return nil, fmt.Errorf("failed to fetch transaction by hash")
	}

	return &txResponse, nil
}

func (c *clientImpl) fetchTransactionReceipt(ctx context.Context, txHash string) (*receiptResponse, error) {
	payload := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getTransactionReceipt",
		"params":  []interface{}{txHash},
		"id":      1,
	}

	var receiptResponse receiptResponse
	resp, err := c.rpcClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&receiptResponse).
		Post("")
	if err != nil || resp.IsError() || receiptResponse.Result.Status == "" {
		return nil, fmt.Errorf("failed to fetch transaction receipt")
	}

	return &receiptResponse, nil
}

func (c *clientImpl) fetchBlockByNumber(ctx context.Context, blockNumberHex string, fetchTransactionDetails bool) (*domain.Block, error) {
	payload := map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_getBlockByNumber",
		"params":  []interface{}{blockNumberHex, fetchTransactionDetails},
		"id":      1,
	}

	type transactionResponse struct {
		BlockHash        string `json:"blockHash"`
		BlockNumber      string `json:"blockNumber"`
		From             string `json:"from"`
		Gas              string `json:"gas"`
		GasPrice         string `json:"gasPrice"`
		Hash             string `json:"hash"`
		Input            string `json:"input"`
		Nonce            string `json:"nonce"`
		To               string `json:"to"`
		TransactionIndex string `json:"transactionIndex"`
		Value            string `json:"value"`
		V                string `json:"v"`
		R                string `json:"r"`
		S                string `json:"s"`
	}

	type blockResponse struct {
		Result struct {
			Number       string      `json:"number"`
			Hash         string      `json:"hash"`
			ParentHash   string      `json:"parentHash"`
			Nonce        string      `json:"nonce"`
			Timestamp    string      `json:"timestamp"`
			Transactions interface{} `json:"transactions"`
		} `json:"result"`
	}

	var blockResp blockResponse

	resp, err := c.rpcClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&blockResp).
		Post("")
	if err != nil || resp.IsError() {
		kglog.WarningfCtx(ctx, "Tron fetchBlockByNumber response body: %s", string(resp.Body()))
		return nil, fmt.Errorf("failed to fetch block by number: %w", err)
	}

	timestampInt, err := strconv.ParseInt(blockResp.Result.Timestamp[2:], 16, 64)
	if err != nil {
		return nil, fmt.Errorf("failed to parse timestamp: %w", err)
	}

	block := &domain.Block{
		Number:     blockResp.Result.Number,
		Hash:       blockResp.Result.Hash,
		ParentHash: blockResp.Result.ParentHash,
		Nonce:      blockResp.Result.Nonce,
		Timestamp:  time.Unix(timestampInt, 0),
	}

	// Check the type of Transactions
	switch transactionsData := blockResp.Result.Transactions.(type) {
	case []interface{}:
		if fetchTransactionDetails {
			var transactions []transactionResponse
			for _, tx := range transactionsData {
				txMap, ok := tx.(map[string]interface{})
				if !ok {
					return nil, fmt.Errorf("failed to assert transaction as map")
				}
				txJSON, err := json.Marshal(txMap)
				if err != nil {
					return nil, fmt.Errorf("failed to marshal transaction map: %w", err)
				}
				var transaction transactionResponse
				if err := json.Unmarshal(txJSON, &transaction); err != nil {
					return nil, fmt.Errorf("failed to unmarshal transaction: %w", err)
				}
				transactions = append(transactions, transaction)
			}
			block.Transactions = lo.Map(transactions, func(tx transactionResponse, _ int) *domain.Transaction {
				return &domain.Transaction{
					Hash:  tx.Hash,
					From:  tx.From,
					To:    tx.To,
					Value: tx.Value,
				}
			})
		} else {
			var transactions []string
			for _, tx := range transactionsData {
				txHash, ok := tx.(string)
				if !ok {
					return nil, fmt.Errorf("failed to assert transaction hash as string")
				}
				transactions = append(transactions, txHash)
			}
			block.Transactions = lo.Map(transactions, func(tx string, _ int) *domain.Transaction {
				return &domain.Transaction{
					Hash: tx,
				}
			})
		}
	default:
		return nil, fmt.Errorf("unexpected type for transactions: %T", transactionsData)
	}

	return block, nil
}

// Add new struct for Tron transaction info response
type transactionInfoResponse struct {
	ID              string   `json:"id"`
	Fee             int64    `json:"fee"`
	BlockNumber     int64    `json:"blockNumber"`
	BlockTimeStamp  int64    `json:"blockTimeStamp"`
	ContractResult  []string `json:"contractResult"`
	ContractAddress string   `json:"contract_address"`
	Receipt         struct {
		EnergyFee        int64  `json:"energy_fee"`
		EnergyUsageTotal int64  `json:"energy_usage_total"`
		NetFee           int64  `json:"net_fee"`
		Result           string `json:"result"`
	} `json:"receipt"`
	Log []struct {
		Address string   `json:"address"`
		Topics  []string `json:"topics"`
		Data    string   `json:"data"`
	} `json:"log"`
	InternalTransactions []struct {
		Hash              string `json:"hash"`
		CallerAddress     string `json:"caller_address"`
		TransferToAddress string `json:"transferTo_address"`
		CallValueInfo     []struct {
			CallValue int64 `json:"callValue"`
		} `json:"callValueInfo"`
		Note string `json:"note"`
	} `json:"internal_transactions"`
}

// Add new method to fetch transaction info
func (c *clientImpl) fetchTransactionInfo(ctx context.Context, txHash string) (*transactionInfoResponse, error) {
	payload := map[string]interface{}{
		"value":   strings.TrimPrefix(txHash, "0x"),
		"visible": true,
	}

	var response transactionInfoResponse
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&response).
		Post("/wallet/gettransactioninfobyid")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch transaction info: %w", err)
	}
	if resp.IsError() {
		return nil, fmt.Errorf("failed to fetch transaction info: %s", string(resp.Body()))
	}

	return &response, nil
}

// Update parseTransactionDetail to include internal transactions and fee calculation
func (c *clientImpl) parseTransactionDetail(txResponse *transactionResponse, receiptResponse *receiptResponse, txHash string, block *domain.Block) (*domain.TransactionDetail, error) {
	// Fetch additional transaction info for internal transactions and fees
	txInfo, err := c.fetchTransactionInfo(context.Background(), txHash)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch transaction info: %w", err)
	}

	blockNumber, _ := strconv.ParseUint(txResponse.Result.BlockNumber[2:], 16, 64)
	value, _ := new(big.Int).SetString(txResponse.Result.Value[2:], 16)

	// Calculate total fee (energy_fee + net_fee)
	totalFee := txInfo.Receipt.EnergyFee + txInfo.Receipt.NetFee

	// Parse internal transfers
	internalTransfers := make([]*domain.NativeTokenTransfer, 0)
	for _, internalTx := range txInfo.InternalTransactions {
		// Sum up all call values in CallValueInfo
		amount := int64(0)
		for _, callValue := range internalTx.CallValueInfo {
			amount += callValue.CallValue
		}

		if amount > 0 {
			internalTransfers = append(internalTransfers, &domain.NativeTokenTransfer{
				From:   domain.NewTronAddress(internalTx.CallerAddress),
				To:     domain.NewTronAddress(internalTx.TransferToAddress),
				Amount: new(big.Int).SetInt64(amount),
			})
		}
	}

	// Parse token transfers
	tokenTransfers, err := c.getTokenTransfers(receiptResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to parse token transfers: %w", err)
	}

	methodID := ""
	if len(txResponse.Result.Input) >= 10 {
		methodID = txResponse.Result.Input[:10]
	}

	var deployedContract domain.Address
	if receiptResponse.Result.ContractAddress != "" {
		deployedContract = domain.NewTronAddress(receiptResponse.Result.ContractAddress)
	}

	detail := &domain.TransactionDetail{
		Chain:            c.chain,
		Hash:             txHash,
		BlockNum:         uint32(blockNumber),
		IsError:          receiptResponse.Result.Status == "0x0",
		From:             domain.NewTronAddress(txResponse.Result.From),
		To:               domain.NewTronAddress(txResponse.Result.To),
		DeployedContract: deployedContract,
		Value:            value,
		Data:             txResponse.Result.Input,
		GasPrice:         big.NewInt(1),                   // Fixed gas price of 1
		GasUsed:          new(big.Int).SetInt64(totalFee), // Total fee as gas used
		MethodID:         methodID,
		FunctionName:     "", // TODO: implement common function name
		Timestamp:        block.Timestamp,
		TransactionTransfers: domain.TransactionTransfers{
			InternalTransfers: internalTransfers,
			TokenTransfers:    tokenTransfers,
		},
	}

	return detail, nil
}

func (c *clientImpl) getTokenTransfers(receiptResponse *receiptResponse) ([]*domain.TokenTransfer, error) {
	tokenTransfers := make([]*domain.TokenTransfer, 0)
	for _, log := range receiptResponse.Result.Logs {
		if len(log.Topics) == 3 && log.Topics[0] == "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef" {
			from := domain.NewTronAddress(log.Topics[1][26:])
			to := domain.NewTronAddress(log.Topics[2][26:])
			amount, _ := new(big.Int).SetString(log.Data[2:], 16)
			tokenTransfers = append(tokenTransfers, &domain.TokenTransfer{
				Contract: domain.NewTronAddress(log.Address),
				From:     from,
				To:       to,
				Amount:   amount,
			})
		}
	}
	return tokenTransfers, nil
}

type transactionResponse struct {
	Result struct {
		BlockHash        string `json:"blockHash"`
		BlockNumber      string `json:"blockNumber"`
		From             string `json:"from"`
		To               string `json:"to"`
		Value            string `json:"value"`
		Gas              string `json:"gas"`
		GasPrice         string `json:"gasPrice"`
		Input            string `json:"input"`
		TransactionIndex string `json:"transactionIndex"`
	} `json:"result"`
}

type receiptResponse struct {
	Result struct {
		Status          string `json:"status"`
		ContractAddress string `json:"contractAddress"`
		GasUsed         string `json:"gasUsed"`
		Logs            []struct {
			Address string   `json:"address"`
			Topics  []string `json:"topics"`
			Data    string   `json:"data"`
		} `json:"logs"`
		BlockNumber string `json:"blockNumber"`
	} `json:"result"`
}

// Helper function to get the latest block
func (c *clientImpl) getLatestBlock(ctx context.Context) (*domain.Block, error) {
	block, err := c.fetchBlockByNumber(ctx, "latest", true)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch latest block: %w", err)
	}
	return block, nil
}

// Add new struct for block transaction info response
type blockTransactionInfoResponse struct {
	BlockNumber     int64    `json:"blockNumber"`
	ContractResult  []string `json:"contractResult"`
	BlockTimeStamp  int64    `json:"blockTimeStamp"`
	ContractAddress string   `json:"contract_address"`
	Receipt         struct {
		Result           string `json:"result"`
		EnergyUsage      int64  `json:"energy_usage"`
		EnergyUsageTotal int64  `json:"energy_usage_total"`
		NetUsage         int64  `json:"net_usage"`
	} `json:"receipt"`
	Log []struct {
		Address string   `json:"address"`
		Topics  []string `json:"topics"`
		Data    string   `json:"data"`
	} `json:"log"`
	InternalTransactions []struct {
		CallerAddress     string `json:"caller_address"`
		TransferToAddress string `json:"transferTo_address"`
		CallValueInfo     []struct {
			CallValue int64 `json:"callValue"`
		} `json:"callValueInfo"`
		Note string `json:"note"`
	} `json:"internal_transactions"`
	ID string `json:"id"`
}

// Add new method to fetch block transaction info
func (c *clientImpl) fetchBlockTransactionInfo(ctx context.Context, blockNum uint64) ([]blockTransactionInfoResponse, error) {
	payload := map[string]interface{}{
		"num":     blockNum,
		"visible": true,
	}

	var response []blockTransactionInfoResponse
	resp, err := c.httpClient.R().
		SetContext(ctx).
		SetBody(payload).
		SetResult(&response).
		Post("/wallet/gettransactioninfobyblocknum")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch block transaction info: %w", err)
	}
	if resp.IsError() {
		return nil, fmt.Errorf("failed to fetch block transaction info: %s", string(resp.Body()))
	}

	return response, nil
}

func (c *clientImpl) FetchTxsInRange(ctx context.Context, chain domain.Chain, fromBlock, toBlock uint64) (uint64, []*domain.TransactionWithAddresses, error) {
	if c.chain != chain {
		return 0, nil, fmt.Errorf("chain mismatch")
	}

	// Get latest block first to avoid querying non-existent blocks
	latestBlock, err := c.getLatestBlock(ctx)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to get latest block: %w", err)
	}

	latestBlockNum, err := strconv.ParseUint(latestBlock.Number[2:], 16, 64)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to parse latest block number: %w", err)
	}

	// Adjust toBlock if it exceeds the latest block
	if toBlock > latestBlockNum {
		toBlock = latestBlockNum
	}

	hashToAddresses := make(map[string][]domain.Address)

	// Fetch native transfers
	for blockNum := fromBlock; blockNum <= toBlock; blockNum++ {
		var block *domain.Block
		var err error

		if blockNum == latestBlockNum {
			// Reuse the latest block data we already have
			block = latestBlock
		} else {
			// Fetch block data for other block numbers
			block, err = c.fetchBlockByNumber(ctx, fmt.Sprintf("0x%x", blockNum), true)
			if err != nil {
				return 0, nil, fmt.Errorf("failed to fetch block %d: %w", blockNum, err)
			}
		}

		// Process regular transactions from the block
		for _, tx := range block.Transactions {
			from := domain.NewTronAddress(tx.From)
			hashToAddresses[tx.Hash] = append(hashToAddresses[tx.Hash], from)
			if tx.To != "" && tx.Value != "0x0" {
				to := domain.NewTronAddress(tx.To)
				hashToAddresses[tx.Hash] = append(hashToAddresses[tx.Hash], to)
			}
		}

		// Fetch and process internal transactions and logs
		txInfos, err := c.fetchBlockTransactionInfo(ctx, blockNum)
		if err != nil {
			return 0, nil, fmt.Errorf("failed to fetch block transaction info: %w", err)
		}

		// Process internal transactions and logs
		for _, txInfo := range txInfos {
			hash := fmt.Sprintf("0x%s", txInfo.ID)
			// Process internal transactions
			for _, internalTx := range txInfo.InternalTransactions {
				// Skip if no value transferred
				if len(internalTx.CallValueInfo) == 0 || internalTx.CallValueInfo[0].CallValue == 0 {
					continue
				}

				from := domain.NewTronAddress(internalTx.CallerAddress)
				to := domain.NewTronAddress(internalTx.TransferToAddress)
				hashToAddresses[hash] = append(hashToAddresses[hash], from, to)
			}

			// Process token transfer logs
			for _, log := range txInfo.Log {
				// Check if it's a token transfer event (topic0 is Transfer event signature)
				if len(log.Topics) == 3 && log.Topics[0] == "ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef" {
					from := domain.NewTronAddress(log.Topics[1][24:]) // Skip '000000000000000000000000'
					to := domain.NewTronAddress(log.Topics[2][24:])
					hashToAddresses[hash] = append(hashToAddresses[hash], from, to)
				}
			}
		}
	}

	return toBlock, lo.MapToSlice(hashToAddresses, func(txHash string, addresses []domain.Address) *domain.TransactionWithAddresses {
		uniqueAddresses := lo.Uniq(addresses)
		uniqueAddresses = lo.Filter(uniqueAddresses, func(addr domain.Address, _ int) bool {
			return addr != (domain.TronAddress{})
		})
		sort.Slice(uniqueAddresses, func(i, j int) bool {
			return uniqueAddresses[i].String() < uniqueAddresses[j].String()
		})
		return &domain.TransactionWithAddresses{
			Chain:     c.chain,
			Hash:      txHash,
			Addresses: uniqueAddresses,
		}
	}), nil
}
