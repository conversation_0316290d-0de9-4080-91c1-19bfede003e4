package tron

import (
	"context"
	"math/big"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestGetTrc20Allowance(t *testing.T) {
	t.<PERSON>llel()
	client := GetClient(domain.Tron)

	owner := domain.NewTronAddress("TDSEUjDdfKtEAoWL387hi433T4Fanwip9Z")
	spender := domain.NewTronAddress("TRzh2k4psPBbQrK4Xv3iWSF4KZcU9QLgjW")
	contractAddress := domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t")

	t.Logf("Testing allowance for owner: %s, spender: %s, contract: %s", owner, spender, contractAddress)

	allowance, err := client.GetTrc20Allowance(context.Background(), owner, spender, contractAddress)
	if err != nil {
		t.Fatalf("Failed to get allowance: %v", err)
	}

	t.Logf("Allowance: %v", allowance.String())
	assert.True(t, allowance.Cmp(big.NewInt(0)) > 0, "Allowance should be greater than 0")
}

func TestTriggerConstantContract(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)

	from := domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	contractAddress := domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t")
	method := "balanceOf(address)"
	params := []map[string]interface{}{{"address": from}}
	callValue := int64(0)

	txExt, err := client.TriggerConstantContract(context.Background(), from, contractAddress, method, params, callValue)
	if err != nil {
		t.Fatalf("Failed to trigger constant contract: %v", err)
	}

	t.Logf("Transaction: %v", txExt)
	assert.Greater(t, txExt.EnergyUsed, int64(0), "Energy used should be greater than 0")

	if len(txExt.ConstantResult) == 0 {
		t.Errorf("Constant result is empty, potential issue with contract execution")
	}

	if txExt.EnergyUsed < 4000 {
		t.Errorf("Energy used is unexpectedly low: %d", txExt.EnergyUsed)
	}
}

func TestEstimateEnergy(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)

	from := domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	contractAddress := domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t")
	method := "transfer(address,uint256)"
	params := []map[string]interface{}{{"address": "TZ4UXDV5ZhNW7fb2AMSbgfAEZ7hWsnYS2g"}, {"uint256": "1"}}
	energy, err := client.EstimateEnergy(context.Background(), from, contractAddress, method, params)
	if err != nil {
		t.Fatalf("Failed to trigger constant contract: %v", err)
	}
	t.Logf("Energy: %d\n", energy)
	assert.Greater(t, energy, int64(64000))
}

func TestReadContract(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Tron)

	from := domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	contractAddress := domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t")
	method := "balanceOf(address)"
	params := []map[string]interface{}{{"address": from}}

	txExt, err := client.ReadContract(context.Background(), from, contractAddress, method, params)
	if err != nil {
		t.Fatalf("Failed to read contract: %v", err)
	}

	t.Logf("Transaction extension: %v", txExt)
	if len(txExt.ConstantResult) == 0 {
		t.Errorf("Constant result is empty, potential issue with contract execution")
	}

	if txExt.EnergyUsed < 4000 {
		t.Errorf("Energy used is unexpectedly low: %d", txExt.EnergyUsed)
	}
}

func TestTriggerContract(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Shasta)

	from := domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd")
	contractAddress := domain.NewTronAddress("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs")
	method := "transfer(address,uint256)"
	params := []map[string]interface{}{{"address": "TLYm7Ls8eY36bcwQqB1wiyGJ6z3tUMwNNN"}, {"uint256": "1000000"}}
	defaultFeeLimit := int64(30_000_000)
	tAmount := int64(0)
	tTokenID := ""
	tTokenAmount := int64(0)

	txExt, err := client.TriggerContract(context.Background(), from, contractAddress, method, params, defaultFeeLimit, tAmount, tTokenID, tTokenAmount)
	if err != nil {
		t.Fatalf("Failed to trigger contract: %v", err)
	}
	t.Logf("Transaction extension: %v", txExt)
	assert.Equal(t, "a9059cbb0000000000000000000000007409c98b083d1508199532c6dee05f032dc1976800000000000000000000000000000000000000000000000000000000000f4240", txExt.Transaction.RawData.Contract[0].Parameter.Value.Data)
}

func TestTransferTRX(t *testing.T) {
	t.Parallel()
	client := GetClient(domain.Shasta)

	from := domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd")
	to := domain.NewTronAddress("TLYm7Ls8eY36bcwQqB1wiyGJ6z3tUMwNNN")
	amount := int64(1000000)

	txExt, err := client.TransferTRX(context.Background(), from, to, amount)
	if err != nil {
		t.Fatalf("Failed to transfer TRX: %v", err)
	}
	t.Logf("Transaction: %v", *txExt.Transaction)
	assert.Equal(t, from.String(), txExt.Transaction.RawData.Contract[0].Parameter.Value.OwnerAddress)
	assert.Equal(t, to.String(), txExt.Transaction.RawData.Contract[0].Parameter.Value.ToAddress)
	assert.Equal(t, amount, txExt.Transaction.RawData.Contract[0].Parameter.Value.Amount)
}
