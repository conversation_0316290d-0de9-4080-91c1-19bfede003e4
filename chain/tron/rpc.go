package tron

import (
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

var quicknodeHost = domain.Tron.QuicknodeRpcBase()

func JsonRpcURL(chain domain.Chain) string {
	if !chain.IsTVM() {
		panic("chain is not tron like")
	}
	switch chain {
	case domain.Tron:
		apiKey := config.GetString("QUICKNODE_API_KEY_V2")
		if apiKey != "" {
			return fmt.Sprintf("%s/%s/jsonrpc", quicknodeHost, apiKey)
		}
		return "https://api.trongrid.io/jsonrpc"
	case domain.Shasta:
		return "https://api.shasta.trongrid.io/jsonrpc"
	}
	return ""
}

func HttpRpcURL(chain domain.Chain) string {
	if !chain.IsTVM() {
		panic("chain is not tron like")
	}
	switch chain {
	case domain.Tron:
		apiKey := config.GetString("QUICKNODE_API_KEY_V2")
		if apiKey != "" {
			return fmt.Sprintf("%s/%s", quicknodeHost, apiKey)
		}
		return "https://api.trongrid.io"
	case domain.Shasta:
		return "https://api.shasta.trongrid.io"
	}
	return ""
}
