package bitcoin

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"time"

	"github.com/jpillora/backoff"
	"github.com/kryptogo/kg-wallet-backend/domain"
	blockchainapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchain-api"
	blockchairapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchair-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

func (c *clientImpl) BlockNumber(ctx context.Context) (uint64, error) {
	blockNumber, err := blockchairapi.Get().GetLatestBlockNumber(ctx)
	if err != nil {
		kglog.ErrorfCtx(ctx, "Failed to get latest block number: %v", err)
		return 0, err
	}
	return blockNumber, nil
}

func (c *clientImpl) NativeBalance(ctx context.Context, address domain.Address) (*big.Int, error) {
	// When context has deadline, split the timeout for two tries
	firstTryCtx := ctx
	if deadline, ok := ctx.Deadline(); ok {
		timeout := time.Until(deadline) / 2
		c, cancel := context.WithTimeout(ctx, timeout)
		firstTryCtx = c
		defer cancel()
	}

	balances, err := blockchainapi.Get().GetAddressInfo(firstTryCtx, address.String())
	if err == nil {
		return big.NewInt(balances.FinalBalance), err
	} else if errors.Is(err, domain.ErrRecordNotFound) {
		return nil, err
	}

	balanceMap, err := blockchairapi.Get().AddressBalances(ctx, address.String())
	if err != nil {
		kglog.ErrorfCtx(ctx, "Failed to get address balances: %v", err)
		return nil, err
	}
	balance, ok := balanceMap[address.String()]
	if !ok {
		return nil, fmt.Errorf("address balance not found")
	}
	return big.NewInt(balance), nil
}

func (c *clientImpl) TokenBalance(ctx context.Context, address domain.Address, tokenID string) (*big.Int, error) {
	if tokenID != "" {
		return nil, fmt.Errorf("token balance not supported for bitcoin")
	}
	return c.NativeBalance(ctx, address)
}

func (c *clientImpl) GetTransactionStatus(ctx context.Context, txHash string) (domain.TransactionStatus, error) {
	tx, err := c.getTransactionData(ctx, txHash)
	if err != nil {
		if errors.Is(err, domain.ErrRecordNotFound) {
			return domain.TransactionStatusUnknown, nil
		}
		kglog.ErrorfCtx(ctx, "Failed to get transaction status: %v", err)
		return domain.TransactionStatusFailed, err
	}

	if tx.BlockID <= 0 {
		return domain.TransactionStatusUnknown, nil
	}
	return domain.TransactionStatusSuccess, nil
}

func (c *clientImpl) WaitUntilTransactionConfirmed(ctx context.Context, txHash string) (domain.TransactionStatus, error) {
	// Configure backoff parameters based on the average block time.
	minWait := c.chain.BlockTime() / 2
	maxWait := c.chain.BlockTime() * 2
	b := &backoff.Backoff{
		Min:    minWait,
		Max:    maxWait,
		Factor: 1.5,
		Jitter: true,
	}
	for {
		select {
		case <-ctx.Done():
			return domain.TransactionStatusUnknown, ctx.Err()
		default:
			status, err := c.GetTransactionStatus(ctx, txHash)
			if err != nil {
				kglog.ErrorfCtx(ctx, "Error checking transaction status: %v", err)
				if ctx.Err() != nil {
					return domain.TransactionStatusUnknown, ctx.Err()
				}
			}
			if status != domain.TransactionStatusUnknown {
				return status, nil
			}

			timer := time.NewTimer(b.Duration())
			select {
			case <-ctx.Done():
				timer.Stop()
				return domain.TransactionStatusUnknown, ctx.Err()
			case <-timer.C:
			}
		}
	}
}

func (c *clientImpl) getTransactionData(ctx context.Context, txHash string) (*domain.BitcoinTransaction, error) {
	// When context has deadline, split the timeout for two tries
	firstTryCtx := ctx
	if deadline, ok := ctx.Deadline(); ok {
		timeout := time.Until(deadline) / 2
		c, cancel := context.WithTimeout(ctx, timeout)
		firstTryCtx = c
		defer cancel()
	}

	tx, err := blockchainapi.Get().GetTransaction(firstTryCtx, txHash)
	if err == nil || errors.Is(err, domain.ErrRecordNotFound) {
		return tx, err
	}

	tx, err = blockchairapi.Get().GetTransaction(ctx, txHash)
	return tx, err
}

func (c *clientImpl) TransactionDetail(ctx context.Context, txHash string) (*domain.TransactionDetail, error) {
	tx, err := c.getTransactionData(ctx, txHash)
	if err != nil {
		if !errors.Is(err, domain.ErrRecordNotFound) {
			kglog.ErrorfCtx(ctx, "Failed to get transaction details: %v", err)
		}
		return nil, err
	}

	timestamp := tx.Time

	detail := &domain.TransactionDetail{
		Chain:     c.chain,
		Hash:      txHash,
		BlockNum:  uint32(tx.BlockID),
		GasUsed:   big.NewInt(int64(tx.Fee)),
		Timestamp: timestamp,
	}

	from, to, realInputs, realOutputs, value := analyzeTransaction(tx)
	detail.From = domain.NewAddressByChain(c.chain, from)
	detail.To = domain.NewAddressByChain(c.chain, to)
	detail.Value = value

	// Include internal transfers only if it's not a 1-to-1 transaction
	// (when either from or to is empty)
	if from == "" || to == "" {
		detail.TransactionTransfers = domain.TransactionTransfers{
			InternalTransfers: buildInternalTransfers(c.chain, from, to, realInputs, realOutputs),
		}
	}

	return detail, nil
}

// analyzeTransaction returns from address, to address, and value for the transaction
func analyzeTransaction(tx *domain.BitcoinTransaction) (from, to string,
	realInputs, realOutputs map[string]*big.Int, value *big.Int) {
	// Aggregate inputs by address
	inputsByAddr := make(map[string]*big.Int)
	for _, input := range tx.Inputs {
		val := big.NewInt(int64(input.Value))
		if existing, ok := inputsByAddr[input.Address]; ok {
			val.Add(val, existing)
		}
		inputsByAddr[input.Address] = val
	}

	// Aggregate outputs by address
	outputsByAddr := make(map[string]*big.Int)
	for _, output := range tx.Outputs {
		val := big.NewInt(int64(output.Value))
		if existing, ok := outputsByAddr[output.Address]; ok {
			val.Add(val, existing)
		}
		outputsByAddr[output.Address] = val
	}

	// Find real input and output addresses (excluding change addresses)
	realInputs = make(map[string]*big.Int)
	realOutputs = make(map[string]*big.Int)

	// An address is a change address if it appears in both inputs and outputs
	for addr, inAmount := range inputsByAddr {
		if outAmount, isChange := outputsByAddr[addr]; isChange {
			// If output amount is less than input amount, the difference went to other addresses
			if outAmount.Cmp(inAmount) < 0 {
				realInputs[addr] = new(big.Int).Sub(inAmount, outAmount)
			}
		} else {
			realInputs[addr] = inAmount
		}
	}

	for addr, outAmount := range outputsByAddr {
		if inAmount, isChange := inputsByAddr[addr]; isChange {
			// If output amount is more than input amount, the extra amount came from other addresses
			if outAmount.Cmp(inAmount) > 0 {
				realOutputs[addr] = new(big.Int).Sub(outAmount, inAmount)
			}
		} else {
			realOutputs[addr] = outAmount
		}
	}

	switch {
	case len(realInputs) == 1 && len(realOutputs) == 1:
		// One-to-one transfer
		for addr := range realInputs {
			from = addr
		}
		for addr, outAmount := range realOutputs {
			to = addr
			value = outAmount
		}
	case len(realInputs) == 1:
		// One-to-many transfer
		for addr := range realInputs {
			from = addr
		}
		to = "" // Multiple recipients
	case len(realOutputs) == 1:
		// Many-to-one transfer
		from = "" // Multiple senders
		for addr, val := range realOutputs {
			to = addr
			value = val
		}
	default:
		// Complex transaction: no clear from/to
		from = ""
		to = ""
	}
	return
}

// buildInternalTransfers creates internal transfers for complex transactions
func buildInternalTransfers(chain domain.Chain, from, to string, realInputs, realOutputs map[string]*big.Int) []*domain.NativeTokenTransfer {
	transfers := make([]*domain.NativeTokenTransfer, 0)

	// For one-to-many: use the known from address
	if from != "" && to == "" {
		for recipient, value := range realOutputs {
			transfers = append(transfers, &domain.NativeTokenTransfer{
				From:   domain.NewAddressByChain(chain, from),
				To:     domain.NewAddressByChain(chain, recipient),
				Amount: value,
			})
		}
		return transfers
	}

	// For many-to-one: use the known to address
	if from == "" && to != "" {
		for sender, value := range realInputs {
			transfers = append(transfers, &domain.NativeTokenTransfer{
				From:   domain.NewAddressByChain(chain, sender),
				To:     domain.NewAddressByChain(chain, to),
				Amount: value,
			})
		}
		return transfers
	}

	// For complex transactions (many-to-many)
	// First add all inputs going to empty address
	for sender, value := range realInputs {
		transfers = append(transfers, &domain.NativeTokenTransfer{
			From:   domain.NewAddressByChain(chain, sender),
			To:     domain.NewAddressByChain(chain, ""),
			Amount: value,
		})
	}

	// Then add all outputs coming from empty address
	for recipient, value := range realOutputs {
		transfers = append(transfers, &domain.NativeTokenTransfer{
			From:   domain.NewAddressByChain(chain, ""),
			To:     domain.NewAddressByChain(chain, recipient),
			Amount: value,
		})
	}

	return transfers
}

func (c *clientImpl) BroadcastRawTransaction(ctx context.Context, rawTx string) (string, error) {
	panic("not implemented")
}
