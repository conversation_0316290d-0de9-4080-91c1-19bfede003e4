package bitcoin

import (
	"context"
	"math/big"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	blockchainapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchain-api"
	blockchairapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchair-api"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestBlockNumber(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	blockchairapi.InitDefault(domain.NewAllPassRateLimiter())
	client := GetClient(domain.Bitcoin)

	ctx := context.Background()

	blockNumber, err := client.BlockNumber(ctx)
	assert.NoError(t, err)
	assert.NotZero(t, blockNumber, "Latest block number should be non-zero")

	// Log latest block number for manual verification
	t.Logf("Latest Block Number: %d", blockNumber)
}

func TestNativeBalance(t *testing.T) {
	blockchainapi.InitDefault(domain.NewAllPassRateLimiter())
	blockchairapi.InitDefault(domain.NewAllPassRateLimiter())
	client := GetClient(domain.Bitcoin)

	// Test address with known balance on Bitcoin mainnet
	// This is an example address, replace with a real address that you know has a balance
	address := domain.NewStrAddress("**********************************")

	balance, err := client.NativeBalance(context.Background(), address)

	assert.NoError(t, err)
	assert.NotNil(t, balance)
	assert.True(t, balance.Sign() > 0, "Balance should be non-negative")

	// Convert balance from satoshis to BTC for logging
	btcBalance := new(big.Float).Quo(new(big.Float).SetInt(balance), big.NewFloat(1e8))
	t.Logf("Balance: %s BTC", btcBalance.Text('f', 8))
}

func TestGetTransactionStatus(t *testing.T) {
	blockchainapi.InitDefault(domain.NewAllPassRateLimiter())
	blockchairapi.InitDefault(domain.NewAllPassRateLimiter())
	client := GetClient(domain.Bitcoin)

	testCases := []struct {
		name        string
		txHash      string
		expectError bool
		expected    domain.TransactionStatus
	}{
		{
			name:     "Successful Transaction",
			txHash:   "2f6eeb0ba44667af26e148f589d5bc539db4e259026a1fd4605ca9fc8fe2a765",
			expected: domain.TransactionStatusSuccess,
		},
		{
			name:     "Non exist transaction",
			txHash:   "2f6eeb0ba44667af26e148f589d5bc539db4e259026a1fd4605ca9fc8fe2a764",
			expected: domain.TransactionStatusUnknown,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			status, err := client.GetTransactionStatus(context.Background(), tc.txHash)
			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expected, status)
			}
		})
	}
}

func TestWaitUntilTransactionConfirmed(t *testing.T) {
	blockchainapi.InitDefault(domain.NewAllPassRateLimiter())
	blockchairapi.InitDefault(domain.NewAllPassRateLimiter())
	client := GetClient(domain.Bitcoin)

	testCases := []struct {
		name        string
		txHash      string
		expected    domain.TransactionStatus
		expectError bool
	}{
		{
			name:     "Successful Transaction",
			txHash:   "2f6eeb0ba44667af26e148f589d5bc539db4e259026a1fd4605ca9fc8fe2a765",
			expected: domain.TransactionStatusSuccess,
		},
		{
			name:        "Non exist transaction",
			txHash:      "2f6eeb0ba44667af26e148f589d5bc539db4e259026a1fd4605ca9fc8fe2a764",
			expected:    domain.TransactionStatusUnknown,
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
			defer cancel()

			status, err := client.WaitUntilTransactionConfirmed(ctx, tc.txHash)
			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expected, status)
			}
		})
	}
}

func Test1to1TransferTransactionDetail(t *testing.T) {
	blockchainapi.InitDefault(domain.NewAllPassRateLimiter())
	blockchairapi.InitDefault(domain.NewAllPassRateLimiter())
	client := GetClient(domain.Bitcoin)
	t.Run("1 input and 1 output", func(t *testing.T) {
		t.Parallel()
		txHash := "d81d645f9a8bbd6f847474458a3a82f57b86bc4c43c83150071eedb532d9c18b"

		detail, err := client.TransactionDetail(context.Background(), txHash)
		t.Logf("err: %v", err)
		assert.NoError(t, err)
		assert.NotNil(t, detail)

		t.Logf("Transaction Hash: %s", detail.Hash)
		t.Logf("Block Number: %d", detail.BlockNum)
		t.Logf("From: %s", detail.From)
		t.Logf("To: %s", detail.To)
		t.Logf("Data: %s", detail.Data)
		t.Logf("Method ID: %s", detail.MethodID)
		t.Logf("Value: %s", detail.Value.String())
		t.Logf("Gas Price: %s", detail.GasPrice.String())
		t.Logf("Gas Used: %s", detail.GasUsed.String())
		t.Logf("Timestamp: %v", detail.Timestamp.Unix())

		assert.Equal(t, txHash, detail.Hash)
		assert.Equal(t, uint32(866632), detail.BlockNum)
		assert.Equal(t, big.NewInt(6333), detail.GasUsed)

		assert.Equal(t, domain.NewAddressByChain(domain.Bitcoin, "******************************************"), detail.From)
		assert.Equal(t, domain.NewAddressByChain(domain.Bitcoin, "******************************************"), detail.To)
		assert.Equal(t, big.NewInt(144732), detail.Value)

		assert.Equal(t, 0, len(detail.InternalTransfers))
		assert.Equal(t, 0, len(detail.TokenTransfers))
	})

	t.Run("1 input and 2 outputs with 1 being change", func(t *testing.T) {
		t.Parallel()
		txHash := "8757449612d9a8caad40a8eacac0457d41595ae010c88dc83ddc8623c5c2e8e7"

		detail, err := client.TransactionDetail(context.Background(), txHash)
		assert.NoError(t, err)
		assert.NotNil(t, detail)

		t.Logf("Transaction Hash: %s", detail.Hash)
		t.Logf("Block Number: %d", detail.BlockNum)
		t.Logf("From: %s", detail.From)
		t.Logf("To: %s", detail.To)
		t.Logf("Data: %s", detail.Data)
		t.Logf("Method ID: %s", detail.MethodID)
		t.Logf("Value: %s", detail.Value.String())
		t.Logf("Gas Price: %s", detail.GasPrice.String())
		t.Logf("Gas Used: %s", detail.GasUsed.String())
		t.Logf("Timestamp: %v", detail.Timestamp.Unix())

		assert.Equal(t, txHash, detail.Hash)
		assert.Equal(t, uint32(868785), detail.BlockNum)
		assert.Equal(t, big.NewInt(28200), detail.GasUsed)

		assert.Equal(t, domain.NewAddressByChain(domain.Bitcoin, "******************************************"), detail.From)
		assert.Equal(t, domain.NewAddressByChain(domain.Bitcoin, "******************************************"), detail.To)
		assert.Equal(t, big.NewInt(210000), detail.Value)

		assert.Equal(t, 0, len(detail.InternalTransfers))
		assert.Equal(t, 0, len(detail.TokenTransfers))
	})

	t.Run("2 inputs and 2 outputs but it's 1-to-1 transfer", func(t *testing.T) {
		t.Parallel()
		txHash := "277bac72568fe0957b90ab71cf9f4b5dc9c4a8504f39c9b4efead58410c263aa"
		detail, err := client.TransactionDetail(context.Background(), txHash)
		assert.NoError(t, err)
		assert.NotNil(t, detail)

		t.Logf("Transaction Hash: %s", detail.Hash)
		t.Logf("Block Number: %d", detail.BlockNum)
		t.Logf("From: %s", detail.From)
		t.Logf("To: %s", detail.To)
		t.Logf("Data: %s", detail.Data)
		t.Logf("Method ID: %s", detail.MethodID)
		t.Logf("Value: %s", detail.Value.String())
		t.Logf("Gas Price: %s", detail.GasPrice.String())
		t.Logf("Gas Used: %s", detail.GasUsed.String())
		t.Logf("Timestamp: %v", detail.Timestamp.Unix())

		assert.Equal(t, txHash, detail.Hash)
		assert.Equal(t, uint32(774326), detail.BlockNum)
		assert.Equal(t, big.NewInt(418), detail.GasUsed)

		assert.Equal(t, domain.NewAddressByChain(domain.Bitcoin, "******************************************"), detail.From)
		assert.Equal(t, domain.NewAddressByChain(domain.Bitcoin, "******************************************"), detail.To)
		assert.Equal(t, big.NewInt(31000), detail.Value)

		assert.Equal(t, 0, len(detail.InternalTransfers))
		assert.Equal(t, 0, len(detail.TokenTransfers))
	})
}

func TestComplicatedTransferTransactionDetail(t *testing.T) {
	blockchainapi.InitDefault(domain.NewAllPassRateLimiter())
	blockchairapi.InitDefault(domain.NewAllPassRateLimiter())
	client := GetClient(domain.Bitcoin)

	t.Run("One to many", func(t *testing.T) {
		txHash := "4c2138601a0ab50228042e2f44a153106965e7b901c90966ee694f571bfba97e"

		detail, err := client.TransactionDetail(context.Background(), txHash)
		t.Logf("err: %v", err)
		assert.NoError(t, err)
		assert.NotNil(t, detail)

		t.Logf("Transaction Hash: %s", detail.Hash)
		t.Logf("Block Number: %d", detail.BlockNum)
		t.Logf("From: %s", detail.From)
		t.Logf("To: %s", detail.To)
		t.Logf("Data: %s", detail.Data)
		t.Logf("Method ID: %s", detail.MethodID)
		t.Logf("Value: %s", detail.Value.String())
		t.Logf("Gas Price: %s", detail.GasPrice.String())
		t.Logf("Gas Used: %s", detail.GasUsed.String())
		t.Logf("Timestamp: %v", detail.Timestamp.Unix())

		assert.Equal(t, txHash, detail.Hash)
		assert.Equal(t, uint32(874224), detail.BlockNum)
		assert.Equal(t, big.NewInt(11380), detail.GasUsed)

		assert.Equal(t, domain.NewAddressByChain(domain.Bitcoin, "******************************************"), detail.From)
		assert.Equal(t, domain.NewAddressByChain(domain.Bitcoin, ""), detail.To)
		assert.Nil(t, detail.Value)

		assert.Equal(t, 13, len(detail.InternalTransfers))
		assert.Equal(t, 0, len(detail.TokenTransfers))

		for _, transfer := range detail.InternalTransfers {
			t.Logf("Token Transfer: %+v", transfer)
		}
		expectedInternalTransfers := []*domain.NativeTokenTransfer{
			{
				From:   domain.NewAddressByChain(domain.Bitcoin, "******************************************"),
				To:     domain.NewAddressByChain(domain.Bitcoin, "******************************************"),
				Amount: big.NewInt(787465),
			},
			{
				From:   domain.NewAddressByChain(domain.Bitcoin, "******************************************"),
				To:     domain.NewAddressByChain(domain.Bitcoin, "**********************************"),
				Amount: big.NewInt(2599374),
			},
		}
		assert.Subset(t, detail.InternalTransfers, expectedInternalTransfers)
	})

	t.Run("Many to many", func(t *testing.T) {
		txHash := "99134f0541de388bec35f276827f3d70b0a2b10078eab10e451ed5826658f79d"

		detail, err := client.TransactionDetail(context.Background(), txHash)
		t.Logf("err: %v", err)
		assert.NoError(t, err)
		assert.NotNil(t, detail)

		t.Logf("Transaction Hash: %s", detail.Hash)
		t.Logf("Block Number: %d", detail.BlockNum)
		t.Logf("From: %s", detail.From)
		t.Logf("To: %s", detail.To)
		t.Logf("Data: %s", detail.Data)
		t.Logf("Method ID: %s", detail.MethodID)
		t.Logf("Value: %s", detail.Value.String())
		t.Logf("Gas Price: %s", detail.GasPrice.String())
		t.Logf("Gas Used: %s", detail.GasUsed.String())
		t.Logf("Timestamp: %v", detail.Timestamp.Unix())

		assert.Equal(t, txHash, detail.Hash)
		assert.Equal(t, uint32(866632), detail.BlockNum)
		assert.Equal(t, big.NewInt(21660), detail.GasUsed)

		assert.Equal(t, domain.NewAddressByChain(domain.Bitcoin, ""), detail.From)
		assert.Equal(t, domain.NewAddressByChain(domain.Bitcoin, ""), detail.To)
		assert.Nil(t, detail.Value)

		assert.Equal(t, 5, len(detail.InternalTransfers))
		assert.Equal(t, 0, len(detail.TokenTransfers))

		for _, transfer := range detail.InternalTransfers {
			t.Logf("Token Transfer: %+v", transfer)
		}
		assert.ElementsMatch(t, detail.InternalTransfers, []*domain.NativeTokenTransfer{
			{
				From:   domain.NewAddressByChain(domain.Bitcoin, "**************************************************************"),
				To:     domain.NewAddressByChain(domain.Bitcoin, ""),
				Amount: big.NewInt(263257139),
			},
			{
				From:   domain.NewAddressByChain(domain.Bitcoin, "**************************************************************"),
				To:     domain.NewAddressByChain(domain.Bitcoin, ""),
				Amount: big.NewInt(366676000),
			},
			{
				From:   domain.NewAddressByChain(domain.Bitcoin, ""),
				To:     domain.NewAddressByChain(domain.Bitcoin, "******************************************"),
				Amount: big.NewInt(609280000),
			},
			{
				From:   domain.NewAddressByChain(domain.Bitcoin, ""),
				To:     domain.NewAddressByChain(domain.Bitcoin, "**********************************"),
				Amount: big.NewInt(18782381),
			},
			{
				From:   domain.NewAddressByChain(domain.Bitcoin, ""),
				To:     domain.NewAddressByChain(domain.Bitcoin, "**********************************"),
				Amount: big.NewInt(1849098),
			},
		})
	})
}
