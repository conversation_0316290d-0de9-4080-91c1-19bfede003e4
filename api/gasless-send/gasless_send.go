package gaslesssend

import (
	"encoding/json"
	"errors"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/chain/tron"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	gaslesssend "github.com/kryptogo/kg-wallet-backend/pkg/service/gasless-send"
	assetpro "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	gaslesssendv2 "github.com/kryptogo/kg-wallet-backend/service/gasless-send"
)

type getQuoteReq struct {
	From         string `form:"from_address" binding:"required"`
	Recipient    string `form:"recipient_address" binding:"required"`
	ChainID      string `form:"chain_id" binding:"required"`
	TokenAddress string `form:"token_address" binding:"required"`
	Amount       string `form:"amount"`
}

// GetQuote .
func GetQuote(c *gin.Context) {
	ctx := c.Request.Context()
	params := &getQuoteReq{}

	if kgErr := util.ToGinContextExt(c).BindQuery(params); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	chain := domain.IDToChain(params.ChainID)
	from := domain.NewTronAddress(params.From)
	recipient := domain.NewTronAddress(params.Recipient)
	tokenAddress := domain.NewTronAddress(params.TokenAddress)
	quote, kgErr := gaslesssend.GetQuote(ctx, orgID, chain, from, recipient, tokenAddress, params.Amount)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, quote)
}

type initReq struct {
	ChainID          string   `json:"chain_id" binding:"required"`
	FromAddress      string   `json:"from_address" binding:"required"`
	RecipientAddress string   `json:"recipient_address" binding:"required"`
	TokenAddress     string   `json:"token_address" binding:"required"`
	AmountStr        string   `json:"amount_str" binding:"required"`
	FeeAmount        string   `json:"fee_amount" binding:"required"`
	SignedTxs        []string `json:"signed_txs" binding:"required"`
}

// Init creates a new gasless send request
func Init(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")
	clientID := c.GetString("client_id")

	if !application.IsKgWallet(ctx, clientID) && !application.IsTongBao(ctx, clientID) {
		// Only supports KryptoGO and TongBao org for now, because for other orgs we need to consider profit sharing and sending TRX to KG org to cover energy rent cost. TongBao may have different requirement so we can implement it later.
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("org not supported yet"), nil))
		return
	}

	params := &initReq{}

	if kgErr := util.ToGinContextExt(c).BindJson(params); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	id, kgErr := gaslesssend.Create(ctx, &domain.GaslessSend{
		OrgID:        orgID,
		UID:          uid,
		ChainID:      params.ChainID,
		From:         params.FromAddress,
		Recipient:    params.RecipientAddress,
		TokenAddress: params.TokenAddress,
		Amount:       params.AmountStr,
		Fee:          params.FeeAmount,
		SignedTxs:    params.SignedTxs,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	resp := struct {
		ID int `json:"gasless_send_id"`
	}{ID: id}
	response.OK(c, resp)
}

// GetByID .
func GetByID(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}
	sendData, kgErr := gaslesssend.GetByID(ctx, uid, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, sendData)
}

// Handle .
func Handle(c *gin.Context) {
	ctx := c.Request.Context()
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}
	kgErr := gaslesssend.Handle(ctx, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}

type enableReq struct {
	ChainID string `json:"chain_id" binding:"required"`
}

// Enable enables gasless send for organization's specific chain. Returns tx hash if success, or empty with nil error if it's already enabled
func Enable(c *gin.Context) {
	ctx := c.Request.Context()
	orgID, err := strconv.Atoi(c.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid org id")
		return
	}
	params := &enableReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	txHash, kgErr := gaslesssend.Enable(ctx, orgID, domain.IDToChain(params.ChainID))
	kglog.InfoWithDataCtx(ctx, "gasless send enabled", map[string]interface{}{"org_id": orgID, "chain_id": params.ChainID, "tx": txHash})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}

type profitMarginRateReq struct {
	ChainID string `form:"chain_id" binding:"required"`
}

type profitMarginRateResp struct {
	ProfitMarginRate float64 `json:"profit_margin_rate"`
	FeeAddress       string  `json:"fee_address"`
}

// GetProfitMarginRate returns profit margin rate for gasless send
func GetProfitMarginRate(c *gin.Context) {
	ctx := c.Request.Context()

	req := profitMarginRateReq{}

	if kgErr := util.ToGinContextExt(c).BindQuery(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	walletType := getWalletType(req.ChainID)
	if len(walletType) == 0 {
		response.BadRequest(c, code.ChainIDNotSupported)
		return
	}

	// fee address is always set to KryptoGO wallet address
	kgWallet, err := organization.GetOrgWallet(c.Request.Context(), 1, walletType)
	if err != nil {
		response.KGError(c, code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil))
		return
	}

	profitRate, kgErr := assetpro.GetProfitRate(ctx, orgID, domain.ProfitRateServiceTypeSendGasless)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	kgMinimumRevenueRate := profitRate.GetKgMinimumRevenueRate()
	profitMarginRate := profitRate.ProfitRate.Add(kgMinimumRevenueRate)

	response.OK(c, profitMarginRateResp{
		ProfitMarginRate: profitMarginRate.InexactFloat64(),
		FeeAddress:       kgWallet.WalletAddress,
	})
}

func getWalletType(chainID string) string {
	chain := domain.IDToChain(chainID)
	if chain.IsTVM() {
		return "tron"
	} else if chain.IsEVM() {
		return "evm"
	}
	return ""
}

// InitReq represents the request payload for initializing a gasless-send transaction.
type InitReq struct {
	ChainID   string   `json:"chain_id" binding:"required"`
	From      string   `json:"from" binding:"required"`
	SignedTxs []string `json:"signed_txs" binding:"required,min=2"`

	// parsed fields
	ParsedTxs []*tron.Transaction `json:"-"`
}

func (r *InitReq) AfterBinding(c *gin.Context) error {
	parsedTxs := make([]*tron.Transaction, len(r.SignedTxs))
	for i, tx := range r.SignedTxs {
		parsedTx := &tron.Transaction{}
		if err := json.Unmarshal([]byte(tx), parsedTx); err != nil {
			return err
		}
		parsedTxs[i] = parsedTx
	}
	r.ParsedTxs = parsedTxs
	return nil
}

// Init initializes a new gasless-send transaction.
func InitV2(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)
	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	var req InitReq
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}
	if req.ChainID != "tron" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "only tron is supported")
		return
	}
	chain := domain.Tron

	// Create the gasless-send transaction
	gaslessSendID, kgErr := gaslesssendv2.Create(ctx, &gaslesssendv2.CreateParams{
		OrgID:     orgID,
		UID:       uid,
		Chain:     chain,
		From:      domain.NewTronAddress(req.From),
		SignedTxs: req.ParsedTxs,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Respond with the created gasless-send ID
	response.OK(c, gin.H{
		"id": gaslessSendID,
	})
}

// Handle .
func HandleV2(c *gin.Context) {
	ctx := c.Request.Context()
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}
	kgErr := gaslesssendv2.Handle(ctx, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}

// GetByID retrieves a gasless-send transaction by its ID.
func GetByIDV2(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}

	resp, kgErr := gaslesssendv2.Get(ctx, uid, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, resp)
}
