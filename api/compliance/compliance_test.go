package compliance

import (
	"bytes"
	"context"
	"encoding/json"
	"math/rand"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/compliance"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type complianceTaskCallbackSuiteV2 struct {
	suite.Suite
	Uid   string
	IdvID *int
	CddID *int
}

func (suite *complianceTaskCallbackSuiteV2) SetupSuite() {
	// set up rdb
	rdb.Reset()
	assert.Nil(suite.T(), rdbtest.CreateStudioDefault(rdb.Get()))

	users, userIDs := dbtest.Users()
	suite.Uid = userIDs[0]
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(suite.T(), err)

	// set up db for customer
	assert.Nil(suite.T(), rdbtest.CreateCustomers(rdb.Get(), []*model.Customer{{OrganizationID: 1, UID: suite.Uid}}))

	// set up db for case submissions
	suite.IdvID = util.Ptr(10 + rand.Intn(10))
	suite.CddID = util.Ptr(10 + rand.Intn(10))
	now := time.Now()
	caseSubmissions := []*model.CaseSubmission{
		{
			OrganizationID: 1,
			UID:            suite.Uid,
			SubmittedAt:    now.AddDate(0, 0, 1),
			UpdatedAt:      now.AddDate(0, 0, 1),
			FormID:         util.Ptr(10 + rand.Intn(10)),
			IdvID:          suite.IdvID,
			CddID:          suite.CddID,
		},
	}
	assert.Nil(suite.T(), rdb.Get().Create(&caseSubmissions).Error)

	compliance.Init(rdb.GormRepo())

	organization.Init(organization.InitParam{
		StudioOrgRepo:  rdb.GormRepo(),
		StudioRoleRepo: rdb.GormRepo(),
	})
}

func TestComplianceTaskCallbackV2(t *testing.T) {
	suite.Run(t, new(complianceTaskCallbackSuiteV2))
}

type reviewReason struct {
	Code        int    `json:"code"`
	Description string `json:"description"`
}

type rejectReason struct {
	Code        int    `json:"code"`
	Description string `json:"description"`
}

type testParamsAndExpected struct {
	Params   interface{}
	Expected interface{}
}

func (suite *complianceTaskCallbackSuiteV2) TestIdvCallbackV2() {
	ctx := context.Background()
	url := "/v1/kyc/idv_callback"
	r := gin.Default()
	r.POST(url, IdvCallback)
	idvStatusParamsAndExpected := []testParamsAndExpected{
		{
			Params:   0,
			Expected: "accept",
		},
		{
			Params:   1,
			Expected: "review",
		},
		{
			Params:   2,
			Expected: "reject",
		},
		{
			Params:   3,
			Expected: "pending",
		},
		{
			Params:   4,
			Expected: "initial",
		},
	}
	for _, testParamsAndExpected := range idvStatusParamsAndExpected {
		body := map[string]interface{}{
			"idv_task_id":        suite.IdvID,
			"country":            "taiwan",
			"customer_reference": suite.Uid,
			"id_type":            "ID_CARD",
			"expected_name":      "王陽明",
			"expected_birthday":  "1990-01-01",
			"state":              testParamsAndExpected.Params,
			"review_reasons": []reviewReason{
				{
					Code:        400,
					Description: "review_description",
				},
			},
			"reject_reasons": []rejectReason{
				{
					Code:        100,
					Description: "reject_description",
				},
			},
			"audit_status": "Accepted",
		}

		jsonStr, err := json.Marshal(body)
		assert.Nil(suite.T(), err)
		req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))

		assert.Nil(suite.T(), err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(suite.T(), http.StatusOK, w.Code)

		var resp struct {
			Code int `json:"code"`
		}

		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(suite.T(), err)
		assert.Equal(suite.T(), 0, resp.Code)
		customer, kgErr := rdb.GetCustomerByUID(ctx, suite.Uid, 1)
		assert.Nil(suite.T(), kgErr)
		assert.Equal(suite.T(), "王陽明", *customer.LegalName)
		assert.Equal(suite.T(), testParamsAndExpected.Expected, customer.IDVStatus.String())
	}
}

func (suite *complianceTaskCallbackSuiteV2) TestCddCallbackV2() {
	ctx := context.Background()
	url := "/v1/kyc/search_task_callback"
	r := gin.Default()
	r.POST(url, SearchTaskCallback)

	body := map[string]interface{}{
		"task_id":            suite.CddID,
		"customer_reference": suite.Uid,
		"report": map[string]interface{}{
			"sanction_matched": true,
			"potential_risk":   66,
		},
	}

	jsonStr, err := json.Marshal(body)
	assert.Nil(suite.T(), err)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))

	assert.Nil(suite.T(), err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var resp struct {
		Code int `json:"code"`
	}

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	assert.Nil(suite.T(), err)
	assert.Equal(suite.T(), 0, resp.Code)
	customer, kgErr := rdb.GetCustomerByUID(ctx, suite.Uid, 1)
	assert.Nil(suite.T(), kgErr)
	assert.Equal(suite.T(), uint8(66), *customer.CDDRiskScore)
	assert.Equal(suite.T(), true, *customer.SanctionMatched)
}
