package compliance

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/compliance"
	restcountriesapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/restcountries-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/l10n"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	complianceSvc "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/compliance"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/notification"
	"golang.org/x/text/language"
	"gorm.io/gorm"
)

func init() {
	if config.IsProd() {
		blackListedCountriesForBuyCrypto["TWN"] = util.Void{}
	}
}

const (
	// MaxRetryCount max retry count
	MaxRetryCount = 5
)

var (
	blackListedCountriesForBuyCrypto = map[string]util.Void{
		// North Korea
		"PRK": {},
		// Singapore
		"SGP": {},
		// China
		"CHN": {},
		// Crimea
		"RUS": {},
		// Donetsk People's Republic
		// Luhansk People's Republic
		"UKR": {},
		// Iran
		"IRN": {},
		// Syria
		"SYR": {},
		// United States
		"USA": {},
		// Puerto Rico
		"PRI": {},
		// Samoa
		"WSM": {},
		// Guam
		"GUM": {},
		// Northern Mariana Islands
		"MNP": {},
		// United States Minor Outlying Islands
		"UMI": {},
		// Virgin Islands, U.S.
		"VIR": {},
		// Cuba
		"CUB": {},
		// Canada
		"CAN": {},
	}
)

type initIdvResp struct {
	Code int     `json:"code"`
	Data idvData `json:"data"`
}

type idvData struct {
	URL string `json:"url"`
}

// InitIdv init idv task
func InitIdv(c *gin.Context) {
	ctx := c.Request.Context()
	req := compliance.IdvTaskRequest{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	uid := auth.GetUID(c)
	user, _ := rdb.GormRepo().GetUser(ctx, uid, "", true, nil)
	if user == nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "user does not exist")
		return
	}
	req.KgUID = uid

	clientID := auth.GetClientID(c)
	org, kgErr := organization.GetStudioOrgRepo().GetOrganizationByOAuthClientID(ctx, clientID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if org.ComplianceAPIKey == nil {
		kglog.ErrorWithDataCtx(ctx, "compliance api key not set yet", map[string]interface{}{
			"org_id":    org.ID,
			"client_id": clientID,
		})
		response.ForbiddenErrorWithMsg(c, code.StudioOrganizationComplianceAPIKeyNotSet, "compliance api key not set yet")
		return
	}

	// check kyc state
	if user.KycState != nil &&
		(*user.KycState == domain.KycStatusPending.String() || *user.KycState == domain.KycStatusVerified.String()) {
		response.ForbiddenErrorWithMsg(c, code.KycStateError, "kyc state is not init")
		return
	}

	kycResult, err := rdb.GetKycResult(ctx, uid)
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			kglog.DebugWithDataCtx(ctx, "get kyc result failed", map[string]interface{}{
				"err": err.Error(),
			})
			response.ErrorWithMsg(c, http.StatusInternalServerError, code.DBError, err.Error(), nil)
			return
		}
	} else {
		// if search task is rejected, user cannot init idv again
		if kycResult.Accepted != nil && !*kycResult.Accepted {
			kglog.DebugWithDataCtx(ctx, "kyc result is rejected", map[string]interface{}{
				"uid":    uid,
				"result": kycResult,
			})
			response.ForbiddenErrorWithMsg(c, code.KycStateError, "high risk customer")
			return
		}
	}

	// send idv task to compliance
	selfHost := config.GetString("SELF_HOST")
	if len(selfHost) == 0 {
		response.ErrorWithMsg(c, http.StatusInternalServerError, code.InternalError, "self host is empty", nil)
		return
	}
	localeTag, i, confidence := compliance.LangMatcher.Match(language.Make(req.Locale))
	assumedTag := compliance.LangTags[i]
	kglog.DebugfCtx(ctx, "localeTag: %v, assumedTag: %v, confidence: %v", localeTag, assumedTag, confidence)
	if jumioLocale, ok := compliance.LangMapForJumio[assumedTag]; ok {
		req.Locale = jumioLocale
	} else {
		req.Locale = assumedTag.String()
	}
	req.SumsubApplicantLevel = compliance.ApplicantLevelBuyCrypto
	req.WorkflowID = 200 // ID + Identity, camera + upload
	req.CallbackURL = selfHost + "/v1/kyc/idv_callback"
	req.CustomerReference = uid
	req.AutoCreateDDTask = true
	req.DDTaskCallbackURL = selfHost + "/v1/kyc/search_task_callback"
	req.DDTaskSearchSettingID = config.GetInt("DD_TASK_SEARCH_SETTING_ID_TW")
	if req.Country != "TWN" {
		req.DDTaskSearchSettingID = config.GetInt("DD_TASK_SEARCH_SETTING_ID_EN")
	}
	req.UseLocaleDefaultSearchSetting = true
	kglog.DebugWithDataCtx(ctx, "idv task request", req)
	// FIXME: using DI to inject client
	client := compliance.NewAPIClient(*org.ComplianceAPIKey)
	initResp, resp, err := client.InitIdv(ctx, &req)
	if err != nil {
		kglog.DebugWithData("init idv failed", map[string]interface{}{
			"err": err.Error(),
		})
		response.ErrorWithMsg(c, http.StatusInternalServerError, code.FailedToSendToCompliance, err.Error(), nil)
		return
	}

	if resp.StatusCode() != http.StatusOK {
		var message string
		if initResp != nil {
			message = initResp.Message
		}
		kglog.DebugWithData("init idv failed", map[string]interface{}{
			"status":  resp.Status(),
			"body":    string(resp.Body()),
			"message": message,
		})
		response.ErrorWithMsg(c, http.StatusInternalServerError, code.FailedToSendToCompliance, resp.String(), nil)
		return
	}

	// save idv log to rdb

	if err := rdb.CreateIdvTaskLog(ctx, &dbmodel.IdvTaskLog{
		UID:              uid,
		IdvTaskID:        int32(initResp.IdvTaskID),
		IdvTaskTimestamp: initResp.Timestamp,
		CreatedAt:        time.Now(),
	}); err != nil {
		response.ErrorWithMsg(c, http.StatusInternalServerError, code.DBError, err.Error(), nil)
		return
	}

	// Previously, we set this to pending to avoid user to init idv again and again
	// But since idv engine (sumsub, jumio) does not charge for creating a task, we want to allow user to init idv
	// until the user has completed one of the idv task
	// The state we used before:
	// "kyc_state": domain.KycStatusPending,

	if err := rdb.GormRepo().SetUser(ctx, &domain.UserData{
		UserInfo: domain.UserInfo{
			UID: req.CustomerReference,
		},
		KycState: util.Ptr(domain.KycStatusUnverified.String()),
	}); err != nil {
		kglog.DebugWithDataCtx(ctx, "set user failed", map[string]interface{}{
			"err": err.Error(),
		})
		response.ErrorWithMsg(c, http.StatusInternalServerError, code.DBError, err.Error(), nil)
		return
	}

	c.JSON(http.StatusOK, &initIdvResp{
		Code: 0,
		Data: idvData{
			URL: initResp.URL,
		},
	})
}

// IdvCallback idv callback
func IdvCallback(c *gin.Context) {
	ctx := c.Request.Context()
	req := compliance.IdvTaskDetailResp{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "idv callback parse failed", map[string]interface{}{
			"err": kgErr.String(),
			"req": c.Request.Body,
		})
		response.OK(c, nil)
		return
	}

	paramGetCases := domain.GetCasesParams{IdvID: &req.IDVTaskID}

	caseSubmission, kgErr := complianceSvc.GetComplianceRepo().GetCaseSubmission(ctx, &paramGetCases)
	if kgErr != nil { // buy crypto v1 flow
		if kgErr.Code == code.RecordNotFound {
			if kgErr := idvCallbackV1(c, oauth.ClientID(c), &req); kgErr == nil {
				response.OK(c, nil)
				return
			} else {
				kglog.ErrorWithDataCtx(ctx, "idv callback v1 failed", map[string]interface{}{
					"err": kgErr.String(),
					"req": req,
				})
				response.OK(c, nil)
				return
			}
		}
		kglog.ErrorWithDataCtx(ctx, "idv, get cases failed", map[string]interface{}{
			"err":    kgErr.String(),
			"idv_id": req.IDVTaskID,
		})
		response.OK(c, nil)
		return
	}

	param := &complianceSvc.UpdateIdvByCallbackParams{
		IdvStatus: compliance.IdvStatusFromIotaToDomain[compliance.State(req.State)],
		Birthday:  req.Birthday,
	}

	if req.IDNumber != nil && *req.IDNumber != "" {
		switch req.IDType {
		case compliance.IDTypePassport:
			param.PassportNumber = req.IDNumber
		case compliance.IDTypeIDCard:
			param.NationalID = req.IDNumber
		}
	}

	if len(req.ExpectedName) > 0 {
		param.LegalName = &req.ExpectedName
	}

	if len(req.Country) > 0 {
		param.Country = &req.Country
	}

	if req.Gender != nil {
		param.Gender = compliance.ParseGenderFromCompliance(*req.Gender)
	}

	if kgErr := complianceSvc.UpdateIdvByCallback(ctx,
		caseSubmission.OrganizationID, caseSubmission.UID, param); kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "update idv failed", map[string]interface{}{
			"err":             kgErr.String(),
			"uid":             caseSubmission.UID,
			"organization_id": caseSubmission.OrganizationID,
			"idv_id":          req.IDVTaskID,
		})
		response.OK(c, nil)
		return
	}

	response.OK(c, nil)
}

// SearchTaskCallback search task callback
func SearchTaskCallback(c *gin.Context) {
	ctx := c.Request.Context()
	req := compliance.SearchTaskCallbackRequest{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "cdd callback parse failed", map[string]interface{}{
			"err": kgErr.String(),
			"req": c.Request.Body,
		})
		response.OK(c, nil)
		return
	}

	caseSubmission, kgErr := complianceSvc.GetComplianceRepo().GetCaseSubmission(ctx, &domain.GetCasesParams{CddID: &req.TaskID})

	// buy crypto v1 flow
	if kgErr != nil {
		if kgErr.Code == code.RecordNotFound {
			if kgErr := cddCallbackV1(ctx, oauth.ClientID(c), &req); kgErr == nil {
				response.OK(c, nil)
				return
			} else {
				kglog.ErrorWithDataCtx(ctx, "cdd callback v1 failed", map[string]interface{}{
					"err": kgErr.String(),
					"req": req,
				})
				response.OK(c, nil)
				return
			}
		}
		kglog.ErrorWithDataCtx(ctx, "cdd, get cases failed", map[string]interface{}{
			"err":    kgErr.String(),
			"cdd_id": req.TaskID,
		})
		response.OK(c, nil)
		return
	}

	paramUpdateCdd := complianceSvc.UpdateCddByCallbackParams{
		SanctionMatched: req.Report.SanctionMatched,
		RiskScore:       uint8(req.Report.PotentialRisk),
	}

	if kgErr := complianceSvc.UpdateCddByCallback(ctx, caseSubmission.OrganizationID,
		caseSubmission.UID, &paramUpdateCdd); kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "update cdd failed", map[string]interface{}{
			"err":             kgErr.String(),
			"organization_id": caseSubmission.OrganizationID,
			"uid":             caseSubmission.UID,
			"cdd_id":          req.TaskID,
		})
		response.OK(c, nil)
		return
	}

	response.OK(c, nil)
}

func acceptIdvTask(ctx context.Context, apiKey string, taskID int) error {
	client := compliance.NewAPIClient(apiKey)
	_, resp, err := client.UpdateIDVTaskAcceptedStatus(ctx, taskID, &compliance.UpdateIDVTaskAcceptedStatusRequest{
		Comment: "auto accepted by admin",
		Result:  "Accepted",
	})
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "update idv task accepted status", map[string]interface{}{"resp": resp.String(), "err": err.Error()})
		return err
	}
	kglog.DebugWithDataCtx(ctx, "update idv task accepted status", map[string]interface{}{"resp": resp.String()})

	return nil
}

func acceptSearchTask(ctx context.Context, apiKey string, sanctionMatched bool, potentialRisk compliance.PotentialRisk, taskID int) error {
	if sanctionMatched || potentialRisk >= compliance.PotentialRiskMedium {
		return nil
	}

	// directly accept the idv task and search task
	// FIXME: using DI to inject client
	client := compliance.NewAPIClient(apiKey)
	_, resp, err := client.UpdateSearchTaskAcceptedStatus(ctx, taskID, &compliance.UpdateSearchTaskAcceptedStatusRequest{
		Comment:  fmt.Sprintf("sanction list not matched and potential risk is low (%d)", potentialRisk),
		Accepted: true,
	})
	if err != nil {
		kglog.DebugWithDataCtx(ctx, "update search task accepted status", map[string]interface{}{
			"resp": resp.String(),
			"err":  err.Error(),
		})
		return err
	}
	kglog.DebugWithDataCtx(ctx, "update search task accepted status", map[string]interface{}{"resp": resp.String()})

	return nil
}

func notifyUser(ctx context.Context, user *domain.UserData, kycState string) {
	if kycState == domain.KycStatusPending.String() ||
		kycState == domain.KycStatusUnverified.String() ||
		user == nil {
		return
	}

	clientID := application.GetDefaultClientID(ctx)
	noti := domain.Notification{
		Receiver:        user.UID,
		ContentType:     domain.NotificationContentTypeText,
		MessageType:     domain.NotificationMessageTypeSystem,
		PrimaryLink:     domain.SingleLocaleText("/main/AccountTab/kyc-verification-screen"),
		PrimaryText:     l10n.MultiLocaleText("notification-kyc-primary_link_text"),
		PrimaryOpenWith: domain.NotificationLinkTypeDeepLink,
		CreatedAt:       time.Now(),
		ClientID:        util.Ptr(clientID),
	}
	var title, summary, message domain.MultiLocaleText
	if kycState == domain.KycStatusVerified.String() {
		// send success message
		title = l10n.MultiLocaleText("kyc-title-passed")
		summary = l10n.MultiLocaleText("kyc-summary-passed")
		message = l10n.MultiLocaleText("kyc-message-passed")
	} else if kycState == domain.KycStatusRejected.String() {
		// send reject message
		title = l10n.MultiLocaleText("kyc-title-failed")
		summary = l10n.MultiLocaleText("kyc-summary-failed")
		message = l10n.MultiLocaleText("kyc-message-failed")
	}
	noti.Title = title
	noti.Summary = summary
	noti.Message = message
	_, kgErr := notification.Get().Send(ctx, &noti)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "send notification failed", map[string]interface{}{
			"err": kgErr.String(),
		})
	}
}

type getKycResultResp struct {
	Code int          `json:"code"`
	Data getKycResult `json:"data"`
}

type getKycResult struct {
	IdvTask struct {
		Passed bool             `json:"passed"`
		State  compliance.State `json:"state"`
	} `json:"idv_task"`
	SearchTask struct {
		Passed bool `json:"passed"`
	} `json:"search_task"`
	Reason string `json:"reason"`
}

// GetKycResult get kyc result
func GetKycResult(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	if len(uid) == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "uid is empty")
		return
	}

	clientID := oauth.ClientID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx, uid, clientID, false, &domain.UserPreloads{
		WithLocale: true,
	})
	if user == nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "user not found")
		return
	}
	locale := user.GetLocale(clientID)

	result := getKycResult{}
	result.IdvTask.State = compliance.StateInitial

	// get user kyc info from rdb
	kycResult, err := rdb.GetKycResult(ctx, uid)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			ctx.JSON(http.StatusOK, getKycResultResp{
				Code: 0,
				Data: result,
			})
			return
		} else {
			response.ErrorWithMsg(ctx, http.StatusInternalServerError, code.DBError, err.Error(), nil)
			return
		}
	}

	result.IdvTask.State = compliance.State(kycResult.State)
	if compliance.State(kycResult.State) == compliance.StateAccept ||
		(kycResult.AuditStatus != nil && compliance.IdvTaskAuditStatus(*kycResult.AuditStatus) == compliance.IdvTaskAuditStatusAccepted) {
		result.IdvTask.Passed = true
	} else {
		if kycResult.State == int32(compliance.StateReview) {
			if kycResult.ReviewReasons != nil {
				reviewReasons := make([]compliance.ReviewReason, 0)
				err := json.Unmarshal([]byte(*kycResult.ReviewReasons), &reviewReasons)
				if err == nil {
					if len(reviewReasons) > 0 {
						reasonTemp := compliance.ReviewReasonTemp(reviewReasons[0].Code)
						result.Reason = l10n.String(reasonTemp, locale)
					}
				}
			}
		} else if kycResult.State == int32(compliance.StateReject) {
			if kycResult.RejectReasons != nil {
				rejectReasons := make([]compliance.RejectReason, 0)
				err := json.Unmarshal([]byte(*kycResult.RejectReasons), &rejectReasons)
				if err == nil {
					if len(rejectReasons) > 0 {
						reasonTemp := compliance.RejectReasonTemp(rejectReasons[0].Code)
						result.Reason = l10n.String(reasonTemp, locale)
					}
				}
			}
		}
	}

	if kycResult.Accepted != nil && *kycResult.Accepted {
		result.SearchTask.Passed = true
	} else {
		// comments were edited by compliance team
		// directly return them
		if kycResult.Comment != nil {
			result.Reason = *kycResult.Comment
		}
	}

	ctx.JSON(http.StatusOK, getKycResultResp{
		Code: 0,
		Data: result,
	})
}

type getAcceptedIDTypesResp struct {
	Code int                      `json:"code"`
	Data getAcceptedIDTypesResult `json:"data"`
}

type getAcceptedIDTypesResult struct {
	Timestamp       time.Time        `json:"timestamp"`
	AcceptedIDTypes []acceptedIDType `json:"accepted_id_types"`
}

type acceptedIDType struct {
	CountryCode string         `json:"country_code"`
	CountryName string         `json:"country_name"`
	IDTypes     []idTypeConfig `json:"id_types"`
}

type idTypeConfig struct {
	AcquisitionConfig acquisitionConfig `json:"acquisition_config"`
	IDType            compliance.IDType `json:"id_type"`
}

type acquisitionConfig struct {
	BackSide bool `json:"back_side"` // if true, backSide is required for idv
}

// GetAcceptedIDTypes get accepted id types
//
// Deprecated: Since we've migrated from Jumio to Sumsub, and Sumsub does not provide accepted id types API,
// it's now not possible to get the accepted id types set on Sumsub.
func GetAcceptedIDTypes(c *gin.Context) {
	ctx := c.Request.Context()
	// FIXME: using DI to inject client
	client := compliance.NewAdminAPIClient()
	data, _, err := client.GetAcceptedIDTypes(ctx)
	if err != nil {
		response.ErrorWithMsg(c, http.StatusInternalServerError, code.FailedToSendToCompliance, err.Error(), nil)
		return
	}
	uid := auth.GetUID(c)
	clientID := oauth.ClientID(c)
	user, _ := rdb.GormRepo().GetUser(ctx, uid, clientID, false, &domain.UserPreloads{
		WithLocale: true,
	})
	if user == nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "user does not exist")
		return
	}
	resp := getAcceptedIDTypesResult{
		Timestamp:       data.Timestamp,
		AcceptedIDTypes: make([]acceptedIDType, 0, len(data.AcceptedIDTypes)),
	}
	locale := user.GetLocale(clientID)
	countryNames := restcountriesapi.GetSingleCountryNameMap(locale)
	for _, v := range data.AcceptedIDTypes {
		code := v.CountryCode
		if _, ok := blackListedCountriesForBuyCrypto[code]; ok {
			continue
		}
		if code == "XKX" {
			// Kosovo's cca3 is different in restcountriesapi and compliance
			code = "UNK"
		}
		countryName, ok := countryNames[code]
		if !ok {
			countryName = v.CountryName
		}
		resp.AcceptedIDTypes = append(resp.AcceptedIDTypes, acceptedIDType{
			CountryCode: v.CountryCode,
			CountryName: countryName,
			IDTypes:     make([]idTypeConfig, 0, len(v.IDTypes)),
		})
		for _, idType := range v.IDTypes {
			resp.AcceptedIDTypes[len(resp.AcceptedIDTypes)-1].IDTypes = append(resp.AcceptedIDTypes[len(resp.AcceptedIDTypes)-1].IDTypes, idTypeConfig{
				AcquisitionConfig: acquisitionConfig{
					BackSide: idType.AcquisitionConfig.BackSide,
				},
				IDType: idType.IDType,
			})
		}
	}
	c.JSON(http.StatusOK, getAcceptedIDTypesResp{
		Code: 0,
		Data: resp,
	})
}

func idvCallbackV1(ctx context.Context, clientID string, req *compliance.IdvTaskDetailResp) *code.KGError {
	if len(req.CustomerReference) == 0 {
		kglog.DebugCtx(ctx, "customer reference is empty")
		return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("customer reference is empty"), nil)
	}

	// lock user
	kycCallbackKey := cache.ComposeKycCallbackCacheKey(req.CustomerReference)
	var retryCount int
	for retryCount < MaxRetryCount {
		acquired, err := cache.AcquireLock(ctx, kycCallbackKey, cache.KycCallbackCacheTTL)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "idvCallbackV1, acquire lock failed", map[string]interface{}{
				"err":                err.Error(),
				"customer_reference": req.CustomerReference,
			})
			return code.NewKGError(code.InternalError, http.StatusInternalServerError, fmt.Errorf("acquire lock failed"), nil)
		}
		if acquired {
			break
		}
		time.Sleep(500 * time.Millisecond)
		retryCount++
	}
	if retryCount >= MaxRetryCount {
		kglog.DebugfCtx(ctx, "acquire lock failed: %s", req.CustomerReference)
		return code.NewKGError(code.InternalError, http.StatusInternalServerError, fmt.Errorf("acquire lock failed"), nil)
	}
	kglog.DebugfCtx(ctx, "acquire lock success: %s, retry count: %d", req.CustomerReference, retryCount)
	defer cache.ReleaseLock(ctx, kycCallbackKey)

	// if the user is already verified/rejected, return
	user, _ := rdb.GormRepo().GetUser(ctx, req.CustomerReference, clientID, true, &domain.UserPreloads{
		WithFcm:    true,
		WithLocale: true,
	})
	if user == nil {
		kglog.DebugWithDataCtx(ctx, "get user failed", map[string]interface{}{
			"uid": req.CustomerReference,
		})
		return code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("get user failed"), nil)
	}

	var kycStatsAlreadyReviewed bool

	if user.KycState != nil &&
		(*user.KycState == domain.KycStatusVerified.String() ||
			*user.KycState == domain.KycStatusRejected.String()) {
		kycStatsAlreadyReviewed = true
	}

	kycResult, err := rdb.GetKycResult(ctx, req.CustomerReference)
	if err != nil && err != gorm.ErrRecordNotFound {
		kglog.DebugWithDataCtx(ctx, "get kyc result failed", map[string]interface{}{
			"err": err.Error(),
		})
		return code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	kglog.DebugWithDataCtx(ctx, "kyc result", kycResult)

	org, kgErr := organization.GetStudioOrgRepo().GetOrganizationByOAuthClientID(ctx, clientID)
	if kgErr != nil {
		kglog.DebugWithDataCtx(ctx, "get org by oauth client id failed", map[string]interface{}{
			"client_id": clientID,
		})
		return code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("get org failed"), nil)
	}

	if org.ComplianceAPIKey == nil {
		kglog.DebugWithDataCtx(ctx, "compliance api key not set yet", map[string]interface{}{
			"org_id":    org.ID,
			"client_id": clientID,
		})
		return code.NewKGError(code.StudioOrganizationComplianceAPIKeyNotSet, http.StatusForbidden, fmt.Errorf("compliance api key not set yet"), nil)
	}

	var needToAccept bool

	kycState := domain.KycStatusPending
	switch req.State {
	case compliance.StateReject:
		kycState = domain.KycStatusRejected
	case compliance.StateInitial:
		kycState = domain.KycStatusUnverified
	case compliance.StateAccept:
		needToAccept = req.AuditStatus == compliance.IdvTaskAuditStatusPending
	default:
		if req.AuditStatus == compliance.IdvTaskAuditStatusRejected {
			kycState = domain.KycStatusRejected
		}
	}

	kglog.DebugfCtx(ctx, "needToAccept: %v, state:%d, auditStatus: %s", needToAccept, req.State, req.AuditStatus)

	if needToAccept {
		if err := acceptIdvTask(ctx, *org.ComplianceAPIKey, req.IDVTaskID); err != nil {
			kglog.DebugWithDataCtx(ctx, "accept user's idv task failed", map[string]interface{}{
				"idv_task_id": req.IDVTaskID,
				"err":         err.Error(),
			})

			if err.Error() != "user already verified/rejected" {
				return code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
			}
		}
	}

	if kycStatsAlreadyReviewed {
		kglog.DebugWithDataCtx(ctx, "user already verified/rejected", map[string]interface{}{
			"uid": req.CustomerReference,
		})
		return code.NewKGError(code.KycStateError, http.StatusBadRequest, fmt.Errorf("user already verified/rejected"), nil)
	}

	kglog.DebugfCtx(ctx, "update to kyc state: %s", kycState)
	err = rdb.GormRepo().SetUser(ctx, &domain.UserData{
		UserInfo: domain.UserInfo{
			UID: req.CustomerReference,
		},
		KycState: util.Ptr(kycState.String()),
	})
	if err != nil {
		kglog.DebugWithDataCtx(ctx, "set user failed", map[string]interface{}{
			"err": err.Error(),
		})
		return code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	// store kyc info to rdb
	kycResult = &dbmodel.KycResult{
		UID:         req.CustomerReference,
		State:       int32(req.State),
		AuditStatus: util.Ptr(string(req.AuditStatus)),
	}
	if len(req.ReviewReasons) > 0 {
		kycResult.ReviewReasons = util.Ptr(util.ToJSONString(req.ReviewReasons))
	}
	if len(req.RejectReasons) > 0 {
		kycResult.RejectReasons = util.Ptr(util.ToJSONString(req.RejectReasons))
	}
	if len(req.DDTasks) > 0 {
		kycResult.LatestDdTask = util.Ptr(util.ToJSONString(req.DDTasks[len(req.DDTasks)-1]))
	}
	if err := rdb.SaveIdvKycResult(ctx, kycResult); err != nil {
		return code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	notifyUser(ctx, user, kycState.String())
	return nil
}

func cddCallbackV1(ctx context.Context, clientID string, req *compliance.SearchTaskCallbackRequest) *code.KGError {
	if len(req.CustomerReference) == 0 {
		kglog.DebugCtx(ctx, "customer reference is empty")
		return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("customer reference is empty"), nil)
	}

	// lock user
	kycCallbackKey := cache.ComposeKycCallbackCacheKey(req.CustomerReference)
	var retryCount int
	for retryCount < MaxRetryCount {
		acquired, err := cache.AcquireLock(ctx, kycCallbackKey, cache.KycCallbackCacheTTL)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "cddCallbackV1, acquire lock failed", map[string]interface{}{
				"err":                err.Error(),
				"customer_reference": req.CustomerReference,
			})
			return code.NewKGError(code.InternalError, http.StatusInternalServerError, fmt.Errorf("acquire lock failed"), nil)
		}
		if acquired {
			break
		}
		time.Sleep(500 * time.Millisecond)
		retryCount++
	}
	if retryCount >= MaxRetryCount {
		kglog.DebugfCtx(ctx, "acquire lock failed: %s", req.CustomerReference)
		return code.NewKGError(code.InternalError, http.StatusInternalServerError, fmt.Errorf("acquire lock failed"), nil)
	}
	kglog.DebugfCtx(ctx, "acquire lock success: %s, retry count: %d", req.CustomerReference, retryCount)
	defer cache.ReleaseLock(ctx, kycCallbackKey)

	// if the user is already verified/rejected, return
	user, _ := rdb.GormRepo().GetUser(ctx, req.CustomerReference, clientID, true, &domain.UserPreloads{
		WithFcm:    true,
		WithLocale: true,
	})
	if user == nil {
		kglog.DebugWithDataCtx(ctx, "get user failed", map[string]interface{}{
			"uid": req.CustomerReference,
		})
		return code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("get user failed"), nil)
	}

	org, kgErr := organization.GetStudioOrgRepo().GetOrganizationByOAuthClientID(ctx, clientID)
	if kgErr != nil {
		kglog.DebugWithDataCtx(ctx, "get org by oauth client id failed", map[string]interface{}{
			"client_id": clientID,
		})
		return code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("get org failed"), nil)
	}

	if org.ComplianceAPIKey == nil {
		kglog.DebugWithDataCtx(ctx, "compliance api key not set yet", map[string]interface{}{
			"org_id":    org.ID,
			"client_id": clientID,
		})
		return code.NewKGError(code.StudioOrganizationComplianceAPIKeyNotSet, http.StatusForbidden, fmt.Errorf("compliance api key not set yet"), nil)
	}

	if user.KycState != nil &&
		(*user.KycState == domain.KycStatusVerified.String() ||
			*user.KycState == domain.KycStatusRejected.String()) {
		kglog.DebugWithDataCtx(ctx, "user already verified/rejected", map[string]interface{}{
			"uid": req.CustomerReference,
		})
		return code.NewKGError(code.KycStateError, http.StatusBadRequest, fmt.Errorf("user already verified/rejected"), nil)
	}

	kycResult, err := rdb.GetKycResult(ctx, req.CustomerReference)
	if err != nil && err != gorm.ErrRecordNotFound {
		kglog.DebugWithDataCtx(ctx, "get kyc result failed", map[string]interface{}{
			"err": err.Error(),
		})
		return code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	kglog.DebugWithDataCtx(ctx, "kyc result", kycResult)

	kycState := domain.KycStatusPending
	// assume idv task already accepted when search task callback
	if req.Report.Accepted != nil {
		kycState = domain.KycStatusRejected
		if *req.Report.Accepted {
			kycState = domain.KycStatusVerified
		}
	} else {
		// check if user can auto verify
		if err := acceptSearchTask(ctx, *org.ComplianceAPIKey, req.Report.SanctionMatched,
			req.Report.PotentialRisk, req.TaskID); err != nil {
			kglog.DebugWithDataCtx(ctx, "accept user's search task failed", map[string]interface{}{
				"search_task_id": req.TaskID,
				"err":            err.Error(),
			})
			return code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
		}
	}

	kglog.DebugfCtx(ctx, "update to kyc state: %s", kycState)

	if err := rdb.GormRepo().SetUser(ctx, &domain.UserData{
		UserInfo: domain.UserInfo{UID: req.CustomerReference},
		KycState: util.Ptr(kycState.String()),
	}); err != nil {
		kglog.DebugWithDataCtx(ctx, "set user failed", map[string]interface{}{
			"err": err.Error(),
		})
		return code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	// save kyc info to rdb
	kycResult = &dbmodel.KycResult{
		UID:             req.CustomerReference,
		SanctionMatched: &req.Report.SanctionMatched,
		PotentialRisk:   util.Ptr(int32(req.Report.PotentialRisk)),
		Accepted:        req.Report.Accepted,
		Comment:         &req.Report.Comment,
	}
	if err := rdb.SaveTaskKycResult(ctx, kycResult); err != nil {
		kglog.DebugWithDataCtx(ctx, "save task kyc result failed", map[string]interface{}{
			"err": err.Error(),
		})
		return code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
	}

	notifyUser(ctx, user, kycState.String())
	return nil
}
