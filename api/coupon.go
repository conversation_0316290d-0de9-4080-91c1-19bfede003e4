package api

import (
	"encoding/json"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/eth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/nft"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type couponsByNftRequest struct {
	ChainID         string `json:"chain_id" binding:"required"`
	ContractAddress string `json:"contract_address" binding:"required"`
	TokenID         string `json:"token_id" binding:"required"`
}

// nftTraits struct
type nftTraits struct {
	TraitType   string       `json:"trait_type"`
	Value       string       `json:"value"`
	DisplayType *interface{} `json:"display_type"`
}

type couponsByNftResp struct {
	Code int `json:"code"`
	Data struct {
		Coupons    []db.Coupon `json:"coupons"`
		ItemDetail struct {
			ImageURL   string `json:"image_url"`
			ItemName   string `json:"item_name"`
			SchemaName string `json:"schema_name"`
		} `json:"item_detail"`
	} `json:"data"`
}

// CouponsByNft returns coupon list
func CouponsByNft(ctx *gin.Context) {
	req := couponsByNftRequest{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if req.ChainID != "ethereum" && req.ChainID != "polygon" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid chain_id")
		return
	}

	asset, err := nft.FetchAsset(ctx.Request.Context(), req.ChainID, req.ContractAddress, req.TokenID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	coupons, code, err := rdb.CouponsByNft(ctx.Request.Context(), req.ChainID, req.ContractAddress, req.TokenID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	resp := new(couponsByNftResp)
	resp.Code = code
	resp.Data.Coupons = *coupons
	resp.Data.ItemDetail.ImageURL = asset.ImageURL
	resp.Data.ItemDetail.ItemName = asset.Name
	resp.Data.ItemDetail.SchemaName = *asset.ContractSchemaName
	ctx.JSON(http.StatusOK, resp)
}

func unmarshalTraits(traitStr *string) *[]nftTraits {
	if traitStr == nil {
		return nil
	}
	traits := make([]nftTraits, 0)
	err := json.Unmarshal([]byte(*traitStr), &traits)
	if err != nil {
		kglog.WarningWithData("unmarshalTraits error", map[string]interface{}{
			"err": err.Error(),
		})
		return nil
	}
	return &traits
}

func containsTrait(matchedTraits db.CouponMatchedTrait, traits *[]nftTraits) bool {
	if matchedTraits.TraitType == nil && matchedTraits.Value == nil {
		return true
	}
	if (matchedTraits.TraitType != nil || matchedTraits.Value != nil) && traits == nil {
		return false
	}
	for _, trait := range *traits {
		if (matchedTraits.TraitType == nil || (*matchedTraits.TraitType == trait.TraitType)) &&
			(matchedTraits.Value == nil || (*matchedTraits.Value == trait.Value)) {
			return true
		}
	}
	return false
}

type couponByIDRequest struct {
	ChainID         string `json:"chain_id" binding:"required"`
	ContractAddress string `json:"contract_address" binding:"required"`
	TokenID         string `json:"token_id" binding:"required"`
	CouponID        string `json:"coupon_id" binding:"required"`
}

type couponByIDResp struct {
	Code int       `json:"code"`
	Data db.Coupon `json:"data"`
}

// CouponByID returns single coupon
func CouponByID(ctx *gin.Context) {
	req := couponByIDRequest{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if req.ChainID != "ethereum" && req.ChainID != "polygon" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid chain_id")
		return
	}

	coupon, errCode, err := rdb.CouponByIDWithRedeemStatus(ctx.Request.Context(), req.ChainID, req.ContractAddress, req.CouponID, req.TokenID)
	if err != nil {
		if errCode == code.DashboardPrizeNotFound {
			response.NotFoundWithMsg(ctx, code.DashboardPrizeNotFound, "Prize Not Found")
			return
		}
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	resp := new(couponByIDResp)
	resp.Data = *coupon
	ctx.JSON(http.StatusOK, resp)
}

type redeemCouponByNftRequest struct {
	ChainID         string `json:"chain_id" binding:"required"`
	ContractAddress string `json:"contract_address" binding:"required"`
	TokenID         string `json:"token_id" binding:"required"`
	From            string `json:"from" binding:"required"`
	Timestamp       string `json:"timestamp" binding:"required"`
	Signature       string `json:"signature" binding:"required"`
	CouponID        string `json:"coupon_id" binding:"required"`
	Quantity        int    `json:"quantity" binding:"required"`
}

type redeemCouponByNftResp struct {
	Code       int    `json:"code"`
	Data       string `json:"data"`
	Error      string `json:"error"`
	CouponCode string `json:"coupon_code"`
	URL        string `json:"url"`
}

type redeemSignatureMsg struct {
	Chain           string `json:"chain"`
	ContractAddress string `json:"contract_address"`
	TokenID         string `json:"token_id"`
	From            string `json:"from"`
	Timestamp       string `json:"timestamp"`
}

// RedeemCouponByNft redeem a coupon
func RedeemCouponByNft(ctx *gin.Context) {
	ctxSpan, span := tracing.Start(ctx.Request.Context(), "api.RedeemCouponByNft")
	defer span.End()

	req := redeemCouponByNftRequest{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if req.ChainID != "" && req.ChainID != "ethereum" && req.ChainID != "polygon" {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.ParamIncorrect,
			Error: "invalid chain_id",
		})
		return
	}
	timestamp, _ := strconv.Atoi(req.Timestamp)
	timeDiff := time.Now().Unix() - int64(timestamp)
	if timeDiff > 300 || timeDiff < -300 {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.DashboardSignatureExpired,
			Error: "signature expired",
		})
		return
	}

	msg, _ := json.Marshal(&redeemSignatureMsg{
		Chain:           req.ChainID,
		ContractAddress: req.ContractAddress,
		TokenID:         req.TokenID,
		From:            req.From,
		Timestamp:       req.Timestamp,
	})
	valid := eth.VerifySignature(req.From, req.Signature, string(msg))
	if !valid {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.DashboardSignatureVerifyFailed,
			Error: "signature verify failed",
		})
		return
	}

	valid, err := nft.VerifyOwnerOnChain(ctxSpan, req.ChainID, req.TokenID, &alchemyapi.GetNFTsParams{
		Owner:             req.From,
		ContractAddresses: []string{req.ContractAddress},
		WithMetadata:      false,
	})
	if err != nil {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.EtherscanFailed,
			Error: "etherscan api failed",
		})
		return
	}
	if !valid {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.NotNFTOwner,
			Error: "Not NFT owner",
		})
		return
	}

	dbCoupon, errCode, err := rdb.CouponByIDWithRedeemStatus(ctxSpan, req.ChainID, req.ContractAddress, req.CouponID, req.TokenID)
	if err != nil {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  errCode,
			Error: err.Error(),
		})
		return
	}
	if dbCoupon.EndTime.Before(time.Now()) {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.PrizeExpired,
			Error: "Coupon expired",
		})
		return
	}
	if dbCoupon.TotalAmount <= dbCoupon.Amount {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.CouponNotEnough,
			Error: "Coupon not enough",
		})
		return
	}

	// check match traits
	asset, err := nft.FetchAsset(ctxSpan, req.ChainID, req.ContractAddress, req.TokenID)
	if err != nil {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.DBError,
			Error: err.Error(),
		})
	}
	traits := unmarshalTraits(asset.Traits)
	if !containsTrait(dbCoupon.MatchedTrait, traits) {
		response.BadRequestWithMsg(ctx, code.CouponTraitNotMatched, "Traits not matched")
		return
	}
	project, errCode, err := rdb.ProjectByAddress(ctxSpan, req.ChainID, req.ContractAddress)
	if err != nil {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.DBError,
			Error: err.Error(),
		})
	}

	var couponID int
	// Skip it for luckybag version
	if strings.HasPrefix(req.CouponID, "pid-") {
		couponID, err = strconv.Atoi(req.CouponID[len("pid-"):])
		if err != nil {
			response.InternalServerErrorWithMsg(ctx, code.ParamIncorrect, err.Error())
			return
		}
	} else {
		prize, errCode, err := rdb.PrizeByCustomID(ctxSpan, project.ID, req.CouponID)
		if err != nil {
			response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
			return
		}
		couponID = int(prize.ID)
	}
	err = rdb.SaveRedeemLog(ctxSpan, project.ID, int32(couponID), int32(req.Quantity), req.TokenID, time.Unix(int64(timestamp), 0))
	if err != nil {
		kglog.WarningWithData("save redeem log failed", err.Error()+",project: "+strconv.Itoa(int(project.ID))+",couponID: "+strconv.Itoa(couponID)+",TokenID: "+req.TokenID)
	}

	resp := &redeemCouponByNftResp{}
	if dbCoupon.URL != "" {
		resp.URL = dbCoupon.URL
	}
	if dbCoupon.SameCode != "" {
		resp.CouponCode = dbCoupon.SameCode
	}
	resp.Data = "success"
	resp.Code = errCode
	ctx.JSON(http.StatusOK, resp)
}
