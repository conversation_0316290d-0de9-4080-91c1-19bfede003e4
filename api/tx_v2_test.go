package api_test

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/router"
	chainsync "github.com/kryptogo/kg-wallet-backend/service/chain-sync"
	"go.uber.org/mock/gomock"
)

func TestHandleNewAlchemyTx(t *testing.T) {
	// Initialize Gin router in test mode
	gin.SetMode(gin.TestMode)
	router.InitDependencies()
	r := router.SetupRouter()

	// Initialize gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a mock AsyncTaskExecutor
	mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)

	// Setup mock expectations
	// Expect Execute to be called twice with appropriate parameters
	mockExecutor.EXPECT().
		Execute(
			gomock.Any(), // context.Context
			gomock.Eq(config.GetString("CLOUD_TASK_CHAIN_SYNC_QUEUE")), // queueID from config
			gomock.Any(), // *domain.HttpTask
			gomock.Any(), // taskName
		).
		Times(2).
		DoAndReturn(func(ctx context.Context, queueID string, task *domain.HttpTask, taskName string) error {
			if !strings.Contains(string(task.Body), "0x7a4a39da2a3fa1fc2ef88fd1eaea070286ed2aba21e0419dcfb6d5c5d9f02a72") ||
				!strings.Contains(string(task.Body), "0xc84eeeb72d2b23161fd93b088f304902cbd8b4510f1455a65fdac160e37b3173") {
				return errors.New("task body does not contain expected tx hash")
			}
			return nil
		})

	// Initialize chain-sync with mocks
	// Assuming other dependencies are not used in HandleNewAlchemyTx, we can pass nil or mock them as needed
	chainClients := make(map[domain.Chain]domain.ChainClient)
	syncFetchers := make(map[domain.Chain]domain.ChainSyncFetcher)
	handlerURL := "http://handler.url" // Example handler URL

	chainsync.Init(
		nil,
		chainClients,
		syncFetchers,
		mockExecutor,
		handlerURL,
	)

	// Sample webhook payload
	samplePayload := `{
		"webhookId": "wh_k63lg72rxda78gce",
		"id": "whevt_vq499kv7elmlbp2v",
		"createdAt": "2024-01-23T07:42:26.411977228Z",
		"type": "ADDRESS_ACTIVITY",
		"event": {
			"network": "ETH_MAINNET",
			"activity": [
				{
					"blockNum": "0xdf34a3",
					"hash": "0x7a4a39da2a3fa1fc2ef88fd1eaea070286ed2aba21e0419dcfb6d5c5d9f02a72",
					"fromAddress": "******************************************",
					"toAddress": "******************************************",
					"value": 293.092129,
					"asset": "USDC",
					"category": "token",
					"rawContract": {
						"rawValue": "0x0000000000000000000000000000000000000000000000000000000011783b21",
						"address": "******************************************",
						"decimals": 6
					},
					"log": {
						"transactionHash": "0x7a4a39da2a3fa1fc2ef88fd1eaea070286ed2aba21e0419dcfb6d5c5d9f02a72"
					}
				},
				{
					"blockNum": "0xdf34a3",
					"hash": "0xc84eeeb72d2b23161fd93b088f304902cbd8b4510f1455a65fdac160e37b3173",
					"fromAddress": "******************************************",
					"toAddress": "******************************************",
					"value": 2400,
					"asset": "USDC",
					"category": "token",
					"rawContract": {
						"rawValue": "0x000000000000000000000000000000000000000000000000000000008f0d1800",
						"address": "******************************************",
						"decimals": 6
					},
					"log": {
						"transactionHash": "0xc84eeeb72d2b23161fd93b088f304902cbd8b4510f1455a65fdac160e37b3173"
					}
				}
			]
		}
	}`

	// Create a new HTTP request
	req, err := http.NewRequest(http.MethodPost, "/_v/new_alchemy_tx", bytes.NewBufferString(samplePayload))
	if err != nil {
		t.Fatalf("Failed to create request: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// Perform the request
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	// Assert the response status code
	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d but got %d", http.StatusOK, w.Code)
	}

	// Optionally, assert the response body if needed
	var resp struct {
		Code int `json:"code"`
	}
	if err := json.Unmarshal(w.Body.Bytes(), &resp); err != nil {
		t.Errorf("Failed to unmarshal response: %v", err)
	}
	if resp.Code != 0 {
		t.Errorf("Expected response code 200 but got %d", resp.Code)
	}
}
