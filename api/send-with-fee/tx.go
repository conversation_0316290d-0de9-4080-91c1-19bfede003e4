package sendwithfee

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	sendwithfee "github.com/kryptogo/kg-wallet-backend/pkg/service/send-with-fee"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
)

type notifyTransactionReq struct {
	ChainID          string  `json:"chain_id" binding:"required"`
	TokenAddress     string  `json:"token_address"`
	ProfitMarginRate float64 `json:"profit_margin_rate" binding:"required"`
	TxHash           string  `json:"tx_hash" binding:"required"`
}

// NotifyTransaction records the transaction information for sending with fee
func NotifyTransaction(c *gin.Context) {
	ctx := c.Request.Context()

	req := &notifyTransactionReq{}
	if kgErr := util.ToGinContextExt(c).BindJson(req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	uid := auth.GetUID(c)

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if kgErr := sendwithfee.NotifySendWithFeeTx(ctx, sendwithfee.NotifySendWithFeeTxRequest{
		OrganizationID:   orgID,
		UID:              uid,
		ChainID:          req.ChainID,
		TokenAddress:     req.TokenAddress,
		ProfitMarginRate: decimal.NewFromFloat(req.ProfitMarginRate),
		TxHash:           req.TxHash,
	}); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

// ReFillSendWithFeeTx re-fill send with fee tx
func ReFillSendWithFeeTx(c *gin.Context) {
	ctx := c.Request.Context()
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}

	if kgErr := sendwithfee.ReFillSendWithFeeTx(ctx, id); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}
