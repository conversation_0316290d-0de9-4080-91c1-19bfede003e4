package sendwithfee

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	assetpro "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type profitMarginRateReq struct {
	ChainID string `form:"chain_id" binding:"required"`
}

type profitMarginRateResp struct {
	ProfitMarginRate float64 `json:"profit_margin_rate"`
	ContractAddress  string  `json:"contract_address"`
	FeeAddress       string  `json:"fee_address"`
}

// GetProfitMarginRate returns profit margin rate for sending with fee
func GetProfitMarginRate(c *gin.Context) {
	ctx := c.Request.Context()

	req := profitMarginRateReq{}

	if kgErr := util.ToGinContextExt(c).BindQuery(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	walletType := getWalletType(req.ChainID)
	if len(walletType) == 0 {
		response.BadRequest(c, code.ChainIDNotSupported)
		return
	}

	// fee address is always set to KryptoGO wallet address
	kgWallet, err := organization.GetOrgWallet(c.Request.Context(), 1, walletType)
	if err != nil {
		response.KGError(c, code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil))
		return
	}

	mSendContract := config.GetStringMap("SEND_WITH_FEE_CONTRACTS")
	contractAddress, ok := mSendContract[req.ChainID]
	if !ok {
		response.ForbiddenErrorWithMsg(c, code.ClientForbidden, "chain_id is not supported")
		return
	}

	profitRate, kgErr := assetpro.GetProfitRate(ctx, orgID, domain.ProfitRateServiceTypeSendWithFee)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	kgMinimumRevenueRate := profitRate.GetKgMinimumRevenueRate()
	profitMarginRate := profitRate.ProfitRate.Add(kgMinimumRevenueRate)

	response.OK(c, profitMarginRateResp{
		ProfitMarginRate: profitMarginRate.InexactFloat64(),
		ContractAddress:  contractAddress,
		FeeAddress:       kgWallet.WalletAddress,
	})
}

func getWalletType(chainID string) string {
	chain := domain.IDToChain(chainID)
	if chain.IsTVM() {
		return "tron"
	} else if chain.IsEVM() {
		return "evm"
	}
	return ""
}
