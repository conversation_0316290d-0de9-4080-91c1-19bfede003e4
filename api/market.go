package api

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	defiswap "github.com/kryptogo/kg-wallet-backend/pkg/service/defi-swap"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/market"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	studioassetpro "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type getPricesParams struct {
	ChainID         string `form:"chain_id" binding:"required"`
	ContractAddress string `form:"contract_address"`
}

type tokenPrice struct {
	ChainID         string  `json:"chain_id"`
	ContractAddress string  `json:"contract_address"`
	Price           float64 `json:"price"`
}

// GetPrice returns token price
func GetPrice(c *gin.Context) {
	ctxSpan, span := tracing.Start(c.Request.Context(), "api.GetPrices")
	defer span.End()

	params := &getPricesParams{}
	kgErr := util.ToGinContextExt(c).BindQuery(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// verify params
	if valid := market.TokenIsSupported(params.ChainID, params.ContractAddress); !valid {
		response.BadRequestWithMsg(c, code.ParamIncorrect, fmt.Sprintf("invalid chain_id: %s", params.ChainID))
		return
	}

	// get the latest price from cache
	price, err := market.GetPrice(ctxSpan, params.ChainID, params.ContractAddress)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		return
	}

	response.OK(c, tokenPrice{
		ChainID:         params.ChainID,
		ContractAddress: params.ContractAddress,
		Price:           price,
	})
}

// GetTokens returns all tokens
func GetTokens(ctx *gin.Context) {
	clientID := oauth.ClientID(ctx)
	tokenInfo, err := market.GetTokenInfo(ctx.Request.Context(), clientID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.InternalError, err.Error())
		return
	}

	response.OK(ctx, tokenInfo)
}

type getOrderLimitsResp struct {
	SingleOrderLowerLimit float32 `json:"single_order_lower_limit"`
	WeekOrderUpperLimit   float32 `json:"week_order_upper_limit"`
	AmountBoughtThisWeek  float32 `json:"amount_bought_this_week"`
	OrderEnabled          bool    `json:"order_enabled"`
}

type buyCryptoConfigResp struct {
	WideIconURL      string             `json:"wide_icon_url"`
	AppStoreURL      string             `json:"app_store_url"`
	GooglePlayURL    string             `json:"google_play_url"`
	AppName          string             `json:"app_name"`
	AppIconURL       string             `json:"app_icon_url"`
	PrivacyPolicyURL string             `json:"privacy_policy_url"`
	Tokens           []market.TokenInfo `json:"tokens"`
	SupportedLocales []string           `json:"supported_locales"`
	PrimaryColor     struct {
		Primary           string `json:"primary"`
		PrimaryDT         string `json:"primary_dt"`
		Secondary         string `json:"secondary"`
		SecondaryDT       string `json:"secondary_dt"`
		BackgroundPrimary string `json:"background_primary"`
		BackgroundDT      string `json:"background_dt"`
	} `json:"primary_color"`
}

// GetOrderLimits returns order limits
func GetOrderLimits(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	now := time.Now()
	prevSunday := util.PrevSunday(now)
	amountBought, err := rdb.TotalValueBoughtByUser(ctx.Request.Context(), uid, prevSunday, now)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.InternalError, err.Error())
		return
	}

	resp := &getOrderLimitsResp{
		SingleOrderLowerLimit: float32(config.GetFloat64("SINGLE_ORDER_LOWER_LIMIT")),
		WeekOrderUpperLimit:   float32(config.GetFloat64("WEEK_ORDER_UPPER_LIMIT")),
		AmountBoughtThisWeek:  float32(amountBought),
		OrderEnabled:          config.GetBool("ORDER_ENABLED"),
	}
	response.OK(ctx, resp)
}

// GetBuyCryptoConfig returns buy crypto config
func GetBuyCryptoConfig(ctx *gin.Context) {
	// uid := auth.GetUID(ctx)
	clientID := auth.GetClientID(ctx)

	var resp buyCryptoConfigResp

	org, kgErr := organization.GetStudioOrgRepo().GetOrganizationByOAuthClientID(ctx.Request.Context(), clientID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	app, kgErr := application.GetOAuthApplication(ctx.Request.Context(), clientID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	tokenInfo, err := market.GetTokenInfo(ctx.Request.Context(), clientID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.InternalError, err.Error())
		return
	}

	liquidities, kgErr := studioassetpro.GetLiquidities(ctx.Request.Context(), org.ID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	remoteConfigCondition := firebase.ConditionDefault
	if app.Application.ClientID == "alpha-gate-wallet" {
		remoteConfigCondition = firebase.ConditionIosTongbao
	}

	remoteConfig, kgErr := firebase.GetRemoteConfig(ctx.Request.Context(), remoteConfigCondition)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	type supportedBuyCryptoPair struct {
		chainID         string
		contractAddress string
	}

	mSupportedBuyCryptoPair := make(map[supportedBuyCryptoPair]struct{})
	for _, liquidity := range liquidities {
		if liquidity.LiquidityType != domain.LiquidityTypeBuyCrypto {
			continue
		}

		mSupportedBuyCryptoPair[supportedBuyCryptoPair{
			chainID:         liquidity.ChainID,
			contractAddress: util.Val(liquidity.ContractAddress),
		}] = struct{}{}
	}

	resp.WideIconURL = util.Val(app.WideLogo)
	resp.AppStoreURL = app.AppStoreLink
	resp.GooglePlayURL = app.GooglePlayLink
	resp.AppName = app.Application.Name
	resp.AppIconURL = app.MainLogo
	resp.PrivacyPolicyURL = app.PrivacyPolicyLink

	for _, token := range tokenInfo {
		if _, ok := mSupportedBuyCryptoPair[supportedBuyCryptoPair{
			chainID:         token.ChainID,
			contractAddress: token.ContractAddress,
		}]; !ok {
			continue
		}

		resp.Tokens = append(resp.Tokens, util.Val(token))
	}

	for _, supportedLocale := range remoteConfig.WalletConfig.SupportedLocales {
		var locale = supportedLocale.LanguageCode
		if supportedLocale.LanguageCode == "en" || supportedLocale.LanguageCode == "zh" {
			locale = fmt.Sprintf("%s-%s", supportedLocale.LanguageCode, supportedLocale.CountryCode)
		}
		resp.SupportedLocales = append(resp.SupportedLocales, locale)
	}

	convertHexARGBToRGB := func(hexARGB string) string {
		if len(hexARGB) != 8 {
			return hexARGB
		}

		rgb := hexARGB[2:]
		return rgb
	}

	resp.PrimaryColor.Primary = convertHexARGBToRGB(remoteConfig.ThemeData.PrimaryValue)
	resp.PrimaryColor.PrimaryDT = convertHexARGBToRGB(remoteConfig.ThemeData.PrimaryDTValue)
	resp.PrimaryColor.Secondary = convertHexARGBToRGB(remoteConfig.ThemeData.SecondaryValue)
	resp.PrimaryColor.SecondaryDT = convertHexARGBToRGB(remoteConfig.ThemeData.SecondaryDTValue)
	resp.PrimaryColor.BackgroundPrimary = convertHexARGBToRGB(remoteConfig.ThemeData.BackgroundPrimaryValue)
	resp.PrimaryColor.BackgroundDT = convertHexARGBToRGB(remoteConfig.ThemeData.BackgroundPrimaryDTValue)

	response.OK(ctx, resp)
}

type getSwapConfigResponse struct {
	OneInch struct {
		Referrer string  `json:"referrer"`
		Fee      float64 `json:"fee"`
	} `json:"1inch"`
}

// GetSwapConfig returns swap config
//
// Deprecated: Use defiswap api instead
func GetSwapConfig(c *gin.Context) {
	ctx := c.Request.Context()

	var resp getSwapConfigResponse

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// get wallets from kryptogo org
	wallets, err := organization.GetOrgWallets(ctx, 1)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		return
	}

	for _, wallet := range wallets {
		if wallet.WalletType != assetpro.WalletTypeEvm.String() {
			continue
		}
		resp.OneInch.Referrer = wallet.WalletAddress
		break
	}

	if resp.OneInch.Referrer == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "no evm wallet found")
		return
	}

	profitRate, kgErr := studioassetpro.GetProfitRate(ctx, orgID, domain.ProfitRateServiceTypeSwapDefi)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	kgMinimumRevenueRate := profitRate.GetKgMinimumRevenueRate()

	feeRatio := profitRate.ProfitRate.Add(kgMinimumRevenueRate)

	resp.OneInch.Fee = feeRatio.InexactFloat64()

	response.OK(c, resp)
}

// NotifyDefiSwap creates defi swap tx record and query the tx details asynchronously
//
// Deprecated: Use defiswap api instead
func NotifyDefiSwap(c *gin.Context) {
	ctx := c.Request.Context()
	params := struct {
		ChainID string `json:"chain_id" binding:"required"`
		TxHash  string `json:"tx_hash" binding:"required"`
	}{}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if kgErr := util.ToGinContextExt(c).BindJson(&params); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	notifyDefiSwapReq := domain.CreateDefiSwapRequest{
		UID:     auth.GetUID(c),
		OrgID:   orgID,
		ChainID: params.ChainID,
		TxHash:  params.TxHash,
	}

	if kgErr := defiswap.NotifyDefiSwap(ctx, notifyDefiSwapReq); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}
