package auth

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type customAuthLoginReq struct {
	ClientID string `json:"client_id" binding:"required"`
	Token    string `json:"token" binding:"required"`
}

// CustomAuthLogin .
func CustomAuthLogin(c *gin.Context) {
	ctx := c.Request.Context()
	params := &customAuthLoginReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	loginProvider := &auth.CustomAuthLogin{
		ClientID: params.ClientID,
		Token:    params.Token,
	}

	// verify then issue kg token
	if kgErr := loginProvider.Verify(ctx); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	_, _, kgToken, uid, kgErr := loginProvider.IssueTokens(ctx)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	kglog.DebugfCtx(ctx, "CustomAuthLogin, success uid: %s", uid)
	resp := struct {
		KGToken string `json:"kg_token"`
	}{KGToken: kgToken}
	response.OK(c, resp)
}
