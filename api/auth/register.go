package auth

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	userservice "github.com/kryptogo/kg-wallet-backend/service/user"
)

type registerReq struct {
	ClientID             string            `json:"client_id"`
	Handle               string            `json:"handle" binding:"required"`
	Password             string            `json:"password" binding:"required"`
	PasswordSaltFrontend *string           `json:"password_salt_frontend" binding:"required"`
	VaultData            *domain.VaultData `json:"vault_data" binding:"required"`
}

// Register creates a new user with handle and password
func Register(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.auth.Register")
	defer span.End()

	params := &registerReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Check if user already exists
	user, kgErr := userservice.GetByHandle(ctx, params.Handle, "", false, nil)
	if user != nil {
		response.ConflictWithMsg(c, code.UserAlreadyExists, "user already exists")
		return
	}

	if kgErr != nil && kgErr.Code != code.UserNotFound {
		response.KGError(c, kgErr)
		return
	}

	// Create user
	uid, kgErr := userservice.CreateUser(ctx, &firebase.UserParams{
		Handle: params.Handle,
	}, false)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Update user password
	kgErr = userservice.UpdatePassword(ctx, uid, params.Password, params.PasswordSaltFrontend, util.Ptr(false), params.VaultData)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}
