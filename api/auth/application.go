package auth

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// createCustomAuthApplicationParams represents the request params for creating a custom auth application.
type createCustomAuthApplicationParams struct {
	ApplicationName string `json:"application_name" binding:"required"`
	Domain          string `json:"domain" binding:"required"`
	VerifierType    string `json:"verifier_type" binding:"required"`
	VerifierDetails struct {
		JwkEndpoint string `json:"jwk_endpoint"`
		Audience    string `json:"audience"`
		Issuer      string `json:"issuer"`
		UIDField    string `json:"uid_field"`
	} `json:"verifier_details" binding:"required"`
}

func (c *createCustomAuthApplicationParams) AfterValidate() error {
	if c.VerifierType == string(domain.CustomAuthVerifierTypeJwt) {
		if c.VerifierDetails.JwkEndpoint == "" {
			return fmt.Errorf("jwk_endpoint is required")
		}
		if c.VerifierDetails.Audience == "" {
			return fmt.Errorf("audience is required")
		}
		if c.VerifierDetails.Issuer == "" {
			return fmt.Errorf("issuer is required")
		}
		if c.VerifierDetails.UIDField == "" {
			return fmt.Errorf("uid_field is required")
		}
	}
	return nil
}

// CreateCustomAuthApplication creates a custom auth application. Will generate client ID and client secret randomly.
func CreateCustomAuthApplication(c *gin.Context) {
	ctx := c.Request.Context()
	orgID, err := strconv.Atoi(c.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid organization")
		return
	}

	params := &createCustomAuthApplicationParams{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	vType, err := domain.ParseCustomAuthVerifierType(params.VerifierType)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid verifier type")
		return
	}

	app := &domain.CustomAuthApplication{
		Application: domain.Application{
			Name:         params.ApplicationName,
			Domain:       params.Domain,
			ClientID:     util.RandHex(32),
			ClientSecret: util.RandString(32),
		},
		VerifierType: vType,
	}
	// only supports jwt now, so only jwt has all params here
	if vType == domain.CustomAuthVerifierTypeJwt {
		app.JwtVerifierParams = &domain.JwtVerifierParams{
			JwkURL:   params.VerifierDetails.JwkEndpoint,
			Audience: params.VerifierDetails.Audience,
			Issuer:   params.VerifierDetails.Issuer,
			UIDField: params.VerifierDetails.UIDField,
		}
	}

	kgErr = application.UpsertCustomAuthApplication(ctx, orgID, app)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	go oauth.NotifyClientConfigChanges()

	respData := struct {
		ClientID string `json:"client_id"`
	}{app.ClientID}
	response.OK(c, respData)
}
