package auth

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	userService "github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestRegisterWithHandle(t *testing.T) {
	rdb.Reset()
	userService.Init(repo.Unified())

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	fn := mockCreateAUser(ctrl)
	chatroom.GetService = fn

	mockRepo := asset.NewMockIRepo(ctrl)
	asset.Init(mockRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, domain.NewMockPriceFetcher(ctrl))
	mockRepo.EXPECT().SetTokenAmounts(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(_ context.Context, amounts map[domain.ChainAddress][]*domain.TokenAmount) error {
		return nil
	})
	mockRepo.EXPECT().SetNftAmounts(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(_ context.Context, amounts map[domain.ChainAddress][]*domain.NftAmount) error {
		return nil
	})
	mockRepo.EXPECT().SetDefiAssets(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(_ context.Context, assets map[domain.ChainAddress][]*domain.DefiAsset) error {
		return nil
	})
	mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(_ context.Context, prices []*domain.TokenPrice) error {
		return nil
	})

	server := gin.Default()
	server.POST("/v1/register", Register)
	server.POST("/v1/login", Login)
	handle := util.RandHandle()
	t.Logf("handle: %s", handle)

	t.Run("No handle", func(t *testing.T) {
		body := map[string]interface{}{
			"handle":                 "",
			"password":               "password",
			"password_salt_frontend": "password_salt_frontend",
			"vault_data": map[string]interface{}{
				"random_value": "random_value",
			},
		}
		bodyStr, err := json.Marshal(body)
		assert.Nil(t, err)

		req, err := http.NewRequest("POST", "/v1/register", bytes.NewBuffer(bodyStr))
		assert.Nil(t, err)

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("No password_salt_frontend", func(t *testing.T) {
		body := map[string]interface{}{
			"handle":   "handle",
			"password": "password",
			"vault_data": map[string]interface{}{
				"random_value": "random_value",
			},
		}
		bodyStr, err := json.Marshal(body)
		assert.Nil(t, err)

		req, err := http.NewRequest("POST", "/v1/register", bytes.NewBuffer(bodyStr))
		assert.Nil(t, err)

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
	})

	t.Run("Register success", func(t *testing.T) {
		mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
		mockAlchemy.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
			return nil
		})
		alchemyapi.Set(mockAlchemy)

		body := map[string]interface{}{
			"handle":                 handle,
			"password":               "password",
			"password_salt_frontend": "password_salt_frontend",
			"vault_data": map[string]interface{}{
				"random_value": "random_value",
			},
		}
		bodyStr, err := json.Marshal(body)
		assert.Nil(t, err)

		req, err := http.NewRequest("POST", "/v1/register", bytes.NewBuffer(bodyStr))
		assert.Nil(t, err)

		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		t.Logf("register response: %s", w.Body.String())
		t.Logf("register status: %d", w.Code)
		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code int `json:"code"`
		}
		assert.Nil(t, json.Unmarshal(w.Body.Bytes(), &response))
		assert.Equal(t, 0, response.Code)

		// login with bad password
		{
			body = map[string]interface{}{
				"handle":   handle,
				"password": "bad_password",
			}
			jsonStr, err := json.Marshal(body)
			assert.Nil(t, err)
			req, err := http.NewRequest("POST", "/v1/login", bytes.NewBuffer(jsonStr))
			assert.Nil(t, err)
			w := httptest.NewRecorder()
			server.ServeHTTP(w, req)

			t.Logf("login with bad password status: %d", w.Code)
			t.Logf("login with bad password response: %s", w.Body.String())
			assert.Equal(t, http.StatusBadRequest, w.Code)
			var response testLoginResponse
			responseStr, _ := io.ReadAll(w.Body)
			assert.Nil(t, json.Unmarshal(responseStr, &response))
			assert.Equal(t, 1041, response.Code)
			assert.Empty(t, response.Data.AccessToken)
			assert.Empty(t, response.Data.IDToken)
			assert.Empty(t, response.Data.KgToken)
		}

		// login with good password
		// Login twice because we previously had a bug which would wipe out all data an user has
		// upon the first login
		{
			body = map[string]interface{}{
				"handle":   handle,
				"password": "password",
			}
			jsonStr, err := json.Marshal(body)
			assert.Nil(t, err)
			req, err := http.NewRequest("POST", "/v1/login", bytes.NewBuffer(jsonStr))
			assert.Nil(t, err)
			w := httptest.NewRecorder()
			server.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
			responseStr, _ := io.ReadAll(w.Body)
			var response testLoginResponse
			assert.Nil(t, json.Unmarshal(responseStr, &response))
			assert.Equal(t, 0, response.Code)
			assert.NotEmpty(t, response.Data.AccessToken)
			assert.NotEmpty(t, response.Data.IDToken)
			assert.NotEmpty(t, response.Data.KgToken)

			// Login again
			req, err = http.NewRequest("POST", "/v1/login", bytes.NewBuffer(jsonStr))
			assert.Nil(t, err)
			w = httptest.NewRecorder()
			server.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
			responseStr, _ = io.ReadAll(w.Body)
			assert.Nil(t, json.Unmarshal(responseStr, &response))
			assert.Equal(t, 0, response.Code)
			assert.NotEmpty(t, response.Data.AccessToken)
			assert.NotEmpty(t, response.Data.IDToken)
			assert.NotEmpty(t, response.Data.KgToken)
		}

		// Registered handle should not be allowed to register again
		{
			body := map[string]interface{}{
				"handle":                 handle,
				"password":               "another-password",
				"password_salt_frontend": "another-password_salt_frontend",
				"vault_data": map[string]interface{}{
					"random_value": "another-random_value",
				},
			}

			bodyStr, err := json.Marshal(body)
			assert.Nil(t, err)

			req, err := http.NewRequest("POST", "/v1/register", bytes.NewBuffer(bodyStr))
			assert.Nil(t, err)

			w := httptest.NewRecorder()
			server.ServeHTTP(w, req)
			t.Logf("register response: %s", w.Body.String())
			t.Logf("register status: %d", w.Code)
			assert.Equal(t, http.StatusConflict, w.Code)
			var response struct {
				Code int `json:"code"`
			}
			assert.Nil(t, json.Unmarshal(w.Body.Bytes(), &response))
		}
	})
}
