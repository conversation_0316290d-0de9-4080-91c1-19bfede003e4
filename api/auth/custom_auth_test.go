package auth

import (
	"bytes"
	"context"
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	customerRepo "github.com/kryptogo/kg-wallet-backend/pkg/service/customer/repo"
	oauthservice "github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/verifier"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	userService "github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/lestrrat-go/jwx/jwk"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

const jwkURL = "localhost:6543"
const customAuthLoginUrl = "/v1/login/custom_auth"
const createCustomAuthAppUrl = "/v1/studio/organization/:orgID/custom_auth_application"
const createUrl = "/v1/studio/organization/1/custom_auth_application"
const authorizeUrl = "/v1/oauth/authorize"

func TestCustomAuthSuite(t *testing.T) {
	suite.Run(t, new(CustomAuthSuite))
}

type CustomAuthSuite struct {
	suite.Suite
	privateKey *rsa.PrivateKey
	r          *gin.Engine
	shutdown   func()
}

func (s *CustomAuthSuite) SetupTest() {
	s.privateKey, s.r, s.shutdown = setup(s.T())
}

func (s *CustomAuthSuite) TearDownTest() {
	s.shutdown()
}

func (s *CustomAuthSuite) SetupSuite() {
	userService.Init(repo.Unified())
}

func (s *CustomAuthSuite) TestCustomAuthLoginAndAuthorize() {
	t := s.T()
	clientID := createCustomAuthApplication(t, s.r)

	// prepare request token and login with custom auth
	statusCode, loginRes := loginWithClaims(t, s.r, s.privateKey, clientID, jwt.MapClaims{
		"iss": "https://auth.kryptogo.com",
		"aud": "https://kryptogo.com",
		"exp": time.Now().Add(time.Hour).Unix(),
		"sub": "test-user-1",
	})
	assert.Equal(t, http.StatusOK, statusCode)
	assert.Equal(t, 0, loginRes.Code)
	assert.NotEmpty(t, loginRes.Data.KGToken)
	kgToken := loginRes.Data.KGToken

	// authorize with the token
	location := authorizeWithToken(t, s.r, clientID, kgToken)
	assert.Contains(t, location, "http://localhost:8040/v1/oauth/callback")
	assert.Contains(t, location, "code=")
	assert.Contains(t, location, "state=xyz")
}

func (s *CustomAuthSuite) TestCustomLoginFailed() {
	t := s.T()
	clientID := createCustomAuthApplication(t, s.r)

	cases := []struct {
		name   string
		claims jwt.MapClaims
		expect int
	}{
		{
			name: "Wrong Issuer",
			claims: jwt.MapClaims{
				"iss": "https://kryptogo.com",
				"aud": "https://kryptogo.com",
				"exp": time.Now().Add(time.Hour).Unix(),
				"sub": "test-user-1",
			},
			expect: http.StatusUnauthorized,
		},
		{
			name: "Wrong Audience",
			claims: jwt.MapClaims{
				"iss": "https://auth.kryptogo.com",
				"aud": "https://kryptogo.com/wrong",
				"exp": time.Now().Add(time.Hour).Unix(),
				"sub": "test-user-1",
			},
			expect: http.StatusUnauthorized,
		},
		{
			name: "Expired Token",
			claims: jwt.MapClaims{
				"iss": "https://auth.kryptogo.com",
				"aud": "https://kryptogo.com",
				"exp": time.Now().Add(-time.Hour).Unix(),
				"sub": "test-user-1",
			},
			expect: http.StatusUnauthorized,
		},
		{
			name: "No Sub",
			claims: jwt.MapClaims{
				"iss": "https://auth.kryptogo.com",
				"aud": "https://kryptogo.com",
				"exp": time.Now().Add(time.Hour).Unix(),
				// "sub" is intentionally omitted
			},
			expect: http.StatusUnauthorized,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			statusCode, loginRes := loginWithClaims(t, s.r, s.privateKey, clientID, tc.claims)
			assert.Equal(t, tc.expect, statusCode)
			assert.Equal(t, code.InvalidCustomToken, loginRes.Code)
		})
	}
}

func (s *CustomAuthSuite) TestCustomAuthLoginAuthorizeWrongClient() {
	t := s.T()
	clientID := createCustomAuthApplication(t, s.r)

	// prepare request token and login with custom auth
	statusCode, loginRes := loginWithClaims(t, s.r, s.privateKey, clientID, jwt.MapClaims{
		"iss": "https://auth.kryptogo.com",
		"aud": "https://kryptogo.com",
		"exp": time.Now().Add(time.Hour).Unix(),
		"sub": "test-user-1",
	})
	assert.Equal(t, http.StatusOK, statusCode)
	assert.Equal(t, 0, loginRes.Code)
	assert.NotEmpty(t, loginRes.Data.KGToken)
	kgToken := loginRes.Data.KGToken

	// authorize with the token, but wrong client ID
	location := authorizeWithToken(t, s.r, "74fac8dfa83c8ce0bddda692de170997", kgToken)
	assert.Contains(t, location, "error=server_error")
	assert.Contains(t, location, "state=xyz")
}

func (s *CustomAuthSuite) TestCreateCustomAuthAppBadRequest() {
	t := s.T()
	for _, reqBody := range []map[string]interface{}{
		{ // missing application_name
			"application_name": "",
			"domain":           "http://localhost:8040",
			"verifier_type":    "jwt",
			"verifier_details": map[string]interface{}{
				"jwk_endpoint": fmt.Sprintf("http://%s/.well-known/jwks.json", jwkURL),
				"audience":     "https://kryptogo.com",
				"issuer":       "https://auth.kryptogo.com",
				"uid_field":    "sub",
			},
		},
		{ // missing domain
			"application_name": "KG Testing",
			"domain":           "",
			"verifier_type":    "jwt",
			"verifier_details": map[string]interface{}{
				"jwk_endpoint": fmt.Sprintf("http://%s/.well-known/jwks.json", jwkURL),
				"audience":     "https://kryptogo.com",
				"issuer":       "https://auth.kryptogo.com",
				"uid_field":    "sub",
			},
		},
		{ // missing uid_field
			"application_name": "KG Testing",
			"domain":           "http://localhost:8040",
			"verifier_type":    "jwt",
			"verifier_details": map[string]interface{}{
				"jwk_endpoint": fmt.Sprintf("http://%s/.well-known/jwks.json", jwkURL),
				"audience":     "https://kryptogo.com",
				"issuer":       "https://auth.kryptogo.com",
				"uid_field":    "",
			},
		},
		{ // missing audience
			"application_name": "KG Testing",
			"domain":           "http://localhost:8040",
			"verifier_type":    "jwt",
			"verifier_details": map[string]interface{}{
				"jwk_endpoint": fmt.Sprintf("http://%s/.well-known/jwks.json", jwkURL),
				"audience":     "",
				"issuer":       "https://auth.kryptogo.com",
				"uid_field":    "",
			},
		},
	} {
		reqBodyBytes, _ := json.Marshal(reqBody)
		reqBodyReader := bytes.NewReader(reqBodyBytes)
		req, err := http.NewRequest("POST", createUrl, reqBodyReader)
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		s.r.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
		responseStr, _ := io.ReadAll(w.Body)
		t.Logf("createResponse: %s\n", responseStr)
	}
}

func setup(t *testing.T) (*rsa.PrivateKey, *gin.Engine, func()) {
	rdb.Reset()
	assert.Nil(t, dbtest.CreateStudioDefault(rdb.Get()))
	application.Init(rdb.GormRepo())
	oauthservice.Init(rdb.GormRepo())
	customer.Init(customerRepo.NewCustomerRepo())
	privateKey, shutdown, err := setupJWKServer(t, jwkURL)
	assert.Nil(t, err)
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)

	r := gin.Default()
	r.POST(customAuthLoginUrl, CustomAuthLogin)
	r.POST(createCustomAuthAppUrl, CreateCustomAuthApplication)
	r.GET(authorizeUrl, oauth.AuthorizeHandler)
	return privateKey, r, shutdown
}

func setupJWKServer(t *testing.T, jwkURL string) (*rsa.PrivateKey, func(), error) {
	// Generate a new RSA private key in PEM format and convert to JWK
	_, privateKey, err := verifier.GenerateRSAPrivateKeyPEM()
	assert.Nil(t, err)
	jwkKey, err := jwk.New(privateKey.Public())
	assert.Nil(t, err)

	assert.Nil(t, jwkKey.Set(jwk.KeyIDKey, "test-kid"))
	assert.Nil(t, jwkKey.Set(jwk.AlgorithmKey, "RS256"))
	set := jwk.NewSet()
	set.Add(jwkKey)

	// Setup Gin router
	router := gin.Default()
	router.GET("/.well-known/jwks.json", func(c *gin.Context) {
		assert.Nil(t, json.NewEncoder(c.Writer).Encode(set))
	})

	// listen on localhost:6789
	server := &http.Server{
		Addr:    jwkURL,
		Handler: router,
	}
	shutdown := func() {
		_ = server.Shutdown(context.Background())
	}
	go func() {
		if err := server.ListenAndServe(); err != nil {
			fmt.Printf("failed to start jwk server: %v\n", err)
		}
	}()
	return privateKey, shutdown, nil
}

func createCustomAuthApplication(t *testing.T, r *gin.Engine) (clientID string) {
	// create custom auth application
	reqBody := map[string]interface{}{
		"application_name": "KG Testing",
		"domain":           "http://localhost:8040",
		"verifier_type":    "jwt",
		"verifier_details": map[string]interface{}{
			"jwk_endpoint": fmt.Sprintf("http://%s/.well-known/jwks.json", jwkURL),
			"audience":     "https://kryptogo.com",
			"issuer":       "https://auth.kryptogo.com",
			"uid_field":    "sub",
		},
	}
	reqBodyBytes, _ := json.Marshal(reqBody)
	reqBodyReader := bytes.NewReader(reqBodyBytes)
	req, err := http.NewRequest("POST", createUrl, reqBodyReader)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	var createRes struct {
		Code int `json:"code"`
		Data struct {
			ClientID string `json:"client_id"`
		} `json:"data"`
	}
	responseStr, _ := io.ReadAll(w.Body)
	t.Logf("createResponse: %s\n", responseStr)
	assert.Nil(t, json.Unmarshal(responseStr, &createRes))
	assert.Equal(t, 0, createRes.Code)
	assert.NotEmpty(t, createRes.Data.ClientID)
	clientID = createRes.Data.ClientID
	return
}

type loginResponse struct {
	Code int `json:"code"`
	Data struct {
		KGToken string `json:"kg_token"`
	} `json:"data"`
}

func loginWithClaims(t *testing.T, r *gin.Engine, privateKey *rsa.PrivateKey, clientID string, claims jwt.MapClaims) (int, *loginResponse) {
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
	token.Header["kid"] = "test-kid"
	tokenStr, err := token.SignedString(privateKey)
	assert.Nil(t, err)
	reqBody := map[string]interface{}{
		"client_id": clientID,
		"token":     tokenStr,
	}
	reqBodyBytes, _ := json.Marshal(reqBody)
	reqBodyReader := bytes.NewReader(reqBodyBytes)
	req, err := http.NewRequest("POST", customAuthLoginUrl, reqBodyReader)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	var loginRes loginResponse
	responseStr, _ := io.ReadAll(w.Body)
	t.Logf("loginRes: %s\n", responseStr)
	assert.Nil(t, json.Unmarshal(responseStr, &loginRes))
	return w.Code, &loginRes
}

func authorizeWithToken(t *testing.T, r *gin.Engine, clientID, kgToken string) string {
	// authorize with the token
	scope := "user.info:read,user.info:write,wallet.defaultWallets:read,wallet.defaultWallets:write,wallet.allWallets:read,wallet.allWallets:write,vault:read,vault:write,asset:read"
	params := url.Values{
		"client_id":     {clientID},
		"redirect_uri":  {"http://localhost:8040/v1/oauth/callback"},
		"response_type": {"code"},
		"scope":         {scope},
		"state":         {"xyz"},
	}
	urlWithParam := authorizeUrl + "?" + params.Encode()
	req, _ := http.NewRequest("GET", urlWithParam, nil)
	req.Header.Set("KG-TOKEN", kgToken)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusFound, w.Code)
	location := w.Header().Get("Location")
	return location
}
