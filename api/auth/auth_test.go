package auth

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/api/kms"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/user"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	userService "github.com/kryptogo/kg-wallet-backend/service/user"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

type testLoginResponse struct {
	Code    int           `json:"code"`
	Data    testLoginData `json:"data"`
	Message string        `json:"message"`
}

type testLoginData struct {
	AccessToken string `json:"access_token"`
	IDToken     string `json:"id_token"`
	KgToken     string `json:"kg_token"`
}

func TestLoginWithEmailCode(t *testing.T) {
	rdb.Reset()
	users, _, _, email := dbtest.User()
	userService.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	assert.Nil(t, rdbtest.CreateEmailLogs(rdb.Get(), []string{email}, nil))
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	fn := mockCreateAUser(ctrl)
	chatroom.GetService = fn

	url := "/"
	server := gin.Default()
	server.POST(url, Login)

	// email + verification code
	addBody := map[string]interface{}{
		"email":             email + " ", // add space to test trim space
		"verification_code": "123456",
	}
	jsonStr, err := json.Marshal(addBody)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response testLoginResponse
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	fmt.Println(response)
	assert.Equal(t, 0, response.Code)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.IDToken)
	assert.NotEmpty(t, response.Data.KgToken)
}

func TestLoginWithEmailPassword(t *testing.T) {
	rdb.Reset()
	users, uid, _, email := dbtest.User()
	userService.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	fn := mockCreateAUser(ctrl)
	chatroom.GetService = fn

	url := "/"
	server := gin.Default()
	server.POST(url, Login)
	server.PUT("/v1/auth/password", auth.MockAuthorize(uid), user.UpdatePassword)

	type generalResp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}

	// email + password
	addBody := map[string]interface{}{
		"email":    email,
		"password": "harry",
	}
	jsonStr, err := json.Marshal(addBody)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response testLoginResponse
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.IDToken)
	assert.NotEmpty(t, response.Data.KgToken)

	// update password, using new hashing algorithm
	updateBody := map[string]interface{}{
		"password": "harry2",
		"vault_data": map[string]interface{}{
			"random_value": "random_value",
		},
	}
	jsonStr, err = json.Marshal(updateBody)
	assert.Nil(t, err)
	req, err = http.NewRequest("PUT", "/v1/auth/password", bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var resp generalResp
	err = json.Unmarshal(w.Body.Bytes(), &resp)
	assert.Nil(t, err)
	assert.Equal(t, 0, resp.Code)
	assert.Empty(t, resp.Message)

	// login with new password
	addBody = map[string]interface{}{
		"email":    email,
		"password": "harry2",
	}
	jsonStr, err = json.Marshal(addBody)
	assert.Nil(t, err)
	req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.Empty(t, response.Message)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.IDToken)
	assert.NotEmpty(t, response.Data.KgToken)

	// re-login
	addBody = map[string]interface{}{
		"email":    email,
		"password": "harry2",
	}
	jsonStr, err = json.Marshal(addBody)
	assert.Nil(t, err)
	req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.Empty(t, response.Message)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.IDToken)
	assert.NotEmpty(t, response.Data.KgToken)
}

func setupKMSServer(t *testing.T) func() {
	rKMS := gin.Default()
	rKMS.POST("/v1/kms/generateMnemonicAddresses", kms.GenerateMnemonicAddresses)

	srv := &http.Server{
		Addr:    ":" + config.GetString("TEST_KMS_PORT"),
		Handler: rKMS,
	}
	go func() {
		err := srv.ListenAndServe()
		t.Logf("KMS server terminated with error: %v\n", err)
	}()

	time.Sleep(300 * time.Millisecond)
	return func() {
		assert.NoError(t, srv.Shutdown(context.Background()))
		fmt.Println("Finish Shutdown")
	}
}

func TestLoginWithEmailCodeAndCreateWallets(t *testing.T) {
	shutdownKMSServer := setupKMSServer(t)
	defer shutdownKMSServer()

	rdb.Reset()
	userService.Init(repo.Unified())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	fn := mockCreateAUser(ctrl)
	chatroom.GetService = fn

	url := "/"
	server := gin.Default()
	server.POST(url, Login)
	server.POST("/user/wallets/all", auth.AuthorizeByKgToken(), user.CreateUserWallets)
	server.GET("/user/info", auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeUserInfoRead}), user.GetUserInfo)

	// email + password
	email := "<EMAIL>"
	existingUser, err := firebase.GetUserByEmail(context.Background(), email)
	if err == nil {
		assert.Nil(t, firebase.DeleteUser(existingUser.UID))
	}

	loginBody := map[string]interface{}{
		"email":             email,
		"verification_code": "111111",
	}
	jsonStr, err := json.Marshal(loginBody)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response testLoginResponse
	responseStr, _ := io.ReadAll(w.Body)
	t.Logf("login response: %s\n", responseStr)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.IDToken)
	assert.NotEmpty(t, response.Data.KgToken)

	// create wallets
	req, err = http.NewRequest("POST", "/user/wallets/all", nil)
	assert.Nil(t, err)
	req.Header.Set("KG-TOKEN", response.Data.KgToken)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// Test /user/info endpoint
	req, err = http.NewRequest("GET", "/user/info", nil)
	assert.Nil(t, err)
	req.Header.Set("KG-TOKEN", response.Data.KgToken)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	t.Logf("User info response: %s\n", responseStr)
	if w.Code != http.StatusOK {
		t.FailNow()
	}

	var userInfoResp struct {
		Code int `json:"code"`
		Data struct {
			Email string `json:"email"`
		} `json:"data"`
	}
	assert.Nil(t, json.Unmarshal(responseStr, &userInfoResp))
	assert.Equal(t, 0, userInfoResp.Code)
	assert.Equal(t, email, userInfoResp.Data.Email)
}

func TestLoginWithPhoneCode(t *testing.T) {
	rdb.Reset()
	users, _, phone, _ := dbtest.User()
	userService.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	assert.Nil(t, rdbtest.CreateSmsLogs(rdb.Get(), phone))
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	fn := mockCreateAUser(ctrl)
	chatroom.GetService = fn

	url := "/"
	server := gin.Default()
	server.POST(url, Login)

	// phone + sms code
	addBody := map[string]interface{}{
		"phone_number": phone,
		"sms_code":     "123456",
	}
	jsonStr, err := json.Marshal(addBody)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response testLoginResponse
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	fmt.Println(response)
	assert.Equal(t, 0, response.Code)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.IDToken)
	assert.NotEmpty(t, response.Data.KgToken)
}

func TestLoginWithPhonePassword(t *testing.T) {
	rdb.Reset()
	users, uid, phone, _ := dbtest.User()
	userService.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	fn := mockCreateAUser(ctrl)
	chatroom.GetService = fn

	url := "/"
	server := gin.Default()
	server.POST(url, Login)
	server.PUT("/v1/auth/password", auth.MockAuthorize(uid), user.UpdatePassword)

	// phone + password
	addBody := map[string]interface{}{
		"phone_number": phone,
		"password":     "harry",
	}
	jsonStr, err := json.Marshal(addBody)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response testLoginResponse
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.IDToken)
	assert.NotEmpty(t, response.Data.KgToken)

	// update password, using new hashing algorithm
	updateBody := map[string]interface{}{
		"password": "harry2",
		"vault_data": map[string]interface{}{
			"random_value": "random_value",
		},
	}
	jsonStr, err = json.Marshal(updateBody)
	assert.Nil(t, err)
	req, err = http.NewRequest("PUT", "/v1/auth/password", bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// login with new password
	addBody = map[string]interface{}{
		"phone_number": phone,
		"password":     "harry2",
	}
	jsonStr, err = json.Marshal(addBody)
	assert.Nil(t, err)
	req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.IDToken)
	assert.NotEmpty(t, response.Data.KgToken)

	// re-login
	addBody = map[string]interface{}{
		"phone_number": phone,
		"password":     "harry2",
	}
	jsonStr, err = json.Marshal(addBody)
	assert.Nil(t, err)
	req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.IDToken)
	assert.NotEmpty(t, response.Data.KgToken)
}

func TestLoginWithHandlePassword(t *testing.T) {
	rdb.Reset()
	userData, _ := dbtest.Users()
	userService.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), userData)

	// find an user with handle
	uid := dbtest.UIDWithHandle(userData)
	handle := userData[uid].Handle
	t.Logf("handle: %s", *handle)

	_, err := firebase.BatchCreateUsersBySeed(userData)
	assert.Nil(t, err)

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	server := gin.Default()
	server.POST("/v1/auth/login", Login)

	// handle + password
	addBody := map[string]interface{}{
		"handle":   handle,
		"password": "harry",
	}
	jsonStr, err := json.Marshal(addBody)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", "/v1/auth/login", bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response testLoginResponse
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.IDToken)
	assert.NotEmpty(t, response.Data.KgToken)
}

func mockCreateAUser(ctrl *gomock.Controller) func(client sendbirdapi.SendbirdClientI) chatroom.Service {
	return func(client sendbirdapi.SendbirdClientI) chatroom.Service {
		m := chatroom.NewMockService(ctrl)
		m.EXPECT().CreateAUser(gomock.Any(), gomock.Any()).AnyTimes().Return(&sendbirdapi.User{
			UserID:     "user1",
			Nickname:   "nickname",
			ProfileURL: "profile_url",
		}, &resty.Response{}, nil)
		return m
	}
}

func TestUserNotExistWithEmail(t *testing.T) {
	r := gin.Default()
	url := "/v1/auth/user_exist"
	r.POST(url, UserExist)

	body := map[string]interface{}{
		"email": util.RandEmail(),
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)

	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	var response userExistResp
	responseStr, _ := io.ReadAll(w.Body)
	fmt.Println("responseStr", string(responseStr))
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.False(t, response.Data.Exist)
	assert.False(t, response.Data.HasPassword)
	assert.False(t, response.Data.HasShareKey)
	assert.False(t, response.Data.IsDeleted)
}

func TestUserNotExistWithPhone(t *testing.T) {
	userService.Init(repo.Unified())

	r := gin.Default()
	url := "/v1/auth/user_exist"
	r.POST(url, UserExist)

	body := map[string]interface{}{
		"phone_number": util.RandPhone(),
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)

	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	var response userExistResp
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.False(t, response.Data.Exist)
	assert.False(t, response.Data.HasPassword)
	assert.False(t, response.Data.HasShareKey)
	assert.False(t, response.Data.IsDeleted)
}

func TestUserNotExistWithHandle(t *testing.T) {
	userService.Init(repo.Unified())

	url := "/v1/auth/user_exist"
	r := gin.Default()
	r.POST(url, UserExist)

	body := map[string]interface{}{
		"handle": util.RandHandle(),
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)

	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	var response userExistResp
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.False(t, response.Data.Exist)
	assert.False(t, response.Data.HasPassword)
	assert.False(t, response.Data.HasShareKey)
	assert.False(t, response.Data.IsDeleted)
}

func TestUserWithHandleDeleted(t *testing.T) {
	rdb.Reset()
	userService.Init(repo.Unified())
	handle := util.RandHandle()
	user := rdb.Get().Create(&model.User{
		UID:       "123",
		Handle:    &handle,
		DeletedAt: gorm.DeletedAt{Time: time.Now(), Valid: true},
	})
	assert.Nil(t, user.Error)

	url := "/v1/auth/user_exist"
	r := gin.Default()
	r.POST(url, UserExist)

	body := map[string]interface{}{
		"handle": handle,
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)

	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	var response userExistResp
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.True(t, response.Data.Exist)
	assert.False(t, response.Data.HasPassword)
	assert.False(t, response.Data.HasShareKey)
	assert.True(t, response.Data.IsDeleted)
}

func TestLoginRatelimit(t *testing.T) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	r := gin.Default()
	url := "/v1/auth/login"
	r.POST(url, Login)

	// phone rate limit
	phone := util.RandPhone()
	body := map[string]interface{}{
		"phone_number": phone,
		"sms_code":     util.RandDigits(6),
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)

	ratelimit := 30
	for i := 0; i < ratelimit+1; i++ {
		req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		if i < ratelimit {
			assert.Equal(t, http.StatusUnauthorized, w.Code)
		} else {
			assert.Equal(t, http.StatusTooManyRequests, w.Code)
		}
	}

	// email rate limit
	email := util.RandEmail()
	body = map[string]interface{}{
		"email":             email,
		"verification_code": util.RandDigits(6),
	}
	bodyStr, err = json.Marshal(body)
	assert.Nil(t, err)

	for i := 0; i < ratelimit+1; i++ {
		req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		if i < ratelimit {
			assert.Equal(t, http.StatusBadRequest, w.Code)
		} else {
			assert.Equal(t, http.StatusTooManyRequests, w.Code)
		}
	}
}

func TestSmsWhitelist(t *testing.T) {
	// Setup
	r := gin.Default()
	url := "/v1/sms/code"
	r.POST(url, SmsCode)

	// Test whitelisted phone number
	whitelistedPhone := "+923000000001"
	body := map[string]interface{}{
		"phone_number": whitelistedPhone,
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)

	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	// Assert that the whitelisted number bypasses phone number verification, and fails with recaptcha
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// Test non-whitelisted phone number
	nonWhitelistedPhone := "+923111111111"
	body = map[string]interface{}{
		"phone_number": nonWhitelistedPhone,
	}
	bodyStr, err = json.Marshal(body)
	assert.Nil(t, err)

	req, err = http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	// Assert that non-whitelisted number doesn't bypass SMS verification
	assert.Equal(t, http.StatusForbidden, w.Code)

	otherCountryPhone := "+1234567890"
	body = map[string]interface{}{
		"phone_number": otherCountryPhone,
	}
	bodyStr, err = json.Marshal(body)
	assert.Nil(t, err)

	req, err = http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	// Assert that the whitelisted number bypasses phone number verification, and fails with recaptcha
	assert.Equal(t, http.StatusBadRequest, w.Code)
}
