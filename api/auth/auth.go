package auth

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	authmiddleware "github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/recaptcha"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/l10n"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/sms"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	lo "github.com/samber/lo"
)

type smsCategory string

const (
	smsCategoryLogin             smsCategory = "login"
	smsCategoryChangePhoneNumber smsCategory = "change_phone_number"
)

type emailCategory string

const (
	emailCategoryLogin       emailCategory = "login"
	emailCategoryChangeEmail emailCategory = "change_email"
)

type smsCodeReq struct {
	PhoneNumber    string      `json:"phone_number" binding:"required"`
	Category       smsCategory `json:"category"`
	RecaptchaToken string      `json:"recaptcha_token"`
}

type smsCodeResp struct {
	Code int    `json:"code"`
	Data string `json:"data"`
}

// SmsCode .
func SmsCode(c *gin.Context) {
	ctx := c.Request.Context()
	params := &smsCodeReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// check if phone number is valid
	phoneNumber := strings.TrimSpace(params.PhoneNumber)
	if !sms.IsPhoneNumberValid(phoneNumber) {
		kglog.DebugfCtx(ctx, "SmsCode, invalid phone number: %s", phoneNumber)
		response.BadRequestWithMsg(c, code.PhoneFormatError, "invalid phone number")
		return
	}

	// whitelist for specific country
	// e.g.: if start with +92, then check if it's in Pakistan whitelist
	smsWhitelist := config.GetStringMapSlice("SMS_WHITELIST")
	for countryCode, phoneNumbers := range smsWhitelist {
		if strings.HasPrefix(phoneNumber, countryCode) {
			if !lo.Contains(phoneNumbers, phoneNumber) {
				kglog.DebugfCtx(ctx, "SmsCode, phone number not in whitelist: %s", phoneNumber)
				response.ForbiddenErrorWithMsg(c, code.PhoneNumberBlocked, "invalid phone number")
				return
			}
		}
	}

	if len(params.Category) > 0 {
		if params.Category != smsCategoryLogin && params.Category != smsCategoryChangePhoneNumber {
			kglog.DebugfCtx(ctx, "SmsCode, invalid category: %s", params.Category)
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid category")
			return
		}
	}

	if auth.PhoneIsTesterOrQA(phoneNumber) {
		c.JSON(http.StatusOK, smsCodeResp{
			Code: code.OK,
			Data: "success",
		})
		return
	}

	developPhoneMap := config.GetStringMap("DEVELOPER_PHONES")
	// if not in develop phone list, use ratelimit
	if _, ok := developPhoneMap[phoneNumber]; !ok {
		// check ratelimit by ip
		if !cache.CheckSMSRateLimitByIP(ctx, c.ClientIP()) {
			kglog.Debugf("SmsCode, ratelimit, ip: %s", c.ClientIP())
			response.TooManyRequestsWithMsg(c, code.RateLimit, "reach sms rate limit")
			return
		}

		// check ratelimit by phone number
		if !cache.CheckSMSRateLimitByPhone(ctx, phoneNumber) {
			kglog.DebugfCtx(ctx, "SmsCode, ratelimit, phone_number: %s", phoneNumber)
			response.TooManyRequestsWithMsg(c, code.RateLimit, "reach sms rate limit")
			return
		}
	}

	// check recaptcha
	appPlatform := c.Request.Header.Get("X-Platform")
	_, kgErr = recaptcha.Check(ctx, appPlatform, params.RecaptchaToken)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// send sms code
	seconds := auth.SmsTtl.Seconds()
	locale := util.LocaleByPhoneNumber(phoneNumber)
	clientID := oauth.ClientID(c)
	oauthApp := application.GetOAuthApplicationOrDefault(ctx, clientID)
	if config.GetBool("ENABLE_TWILIO_VERIFY") && !strings.HasPrefix(phoneNumber, "+86") {
		// new sms login flow: twilio verify
		if oauthApp.TwilioVerifySID == nil || *oauthApp.TwilioVerifySID == "" {
			kglog.WarningfCtx(ctx, "SmsCode, TwilioVerifySID is empty, clientID: %s", clientID)
			response.BadRequestWithMsg(c, code.SMSSendFailed, "verify sid not found")
			return
		}
		kgErr = sms.SendByTwilioVerify(ctx, *oauthApp.TwilioVerifySID, phoneNumber, locale)
		if kgErr != nil {
			response.KGError(c, kgErr)
			return
		}
		c.JSON(http.StatusOK, smsCodeResp{
			Code: code.OK,
			Data: "success",
		})
		return
	}

	// legacy sms login flow: twilio programmable message + twm
	// only used in emergency like twilio verify doesn't work, e.g. China phone number
	smsCode := util.GenerateRandomCode(6)
	// TWM only supports zh_TW
	smsContent := l10n.StringWithParamsInTxtTemp("sms-login-content", util.LocaleZhTW, map[string]string{
		"wallet_name": oauthApp.Name,
		"code":        smsCode,
		"expire":      fmt.Sprintf("%.0f", seconds),
	})
	kgErr = sms.Send(ctx, phoneNumber, smsContent)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// create sms log
	err := rdb.CreateSMSLog(ctx, phoneNumber, smsCode, string(params.Category), clientID)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "CreateSMSLog, ERROR: "+err.Error(), map[string]interface{}{
			"phone_number": phoneNumber,
			"sms_code":     smsCode,
		})
		response.InternalServerErrorWithMsg(c, code.DBError, err.Error())
		return
	}

	c.JSON(http.StatusOK, smsCodeResp{
		Code: code.OK,
		Data: "success",
	})
}

type emailCodeReq struct {
	Email          string        `json:"email" binding:"required"`
	Category       emailCategory `json:"category" binding:"required"`
	RecaptchaToken string        `json:"recaptcha_token"`
}

type emailCodeResp struct {
	Code int    `json:"code"`
	Data string `json:"data"`
}

// EmailCode .
func EmailCode(c *gin.Context) {
	ctx := c.Request.Context()
	params := &emailCodeReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// check if email is valid
	email := strings.TrimSpace(params.Email)
	if !util.IsEmailValid(email) {
		kglog.DebugfCtx(ctx, "EmailCode, invalid email: %s", email)
		response.BadRequestWithMsg(c, code.EmailFormatError, "invalid email")
		return
	}

	if params.Category != emailCategoryLogin && params.Category != emailCategoryChangeEmail {
		kglog.DebugfCtx(ctx, "EmailCode, invalid category: %s", params.Category)
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid category")
		return
	}

	if auth.EmailIsTesterOrQA(email) {
		c.JSON(http.StatusOK, emailCodeResp{
			Code: code.OK,
			Data: "success",
		})
		return
	}

	// check ratelimit by ip
	if !cache.CheckEmailRateLimitByIP(ctx, c.ClientIP()) {
		kglog.DebugfCtx(ctx, "EmailCode, ratelimit, ip: %s", c.ClientIP())
		response.TooManyRequestsWithMsg(c, code.RateLimit, "reach email rate limit")
		return
	}

	// check ratelimit by email
	if !cache.CheckEmailRateLimitByEmail(ctx, email) {
		kglog.DebugfCtx(ctx, "EmailCode, ratelimit, email: %s", email)
		response.TooManyRequestsWithMsg(c, code.RateLimit, "reach email rate limit")
		return
	}

	// check recaptcha
	appPlatform := c.Request.Header.Get("X-Platform")
	_, kgErr = recaptcha.Check(ctx, appPlatform, params.RecaptchaToken)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// generate email code
	emailCode := util.GenerateRandomCode(6)

	// send email code
	clientID := oauth.ClientID(c)
	oauthApp := application.GetOAuthApplicationOrDefault(ctx, clientID)

	sendgridClient := sendgrid.NewClient()
	_, err := sendgridClient.SendEmail(ctx, email, oauthApp.Name, sendgrid.EmailTypeVerificationCode, map[string]string{
		"main_logo":        oauthApp.MainLogo,
		"client_name":      oauthApp.Name,
		"code":             emailCode,
		"expires_at":       time.Now().Add(auth.EmailTtl).Format("2006-01-02 15:04:05"),
		"valid_duration":   strconv.Itoa(int(auth.EmailTtl / time.Minute)),
		"support_address":  oauthApp.SupportAddress,
		"app_store_link":   oauthApp.AppStoreLink,
		"google_play_link": oauthApp.GooglePlayLink,
	})
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "[api.auth] SendEmail, ERROR: "+err.Error(), map[string]interface{}{
			"email": email,
		})
		response.InternalServerErrorWithMsg(c, code.EmailSendFailed, err.Error())
		return
	}

	// create email log
	err = rdb.CreateEmailLog(ctx, email, emailCode, string(params.Category), clientID)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "CreateEmailLog, ERROR: "+err.Error(), map[string]interface{}{
			"email":             email,
			"verification_code": emailCode,
		})
		response.InternalServerErrorWithMsg(c, code.DBError, err.Error())
		return
	}

	c.JSON(http.StatusOK, smsCodeResp{
		Code: code.OK,
		Data: "success",
	})
}

type loginResp struct {
	Code int       `json:"code"`
	Data loginData `json:"data"`
}

type loginData struct {
	AccessToken string `json:"access_token"`
	IDToken     string `json:"id_token"`
	KgToken     string `json:"kg_token"`
}

// Login .
func Login(c *gin.Context) {
	ctx := c.Request.Context()
	params := &auth.LoginReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	clientID := oauth.ClientID(c)
	params.ClientID = clientID

	loginProvider := auth.NewLoginProvider(params)
	if kgErr = loginProvider.Valid(ctx); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if kgErr = loginProvider.Verify(ctx); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	accessToken, idToken, kgToken, uid, kgErr := loginProvider.IssueTokens(ctx)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// FIXME: integrate with onboarding process (login api)
	applicationID, apiTokenName, _ := application.GetSendbirdAppIDAndAPIToken(ctx, clientID)
	if applicationID != "" && apiTokenName != "" {
		// create sendbird user
		user, _ := rdb.GormRepo().GetUser(ctx, uid, "", false, &domain.UserPreloads{
			WithAvatar: true,
		})
		if user == nil {
			kglog.ErrorWithDataCtx(ctx, "GetUser, user not found", map[string]interface{}{
				"uid": uid,
			})
			response.NotFoundWithMsg(c, code.UserNotFound, "user not found")
		}
		sendbirdClient := sendbirdapi.NewClient(applicationID, apiTokenName)
		sendbirdService := chatroom.GetService(sendbirdClient)
		_, _, err := sendbirdService.CreateAUser(ctx, user)
		if err != nil {
			kglog.InfoWithDataCtx(ctx, "CreateAUser, ERROR: "+err.Error(), map[string]interface{}{
				"uid": uid,
			})
			// don't return error, because it may be existing user login
		}
	}

	resp := loginResp{
		Code: code.OK,
		Data: loginData{
			AccessToken: accessToken,
			IDToken:     idToken,
			KgToken:     kgToken,
		},
	}

	c.JSON(http.StatusOK, resp)
}

type userExistReq struct {
	PhoneNumber string `json:"phone_number"`
	Email       string `json:"email"`
	Handle      string `json:"handle"`
}

type userExistResp struct {
	Code int           `json:"code"`
	Data userExistData `json:"data"`
}

type userExistData struct {
	Exist       bool `json:"exist"`
	HasPassword bool `json:"has_password"`
	HasWallet   bool `json:"has_wallet"`
	HasShareKey bool `json:"has_share_key"`
	IsDeleted   bool `json:"is_deleted,omitempty"`
}

// UserExist .
func UserExist(c *gin.Context) {
	ctx := c.Request.Context()
	params := &userExistReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if len(params.PhoneNumber) == 0 && len(params.Email) == 0 && len(params.Handle) == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "none of phone number, email or handle is provided")
		return
	}

	ip := c.ClientIP()

	// check ratelimit
	if !cache.CheckUserExistsRateLimit(ctx, ip) {
		kglog.Debugf("User Exists, ratelimit, ip: %s", ip)
		response.TooManyRequestsWithMsg(c, code.RateLimit, "reach user exists rate limit")
		return
	}

	if len(params.PhoneNumber) > 0 {
		if !cache.CheckUserExistsRateLimitByPhone(ctx, params.PhoneNumber) {
			kglog.Debugf("User Exists, ratelimit, phone_number: %s", params.PhoneNumber)
			response.TooManyRequestsWithMsg(c, code.RateLimit, "reach login rate limit")
			return
		}
	}

	if len(params.Email) > 0 {
		if !cache.CheckUserExistsRateLimitByEmail(ctx, params.Email) {
			kglog.Debugf("User Exists, ratelimit, email: %s", params.Email)
			response.TooManyRequestsWithMsg(c, code.RateLimit, "reach login rate limit")
			return
		}
	}

	if len(params.Handle) > 0 {
		if !cache.CheckUserExistsRateLimitByHandle(ctx, params.Handle) {
			kglog.Debugf("User Exists, ratelimit, handle: %s", params.Handle)
			response.TooManyRequestsWithMsg(c, code.RateLimit, "reach login rate limit")
			return
		}
	}

	var user *domain.UserData
	if len(params.PhoneNumber) > 0 {
		if !sms.IsPhoneNumberValid(params.PhoneNumber) {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "phone number is incorrect")
			return
		}
		user, kgErr = rdb.GormRepo().GetUserByPhoneNumber(ctx, params.PhoneNumber, "", true, &domain.UserPreloads{
			WithWallets: true,
		})

		if kgErr != nil {
			if errors.Is(kgErr.Error, code.ErrUserNotFound) || errors.Is(kgErr.Error, db.ErrorNoMatchingDoc) {
				resp := userExistResp{
					Code: code.OK,
					Data: userExistData{
						Exist:       false,
						HasPassword: false,
						HasWallet:   false,
					},
				}
				c.JSON(http.StatusOK, resp)
				return
			}
			kglog.ErrorWithDataCtx(ctx, "UserByPhoneNumber, ERROR: "+kgErr.String(), map[string]interface{}{
				"phone_number": params.PhoneNumber,
			})
			response.InternalServerErrorWithMsg(c, code.DBError, kgErr.String())
			return
		}
	}

	if len(params.Email) > 0 {
		if !util.IsEmailValid(params.Email) {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "email is incorrect")
			return
		}
		user, kgErr = rdb.GormRepo().GetUserByEmail(ctx, params.Email, "", true, &domain.UserPreloads{
			WithWallets: true,
		})
		if kgErr != nil {
			if errors.Is(kgErr.Error, code.ErrUserNotFound) || errors.Is(kgErr.Error, db.ErrorNoMatchingDoc) {
				resp := userExistResp{
					Code: code.OK,
					Data: userExistData{
						Exist:       false,
						HasPassword: false,
						HasWallet:   false,
					},
				}
				c.JSON(http.StatusOK, resp)
				return
			}
			kglog.ErrorWithDataCtx(ctx, "UserByEmail, ERROR: "+kgErr.String(), map[string]interface{}{
				"email": params.Email,
			})
			response.InternalServerErrorWithMsg(c, code.DBError, kgErr.String())
			return
		}
	}

	if len(params.Handle) > 0 {
		if !util.IsHandleValid(params.Handle) {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "handle is incorrect")
			return
		}
		user, kgErr = rdb.GormRepo().GetUserByHandle(ctx, params.Handle, "", true, &domain.UserPreloads{
			WithWallets: true,
		})
		if kgErr != nil {
			if errors.Is(kgErr.Error, code.ErrUserNotFound) || errors.Is(kgErr.Error, db.ErrorNoMatchingDoc) {
				resp := userExistResp{
					Code: code.OK,
					Data: userExistData{
						Exist:       false,
						HasPassword: false,
						HasWallet:   false,
						IsDeleted:   false,
					},
				}
				c.JSON(http.StatusOK, resp)
				return
			}
			kglog.ErrorWithDataCtx(ctx, "UserByHandle, ERROR: "+kgErr.String(), map[string]interface{}{
				"handle": params.Handle,
			})
			response.InternalServerErrorWithMsg(c, code.DBError, kgErr.String())
			return
		}
	}

	resp := userExistResp{
		Code: code.OK,
		Data: userExistData{
			Exist:       kgErr == nil && user != nil,
			HasPassword: user != nil && user.Password != nil && len(*user.Password) > 0,
			HasWallet:   user != nil && user.Wallets != nil,
			HasShareKey: false, // FIXME: remove in the future, this field should be retrieved in another authed api
			IsDeleted:   user != nil && user.DeletedAt != nil,
		},
	}

	c.JSON(http.StatusOK, resp)
}

// ExchangeKgToken .
func ExchangeKgToken(c *gin.Context) {
	ctx := c.Request.Context()
	uid := authmiddleware.GetUID(c)
	if uid == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "uid is empty")
		return
	}

	clientID := authmiddleware.GetClientID(c)
	// for security, only kg wallet client can exchange kg token using oauth token
	if !application.IsKgWallet(c, clientID) {
		response.ForbiddenErrorWithMsg(c, code.ClientForbidden, "client is forbidden")
		return
	}

	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeKg)
	kgToken, _, err := strategy.CreateToken(uid)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "signKgToken, ERROR: "+err.Error(), map[string]interface{}{
			"uid": uid,
		})
		response.InternalServerErrorWithMsg(c, code.SignKgTokenFailed, err.Error())
		return
	}

	response.OK(c, gin.H{
		"kg_token": kgToken,
	})
}
