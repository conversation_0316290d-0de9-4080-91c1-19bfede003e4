package api

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
)

// UpdateTokenMetadata updates the token metadata
func UpdateTokenMetadata(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "UpdateTokenMetadata")
	defer span.End()

	var req struct {
		Timeout int `json:"timeout" binding:"required"`
	}

	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Create a context with timeout based on requestBody.Timeout
	ctxWithTimeout, cancel := tracing.WithTimeoutAndTrace(ctx, time.Duration(req.Timeout)*time.Second)
	defer cancel()

	err := tokenmeta.Update(ctxWithTimeout)
	if err != nil {
		response.KGError(c, err)
		return
	}
	response.OK(c, nil)
}
