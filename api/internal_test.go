package api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/kms"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// TestCreateTestUsers tests the CreateTestUsers function
func TestCreateTestUsers(t *testing.T) {
	testNumber := 3
	rdb.Reset()

	// kms server
	shutdownKmsServer := setupKmsServer(t)
	defer shutdownKmsServer()

	user.Init(repo.Unified())

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	m := alchemyapi.NewMockIAlchemy(ctrl)
	{
		countOfWebhook := lo.Reduce(lo.Values(config.GetStringMap("ALCHEMY_WEBHOOK_ID_MAP")), func(count int, v string, _ int) int {
			if len(v) == 0 {
				return count
			}

			return count + 1
		}, 0)

		m.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).
			Times(countOfWebhook * testNumber).
			DoAndReturn(func(ctx context.Context, webhookID string, addressesToAdd []string) error {
				for _, address := range addressesToAdd {
					assert.Len(t, address, 42)
				}
				return nil
			})
	}
	alchemyapi.Set(m)

	createTestUsersUrl := "/v1/createtestusers"
	server := gin.Default()
	server.POST(createTestUsersUrl, CreateTestUsers)

	reqBody := &CreateTestUsersReq{
		Number:   testNumber,
		ClientID: "testClient",
	}

	// Convert the request body to bytes for the NewRequest function
	reqBodyBytes, _ := json.Marshal(reqBody)
	reqBodyReader := bytes.NewReader(reqBodyBytes)

	req, err := http.NewRequest("POST", createTestUsersUrl, reqBodyReader)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response struct {
		Code  int        `json:"code"`
		Data  []TestUser `json:"data"`
		Error string     `json:"error"`
	}
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "", response.Error)
	testUsers := response.Data
	assert.Equal(t, testNumber, len(testUsers))
}

// TestCreateUser test create users
func TestCreateUser(t *testing.T) {
	s := assert.New(t)
	rdb.Reset()

	// kms server
	shutdownKmsServer := setupKmsServer(t)
	defer shutdownKmsServer()

	user.Init(repo.Unified())

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	m := alchemyapi.NewMockIAlchemy(ctrl)
	{
		countOfWebhook := lo.Reduce(lo.Values(config.GetStringMap("ALCHEMY_WEBHOOK_ID_MAP")), func(count int, v string, _ int) int {
			if len(v) == 0 {
				return count
			}

			return count + 1
		}, 0)

		m.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).
			Times(countOfWebhook * 3).
			DoAndReturn(func(ctx context.Context, webhookID string, addressesToAdd []string) error {
				for _, address := range addressesToAdd {
					s.Len(address, 42)
				}
				return nil
			})
	}
	alchemyapi.Set(m)

	// testdata
	phone := util.RandPhone()
	email := util.RandEmail()

	// phone and email which does not exist
	{
		addBodyList := []map[string]interface{}{
			{
				"phone_number": phone,
			},
			{
				"email": email,
			},
			{
				"phone_number": util.RandPhone(),
				"email":        util.RandEmail(),
			},
		}
		for _, addBody := range addBodyList {
			url := "/_v/users"
			r := gin.Default()
			r.POST(url, CreateUser)
			jsonStr, err := json.Marshal(addBody)
			s.NoError(err)
			req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
			s.NoError(err)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp struct {
				Code int `json:"code"`
				Data struct {
					UID string `json:"uid"`
				} `json:"data"`
			}
			err = json.Unmarshal(w.Body.Bytes(), &resp)
			s.NoError(err)
			s.Equal(http.StatusOK, w.Code)
			s.Equal(0, resp.Code)
			s.NotEmpty(resp.Data.UID)
		}
	}

	// already exists
	{
		addBodyList := []map[string]interface{}{
			{
				"phone_number": phone,
			},
			{
				"email": email,
			},
			{
				"phone_number": phone,
				"email":        util.RandEmail(),
			},
			{
				"phone_number": util.RandPhone(),
				"email":        email,
			},
		}
		exceptedCodeList := []int{
			1059,
			1058,
			1059,
			1058,
		}

		for i, addBody := range addBodyList {
			url := "/_v/users"
			r := gin.Default()
			r.POST(url, CreateUser)
			jsonStr, err := json.Marshal(addBody)
			s.NoError(err)
			req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
			s.NoError(err)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			var resp struct {
				Code int `json:"code"`
				Data struct {
					UID string `json:"uid"`
				} `json:"data"`
			}
			err = json.Unmarshal(w.Body.Bytes(), &resp)
			s.NoError(err)
			s.Equal(http.StatusConflict, w.Code)
			s.Equal(exceptedCodeList[i], resp.Code)
		}
	}
}

func setupKmsServer(t *testing.T) func() {
	// kms server
	rKMS := gin.Default()
	rKMS.POST("/v1/kms/generateMnemonicAddresses", kms.GenerateMnemonicAddresses)

	srv := &http.Server{
		Addr:    ":" + config.GetString("TEST_KMS_PORT"),
		Handler: rKMS,
	}
	go func() {
		err := srv.ListenAndServe()
		t.Logf("KMS server terminated with error: %v\n", err)
	}()

	time.Sleep(1 * time.Second) // wait for server to start
	return func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}

}
