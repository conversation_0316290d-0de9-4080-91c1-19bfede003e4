package api

import (
	"net/http"
	"net/http/httptest"
	"net/url"
	"strconv"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	oauthapi "github.com/kryptogo/kg-wallet-backend/api/oauth"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer/repo"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/assert"
)

func TestOAuthValidation(t *testing.T) {
	authorizeUrl := "/v1/oauth/authorize"
	orderUrl := "/v1/order/:order_id"
	server := gin.Default()
	server.GET(authorizeUrl, oauthapi.AuthorizeHandler)
	server.GET(orderUrl, auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeOrderRead}), GetOrderDetail)

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())
	customer.Init(repo.NewCustomerRepo())

	{
		// call oauth authorize
		scope := "user.info:read,user.info:write,wallet.defaultWallets:read,wallet.defaultWallets:write,wallet.allWallets:read,wallet.allWallets:write,vault:read,vault:write,asset:read"
		params := url.Values{
			"client_id":     {"20991a3ae83233d6de85d62906d71fd3"},
			"redirect_uri":  {"http://localhost:8040/v1/oauth/callback"},
			"response_type": {"token"},
			"scope":         {scope},
			"state":         {"xyz"},
		}
		urlWithParam := authorizeUrl + "?" + params.Encode()

		// test already login case
		req, _ := http.NewRequest("GET", urlWithParam, nil)
		req.Header.Set("KG-TOKEN", "KG-DEV:123456")
		req.Header.Set("KG-DEV-UID", "user1")
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusFound, w.Code)
		location := w.Header().Get("Location")
		assert.Contains(t, location, "http://localhost:8040/v1/oauth/callback")

		// call oauth token
		redirectUrl, err := url.Parse(location)
		assert.Nil(t, err)
		fragments, err := url.ParseQuery(redirectUrl.Fragment)
		assert.Nil(t, err)
		assert.Equal(t, "Bearer", fragments.Get("token_type"))
		expiresInSeconds, err := strconv.Atoi(fragments.Get("expires_in"))
		assert.Nil(t, err)
		assert.Equal(t, config.GetInt("ACCESS_TOKEN_EXP_MINUTES"), expiresInSeconds/60)
		assert.NotEmpty(t, fragments.Get("access_token"))
		assert.NotEmpty(t, fragments.Get("refresh_token"))
		assert.Equal(t, scope, fragments.Get("scope"))

		// validate scope, KG client should pass
		req, _ = http.NewRequest("GET", "/v1/order/123", nil)
		req.Header.Set("Authorization", "Bearer "+fragments.Get("access_token"))
		w = httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	}
	{
		// call oauth authorize
		scope := "user.info:read,user.info:write,wallet.defaultWallets:read,wallet.defaultWallets:write,wallet.allWallets:read,wallet.allWallets:write,vault:read,vault:write,asset:read"
		params := url.Values{
			"client_id":     {"41902cd3a636c7eb0af0fe9b"},
			"redirect_uri":  {"http://localhost:8040/v1/oauth/callback"},
			"response_type": {"token"},
			"scope":         {scope},
			"state":         {"xyz"},
		}
		urlWithParam := authorizeUrl + "?" + params.Encode()

		// test already login case
		req, _ := http.NewRequest("GET", urlWithParam, nil)
		req.Header.Set("KG-TOKEN", "KG-DEV:123456")
		req.Header.Set("KG-DEV-UID", "user1")
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusFound, w.Code)
		location := w.Header().Get("Location")
		assert.Contains(t, location, "http://localhost:8040/v1/oauth/callback")

		// call oauth token
		redirectUrl, err := url.Parse(location)
		assert.Nil(t, err)
		fragments, err := url.ParseQuery(redirectUrl.Fragment)
		assert.Nil(t, err)
		assert.Equal(t, "Bearer", fragments.Get("token_type"))
		expiresInSeconds, err := strconv.Atoi(fragments.Get("expires_in"))
		assert.Nil(t, err)
		assert.Equal(t, config.GetInt("ACCESS_TOKEN_EXP_MINUTES"), expiresInSeconds/60)
		assert.NotEmpty(t, fragments.Get("access_token"))
		assert.NotEmpty(t, fragments.Get("refresh_token"))
		assert.Equal(t, scope, fragments.Get("scope"))

		// validate scope, stickey(not KG) client should not pass
		req, _ = http.NewRequest("GET", "/v1/order/123", nil)
		req.Header.Set("Authorization", "Bearer "+fragments.Get("access_token"))
		w = httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusUnauthorized, w.Code)
	}
}
