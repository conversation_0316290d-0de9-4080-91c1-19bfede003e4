package universalswap

import (
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	universalswap "github.com/kryptogo/kg-wallet-backend/service/universal-swap"
)

// InitRequest represents the request payload for initializing a universal swap
type InitRequest struct {
	SourceTransactions []struct {
		ChainID    string `json:"chain_id" binding:"required"`
		From       string `json:"from" binding:"required"`
		To         string `json:"to" binding:"required"`
		TokenID    string `json:"token_id" binding:"required"`
		RawAmount  string `json:"raw_amount" binding:"required"`
		SignedTx   string `json:"signed_tx"`
		PermitData *struct {
			Owner    string `json:"owner" binding:"required"`
			Spender  string `json:"spender" binding:"required"`
			Value    string `json:"value" binding:"required"`
			Deadline string `json:"deadline" binding:"required"`
			R        string `json:"r" binding:"required"`
			S        string `json:"s" binding:"required"`
			V        string `json:"v" binding:"required"`
		} `json:"permit_data"`
	} `json:"source_transactions" binding:"required,min=1"`
	Destination struct {
		ChainID       string `json:"chain_id" binding:"required"`
		WalletAddress string `json:"wallet_address" binding:"required"`
		TokenID       string `json:"token_id" binding:"required"`
	} `json:"destination" binding:"required"`
}

// InitResponse represents the response for initializing a universal swap
type InitResponse struct {
	ID                int64 `json:"id"`
	EstimatedFinishAt int64 `json:"estimated_finish_at"`
}

// SwapStatusResponse represents the response for getting swap status
type SwapStatusResponse struct {
	ID                 int               `json:"id"`
	Status             string            `json:"status"`
	SourceTransactions []SourceTxStatus  `json:"source_transactions"`
	Destination        DestinationStatus `json:"destination"`
	EstimatedFinishAt  int64             `json:"estimated_finish_at"`
}

type SourceTxStatus struct {
	ChainID   string `json:"chain_id"`
	From      string `json:"from"`
	To        string `json:"to"`
	TokenID   string `json:"token_id"`
	Amount    string `json:"amount"`
	TxHash    string `json:"tx_hash"`
	Status    string `json:"status"`
	FundsSent bool   `json:"funds_sent"`
}

type DestinationStatus struct {
	ChainID        string `json:"chain_id"`
	WalletAddress  string `json:"wallet_address"`
	TokenID        string `json:"token_id"`
	ReceivedAmount string `json:"received_amount"`
}

// CollectRequest represents the request payload for collecting funds for an uid
type CollectRequest struct {
	ChainID       string `json:"chain_id" binding:"required"`
	WalletAddress string `json:"wallet_address" binding:"required"`
	TokenID       string `json:"token_id" binding:"required"`
}

// Init initializes a new universal swap request
func Init(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)

	var req InitRequest
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}
	// support from address as uid
	if uid == "" {
		uid = strings.ToLower(req.SourceTransactions[0].From)
	}

	// Convert request to domain model
	sourceTxs := make([]*domain.SourceTransaction, len(req.SourceTransactions))
	for i, tx := range req.SourceTransactions {
		chain := domain.IDToChain(tx.ChainID)
		if chain == nil {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid chain id")
			return
		}

		var permitData *domain.PermitData
		if tx.PermitData != nil {
			permitData = &domain.PermitData{
				Owner:    domain.NewEvmAddress(tx.PermitData.Owner),
				Spender:  domain.NewEvmAddress(tx.PermitData.Spender),
				Value:    tx.PermitData.Value,
				Deadline: tx.PermitData.Deadline,
				R:        tx.PermitData.R,
				S:        tx.PermitData.S,
				V:        tx.PermitData.V,
			}
		}

		sourceTxs[i] = &domain.SourceTransaction{
			Chain:      chain,
			From:       domain.NewAddressByChain(chain, tx.From),
			To:         domain.NewAddressByChain(chain, tx.To),
			TokenID:    tx.TokenID,
			RawAmount:  tx.RawAmount,
			SignedTx:   tx.SignedTx,
			PermitData: permitData,
			Status:     domain.SourceTxStatusInit,
		}
	}

	destChain := domain.IDToChain(req.Destination.ChainID)
	if destChain == nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid destination chain id")
		return
	}

	swap := &domain.UniversalSwap{
		UID:                uid,
		SourceTransactions: sourceTxs,
		Destination: &domain.Destination{
			Chain:         destChain,
			WalletAddress: domain.NewAddressByChain(destChain, req.Destination.WalletAddress),
			TokenID:       req.Destination.TokenID,
		},
	}

	// Create universal swap
	id, kgErr := universalswap.Create(ctx, swap)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, &InitResponse{
		ID:                int64(id),
		EstimatedFinishAt: time.Now().Add(2 * time.Minute).Unix(),
	})
}

// GetByID retrieves a universal swap by ID
func GetByID(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)
	if uid == "" {
		uid = strings.ToLower(c.Query("address"))
	}

	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}

	// Use the service layer to get the swap status
	resp, kgErr := universalswap.GetUniversalSwapStatus(ctx, id, uid)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, resp)
}

// GetDepositAddresses returns deposit addresses for universal swap
func GetDepositAddresses(c *gin.Context) {
	uid := c.GetString("uid")
	if uid == "" {
		uid = strings.ToLower(c.Query("address"))
	}
	if uid == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "UID is required")
		return
	}

	result, kgErr := universalswap.GetDepositAddresses(c.Request.Context(), uid)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, result)
}

func Collect(c *gin.Context) {
	var req CollectRequest
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}
	uid := c.GetString("uid")
	if uid == "" {
		uid = strings.ToLower(req.WalletAddress)
	}

	chain := domain.IDToChain(req.ChainID)
	destination := &domain.Destination{
		Chain:         chain,
		WalletAddress: domain.NewAddressByChain(chain, req.WalletAddress),
		TokenID:       req.TokenID,
	}
	kgErr := universalswap.Collect(c.Request.Context(), uid, destination)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}

// Handle processes a universal swap request (internal API)
func Handle(c *gin.Context) {
	ctx := c.Request.Context()
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}

	kgErr := universalswap.Handle(ctx, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}
