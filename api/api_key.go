package api

import (
	"crypto/rand"
	"encoding/base64"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type generateApiKeyParams struct {
	Name  string `json:"name" binding:"required"`
	Email string `json:"email" binding:"required"`
}

type generateApiKeyResp struct {
	Code int         `json:"code"`
	Data *apiKeyResp `json:"data"`
}

type apiKeyResp struct {
	Key string `json:"key"`
}

// GenerateApiKey generates an api key
func GenerateApiKey(ctx *gin.Context) {
	// bind params
	params := &generateApiKeyParams{}
	kgErr := util.ToGinContextExt(ctx).BindJson(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	apiKeyBytes := make([]byte, 32) // random 32 bytes
	_, err := rand.Read(apiKeyBytes)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.UnknownError, err.Error())
		return
	}

	creatorEmail := auth.GetGcpEmail(ctx)
	apiKey := base64.StdEncoding.EncodeToString(apiKeyBytes)
	apiKeyHash := util.HashAPIKey(apiKey)
	apiKeyModel := &model.APIKey{
		Name:      params.Name,
		Email:     params.Email,
		KeyHash:   apiKeyHash,
		Creator:   creatorEmail,
		CreatedAt: time.Now(),
	}
	err = rdb.CreateAPIKey(ctx.Request.Context(), apiKeyModel)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}
	ctx.JSON(http.StatusOK, &generateApiKeyResp{
		Code: 0,
		Data: &apiKeyResp{
			Key: apiKey,
		},
	})
}
