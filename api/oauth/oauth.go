//go:generate go-enum
package oauth

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	firebaseauth "firebase.google.com/go/v4/auth"
	"github.com/gin-gonic/gin"
	"github.com/go-oauth2/oauth2/v4"
	"github.com/go-oauth2/oauth2/v4/server"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	googleoauth "github.com/kryptogo/kg-wallet-backend/pkg/apis/google/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	customerService "github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	oauthservice "github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	authservice "github.com/kryptogo/kg-wallet-backend/pkg/service/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

const (
	stateUserNotExist = "nan"
)

var (
	accountCallbackURL = config.GetString("ACCOUNT_HOST") + "/auth/oauth-callback"
	studioCallbackURL  = config.GetString("STUDIO_HOST") + "/auth/login/oauth-callback"
	storeCallbackURL   = config.GetString("KGSTORE_BASE_URL")
	storeCallbackPath  = "/login/verify-account"
	pwaCallbackURL     = config.GetString("PWA_HOST") + "/third-party-login-callback"
)

// AuthorizeHandler checks if the user is logged in, then redirects to callback
func AuthorizeHandler(c *gin.Context) {
	oauth.RLockManager()
	defer oauth.RUnlockManager()
	err := oauth.GetServer().HandleAuthorizeRequest(c.Writer, c.Request)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		return
	}

	userID := c.Request.Header.Get("KG-UID")
	clientID := c.Request.Header.Get("KG-CLIENT-ID")
	if userID != "" && clientID != "" {
		err = rdb.SaveClientUser(c.Request.Context(), &model.ClientUser{
			UserUID:  userID,
			ClientID: clientID,
		})
		if err != nil {
			response.InternalServerErrorWithMsg(c, code.DBError, err.Error())
			return
		}
		kgErr := customerService.CreateCustomer(c.Request.Context(), userID, clientID)
		if kgErr != nil {
			response.KGError(c, kgErr)
			return
		}
	} else {
		kglog.ErrorWithData("missing uid or client id", map[string]interface{}{
			"uid":       userID,
			"client_id": clientID,
		})
	}
	c.Abort()
}

// TokenHandler retrieves access token
func TokenHandler(c *gin.Context) {
	oauth.RLockManager()
	defer oauth.RUnlockManager()
	err := oauth.GetServer().HandleTokenRequest(c.Writer, c.Request)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		return
	}
	c.Abort()
}

// CallbackHandler return ok
func CallbackHandler(c *gin.Context) {
	c.JSON(200, gin.H{
		"code": 0,
	})
}

// ClientID get client id from context, if not exist, use default client id
func ClientID(ctx *gin.Context) string {
	clientID := auth.GetClientID(ctx)
	if len(clientID) == 0 {
		clientID = ctx.GetHeader("X-Client-ID")
	}
	if len(clientID) == 0 {
		clientID = application.GetDefaultClientID(ctx.Request.Context())
	}
	return clientID
}

type googleLoginReq struct {
	State string `json:"state" binding:"required"`
}

// GoogleLoginResp .
type GoogleLoginResp struct {
	Code int             `json:"code"`
	Data googleLoginData `json:"data"`
}

type googleLoginData struct {
	LoginURL string `json:"login_url"`
}

// GoogleLogin .
func GoogleLogin(ctx *gin.Context) {
	params := &googleLoginReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	service := googleoauth.GetService()
	loginURL := service.GenerateOAuthURL(params.State)
	// uid will be empty if user non-exist
	uid := auth.GetUID(ctx)
	if len(uid) == 0 {
		uid = stateUserNotExist
	}
	// store state in redis
	stateKey := cache.ComposeGoogleOAuthStateCacheKey(params.State)
	err := cache.Set(ctx.Request.Context(), stateKey, uid, cache.OAuthStateCacheTTL)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx.Request.Context(), "set google oauth state failed", map[string]interface{}{
			"state": params.State,
			"uid":   uid,
		})
	}

	ctx.JSON(http.StatusOK, GoogleLoginResp{
		Code: 0,
		Data: googleLoginData{
			LoginURL: loginURL,
		},
	})
}

type googleLoginCallbackReq struct {
	Code  string `form:"code"`
	State string `form:"state"`
	Error string `form:"error"`
}

func (req *googleLoginCallbackReq) Valid() (errCode int, err error) {
	if len(req.Error) > 0 {
		errCode = code.GoogleLoginFailed
		err = errors.New(req.Error)
		return
	}
	if len(req.Code) == 0 {
		errCode = code.ParamIncorrect
		err = errors.New("code is empty")
		return
	}
	if len(req.State) == 0 {
		errCode = code.ParamIncorrect
		err = errors.New("state is empty")
		return
	}
	return
}

// GoogleLoginCallback .
func GoogleLoginCallback(ctx *gin.Context) {
	params := &googleLoginCallbackReq{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	var errCode int
	var err error
	var kgToken string
	var uid string
	var payload *statePayload

	defer func() {
		callbackURL := getCallbackURL(ctx.Request.Context(), payload)
		query := url.Values{}
		if err != nil {
			query.Set("error", err.Error())
			query.Set("error_code", strconv.Itoa(errCode))
		} else {
			query.Set("kg_token", kgToken)
		}
		ctx.Redirect(http.StatusFound, callbackURL+"?"+query.Encode())
	}()

	errCode, err = params.Valid()
	if err != nil {
		kglog.ErrorWithDataCtx(ctx.Request.Context(), "params.Valid, ERROR: "+err.Error(), map[string]interface{}{
			"params": params,
		})
		return
	}

	// check state in redis
	uid, errCode, err = checkState(params.State)
	if err != nil {
		kglog.WarningWithDataCtx(ctx.Request.Context(), "checkState, ERROR: "+err.Error(), map[string]interface{}{
			"state": params.State,
		})
		return
	}

	service := googleoauth.GetService()

	// exchange access token
	resp, restyResp, err := service.ExchangeAccessToken(params.Code)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx.Request.Context(), "Authorize, ERROR: "+err.Error(), map[string]interface{}{
			"code":      params.Code,
			"resp":      resp,
			"err":       err.Error(),
			"restyResp": restyResp.String(),
		})
		errCode = code.GoogleLoginFailed
		err = errors.New("exchange access token failed")
		return
	}

	// get google user info if needed
	userInfo, restyResp, err := service.GetUserInfo(resp.AccessToken)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx.Request.Context(), "GetUserInfo, ERROR: "+err.Error(), map[string]interface{}{
			"access_token": resp.AccessToken,
			"err":          err.Error(),
			"restyResp":    restyResp.String(),
		})
		errCode = code.GoogleLoginFailed
		err = errors.New("get user info failed")
		return
	}

	// get clientID by state
	payload, err = parseState(params.State)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx.Request.Context(), "parseState, ERROR: "+err.Error(), map[string]interface{}{
			"state": params.State,
		})
		errCode = code.ParamIncorrect
		return
	}

	// link google account if needed
	errCode, err = linkGoogleAccount(ctx.Request.Context(), uid, userInfo.Email)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx.Request.Context(), "handleUserUpsert, ERROR: "+err.Error(), map[string]interface{}{
			"uid":   uid,
			"email": userInfo.Email,
		})
		return
	}

	// create kg user if not exist, link google account if exist, and issue tokens
	loginProvider := authservice.NewLoginProvider(&authservice.LoginReq{
		GoogleEmail: userInfo.Email,
		ClientID:    payload.ClientID,
	})
	_, _, kgToken, uid, kgErr = loginProvider.IssueTokens(ctx.Request.Context())
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx.Request.Context(), "IssueTokens, ERROR: "+kgErr.String(), map[string]interface{}{
			"google_email": userInfo.Email,
			"client_id":    payload.ClientID,
		})
		errCode = code.GoogleLoginFailed
		return
	}

	// if exist, update google user info
	errCode, err = saveUserGoogleInfo(ctx.Request.Context(),
		uid,
		payload.ClientID,
		resp.AccessToken,
		resp.RefreshToken,
	)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx.Request.Context(), "saveUserGoogleInfo, ERROR: "+err.Error(), map[string]interface{}{
			"uid":       uid,
			"client_id": payload.ClientID,
		})
		return
	}

	// FIXME: integrate with onboarding process (login api)
	// create sendbird user if not exist
	createSendbirdUser(ctx.Request.Context(), uid, payload.ClientID)
}

// StateService .
// ENUM(studio, app, websdk, store, pwa)
type StateService string

type statePayload struct {
	ClientID string       `json:"client_id"` // for app, websdk, pwa. to fetch user's locale.
	Nonce    string       `json:"nonce"`
	Service  StateService `json:"service"`
	Org      string       `json:"org"` // for store, just to composite the redirect url
}

func parseState(state string) (*statePayload, error) {
	payload := &statePayload{}

	// First, attempt to Base64-decode
	decodedBytes, err := base64.StdEncoding.DecodeString(state)
	if err == nil {
		// Attempt to parse as JSON
		if jsonErr := json.Unmarshal(decodedBytes, payload); jsonErr == nil {
			return payload, nil
		}
	}

	// Fallback to parsing as a query string
	// format: client_id:xxx
	stateArr := strings.Split(state, ":")
	if len(stateArr) != 2 {
		return nil, fmt.Errorf("invalid state")
	}
	payload.ClientID = stateArr[0]
	payload.Nonce = stateArr[1]
	return payload, nil
}

func getStoreCallbackURL(ctx context.Context, marketCode string) string {
	callbackURL, err := url.JoinPath(storeCallbackURL, marketCode, storeCallbackPath)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "getStoreCallbackURL, ERROR: "+err.Error(), map[string]interface{}{
			"base":        storeCallbackPath,
			"market_code": marketCode,
			"path":        storeCallbackPath,
		})
	}
	return callbackURL
}

func getCallbackURL(ctx context.Context, payload *statePayload) string {
	var callbackURL string
	if payload != nil {

		switch payload.Service {
		case StateServiceStudio:
			callbackURL = studioCallbackURL
		case StateServiceApp, StateServiceWebsdk:
			callbackURL = accountCallbackURL
		case StateServicePwa:
			callbackURL = pwaCallbackURL
		case StateServiceStore:
			callbackURL = getStoreCallbackURL(ctx, payload.Org)
		default:
			callbackURL = accountCallbackURL
		}
	} else {
		callbackURL = accountCallbackURL
	}
	return callbackURL
}

func checkState(state string) (uid string, errCode int, err error) {
	stateCacheKey := cache.ComposeGoogleOAuthStateCacheKey(state)
	uid = cache.String(stateCacheKey)
	if len(uid) == 0 {
		errCode = code.ParamIncorrect
		err = errors.New("state is not valid")
		return
	}
	cache.Del(stateCacheKey)
	return
}

func linkGoogleAccount(ctx context.Context, probableUID, googleEmail string) (errCode int, err error) {
	shouldLinkedUID, errCode, err := shouldCreateUser(ctx, probableUID, googleEmail)
	if err != nil {
		kglog.DebugWithDataCtx(ctx, "shouldCreateUser, ERROR: "+err.Error(), map[string]interface{}{
			"probable_uid": probableUID,
			"google_email": googleEmail,
		})
		return errCode, err
	}

	kglog.DebugWithDataCtx(ctx, "shouldCreateUser, SUCCESS", map[string]interface{}{
		"probable_uid":     probableUID,
		"google_email":     googleEmail,
		"should_linked_id": shouldLinkedUID,
	})

	var firebaseUser *firebaseauth.UserRecord
	// If shouldLinkedUID is not empty, link the Google account to this user
	if shouldLinkedUID != "" {
		// get firebase user
		firebaseUser, err = firebase.GetUserByUID(ctx, shouldLinkedUID)
		if err != nil {
			return code.FirebaseFailed, err
		}

		// update email if needed
		if len(firebaseUser.Email) == 0 {
			firebaseUser, err = firebase.UpdateUserEmail(ctx, firebaseUser.UID, googleEmail)
			if err != nil {
				errCode = code.FirebaseFailed
				return
			}
		}

		// update google account if needed
		if firebaseUser.Email != googleEmail {
			kglog.DebugWithDataCtx(ctx, "firebaseUser.Email != googleEmail", map[string]interface{}{
				"firebase_user_email": firebaseUser.Email,
				"google_email":        googleEmail,
			})
			errCode = code.PrimaryEmailNotTheSame
			err = errors.New("primary email is not the same")
			return
		}

		// update google account if needed
		googleLinkedEmail, hasLinkedGoogle := firebase.HasLinkedGoogle(firebaseUser)

		kglog.DebugWithDataCtx(ctx, "HasLinkedGoogle", map[string]interface{}{
			"google_linked_email": googleLinkedEmail,
			"has_linked_google":   hasLinkedGoogle,
		})

		if !hasLinkedGoogle || googleLinkedEmail != googleEmail {
			_, err = firebase.UpdateUserGoogleAccount(ctx, firebaseUser.UID, googleEmail)
			if err != nil {
				errCode = code.FirebaseFailed
				return
			}
		}

		// update user in firestore
		user, _ := rdb.GormRepo().GetUser(ctx, firebaseUser.UID, "", false, nil)
		if user != nil {
			// update user if user exists
			err = rdb.GormRepo().UpdateUserEmail(ctx, firebaseUser.UID, googleEmail)
			if err != nil {
				errCode = code.DBError
				return
			}
		}

		return 0, nil
	}

	return 0, nil
}

func shouldCreateUser(ctx context.Context, uid string, email string) (string, int, error) {
	emailUsed, existingUID, errCode, err := checkIfEmailIsUsed(ctx, email)
	if err != nil {
		return "", errCode, err
	}

	if uid != stateUserNotExist {
		if emailUsed {
			// Email already exists, compare uid and existingUID
			// link Google account to this user if uid is the same
			if uid == existingUID {
				return uid, 0, nil
			}
			// uid is not the same, return error
			return "", code.EmailAlreadyExists, errors.New("uid is not the same")
		}
		// UID is provided and email does not exist, update this user
		return uid, 0, nil
	}

	if emailUsed {
		// UID is not provided and email exists, link Google account to this user
		return existingUID, 0, nil
	}
	// Email does not exist, create a new user
	return "", 0, nil
}

func checkIfEmailIsUsed(ctx context.Context, email string) (emailUsed bool, existingUID string, errCode int, err error) {
	var emailUser *firebaseauth.UserRecord
	emailUser, err = firebase.GetUserByEmail(ctx, email)
	if err != nil {
		if !firebase.IsUserNotFound(err) {
			errCode = code.FirebaseFailed
			return
		}
		err = nil
	}
	emailUsed = emailUser != nil
	if emailUsed {
		existingUID = emailUser.UID
	}
	return
}

func saveUserGoogleInfo(ctx context.Context, uid, clientID, accessToken, refreshToken string) (errCode int, err error) {
	user, _ := rdb.GormRepo().GetUser(ctx, uid, clientID, false, nil)
	if user != nil {
		err = rdb.GormRepo().SaveGoogleInfo(ctx, uid, accessToken, refreshToken)
		if err != nil {
			errCode = code.DBError
			return
		}
	} else {
		errCode = code.UserNotFound
		err = code.ErrUserNotFound
		return
	}
	return
}

func createSendbirdUser(ctx context.Context, uid, clientID string) {
	applicationID, apiTokenName, _ := application.GetSendbirdAppIDAndAPIToken(ctx, clientID)
	if applicationID != "" && apiTokenName != "" {
		// create sendbird user
		user, _ := rdb.GormRepo().GetUser(ctx, uid, "", false, &domain.UserPreloads{
			WithAvatar: true,
		})
		sendbirdClient := sendbirdapi.NewClient(applicationID, apiTokenName)
		sendbirdService := chatroom.GetService(sendbirdClient)
		_, _, err := sendbirdService.CreateAUser(ctx, user)
		if err != nil {
			kglog.InfoWithDataCtx(ctx, "CreateAUser, ERROR: "+err.Error(), map[string]interface{}{
				"uid": uid,
			})
			// don't return error, because it may be existing user login
		}
	}
}

type exchangeTokenReq struct {
	Scope    string `json:"scope" binding:"required"`
	ClientID string `json:"client_id" binding:"required"`
}

type exchangeTokenData struct {
	OAuthToken   string `json:"oauth_token"`
	RefreshToken string `json:"refresh_token"`
}

// ExchangeToken .
func ExchangeToken(ctx *gin.Context) {
	params := &exchangeTokenReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	uid := auth.GetUID(ctx)
	if len(uid) == 0 {
		response.NotFoundWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	// validate scope
	allowed, err := oauthservice.ValidateClientScope(params.Scope, params.ClientID)
	if err != nil {
		kglog.InfofCtx(ctx.Request.Context(), "validate client scope error: %v", err)
		response.ForbiddenErrorWithMsg(ctx, code.ScopeNotAllowed, err.Error())
		return
	}

	if !allowed {
		kglog.InfofCtx(ctx.Request.Context(), "client scope not allowed: %s", params.Scope)
		response.ForbiddenErrorWithMsg(ctx, code.ScopeNotAllowed, "client scope not allowed")
		return
	}

	// create tokens
	// reuse the issue token logic in oauth server
	oauth.RLockManager()
	defer oauth.RUnlockManager()
	tokenInfo, err := oauth.GetServer().GetAuthorizeToken(ctx.Request.Context(), &server.AuthorizeRequest{
		ResponseType: oauth2.Token,
		ClientID:     params.ClientID,
		Scope:        params.Scope,
		UserID:       uid,
	})
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.InternalError, err.Error())
		return
	}

	resp := &exchangeTokenData{
		OAuthToken:   tokenInfo.GetAccess(),
		RefreshToken: tokenInfo.GetRefresh(),
	}

	response.OK(ctx, resp)
}
