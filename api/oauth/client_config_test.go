package oauth

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	oauthservice "github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/assert"
)

const putConfigsUrl = "/_v/oauth/configs"
const getConfigsUrl = "/oauth/configs"

func TestUpsertOAuthClientConfigsAndGet(t *testing.T) {
	r := setup(t)
	clientID := "test-client-id"
	domainName := "http://localhost:8765"

	// put config
	reqBody := map[string]interface{}{
		"id":               clientID,
		"domain":           domainName,
		"is_privileged":    false,
		"name":             "Testing",
		"organization_id":  1,
		"application_type": "market",
		"secret":           "test-secret",
		"login_methods":    []string{"email", "phone", "google"},
	}
	reqBodyBytes, _ := json.Marshal(reqBody)
	reqBodyReader := bytes.NewReader(reqBodyBytes)
	req, err := http.NewRequest("PUT", putConfigsUrl, reqBodyReader)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	var createRes struct {
		Code int `json:"code"`
		Data struct {
			ClientID string `json:"client_id"`
		} `json:"data"`
	}
	responseStr, _ := io.ReadAll(w.Body)
	t.Logf("createResponse: %s\n", responseStr)
	assert.Nil(t, json.Unmarshal(responseStr, &createRes))
	assert.Equal(t, 0, createRes.Code)

	// get config
	reqUrl := fmt.Sprintf("%s?id=%s", getConfigsUrl, clientID)
	req, err = http.NewRequest("GET", reqUrl, nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	type getResType struct {
		Code int `json:"code"`
		Data struct {
			Name         string   `json:"name"`
			ClientID     string   `json:"client_id"`
			Domain       string   `json:"domain"`
			LoginMethods []string `json:"login_methods"`
		} `json:"data"`
	}
	var getRes getResType
	responseStr, _ = io.ReadAll(w.Body)
	t.Logf("getRes: %s\n", responseStr)
	assert.Nil(t, json.Unmarshal(responseStr, &getRes))
	assert.Equal(t, 0, getRes.Code)
	assert.Equal(t, clientID, getRes.Data.ClientID)
	assert.Equal(t, domainName, getRes.Data.Domain)
	assert.Equal(t, "Testing", getRes.Data.Name)
	assert.ElementsMatch(t, []string{"email", "google", "phone"}, getRes.Data.LoginMethods)

	// update config
	reqBody = map[string]interface{}{
		"id":               clientID,
		"domain":           domainName,
		"is_privileged":    false,
		"name":             "Testing2",
		"organization_id":  1,
		"application_type": "market",
		"secret":           "test-secret-2",
		"login_methods":    []string{"email", "google"},
	}
	reqBodyBytes, _ = json.Marshal(reqBody)
	reqBodyReader = bytes.NewReader(reqBodyBytes)
	req, err = http.NewRequest("PUT", putConfigsUrl, reqBodyReader)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	t.Logf("createResponse: %s\n", responseStr)
	assert.Nil(t, json.Unmarshal(responseStr, &createRes))
	assert.Equal(t, 0, createRes.Code)

	// get config again
	req, err = http.NewRequest("GET", reqUrl, nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	t.Logf("getRes: %s\n", responseStr)
	assert.Nil(t, json.Unmarshal(responseStr, &getRes))
	assert.Equal(t, 0, getRes.Code)
	assert.Equal(t, clientID, getRes.Data.ClientID)
	assert.Equal(t, domainName, getRes.Data.Domain)
	assert.Equal(t, "Testing2", getRes.Data.Name)
	assert.ElementsMatch(t, []string{"email", "google"}, getRes.Data.LoginMethods)
}

func setup(t *testing.T) *gin.Engine {
	rdb.Reset()
	assert.Nil(t, dbtest.CreateStudioDefault(rdb.Get()))
	application.Init(rdb.GormRepo())
	oauthservice.Init(rdb.GormRepo())

	r := gin.Default()
	r.PUT(putConfigsUrl, UpsertOAuthClientConfigs)
	r.GET(getConfigsUrl, GetOAuthClientConfig)
	return r
}
