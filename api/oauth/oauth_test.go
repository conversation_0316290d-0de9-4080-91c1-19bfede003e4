package oauth

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"

	firebaseauth "firebase.google.com/go/v4/auth"

	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/suite"

	googleoauth "github.com/kryptogo/kg-wallet-backend/pkg/apis/google/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	cRepo "github.com/kryptogo/kg-wallet-backend/pkg/service/customer/repo"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

type tokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	Scope        string `json:"scope"`
}

func TestAuthorizeAndTokenHandler(t *testing.T) {
	authorizeUrl := "/v1/oauth/authorize"
	tokenUrl := "/v1/oauth/token"
	server := gin.Default()
	server.GET(authorizeUrl, AuthorizeHandler)
	server.POST(tokenUrl, TokenHandler)

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	customer.Init(cRepo.NewCustomerRepo())

	// call oauth authorize
	scope := "user.info:read,user.info:write,wallet.defaultWallets:read,wallet.defaultWallets:write,wallet.allWallets:read,wallet.allWallets:write,vault:read,vault:write,asset:read"
	params := url.Values{
		"client_id":     {"41902cd3a636c7eb0af0fe9b"},
		"redirect_uri":  {"http://localhost:8040/v1/oauth/callback"},
		"response_type": {"code"},
		"scope":         {scope},
		"state":         {"xyz"},
	}
	urlWithParam := authorizeUrl + "?" + params.Encode()

	// test not login case
	req, _ := http.NewRequest("GET", urlWithParam, nil)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusFound, w.Code)
	assert.Equal(t, config.GetString("DYNAMIC_LINK_HOST")+"/login", w.Header().Get("Location"))

	// test already login case
	req, _ = http.NewRequest("GET", urlWithParam, nil)
	req.Header.Set("KG-WALLET-TOKEN", "KG-DEV:123456")
	req.Header.Set("KG-DEV-UID", "user1")
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusFound, w.Code)
	location := w.Header().Get("Location")
	assert.Contains(t, location, "http://localhost:8040/v1/oauth/callback")
	assert.Contains(t, location, "code=")
	assert.Contains(t, location, "state=xyz")

	// call oauth token
	redirectUrl, err := url.Parse(location)
	assert.Nil(t, err)
	code := redirectUrl.Query().Get("code")
	params = url.Values{
		"client_id":     {"41902cd3a636c7eb0af0fe9b"},
		"client_secret": {"stickey-secret-456"},
		"redirect_uri":  {"http://localhost:8040/v1/oauth/callback"},
		"grant_type":    {"authorization_code"},
		"code":          {code},
	}
	urlWithParam = tokenUrl + "?" + params.Encode()
	req, _ = http.NewRequest("POST", urlWithParam, nil)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// parse response
	var response tokenResponse
	responseStr, _ := io.ReadAll(w.Body)
	err = json.Unmarshal(responseStr, &response)
	assert.Nil(t, err)
	assert.Equal(t, "Bearer", response.TokenType)
	assert.Equal(t, config.GetInt("ACCESS_TOKEN_EXP_MINUTES"), response.ExpiresIn/60)
	assert.NotEmpty(t, response.AccessToken)
	assert.NotEmpty(t, response.RefreshToken)
	assert.Equal(t, scope, response.Scope)

	// refresh token
	params = url.Values{
		"client_id":     {"41902cd3a636c7eb0af0fe9b"},
		"client_secret": {"stickey-secret-456"},
		"redirect_uri":  {"http://localhost:8040/v1/oauth/callback"},
		"grant_type":    {"refresh_token"},
		"refresh_token": {response.RefreshToken},
	}
	urlWithParam = tokenUrl + "?" + params.Encode()
	req, _ = http.NewRequest("POST", urlWithParam, nil)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	err = json.Unmarshal(responseStr, &response)
	assert.Nil(t, err)
	assert.Equal(t, "Bearer", response.TokenType)
	assert.Equal(t, config.GetInt("ACCESS_TOKEN_EXP_MINUTES"), response.ExpiresIn/60)
	assert.NotEmpty(t, response.AccessToken)
	assert.NotEmpty(t, response.RefreshToken)
	assert.Equal(t, scope, response.Scope)
}

func TestImplicitAuthorize(t *testing.T) {
	authorizeUrl := "/v1/oauth/authorize"
	server := gin.Default()
	server.GET(authorizeUrl, AuthorizeHandler)

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	customer.Init(cRepo.NewCustomerRepo())

	// call oauth authorize
	scope := "user.info:read,user.info:write,wallet.defaultWallets:read,wallet.defaultWallets:write,wallet.allWallets:read,wallet.allWallets:write,vault:read,vault:write,asset:read"
	params := url.Values{
		"client_id":     {"41902cd3a636c7eb0af0fe9b"},
		"redirect_uri":  {"http://localhost:8040/v1/oauth/callback"},
		"response_type": {"token"},
		"scope":         {scope},
		"state":         {"xyz"},
	}
	urlWithParam := authorizeUrl + "?" + params.Encode()

	// test not login case
	req, _ := http.NewRequest("GET", urlWithParam, nil)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusFound, w.Code)
	assert.Equal(t, config.GetString("DYNAMIC_LINK_HOST")+"/login", w.Header().Get("Location"))

	// test already login case
	req, _ = http.NewRequest("GET", urlWithParam, nil)
	req.Header.Set("KG-TOKEN", "KG-DEV:123456")
	req.Header.Set("KG-DEV-UID", "user1")
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusFound, w.Code)
	location := w.Header().Get("Location")
	assert.Contains(t, location, "http://localhost:8040/v1/oauth/callback")

	// call oauth token
	redirectUrl, err := url.Parse(location)
	assert.Nil(t, err)
	fragments, err := url.ParseQuery(redirectUrl.Fragment)
	assert.Nil(t, err)
	assert.Equal(t, "Bearer", fragments.Get("token_type"))
	expiresInSeconds, err := strconv.Atoi(fragments.Get("expires_in"))
	assert.Nil(t, err)
	assert.Equal(t, config.GetInt("ACCESS_TOKEN_EXP_MINUTES"), expiresInSeconds/60)
	assert.NotEmpty(t, fragments.Get("access_token"))
	assert.NotEmpty(t, fragments.Get("refresh_token"))
	assert.Equal(t, scope, fragments.Get("scope"))
}

type testRefreshResponse struct {
	Code int             `json:"code"`
	Data testRefreshData `json:"data"`
}

type testRefreshData struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type oauthTokenResponse struct {
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	Scope        string `json:"scope"`
}

func TestRefreshToken(t *testing.T) {
	rdb.Reset()
	users, userIDs := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)
	uid := userIDs[0]

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	customer.Init(cRepo.NewCustomerRepo())

	authorizeUrl := "/v1/oauth/authorize"
	refreshTokenUrl := "/v1/oauth/refresh"
	tokenUrl := "/v1/oauth/token"
	server := gin.Default()
	server.GET(authorizeUrl, AuthorizeHandler)
	server.POST(refreshTokenUrl, RefreshToken)
	server.POST(tokenUrl, TokenHandler)

	// call oauth authorize
	scope := "user.info:read,user.info:write,wallet.defaultWallets:read,wallet.defaultWallets:write,wallet.allWallets:read,wallet.allWallets:write,vault:read,vault:write,asset:read"
	params := url.Values{
		"client_id":     {"41902cd3a636c7eb0af0fe9b"},
		"redirect_uri":  {"http://localhost:8040/v1/oauth/callback"},
		"response_type": {"token"},
		"scope":         {scope},
		"state":         {"xyz"},
	}
	urlWithParam := authorizeUrl + "?" + params.Encode()

	// test not login case
	req, _ := http.NewRequest("GET", urlWithParam, nil)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusFound, w.Code)
	assert.Equal(t, config.GetString("DYNAMIC_LINK_HOST")+"/login", w.Header().Get("Location"))

	// test already login case
	req, _ = http.NewRequest("GET", urlWithParam, nil)
	req.Header.Set("KG-TOKEN", "KG-DEV:123456")
	req.Header.Set("KG-DEV-UID", uid)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusFound, w.Code)
	location := w.Header().Get("Location")
	assert.Contains(t, location, "http://localhost:8040/v1/oauth/callback")

	// call oauth token
	redirectUrl, err := url.Parse(location)
	assert.Nil(t, err)
	fragments, err := url.ParseQuery(redirectUrl.Fragment)
	accessToken := fragments.Get("access_token")
	assert.Nil(t, err)
	assert.Equal(t, "Bearer", fragments.Get("token_type"))
	expiresInSeconds, err := strconv.Atoi(fragments.Get("expires_in"))
	assert.Nil(t, err)
	assert.Equal(t, config.GetInt("ACCESS_TOKEN_EXP_MINUTES"), expiresInSeconds/60)
	assert.NotEmpty(t, accessToken)
	assert.NotEmpty(t, fragments.Get("refresh_token"))
	assert.Equal(t, scope, fragments.Get("scope"))
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeOAuth)
	_, code, err := strategy.Parse(accessToken)
	assert.Equal(t, 0, code)
	assert.Nil(t, err)

	time.Sleep(5 * time.Second)
	addBody := map[string]interface{}{
		"refresh_token": accessToken,
	}
	jsonStr, err := json.Marshal(addBody)
	assert.Nil(t, err)
	req, err = http.NewRequest("POST", refreshTokenUrl, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	// assert response
	assert.Equal(t, http.StatusOK, w.Code)
	var response testRefreshResponse
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.NotEmpty(t, response.Data.AccessToken)
	assert.NotEmpty(t, response.Data.RefreshToken)
	_, code, err = strategy.Parse(response.Data.AccessToken)
	assert.Equal(t, 0, code)
	assert.Nil(t, err)

	// refresh token using the new refresh token and /v1/oauth/token
	params = url.Values{
		"client_id":     {"41902cd3a636c7eb0af0fe9b"},
		"grant_type":    {"refresh_token"},
		"refresh_token": {response.Data.RefreshToken},
	}
	urlWithParam = tokenUrl + "?" + params.Encode()
	req, err = http.NewRequest("POST", urlWithParam, nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	var tokenResponse oauthTokenResponse
	responseStr, _ = io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &tokenResponse))
	assert.Equal(t, "Bearer", tokenResponse.TokenType)
	assert.Equal(t, config.GetInt("ACCESS_TOKEN_EXP_MINUTES"), tokenResponse.ExpiresIn/60)
	assert.NotEmpty(t, tokenResponse.AccessToken)
	assert.NotEmpty(t, tokenResponse.RefreshToken)
	assert.Equal(t, scope, tokenResponse.Scope)
	_, code, err = strategy.Parse(tokenResponse.AccessToken)
	assert.Equal(t, 0, code)
	assert.Nil(t, err)
}

type googleLoginSuite struct {
	suite.Suite
	UID            string
	DifferentUID   string
	UIDWithNoEmail string
}

func TestGoogleLoginSuite(t *testing.T) {
	suite.Run(t, new(googleLoginSuite))
}

func (s *googleLoginSuite) SetupSuite() {
	// set up rdb
	rdb.Reset()
	assert.Nil(s.T(), rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(s.T(), rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(s.T(), rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)

	// Link firebase auth to provider is not supported in firebase emulator
	// so we need to mock it
	// ref: https://github.com/firebase/firebase-tools/issues/4170
	firebase.UpdateUserGoogleAccount = mockUpdateUserGoogleAccount
}

func (s *googleLoginSuite) TestGoogleLoginCases() {
	s.Run("non-existing user google login", func() {
		suite.Run(s.T(), new(nonExistingUserGoogleLoginSuite))
	})
	s.Run("existing user google login", func() {
		suite.Run(s.T(), new(existingUserGoogleLoginSuite))
	})
	s.Run("existing user with no email google login", func() {
		suite.Run(s.T(), new(existingUserNoEmailGoogleLoginSuite))
	})
}

type nonExistingUserGoogleLoginSuite struct {
	suite.Suite
	Router *gin.Engine
	UID    string
	State  string
	Code   string
	Email  string
}

func (s *nonExistingUserGoogleLoginSuite) SetupSuite() {
	// setup firebase store data for users
	rdb.Reset()
	users, userIDs := dbtest.Users()
	user.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(s.T(), rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(s.T(), rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(s.T(), rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	application.Init(rdb.GormRepo())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	assert.Nil(s.T(), err)
	uid := userIDs[0]
	s.UID = uid

	// setup router
	router := gin.Default()
	googleLoginUrl := "/v1/login/google"
	googleCallbackUrl := "/v1/oauth/google_callback"
	callbackUrl := "/v1/oauth/callback"
	router.POST(googleLoginUrl, GoogleLogin)
	router.GET(googleCallbackUrl, GoogleLoginCallback)
	router.GET(callbackUrl, CallbackHandler)
	s.Router = router
}

func (s *nonExistingUserGoogleLoginSuite) SetupTest() {
	clientID := application.GetDefaultClientID(context.Background())
	s.State = generateState(clientID)
	s.Code = util.RandString(6)
	s.Email = util.RandEmail()
}

func (s *nonExistingUserGoogleLoginSuite) TestNonExistingUserGoogleLogin() {
	// default test order is alphabetical, so we need to set it manually
	s.nonExistingUserGoogleLogin()
	s.userTryToLoginWithSameGoogleAccount()
}

func (s *nonExistingUserGoogleLoginSuite) nonExistingUserGoogleLogin() {
	// setup google oauth service every time
	// cuz we will mock part of the methods in tests
	// so we need to reset it every time
	googleoauth.GetService = googleoauth.NewGoogleOAuthService

	body := map[string]interface{}{
		"state": s.State,
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(s.T(), err)

	req, err := http.NewRequest("POST", "/v1/login/google", strings.NewReader(string(bodyStr)))
	assert.Nil(s.T(), err)
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusOK, w.Code)
	var response GoogleLoginResp
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(s.T(), json.Unmarshal(responseStr, &response))
	assert.Equal(s.T(), 0, response.Code)
	expectURL := generateExpectLoginURL(s.State)
	assert.Equal(s.T(), expectURL, response.Data.LoginURL)

	// mock google oauth service and sendbird service
	ctrl := gomock.NewController(s.T())
	fn := mockGoogleOAuthService(ctrl, s.Code, s.Email)
	googleoauth.GetService = fn
	createUserFn := mockCreateAUser(ctrl, nil)
	chatroom.GetService = createUserFn

	// call google callback
	params := url.Values{}
	params.Set("code", s.Code)
	params.Set("state", s.State)
	urlWithParam := "/v1/oauth/google_callback?" + params.Encode()
	req, err = http.NewRequest("GET", urlWithParam, nil)
	assert.Nil(s.T(), err)
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusFound, w.Code)
	location := w.Header().Get("Location")
	params = parseLocation(s.T(), location)
	assert.NotEmpty(s.T(), params.Get("kg_token"))
	assert.Empty(s.T(), params.Get("error"))
	assert.Empty(s.T(), params.Get("error_code"))

	// check user data
	firebaseUser, err := firebase.GetUserByEmail(context.Background(), s.Email)
	assert.Nil(s.T(), err)
	// ignore google provider check, because firebase emulator doesn't support it
	user, _ := rdb.GormRepo().GetUser(context.Background(), firebaseUser.UID, "", false, nil)
	assert.NotNil(s.T(), user)
	assert.NotNil(s.T(), user.Email)
	assert.Equal(s.T(), s.Email, *user.Email)
}

func (s *nonExistingUserGoogleLoginSuite) userTryToLoginWithSameGoogleAccount() {
	// setup google oauth service every time
	// cuz we will mock part of the methods in tests
	// so we need to reset it every time
	googleoauth.GetService = googleoauth.NewGoogleOAuthService

	body := map[string]interface{}{
		"state": s.State,
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(s.T(), err)

	req, err := http.NewRequest("POST", "/v1/login/google", strings.NewReader(string(bodyStr)))
	assert.Nil(s.T(), err)
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusOK, w.Code)
	var response GoogleLoginResp
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(s.T(), json.Unmarshal(responseStr, &response))
	assert.Equal(s.T(), 0, response.Code)
	expectURL := generateExpectLoginURL(s.State)
	assert.Equal(s.T(), expectURL, response.Data.LoginURL)

	// mock google oauth service and sendbird service
	ctrl := gomock.NewController(s.T())
	fn := mockGoogleOAuthService(ctrl, s.Code, s.Email)
	googleoauth.GetService = fn
	createUserFn := mockCreateAUser(ctrl, nil)
	chatroom.GetService = createUserFn

	// call google callback
	params := url.Values{}
	params.Set("code", s.Code)
	params.Set("state", s.State)
	urlWithParam := "/v1/oauth/google_callback?" + params.Encode()
	req, err = http.NewRequest("GET", urlWithParam, nil)
	assert.Nil(s.T(), err)
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusFound, w.Code)
	location := w.Header().Get("Location")
	params = parseLocation(s.T(), location)
	assert.NotEmpty(s.T(), params.Get("kg_token"))
	assert.Empty(s.T(), params.Get("error"))
	assert.Empty(s.T(), params.Get("error_code"))

	// check user data
	firebaseUser, err := firebase.GetUserByEmail(context.Background(), s.Email)
	assert.Nil(s.T(), err)
	// ignore google provider check, because firebase emulator doesn't support it
	user, _ := rdb.GormRepo().GetUser(context.Background(), firebaseUser.UID, "", false, nil)
	assert.NotNil(s.T(), user)
	assert.NotNil(s.T(), user.Email)
	assert.Equal(s.T(), s.Email, *user.Email)
}

type existingUserGoogleLoginSuite struct {
	suite.Suite
	Router       *gin.Engine
	UID          string
	DifferentUID string
	State        string
	Code         string
	PrimaryEmail string
	Phone        string
}

func (s *existingUserGoogleLoginSuite) SetupSuite() {
	// setup firebase store data for users
	rdb.Reset()
	users, uid, phone, email := dbtest.User()
	user.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(s.T(), err)
	assert.Nil(s.T(), rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(s.T(), rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(s.T(), rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	application.Init(rdb.GormRepo())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	s.UID = uid
	s.PrimaryEmail = email
	s.Phone = phone
	s.DifferentUID = util.RandString(6)

	// setup router
	router := gin.Default()
	googleLoginUrl := "/v1/login/google"
	googleLoginUrlWithUID := "/v1/login/google/uid"
	googleLoginUrlWithDifferentUID := "/v1/login/google/different_uid"
	googleCallbackUrl := "/v1/oauth/google_callback"
	callbackUrl := "/v1/oauth/callback"
	router.POST(googleLoginUrl, GoogleLogin)
	router.POST(googleLoginUrlWithUID, auth.MockAuthorize(s.UID), GoogleLogin)
	router.POST(googleLoginUrlWithDifferentUID, auth.MockAuthorize(s.DifferentUID), GoogleLogin)
	router.GET(googleCallbackUrl, GoogleLoginCallback)
	router.GET(callbackUrl, CallbackHandler)
	s.Router = router
}

func (s *existingUserGoogleLoginSuite) SetupTest() {
	// setup google oauth service every time
	// cuz we will mock part of the methods in tests
	// so we need to reset it every time
	googleoauth.GetService = googleoauth.NewGoogleOAuthService

	clientID := application.GetDefaultClientID(context.Background())
	s.State = generateState(clientID)
	s.Code = util.RandString(6)
}

func (s *existingUserGoogleLoginSuite) TestMultipleExistingUserGoogleLogin() {
	// default test order is alphabetical, so we need to set it manually
	s.existingUserGoogleLogin()
	s.userTryToLoginWithDifferentGoogleAccount()
	s.differentUserTryToLoginWithSameGoogleAccount()
}

func (s *existingUserGoogleLoginSuite) existingUserGoogleLogin() {
	// setup google oauth service every time
	// cuz we will mock part of the methods in tests
	// so we need to reset it every time
	googleoauth.GetService = googleoauth.NewGoogleOAuthService

	body := map[string]interface{}{
		"state": s.State,
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(s.T(), err)

	req, err := http.NewRequest("POST", "/v1/login/google/uid", strings.NewReader(string(bodyStr)))
	assert.Nil(s.T(), err)
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusOK, w.Code)
	var response GoogleLoginResp
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(s.T(), json.Unmarshal(responseStr, &response))
	assert.Equal(s.T(), 0, response.Code)
	expectURL := generateExpectLoginURL(s.State)
	assert.Equal(s.T(), expectURL, response.Data.LoginURL)

	// mock google oauth service and sendbird service
	ctrl := gomock.NewController(s.T())
	fn := mockGoogleOAuthService(ctrl, s.Code, s.PrimaryEmail)
	googleoauth.GetService = fn
	createUserFn := mockCreateAUser(ctrl, &s.UID)
	chatroom.GetService = createUserFn

	// call google callback
	params := url.Values{}
	params.Set("code", s.Code)
	params.Set("state", s.State)
	urlWithParam := "/v1/oauth/google_callback?" + params.Encode()
	req, err = http.NewRequest("GET", urlWithParam, nil)
	assert.Nil(s.T(), err)
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusFound, w.Code)
	location := w.Header().Get("Location")
	params = parseLocation(s.T(), location)
	assert.NotEmpty(s.T(), params.Get("kg_token"))
	assert.Empty(s.T(), params.Get("error"))
	assert.Empty(s.T(), params.Get("error_code"))

	// check user data
	firebaseUser, err := firebase.GetUserByEmail(context.Background(), s.PrimaryEmail)
	assert.Nil(s.T(), err)
	// ignore google provider check, because firebase emulator doesn't support it
	user, _ := rdb.GormRepo().GetUser(context.Background(), firebaseUser.UID, "", false, nil)
	assert.NotNil(s.T(), user)
	assert.NotNil(s.T(), user.Email)
	assert.Equal(s.T(), s.PrimaryEmail, *user.Email)
}

func (s *existingUserGoogleLoginSuite) userTryToLoginWithDifferentGoogleAccount() {
	// setup google oauth service every time
	// cuz we will mock part of the methods in tests
	// so we need to reset it every time
	googleoauth.GetService = googleoauth.NewGoogleOAuthService

	body := map[string]interface{}{
		"state": s.State,
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(s.T(), err)

	req, err := http.NewRequest("POST", "/v1/login/google/uid", strings.NewReader(string(bodyStr)))
	assert.Nil(s.T(), err)
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusOK, w.Code)
	var response GoogleLoginResp
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(s.T(), json.Unmarshal(responseStr, &response))
	assert.Equal(s.T(), 0, response.Code)
	expectURL := generateExpectLoginURL(s.State)
	assert.Equal(s.T(), expectURL, response.Data.LoginURL)

	// mock google oauth service and sendbird service
	differentEmail := util.RandEmail()
	ctrl := gomock.NewController(s.T())
	fn := mockGoogleOAuthService(ctrl, s.Code, differentEmail)
	googleoauth.GetService = fn
	createUserFn := mockCreateAUser(ctrl, &s.UID)
	chatroom.GetService = createUserFn

	// call google callback
	params := url.Values{}
	params.Set("code", s.Code)
	params.Set("state", s.State)
	urlWithParam := "/v1/oauth/google_callback?" + params.Encode()
	req, err = http.NewRequest("GET", urlWithParam, nil)
	assert.Nil(s.T(), err)
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusFound, w.Code)
	location := w.Header().Get("Location")
	params = parseLocation(s.T(), location)
	assert.Empty(s.T(), params.Get("kg_token"))
	assert.Equal(s.T(), "primary email is not the same", params.Get("error"))
	assert.Equal(s.T(), "1060", params.Get("error_code"))
}

func (s *existingUserGoogleLoginSuite) differentUserTryToLoginWithSameGoogleAccount() {
	// setup google oauth service every time
	// cuz we will mock part of the methods in tests
	// so we need to reset it every time
	googleoauth.GetService = googleoauth.NewGoogleOAuthService

	body := map[string]interface{}{
		"state": s.State,
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(s.T(), err)

	req, err := http.NewRequest("POST", "/v1/login/google/different_uid", strings.NewReader(string(bodyStr)))
	assert.Nil(s.T(), err)
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusOK, w.Code)
	var response GoogleLoginResp
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(s.T(), json.Unmarshal(responseStr, &response))
	assert.Equal(s.T(), 0, response.Code)
	expectURL := generateExpectLoginURL(s.State)
	assert.Equal(s.T(), expectURL, response.Data.LoginURL)

	// mock google oauth service and sendbird service
	ctrl := gomock.NewController(s.T())
	fn := mockGoogleOAuthService(ctrl, s.Code, s.PrimaryEmail)
	googleoauth.GetService = fn
	createUserFn := mockCreateAUser(ctrl, &s.UID)
	chatroom.GetService = createUserFn

	// call google callback
	params := url.Values{}
	params.Set("code", s.Code)
	params.Set("state", s.State)
	urlWithParam := "/v1/oauth/google_callback?" + params.Encode()
	req, err = http.NewRequest("GET", urlWithParam, nil)
	assert.Nil(s.T(), err)
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusFound, w.Code)
	location := w.Header().Get("Location")
	params = parseLocation(s.T(), location)
	assert.Empty(s.T(), params.Get("kg_token"))
	assert.Equal(s.T(), "uid is not the same", params.Get("error"))
	assert.Equal(s.T(), "1058", params.Get("error_code"))
}

type existingUserNoEmailGoogleLoginSuite struct {
	suite.Suite
	Router *gin.Engine
	UID    string
	State  string
	Code   string
	Email  string
}

func (s *existingUserNoEmailGoogleLoginSuite) SetupSuite() {
	// setup firebase store data for users
	rdb.Reset()
	users, userIDs := dbtest.Users()
	user.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(s.T(), err)
	assert.Nil(s.T(), rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(s.T(), rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(s.T(), rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	application.Init(rdb.GormRepo())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	s.UID = userIDs[0]

	// setup router
	router := gin.Default()
	googleLoginUrl := "/v1/login/google"
	googleCallbackUrl := "/v1/oauth/google_callback"
	callbackUrl := "/v1/oauth/callback"
	router.POST(googleLoginUrl, GoogleLogin)
	router.GET(googleCallbackUrl, GoogleLoginCallback)
	router.GET(callbackUrl, CallbackHandler)
	s.Router = router
}

func (s *existingUserNoEmailGoogleLoginSuite) SetupTest() {
	// setup google oauth service every time
	// cuz we will mock part of the methods in tests
	// so we need to reset it every time
	googleoauth.GetService = googleoauth.NewGoogleOAuthService

	clientID := application.GetDefaultClientID(context.Background())
	s.State = generateState(clientID)
	s.Code = util.RandString(6)
	s.Email = util.RandEmail()
}

func (s *existingUserNoEmailGoogleLoginSuite) TestExistingUserNoEmailGoogleLogin() {
	// default test order is alphabetical, so we need to set it manually
	s.existingUserWithNoEmailGoogleLogin()
}

func (s *existingUserNoEmailGoogleLoginSuite) existingUserWithNoEmailGoogleLogin() {
	// setup google oauth service every time
	// cuz we will mock part of the methods in tests
	// so we need to reset it every time
	googleoauth.GetService = googleoauth.NewGoogleOAuthService

	body := map[string]interface{}{
		"state": s.State,
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(s.T(), err)

	req, err := http.NewRequest("POST", "/v1/login/google", strings.NewReader(string(bodyStr)))
	assert.Nil(s.T(), err)
	w := httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusOK, w.Code)
	var response GoogleLoginResp
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(s.T(), json.Unmarshal(responseStr, &response))
	assert.Equal(s.T(), 0, response.Code)
	expectURL := generateExpectLoginURL(s.State)
	assert.Equal(s.T(), expectURL, response.Data.LoginURL)

	// mock google oauth service and sendbird service
	ctrl := gomock.NewController(s.T())
	fn := mockGoogleOAuthService(ctrl, s.Code, s.Email)
	googleoauth.GetService = fn
	createUserFn := mockCreateAUser(ctrl, &s.UID)
	chatroom.GetService = createUserFn

	// call google callback
	params := url.Values{}
	params.Set("code", s.Code)
	params.Set("state", s.State)
	urlWithParam := "/v1/oauth/google_callback?" + params.Encode()
	req, err = http.NewRequest("GET", urlWithParam, nil)
	assert.Nil(s.T(), err)
	w = httptest.NewRecorder()
	s.Router.ServeHTTP(w, req)
	assert.Equal(s.T(), http.StatusFound, w.Code)
	location := w.Header().Get("Location")
	params = parseLocation(s.T(), location)
	assert.NotEmpty(s.T(), params.Get("kg_token"))
	assert.Empty(s.T(), params.Get("error"))
	assert.Empty(s.T(), params.Get("error_code"))

	// check user data
	firebaseUser, err := firebase.GetUserByEmail(context.Background(), s.Email)
	assert.Nil(s.T(), err)
	// ignore google provider check, because firebase emulator doesn't support it
	user, _ := rdb.GormRepo().GetUser(context.Background(), firebaseUser.UID, "", false, nil)
	assert.NotNil(s.T(), user)
	assert.NotNil(s.T(), user.Email)
	assert.Equal(s.T(), s.Email, *user.Email)
}

func generateExpectLoginURL(state string) string {
	val := url.Values{}
	val.Set("client_id", "REDACTED")
	val.Set("redirect_uri", config.GetString("SELF_HOST")+"/v1/oauth/google_callback")
	val.Set("response_type", "code")
	val.Set("scope", "openid email profile")
	val.Set("access_type", "offline")
	val.Set("state", state)
	val.Set("include_granted_scopes", "true")
	val.Set("prompt", "consent select_account")
	expectURL := "https://accounts.google.com/o/oauth2/v2/auth?" + val.Encode()
	return expectURL
}

func parseLocation(t *testing.T, location string) url.Values {
	splitted := strings.Split(location, "?")
	assert.Equal(t, 2, len(splitted))
	assert.Equal(t, accountCallbackURL, splitted[0])
	params, err := url.ParseQuery(splitted[1])
	assert.Nil(t, err)
	return params
}

func mockGoogleOAuthService(ctrl *gomock.Controller, code, email string) func() googleoauth.Service {
	accessToken := util.RandString(6)
	return func() googleoauth.Service {
		m := googleoauth.NewMockService(ctrl)
		m.EXPECT().ExchangeAccessToken(code).AnyTimes().Return(&googleoauth.AccessTokenResponse{
			AccessToken:  accessToken,
			ExpiresIn:    3600,
			RefreshToken: "refresh_token",
			Scope:        "openid email profile",
			TokenType:    "Bearer",
		}, &resty.Response{}, nil)
		m.EXPECT().GetUserInfo(accessToken).AnyTimes().Return(&googleoauth.UserInfoResponse{
			Sub:           "sub",
			Name:          "name",
			GivenName:     "given_name",
			FamilyName:    "family_name",
			Picture:       "picture",
			Email:         email,
			EmailVerified: true,
			Locale:        "locale",
		}, &resty.Response{}, nil)
		return m
	}
}

func mockUpdateUserGoogleAccount(ctx context.Context, uid, email string) (*firebaseauth.UserRecord, error) {
	return firebase.GetUser(ctx, &firebase.UserParams{
		UID: uid,
	})
}

func mockCreateAUser(ctrl *gomock.Controller, userID *string) func(client sendbirdapi.SendbirdClientI) chatroom.Service {
	return func(client sendbirdapi.SendbirdClientI) chatroom.Service {
		m := chatroom.NewMockService(ctrl)
		if userID == nil {
			m.EXPECT().CreateAUser(gomock.Any(), gomock.Any()).AnyTimes().Return(&sendbirdapi.User{
				UserID:     "user_id",
				Nickname:   "nickname",
				ProfileURL: "profile_url",
			}, &resty.Response{}, nil)
			return m
		} else {
			m.EXPECT().CreateAUser(gomock.Any(), gomock.Any()).AnyTimes().Return(&sendbirdapi.User{
				UserID:     *userID,
				Nickname:   "nickname",
				ProfileURL: "profile_url",
			}, &resty.Response{}, nil)
			return m
		}
	}
}

func generateState(clientID string) string {
	payload := map[string]interface{}{
		"client_id": clientID,
		"nonce":     util.RandString(6),
	}
	payloadStr, _ := json.Marshal(payload)
	return base64.StdEncoding.EncodeToString(payloadStr)
}

func TestExchangeToken(t *testing.T) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	oauth.Reload()

	uid := util.RandString(6)
	router := gin.Default()
	router.POST("/v1/oauth/exchange_token", auth.MockAuthorize(uid), ExchangeToken)

	// invalid scope
	body := map[string]interface{}{
		"client_id": "20991a3ae83233d6de85d62906d71fd3",
		"scope":     "asset:read,asset:write,invalid_scope",
	}
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", "/v1/oauth/exchange_token", strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)
	assert.Equal(t, http.StatusForbidden, w.Code)

	// invalid client id
	body = map[string]interface{}{
		"client_id": "invalid_client_id",
		"scope":     "asset:read,asset:write",
	}
	bodyStr, err = json.Marshal(body)
	assert.Nil(t, err)
	req, err = http.NewRequest("POST", "/v1/oauth/exchange_token", strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)
	assert.Equal(t, http.StatusForbidden, w.Code)

	// valid request
	body = map[string]interface{}{
		"client_id": "20991a3ae83233d6de85d62906d71fd3",
		"scope":     "asset:read,asset:write",
	}
	bodyStr, err = json.Marshal(body)
	assert.Nil(t, err)
	req, err = http.NewRequest("POST", "/v1/oauth/exchange_token", strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	type response struct {
		Code int `json:"code"`
		Data struct {
			OAuthToken   string `json:"oauth_token"`
			RefreshToken string `json:"refresh_token"`
		} `json:"data"`
	}
	var resp response
	respStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(respStr, &resp))
	assert.Equal(t, 0, resp.Code)
	assert.NotEmpty(t, resp.Data.OAuthToken)
	assert.NotEmpty(t, resp.Data.RefreshToken)
}

func TestGetStoreCallbackURL(t *testing.T) {
	s := assert.New(t)
	expectedURL := "https://store-dev.kryptogo.com/kryptogo/login/verify-account"
	testCases := []struct {
		baseURL    string
		marketCode string
	}{
		{baseURL: "https://store-dev.kryptogo.com", marketCode: "kryptogo"},
		{baseURL: "https://store-dev.kryptogo.com", marketCode: "/kryptogo"},
		{baseURL: "https://store-dev.kryptogo.com", marketCode: "kryptogo/"},
		{baseURL: "https://store-dev.kryptogo.com", marketCode: "/kryptogo/"},
		{baseURL: "https://store-dev.kryptogo.com/", marketCode: "kryptogo"},
		{baseURL: "https://store-dev.kryptogo.com/", marketCode: "/kryptogo"},
		{baseURL: "https://store-dev.kryptogo.com/", marketCode: "kryptogo/"},
		{baseURL: "https://store-dev.kryptogo.com/", marketCode: "/kryptogo/"},
	}

	for _, tc := range testCases {
		os.Setenv("KGSTORE_BASE_URL", tc.baseURL)
		s.Equal(expectedURL, getStoreCallbackURL(context.Background(), tc.marketCode))
	}
}
