// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package oauth

import (
	"errors"
	"fmt"
)

const (
	// StateServiceStudio is a StateService of type studio.
	StateServiceStudio StateService = "studio"
	// StateServiceApp is a StateService of type app.
	StateServiceApp StateService = "app"
	// StateServiceWebsdk is a StateService of type websdk.
	StateServiceWebsdk StateService = "websdk"
	// StateServiceStore is a StateService of type store.
	StateServiceStore StateService = "store"
	// StateServicePwa is a StateService of type pwa.
	StateServicePwa StateService = "pwa"
)

var ErrInvalidStateService = errors.New("not a valid StateService")

// String implements the Stringer interface.
func (x StateService) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x StateService) IsValid() bool {
	_, err := ParseStateService(string(x))
	return err == nil
}

var _StateServiceValue = map[string]StateService{
	"studio": StateServiceStudio,
	"app":    StateServiceApp,
	"websdk": StateServiceWebsdk,
	"store":  StateServiceStore,
	"pwa":    StateServicePwa,
}

// ParseStateService attempts to convert a string to a StateService.
func ParseStateService(name string) (StateService, error) {
	if x, ok := _StateServiceValue[name]; ok {
		return x, nil
	}
	return StateService(""), fmt.Errorf("%s is %w", name, ErrInvalidStateService)
}
