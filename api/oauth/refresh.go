package oauth

import (
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/go-oauth2/oauth2/v4"
	"github.com/go-oauth2/oauth2/v4/server"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type refreshTokenReq struct {
	RefreshToken string `json:"refresh_token"`
}

type refreshTokenData struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

// RefreshToken .
//
// Deprecated: deprecated
func RefreshToken(ctx *gin.Context) {
	params := &refreshTokenReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// verify refresh token & validate session
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeOAuth)
	claimsI, errCode, err := strategy.Parse(params.RefreshToken)
	if err != nil {
		if errors.Is(err, code.ErrJwtExpired) {
			// we're passing the expired token here
		} else {
			response.ForbiddenErrorWithMsg(ctx, errCode, err.Error())
			return
		}
	}
	claims := claimsI.(*jwtservice.AccessTokenClaims)

	// get user
	uid := claims.Subject
	clientID := ClientID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, clientID, false, nil)
	if user == nil {
		response.ForbiddenErrorWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	// create tokens
	// reuse the issue token logic in oauth server
	oauth.RLockManager()
	defer oauth.RUnlockManager()
	tokenInfo, err := oauth.GetServer().GetAuthorizeToken(ctx.Request.Context(), &server.AuthorizeRequest{
		ResponseType: oauth2.Token,
		ClientID:     clientID,
		Scope:        claims.Scopes,
		UserID:       uid,
	})
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.InternalError, err.Error())
		return
	}

	response.OK(ctx, refreshTokenData{
		AccessToken:  tokenInfo.GetAccess(),
		RefreshToken: tokenInfo.GetRefresh(),
	})
}
