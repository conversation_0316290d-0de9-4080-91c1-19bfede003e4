package oauth

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type upsertOAuthClientConfigReq struct {
	ID                   string   `json:"id"`
	Domain               string   `json:"domain"`
	IsPrivileged         bool     `json:"is_privileged"`
	Name                 string   `json:"name"`
	SupportAddress       string   `json:"support_address"`
	MainLogo             string   `json:"main_logo"`
	AppStoreLink         string   `json:"app_store_link"`
	GooglePlayLink       string   `json:"google_play_link"`
	SendbirdAppID        string   `json:"sendbird_app_id"`
	SendbirdApiTokenName string   `json:"sendbird_api_token_name"`
	LineChannelID        *string  `json:"line_channel_id"`
	OrganizationID       *int     `json:"organization_id"`
	ApplicationType      *string  `json:"application_type"`
	SquareLogo           string   `json:"square_logo"`
	WideLogo             string   `json:"wide_logo"`
	Scopes               []string `json:"scopes"`
	Secret               string   `json:"secret"`
	LoginMethods         []string `json:"login_methods"`
}

// UpsertOAuthClientConfigs upserts oauth client configs
func UpsertOAuthClientConfigs(ctx *gin.Context) {
	req := upsertOAuthClientConfigReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	if req.OrganizationID == nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "organization_id is required")
		return
	}

	var applicationType *domain.OAuthApplicationType
	if req.ApplicationType != nil {
		t, err := domain.ParseOAuthApplicationType(*req.ApplicationType)
		if err != nil {
			response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid application type")
			return
		}
		applicationType = &t
	}

	oauthApp := &domain.OAuthApplication{
		Application: domain.Application{
			ClientID:     req.ID,
			ClientSecret: req.Secret,
			Name:         req.Name,
			Domain:       req.Domain,
		},
		IsPrivileged:         req.IsPrivileged,
		SupportAddress:       req.SupportAddress,
		MainLogo:             req.MainLogo,
		AppStoreLink:         req.AppStoreLink,
		GooglePlayLink:       req.GooglePlayLink,
		SendbirdAppID:        req.SendbirdAppID,
		SendbirdApiTokenName: req.SendbirdApiTokenName,
		LineChannelID:        req.LineChannelID,
		SquareLogo:           util.Ptr(req.SquareLogo),
		WideLogo:             util.Ptr(req.WideLogo),
		Scopes:               req.Scopes,
		Type:                 applicationType,
	}

	if req.LoginMethods != nil {
		// validate and convert login methods
		loginMethods := []domain.LoginMethod{}
		for _, m := range req.LoginMethods {
			loginMethod, err := domain.ParseLoginMethod(m)
			if err != nil {
				response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid login method")
				return
			}
			loginMethods = append(loginMethods, loginMethod)
		}
		oauthApp.LoginMethods = loginMethods
	}

	err := application.UpsertOAuthApplication(ctx.Request.Context(), *req.OrganizationID, oauthApp)
	if err != nil {
		response.KGError(ctx, err)
		return
	}
	go oauth.NotifyClientConfigChanges()

	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
	})
}

type getOAuthClientConfigReq struct {
	ID     string `form:"id"`
	Domain string `form:"domain"`
}

type getOAuthClientConfigResp struct {
	Code int                      `json:"code"`
	Data getOAuthClientConfigData `json:"data"`
}

type getOAuthClientConfigData struct {
	Name         string   `json:"name"`
	ID           string   `json:"client_id"`
	Domain       string   `json:"domain"`
	MainLogo     string   `json:"main_logo"`
	WideLogo     string   `json:"wide_logo"`
	Scopes       []string `json:"scopes"`
	LoginMethods []string `json:"login_methods"`
}

// GetOAuthClientConfig gets oauth client config
func GetOAuthClientConfig(ctx *gin.Context) {
	req := getOAuthClientConfigReq{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	var app *domain.OAuthApplication
	var err *code.KGError
	if req.ID != "" {
		app, err = application.GetOAuthApplication(ctx.Request.Context(), req.ID)
	} else if req.Domain != "" {
		app, err = application.GetOAuthApplicationByDomain(ctx.Request.Context(), req.Domain)
	} else {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "id or domain is required")
		return
	}

	if err != nil {
		response.KGError(ctx, err)
		return
	}

	ctx.JSON(http.StatusOK, &getOAuthClientConfigResp{
		Code: 0,
		Data: getOAuthClientConfigData{
			Name:   app.Name,
			ID:     app.ClientID,
			Domain: app.Domain,
			// use square logo as main logo, because main logo is used in email template
			MainLogo:     util.Val(app.SquareLogo),
			WideLogo:     util.Val(app.WideLogo),
			Scopes:       app.Scopes,
			LoginMethods: app.LoginMethods.ToStrings(),
		},
	})
}
