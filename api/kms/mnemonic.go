package kms

import (
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/gcp"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/kms"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

const (
	keyRingName = "mnemonic"
	keyName     = "key"
	keySize     = 32
)

// GenerateMnemonicAddresses generate mnemonic addresses, and then encrypt them with kms key
func GenerateMnemonicAddresses(ctx *gin.Context) {
	mnemonic, err := kms.GenerateMnemonic()
	if err != nil {
		kgErr := code.NewKGError(code.GenerateMnemonicFailed, http.StatusInternalServerError, err, nil)
		response.KGError(ctx, kgErr)
		return
	}

	projectID := config.GetString("PROJECT_ID")
	keyName := fmt.Sprintf("projects/%s/locations/global/keyRings/%s/cryptoKeys/%s", projectID, keyRingName, keyName)
	encryptedMnemonic, err := kms.EncryptSymmetric(ctx.Request.Context(), keyName, []byte(mnemonic))
	if err != nil {
		kgErr := code.NewKGError(code.KmsFailed, http.StatusInternalServerError, err, nil)
		response.KGError(ctx, kgErr)
		return
	}

	resp := map[string]interface{}{
		"encrypted_mnemonic": base64.StdEncoding.EncodeToString(encryptedMnemonic),
	}

	for chainName, derivationPath := range kms.WalletDerivationPath {
		accountName := fmt.Sprintf("%s_address", chainName)
		account, _, err := kms.GenerateAddress(mnemonic, chainName, derivationPath)
		if err != nil {
			kgErr := code.NewKGError(code.GenerateAddressesFailed, http.StatusInternalServerError, err, nil)
			response.KGError(ctx, kgErr)
			return
		}
		resp[accountName] = account
	}
	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": resp,
	})
}

type retrieveMnemonicReq struct {
	EncryptedMnemonic string `json:"encrypted_mnemonic"`
	PublicKey         string `json:"public_key"`
}
type retrieveMnemonicResp struct {
	Code  int                 `json:"code"`
	Data  retrieveMnemonicReq `json:"data"`
	Error string              `json:"error,omitempty"`
}

// RetrieveMnemonic decrypt mnemonic with kms key, and reutrn encrypted mnemonic with shared secret
func RetrieveMnemonic(ctx *gin.Context) {
	req := retrieveMnemonicReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	ciphertext, err := base64.StdEncoding.DecodeString(req.EncryptedMnemonic)
	if err != nil {
		kgErr := code.NewKGError(code.DecodeFailed, http.StatusBadRequest, err, nil)
		response.KGError(ctx, kgErr)
		return
	}
	bytePubKey, err := base64.StdEncoding.DecodeString(req.PublicKey)
	if err != nil {
		kgErr := code.NewKGError(code.DecodeFailed, http.StatusBadRequest, err, nil)
		response.KGError(ctx, kgErr)
		return
	}

	projectID := config.GetString("PROJECT_ID")
	keyName := fmt.Sprintf("projects/%s/locations/global/keyRings/%s/cryptoKeys/%s", projectID, keyRingName, keyName)

	plaintext, err := gcp.DecryptSymmetric(ctx.Request.Context(), keyName, ciphertext)
	if err != nil {
		kgErr := code.NewKGError(code.KmsFailed, http.StatusInternalServerError, err, nil)
		response.KGError(ctx, kgErr)
		return
	}

	// compatible with old version
	if len(plaintext) == 64 {
		plaintext, _ = hex.DecodeString(string(plaintext))
	}

	priKey, pubKey := util.GenerateKeyPair()
	var b [keySize]byte
	copy(b[:], bytePubKey)
	sharedSecret := util.GenerateSharedSecret(priKey, &b)
	encryptedMnemonic, err := util.Encrypt(plaintext, sharedSecret)
	if err != nil {
		kgErr := code.NewKGError(code.EncryptDecryptFailed, http.StatusInternalServerError, err, nil)
		response.KGError(ctx, kgErr)
		return
	}

	data := retrieveMnemonicResp{
		Code: code.OK,
		Data: retrieveMnemonicReq{
			PublicKey:         base64.StdEncoding.EncodeToString(pubKey.(*[keySize]byte)[:]),
			EncryptedMnemonic: encryptedMnemonic,
		},
	}
	ctx.JSON(http.StatusOK, data)
}
