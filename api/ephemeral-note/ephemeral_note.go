package ephemeralnote

import (
	"github.com/ethereum/go-ethereum/common"
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	ephemeralnote "github.com/kryptogo/kg-wallet-backend/pkg/service/ephemeral-note"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
)

type releaseEphemeralOwnerReq struct {
	ChainID string `json:"chain_id" binding:"required"`
	Address string `json:"address" binding:"required"`
}

type createEphemeralNoteReq struct {
	ChainID string `json:"chain_id" binding:"required"`
	TxHash  string `json:"tx_hash" binding:"required"`
}

type claimEphemeralNoteReq struct {
	Recipient string `json:"recipient" binding:"required"`
}

type ephemeralOwnerResp struct {
	Address string `json:"address"`
}

type createEphemeralNoteResp struct {
	NoteID string `json:"note_id"`
}

type ephemeralNoteResp struct {
	ID                string                     `json:"id"`
	ChainID           string                     `json:"chain_id"`
	From              string                     `json:"from"`
	TokenAddress      string                     `json:"token_address"`
	Amount            string                     `json:"amount"`
	DisplayAmount     string                     `json:"display_amount"`
	Symbol            string                     `json:"symbol"`
	DepositTxHash     string                     `json:"deposit_tx_hash"`
	ClaimTxHash       *string                    `json:"claim_tx_hash,omitempty"`
	EphemeralOwner    string                     `json:"ephemeral_owner"`
	Status            domain.EphemeralNoteStatus `json:"status"`
	SenderUID         string                     `json:"sender_uid"`
	SenderAvatar      string                     `json:"sender_avatar"`
	SenderDisplayName string                     `json:"sender_display_name"`
	TokenPrice        float64                    `json:"token_price"`
	TokenImage        string                     `json:"token_image"`
	CreatedAt         int64                      `json:"created_at"`
}

// GetEphemeralOwner handles the request to get an ephemeral owner
func GetEphemeralOwner(c *gin.Context) {
	ctx := c.Request.Context()
	chainID := c.Query("chain_id")
	address, kgErr := ephemeralnote.RequestEphemeralOwner(ctx, chainID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	resp := ephemeralOwnerResp{Address: util.AddressToString(chainID, address)}
	response.OK(c, resp)
}

// ReleaseEphemeralOwner handles the request to release an ephemeral owner
func ReleaseEphemeralOwner(c *gin.Context) {
	ctx := c.Request.Context()
	var req releaseEphemeralOwnerReq

	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	kgErr := ephemeralnote.ReleaseEphemeralOwner(ctx, req.ChainID, util.StringToAddress(req.ChainID, req.Address))
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

// Create handles the request to create an ephemeral note
func Create(c *gin.Context) {
	ctx := c.Request.Context()
	var req createEphemeralNoteReq

	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	uid := c.GetString("uid")
	noteID, kgErr := ephemeralnote.CreateNote(ctx, uid, req.ChainID, common.HexToHash(req.TxHash))
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp := createEphemeralNoteResp{NoteID: noteID}
	response.OK(c, resp)
}

// Claim handles the request to claim an ephemeral note
func Claim(c *gin.Context) {
	ctx := c.Request.Context()
	noteID := c.Param("id")
	uid := c.GetString("uid")
	var req claimEphemeralNoteReq

	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	claimedNote, kgErr := ephemeralnote.ClaimNote(ctx, noteID, req.Recipient, uid)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	resp := toEphemeralNoteResp(claimedNote)
	response.OK(c, resp)
}

type listActiveNotesResp struct {
	Notes []ephemeralNoteResp `json:"notes"`
}

// ListActiveNotes handles the request to list all active ephemeral notes for a user
func ListActiveNotes(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")

	activeNotes, kgErr := ephemeralnote.ListActiveNotes(ctx, uid)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	resp := listActiveNotesResp{
		Notes: toEphemeralNotesResp(activeNotes),
	}
	response.OK(c, resp)
}

// GetEphemeralNote handles the request to get an ephemeral note by ID
func GetEphemeralNote(c *gin.Context) {
	ctx := c.Request.Context()
	noteID := c.Param("id")
	note, kgErr := ephemeralnote.GetNoteByID(ctx, noteID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, toEphemeralNoteResp(note))
}

// ListAllNotes handles the request to list all ephemeral notes
func ListAllNotes(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")
	notes, kgErr := ephemeralnote.ListAllNotes(ctx, uid)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	resp := listAllNotesResp{
		Notes: toEphemeralNotesResp(notes),
	}
	response.OK(c, resp)
}

func toEphemeralNoteResp(note *domain.EnrichedEphemeralNote) ephemeralNoteResp {
	amount, _ := decimal.NewFromString(note.Amount)
	displayAmount := amount.Div(decimal.New(1, int32(note.TokenDecimals))).String()

	resp := ephemeralNoteResp{
		ID:                note.ID,
		ChainID:           note.ChainID,
		From:              util.AddressToString(note.ChainID, note.From),
		TokenAddress:      util.AddressToString(note.ChainID, note.TokenAddress),
		Amount:            note.Amount,
		DisplayAmount:     displayAmount,
		Symbol:            note.Symbol,
		DepositTxHash:     note.DepositTxHash.Hex(),
		EphemeralOwner:    util.AddressToString(note.ChainID, note.EphemeralOwner),
		Status:            note.Status,
		SenderUID:         note.SenderUID,
		SenderAvatar:      note.SenderAvatar,
		SenderDisplayName: note.SenderDisplayName,
		TokenPrice:        note.TokenPrice,
		TokenImage:        note.TokenImage,
		CreatedAt:         note.CreatedAt.Unix(),
	}
	if resp.ChainID == model.ChainIDTron || resp.ChainID == model.ChainIDShasta {
		resp.DepositTxHash = resp.DepositTxHash[2:]
	}
	if note.ClaimTxHash != nil {
		claimTxHash := note.ClaimTxHash.Hex()
		if resp.ChainID == model.ChainIDTron || resp.ChainID == model.ChainIDShasta {
			claimTxHash = claimTxHash[2:]
		}
		resp.ClaimTxHash = &claimTxHash
	}
	return resp
}

func toEphemeralNotesResp(notes []*domain.EnrichedEphemeralNote) []ephemeralNoteResp {
	resp := make([]ephemeralNoteResp, len(notes))
	for i, note := range notes {
		resp[i] = toEphemeralNoteResp(note)
	}
	return resp
}

type listAllNotesResp struct {
	Notes []ephemeralNoteResp `json:"notes"`
}
