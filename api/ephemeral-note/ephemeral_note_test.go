package ephemeralnote

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"math/big"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/chain/evm"
	abibinding "github.com/kryptogo/kg-wallet-backend/chain/evm/abi-binding"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	ephemeralnote "github.com/kryptogo/kg-wallet-backend/pkg/service/ephemeral-note"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/eth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/repo"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func TestEphemeralNote(t *testing.T) {
	r, uid, userData := setupTestRouter(t)
	t.Run("EVM", func(t *testing.T) {
		// t.Parallel()
		testEVMEphemeralNote(t, r, uid, userData)
	})

	t.Run("Tron", func(t *testing.T) {
		// t.Parallel()
		testTronEphemeralNote(t, r, uid, userData)
	})
}

func setupSigningServer(t *testing.T) {
	// setup rdb
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())

	prices := []*model.AssetPrice{
		{
			ChainID:    "holesky",
			AssetGroup: "******************************************",
			Price:      "100000",
		},
		{
			ChainID:    "shasta",
			AssetGroup: "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs",
			Price:      "100000",
		},
	}
	assert.Nil(t, rdb.SyncAssetPrices(context.Background(), prices))
	assetsToInsert := []model.Asset{{
		ChainID:       "holesky",
		AssetGroup:    "******************************************",
		Amount:        util.Ptr("1.0"),
		WalletAddress: "******************************************",
		AssetType:     "token",
		Symbol:        util.Ptr("USDC"),
		LogoUrls:      `["https://example.com/usdc.png"]`,
	}, {
		ChainID:       "shasta",
		AssetGroup:    "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs",
		Amount:        util.Ptr("1.0"),
		WalletAddress: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7",
		AssetType:     "token",
		Symbol:        util.Ptr("USDT"),
		LogoUrls:      `["https://example.com/usdt.png"]`,
	}}
	assert.Nil(t, rdb.Get().Create(&assetsToInsert).Error)

	// init services
	ctrl := gomock.NewController(t)
	application.Init(rdb.GormRepo())
	ephemeralnote.Init(repo.Unified(), domain.NewMockAsyncTaskExecutor(ctrl))

	// signing server
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", signing.SignEvmTransaction)
	rSigning.POST("/v1/sign/tron", signing.SignTronTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
}

func setupTestRouter(t *testing.T) (*gin.Engine, string, domain.UserData) {
	setupSigningServer(t)

	r := gin.Default()
	users, uid, _, _ := dbtest.User()
	users[uid].Wallets.Wallets = append(users[uid].Wallets.Wallets, &domain.UserWallet{
		Chain:   "tron",
		Address: "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd",
	})
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	tron.Init(tron.InitParams{
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})
	alchemyapi.InitDefault()

	tokenmeta.Init(repo.Unified(), []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})
	tokenmeta.AddKnown(map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: domain.IDToChain("holesky"), TokenID: "******************************************"}: {
			Name:    "USD Coin",
			Symbol:  "USDC",
			LogoUrl: "https://example.com/usdc.png",
		},
		{Chain: domain.IDToChain("shasta"), TokenID: "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"}: {
			Name:    "USD Tether",
			Symbol:  "USDT",
			LogoUrl: "https://example.com/usdt.png",
		},
	})

	r.GET("/ephemeral_owner", auth.MockAuthorize(uid), GetEphemeralOwner)
	r.POST("/ephemeral_owner/release", auth.MockAuthorize(uid), ReleaseEphemeralOwner)
	r.POST("/ephemeral_note", auth.MockAuthorize(uid), Create)
	r.POST("/ephemeral_note/:id/claim", auth.MockAuthorize(uid), Claim)
	r.GET("/ephemeral_note/:id", auth.MockAuthorize(uid), GetEphemeralNote)
	r.GET("/ephemeral_notes/active", auth.MockAuthorize(uid), ListActiveNotes)
	r.GET("/ephemeral_notes", auth.MockAuthorize(uid), ListAllNotes)
	r.GET("/ephemeral_note/config", auth.MockAuthorize(uid), GetConfig)

	return r, uid, users[uid]
}

type noteData struct {
	ID                string  `json:"id"`
	ChainID           string  `json:"chain_id"`
	From              string  `json:"from"`
	TokenAddress      string  `json:"token_address"`
	Amount            string  `json:"amount"`
	DisplayAmount     string  `json:"display_amount"`
	Symbol            string  `json:"symbol"`
	DepositTxHash     string  `json:"deposit_tx_hash"`
	ClaimTxHash       *string `json:"claim_tx_hash,omitempty"`
	EphemeralOwner    string  `json:"ephemeral_owner"`
	Status            string  `json:"status"`
	SenderUID         string  `json:"sender_uid"`
	SenderAvatar      string  `json:"sender_avatar"`
	SenderDisplayName string  `json:"sender_display_name"`
	TokenPrice        float64 `json:"token_price"`
	TokenImage        string  `json:"token_image"`
	CreatedAt         int64   `json:"created_at"`
}

func testEVMEphemeralNote(t *testing.T, r *gin.Engine, uid string, userData domain.UserData) {
	// Step 1: Get Ephemeral Owner
	chainID := "holesky"
	req, _ := http.NewRequest("GET", fmt.Sprintf("/ephemeral_owner?chain_id=%s", chainID), nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var getOwnerResp struct {
		Code int `json:"code"`
		Data struct {
			Address string `json:"address"`
		} `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &getOwnerResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, getOwnerResp.Code)
	assert.NotEmpty(t, getOwnerResp.Data.Address)
	ephOwnerAddress := getOwnerResp.Data.Address

	// Step 2: Send a transaction on holesky for depositing USDC
	ctx := context.Background()
	senderPrivKey, _ := crypto.HexToECDSA("4f912942c735b8a7b66d0fd116aae44c909c0339a2f16713c5aa3b502ed1a26d")
	senderAddress := crypto.PubkeyToAddress(senderPrivKey.PublicKey)
	t.Logf("Sender address: %s\n", senderAddress.Hex())
	usdcAddress := common.HexToAddress("******************************************")
	amount := big.NewInt(10) // 0.00001 USDC

	// Connect to holesky
	client, err := ethclient.Dial(eth.RpcURL(chainID))
	require.NoError(t, err)

	// Get the real contract address from config
	contractAddress := common.HexToAddress(config.GetStringMap("EPHEMERAL_NOTES_CONTRACTS")[chainID])
	contract, err := abibinding.NewEphemeralNotes(contractAddress, client)
	require.NoError(t, err)

	// Create note on the real contract with higher gas price
	gasPrice, err := client.SuggestGasPrice(ctx)
	require.NoError(t, err)
	gasPrice = new(big.Int).Mul(gasPrice, big.NewInt(2))

	auth, err := bind.NewKeyedTransactorWithChainID(senderPrivKey, big.NewInt(17000)) // holesky chain ID
	require.NoError(t, err)
	auth.GasPrice = gasPrice

	tx, err := contract.CreateNote(auth, common.HexToAddress(ephOwnerAddress), usdcAddress, amount)
	require.NoError(t, err)

	// Wait for the transaction to be mined
	receipt, err := bind.WaitMined(ctx, client, tx)
	require.NoError(t, err)
	require.Equal(t, uint64(1), receipt.Status) // Check if transaction was successful
	txHash := tx.Hash()
	t.Logf("Create Note tx hash: %s\n", txHash.Hex())
	time.Sleep(3 * time.Second) // avoid flaky

	// Step 3: Create Ephemeral Note
	createNoteBody := createEphemeralNoteReq{
		ChainID: chainID,
		TxHash:  txHash.Hex(),
	}
	bodyBytes, _ := json.Marshal(createNoteBody)
	req, _ = http.NewRequest("POST", "/ephemeral_note", bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	if w.Code != http.StatusOK {
		t.Errorf("Create Note response: %s\n", w.Body.String())
	}

	var createNoteResp struct {
		Code int `json:"code"`
		Data struct {
			NoteID string `json:"note_id"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &createNoteResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, createNoteResp.Code)
	assert.NotEmpty(t, createNoteResp.Data.NoteID)
	noteID := createNoteResp.Data.NoteID

	// New Step: List Active Ephemeral Notes (after creation)
	req, _ = http.NewRequest("GET", "/ephemeral_notes/active", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var listNotesResp struct {
		Code int `json:"code"`
		Data struct {
			Notes []struct {
				ID                string  `json:"id"`
				ChainID           string  `json:"chain_id"`
				From              string  `json:"from"`
				TokenAddress      string  `json:"token_address"`
				Amount            string  `json:"amount"`
				DisplayAmount     string  `json:"display_amount"`
				Symbol            string  `json:"symbol"`
				DepositTxHash     string  `json:"deposit_tx_hash"`
				EphemeralOwner    string  `json:"ephemeral_owner"`
				SenderUID         string  `json:"sender_uid"`
				SenderAvatar      string  `json:"sender_avatar"`
				SenderDisplayName string  `json:"sender_display_name"`
				TokenPrice        float64 `json:"token_price"`
				TokenImage        string  `json:"token_image"`
			} `json:"notes"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &listNotesResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, listNotesResp.Code)
	assert.Len(t, listNotesResp.Data.Notes, 1)
	assert.Equal(t, noteID, listNotesResp.Data.Notes[0].ID)
	assert.Equal(t, chainID, listNotesResp.Data.Notes[0].ChainID)
	assert.Equal(t, senderAddress.Hex(), listNotesResp.Data.Notes[0].From)
	assert.Equal(t, usdcAddress.Hex(), listNotesResp.Data.Notes[0].TokenAddress)
	assert.Equal(t, amount.String(), listNotesResp.Data.Notes[0].Amount)
	assert.Equal(t, "0.00001", listNotesResp.Data.Notes[0].DisplayAmount) // Assuming 6 decimals for USDC
	assert.Equal(t, "USDC", listNotesResp.Data.Notes[0].Symbol)
	assert.Equal(t, txHash.Hex(), listNotesResp.Data.Notes[0].DepositTxHash)
	assert.Equal(t, ephOwnerAddress, listNotesResp.Data.Notes[0].EphemeralOwner)
	assert.Equal(t, uid, listNotesResp.Data.Notes[0].SenderUID)
	assert.Equal(t, userData.Avatar.AvatarURL, listNotesResp.Data.Notes[0].SenderAvatar)
	assert.Equal(t, userData.DisplayName, listNotesResp.Data.Notes[0].SenderDisplayName)
	assert.Equal(t, 100000.0, listNotesResp.Data.Notes[0].TokenPrice)
	assert.Equal(t, "https://example.com/usdc.png", listNotesResp.Data.Notes[0].TokenImage)

	// New Step: Get Ephemeral Note by ID
	req, _ = http.NewRequest("GET", fmt.Sprintf("/ephemeral_note/%s", noteID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var getNoteResp struct {
		Code int `json:"code"`
		Data struct {
			ID                string  `json:"id"`
			ChainID           string  `json:"chain_id"`
			From              string  `json:"from"`
			TokenAddress      string  `json:"token_address"`
			Amount            string  `json:"amount"`
			DisplayAmount     string  `json:"display_amount"`
			Symbol            string  `json:"symbol"`
			DepositTxHash     string  `json:"deposit_tx_hash"`
			ClaimTxHash       *string `json:"claim_tx_hash"`
			EphemeralOwner    string  `json:"ephemeral_owner"`
			Status            string  `json:"status"`
			SenderUID         string  `json:"sender_uid"`
			SenderAvatar      string  `json:"sender_avatar"`
			SenderDisplayName string  `json:"sender_display_name"`
			TokenPrice        float64 `json:"token_price"`
			TokenImage        string  `json:"token_image"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &getNoteResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, getNoteResp.Code)
	assert.Equal(t, noteID, getNoteResp.Data.ID)
	assert.Equal(t, chainID, getNoteResp.Data.ChainID)
	assert.Equal(t, senderAddress.Hex(), getNoteResp.Data.From)
	assert.Equal(t, usdcAddress.Hex(), getNoteResp.Data.TokenAddress)
	assert.Equal(t, amount.String(), getNoteResp.Data.Amount)
	assert.Equal(t, "0.00001", getNoteResp.Data.DisplayAmount) // Assuming 6 decimals for USDC
	assert.Equal(t, "USDC", getNoteResp.Data.Symbol)
	assert.Equal(t, txHash.Hex(), getNoteResp.Data.DepositTxHash)
	assert.Nil(t, getNoteResp.Data.ClaimTxHash)
	assert.Equal(t, ephOwnerAddress, getNoteResp.Data.EphemeralOwner)
	assert.Equal(t, string(domain.EphemeralNoteStatusActive), getNoteResp.Data.Status)
	assert.Equal(t, uid, getNoteResp.Data.SenderUID)
	assert.Equal(t, userData.Avatar.AvatarURL, getNoteResp.Data.SenderAvatar)
	assert.Equal(t, userData.DisplayName, getNoteResp.Data.SenderDisplayName)
	assert.Equal(t, 100000.0, getNoteResp.Data.TokenPrice)
	assert.Equal(t, "https://example.com/usdc.png", getNoteResp.Data.TokenImage)

	// Step 4: Claim Ephemeral Note
	time.Sleep(2 * time.Second)
	recipient := common.HexToAddress("0x9BAE3A9cBac6E769D980f6ce0fd52397dD45d361")
	claimNoteBody := claimEphemeralNoteReq{
		Recipient: recipient.Hex(),
	}
	bodyBytes, _ = json.Marshal(claimNoteBody)
	req, _ = http.NewRequest("POST", fmt.Sprintf("/ephemeral_note/%s/claim", noteID), bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	if w.Code != http.StatusOK {
		t.Errorf("Claim Note response: %s\n", w.Body.String())
	}

	var claimNoteResp struct {
		Code int `json:"code"`
		Data struct {
			ID                string  `json:"id"`
			ChainID           string  `json:"chain_id"`
			From              string  `json:"from"`
			TokenAddress      string  `json:"token_address"`
			Amount            string  `json:"amount"`
			DisplayAmount     string  `json:"display_amount"`
			Symbol            string  `json:"symbol"`
			DepositTxHash     string  `json:"deposit_tx_hash"`
			ClaimTxHash       *string `json:"claim_tx_hash,omitempty"`
			EphemeralOwner    string  `json:"ephemeral_owner"`
			Status            string  `json:"status"`
			SenderUID         string  `json:"sender_uid"`
			SenderAvatar      string  `json:"sender_avatar"`
			SenderDisplayName string  `json:"sender_display_name"`
			TokenPrice        float64 `json:"token_price"`
			TokenImage        string  `json:"token_image"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &claimNoteResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, claimNoteResp.Code)
	assert.NotNil(t, claimNoteResp.Data)
	assert.Equal(t, noteID, claimNoteResp.Data.ID)
	assert.Equal(t, string(domain.EphemeralNoteStatusClaimed), claimNoteResp.Data.Status)
	assert.NotNil(t, claimNoteResp.Data.ClaimTxHash)
	assert.Equal(t, "0.00001", claimNoteResp.Data.DisplayAmount)
	assert.Equal(t, "USDC", claimNoteResp.Data.Symbol)
	assert.Equal(t, uid, claimNoteResp.Data.SenderUID)
	assert.Equal(t, userData.Avatar.AvatarURL, claimNoteResp.Data.SenderAvatar)
	assert.Equal(t, userData.DisplayName, claimNoteResp.Data.SenderDisplayName)
	assert.Equal(t, 100000.0, claimNoteResp.Data.TokenPrice)
	assert.Equal(t, "https://example.com/usdc.png", claimNoteResp.Data.TokenImage)
	assert.NotNilf(t, claimNoteResp.Data.ClaimTxHash, "Claim tx hash should not be nil")
	t.Logf("Claim Note tx hash: %s\n", *claimNoteResp.Data.ClaimTxHash)
	c := evm.GetClient(domain.Holesky)
	status, err := c.WaitUntilTransactionConfirmed(ctx, *claimNoteResp.Data.ClaimTxHash)
	require.NoError(t, err)
	assert.Equal(t, domain.TransactionStatusSuccess, status)

	// Verify the note was actually claimed on the contract
	time.Sleep(2 * time.Second)
	contractNote, err := contract.Notes(&bind.CallOpts{}, common.HexToAddress(ephOwnerAddress))
	require.NoError(t, err)
	assert.Equal(t, common.Address{}, contractNote.EphemeralOwner) // Should be zero address after claiming

	// New Step: List Active Ephemeral Notes (after claiming)
	req, _ = http.NewRequest("GET", "/ephemeral_notes/active", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var listNotesResp2 struct {
		Code int `json:"code"`
		Data struct {
			Notes []struct {
				ID string `json:"id"`
			} `json:"notes"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &listNotesResp2)
	assert.NoError(t, err)
	assert.Equal(t, 0, listNotesResp2.Code)
	assert.Len(t, listNotesResp2.Data.Notes, 0) // Expect no active notes after claiming

	// New Step: Get Ephemeral Note by ID after claimed
	req, _ = http.NewRequest("GET", fmt.Sprintf("/ephemeral_note/%s", noteID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	err = json.Unmarshal(w.Body.Bytes(), &getNoteResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, getNoteResp.Code)
	assert.Equal(t, noteID, getNoteResp.Data.ID)
	assert.Equal(t, chainID, getNoteResp.Data.ChainID)
	assert.Equal(t, senderAddress.Hex(), getNoteResp.Data.From)
	assert.Equal(t, usdcAddress.Hex(), getNoteResp.Data.TokenAddress)
	assert.Equal(t, amount.String(), getNoteResp.Data.Amount)
	assert.Equal(t, "0.00001", getNoteResp.Data.DisplayAmount) // Assuming 6 decimals for USDC
	assert.Equal(t, "USDC", getNoteResp.Data.Symbol)
	assert.Equal(t, txHash.Hex(), getNoteResp.Data.DepositTxHash)
	assert.NotNil(t, getNoteResp.Data.ClaimTxHash)
	assert.Equal(t, ephOwnerAddress, getNoteResp.Data.EphemeralOwner)
	assert.Equal(t, string(domain.EphemeralNoteStatusClaimed), getNoteResp.Data.Status)
	assert.Equal(t, uid, getNoteResp.Data.SenderUID)
	assert.Equal(t, userData.Avatar.AvatarURL, getNoteResp.Data.SenderAvatar)
	assert.Equal(t, userData.DisplayName, getNoteResp.Data.SenderDisplayName)

	// Step 5: Release Ephemeral Owner
	releaseOwnerBody := releaseEphemeralOwnerReq{
		ChainID: chainID,
		Address: ephOwnerAddress,
	}
	bodyBytes, _ = json.Marshal(releaseOwnerBody)
	req, _ = http.NewRequest("POST", "/ephemeral_owner/release", bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	if w.Code != http.StatusOK {
		t.Errorf("Release Ephemeral Owner response: %s\n", w.Body.String())
	}

	var releaseOwnerResp struct {
		Code int         `json:"code"`
		Data interface{} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &releaseOwnerResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, releaseOwnerResp.Code)
	assert.Nil(t, releaseOwnerResp.Data)

	// Additional Step: Try to get the released ephemeral owner
	req, _ = http.NewRequest("GET", fmt.Sprintf("/ephemeral_owner?chain_id=%s", chainID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var getReleasedOwnerResp struct {
		Code int `json:"code"`
		Data struct {
			Address string `json:"address"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &getReleasedOwnerResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, getReleasedOwnerResp.Code)
	assert.Equal(t, ephOwnerAddress, getReleasedOwnerResp.Data.Address) // Should be the same address

	// New Step: List All Ephemeral Notes after claiming
	req, _ = http.NewRequest("GET", "/ephemeral_notes", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var listAllNotesResp struct {
		Code int `json:"code"`
		Data struct {
			Notes []noteData `json:"notes"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &listAllNotesResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, listAllNotesResp.Code)
	// find the note with same noteID
	note, found := lo.Find(listAllNotesResp.Data.Notes, func(n noteData) bool {
		return n.ID == noteID
	})
	assert.True(t, found, "Note with the specified ID not found")

	assert.Equal(t, chainID, note.ChainID)
	assert.Equal(t, senderAddress.Hex(), note.From)
	assert.Equal(t, usdcAddress.Hex(), note.TokenAddress)
	assert.Equal(t, amount.String(), note.Amount)
	assert.Equal(t, "0.00001", note.DisplayAmount)
	assert.Equal(t, "USDC", note.Symbol)
	assert.Equal(t, txHash.Hex(), note.DepositTxHash)
	assert.Equal(t, ephOwnerAddress, note.EphemeralOwner)
	assert.Equal(t, string(domain.EphemeralNoteStatusClaimed), note.Status)
	assert.Equal(t, uid, note.SenderUID)
	assert.Equal(t, userData.Avatar.AvatarURL, note.SenderAvatar)
	assert.Equal(t, userData.DisplayName, note.SenderDisplayName)
	assert.Equal(t, 100000.0, note.TokenPrice)
	assert.Equal(t, "https://example.com/usdc.png", note.TokenImage)
}

func testTronEphemeralNote(t *testing.T, r *gin.Engine, uid string, userData domain.UserData) {
	// Step 1: Get Ephemeral Owner
	chainID := "shasta"
	req, _ := http.NewRequest("GET", fmt.Sprintf("/ephemeral_owner?chain_id=%s", chainID), nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var getOwnerResp struct {
		Code int `json:"code"`
		Data struct {
			Address string `json:"address"`
		} `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &getOwnerResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, getOwnerResp.Code)
	assert.NotEmpty(t, getOwnerResp.Data.Address)
	ephOwnerAddress := getOwnerResp.Data.Address
	t.Logf("Ephemeral owner address: %s\n", ephOwnerAddress)

	// Step 2: Send a transaction on Tron for depositing USDT
	senderPrivKey, _ := crypto.HexToECDSA("df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3")
	senderAddress := crypto.PubkeyToAddress(senderPrivKey.PublicKey)
	t.Logf("Sender address: %s\n", senderAddress.Hex())
	usdtAddress := "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs" // Tron USDT address
	amount := big.NewInt(10)                            // 0.00001 USDT (assuming 6 decimals)

	// Connect to Tron network (you'll need to implement this part)
	client, err := tron.GetClient(chainID)
	require.NoError(t, err)
	contractAddress := config.GetStringMap("EPHEMERAL_NOTES_CONTRACTS")[chainID]

	// Create note on the real contract
	createNoteParams := []map[string]interface{}{
		{"address": ephOwnerAddress},
		{"address": usdtAddress},
		{"uint256": amount.String()},
	}
	tx, err := client.TriggerContract(context.Background(), util.EthAddressToTronAddress(senderAddress), contractAddress, "createNote(address,address,uint256)", createNoteParams, int64(50_000_000), 0, "", 0)
	require.NoError(t, err)

	// Broadcast the transaction
	wallet := domain.NewTronWallet("", senderPrivKey)
	createNoteTx, err := wallet.SignTransaction(tx.Transaction)
	require.NoError(t, err)
	txHash, err := client.BroadcastTransaction(createNoteTx)
	require.NoError(t, err)
	t.Logf("Create Note tx hash: %s\n", txHash)

	// Step 3: Create Ephemeral Note
	createNoteBody := createEphemeralNoteReq{
		ChainID: chainID,
		TxHash:  txHash,
	}
	bodyBytes, _ := json.Marshal(createNoteBody)
	req, _ = http.NewRequest("POST", "/ephemeral_note", bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	t.Logf("Create Note response: %s\n", w.Body.String())
	assert.Equal(t, http.StatusOK, w.Code)

	var createNoteResp struct {
		Code int `json:"code"`
		Data struct {
			NoteID string `json:"note_id"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &createNoteResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, createNoteResp.Code)
	assert.NotEmpty(t, createNoteResp.Data.NoteID)
	noteID := createNoteResp.Data.NoteID

	// Step 4: List Active Ephemeral Notes
	req, _ = http.NewRequest("GET", "/ephemeral_notes/active", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var listNotesResp struct {
		Code int `json:"code"`
		Data struct {
			Notes []noteData `json:"notes"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &listNotesResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, listNotesResp.Code)
	note, found := lo.Find(listNotesResp.Data.Notes, func(n noteData) bool {
		return n.ID == noteID
	})
	assert.Truef(t, found, "Note with the specified ID not found")
	assert.Equal(t, chainID, note.ChainID)
	assert.Equal(t, "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd", note.From)
	assert.Equal(t, usdtAddress, note.TokenAddress)
	assert.Equal(t, amount.String(), note.Amount)
	assert.Equal(t, "0.00001", note.DisplayAmount)
	assert.Equal(t, "USDT", note.Symbol)
	assert.Equal(t, txHash, note.DepositTxHash)
	assert.Equal(t, ephOwnerAddress, note.EphemeralOwner)
	assert.Equal(t, uid, note.SenderUID)
	assert.Equal(t, userData.Avatar.AvatarURL, note.SenderAvatar)
	assert.Equal(t, userData.DisplayName, note.SenderDisplayName)
	assert.Equal(t, 100000.0, note.TokenPrice)
	assert.Equal(t, "https://example.com/usdt.png", note.TokenImage)

	// Step 5: Claim Ephemeral Note
	recipient := "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	claimNoteBody := claimEphemeralNoteReq{
		Recipient: recipient,
	}
	bodyBytes, _ = json.Marshal(claimNoteBody)
	req, _ = http.NewRequest("POST", fmt.Sprintf("/ephemeral_note/%s/claim", noteID), bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var claimNoteResp struct {
		Code int `json:"code"`
		Data struct {
			ID                string  `json:"id"`
			Status            string  `json:"status"`
			ClaimTxHash       *string `json:"claim_tx_hash"`
			DisplayAmount     string  `json:"display_amount"`
			Symbol            string  `json:"symbol"`
			SenderUID         string  `json:"sender_uid"`
			SenderAvatar      string  `json:"sender_avatar"`
			SenderDisplayName string  `json:"sender_display_name"`
			TokenPrice        float64 `json:"token_price"`
			TokenImage        string  `json:"token_image"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &claimNoteResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, claimNoteResp.Code)
	assert.Equal(t, noteID, claimNoteResp.Data.ID)
	assert.Equal(t, string(domain.EphemeralNoteStatusClaimed), claimNoteResp.Data.Status)
	assert.NotNil(t, claimNoteResp.Data.ClaimTxHash)
	assert.Equal(t, "0.00001", claimNoteResp.Data.DisplayAmount)
	assert.Equal(t, "USDT", claimNoteResp.Data.Symbol)
	assert.Equal(t, uid, claimNoteResp.Data.SenderUID)
	assert.Equal(t, userData.Avatar.AvatarURL, claimNoteResp.Data.SenderAvatar)
	assert.Equal(t, userData.DisplayName, claimNoteResp.Data.SenderDisplayName)
	assert.Equal(t, 100000.0, claimNoteResp.Data.TokenPrice)
	assert.Equal(t, "https://example.com/usdt.png", claimNoteResp.Data.TokenImage)

	// Step 6: Release Ephemeral Owner
	releaseOwnerBody := releaseEphemeralOwnerReq{
		ChainID: chainID,
		Address: ephOwnerAddress,
	}
	bodyBytes, _ = json.Marshal(releaseOwnerBody)
	req, _ = http.NewRequest("POST", "/ephemeral_owner/release", bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var releaseOwnerResp struct {
		Code int         `json:"code"`
		Data interface{} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &releaseOwnerResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, releaseOwnerResp.Code)
	assert.Nil(t, releaseOwnerResp.Data)

	// Step 7: Verify released ephemeral owner
	req, _ = http.NewRequest("GET", fmt.Sprintf("/ephemeral_owner?chain_id=%s", chainID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var getReleasedOwnerResp struct {
		Code int `json:"code"`
		Data struct {
			Address string `json:"address"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &getReleasedOwnerResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, getReleasedOwnerResp.Code)
	assert.Equal(t, ephOwnerAddress, getReleasedOwnerResp.Data.Address)
}
