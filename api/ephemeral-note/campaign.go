package ephemeralnote

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	ephemeralnote "github.com/kryptogo/kg-wallet-backend/pkg/service/ephemeral-note"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

func GetSendLinkCampaignStatus(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")
	status, err := ephemeralnote.GetSendLinkCampaignStatus(ctx, uid)
	if err != nil {
		response.KGError(c, err)
		return
	}
	response.OK(c, status)
}

type updateEligibilityReq struct {
	ExtraUIDs []string `json:"extra_uids"`
}

func UpdateSendLinkCampaignEligibility(c *gin.Context) {
	ctx := c.Request.Context()
	req := &updateEligibilityReq{}
	kgErr := util.ToGinContextExt(c).BindJson(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	err := ephemeralnote.AddCampaignEligibleUsers(ctx, req.ExtraUIDs...)
	if err != nil {
		response.KGError(c, err)
		return
	}
	response.OK(c, nil)
}

type sendCampaignRewardReq struct {
	SenderUID    string `json:"sender_uid" binding:"required"`
	RecipientUID string `json:"recipient_uid" binding:"required"`
}

func SendCampaignRewardInternal(c *gin.Context) {
	ctx := c.Request.Context()
	req := &sendCampaignRewardReq{}
	kgErr := util.ToGinContextExt(c).BindJson(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	err := ephemeralnote.SendCampaignRewardAndNotification(ctx, req.SenderUID, req.RecipientUID)
	if err != nil {
		response.KGError(c, err)
		return
	}
	response.OK(c, nil)
}

type sendCampaignAnnouncementReq struct {
	MinAppBuildVersion int `json:"min_app_build_version" binding:"required"`
}

func SendCampaignAnnouncement(c *gin.Context) {
	ctx := c.Request.Context()
	req := &sendCampaignAnnouncementReq{}
	kgErr := util.ToGinContextExt(c).BindJson(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	err := ephemeralnote.SendCampaignAnnouncement(ctx, req.MinAppBuildVersion)
	if err != nil {
		response.KGError(c, err)
		return
	}
	response.OK(c, nil)
}
