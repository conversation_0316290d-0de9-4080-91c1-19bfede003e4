package ephemeralnote

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

type configResponse struct {
	Contracts map[string]string `json:"contracts"`
	Fees      map[string]string `json:"fees"`
}

func GetConfig(c *gin.Context) {
	contracts := config.GetStringMap("EPHEMERAL_NOTES_CONTRACTS")
	fees := config.GetStringMap("EPHEMERAL_NOTES_FEES")
	resp := configResponse{
		Contracts: contracts,
		Fees:      fees,
	}
	response.OK(c, resp)
}
