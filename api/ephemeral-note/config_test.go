package ephemeralnote

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetEphemeralNoteConfig(t *testing.T) {
	r, _, _ := setupTestRouter(t)
	t.<PERSON>()

	req, _ := http.NewRequest("GET", "/ephemeral_note/config", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var configResp struct {
		Code int `json:"code"`
		Data struct {
			Contracts map[string]string `json:"contracts"`
			Fees      map[string]string `json:"fees"`
		} `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &configResp)
	assert.NoError(t, err)
	assert.Equal(t, 0, configResp.Code)

	// Check contracts
	assert.NotEmpty(t, configResp.Data.Contracts)
	assert.Contains(t, configResp.Data.Contracts, "sepolia")
	assert.Equal(t, "0x62dd584238497f8EA106Aa08F5c3d5ff44a972D8", configResp.Data.Contracts["sepolia"])

	// Check fees
	assert.NotEmpty(t, configResp.Data.Fees)
	assert.Contains(t, configResp.Data.Fees, "sepolia")
	assert.Equal(t, "0.1", configResp.Data.Fees["sepolia"])
}
