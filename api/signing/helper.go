package signing

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
)

// SetupDeploySmartContract sets up a signing server for testing
func SetupDeploySmartContract(t *testing.T) *http.Server {
	// signing server
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", SignEvmTransaction)

	// create HTTP server for graceful shutdown
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	fmt.Println("Signing server listening on port " + TEST_SIGNING_PORT)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("listen: %s\n", err)
		}
	}()

	time.Sleep(1 * time.Second) // wait for server to start
	return srv
}
