package signing_test

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"math/big"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	signingclient "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/stretchr/testify/assert"
)

// Seed database with necessary data.
func setup(t *testing.T) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
}

type ResponseType struct {
	Code    int               `json:"code"`
	Data    map[string]string `json:"data,omitempty"`
	Message string            `json:"message,omitempty"`
}

type BridgingResponseType struct {
	Code    int               `json:"code"`
	Data    map[string]string `json:"data,omitempty"`
	Message string            `json:"message,omitempty"`
}

type SignResponseType struct {
	Code int `json:"code"`
	Data struct {
		SignedTx string `json:"signed_transaction"`
	} `json:"data,omitempty"`
	Message string `json:"message,omitempty"`
}

func TestGetWalletsByOrganization(t *testing.T) {
	setup(t)

	r := gin.Default()
	r.GET("/v1/wallets", signing.GetWalletsByOrganization)

	// 1. Test for organization_id=1 (success case)
	w := httptest.NewRecorder()
	req, err := http.NewRequest("GET", "/v1/wallets?organization_id=1", nil)
	assert.Nil(t, err)
	r.ServeHTTP(w, req)

	// Check the response status and body for organization_id=1
	assert.Equal(t, http.StatusOK, w.Code)
	var response1 ResponseType
	err = json.Unmarshal(w.Body.Bytes(), &response1)
	assert.Nil(t, err)
	assert.Equal(t, 0, response1.Code)
	assert.Equal(t, "******************************************", response1.Data["evm"])
	assert.Equal(t, "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7", response1.Data["tron"])

	// 2. Test for organization_id=9 (error case: org not found)
	w2 := httptest.NewRecorder()
	req2, err := http.NewRequest("GET", "/v1/wallets?organization_id=9", nil)
	assert.Nil(t, err)
	r.ServeHTTP(w2, req2)

	// Check the response status and body for organization_id=9
	assert.Equal(t, http.StatusNotFound, w2.Code)
	var response2 ResponseType
	err = json.Unmarshal(w2.Body.Bytes(), &response2)
	assert.Nil(t, err)
	assert.Equal(t, 7009, response2.Code)

	// 3. Test for organization_id=3 (error case: wallet not found)
	w3 := httptest.NewRecorder()
	req3, err := http.NewRequest("GET", "/v1/wallets?organization_id=3", nil)
	assert.Nil(t, err)
	r.ServeHTTP(w3, req3)

	// Check the response status and body for organization_id=3
	assert.Equal(t, http.StatusNotFound, w3.Code)
	var response3 ResponseType
	err = json.Unmarshal(w3.Body.Bytes(), &response3)
	assert.Nil(t, err)
	assert.Equal(t, 7012, response3.Code)
}

func TestCreateOrganizationWallets(t *testing.T) {
	// Setup
	setup(t)

	r := gin.Default()
	r.POST("/v1/wallets", signing.CreateOrganizationWallet)
	r.GET("/v1/wallets", signing.GetWalletsByOrganization)

	// Define the expected responses
	type ResponseType struct {
		Code    int               `json:"code"`
		Data    map[string]string `json:"data,omitempty"`
		Message string            `json:"message,omitempty"`
	}

	// 1. Test for organization_id=3 (Successful creation)
	payload1 := `{"organization_id": 3}`
	w1 := httptest.NewRecorder()
	req1, err := http.NewRequest("POST", "/v1/wallets", strings.NewReader(payload1))
	assert.Nil(t, err)
	r.ServeHTTP(w1, req1)
	assert.Equal(t, http.StatusOK, w1.Code)

	// Check the created wallet
	wCheck := httptest.NewRecorder()
	reqCheck, err := http.NewRequest("GET", "/v1/wallets?organization_id=3", nil)
	assert.Nil(t, err)
	r.ServeHTTP(wCheck, reqCheck)
	assert.Equal(t, http.StatusOK, wCheck.Code)
	var responseCheck ResponseType
	err = json.Unmarshal(wCheck.Body.Bytes(), &responseCheck)
	assert.Nil(t, err)
	assert.Equal(t, 0, responseCheck.Code)
	assert.NotEmpty(t, responseCheck.Data["evm"])
	assert.NotEmpty(t, responseCheck.Data["tron"])
	t.Logf("Evm wallet: %s\nTron wallet: %s\n", responseCheck.Data["evm"], responseCheck.Data["tron"])

	// 2. Test for organization_id=1 (Already has wallet)
	payload2 := `{"organization_id": 1}`
	w2 := httptest.NewRecorder()
	req2, err := http.NewRequest("POST", "/v1/wallets", strings.NewReader(payload2))
	assert.Nil(t, err)
	r.ServeHTTP(w2, req2)
	assert.Equal(t, http.StatusBadRequest, w2.Code)
	var response2 ResponseType
	err = json.Unmarshal(w2.Body.Bytes(), &response2)
	assert.Nil(t, err)
	assert.Equal(t, 7010, response2.Code)

	// 3. Test for organization_id=20 (Organization not found)
	payload3 := `{"organization_id": 20}`
	w3 := httptest.NewRecorder()
	req3, err := http.NewRequest("POST", "/v1/wallets", strings.NewReader(payload3))
	assert.Nil(t, err)
	r.ServeHTTP(w3, req3)
	assert.Equal(t, http.StatusNotFound, w3.Code)
	var response3 ResponseType
	err = json.Unmarshal(w3.Body.Bytes(), &response3)
	assert.Nil(t, err)
	assert.Equal(t, 7009, response3.Code)
}

func TestSignEvm(t *testing.T) {
	setup(t)

	r := gin.Default()
	r.POST("/v1/sign/evm", signing.SignEvmTransaction)

	// Define the request payload
	payload := map[string]interface{}{
		"organization_id": 1,
		"chain_id":        1,
		"transaction": map[string]string{
			"to":       "******************************************",
			"gas":      "0x5330",
			"gasPrice": "0x533000000",
			"value":    "0x0",
			"input":    "0xa9059cbb0000000000000000000000002302bb75449cb2de0d8e6e693bcbece657aff5800000000000000000000000000000000000000000000000000000000011cdb861",
			"nonce":    "0x10",
			"v":        "0x0",
			"r":        "0x0",
			"s":        "0x0",
		},
	}

	body, _ := json.Marshal(payload)
	req, err := http.NewRequest("POST", "/v1/sign/evm", bytes.NewBuffer(body))
	assert.Nil(t, err)

	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// Define the expected response structure
	type responseStruct struct {
		Code int `json:"code"`
		Data struct {
			SignedTransaction map[string]string `json:"signed_transaction"`
		} `json:"data"`
	}

	var response responseStruct
	responseBytes, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseBytes, &response))

	// Validate the returned fields
	signedTx := response.Data.SignedTransaction
	assert.Equal(t, 0, response.Code)
	assert.NotNil(t, signedTx["hash"])
	assert.Equal(t, "0x10", signedTx["nonce"])
	assert.Equal(t, "0x5330", signedTx["gas"])
	assert.Equal(t, "0x533000000", signedTx["gasPrice"])
	assert.Equal(t, "******************************************", signedTx["to"])
	assert.Equal(t, "0x0", signedTx["value"])
	assert.Equal(t, "0xa9059cbb0000000000000000000000002302bb75449cb2de0d8e6e693bcbece657aff5800000000000000000000000000000000000000000000000000000000011cdb861", signedTx["input"])
	assert.Equal(t, "0x26", signedTx["v"])
	assert.Equal(t, "0x2d523c28fba67703ea5dff9982a77968f26633c341a55f96f1ebc60df1f2f7b8", signedTx["r"])
	assert.Equal(t, "0x3b480a977d4863d75ddae1d4cbc44ea96d79e079562b1d68bd1ed4f48a1ae6d4", signedTx["s"])

	// Validate the signature
	responseStr, err := json.Marshal(response.Data.SignedTransaction)
	assert.Nil(t, err)
	tx := &types.Transaction{}
	err = json.Unmarshal(responseStr, tx)
	assert.Nil(t, err)
	recoveredAddress, err := SenderAddress(tx)
	assert.NoError(t, err)
	assert.Equal(t, "******************************************", recoveredAddress.Hex())
}

func TestSignTron(t *testing.T) {
	setup(t)

	r := gin.Default()
	r.POST("/v1/sign/tron", signing.SignTronTransaction)

	// Define request and response type
	type parameterType struct {
		TypeURL string `json:"type_url"`
		Value   string `json:"value"`
	}
	type contractType struct {
		Type      int           `json:"type"`
		Parameter parameterType `json:"parameter"`
	}
	type rawDataType struct {
		RefBlockBytes string         `json:"ref_block_bytes"`
		RefBlockHash  string         `json:"ref_block_hash"`
		Expiration    int64          `json:"expiration"`
		Contract      []contractType `json:"contract"`
		Timestamp     int64          `json:"timestamp"`
		FeeLimit      int64          `json:"fee_limit"`
	}

	type transactionType struct {
		RawData   rawDataType `json:"raw_data"`
		Signature []string    `json:"signature,omitempty"`
	}

	type transactionReqType struct {
		OrganizationID int             `json:"organization_id"`
		Transaction    transactionType `json:"transaction"`
	}

	// Request body
	requestBody := transactionReqType{
		OrganizationID: 1,
		Transaction: transactionType{
			RawData: rawDataType{
				RefBlockBytes: "CXg=",
				RefBlockHash:  "Z3o0Fm2VogU=",
				Expiration:    1689235044000,
				Contract: []contractType{
					{
						Type: 31,
						Parameter: parameterType{
							TypeURL: "type.googleapis.com/protocol.TriggerSmartContract",
							Value:   "ChVBupz3qWLlG3DTL+0KMm+XCwBueC0SFUG2w0vH4DoNUm9Dhe6kJA07N7QHbyJEqQWcuwAAAAAAAAAAAAAAAGOzDOSj4/H3DkpmWkh6htqnYOccAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPQkA=",
						},
					},
				},
				Timestamp: 1689234985172,
				FeeLimit:  100000000,
			},
		},
	}

	requestBodyStr, _ := json.Marshal(requestBody)
	req, err := http.NewRequest("POST", "/v1/sign/tron", strings.NewReader(string(requestBodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var response struct {
		Code int `json:"code"`
		Data struct {
			SignedTx transactionType `json:"signed_transaction"`
		} `json:"data"`
	}

	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))

	// Validate the response
	assert.Equal(t, 0, response.Code)

	// Compare request and response fields
	signedTx := response.Data.SignedTx
	assert.Equal(t, requestBody.Transaction.RawData, signedTx.RawData)

	// Additional validation for new or modified fields
	assert.NotNil(t, signedTx.Signature)
	assert.Greater(t, len(signedTx.Signature), 0) // Ensure there is at least one signature
	assert.Equal(t, "6M4ZKfuptzg7KZT62O6OW4AfY5eOuMd+QU8GYrRpY0lGK4wWOoOF6IP51/DyZiN2yGdDagJRqfBjABwkGm0yewA=", signedTx.Signature[0])
}

func TestGetAndSetAlertThreshold(t *testing.T) {
	setup(t)

	r := gin.Default()
	r.GET("/v1/alert_threshold", signing.GetAlertThreshold)
	r.POST("/v1/alert_threshold", signing.UpdateAlertThreshold)

	// 1. GET request to retrieve threshold
	req, err := http.NewRequest("GET", "/v1/alert_threshold?organization_id=1", nil)
	assert.NoError(t, err)

	resp := httptest.NewRecorder()
	r.ServeHTTP(resp, req)

	assert.Equal(t, http.StatusOK, resp.Code)

	var response map[string]interface{}
	err = json.Unmarshal(resp.Body.Bytes(), &response)
	assert.Nil(t, err)

	// Assert the response
	assert.Equal(t, float64(0), response["code"])
	assert.Equal(t, float64(100.0), response["data"].(map[string]interface{})["threshold_usd"])

	// 2. POST request to update the threshold
	payload := map[string]interface{}{
		"organization_id": 1,
		"threshold_usd":   100000,
	}
	payloadBytes, err := json.Marshal(payload)
	assert.NoError(t, err)

	req, err = http.NewRequest("POST", "/v1/alert_threshold", bytes.NewBuffer(payloadBytes))
	assert.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	resp = httptest.NewRecorder()
	r.ServeHTTP(resp, req)
	assert.Equal(t, http.StatusOK, resp.Code)

	var postResponse map[string]interface{}
	err = json.Unmarshal(resp.Body.Bytes(), &postResponse)
	assert.NoError(t, err)
	assert.Equal(t, float64(0), postResponse["code"])

	// 3. GET request to retrieve the updated threshold
	req, err = http.NewRequest("GET", "/v1/alert_threshold?organization_id=1", nil)
	assert.NoError(t, err)

	resp = httptest.NewRecorder()
	r.ServeHTTP(resp, req)

	assert.Equal(t, http.StatusOK, resp.Code)

	var getResponse map[string]interface{}
	err = json.Unmarshal(resp.Body.Bytes(), &getResponse)
	assert.NoError(t, err)

	// 3. Assert that the returned threshold matches the updated value
	data, ok := getResponse["data"].(map[string]interface{})
	assert.True(t, ok)
	assert.Equal(t, float64(100000), data["threshold_usd"])
}

func TestGetBridgingAddress(t *testing.T) {
	ctx := context.Background()
	signingservertest.Setup(t)

	saltBytes := [32]byte{}
	saltBytes[31] = 1
	addresses, err := signingclient.GetBridgingAddressesBySalt(ctx, 1, saltBytes)
	assert.Nil(t, err)
	assert.Equal(t, "0x9772D6B876D9e1E837ab1Ec1c54Ec7928Df5F3b2", addresses.EvmAddress)
}

func TestSignEvmTransactionWithSalt(t *testing.T) {
	ctx := context.Background()
	signingservertest.Setup(t)

	saltBytes := [32]byte{}
	saltBytes[31] = 1

	addresses, err := signingclient.GetBridgingAddressesBySalt(ctx, 1, saltBytes)
	assert.Nil(t, err)
	address := addresses.EvmAddress
	tx := types.NewTransaction(0, common.HexToAddress("0x0001"), big.NewInt(1000000000000000000), 21000, big.NewInt(20000000000), nil)
	signedTx, err := signingclient.SignEvmTransactionWithSalt(ctx, 1, "eth", tx, saltBytes)
	assert.Nil(t, err)
	signer, err := SenderAddress(signedTx)
	assert.Nil(t, err)
	assert.Equal(t, address, signer.String())

	// sign with another salt and the signer should be different
	saltBytes2 := [32]byte{}
	saltBytes2[31] = 2
	signedTx2, err := signingclient.SignEvmTransactionWithSalt(ctx, 1, "eth", tx, saltBytes2)
	assert.Nil(t, err)
	signer2, err := SenderAddress(signedTx2)
	assert.Nil(t, err)
	assert.NotEqual(t, address, signer2.String())
}

func SenderAddress(tx *types.Transaction) (common.Address, error) {
	signer := types.NewEIP155Signer(tx.ChainId())
	addr, err := types.Sender(signer, tx)
	if err != nil {
		return common.Address{}, err
	}
	return addr, nil
}
