package signing

import (
	"encoding/hex"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// GetAddressesBySalt returns bridging addresses for org and salt
func GetAddressesBySalt(c *gin.Context) {
	// Parse org_id from query
	orgIDStr := c.Query("org_id")
	orgID, err := strconv.Atoi(orgIDStr)
	if err != nil {
		kglog.Warning("invalid organization id")
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid organization id")
		return
	}

	// Parse salt from query
	saltHex := c.Query("salt")
	saltBytes, err := hex.DecodeString(saltHex)
	if err != nil || len(saltBytes) != 32 {
		kglog.Warning("invalid salt")
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid salt")
		return
	}
	var salt [32]byte
	copy(salt[:], saltBytes)

	// Get bridging addresses
	addresses, kgErr := server.GetBridgingAddressesBySalt(c.Request.Context(), orgID, salt)
	if kgErr != nil {
		if kgErr.HttpStatus >= 500 {
			kglog.WarningfCtx(c.Request.Context(), "GetBridgingAddresses failed: %s", kgErr.String())
		}
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, addresses)
}

// SignEvmTransactionWithSalt signs an EVM transaction using a bridging address
func SignEvmTransactionWithSalt(c *gin.Context) {
	var req struct {
		*server.SignEvmReq
		Salt string `json:"salt" binding:"required"` // hex encoded salt
	}

	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Parse salt
	saltBytes, err := hex.DecodeString(req.Salt)
	if err != nil || len(saltBytes) != 32 {
		kglog.Warning("invalid salt")
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid salt")
		return
	}
	var salt [32]byte
	copy(salt[:], saltBytes)

	// Sign transaction
	res, kgErr := server.SignEvmTransactionWithSalt(c.Request.Context(), req.SignEvmReq, salt)
	if kgErr != nil {
		if kgErr.HttpStatus >= 500 {
			kglog.WarningfCtx(c.Request.Context(), "SignEvmTransactionWithSalt failed: %s", kgErr.String())
		}
		response.KGError(c, kgErr)
		return
	}

	// Save audit log
	kgErr = server.SaveAuditLog(c.Request.Context(), c.ClientIP(), c.GetHeader("Authorization"), util.ToJSONString(req))
	if kgErr != nil {
		kglog.Warningf("[SignEvmTransactionWithSalt] SaveAuditLog failed: %s", kgErr.String())
	}

	response.OK(c, res)
}
