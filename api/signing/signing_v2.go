package signing

import (
	"encoding/hex"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	signingServer "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// GetWalletsByOrganization .
func GetWalletsByOrganization(c *gin.Context) {
	orgIDStr := c.DefaultQuery("organization_id", "")
	orgID, err := strconv.Atoi(orgIDStr)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid organization ID")
		return
	}
	addrs, kgErr := signingServer.GetWalletsByOrganization(c.Request.Context(), orgID)
	if kgErr != nil {
		if kgErr.HttpStatus >= 500 {
			kglog.WarningfCtx(c.Request.Context(), "GetWalletsByOrganization, GetWalletsByOrganization failed: %s", kgErr.String())
		}
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, addrs)
}

// CreateOrganizationWallet .
func CreateOrganizationWallet(ctx *gin.Context) {
	params := &struct {
		OrgID int `json:"organization_id" binding:"required"`
	}{}
	kgErr := util.ToGinContextExt(ctx).BindJson(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	wallets, kgErr := signingServer.CreateOrganizationWallets(ctx.Request.Context(), params.OrgID)
	if kgErr != nil {
		if kgErr.HttpStatus >= 500 {
			kglog.WarningfCtx(ctx.Request.Context(), "CreateOrganizationWallet, CreateOrganizationWallets failed: %s", kgErr.String())
		}
		response.KGError(ctx, kgErr)
		return
	}
	response.OK(ctx, wallets)
}

// AuthorizeSigningServer sign server authorization middleware
func AuthorizeSigningServer(c *gin.Context) {
	token := c.GetHeader("KG-SIGNING-TOKEN")
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeKg)
	claimsI, errCode, err := strategy.Parse(token)
	if err != nil {
		response.UnauthorizedWithMsg(c, errCode, err.Error())
		return
	}
	claims := claimsI.(*jwtservice.KGTokenClaims)
	if claims.Subject != "kg-backend-api-server" {
		response.UnauthorizedWithMsg(c, code.KgTokenInvalid, "KG-SIGNING-TOKEN is invalid")
		return
	}
	// pass
	c.Next()
}

// SignEvmTransaction .
func SignEvmTransaction(ctx *gin.Context) {
	signReq := &signingServer.SignEvmReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(signReq)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// sign tx
	res, kgErr := signingServer.SignEvmTransaction(ctx.Request.Context(), signReq)
	if kgErr != nil {
		if kgErr.HttpStatus >= 500 {
			kglog.WarningfCtx(ctx.Request.Context(), "SignEvmTransaction failed: %s", kgErr.String())
		}
		response.KGError(ctx, kgErr)
		return
	}

	// save audit log
	kgErr = signingServer.SaveAuditLog(ctx.Request.Context(), ctx.ClientIP(), ctx.GetHeader("Authorization"), util.ToJSONString(signReq))
	if kgErr != nil {
		kglog.Warningf("[SignEvmTransaction] SaveAuditLog failed: %s", kgErr.String())
	}
	response.OK(ctx, res)
}

func SignEvmMessage(ctx *gin.Context) {
	signReq := &signingServer.SignEvmMessageReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(signReq)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	res, kgErr := signingServer.SignEvmMessage(ctx.Request.Context(), signReq)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	response.OK(ctx, res)
}

// SignTronTransaction .
func SignTronTransaction(ctx *gin.Context) {
	signReq := &signingServer.SignTronReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(signReq)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// sign tx
	res, kgErr := signingServer.SignTronTransaction(ctx.Request.Context(), signReq)
	if kgErr != nil {
		if kgErr.HttpStatus >= 500 {
			kglog.WarningfCtx(ctx.Request.Context(), "SignTronTransaction failed: %s", kgErr.String())
		}
		response.KGError(ctx, kgErr)
		return
	}

	// save audit log
	kgErr = signingServer.SaveAuditLog(ctx.Request.Context(), ctx.ClientIP(), ctx.GetHeader("Authorization"), util.ToJSONString(signReq))
	if kgErr != nil {
		kglog.Warningf("[SignTronTransaction] SaveAuditLog failed: %s", kgErr.String())
	}

	response.OK(ctx, res)
}

func SignSolanaTransaction(c *gin.Context) {
	ctx := c.Request.Context()

	signReq := &signingServer.SignSolanaReq{}
	kgErr := util.ToGinContextExt(c).BindJson(signReq)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	res, kgErr := signingServer.SignSolanaTransaction(ctx, signReq)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// save audit log
	kgErr = signingServer.SaveAuditLog(ctx, c.ClientIP(), c.GetHeader("Authorization"), util.ToJSONString(signReq))
	if kgErr != nil {
		kglog.Warningf("[SignSolanaTransaction] SaveAuditLog failed: %s", kgErr.String())
	}

	response.OK(c, res)
}

// GetAlertThreshold .
func GetAlertThreshold(c *gin.Context) {
	orgIDStr := c.DefaultQuery("organization_id", "")
	orgID, err := strconv.Atoi(orgIDStr)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid organization ID")
		return
	}

	threshold, kgErr := signingServer.GetAlertThreshold(c.Request.Context(), orgID)
	if kgErr != nil {
		if kgErr.HttpStatus >= 500 {
			kglog.WarningfCtx(c.Request.Context(), "GetAlertThreshold failed: %s", kgErr.String())
		}
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, map[string]interface{}{
		"threshold_usd": threshold,
	})
}

// UpdateAlertThreshold .
func UpdateAlertThreshold(ctx *gin.Context) {
	params := &struct {
		OrgID        int     `json:"organization_id" binding:"required"`
		ThresholdUSD float64 `json:"threshold_usd" binding:"required"`
	}{}
	kgErr := util.ToGinContextExt(ctx).BindJson(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	kgErr = signingServer.UpdateAlertThreshold(ctx.Request.Context(), params.OrgID, params.ThresholdUSD)
	if kgErr != nil {
		if kgErr.HttpStatus >= 500 {
			kglog.WarningfCtx(ctx.Request.Context(), "UpdateAlertThreshold failed: %s", kgErr.String())
		}
		response.KGError(ctx, kgErr)
		return
	}
	response.OK(ctx, nil)
}

// GetDepositAddress returns deposit addresses for a user
func GetDepositAddress(c *gin.Context) {
	uid := c.Query("uid")
	if uid == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "UID is required")
		return
	}

	depositAddresses, kgErr := signingServer.GetDepositAddresses(c.Request.Context(), uid)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, depositAddresses)
}

// GetDepositAddressesBySalt returns deposit addresses for a given salt
func GetDepositAddressesBySalt(c *gin.Context) {
	var req struct {
		Salt string `form:"salt" binding:"required"`
	}
	if err := c.ShouldBindQuery(&req); err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Salt is required")
		return
	}

	// Decode salt from hex
	saltBytes, err := hex.DecodeString(req.Salt)
	if err != nil || len(saltBytes) != 32 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid salt format")
		return
	}

	var salt [32]byte
	copy(salt[:], saltBytes)

	depositAddresses, kgErr := signingServer.GetDepositAddressesBySalt(c.Request.Context(), salt)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, depositAddresses)
}
