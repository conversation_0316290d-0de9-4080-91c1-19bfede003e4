package gasfaucet

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	gasfaucet "github.com/kryptogo/kg-wallet-backend/pkg/service/gas-faucet"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

type createGasFaucetRes struct {
	Code int `json:"code"`
}

func TestCreate(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY"})

	clientID := "********************************"
	r, _ := setup(t, clientID)
	wallet := "******************************************"

	body := map[string]interface{}{"chain_id": "arb", "wallet_address": wallet, "gas_amount": 100000}
	bodyStr, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", "/gas_faucet", bytes.NewBuffer(bodyStr))
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ := io.ReadAll(w.Body)
	t.Logf("response: %v", string(responseStr))
}

func TestCreateFailed(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY"})

	clientID := "********************************"
	r, uid := setup(t, clientID)
	wallet := "******************************************"

	assert.Nil(t, rdb.Get().Create(&model.StudioOrganizationGasFaucet{
		OrganizationID: 1,
		UID:            uid,
		ChainID:        "arb",
		ReceiveWallet:  wallet,
		GasAmount:      100000,
		CreatedAt:      time.Now(),
		Status:         util.Ptr(domain.TxStatusSuccess),
		TxHash:         util.Ptr("0x1234"),
		TotalCost:      util.Ptr(decimal.NewFromFloat(1)),
		TotalCostUsd:   util.Ptr(decimal.NewFromFloat(2000)),
	}).Error)

	// user daily limit exceeded
	body := map[string]interface{}{"chain_id": "arb", "wallet_address": wallet, "gas_amount": 100000}
	bodyStr, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", "/gas_faucet", bytes.NewBuffer(bodyStr))
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
	responseStr, _ := io.ReadAll(w.Body)
	t.Logf("response 1: %v", string(responseStr))
	resp := createGasFaucetRes{}
	assert.Nil(t, json.Unmarshal(responseStr, &resp))
	assert.Equal(t, 13012, resp.Code)

	// organization daily limit exceeded
	wallet = "******************************************"
	body = map[string]interface{}{"chain_id": "arb", "wallet_address": wallet, "gas_amount": 100000}
	bodyStr, _ = json.Marshal(body)
	req, _ = http.NewRequest("POST", "/gas_faucet", bytes.NewBuffer(bodyStr))
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	t.Logf("response 2: %v", string(responseStr))
	resp = createGasFaucetRes{}
	assert.Nil(t, json.Unmarshal(responseStr, &resp))
	assert.Equal(t, 13012, resp.Code)

	// user doesn't own the wallet
	wallet = "******************************************"
	body = map[string]interface{}{"chain_id": "arb", "wallet_address": wallet, "gas_amount": 100000}
	bodyStr, _ = json.Marshal(body)
	req, _ = http.NewRequest("POST", "/gas_faucet", bytes.NewBuffer(bodyStr))
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	t.Logf("response 3: %v", string(responseStr))
	resp = createGasFaucetRes{}
	assert.Nil(t, json.Unmarshal(responseStr, &resp))
	assert.Equal(t, 13014, resp.Code)
}

func setup(t *testing.T, clientID string) (*gin.Engine, string) {
	// setup rdb
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())

	// init services
	application.Init(rdb.GormRepo())
	gasfaucet.Init(repo.Unified())
	alchemyapi.InitDefault()
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})

	// setup firebase
	users, uid, _, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	r := gin.Default()
	r.POST("/gas_faucet", auth.MockAuthorize(uid), auth.MockClientID(clientID), Create)

	// signing server
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", signing.SignEvmTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()

	return r, uid
}
