package gasfaucet

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	gasfaucet "github.com/kryptogo/kg-wallet-backend/pkg/service/gas-faucet"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type createReq struct {
	ChainID       string `json:"chain_id" binding:"required"`
	WalletAddress string `json:"wallet_address" binding:"required"`
	GasAmount     int64  `json:"gas_amount" binding:"required,gt=0"`
}

// Create a gas faucet request
func Create(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "gasfaucet.Create")
	defer span.End()

	uid := c.GetString("uid")

	params := &createReq{}

	if kgErr := util.ToGinContextExt(c).BindJson(params); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if kgErr := gasfaucet.Create(ctx, &domain.GasFaucet{
		OrgID:         orgID,
		UID:           uid,
		ChainID:       params.ChainID,
		ReceiveWallet: params.WalletAddress,
		GasAmount:     params.GasAmount,
	}); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}
