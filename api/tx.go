package api

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/user"
)

type txlistsResp struct {
	Code   int                    `json:"code"`
	Data   []*tx.VTxList          `json:"data"`
	Paging *rdb.TokenPagingParams `json:"paging"`
}

// TxLists return tx list
func TxLists(c *gin.Context) {
	params := &rdb.TxListParams{}
	kgErr := util.ToGinContextExt(c).BindQuery(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	params.UID = auth.GetUID(c)
	params.ClientID = auth.GetClientID(c)
	txlists, paging, retCode, err := tx.Get().Lists(c.Request.Context(), params)
	if err != nil {
		if err == code.ErrUserNotFound {
			response.BadRequestWithMsg(c, retCode, err.Error())
			return
		}
		response.InternalServerErrorWithMsg(c, retCode, err.Error())
		return
	}

	resp := new(txlistsResp)
	resp.Code = retCode
	resp.Data = txlists
	resp.Paging = paging
	c.JSON(http.StatusOK, resp)
}

// UpdateTxListReq is the request body for UpdateTxList
type UpdateTxListReq struct {
	ExcludeObserver bool `json:"exclude_observer"`
}

// UpdateTxList update tx list
func UpdateTxList(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.UpdateTxList")
	defer span.End()

	var req UpdateTxListReq
	if kgErr := util.ToGinContextExt(c).BindRawBody(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	uid := auth.GetUID(c)
	tx.Get().UpdateTxByUIDAndWait(ctx, uid, req.ExcludeObserver)

	c.JSON(http.StatusOK, gin.H{"code": 0})
}

type txDetailResp struct {
	Code int           `json:"code"`
	Data *tx.VTxDetail `json:"data"`
}

// TxDetail return tx detail
func TxDetail(ctx *gin.Context) {
	params := &tx.DetailParams{}
	kgErr := util.ToGinContextExt(ctx).BindUri(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	clientID := oauth.ClientID(ctx)
	user, _ := user.GetByUID(ctx.Request.Context(), auth.GetUID(ctx), clientID, false, &domain.UserPreloads{
		WithLocale: true,
	})
	if user == nil {
		response.BadRequestWithMsg(ctx, 400, "user not found")
		return
	}
	params.Locale = user.GetLocale(clientID)
	txDetail, code, err := tx.Detail(ctx.Request.Context(), params)
	if err != nil {
		if code > 0 {
			response.InternalServerErrorWithMsg(ctx, code, err.Error())
			return
		} else {
			response.AcceptedWithMsg(ctx, code, err.Error())
			return
		}
	}

	resp := new(txDetailResp)
	resp.Code = code
	resp.Data = txDetail
	ctx.JSON(http.StatusOK, resp)
}
