package payment

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/callback"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func setupCallbackAPITest(t *testing.T) (*gin.Engine, *domain.MockCallbackLogRepo) {
	gin.SetMode(gin.TestMode)

	ctrl := gomock.NewController(t)
	mockRepo := domain.NewMockCallbackLogRepo(ctrl)

	// Initialize callback service with mock repo
	callback.Init(mockRepo)

	server := gin.Default()

	// Add pagination middleware
	server.Use(middleware.Pagination())

	// Setup routes with studio organization structure
	server.GET("/studio/organization/:orgID/payment/callbacks", func(c *gin.Context) {
		// Mock org_id in context for testing
		c.Set("org_id", 1)
		c.Set("client_id", "test-client-id")
		GetCallbackLogs(c)
	})

	server.GET("/studio/organization/:orgID/payment/callbacks/:id", func(c *gin.Context) {
		// Mock org_id in context for testing
		c.Set("org_id", 1)
		c.Set("client_id", "test-client-id")
		GetCallbackLogByID(c)
	})

	server.POST("/studio/organization/:orgID/payment/callbacks/test", func(c *gin.Context) {
		// Mock org_id in context for testing
		c.Set("org_id", 1)
		c.Set("client_id", "test-client-id")
		TestCallback(c)
	})

	return server, mockRepo
}

func TestGetCallbackLogsAPI(t *testing.T) {
	server, mockRepo := setupCallbackAPITest(t)
	now := time.Now().UTC()

	t.Run("GetCallbackLogs_Success", func(t *testing.T) {
		expectedLogs := []*domain.CallbackLog{
			{
				ID:              "log-1",
				PaymentIntentID: util.Ptr("payment-intent-1"),
				URL:             "https://example.com/webhook",
				Type:            domain.CallbackTypePayment,
				Status:          domain.CallbackStatusSuccess,
				StatusCode:      util.Ptr(200),
				CallbackPayload: `{"event":"payment_completed"}`,
				Duration:        250 * time.Millisecond,
				OrgID:           util.Ptr(1),
				ClientID:        util.Ptr("test-client-id"),
				CreatedAt:       now,
			},
		}

		mockRepo.EXPECT().
			GetCallbackLogs(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, filter domain.CallbackLogFilter) ([]*domain.CallbackLog, int, error) {
				// Verify organization filtering is applied
				assert.NotNil(t, filter.OrgID)
				assert.Equal(t, 1, *filter.OrgID)
				// Verify default pagination
				assert.Equal(t, 1, filter.Page)
				assert.Equal(t, 10, filter.PageSize)
				return expectedLogs, 1, nil
			})

		req := httptest.NewRequest("GET", "/studio/organization/1/payment/callbacks", nil)
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(0), response["code"])

		data, ok := response["data"].([]interface{})
		require.True(t, ok)
		assert.Len(t, data, 1)

		// Verify the returned data includes new fields
		logData := data[0].(map[string]interface{})
		assert.Equal(t, "log-1", logData["id"])
		assert.Equal(t, "payment-intent-1", logData["payment_intent_id"])
		assert.Equal(t, float64(1), logData["org_id"])
		assert.Equal(t, "test-client-id", logData["client_id"])

		paging, ok := response["paging"].(map[string]interface{})
		require.True(t, ok)
		assert.Equal(t, float64(1), paging["page_number"])
		assert.Equal(t, float64(10), paging["page_size"])
		assert.Equal(t, float64(1), paging["total_count"])
	})

	t.Run("GetCallbackLogs_WithFilters", func(t *testing.T) {
		mockRepo.EXPECT().
			GetCallbackLogs(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, filter domain.CallbackLogFilter) ([]*domain.CallbackLog, int, error) {
				// Verify filters are applied
				assert.NotNil(t, filter.OrgID)
				assert.Equal(t, 1, *filter.OrgID)
				assert.NotNil(t, filter.PaymentIntentID)
				assert.Equal(t, "payment-123", *filter.PaymentIntentID)
				assert.Len(t, filter.Type, 1)
				assert.Equal(t, domain.CallbackTypePayment, filter.Type[0])
				assert.Len(t, filter.Status, 1)
				assert.Equal(t, domain.CallbackStatusSuccess, filter.Status[0])
				return []*domain.CallbackLog{}, 0, nil
			})

		req := httptest.NewRequest("GET", "/studio/organization/1/payment/callbacks?payment_intent_id=payment-123&type=payment&status=success", nil)
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("GetCallbackLogs_WithDateRange", func(t *testing.T) {
		fromDate := "2024-01-01T00:00:00Z"
		toDate := "2024-01-02T00:00:00Z"

		mockRepo.EXPECT().
			GetCallbackLogs(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, filter domain.CallbackLogFilter) ([]*domain.CallbackLog, int, error) {
				// Verify date filters are applied
				assert.NotNil(t, filter.OrgID)
				assert.Equal(t, 1, *filter.OrgID)
				assert.NotNil(t, filter.FromDate)
				assert.NotNil(t, filter.ToDate)
				return []*domain.CallbackLog{}, 0, nil
			})

		req := httptest.NewRequest("GET", "/studio/organization/1/payment/callbacks?from_date="+fromDate+"&to_date="+toDate, nil)
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("GetCallbackLogs_ServiceError", func(t *testing.T) {
		mockRepo.EXPECT().
			GetCallbackLogs(gomock.Any(), gomock.Any()).
			Return(nil, 0, assert.AnError)

		req := httptest.NewRequest("GET", "/studio/organization/1/payment/callbacks", nil)
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(500), response["status"])
		assert.Contains(t, response["message"], "Failed to get callback logs")
	})

	t.Run("GetCallbackLogs_MissingOrgID", func(t *testing.T) {
		// Test with handler that doesn't set org_id
		server := gin.Default()
		server.Use(middleware.Pagination())
		server.GET("/studio/organization/:orgID/payment/callbacks", GetCallbackLogs)

		req := httptest.NewRequest("GET", "/studio/organization/1/payment/callbacks", nil)
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(400), response["status"])
		assert.Contains(t, response["message"], "Organization ID is required")
	})
}

func TestGetCallbackLogByIDAPI(t *testing.T) {
	server, mockRepo := setupCallbackAPITest(t)
	now := time.Now().UTC()

	t.Run("GetCallbackLogByID_Success", func(t *testing.T) {
		logID := "test-log-id"
		expectedLog := &domain.CallbackLog{
			ID:              logID,
			PaymentIntentID: util.Ptr("payment-intent-1"),
			URL:             "https://example.com/webhook",
			Type:            domain.CallbackTypePayment,
			Status:          domain.CallbackStatusSuccess,
			StatusCode:      util.Ptr(200),
			CallbackPayload: `{"event":"payment_completed"}`,
			Duration:        250 * time.Millisecond,
			OrgID:           util.Ptr(1),
			ClientID:        util.Ptr("test-client-id"),
			CreatedAt:       now,
		}

		mockRepo.EXPECT().
			GetCallbackLogByID(gomock.Any(), logID).
			Return(expectedLog, nil)

		req := httptest.NewRequest("GET", "/studio/organization/1/payment/callbacks/"+logID, nil)
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(0), response["code"])

		data, ok := response["data"].(map[string]interface{})
		require.True(t, ok)
		assert.Equal(t, logID, data["id"])
		assert.Equal(t, "payment-intent-1", data["payment_intent_id"])
		assert.Equal(t, "https://example.com/webhook", data["url"])
		assert.Equal(t, float64(1), data["org_id"])
		assert.Equal(t, "test-client-id", data["client_id"])
		assert.Equal(t, "payment", data["type"])
		assert.Equal(t, "success", data["status"])
		assert.Equal(t, float64(200), data["status_code"])
	})

	t.Run("GetCallbackLogByID_NotFound", func(t *testing.T) {
		logID := "non-existent-id"

		mockRepo.EXPECT().
			GetCallbackLogByID(gomock.Any(), logID).
			Return(nil, domain.ErrRecordNotFound)

		req := httptest.NewRequest("GET", "/studio/organization/1/payment/callbacks/"+logID, nil)
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(404), response["status"])
		assert.Contains(t, response["message"], "Callback log not found")
	})

	t.Run("GetCallbackLogByID_ServiceError", func(t *testing.T) {
		logID := "test-log-id"

		mockRepo.EXPECT().
			GetCallbackLogByID(gomock.Any(), logID).
			Return(nil, assert.AnError)

		req := httptest.NewRequest("GET", "/studio/organization/1/payment/callbacks/"+logID, nil)
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(500), response["status"])
		assert.Contains(t, response["message"], "Failed to get callback log")
	})

	t.Run("GetCallbackLogByID_EmptyID", func(t *testing.T) {
		// Test with trailing slash - Gin will redirect this
		req := httptest.NewRequest("GET", "/studio/organization/1/payment/callbacks/", nil)
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		// Should return 301 redirect for trailing slash - this is expected Gin behavior
		assert.Equal(t, http.StatusMovedPermanently, w.Code)
	})
}

func TestTestCallbackAPI(t *testing.T) {
	server, mockRepo := setupCallbackAPITest(t)

	t.Run("TestCallback_Success", func(t *testing.T) {
		requestBody := TestCallbackRequest{
			URL:         "https://example.com/test-webhook",
			Payload:     map[string]any{"test": "data", "value": 123},
			SignPayload: false,
		}

		expectedLog := &domain.CallbackLog{
			ID:              "generated-log-id",
			URL:             requestBody.URL,
			Type:            domain.CallbackTypeTest,
			Status:          domain.CallbackStatusSuccess,
			StatusCode:      util.Ptr(200),
			CallbackPayload: `{"test":"data","value":123}`,
			Duration:        150 * time.Millisecond,
			OrgID:           util.Ptr(1),
			ClientID:        util.Ptr("test-client-id"),
			CreatedAt:       time.Now().UTC(),
		}

		// Mock the CreateCallbackLog call that will be made by SendTestCallback
		mockRepo.EXPECT().
			CreateCallbackLog(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, log *domain.CallbackLog) (*domain.CallbackLog, error) {
				// Simulate the callback execution and verify new fields
				log.ID = expectedLog.ID
				log.Status = expectedLog.Status
				log.StatusCode = expectedLog.StatusCode
				log.CallbackPayload = expectedLog.CallbackPayload
				log.OrgID = expectedLog.OrgID
				log.ClientID = expectedLog.ClientID

				// Verify that org_id and client_id are set correctly
				assert.NotNil(t, log.OrgID)
				assert.Equal(t, 1, *log.OrgID)
				assert.NotNil(t, log.ClientID)
				assert.Equal(t, "test-client-id", *log.ClientID)

				return log, nil
			})

		reqBodyBytes, _ := json.Marshal(requestBody)
		req := httptest.NewRequest("POST", "/studio/organization/1/payment/callbacks/test", bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(0), response["code"])

		data, ok := response["data"].(map[string]interface{})
		require.True(t, ok)
		assert.Equal(t, expectedLog.ID, data["id"])
		assert.Equal(t, "test", data["type"])
		assert.Equal(t, requestBody.URL, data["url"])
		assert.Equal(t, float64(1), data["org_id"])
		assert.Equal(t, "test-client-id", data["client_id"])
	})

	t.Run("TestCallback_InvalidJSON", func(t *testing.T) {
		req := httptest.NewRequest("POST", "/studio/organization/1/payment/callbacks/test", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(400), response["status"])
		assert.Contains(t, response["message"], "Invalid request")
	})

	t.Run("TestCallback_MissingURL", func(t *testing.T) {
		requestBody := TestCallbackRequest{
			// Missing URL
			Payload:     map[string]any{"test": "data"},
			SignPayload: false,
		}

		reqBodyBytes, _ := json.Marshal(requestBody)
		req := httptest.NewRequest("POST", "/studio/organization/1/payment/callbacks/test", bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(400), response["status"])
		assert.Contains(t, response["message"], "Invalid request")
	})

	t.Run("TestCallback_MissingPayload", func(t *testing.T) {
		requestBody := TestCallbackRequest{
			URL: "https://example.com/test-webhook",
			// Missing Payload
			SignPayload: false,
		}

		reqBodyBytes, _ := json.Marshal(requestBody)
		req := httptest.NewRequest("POST", "/studio/organization/1/payment/callbacks/test", bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(400), response["status"])
		assert.Contains(t, response["message"], "Invalid request")
	})

	t.Run("TestCallback_ServiceError", func(t *testing.T) {
		requestBody := TestCallbackRequest{
			URL:         "https://example.com/test-webhook",
			Payload:     map[string]any{"test": "data"},
			SignPayload: false,
		}

		// Mock service error
		mockRepo.EXPECT().
			CreateCallbackLog(gomock.Any(), gomock.Any()).
			Return(nil, assert.AnError)

		reqBodyBytes, _ := json.Marshal(requestBody)
		req := httptest.NewRequest("POST", "/studio/organization/1/payment/callbacks/test", bytes.NewBuffer(reqBodyBytes))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		server.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err)

		assert.Equal(t, float64(500), response["status"])
		assert.Contains(t, response["message"], "Failed to send test callback")
	})
}
