package payment

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/service/payment"
)

// GetQuote handles getting payment quotes via GET with query parameters
// Always returns quotes for all supported tokens and prevents caching
func GetQuote(c *gin.Context) {
	ctx := c.Request.Context()

	// Set no-cache headers since exchange rates change frequently
	c.<PERSON><PERSON>("Cache-Control", "no-cache, no-store, must-revalidate")
	c.<PERSON><PERSON>("Pragma", "no-cache")
	c.<PERSON><PERSON>("Expires", "0")

	clientID := c.GetString("client_id")
	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing client ID (X-Client-ID header)")
		return
	}

	// Parse query parameters
	sourceType := c.Query("source_type")
	if sourceType == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "source_type query parameter is required")
		return
	}

	amount := c.Query("amount")
	if amount == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "amount query parameter is required")
		return
	}

	// Validate source type
	var sourceTypeEnum payment.QuoteSourceType
	switch sourceType {
	case "fiat":
		sourceTypeEnum = payment.QuoteSourceTypeFiat
	case "crypto":
		sourceTypeEnum = payment.QuoteSourceTypeCrypto
	default:
		response.BadRequestWithMsg(c, code.ParamIncorrect, "source_type must be either 'fiat' or 'crypto'")
		return
	}

	// Build QuoteParams - always returns all supported tokens
	params := payment.QuoteParams{
		SourceType:   sourceTypeEnum,
		Amount:       amount,
		TargetTokens: []payment.TargetToken{}, // Empty = all supported tokens
	}

	// Validate and set source-specific parameters
	switch sourceTypeEnum {
	case payment.QuoteSourceTypeFiat:
		sourceCurrency := c.Query("source_currency")
		if sourceCurrency == "" {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "source_currency query parameter is required when source_type is 'fiat'")
			return
		}
		params.SourceCurrency = &sourceCurrency
	case payment.QuoteSourceTypeCrypto:
		sourceChainID := c.Query("source_chain_id")
		sourceContractAddress := c.Query("source_contract_address")

		if sourceChainID == "" || sourceContractAddress == "" {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "source_chain_id and source_contract_address query parameters are required when source_type is 'crypto'")
			return
		}

		params.SourceChainID = &sourceChainID
		params.SourceContractAddress = &sourceContractAddress
	}

	quote, kgErr := payment.GetQuote(ctx, params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, quote)
}
