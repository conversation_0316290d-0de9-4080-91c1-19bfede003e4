package payment

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/service/payment"
)

// GetDashboard handles the retrieval of dashboard metrics for payment intents
// Supports optional 'days' query parameter for time-based analytics
func GetDashboard(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "payment.GetDashboard")
	defer span.End()

	// Must have org ID
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing org ID")
		return
	}

	// Parse optional days parameter
	var days *int
	if daysStr := c.Query("days"); daysStr != "" {
		if daysVal, err := strconv.Atoi(daysStr); err != nil {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid days parameter: must be a positive integer")
			return
		} else if daysVal <= 0 {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "days parameter must be positive")
			return
		} else {
			days = &daysVal
		}
	}

	// Get dashboard metrics with optional time range
	params := payment.GetDashboardMetricsParams{
		OrgID: orgID,
		Days:  days,
	}

	metrics, kgErr := payment.GetDashboardMetricsWithTimeRange(ctx, params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, metrics)
}
