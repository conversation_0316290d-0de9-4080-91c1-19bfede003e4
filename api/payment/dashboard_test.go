package payment

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/coingecko"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/service/payment"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func setupDashboardTest(t *testing.T) *gin.Engine {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock chain tokens and rate map for the test
	chainToken1 := domain.ChainToken{Chain: domain.Ethereum, TokenID: domain.Ethereum.MainToken().ID()}
	chainToken2 := domain.ChainToken{Chain: domain.Polygon, TokenID: domain.Polygon.MainToken().ID()}

	revenue1, _ := decimal.NewFromString("10.5")
	revenue2, _ := decimal.NewFromString("20.75")
	rate1, _ := decimal.NewFromString("2000")
	rate2, _ := decimal.NewFromString("1.5")

	// Create mock return values for repository
	mockStats := &domain.PaymentIntentStats{
		TotalRevenue: map[domain.ChainToken]decimal.Decimal{
			chainToken1: revenue1,
			chainToken2: revenue2,
		},
		RateMap: map[domain.ChainToken]decimal.Decimal{
			chainToken1: rate1,
			chainToken2: rate2,
		},
		ValidOrderCount:     150,
		UniqueCustomerCount: 100,
	}

	// Create mock repository
	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)

	// Mock GetPaymentIntentStatsWithTimeRange for the new implementation
	mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
		OrgID: 1,
		Days:  nil, // No time range specified (all-time stats)
	}).Return(mockStats, nil).AnyTimes()

	mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
		OrgID: 0,
		Days:  nil,
	}).Return(nil, errors.New("invalid org ID")).AnyTimes()

	mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
		OrgID: 999,
		Days:  nil,
	}).Return(nil, errors.New("database error")).AnyTimes()

	// Initialize tokenmeta service
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(mockTokenMetaRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	// Add known tokens for the test
	ethTokenID := domain.Ethereum.MainToken().ID()  // Should be "eth"
	maticTokenID := domain.Polygon.MainToken().ID() // Should be "matic"

	tokenmeta.AddKnown(map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: domain.Ethereum, TokenID: ethTokenID}: {
			Name:        "Ether",
			Symbol:      "ETH", // Test expects ETH
			Decimals:    18,
			CoingeckoID: "ethereum", // Added Coingecko ID
			IsVerified:  true,
		},
		{Chain: domain.Polygon, TokenID: maticTokenID}: {
			Name:        "Polygon",
			Symbol:      "MATIC", // Test expects MATIC (even though main token is POL, test expects MATIC)
			Decimals:    18,
			CoingeckoID: "matic-network", // Added Coingecko ID
			IsVerified:  true,
		},
	})

	// Mock BatchGetTokenMetadata for any calls made by tokenmeta.Get (indirectly via fetchTokenRates)
	// This ensures that if AddKnown isn't enough (e.g. due to case sensitivity or exact ID match issues),
	// the Get call will still find the metadata.
	mockTokenMetaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx interface{}, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			results := make(map[domain.ChainToken]*domain.TokenMetadata)
			for _, ct := range tokens {
				if ct.Chain.ID() == domain.Ethereum.ID() && ct.TokenID == ethTokenID {
					results[ct] = &domain.TokenMetadata{Name: "Ether", Symbol: "ETH", Decimals: 18, CoingeckoID: "ethereum", IsVerified: true}
				}
				if ct.Chain.ID() == domain.Polygon.ID() && ct.TokenID == maticTokenID {
					results[ct] = &domain.TokenMetadata{Name: "Polygon", Symbol: "MATIC", Decimals: 18, CoingeckoID: "matic-network", IsVerified: true}
				}
			}
			return results, nil
		}).AnyTimes()

	// Mock Coingecko IService (this is what service/payment/dashboard.go uses via coingecko.Get())
	mockCoingeckoService := coingecko.NewMockIService(ctrl)
	coingecko.Set(mockCoingeckoService) // Use coingecko.Set to set the IService mock

	// Now, expect calls to QuoteInUSD on this mockCoingeckoService
	mockCoingeckoService.EXPECT().QuoteInUSD(gomock.Any(), "ethereum").Return(2000.0, nil).AnyTimes()
	mockCoingeckoService.EXPECT().QuoteInUSD(gomock.Any(), "matic-network").Return(1.5, nil).AnyTimes()

	// Initialize payment service with mocked payment intent repository
	payment.Init(mockRepo, nil, nil)

	// Setup test database with organization
	rdb.Reset()
	assert.Nil(t, rdb.Get().Create([]model.StudioOrganization{
		{ID: 1, Name: "Test Organization"},
	}).Error)

	// Setup Gin router with the dashboard endpoint
	gin.SetMode(gin.TestMode)
	router := gin.Default()

	// Add middleware to set org_id for authorized routes
	router.GET("/dashboard", func(c *gin.Context) {
		c.Set("org_id", 1) // Mock authenticated user with org_id = 1
		GetDashboard(c)
	})

	router.GET("/dashboard-no-org", func(c *gin.Context) {
		// Don't set org_id to test that case
		GetDashboard(c)
	})

	router.GET("/dashboard-error", func(c *gin.Context) {
		c.Set("org_id", 999) // Use this ID to trigger the database error
		GetDashboard(c)
	})

	return router
}

func TestDashboardAPI(t *testing.T) {
	router := setupDashboardTest(t)

	// Test case: successful dashboard retrieval
	t.Run("successful dashboard retrieval", func(t *testing.T) {
		expectedStatus := http.StatusOK
		endpoint := "/dashboard"

		req, _ := http.NewRequest("GET", endpoint, nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, expectedStatus, w.Code)

		var respMap map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &respMap)
		assert.NoError(t, err)

		data := respMap["data"].(map[string]interface{})

		// Verify metrics
		assert.Equal(t, float64(150), data["valid_order_count"])
		assert.Equal(t, float64(100), data["unique_customer_count"])

		// Verify revenue data
		totalRevenue := data["total_revenue"].(map[string]interface{})

		// Verify Ethereum/ETH data
		ethChain := domain.Ethereum.ID()
		ethTokenID := domain.Ethereum.MainToken().ID() // "eth"
		assert.Contains(t, totalRevenue, ethChain)
		ethData := totalRevenue[ethChain].(map[string]interface{})
		assert.Contains(t, ethData, ethTokenID)
		ethToken := ethData[ethTokenID].(map[string]interface{})
		assert.Equal(t, "10.5", ethToken["amount"])
		assert.Equal(t, "2000", ethToken["rate"])
		assert.Equal(t, "21000", ethToken["total_usd"])

		// Verify Polygon/MATIC data
		polygonChain := domain.Polygon.ID()
		maticTokenID := domain.Polygon.MainToken().ID() // "matic"
		assert.Contains(t, totalRevenue, polygonChain)
		maticData := totalRevenue[polygonChain].(map[string]interface{})
		assert.Contains(t, maticData, maticTokenID)
		maticToken := maticData[maticTokenID].(map[string]interface{})
		assert.Equal(t, "20.75", maticToken["amount"])
		assert.Equal(t, "1.5", maticToken["rate"])
		assert.Equal(t, "31.125", maticToken["total_usd"])
	})

	// Test case: missing org_id
	t.Run("missing org_id", func(t *testing.T) {
		expectedStatus := http.StatusBadRequest
		endpoint := "/dashboard-no-org"

		req, _ := http.NewRequest("GET", endpoint, nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, expectedStatus, w.Code)
	})

	// Test case: database error
	t.Run("database error", func(t *testing.T) {
		expectedStatus := http.StatusInternalServerError
		endpoint := "/dashboard-error"

		req, _ := http.NewRequest("GET", endpoint, nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, expectedStatus, w.Code)
	})
}

func TestDashboardAPIWithTimeRange(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock chain tokens and revenues for time range tests
	chainToken1 := domain.ChainToken{Chain: domain.Ethereum, TokenID: domain.Ethereum.MainToken().ID()}
	chainToken2 := domain.ChainToken{Chain: domain.Polygon, TokenID: domain.Polygon.MainToken().ID()}

	// Mock stats for 7-day range
	revenue7d1, _ := decimal.NewFromString("5.25")
	revenue7d2, _ := decimal.NewFromString("10.5")
	mockStats7d := &domain.PaymentIntentStats{
		TotalRevenue: map[domain.ChainToken]decimal.Decimal{
			chainToken1: revenue7d1,
			chainToken2: revenue7d2,
		},
		ValidOrderCount:     25,
		UniqueCustomerCount: 15,
	}

	// Mock stats for 30-day range
	revenue30d1, _ := decimal.NewFromString("15.75")
	revenue30d2, _ := decimal.NewFromString("31.5")
	mockStats30d := &domain.PaymentIntentStats{
		TotalRevenue: map[domain.ChainToken]decimal.Decimal{
			chainToken1: revenue30d1,
			chainToken2: revenue30d2,
		},
		ValidOrderCount:     75,
		UniqueCustomerCount: 45,
	}

	// Create mock repository
	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)

	// Mock expectations for different time ranges
	days7 := 7
	days30 := 30

	mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
		OrgID: 1,
		Days:  &days7,
	}).Return(mockStats7d, nil).AnyTimes()

	mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
		OrgID: 1,
		Days:  &days30,
	}).Return(mockStats30d, nil).AnyTimes()

	// Initialize tokenmeta service
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(mockTokenMetaRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	// Add known tokens for the test
	ethTokenID := domain.Ethereum.MainToken().ID()
	maticTokenID := domain.Polygon.MainToken().ID()

	tokenmeta.AddKnown(map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: domain.Ethereum, TokenID: ethTokenID}: {
			Name:        "Ether",
			Symbol:      "ETH",
			Decimals:    18,
			CoingeckoID: "ethereum",
			IsVerified:  true,
		},
		{Chain: domain.Polygon, TokenID: maticTokenID}: {
			Name:        "Polygon",
			Symbol:      "MATIC",
			Decimals:    18,
			CoingeckoID: "matic-network",
			IsVerified:  true,
		},
	})

	// Mock BatchGetTokenMetadata
	mockTokenMetaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx interface{}, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			results := make(map[domain.ChainToken]*domain.TokenMetadata)
			for _, ct := range tokens {
				if ct.Chain.ID() == domain.Ethereum.ID() && ct.TokenID == ethTokenID {
					results[ct] = &domain.TokenMetadata{Name: "Ether", Symbol: "ETH", Decimals: 18, CoingeckoID: "ethereum", IsVerified: true}
				}
				if ct.Chain.ID() == domain.Polygon.ID() && ct.TokenID == maticTokenID {
					results[ct] = &domain.TokenMetadata{Name: "Polygon", Symbol: "MATIC", Decimals: 18, CoingeckoID: "matic-network", IsVerified: true}
				}
			}
			return results, nil
		}).AnyTimes()

	// Mock Coingecko service
	mockCoingeckoService := coingecko.NewMockIService(ctrl)
	coingecko.Set(mockCoingeckoService)
	mockCoingeckoService.EXPECT().QuoteInUSD(gomock.Any(), "ethereum").Return(2000.0, nil).AnyTimes()
	mockCoingeckoService.EXPECT().QuoteInUSD(gomock.Any(), "matic-network").Return(1.5, nil).AnyTimes()

	// Initialize payment service
	payment.Init(mockRepo, nil, nil)

	// Setup test database
	rdb.Reset()
	assert.Nil(t, rdb.Get().Create([]model.StudioOrganization{
		{ID: 1, Name: "Test Organization"},
	}).Error)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.Default()

	router.GET("/dashboard", func(c *gin.Context) {
		c.Set("org_id", 1)
		GetDashboard(c)
	})

	t.Run("Last7Days", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/dashboard?days=7", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var respMap map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &respMap)
		assert.NoError(t, err)

		data := respMap["data"].(map[string]interface{})

		// Verify metrics for 7-day range
		assert.Equal(t, float64(25), data["valid_order_count"])
		assert.Equal(t, float64(15), data["unique_customer_count"])

		// Verify revenue data
		totalRevenue := data["total_revenue"].(map[string]interface{})

		// Verify ETH data
		ethTokenID := domain.Ethereum.MainToken().ID()
		ethData := totalRevenue[domain.Ethereum.ID()].(map[string]interface{})
		ethToken := ethData[ethTokenID].(map[string]interface{})
		assert.Equal(t, "5.25", ethToken["amount"])
		assert.Equal(t, "2000", ethToken["rate"])
		assert.Equal(t, "10500", ethToken["total_usd"])

		// Verify MATIC data
		maticTokenID := domain.Polygon.MainToken().ID()
		maticData := totalRevenue[domain.Polygon.ID()].(map[string]interface{})
		maticToken := maticData[maticTokenID].(map[string]interface{})
		assert.Equal(t, "10.5", maticToken["amount"])
		assert.Equal(t, "1.5", maticToken["rate"])
		assert.Equal(t, "15.75", maticToken["total_usd"])
	})

	t.Run("Last30Days", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/dashboard?days=30", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var respMap map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &respMap)
		assert.NoError(t, err)

		data := respMap["data"].(map[string]interface{})

		// Verify metrics for 30-day range
		assert.Equal(t, float64(75), data["valid_order_count"])
		assert.Equal(t, float64(45), data["unique_customer_count"])
	})

	t.Run("InvalidDaysParameter", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/dashboard?days=91", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var respMap map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &respMap)
		assert.NoError(t, err)

		// Should contain error message about invalid days parameter in the data field
		data, exists := respMap["data"].(map[string]interface{})
		assert.True(t, exists, "Response should have data field")
		assert.Contains(t, data["error"], "days parameter must be between 1 and 90")
	})

	t.Run("InvalidDaysFormat", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/dashboard?days=abc", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var respMap map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &respMap)
		assert.NoError(t, err)

		// Should contain error message about invalid days parameter format
		assert.Contains(t, respMap["message"], "invalid days parameter")
	})

	t.Run("ZeroDays", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/dashboard?days=0", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var respMap map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &respMap)
		assert.NoError(t, err)

		// Should contain error message about positive days parameter
		assert.Contains(t, respMap["message"], "days parameter must be positive")
	})

	t.Run("NegativeDays", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/dashboard?days=-5", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var respMap map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &respMap)
		assert.NoError(t, err)

		// Should contain error message about positive days parameter
		assert.Contains(t, respMap["message"], "days parameter must be positive")
	})
}
