package payment

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/service/callback"
)

// validateClientInOrganization validates that a client belongs to the specified organization
func validateClientInOrganization(ctx context.Context, clientID string, orgID int) error {
	if clientID == "" {
		return nil // No client validation needed if no client ID provided
	}

	clientInOrg, kgErr := organization.IsOAuthClientInOrganization(ctx, orgID, clientID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to validate client in organization", map[string]interface{}{
			"error":     kgErr.Error.Error(),
			"client_id": clientID,
			"org_id":    orgID,
		})
		return fmt.Errorf("failed to validate client")
	}
	if !clientInOrg {
		return fmt.Errorf("client does not belong to this organization")
	}
	return nil
}

// TestCallbackRequest represents the API request to test a callback
type TestCallbackRequest struct {
	URL         string         `json:"url" binding:"required"`
	Payload     map[string]any `json:"payload" binding:"required"`
	ClientID    string         `json:"client_id"`
	SignPayload bool           `json:"sign_payload"`
}

// CallbackLogResponse represents the API response for a callback log
type CallbackLogResponse struct {
	ID              string    `json:"id"`
	PaymentIntentID *string   `json:"payment_intent_id"`
	ClientID        *string   `json:"client_id"`
	OrgID           *int      `json:"org_id"`
	URL             string    `json:"url"`
	Type            string    `json:"type"`
	Status          string    `json:"status"`
	StatusCode      *int      `json:"status_code"`
	CallbackPayload string    `json:"callback_payload"`
	Error           *string   `json:"error"`
	Duration        int64     `json:"duration"` // Duration in milliseconds
	CreatedAt       time.Time `json:"created_at"`
}

// toCallbackLogResponse converts domain.CallbackLog to CallbackLogResponse
func toCallbackLogResponse(log *domain.CallbackLog) *CallbackLogResponse {
	return &CallbackLogResponse{
		ID:              log.ID,
		PaymentIntentID: log.PaymentIntentID,
		ClientID:        log.ClientID,
		OrgID:           log.OrgID,
		URL:             log.URL,
		Type:            string(log.Type),
		Status:          string(log.Status),
		StatusCode:      log.StatusCode,
		CallbackPayload: log.CallbackPayload,
		Error:           log.Error,
		Duration:        log.Duration.Milliseconds(),
		CreatedAt:       log.CreatedAt,
	}
}

// toCallbackLogResponses converts slice of domain.CallbackLog to slice of CallbackLogResponse
func toCallbackLogResponses(logs []*domain.CallbackLog) []*CallbackLogResponse {
	responses := make([]*CallbackLogResponse, len(logs))
	for i, log := range logs {
		responses[i] = toCallbackLogResponse(log)
	}
	return responses
}

// GetCallbackLogs returns a list of callback logs with pagination and filtering
func GetCallbackLogs(c *gin.Context) {
	// Get organization ID from context (set by studio middleware)
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Organization ID is required")
		return
	}

	// Get pagination from middleware
	page, pageSize, _ := middleware.GetPaginationFromContext(c)

	// Get and validate client_id if provided
	clientID := c.Query("client_id")
	if err := validateClientInOrganization(c.Request.Context(), clientID, orgID); err != nil {
		if err.Error() == "failed to validate client" {
			response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		} else {
			response.ForbiddenErrorWithMsg(c, code.ClientForbidden, err.Error())
		}
		return
	}

	// Build filter with organization constraints
	filter := domain.CallbackLogFilter{
		OrgID:    &orgID, // Always filter by organization for security
		Page:     page,
		PageSize: pageSize,
	}

	// Add client_id filter if provided and validated
	if clientID != "" {
		filter.ClientID = &clientID
	}

	// Parse additional query parameters
	paymentIntentID := c.Query("payment_intent_id")
	if paymentIntentID != "" {
		filter.PaymentIntentID = &paymentIntentID
	}

	// Parse type filters
	var typeFilters []domain.CallbackType
	if callbackType := c.Query("type"); callbackType != "" {
		parsedType, err := domain.ParseCallbackType(callbackType)
		if err == nil {
			typeFilters = append(typeFilters, parsedType)
		}
	}
	filter.Type = typeFilters

	// Parse status filters
	var statusFilters []domain.CallbackStatus
	if status := c.Query("status"); status != "" {
		parsedStatus, err := domain.ParseCallbackStatus(status)
		if err == nil {
			statusFilters = append(statusFilters, parsedStatus)
		}
	}
	filter.Status = statusFilters

	// Parse date filters
	if fromDateStr := c.Query("from_date"); fromDateStr != "" {
		if fromDate, err := time.Parse(time.RFC3339, fromDateStr); err == nil {
			filter.FromDate = &fromDate
		}
	}
	if toDateStr := c.Query("to_date"); toDateStr != "" {
		if toDate, err := time.Parse(time.RFC3339, toDateStr); err == nil {
			filter.ToDate = &toDate
		}
	}

	// Get logs
	logs, total, err := callback.GetCallbackLogs(c.Request.Context(), filter)
	if err != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "Failed to get callback logs", map[string]interface{}{
			"error":  err.Error(),
			"org_id": orgID,
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, "Failed to get callback logs")
		return
	}

	// Create paging object
	paging := response.Paging{
		PageNumber: page,
		PageSize:   pageSize,
		TotalCount: total,
		PageSort:   "created_at desc",
	}

	response.OKWithPaging(c, toCallbackLogResponses(logs), paging)
}

// GetCallbackLogByID returns a single callback log by ID
func GetCallbackLogByID(c *gin.Context) {
	// Get organization ID from context (set by studio middleware)
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Organization ID is required")
		return
	}

	// Get and validate client_id if provided
	clientID := c.Query("client_id")
	if err := validateClientInOrganization(c.Request.Context(), clientID, orgID); err != nil {
		if err.Error() == "failed to validate client" {
			response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		} else {
			response.ForbiddenErrorWithMsg(c, code.ClientForbidden, err.Error())
		}
		return
	}

	id := c.Param("id")
	if id == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Callback log ID is required")
		return
	}

	// Get the callback log by ID
	log, err := callback.GetCallbackLogByID(c.Request.Context(), id)
	if err != nil {
		if errors.Is(err, domain.ErrRecordNotFound) {
			response.NotFoundWithMsg(c, code.RecordNotFound, "Callback log not found")
		} else {
			kglog.ErrorWithDataCtx(c.Request.Context(), "Failed to get callback log", map[string]interface{}{
				"error":  err.Error(),
				"id":     id,
				"org_id": orgID,
			})
			response.InternalServerErrorWithMsg(c, code.InternalError, "Failed to get callback log")
		}
		return
	}

	// Validate that the log belongs to this organization
	if log.OrgID == nil || *log.OrgID != orgID {
		kglog.WarningWithDataCtx(c.Request.Context(), "Attempted access to callback log from different organization", map[string]interface{}{
			"id":              id,
			"requesting_org":  orgID,
			"log_org":         log.OrgID,
			"requesting_user": c.GetString("user_id"),
		})
		response.NotFoundWithMsg(c, code.RecordNotFound, "Callback log not found")
		return
	}

	// If client_id was provided, validate that the log belongs to that client
	if clientID != "" && (log.ClientID == nil || *log.ClientID != clientID) {
		kglog.WarningWithDataCtx(c.Request.Context(), "Attempted access to callback log from different client", map[string]interface{}{
			"id":                id,
			"requesting_client": clientID,
			"log_client":        log.ClientID,
			"org_id":            orgID,
		})
		response.NotFoundWithMsg(c, code.RecordNotFound, "Callback log not found")
		return
	}

	response.OK(c, toCallbackLogResponse(log))
}

// TestCallback sends a test callback to the specified URL
func TestCallback(c *gin.Context) {
	// Get organization ID from context (set by studio middleware)
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Organization ID is required")
		return
	}

	var request TestCallbackRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid request: "+err.Error())
		return
	}

	// Validate URL
	if request.URL == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "URL is required")
		return
	}

	// Validate client_id if provided
	if err := validateClientInOrganization(c.Request.Context(), request.ClientID, orgID); err != nil {
		if err.Error() == "failed to validate client" {
			response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		} else {
			response.ForbiddenErrorWithMsg(c, code.ClientForbidden, err.Error())
		}
		return
	}

	// Convert to domain struct
	domainRequest := &domain.TestCallbackRequest{
		URL:         request.URL,
		Payload:     request.Payload,
		ClientID:    request.ClientID,
		SignPayload: request.SignPayload,
	}

	// Create execution context for callback logging
	execCtx := domain.CallbackExecutionContext{
		ClientID: request.ClientID,
		OrgID:    orgID,
		// PaymentIntentID is nil for test callbacks
	}

	// Send test callback with execution context
	log, err := callback.SendTestCallback(c.Request.Context(), domainRequest, execCtx)
	if err != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "Failed to send test callback", map[string]interface{}{
			"error":     err.Error(),
			"url":       request.URL,
			"org_id":    orgID,
			"client_id": request.ClientID,
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, "Failed to send test callback: "+err.Error())
		return
	}

	response.OK(c, toCallbackLogResponse(log))
}
