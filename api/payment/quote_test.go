package payment

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/domain"
	coingeckoapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/coingecko-api"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestGetQuote(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Setup mock coingecko
	mockCoingecko := coingeckoapi.NewMockICoingecko(ctrl)
	coingeckoapi.Set(mockCoingecko)

	// Setup mock token metadata
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(mockTokenMetaRepo, nil, nil)

	// Add known metadata for supported tokens
	usdcTokenAddress := "******************************************"
	usdtTokenAddress := "******************************************"

	usdcMetadata := &domain.TokenMetadata{
		Name:        "USD Coin",
		Symbol:      "USDC",
		Decimals:    6,
		CoingeckoID: "usd-coin",
		LogoUrl:     "https://usdc.logo",
		IsVerified:  true,
	}
	usdtMetadata := &domain.TokenMetadata{
		Name:        "Tether USD",
		Symbol:      "USDT",
		Decimals:    6,
		CoingeckoID: "tether",
		LogoUrl:     "https://usdt.logo",
		IsVerified:  true,
	}

	tokenmeta.AddKnown(map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: domain.Arbitrum, TokenID: usdcTokenAddress}: usdcMetadata,
		{Chain: domain.Arbitrum, TokenID: usdtTokenAddress}: usdtMetadata,
	})

	t.Run("GetQuote_Fiat_Success", func(t *testing.T) {
		// Mock currency support
		mockCoingecko.EXPECT().IsCurrencySupported(gomock.Any(), "usd").Return(true, nil)

		// Mock exchange rates
		mockCoingecko.EXPECT().SimplePrice(gomock.Any(), gomock.Any(), "usd").DoAndReturn(
			func(_ context.Context, ids []domain.CoingeckoID, currency string) (map[domain.CoingeckoID]float64, error) {
				if len(ids) == 1 {
					switch ids[0] {
					case "usd-coin":
						assert.Equal(t, []domain.CoingeckoID{"usd-coin"}, ids)
						return map[domain.CoingeckoID]float64{"usd-coin": 1.0}, nil
					case "tether":
						assert.Equal(t, []domain.CoingeckoID{"tether"}, ids)
						return map[domain.CoingeckoID]float64{"tether": 1.0}, nil
					}
				}
				return nil, nil
			}).AnyTimes()

		url := "/v1/payment/quote?source_type=fiat&source_currency=USD&amount=100"
		req := httptest.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", "test-client-id")

		w := httptest.NewRecorder()
		router := gin.New()
		router.GET("/v1/payment/quote", middleware.PublicHeaderValidation(middleware.HeaderValidation{
			Key:      "X-Client-ID",
			Name:     "client_id",
			Required: false,
		}), GetQuote)

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		// Check no-cache headers
		assert.Equal(t, "no-cache, no-store, must-revalidate", w.Header().Get("Cache-Control"))
		assert.Equal(t, "no-cache", w.Header().Get("Pragma"))
		assert.Equal(t, "0", w.Header().Get("Expires"))

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)

		// The actual quote data is nested under "data" key
		data, ok := resp["data"].(map[string]interface{})
		assert.True(t, ok, "Response should have 'data' field")

		assert.Equal(t, "fiat", data["source_type"])
		assert.Equal(t, "USD", data["source_currency"])
		assert.Equal(t, "100", data["source_amount"])

		results, ok := data["results"].([]interface{})
		assert.True(t, ok)
		assert.Len(t, results, 5) // Should return all supported tokens on all chains

		// Verify each result has the expected structure - use correct chain ID "arb"
		for _, r := range results {
			result := r.(map[string]interface{})
			if result["chain_id"] == "arb" {
				assert.Equal(t, "arb", result["chain_id"]) // Fixed: use "arb" instead of "arbitrum"
				assert.Contains(t, []string{"USDC", "USDT"}, result["symbol"])
				// The amounts might have decimal precision, so check they're close to 100
				amountStr, ok := result["amount"].(string)
				assert.True(t, ok)
				assert.Equal(t, "100", amountStr)
				exchangeRateStr, ok := result["exchange_rate"].(string)
				assert.True(t, ok)
				assert.Equal(t, "1", exchangeRateStr)
			}
			if result["chain_id"] == "optimism" {
				assert.Equal(t, "optimism", result["chain_id"]) // Fixed: use "arb" instead of "arbitrum"
				assert.Contains(t, []string{"USDC", "USDT"}, result["symbol"])
				// The amounts might have decimal precision, so check they're close to 100
				amountStr, ok := result["amount"].(string)
				assert.True(t, ok)
				assert.Equal(t, "100", amountStr)
			}
		}
	})

	t.Run("GetQuote_MissingParams", func(t *testing.T) {
		url := "/v1/payment/quote?source_type=fiat&amount=100"
		req := httptest.NewRequest("GET", url, nil)

		w := httptest.NewRecorder()
		router := gin.New()
		router.GET("/v1/payment/quote", middleware.PublicHeaderValidation(middleware.HeaderValidation{
			Key:      "X-Client-ID",
			Name:     "client_id",
			Required: false,
		}), GetQuote)

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
