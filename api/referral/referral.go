package referral

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/service/referral"
	"github.com/shopspring/decimal"
)

// RecordRewardRequest represents the request payload for recording a referral reward
type RecordRewardRequest struct {
	ReferralCode string `json:"referral_code" binding:"required"`
	FromAddress  string `json:"from_address" binding:"required"`
	TxHash       string `json:"tx_hash" binding:"required"`
}

// WithdrawRequest represents the request payload for withdrawing referral rewards
type WithdrawRequest struct {
	Amount           string `json:"amount" binding:"required"`
	RecipientAddress string `json:"recipient_address" binding:"required"`
}

func RecordReward(c *gin.Context) {
	ctx := c.Request.Context()
	var req RecordRewardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		kglog.ErrorWithDataCtx(c, "Failed to bind request", map[string]interface{}{
			"error": err.Error(),
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
		return
	}

	result, err := referral.RecordReferralReward(ctx, req.ReferralCode, req.FromAddress, req.TxHash)
	if err != nil {
		response.KGError(c, err)
		return
	}

	response.OK(c, result)
}

func GetBalance(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)

	balance, err := referral.GetReferralRewards(ctx, uid)
	if err != nil {
		response.KGError(c, err)
		return
	}

	response.OK(c, balance)
}

func Withdraw(c *gin.Context) {
	ctx := c.Request.Context()
	var req WithdrawRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		kglog.ErrorWithDataCtx(c, "Failed to bind request", map[string]interface{}{
			"error": err.Error(),
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
		return
	}

	uid := auth.GetUID(c)
	amount, err := decimal.NewFromString(req.Amount)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid amount")
		return
	}

	result, kgErr := referral.WithdrawReferralRewards(ctx, uid, amount, req.RecipientAddress)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, result)
}
