package referral

import (
	"bytes"
	"context"
	"encoding/json"
	"math/big"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	solanaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/referral"
	"github.com/stretchr/testify/suite"
)

// Test request structs based on OpenAPI spec
type recordRewardRequest struct {
	ReferralCode string `json:"referral_code"`
	FromAddress  string `json:"from_address"`
	TxHash       string `json:"tx_hash"`
}

type withdrawRequest struct {
	Amount           string `json:"amount"`
	RecipientAddress string `json:"recipient_address"`
}

// Test response structs based on OpenAPI spec
type rewardResponse struct {
	Code int `json:"code"`
	Data struct {
		RewardID string `json:"reward_id"`
		Amount   string `json:"amount"`
	} `json:"data"`
}

type balanceResponse struct {
	Code int `json:"code"`
	Data struct {
		TotalRewards     string `json:"total_rewards"`
		AvailableRewards string `json:"available_rewards"`
		WithdrawnRewards string `json:"withdrawn_rewards"`
	} `json:"data"`
}

type withdrawalResponse struct {
	Code int `json:"code"`
	Data struct {
		WithdrawalID string `json:"withdrawal_id"`
		TxHash       string `json:"tx_hash"`
		Amount       string `json:"amount"`
	} `json:"data"`
}

type errorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// ReferralTestSuite is the test suite for referral API
type ReferralTestSuite struct {
	suite.Suite
	repo  referral.IRepo
	users map[string]domain.UserData
	uid   string
}

func (s *ReferralTestSuite) SetupSuite() {
	rdb.Reset()
	s.repo = repo.Unified()
	referral.Init(s.repo)
	solanaapi.InitDefault()
}

func (s *ReferralTestSuite) SetupTest() {
	s.T().Logf("Running SetupTest()")
	signingservertest.Setup(s.T())
	rdb.Reset()
	// Set up test users
	s.Require().Nil(rdb.CreateSeedData())
	s.users, s.uid, _, _ = dbtest.User()
	kgErr := rdb.GormRepo().BatchSetUsers(context.Background(), s.users)
	s.Require().Nil(kgErr)

	// Create test user with referral balance
	reward := &domain.ReferralReward{
		ReferrerUID: s.uid,
		From:        domain.NewStrAddress("7mhctkPyPNGTKKZZmZxqM8NghapZcWVVBpn2SgyM9KBT"),
		TxHash:      "test_tx_hash",
		Amount:      big.NewInt(1e9), // 1 SOL
	}
	_, err := s.repo.CreateReferralReward(context.Background(), reward)
	s.Require().Nil(err)

	// Create test user 2 (for insufficient balance test)
	users2, uid2, _, _ := dbtest.User()
	kgErr = rdb.GormRepo().BatchSetUsers(context.Background(), users2)
	s.Require().Nil(kgErr)

	reward2 := &domain.ReferralReward{
		ReferrerUID: uid2,
		From:        domain.NewStrAddress("7mhctkPyPNGTKKZZmZxqM8NghapZcWVVBpn2SgyM9KBT"),
		TxHash:      "test_tx_hash_2",
		Amount:      big.NewInt(0), // No rewards
	}
	_, err = s.repo.CreateReferralReward(context.Background(), reward2)
	s.Require().Nil(err)
}

func TestReferralSuite(t *testing.T) {
	suite.Run(t, new(ReferralTestSuite))
}

func (s *ReferralTestSuite) TestRecordReward() {
	s.Run("success", func() {
		r := gin.New()
		r.POST("/rewards", auth.MockAuthorize(s.uid), auth.MockOrgID(1), RecordReward)
		r.GET("/balance", auth.MockAuthorize(s.uid), auth.MockOrgID(1), GetBalance)

		// Use test request struct
		req := &recordRewardRequest{
			ReferralCode: util.Val(s.users[s.uid].TxReferralCode),
			FromAddress:  "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			TxHash:       "n7JeiuFr4q4hatgbg3fEbTtRhoF4WPSY96xfz1qwVnm3QEs3Hqc7bxecTJLxHneGQfLv2Gm7MYhvPde5QJbePWV",
		}
		body, _ := json.Marshal(req)

		w := httptest.NewRecorder()
		httpReq, _ := http.NewRequest(http.MethodPost, "/rewards", bytes.NewBuffer(body))
		r.ServeHTTP(w, httpReq)

		s.Equal(http.StatusOK, w.Code)
		if w.Code != http.StatusOK {
			s.T().Fatalf("expected ok, got %s", w.Body.String())
		}
		var resp rewardResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		s.Require().NoError(err)
		s.Equal(0, resp.Code)
		s.Equal("3", resp.Data.RewardID)
		s.Equal("0.0001", resp.Data.Amount)

		w = httptest.NewRecorder()
		httpReq, _ = http.NewRequest(http.MethodGet, "/balance", nil)
		r.ServeHTTP(w, httpReq)

		s.Equal(http.StatusOK, w.Code)
		var resp2 balanceResponse
		err = json.Unmarshal(w.Body.Bytes(), &resp2)
		s.Require().NoError(err)
		s.Equal(0, resp2.Code)
		s.Equal("1.0001", resp2.Data.TotalRewards)
		if resp2.Data.TotalRewards != "1.0001" {
			s.T().Fatalf("expected 1.0001, got %s", resp2.Data.TotalRewards)
		}
	})

	s.Run("invalid request", func() {
		r := gin.New()
		r.POST("/rewards", auth.MockAuthorize(s.uid), auth.MockOrgID(1), RecordReward)
		r.GET("/balance", auth.MockAuthorize(s.uid), auth.MockOrgID(1), GetBalance)

		// Use test request struct with missing fields
		req := &recordRewardRequest{
			// Missing required fields
		}
		body, _ := json.Marshal(req)

		w := httptest.NewRecorder()
		httpReq, _ := http.NewRequest(http.MethodPost, "/rewards", bytes.NewBuffer(body))
		r.ServeHTTP(w, httpReq)

		s.Equal(http.StatusBadRequest, w.Code)
		var resp errorResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		s.Require().NoError(err)
		s.Equal(code.ParamIncorrect, resp.Code)
		s.NotEmpty(resp.Message)
	})
}

func (s *ReferralTestSuite) TestGetBalance() {
	r := gin.New()
	r.GET("/balance", auth.MockAuthorize(s.uid), auth.MockOrgID(1), GetBalance)

	s.Run("success", func() {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/balance", nil)
		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)
		var resp balanceResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		s.Require().NoError(err)
		s.Equal(0, resp.Code)
		s.Equal("1", resp.Data.TotalRewards)     // 1 SOL
		s.Equal("1", resp.Data.AvailableRewards) // 1 SOL
		s.Equal("0", resp.Data.WithdrawnRewards) // 0 SOL
	})
}

func (s *ReferralTestSuite) TestWithdraw() {

	s.Run("success", func() {
		r := gin.New()
		r.POST("/withdraw", auth.MockAuthorize(s.uid), auth.MockOrgID(1), Withdraw)
		r.GET("/balance", auth.MockAuthorize(s.uid), auth.MockOrgID(1), GetBalance)

		// Use test request struct
		req := &withdrawRequest{
			Amount:           "0.000001",
			RecipientAddress: "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
		}
		body, _ := json.Marshal(req)

		w := httptest.NewRecorder()
		httpReq, _ := http.NewRequest(http.MethodPost, "/withdraw", bytes.NewBuffer(body))
		r.ServeHTTP(w, httpReq)

		s.Equal(http.StatusOK, w.Code)
		if w.Code != http.StatusOK {
			s.T().Fatalf("expected ok, got %s", w.Body.String())
		}
		var resp withdrawalResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		s.Require().NoError(err)
		s.Equal(0, resp.Code)
		s.NotEmpty(resp.Data.WithdrawalID)
		s.NotEmpty(resp.Data.TxHash)
		s.Equal("0.000001", resp.Data.Amount)

		// Verify database state
		w = httptest.NewRecorder()
		httpReq, _ = http.NewRequest(http.MethodGet, "/balance", nil)
		r.ServeHTTP(w, httpReq)

		s.Equal(http.StatusOK, w.Code)
		var resp2 balanceResponse
		err = json.Unmarshal(w.Body.Bytes(), &resp2)
		s.Require().NoError(err)
		s.Equal(0, resp2.Code)
		s.Equal("1", resp2.Data.TotalRewards)
		s.Equal("0.000001", resp2.Data.WithdrawnRewards)
		s.Equal("0.999999", resp2.Data.AvailableRewards)
	})

	s.Run("insufficient balance", func() {
		r := gin.New()
		r.POST("/withdraw", auth.MockAuthorize(s.uid), auth.MockOrgID(1), Withdraw)

		// Use test request struct
		req := &withdrawRequest{
			Amount:           "2.0", // More than available
			RecipientAddress: "7mhctkPyPNGTKKZZmZxqM8NghapZcWVVBpn2SgyM9KBT",
		}
		body, _ := json.Marshal(req)

		w := httptest.NewRecorder()
		httpReq, _ := http.NewRequest(http.MethodPost, "/withdraw", bytes.NewBuffer(body))
		r.ServeHTTP(w, httpReq)

		s.Equal(http.StatusBadRequest, w.Code)
		var resp errorResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		s.Require().NoError(err)
		s.Equal(code.InsufficientRewardBalance, resp.Code)
		s.NotEmpty(resp.Message)
	})
}
