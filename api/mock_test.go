package api

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGenerateV4PutObjectSignedURL(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	object := "public/test/example.jpeg"

	url, err := db.GoogleStorageServiceObj.GenerateV4PutObjectSignedURL(object)
	assert.Nil(t, err)
	assert.NotEmpty(t, url)

}

func TestGenerateV4GetObjectSignedURL(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	object := "public/test/heart2.png"
	url, err := db.GoogleStorageServiceObj.GenerateV4GetObjectSignedURL(object)
	assert.Nil(t, err)
	assert.NotEmpty(t, url)
}

func TestUploadFileBySignedUrl(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	// run mock server for gcs
	setupMockServer(t)

	filePath := "example.jpeg"
	fileName := "example3.jpeg"

	object := "public/test/" + fileName

	signedUrl, err := db.GoogleStorageServiceObj.GenerateV4PutObjectSignedURL(object)
	assert.Nil(t, err)
	fmt.Println("signedUrl: ", signedUrl)
	err = db.UploadFileBySignedUrl(signedUrl, filePath, fileName, nil)
	assert.Nil(t, err)
}

func TestUploadFileBySignedUrlasPublic(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	// run mock server for gcs
	setupMockServer(t)

	filePath := "example.jpeg"
	fileName := "example4.jpeg"

	object := "public/test/" + fileName

	signedUrl, err := db.GoogleStorageServiceObj.GenerateV4PutObjectSignedURLasPublic(object)
	assert.Nil(t, err)
	fmt.Println("signedUrl: ", signedUrl)

	headers := map[string]string{
		"x-goog-acl": "public-read",
	}
	err = db.UploadFileBySignedUrl(signedUrl, filePath, fileName, headers)
	assert.Nil(t, err)
}

func TestGetCdnUrlFromSignedUrl(t *testing.T) {
	signedUrl := "https://storage.googleapis.com/kryptogo-wallet-app-dev.appspot.com/public/studio/nft/kryptogo/example.png?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=firebase-adminsdk-mxt0f%40kryptogo-wallet-app-dev.iam.gserviceaccount.com%2F20230801%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20230801T030405Z&X-Goog-Expires=899&X-Goog-Signature=433177034c68254411832c7b3a54acd8182274ffdad18f744018dabcc190bd198e8085003dc12f716b765f0a6909d8b54eafd7b875f6d8240a4375d58b3748dc29b381998b4bb41ca1017a28c2427800de88d1bd71e97f921f917a0dc76889b641a5ccf4174345d89ba824c260fa8d1665f743dc98dd826863ee1a60c8c5e2e61cee5ce421aa88e9ce4a5f4c43416bd51e7cafe76280dcd2131fde1ceac367a6f79c32bb0ad67edb90403860a527df38eb345343cbdb92e8a7bacc447f7895868218ed6a577ddab7e52793f06ae104763b08efba930fd0a3c95e9ab76e11fecd3de9c1cc3b508d48c44df9763740fe970399d2b5771f3aa6c7610f45a670f31a&X-Goog-SignedHeaders=host%3Bx-goog-acl"

	cdnUrl, err := db.GetCdnUrlFromSignedUrl(signedUrl)
	assert.Nil(t, err)
	fmt.Println(cdnUrl)
	assert.Equal(t, "https://wallet-static-dev.kryptogo.com/public/studio/nft/kryptogo/example.png", cdnUrl)
}

func setupMockServer(t *testing.T) *http.Server {
	// signing server
	mockServer := gin.Default()
	mockServer.PUT("/v1/gcs/signed_url/*object_path", MockSignedUrl)

	// create HTTP server for graceful shutdown
	srv := &http.Server{
		Addr:    ":" + config.GetString("APP_PORT"),
		Handler: mockServer,
	}
	go func() {
		err := srv.ListenAndServe()
		t.Logf("GCS server terminated with error: %v\n", err)
	}()

	time.Sleep(1 * time.Second) // wait for server to start
	return srv
}
