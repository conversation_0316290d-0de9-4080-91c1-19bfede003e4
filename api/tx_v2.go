package api

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	chainsync "github.com/kryptogo/kg-wallet-backend/service/chain-sync"
	tokenanalysis "github.com/kryptogo/kg-wallet-backend/service/token-analysis"
	"github.com/samber/lo"
)

type handleNewTxReq struct {
	ChainID string `json:"chain_id"`
	TxHash  string `json:"tx_hash"`
}

// HandleNewTx processes the transaction request from the Google Cloud Task.
func HandleNewTx(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "HandleNewTx")
	defer span.End()

	// if previous response is 4xx, no need to process
	if prevResp := c.GetHeader("X-Cloudtasks-Taskpreviousresponse"); prevResp != "" {
		prevCode, err := strconv.Atoi(prevResp)
		if err == nil && prevCode >= 400 && prevCode < 500 {
			kglog.DebugCtx(ctx, "previous response is 4xx, no need to process")
			c.AbortWithStatusJSON(prevCode, nil)
			return
		}
	}

	req := &handleNewTxReq{}
	kgErr := util.ToGinContextExt(c).BindJson(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Get the chain object from the chain ID
	chain := domain.IDToChain(req.ChainID)
	if chain == nil {
		kglog.ErrorfCtx(ctx, "invalid chain ID: %v", req.ChainID)
		response.BadRequestWithMsg(c, code.ParamIncorrect, fmt.Sprintf("invalid chain ID: %v", req.ChainID))
		return
	}

	// Call the Handle function to process the transaction
	if kgErr := chainsync.Handle(ctx, chain, req.TxHash); kgErr != nil {
		kglog.WarningfCtx(ctx, "failed to handle transaction: %v", kgErr.String())
		response.KGError(c, kgErr)
		return
	}

	// Call token analysis service to record trading volume
	kgErr = tokenanalysis.ProcessTradingVolume(ctx, req.TxHash)
	if kgErr != nil {
		kglog.WarningfCtx(ctx, "failed to process trading volume: %v", kgErr.String())
		response.KGError(c, kgErr)
		return
	}

	// Respond with success
	response.OK(c, nil)
}

type AlchemyWebhookRequest struct {
	Type  string `json:"type"`
	Event struct {
		Network  string                   `json:"network"`
		Activity []AlchemyWebhookActivity `json:"activity"`
	} `json:"event"`
}

type AlchemyWebhookActivity struct {
	Hash string `json:"hash"`
}

// HandleNewAlchemyTx processes the transaction request from the Alchemy webhook.
func HandleNewAlchemyTx(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "HandleNewAlchemyTx")
	defer span.End()

	var webhook AlchemyWebhookRequest
	if err := c.ShouldBindJSON(&webhook); err != nil {
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid webhook payload"), nil))
		return
	}
	// Map Alchemy network to your Chain ID
	chain, ok := lo.Find(domain.Chains, func(c domain.Chain) bool {
		return c.AlchemyNetwork() == webhook.Event.Network
	})
	if !ok {
		kglog.ErrorfCtx(ctx, "unsupported network: %v", webhook.Event.Network)
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Unsupported network")
		return
	}

	// Extract unique transaction hashes
	txHashes := lo.FilterMap(webhook.Event.Activity, func(activity AlchemyWebhookActivity, _ int) (string, bool) {
		return activity.Hash, activity.Hash != ""
	})
	txHashes = lo.Uniq(txHashes)

	// Enqueue each unique transaction hash
	for _, txHash := range txHashes {
		if err := chainsync.Enqueue(ctx, chain, txHash); err != nil {
			kglog.ErrorfCtx(ctx, "failed to enqueue transaction %s: %v", txHash, err)
			// Continue processing other transactions even if one fails
		}
	}

	// Respond with success
	response.OK(c, nil)
}
