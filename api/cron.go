package api

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/asset"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/cron"
	cronfetchnewcollections "github.com/kryptogo/kg-wallet-backend/pkg/service/cron/cron-fetch-new-collections"
	cronmetadata "github.com/kryptogo/kg-wallet-backend/pkg/service/cron/cron-metadata"
	crontxjob "github.com/kryptogo/kg-wallet-backend/pkg/service/cron/cron-tx-job"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	assetpro "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	assetservice "github.com/kryptogo/kg-wallet-backend/service/asset"
)

type cronParams struct {
	CronTimeoutSeconds int `form:"cronTimeoutSeconds,default=600"`
}

// UpdateBalance .
func UpdateBalance(c *gin.Context) {
	cron.UpdateBalance(c.Request.Context())
	c.String(http.StatusOK, "done")
}

// UpdateUserBalance .
func UpdateUserBalance(c *gin.Context) {
	kgErr := cron.UpdateUserBalance(c.Request.Context())
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	c.String(http.StatusOK, "done")
}

// UpdateFloorFromOpensea .
func UpdateFloorFromOpensea(ctx *gin.Context) {
	params := cronParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	cron.UpdateFloorFromOpensea(ctx.Request.Context(), params.CronTimeoutSeconds)
	ctx.String(http.StatusOK, "done")
}

// UpdateNftMetadata .
func UpdateNftMetadata(ctx *gin.Context) {
	loop := true
	params := cronParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	cronmetadata.Update(ctx.Request.Context(), loop, params.CronTimeoutSeconds)
	ctx.String(http.StatusOK, "done")
}

// FetchNewCollections .
func FetchNewCollections(c *gin.Context) {
	cronfetchnewcollections.FromUserAsset(c.Request.Context())
	c.String(http.StatusOK, "done")
}

// UpdateAssetPrices .
func UpdateAssetPrices(c *gin.Context) {
	// We directly call this update function instead of calling AddUpdateAssetPricesJob since we're updating all addresses
	err := asset.UpdateAssetPrices(c.Request.Context(), nil)
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}
	c.String(http.StatusOK, "done")
}

// CheckAndUpdateAssetPrices .
func CheckAndUpdateAssetPrices(c *gin.Context) {
	err := asset.CheckAndUpdateAssetPrices(c.Request.Context())
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}
	c.String(http.StatusOK, "done")
}

// UpdateCoinList .
func UpdateCoinList(c *gin.Context) {
	kgErr := cron.UpdateCoinList(c.Request.Context())
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	c.String(http.StatusOK, "done")
}

// TxJob .
// /txJob/eth/5/60/599
// /txJob/matic/5/60/599
// /txJob/bsc/5/60/599
// /txJob/arb/5/60/599
// /txJob/kcc/5/60/599
// /txJob/btc/5/60/599
// /txJob/tron/5/60/599
// /txJob/sol/5/60/599
func TxJob(ctx *gin.Context) {
	chainID := ctx.Param("chainID")
	concurrentCnt := parseIntParam(ctx, "concurrentCnt", 5)
	idleBetweenBatch := parseIntParam(ctx, "idleBetweenBatch", 60)
	cronTimeoutSeconds := parseIntParam(ctx, "cronTimeoutSeconds", 599)

	tracing.AsyncOp(ctx.Request.Context(), "TxJob.deleteOldTxJobLogs", func(ctx context.Context) {
		err := tx.DeleteOldTxJobLogs(ctx)
		if err != nil {
			kglog.WarningWithDataCtx(ctx, "[HandleTxJob] deleteOldTxJobLogs", err)
		}
	})

	// Create a context with timeout based on cronTimeoutSeconds
	cronCtx, cancel := context.WithTimeout(ctx.Request.Context(), time.Duration(cronTimeoutSeconds)*time.Second)
	defer cancel() // Ensure that resources are released when the function completes

	kglog.InfoWithDataCtx(cronCtx, "TxJob", map[string]interface{}{
		"chainID":            chainID,
		"concurrentCnt":      concurrentCnt,
		"idleBetweenBatch":   idleBetweenBatch,
		"cronTimeoutSeconds": cronTimeoutSeconds,
	})
	crontxjob.Run(cronCtx, chainID, concurrentCnt, idleBetweenBatch)

	ctx.String(http.StatusOK, "done")
}

func parseIntParam(ctx *gin.Context, name string, defaultVal int) int {
	val := ctx.Param(name)
	if val == "" {
		return defaultVal
	}
	valInt, err := strconv.Atoi(val)
	if err != nil {
		return defaultVal
	}
	return valInt
}

// TxJobStat get and log stat
func TxJobStat(ctx *gin.Context) {
	stat := rdb.TxJobStats(ctx.Request.Context())
	kglog.InfoWithData("TxJobStat", stat)

	ctx.JSON(http.StatusOK, stat)
}

// CrawlerAirdropSheet .
func CrawlerAirdropSheet(ctx *gin.Context) {
	eventID := ctx.Param("eventID")
	cron.CrawlerAirdropSheet(ctx.Request.Context(), eventID)
	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}

// UpdateOrders .
func UpdateOrders(c *gin.Context) {
	cron.UpdateOrders(c.Request.Context())
	c.String(http.StatusOK, "done")
}

// DetectSigningServer .
func DetectSigningServer(c *gin.Context) {
	cron.DetectSigningServer(c.Request.Context())
	c.String(http.StatusOK, "detect signing server done")
}

// UpdateStudioNftProjects .
func UpdateStudioNftProjects(c *gin.Context) {
	cron.UpdateStudioNftProjects(c.Request.Context())
	c.String(http.StatusOK, "done")
}

// UpdateExchangeRate .
func UpdateExchangeRate(c *gin.Context) {
	cron.UpdateExchangeRate(c.Request.Context())
	c.String(http.StatusOK, "done")
}

// UpdateShipmentStatus .
func UpdateShipmentStatus(c *gin.Context) {
	cron.UpdateShipmentStatus(c.Request.Context())
	c.String(http.StatusOK, "done")
}

// CheckAssetProAlertThreshold .
func CheckAssetProAlertThreshold(c *gin.Context) {
	kgErr := assetpro.CheckAssetProAlertThreshold(c.Request.Context())
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	c.String(http.StatusOK, "done")
}

// RealtimePriceUpdate is an endpoint for realtime token price updates
// This is designed for tokens (like meme tokens) that require more frequent price updates
// This endpoint runs for a fixed duration of 10 minutes (599 seconds)
func RealtimePriceUpdate(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "realtime token price update")
	defer span.End()

	assetservice.AcquireAndProcessTasks(ctx)

	c.String(http.StatusOK, "done")
}

// SendPaymentItemReminders .
func SendPaymentItemReminders(c *gin.Context) {
	ctx := c.Request.Context()
	
	kglog.InfoCtx(ctx, "[cron] Starting SendPaymentItemReminders")
	
	results, err := getOrganizationsWithoutPaymentItems(ctx)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "[cron] Failed to get organizations without payment items", map[string]interface{}{
			"error": err.Error(),
		})
		c.String(http.StatusInternalServerError, err.Error())
		return
	}
	
	if len(results) == 0 {
		kglog.InfoCtx(ctx, "[cron] No organizations found without payment items")
		c.String(http.StatusOK, "No organizations found without payment items")
		return
	}
	
	emailsSent := 0
	emailsFailed := 0
	
	sendgridClient := sendgrid.NewClient()
	
	for _, result := range results {
		// 準備發送郵件的參數
		emailParams := map[string]interface{}{
			"Email":            result.UserEmail,
			"OrganizationName": result.OrganizationName,
		}
		
		kglog.InfoWithDataCtx(ctx, "[cron] Sending payment item reminder email", map[string]interface{}{
			"organization_id":   result.OrganizationID,
			"organization_name": result.OrganizationName,
			"user_email":        result.UserEmail,
			"user_name":         result.UserName,
		})
		
		// 發送郵件
		_, emailErr := sendgridClient.SendEmail(
			ctx,
			result.UserEmail,
			"KryptoGO Pay",
			sendgrid.EmailTypePaymentItemReminder,
			emailParams,
		)
		
		if emailErr != nil {
			kglog.ErrorWithDataCtx(ctx, "[cron] Failed to send payment item reminder email", map[string]interface{}{
				"organization_id":   result.OrganizationID,
				"organization_name": result.OrganizationName,
				"user_email":        result.UserEmail,
				"error":             emailErr.Error(),
			})
			emailsFailed++
		} else {
			kglog.InfoWithDataCtx(ctx, "[cron] Payment item reminder email sent successfully", map[string]interface{}{
				"organization_id":   result.OrganizationID,
				"organization_name": result.OrganizationName,
				"user_email":        result.UserEmail,
			})
			emailsSent++
		}
	}
	
	kglog.InfoWithDataCtx(ctx, "[cron] SendPaymentItemReminders completed", map[string]interface{}{
		"total_organizations": len(results),
		"emails_sent":         emailsSent,
		"emails_failed":       emailsFailed,
	})
	
	c.JSON(http.StatusOK, gin.H{
		"total_organizations": len(results),
		"emails_sent":         emailsSent,
		"emails_failed":       emailsFailed,
	})
}

type OrganizationWithoutPaymentItem struct {
	OrganizationID   int    `db:"organization_id"`
	OrganizationName string `db:"organization_name"`
	UserEmail        string `db:"user_email"`
	UserName         string `db:"user_name"`
}

func getOrganizationsWithoutPaymentItems(ctx context.Context) ([]OrganizationWithoutPaymentItem, error) {
	_, span := tracing.Start(ctx, "[cron] getOrganizationsWithoutPaymentItems")
	defer span.End()
	
	db := rdb.GetWith(ctx)
	
	query := `
		WITH ranked_users AS (
			SELECT 
				so.id as organization_id,
				so.name as organization_name,
				su.email as user_email,
				su.name as user_name,
				ROW_NUMBER() OVER (PARTITION BY su.email ORDER BY so.name, su.name) as rn
			FROM studio_organizations so
			LEFT JOIN payment_items pi ON so.id = pi.organization_id
			LEFT JOIN studio_users su ON so.id = su.organization_id
			WHERE pi.organization_id IS NULL  -- 沒有 payment item 的組織
				AND su.email IS NOT NULL       -- 確保有 email
				AND su.deleted_at IS NULL      -- 排除已刪除的用戶
				AND so.deleted_at IS NULL      -- 排除已刪除的組織
				AND su.status = 'active'
				AND so.id > 73                 -- 只要 organization_id 大於 73 的
			ORDER BY su.created_at DESC
		)
		SELECT 
			organization_id,
			organization_name,
			user_email,
			user_name
		FROM ranked_users
		WHERE rn = 1
		ORDER BY organization_name, user_name
	`
	
	var results []OrganizationWithoutPaymentItem
	err := db.Raw(query).Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query organizations without payment items: %w", err)
	}
	
	kglog.InfoWithDataCtx(ctx, "[cron] Query completed", map[string]interface{}{
		"organizations_found": len(results),
	})
	
	return results, nil
}