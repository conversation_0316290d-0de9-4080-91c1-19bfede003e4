package api

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/eth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/nft"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb/prize"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/redeem"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type prizesByUserResp struct {
	Code   int               `json:"code"`
	Data   *[]prize.VPrize   `json:"data"`
	Paging *rdb.PagingParams `json:"paging"`
}

// PrizesByUser returns user prize list
func PrizesByUser(ctx *gin.Context) {
	params := &prize.Params{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if params.Tagtype != "ACTIVE" && params.Tagtype != "FAVORITE" &&
		params.Tagtype != "HIDDEN" && params.Tagtype != "EXPIRED" &&
		params.Tagtype != "HISTORY" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid type")
		return
	}
	params.UID = auth.GetUID(ctx)
	params.ClientID = oauth.ClientID(ctx)
	if params.ChainID != "" { // empty chain_id means all chains
		nftChainID, ok := model.ToNftChainID[params.ChainID]
		if !ok {
			response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid chain_id")
			return
		}
		params.ChainID = nftChainID
	}
	prizes, paging, code, err := prize.List(ctx.Request.Context(), params)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}
	resp := new(prizesByUserResp)
	resp.Code = code
	resp.Data = prizes
	resp.Paging = paging
	ctx.JSON(http.StatusOK, resp)
}

type userPrizeResp struct {
	Code int           `json:"code"`
	Data *prize.VPrize `json:"data"`
}

// UserPrize returns single prize
func UserPrize(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	tokenID := ctx.Param("token_id")
	prizeIDStr := ctx.Param("prize_id")
	clientID := oauth.ClientID(ctx)
	prizeID, err := strconv.Atoi(prizeIDStr)
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid prize_id")
		return
	}

	userPrize, code, err := prize.Detail(ctx.Request.Context(), uid, clientID, int32(prizeID), tokenID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}
	resp := new(userPrizeResp)
	resp.Code = code
	resp.Data = userPrize
	ctx.JSON(http.StatusOK, resp)
}

// PrizeTag param: /:prize_id/:token_id/:type
func PrizeTag(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	tokenID := ctx.Param("token_id")
	prizeIDStr := ctx.Param("prize_id")
	tagType := ctx.Param("type")
	if tagType != prize.TagTypeFavorite && tagType != prize.TagTypeHidden {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid tag type")
		return
	}
	prizeID, err := strconv.Atoi(prizeIDStr)
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid prize_id")
		return
	}

	if ctx.Request.Method == "POST" {
		err = prize.AddTag(ctx.Request.Context(), uid, int32(prizeID), tokenID, tagType)
	} else if ctx.Request.Method == "DELETE" {
		err = prize.DeleteTag(ctx.Request.Context(), uid, int32(prizeID), tokenID, tagType)
	} else {
		ctx.AbortWithStatus(http.StatusMethodNotAllowed)
		return
	}
	if err != nil {
		response.BadRequestWithMsg(ctx, code.DBError, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}

type redeemPrizeRequest struct {
	PrizeID      int32  `json:"prize_id" binding:"required"`
	TokenID      string `json:"token_id" binding:"required"`
	OwnerAddress string `json:"owner_address" binding:"required"`
	Timestamp    int64  `json:"timestamp" binding:"required"`
	Quantity     int32  `json:"quantity" binding:"required"`
	Signature    string `json:"signature" binding:"required"`
	DryRun       bool   `json:"dry_run"`
}

type redeemPrizeSignatureMsg struct {
	PrizeID      int32  `json:"prize_id" binding:"required"`
	TokenID      string `json:"token_id"`
	OwnerAddress string `json:"owner_address" binding:"required"`
	Timestamp    int64  `json:"timestamp" binding:"required"`
	Quantity     int32  `json:"quantity" binding:"required"`
}

// PrizeRedeem redeem a prize
func PrizeRedeem(ctx *gin.Context) {
	ctxSpan, span := tracing.Start(ctx.Request.Context(), "api.PrizeRedeem")
	defer span.End()

	req := redeemPrizeRequest{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	timeDiff := time.Now().Unix() - int64(req.Timestamp)
	if timeDiff > 300 || timeDiff < -300 {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.DashboardSignatureExpired,
			Error: "signature expired",
		})
		redeem.PublishSign(req.Signature, code.DashboardSignatureExpired)
		return
	}

	msg, _ := json.Marshal(&redeemPrizeSignatureMsg{
		PrizeID:      req.PrizeID,
		TokenID:      req.TokenID,
		OwnerAddress: req.OwnerAddress,
		Timestamp:    req.Timestamp,
		Quantity:     req.Quantity,
	})
	valid := eth.VerifySignature(req.OwnerAddress, req.Signature, string(msg))
	if !valid {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.DashboardSignatureVerifyFailed,
			Error: "signature verify failed",
		})
		redeem.PublishSign(req.Signature, code.DashboardSignatureVerifyFailed)
		return
	}

	thePrize, errCode, err := rdb.PrizeByID(ctxSpan, req.PrizeID)
	if err != nil {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  errCode,
			Error: err.Error(),
		})
		redeem.PublishSign(req.Signature, errCode)
		return
	}

	valid, err = nft.VerifyOwnerOnChain(ctxSpan, thePrize.ChainID, req.TokenID, &alchemyapi.GetNFTsParams{
		Owner:             req.OwnerAddress,
		ContractAddresses: []string{thePrize.ContractAddress},
		WithMetadata:      false,
	})
	if err != nil {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.EtherscanFailed,
			Error: "etherscan api failed",
		})
		redeem.PublishSign(req.Signature, code.EtherscanFailed)
		return
	}
	if !valid {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.NotNFTOwner,
			Error: "Not NFT owner",
		})
		redeem.PublishSign(req.Signature, code.NotNFTOwner)
		return
	}

	if thePrize.EndTime.Before(time.Now()) {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.PrizeExpired,
			Error: "Coupon expired",
		})
		redeem.PublishSign(req.Signature, code.PrizeExpired)
		return
	}

	prizedRedeemed := rdb.PrizeRedeemed(ctxSpan, thePrize.ID, req.TokenID, thePrize.ChainID, thePrize.ContractAddress)
	if prizedRedeemed != nil && prizedRedeemed.TotalQuantity <= prizedRedeemed.RedeemedQuantity {
		ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
			Code:  code.CouponNotEnough,
			Error: "Coupon not enough",
		})
		redeem.PublishSign(req.Signature, code.CouponNotEnough)
		return
	}

	// check match traits
	if thePrize.MatchedTrait != nil {
		couponMatchedTrait := db.CouponMatchedTrait{}
		err := json.Unmarshal([]byte(*thePrize.MatchedTrait), &couponMatchedTrait)
		if err != nil {
			asset, err := nft.FetchAsset(ctxSpan, thePrize.ChainID, thePrize.ContractAddress, req.TokenID)
			if err != nil {
				ctx.JSON(http.StatusOK, &redeemCouponByNftResp{
					Code:  code.DBError,
					Error: err.Error(),
				})
				redeem.PublishSign(req.Signature, code.DBError)
				return
			}
			traits := unmarshalTraits(asset.Traits)
			if !containsTrait(couponMatchedTrait, traits) {
				response.BadRequestWithMsg(ctx, code.CouponTraitNotMatched, "Traits not matched")
				redeem.PublishSign(req.Signature, code.DBError)
				return
			}
		}
	}

	if req.DryRun {
		userPrize, code, err := prize.DetailForPublic(ctxSpan, req.PrizeID, req.TokenID)
		if err != nil {
			response.InternalServerErrorWithMsg(ctx, code, err.Error())
			return
		}
		resp := new(userPrizeResp)
		resp.Code = code
		resp.Data = userPrize
		ctx.JSON(http.StatusOK, resp)
		return
	}

	// redeem & log
	project, _, err := rdb.ProjectByID(ctxSpan, thePrize.ProjectID)
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, fmt.Sprintf("project not found: %d", thePrize.ProjectID))
		return
	}
	err = rdb.SaveRedeemLog(ctxSpan, project.ID, thePrize.ID, req.Quantity, req.TokenID, time.Unix(req.Timestamp, 0))
	if err != nil {
		kglog.ErrorWithData("save redeem log failed", map[string]interface{}{
			"project_id": project.ID,
			"prize_id":   thePrize.ID,
			"quantity":   req.Quantity,
			"token_id":   req.TokenID,
		})
		response.InternalServerError(ctx, code.DBError)
		return
	}

	resp := &redeemCouponByNftResp{}
	if thePrize.RedeemURL != nil {
		resp.URL = *thePrize.RedeemURL
	}
	if thePrize.SameCode != nil {
		resp.CouponCode = *thePrize.SameCode
	}
	resp.Data = "success"
	resp.Code = code.OK
	ctx.JSON(http.StatusOK, resp)
	redeem.PublishSign(req.Signature, code.OK)
}

type prizesByProjectResp struct {
	Code    int             `json:"code"`
	Project *prize.VProject `json:"project"`
	Data    *[]prize.VPrize `json:"data"`
}

// PrizesByProject returns prize list in a project
func PrizesByProject(ctx *gin.Context) {
	projectID, _ := strconv.Atoi(ctx.Param("project_id"))
	if projectID == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "project_id is empty")
		return
	}
	project, errCode, err := prize.ProjectWithSchema(ctx.Request.Context(), int32(projectID))
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	prizes, code, err := prize.ListByProjectID(ctx.Request.Context(), int32(projectID))
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	resp := new(prizesByProjectResp)
	resp.Code = code
	resp.Project = project
	resp.Data = prizes
	ctx.JSON(http.StatusOK, resp)
}

type prizesByNftResp struct {
	Code    int             `json:"code"`
	Project *prize.VProject `json:"project"`
	Nft     vNft            `json:"nft"`
	Data    *[]prize.VPrize `json:"data"`
}

type vNft struct {
	model.NftAsset
	OwnerAddress *string `json:"owner_address"`
}

// PrizesByNft returns prize list for a nft
func PrizesByNft(ctx *gin.Context) {
	chainID := ctx.Param("chain_id")
	contractAddress := ctx.Param("contract_address")
	tokenID := ctx.Param("token_id")

	asset, err := nft.FetchAsset(ctx.Request.Context(), chainID, contractAddress, tokenID)
	if err != nil {
		if errors.Is(err, code.ErrNftNotFound) {
			response.BadRequestWithMsg(ctx, code.NftNotFound, err.Error())
			return
		}
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	project, code, err := prize.ProjectWithSchemaByAddress(ctx.Request.Context(), chainID, contractAddress)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	prizes, code, err := prize.ListByNft(ctx.Request.Context(), chainID, contractAddress, tokenID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	nftOwner := rdb.NftOwner(ctx.Request.Context(), chainID, contractAddress, tokenID)
	nft := vNft{
		OwnerAddress: nftOwner,
	}
	_ = copier.Copy(&nft, asset)

	resp := new(prizesByNftResp)
	resp.Code = code
	resp.Project = project
	resp.Nft = nft
	resp.Data = prizes
	ctx.JSON(http.StatusOK, resp)
}
