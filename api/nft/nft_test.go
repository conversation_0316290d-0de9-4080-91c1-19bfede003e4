package nft

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/transpose"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/asset"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/nft"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestNftDetail(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	nft.InitTranspose(transpose.NewClient())

	nftDetailUrl := "/v1/nft_detail"
	server := gin.Default()
	server.GET(nftDetailUrl, NftDetail)

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateNftAsset(rdb.Get()))
	assert.Nil(t, rdbtest.CreateNftCollection(rdb.Get()))

	params := url.Values{
		"chain_id":         {"eth"},
		"contract_address": {"******************************************"},
		"token_id":         {"1"},
	}
	urlWithParam := nftDetailUrl + "?" + params.Encode()

	req, _ := http.NewRequest("GET", urlWithParam, nil)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var nftDetailResp struct {
		Code int `json:"code"`
		Data struct {
			PriceSymbol           string `json:"price_symbol"`
			FloorPrice            string `json:"floor_price"`
			AveragePrice          string `json:"average_price"`
			OneDayAveragePrice    string `json:"one_day_average_price"`
			SevenDayAveragePrice  string `json:"seven_day_average_price"`
			ThirtyDayAveragePrice string `json:"thirty_day_average_price"`
			Orders                []struct {
				Price     string `json:"price"`
				Side      string `json:"side"`
				StartTime int    `json:"start_time"`
				EndTime   int    `json:"end_time"`
			} `json:"orders"`
			LastPrice string `json:"last_price"`
		} `json:"data"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &nftDetailResp)
	assert.Nil(t, err)
	assert.Equal(t, 0, nftDetailResp.Code)
	assert.Equal(t, "ETH", nftDetailResp.Data.PriceSymbol)
	assert.Equal(t, "100000000000000000000", nftDetailResp.Data.AveragePrice)
	assert.Equal(t, "12345000000000000000", nftDetailResp.Data.FloorPrice)
	assert.Equal(t, "123456000000000000000", nftDetailResp.Data.OneDayAveragePrice)
	assert.Equal(t, "234567000000000000000", nftDetailResp.Data.SevenDayAveragePrice)
	assert.Equal(t, "345678000000000000000", nftDetailResp.Data.ThirtyDayAveragePrice)
	assert.NotZero(t, len(nftDetailResp.Data.Orders))
	assert.NotEmpty(t, nftDetailResp.Data.Orders[0].Price)
	assert.NotEmpty(t, nftDetailResp.Data.Orders[0].Side)
	assert.NotEmpty(t, nftDetailResp.Data.LastPrice)
	assert.NotEmpty(t, nftDetailResp.Data.Orders[0].StartTime)
	assert.NotEmpty(t, nftDetailResp.Data.Orders[0].EndTime)
}

func TestGetNftsEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	repo := rdb.GormRepo()
	oauth.Init(repo)
	application.Init(repo)
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())
	users, uids := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	mockAssetService := asset.NewMockIService(ctrl)
	asset.Set(mockAssetService)

	mockAssetService.EXPECT().GetNfts(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, uid string, param *asset.GetNftsParams) (*asset.GetNftsData, error) {
			t.Logf("param: %+v", param)
			assert.Equal(t, uids[0], param.UID)
			assert.Equal(t, uids[0], uid)
			assert.Equal(t, "ALL", param.Tagtype)
			assert.ElementsMatch(t, []string{}, param.ChainIDs)
			return &asset.GetNftsData{
				Nfts: &[]rdb.VNft{
					{
						OwnerAddress: "******************************************",
					},
				},
			}, nil
		},
	).Times(1)

	url := "/v1/nfts"
	server := gin.Default()
	server.GET(url, Nfts)

	fullURL := url + "?uid=" + uids[0] + "&type=ALL"
	req, _ := http.NewRequest("GET", fullURL, nil)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var responseBody map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &responseBody)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(responseBody["data"].([]interface{})))
}

func TestUpdateNftsEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	repo := rdb.GormRepo()
	oauth.Init(repo)
	application.Init(repo)
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())
	users, uids := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	mockAssetService := asset.NewMockIService(ctrl)
	asset.Set(mockAssetService)

	url := "/v1/nfts/update"
	server := gin.Default()
	server.POST(url, auth.MockAuthorize(uids[0]), UpdateNfts)

	{ // 1. Bad chain_ids
		requestBody := map[string]interface{}{}
		body, err := json.Marshal(requestBody)
		assert.Nil(t, err)

		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(body))
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "invalid chain_ids")
	}
	{ // 2. Normal case
		requestBody := map[string]interface{}{
			"chain_ids": []string{"eth", "matic", "ronin", "oasys"},
		}
		body, err := json.Marshal(requestBody)
		assert.Nil(t, err)

		mockAssetService.EXPECT().TryToUpdateAssetsAndWait(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, param *asset.UpdateAssetsParam) {
				t.Logf("param: %+v", param)
				assert.ElementsMatch(t, []string{"eth", "matic", "ronin", "oasys"}, param.SyncChainIDs)
				assert.ElementsMatch(t, []string{"nft"}, param.SyncAssetTypes)
				assert.ElementsMatch(t, []string{"******************************************", "******************************************", "******************************************", "******************************************", "******************************************"}, param.EthAddresses)
				assert.ElementsMatch(t, []string{}, param.SolAddresses)
				assert.ElementsMatch(t, []string{}, param.BtcAddresses)
				assert.ElementsMatch(t, []string{}, param.TronAddresses)
				assert.ElementsMatch(t, []string{"******************************************", "******************************************", "******************************************", "******************************************", "******************************************"}, param.AllAddresses)
				assert.False(t, param.ForceUpdate)
			},
		)

		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(body))
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
	}
}

func TestUpdateNfts(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())
	users, uids := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	mockAssetService := asset.NewMockIService(ctrl)
	asset.Set(mockAssetService)

	params := UpdateNftsParams{
		UID:             uids[0],
		ChainIDs:        dbmodel.NftChainIDs,
		Path:            "",
		ExcludeObserver: false,
		ClientID:        "test-client-id",
	}

	mockAssetService.EXPECT().TryToUpdateAssetsAndWait(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, param *asset.UpdateAssetsParam) {
			t.Logf("param: %+v", param)
			assert.ElementsMatch(t, []string{"eth", "matic", "ronin", "oasys"}, param.SyncChainIDs)
			assert.ElementsMatch(t, []string{"nft"}, param.SyncAssetTypes)
			assert.ElementsMatch(t, []string{"******************************************", "******************************************", "******************************************", "******************************************", "******************************************"}, param.EthAddresses)
			assert.ElementsMatch(t, []string{}, param.SolAddresses)
			assert.ElementsMatch(t, []string{}, param.BtcAddresses)
			assert.ElementsMatch(t, []string{}, param.TronAddresses)
			assert.ElementsMatch(t, []string{"******************************************", "******************************************", "******************************************", "******************************************", "******************************************"}, param.AllAddresses)
			assert.False(t, param.ForceUpdate)
		},
	)

	err := updateNfts(context.Background(), params)
	assert.Nil(t, err)
}
