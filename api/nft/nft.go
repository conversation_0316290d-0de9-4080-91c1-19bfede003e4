package nft

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	openseaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/opensea-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/airdrop"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/airdrop/module"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/asset"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/coingecko"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/nft"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	lo "github.com/samber/lo"
	"go.opentelemetry.io/otel/attribute"

	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
)

// AddOrDeleteNftTag param: /:type/:chain/:address/:token_id
func AddOrDeleteNftTag(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	tagType := ctx.Param("type")
	chainID := ctx.Param("chain")
	address := ctx.Param("address")
	tokenID := ctx.Param("token_id")
	if tagType != "FAVORITE" && tagType != "HIDDEN" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid tag type")
		return
	}

	var err error
	switch ctx.Request.Method {
	case "POST":
		err = nft.AddTag(ctx.Request.Context(), uid, tagType, chainID, address, tokenID)
	case "DELETE":
		err = rdb.DeleteTag(ctx.Request.Context(), uid, tagType, chainID, address, tokenID)
	default:
		ctx.AbortWithStatus(http.StatusMethodNotAllowed)
		return
	}
	if err != nil {
		response.BadRequestWithMsg(ctx, code.DBError, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}

type nftsResp struct {
	Code   int               `json:"code"`
	Data   *[]rdb.VNft       `json:"data"`
	Paging *rdb.PagingParams `json:"paging"`
}

// Nfts returns user nfts
func Nfts(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.Nfts")
	defer span.End()

	params := &rdb.NftParams{}
	kgErr := util.ToGinContextExt(c).BindQuery(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if params.Tagtype != "FAVORITE" && params.Tagtype != "HIDDEN" && params.Tagtype != "OTHERS" && params.Tagtype != "ALL" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid type")
		return
	}
	if params.ChainID != "" && params.ChainID != "ethereum" && params.ChainID != "polygon" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid chain_id")
		return
	}
	// update nft when user open nft page
	clientID := oauth.ClientID(c)
	params.ClientID = clientID
	uid := params.UID
	if uid == "" {
		uid = auth.GetUID(c)
	}

	nftData, kgErr := asset.Get().GetNfts(ctx, uid, &asset.GetNftsParams{
		NftParams: *params,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	resp := new(nftsResp)
	resp.Code = nftData.Code
	resp.Data = nftData.Nfts
	resp.Paging = nftData.Paging
	c.JSON(http.StatusOK, resp)
}

// UpdateNftsParams is params for updating nfts
type UpdateNftsParams struct {
	ChainIDs        []string `json:"chain_ids"`
	Path            string   `json:"path"`
	ExcludeObserver bool     `json:"exclude_observer"`
	ForceUpdate     bool     `json:"force_update"`
	UID             string
	ClientID        string
}

func updateNfts(ctx context.Context, params UpdateNftsParams) *code.KGError {
	ctx, span := tracing.Start(ctx, "api.updateNfts")
	defer span.End()

	if params.UID == "" {
		return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("uid is empty"), nil)
	}

	user, kgErr := rdb.GormRepo().GetUser(ctx, params.UID, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if kgErr != nil {
		return kgErr
	}

	var walletAddressesByChainID map[string][]string
	var queryChains []string = lo.Intersect(model.NftChainIDs, params.ChainIDs)
	if len(queryChains) == 0 {
		return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("invalid chain_ids: %v. Available chain_ids are: %v", params.ChainIDs, model.NftChainIDs), nil)
	}

	walletAddressesByChainID = user.AddressesByChains(params.Path, queryChains, params.ExcludeObserver)
	if len(walletAddressesByChainID) == 0 {
		return code.NewKGError(code.UserWalletNotFound, http.StatusBadRequest, errors.New("user wallet not found"), nil)
	}
	// walletAddressesByChainID will have the same addresses array for all evm chains
	// so we can use the first chain id to get the evm addresses
	evmChainID := queryChains[0]
	walletAddresses := walletAddressesByChainID[evmChainID]
	asset.Get().TryToUpdateAssetsAndWait(
		ctx,
		&asset.UpdateAssetsParam{
			ForceUpdate:    params.ForceUpdate,
			EthAddresses:   walletAddressesByChainID[evmChainID],
			AllAddresses:   walletAddresses,
			SyncAssetTypes: []string{model.AssetTypeNft},
			SyncChainIDs:   queryChains,
		},
	)
	return nil
}

// UpdateNfts triggers nfts update
func UpdateNfts(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.UpdateNfts")
	defer span.End()

	params := UpdateNftsParams{}
	kgErr := util.ToGinContextExt(c).BindRawBody(&params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	params.ClientID = oauth.ClientID(c)
	params.UID = auth.GetUID(c)
	kgErr = updateNfts(ctx, params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 0})
}

type nftsBySlugResp struct {
	Code   int               `json:"code"`
	Data   *[]rdb.VNft       `json:"data"`
	Stats  *rdb.VNftStats    `json:"stats"`
	Paging *rdb.PagingParams `json:"paging"`
}

// NftsBySlug returns user nfts by slug
func NftsBySlug(ctx *gin.Context) {
	ctxSpan, span := tracing.Start(ctx.Request.Context(), "api.NftsBySlug")
	defer span.End()

	params := &rdb.NftParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	slug := ctx.Param("slug")
	if slug == "" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "slug is empty")
		return
	}
	params.Slug = slug
	params.Tagtype = "SLUG_ALL"
	params.UID = auth.GetUID(ctx)

	// convert chain_id to chain_ids
	params.Path = "" // all
	if params.ChainID == "" {
		params.ChainIDs = model.NftChainIDs
	} else {
		params.ChainIDs = []string{params.ChainID}
	}
	clientID := oauth.ClientID(ctx)
	params.ClientID = clientID
	user, _ := rdb.GormRepo().GetUser(ctxSpan, auth.GetUID(ctx), "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	// get user wallet addresses
	queryChains := lo.Intersect(model.NftChainIDs, params.ChainIDs)
	walletAddressesByChainID := user.AddressesByChains(params.Path, queryChains, params.ExcludeObserver)
	if len(walletAddressesByChainID) == 0 {
		response.BadRequestWithMsg(ctx, code.UserWalletNotFound, "user wallet not found")
		return
	}

	nfts, paging, code, err := rdb.Nfts(ctxSpan, params, walletAddressesByChainID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	stats, code, err := rdb.NftStats(ctxSpan, params)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	stats.TotalProfit = stats.TotalFloor - stats.TotalCost
	if stats.TotalProfit != 0 {
		ethQuote, _ := coingecko.Get().QuoteInUSD(ctxSpan, "ethereum")
		stats.TotalProfitUSD = stats.TotalProfit * ethQuote
	}

	resp := new(nftsBySlugResp)
	resp.Code = code
	resp.Data = nfts
	resp.Stats = stats
	resp.Paging = paging
	ctx.JSON(http.StatusOK, resp)
}

type nftsValueResp struct {
	Code   int     `json:"code"`
	Values float64 `json:"values"`
}

// NftsValue returns user nft values
func NftsValue(ctx *gin.Context) {
	chainID := ctx.Query("chain_id")
	if chainID != "" && chainID != "ethereum" && chainID != "polygon" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid chain_id")
		return
	}

	uid := auth.GetUID(ctx)
	clientID := oauth.ClientID(ctx)
	ethValue, code, err := rdb.NftsValue(ctx.Request.Context(), uid, chainID, clientID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	ethQuote, _ := coingecko.Get().QuoteInUSD(ctx, "ethereum")

	resp := new(nftsValueResp)
	resp.Code = code
	resp.Values = ethValue * ethQuote
	ctx.JSON(http.StatusOK, resp)
}

type nftCollectionsResp struct {
	Code int             `json:"code"`
	Data []nftCollection `json:"data"`
}
type nftCollection struct {
	Slug                string  `json:"slug"`
	Name                string  `json:"name"`
	ImageURL            string  `json:"image_url"`
	Amount              string  `json:"amount"`
	WeiFloorPrice       string  `json:"floor_price"`
	WeiFloorPriceSymbol string  `json:"floor_price_symbol"`
	Value               float64 `json:"value"`
	ContractAddress     string  `json:"contract_address"` // workaround for legacy history price
}

// NftCollections returns user nft values by collection
func NftCollections(ctx *gin.Context) {
	params := &rdb.NftCollectionsReq{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if params.ChainID != "" && params.ChainID != "ethereum" && params.ChainID != "polygon" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid chain_id")
		return
	}

	uid := auth.GetUID(ctx)
	clientID := oauth.ClientID(ctx)
	nftCollections, code, err := rdb.NftCollections(ctx.Request.Context(), uid, clientID, params)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	ethQuote, _ := coingecko.Get().QuoteInUSD(ctx, "ethereum")
	data := make([]nftCollection, 0)
	for _, v := range *nftCollections {
		decAmount := decimal.NewFromInt(v.Amount)
		data = append(data, nftCollection{
			Slug:                v.CollectionSlug,
			Name:                v.CollectionName,
			ImageURL:            v.CollectionImageURL,
			Amount:              decAmount.String(),
			WeiFloorPrice:       util.EthToWei(v.FloorPrice),
			WeiFloorPriceSymbol: "WEI",
			Value:               v.EthValue * ethQuote,
			ContractAddress:     v.ContractAddress,
		})
	}

	resp := new(nftCollectionsResp)
	resp.Code = code
	resp.Data = data
	ctx.JSON(http.StatusOK, resp)
}

type nftsReceivedResp struct {
	Code int                 `json:"code"`
	Data []*rdb.VNftReceived `json:"data"`
}

// NftsReceived returns user nfts received
func NftsReceived(ctx *gin.Context) {
	params := &rdb.NftsReceivedParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	params.UID = auth.GetUID(ctx)
	now := time.Now()
	minStartTime := now.AddDate(0, 0, -30*3)
	params.StartedAtTime = time.Unix(params.StartedAt, 0)
	if params.StartedAtTime.After(now) {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "started_at is in the future")
		return
	}
	if params.StartedAtTime.Before(minStartTime) {
		params.StartedAtTime = minStartTime
	}
	params.ClientID = oauth.ClientID(ctx)

	// user info
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), params.UID, params.ClientID, false, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}
	nfts, code, err := rdb.NftsReceived(ctx.Request.Context(), params, user.HideSpamNft)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}
	resp := new(nftsReceivedResp)
	resp.Code = code
	resp.Data = *nfts
	ctx.JSON(http.StatusOK, resp)
}

type nftsByCollectionsResp struct {
	Code   int                      `json:"code"`
	Data   *[]*rdb.VNftByCollection `json:"data"`
	Paging *rdb.PagingParams        `json:"paging"`
}

// NftsByCollections returns user nfts by collections
func NftsByCollections(ctx *gin.Context) {
	params := &rdb.NftsByCollectionsParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// check params
	for _, chainID := range params.ChainIDs {
		if _, ok := model.NftChainSet[chainID]; !ok {
			response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid chain_id")
			return
		}
	}
	var walletAddressesByChainID map[string][]string
	if params.UID == "" {
		params.UID = auth.GetUID(ctx)
		// get user wallet addresses
		user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), params.UID, "", true, &domain.UserPreloads{
			WithWallets: true,
		})
		if user == nil {
			response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
			return
		}
		queryChains := lo.Intersect(model.NftChainIDs, params.ChainIDs)
		walletAddressesByChainID = user.AddressesByChains(params.Path, queryChains, params.ExcludeObserver)
		if len(walletAddressesByChainID) == 0 {
			response.BadRequestWithMsg(ctx, code.UserWalletNotFound, "user wallet not found")
			return
		}
	} else {
		if len(params.ChainIDs) == 0 {
			params.ChainIDs = model.NftChainIDs
		}
		chains := lo.Map(params.ChainIDs, func(chainID string, _ int) domain.Chain {
			return domain.IDToChain(chainID)
		})
		receiveAddresses, kgErr := rdb.GormRepo().GetUserDefaultAddresses(ctx, params.UID)
		if kgErr != nil {
			response.KGError(ctx, kgErr)
			return
		}
		walletAddressesByChainID = make(map[string][]string)
		for _, chain := range chains {
			walletAddressesByChainID[chain.ID()] = []string{receiveAddresses[chain].String()}
		}
	}

	nfts, paging, code, err := rdb.NftsByCollections(ctx.Request.Context(), params, &walletAddressesByChainID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}
	resp := new(nftsByCollectionsResp)
	resp.Code = code
	resp.Data = nfts
	resp.Paging = paging
	ctx.JSON(http.StatusOK, resp)
}

type nftMint91APPReq struct {
	ContractAddress   string `json:"contract_address" binding:"required"`
	TransferToAddress string `json:"transfer_to_address" binding:"required"`
	RequestID         string `json:"request_id" binding:"required"`
	CallbackEndpoint  string `json:"callback_endpoint" binding:"required"`
}

// NftMint91APP mint nft for 91APP
func NftMint91APP(ctx *gin.Context) {

	params := &nftMint91APPReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// validate params
	if params.RequestID == "" || params.CallbackEndpoint == "" ||
		params.TransferToAddress == "" || params.ContractAddress == "" {
		response.InternalServerErrorWithMsg(ctx, code.ParamIncorrect, "param is empty")
		return
	}

	// the requested contract address is in the whitelist
	jwtClaims, ok := ctx.Get("jwt_claims")
	if !ok {
		response.InternalServerErrorWithMsg(ctx, code.IDTokenNotValid, "Invalid jwt token")
		return
	}
	allowedContractAddress := jwtClaims.(*auth.ContractAddressClaims).ContractAddressList
	if !util.ContainsInsensitive(allowedContractAddress, params.ContractAddress) {
		response.BadRequestWithMsg(ctx, code.InvalidContractAddress, "Invalid Contract Address")
		return
	}

	// Valid contract address
	event, errCode, err := rdb.GetValidAirdropEventByAddress(ctx.Request.Context(), params.ContractAddress)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	moduleData := &module.Data{
		Event: event,
		Callback: &module.CallbackInfo{
			RequestID:        params.RequestID,
			CallbackEndpoint: params.CallbackEndpoint,
		},
	}

	_, errCode, err = airdrop.NFT(ctx.Request.Context(), event, moduleData, params.TransferToAddress, "")
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0},
	)

}

type nftRefreshResp struct {
	Code int             `json:"code"`
	Data *model.NftAsset `json:"data"`
}

// RefreshNft refresh nft metadata
func RefreshNft(ctx *gin.Context) {
	chainID := ctx.Param("chain")
	contractAddress := ctx.Param("address")
	tokenID := ctx.Param("token_id")
	if chainID == "" || contractAddress == "" || tokenID == "" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "param is empty")
		return
	}

	if _, ok := openseaapi.ChainIDToOpenSeaChain[chainID]; !ok {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "chain is not supported")
		return
	}

	err := openseaapi.Get().RefreshMetadata(ctx.Request.Context(), chainID, contractAddress, tokenID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.OpenseaFailed, err.Error())
		return
	}

	// get nft asset
	assets := rdb.NftsByContract(ctx.Request.Context(), chainID, contractAddress, tokenID)
	if len(assets) == 0 {
		response.InternalServerErrorWithMsg(ctx, code.NftNotFound, "asset not found")
		return
	}
	asset := assets[0]
	asset.ChainID = chainID
	asset.ContractAddress = contractAddress
	kglog.DebugWithData("UpdateMetadata, asset", asset)

	// update nft asset from opensea
	nft.UpdateMetadataFromOpensea(ctx.Request.Context(), asset)

	resp := new(nftRefreshResp)
	resp.Code = 0
	resp.Data = asset
	ctx.JSON(http.StatusOK, resp)
}

type nftDetailResp struct {
	PriceSymbol           string     `json:"price_symbol"`
	FloorPrice            string     `json:"floor_price"`
	AveragePrice          string     `json:"average_price"`
	OneDayAveragePrice    string     `json:"one_day_average_price"`
	SevenDayAveragePrice  string     `json:"seven_day_average_price"`
	ThirtyDayAveragePrice string     `json:"thirty_day_average_price"`
	Orders                []nftOrder `json:"orders"`
	LastPrice             *string    `json:"last_price"`
}
type nftOrder struct {
	Price     string `json:"price"`
	Side      string `json:"side"`
	StartTime int    `json:"start_time"`
	EndTime   int    `json:"end_time"`
}

func (resp *nftDetailResp) convertFromService(serviceResp *nft.Detail) {
	resp.PriceSymbol = serviceResp.PriceSymbol
	resp.FloorPrice = serviceResp.FloorPrice
	resp.AveragePrice = serviceResp.AveragePrice
	resp.OneDayAveragePrice = serviceResp.OneDayAveragePrice
	resp.SevenDayAveragePrice = serviceResp.SevenDayAveragePrice
	resp.ThirtyDayAveragePrice = serviceResp.ThirtyDayAveragePrice
	resp.Orders = make([]nftOrder, 0, len(serviceResp.Orders))
	for _, order := range serviceResp.Orders {
		resp.Orders = append(resp.Orders, nftOrder{
			Price:     order.Price,
			Side:      string(order.Side),
			StartTime: order.StartTime,
			EndTime:   order.EndTime,
		})
	}
	resp.LastPrice = serviceResp.LastPrice
}

// NftDetail returns user nfts
func NftDetail(c *gin.Context) {
	ctxSpan, span := tracing.Start(c.Request.Context(), "api.NftDetail")
	span.SetAttributes(attribute.Float64(tracing.AttrKeySampleRate, 0.5)) // higher due to slow query was observed
	defer span.End()

	chainID := c.Param("chain_id")
	contractAddress := c.Param("contract_address")
	tokenID := c.Param("token_id")

	vNftDetail := new(nftDetailResp)
	vNftDetail.Orders = make([]nftOrder, 0)

	// FIXME: polygon nft currently fetched from alchemy
	// so, we don't support polygon nft market detail now
	// should be fixed with SaveNftAsset() in the future
	if chainID != model.NftChainIDEthereum {
		// FIXME: should return error after app hotfix
		response.OK(c, vNftDetail)
		return
	}

	nftDetail, kgErr := nft.GetNftDetail(ctxSpan, chainID, contractAddress, tokenID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	vNftDetail.convertFromService(nftDetail)

	response.OK(c, vNftDetail)
}
