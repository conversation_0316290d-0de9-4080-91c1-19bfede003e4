package quest

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/user"
)

type AddressExistsRequest struct {
	Address string `json:"address"`
	ChainID string `json:"chain_id"`
}

func AddressExists(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.AddressExists")
	defer span.End()

	var req AddressExistsRequest
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	chain := domain.IDToChain(req.ChainID)
	if chain == nil {
		response.BadRequestWithMsg(c, code.ChainIDNotSupported, "invalid chain id")
		return
	}

	exists := user.CheckAddressExistence(ctx, chain, req.Address)

	response.OK(c, gin.H{"address": req.Address, "is_ok": exists})
}
