package quest

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

type addressExistsResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Address string `json:"address"`
		IsOk    bool   `json:"is_ok"`
	} `json:"data"`
}

func TestAddressExists(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type compositeRepo struct {
		domain.UserRepo
		domain.AddressRepo
	}
	tests := []struct {
		name               string
		request            AddressExistsRequest
		expectedStatusCode int
		expectedCode       int
		expectedIsOk       bool
		expectedErrMsg     string
		setupFn            func()
	}{
		{
			name: "invalid chain id",
			request: AddressExistsRequest{
				Address: "9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b",
				ChainID: "invalid_chain",
			},
			expectedStatusCode: 400,
			expectedCode:       1020,
			expectedErrMsg:     "invalid chain id",
		},
		{
			name: "empty address",
			request: AddressExistsRequest{
				Address: "",
				ChainID: "sol",
			},
			expectedStatusCode: 200,
			expectedCode:       0,
			expectedIsOk:       false,
		},
		{
			name: "valid non-existent solana address",
			request: AddressExistsRequest{
				Address: "9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b",
				ChainID: "sol",
			},
			expectedStatusCode: 200,
			expectedCode:       0,
			expectedIsOk:       false,
			setupFn: func() {
				addrRepo := user.NewMockIRepo(ctrl)
				mockUserRepo := compositeRepo{
					UserRepo:    rdb.GormRepo(),
					AddressRepo: addrRepo,
				}
				addrRepo.EXPECT().CheckAddressOwnedByUser(gomock.Any(),
					domain.Solana, domain.NewAddressByChain(domain.Solana, "9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b")).Return(false, nil)
				user.Init(mockUserRepo)
			},
		},
		{
			name: "valid existent solana address",
			request: AddressExistsRequest{
				Address: "9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b",
				ChainID: "sol",
			},
			expectedStatusCode: 200,
			expectedCode:       0,
			expectedIsOk:       true,
			setupFn: func() {
				addrRepo := user.NewMockIRepo(ctrl)
				mockUserRepo := compositeRepo{
					UserRepo:    rdb.GormRepo(),
					AddressRepo: addrRepo,
				}
				addrRepo.EXPECT().CheckAddressOwnedByUser(gomock.Any(),
					domain.Solana, domain.NewAddressByChain(domain.Solana, "9ZNTfG4NyQgxy2SWjSiQoUyBPEvXT2xo7fKc5hPYYJ7b")).Return(true, nil)
				user.Init(mockUserRepo)
			},
		},
	}

	server := gin.Default()
	server.POST("/", AddressExists)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setupFn != nil {
				tt.setupFn()
			}
			jsonStr, err := json.Marshal(tt.request)
			assert.NoError(t, err)

			req, err := http.NewRequest("POST", "/", bytes.NewBuffer(jsonStr))
			assert.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			server.ServeHTTP(w, req)

			var response addressExistsResponse
			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			assert.Equal(t, tt.expectedCode, response.Code)
			assert.Equal(t, tt.expectedStatusCode, w.Code)
			if tt.expectedCode == 0 {
				assert.Equal(t, tt.request.Address, response.Data.Address)
				assert.Equal(t, tt.expectedIsOk, response.Data.IsOk)
			} else {
				assert.Equal(t, tt.expectedErrMsg, response.Message)
			}
		})
	}
}
