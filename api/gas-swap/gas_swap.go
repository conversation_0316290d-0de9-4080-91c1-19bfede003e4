package gasswap

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	gasswap "github.com/kryptogo/kg-wallet-backend/pkg/service/gas-swap"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

// GetAvailableChains .
func GetAvailableChains(c *gin.Context) {
	ctx := c.Request.Context()

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	chains, kgErr := gasswap.GetAvailableChains(ctx, orgID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, struct {
		ChainIDs []string `json:"chain_ids"`
	}{ChainIDs: chains})
}

type getAvailableAssetsReq struct {
	ChainID string `form:"chain_id" binding:"required"`
	Path    string `form:"path"`
}

type getAvailableAssetsRes struct {
	Assets []*availableAsset `json:"assets"`
}

type availableAsset struct {
	ChainID       string          `json:"chain_id"`
	AssetGroup    string          `json:"asset_group"`
	Name          string          `json:"name"`
	Symbol        string          `json:"symbol"`
	Amount        float64         `json:"amount"`
	AmountStr     string          `json:"amount_str"`
	USDValue      float64         `json:"usd_value"`
	Decimals      int             `json:"decimals"`
	Price         float64         `json:"price"`
	Wallets       []walletBalance `json:"wallets"`
	LogoUrls      []string        `json:"logo_urls"`
	MinimumAmount float64         `json:"minimum_amount"`
}

type walletBalance struct {
	Address   string  `json:"address"`
	Amount    float64 `json:"amount"`
	AmountStr string  `json:"amount_str"`
	UsdValue  float64 `json:"usd_value"`
}

// GetAvailableAssets .
func GetAvailableAssets(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")
	params := &getAvailableAssetsReq{}
	kgErr := util.ToGinContextExt(c).BindQuery(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	assets, kgErr := gasswap.GetAvailableAssets(ctx, orgID, uid, params.ChainID, params.Path)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	resp := &getAvailableAssetsRes{
		Assets: lo.Map(assets, func(asset *domain.GasSwapAsset, _ int) *availableAsset {
			return &availableAsset{
				ChainID:    asset.ChainID,
				AssetGroup: asset.AssetGroup,
				Name:       asset.Name,
				Symbol:     asset.Symbol,
				Amount:     util.ParseFloat64OrDefault(asset.Amount, 0),
				AmountStr:  asset.Amount,
				USDValue:   asset.UsdValue,
				Decimals:   asset.Decimals,
				Price:      asset.Price,
				Wallets: lo.Map(asset.Wallets, func(wallet *domain.WalletBalance, _ int) walletBalance {
					return walletBalance{
						Address:   wallet.Address,
						Amount:    wallet.Amount,
						AmountStr: wallet.AmountStr,
						UsdValue:  wallet.UsdValue,
					}
				},
				),
				LogoUrls:      asset.LogoUrls,
				MinimumAmount: asset.MinimumAmount,
			}
		}),
	}
	response.OK(c, resp)
}

type getQuoteReq struct {
	ChainID      string  `form:"chain_id" binding:"required"`
	TokenAddress string  `form:"token_address" binding:"required"`
	Amount       *string `form:"amount"`
}

// GetQuote .
func GetQuote(c *gin.Context) {
	ctx := c.Request.Context()

	params := &getQuoteReq{}
	if kgErr := util.ToGinContextExt(c).BindQuery(params); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	var specificAmount *decimal.Decimal
	if params.Amount != nil {
		parsedAmount, err := decimal.NewFromString(*params.Amount)
		if err != nil {
			response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
			return
		}

		specificAmount = &parsedAmount
	}

	quote, kgErr := gasswap.GetQuotes(ctx, orgID, params.ChainID, params.TokenAddress, specificAmount)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, quote)
}

type initReq struct {
	ChainID       string   `json:"chain_id" binding:"required"`
	TokenAddress  string   `json:"token_address" binding:"required"`
	Amount        string   `json:"amount_str" binding:"required"`
	ReceiveWallet string   `json:"receive_wallet" binding:"required"`
	SignedTxs     []string `json:"signed_txs" binding:"required,min=1"`
}

// Init .
func Init(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")
	params := &initReq{}

	if kgErr := util.ToGinContextExt(c).BindJson(params); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	id, kgErr := gasswap.Create(ctx, &gasswap.CreateParams{
		OrgID:         orgID,
		UID:           uid,
		ChainID:       params.ChainID,
		TokenAddress:  params.TokenAddress,
		Amount:        params.Amount,
		ReceiveWallet: params.ReceiveWallet,
		SignedTxs:     params.SignedTxs,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp := struct {
		GasSwapID int `json:"gas_swap_id"`
	}{GasSwapID: id}
	response.OK(c, resp)
}

type getGasSwapResp struct {
	ChainID             string  `json:"chain_id"`
	TokenAddress        string  `json:"token_address"`
	AmountStr           string  `json:"amount_str"`
	EstimatedReceive    float64 `json:"estimated_receive"`
	EstimatedReceiveUsd float64 `json:"estimated_receive_usd"`
	ReceiveWallet       string  `json:"receive_wallet"`
	CreatedAt           int64   `json:"created_at"`
	EstimatedFinishAt   int64   `json:"estimated_finish_at"`
	Status              string  `json:"status"`
	GasFaucetTxHash     *string `json:"gas_faucet_tx_hash"`
	UserApproveTxHash   *string `json:"user_approve_tx_hash"`
	GasSwapTxHash       *string `json:"gas_swap_tx_hash"`
}

// GetByID .
func GetByID(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}
	gasSwap, kgErr := gasswap.GetByID(ctx, uid, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	createdAt := gasSwap.CreatedAt.Unix()
	estimatedFinishAt := gasSwap.EstimatedFinishAt.Unix()
	resp := &getGasSwapResp{
		ChainID:             gasSwap.ChainID,
		TokenAddress:        gasSwap.TokenAddress,
		AmountStr:           gasSwap.Amount,
		EstimatedReceive:    gasSwap.EstimatedReceive,
		EstimatedReceiveUsd: gasSwap.EstimatedReceiveUsd,
		ReceiveWallet:       gasSwap.ReceiveWallet,
		CreatedAt:           createdAt,
		EstimatedFinishAt:   estimatedFinishAt,
		Status:              gasSwap.Status.String(),
		GasFaucetTxHash:     gasSwap.GasFaucetTxHash,
		UserApproveTxHash:   gasSwap.UserApproveTxHash,
		GasSwapTxHash:       gasSwap.GasSwapTxHash,
	}
	response.OK(c, resp)
}

// Handle .
func Handle(c *gin.Context) {
	ctx := c.Request.Context()
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}
	kgErr := gasswap.Handle(ctx, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}

type enableReq struct {
	ChainID string `json:"chain_id" binding:"required"`
}

// Enable enable organization gas swap feature
func Enable(c *gin.Context) {
	ctx := c.Request.Context()
	orgID, err := strconv.Atoi(c.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid org id")
		return
	}
	params := &enableReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	txHash, kgErr := gasswap.Enable(ctx, orgID, params.ChainID)
	kglog.InfoWithDataCtx(ctx, "gas swap enabled", map[string]interface{}{"org_id": orgID, "chain_id": params.ChainID, "tx": txHash})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}

type setConfigReq struct {
	Tokens []*configToken `json:"supported_tokens" binding:"required"`
}

type configToken struct {
	ChainID      string `json:"chain_id" binding:"required"`
	TokenAddress string `json:"token_address" binding:"required"`
}

// SetConfig set gas swap config
func SetConfig(c *gin.Context) {
	ctx := c.Request.Context()
	orgID, err := strconv.Atoi(c.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid org id")
		return
	}
	params := &setConfigReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	kgErr = gasswap.SaveConfig(ctx, orgID, lo.Map(params.Tokens, func(token *configToken, _ int) *domain.GasSwapToken {
		return &domain.GasSwapToken{
			ChainID:         token.ChainID,
			ContractAddress: token.TokenAddress,
		}
	}))
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}
