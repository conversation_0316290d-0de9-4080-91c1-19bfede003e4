package gasswapv2

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	gasswap "github.com/kryptogo/kg-wallet-backend/service/gas-swap"
	"github.com/shopspring/decimal"
)

type getQuoteReqV2 struct {
	ChainID       string  `form:"chain_id" binding:"required"`
	TokenAddress  string  `form:"token_address" binding:"required"`
	ReceiveWallet string  `form:"receive_wallet" binding:"required"`
	Amount        *string `form:"amount"`
}

// GetQuoteV2 returns the quote for gas swap
func GetQuoteV2(c *gin.Context) {
	ctx := c.Request.Context()

	params := &getQuoteReqV2{}
	if kgErr := util.ToGinContextExt(c).BindQuery(params); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	var specificAmount *decimal.Decimal
	if params.Amount != nil {
		parsedAmount, err := decimal.NewFromString(*params.Amount)
		if err != nil {
			response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
			return
		}

		specificAmount = &parsedAmount
	}

	chain := domain.IDToChain(params.ChainID)
	tokenAddr := domain.NewTronAddress(params.TokenAddress)
	receiveAddr := domain.NewTronAddress(params.ReceiveWallet)
	quote, kgErr := gasswap.GetQuotesV2(ctx, orgID, chain, receiveAddr, tokenAddr, specificAmount)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, quote)
}

type initReqV2 struct {
	ChainID       string   `json:"chain_id" binding:"required"`
	TokenAddress  string   `json:"token_address" binding:"required"`
	Amount        string   `json:"amount_str" binding:"required"`
	ReceiveWallet string   `json:"receive_wallet" binding:"required"`
	SignedTxs     []string `json:"signed_txs" binding:"required,min=1"`
}

// InitV2 creates a gas swap request
func InitV2(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")
	params := &initReqV2{}

	if kgErr := util.ToGinContextExt(c).BindJson(params); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	chain := domain.IDToChain(params.ChainID)
	tokenAddr := domain.NewTronAddress(params.TokenAddress)
	receiveAddr := domain.NewTronAddress(params.ReceiveWallet)
	id, kgErr := gasswap.CreateV2(ctx, &gasswap.CreateParamsV2{
		OrgID:         orgID,
		UID:           uid,
		Chain:         chain,
		TokenAddress:  tokenAddr,
		Amount:        params.Amount,
		ReceiveWallet: receiveAddr,
		SignedTxs:     params.SignedTxs,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp := struct {
		GasSwapID int `json:"gas_swap_id"`
	}{GasSwapID: id}
	response.OK(c, resp)
}

type getGasSwapRespV2 struct {
	ChainID             string  `json:"chain_id"`
	TokenAddress        string  `json:"token_address"`
	AmountStr           string  `json:"amount_str"`
	EstimatedReceive    float64 `json:"estimated_receive"`
	EstimatedReceiveUsd float64 `json:"estimated_receive_usd"`
	ReceiveWallet       string  `json:"receive_wallet"`
	CreatedAt           int64   `json:"created_at"`
	EstimatedFinishAt   int64   `json:"estimated_finish_at"`
	Status              string  `json:"status"`
	GasFaucetTxHash     *string `json:"gas_faucet_tx_hash"`
	UserTransferTxHash  *string `json:"user_transfer_tx_hash"`
	GasSwapTxHash       *string `json:"gas_swap_tx_hash"`
	Version             int     `json:"version"`
}

// GetByIDV2 returns a gas swap request by id
func GetByIDV2(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}
	gasSwap, kgErr := gasswap.GetByID(ctx, uid, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	createdAt := gasSwap.CreatedAt.Unix()
	estimatedFinishAt := gasSwap.EstimatedFinishAt.Unix()
	resp := &getGasSwapRespV2{
		ChainID:             gasSwap.ChainID,
		TokenAddress:        gasSwap.TokenAddress,
		AmountStr:           gasSwap.Amount,
		EstimatedReceive:    gasSwap.EstimatedReceive,
		EstimatedReceiveUsd: gasSwap.EstimatedReceiveUsd,
		ReceiveWallet:       gasSwap.ReceiveWallet,
		CreatedAt:           createdAt,
		EstimatedFinishAt:   estimatedFinishAt,
		Status:              gasSwap.Status.String(),
		GasFaucetTxHash:     gasSwap.GasFaucetTxHash,
		UserTransferTxHash:  gasSwap.UserTransferTxHash,
		GasSwapTxHash:       gasSwap.GasSwapTxHash,
		Version:             gasSwap.Version,
	}
	response.OK(c, resp)
}

// HandleV2 handles a gas swap request
func HandleV2(c *gin.Context) {
	ctx := c.Request.Context()
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}
	kgErr := gasswap.HandleV2(ctx, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}
