package gasswapv2_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	gasswap "github.com/kryptogo/kg-wallet-backend/api/gas-swap"
	gasswapv2 "github.com/kryptogo/kg-wallet-backend/api/gas-swap/v2"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	gasswapV1Service "github.com/kryptogo/kg-wallet-backend/pkg/service/gas-swap"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/kryptogo/kg-wallet-backend/repo"
	gasswapV2Service "github.com/kryptogo/kg-wallet-backend/service/gas-swap"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

type getAvailableChainsResp struct {
	Code int `json:"code"`
	Data struct {
		ChainIDs []string `json:"chain_ids"`
	} `json:"data"`
}

type getAvailableAssetsResp struct {
	Code int `json:"code"`
	Data struct {
		Assets []struct {
			ChainID    string  `json:"chain_id"`
			AssetGroup string  `json:"asset_group"`
			Name       string  `json:"name"`
			Symbol     string  `json:"symbol"`
			Amount     float64 `json:"amount"`
			AmountStr  string  `json:"amount_str"`
			UsdValue   float64 `json:"usd_value"`
			Decimals   int     `json:"decimals"`
			Price      float64 `json:"price"`
			Wallets    []struct {
				Address   string  `json:"address"`
				Amount    float64 `json:"amount"`
				AmountStr string  `json:"amount_str"`
				UsdValue  float64 `json:"usd_value"`
			} `json:"wallets"`
			LogoURLs []string `json:"logo_urls"`
		} `json:"assets"`
	} `json:"data"`
}

type getQuoteResp struct {
	Code int `json:"code"`
	Data []struct {
		Symbol              string  `json:"symbol"`
		ImageURL            string  `json:"image_url"`
		AmountStr           string  `json:"amount_str"`
		EstimatedReceive    float64 `json:"estimated_receive"`
		EstimatedReceiveUsd float64 `json:"estimated_receive_usd"`
		SwapAddress         string  `json:"swap_address"`
	} `json:"data"`
}

type initGasSwapResp struct {
	Code int `json:"code"`
	Data struct {
		GasSwapID int `json:"gas_swap_id"`
	} `json:"data"`
}

type gasSwapResp struct {
	Code int `json:"code"`
	Data struct {
		ChainID             string  `json:"chain_id"`
		TokenAddress        string  `json:"token_address"`
		AmountStr           string  `json:"amount_str"`
		EstimatedReceive    float64 `json:"estimated_receive"`
		EstimatedReceiveUSD float64 `json:"estimated_receive_usd"`
		ReceiveWallet       string  `json:"receive_wallet"`
		CreatedAt           int64   `json:"created_at"`
		EstimatedFinishAt   int64   `json:"estimated_finish_at"`
		Status              string  `json:"status"`
		GasFaucetTxHash     string  `json:"gas_faucet_tx_hash"`
		UserTransferTxHash  string  `json:"user_transfer_tx_hash"`
		GasSwapTxHash       string  `json:"gas_swap_tx_hash"`
		Version             int     `json:"version"`
	} `json:"data"`
}

func setup(t *testing.T, clientID string) (*gin.Engine, *domain.MockAsyncTaskExecutor, string) {
	// setup rdb
	signingservertest.Setup(t)

	// init services
	uRepo := repo.Unified()
	executor := domain.NewMockAsyncTaskExecutor(gomock.NewController(t))
	application.Init(rdb.GormRepo())
	gasswapV1Service.Init(uRepo, rdb.GormRepo(), executor)
	gasswapV2Service.Init(uRepo, executor)
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})

	// setup firebase
	users, uid, _, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	r := gin.Default()
	r.POST("/_v/organization/:orgID/config", gasswap.SetConfig)
	r.GET("/available_chains", auth.MockClientID(clientID), gasswap.GetAvailableChains)
	r.GET("/available_assets", auth.MockAuthorize(uid), auth.MockClientID(clientID), gasswap.GetAvailableAssets)

	r.POST("/_v/handle_gas_swap/v2/:id", gasswapv2.HandleV2)
	r.GET("/quote", auth.MockClientID(clientID), gasswapv2.GetQuoteV2)
	r.POST("/init", auth.MockAuthorize(uid), auth.MockClientID(clientID), gasswapv2.InitV2)
	r.GET("/:id", auth.MockAuthorize(uid), auth.MockClientID(clientID), gasswapv2.GetByIDV2)
	return r, executor, uid
}

const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
const orgWallet = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
const receiveWallet = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"

// TestGasSwapV2 tests full gas swap v2 scenario
func TestGasSwapV2(t *testing.T) {
	orgID := 2
	clientID := "41902cd3a636c7eb0af0fe9b"
	chainID := "shasta"

	r, e, uid := setup(t, clientID)
	t.Logf("uid: %s\n", uid)

	e.EXPECT().Execute(gomock.Any(), gomock.Any(), gomock.Cond(
		func(v any) bool {
			task, ok := v.(*domain.HttpTask)
			assert.True(t, ok)
			taskURL := config.GetString("SELF_INTERNAL_HOST") + "/_v/handle_gas_swap/v2/1"
			assert.Equal(t, taskURL, task.URL)
			assert.Equal(t, "POST", task.Method)
			return true
		}), gomock.Cond(
		func(v any) bool {
			taskName, ok := v.(string)
			assert.True(t, ok)
			assert.Contains(t, taskName, "handle-gas-swap-v2-")
			return true
		})).Return(nil)

	// get gas swap: not found
	gasSwapID := 1
	req, _ := http.NewRequest("GET", fmt.Sprintf("/gas-swap/%d", gasSwapID), nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusNotFound, w.Code)
	t.Logf("get gas swap resp: %s", w.Body.String())

	// set config
	body := map[string]interface{}{"supported_tokens": []map[string]interface{}{{"chain_id": chainID, "token_address": usdt}}}
	bodyStr, _ := json.Marshal(body)
	req, _ = http.NewRequest("POST", fmt.Sprintf("/_v/organization/%d/config", orgID), bytes.NewBuffer(bodyStr))
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("set org config resp: %s", w.Body.String())

	// get available chains
	req, _ = http.NewRequest("GET", "/available_chains", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("get available chains resp: %s", w.Body.String())
	var resp getAvailableChainsResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	assert.Equal(t, 0, resp.Code)
	assert.Equal(t, []string{chainID}, resp.Data.ChainIDs)
	// get available assets
	req, _ = http.NewRequest("GET", fmt.Sprintf("/available_assets?chain_id=%s&path=", chainID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("get available assets resp: %s", w.Body.String())
	var resp2 getAvailableAssetsResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp2))
	assert.Equal(t, 0, resp2.Code)
	assert.Len(t, resp2.Data.Assets, 1)
	assert.Equal(t, chainID, resp2.Data.Assets[0].ChainID)
	assert.Equal(t, "USDT", resp2.Data.Assets[0].Symbol)
	assert.Equal(t, "Tether USD", resp2.Data.Assets[0].Name)
	assert.Equal(t, 102.34, resp2.Data.Assets[0].Amount)
	assert.Equal(t, "102.34", resp2.Data.Assets[0].AmountStr)
	assert.Equal(t, 102.44234, resp2.Data.Assets[0].UsdValue)
	assert.Equal(t, 6, resp2.Data.Assets[0].Decimals)
	assert.Equal(t, 1.001, resp2.Data.Assets[0].Price)
	assert.Equal(t, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", resp2.Data.Assets[0].Wallets[0].Address)
	assert.Equal(t, 102.34, resp2.Data.Assets[0].Wallets[0].Amount)
	assert.Equal(t, "102.34", resp2.Data.Assets[0].Wallets[0].AmountStr)
	assert.Equal(t, 102.44234, resp2.Data.Assets[0].Wallets[0].UsdValue)
	assert.Len(t, resp2.Data.Assets[0].LogoURLs, 1)

	// get quote
	req, _ = http.NewRequest("GET", fmt.Sprintf("/quote?chain_id=%s&token_address=%s&receive_wallet=%s", chainID, usdt, receiveWallet), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("get quote resp: %s", w.Body.String())
	var resp3 getQuoteResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp3))
	assert.Equal(t, 0, resp3.Code)
	assert.Len(t, resp3.Data, 3)
	assert.Equal(t, "20.0", resp3.Data[0].AmountStr)
	assert.InDelta(t, 157.01465416666665, resp3.Data[0].EstimatedReceive, 0.001)
	assert.InDelta(t, 18.841758499999997, resp3.Data[0].EstimatedReceiveUsd, 0.001)
	assert.Equal(t, orgWallet, resp3.Data[0].SwapAddress)
	assert.Equal(t, "40.0", resp3.Data[1].AmountStr)
	assert.InDelta(t, 315.26858333333325, resp3.Data[1].EstimatedReceive, 0.001)
	assert.InDelta(t, 37.83222999999999, resp3.Data[1].EstimatedReceiveUsd, 0.001)
	assert.Equal(t, orgWallet, resp3.Data[1].SwapAddress)
	assert.Equal(t, "60.0", resp3.Data[2].AmountStr)
	assert.InDelta(t, 473.5225125, resp3.Data[2].EstimatedReceive, 0.001)
	assert.InDelta(t, 56.8227015, resp3.Data[2].EstimatedReceiveUsd, 0.001)
	assert.Equal(t, orgWallet, resp3.Data[2].SwapAddress)

	// init gas swap
	privateKey := "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3"
	signedTx := signingservertest.SignTransferTx(t, privateKey, domain.NewTronAddress(receiveWallet), domain.NewTronAddress(resp3.Data[0].SwapAddress), "20.0", time.Hour*4)
	signedTxBytes, err := json.Marshal(signedTx)
	assert.NoError(t, err)
	signedTxStr := string(signedTxBytes)
	body = map[string]interface{}{
		"chain_id":       chainID,
		"token_address":  usdt,
		"amount_str":     "20.0",
		"receive_wallet": receiveWallet,
		"signed_txs":     []string{signedTxStr},
	}
	bodyStr, _ = json.Marshal(body)
	req, _ = http.NewRequest("POST", "/init", bytes.NewBuffer(bodyStr))
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("resp4: %s", w.Body.String())
	var resp4 initGasSwapResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp4))
	assert.Equal(t, 0, resp4.Code)
	assert.Equal(t, gasSwapID, resp4.Data.GasSwapID)

	// get gas swap at first stage
	req, _ = http.NewRequest("GET", fmt.Sprintf("/%d", gasSwapID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("get gas swap at first stage resp: %s", w.Body.String())
	var resp5 gasSwapResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp5))
	assert.Equal(t, 0, resp5.Code)
	assert.Equal(t, chainID, resp5.Data.ChainID)
	assert.Equal(t, usdt, resp5.Data.TokenAddress)
	assert.Equal(t, "20.0", resp5.Data.AmountStr)
	assert.InDelta(t, 157.014654167, resp5.Data.EstimatedReceive, 0.001)
	assert.InDelta(t, 18.8417585, resp5.Data.EstimatedReceiveUSD, 0.001)
	assert.Equal(t, receiveWallet, resp5.Data.ReceiveWallet)
	assert.NotEmpty(t, resp5.Data.CreatedAt)
	assert.NotEmpty(t, resp5.Data.EstimatedFinishAt)
	assert.Equal(t, "processing", resp5.Data.Status)
	assert.Empty(t, resp5.Data.GasFaucetTxHash)
	assert.Empty(t, resp5.Data.UserTransferTxHash)
	assert.Empty(t, resp5.Data.GasSwapTxHash)
	assert.Equal(t, 2, resp5.Data.Version)

	// handle gas swap
	req, _ = http.NewRequest("POST", fmt.Sprintf("/_v/handle_gas_swap/v2/%d", gasSwapID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("handle gas swap resp: %s", w.Body.String())

	// get gas swap at final stage
	req, _ = http.NewRequest("GET", fmt.Sprintf("/%d", gasSwapID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("get gas swap at final stage resp: %s", w.Body.String())
	var resp7 gasSwapResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp7))
	assert.Equal(t, 0, resp7.Code)
	assert.Equal(t, chainID, resp7.Data.ChainID)
	assert.Equal(t, usdt, resp7.Data.TokenAddress)
	assert.Equal(t, "20.0", resp7.Data.AmountStr)
	assert.InDelta(t, 157.014654167, resp7.Data.EstimatedReceive, 0.001)
	assert.InDelta(t, 18.8417585, resp7.Data.EstimatedReceiveUSD, 0.001)
	assert.Equal(t, receiveWallet, resp7.Data.ReceiveWallet)
	assert.NotEmpty(t, resp7.Data.CreatedAt)
	assert.NotEmpty(t, resp7.Data.EstimatedFinishAt)
	assert.Equal(t, "success", resp7.Data.Status)
	assert.NotEmpty(t, resp7.Data.GasFaucetTxHash)
	assert.NotEmpty(t, resp7.Data.UserTransferTxHash)
	assert.NotEmpty(t, resp7.Data.GasSwapTxHash)
	assert.Equal(t, 2, resp5.Data.Version)

	{
		var gasSwap model.StudioOrganizationGasSwap
		assert.NoError(t, rdb.Get().First(&gasSwap, "id = ?", resp4.Data.GasSwapID).Error)
		assert.NotNil(t, gasSwap.ProfitMargin)
	}
}

func TestGasSwapQuoteV2(t *testing.T) {
	orgID := 2
	chainID := "shasta"
	clientID := "41902cd3a636c7eb0af0fe9b"
	r, _, uid := setup(t, clientID)
	t.Logf("uid: %s\n", uid)

	// set config
	body := map[string]interface{}{"supported_tokens": []map[string]interface{}{{"chain_id": chainID, "token_address": usdt}}}
	bodyStr, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", fmt.Sprintf("/_v/organization/%d/config", orgID), bytes.NewBuffer(bodyStr))
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// get quote with specific amount
	req, _ = http.NewRequest("GET", fmt.Sprintf("/quote?chain_id=%s&token_address=%s&amount=30.0&receive_wallet=%s", chainID, usdt, receiveWallet), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("resp1: %s", w.Body.String())
	var resp1 getQuoteResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp1))
	assert.Equal(t, 0, resp1.Code)
	assert.Len(t, resp1.Data, 1)
	assert.Equal(t, "30", resp1.Data[0].AmountStr)
	assert.Equal(t, "TRX", resp1.Data[0].Symbol)
	assert.Equal(t, "https://static.tronscan.org/production/logo/trx.png", resp1.Data[0].ImageURL)
	assert.InDelta(t, 236.14161875000002, resp1.Data[0].EstimatedReceive, 0.001)
	assert.InDelta(t, 28.33699425, resp1.Data[0].EstimatedReceiveUsd, 0.001)
	assert.Equal(t, orgWallet, resp1.Data[0].SwapAddress)
}
