package gasswap

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	gasswapService "github.com/kryptogo/kg-wallet-backend/pkg/service/gas-swap"
	gasswaptest "github.com/kryptogo/kg-wallet-backend/pkg/service/gas-swap/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/repo"

	sign_client "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

type getAvailableChainsResp struct {
	Code int `json:"code"`
	Data struct {
		ChainIDs []string `json:"chain_ids"`
	} `json:"data"`
}

type getAvailableAssetsResp struct {
	Code int `json:"code"`
	Data struct {
		Assets []struct {
			ChainID    string  `json:"chain_id"`
			AssetGroup string  `json:"asset_group"`
			Name       string  `json:"name"`
			Symbol     string  `json:"symbol"`
			Amount     float64 `json:"amount"`
			AmountStr  string  `json:"amount_str"`
			UsdValue   float64 `json:"usd_value"`
			Decimals   int     `json:"decimals"`
			Price      float64 `json:"price"`
			Wallets    []struct {
				Address   string  `json:"address"`
				Amount    float64 `json:"amount"`
				AmountStr string  `json:"amount_str"`
				UsdValue  float64 `json:"usd_value"`
			} `json:"wallets"`
			LogoURLs []string `json:"logo_urls"`
		} `json:"assets"`
	} `json:"data"`
}

type getQuoteResp struct {
	Code int `json:"code"`
	Data []struct {
		Symbol              string  `json:"symbol"`
		ImageURL            string  `json:"image_url"`
		AmountStr           string  `json:"amount_str"`
		EstimatedReceive    float64 `json:"estimated_receive"`
		EstimatedReceiveUsd float64 `json:"estimated_receive_usd"`
		ApproveSpender      string  `json:"approve_spender"`
	} `json:"data"`
}

type initGasSwapResp struct {
	Code int `json:"code"`
	Data struct {
		GasSwapID int `json:"gas_swap_id"`
	} `json:"data"`
}

type gasSwapResp struct {
	Code int `json:"code"`
	Data struct {
		ChainID             string  `json:"chain_id"`
		TokenAddress        string  `json:"token_address"`
		AmountStr           string  `json:"amount_str"`
		EstimatedReceive    float64 `json:"estimated_receive"`
		EstimatedReceiveUSD float64 `json:"estimated_receive_usd"`
		ReceiveWallet       string  `json:"receive_wallet"`
		CreatedAt           int64   `json:"created_at"`
		EstimatedFinishAt   int64   `json:"estimated_finish_at"`
		Status              string  `json:"status"`
		GasFaucetTxHash     string  `json:"gas_faucet_tx_hash"`
		UserApproveTxHash   string  `json:"user_approve_tx_hash"`
		GasSwapTxHash       string  `json:"gas_swap_tx_hash"`
	} `json:"data"`
}

func setup(t *testing.T, clientID string) (*gin.Engine, *domain.MockAsyncTaskExecutor, string) {
	// setup rdb
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())

	// init services
	uRepo := repo.Unified()
	executor := domain.NewMockAsyncTaskExecutor(gomock.NewController(t))
	application.Init(rdb.GormRepo())
	gasswapService.Init(uRepo, rdb.GormRepo(), executor)
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})

	// setup firebase
	users, uid, _, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	r := gin.Default()
	r.POST("/_v/handle_gas_swap/:id", Handle)
	r.POST("/_v/organization/:orgID/enable", Enable)
	r.POST("/_v/organization/:orgID/config", SetConfig)
	r.GET("/available_chains", auth.MockClientID(clientID), GetAvailableChains)
	r.GET("/available_assets", auth.MockAuthorize(uid), auth.MockClientID(clientID), GetAvailableAssets)
	r.GET("/quote", auth.MockClientID(clientID), GetQuote)
	r.POST("/init", auth.MockAuthorize(uid), auth.MockClientID(clientID), Init)
	r.GET("/:id", auth.MockAuthorize(uid), auth.MockClientID(clientID), GetByID)

	// signing server
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	sign_client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/tron", signing.SignTronTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()

	return r, executor, uid
}

const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"

// TestGasSwap tests full gas swap scenario
func TestGasSwap(t *testing.T) {
	orgID := 2
	clientID := "41902cd3a636c7eb0af0fe9b"
	chainID := "shasta"

	r, e, uid := setup(t, clientID)
	t.Logf("uid: %s\n", uid)

	e.EXPECT().Execute(gomock.Any(), gomock.Any(), gomock.Cond(
		func(v any) bool {
			task, ok := v.(*domain.HttpTask)
			assert.True(t, ok)
			taskURL := config.GetString("SELF_INTERNAL_HOST") + "/_v/handle_gas_swap/1"
			assert.Equal(t, taskURL, task.URL)
			assert.Equal(t, "POST", task.Method)
			return true
		}), gomock.Cond(
		func(v any) bool {
			taskName, ok := v.(string)
			assert.True(t, ok)
			assert.Contains(t, taskName, "handle-gas-swap-")
			return true
		})).Return(nil)

	// get gas swap: not found
	gasSwapID := 1
	req, _ := http.NewRequest("GET", fmt.Sprintf("/gas-swap/%d", gasSwapID), nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusNotFound, w.Code)

	// enable for organization
	body := map[string]interface{}{"chain_id": chainID}
	bodyStr, _ := json.Marshal(body)
	req, _ = http.NewRequest("POST", fmt.Sprintf("/_v/organization/%d/enable", orgID), bytes.NewBuffer(bodyStr))
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// set config
	body = map[string]interface{}{"supported_tokens": []map[string]interface{}{{"chain_id": chainID, "token_address": usdt}}}
	bodyStr, _ = json.Marshal(body)
	req, _ = http.NewRequest("POST", fmt.Sprintf("/_v/organization/%d/config", orgID), bytes.NewBuffer(bodyStr))
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// get available chains
	req, _ = http.NewRequest("GET", "/available_chains", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("resp: %s", w.Body.String())
	var resp getAvailableChainsResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	assert.Equal(t, 0, resp.Code)
	assert.Equal(t, []string{chainID}, resp.Data.ChainIDs)

	// get available assets
	req, _ = http.NewRequest("GET", fmt.Sprintf("/available_assets?chain_id=%s&path=", chainID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("resp2: %s", w.Body.String())
	var resp2 getAvailableAssetsResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp2))
	assert.Equal(t, 0, resp2.Code)
	assert.Len(t, resp2.Data.Assets, 1)
	assert.Equal(t, chainID, resp2.Data.Assets[0].ChainID)
	assert.Equal(t, "USDT", resp2.Data.Assets[0].Symbol)
	assert.Equal(t, "Tether USD", resp2.Data.Assets[0].Name)
	assert.Equal(t, 102.34, resp2.Data.Assets[0].Amount)
	assert.Equal(t, "102.34", resp2.Data.Assets[0].AmountStr)
	assert.Equal(t, 102.44234, resp2.Data.Assets[0].UsdValue)
	assert.Equal(t, 6, resp2.Data.Assets[0].Decimals)
	assert.Equal(t, 1.001, resp2.Data.Assets[0].Price)
	assert.Equal(t, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", resp2.Data.Assets[0].Wallets[0].Address)
	assert.Equal(t, 102.34, resp2.Data.Assets[0].Wallets[0].Amount)
	assert.Equal(t, "102.34", resp2.Data.Assets[0].Wallets[0].AmountStr)
	assert.Equal(t, 102.44234, resp2.Data.Assets[0].Wallets[0].UsdValue)
	assert.Len(t, resp2.Data.Assets[0].LogoURLs, 1)

	// get quote
	req, _ = http.NewRequest("GET", fmt.Sprintf("/quote?chain_id=%s&token_address=%s", chainID, usdt), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("resp3: %s", w.Body.String())
	var resp3 getQuoteResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp3))
	assert.Equal(t, 0, resp3.Code)
	assert.Len(t, resp3.Data, 3)
	assert.Equal(t, "20.0", resp3.Data[0].AmountStr)
	assert.InDelta(t, 136.02891666666665, resp3.Data[0].EstimatedReceive, 0.001)
	assert.InDelta(t, 16.323469999999997, resp3.Data[0].EstimatedReceiveUsd, 0.001)
	assert.Equal(t, "TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft", resp3.Data[0].ApproveSpender)
	assert.Equal(t, "40.0", resp3.Data[1].AmountStr)
	assert.InDelta(t, 294.5205833333333, resp3.Data[1].EstimatedReceive, 0.001)
	assert.InDelta(t, 35.34247, resp3.Data[1].EstimatedReceiveUsd, 0.001)
	assert.Equal(t, "TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft", resp3.Data[1].ApproveSpender)
	assert.Equal(t, "60.0", resp3.Data[2].AmountStr)
	assert.InDelta(t, 453.01225, resp3.Data[2].EstimatedReceive, 0.001)
	assert.InDelta(t, 54.36147, resp3.Data[2].EstimatedReceiveUsd, 0.001)
	assert.Equal(t, "TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft", resp3.Data[2].ApproveSpender)

	// init gas swap
	privateKey := "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3"
	receiveWallet := "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	signedTx := gasswaptest.SignApproveTx(t, privateKey, receiveWallet, resp3.Data[0].ApproveSpender, "20.0")
	signedTxBytes, err := json.Marshal(signedTx)
	assert.NoError(t, err)
	signedTxStr := string(signedTxBytes)
	body = map[string]interface{}{
		"chain_id":       chainID,
		"token_address":  usdt,
		"amount_str":     "20.0",
		"receive_wallet": receiveWallet,
		"signed_txs":     []string{signedTxStr},
	}
	bodyStr, _ = json.Marshal(body)
	req, _ = http.NewRequest("POST", "/init", bytes.NewBuffer(bodyStr))
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("resp4: %s", w.Body.String())
	var resp4 initGasSwapResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp4))
	assert.Equal(t, 0, resp4.Code)
	assert.Equal(t, gasSwapID, resp4.Data.GasSwapID)

	// get gas swap at first stage
	req, _ = http.NewRequest("GET", fmt.Sprintf("/%d", gasSwapID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("resp5: %s", w.Body.String())
	var resp5 gasSwapResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp5))
	assert.Equal(t, 0, resp5.Code)
	assert.Equal(t, chainID, resp5.Data.ChainID)
	assert.Equal(t, usdt, resp5.Data.TokenAddress)
	assert.Equal(t, "20.0", resp5.Data.AmountStr)
	assert.InDelta(t, 136.028916667, resp5.Data.EstimatedReceive, 0.001)
	assert.InDelta(t, 16.32347, resp5.Data.EstimatedReceiveUSD, 0.001)
	assert.Equal(t, receiveWallet, resp5.Data.ReceiveWallet)
	assert.NotEmpty(t, resp5.Data.CreatedAt)
	assert.NotEmpty(t, resp5.Data.EstimatedFinishAt)
	assert.Equal(t, "processing", resp5.Data.Status)
	assert.NotEmpty(t, resp5.Data.GasFaucetTxHash)
	assert.Empty(t, resp5.Data.UserApproveTxHash)
	assert.Empty(t, resp5.Data.GasSwapTxHash)

	// handle gas swap
	req, _ = http.NewRequest("POST", fmt.Sprintf("/_v/handle_gas_swap/%d", gasSwapID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("resp6: %s", w.Body.String())

	// get gas swap at final stage
	req, _ = http.NewRequest("GET", fmt.Sprintf("/%d", gasSwapID), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("resp7: %s", w.Body.String())
	var resp7 gasSwapResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp7))
	assert.Equal(t, 0, resp7.Code)
	assert.Equal(t, chainID, resp7.Data.ChainID)
	assert.Equal(t, usdt, resp7.Data.TokenAddress)
	assert.Equal(t, "20.0", resp7.Data.AmountStr)
	assert.InDelta(t, 136.028916667, resp7.Data.EstimatedReceive, 0.001)
	assert.InDelta(t, 16.32347, resp7.Data.EstimatedReceiveUSD, 0.001)
	assert.Equal(t, receiveWallet, resp7.Data.ReceiveWallet)
	assert.NotEmpty(t, resp7.Data.CreatedAt)
	assert.NotEmpty(t, resp7.Data.EstimatedFinishAt)
	assert.Equal(t, "success", resp7.Data.Status)
	assert.NotEmpty(t, resp7.Data.GasFaucetTxHash)
	assert.NotEmpty(t, resp7.Data.UserApproveTxHash)
	assert.NotEmpty(t, resp7.Data.GasSwapTxHash)

	{
		var gasSwap model.StudioOrganizationGasSwap
		assert.NoError(t, rdb.Get().First(&gasSwap, "id = ?", resp4.Data.GasSwapID).Error)
		assert.NotNil(t, gasSwap.ProfitMargin)
	}
}

func TestGasSwapQuote(t *testing.T) {
	chainID := "shasta"
	clientID := "41902cd3a636c7eb0af0fe9b"
	orgID := 2
	r, _, uid := setup(t, clientID)
	t.Logf("uid: %s\n", uid)

	// enable for organization
	body := map[string]interface{}{"chain_id": chainID}
	bodyStr, _ := json.Marshal(body)
	req, _ := http.NewRequest("POST", fmt.Sprintf("/_v/organization/%d/enable", orgID), bytes.NewBuffer(bodyStr))
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// set config
	body = map[string]interface{}{"supported_tokens": []map[string]interface{}{{"chain_id": chainID, "token_address": usdt}}}
	bodyStr, _ = json.Marshal(body)
	req, _ = http.NewRequest("POST", fmt.Sprintf("/_v/organization/%d/config", orgID), bytes.NewBuffer(bodyStr))
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// get quote with specific amount
	req, _ = http.NewRequest("GET", fmt.Sprintf("/quote?chain_id=%s&token_address=%s&amount=30.0", chainID, usdt), nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("resp1: %s", w.Body.String())
	var resp1 getQuoteResp
	assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp1))
	assert.Equal(t, 0, resp1.Code)
	assert.Len(t, resp1.Data, 1)
	assert.Equal(t, "30", resp1.Data[0].AmountStr)
	assert.Equal(t, "TRX", resp1.Data[0].Symbol)
	assert.Equal(t, "https://static.tronscan.org/production/logo/trx.png", resp1.Data[0].ImageURL)
	assert.InDelta(t, 215.27474999999998, resp1.Data[0].EstimatedReceive, 0.001)
	assert.InDelta(t, 25.832969999999996, resp1.Data[0].EstimatedReceiveUsd, 0.001)
	assert.Equal(t, "TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft", resp1.Data[0].ApproveSpender)
}
