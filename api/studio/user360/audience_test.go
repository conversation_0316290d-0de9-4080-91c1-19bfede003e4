package user360

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"

	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	customerRepo "github.com/kryptogo/kg-wallet-backend/pkg/service/customer/repo"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"
)

func TestListAudience(t *testing.T) {
	s := assert.New(t)
	orgID := 1
	setup(t)
	// FIXME: setup with test data instead of reuse seed data
	// to avoid redundant data in test db

	s.NoError(rdb.Get().Delete(&model.Customer{}, "uid = ?", "testuser1").Error)

	uids := setupCustomers(t, orgID)

	caseSubmissions := []model.CaseSubmission{
		{
			UID:            uids[0],
			OrganizationID: orgID,
			FormID:         util.Ptr(10),
			SubmittedAt:    time.Unix(1600000000, 0).UTC(),
			UpdatedAt:      time.Unix(1600000000, 0).UTC(),
		},
		{
			UID:            uids[1],
			OrganizationID: orgID,
			FormID:         util.Ptr(11),
			SubmittedAt:    time.Unix(1600000000, 0).UTC(),
			UpdatedAt:      time.Unix(1600000000, 0).UTC(),
		},
	}

	kycAuditLogs := []model.KycAuditLog{
		{
			UID:            uids[0],
			OrganizationID: orgID,
			BeforeStatus:   domain.KycStatusUnverified,
			AfterStatus:    domain.KycStatusVerified,
			CreatedAt:      time.Unix(1600000000, 0).UTC(),
		},
		{
			UID:            uids[1],
			OrganizationID: orgID,
			BeforeStatus:   domain.KycStatusUnverified,
			AfterStatus:    domain.KycStatusVerified,
			CreatedAt:      time.Unix(1600000001, 0).UTC(),
		},
	}

	s.NoError(rdb.Get().Model(&model.Customer{}).Where("uid = ?", uids[0]).Update("kyc_status", "verified").Error)
	s.NoError(rdb.Get().Model(&model.Customer{}).Where("uid = ?", uids[1]).Update("kyc_status", "pending").Error)
	s.NoError(rdb.Get().Create(&caseSubmissions).Error)
	s.NoError(rdb.Get().Create(&kycAuditLogs).Error)

	customer.Init(customerRepo.NewCustomerRepo())

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/user_360/audience", auth.MockOrgID(orgID), ListAudience)

	type audience struct {
		UID        string `json:"uid"`
		CreatedAt  int64  `json:"created_at"`
		UpdatedAt  int64  `json:"updated_at"`
		Name       string `json:"name"`
		Email      string `json:"email"`
		Phone      string `json:"phone"`
		ComplyFlow struct {
			KycStatus string `json:"kyc_status"`
			FormID    *int   `json:"form_id"`
		} `json:"comply_flow"`
	}
	var response struct {
		Code   int        `json:"code"`
		Data   []audience `json:"data"`
		Paging struct {
			PageNumber int    `json:"page_number"`
			PageSize   int    `json:"page_size"`
			TotalCount int    `json:"total_count"`
			PageSort   string `json:"page_sort"`
		} `json:"paging"`
	}

	{ // test list all, sort by created_at desc
		assert.Nil(t, rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:         uids[1],
				DisplayName: "AABBC",
				Email:       util.Ptr("<EMAIL>"),
			},
		}))
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/user_360/audience", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		err = json.Unmarshal(w.Body.Bytes(), &response)
		s.NoError(err)
		s.Equal(0, response.Code)
		s.Equal(2, len(response.Data))
		{ // with FormConfig.IdvAutoCreate but form submission.RealName is empty
			s.Equal("verified", response.Data[1].ComplyFlow.KycStatus)
			s.Equal(10, *response.Data[1].ComplyFlow.FormID)
			s.Equal("哈里", response.Data[1].Name)
		}
		{
			s.Equal("pending", response.Data[0].ComplyFlow.KycStatus)
			s.Equal(11, *response.Data[0].ComplyFlow.FormID)
			s.Equal("AABBC", response.Data[0].Name)
			s.Equal("<EMAIL>", response.Data[0].Email)
		}
		s.True(response.Data[0].CreatedAt > response.Data[1].CreatedAt)
		s.True(response.Data[0].UpdatedAt > response.Data[1].UpdatedAt)
	}
	{
		assert.Nil(t, rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:         uids[0],
				DisplayName: "Fuzzy search",
				Email:       util.Ptr("<EMAIL>"),
			},
		}))
		{ // case with legal name and search by legal name
			w := httptest.NewRecorder()
			req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/user_360/audience?q=Fuzzy", orgID), nil)
			s.NoError(err)
			r.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)

			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.Nil(t, err)
			s.Equal(0, response.Code)
			s.Equal(1, len(response.Data))
			s.Equal("verified", response.Data[0].ComplyFlow.KycStatus)
			s.Equal(10, *response.Data[0].ComplyFlow.FormID)
			s.Equal("Fuzzy search", response.Data[0].Name)
		}
		{ // case with legal name and search by email
			w := httptest.NewRecorder()
			req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/user_360/audience?q=kryptogo", orgID), nil)
			s.NoError(err)
			r.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)

			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.Nil(t, err)
			s.Equal(0, response.Code)
			s.Equal(1, len(response.Data))
			s.Equal("verified", response.Data[0].ComplyFlow.KycStatus)
			s.Equal(10, *response.Data[0].ComplyFlow.FormID)
			s.Equal("Fuzzy search", response.Data[0].Name)
			s.Equal("<EMAIL>", response.Data[0].Email)
		}
		{ // case not found
			w := httptest.NewRecorder()
			req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/user_360/audience?q=9527", orgID), nil)
			s.NoError(err)
			r.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)

			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.Nil(t, err)
			s.Equal(0, response.Code)
			s.Equal(0, len(response.Data))
		}
	}
	{ // case with empty display_name and legal_name
		assert.Nil(t, rdb.GormRepo().DeleteUserFields(context.Background(), uids[0], []string{"display_name", "email"}))
		assert.Nil(t, rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:   uids[1],
				Email: util.Ptr("<EMAIL>"),
			},
		}))
		assert.Nil(t, rdb.GormRepo().DeleteUserFields(context.Background(), uids[1], []string{"display_name"}))
		{
			w := httptest.NewRecorder()
			req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/user_360/audience?q=kryptogo", orgID), nil)
			s.NoError(err)
			r.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)

			err = json.Unmarshal(w.Body.Bytes(), &response)
			assert.Nil(t, err)
			s.Equal(0, response.Code)
			s.Equal(1, len(response.Data))
			s.Equal("pending", response.Data[0].ComplyFlow.KycStatus)
			s.Equal(11, *response.Data[0].ComplyFlow.FormID)
			s.Equal("email", response.Data[0].Name)
			s.Equal("<EMAIL>", response.Data[0].Email)
		}
	}
}

func TestGetSingleAudience(t *testing.T) {
	s := assert.New(t)
	orgID := 1
	setup(t)

	type audienceResp struct {
		Code int             `json:"code"`
		Data *audienceDetail `json:"data"`
	}

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/user_360/audience/:uid", auth.MockOrgID(orgID), GetSingleAudience)

	customer.Init(customerRepo.NewCustomerRepo())

	assertWallets := func(wallets []struct {
		ChainID string  `json:"chain_id"`
		Address string  `json:"address"`
		Balance float64 `json:"balance"`
		Tag     *string `json:"tag"`
	}) {
		// order by usd_value desc
		s.Equal("btc", wallets[0].ChainID)
		s.Equal("******************************************", wallets[0].Address)
		s.Equal("tron", wallets[1].ChainID)
		s.Equal("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", wallets[1].Address)
		s.Equal("sol", wallets[2].ChainID)
		s.Equal("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7", wallets[2].Address)
		s.Equal("eth", wallets[3].ChainID)
		s.Equal("******************************************", wallets[3].Address)
		s.Equal("bsc", wallets[4].ChainID)
		s.Equal("******************************************", wallets[4].Address)
	}

	getAudienceDetail := func(uid string) (audienceResp, error) {
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/user_360/audience/%s", orgID, uid), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		resp := audienceResp{}
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		t.Logf("getAudienceDetail resp: %+v", w.Body.String())

		return resp, err
	}

	{ // user not found
		uid := util.RandString(20)

		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/user_360/audience/%s", orgID, uid), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
		type audienceResp struct {
			Code int             `json:"code"`
			Data *audienceDetail `json:"data"`
		}
		resp := audienceResp{}
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.Equal(7016, resp.Code)
		s.Nil(resp.Data)
	}

	uid := setupCustomers(t, orgID)[0]

	{ // user found, but no display name and email and any kyc info
		assert.Nil(t, rdb.GormRepo().DeleteUserFields(context.Background(), uid, []string{"display_name", "email", "kyc_state"}))

		resp, err := getAudienceDetail(uid)
		s.NoError(err)
		s.Equal(0, resp.Code)
		s.NotEmpty(resp.Data.CreatedAt)
		s.Nil(resp.Data.Name)
		s.Equal("unverified", resp.Data.Compliance.KycStatus)
		s.Nil(resp.Data.Compliance.RiskLevel)
		s.Nil(resp.Data.Compliance.AppliedAt)
		s.Nil(resp.Data.Compliance.ApprovedAt)
		s.Nil(resp.Data.Compliance.FormID)
		s.Nil(resp.Data.Compliance.Email)
		s.Empty(resp.Data.Compliance.RealName)
		s.Empty(resp.Data.Compliance.NationName)
		s.Empty(resp.Data.Compliance.NationalID)
		s.Nil(resp.Data.Compliance.DOB)
		s.NotEmpty(resp.Data.Compliance.Phone)

		assertWallets(resp.Data.Wallets)
	}
	{ // user found, with email, but no display name and any kyc info
		err := rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:   uid,
				Email: util.Ptr("<EMAIL>"),
			},
		})
		assert.Nil(t, err)

		resp, err := getAudienceDetail(uid)
		s.NoError(err)
		s.Equal(0, resp.Code)
		s.NotEmpty(resp.Data.CreatedAt)
		s.Equal("audience-test", *resp.Data.Name)
		s.Equal("unverified", resp.Data.Compliance.KycStatus)
		s.Nil(resp.Data.Compliance.RiskLevel)
		s.Nil(resp.Data.Compliance.AppliedAt)
		s.Nil(resp.Data.Compliance.ApprovedAt)
		s.Nil(resp.Data.Compliance.FormID)
		s.Equal("<EMAIL>", *resp.Data.Compliance.Email)
		s.Empty(resp.Data.Compliance.RealName)
		s.Empty(resp.Data.Compliance.NationName)
		s.Empty(resp.Data.Compliance.NationalID)
		s.Nil(resp.Data.Compliance.DOB)
		s.NotEmpty(resp.Data.Compliance.Phone)

		assertWallets(resp.Data.Wallets)
	}
	{ // user found, with display name and email, but no any kyc info
		assert.Nil(t, rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:         uid,
				DisplayName: "Audience Tester",
				Email:       util.Ptr("<EMAIL>"),
			},
		}))
		resp, err := getAudienceDetail(uid)
		s.NoError(err)
		s.Equal(0, resp.Code)
		s.NotEmpty(resp.Data.CreatedAt)
		s.Equal("Audience Tester", *resp.Data.Name)
		s.Equal("unverified", resp.Data.Compliance.KycStatus)
		s.Nil(resp.Data.Compliance.RiskLevel)
		s.Nil(resp.Data.Compliance.AppliedAt)
		s.Nil(resp.Data.Compliance.ApprovedAt)
		s.Nil(resp.Data.Compliance.FormID)
		s.Equal("<EMAIL>", *resp.Data.Compliance.Email)
		s.Empty(resp.Data.Compliance.RealName)
		s.Empty(resp.Data.Compliance.NationName)
		s.Empty(resp.Data.Compliance.NationalID)
		s.Nil(resp.Data.Compliance.DOB)
		s.NotEmpty(resp.Data.Compliance.Phone)

		assertWallets(resp.Data.Wallets)
	}
	{ // user found, with display name and email and approved form submission
		// with enabled idv_auto_create and enabled search_task_auto_create,
		// and accepted search task,
		// and accepted idv task.
		assert.Nil(t, rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:         uid,
				DisplayName: "Audience Tester",
				Email:       util.Ptr("<EMAIL>"),
			},
		}))

		s.NoError(rdb.Get().Model(&model.Customer{}).Where("uid = ?", uid).Updates(map[string]interface{}{
			"kyc_status":     "verified",
			"legal_name":     "John Doe",
			"birthday":       "2006-01-02",
			"country":        "TWN",
			"national_id":    "A123456789",
			"cdd_risk_score": 10,
		}).Error)
		s.NoError(rdb.Get().Create(&model.CaseSubmission{
			UID:            uid,
			OrganizationID: orgID,
			FormID:         util.Ptr(1),
			SubmittedAt:    time.Unix(1600000000, 0).UTC(),
			UpdatedAt:      time.Unix(1700000000, 0).UTC(),
		}).Error)

		resp, err := getAudienceDetail(uid)
		s.NoError(err)
		s.Equal(0, resp.Code)
		s.NotEmpty(resp.Data.CreatedAt)
		s.Equal("John Doe", *resp.Data.Name)
		s.Equal("verified", resp.Data.Compliance.KycStatus)
		s.Equal("low", *resp.Data.Compliance.RiskLevel)
		s.Equal(int64(1600000000), *resp.Data.Compliance.AppliedAt)
		s.Equal(int64(1700000000), *resp.Data.Compliance.ApprovedAt)
		s.Equal(1, *resp.Data.Compliance.FormID)
		s.Equal("<EMAIL>", *resp.Data.Compliance.Email)
		s.Equal("John Doe", resp.Data.Compliance.RealName)
		s.Equal("Taiwan", resp.Data.Compliance.NationName)
		s.Equal("A123456789", resp.Data.Compliance.NationalID)
		s.Equal(int64(1136160000), *resp.Data.Compliance.DOB)
		s.NotEmpty(resp.Data.Compliance.Phone)

		assertWallets(resp.Data.Wallets)
	}
	{ // user found, with no wallets
		assert.Nil(t, rdb.GormRepo().DeleteUserWallets(context.Background(), uid))
		resp, err := getAudienceDetail(uid)
		s.NoError(err)
		s.Equal(0, resp.Code)
		s.NotEmpty(resp.Data.CreatedAt)
		s.Equal("John Doe", *resp.Data.Name)
		s.Equal("verified", resp.Data.Compliance.KycStatus)
		s.Equal("low", *resp.Data.Compliance.RiskLevel)
		s.Equal(int64(1600000000), *resp.Data.Compliance.AppliedAt)
		s.Equal(int64(1700000000), *resp.Data.Compliance.ApprovedAt)
		s.Equal(1, *resp.Data.Compliance.FormID)
		s.Equal("<EMAIL>", *resp.Data.Compliance.Email)
		s.Equal("John Doe", resp.Data.Compliance.RealName)
		s.Equal("Taiwan", resp.Data.Compliance.NationName)
		s.Equal("A123456789", resp.Data.Compliance.NationalID)
		s.Equal(int64(1136160000), *resp.Data.Compliance.DOB)
		s.NotEmpty(resp.Data.Compliance.Phone)

		s.Empty(resp.Data.Wallets)
	}
}

// Seed database with necessary data.
func setup(t *testing.T) {
	rdb.Reset()

	user.Init(repo.Unified())
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	// assets and prices are needed for audience detail to get an user's wallets balance data
	assert.Nil(t, rdbtest.CreateAssets(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAssetPrices(rdb.Get()))
	organization.Init(organization.InitParam{
		StudioOrgRepo:  rdb.GormRepo(),
		StudioRoleRepo: rdb.GormRepo(),
	})
}

func setupCustomers(t *testing.T, orgID int) []string {
	users, userIDs := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.NoError(t, err)

	now := time.Now()
	ctx := context.Background()
	for index, uid := range userIDs {
		err = rdb.SaveCustomer(ctx, &model.Customer{
			UID:            uid,
			OrganizationID: orgID,
			CreatedAt:      now.AddDate(0, 0, index),
		})
		assert.NoError(t, err)
	}
	return userIDs
}
