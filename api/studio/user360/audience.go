package user360

import (
	"github.com/gin-gonic/gin"
	request "github.com/kryptogo/kg-wallet-backend/api/paging"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/user360"

	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type listAudienceParams struct {
	KycStatus string `form:"kyc_status"`
	request.PagingRequest
}

type audience struct {
	UID        string  `json:"uid"`
	CreatedAt  int64   `json:"created_at"`
	UpdatedAt  int64   `json:"updated_at"`
	Name       *string `json:"name"`
	Email      *string `json:"email"`
	Phone      *string `json:"phone"`
	ComplyFlow struct {
		KycStatus string `json:"kyc_status"`
		FormID    *int   `json:"form_id"`
	} `json:"comply_flow"`
}

func (au *audience) fromService(customer user360.Audience) {
	au.UID = customer.UID
	au.CreatedAt = customer.CreatedAt
	au.UpdatedAt = customer.UpdatedAt
	au.Name = customer.Name
	au.Email = customer.Email
	au.Phone = customer.Phone
	au.ComplyFlow.KycStatus = customer.ComplyFlow.KycStatus
	au.ComplyFlow.FormID = customer.ComplyFlow.FormID
}

// ListAudience list audience
func ListAudience(ctx *gin.Context) {
	req := listAudienceParams{
		PagingRequest: request.PagingRequest{
			PageNumber: 1,
			PageSize:   10,
			// default sort by updated_at desc.
			// the updated_at means kyc status updated time.
			// if the kyc status is not updated, the updated_at will be the customer's created_at time.
			PageSort: "updated_at:d",
		},
	}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	query, kgError := req.ParseQuery([]string{"created_at", "updated_at"})
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	orgID := ctx.GetInt("org_id")
	audiences, pagingResp, kgErr := user360.ListAudience(ctx.Request.Context(), &user360.ListAudienceParams{
		OrgID:     orgID,
		KycStatus: req.KycStatus,
		Query:     query,
	})
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	vAudiences := make([]audience, len(audiences))
	for index := range vAudiences {
		vAudiences[index].fromService(audiences[index])
	}

	response.OKWithPaging(ctx, vAudiences, response.Paging{
		PageNumber: pagingResp.PageNumber,
		PageSize:   pagingResp.PageSize,
		TotalCount: pagingResp.TotalCount,
	})
}

type audienceDetail struct {
	UID        string  `json:"uid"`
	CreatedAt  int64   `json:"created_at"`
	Name       *string `json:"name"`
	Compliance struct {
		KycStatus     string  `json:"kyc_status"`
		PotentialRisk *int    `json:"potential_risk"`
		RiskLevel     *string `json:"risk_level"`
		AppliedAt     *int64  `json:"applied_at"`
		ApprovedAt    *int64  `json:"approved_at"`
		FormID        *int    `json:"form_id"`
		Email         *string `json:"email"`
		RealName      string  `json:"real_name"`
		NationName    string  `json:"nation_name"`
		NationalID    string  `json:"national_id"`
		DOB           *int64  `json:"dob"`
		Phone         *string `json:"phone"`
	} `json:"compliance"`
	Wallets []struct {
		ChainID string  `json:"chain_id"`
		Address string  `json:"address"`
		Balance float64 `json:"balance"`
		Tag     *string `json:"tag"`
	} `json:"wallets"`
}

func (au *audienceDetail) fromService(customer *user360.AudienceDetail) {
	au.UID = customer.UID
	au.CreatedAt = customer.CreatedAt
	au.Name = customer.Name
	au.Compliance.KycStatus = customer.Compliance.KycStatus
	au.Compliance.PotentialRisk = customer.Compliance.PotentialRisk
	au.Compliance.RiskLevel = customer.Compliance.RiskLevel
	au.Compliance.AppliedAt = customer.Compliance.AppliedAt
	au.Compliance.ApprovedAt = customer.Compliance.AuditedAt
	au.Compliance.FormID = customer.Compliance.FormID
	au.Compliance.Email = customer.Compliance.Email
	au.Compliance.RealName = customer.Compliance.RealName
	au.Compliance.NationName = customer.Compliance.NationName
	au.Compliance.NationalID = customer.Compliance.NationalID
	au.Compliance.DOB = customer.Compliance.DOB
	au.Compliance.Phone = customer.Compliance.Phone
	au.Wallets = make([]struct {
		ChainID string  `json:"chain_id"`
		Address string  `json:"address"`
		Balance float64 `json:"balance"`
		Tag     *string `json:"tag"`
	}, len(customer.Wallets))
	for index := range au.Wallets {
		au.Wallets[index].ChainID = customer.Wallets[index].ChainID
		au.Wallets[index].Address = customer.Wallets[index].Address
		au.Wallets[index].Balance = customer.Wallets[index].Balance
		au.Wallets[index].Tag = customer.Wallets[index].Tag
	}
}

// GetSingleAudience get single audience
func GetSingleAudience(c *gin.Context) {
	orgID := c.GetInt("org_id")
	uid := c.Param("uid")

	audience, kgErr := user360.SingleAudience(c.Request.Context(), orgID, uid)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	audienceResp := &audienceDetail{}
	audienceResp.fromService(audience)

	response.OK(c, audienceResp)
}
