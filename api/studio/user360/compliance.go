package user360

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/samber/lo"
)

// StatisticsComplianceResponseValue defines the response body for StatisticsComplianceValue.
type StatisticsComplianceResponseValue struct {
	Value int `json:"value"`
}

// StatisticsComplianceResponseComplyFlow defines the response body for StatisticsComplianceComplyFlow.
type StatisticsComplianceResponseComplyFlow struct {
	FormSubmission StatisticsComplianceResponseValue `json:"form_submission"`
	IDVTasks       StatisticsComplianceResponseValue `json:"idv_tasks"`
	CDDTasks       StatisticsComplianceResponseValue `json:"cdd_tasks"`
}

// StatisticsComplianceResponseKYC defines the response body for StatisticsComplianceKYC.
type StatisticsComplianceResponseKYC struct {
	PendingTasks      StatisticsComplianceResponseValue `json:"pending_tasks"`
	VerifiedCustomers StatisticsComplianceResponseValue `json:"verified_customers"`
	KYCStatus         struct {
		Verified StatisticsComplianceResponseValue `json:"verified"`
		Pending  StatisticsComplianceResponseValue `json:"pending"`
		Rejected StatisticsComplianceResponseValue `json:"rejected"`
	} `json:"kyc_status"`
	Wallet struct {
		Registered   StatisticsComplianceResponseValue `json:"registered"`
		Unregistered StatisticsComplianceResponseValue `json:"unregistered"`
	} `json:"wallet"`
}

// StatisticsComplianceResponse defines the response body for StatisticsCompliance.
type StatisticsComplianceResponse struct {
	ComplyFlow StatisticsComplianceResponseComplyFlow `json:"comply_flow"`
	KYC        StatisticsComplianceResponseKYC        `json:"kyc"`
}

// StatisticsCompliance returns compliance statistics.
func StatisticsCompliance(c *gin.Context) {
	statistics, kgErr := customer.StatisticsComplianceByOrg(c, c.GetInt("org_id"))
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp := StatisticsComplianceResponse{
		ComplyFlow: StatisticsComplianceResponseComplyFlow{
			FormSubmission: StatisticsComplianceResponseValue{
				Value: statistics.TotalFormSubmission,
			},
			IDVTasks: StatisticsComplianceResponseValue{
				Value: statistics.TotalIDVTasks,
			},
			CDDTasks: StatisticsComplianceResponseValue{
				Value: statistics.TotalCDDTasks,
			},
		},
		KYC: StatisticsComplianceResponseKYC{
			PendingTasks: StatisticsComplianceResponseValue{
				Value: statistics.PendingTasks,
			},
			VerifiedCustomers: StatisticsComplianceResponseValue{
				Value: statistics.VerifiedCustomers,
			},
			KYCStatus: struct {
				Verified StatisticsComplianceResponseValue `json:"verified"`
				Pending  StatisticsComplianceResponseValue `json:"pending"`
				Rejected StatisticsComplianceResponseValue `json:"rejected"`
			}{
				Verified: StatisticsComplianceResponseValue{
					Value: statistics.KYCStatus.Approved,
				},
				Pending: StatisticsComplianceResponseValue{
					Value: statistics.KYCStatus.Pending,
				},
				Rejected: StatisticsComplianceResponseValue{
					Value: statistics.KYCStatus.Rejected,
				},
			},
			Wallet: struct {
				Registered   StatisticsComplianceResponseValue `json:"registered"`
				Unregistered StatisticsComplianceResponseValue `json:"unregistered"`
			}{
				Registered: StatisticsComplianceResponseValue{
					Value: statistics.Wallet.Registered,
				},
				Unregistered: StatisticsComplianceResponseValue{
					Value: statistics.Wallet.Unregistered,
				},
			},
		},
	}

	response.OK(c, resp)
}

// SignGrafanaAuthToken signs grafana auth token.
func SignGrafanaAuthToken(c *gin.Context) {
	type authTokenResp struct {
		AuthToken string         `json:"auth_token"`
		Configs   map[string]any `json:"configs"`
	}

	ctx := c.Request.Context()
	uid := auth.GetUID(c)
	orgID := c.GetInt("org_id")

	org, kgErr := organization.GetOrgInfo(ctx, orgID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if !lo.Contains(org.Modules.User360, domain.User360Data) {
		response.ForbiddenErrorWithMsg(c, code.StudioOrganizationModuleNotEnabled, "module user_360.data not enabled")
		return
	}

	studioUser, kgErr := organization.GetUser(ctx, orgID, uid)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeGrafana)
	token, _, err := strategy.CreateToken(&jwtservice.GrafanaUserInfo{
		Name:  studioUser.Name,
		Email: studioUser.Email,
	})
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "sign grafana token, ERROR: "+err.Error(), map[string]interface{}{
			"uid": uid,
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		return
	}

	response.OK(c, authTokenResp{
		AuthToken: token,
		Configs: map[string]any{
			"kiosk": nil,
			"from":  org.CreatedAt,
		},
	})
}
