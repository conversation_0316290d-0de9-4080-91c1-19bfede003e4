package nftprojects

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/alert"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// TestGetAllNftProjects tests the GetAllNftProjects function
func TestGetAllNftProjects(t *testing.T) {
	orgID := 1
	url := "/v1/studio/organization/:orgID/nft/projects"

	rdb.Reset()
	setupStudioNftBoost(t)

	// Setup test server
	router := gin.Default()
	router.GET(url, auth.MockOrgID(orgID), GetAllNftProjects)

	// Mock the request
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/nft/projects", orgID)+"?page_number=1&page_size=10", nil)

	// Set the organization in the request context
	ctx := req.Context()
	req = req.WithContext(ctx)

	// Perform the request
	router.ServeHTTP(w, req)

	// Assert the response
	assert.Equal(t, http.StatusOK, w.Code)

	var response struct {
		Code   int
		Data   []rdb.StudioNftProjectStats
		Paging rdb.PagingParams
	}

	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)

	// Assert the response data
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, 5, len(response.Data))
	// check the order by created_at desc
	assert.Equal(t, "verify-failed-test", response.Data[0].EventID)
	assert.Equal(t, "Verify Failed Test", response.Data[0].CollectionName)

	assert.Equal(t, "test-erc1155", response.Data[1].EventID)
	assert.Equal(t, "Test Erc1155", response.Data[1].CollectionName)
}

// TestGetAllNftProjects tests the GetAllNftProjects function
func TestGetAllNftProjectsWithFilter(t *testing.T) {
	orgID := 1
	url := "/v1/studio/organization/:orgID/nft/projects"

	rdb.Reset()
	setupStudioNftBoost(t)

	// Setup test server
	router := gin.Default()
	router.GET(url, auth.MockOrgID(orgID), GetAllNftProjects)

	// Mock the request
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/nft/projects", orgID)+"?q=draft&page_number=1&page_size=10", nil)

	// Perform the request
	router.ServeHTTP(w, req)

	// Assert the response
	assert.Equal(t, http.StatusOK, w.Code)

	var response struct {
		Code   int
		Data   []rdb.StudioNftProjectStats
		Paging rdb.PagingParams
	}

	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)

	// Assert the response data
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, 2, len(response.Data))
	assert.Equal(t, "big-things", response.Data[0].EventID)
	assert.Equal(t, "Big Things", response.Data[0].CollectionName)
}

func TestUpsertNftProjectSameCollection(t *testing.T) {
	// Mock the request
	body := map[string]interface{}{
		"collection_name": "kryptogo-yacht-club",
		"status":          "draft",
	}
	orgID := 1

	setupDB(t)
	httpCode, response := setupUpsertNftProject(t, orgID, body)
	assert.Equal(t, http.StatusBadRequest, httpCode)
	assert.Equal(t, code.StudioNftCollectionNameExists, response.Code)
}

func TestUpsertNftProjectNoData(t *testing.T) {
	// Mock the request
	body := map[string]interface{}{
		"status": "draft",
	}
	orgID := 1

	setupDB(t)
	httpCode, response := setupUpsertNftProject(t, orgID, body)
	assert.Equal(t, http.StatusOK, httpCode)
	assert.Equal(t, code.OK, response.Code)
	assert.NotEqual(t, 0, response.Data.ProjectID)
}

// TestUpsertNftProjectPublish should success
func TestUpsertNftProjectPublish(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	srv := signing.SetupDeploySmartContract(t)
	defer func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}()

	orgID := 1
	randString := util.RandString(5)
	setupDB(t)
	server.Init(rdb.GormRepo(), &alert.SlackAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	// Mock the request
	body := map[string]interface{}{
		"chain_id":               "sepolia",
		"status":                 "publish",
		"collection_image_url":   randString,
		"collection_name":        fmt.Sprintf("%s %s %s", randString, randString, randString),
		"symbol_name":            randString,
		"collection_description": randString,
		"banner_image_url":       randString,
		"contract_schema_name":   "ERC721",
		"max_supply":             100,
		"start_time":             time.Now().Unix(),
		"end_time":               time.Now().Add(1 * time.Hour).Unix(),
	}

	httpCode, response := setupUpsertNftProject(t, orgID, body)
	assert.Equal(t, http.StatusOK, httpCode)
	assert.Equal(t, code.OK, response.Code)
	assert.NotEqual(t, 0, response.Data.ProjectID)

	// wait for publish status
	time.Sleep(160 * time.Second) // wait for verify contract
	waitProjectPublished(t, orgID, response.Data.ProjectID)

	// check db
	orgInfo, kgErr := organization.GetOrgInfo(context.Background(), orgID)
	assert.Nil(t, kgErr)
	project, err := rdb.GetStudioNftProjectByID(context.Background(), orgInfo.Name, response.Data.ProjectID)
	assert.Nil(t, err)
	assert.Equal(t, "sepolia", *project.ChainID)
	assert.Equal(t, "publish", string(project.Status))
	assert.Equal(t, randString, *project.CollectionImageURL)
	assert.Equal(t, fmt.Sprintf("%s %s %s", randString, randString, randString), *project.CollectionName)
	assert.Equal(t, randString, *project.SymbolName)
	assert.Equal(t, randString, *project.CollectionDescription)

	// check the event is created
	event, errCode, err := rdb.GetAirdropEvent(context.Background(), *project.EventID, util.LocaleEnUS)
	assert.Nil(t, err)
	assert.Equal(t, 0, errCode)
	assert.Nil(t, event.Title)

	// upsert
	body = map[string]interface{}{
		"project_id":        response.Data.ProjectID,
		"chain_id":          "sepolia",
		"title":             "title_after",
		"subtitle":          "subtitle_after",
		"favicon_image_url": "favicon_image_url_after",
		"msg_content":       "msg_content_after",
	}
	httpCode, response = setupUpsertNftProject(t, orgID, body)
	assert.Equal(t, http.StatusOK, httpCode)
	assert.Equal(t, code.OK, response.Code)
	assert.Equal(t, response.Data.ProjectID, response.Data.ProjectID)

	// check the event is updated
	event, errCode, err = rdb.GetAirdropEvent(context.Background(), *project.EventID, util.LocaleEnUS)
	assert.Nil(t, err)
	assert.Equal(t, 0, errCode)
	assert.Equal(t, "title_after", *event.Title)
	assert.Equal(t, "subtitle_after", *event.SubTitle)
	assert.NotEqual(t, "msg_content_after", *event.MsgContent) // Now msg_content is from templates. It should not be updated.
	assert.Equal(t, "favicon_image_url_after", *event.FaviconURL)
}

func TestUpsertNftProjectPublishNoNecessary(t *testing.T) {

	// Mock the request
	body := map[string]interface{}{
		"status": "publish",
	}
	orgID := 1

	setupDB(t)
	httpCode, response := setupUpsertNftProject(t, orgID, body)
	assert.Equal(t, http.StatusBadRequest, httpCode)
	assert.Equal(t, code.ParamIncorrect, response.Code)
}

func TestUpsertNftProjectModifyWhenDraft(t *testing.T) {
	// Mock the request
	projectID := 3
	collectionName := util.RandString(10)
	body := map[string]interface{}{
		"status":          "draft",
		"collection_name": collectionName,
		"project_id":      projectID,
		"title":           "test",
	}
	orgID := 1

	setupDB(t)
	httpCode, response := setupUpsertNftProject(t, orgID, body)
	assert.Equal(t, http.StatusOK, httpCode)
	assert.Equal(t, code.OK, response.Code)

	// check the project is updated
	time.Sleep(1 * time.Second)
	orgInfo, kgErr := organization.GetOrgInfo(context.Background(), orgID)
	assert.Nil(t, kgErr)
	project, err := rdb.GetStudioNftProjectByID(context.Background(), orgInfo.Name, projectID)
	assert.Nil(t, err)
	fmt.Printf("%+v\n", project)
	assert.Equal(t, "test", *project.Title)
	assert.Equal(t, collectionName, *project.CollectionName)
}

// TestUpsertNftProjectModifyWhenPublish should success. Modify these allowed fields when the project is published.
func TestUpsertNftProjectModifyWhenPublish(t *testing.T) {
	// Mock the request
	projectID := 2
	body := map[string]interface{}{
		"status":     "draft",
		"project_id": projectID,
		"title":      "test",
		"chain_id":   "sepolia",
	}
	orgID := 1

	setupDB(t)
	httpCode, response := setupUpsertNftProject(t, orgID, body)
	assert.Equal(t, http.StatusOK, httpCode)
	assert.Equal(t, code.OK, response.Code)

	// check the project is updated
	orgInfo, kgErr := organization.GetOrgInfo(context.Background(), orgID)
	assert.Nil(t, kgErr)
	project, err := rdb.GetStudioNftProjectByID(context.Background(), orgInfo.Name, projectID)
	assert.Nil(t, err)
	assert.Equal(t, "test", *project.Title)
}

// TestUpsertNftProjectRepublish should fail. The project is already published
func TestUpsertNftProjectRepublish(t *testing.T) {
	projectID := 2
	// Mock the request
	body := map[string]interface{}{
		"status":                 "publish",
		"collection_image_url":   util.RandString(10),
		"collection_name":        util.RandString(10),
		"symbol_name":            util.RandString(10),
		"collection_description": util.RandString(10),
		"banner_image_url":       util.RandString(10),
		"contract_schema_name":   "ERC721",
		"max_supply":             100,
		"project_id":             projectID,
		"start_time":             time.Now().Unix(),
		"end_time":               time.Now().Add(1 * time.Hour).Unix(),
	}
	orgID := 1

	setupDB(t)
	httpCode, response := setupUpsertNftProject(t, orgID, body)
	assert.Equal(t, http.StatusBadRequest, httpCode)
	assert.Equal(t, code.StudioNftProjectAlreadyPublished, response.Code)
}

// TestUpsertNftProjectModifyWhenPublishInvalidField should fail. Modify these fields when the project is published.
func TestUpsertNftProjectModifyWhenPublishInvalidField(t *testing.T) {
	// Mock the request
	projectID := 2
	collectionName := util.RandString(10)
	body := map[string]interface{}{
		"status":          "draft",
		"collection_name": collectionName,
		"project_id":      projectID,
	}
	orgID := 1

	setupDB(t)
	httpCode, response := setupUpsertNftProject(t, orgID, body)
	assert.Equal(t, http.StatusBadRequest, httpCode)
	assert.Equal(t, code.ParamIncorrect, response.Code)
}

type upsertNftProjectResponse struct {
	Code int `json:"code"`
	Data struct {
		ProjectID int `json:"project_id"`
	} `json:"data"`
}

func setupDB(t *testing.T) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateNftProjects(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAirdropEvents(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
}

// setupUpsertNftProject mock organization as "kryptogo"
func setupUpsertNftProject(t *testing.T, orgID int, body map[string]interface{}) (int, *upsertNftProjectResponse) {
	url := "/v1/studio/organization/:orgID/nft/projects"

	rdb.Reset()
	setupStudioNftBoost(t)

	// Setup test server
	router := gin.Default()
	router.PUT(url, auth.MockOrgID(orgID), UpsertNftProject)

	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/v1/studio/organization/%d/nft/projects", orgID), strings.NewReader(string(bodyStr)))

	// Perform the request
	router.ServeHTTP(w, req)

	// Assert the response
	response := upsertNftProjectResponse{}
	fmt.Println(w.Body.String())
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)

	return w.Code, &response
}

func waitProjectPublished(t *testing.T, orgID, projectID int) {
	waitTimes := 5
	orgInfo, kgErr := organization.GetOrgInfo(context.Background(), orgID)
	assert.Nil(t, kgErr)
	// wait for publish status
	for i := 0; i < waitTimes; i++ {
		project, err := rdb.GetStudioNftProjectByID(context.Background(), orgInfo.Name, projectID)
		assert.Nil(t, err)
		if project != nil && project.PublishStatus != nil && *project.PublishStatus == "success" {
			return
		}

		time.Sleep(1 * time.Second)
	}

	assert.Fail(t, "wait for publish status timeout")
}

func setupStudioNftBoost(t *testing.T) {
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAirdropEvents(rdb.Get()))
	assert.Nil(t, rdbtest.CreateNftProjects(rdb.Get()))
	organization.Init(organization.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})
	server.Init(rdb.GormRepo(), &alert.SlackAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
}
