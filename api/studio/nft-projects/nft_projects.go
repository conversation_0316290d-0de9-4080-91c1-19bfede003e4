package nftprojects

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/nft"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"gorm.io/gorm"
)

// UpsertNftProject upsert nft project
func UpsertNftProject(ctx *gin.Context) {
	req := nft.UpsertProjectParams{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	orgID := ctx.GetInt("org_id")
	orgInfo, kgErr := organization.GetOrgInfo(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	if req.EndTime == nil {
		req.EndTime = util.Ptr(util.GetMaxTime().Unix())
	}

	// check the event-id is unique
	if req.CollectionName != nil {
		// check collection-name
		exists, err := rdb.CheckStudioNftProjectExists(ctx.Request.Context(), req.ProjectID, *req.CollectionName)
		if exists {
			response.BadRequestWithMsg(ctx, code.StudioNftCollectionNameExists, "collection name already exists")
			return
		} else if err != nil {
			response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
			return
		}

		// check event-id
		eventID := nft.GetEventID(*req.CollectionName)
		exists, err = rdb.CheckAirdropEventExists(ctx.Request.Context(), eventID)
		if exists {
			response.BadRequestWithMsg(ctx, code.StudioNftCollectionNameExists, "collection name already exists")
			return
		} else if err != nil {
			response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
			return
		}
	}

	// v1 params
	if req.ChainID == "" {
		req.ChainID = model.ChainIDPolygon
	}

	projectID, kgErr := nft.UpsertProject(ctx.Request.Context(), &req, orgInfo.Name, orgInfo.ID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": map[string]interface{}{
			"project_id": projectID,
		},
	})

}

// GetAllNftProjects get all nft projects
func GetAllNftProjects(ctx *gin.Context) {
	params := rdb.GetStudioNftProjectStatsParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")
	orgInfo, kgErr := organization.GetOrgInfo(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	projects, paging, err := rdb.GetStudioNftProjectStats(ctx.Request.Context(), orgInfo.Name, params)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "GetStudioNftProjectStats failed", err.Error())
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}
	for i := range projects {
		projects[i].PublishStatus = util.Val(convertPublishStatus(util.Ptr(projects[i].PublishStatus)))
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code":   0,
		"data":   projects,
		"paging": paging,
	})
}

type getNftProject struct {
	ProjectID             int                       `json:"project_id"`
	Status                model.ProjectStatus       `json:"status"`
	CollectionImageURL    *string                   `json:"collection_image_url"`
	CollectionName        *string                   `json:"collection_name"`
	SymbolName            *string                   `json:"symbol_name"`
	CollectionDescription *string                   `json:"collection_description"`
	BannerImageURL        *string                   `json:"banner_image_url"`
	ConstractSchemaName   *model.ContractSchemaName `json:"contract_schema_name"`
	TotalSupply           *int                      `json:"total_supply"`
	MaxSupply             *int                      `json:"max_supply"`
	StartTime             *int64                    `json:"start_time"`
	EndTime               *int64                    `json:"end_time"`
	Title                 *string                   `json:"title"`
	Subtitle              *string                   `json:"subtitle"`
	FaviconImageURL       *string                   `json:"favicon_image_url"`
	MsgContent            *string                   `json:"msg_content"`
	PublishStatus         *model.PublishStatus      `json:"publish_status"`
	EventID               *string                   `json:"event_id"`
	ContractAddress       *string                   `json:"contract_address"`
}

// GetNftProject get all nft projects
func GetNftProject(ctx *gin.Context) {
	projectID, _ := strconv.Atoi(ctx.Param("project_id"))
	orgID := ctx.GetInt("org_id")
	orgInfo, kgErr := organization.GetOrgInfo(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	project, err := rdb.GetStudioNftProjectByID(ctx.Request.Context(), orgInfo.Name, projectID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.BadRequestWithMsg(ctx, code.StudioNftProjectNotFound, "project not found")
			return
		}

		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	resp := getNftProject{
		ProjectID:             project.ProjectID,
		Status:                project.Status,
		CollectionImageURL:    project.CollectionImageURL,
		CollectionName:        project.CollectionName,
		SymbolName:            project.SymbolName,
		CollectionDescription: project.CollectionDescription,
		BannerImageURL:        project.BannerImageURL,
		ConstractSchemaName:   project.ContractSchemaName,
		TotalSupply:           project.TotalSupply,
		MaxSupply:             project.MaxSupply,
		StartTime:             project.StartTime,
		EndTime:               project.EndTime,
		Title:                 project.Title,
		Subtitle:              project.Subtitle,
		FaviconImageURL:       project.FaviconImageURL,
		MsgContent:            project.MsgContent,
		PublishStatus:         convertPublishStatus(project.PublishStatus),
		EventID:               project.EventID,
		ContractAddress:       project.ContractAddress,
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": resp,
	})

}

// convertPublishStatus convert publish status to [pending, failed, success]
func convertPublishStatus(s *model.PublishStatus) *model.PublishStatus {
	if s != nil {
		switch *s {
		case model.PublishStatusFailed, model.PublishStatusInitFailed, model.PublishStatusDeployFailed, model.PublishStatusUploadNftItemsFailed, model.PublishStatusUploadNftCollectionFailed, model.PublishStatusUploadNftDataFailed:
			return util.Ptr(model.PublishStatusFailed)
		case model.PublishStatusSuccess:
			return util.Ptr(model.PublishStatusSuccess)
		default:
			return util.Ptr(model.PublishStatusPending)
		}
	}

	return nil
}
