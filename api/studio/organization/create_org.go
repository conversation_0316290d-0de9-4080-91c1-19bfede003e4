package organization

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// CreateOrgRequest represents the minimal request for creating an organization
type CreateOrgRequest struct {
	OrgName string `json:"org_name" binding:"required"`
	Email   string `json:"email" binding:"required"`
}

// CreateOrgResponse represents the response after creating a streamlined organization
type CreateOrgResponse struct {
	OrganizationID int `json:"organization_id"`
}

// CreateOrg creates a streamlined organization with minimal required data
// using the current authenticated user as the admin
func CreateOrg(c *gin.Context) {
	ctx := c.Request.Context()
	var req CreateOrgRequest

	// Get current user information from studio token
	uid := c.GetString("uid")
	if uid == "" {
		response.UnauthorizedWithMsg(c, code.UserNotFound, "User UID not found in context")
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
		return
	}

	kglog.InfoWithDataCtx(ctx, "Creating organization", map[string]interface{}{
		"admin_uid": uid,
		"org_name":  req.OrgName,
	})

	// Create organization without compliance integration
	orgReq := &organization.CreateOrganizationReq{
		OrgName:                  req.OrgName,
		AdminUID:                 uid,
		AdminName:                "admin",
		Email:                    req.Email,
		ComplianceOrganizationID: nil,
		ComplianceAPIKey:         nil,
	}

	resp, kgErr := organization.CreateOrganization(ctx, orgReq)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Set sign alert threshold and default organization modules in one update call
	modules := &organization.UpdateOrganizationReqModule{
		User360:       []domain.User360{},
		WalletBuilder: []domain.WalletBuilder{},
		AssetPro:      []domain.AssetPro{domain.AssetProTreasury, domain.AssetProSendToken, domain.AssetProTransactionHistory},
		NFTBoost:      []domain.NFTBoost{},
		Compliance:    []domain.Compliance{},
		Admin:         []domain.Admin{domain.AdminBilling},
	}

	updateReq := &organization.UpdateOrganizationReq{
		SignAlertThreshold: util.Ptr(10.0),
		Module:             modules,
	}

	if kgErr := organization.UpdateOrganization(ctx, resp.ID, updateReq); kgErr != nil {
		response.InternalServerErrorWithMsg(c, code.DBError, kgErr.Error.Error())
		return
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created organization", map[string]interface{}{
		"organization_id": resp.ID,
		"admin_uid":       uid,
	})

	resp_data := CreateOrgResponse{
		OrganizationID: resp.ID,
	}

	response.OK(c, resp_data)
}
