package organization

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
)

// setupTest initializes the database and services needed for testing
func setupTest(t *testing.T) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	user.Init(repo.Unified())

	// Initialize organization service
	initParam := organization.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	}

	organization.Init(initParam)

	setupSigningServer(t)
}

// TestCreateOrgHappyPath tests the happy path of creating an organization using a real database
func TestCreateOrgHappyPath(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()

	// Initialize database and services
	setupTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Set up test route with user authentication
	testUID := "test-user-" + util.RandString(5)
	router.POST("/organizations/create", func(c *gin.Context) {
		// Set user context for authentication
		c.Set("uid", testUID)
		CreateOrg(c)
	})

	// Create test request with organization name
	orgName := "Test Organization " + util.RandString(5)
	testEmail := "test-" + util.RandString(5) + "@example.com"
	reqBody := CreateOrgRequest{
		OrgName: orgName,
		Email:   testEmail,
	}
	jsonBody, err := json.Marshal(reqBody)
	s.NoError(err)

	req, err := http.NewRequest("POST", "/organizations/create", bytes.NewBuffer(jsonBody))
	s.NoError(err)
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response status
	if w.Code != http.StatusOK {
		t.Fatalf("Failed to create organization: %v", w.Body.String())
	}

	// Parse response
	var response struct {
		Data CreateOrgResponse `json:"data"`
	}
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))

	// Verify organization ID in response
	orgID := response.Data.OrganizationID
	s.NotZero(orgID)
	t.Logf("orgID: %d", orgID)

	// Verify organization was created in the database
	org, kgErr := rdb.GormRepo().GetOrganizationByID(ctx, orgID)
	if kgErr != nil {
		t.Fatalf("Failed to get organization by ID: %v", kgErr)
	}
	s.Equal(orgName, org.Name)

	// Verify user is an admin for the organization
	user, kgErr := rdb.GormRepo().GetStudioUser(ctx, orgID, testUID)
	if kgErr != nil {
		t.Fatalf("Failed to get studio user: %v", kgErr)
	}
	s.Equal(domain.StudioUserStatusActive, user.Status)
	s.Contains(user.Roles, domain.StudioRole{Module: "", Name: "owner"})
	s.Equal(testEmail, user.Email)

	// Verify organization modules were enabled
	modules, kgErr := organization.GetOrgEnabledModules(ctx, orgID)
	if kgErr != nil {
		t.Fatalf("Failed to get organization modules: %v", kgErr)
	}
	// Should have AdminBilling enabled
	s.Contains(modules.Admin, domain.AdminBilling)
	s.Contains(modules.AssetPro, domain.AssetProTreasury)
	s.Contains(modules.AssetPro, domain.AssetProSendToken)
	s.Contains(modules.AssetPro, domain.AssetProTransactionHistory)

	// Verify organization update (sign alert threshold)
	org, kgErr = rdb.GormRepo().GetOrganizationByID(ctx, orgID)
	if kgErr != nil {
		t.Fatalf("Failed to get organization by ID: %v", kgErr)
	}
	expectedThreshold := 10.0
	s.Equal(expectedThreshold, org.SignAlertThreshold)
}
