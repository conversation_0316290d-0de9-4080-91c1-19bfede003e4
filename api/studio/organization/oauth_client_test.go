package organization

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
)

// setupOAuthClientTest initializes the database and services needed for testing
// and creates a test organization and user, returning the orgID and userID
func setupOAuthClientTest(t *testing.T) (int, string) {
	// Initialize DB and services
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))

	user.Init(repo.Unified())
	application.Init(rdb.GormRepo())

	// Initialize organization service
	initParam := organization.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	}
	organization.Init(initParam)
	setupSigningServer(t)

	// Create test organization
	ctx := context.Background()
	orgName := "Test Organization " + util.RandString(5)
	testUID := "test-user-" + util.RandString(5)

	// Create organization directly in the DB
	req := &organization.CreateOrganizationReq{
		OrgName:   orgName,
		AdminUID:  testUID,
		AdminName: "Test Admin",
	}
	resp, kgErr := organization.CreateOrganization(ctx, req)
	assert.Nil(t, kgErr, "Failed to create organization")
	orgID := int(resp.ID)

	// Add the user to the organization using the test helper
	assert.Nil(t, rdbtest.CreateStudioUsers(rdb.Get(), testUID, nil))

	return orgID, testUID
}

// TestCreateOAuthClientHappyPath tests the happy path of creating an OAuth client
func TestCreateOAuthClientHappyPath(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()

	// Initialize database and services and get test org/user
	orgID, testUID := setupOAuthClientTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Set up test route with authentication
	router.POST("/v1/studio/organization/:orgID/oauth_clients", func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		CreateOAuthClient(c)
	})

	// Create test request
	reqBody := CreateOAuthClientRequest{
		ClientName:   "Test OAuth Client",
		ClientDomain: "https://test.kryptogo.com",
		ClientType:   "mobile_wallet",
	}
	jsonBody, err := json.Marshal(reqBody)
	s.NoError(err)

	req, err := http.NewRequest("POST", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients", orgID), bytes.NewBuffer(jsonBody))
	s.NoError(err)
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response status
	s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())

	// Parse response
	var response struct {
		Data CreateOAuthClientResponse `json:"data"`
	}
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))

	// Verify response fields
	s.NotEmpty(response.Data.ClientID)
	s.NotEmpty(response.Data.ClientSecret)
	s.Equal(reqBody.ClientName, response.Data.ClientName)
	s.Equal(reqBody.ClientDomain, response.Data.ClientDomain)

	// Verify OAuth client was created in the database
	clientID := response.Data.ClientID
	app, kgErr := application.GetOAuthApplication(ctx, clientID)
	s.Nil(kgErr, "Failed to get OAuth application")
	s.Equal(reqBody.ClientName, app.Name)
	s.Equal(reqBody.ClientDomain, app.Domain)
	s.Equal(domain.OAuthApplicationTypeMobileWallet.String(), app.Type.String())
}

// TestGetOAuthClientHappyPath tests the happy path of retrieving an OAuth client
func TestGetOAuthClientHappyPath(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()

	// Initialize database and services and get test org/user
	orgID, testUID := setupOAuthClientTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Create test OAuth client directly
	clientName := "Test OAuth Client"
	clientDomain := "https://test.kryptogo.com"
	clientID := "test-oauth-client-" + util.RandString(5)
	clientSecret := util.RandString(32)

	appType := domain.OAuthApplicationTypeMobileWallet
	squareLogo := defaultOauthClientSquareLogo

	app := &domain.OAuthApplication{
		Application: domain.Application{
			ClientID:     clientID,
			ClientSecret: clientSecret,
			Name:         clientName,
			Domain:       clientDomain,
		},
		IsPrivileged:   true,
		SupportAddress: "",
		MainLogo:       defaultOauthClientMainLogo,
		SquareLogo:     &squareLogo,
		Type:           &appType,
	}

	kgErr := application.UpsertOAuthApplication(ctx, orgID, app)
	s.Nil(kgErr, "Failed to create OAuth application")

	// Set up test route with authentication
	router.GET("/v1/studio/organization/:orgID/oauth_clients/:client_id", func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		GetOAuthClient(c)
	})

	// Create test request
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients/%s", orgID, clientID), nil)
	s.NoError(err)

	// Perform request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response status
	s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())

	// Parse response
	var response struct {
		Data OAuthClientResponse `json:"data"`
	}
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))

	// Verify response fields
	s.Equal(clientID, response.Data.ClientID)
	s.Equal(clientName, response.Data.ClientName)
	s.Equal(clientDomain, response.Data.ClientDomain)
	s.Equal(appType.String(), response.Data.ClientType)
	s.Equal(defaultOauthClientMainLogo, response.Data.MainLogo)
	s.Equal(squareLogo, response.Data.SquareLogo)
}

// TestListOAuthClientsHappyPath tests the happy path of listing OAuth clients
func TestListOAuthClientsHappyPath(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()

	// Initialize database and services and get test org/user
	orgID, testUID := setupOAuthClientTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Create multiple test OAuth clients
	for range 3 {
		clientName := "Test OAuth Client " + util.RandString(3)
		clientDomain := "https://test" + util.RandString(3) + ".kryptogo.com"
		clientID := "test-oauth-client-" + util.RandString(5)
		clientSecret := util.RandString(32)

		appType := domain.OAuthApplicationTypeMobileWallet
		squareLogo := defaultOauthClientSquareLogo

		app := &domain.OAuthApplication{
			Application: domain.Application{
				ClientID:     clientID,
				ClientSecret: clientSecret,
				Name:         clientName,
				Domain:       clientDomain,
			},
			IsPrivileged:   true,
			SupportAddress: "",
			MainLogo:       defaultOauthClientMainLogo,
			SquareLogo:     &squareLogo,
			Type:           &appType,
		}

		kgErr := application.UpsertOAuthApplication(ctx, orgID, app)
		s.Nil(kgErr, "Failed to create OAuth application")
	}

	// Set up test route with authentication
	router.GET("/v1/studio/organization/:orgID/oauth_clients", middleware.Pagination(), func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		c.Next()
	}, ListOAuthClients)

	// Create test request
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients", orgID), nil)
	s.NoError(err)

	// Perform request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response status
	s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())

	// Parse response
	var response struct {
		Data []OAuthClientResponse `json:"data"`
	}
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))

	// Verify we have the expected number of clients
	s.Len(response.Data, 3)

	// Verify each client has the expected fields
	for _, client := range response.Data {
		s.NotEmpty(client.ClientID)
		s.NotEmpty(client.ClientName)
		s.NotEmpty(client.ClientDomain)
		s.Equal("mobile_wallet", client.ClientType)
		s.Equal(defaultOauthClientMainLogo, client.MainLogo)
	}
}

// TestUpdateOAuthClientHappyPath tests the happy path of updating an OAuth client
func TestUpdateOAuthClientHappyPath(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()

	// Initialize database and services and get test org/user
	orgID, testUID := setupOAuthClientTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Create test OAuth client
	clientName := "Test OAuth Client"
	clientDomain := "https://test.kryptogo.com"
	clientID := "test-oauth-client-" + util.RandString(5)
	clientSecret := util.RandString(32)

	appType := domain.OAuthApplicationTypeMobileWallet
	squareLogo := defaultOauthClientSquareLogo

	app := &domain.OAuthApplication{
		Application: domain.Application{
			ClientID:     clientID,
			ClientSecret: clientSecret,
			Name:         clientName,
			Domain:       clientDomain,
		},
		IsPrivileged:   true,
		SupportAddress: "",
		MainLogo:       defaultOauthClientMainLogo,
		SquareLogo:     &squareLogo,
		Type:           &appType,
	}

	kgErr := application.UpsertOAuthApplication(ctx, orgID, app)
	s.Nil(kgErr, "Failed to create OAuth application")

	// Set up test route with authentication
	router.PUT("/v1/studio/organization/:orgID/oauth_clients/:client_id", func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		UpdateOAuthClient(c)
	})

	// Create update request
	updatedName := "Updated OAuth Client"
	updatedLogo := "https://updated-logo-url.example.com/logo.png"

	reqBody := UpdateOAuthClientRequest{
		ClientName: updatedName,
		MainLogo:   updatedLogo,
	}
	jsonBody, err := json.Marshal(reqBody)
	s.NoError(err)

	req, err := http.NewRequest("PUT", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients/%s", orgID, clientID), bytes.NewBuffer(jsonBody))
	s.NoError(err)
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response status
	s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())

	// Parse response
	var response struct {
		Data OAuthClientResponse `json:"data"`
	}
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))

	// Verify response fields reflect updates
	s.Equal(clientID, response.Data.ClientID)
	s.Equal(updatedName, response.Data.ClientName)
	s.Equal(clientDomain, response.Data.ClientDomain)
	s.Equal(updatedLogo, response.Data.MainLogo)

	// Verify OAuth client was updated in the database
	updatedApp, kgErr := application.GetOAuthApplication(ctx, clientID)
	s.Nil(kgErr, "Failed to get OAuth application")
	s.Equal(updatedName, updatedApp.Name)
	s.Equal(updatedLogo, updatedApp.MainLogo)
}

// TestCreateOAuthClientWithInvalidDomain tests that a domain without http:// or https:// prefix is rejected
func TestCreateOAuthClientWithInvalidDomain(t *testing.T) {
	s := assert.New(t)

	// Initialize database and services and get test org/user
	orgID, testUID := setupOAuthClientTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Set up test route with authentication
	router.POST("/v1/studio/organization/:orgID/oauth_clients", func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		CreateOAuthClient(c)
	})

	testCases := []struct {
		name         string
		clientDomain string
		expectError  bool
	}{
		{"Invalid domain without prefix", "example.com", true},
		{"Invalid domain with incorrect prefix", "htp://example.com", true},
		{"Valid domain with http prefix", "http://example.com", false},
		{"Valid domain with https prefix", "https://example.com", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create test request with invalid domain
			reqBody := CreateOAuthClientRequest{
				ClientName:   "Test OAuth Client",
				ClientDomain: tc.clientDomain,
				ClientType:   "mobile_wallet",
			}
			jsonBody, err := json.Marshal(reqBody)
			s.NoError(err)

			req, err := http.NewRequest("POST", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients", orgID), bytes.NewBuffer(jsonBody))
			s.NoError(err)
			req.Header.Set("Content-Type", "application/json")

			// Perform request
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			if tc.expectError {
				// Expect a 400 Bad Request error for invalid domains
				s.Equal(http.StatusBadRequest, w.Code, "Response body: %v", w.Body.String())
				s.Contains(w.Body.String(), "domain must begin with http:// or https://")
			} else {
				// Expect a successful response for valid domains
				s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())
			}
		})
	}
}

// TestUpdateOAuthClientWithInvalidDomain tests that a domain update without http:// or https:// prefix is rejected
func TestUpdateOAuthClientWithInvalidDomain(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()

	// Initialize database and services and get test org/user
	orgID, testUID := setupOAuthClientTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Create test OAuth client
	clientName := "Test OAuth Client"
	clientDomain := "https://test.kryptogo.com"
	clientID := "test-oauth-client-" + util.RandString(5)
	clientSecret := util.RandString(32)

	appType := domain.OAuthApplicationTypeMobileWallet
	squareLogo := defaultOauthClientSquareLogo

	app := &domain.OAuthApplication{
		Application: domain.Application{
			ClientID:     clientID,
			ClientSecret: clientSecret,
			Name:         clientName,
			Domain:       clientDomain,
		},
		IsPrivileged:   true,
		SupportAddress: "",
		MainLogo:       defaultOauthClientMainLogo,
		SquareLogo:     &squareLogo,
		Type:           &appType,
	}

	kgErr := application.UpsertOAuthApplication(ctx, orgID, app)
	s.Nil(kgErr, "Failed to create OAuth application")

	// Set up test route with authentication
	router.PUT("/v1/studio/organization/:orgID/oauth_clients/:client_id", func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		UpdateOAuthClient(c)
	})

	testCases := []struct {
		name         string
		clientDomain string
		expectError  bool
	}{
		{"Invalid domain without prefix", "updated-example.com", true},
		{"Invalid domain with incorrect prefix", "htp://updated-example.com", true},
		{"Valid domain with http prefix", "http://updated-example.com", false},
		{"Valid domain with https prefix", "https://updated-example.com", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create update request with the test domain
			reqBody := UpdateOAuthClientRequest{
				ClientDomain: tc.clientDomain,
			}
			jsonBody, err := json.Marshal(reqBody)
			s.NoError(err)

			req, err := http.NewRequest("PUT", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients/%s", orgID, clientID), bytes.NewBuffer(jsonBody))
			s.NoError(err)
			req.Header.Set("Content-Type", "application/json")

			// Perform request
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			if tc.expectError {
				// Expect a 400 Bad Request error for invalid domains
				s.Equal(http.StatusBadRequest, w.Code, "Response body: %v", w.Body.String())
				s.Contains(w.Body.String(), "domain must begin with http:// or https://")
			} else {
				// Expect a successful response for valid domains
				s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())

				// Verify the domain was updated in the database
				updatedApp, kgErr := application.GetOAuthApplication(ctx, clientID)
				s.Nil(kgErr, "Failed to get OAuth application")
				s.Equal(tc.clientDomain, updatedApp.Domain)
			}
		})
	}
}

// TestDomainWithTrailingSlash tests that domains with trailing slashes are properly formatted
func TestDomainWithTrailingSlash(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()

	// Initialize database and services and get test org/user
	orgID, testUID := setupOAuthClientTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Set up test route with authentication for create
	router.POST("/v1/studio/organization/:orgID/oauth_clients", func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		CreateOAuthClient(c)
	})

	// Test creating with trailing slash
	t.Run("Create with trailing slash", func(t *testing.T) {
		// Domain with trailing slash
		domainWithSlash := "https://example.com/"
		expectedDomain := "https://example.com"

		// Create test request
		reqBody := CreateOAuthClientRequest{
			ClientName:   "Test Client With Slash",
			ClientDomain: domainWithSlash,
			ClientType:   "mobile_wallet",
		}
		jsonBody, err := json.Marshal(reqBody)
		s.NoError(err)

		req, err := http.NewRequest("POST", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients", orgID), bytes.NewBuffer(jsonBody))
		s.NoError(err)
		req.Header.Set("Content-Type", "application/json")

		// Perform request
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Check response status
		s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())

		// Parse response
		var response struct {
			Data CreateOAuthClientResponse `json:"data"`
		}
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))

		// Verify the trailing slash was removed in the response
		s.Equal(expectedDomain, response.Data.ClientDomain)

		// Verify in the database
		clientID := response.Data.ClientID
		app, kgErr := application.GetOAuthApplication(ctx, clientID)
		s.Nil(kgErr, "Failed to get OAuth application")
		s.Equal(expectedDomain, app.Domain)
	})

	// Create a client for update test
	clientID := "test-oauth-client-" + util.RandString(5)
	appType := domain.OAuthApplicationTypeMobileWallet
	squareLogo := defaultOauthClientSquareLogo

	app := &domain.OAuthApplication{
		Application: domain.Application{
			ClientID:     clientID,
			ClientSecret: util.RandString(32),
			Name:         "Update Test Client",
			Domain:       "https://original-domain.com",
		},
		IsPrivileged:   true,
		SupportAddress: "",
		MainLogo:       defaultOauthClientMainLogo,
		SquareLogo:     &squareLogo,
		Type:           &appType,
	}

	kgErr := application.UpsertOAuthApplication(ctx, orgID, app)
	s.Nil(kgErr, "Failed to create OAuth application")

	// Set up test route for update
	router.PUT("/v1/studio/organization/:orgID/oauth_clients/:client_id", func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		UpdateOAuthClient(c)
	})

	// Test updating with trailing slash
	t.Run("Update with trailing slash", func(t *testing.T) {
		// Domain with trailing slash
		domainWithSlash := "https://updated-example.com/"
		expectedDomain := "https://updated-example.com"

		// Create update request
		reqBody := UpdateOAuthClientRequest{
			ClientDomain: domainWithSlash,
		}
		jsonBody, err := json.Marshal(reqBody)
		s.NoError(err)

		req, err := http.NewRequest("PUT", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients/%s", orgID, clientID), bytes.NewBuffer(jsonBody))
		s.NoError(err)
		req.Header.Set("Content-Type", "application/json")

		// Perform request
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Check response status
		s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())

		// Parse response
		var response struct {
			Data OAuthClientResponse `json:"data"`
		}
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))

		// Verify the trailing slash was removed in the response
		s.Equal(expectedDomain, response.Data.ClientDomain)

		// Verify in the database
		updatedApp, kgErr := application.GetOAuthApplication(ctx, clientID)
		s.Nil(kgErr, "Failed to get OAuth application")
		s.Equal(expectedDomain, updatedApp.Domain)
	})
}

// TestGenerateClientIDSanitization tests that the generateClientID function properly
// sanitizes non-ASCII characters and creates valid IDs
func TestGenerateClientIDSanitization(t *testing.T) {
	s := assert.New(t)

	testCases := []struct {
		name            string
		input           string
		expectedPattern string // We can't check the exact output due to randomness
	}{
		{
			name:            "ASCII Only",
			input:           "Test Client Name",
			expectedPattern: "test-client-name-[a-f0-9]{6}",
		},
		{
			name:            "With Non-ASCII Characters",
			input:           "Café with 你好 and 👋",
			expectedPattern: "caf-with-and-[a-f0-9]{6}",
		},
		{
			name:            "With Special Characters",
			input:           "Test@Client#Name!",
			expectedPattern: "testclientname-[a-f0-9]{6}",
		},
		{
			name:            "With Consecutive Special Characters",
			input:           "Test---Client   Name",
			expectedPattern: "test--client--name-[a-f0-9]{6}",
		},
		{
			name:            "Only Non-ASCII",
			input:           "你好👋🌟",
			expectedPattern: "client-[a-f0-9]{6}",
		},
		{
			name:            "Empty String",
			input:           "",
			expectedPattern: "client-[a-f0-9]{6}",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := generateClientID(tc.input)
			s.Regexp(tc.expectedPattern, result, "Generated client ID doesn't match expected pattern")

			// Check that the result only contains allowed characters
			for _, r := range result {
				s.True(
					(r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '-',
					"Generated ID contains non-allowed character: %c", r,
				)
			}
		})
	}
}

// TestCreateOAuthClientWithNonASCIIName tests creating an OAuth client with non-ASCII characters in the name
func TestCreateOAuthClientWithNonASCIIName(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()

	// Initialize database and services and get test org/user
	orgID, testUID := setupOAuthClientTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Set up test route with authentication
	router.POST("/v1/studio/organization/:orgID/oauth_clients", func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		CreateOAuthClient(c)
	})

	// Create test request with non-ASCII name
	reqBody := CreateOAuthClientRequest{
		ClientName:   "中文名称测试 Japanese テスト",
		ClientDomain: "https://international.example.com",
		ClientType:   "mobile_wallet",
	}
	jsonBody, err := json.Marshal(reqBody)
	s.NoError(err)

	req, err := http.NewRequest("POST", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients", orgID), bytes.NewBuffer(jsonBody))
	s.NoError(err)
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response status
	s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())

	// Parse response
	var response struct {
		Data CreateOAuthClientResponse `json:"data"`
	}
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))

	// Verify response fields
	s.NotEmpty(response.Data.ClientID)
	s.NotEmpty(response.Data.ClientSecret)
	s.Equal(reqBody.ClientName, response.Data.ClientName)

	// Verify the client ID contains only ASCII characters
	for _, r := range response.Data.ClientID {
		s.True((r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '-' || r == '_',
			"Character %c in client ID is not allowed", r)
	}

	// Verify OAuth client was created in the database
	clientID := response.Data.ClientID
	app, kgErr := application.GetOAuthApplication(ctx, clientID)
	s.Nil(kgErr, "Failed to get OAuth application")
	s.Equal(reqBody.ClientName, app.Name)
}

// TestCreateOAuthClientWithoutDomain tests creating an OAuth client without providing a domain
func TestCreateOAuthClientWithoutDomain(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()

	// Initialize database and services and get test org/user
	orgID, testUID := setupOAuthClientTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Set up test route with authentication
	router.POST("/v1/studio/organization/:orgID/oauth_clients", func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		CreateOAuthClient(c)
	})

	// Create test request without domain
	reqBody := CreateOAuthClientRequest{
		ClientName:   "Test OAuth Client Without Domain",
		ClientDomain: "", // Empty domain
		ClientType:   "mobile_wallet",
	}
	jsonBody, err := json.Marshal(reqBody)
	s.NoError(err)

	req, err := http.NewRequest("POST", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients", orgID), bytes.NewBuffer(jsonBody))
	s.NoError(err)
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response status
	s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())

	// Parse response
	var response struct {
		Data CreateOAuthClientResponse `json:"data"`
	}
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))

	// Verify response fields
	s.NotEmpty(response.Data.ClientID)
	s.NotEmpty(response.Data.ClientSecret)
	s.Equal(reqBody.ClientName, response.Data.ClientName)
	s.Equal("", response.Data.ClientDomain) // Domain should be empty

	// Verify OAuth client was created in the database
	clientID := response.Data.ClientID
	app, kgErr := application.GetOAuthApplication(ctx, clientID)
	s.Nil(kgErr, "Failed to get OAuth application")
	s.Equal(reqBody.ClientName, app.Name)
	s.Equal("", app.Domain) // Domain should be empty in the database
}

// TestValidateAndFormatDomain tests the helper function for domain validation
func TestValidateAndFormatDomain(t *testing.T) {
	s := assert.New(t)

	testCases := []struct {
		name           string
		input          string
		expectedOutput string
		expectError    bool
	}{
		{"Empty domain", "", "", false},
		{"Valid HTTP domain", "http://example.com", "http://example.com", false},
		{"Valid HTTPS domain", "https://example.com", "https://example.com", false},
		{"Domain with trailing slash", "https://example.com/", "https://example.com", false},
		{"Invalid domain without protocol", "example.com", "", true},
		{"Invalid protocol", "ftp://example.com", "", true},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			output, err := validateAndFormatDomain(tc.input)

			if tc.expectError {
				s.Error(err)
			} else {
				s.NoError(err)
				s.Equal(tc.expectedOutput, output)
			}
		})
	}
}

// TestCreateOAuthClientWithoutTypeAndID tests creating an OAuth client without providing client type or ID
func TestCreateOAuthClientWithoutTypeAndID(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()

	// Initialize database and services and get test org/user
	orgID, testUID := setupOAuthClientTest(t)

	// Set up test router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Set up test route with authentication
	router.POST("/v1/studio/organization/:orgID/oauth_clients", func(c *gin.Context) {
		// Set context values
		c.Set("org_id", orgID)
		c.Set("uid", testUID)
		CreateOAuthClient(c)
	})

	// Create test request without client type and client ID
	reqBody := CreateOAuthClientRequest{
		ClientName:   "Test OAuth Client Without Type And ID",
		ClientDomain: "https://example.com",
		// ClientType is intentionally omitted
	}
	jsonBody, err := json.Marshal(reqBody)
	s.NoError(err)

	req, err := http.NewRequest("POST", fmt.Sprintf("/v1/studio/organization/%d/oauth_clients", orgID), bytes.NewBuffer(jsonBody))
	s.NoError(err)
	req.Header.Set("Content-Type", "application/json")

	// Perform request
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Check response status
	s.Equal(http.StatusOK, w.Code, "Response body: %v", w.Body.String())

	// Parse response
	var response struct {
		Data CreateOAuthClientResponse `json:"data"`
	}
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))

	// Verify response fields
	s.NotEmpty(response.Data.ClientID)
	s.NotEmpty(response.Data.ClientSecret)
	s.Equal(reqBody.ClientName, response.Data.ClientName)
	s.Equal(reqBody.ClientDomain, response.Data.ClientDomain)

	// Verify OAuth client was created in the database
	clientID := response.Data.ClientID
	app, kgErr := application.GetOAuthApplication(ctx, clientID)
	s.Nil(kgErr, "Failed to get OAuth application")
	s.Equal(reqBody.ClientName, app.Name)
	s.Equal(reqBody.ClientDomain, app.Domain)
	s.Equal(domain.OAuthApplicationTypeWebApp.String(), app.Type.String())
}
