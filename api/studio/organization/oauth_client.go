package organization

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
)

const (
	defaultOauthClientMainLogo   = "https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png"
	defaultOauthClientSquareLogo = "https://wallet-static.kryptogo.com/public/icon/KryptoGO-24.png"
)

// CreateOAuthClientRequest represents the request for creating an OAuth client
type CreateOAuthClientRequest struct {
	// OAuth client information
	ClientName   string `json:"client_name" binding:"required"`
	ClientDomain string `json:"client_domain"` // Must begin with http:// or https:// and not end with /
	ClientType   string `json:"client_type"`
}

// UpdateOAuthClientRequest represents the request for updating an OAuth client
type UpdateOAuthClientRequest struct {
	ClientName     string `json:"client_name,omitempty"`
	ClientDomain   string `json:"client_domain,omitempty"` // Must begin with http:// or https:// and not end with /
	ClientType     string `json:"client_type,omitempty"`
	MainLogo       string `json:"main_logo,omitempty"`
	SquareLogo     string `json:"square_logo,omitempty"`
	AppStoreLink   string `json:"app_store_link,omitempty"`
	GooglePlayLink string `json:"google_play_link,omitempty"`
}

// CreateOAuthClientResponse represents the response after creating an OAuth client
type CreateOAuthClientResponse struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	ClientName   string `json:"client_name"`
	ClientDomain string `json:"client_domain"` // Format: http(s)://domain (no trailing slash)
}

// OAuthClientResponse represents the response for OAuth client data
type OAuthClientResponse struct {
	ClientID       string `json:"client_id"`
	ClientName     string `json:"client_name"`
	ClientDomain   string `json:"client_domain"` // Format: http(s)://domain (no trailing slash)
	ClientType     string `json:"client_type"`
	MainLogo       string `json:"main_logo"`
	SquareLogo     string `json:"square_logo,omitempty"`
	CreatedAt      int64  `json:"created_at,omitempty"`
	AppStoreLink   string `json:"app_store_link,omitempty"`
	GooglePlayLink string `json:"google_play_link,omitempty"`
}

// ListOAuthClients lists all OAuth clients for an organization
func ListOAuthClients(c *gin.Context) {
	ctx := c.Request.Context()

	// Get organization ID from path
	orgID, exists := c.Get("org_id")
	if !exists {
		kglog.ErrorWithDataCtx(ctx, "Organization ID not found in context", nil)
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Organization ID not found")
		return
	}

	organizationID := orgID.(int)

	// Get pagination parameters from context
	pageNum, pageSizeNum, _ := middleware.GetPaginationFromContext(c)

	// Create params struct
	params := domain.GetOAuthApplicationInOrgParams{
		OrgID:    organizationID,
		Page:     pageNum,
		PageSize: pageSizeNum,
	}

	// Get all OAuth applications for this organization with pagination
	oauthApps, totalCount, kgErr := application.GetOAuthApplicationInOrg(ctx, params)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to retrieve OAuth clients", map[string]interface{}{
			"organization_id": organizationID,
			"error":           kgErr.Error.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.DBError, "Failed to retrieve OAuth clients")
		return
	}

	// Convert to response format
	var respData []OAuthClientResponse
	for _, app := range oauthApps {
		clientType := ""
		if app.Type != nil {
			clientType = app.Type.String()
		}

		squareLogo := ""
		if app.SquareLogo != nil {
			squareLogo = *app.SquareLogo
		}

		respData = append(respData, OAuthClientResponse{
			ClientID:       app.ClientID,
			ClientName:     app.Name,
			ClientDomain:   app.Domain,
			ClientType:     clientType,
			MainLogo:       app.MainLogo,
			SquareLogo:     squareLogo,
			CreatedAt:      app.CreatedAt.Unix(),
			AppStoreLink:   app.AppStoreLink,
			GooglePlayLink: app.GooglePlayLink,
		})
	}

	// Create paging info
	paging := response.Paging{
		PageNumber: pageNum,
		PageSize:   pageSizeNum,
		TotalCount: totalCount,
	}

	response.OKWithPaging(c, respData, paging)
}

// GetOAuthClient gets a specific OAuth client
func GetOAuthClient(c *gin.Context) {
	ctx := c.Request.Context()

	// Get organization ID from path
	orgID, exists := c.Get("org_id")
	if !exists {
		kglog.ErrorWithDataCtx(ctx, "Organization ID not found in context", nil)
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Organization ID not found")
		return
	}

	// Get client ID from path
	clientID := c.Param("client_id")
	if clientID == "" {
		kglog.ErrorWithDataCtx(ctx, "Client ID not provided", nil)
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Client ID is required")
		return
	}

	// Get the OAuth application
	app, kgErr := application.GetOAuthApplication(ctx, clientID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to retrieve OAuth client", map[string]interface{}{
			"client_id": clientID,
			"error":     kgErr.Error.Error(),
		})
		response.KGError(c, kgErr)
		return
	}

	// Verify the application belongs to this organization
	organizationID := orgID.(int)
	appOrgID, kgErr := application.GetApplicationOrgId(ctx, clientID)
	if kgErr != nil || appOrgID != organizationID {
		kglog.ErrorWithDataCtx(ctx, "OAuth client does not belong to this organization", map[string]interface{}{
			"client_id":       clientID,
			"organization_id": organizationID,
		})
		response.NotFoundWithMsg(c, code.RecordNotFound, "OAuth client not found")
		return
	}

	// Convert to response format
	clientType := ""
	if app.Type != nil {
		clientType = app.Type.String()
	}

	squareLogo := ""
	if app.SquareLogo != nil {
		squareLogo = *app.SquareLogo
	}

	respData := OAuthClientResponse{
		ClientID:       app.ClientID,
		ClientName:     app.Name,
		ClientDomain:   app.Domain,
		ClientType:     clientType,
		MainLogo:       app.MainLogo,
		SquareLogo:     squareLogo,
		CreatedAt:      app.CreatedAt.Unix(),
		AppStoreLink:   app.AppStoreLink,
		GooglePlayLink: app.GooglePlayLink,
	}

	response.OK(c, respData)
}

// UpdateOAuthClient updates an existing OAuth client
func UpdateOAuthClient(c *gin.Context) {
	ctx := c.Request.Context()
	var req UpdateOAuthClientRequest

	// Get organization ID from path
	orgID, exists := c.Get("org_id")
	if !exists {
		kglog.ErrorWithDataCtx(ctx, "Organization ID not found in context", nil)
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Organization ID not found")
		return
	}

	// Get client ID from path
	clientID := c.Param("client_id")
	if clientID == "" {
		kglog.ErrorWithDataCtx(ctx, "Client ID not provided", nil)
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Client ID is required")
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to bind request", map[string]interface{}{
			"error": err.Error(),
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid request body")
		return
	}

	// Get the existing OAuth application
	existingApp, kgErr := application.GetOAuthApplication(ctx, clientID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to retrieve OAuth client", map[string]interface{}{
			"client_id": clientID,
			"error":     kgErr.Error.Error(),
		})
		response.KGError(c, kgErr)
		return
	}

	// Verify the application belongs to this organization
	organizationID := orgID.(int)
	appOrgID, kgErr := application.GetApplicationOrgId(ctx, clientID)
	if kgErr != nil || appOrgID != organizationID {
		kglog.ErrorWithDataCtx(ctx, "OAuth client does not belong to this organization", map[string]interface{}{
			"client_id":       clientID,
			"organization_id": organizationID,
		})
		response.NotFoundWithMsg(c, code.RecordNotFound, "OAuth client not found")
		return
	}

	// Update the application with new values
	if req.ClientName != "" {
		existingApp.Name = req.ClientName
	}

	// Handle ClientDomain update - domain is now optional
	if req.ClientDomain != "" {
		// Validate and format domain
		formattedDomain, err := validateAndFormatDomain(req.ClientDomain)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "Invalid domain format", map[string]interface{}{
				"client_domain": req.ClientDomain,
				"error":         err.Error(),
			})
			response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
			return
		}

		existingApp.Domain = formattedDomain
	}

	if req.ClientType != "" {
		appType, err := domain.ParseOAuthApplicationType(req.ClientType)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "Invalid client type", map[string]interface{}{
				"client_type": req.ClientType,
			})
			response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid client type. Must be one of: mobile_wallet, complyflow, market")
			return
		}
		existingApp.Type = &appType
	}

	if req.MainLogo != "" {
		existingApp.MainLogo = req.MainLogo
	}

	if req.SquareLogo != "" {
		existingApp.SquareLogo = &req.SquareLogo
	}

	if req.AppStoreLink != "" {
		existingApp.AppStoreLink = req.AppStoreLink
	}

	if req.GooglePlayLink != "" {
		existingApp.GooglePlayLink = req.GooglePlayLink
	}

	kglog.InfoWithDataCtx(ctx, "Updating OAuth client", map[string]interface{}{
		"organization_id": organizationID,
		"client_id":       clientID,
	})

	// Use the application service to update the OAuth application
	if kgErr := application.UpsertOAuthApplication(ctx, organizationID, existingApp); kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to update OAuth client", map[string]interface{}{
			"error": kgErr.Error.Error(),
		})
		response.KGError(c, kgErr)
		return
	}

	kglog.InfoWithDataCtx(ctx, "Successfully updated OAuth client", map[string]interface{}{
		"organization_id": organizationID,
		"client_id":       clientID,
	})

	// Convert to response format
	clientType := ""
	if existingApp.Type != nil {
		clientType = existingApp.Type.String()
	}

	squareLogo := ""
	if existingApp.SquareLogo != nil {
		squareLogo = *existingApp.SquareLogo
	}

	respData := OAuthClientResponse{
		ClientID:       existingApp.ClientID,
		ClientName:     existingApp.Name,
		ClientDomain:   existingApp.Domain,
		ClientType:     clientType,
		MainLogo:       existingApp.MainLogo,
		SquareLogo:     squareLogo,
		CreatedAt:      existingApp.CreatedAt.Unix(),
		AppStoreLink:   existingApp.AppStoreLink,
		GooglePlayLink: existingApp.GooglePlayLink,
	}

	response.OK(c, respData)
}

// CreateOAuthClient creates a new OAuth client for the organization
func CreateOAuthClient(c *gin.Context) {
	ctx := c.Request.Context()
	var req CreateOAuthClientRequest

	// Get organization ID from path
	orgID, exists := c.Get("org_id")
	if !exists {
		kglog.ErrorWithDataCtx(ctx, "Organization ID not found in context", nil)
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Organization ID not found")
		return
	}

	// Get user UID
	userUID := c.GetString("uid")
	if userUID == "" {
		kglog.ErrorWithDataCtx(ctx, "User UID not found in context", nil)
		response.UnauthorizedWithMsg(c, code.IDTokenNotValid, "User not authenticated")
		return
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to bind request", map[string]interface{}{
			"error": err.Error(),
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid request body")
		return
	}

	formattedDomain, err := validateAndFormatDomain(req.ClientDomain)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Invalid domain format", map[string]interface{}{
			"client_domain": req.ClientDomain,
			"error":         err.Error(),
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
		return
	}

	if req.ClientType == "" {
		req.ClientType = string(domain.OAuthApplicationTypeWebApp)
	}

	// Validate client type using ParseOAuthApplicationType
	appType, err := domain.ParseOAuthApplicationType(req.ClientType)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Invalid client type", map[string]interface{}{
			"client_type": req.ClientType,
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid client type. Must be one of: mobile_wallet, complyflow, market")
		return
	}

	// Generate client ID based on client name
	clientID := generateClientID(req.ClientName)

	// Generate client secret
	clientSecret, err := generateRandomString(32)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to generate client secret", map[string]interface{}{
			"error": err.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, "Failed to generate client secret")
		return
	}

	organizationID := orgID.(int)

	// Create OAuth application
	squareLogo := defaultOauthClientSquareLogo
	app := &domain.OAuthApplication{
		Application: domain.Application{
			ClientID:     clientID,
			ClientSecret: clientSecret,
			Name:         req.ClientName,
			Domain:       formattedDomain,
		},
		IsPrivileged:   true,
		SupportAddress: "", // Will be filled by the service
		MainLogo:       defaultOauthClientMainLogo,
		SquareLogo:     &squareLogo,
		Type:           &appType,
		AppStoreLink:   "",
		GooglePlayLink: "",
		LoginMethods:   domain.LoginMethodList{domain.LoginMethodEmail, domain.LoginMethodPhone, domain.LoginMethodGoogle},
	}

	kglog.InfoWithDataCtx(ctx, "Creating OAuth client", map[string]interface{}{
		"organization_id": organizationID,
		"client_name":     req.ClientName,
		"client_domain":   formattedDomain,
		"client_type":     req.ClientType,
	})

	// Use the application service to create the OAuth application
	if kgErr := application.UpsertOAuthApplication(ctx, organizationID, app); kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to create OAuth client", map[string]interface{}{
			"error": kgErr.Error.Error(),
		})
		response.KGError(c, kgErr)
		return
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created OAuth client", map[string]interface{}{
		"organization_id": organizationID,
		"client_id":       clientID,
	})

	respData := CreateOAuthClientResponse{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		ClientName:   req.ClientName,
		ClientDomain: formattedDomain,
	}

	response.OK(c, respData)
}

// Helper functions

// validateAndFormatDomain validates that the domain has the correct format and returns the formatted domain
// Returns the formatted domain and an error if validation fails
func validateAndFormatDomain(domain string) (string, error) {
	// Handle empty domain case
	if domain == "" {
		return "", nil
	}

	// Check if domain starts with http:// or https://
	if !strings.HasPrefix(domain, "http://") && !strings.HasPrefix(domain, "https://") {
		return "", fmt.Errorf("domain must begin with http:// or https://")
	}

	domain = strings.TrimSuffix(domain, "/")

	return domain, nil
}

// generateClientID creates a client ID from the client name
func generateClientID(clientName string) string {
	// Clean up the client name to make it suitable for an ID
	id := strings.ToLower(clientName)

	// Filter out non-ASCII characters and replace with empty string
	var result strings.Builder
	for _, r := range id {
		// Keep only alphanumeric characters and some safe special characters
		if (r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '-' || r == '_' {
			result.WriteRune(r)
		} else if r == ' ' {
			// Replace spaces with hyphens
			result.WriteRune('-')
		}
		// Skip all other characters
	}

	// Ensure we have at least some alphanumeric content
	sanitizedID := result.String()
	if sanitizedID == "" {
		sanitizedID = "client"
	}

	// Ensure no consecutive hyphens
	sanitizedID = strings.ReplaceAll(sanitizedID, "--", "-")

	// Trim hyphens from start and end
	sanitizedID = strings.Trim(sanitizedID, "-")

	// Add a random suffix to ensure uniqueness
	randomSuffix, _ := generateRandomString(6)
	return sanitizedID + "-" + randomSuffix
}

// generateRandomString creates a random string of the specified length
func generateRandomString(length int) (string, error) {
	bytes := make([]byte, length/2)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
