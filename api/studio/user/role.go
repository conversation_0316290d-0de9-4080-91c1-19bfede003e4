package user

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
)

type getAllRolesData struct {
	User360       []rbac.Role `json:"user_360,omitempty"`
	WalletBuilder []rbac.Role `json:"wallet_builder,omitempty"`
	AssetPro      []rbac.Role `json:"asset_pro,omitempty"`
	NFTBoost      []rbac.Role `json:"nft_boost,omitempty"`
	Compliance    []rbac.Role `json:"compliance,omitempty"`
	Admin         []rbac.Role `json:"admin"`
}

type roles struct {
	User360       []string `json:"user_360,omitempty"`
	WalletBuilder []string `json:"wallet_builder,omitempty"`
	AssetPro      []string `json:"asset_pro,omitempty"`
	NFTBoost      []string `json:"nft_boost,omitempty"`
	Compliance    []string `json:"compliance,omitempty"`
	Admin         []string `json:"admin,omitempty"`
}

func (o *roles) fromDomainRoles(domainRoles []domain.StudioRole) {
	for _, domainRole := range domainRoles {
		r := domainRole.String()
		switch domainRole.Module {
		case "user_360":
			o.User360 = append(o.User360, r)
		case "wallet_builder":
			o.WalletBuilder = append(o.WalletBuilder, r)
		case "asset_pro":
			o.AssetPro = append(o.AssetPro, r)
		case "nft_boost":
			o.NFTBoost = append(o.NFTBoost, r)
		case "compliance":
			o.Compliance = append(o.Compliance, r)
		case "":
			o.Admin = append(o.Admin, r)
		default:
			kglog.ErrorWithData("[convertDomainRoles] unknown module", map[string]interface{}{
				"module": domainRole.Module,
			})
		}

	}
}

// GetAllRoles returns all roles.
func GetAllRoles(c *gin.Context) {
	data, kgErr := organization.GetOrgEnabledModules(c, c.GetInt("org_id"))
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	roles := getAllRolesData{
		Admin: []rbac.Role{rbac.RoleOwner},
	}

	if len(data.User360) > 0 {
		roles.User360 = []rbac.Role{rbac.RoleUser360Admin}
	}
	if len(data.WalletBuilder) > 0 {
		roles.WalletBuilder = []rbac.Role{rbac.RoleWalletBuilderAdmin}
	}
	if len(data.AssetPro) > 0 {
		roles.AssetPro = []rbac.Role{
			rbac.RoleAssetProAdmin,
			rbac.RoleAssetProApprover,
			rbac.RoleAssetProTrader,
			rbac.RoleAssetProFinanceManager,
		}
	}
	if len(data.NFTBoost) > 0 {
		roles.NFTBoost = []rbac.Role{rbac.RoleNFTBoostAdmin}
	}
	if len(data.Compliance) > 0 {
		roles.Compliance = []rbac.Role{
			rbac.RoleComplianceAdmin,
			rbac.RoleComplianceReviewer,
		}
	}

	response.OK(c, roles)
}

// RefreshRBACCache forcefully refreshes all RBAC cache.
func RefreshRBACCache(c *gin.Context) {
	kgError := organization.CacheRoleBindings(c.Request.Context())
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	response.OK(c, nil)
}
