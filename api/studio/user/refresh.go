package user

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type refreshTokenReq struct {
	RefreshToken string `json:"refresh_token"`
}

type refreshTokenResp struct {
	Code int              `json:"code"`
	Data refreshTokenData `json:"data"`
}

type refreshTokenData struct {
	AccessToken string `json:"access_token"`
}

// RefreshToken .
func RefreshToken(ctx *gin.Context) {
	params := &refreshTokenReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// verify refresh token & validate session
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeStudioV2)
	claimsI, errCode, err := strategy.Parse(params.RefreshToken)
	if err != nil {
		if errors.Is(err, code.ErrJwtExpired) {
			// we're passing the expired token here
		} else {
			response.UnauthorizedWithMsg(ctx, errCode, err.Error())
			return
		}
	}
	claims := claimsI.(*jwtservice.StudioAccessTokenClaimsV2)

	// get user
	uid := claims.Subject
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", false, nil)
	if user == nil {
		response.ForbiddenErrorWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	// create access token
	token, _, err := strategy.CreateToken(uid)
	if err != nil {
		kglog.ErrorfCtx(ctx.Request.Context(), "create token error: %v", err)
		response.InternalServerErrorWithMsg(ctx, code.InternalError, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, refreshTokenResp{
		Code: 0,
		Data: refreshTokenData{
			AccessToken: token,
		},
	})
}
