package user

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/eth"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"gorm.io/gorm"
)

// Account get account info
func Account(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")

	orgInfo, kgErr := organization.GetOrgInfo(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	wallets, kgErr := assetpro.Account(orgInfo.Name)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": wallets,
	})
}

type studioLoginReq struct {
	UserAddress string `json:"user_address" binding:"required"`
	Signature   string `json:"signature" binding:"required"`
	Timestamp   string `json:"timestamp" binding:"required"`
}

type studioLoginResp struct {
	Code int                 `json:"code"`
	Data studioLoginRespData `json:"data"`
}

type studioLoginRespData struct {
	AccessToken string `json:"access_token"`
	Role        string `json:"role"`
}

// Login login with signature
func Login(ctx *gin.Context) {
	req := studioLoginReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// check if signature expired
	timestamp, err := strconv.Atoi(req.Timestamp)
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "message should be timestamp")
		return
	}
	timeDiff := time.Now().Unix() - int64(timestamp)
	if timeDiff > 600 || timeDiff < -600 {
		response.BadRequestWithMsg(ctx, code.StudioSignatureExpired, "signature expired")
		return
	}

	// verify signature
	msg := strings.ToLower(req.UserAddress) + ":" + req.Timestamp
	valid := eth.VerifySignature(req.UserAddress, req.Signature, msg)
	if !valid {
		response.BadRequestWithMsg(ctx, code.DashboardSignatureVerifyFailed, "signature verify failed")
		return
	}

	// get studio member
	member, err := rdb.GetStudioMemberByAddress(ctx.Request.Context(), req.UserAddress)
	if err == gorm.ErrRecordNotFound {
		response.ForbiddenErrorWithMsg(ctx, code.NotAllowed, "not allowed")
		return
	} else if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	// create access token
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeStudio)
	accessToken, _, err := strategy.CreateToken(member)
	if err != nil {
		kglog.WarningWithData("create access token failed", map[string]interface{}{
			"client_id": member.ClientID,
			"member_id": member.MemberID,
			"err":       err.Error(),
		})
		response.InternalServerErrorWithMsg(ctx, code.InternalError, err.Error())
		return
	}

	resp := studioLoginResp{
		Code: 0,
		Data: studioLoginRespData{
			AccessToken: accessToken,
			Role:        string(member.Role),
		},
	}
	ctx.JSON(http.StatusOK, resp)
}

type studioLoginRespDataV2 struct {
	StudioToken string `json:"studio_token"`
}

// LoginV2 login with signature
func LoginV2(c *gin.Context) {
	uid := c.GetString("uid")
	if uid == "" {
		response.NotFoundWithMsg(c, code.ParamIncorrect, "uid does not exist")
		return
	}

	organizations, err := rdb.GormRepo().GetOrganizationsByActiveUser(c.Request.Context(), uid)
	if err != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "GetOrganizationsByActiveUser", map[string]interface{}{
			"uid": uid,
			"err": err.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.DBError, err.Error())
		return
	} else if len(organizations) == 0 {
		response.NotFoundWithMsg(c, code.StudioUserNotFound, "studio user not found")
		return
	}

	// create access token
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeStudioV2)
	accessToken, _, err := strategy.CreateToken(uid)
	if err != nil {
		kglog.WarningWithDataCtx(c.Request.Context(), "create access token failed", map[string]interface{}{
			"uid": uid,
			"err": err.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		return
	}

	response.OK(c, studioLoginRespDataV2{
		StudioToken: accessToken,
	})
}
