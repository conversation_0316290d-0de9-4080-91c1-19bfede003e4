package user

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestStudioLogin(t *testing.T) {
	// create a wallet address & private key
	privateKey := "ac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80"
	address := "******************************************"
	timestamp := strconv.Itoa(int(time.Now().Unix()))
	signedMessage := address + ":" + timestamp

	// create db stage member
	rdb.Reset()
	_, err := rdbtest.CreateStudioAdmin(rdb.Get(), address, "")
	assert.Nil(t, err)

	// sign message
	signature, err := util.EthereumPersonalSign([]byte(signedMessage), privateKey)
	assert.Nil(t, err)
	assert.NotEmpty(t, signature)

	url := "/v1/assetpro/login"
	server := gin.Default()
	server.POST(url, Login)

	// request
	body := map[string]interface{}{
		"user_address": address,
		"signature":    signature,
		"timestamp":    timestamp,
	}

	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var response studioLoginResp
	responseStr, _ := io.ReadAll(w.Body)
	fmt.Println(string(responseStr))

	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, "admin", response.Data.Role)
	assert.NotEmpty(t, response.Data.AccessToken)
}

func TestStudioLoginV2(t *testing.T) {
	s := assert.New(t)
	uid := "testuser1"

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioUsers(rdb.Get(), uid, nil))

	url := "/v1/studio/login_v2"
	server := gin.Default()
	server.POST(url, auth.MockAuthorize(uid), LoginV2)

	// request
	req, err := http.NewRequest("POST", url, nil)
	s.NoError(err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var response struct {
		Code int `json:"code"`
		Data struct {
			studioLoginRespDataV2
		} `json:"data"`
	}

	responseStr, _ := io.ReadAll(w.Body)
	fmt.Println(string(responseStr))

	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	studioToken := response.Data.studioLoginRespDataV2.StudioToken

	// verify token
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeStudioV2)
	claimsI, _, err := strategy.Parse(studioToken)
	s.NoError(err)
	claims := claimsI.(*jwtservice.StudioAccessTokenClaimsV2)
	s.Equal(uid, claims.Subject)
}

func TestAccount(t *testing.T) {
	s := assert.New(t)
	orgID := 1

	rdb.Reset()
	setupStudio(t)

	url := "/v1/studio/organization/:orgID/account"
	server := gin.Default()
	server.GET(url, auth.MockOrgID(orgID), Account)

	// request
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/account", orgID), nil)
	s.NoError(err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var response struct {
		Code int `json:"code"`
		Data []struct {
			ChainID string `json:"chain_id"`
			Address string `json:"address"`
		} `json:"data"`
	}

	responseStr, _ := io.ReadAll(w.Body)
	fmt.Println(string(responseStr))

	s.Nil(json.Unmarshal(responseStr, &response))
	s.Equal(0, response.Code)
	s.Equal(4, len(response.Data))
	for _, d := range response.Data {
		s.Contains([]string{"eth", "matic", "bsc", "tron"}, d.ChainID)
		s.Contains([]string{"******************************************", "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"}, d.Address)
	}

}

func setupStudio(t *testing.T) {
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))

	organization.Init(organization.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})
}
