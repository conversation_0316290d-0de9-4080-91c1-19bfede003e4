package user

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/service/studio"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// Test response types
type createAPIKeyResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message,omitempty"`
	Data    struct {
		APIKey string `json:"api_key"`
	} `json:"data"`
}

type listAPIKeysResponse struct {
	Code    int              `json:"code"`
	Message string           `json:"message,omitempty"`
	Data    []APIKeyResponse `json:"data"`
}

type deleteAPIKeyResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data"`
}

func setupAPIKeyTest(t *testing.T) (*gin.Engine, *domain.MockStudioUserAPIKeyRepo) {
	// Initialize Gin in test mode
	gin.SetMode(gin.TestMode)
	r := gin.Default()

	// Create a mock controller
	ctrl := gomock.NewController(t)

	// Create mock repos
	mockAPIKeyRepo := domain.NewMockStudioUserAPIKeyRepo(ctrl)
	mockStudioOrgRepo := domain.NewMockStudioOrgRepo(ctrl)

	// Create a struct that satisfies the studio.IRepo interface by embedding the mocks
	type mockStudioFullRepo struct {
		*domain.MockStudioUserAPIKeyRepo
		*domain.MockStudioOrgRepo
	}

	fullRepo := &mockStudioFullRepo{
		MockStudioUserAPIKeyRepo: mockAPIKeyRepo,
		MockStudioOrgRepo:        mockStudioOrgRepo,
	}

	// Initialize studio service with the combined mock repo struct
	studio.Init(fullRepo)

	return r, mockAPIKeyRepo // Return the specific mock needed for expectations
}

func TestCreateAPIKey(t *testing.T) {
	r, mockRepo := setupAPIKeyTest(t)

	// Set up test data
	uid := "test-user-123"
	orgID := 1
	keyName := "Test API Key"
	description := "This is a test API key"

	// Set up test route with the actual handler
	r.POST("/api_keys", auth.MockAuthorize(uid), auth.MockOrgID(orgID), CreateAPIKey)

	// Set up mock expectations for the repository call
	mockRepo.EXPECT().
		CreateStudioUserAPIKey(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx interface{}, apiKey *domain.StudioUserAPIKey) *code.KGError {
			// Verify the API key parameters
			assert.Equal(t, uid, apiKey.UID)
			assert.Equal(t, orgID, apiKey.OrgID)
			assert.Equal(t, keyName, apiKey.Name)
			assert.Equal(t, description, *apiKey.Description)

			// Set ID as if it was created in DB
			apiKey.ID = 123

			// We'll add a prefix which will be used by the service to generate the full key
			apiKey.KeyPrefix = "testapiprefix"

			return nil
		})

	// Create request body
	body := map[string]interface{}{
		"name":        keyName,
		"description": description,
	}
	bodyBytes, _ := json.Marshal(body)

	// Create request
	req, _ := http.NewRequest("POST", "/api_keys", bytes.NewBuffer(bodyBytes))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	// Execute request
	r.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response createAPIKeyResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Verify response
	assert.Equal(t, 0, response.Code)
	// We can't know the exact API key since it's generated by the service,
	// but we can check that it has a value
	assert.NotEmpty(t, response.Data.APIKey)
}

func TestListAPIKeys(t *testing.T) {
	r, mockRepo := setupAPIKeyTest(t)

	// Set up test data
	uid := "test-user-123"
	orgID := 1
	now := time.Now().Unix()
	description1 := "Description 1"
	description2 := "Description 2"
	lastUsed := now - 3600 // 1 hour ago

	mockAPIKeys := []*domain.StudioUserAPIKey{
		{
			ID:          123,
			OrgID:       orgID,
			UID:         uid,
			Name:        "Test API Key 1",
			KeyPrefix:   "prefix1",
			KeyHash:     "hash1",
			Description: &description1,
			LastUsedAt:  &lastUsed,
			CreatedAt:   now - 86400, // 1 day ago
		},
		{
			ID:          124,
			OrgID:       orgID,
			UID:         uid,
			Name:        "Test API Key 2",
			KeyPrefix:   "prefix2",
			KeyHash:     "hash2",
			Description: &description2,
			CreatedAt:   now,
		},
	}

	// Set up test route with the actual handler
	r.GET("/api_keys", auth.MockAuthorize(uid), auth.MockOrgID(orgID), middleware.Pagination(), ListAPIKeys)

	// Set up mock expectations
	mockRepo.EXPECT().
		ListStudioUserAPIKeys(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx interface{}, params domain.GetStudioUserAPIKeysParams) ([]*domain.StudioUserAPIKey, int, *code.KGError) {
			// Verify params
			assert.Equal(t, orgID, params.OrgID)
			assert.Equal(t, uid, params.UID)
			// Return mock data regardless of pagination
			return mockAPIKeys, len(mockAPIKeys), nil
		})

	// Create request
	req, _ := http.NewRequest("GET", "/api_keys", nil)
	w := httptest.NewRecorder()

	// Execute request
	r.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response listAPIKeysResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Verify response
	assert.Equal(t, 0, response.Code)
	assert.Len(t, response.Data, 2)

	// Verify first key
	assert.Equal(t, 123, response.Data[0].ID)
	assert.Equal(t, "Test API Key 1", response.Data[0].Name)
	assert.Equal(t, "prefix1", response.Data[0].KeyPrefix)
	assert.Equal(t, description1, *response.Data[0].Description)
	assert.Equal(t, lastUsed, *response.Data[0].LastUsedAt)
	assert.Equal(t, now-86400, response.Data[0].CreatedAt)

	// Verify second key
	assert.Equal(t, 124, response.Data[1].ID)
	assert.Equal(t, "Test API Key 2", response.Data[1].Name)
	assert.Equal(t, "prefix2", response.Data[1].KeyPrefix)
	assert.Equal(t, description2, *response.Data[1].Description)
	assert.Nil(t, response.Data[1].LastUsedAt)
	assert.Equal(t, now, response.Data[1].CreatedAt)
}

func TestDeleteAPIKey(t *testing.T) {
	r, mockRepo := setupAPIKeyTest(t)

	// Set up test data
	uid := "test-user-123"
	orgID := 1
	apiKeyID := 123

	// Set up test route with the actual handler
	r.DELETE("/api_keys/:id", auth.MockAuthorize(uid), auth.MockOrgID(orgID), DeleteAPIKey)

	// Test 1: Successful deletion
	t.Run("Successful Deletion", func(t *testing.T) {
		// Set up mock expectations
		mockRepo.EXPECT().
			DeleteStudioUserAPIKey(gomock.Any(), orgID, apiKeyID, uid).
			Return(nil)

		// Create request
		req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api_keys/%d", apiKeyID), nil)
		w := httptest.NewRecorder()

		// Execute request
		r.ServeHTTP(w, req)

		// Assertions
		assert.Equal(t, http.StatusOK, w.Code)

		var response deleteAPIKeyResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Equal(t, 0, response.Code)
		assert.Nil(t, response.Data)
	})

	// Test 2: Invalid API key ID format
	t.Run("Invalid API Key ID", func(t *testing.T) {
		// Create request with invalid ID
		req, _ := http.NewRequest("DELETE", "/api_keys/invalid-id", nil)
		w := httptest.NewRecorder()

		// Execute request
		r.ServeHTTP(w, req)

		// Assertions
		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response deleteAPIKeyResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Equal(t, 2004, response.Code)
		assert.Contains(t, response.Message, "Invalid API key ID")
	})

	// Test 3: API key not found
	t.Run("API Key Not Found", func(t *testing.T) {
		// Set up mock expectations for not found error
		notFoundError := code.NewKGError(code.APIKeyNotValid, http.StatusNotFound, fmt.Errorf("API key not found"), nil)

		mockRepo.EXPECT().
			DeleteStudioUserAPIKey(gomock.Any(), orgID, apiKeyID, uid).
			Return(notFoundError)

		// Create request
		req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api_keys/%d", apiKeyID), nil)
		w := httptest.NewRecorder()

		// Execute request
		r.ServeHTTP(w, req)

		// Assertions
		assert.Equal(t, http.StatusNotFound, w.Code)

		var response deleteAPIKeyResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Equal(t, code.APIKeyNotValid, response.Code)
		assert.Contains(t, response.Message, "API key not found")
	})
}
