package user

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/api/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/paging"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	orgService "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
)

type studioUserTransferLimitation struct {
	DailyTransferLimit        float64 `json:"daily_transfer_limit"`
	TransferApprovalThreshold float64 `json:"transfer_approval_threshold"`
}

func (o *studioUserTransferLimitation) fromDomainTransferLimitation(domainLimitation *domain.StudioUserTransferLimitation) {
	dailyTransferLimit, _ := domainLimitation.DailyTransferLimit.Float64()
	o.DailyTransferLimit = dailyTransferLimit
	transferApprovalThreshold, _ := domainLimitation.TransferApprovalThreshold.Float64()
	o.TransferApprovalThreshold = transferApprovalThreshold
}

type permission struct {
	Resource string `json:"resource"`
	Action   string `json:"action"`
}

type userInfoResponse struct {
	jsonUser
	Phone       string                                `json:"phone"`
	Modules     organization.StudioOrganizationModule `json:"modules"`
	Permissions []permission                          `json:"permissions"`
	AssetPro    struct {
		RemainLimit float64 `json:"remain_limit"`
		studioUserTransferLimitation
	} `json:"asset_pro"`
}

// UserInfo returns studio user info. Studio user is a user in an organization.
func UserInfo(c *gin.Context) {
	userID := c.GetString("uid")
	orgID := c.GetInt("org_id")

	firebaseUser, err := firebase.GetUserByUID(c.Request.Context(), userID)
	if err != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "GetUserByUID, ERROR: "+err.Error(), map[string]interface{}{
			"uid": userID,
		})
		response.InternalServerErrorWithMsg(c, code.FirebaseFailed, err.Error())
		return
	}

	studioUser, kgError := orgService.GetUser(c.Request.Context(), orgID, userID)
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	enabledModules, kgError := orgService.GetOrgEnabledModules(c.Request.Context(), orgID)
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}
	enabledModules.FilterByRole(studioUser.Roles)

	remainLimit, kgError := assetpro.GetRemainLimit(c.Request.Context(), orgID, userID)
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	grants, err := rbac.RBACService.GetGrants(c.Request.Context(),
		lo.Map(studioUser.Roles, func(role domain.StudioRole, _ int) rbac.Role {
			return rbac.Role(role.String())
		})...,
	)
	if err != nil {
		response.KGError(c, code.NewKGError(code.InvalidRoles, http.StatusInternalServerError, err, nil))
		return
	}

	var resp userInfoResponse

	resp.jsonUser.fromDomainUser(studioUser)
	resp.Phone = firebaseUser.PhoneNumber
	resp.Permissions = lo.Map(grants, func(grant rbac.Grant, _ int) permission {
		return permission{
			Resource: grant.Resource.String(),
			Action:   grant.Action.String(),
		}
	})
	resp.Modules.FromDomainModule(enabledModules)
	resp.AssetPro.RemainLimit, _ = remainLimit.Float64()
	resp.AssetPro.fromDomainTransferLimitation(&studioUser.TransferLimitation)

	response.OK(c, resp)
}

// InviteStudioUserRequest invite studio user request
type InviteStudioUserRequest struct {
	Roles           []string `json:"roles"`
	InviteeEmail    string   `json:"email" binding:"required"`
	InviteeName     string   `json:"name" binding:"required"`
	InviteeMemberID *string  `json:"member_id"`
}

// InviteStudioUser invite studio user
func InviteStudioUser(ctx *gin.Context) {
	req := InviteStudioUserRequest{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	inviterUID := ctx.GetString("uid")
	orgID := ctx.GetInt("org_id")

	studioRoles, kgError := orgService.ParseStudioRoles(ctx.Request.Context(), req.Roles)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	if len(studioRoles) == 0 {
		response.BadRequestWithMsg(ctx, code.InvalidRoles, "roles is required")
		return
	}

	if !cache.CheckStudioInviteRateLimitByOrgAndUID(ctx.Request.Context(), orgID, inviterUID, req.InviteeEmail) {
		response.TooManyRequestsWithMsg(ctx, code.RateLimit, "3 invites per minute")
		return
	}

	if kgError := orgService.InviteStudioUser(ctx.Request.Context(), orgService.InviteStudioUserParams{
		OrgID:           orgID,
		InviterUID:      inviterUID,
		InviteeEmail:    req.InviteeEmail,
		InviteeName:     req.InviteeName,
		InviteeMemberID: req.InviteeMemberID,
		Roles:           studioRoles,
	}); kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	response.OK(ctx, nil)
}

// UpdateStudioUserRequest update studio user request
type UpdateStudioUserRequest struct {
	Name     *string  `json:"name"`
	MemberID *string  `json:"member_id"`
	Roles    []string `json:"roles"`
}

// UpdateStudioUser update studio user's info.
func UpdateStudioUser(ctx *gin.Context) {
	var req UpdateStudioUserRequest
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")

	// we don't need to check if the user is empty,
	// because the route can't reach here if the user is empty.
	userID := ctx.Param("uid")

	studioRoles, kgError := orgService.ParseStudioRoles(ctx.Request.Context(), req.Roles)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	if len(studioRoles) == 0 {
		response.BadRequestWithMsg(ctx, code.InvalidRoles, "roles is required")
		return
	}

	kgError = orgService.UpdateStudioUser(ctx.Request.Context(), orgService.UpdateStudioUserParams{
		OrgID:    orgID,
		UID:      userID,
		Name:     req.Name,
		MemberID: req.MemberID,
		Roles:    studioRoles,
	})

	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	response.OK(ctx, nil)
}

type jsonUser struct {
	UID      string `json:"uid"`
	Name     string `json:"name"`
	MemberID string `json:"member_id"`
	Email    string `json:"email"`
	Roles    roles  `json:"roles"`
	Status   string `json:"status"`
}

func (o *jsonUser) fromDomainUser(domainUser *domain.StudioUser) {
	o.UID = domainUser.UID
	o.Name = domainUser.Name
	o.MemberID = util.Val(domainUser.MemberID)
	o.Status = domainUser.Status.String()
	o.Email = domainUser.Email
	o.Roles.fromDomainRoles(domainUser.Roles)
	o.Status = domainUser.Status.String()
}

type listUsersReq struct {
	PageNumber int    `form:"page_number"`
	PageSize   int    `form:"page_size"`
	Q          string `form:"q"`
}

// ListUsers returns users of an organization.
func ListUsers(ctx *gin.Context) {
	req := &listUsersReq{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")

	studioUsers, domainPaging, kgErr := orgService.ListUsers(
		ctx.Request.Context(), orgID,
		[]domain.StudioRole{}, &paging.Query{
			Paging: &paging.Paging{
				PageNumber: req.PageNumber,
				PageSize:   req.PageSize,
			},
			FuzzySearch: req.Q,
		},
	)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	users := lo.Map(studioUsers, func(u *domain.StudioUser, _ int) *jsonUser {
		user := &jsonUser{}
		user.fromDomainUser(u)
		return user
	})

	response.OKWithPaging(ctx, users, response.Paging{
		PageNumber: domainPaging.PageNumber,
		PageSize:   domainPaging.PageSize,
		TotalCount: domainPaging.TotalCount,
		PageSort:   string(domainPaging.PageSort),
	})
}

// DisableUser disables a user.
func DisableUser(c *gin.Context) {
	orgID := c.GetInt("org_id")
	userID := c.Param("userID")

	studioUser, kgErr := orgService.DisableUser(c.Request.Context(), orgID, userID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	user := &jsonUser{}
	user.fromDomainUser(studioUser)

	response.OK(c, user)
}

// ReinviteStudioUser re-invite studio user
func ReinviteStudioUser(ctx *gin.Context) {
	params := orgService.ReinviteStudioUserParams{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")
	params.OrgID = orgID
	params.InviterUID = ctx.GetString("uid")

	if !cache.CheckStudioInviteRateLimitByOrgAndUID(ctx.Request.Context(), orgID, params.InviterUID, params.InviteeUID) {
		response.TooManyRequestsWithMsg(ctx, code.RateLimit, "3 invites per minute")
		return
	}

	kgError := orgService.ReinviteStudioUser(ctx.Request.Context(), &params)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	response.OK(ctx, nil)
}

// AcceptInvitation accept invitation
func AcceptInvitation(c *gin.Context) {
	orgID, err := strconv.Atoi(c.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "orgID is invalid")
		return
	}
	uid := auth.GetUID(c)

	resp, kgError := orgService.AcceptInvitation(c.Request.Context(), orgID, uid)
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	response.OK(c, resp)
}

type userExist struct {
	Email string `form:"email" binding:"required"`
}

// Exist check user exist
func Exist(ctx *gin.Context) {
	params := userExist{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")

	resp, kgError := orgService.UserExist(ctx.Request.Context(), orgID, params.Email)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	response.OK(ctx, resp)
}
