package user

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	authapi "github.com/kryptogo/kg-wallet-backend/api/auth"
	authmiddleware "github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/repo"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
)

type testRefreshResponse struct {
	Code int             `json:"code"`
	Data testRefreshData `json:"data"`
}

type testRefreshData struct {
	AccessToken string `json:"access_token"`
}

type testLoginResponse struct {
	Code int           `json:"code"`
	Data testLoginData `json:"data"`
}

type testLoginData struct {
	AccessToken string `json:"access_token"`
	IDToken     string `json:"id_token"`
	KgToken     string `json:"kg_token"`
}

type studioLoginV2Resp struct {
	Code int                   `json:"code"`
	Data studioLoginRespDataV2 `json:"data"`
}

func TestRefreshToken(t *testing.T) {
	rdb.Reset()
	users, uid, phone, _ := dbtest.User()
	user.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioUsers(rdb.Get(), uid, nil))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	assert.Nil(t, rdbtest.CreateSmsLogs(rdb.Get(), phone))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	loginUrl := "/v1/login"
	refreshTokenUrl := "/v1/studio/refresh"
	studioLoginUrl := "/v1/studio/login_v2"
	server := gin.Default()
	server.POST(loginUrl, authapi.Login)
	server.POST(refreshTokenUrl, RefreshToken)
	server.POST(studioLoginUrl, authmiddleware.AuthorizeByKgToken(), LoginV2)

	// login
	addBody := map[string]interface{}{
		"phone_number": phone,
		"sms_code":     "123456",
	}
	jsonStr, err := json.Marshal(addBody)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", loginUrl, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var res testLoginResponse
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &res))
	assert.Equal(t, 0, res.Code)
	assert.NotEmpty(t, res.Data.AccessToken)
	assert.NotEmpty(t, res.Data.IDToken)
	assert.NotEmpty(t, res.Data.KgToken)

	// studio login
	req, err = http.NewRequest("POST", studioLoginUrl, nil)
	req.Header.Set("KG-TOKEN", res.Data.KgToken)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var studioLoginRes studioLoginV2Resp
	responseStr, _ = io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &studioLoginRes))
	assert.Equal(t, 0, studioLoginRes.Code)
	assert.NotEmpty(t, studioLoginRes.Data.StudioToken)

	accessToken := studioLoginRes.Data.StudioToken
	addBody = map[string]interface{}{
		"refresh_token": accessToken,
	}
	jsonStr, err = json.Marshal(addBody)
	assert.Nil(t, err)
	req, err = http.NewRequest("POST", refreshTokenUrl, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	// assert response
	assert.Equal(t, http.StatusOK, w.Code)
	var response testRefreshResponse
	responseStr, _ = io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	fmt.Println(response)
	assert.Equal(t, 0, response.Code)
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeStudioV2)
	_, code, err := strategy.Parse(response.Data.AccessToken)
	assert.Equal(t, 0, code)
	assert.Nil(t, err)
}
