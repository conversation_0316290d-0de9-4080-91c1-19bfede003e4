package user

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/service/studio"
)

// CreateAPIKeyRequest is the request body for creating an API key
type CreateAPIKeyRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description *string `json:"description"`
}

// APIKeyResponse represents a studio API key in responses
type APIKeyResponse struct {
	ID          int     `json:"id"`
	Name        string  `json:"name"`
	KeyPrefix   string  `json:"key_prefix"`
	Description *string `json:"description,omitempty"`
	LastUsedAt  *int64  `json:"last_used_at,omitempty"`
	CreatedAt   int64   `json:"created_at"`
}

// CreateAPIKeyResponse is the response for creating an API key
type CreateAPIKeyResponse struct {
	APIKey string `json:"api_key"`
}

// APIKeyFromDomain converts a domain.StudioUserAPIKey to an APIKeyResponse
func APIKeyFromDomain(apiKey *domain.StudioUserAPIKey) APIKeyResponse {
	response := APIKeyResponse{
		ID:          apiKey.ID,
		Name:        apiKey.Name,
		KeyPrefix:   apiKey.KeyPrefix,
		Description: apiKey.Description,
		CreatedAt:   apiKey.CreatedAt,
		LastUsedAt:  apiKey.LastUsedAt,
	}
	return response
}

// CreateAPIKey creates a new API key for the authenticated studio user
func CreateAPIKey(c *gin.Context) {
	var req CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithMsg(c, 0, err.Error())
		return
	}

	uid := auth.GetUID(c)
	orgID := c.GetInt("org_id")

	apiKey, kgErr := studio.CreateStudioUserAPIKey(c.Request.Context(), &studio.CreateStudioUserAPIKeyParams{
		OrgID:       orgID,
		UID:         uid,
		Name:        req.Name,
		Description: req.Description,
	})

	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, CreateAPIKeyResponse{
		APIKey: apiKey,
	})
}

// ListAPIKeys lists all API keys for the authenticated studio user
func ListAPIKeys(c *gin.Context) {
	uid := auth.GetUID(c)
	orgID := c.GetInt("org_id")

	// Get pagination parameters from context
	pageNum, pageSizeNum, _ := middleware.GetPaginationFromContext(c)

	// Create the params struct
	params := domain.GetStudioUserAPIKeysParams{
		OrgID:    orgID,
		UID:      uid,
		Page:     pageNum,
		PageSize: pageSizeNum,
	}

	// Get API keys with pagination at the database level
	apiKeys, totalCount, kgErr := studio.ListStudioUserAPIKeys(c.Request.Context(), params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Convert to response format
	resp := make([]APIKeyResponse, 0, len(apiKeys))
	for _, apiKey := range apiKeys {
		resp = append(resp, APIKeyFromDomain(apiKey))
	}

	// Create paging info
	paging := response.Paging{
		PageNumber: pageNum,
		PageSize:   pageSizeNum,
		TotalCount: totalCount,
	}

	response.OKWithPaging(c, resp, paging)
}

// DeleteAPIKey deletes an API key for the authenticated studio user
func DeleteAPIKey(c *gin.Context) {
	uid := auth.GetUID(c)
	orgID := c.GetInt("org_id")

	apiKeyIDStr := c.Param("id")
	apiKeyID, err := strconv.Atoi(apiKeyIDStr)
	if err != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "Invalid API key ID", map[string]interface{}{
			"apiKeyID": apiKeyIDStr,
			"error":    err.Error(),
		})
		response.BadRequestWithMsg(c, code.APIKeyNotFound, "Invalid API key ID")
		return
	}

	kgErr := studio.DeleteStudioUserAPIKey(c.Request.Context(), orgID, apiKeyID, uid)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}
