package user

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	"github.com/kryptogo/kg-wallet-backend/service/user"

	"github.com/gin-gonic/gin"
	authapi "github.com/kryptogo/kg-wallet-backend/api/auth"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/studio/organization"
	userapi "github.com/kryptogo/kg-wallet-backend/api/user"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	authservice "github.com/kryptogo/kg-wallet-backend/pkg/service/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	cRepo "github.com/kryptogo/kg-wallet-backend/pkg/service/customer/repo"
	"github.com/kryptogo/kg-wallet-backend/repo"

	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	orgService "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/sendgrid/rest"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// Seed database with necessary data.
func setup(t *testing.T, m sendgrid.EmailClient) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	user.Init(repo.Unified())

	initParam := orgService.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	}

	if m != nil {
		initParam.SendgridClient = m
	}

	orgService.Init(initParam)
}

func TestGetStudioUserInfo(t *testing.T) {
	s := assert.New(t)
	s.NoError(rbac.Init(context.Background()))
	rdb.Reset()
	customer.Init(cRepo.NewCustomerRepo())
	orgService.Init(orgService.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})
	assetpro.InitTransfer(rdb.GormRepo())

	// setup firebase user
	users, uid, phone, email := dbtest.User()

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)

	_, err := firebase.BatchCreateUsersBySeed(users)
	s.NoError(err)

	organizations := []model.StudioOrganization{
		{ID: 1, Name: "org1"},
		{ID: 2, Name: "org2"},
		{ID: 3, Name: "org3"},
	}

	s.NoError(rdb.Get().Create(&organizations).Error)

	modules := []model.StudioOrganizationModule{
		{
			OrganizationID: 1,
			User360:        "data,engage,audience",
			Compliance:     "create_a_task,case_management,all_tasks",
			Admin:          "billing",
		},
		{
			OrganizationID: 2,
			AssetPro:       "send_token,transaction_history",
			Admin:          "billing",
		},
	}

	s.NoError(rdb.Get().Create(&modules).Error)

	orgID := 1

	studioUsers := []model.StudioUser{
		{
			OrganizationID: 1,
			UID:            uid,
			Status:         model.StudioUserStatusActive,
			Email:          util.Ptr(email),
			RoleBinding: []model.StudioRoleBinding{
				{
					RoleID:         1,
					OrganizationID: 1,
					UID:            uid,
					Role: model.StudioRole{
						ID:     1,
						Module: "user_360",
						Name:   "admin",
					},
				},
			},
			StudioUserTransferLimitation: model.StudioUserTransferLimitation{
				DailyTransferLimit:        decimal.NewFromFloat(1234.5),
				TransferApprovalThreshold: decimal.NewFromFloat(123.45),
			},
		},
		{
			OrganizationID: 2,
			UID:            uid,
			Status:         model.StudioUserStatusActive,
		},
	}

	s.NoError(rdb.Get().Create(&studioUsers).Error)

	now := util.NowInCST()
	assert.Nil(t, rdbtest.CreateAssetProTxLogs(rdb.Get(), uid))
	cache.Del(cache.ComposeDailyUsedLimitCacheKey(1, uid, now))

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/me", func(c *gin.Context) {
		if c.GetHeader("Authorization") != "Bearer uid" {
			c.AbortWithStatus(http.StatusUnauthorized)
			return
		}

		c.Set("uid", uid)
	}, auth.MockOrgID(orgID), UserInfo)

	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/v1/studio/organization/1/me", nil)

		r.ServeHTTP(w, req)

		s.Equal(http.StatusUnauthorized, w.Code)
	}
	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/v1/studio/organization/1/me", nil)
		req.Header.Set("Authorization", "Bearer uid unknown")

		r.ServeHTTP(w, req)

		s.Equal(http.StatusUnauthorized, w.Code)
	}
	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/v1/studio/organization/1/me", nil)
		req.Header.Set("Authorization", "Bearer uid")

		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)

		type resp struct {
			Code int `json:"code"`
			Data struct {
				jsonUser
				Phone       string                                `json:"phone"`
				Modules     organization.StudioOrganizationModule `json:"modules"`
				Permissions []permission                          `json:"permissions"`
				AssetPro    struct {
					RemainLimit float64 `json:"remain_limit"`
					studioUserTransferLimitation
				} `json:"asset_pro"`
			} `json:"data"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		// user info
		s.Equal(phone, response.Data.Phone)
		s.Equal(email, response.Data.Email)
		// roles
		s.Len(response.Data.Roles.User360, 1)
		s.Len(response.Data.Roles.Admin, 0)
		s.Equal("user_360:admin", response.Data.Roles.User360[0])
		// permissions
		s.Len(response.Data.Permissions, 3)
		s.Contains(response.Data.Permissions, permission{Resource: "user_360_statistics_compliance", Action: "read"})
		// modules
		s.Len(response.Data.Modules.User360, 3)
		s.Len(response.Data.Modules.Admin, 0)
		s.Len(response.Data.Modules.Compliance, 0)
		s.Len(response.Data.Modules.WalletBuilder, 0)
		s.Len(response.Data.Modules.NFTBoost, 0)
		s.Len(response.Data.Modules.AssetPro, 0)
		// asset pro
		s.Equal(float64(1134.5), response.Data.AssetPro.RemainLimit)
		s.Equal(float64(1234.5), response.Data.AssetPro.DailyTransferLimit)
		s.Equal(float64(123.45), response.Data.AssetPro.TransferApprovalThreshold)
		s.Equal(model.StudioUserStatusActive.String(), response.Data.Status)
	}
}

func TestInviteStudioUser(t *testing.T) {
	s := assert.New(t)

	ctx := context.Background()
	// inject mock
	ctrl := gomock.NewController(t)
	m := sendgrid.NewMockEmailClient(ctrl)
	m.EXPECT().SendEmailWithSubject(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(3).Return(&rest.Response{StatusCode: 200}, nil)

	setup(t, m)

	user.Init(repo.Unified())
	application.Init(rdb.GormRepo())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	orgService.Init(orgService.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
		SendgridClient:      m,
	})

	var adminUID string
	// preparing admin user
	{
		users, userIDs := dbtest.Users()

		_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

		_, err := firebase.BatchCreateUsersBySeed(users)
		assert.Nil(t, err)

		adminUID = userIDs[0]
		assert.Nil(t, rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:         adminUID,
				DisplayName: util.RandString(10),
			},
		}))

		s.NoError(rdb.Get().Create(&[]model.StudioUser{
			{
				OrganizationID: 1,
				UID:            adminUID,
				Status:         model.StudioUserStatusActive,
			},
		}).Error)
	}

	// new user
	users, userIDs := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	newUserUID := userIDs[0]
	newUserData := users[newUserUID]
	newUserEmail := newUserData.Email
	newUserName := util.RandString(10)
	err = rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         newUserUID,
			DisplayName: newUserName,
		},
	})
	assert.Nil(t, err)

	r := gin.Default()
	r.POST("/v1/studio/organization/:orgID/users", auth.MockAuthorize(adminUID), auth.MockOrgID(1), InviteStudioUser)

	{ // case failed: without role filed
		w := httptest.NewRecorder()
		addBody := map[string]interface{}{
			"email": newUserEmail,
			"name":  newUserName,
			// "roles": []string{},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)
		req, _ := http.NewRequest(http.MethodPost, "/v1/studio/organization/1/users", bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)

		s.Equal(http.StatusBadRequest, w.Code)

		type resp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7020, response.Code)
		s.Equal("roles is required", response.Message)
	}

	{ // case failed: empty roles
		w := httptest.NewRecorder()
		addBody := map[string]interface{}{
			"email": newUserEmail,
			"name":  newUserName,
			"roles": []string{},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)
		req, _ := http.NewRequest(http.MethodPost, "/v1/studio/organization/1/users", bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)

		s.Equal(http.StatusBadRequest, w.Code)

		type resp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7020, response.Code)
		s.Equal("roles is required", response.Message)
	}

	for i := 0; i < 3; i++ {
		w := httptest.NewRecorder()
		addBody := map[string]interface{}{
			"email": newUserEmail,
			"name":  newUserName,
			"roles": []string{"user_360:admin", "asset_pro:trader", "compliance:admin"},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)
		req, _ := http.NewRequest(http.MethodPost, "/v1/studio/organization/1/users", bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code, fmt.Sprintf("case %d", i))

		type resp struct {
			Code int `json:"code"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		// check db, the invited user should be pending
		ctx := context.Background()
		orgID := 1
		user, kgError := rdb.GormRepo().GetStudioUser(ctx, orgID, newUserUID)
		s.Nil(kgError)
		s.Equal(newUserUID, user.UID)
		s.Equal(domain.StudioUserStatusPending, user.Status)
		s.Equal(newUserName, user.Name)
		s.Equal(*newUserEmail, user.Email)
		s.Contains(user.Roles, domain.StudioRole{Module: "user_360", Name: "admin"})
		s.Contains(user.Roles, domain.StudioRole{Module: "asset_pro", Name: "trader"})
		s.Contains(user.Roles, domain.StudioRole{Module: "compliance", Name: "admin"})
	}

	// invite again immediately
	{
		w := httptest.NewRecorder()
		addBody := map[string]interface{}{
			"email": newUserEmail,
			"name":  newUserName,
			"roles": []string{"user_360:admin", "asset_pro:trader", "compliance:admin"},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)
		req, _ := http.NewRequest(http.MethodPost, "/v1/studio/organization/1/users", bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)

		s.Equal(http.StatusTooManyRequests, w.Code)

		type resp struct {
			Code int `json:"code"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(1019, response.Code)
	}
}

func TestInviteStudioUserAfterDeleted(t *testing.T) {
	s := assert.New(t)

	ctx := context.Background()
	// inject mock
	ctrl := gomock.NewController(t)
	m := sendgrid.NewMockEmailClient(ctrl)
	m.EXPECT().SendEmailWithSubject(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).Return(&rest.Response{StatusCode: 200}, nil)

	setup(t, m)
	user.Init(repo.Unified())
	application.Init(rdb.GormRepo())

	var adminUID string
	// create admin
	{
		users, userIDs := dbtest.Users()

		asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
		_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
		_, err := firebase.BatchCreateUsersBySeed(users)
		assert.Nil(t, err)
		adminUID = userIDs[0]
		err = rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:         adminUID,
				DisplayName: util.RandString(10),
			},
		})
		assert.Nil(t, err)

		studioUsers := []model.StudioUser{
			{
				OrganizationID: 1,
				UID:            adminUID,
				Status:         model.StudioUserStatusActive,
			},
		}

		s.NoError(rdb.Get().Create(&studioUsers).Error)
	}

	// new user
	users, userIDs := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)
	newUserUID := userIDs[0]
	newUserData := users[newUserUID]
	newUserEmail := newUserData.Email
	newUserName := util.RandString(10)
	err = rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         newUserUID,
			DisplayName: newUserName,
		},
	})
	assert.Nil(t, err)

	r := gin.Default()
	r.POST("/v1/studio/organization/:orgID/users", auth.MockAuthorize(adminUID), auth.MockOrgID(1), InviteStudioUser)

	// create user
	s.NoError(rdb.Get().Create(&model.StudioUser{
		OrganizationID: 1,
		UID:            newUserUID,
		Name:           newUserName,
		Status:         model.StudioUserStatusPending,
	}).Error)
	// delete user
	s.NoError(rdb.Get().Where("organization_id = ? AND uid = ?", 1, newUserUID).Delete(&model.StudioUser{}).Error)

	// invite again after delete
	{
		w := httptest.NewRecorder()
		addBody := map[string]interface{}{
			"email": newUserEmail,
			"name":  newUserName,
			"roles": []string{"user_360:admin", "asset_pro:trader", "compliance:admin"},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)
		req, _ := http.NewRequest(http.MethodPost, "/v1/studio/organization/1/users", bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)

		type resp struct {
			Code int `json:"code"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		// check db, the invited user should be pending
		ctx := context.Background()
		orgID := 1
		user, kgError := rdb.GormRepo().GetStudioUser(ctx, orgID, newUserUID)
		s.Nil(kgError)
		s.Equal(newUserUID, user.UID)
		s.Equal(domain.StudioUserStatusPending, user.Status)
		s.Equal(newUserName, user.Name)
		s.Equal(*newUserEmail, user.Email)
		s.Contains(user.Roles, domain.StudioRole{Module: "user_360", Name: "admin"})
		s.Contains(user.Roles, domain.StudioRole{Module: "asset_pro", Name: "trader"})
		s.Contains(user.Roles, domain.StudioRole{Module: "compliance", Name: "admin"})
	}
}

func TestListUsers(t *testing.T) {
	s := assert.New(t)
	setup(t, nil)
	orgService.Init(orgService.InitParam{
		StudioOrgRepo:  rdb.GormRepo(),
		StudioRoleRepo: rdb.GormRepo(),
	})

	now := time.Now()
	studioUsers := []model.StudioUser{
		{
			OrganizationID: 100,
			UID:            "uid1",
			Name:           "abc",
			MemberID:       util.Ptr("cde"),
			Email:          util.Ptr("email1"),
			Status:         model.StudioUserStatusActive,
			CreatedAt:      now,
		},
		{
			OrganizationID: 100,
			UID:            "uid2",
			Name:           "def",
			Email:          util.Ptr("email2"),
			Status:         model.StudioUserStatusPending,
			CreatedAt:      now.Add(time.Second),
		},
	}
	s.NoError(rdb.Get().Create(&model.StudioOrganization{
		ID:   100,
		Name: "test org",
	}).Error)

	s.NoError(rdb.Get().Create(&studioUsers).Error)

	type roles struct {
		User360       []string `json:"user_360"`
		WalletBuilder []string `json:"wallet_builder"`
		AssetPro      []string `json:"asset_pro"`
		NFTBoost      []string `json:"nft_boost"`
		Compliance    []string `json:"compliance"`
		Admin         []string `json:"admin"`
	}

	type user struct {
		UID      string `json:"uid"`
		Name     string `json:"name"`
		MemberID string `json:"member_id"`
		Email    string `json:"email"`
		Roles    roles  `json:"roles"`
		Status   string `json:"status"`
	}

	type paging struct {
		PageNumber int    `json:"page_number"`
		PageSize   int    `json:"page_size"`
		TotalCount int    `json:"total_count"`
		PageSort   string `json:"page_sort"`
	}

	type resp struct {
		Code int `json:"code"`
		Data []struct {
			user
		} `json:"data"`
		Paging *paging `json:"paging"`
	}

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/users", auth.MockOrgID(100), ListUsers)
	{
		// all users in org 100
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/v1/studio/organization/100/users", nil)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal(2, len(response.Data))
		s.Equal(1, response.Paging.PageNumber)
		s.Equal(10, response.Paging.PageSize)
		s.Equal(2, response.Paging.TotalCount)
		for idx, u := range response.Data {
			i := len(studioUsers) - idx - 1 // reverse order
			s.Equal(util.Val(studioUsers[i].MemberID), u.MemberID)
			s.Equal(studioUsers[i].Name, u.Name)
			s.Equal(util.Val(studioUsers[i].Email), u.Email)
			s.Equal(studioUsers[i].UID, u.UID)
			s.Equal(string(studioUsers[i].Status), u.Status)
		}
	}
	{
		// filter users by q=c, name, member_id, and email either one should contain c
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/v1/studio/organization/100/users?q=c", nil)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal(1, len(response.Data))
		s.Equal(1, response.Paging.PageNumber)
		s.Equal(10, response.Paging.PageSize)
		s.Equal(1, response.Paging.TotalCount)
		s.Equal(util.Val(studioUsers[0].MemberID), response.Data[0].MemberID)
		s.Equal(studioUsers[0].Name, response.Data[0].Name)
		s.Equal(util.Val(studioUsers[0].Email), response.Data[0].Email)
		s.Equal(studioUsers[0].UID, response.Data[0].UID)
		s.Equal(string(studioUsers[0].Status), response.Data[0].Status)
	}
}

func TestDeleteUser(t *testing.T) {
	s := assert.New(t)
	setup(t, nil)

	orgID := 10 + rand.Intn(100)
	userID := util.RandString(10)
	adminUID := util.RandString(10)

	createOrgAndAdmin(s, orgID, adminUID)

	r := gin.Default()
	r.DELETE("/v1/studio/organization/:orgID/users/:userID", auth.MockOrgID(orgID), DisableUser)

	// not found
	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodDelete, fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, userID), nil)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		type deleteUserResp struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			MemberID string `json:"member_id"`
		}

		type resp struct {
			Code int `json:"code"`
			Data struct {
				deleteUserResp
			} `json:"data"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7014, response.Code)
	}
	// success and return deleted user
	{
		// create user
		user := model.StudioUser{
			OrganizationID: orgID,
			UID:            userID,
			Name:           util.RandString(10),
			Email:          util.Ptr(util.RandEmail()),
			Status:         model.StudioUserStatusActive,
		}
		s.NoError(rdb.Get().Create(&user).Error)

		roleBindings := []model.StudioRoleBinding{
			{
				OrganizationID: orgID,
				UID:            userID,
				RoleID:         2, // {Module: "user_360", Name: "admin"}
			},
			{
				OrganizationID: orgID,
				UID:            userID,
				RoleID:         3, // {Module: "wallet_builder", Name: "admin"}
			},
		}
		s.NoError(rdb.Get().Create(&roleBindings).Error)

		// assert role has been cached
		s.Nil(orgService.CacheUserRoleBindings(context.Background(), orgID, userID))

		rolesInCache, kgError := orgService.GetStudioRoleCacheRepo().GetRolesByUser(context.Background(), orgID, userID)
		s.Nil(kgError)
		s.Len(rolesInCache, 2)

		s.Contains(rolesInCache, domain.StudioRole{Module: "user_360", Name: "admin"})
		s.Contains(rolesInCache, domain.StudioRole{Module: "wallet_builder", Name: "admin"})

		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodDelete, fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, userID), nil)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type deleteUserResp struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			MemberID string `json:"member_id"`
		}

		type resp struct {
			Code int `json:"code"`
			Data struct {
				deleteUserResp
			} `json:"data"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal(user.Name, response.Data.Name)
		s.Equal(util.Val(user.Email), response.Data.Email)
		s.Equal(util.Val(user.MemberID), response.Data.MemberID)

		// assert role in cache has been deleted
		rolesInCache, kgError = orgService.GetStudioRoleCacheRepo().GetRolesByUser(context.Background(), orgID, userID)
		s.Nil(kgError)
		s.Len(rolesInCache, 0)
	}
}

func TestDeleteAdmin(t *testing.T) {
	s := assert.New(t)
	setup(t, nil)

	orgID := 10 + rand.Intn(100)
	adminUID := util.RandString(10)

	createOrgAndAdmin(s, orgID, adminUID)

	r := gin.Default()
	r.DELETE("/v1/studio/organization/:orgID/users/:userID", auth.MockOrgID(orgID), DisableUser)

	// break the rule: at least one owner
	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodDelete, fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, adminUID), nil)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		type deleteUserResp struct {
			Name     string `json:"name"`
			Email    string `json:"email"`
			MemberID string `json:"member_id"`
		}

		type resp struct {
			Code int `json:"code"`
			Data struct {
				deleteUserResp
			} `json:"data"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7021, response.Code)
	}
}

func TestReinviteStudioUser(t *testing.T) {
	s := assert.New(t)
	// inject mock
	ctrl := gomock.NewController(t)
	m := sendgrid.NewMockEmailClient(ctrl)
	ctx := context.Background()
	m.EXPECT().SendEmailWithSubject(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(1).Return(&rest.Response{StatusCode: 200}, nil)

	setup(t, m)

	application.Init(rdb.GormRepo())
	user.Init(repo.Unified())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	// create admin
	users, userIDs := dbtest.Users()
	kgErr := rdb.GormRepo().BatchSetUsers(context.Background(), users)
	if kgErr != nil {
		t.Fatalf("failed to set users: %v", kgErr.Error)
	}
	adminUID := userIDs[0]

	studioUsers := []model.StudioUser{
		{
			OrganizationID: 1,
			UID:            adminUID,
			Status:         model.StudioUserStatusActive,
		},
	}

	s.NoError(rdb.Get().Create(&studioUsers).Error)

	// new user
	newUserUID := userIDs[1]
	newUserName := util.RandString(10)
	err := rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         newUserUID,
			DisplayName: newUserName,
		},
	})
	assert.Nil(t, err)

	// create user with pending status
	s.NoError(rdb.Get().Create(&model.StudioUser{
		OrganizationID: 1,
		UID:            newUserUID,
		Status:         model.StudioUserStatusPending,
		Name:           newUserName,
	}).Error)

	r := gin.Default()
	r.POST("/v1/studio/organization/:orgID/reinvite", auth.MockAuthorize(adminUID), auth.MockOrgID(1), ReinviteStudioUser)
	{
		w := httptest.NewRecorder()
		addBody := map[string]interface{}{
			"uid": newUserUID,
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)
		req, _ := http.NewRequest(http.MethodPost, "/v1/studio/organization/1/reinvite", bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)

		type resp struct {
			Code int `json:"code"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		// check db, the invited user should be pending
		ctx := context.Background()
		orgID := 1
		user, kgError := rdb.GormRepo().GetStudioUser(ctx, orgID, newUserUID)
		s.Nil(kgError)
		s.Equal(newUserUID, user.UID)
		s.Equal(domain.StudioUserStatusPending, user.Status)
		s.Equal(newUserName, user.Name)
	}
}

func TestAcceptInvitation(t *testing.T) {
	s := assert.New(t)
	setup(t, nil)

	orgID := 100 + rand.Intn(100)
	uid := util.RandString(10)
	studioUsers := []model.StudioUser{
		{
			OrganizationID: orgID,
			UID:            uid,
			Status:         model.StudioUserStatusPending,
		},
	}
	s.NoError(rdb.Get().Create(&model.StudioOrganization{
		ID: orgID,
	}).Error)
	s.NoError(rdb.Get().Create(&studioUsers).Error)

	r := gin.Default()
	r.POST("/v1/studio/organization/:orgID/accept_invitation", auth.MockAuthorize(uid), auth.MockClientID("TODO:"), AcceptInvitation)
	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("/v1/studio/organization/%d/accept_invitation", orgID), nil)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type resp struct {
			Code int `json:"code"`
			Data struct {
				StudioToken string `json:"studio_token"`
			} `json:"data"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.NotEmpty(response.Data.StudioToken)

		// check db, the invited user should be active
		ctx := context.Background()

		user, kgError := rdb.GormRepo().GetStudioUser(ctx, orgID, uid)
		s.Nil(kgError)
		s.Equal(domain.StudioUserStatusActive, user.Status)
	}
}

func createOrgAndAdmin(s *assert.Assertions, orgID int, uid string) {
	s.NoError(rdb.Get().Where("organization_id = ? AND uid = ?", orgID, uid).
		Delete(&model.StudioUser{}).Error)

	s.NoError(rdb.Get().Create(&model.StudioOrganization{
		ID:   orgID,
		Name: "test org",
	}).Error)

	user := model.StudioUser{
		OrganizationID: orgID,
		UID:            uid,
		Name:           util.RandString(10),
		Email:          util.Ptr(util.RandEmail()),
		Status:         model.StudioUserStatusActive,
	}
	s.NoError(rdb.Get().Create(&user).Error)

	roleBinding := model.StudioRoleBinding{
		OrganizationID: orgID,
		UID:            uid,
		RoleID:         1,
	}
	s.NoError(rdb.Get().Create(&roleBinding).Error)
}

func TestInviteStudioUserFromBegin(t *testing.T) {
	s := assert.New(t)

	ctx := context.Background()
	// inject mock
	ctrl := gomock.NewController(t)
	m := sendgrid.NewMockEmailClient(ctrl)
	m.EXPECT().SendEmailWithSubject(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(2).Return(&rest.Response{StatusCode: 200}, nil)

	setup(t, m)
	application.Init(rdb.GormRepo())
	// create owner
	users, userIDs := dbtest.Users()

	user.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	s.NoError(err)
	adminUID := userIDs[0]
	err = rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         adminUID,
			DisplayName: util.RandString(10),
		},
	})
	s.NoError(err)
	studioUsers := []model.StudioUser{
		{
			OrganizationID: 1,
			UID:            adminUID,
			Status:         model.StudioUserStatusActive,
		},
	}
	s.NoError(rdb.Get().Create(&studioUsers).Error)

	email := util.RandEmail()

	r := gin.Default()
	r.POST("/v1/studio/organization/:orgID/users", auth.MockAuthorize(adminUID), auth.MockOrgID(1), InviteStudioUser)
	r.GET("/v1/studio/organization/:orgID/user_exist", auth.MockAuthorize(adminUID), Exist)
	r.POST("/v1/studio/organization/:orgID/accept_invitation", auth.AuthorizeByKgToken(), AcceptInvitation)
	r.POST("/v1/login", authapi.Login)
	r.POST("/v1/studio/login_v2", auth.AuthorizeByKgToken(), LoginV2)
	r.DELETE("/v1/user", auth.AuthorizeByKgToken(), userapi.DeleteUser)

	// check user not exist
	w := httptest.NewRecorder()
	req, _ := http.NewRequest(http.MethodGet, "/v1/studio/organization/1/user_exist?email="+email, nil)

	type userExistResp struct {
		Code int `json:"code"`
		Data struct {
			Exist bool `json:"exist"`
		} `json:"data"`
	}

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	var response userExistResp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)
	s.False(response.Data.Exist)

	// invite user
	w = httptest.NewRecorder()
	addBody := map[string]interface{}{
		"roles": []string{"user_360:admin", "asset_pro:trader", "compliance:admin"},
		"email": email,
		"name":  util.RandString(10),
	}
	jsonStr, err := json.Marshal(addBody)
	s.NoError(err)
	req, _ = http.NewRequest(http.MethodPost, "/v1/studio/organization/1/users", bytes.NewBuffer(jsonStr))

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type simpleResp struct {
		Code int `json:"code"`
	}

	var resp simpleResp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
	s.Equal(0, resp.Code)

	// user accept invitation
	loginProvider := authservice.NewLoginProvider(&authservice.LoginReq{
		Email:            email,
		VerificationCode: "123456",
	})
	_, _, kgToken, _, kgErr := loginProvider.IssueTokens(context.Background())
	s.Nil(kgErr)

	w = httptest.NewRecorder()
	req, _ = http.NewRequest(http.MethodPost, "/v1/studio/organization/1/accept_invitation", nil)
	req.Header.Set("KG-TOKEN", kgToken)

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type acceptInvitationResp struct {
		Code int `json:"code"`
		Data struct {
			StudioToken string `json:"studio_token"`
		} `json:"data"`
	}

	var acceptInvitationResponse acceptInvitationResp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &acceptInvitationResponse))
	s.Equal(0, acceptInvitationResponse.Code)
	s.NotEmpty(acceptInvitationResponse.Data.StudioToken)

	// user login studio
	now := time.Now()
	s.NoError(rdbtest.CreateEmailLogs(rdb.Get(), []string{
		email,
	}, &now))

	w = httptest.NewRecorder()
	addBody = map[string]interface{}{
		"email":             email,
		"verification_code": "123456",
	}
	jsonStr, err = json.Marshal(addBody)
	s.NoError(err)
	req, _ = http.NewRequest(http.MethodPost, "/v1/login", bytes.NewBuffer(jsonStr))

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type loginResp struct {
		Code int `json:"code"`
		Data struct {
			AccessToken string `json:"access_token"`
			IDToken     string `json:"id_token"`
			KgToken     string `json:"kg_token"`
		} `json:"data"`
	}

	var loginResponse loginResp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &loginResponse))
	s.Equal(0, loginResponse.Code)
	s.NotEmpty(loginResponse.Data.AccessToken)
	s.NotEmpty(loginResponse.Data.IDToken)
	s.NotEmpty(loginResponse.Data.KgToken)

	w = httptest.NewRecorder()
	req, _ = http.NewRequest(http.MethodPost, "/v1/studio/login_v2", nil)
	req.Header.Set("KG-TOKEN", loginResponse.Data.KgToken)

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type studioLoginResp struct {
		Code int `json:"code"`
		Data struct {
			StudioToken string `json:"studio_token"`
		} `json:"data"`
	}

	var studioLoginResponse studioLoginResp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &studioLoginResponse))
	s.Equal(0, studioLoginResponse.Code)
	s.NotEmpty(studioLoginResponse.Data.StudioToken)

	// user delete account
	w = httptest.NewRecorder()
	req, _ = http.NewRequest(http.MethodDelete, "/v1/user", nil)
	req.Header.Set("KG-TOKEN", loginResponse.Data.KgToken)

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type deleteUserResp struct {
		Code int `json:"code"`
	}

	var deleteUserResponse deleteUserResp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &deleteUserResponse))
	s.Equal(0, deleteUserResponse.Code)

	// check user not exist
	w = httptest.NewRecorder()
	req, _ = http.NewRequest(http.MethodGet, "/v1/studio/organization/1/user_exist?email="+email, nil)

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)
	s.False(response.Data.Exist)

	// user login studio again, should fail
	now = now.Add(time.Second)
	s.NoError(rdbtest.CreateEmailLogs(rdb.Get(), []string{
		email,
	}, &now))

	w = httptest.NewRecorder()
	addBody = map[string]interface{}{
		"email":             email,
		"verification_code": "123456",
	}
	jsonStr, err = json.Marshal(addBody)
	s.NoError(err)
	req, _ = http.NewRequest(http.MethodPost, "/v1/login", bytes.NewBuffer(jsonStr))

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	s.NoError(json.Unmarshal(w.Body.Bytes(), &loginResponse))
	s.Equal(0, loginResponse.Code)
	s.NotEmpty(loginResponse.Data.AccessToken)
	s.NotEmpty(loginResponse.Data.IDToken)
	s.NotEmpty(loginResponse.Data.KgToken)

	w = httptest.NewRecorder()
	req, _ = http.NewRequest(http.MethodPost, "/v1/studio/login_v2", nil)
	req.Header.Set("KG-TOKEN", loginResponse.Data.KgToken)

	r.ServeHTTP(w, req)
	s.Equal(http.StatusNotFound, w.Code)

	// invite user
	w = httptest.NewRecorder()
	addBody = map[string]interface{}{
		"roles": []string{"user_360:admin", "asset_pro:trader", "compliance:admin"},
		"email": email,
		"name":  util.RandString(10),
	}
	jsonStr, err = json.Marshal(addBody)
	s.NoError(err)
	req, _ = http.NewRequest(http.MethodPost, "/v1/studio/organization/1/users", bytes.NewBuffer(jsonStr))

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
	s.Equal(0, resp.Code)

	// user accept invitation
	loginProvider = authservice.NewLoginProvider(&authservice.LoginReq{
		Email:            email,
		VerificationCode: "123456",
	})
	_, _, kgToken, _, kgErr = loginProvider.IssueTokens(context.Background())
	s.Nil(kgErr)

	w = httptest.NewRecorder()
	req, _ = http.NewRequest(http.MethodPost, "/v1/studio/organization/1/accept_invitation", nil)
	req.Header.Set("KG-TOKEN", kgToken)

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	// user login studio
	now = now.Add(time.Second)
	s.NoError(rdbtest.CreateEmailLogs(rdb.Get(), []string{
		email,
	}, &now))

	w = httptest.NewRecorder()
	addBody = map[string]interface{}{
		"email":             email,
		"verification_code": "123456",
	}
	jsonStr, err = json.Marshal(addBody)
	s.NoError(err)
	req, _ = http.NewRequest(http.MethodPost, "/v1/login", bytes.NewBuffer(jsonStr))

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	s.NoError(json.Unmarshal(w.Body.Bytes(), &loginResponse))
	s.Equal(0, loginResponse.Code)
	s.NotEmpty(loginResponse.Data.AccessToken)
	s.NotEmpty(loginResponse.Data.IDToken)
	s.NotEmpty(loginResponse.Data.KgToken)

	w = httptest.NewRecorder()
	req, _ = http.NewRequest(http.MethodPost, "/v1/studio/login_v2", nil)
	req.Header.Set("KG-TOKEN", loginResponse.Data.KgToken)

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	s.NoError(json.Unmarshal(w.Body.Bytes(), &studioLoginResponse))
	s.Equal(0, studioLoginResponse.Code)
	s.NotEmpty(studioLoginResponse.Data.StudioToken)
}

func TestUpdateStudioUser(t *testing.T) {
	s := assert.New(t)
	setup(t, nil)

	orgID := 10 + rand.Intn(100)
	userID := util.RandString(10)
	adminUID := util.RandString(10)

	createOrgAndAdmin(s, orgID, adminUID)

	r := gin.Default()
	r.PUT("/v1/studio/organization/:orgID/users/:uid",
		auth.MockOrgID(orgID), UpdateStudioUser)

	// failed: without role filed
	{
		expectedName := util.RandString(10)
		expectedMemberID := util.RandString(10)

		addBody := map[string]interface{}{
			"name":      expectedName,
			"member_id": expectedMemberID,
			// "roles":     []string{},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, userID), bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		type resp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7020, response.Code)
		s.Equal("roles is required", response.Message)
		// s.Equal(7014, response.Code)
	}

	// failed: empty roles
	{
		expectedName := util.RandString(10)
		expectedMemberID := util.RandString(10)

		addBody := map[string]interface{}{
			"name":      expectedName,
			"member_id": expectedMemberID,
			"roles":     []string{},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, userID), bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		type resp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7020, response.Code)
		s.Equal("roles is required", response.Message)
	}
	{ // no studio module
		expectedName := util.RandString(10)
		expectedMemberID := util.RandString(10)
		addBody := map[string]interface{}{
			"name":      expectedName,
			"member_id": expectedMemberID,
			"roles":     []string{"user_360:admin", "asset_pro:trader", "compliance:admin"},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, userID), bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		type resp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7024, response.Code)
		s.Equal("studio organization module not found", response.Message)
	}

	s.NoError(rdb.Get().Create(&model.StudioOrganizationModule{
		OrganizationID: orgID,
		User360:        "data,engage,audience",
		Admin:          "billing",
	}).Error)

	{ // module not enabled
		expectedName := util.RandString(10)
		expectedMemberID := util.RandString(10)
		addBody := map[string]interface{}{
			"name":      expectedName,
			"member_id": expectedMemberID,
			"roles":     []string{"user_360:admin", "asset_pro:trader", "compliance:admin"},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, userID), bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		type resp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7020, response.Code)
		s.Equal(fmt.Sprintf("org(%d) just enabled modules: [user_360]", orgID), response.Message)
	}
	{ // target user not found
		expectedName := util.RandString(10)
		expectedMemberID := util.RandString(10)
		addBody := map[string]interface{}{
			"name":      expectedName,
			"member_id": expectedMemberID,
			"roles":     []string{"user_360:admin"},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, userID), bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		type resp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7014, response.Code)
		s.Equal("studio user not found", response.Message)
	}

	s.NoError(rdb.Get().Model(&model.StudioOrganizationModule{}).
		Where("organization_id = ?", orgID).Updates(map[string]interface{}{
		"user_360":       "data",
		"wallet_builder": "project",
		"asset_pro":      "treasury",
		"nft_boost":      "campaign",
		"compliance":     "create_a_task",
		"admin":          "billing",
	}).Error)

	s.NoError(rdb.Get().Create(&model.StudioUser{
		OrganizationID: orgID,
		UID:            userID,
		Name:           util.RandString(10),
		Status:         model.StudioUserStatusActive,
	}).Error)

	{ // success
		expectedName := util.RandString(10)
		expectedMemberID := util.RandString(10)
		addBody := map[string]interface{}{
			"name":      expectedName,
			"member_id": expectedMemberID,
			"roles":     []string{"user_360:admin"},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, userID), bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type resp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Empty(response.Message)

		var dbUser model.StudioUser
		s.NoError(rdb.Get().Preload("RoleBinding.Role").
			Where("organization_id = ? AND uid = ?", orgID, userID).First(&dbUser).Error)
		s.Equal(expectedName, dbUser.Name)
		s.Equal(expectedMemberID, util.Val(dbUser.MemberID))

		var actualRoles []string
		for _, roleBinding := range dbUser.RoleBinding {
			actualRoles = append(actualRoles, roleBinding.Role.String())
		}

		s.Len(actualRoles, 1)
		s.Contains(actualRoles, "user_360:admin")

		// check cache
		rolesInCache, kgError := orgService.GetStudioRoleCacheRepo().
			GetRolesByUser(context.Background(), orgID, userID)
		s.Nil(kgError)
		s.Len(rolesInCache, 1)
		s.Contains(rolesInCache, domain.StudioRole{Module: "user_360", Name: "admin"})
	}
	{ // success just update roles

		var expectedName string
		var expectedMemberID *string
		{
			var dbUser model.StudioUser
			s.NoError(rdb.Get().Preload("RoleBinding.Role").
				Where("organization_id = ? AND uid = ?", orgID, userID).First(&dbUser).Error)
			expectedName = dbUser.Name
			expectedMemberID = dbUser.MemberID
		}

		addBody := map[string]interface{}{
			// "name":      expectedName,
			// "member_id": expectedMemberID,
			"roles": []string{"user_360:admin", "asset_pro:admin"},
		}
		jsonStr, err := json.Marshal(addBody)
		assert.Nil(t, err)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, userID), bytes.NewBuffer(jsonStr))

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type resp struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Empty(response.Message)

		var dbUser model.StudioUser
		s.NoError(rdb.Get().Preload("RoleBinding.Role").
			Where("organization_id = ? AND uid = ?", orgID, userID).First(&dbUser).Error)
		s.Equal(expectedName, dbUser.Name)
		s.Equal(expectedMemberID, dbUser.MemberID)

		var actualRoles []string
		for _, roleBinding := range dbUser.RoleBinding {
			actualRoles = append(actualRoles, roleBinding.Role.String())
		}

		s.Len(actualRoles, 2)
		s.Contains(actualRoles, "user_360:admin")
		s.Contains(actualRoles, "asset_pro:admin")

		// check cache
		rolesInCache, kgError := orgService.GetStudioRoleCacheRepo().
			GetRolesByUser(context.Background(), orgID, userID)
		s.Nil(kgError)
		s.Len(rolesInCache, 2)
		s.Contains(rolesInCache, domain.StudioRole{Module: "user_360", Name: "admin"})
	}
}
