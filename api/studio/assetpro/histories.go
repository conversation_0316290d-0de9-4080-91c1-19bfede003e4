package assetpro

import (
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"

	request "github.com/kryptogo/kg-wallet-backend/api/paging"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type txLog struct {
	ID               string `json:"id"`
	ChainID          string `json:"chain_id"`
	TokenName        string `json:"token_name"`
	TokenLogoURL     string `json:"token_logo_url"`
	TokenCoingeckoID string `json:"token_coingecko_id"`
	Amount           string `json:"amount"`
	SubmitTime       int    `json:"submit_time"`
	TransferTime     int    `json:"transfer_time"`
	UpdateTime       int    `json:"update_time"`
	Recipient        struct {
		UID           string  `json:"uid"`
		Name          *string `json:"name"`
		KycStatus     *string `json:"kyc_status"`
		Phone         *string `json:"phone"`
		Email         *string `json:"email"`
		WalletAddress string  `json:"wallet_address"`
	} `json:"recipient"`
	Status    string  `json:"status"`
	TxHash    string  `json:"tx_hash"`
	UpdatedBy *string `json:"updated_by"`
}

func (o *txLog) fromDomain(txLog domain.AssetProTxLog) {
	o.ID = txLog.SerialID
	o.ChainID = txLog.ChainID
	o.TokenName = txLog.TokenName
	o.TokenLogoURL = txLog.TokenLogoURL
	o.TokenCoingeckoID = txLog.TokenCoingeckoID
	o.Amount = txLog.Amount.String()
	o.SubmitTime = txLog.SubmitTime
	o.TransferTime = util.Val(txLog.TransferTime)
	o.UpdateTime = txLog.UpdateTime
	o.Recipient.Name = txLog.RecipientName
	var kycStatus *string
	if txLog.RecipientKycStatus != nil {
		kycStatus = util.Ptr(txLog.RecipientKycStatus.String())
	}
	o.Recipient.KycStatus = kycStatus
	o.Recipient.UID = util.Val(txLog.RecipientUID)
	o.Recipient.Email = txLog.RecipientEmail
	o.Recipient.Phone = txLog.RecipientPhone
	o.Recipient.WalletAddress = txLog.ToAddress
	o.Status = txLog.Status.String()
	o.TxHash = txLog.TxHash
	o.UpdatedBy = util.Ptr(txLog.UpdatedBy)
}

type getHistoriesParams struct {
	Submitter        string   `form:"submitter"`
	Approver         string   `form:"approver"`
	FinanceManager   string   `form:"finance_manager"`
	Rejecter         string   `form:"rejecter"`
	StatusList       []string `form:"status"`
	Token            string   `form:"token"`
	ChainID          string   `form:"chain_id"`
	SubmitTimeFrom   int64    `form:"submit_time_from"`
	SubmitTimeTo     int64    `form:"submit_time_to"`
	AmountFrom       float64  `form:"amount_from"`
	AmountTo         float64  `form:"amount_to"`
	UsdAmountFrom    float64  `form:"usd_amount_from"`
	UsdAmountTo      float64  `form:"usd_amount_to"`
	TransferTimeFrom int64    `form:"transfer_time_from"`
	TransferTimeTo   int64    `form:"transfer_time_to"`
	TxHash           string   `form:"tx_hash"`
	request.PagingRequest
}

// Histories get histories by filter
func Histories(ctx *gin.Context) {
	req := getHistoriesParams{
		PagingRequest: request.PagingRequest{
			PageSort: "submit_time:d,status:a",
		},
	}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")
	uid := ctx.GetString("uid")

	studioRoles, kgError := organization.GetStudioRoleByUserFromCache(
		ctx.Request.Context(), orgID, uid)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	roles := make([]rbac.Role, 0, len(studioRoles))
	for _, studioRole := range studioRoles {
		roles = append(roles, rbac.Role(studioRole.String()))
	}

	// check if user has permission to read all
	// if not, he/she is a trader, should only see his/her own tx logs
	isTrader := !rbac.RBACService.IsGranted(ctx.Request.Context(), rbac.IsGrantRequest{
		Roles:    roles,
		Resource: rbac.ResourceTransaction,
		Action:   rbac.ActionReadAll,
	})

	query, kgError := req.ParseQuery([]string{"submit_time", "amount", "status", "update_time"})
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	params := domain.GetAssetProTxLogsParams{
		OrganizationID:    orgID,
		SubmitterUID:      req.Submitter,
		ApproverUID:       req.Approver,
		FinanceManagerUID: req.FinanceManager,
		RejecterUID:       req.Rejecter,
		StatusList:        req.StatusList,
		Token:             req.Token,
		ChainID:           req.ChainID,
		SubmitTimeFrom:    req.SubmitTimeFrom,
		SubmitTimeTo:      req.SubmitTimeTo,
		AmountFrom:        req.AmountFrom,
		AmountTo:          req.AmountTo,
		UsdAmountFrom:     req.UsdAmountFrom,
		UsdAmountTo:       req.UsdAmountTo,
		TransferTimeFrom:  req.TransferTimeFrom,
		TransferTimeTo:    req.TransferTimeTo,
		TxHash:            req.TxHash,
		Query:             query,
	}
	if isTrader {
		params.TraderUID = uid
	}
	txLogs, pagingResp, err := assetpro.Histories(ctx.Request.Context(), &params)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	resp := make([]txLog, len(txLogs))
	for index := range txLogs {
		resp[index].fromDomain(txLogs[index])
	}

	response.OKWithPaging(ctx, resp, response.Paging{
		PageNumber: pagingResp.PageNumber,
		PageSize:   pagingResp.PageSize,
		TotalCount: pagingResp.TotalCount,
		PageSort:   string(pagingResp.PageSort),
	})
}

type txLogDetail struct {
	TxInfo struct {
		ID        string `json:"id"`
		Status    string `json:"status"`
		Amount    string `json:"amount"`
		UsdAmount string `json:"usd_amount"`
		Token     struct {
			Name            string `json:"name"`
			Symbol          string `json:"symbol"`
			ChainID         string `json:"chain_id"`
			LogoURL         string `json:"logo_url"`
			CoingeckoID     string `json:"coingecko_id"`
			ContractAddress string `json:"contract_address"`
			Decimals        int    `json:"decimals"`
		} `json:"token"`
		Recipient struct {
			WalletAddress string    `json:"wallet_address"`
			Customer      *customer `json:"customer"`
		} `json:"recipient"`
		Notes struct {
			SubmissionNote *string `json:"submission_note"`
			RejectionNote  *string `json:"rejection_note"`
		} `json:"notes"`
		Attachments *[]string `json:"attachments"`
		TxHashes    *[]string `json:"tx_hashes"`
	} `json:"tx_info"`
	Operators struct {
		Trader         txLogOperator  `json:"trader"`
		Approver       *txLogOperator `json:"approver"`
		FinanceManager *txLogOperator `json:"finance_manager"`
	} `json:"operators"`
}

type customer struct {
	UID        string  `json:"uid"`
	Name       string  `json:"name"`
	ProfileImg *string `json:"profile_img"`
	Phone      *string `json:"phone"`
	Email      *string `json:"email"`
	KycStatus  string  `json:"kyc_status"`
}

type txLogOperator struct {
	Operator operatorInfo `json:"operator"`
	Time     int          `json:"time"`
}

type operatorInfo struct {
	Name       string `json:"name"`
	ProfileImg string `json:"profile_img"`
	Email      string `json:"email"`
}

func (o *txLogDetail) fromDomain(txLog domain.AssetProTxLogDetail) {
	o.TxInfo.ID = txLog.SerialID
	o.TxInfo.Status = txLog.Status.String()
	o.TxInfo.Amount = txLog.Amount.String()
	o.TxInfo.UsdAmount = txLog.UsdAmount.String()
	o.TxInfo.Token.Name = txLog.TokenName
	o.TxInfo.Token.Symbol = txLog.TokenSymbol
	o.TxInfo.Token.ChainID = txLog.ChainID
	o.TxInfo.Token.LogoURL = txLog.TokenLogoURL
	o.TxInfo.Token.CoingeckoID = txLog.TokenCoingeckoID
	o.TxInfo.Token.ContractAddress = txLog.ContractAddress
	o.TxInfo.Token.Decimals = txLog.TokenDecimals
	o.TxInfo.Recipient.WalletAddress = txLog.ToAddress
	if txLog.RecipientUID != nil {
		o.TxInfo.Recipient.Customer = &customer{
			UID:        *txLog.RecipientUID,
			Name:       util.Val(txLog.RecipientName),
			ProfileImg: txLog.RecipientProfileImg,
			Phone:      txLog.RecipientPhone,
			Email:      txLog.RecipientEmail,
		}
		if txLog.RecipientKycStatus != nil {
			o.TxInfo.Recipient.Customer.KycStatus = txLog.RecipientKycStatus.String()
		}
	}
	o.TxInfo.Notes.SubmissionNote = txLog.SubmissionNote
	o.TxInfo.Notes.RejectionNote = txLog.RejectionNote
	o.TxInfo.Attachments = txLog.Attachments
	o.TxInfo.TxHashes = txLog.TxHashes

	toTxLogOperatorFunc := func(domainOperator domain.AssetProTxOperator) txLogOperator {
		return txLogOperator{
			Operator: operatorInfo{
				Name:       domainOperator.Name,
				ProfileImg: domainOperator.ProfileImg,
				Email:      domainOperator.Email,
			},
			Time: domainOperator.OperationTime,
		}
	}

	o.Operators.Trader = toTxLogOperatorFunc(txLog.Trader)
	if txLog.Approver != nil {
		o.Operators.Approver = util.Ptr(toTxLogOperatorFunc(*txLog.Approver))
	}
	if txLog.FinanceManager != nil {
		o.Operators.FinanceManager = util.Ptr(toTxLogOperatorFunc(*txLog.FinanceManager))
	}
}

// HistoryDetail get history detail by tx id
func HistoryDetail(ctx *gin.Context) {
	domainTxLog, kgErr := assetpro.HistoryDetail(ctx.Request.Context(), ctx.GetInt("org_id"), ctx.Param("id"))
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	var resp txLogDetail
	resp.fromDomain(*domainTxLog)

	response.OK(ctx, resp)
}

// filterOptionUser is a json model that represents filter option operator.
type filterOptionUser struct {
	Name string `json:"name"`
	UID  string `json:"uid"`
}

type filterOptions struct {
	Submitter      []filterOptionUser `json:"submitter"`
	Approver       []filterOptionUser `json:"approver"`
	FinanceManager []filterOptionUser `json:"finance_manager"`
	Rejecter       []filterOptionUser `json:"rejecter"`
	Status         []string           `json:"status"`
	Token          []string           `json:"token"`
	BlockChain     []string           `json:"chain_id"`
	SubmitTimeFrom *int64             `json:"submit_time_from"`
	SubmitTimeTo   *int64             `json:"submit_time_to"`
	AmountFrom     *float64           `json:"amount_from"`
	AmountTo       *float64           `json:"amount_to"`
	UsdAmountFrom  *float64           `json:"usd_amount_from"`
	UsdAmountTo    *float64           `json:"usd_amount_to"`
}

func (o *filterOptions) fromDomain(options *domain.FilterOptions) {
	toFilterOptionUserFunc := func(Submitter domain.FilterOptionUser, _ int) filterOptionUser {
		return filterOptionUser(Submitter)
	}
	o.Submitter = lo.Map(options.Submitter, toFilterOptionUserFunc)
	o.Approver = lo.Map(options.Approver, toFilterOptionUserFunc)
	o.FinanceManager = lo.Map(options.FinanceManager, toFilterOptionUserFunc)
	o.Rejecter = lo.Map(options.Rejecter, toFilterOptionUserFunc)
	o.Status = lo.Map(options.Status, func(status domain.AssetProTxLogStatus, _ int) string {
		return status.String()
	})

	o.Token = lo.Map(options.Token, func(token string, _ int) string {
		return token
	})
	o.BlockChain = lo.Map(options.BlockChain, func(chainID string, _ int) string {
		return chainID
	})
	o.SubmitTimeFrom = options.SubmitTimeFrom
	o.SubmitTimeTo = options.SubmitTimeTo
	if options.AmountFrom != nil {
		amountFrom, _ := options.AmountFrom.Float64()
		o.AmountFrom = &amountFrom
	}
	if options.AmountTo != nil {
		amountTo, _ := options.AmountTo.Float64()
		o.AmountTo = &amountTo
	}
	if options.UsdAmountFrom != nil {
		usdAmountFrom, _ := options.UsdAmountFrom.Float64()
		o.UsdAmountFrom = &usdAmountFrom
	}
	if options.UsdAmountTo != nil {
		usdAmountTo, _ := options.UsdAmountTo.Float64()
		o.UsdAmountTo = &usdAmountTo
	}
}

// HistoriesFilterOptions get filter options.
func HistoriesFilterOptions(c *gin.Context) {
	orgID := c.GetInt("org_id")
	options, err := assetpro.FilterOptions(c.Request.Context(), orgID)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.DBError, err.Error())
		return
	}

	var resp filterOptions
	resp.fromDomain(options)

	response.OK(c, resp)
}

type pendingHistoryCount struct {
	Count                     int `json:"count"`
	CountAwaitingApprovalSelf int `json:"count_awaiting_approval_self"`
	CountAwaitingApproval     int `json:"count_awaiting_approval"`
	CountAwaitingRelease      int `json:"count_awaiting_release"`
}

func (o *pendingHistoryCount) fromDomain(txLog domain.AssetProTxLogPendingCount) {
	o.Count = txLog.Count
	o.CountAwaitingApprovalSelf = txLog.CountAwaitingApprovalSelf
	o.CountAwaitingApproval = txLog.CountAwaitingApproval
	o.CountAwaitingRelease = txLog.CountAwaitingRelease
}

// PendingHistoryCount get pending history count
func PendingHistoryCount(c *gin.Context) {
	orgID := c.GetInt("org_id")
	uid := c.GetString("uid")

	studioRoles, kgError := organization.GetStudioRoleByUserFromCache(
		c.Request.Context(), orgID, uid)
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	roles := make([]rbac.Role, 0, len(studioRoles))
	for _, studioRole := range studioRoles {
		roles = append(roles, rbac.Role(studioRole.String()))
	}

	isAdmin := lo.Contains(roles, rbac.RoleAssetProAdmin) || lo.Contains(roles, rbac.RoleOwner)
	isTrader := rbac.RBACService.IsGranted(c.Request.Context(), rbac.IsGrantRequest{
		Roles:    roles,
		Resource: rbac.ResourceTransaction,
		Action:   rbac.ActionApply,
	})
	isApprover := rbac.RBACService.IsGranted(c.Request.Context(), rbac.IsGrantRequest{
		Roles:    roles,
		Resource: rbac.ResourceTransaction,
		Action:   rbac.ActionApprove,
	})
	isFinanceManager := rbac.RBACService.IsGranted(c.Request.Context(), rbac.IsGrantRequest{
		Roles:    roles,
		Resource: rbac.ResourceTransaction,
		Action:   rbac.ActionRelease,
	})

	params := domain.AssetProTxLogPendingCountParams{
		OrganizationID:   orgID,
		UID:              uid,
		IsTrader:         isAdmin || isTrader,
		IsApprover:       isAdmin || isApprover,
		IsFinanceManager: isAdmin || isFinanceManager,
	}
	countRes, kgErr := assetpro.PendingHistoryCount(c.Request.Context(), params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	var count pendingHistoryCount
	count.fromDomain(countRes)

	response.OK(c, count)
}
