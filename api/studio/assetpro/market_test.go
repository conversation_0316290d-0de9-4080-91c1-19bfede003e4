package assetpro_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/studio/assetpro"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro/market"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/stretchr/testify/suite"
)

type testMarketSuite struct {
	suite.Suite

	ginEngine *gin.Engine
}

func (s *testMarketSuite) SetupSuite() {
	s.ginEngine = gin.Default()
	oauth.Init(rdb.GormRepo())
	organization.Init(organization.InitParam{
		StudioOrgRepo:  rdb.GormRepo(),
		StudioRoleRepo: rdb.GormRepo(),
	})
	market.Init(market.InitParam{
		AssetProProductRepo: rdb.GormRepo(),
	})
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())
	tx.Init(repo.Unified())

	s.ginEngine = gin.Default()
	s.createTestMarketInfoSeed()
	orgMiddleware := func(ctx *gin.Context) {
		orgID, _ := strconv.Atoi(ctx.Param("orgID"))
		ctx.Set("org_id", orgID)
		ctx.Next()
	}
	s.ginEngine.PUT("/_v/studio/organization/:orgID/asset_pro/market",
		assetpro.UpsertMarketInfo)

	s.ginEngine.GET("/v1/studio/organization/:orgID/asset_pro/market",
		orgMiddleware,
		assetpro.GetMarketInfoForMerchant)

	s.ginEngine.PUT("/v1/studio/organization/:orgID/asset_pro/market",
		orgMiddleware,
		assetpro.SaveMarketInfoForMerchant)

	s.ginEngine.GET("/v1/studio/asset_pro/market_config",
		assetpro.GetMarketInfoForCustomer,
	)
}

func (s *testMarketSuite) createTestMarketInfoSeed() {
	db := rdb.Get()
	rdb.Reset()
	s.NoError(rdbtest.CreateStudioDefault(rdb.Get()))
	s.NoError(db.Create([]*model.StudioOrganization{
		{
			ID:                       5,
			Name:                     "TAG",
			SignAlertThreshold:       100.0,
			ComplianceOrganizationID: 999,
		}}).Error)
	s.NoError(rdbtest.CreateAssetProProductBaseInfos(rdb.Get()))
	oauthClientConfigs := []model.OAuthClientConfig{
		{
			ID:     "kryptogo-asset-pro-market",
			Domain: "store-dev.kryptogo.com",
			Name:   "kryptogo-asset-pro-market",
		},
		{
			ID:     "stickey-asset-pro-market-2",
			Domain: "store-dev.kryptogo.com",
			Name:   "stickey-asset-pro-market-2",
		},
		{
			ID:     "tag-asset-pro-market",
			Domain: "tag-dev.kryptogo.com",
			Name:   "tag-asset-pro-market",
		},
	}

	s.NoError(db.Create(&oauthClientConfigs).Error)

	studioClients := []model.StudioOrganizationClient{
		{
			ClientID:        "kryptogo-asset-pro-market",
			OrganizationID:  1,
			ApplicationType: util.Ptr(string(domain.OAuthApplicationTypeMarket)),
		},
		{
			ClientID:        "stickey-asset-pro-market-2",
			OrganizationID:  2,
			ApplicationType: util.Ptr(string(domain.OAuthApplicationTypeMarket)),
		},
		{
			ClientID:        "tag-asset-pro-market",
			OrganizationID:  5,
			ApplicationType: util.Ptr(string(domain.OAuthApplicationTypeMarket)),
		},
	}

	s.NoError(db.Create(&studioClients).Error)

	studioMarkets := []model.StudioMarket{
		{
			OrganizationID:       1,
			MarketCode:           "kryptogo",
			ClientID:             "kryptogo-asset-pro-market",
			MarketURL:            "https://store-dev.kryptogo.com/kryptogo",
			Title:                "KryptoGO marketplace",
			Logo:                 "https://static.kryptogo.com/logo/market/kryptogo.png",
			Email:                util.Ptr("<EMAIL>"),
			Phone:                nil,
			LineID:               util.Ptr("kryptogo"),
			Telegram:             util.Ptr("kryptogo"),
			PaymentExpirationSec: 86400,
			Introduction:         util.Ptr("KryptoGO marketplace"),
			PaymentMethod:        "bank_transfer",
			PaymentCurrency:      "TWD",
			BankName:             util.Ptr("Mega Bank"),
			BranchName:           util.Ptr("Xin Yi"),
			BankAccount:          util.Ptr("168"),
			AccountHolderName:    util.Ptr("KryptoGO 168"),
		},
		{
			OrganizationID:  2,
			Title:           "Stickey marketplace",
			MarketCode:      "stickey",
			ClientID:        "stickey-asset-pro-market-2",
			MarketURL:       "https://store-dev.kryptogo.com/stickey",
			Email:           util.Ptr("<EMAIL>"),
			Telegram:        util.Ptr("stickey"),
			PaymentMethod:   "bank_transfer",
			PaymentCurrency: "TWD",
		},
		{
			OrganizationID:  5,
			Title:           "TAG marketplace",
			MarketCode:      "tag",
			ClientID:        "tag-asset-pro-market",
			MarketURL:       "https://store-dev.kryptogo.com/tag",
			Email:           util.Ptr("<EMAIL>"),
			PaymentMethod:   "bank_transfer",
			PaymentCurrency: "TWD",
		},
	}

	s.NoError(db.Create(&studioMarkets).Error)
}

func (s *testMarketSuite) TestGetMarketInfoForMerchantForMerchant() {
	type resp struct {
		Code int `json:"code"`
		Data struct {
			MarketURL            string  `json:"market_url"`
			Title                string  `json:"title"`
			Logo                 string  `json:"logo"`
			Introduction         *string `json:"introduction"`
			Email                *string `json:"email"`
			Phone                *string `json:"phone"`
			LineID               *string `json:"line_id"`
			Telegram             *string `json:"telegram"`
			Discord              *string `json:"discord"`
			Twitter              *string `json:"twitter"`
			PaymentMethod        string  `json:"payment_method"`
			PaymentCurrency      string  `json:"payment_currency"`
			BankName             *string `json:"bank_name"`
			BranchName           *string `json:"branch_name"`
			BankAccount          *string `json:"bank_account"`
			AccountHolderName    *string `json:"bank_account_holder_name"`
			PaymentExpirationSec int     `json:"payment_expiration_sec"`
		} `json:"data"`
	}

	{
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/market", nil,
		)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal("KryptoGO marketplace", *response.Data.Introduction)
		s.Equal("<EMAIL>", *response.Data.Email)
		s.Equal("kryptogo", *response.Data.LineID)
		s.Equal("https://store-dev.kryptogo.com/kryptogo", response.Data.MarketURL)
		s.Equal("KryptoGO marketplace", response.Data.Title)
		s.Equal("https://static.kryptogo.com/logo/market/kryptogo.png", response.Data.Logo)
		s.Equal("kryptogo", *response.Data.Telegram)
		s.Nil(response.Data.Discord)
		s.Nil(response.Data.Twitter)
		s.Equal("bank_transfer", response.Data.PaymentMethod)
		s.Equal("TWD", response.Data.PaymentCurrency)
		s.Equal("Mega Bank", *response.Data.BankName)
		s.Equal("Xin Yi", *response.Data.BranchName)
		s.Equal("168", *response.Data.BankAccount)
		s.Equal("KryptoGO 168", *response.Data.AccountHolderName)
	}
	{
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/asset_pro/market", nil,
		)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal("https://store-dev.kryptogo.com/stickey", response.Data.MarketURL)
		s.Nil(response.Data.Introduction)
		s.Equal("<EMAIL>", *response.Data.Email)
		s.Nil(response.Data.LineID)
		s.Equal("Stickey marketplace", response.Data.Title)
		s.Equal("", response.Data.Logo)
		s.Equal("stickey", *response.Data.Telegram)
		s.Nil(response.Data.Discord)
		s.Nil(response.Data.Twitter)
		s.Equal("bank_transfer", response.Data.PaymentMethod)
		s.Equal("TWD", response.Data.PaymentCurrency)
		s.Nil(response.Data.BankName)
		s.Nil(response.Data.BranchName)
		s.Nil(response.Data.BankAccount)
		s.Nil(response.Data.AccountHolderName)
	}
}

func (s *testMarketSuite) TestGetMarketInfoForMerchantForMerchantNotFound() {
	type resp struct {
		Code int      `json:"code"`
		Data struct{} `json:"data"`
	}

	w := httptest.NewRecorder()

	req, _ := http.NewRequest(http.MethodGet,
		"/v1/studio/organization/3/asset_pro/market", nil,
	)

	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusNotFound, w.Code)

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(code.StudioMarketNotFound, response.Code)
}

func (s *testMarketSuite) TestGetMarketInfoForCustomer() {
	type resp struct {
		Code int `json:"code"`
		Data struct {
			OrganizationID       int     `json:"organization_id"`
			OauthClientID        string  `json:"oauth_client_id"`
			Title                string  `json:"title"`
			Logo                 string  `json:"logo"`
			ComplyflowURL        *string `json:"complyflow_url"`
			Introduction         *string `json:"introduction"`
			Email                *string `json:"email"`
			Phone                *string `json:"phone"`
			LineID               *string `json:"line_id"`
			Telegram             *string `json:"telegram"`
			Discord              *string `json:"discord"`
			Twitter              *string `json:"twitter"`
			PaymentMethod        string  `json:"payment_method"`
			PaymentCurrency      string  `json:"payment_currency"`
			BankName             *string `json:"bank_name"`
			BranchName           *string `json:"branch_name"`
			BankAccount          *string `json:"bank_account"`
			AccountHolderName    *string `json:"bank_account_holder_name"`
			PaymentExpirationSec int     `json:"payment_expiration_sec"`
		} `json:"data"`
	}

	{
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/asset_pro/market_config?market_code=kryptogo", nil,
		)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal(1, response.Data.OrganizationID)
		s.Equal("kryptogo-asset-pro-market", response.Data.OauthClientID)
		s.Equal("KryptoGO marketplace", *response.Data.Introduction)
		s.Nil(response.Data.ComplyflowURL)
		s.Equal("<EMAIL>", *response.Data.Email)
		s.Equal("kryptogo", *response.Data.LineID)
		s.Equal("kryptogo", *response.Data.Telegram)
		s.Nil(response.Data.Discord)
		s.Nil(response.Data.Twitter)
		s.Equal("KryptoGO marketplace", response.Data.Title)
		s.Equal("https://static.kryptogo.com/logo/market/kryptogo.png", response.Data.Logo)
		s.Equal("bank_transfer", response.Data.PaymentMethod)
		s.Equal("TWD", response.Data.PaymentCurrency)
		s.Equal("Mega Bank", *response.Data.BankName)
		s.Equal("Xin Yi", *response.Data.BranchName)
		s.Equal("168", *response.Data.BankAccount)
		s.Equal("KryptoGO 168", *response.Data.AccountHolderName)
	}

	db := rdb.Get()

	s.NoError(db.Create(&model.StudioOrganizationClient{
		OrganizationID:  1,
		ClientID:        "kryptogo-complyflow",
		ApplicationType: util.Ptr(string(domain.OAuthApplicationTypeComplyflow)),
		OAuthClientConfig: model.OAuthClientConfig{
			ID:     "kryptogo-complyflow",
			Domain: "complyflow.kryptogo.com",
			Name:   "kryptogo-complyflow",
		},
	}).Error)

	{
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/asset_pro/market_config?market_code=kryptogo", nil,
		)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal(1, response.Data.OrganizationID)
		s.Equal("kryptogo-asset-pro-market", response.Data.OauthClientID)
		s.Equal("KryptoGO marketplace", *response.Data.Introduction)
		s.Equal("<EMAIL>", *response.Data.Email)
		s.Equal("https://complyflow.kryptogo.com", *response.Data.ComplyflowURL)
		s.Equal("kryptogo", *response.Data.LineID)
		s.Equal("kryptogo", *response.Data.Telegram)
		s.Nil(response.Data.Discord)
		s.Nil(response.Data.Twitter)
		s.Equal("KryptoGO marketplace", response.Data.Title)
		s.Equal("https://static.kryptogo.com/logo/market/kryptogo.png", response.Data.Logo)
		s.Equal("bank_transfer", response.Data.PaymentMethod)
		s.Equal("TWD", response.Data.PaymentCurrency)
		s.Equal("Mega Bank", *response.Data.BankName)
		s.Equal("Xin Yi", *response.Data.BranchName)
		s.Equal("168", *response.Data.BankAccount)
		s.Equal("KryptoGO 168", *response.Data.AccountHolderName)
	}
}

func (s *testMarketSuite) TestGetMarketInfoForCustomerNotEnabled() {
	type resp struct {
		Code int      `json:"code"`
		Data struct{} `json:"data"`
	}

	{
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/asset_pro/market_config?market_code=stickey", nil,
		)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(code.StudioMarketNotEnabled, response.Code)
	}
}

func (s *testMarketSuite) TestGetMarketInfoForCustomerNotFound() {
	type resp struct {
		Code int      `json:"code"`
		Data struct{} `json:"data"`
	}

	{
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/asset_pro/market_config?market_code=kryptogo2", nil,
		)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(code.StudioMarketNotFound, response.Code)
	}
}

func (s *testMarketSuite) TestUpsertMarketInfoParamError() {
	type request struct {
		OauthClientID        string  `json:"oauth_client_id"`
		Title                string  `json:"title"`
		Logo                 string  `json:"logo"`
		Introduction         *string `json:"introduction"`
		PaymentMethod        string  `json:"payment_method"`
		PaymentCurrency      string  `json:"payment_currency"`
		BankName             *string `json:"bank_name"`
		BranchName           *string `json:"branch_name"`
		BankAccount          *string `json:"bank_account"`
		AccountHolderName    *string `json:"bank_account_holder_name"`
		PaymentExpirationSec int     `json:"payment_expiration_sec"`
	}

	{ // without column
		w := httptest.NewRecorder()

		bs, _ := json.Marshal(request{})

		req, _ := http.NewRequest(http.MethodPut,
			"/_v/studio/organization/999/asset_pro/market", bytes.NewBuffer(bs))

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)
	}
	{ // wrong expiration seconds
		w := httptest.NewRecorder()

		bs, _ := json.Marshal(request{
			OauthClientID:        "55688",
			Title:                "title",
			Logo:                 "logo",
			Introduction:         util.Ptr("intro"),
			PaymentMethod:        "bank_transfer",
			PaymentCurrency:      "TWD",
			BankName:             util.Ptr("Mega Bank"),
			BranchName:           util.Ptr("Xin Yi"),
			BankAccount:          util.Ptr("168"),
			AccountHolderName:    util.Ptr("KryptoGO 168"),
			PaymentExpirationSec: 10,
		})

		req, _ := http.NewRequest(http.MethodPut,
			"/_v/studio/organization/999/asset_pro/market", bytes.NewBuffer(bs))

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)
	}
}

func (s *testMarketSuite) TestUpsertMarketInfoBrandNew() {
	type resp struct {
		Code int      `json:"code"`
		Data struct{} `json:"data"`
	}

	db := rdb.Get()

	s.NoError(db.Create(&model.OAuthClientConfig{
		ID:     "TestUpsertMarketInfoBrandNew",
		Domain: "store-dev.kryptogo.com",
		Name:   "TestUpsertMarketInfoBrandNew",
	}).Error)

	var marketExist bool
	s.NoError(db.Model(&model.StudioMarket{}).
		Select("count(*) = 1").
		Where("organization_id = ?", 3).
		Scan(&marketExist).Error)
	s.False(marketExist)

	var countOfMarketInfo, countOfStudioClient, countOfProducts int64
	s.NoError(db.Model(&model.StudioMarket{}).Count(&countOfMarketInfo).Error)
	s.NoError(db.Model(&model.StudioOrganizationClient{}).Count(&countOfStudioClient).Error)
	s.NoError(db.Model(&model.AssetProProduct{}).
		Where("organization_id = ?", 3).Count(&countOfProducts).Error)
	s.Zero(countOfProducts)

	type request struct {
		OauthClientID        string  `json:"oauth_client_id"`
		Title                string  `json:"title"`
		Logo                 string  `json:"logo"`
		MarketCode           string  `json:"market_code"`
		Introduction         *string `json:"introduction"`
		PaymentMethod        string  `json:"payment_method"`
		PaymentCurrency      string  `json:"payment_currency"`
		BankName             *string `json:"bank_name"`
		BranchName           *string `json:"branch_name"`
		BankAccount          *string `json:"bank_account"`
		AccountHolderName    *string `json:"bank_account_holder_name"`
		PaymentExpirationSec int     `json:"payment_expiration_sec"`
	}

	{
		w := httptest.NewRecorder()

		bs, _ := json.Marshal(request{
			OauthClientID:        "TestUpsertMarketInfoBrandNew",
			Title:                "title",
			Logo:                 "logo",
			MarketCode:           "brand_new",
			Introduction:         util.Ptr("intro"),
			PaymentMethod:        "bank_transfer",
			PaymentCurrency:      "TWD",
			BankName:             util.Ptr("Mega Bank"),
			BranchName:           util.Ptr("Xin Yi"),
			BankAccount:          util.Ptr("168"),
			AccountHolderName:    util.Ptr("KryptoGO 168"),
			PaymentExpirationSec: 600,
		})

		req, _ := http.NewRequest(http.MethodPut,
			"/_v/studio/organization/3/asset_pro/market", bytes.NewBuffer(bs))

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(code.StudioOrganizationClientNotFound, response.Code)
	}

	var postCountOfMarketInfo, postCuntOfStudioClient, postCountOfProducts int64
	s.NoError(db.Model(&model.StudioMarket{}).Count(&postCountOfMarketInfo).Error)
	s.NoError(db.Model(&model.StudioOrganizationClient{}).Count(&postCuntOfStudioClient).Error)
	s.NoError(db.Model(&model.AssetProProduct{}).
		Where("organization_id = ?", 3).Count(&postCountOfProducts).Error)
	s.NotEqual(0, postCountOfMarketInfo)
	s.NotEqual(0, postCuntOfStudioClient)
	s.Equal(countOfMarketInfo, postCountOfMarketInfo)
	s.Equal(countOfStudioClient, postCuntOfStudioClient)

	s.NoError(db.Create(&model.StudioOrganizationClient{
		OrganizationID:  3,
		ClientID:        "TestUpsertMarketInfoBrandNew",
		ApplicationType: util.Ptr(string(domain.OAuthApplicationTypeMarket)),
	}).Error)

	{
		w := httptest.NewRecorder()

		bs, _ := json.Marshal(request{
			OauthClientID:        "TestUpsertMarketInfoBrandNew",
			Title:                "title",
			Logo:                 "logo",
			MarketCode:           "brand_new",
			Introduction:         util.Ptr("intro"),
			PaymentMethod:        "bank_transfer",
			PaymentCurrency:      "TWD",
			BankName:             util.Ptr("Mega Bank"),
			BranchName:           util.Ptr("Xin Yi"),
			BankAccount:          util.Ptr("168"),
			AccountHolderName:    util.Ptr("KryptoGO 168"),
			PaymentExpirationSec: 600,
		})

		req, _ := http.NewRequest(http.MethodPut,
			"/_v/studio/organization/3/asset_pro/market", bytes.NewBuffer(bs))

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)
	}

	s.NoError(db.Model(&model.StudioMarket{}).Count(&postCountOfMarketInfo).Error)
	s.NoError(db.Model(&model.StudioOrganizationClient{}).Count(&postCuntOfStudioClient).Error)
	s.NoError(db.Model(&model.AssetProProduct{}).
		Where("organization_id = ?", 3).Count(&postCountOfProducts).Error)
	s.NotEqual(0, postCountOfMarketInfo)
	s.NotEqual(0, postCuntOfStudioClient)
	s.NotEqual(countOfMarketInfo, postCountOfMarketInfo)
	s.NotZero(postCountOfProducts)
}

func (s *testMarketSuite) TestUpsertMarketInfoExist() {
	type resp struct {
		Code int      `json:"code"`
		Data struct{} `json:"data"`
	}

	db := rdb.Get()

	s.NoError(db.Create(&model.StudioOrganization{
		ID: 4,
	}).Error)

	s.NoError(db.Create(&model.StudioMarket{
		OrganizationID:       4,
		ClientID:             util.RandString(50),
		MarketCode:           util.RandString(50),
		Title:                "title-4",
		MarketURL:            "https://example.com",
		Logo:                 "logo",
		Introduction:         util.Ptr("intro"),
		PaymentMethod:        "bank_transfer",
		PaymentCurrency:      "TWD",
		BankName:             util.Ptr("Mega Bank"),
		BranchName:           util.Ptr("Xin Yi"),
		BankAccount:          util.Ptr("168"),
		AccountHolderName:    util.Ptr("KryptoGO 168"),
		PaymentExpirationSec: 600,
	}).Error)

	s.NoError(db.Create(&model.StudioOrganizationClient{
		ClientID:        "TestUpsertMarketInfoExist",
		OrganizationID:  4,
		ApplicationType: util.Ptr(string(domain.OAuthApplicationTypeMarket)),
		OAuthClientConfig: model.OAuthClientConfig{
			ID:     "TestUpsertMarketInfoExist",
			Domain: "store-dev-exist.kryptogo.com",
			Name:   "TestUpsertMarketInfoExist",
		},
	}).Error)

	type request struct {
		OauthClientID        string  `json:"oauth_client_id"`
		Title                string  `json:"title"`
		Logo                 string  `json:"logo"`
		MarketCode           *string `json:"market_code"`
		Introduction         *string `json:"introduction"`
		PaymentMethod        string  `json:"payment_method"`
		PaymentCurrency      string  `json:"payment_currency"`
		BankName             string  `json:"bank_name"`
		BranchName           string  `json:"branch_name"`
		BankAccount          string  `json:"bank_account"`
		AccountHolderName    string  `json:"bank_account_holder_name"`
		PaymentExpirationSec int     `json:"payment_expiration_sec"`
	}

	{ // oauth config not found, will not create
		w := httptest.NewRecorder()

		bs, _ := json.Marshal(request{
			OauthClientID:        "TestUpsertMarketInfoExist2",
			Title:                "title-4-2",
			Logo:                 "logo2",
			MarketCode:           util.Ptr("exist2"),
			Introduction:         util.Ptr("intro2"),
			PaymentMethod:        "bank_transfer",
			PaymentCurrency:      "TWD",
			PaymentExpirationSec: 86400,
			BankName:             "TestUpsertMarketInfoExist2-BankName",
			BranchName:           "TestUpsertMarketInfoExist2-BranchName",
			BankAccount:          "TestUpsertMarketInfoExist2-BankAccount",
			AccountHolderName:    "TestUpsertMarketInfoExist2-AccountHolderName",
		})

		req, _ := http.NewRequest(http.MethodPut,
			"/_v/studio/organization/4/asset_pro/market", bytes.NewBuffer(bs))

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(code.OAuthConfigNotFound, response.Code)
	}

	s.NoError(db.Create(&model.OAuthClientConfig{
		ID:     "TestUpsertMarketInfoExistAnotherOrg",
		Domain: "store-dev.kryptogo.com",
		Name:   "TestUpsertMarketInfoExistAnotherOrg",
	}).Error)

	s.NoError(db.Create(&model.StudioOrganizationClient{
		ClientID:        "TestUpsertMarketInfoExistAnotherOrg",
		OrganizationID:  4,
		ApplicationType: util.Ptr(string(domain.OAuthApplicationTypeMarket)),
	}).Error)

	s.NoError(db.Create(&model.OAuthClientConfig{
		ID:     "TestUpsertMarketInfoExist2",
		Domain: "store-dev.kryptogo.com",
		Name:   "TestUpsertMarketInfoExist2",
	}).Error)

	{ // init but no provide oauth client id
		var countOfMarketInfo, countOfStudioClient int64
		s.NoError(db.Model(&model.StudioMarket{}).Count(&countOfMarketInfo).Error)
		s.NoError(db.Model(&model.StudioOrganizationClient{}).Count(&countOfStudioClient).Error)

		w := httptest.NewRecorder()

		bs, _ := json.Marshal(request{
			// OauthClientID:        "TestUpsertMarketInfoExist2",
			Title:                "title2",
			Logo:                 "logo2",
			Introduction:         util.Ptr("intro2"),
			PaymentMethod:        "bank_transfer",
			PaymentCurrency:      "TWD",
			PaymentExpirationSec: 86400,
		})

		req, _ := http.NewRequest(http.MethodPut,
			"/_v/studio/organization/4/asset_pro/market", bytes.NewBuffer(bs))

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(code.ParamIncorrect, response.Code)
	}
}

func (s *testMarketSuite) TestSaveMarketInfo() {

	type request struct {
		Title                string  `json:"title" binding:"required"`
		Logo                 string  `json:"logo" binding:"required"`
		Email                *string `json:"email"`
		Phone                *string `json:"phone"`
		LineID               *string `json:"line_id"`
		Telegram             *string `json:"telegram"`
		Discord              *string `json:"discord" `
		Twitter              *string `json:"twitter"`
		Introduction         *string `json:"introduction"`
		PaymentExpirationSec int     `json:"payment_expiration_sec" binding:"required,gte=600,lte=86400"`
		PaymentMethod        string  `json:"payment_method" binding:"required"`
		PaymentCurrency      string  `json:"payment_currency" binding:"required"`
		BankName             string  `json:"bank_name" binding:"required"`
		BranchName           string  `json:"branch_name" binding:"required"`
		BankAccount          string  `json:"bank_account" binding:"required"`
		AccountHolderName    string  `json:"bank_account_holder_name" binding:"required"`
	}

	type resp struct {
		Code int `json:"code"`
	}

	{ // duplicated title
		w := httptest.NewRecorder()
		bs, _ := json.Marshal(request{
			Title:                "KryptoGO marketplace", // duplicated
			Logo:                 "logo",
			Email:                util.Ptr("email"),
			Phone:                util.Ptr("phone"),
			LineID:               util.Ptr("line_id"),
			Telegram:             util.Ptr("telegram"),
			Discord:              util.Ptr("discord"),
			Twitter:              util.Ptr("twitter"),
			Introduction:         util.Ptr("intro"),
			PaymentExpirationSec: 86400,
			PaymentMethod:        "bank_transfer",
			PaymentCurrency:      "TWD",
			BankName:             "TestSaveMarketInfo-BankName",
			BranchName:           "TestSaveMarketInfo-BranchName",
			BankAccount:          "TestSaveMarketInfo-BankAccount",
			AccountHolderName:    "TestSaveMarketInfo-AccountHolderName",
		})

		req, _ := http.NewRequest(http.MethodPut,
			"/v1/studio/organization/5/asset_pro/market", bytes.NewBuffer(bs))

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(code.StudioMarketTitleDuplicated, response.Code)
	}
	{ // normal update
		w := httptest.NewRecorder()
		bs, _ := json.Marshal(request{
			Title:                "tag-tag",
			Logo:                 "tag-logo",
			Email:                util.Ptr("tag-email"),
			Phone:                util.Ptr("tag-phone"),
			LineID:               util.Ptr("tag-line_id"),
			Telegram:             util.Ptr("tag-telegram"),
			Discord:              util.Ptr("tag-discord"),
			Twitter:              util.Ptr("tag-twitter"),
			Introduction:         util.Ptr("tag-intro"),
			PaymentExpirationSec: 600,
			PaymentMethod:        "bank_transfer",
			PaymentCurrency:      "TWD",
			BankName:             "TestSaveMarketInfo-BankName",
			BranchName:           "TestSaveMarketInfo-BranchName",
			BankAccount:          "TestSaveMarketInfo-BankAccount",
			AccountHolderName:    "TestSaveMarketInfo-AccountHolderName",
		})

		req, _ := http.NewRequest(http.MethodPut,
			"/v1/studio/organization/5/asset_pro/market", bytes.NewBuffer(bs))

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		{
			type resp struct {
				Code int `json:"code"`
				Data struct {
					MarketURL            string  `json:"market_url"`
					Title                string  `json:"title"`
					Logo                 string  `json:"logo"`
					Introduction         *string `json:"introduction"`
					Email                *string `json:"email"`
					Phone                *string `json:"phone"`
					LineID               *string `json:"line_id"`
					Telegram             *string `json:"telegram"`
					Discord              *string `json:"discord"`
					Twitter              *string `json:"twitter"`
					PaymentMethod        string  `json:"payment_method"`
					PaymentCurrency      string  `json:"payment_currency"`
					BankName             *string `json:"bank_name"`
					BranchName           *string `json:"branch_name"`
					BankAccount          *string `json:"bank_account"`
					AccountHolderName    *string `json:"bank_account_holder_name"`
					PaymentExpirationSec int     `json:"payment_expiration_sec"`
				} `json:"data"`
			}

			{
				w := httptest.NewRecorder()

				req, _ := http.NewRequest(http.MethodGet,
					"/v1/studio/organization/5/asset_pro/market", nil,
				)

				s.ginEngine.ServeHTTP(w, req)
				s.Equal(http.StatusOK, w.Code)

				var response resp
				s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
				s.Equal(0, response.Code)

				s.Equal("tag-tag", response.Data.Title)
				s.Equal("tag-logo", response.Data.Logo)
				s.Equal("https://store-dev.kryptogo.com/tag", response.Data.MarketURL)
				s.Equal("tag-intro", *response.Data.Introduction)
				s.Equal("tag-email", *response.Data.Email)
				s.Equal("tag-line_id", *response.Data.LineID)
				s.Equal("tag-phone", *response.Data.Phone)
				s.Equal("tag-telegram", *response.Data.Telegram)
				s.Equal("tag-discord", *response.Data.Discord)
				s.Equal("tag-twitter", *response.Data.Twitter)
				s.Equal("bank_transfer", response.Data.PaymentMethod)
				s.Equal("TWD", response.Data.PaymentCurrency)
				s.Equal("TestSaveMarketInfo-BankName", *response.Data.BankName)
				s.Equal("TestSaveMarketInfo-BranchName", *response.Data.BranchName)
				s.Equal("TestSaveMarketInfo-BankAccount", *response.Data.BankAccount)
				s.Equal("TestSaveMarketInfo-AccountHolderName", *response.Data.AccountHolderName)
			}
		}
	}
	{ // clear update
		w := httptest.NewRecorder()
		bs, _ := json.Marshal(request{
			Title:                "tag-tag",
			Logo:                 "tag-logo",
			PaymentExpirationSec: 600,
			PaymentMethod:        "bank_transfer",
			PaymentCurrency:      "TWD",
			BankName:             "TestSaveMarketInfo-BankName",
			BranchName:           "TestSaveMarketInfo-BranchName",
			BankAccount:          "TestSaveMarketInfo-BankAccount",
			AccountHolderName:    "TestSaveMarketInfo-AccountHolderName",
		})

		req, _ := http.NewRequest(http.MethodPut,
			"/v1/studio/organization/5/asset_pro/market", bytes.NewBuffer(bs))

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		{
			type resp struct {
				Code int `json:"code"`
				Data struct {
					MarketURL            string  `json:"market_url"`
					Title                string  `json:"title"`
					Logo                 string  `json:"logo"`
					Introduction         *string `json:"introduction"`
					Email                *string `json:"email"`
					Phone                *string `json:"phone"`
					LineID               *string `json:"line_id"`
					Telegram             *string `json:"telegram"`
					Discord              *string `json:"discord"`
					Twitter              *string `json:"twitter"`
					PaymentMethod        string  `json:"payment_method"`
					PaymentCurrency      string  `json:"payment_currency"`
					BankName             *string `json:"bank_name"`
					BranchName           *string `json:"branch_name"`
					BankAccount          *string `json:"bank_account"`
					AccountHolderName    *string `json:"bank_account_holder_name"`
					PaymentExpirationSec int     `json:"payment_expiration_sec"`
				} `json:"data"`
			}

			{
				w := httptest.NewRecorder()

				req, _ := http.NewRequest(http.MethodGet,
					"/v1/studio/organization/5/asset_pro/market", nil,
				)

				s.ginEngine.ServeHTTP(w, req)
				s.Equal(http.StatusOK, w.Code)

				var response resp
				s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
				s.Equal(0, response.Code)

				s.Equal("tag-tag", response.Data.Title)
				s.Equal("https://store-dev.kryptogo.com/tag", response.Data.MarketURL)
				s.Equal("tag-logo", response.Data.Logo)
				s.Nil(response.Data.Introduction)
				s.Nil(response.Data.Email)
				s.Nil(response.Data.LineID)
				s.Nil(response.Data.Phone)
				s.Nil(response.Data.Telegram)
				s.Nil(response.Data.Discord)
				s.Nil(response.Data.Twitter)
				s.Equal("bank_transfer", response.Data.PaymentMethod)
				s.Equal("TWD", response.Data.PaymentCurrency)
				s.Equal("TestSaveMarketInfo-BankName", *response.Data.BankName)
				s.Equal("TestSaveMarketInfo-BranchName", *response.Data.BranchName)
				s.Equal("TestSaveMarketInfo-BankAccount", *response.Data.BankAccount)
				s.Equal("TestSaveMarketInfo-AccountHolderName", *response.Data.AccountHolderName)
			}
		}
	}

}

func TestMarketSuite(t *testing.T) {
	suite.Run(t, new(testMarketSuite))
}
