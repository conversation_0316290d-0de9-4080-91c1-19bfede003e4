package assetpro

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type approveOperation string

const (
	approveOperationApprove approveOperation = "approve"
	approveOperationReject  approveOperation = "reject"
)

type approveRequest struct {
	Operation approveOperation `json:"operation" binding:"required,oneof=approve reject"`
	Note      *string          `json:"note"`
}

// Approve approves or rejects an transfer application
func Approve(ctx *gin.Context) {
	var req approveRequest

	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	serialID := ctx.Param("serial_id")

	approvalRequest := assetpro.ApprovalRequest{
		SerialID:    serialID,
		OrgID:       ctx.GetInt("org_id"),
		OperatorUID: ctx.GetString("uid"),
	}

	if req.Operation == approveOperationApprove {
		kgErr = assetpro.Approve(ctx, approvalRequest)
	} else {
		if req.Note == nil {
			response.KGError(ctx, code.NewKGError(code.ParamIncorrect,
				http.StatusBadRequest, errors.New("note is required"), nil))
			return
		}

		kgErr = assetpro.Reject(ctx, assetpro.RejectRequest{
			ApprovalRequest: approvalRequest,
			Note:            *req.Note,
		})
	}

	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, nil)
}

type releaseOperation string

const (
	releaseOperationRelease releaseOperation = "release"
	releaseOperationReject  releaseOperation = "reject"
)

type releaseRequest struct {
	Operation releaseOperation `json:"operation" binding:"required,oneof=release reject"`
	Note      *string          `json:"note"`
}

// Release release or rejects an transfer application
func Release(ctx *gin.Context) {
	var req releaseRequest

	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	serialID := ctx.Param("serial_id")

	approvalRequest := assetpro.ApprovalRequest{
		SerialID:    serialID,
		OrgID:       ctx.GetInt("org_id"),
		OperatorUID: ctx.GetString("uid"),
	}

	if req.Operation == releaseOperationRelease {
		kgErr = assetpro.Release(ctx, approvalRequest)
	} else {
		if req.Note == nil {
			response.KGError(ctx, code.NewKGError(code.ParamIncorrect,
				http.StatusBadRequest, errors.New("note is required"), nil))
			return
		}

		kgErr = assetpro.Reject(ctx, assetpro.RejectRequest{
			ApprovalRequest: approvalRequest,
			Note:            *req.Note,
		})
	}

	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, nil)
}
