package assetpro

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/coingecko"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	assetpro "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func setupAssetProLiquidity(t *testing.T) (testPhone, testEmail string) {
	testPhone = util.RandPhone()
	testEmail = util.RandEmail()
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	assert.Nil(t, rdbtest.CreateTokenMetadata(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAssetProLiquidities(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAssetProProfitRates(rdb.Get()))
	assert.Nil(t, rbac.Init(context.Background()))
	assetpro.InitFinance(assetpro.InitParam{
		AssetProFinanceRepo: rdb.GormRepo(),
	})
	organization.Init(organization.InitParam{
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	})
	tx.Init(repo.Unified())
	return
}

func TestGetLiquidity(t *testing.T) {
	s := assert.New(t)
	orgID := 1

	// Seed the database
	setupAssetProLiquidity(t)

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/liquidities", auth.MockOrgID(orgID), GetLiquidities)

	// Test GetLiquidities
	t.Run("GetLiquidities", func(t *testing.T) {
		// mock coingecko
		ctrl := gomock.NewController(t)
		m := coingecko.NewMockIService(ctrl)
		m.EXPECT().QuotesInUSD(gomock.Any(), gomock.Any()).Return(map[string]float64{
			"tether": 1,
			"tron":   0.1,
		}, nil)
		coingecko.Set(m)

		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/liquidities", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code int         `json:"code"`
			Data []liquidity `json:"data"`
		}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		s.NoError(err)
		s.Equal(0, response.Code)
		s.NotZero(len(response.Data))

		// Check some values
		s.Equal("buy_crypto", response.Data[0].LiquidityType)
		s.Equal("USDT", response.Data[0].Symbol)
		s.Equal("tron", response.Data[0].ChainID)
		s.Equal("https://example.com/usdt", response.Data[0].TokenURL)
		s.Equal("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", *response.Data[0].ContractAddress)
		s.Equal("1", response.Data[0].Price)
		s.Equal("USD", response.Data[0].Unit)
		s.Equal(0.03, response.Data[0].ProfitMargin)
		s.Equal("1000000", *response.Data[0].AlertThreshold)

		s.Equal("gas_swap", response.Data[1].LiquidityType)
		s.Equal("TRX", response.Data[1].Symbol)
		s.Equal("tron", response.Data[1].ChainID)
		s.Equal("https://example.com/trx", response.Data[1].TokenURL)
		s.Nil(response.Data[1].ContractAddress)
		s.Equal("0.1", response.Data[1].Price)
		s.Equal("USDT", response.Data[1].Unit)
		s.Equal(0.02, response.Data[1].ProfitMargin)
		s.Equal("1000", *response.Data[1].AlertThreshold)
	})
}

func TestGetAndUpdateProfitRate(t *testing.T) {
	s := assert.New(t)
	r := gin.Default()

	rdb.Reset()
	s.NoError(rdbtest.CreateStudioOrganizations(rdb.Get()))
	assetpro.InitFinance(assetpro.InitParam{
		AssetProFinanceRepo: rdb.GormRepo(),
	})

	r.GET("/v1/studio/organization/:org_id/asset_pro/profit_rates", auth.MockOrgID(1), GetProfitRates)
	r.POST("/v1/studio/organization/:org_id/asset_pro/profit_rates", auth.MockOrgID(1), UpsertProfitRate)
	r.POST("/_v/organization/:orgID/asset_pro/profit_rates", auth.MockOrgID(1), InternalUpsertProfitRate)

	t.Run("GetProfitRate", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/profit_rates", nil)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code int `json:"code"`
			Data []struct {
				Service    string  `json:"service"`
				ProfitRate float64 `json:"profit_rate"`
			} `json:"data"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		s.NoError(err)
		s.Equal(0, response.Code)
		s.Len(response.Data, 7)

		s.Equal("buy", response.Data[0].Service)
		s.Zero(response.Data[0].ProfitRate)
		s.Equal("swap_gas", response.Data[1].Service)
		s.Zero(response.Data[1].ProfitRate)
		s.Equal("swap_defi", response.Data[2].Service)
		s.Zero(response.Data[2].ProfitRate)
		s.Equal("bridge", response.Data[3].Service)
		s.Zero(response.Data[3].ProfitRate)
		s.Equal("send_with_fee", response.Data[4].Service)
		s.Zero(response.Data[4].ProfitRate)
		s.Equal("send_gasless", response.Data[5].Service)
		s.Zero(response.Data[5].ProfitRate)
		s.Equal("send_batch", response.Data[6].Service)
		s.Zero(response.Data[6].ProfitRate)
	})

	t.Run("UpsertProfitRateSuccess", func(t *testing.T) {
		{
			w := httptest.NewRecorder()
			body := map[string]interface{}{
				"service":     "buy",
				"profit_rate": 0.03,
			}
			bodyStr, _ := json.Marshal(body)
			req, _ := http.NewRequest(http.MethodPost,
				"/v1/studio/organization/1/asset_pro/profit_rates", strings.NewReader(string(bodyStr)))
			r.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
			var response struct {
				Code int `json:"code"`
			}

			s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
			s.Equal(0, response.Code)
		}
		{
			w := httptest.NewRecorder()
			req, _ := http.NewRequest(http.MethodGet,
				"/v1/studio/organization/1/asset_pro/profit_rates", nil)
			r.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
			var response struct {
				Code int `json:"code"`
				Data []struct {
					Service    string  `json:"service"`
					ProfitRate float64 `json:"profit_rate"`
				} `json:"data"`
			}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			s.NoError(err)
			s.Equal(0, response.Code)
			s.Len(response.Data, 7)

			s.Equal("buy", response.Data[0].Service)
			s.Equal(0.03, response.Data[0].ProfitRate)
			s.Equal("swap_gas", response.Data[1].Service)
			s.Zero(response.Data[1].ProfitRate)
			s.Equal("swap_defi", response.Data[2].Service)
			s.Zero(response.Data[2].ProfitRate)
			s.Equal("bridge", response.Data[3].Service)
			s.Zero(response.Data[3].ProfitRate)
			s.Equal("send_with_fee", response.Data[4].Service)
			s.Zero(response.Data[4].ProfitRate)
			s.Equal("send_gasless", response.Data[5].Service)
			s.Zero(response.Data[5].ProfitRate)
			s.Equal("send_batch", response.Data[6].Service)
			s.Zero(response.Data[6].ProfitRate)
		}
	})

	t.Run("UpsertProfitRateFailed-InvalidService", func(t *testing.T) {
		w := httptest.NewRecorder()
		body := map[string]interface{}{
			"service":     "55688",
			"profit_rate": 0.03,
		}
		bodyStr, _ := json.Marshal(body)
		req, _ := http.NewRequest(http.MethodPost,
			"/v1/studio/organization/1/asset_pro/profit_rates", strings.NewReader(string(bodyStr)))
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
	t.Run("UpsertProfitRateFailed-WithoutProfitRate", func(t *testing.T) {
		w := httptest.NewRecorder()
		body := map[string]interface{}{
			"service": "55688",
		}
		bodyStr, _ := json.Marshal(body)
		req, _ := http.NewRequest(http.MethodPost,
			"/v1/studio/organization/1/asset_pro/profit_rates", strings.NewReader(string(bodyStr)))
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
	t.Run("UpsertProfitRateFailed-ProfitRateOutOfRange", func(t *testing.T) {
		w := httptest.NewRecorder()
		body := map[string]interface{}{
			"service":     "buy",
			"profit_rate": 1.5,
		}
		bodyStr, _ := json.Marshal(body)
		req, _ := http.NewRequest(http.MethodPost,
			"/v1/studio/organization/1/asset_pro/profit_rates", strings.NewReader(string(bodyStr)))
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
	t.Run("UpsertProfitRateFailed-ProfitRateNegative", func(t *testing.T) {
		w := httptest.NewRecorder()
		body := map[string]interface{}{
			"service":     "buy",
			"profit_rate": -1,
		}
		bodyStr, _ := json.Marshal(body)
		req, _ := http.NewRequest(http.MethodPost,
			"/v1/studio/organization/1/asset_pro/profit_rates", strings.NewReader(string(bodyStr)))
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
	t.Run("UpsertProfitRateFailed-ProfitRateDefiSwapOutOfRange", func(t *testing.T) {
		w := httptest.NewRecorder()
		body := map[string]interface{}{
			"service":     "swap_defi",
			"profit_rate": 0.05,
		}
		bodyStr, _ := json.Marshal(body)
		req, _ := http.NewRequest(http.MethodPost,
			"/v1/studio/organization/1/asset_pro/profit_rates", strings.NewReader(string(bodyStr)))
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("GetProfitRateAfterUpsertProfitRateAgain", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/profit_rates", nil)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code int `json:"code"`
			Data []struct {
				Service    string  `json:"service"`
				ProfitRate float64 `json:"profit_rate"`
			} `json:"data"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		s.NoError(err)
		s.Equal(0, response.Code)
		s.Len(response.Data, 7)

		s.Equal("buy", response.Data[0].Service)
		s.Equal(0.03, response.Data[0].ProfitRate)
		s.Equal("swap_gas", response.Data[1].Service)
		s.Zero(response.Data[1].ProfitRate)
		s.Equal("swap_defi", response.Data[2].Service)
		s.Zero(response.Data[2].ProfitRate)
		s.Equal("bridge", response.Data[3].Service)
		s.Zero(response.Data[3].ProfitRate)
		s.Equal("send_with_fee", response.Data[4].Service)
		s.Zero(response.Data[4].ProfitRate)
		s.Equal("send_gasless", response.Data[5].Service)
		s.Zero(response.Data[5].ProfitRate)
		s.Equal("send_batch", response.Data[6].Service)
		s.Zero(response.Data[6].ProfitRate)
	})
}
