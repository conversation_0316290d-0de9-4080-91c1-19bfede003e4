package assetpro

import (
	"context"
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	request "github.com/kryptogo/kg-wallet-backend/api/paging"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

type operator struct {
	UID                       string   `json:"uid"`
	Name                      string   `json:"name"`
	MemberID                  string   `json:"member_id"`
	Email                     string   `json:"email"`
	Roles                     []string `json:"roles"`
	DailyTransferLimit        float64  `json:"daily_transfer_limit"`
	TransferApprovalThreshold float64  `json:"transfer_approval_threshold"`
}

func (o *operator) fromDomainStudioUser(studioUser *domain.StudioUser) {
	o.UID = studioUser.UID
	o.Name = studioUser.Name
	o.MemberID = util.Val(studioUser.MemberID)
	o.Email = studioUser.Email
	o.Roles = lo.FilterMap(studioUser.Roles, func(role domain.StudioRole, _ int) (string, bool) {
		if role.Module == "asset_pro" || role.Name == "owner" {
			return role.Name, true
		}

		return "", false
	})

	dailyTransferLimit, _ := studioUser.TransferLimitation.DailyTransferLimit.Float64()
	o.DailyTransferLimit = dailyTransferLimit

	transferApprovalThreshold, _ := studioUser.TransferLimitation.TransferApprovalThreshold.Float64()
	o.TransferApprovalThreshold = transferApprovalThreshold
}

type listOperatorsRequest struct {
	request.PagingRequest
}

// ListOperators lists asset pro operators by query string.
func ListOperators(ctx *gin.Context) {
	var req listOperatorsRequest
	kgErr := util.ToGinContextExt(ctx).BindQuery(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	query, kgError := req.ParseQuery([]string{"daily_transfer_limit", "transfer_approval_threshold"})
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	orgID := ctx.GetInt("org_id")
	domainUsers, servicePaging, kgError := organization.ListUsers(
		ctx.Request.Context(), orgID, []domain.StudioRole{
			{Module: "", Name: "owner"},
			{Module: "asset_pro", Name: "admin"},
			{Module: "asset_pro", Name: "trader"},
			{Module: "asset_pro", Name: "approver"},
		},
		query,
	)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	operators := lo.Map(domainUsers, func(u *domain.StudioUser, _ int) *operator {
		o := &operator{}
		o.fromDomainStudioUser(u)
		return o
	})

	response.OKWithPaging(ctx, operators, response.Paging{
		PageNumber: servicePaging.PageNumber,
		PageSize:   servicePaging.PageSize,
		TotalCount: servicePaging.TotalCount,
		PageSort:   string(servicePaging.PageSort),
	})
}

type updateOperatorRequest struct {
	DailyTransferLimit        *decimal.Decimal `json:"daily_transfer_limit"`
	TransferApprovalThreshold *decimal.Decimal `json:"transfer_approval_threshold"`
}

func (req updateOperatorRequest) validate(ctx context.Context, orgID int, uid string) *code.KGError {
	if req.DailyTransferLimit == nil && req.TransferApprovalThreshold == nil {
		return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("empty request body"), nil)
	}

	var (
		dailyTransferLimit        *decimal.Decimal
		transferApprovalThreshold *decimal.Decimal
	)

	if req.DailyTransferLimit != nil {
		if req.DailyTransferLimit.IsNegative() {
			return code.NewKGError(
				code.NumberCannotBeNegative,
				http.StatusBadRequest,
				errors.New("daily_transfer_limit must be equal or greater than zero"),
				nil,
			)
		}

		dailyTransferLimit = req.DailyTransferLimit
	}

	if req.TransferApprovalThreshold != nil {
		if req.TransferApprovalThreshold.IsNegative() {
			return code.NewKGError(
				code.NumberCannotBeNegative,
				http.StatusBadRequest,
				errors.New("transfer_approval_threshold must be equal or greater than zero"),
				nil,
			)
		}

		transferApprovalThreshold = req.TransferApprovalThreshold
	}

	if dailyTransferLimit == nil || transferApprovalThreshold == nil {
		// Get studio user
		studioUser, kgError := organization.GetUser(ctx, orgID, uid)
		if kgError != nil {
			return kgError
		}

		if dailyTransferLimit == nil {
			dailyTransferLimit = &studioUser.TransferLimitation.DailyTransferLimit
		}

		if transferApprovalThreshold == nil {
			transferApprovalThreshold = &studioUser.TransferLimitation.TransferApprovalThreshold
		}
	}

	if dailyTransferLimit == nil {
		return code.NewKGError(
			code.ParamIncorrect,
			http.StatusBadRequest,
			errors.New("daily_transfer_limit is required"),
			nil,
		)
	}

	// by pass update daily transfer limit
	if transferApprovalThreshold == nil {
		return nil
	}

	// dailyTransferLimit won't be nil here.
	if transferApprovalThreshold.GreaterThan(*dailyTransferLimit) {
		return code.NewKGError(
			code.DailyTransferLimitMustBeGreaterThanApproval,
			http.StatusBadRequest,
			errors.New("daily_transfer_limit must be equal or greater than transfer_approval_threshold"),
			nil,
		)
	}

	return nil
}

// UpdateOperator updates asset pro operator info.
func UpdateOperator(ctx *gin.Context) {
	var req updateOperatorRequest
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")
	userID := ctx.Param("uid")

	if kgError := req.validate(ctx.Request.Context(), orgID, userID); kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	kgError := organization.UpdateStudioUser(ctx.Request.Context(), organization.UpdateStudioUserParams{
		OrgID:                     orgID,
		UID:                       userID,
		DailyTransferLimit:        req.DailyTransferLimit,
		TransferApprovalThreshold: req.TransferApprovalThreshold,
	})

	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	response.OK(ctx, nil)
}
