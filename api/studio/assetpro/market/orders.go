package market

import (
	"encoding/json"
	"errors"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	request "github.com/kryptogo/kg-wallet-backend/api/paging"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/paging"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro/market"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

// CountOfPendingOrdersForMerchant returns the count of pending orders for a merchant
func CountOfPendingOrdersForMerchant(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")
	count, kgErr := market.CountOfPendingOrdersByOrg(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, gin.H{
		"count": count,
	})
}

type listOrdersForMerchantReq struct {
	request.PagingRequest

	query *paging.Query
}

func (req *listOrdersForMerchantReq) parse(ctx *gin.Context) *code.KGError {
	kgErr := util.ToGinContextExt(ctx).BindQuery(req)
	if kgErr != nil {
		return kgErr
	}

	pagingQuery, kgErr := req.ParseQuery([]string{"created_at", "total_cost"})
	if kgErr != nil {
		return kgErr
	}

	req.query = pagingQuery

	return nil
}

type orderForMerchant struct {
	CreateTime int64  `json:"create_time"`
	OrderID    string `json:"order_id"`
	Customer   struct {
		Name      string  `json:"name"`
		Phone     *string `json:"phone"`
		Email     *string `json:"email"`
		KycStatus string  `json:"kyc_status"`
	} `json:"customer"`
	Purchase struct {
		Amount       string          `json:"amount"`
		LogoURL      string          `json:"logo_url"`
		Name         string          `json:"name"`
		ChainID      string          `json:"chain_id"`
		BaseCurrency string          `json:"base_currency"`
		UsdPrice     decimal.Decimal `json:"usd_price"`
	} `json:"purchase"`
	TotalPrice struct {
		Amount        decimal.Decimal `json:"amount"`
		LogoURL       string          `json:"logo_url"`
		QuoteCurrency string          `json:"quote_currency"`
		UsdAmount     decimal.Decimal `json:"usd_amount"`
	} `json:"total_price"`
	PaymentStatus   string `json:"payment_status"`
	ShipmentDetails *struct {
		TxHash    string `json:"tx_hash"`
		ShippedAt int64  `json:"shipped_at"`
	} `json:"shipment_details"`
	ShippingStatus string `json:"shipping_status"`
	OrderStatus    string `json:"order_status"`
}

func (o *orderForMerchant) fromDomain(domainOrder *domain.AssetProOrder) {
	o.CreateTime = domainOrder.CreatedAt.Unix()
	o.OrderID = domainOrder.ID
	o.Customer.Name = util.Val(domainOrder.Customer.Name)
	o.Customer.Phone = domainOrder.Customer.Phone
	o.Customer.Email = domainOrder.Customer.Email
	o.Customer.KycStatus = domainOrder.Customer.KycStatus.String()
	o.Purchase.Amount = domainOrder.Purchase.Amount.String()
	o.Purchase.LogoURL = domainOrder.Purchase.LogoURL
	o.Purchase.Name = domainOrder.Purchase.Name
	o.Purchase.ChainID = domainOrder.Purchase.ChainID
	o.Purchase.BaseCurrency = domainOrder.Purchase.BaseCurrency.String()
	o.Purchase.UsdPrice = domainOrder.Purchase.UsdAmount
	o.TotalPrice.Amount = domainOrder.TotalPrice.Amount
	o.TotalPrice.LogoURL = domainOrder.TotalPrice.LogoURL
	o.TotalPrice.QuoteCurrency = domainOrder.TotalPrice.QuoteCurrency.String()
	o.TotalPrice.UsdAmount = domainOrder.TotalPrice.UsdAmount
	o.PaymentStatus = domainOrder.PaymentStatus.String()
	if domainOrder.ShipmentDetail != nil {
		o.ShipmentDetails = &struct {
			TxHash    string `json:"tx_hash"`
			ShippedAt int64  `json:"shipped_at"`
		}{
			TxHash:    domainOrder.ShipmentDetail.TxHash,
			ShippedAt: domainOrder.ShipmentDetail.ShippedAt.Unix(),
		}
	}
	o.ShippingStatus = domainOrder.ShippingStatus.String()
	o.OrderStatus = string(domainOrder.OrderStatus)
}

// ListOrdersForMerchant returns the orders for a merchant
func ListOrdersForMerchant(ctx *gin.Context) {
	req := &listOrdersForMerchantReq{
		PagingRequest: request.PagingRequest{
			PageSort: "created_at:d",
		},
	}
	if kgErr := req.parse(ctx); kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")

	orders, pagingResp, kgErr := market.ListOrders(ctx.Request.Context(), orgID, req.query)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	resp := lo.Map(orders, func(order *domain.AssetProOrder, _inviteeUID int) *orderForMerchant {
		o := &orderForMerchant{}
		o.fromDomain(order)
		return o
	})

	response.OKWithPaging(ctx, resp, response.Paging{
		PageNumber: pagingResp.PageNumber,
		PageSize:   pagingResp.PageSize,
		TotalCount: pagingResp.TotalCount,
		PageSort:   string(pagingResp.PageSort),
	})
}

type customerTransferred struct {
	QuoteCurrency string   `json:"quote_currency"`
	UpdatedAt     int64    `json:"updated_at"`
	Attachments   []string `json:"attachments"`
}
type paymentDetailsCustomer struct {
	Transferred customerTransferred `json:"transferred"`
}

type orderDetailForMerchant struct {
	CreateTime  int64  `json:"create_time"`
	CancelledAt *int64 `json:"cancelled_at"`
	OrderID     string `json:"order_id"`
	Customer    struct {
		Name          string  `json:"name"`
		Phone         *string `json:"phone"`
		Email         *string `json:"email"`
		KycStatus     string  `json:"kyc_status"`
		WalletAddress string  `json:"wallet_address"`
		BankAccount   *struct {
			BankName          string `json:"bank_name"`
			BranchName        string `json:"branch_name"`
			AccountNumber     string `json:"account_number"`
			AccountHolderName string `json:"account_holder_name"`
		} `json:"bank_account"`
	} `json:"customer"`
	Purchase struct {
		Amount       decimal.Decimal `json:"amount"`
		LogoURL      string          `json:"logo_url"`
		Name         string          `json:"name"`
		ChainID      string          `json:"chain_id"`
		BaseCurrency string          `json:"base_currency"`
		UsdAmount    decimal.Decimal `json:"usd_amount"`
		Price        decimal.Decimal `json:"price"`
	} `json:"purchase"`
	TotalPrice struct {
		Amount                decimal.Decimal `json:"amount"`
		LogoURL               string          `json:"logo_url"`
		QuoteCurrency         string          `json:"quote_currency"`
		UsdAmount             decimal.Decimal `json:"usd_amount"`
		HandlingFee           string          `json:"handling_fee"`
		HandlingFeePercentage string          `json:"handling_fee_percentage"`
	} `json:"total_price"`
	PaymentStatus   string `json:"payment_status"`
	ShipmentDetails *struct {
		TxHash      string `json:"tx_hash"`
		ShippedAt   int64  `json:"shipped_at"`
		DeliveredAt *int64 `json:"delivered_at"`
		TxID        int    `json:"tx_id"`
		SendTo      string `json:"send_to"`
		TxStatus    string `json:"tx_status"`
		ProcessedBy struct {
			Name       string  `json:"name"`
			Role       string  `json:"role"`
			ProfileImg *string `json:"profile_img"`
			Email      string  `json:"email"`
		} `json:"processed_by"`
	} `json:"shipment_details"`
	OrderStatus    string `json:"order_status"`
	PaymentDetails struct {
		Deadline   int64 `json:"deadline"`
		TransferTo struct {
			BankName          string `json:"bank_name"`
			BranchName        string `json:"branch_name"`
			AccountNumber     string `json:"account_number"`
			AccountHolderName string `json:"account_holder_name"`
		} `json:"transfer_to"`
		PaymentMethod        string                  `json:"payment_method"`
		Note                 *string                 `json:"note"`
		Attachments          []string                `json:"attachments"`
		CustomerTransferTime *int64                  `json:"customer_transfer_time"`
		LastFiveDigits       *string                 `json:"last_five_digits"`
		Customer             *paymentDetailsCustomer `json:"customer"`
		LastEditedAt         *int64                  `json:"last_edited_at"`
		TransferAmount       *decimal.Decimal        `json:"transfer_amount"`
		Editor               *struct {
			Name  string `json:"name"`
			Role  string `json:"role"`
			Email string `json:"email"`
		} `json:"editor"`
	} `json:"payment_details"`
	ShippingStatus string  `json:"shipping_status"`
	InternalNote   *string `json:"internal_note"`
}

func (o *orderDetailForMerchant) fromDomain(domainOrder *domain.AssetProOrder) {
	o.OrderID = domainOrder.ID
	o.CreateTime = domainOrder.CreatedAt.Unix()
	if domainOrder.CancelledAt != nil {
		o.CancelledAt = util.Ptr(domainOrder.CancelledAt.Unix())
	}
	o.Customer.Name = util.Val(domainOrder.Customer.Name)
	o.Customer.Phone = domainOrder.Customer.Phone
	o.Customer.Email = domainOrder.Customer.Email
	o.Customer.KycStatus = domainOrder.Customer.KycStatus.String()
	o.Customer.WalletAddress = domainOrder.Customer.WalletAddress
	if domainOrder.Customer.BankAccount != nil {
		o.Customer.BankAccount = &struct {
			BankName          string `json:"bank_name"`
			BranchName        string `json:"branch_name"`
			AccountNumber     string `json:"account_number"`
			AccountHolderName string `json:"account_holder_name"`
		}{
			BankName:          util.Val(domainOrder.Customer.BankAccount.BankName),
			BranchName:        util.Val(domainOrder.Customer.BankAccount.BranchName),
			AccountNumber:     util.Val(domainOrder.Customer.BankAccount.AccountNumber),
			AccountHolderName: util.Val(domainOrder.Customer.BankAccount.AccountHolderName),
		}
	}
	o.Purchase.Amount = domainOrder.Purchase.Amount
	o.Purchase.LogoURL = domainOrder.Purchase.LogoURL
	o.Purchase.Name = domainOrder.Purchase.Name
	o.Purchase.ChainID = domainOrder.Purchase.ChainID
	o.Purchase.BaseCurrency = domainOrder.Purchase.BaseCurrency.String()
	o.Purchase.UsdAmount = domainOrder.Purchase.UsdAmount
	o.Purchase.Price = domainOrder.Purchase.Price
	o.TotalPrice.Amount = domainOrder.TotalPrice.Amount
	o.TotalPrice.LogoURL = domainOrder.TotalPrice.LogoURL
	o.TotalPrice.QuoteCurrency = domainOrder.TotalPrice.QuoteCurrency.String()
	o.TotalPrice.UsdAmount = domainOrder.TotalPrice.UsdAmount
	o.TotalPrice.HandlingFee = domainOrder.TotalPrice.HandlingFee.String()
	handlingFeePercentage := decimal.Zero
	if domainOrder.TotalPrice.HandlingFeePercentage != nil {
		handlingFeePercentage = *domainOrder.TotalPrice.HandlingFeePercentage
	}
	o.TotalPrice.HandlingFeePercentage = handlingFeePercentage.String()
	o.PaymentStatus = domainOrder.PaymentStatus.String()
	if domainOrder.ShipmentDetail != nil {
		o.ShipmentDetails = &struct {
			TxHash      string `json:"tx_hash"`
			ShippedAt   int64  `json:"shipped_at"`
			DeliveredAt *int64 `json:"delivered_at"`
			TxID        int    `json:"tx_id"`
			SendTo      string `json:"send_to"`
			TxStatus    string `json:"tx_status"`
			ProcessedBy struct {
				Name       string  `json:"name"`
				Role       string  `json:"role"`
				ProfileImg *string `json:"profile_img"`
				Email      string  `json:"email"`
			} `json:"processed_by"`
		}{
			TxHash:    domainOrder.ShipmentDetail.TxHash,
			ShippedAt: domainOrder.ShipmentDetail.ShippedAt.Unix(),
			TxID:      domainOrder.ShipmentDetail.TxID,
			SendTo:    domainOrder.ShipmentDetail.SendTo,
			TxStatus:  domainOrder.ShipmentDetail.TxStatus,
			ProcessedBy: struct {
				Name       string  `json:"name"`
				Role       string  `json:"role"`
				ProfileImg *string `json:"profile_img"`
				Email      string  `json:"email"`
			}{
				Name:       domainOrder.ShipmentDetail.ProcessedBy.Name,
				Role:       domainOrder.ShipmentDetail.ProcessedBy.Role,
				ProfileImg: domainOrder.ShipmentDetail.ProcessedBy.ProfileImg,
				Email:      domainOrder.ShipmentDetail.ProcessedBy.Email,
			},
		}
		if domainOrder.ShipmentDetail.DeliveredAt != nil {
			o.ShipmentDetails.DeliveredAt = util.Ptr(domainOrder.ShipmentDetail.DeliveredAt.Unix())
		}
	}
	o.OrderStatus = string(domainOrder.OrderStatus)

	o.PaymentDetails.Deadline = domainOrder.PaymentDetail.Deadline.Unix()

	o.PaymentDetails.TransferTo.BankName = util.Val(domainOrder.PaymentDetail.TransferTo.BankName)
	o.PaymentDetails.TransferTo.BranchName = util.Val(domainOrder.PaymentDetail.TransferTo.BranchName)
	o.PaymentDetails.TransferTo.AccountNumber = util.Val(domainOrder.PaymentDetail.TransferTo.AccountNumber)
	o.PaymentDetails.TransferTo.AccountHolderName = util.Val(domainOrder.PaymentDetail.TransferTo.AccountHolderName)
	o.PaymentDetails.PaymentMethod = domainOrder.PaymentDetail.PaymentMethod
	o.PaymentDetails.Note = domainOrder.PaymentDetail.Note
	o.PaymentDetails.Attachments = domainOrder.PaymentDetail.Attachments
	if domainOrder.PaymentDetail.CustomerTransferTime != nil {
		o.PaymentDetails.CustomerTransferTime = util.Ptr(domainOrder.PaymentDetail.CustomerTransferTime.Unix())
	}
	o.PaymentDetails.LastFiveDigits = domainOrder.PaymentDetail.LastFiveDigits
	if domainOrder.PaymentDetail.Customer.Transferred != nil {
		o.PaymentDetails.Customer = &paymentDetailsCustomer{
			Transferred: customerTransferred{
				QuoteCurrency: domainOrder.PaymentDetail.Customer.Transferred.QuoteCurrency.String(),
				UpdatedAt:     domainOrder.PaymentDetail.Customer.Transferred.UpdatedAt.Unix(),
				Attachments:   domainOrder.PaymentDetail.Customer.Transferred.Attachments,
			},
		}
	}
	if domainOrder.PaymentDetail.LastEditedAt != nil {
		o.PaymentDetails.LastEditedAt = util.Ptr(domainOrder.PaymentDetail.LastEditedAt.Unix())
	}
	if domainOrder.PaymentDetail.Editor != nil {
		o.PaymentDetails.Editor = &struct {
			Name  string `json:"name"`
			Role  string `json:"role"`
			Email string `json:"email"`
		}{
			Name:  domainOrder.PaymentDetail.Editor.Name,
			Role:  domainOrder.PaymentDetail.Editor.Role,
			Email: domainOrder.PaymentDetail.Editor.Email,
		}
	}
	o.PaymentDetails.TransferAmount = domainOrder.PaymentDetail.Amount

	o.ShippingStatus = domainOrder.ShippingStatus.String()
	o.InternalNote = domainOrder.InternalNote

}

// GetOrderDetailForMerchant returns the order for a merchant
func GetOrderDetailForMerchant(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")
	orderID := ctx.Param("order_id")
	if orderID == "" {
		response.KGError(ctx, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("order id is required"), nil))
		return
	}

	order, kgErr := market.GetOrderByID(ctx.Request.Context(), orgID, orderID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	resp := &orderDetailForMerchant{}
	resp.fromDomain(order)
	response.OK(ctx, resp)
}

type updateOrderForMerchant struct {
	InternalNote   *string `json:"internal_note"`
	PaymentDetails struct {
		CustomerTransferTime *int64           `json:"customer_transfer_time"`
		TransferAmount       *decimal.Decimal `json:"transfer_amount"`
		LastFiveDigits       *string          `json:"last_five_digits"`
		Note                 *string          `json:"note"`
		Attachments          []string         `json:"attachments"`
	} `json:"payment_details"`
}

// UpdateOrderForMerchant updates an order for a merchant
func UpdateOrderForMerchant(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")
	orderID := ctx.Param("order_id")
	if orderID == "" {
		response.KGError(ctx, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("order id is required"), nil))
		return
	}
	params := &updateOrderForMerchant{}
	kgErr := util.ToGinContextExt(ctx).BindJson(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	uid := auth.GetUID(ctx)
	if uid == "" {
		response.UnauthorizedWithMsg(ctx, code.StudioTokenInvalid, "uid not found")
		return
	}

	kgErr = market.UpdateOrderByIDForMerchant(ctx.Request.Context(), orgID, uid, orderID, &domain.UpdateOrderParams{
		InternalNote: params.InternalNote,
		PaymentDetails: domain.UpdateOrderPaymentDetailsParams{
			CustomerTransferTime: params.PaymentDetails.CustomerTransferTime,
			TransferAmount:       params.PaymentDetails.TransferAmount,
			LastFiveDigits:       params.PaymentDetails.LastFiveDigits,
			Note:                 params.PaymentDetails.Note,
			Attachments:          params.PaymentDetails.Attachments,
		},
	})
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, nil)
}

type orderForCustomer struct {
	OrderID    string `json:"order_id"`
	CreateTime int64  `json:"create_time"`
	TotalPrice struct {
		Amount        decimal.Decimal `json:"amount"`
		LogoURL       string          `json:"logo_url"`
		QuoteCurrency string          `json:"quote_currency"`
		UsdAmount     decimal.Decimal `json:"usd_amount"`
	} `json:"total_price"`
	Purchase struct {
		Amount       decimal.Decimal `json:"amount"`
		LogoURL      string          `json:"logo_url"`
		Image        string          `json:"image"`
		Name         string          `json:"name"`
		BaseCurrency string          `json:"base_currency"`
		ChainID      string          `json:"chain_id"`
		Price        decimal.Decimal `json:"price"`
	} `json:"purchase"`
	ReceivingAddress string `json:"receiving_address"`
	OrderStatus      string `json:"order_status"`
	PaymentStatus    string `json:"payment_status"`
	PaidAt           *int64 `json:"paid_at"`
	ShippedAt        *int64 `json:"shipped_at"`
	CancelledAt      *int64 `json:"cancelled_at"`
	DeliveredAt      *int64 `json:"delivered_at"`
}

func (o *orderForCustomer) formDomain(domainOrder *domain.AssetProOrder) {
	o.OrderID = domainOrder.ID
	o.CreateTime = domainOrder.CreatedAt.Unix()
	o.TotalPrice.Amount = domainOrder.TotalPrice.Amount
	o.TotalPrice.LogoURL = domainOrder.TotalPrice.LogoURL
	o.TotalPrice.QuoteCurrency = domainOrder.TotalPrice.QuoteCurrency.String()
	o.TotalPrice.UsdAmount = domainOrder.TotalPrice.UsdAmount
	o.Purchase.Amount = domainOrder.Purchase.Amount
	o.Purchase.LogoURL = domainOrder.Purchase.LogoURL
	o.Purchase.Image = domainOrder.Purchase.Image
	o.Purchase.Name = domainOrder.Purchase.Name
	o.Purchase.BaseCurrency = domainOrder.Purchase.BaseCurrency.String()
	o.Purchase.ChainID = domainOrder.Purchase.ChainID
	o.Purchase.Price = domainOrder.Purchase.Price
	o.ReceivingAddress = domainOrder.Customer.WalletAddress
	o.OrderStatus = string(domainOrder.OrderStatus)
	o.PaymentStatus = domainOrder.PaymentStatus.String()

	if domainOrder.PaymentDetail.CustomerTransferTime != nil {
		o.PaidAt = util.Ptr(domainOrder.PaymentDetail.CustomerTransferTime.Unix())
	}
	if domainOrder.ShipmentDetail != nil {
		o.ShippedAt = util.Ptr(domainOrder.ShipmentDetail.ShippedAt.Unix())
		if domainOrder.ShipmentDetail.DeliveredAt != nil {
			o.DeliveredAt = util.Ptr(domainOrder.ShipmentDetail.DeliveredAt.Unix())
		}
	}
	if domainOrder.CancelledAt != nil {
		o.CancelledAt = util.Ptr(domainOrder.CancelledAt.Unix())
	}
}

type listOrdersForCustomerReq struct {
	IsActive *bool `form:"active"`
	request.PagingRequest

	query      *paging.Query `form:"-" json:"-"`
	lastSeenID *string       `form:"-" json:"-"`
}

type listOrdersForCustomerPagingToken struct {
	LastSeenID string `json:"last_seen_id"`
}

func (req *listOrdersForCustomerReq) parse(ctx *gin.Context) *code.KGError {
	kgErr := util.ToGinContextExt(ctx).BindQuery(req)
	if kgErr != nil {
		return kgErr
	}

	pagingQuery, kgErr := req.ParseQuery([]string{"created_at"})
	if kgErr != nil {
		return kgErr
	}

	req.query = pagingQuery

	if req.PageToken == "" {
		return nil
	}

	bsPagingToken, err := util.URLSafeBase64Decode(req.PageToken)
	if err != nil {
		return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("page token is not valid"), nil)
	}

	var token listOrdersForCustomerPagingToken
	if err := json.Unmarshal(bsPagingToken, &token); err != nil {
		return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("page token is not valid"), nil)
	}

	if token.LastSeenID == "" {
		return nil
	}

	req.lastSeenID = &token.LastSeenID

	return nil
}

// ListOrdersForCustomer returns the orders for a customer
func ListOrdersForCustomer(ctx *gin.Context) {
	req := &listOrdersForCustomerReq{
		PagingRequest: request.PagingRequest{
			PageSort: "created_at:d",
		},
	}
	if kgErr := req.parse(ctx); kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")
	uid := auth.GetUID(ctx)

	orders, pagingResp, kgErr := market.ListOrdersForCustomer(
		ctx.Request.Context(), orgID, domain.ListOrdersReq{
			CustomerUID: &uid,
			LastSeenID:  req.lastSeenID,
			IsActive:    req.IsActive,
		}, req.query)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	resp := lo.Map(orders, func(order *domain.AssetProOrder, _ int) *orderForCustomer {
		resp := &orderForCustomer{}
		resp.formDomain(order)
		return resp
	})

	var nextPageToken *string
	if len(orders) == pagingResp.PageSize {
		nextPageToken = util.Ptr(util.URLSafeBase64EncodeFromInterface(listOrdersForCustomerPagingToken{
			LastSeenID: orders[len(orders)-1].ID,
		}))
	}

	response.OKWithTokenPaging(ctx, resp, response.TokenPaging{
		NextPageToken: nextPageToken,
	})
}

type orderDetailForCustomer struct {
	OrderID     string `json:"order_id"`
	CreateTime  int64  `json:"create_time"`
	CancelledAt *int64 `json:"cancelled_at"`
	Customer    struct {
		Name          *string `json:"name"`
		Phone         *string `json:"phone"`
		Email         *string `json:"email"`
		WalletAddress string  `json:"wallet_address"`
	} `json:"customer"`
	Purchase struct {
		Amount       decimal.Decimal `json:"amount"`
		LogoURL      string          `json:"logo_url"`
		Image        string          `json:"image"`
		Name         string          `json:"name"`
		BaseCurrency string          `json:"base_currency"`
		ChainID      string          `json:"chain_id"`
		Price        decimal.Decimal `json:"price"`
	} `json:"purchase"`
	TotalPrice struct {
		Amount                decimal.Decimal `json:"amount"`
		LogoURL               string          `json:"logo_url"`
		QuoteCurrency         string          `json:"quote_currency"`
		HandlingFee           decimal.Decimal `json:"handling_fee"`
		HandlingFeePercentage decimal.Decimal `json:"handling_fee_percentage"`
	} `json:"total_price"`
	ShipmentDetails *struct {
		TxHash      string `json:"tx_hash"`
		ShippedAt   int64  `json:"shipped_at"`
		DeliveredAt *int64 `json:"delivered_at"`
	} `json:"shipment_details"`
	OrderStatus    string `json:"order_status"`
	PaymentStatus  string `json:"payment_status"`
	PaymentDetails struct {
		Deadline   int64 `json:"deadline"`
		TransferTo struct {
			BankName          *string `json:"bank_name"`
			BranchName        *string `json:"branch_name"`
			AccountNumber     *string `json:"account_number"`
			AccountHolderName *string `json:"account_holder_name"`
		} `json:"transfer_to"`
		PaymentMethod        string                  `json:"payment_method"`
		CustomerTransferTime *int64                  `json:"customer_transfer_time"`
		Customer             *paymentDetailsCustomer `json:"customer"`
	} `json:"payment_details"`
}

func (o *orderDetailForCustomer) fromDomain(domainOrder *domain.AssetProOrder) {
	o.OrderID = domainOrder.ID
	o.CreateTime = domainOrder.CreatedAt.Unix()
	if domainOrder.CancelledAt != nil {
		o.CancelledAt = util.Ptr(domainOrder.CancelledAt.Unix())
	}
	o.Customer.Name = domainOrder.Customer.Name
	o.Customer.Phone = domainOrder.Customer.Phone
	o.Customer.Email = domainOrder.Customer.Email
	o.Customer.WalletAddress = domainOrder.Customer.WalletAddress

	o.Purchase.Amount = domainOrder.Purchase.Amount
	o.Purchase.LogoURL = domainOrder.Purchase.LogoURL
	o.Purchase.Image = domainOrder.Purchase.Image
	o.Purchase.Name = domainOrder.Purchase.Name
	o.Purchase.ChainID = domainOrder.Purchase.ChainID
	o.Purchase.BaseCurrency = domainOrder.Purchase.BaseCurrency.String()
	o.Purchase.Price = domainOrder.Purchase.Price

	o.TotalPrice.Amount = domainOrder.TotalPrice.Amount
	o.TotalPrice.LogoURL = domainOrder.TotalPrice.LogoURL
	o.TotalPrice.QuoteCurrency = domainOrder.TotalPrice.QuoteCurrency.String()
	o.TotalPrice.HandlingFee = domainOrder.TotalPrice.HandlingFee
	handlingFeePercentage := decimal.Zero
	if domainOrder.TotalPrice.HandlingFeePercentage != nil {
		handlingFeePercentage = *domainOrder.TotalPrice.HandlingFeePercentage
	}
	o.TotalPrice.HandlingFeePercentage = handlingFeePercentage

	if domainOrder.ShipmentDetail != nil {
		o.ShipmentDetails = &struct {
			TxHash      string `json:"tx_hash"`
			ShippedAt   int64  `json:"shipped_at"`
			DeliveredAt *int64 `json:"delivered_at"`
		}{
			TxHash:    domainOrder.ShipmentDetail.TxHash,
			ShippedAt: domainOrder.ShipmentDetail.ShippedAt.Unix(),
		}
		if domainOrder.ShipmentDetail.DeliveredAt != nil {
			o.ShipmentDetails.DeliveredAt = util.Ptr(domainOrder.ShipmentDetail.DeliveredAt.Unix())
		}
	}

	o.OrderStatus = string(domainOrder.OrderStatus)

	o.PaymentStatus = domainOrder.PaymentStatus.String()

	o.PaymentDetails.Deadline = domainOrder.PaymentDetail.Deadline.Unix()
	o.PaymentDetails.TransferTo.BankName = domainOrder.PaymentDetail.TransferTo.BankName
	o.PaymentDetails.TransferTo.BranchName = domainOrder.PaymentDetail.TransferTo.BranchName
	o.PaymentDetails.TransferTo.AccountNumber = domainOrder.PaymentDetail.TransferTo.AccountNumber
	o.PaymentDetails.TransferTo.AccountHolderName = domainOrder.PaymentDetail.TransferTo.AccountHolderName
	o.PaymentDetails.PaymentMethod = domainOrder.PaymentDetail.PaymentMethod
	if domainOrder.PaymentDetail.CustomerTransferTime != nil {
		o.PaymentDetails.CustomerTransferTime = util.Ptr(domainOrder.PaymentDetail.CustomerTransferTime.Unix())
	}
	if domainOrder.PaymentDetail.Customer.Transferred != nil {
		o.PaymentDetails.Customer = &paymentDetailsCustomer{
			Transferred: customerTransferred{
				QuoteCurrency: domainOrder.PaymentDetail.Customer.Transferred.QuoteCurrency.String(),
				UpdatedAt:     domainOrder.PaymentDetail.Customer.Transferred.UpdatedAt.Unix(),
				Attachments:   domainOrder.PaymentDetail.Customer.Transferred.Attachments,
			},
		}
	}
}

// GetOrderDetailForCustomer returns the order for a customer
func GetOrderDetailForCustomer(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")
	uid := auth.GetUID(ctx)

	orderID := ctx.Param("order_id")
	if orderID == "" {
		response.KGError(ctx, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("order id is required"), nil))
		return
	}

	order, kgErr := market.GetOrderByID(ctx.Request.Context(), orgID, orderID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// access control
	if order.Customer.UID != uid {
		response.KGError(ctx, code.NewKGError(code.OrderNotFound, http.StatusNotFound, errors.New("order not found"), nil))
		return
	}

	resp := &orderDetailForCustomer{}
	resp.fromDomain(order)
	response.OK(ctx, resp)
}

type createOrdersForCustomerParams struct {
	ProductID                 int                           `json:"product_id" binding:"required"`
	Name                      string                        `json:"name" binding:"required"`
	WalletAddress             string                        `json:"wallet_address" binding:"required"`
	Price                     decimal.Decimal               `json:"price" binding:"required"`
	Cost                      decimal.Decimal               `json:"cost" binding:"required"`
	Amount                    decimal.Decimal               `json:"amount" binding:"required"`
	TransferTo                assetProOrderTransferTo       `json:"transfer_to"`
	FeeType                   domain.AssetProProductFeeType `json:"fee_type" binding:"required"`
	ProportionalFeePercentage decimal.Decimal               `json:"proportional_fee_percentage" binding:"required"`
	ProportionalMinimumFee    decimal.Decimal               `json:"proportional_minimum_fee" binding:"required"`
}

type assetProOrderTransferTo struct {
	BankName          *string `json:"bank_name"`
	BranchName        *string `json:"branch_name"`
	AccountNumber     *string `json:"account_number"`
	AccountHolderName *string `json:"account_holder_name"`
}

// CreateOrdersForCustomer creates an order after validating the request body with the product in the database
func CreateOrdersForCustomer(c *gin.Context) {
	req := createOrdersForCustomerParams{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID := c.GetInt("org_id")
	uid := auth.GetUID(c)

	kgErr = market.CreateOrder(c.Request.Context(), orgID, uid, &domain.CreateAssetProOrderParams{
		ProductID:     req.ProductID,
		Name:          req.Name,
		WalletAddress: req.WalletAddress,
		Price:         req.Price,
		Amount:        req.Amount,
		TotalCost:     req.Cost,
		TransferTo: domain.AssetProOrderBankAccount{
			BankName:          req.TransferTo.BankName,
			BranchName:        req.TransferTo.BranchName,
			AccountNumber:     req.TransferTo.AccountNumber,
			AccountHolderName: req.TransferTo.AccountHolderName,
		},
		FeeType:                   req.FeeType,
		ProportionalFeePercentage: req.ProportionalFeePercentage,
		ProportionalMinimumFee:    req.ProportionalMinimumFee,
		PaymentStatus:             domain.AssetProPaymentStatusUnpaid,
		OrderStatus:               domain.AssetProOrderStatusUnpaid,
	})

	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

type confirmPaymentForMerchantParams struct {
	CustomerTransferTime int64           `json:"customer_transfer_time" binding:"required"`
	TransferAmount       decimal.Decimal `json:"transfer_amount" binding:"required"`
	LastFiveDigits       string          `json:"last_five_digits" binding:"required"`
	Note                 *string         `json:"note"`
	Attachments          []string        `json:"attachments"`
}

// ConfirmPaymentForMerchant confirms payment for an order
func ConfirmPaymentForMerchant(c *gin.Context) {
	organizationID := c.GetInt("org_id")
	orderID := c.Param("order_id")
	req := &confirmPaymentForMerchantParams{}
	kgErr := util.ToGinContextExt(c).BindJson(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	kgErr = market.ConfirmPaymentForMerchant(c.Request.Context(), organizationID, orderID, &domain.ConfirmPaymentForMerchantParams{
		OperatorUID:          auth.GetUID(c),
		CustomerTransferTime: req.CustomerTransferTime,
		TransferAmount:       req.TransferAmount,
		LastFiveDigits:       req.LastFiveDigits,
		Note:                 req.Note,
		Attachments:          req.Attachments,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

type confirmPaymentForCustomerParams struct {
	Attachments []string `json:"attachments"`
}

// ConfirmPaymentForCustomer confirms payment for an order
func ConfirmPaymentForCustomer(c *gin.Context) {
	orgID := c.GetInt("org_id")
	orderID := c.Param("order_id")
	uid := auth.GetUID(c)
	req := &confirmPaymentForCustomerParams{}
	kgErr := util.ToGinContextExt(c).BindJson(req)
	if kgErr != nil && kgErr.Error != io.EOF { // allow empty body
		response.KGError(c, kgErr)
		return
	}
	kgErr = market.ConfirmPaymentForCustomer(c.Request.Context(), orgID, orderID, &domain.ConfirmPaymentForCustomerParams{
		Attachments: req.Attachments,
		CustomerUID: uid,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

// TransferProductForMerchant transfer product for merchant.
func TransferProductForMerchant(ctx *gin.Context) {
	kgErr := market.TransferProduct(ctx.Request.Context(), ctx.GetInt("org_id"), ctx.GetString("uid"), ctx.Param("order_id"))
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, nil)
}

// CancelOrderForMerchant cancels an order
func CancelOrderForMerchant(c *gin.Context) {
	organizationID := c.GetInt("org_id")
	orderID := c.Param("order_id")

	operatorUID := auth.GetUID(c)
	kgErr := market.CancelOrderForMerchant(c.Request.Context(), organizationID, orderID, operatorUID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}
