package market

import (
	"fmt"
	"strconv"

	request "github.com/kryptogo/kg-wallet-backend/api/paging"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro/market"
	orgservice "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
)

type getProductsForMerchantParams struct {
	IsPublished bool `form:"is_published"`
	request.PagingRequest
}

type productForMerchant struct {
	ID             int `json:"product_id"`
	OrganizationID int `json:"organization_id"`
	Operator       struct {
		Name *string `json:"name"`
	} `json:"operator"`
	IsPublished               bool             `json:"is_published"`
	ChainID                   string           `json:"chain_id"`
	BaseCurrency              string           `json:"base_currency"`
	QuoteCurrency             string           `json:"quote_currency"`
	Type                      string           `json:"type"`
	LogoUrl                   string           `json:"logo_url"`
	Image                     string           `json:"image"`
	Name                      string           `json:"name"`
	Price                     *decimal.Decimal `json:"price"`
	OrderLimitsFrom           *decimal.Decimal `json:"order_limits_from"`
	OrderLimitsTo             *decimal.Decimal `json:"order_limits_to"`
	Stock                     *decimal.Decimal `json:"stock"`
	FeeType                   string           `json:"fee_type"`
	ProportionalFeePercentage *decimal.Decimal `json:"proportional_fee_percentage"`
	ProportionalMinimumFee    *decimal.Decimal `json:"proportional_minimum_fee"`
	CreatedAt                 int64            `json:"created_at"`
	UpdatedAt                 int64            `json:"updated_at"`
}

func (p *productForMerchant) fromDomain(domain *domain.AssetProProduct) {
	p.ID = domain.AssetProProductInfo.ID
	p.OrganizationID = domain.OrganizationID
	p.ChainID = domain.AssetProProductBaseInfo.ChainID
	p.IsPublished = domain.AssetProProductInfo.IsPublished
	p.BaseCurrency = domain.AssetProProductBaseInfo.BaseCurrency.String()
	p.QuoteCurrency = domain.AssetProProductBaseInfo.QuoteCurrency.String()
	p.Type = domain.AssetProProductBaseInfo.Type.String()
	p.LogoUrl = domain.AssetProProductBaseInfo.TokenLogo
	p.Price = domain.Price
	p.OrderLimitsFrom = domain.OrderLimitsFrom
	p.OrderLimitsTo = domain.OrderLimitsTo
	p.Stock = domain.Stock
	p.FeeType = domain.FeeType.String()
	p.ProportionalFeePercentage = domain.ProportionalFeePercentage
	p.ProportionalMinimumFee = domain.ProportionalMinimumFee
	p.CreatedAt = domain.AssetProProductInfo.CreatedAt.Unix()
	p.UpdatedAt = domain.AssetProProductInfo.UpdatedAt.Unix()

	if domain.AssetProProductInfo.Name != "" { // Use store name if it exists
		p.Name = domain.AssetProProductInfo.Name
	} else {
		p.Name = domain.AssetProProductBaseInfo.Name
	}
	if domain.AssetProProductInfo.Image != "" { // Use store image if it exists
		p.Image = domain.AssetProProductInfo.Image
	} else {
		p.Image = domain.AssetProProductBaseInfo.Image
	}

	if domain.AssetProProductInfo.Operator != nil {
		p.Operator.Name = &domain.AssetProProductInfo.Operator.Name
	}
}

// GetProductsForMerchant get all products by org id
func GetProductsForMerchant(c *gin.Context) {
	req := getProductsForMerchantParams{
		PagingRequest: request.PagingRequest{
			PageSort: "stock:d",
		},
	}
	kgErr := util.ToGinContextExt(c).BindQuery(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	query, kgError := req.ParseQuery([]string{"stock", "price"})
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	organizationID := c.GetInt("org_id")
	domainProducts, paging, kgErr := market.GetProducts(c.Request.Context(), organizationID, &domain.GetProductsParams{
		IsPublished: util.Ptr(req.IsPublished),
	}, query)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	products := make([]productForMerchant, 0, len(domainProducts))
	for _, domainProduct := range domainProducts {
		p := productForMerchant{}
		p.fromDomain(domainProduct)
		products = append(products, p)
	}

	response.OKWithPaging(c, products, response.Paging{
		PageNumber: paging.PageNumber,
		PageSize:   paging.PageSize,
		TotalCount: paging.TotalCount,
		PageSort:   string(paging.PageSort),
	})
}

type updateProductForMerchantParams struct {
	IsPublished               bool                          `json:"is_published"`
	FeeType                   domain.AssetProProductFeeType `json:"fee_type"`
	ProportionalFeePercentage *decimal.Decimal              `json:"proportional_fee_percentage"`
	ProportionalMinimumFee    *decimal.Decimal              `json:"proportional_minimum_fee"`
	Price                     decimal.Decimal               `json:"price"`
	OrderLimitsFrom           *decimal.Decimal              `json:"order_limits_from"`
	OrderLimitsTo             *decimal.Decimal              `json:"order_limits_to"`
	Stock                     *decimal.Decimal              `json:"stock"`
	Image                     *string                       `json:"image"`
	Name                      string                        `json:"name"`
}

// AfterValidate validates the update product for merchant params
func (u *updateProductForMerchantParams) AfterValidate() error {
	if u.FeeType == domain.AssetProProductFeeTypeFeeIncluded {
		if u.ProportionalFeePercentage == nil || u.ProportionalMinimumFee == nil {
			return fmt.Errorf("proportional_fee_percentage and proportional_minimum_fee must be provided")
		}
	}
	if u.Price.LessThan(decimal.Zero) {
		return fmt.Errorf("price must be non-negative")
	}
	if u.OrderLimitsFrom != nil && (*u.OrderLimitsFrom).LessThan(decimal.Zero) {
		return fmt.Errorf("order_limits_from must be non-negative")
	}
	if u.OrderLimitsTo != nil && (*u.OrderLimitsTo).LessThan(decimal.Zero) {
		return fmt.Errorf("order_limits_to must be non-negative")
	}
	if u.Stock != nil && (*u.Stock).LessThan(decimal.Zero) {
		return fmt.Errorf("stock must be non-negative")
	}
	if u.Stock != nil && (*u.Stock).GreaterThanOrEqual(decimal.NewFromInt(1_000_000_000_000)) {
		return fmt.Errorf("stock must be less than 1,000,000,000,000")
	}
	if u.OrderLimitsFrom != nil && u.OrderLimitsTo != nil && (*u.OrderLimitsFrom).GreaterThan(*u.OrderLimitsTo) {
		return fmt.Errorf("order_limits_from must be less than order_limits_to")
	}
	return nil
}

// UpdateProductForMerchant update product by id
func UpdateProductForMerchant(c *gin.Context) {
	req := updateProductForMerchantParams{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	productID, err := strconv.Atoi(c.Param("product_id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "product_id must be an integer")
		return
	}
	organizationID := c.GetInt("org_id")
	uid := c.GetString("uid")

	kgErr = market.UpdateProduct(c, organizationID, uid, productID, &domain.UpdateProductParams{
		IsPublished:               req.IsPublished,
		FeeType:                   req.FeeType,
		ProportionalFeePercentage: req.ProportionalFeePercentage,
		ProportionalMinimumFee:    req.ProportionalMinimumFee,
		Price:                     req.Price,
		OrderLimitsFrom:           req.OrderLimitsFrom,
		OrderLimitsTo:             req.OrderLimitsTo,
		Stock:                     req.Stock,
		Image:                     req.Image,
		Name:                      req.Name,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

type getProductsForCustomerParams struct {
	request.PagingRequest
}

type productForCustomer struct {
	ID                        int              `json:"product_id"`
	OrganizationID            int              `json:"organization_id"`
	ChainID                   string           `json:"chain_id"`
	BaseCurrency              string           `json:"base_currency"`
	QuoteCurrency             string           `json:"quote_currency"`
	Type                      string           `json:"type"`
	LogoUrl                   string           `json:"logo_url"`
	Image                     string           `json:"image"`
	Name                      string           `json:"name"`
	Price                     *decimal.Decimal `json:"price"`
	OrderLimitsFrom           *decimal.Decimal `json:"order_limits_from"`
	OrderLimitsTo             *decimal.Decimal `json:"order_limits_to"`
	FeeType                   string           `json:"fee_type"`
	ProportionalFeePercentage *decimal.Decimal `json:"proportional_fee_percentage"`
	ProportionalMinimumFee    *decimal.Decimal `json:"proportional_minimum_fee"`
	Decimals                  int              `json:"decimals"`
}

func (p *productForCustomer) fromDomain(domain *domain.AssetProProduct) {
	p.ID = domain.AssetProProductInfo.ID
	p.OrganizationID = domain.OrganizationID
	p.ChainID = domain.AssetProProductBaseInfo.ChainID
	p.BaseCurrency = domain.AssetProProductBaseInfo.BaseCurrency.String()
	p.QuoteCurrency = domain.AssetProProductBaseInfo.QuoteCurrency.String()
	p.Type = domain.AssetProProductBaseInfo.Type.String()
	p.LogoUrl = domain.AssetProProductBaseInfo.TokenLogo
	p.Price = domain.Price
	p.OrderLimitsFrom = domain.OrderLimitsFrom
	p.OrderLimitsTo = domain.OrderLimitsTo
	p.FeeType = domain.FeeType.String()
	p.ProportionalFeePercentage = domain.ProportionalFeePercentage
	p.ProportionalMinimumFee = domain.ProportionalMinimumFee
	p.Decimals = domain.Decimals
	if domain.AssetProProductInfo.Name != "" { // Use store name if it exists
		p.Name = domain.AssetProProductInfo.Name
	} else {
		p.Name = domain.AssetProProductBaseInfo.Name
	}

	if domain.AssetProProductInfo.Image != "" { // Use store image if it exists
		p.Image = domain.AssetProProductInfo.Image
	} else {
		p.Image = domain.AssetProProductBaseInfo.Image
	}
}

// GetProductsForCustomer get all products by org id
func GetProductsForCustomer(c *gin.Context) {
	req := getProductsForCustomerParams{
		PagingRequest: request.PagingRequest{
			PageSort: "stock:d,name:a",
		},
	}
	kgErr := util.ToGinContextExt(c).BindQuery(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	query, kgError := req.ParseQuery([]string{"stock", "name"})
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	organizationID, err := strconv.Atoi(c.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid org id")
		return
	}
	domainProducts, _, kgErr := market.GetProducts(c.Request.Context(), organizationID, &domain.GetProductsParams{
		IsPublished: util.Ptr(true),
	}, query)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	products := make([]productForCustomer, 0, len(domainProducts))
	for _, domainProduct := range domainProducts {
		p := productForCustomer{}
		p.fromDomain(domainProduct)
		products = append(products, p)
	}

	response.OK(c, products)
}

// SyncNewArrivalProduct create new product for each enabled market.
func SyncNewArrivalProduct(ctx *gin.Context) {
	markets, kgErr := orgservice.GetMarkets(ctx.Request.Context())
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	for _, m := range markets {
		if kgErr := market.CreateProducts(ctx.Request.Context(), m.OrganizationID); kgErr != nil {
			response.KGError(ctx, kgErr)
			return
		}
	}

	response.OK(ctx, nil)
}
