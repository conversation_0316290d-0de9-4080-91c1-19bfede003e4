package market

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	customerRepo "github.com/kryptogo/kg-wallet-backend/pkg/service/customer/repo"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	marketservice "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro/market"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type testMarketSuite struct {
	suite.Suite

	operatorUID string

	ginEngine *gin.Engine
}

func (s *testMarketSuite) SetupSuite() {
	s.ginEngine = gin.Default()

	marketservice.Init(marketservice.InitParam{
		AssetProOrderRepo: rdb.GormRepo(),
	})

	middlewareToGetOrgID := func(ctx *gin.Context) {
		orgID, _ := strconv.Atoi(ctx.Param("orgID"))
		ctx.Set("org_id", orgID)
		ctx.Next()
	}
	middlewareToGetCustomerID := func(ctx *gin.Context) {
		uid := ctx.GetHeader("UID")
		ctx.Set("uid", uid)
		ctx.Next()
	}

	s.createTestOrderSeed()

	s.ginEngine.GET("/v1/studio/organization/:orgID/asset_pro/pending_order_count",
		middlewareToGetOrgID, CountOfPendingOrdersForMerchant)
	s.ginEngine.GET("/v1/studio/organization/:orgID/asset_pro/orders",
		middlewareToGetOrgID, ListOrdersForMerchant)
	s.ginEngine.GET("/v1/studio/organization/:orgID/asset_pro/orders/:order_id",
		middlewareToGetOrgID, GetOrderDetailForMerchant)

	s.ginEngine.GET("/v1/studio/organization/:orgID/orders",
		middlewareToGetOrgID, middlewareToGetCustomerID, ListOrdersForCustomer)
	s.ginEngine.GET("/v1/studio/organization/:orgID/orders/:order_id",
		middlewareToGetOrgID, middlewareToGetCustomerID, GetOrderDetailForCustomer)
}

func (s *testMarketSuite) createTestOrderSeed() {
	{ // create firebase user which is used for testing
		rdb.Reset()
		users, uids := dbtest.Users()
		s.operatorUID = uids[0]
		_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
		_, err := firebase.BatchCreateUsersBySeed(users)
		if err != nil {
			panic(err)
		}
	}

	s.NoError(rdbtest.CreateStudioDefault(rdb.Get()))

	s.NoError(rdb.Get().Create(&model.StudioUser{
		OrganizationID: 1,
		UID:            s.operatorUID,
		Name:           "operator1",
		Status:         "active",
		Email:          util.Ptr("<EMAIL>"),
		RoleBinding: []model.StudioRoleBinding{{
			RoleID:         4,
			OrganizationID: 1,
			UID:            s.operatorUID,
		}},
	}).Error)

	s.NoError(rdb.Get().Create(&model.Customer{OrganizationID: 2, UID: "testuser2"}).Error)

	s.NoError(rdb.Get().Create([]model.AssetProOrder{
		{
			ID:                          "test-order-id-1",
			OrganizationID:              1,
			CustomerUID:                 "customer1",
			OperatorUID:                 util.Ptr(s.operatorUID),
			WalletAddress:               "******************************************",
			ProductID:                   1,
			Amount:                      decimal.NewFromInt(99),
			USDAmount:                   decimal.NewFromInt(99),
			TotalCost:                   decimal.NewFromInt(3000),
			USDTotalCost:                decimal.NewFromInt(100),
			Price:                       decimal.NewFromInt(30),
			ExchangeRate:                decimal.NewFromInt(30),
			FeeType:                     domain.AssetProProductFeeTypeFeeIncluded,
			ProportionalFeePercentage:   util.Ptr(decimal.NewFromFloat(1.0)),
			ProportionalMinimumFee:      util.Ptr(decimal.NewFromInt(1)),
			TxID:                        util.Ptr(1),
			PaymentStatus:               domain.AssetProPaymentStatusPaid,
			OrderStatus:                 domain.AssetProOrderStatusDelivered,
			Deadline:                    time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
			TransferToBankName:          util.Ptr("Mega Bank"),
			TransferToBranchName:        util.Ptr("Xin Yi"),
			TransferToAccountNumber:     util.Ptr("55688"),
			TransferToAccountHolderName: util.Ptr("KryptoGO"),
			PaymentNote:                 util.Ptr("test payment note"),
			PaymentAttachments:          util.Ptr(model.StringArray{"payment attachment 1", "payment attachment 2"}),
			LastFiveDigits:              util.Ptr("55555"),
			CustomerAttachments:         util.Ptr(model.StringArray{"customer attachment 1", "customer attachment 2"}),
			InternalNote:                util.Ptr("test internal note"),
			CustomerUpdatedAt:           util.Ptr(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC)),
			OperatorUpdatedAt:           util.Ptr(time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC)),
			CancelledAt:                 nil,
			RefundedAt:                  nil,
			CreatedAt:                   time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			UpdatedAt:                   time.Date(2024, 1, 5, 0, 0, 0, 0, time.UTC),
			DeleteAt:                    nil,
			Product: model.AssetProProduct{
				ID:                        1,
				OrganizationID:            1,
				ProductBaseInfoID:         1,
				IsPublished:               true,
				Name:                      "test product",
				Price:                     util.Ptr(decimal.NewFromInt(30)),
				OrderLimitsFrom:           util.Ptr(decimal.NewFromInt(1)),
				OrderLimitsTo:             util.Ptr(decimal.NewFromInt(100)),
				Stock:                     util.Ptr(decimal.NewFromInt(1000)),
				FeeType:                   domain.AssetProProductFeeTypeFeeIncluded,
				ProportionalFeePercentage: util.Ptr(decimal.NewFromFloat(1.0)),
				ProportionalMinimumFee:    util.Ptr(decimal.NewFromInt(1)),
				CreatedAt:                 time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				UpdatedAt:                 time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				BaseInfo: model.AssetProProductBaseInfo{
					ID:             1,
					Name:           "USDT/TWD",
					ChainID:        model.ChainIDEthereum,
					BaseCurrency:   "USDT",
					QuoteCurrency:  "TWD",
					Type:           domain.AssetProProductTypeBuyCrypto,
					Image:          "image_url_usdt",
					TokenLogo:      "token_logo_url_usdt",
					QuoteTokenLogo: "quote_token_logo_url_ntd",
					CreatedAt:      time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
					UpdatedAt:      time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				},
			},
			Customer: model.Customer{
				OrganizationID: 1,
				UID:            "customer1",
				Country:        util.Ptr("country1"),
				NationalID:     util.Ptr("national_id1"),
				Email:          util.Ptr("email1"),
				Phone:          util.Ptr("phone1"),
				LegalName:      util.Ptr("Customer1"),
				KYCStatus:      domain.KycStatusVerified,
			},
			/* see rdbtest.CreateStudioDefault()
			Operator: &model.StudioUser{}, */
			TxLog: &model.AssetProTxLog{
				ID:                 1,
				OrganizationID:     1,
				OperatorUID:        s.operatorUID,
				Token:              "USDT",
				SubmitTime:         int(time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC).Unix()),
				Status:             domain.AssetProTxLogStatusSendSuccess,
				ChainID:            model.ChainIDEthereum,
				ContractAddress:    "******************************************",
				FromAddress:        "******************************************",
				ToAddress:          "******************************************",
				TxHash:             util.Ptr("******************************************111111111111111111111111"),
				TransferTime:       util.Ptr(int(time.Date(2024, 1, 4, 1, 0, 0, 0, time.UTC).Unix())),
				Amount:             decimal.NewFromFloat(99),
				RecipientName:      util.Ptr("recipient name"),
				RecipientPhone:     util.Ptr("recipient phone"),
				RecipientEmail:     util.Ptr("<EMAIL>"),
				RecipientKycStatus: util.Ptr(domain.KycStatusVerified),
			},
		},
		{
			ID:                          "test-order-id-2",
			OrganizationID:              2,
			CustomerUID:                 "customer2",
			OperatorUID:                 nil,
			WalletAddress:               "******************************************",
			ProductID:                   2,
			Amount:                      decimal.NewFromInt(99),
			USDAmount:                   decimal.NewFromInt(99),
			TotalCost:                   decimal.NewFromInt(3000),
			USDTotalCost:                decimal.NewFromInt(100),
			Price:                       decimal.NewFromInt(30),
			ExchangeRate:                decimal.NewFromInt(30),
			FeeType:                     domain.AssetProProductFeeTypeFeeIncluded,
			ProportionalFeePercentage:   util.Ptr(decimal.NewFromFloat(1.0)),
			ProportionalMinimumFee:      util.Ptr(decimal.NewFromInt(1)),
			TxID:                        nil,
			PaymentStatus:               domain.AssetProPaymentStatusUnpaid,
			OrderStatus:                 domain.AssetProOrderStatusUnpaid,
			Deadline:                    time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
			TransferToBankName:          nil,
			TransferToBranchName:        nil,
			TransferToAccountNumber:     nil,
			TransferToAccountHolderName: nil,
			PaymentNote:                 nil,
			PaymentAttachments:          nil,
			LastFiveDigits:              nil,
			CustomerAttachments:         nil,
			InternalNote:                nil,
			CustomerUpdatedAt:           nil,
			OperatorUpdatedAt:           nil,
			CancelledAt:                 nil,
			RefundedAt:                  nil,
			CreatedAt:                   time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			UpdatedAt:                   time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			DeleteAt:                    nil,
			Product: model.AssetProProduct{
				ID:                        2,
				OrganizationID:            2,
				ProductBaseInfoID:         1,
				IsPublished:               true,
				Image:                     util.Ptr("image_url"),
				Name:                      "",
				Price:                     util.Ptr(decimal.NewFromInt(30)),
				OrderLimitsFrom:           util.Ptr(decimal.NewFromInt(1)),
				OrderLimitsTo:             util.Ptr(decimal.NewFromInt(100)),
				Stock:                     util.Ptr(decimal.NewFromInt(1000)),
				FeeType:                   domain.AssetProProductFeeTypeFeeIncluded,
				ProportionalFeePercentage: util.Ptr(decimal.NewFromFloat(1.0)),
				ProportionalMinimumFee:    util.Ptr(decimal.NewFromInt(1)),
				CreatedAt:                 time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				UpdatedAt:                 time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			Customer: model.Customer{
				OrganizationID: 2,
				UID:            "customer2",
				Country:        util.Ptr("country2"),
				NationalID:     util.Ptr("national_id2"),
				Email:          util.Ptr("email2"),
				Phone:          util.Ptr("phone2"),
				LegalName:      util.Ptr("Customer2"),
			},
		},
		{
			ID:                          "test-order-id-3",
			OrganizationID:              1,
			CustomerUID:                 "customer1",
			OperatorUID:                 util.Ptr(s.operatorUID),
			WalletAddress:               "******************************************",
			ProductID:                   2,
			Amount:                      decimal.NewFromInt(100),
			USDAmount:                   decimal.NewFromInt(100),
			TotalCost:                   decimal.NewFromInt(3200),
			USDTotalCost:                decimal.NewFromInt(100),
			Price:                       decimal.NewFromInt(32),
			ExchangeRate:                decimal.NewFromInt(32),
			FeeType:                     domain.AssetProProductFeeTypeNoFee,
			PaymentStatus:               domain.AssetProPaymentStatusUnpaid,
			OrderStatus:                 domain.AssetProOrderStatusUnpaid,
			Deadline:                    time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
			TransferToBankName:          util.Ptr("Mega Bank"),
			TransferToBranchName:        util.Ptr("Xin Yi"),
			TransferToAccountNumber:     util.Ptr("55688"),
			TransferToAccountHolderName: util.Ptr("KryptoGO"),
			CustomerUpdatedAt:           util.Ptr(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC)),
			CancelledAt:                 nil,
			RefundedAt:                  nil,
			CreatedAt:                   time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC),
			UpdatedAt:                   time.Date(2024, 1, 5, 0, 0, 0, 0, time.UTC),
			DeleteAt:                    nil,
			Product: model.AssetProProduct{
				ID:                2,
				OrganizationID:    1,
				ProductBaseInfoID: 2,
				IsPublished:       true,
				Name:              "test product",
				Price:             util.Ptr(decimal.NewFromInt(32)),
				OrderLimitsFrom:   util.Ptr(decimal.NewFromInt(1)),
				OrderLimitsTo:     util.Ptr(decimal.NewFromInt(100)),
				Stock:             util.Ptr(decimal.NewFromInt(1000)),
				FeeType:           domain.AssetProProductFeeTypeNoFee,
				CreatedAt:         time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				UpdatedAt:         time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				BaseInfo: model.AssetProProductBaseInfo{
					ID:             2,
					Name:           "USDT/TWD",
					ChainID:        model.ChainIDEthereum,
					BaseCurrency:   "USDT",
					QuoteCurrency:  "TWD",
					Type:           domain.AssetProProductTypeBuyCrypto,
					Image:          "image_url_usdt",
					TokenLogo:      "token_logo_url_usdt",
					QuoteTokenLogo: "quote_token_logo_url_ntd",
					CreatedAt:      time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
					UpdatedAt:      time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				},
			},
		},
		{
			ID:                          "test-order-id-4",
			OrganizationID:              1,
			CustomerUID:                 "customer1",
			OperatorUID:                 util.Ptr(s.operatorUID),
			WalletAddress:               "******************************************",
			ProductID:                   3,
			Amount:                      decimal.NewFromInt(100),
			USDAmount:                   decimal.NewFromInt(100),
			TotalCost:                   decimal.NewFromInt(3400),
			USDTotalCost:                decimal.NewFromInt(100),
			Price:                       decimal.NewFromInt(34),
			ExchangeRate:                decimal.NewFromInt(34),
			FeeType:                     domain.AssetProProductFeeTypeNoFee,
			PaymentStatus:               domain.AssetProPaymentStatusUnpaid,
			OrderStatus:                 domain.AssetProOrderStatusUnpaid,
			Deadline:                    time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
			TransferToBankName:          util.Ptr("Mega Bank"),
			TransferToBranchName:        util.Ptr("Xin Yi"),
			TransferToAccountNumber:     util.Ptr("55688"),
			TransferToAccountHolderName: util.Ptr("KryptoGO"),
			CustomerUpdatedAt:           util.Ptr(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC)),
			CancelledAt:                 nil,
			RefundedAt:                  nil,
			CreatedAt:                   time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC),
			UpdatedAt:                   time.Date(2024, 1, 5, 0, 0, 0, 0, time.UTC),
			DeleteAt:                    nil,
			Product: model.AssetProProduct{
				ID:                3,
				OrganizationID:    1,
				ProductBaseInfoID: 3,
				IsPublished:       true,
				Name:              "test product",
				Price:             util.Ptr(decimal.NewFromInt(34)),
				OrderLimitsFrom:   util.Ptr(decimal.NewFromInt(1)),
				OrderLimitsTo:     util.Ptr(decimal.NewFromInt(100)),
				Stock:             util.Ptr(decimal.NewFromInt(1000)),
				FeeType:           domain.AssetProProductFeeTypeNoFee,
				CreatedAt:         time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				UpdatedAt:         time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				BaseInfo: model.AssetProProductBaseInfo{
					ID:             3,
					Name:           "USDT/TWD",
					ChainID:        model.ChainIDEthereum,
					BaseCurrency:   "USDT",
					QuoteCurrency:  "TWD",
					Type:           domain.AssetProProductTypeBuyCrypto,
					Image:          "image_url_usdt",
					TokenLogo:      "token_logo_url_usdt",
					QuoteTokenLogo: "quote_token_logo_url_ntd",
					CreatedAt:      time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
					UpdatedAt:      time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
				},
			},
		},
	}).Error)
}

func (s *testMarketSuite) TestCountOfPendingOrdersForMerchant() {
	type resp struct {
		Code int `json:"code"`
		Data struct {
			Count int64 `json:"count"`
		} `json:"data"`
	}
	{ // query in org(1)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/pending_order_count", nil)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal(int64(2), response.Data.Count)
	}
	{ // query in org(2)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/asset_pro/pending_order_count", nil)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal(int64(1), response.Data.Count)
	}
}

func (s *testMarketSuite) TestListOrdersForMerchant() {
	type resp struct {
		Code int `json:"code"`
		Data []struct {
			CreateTime int64  `json:"create_time"`
			OrderID    string `json:"order_id"`
			Customer   struct {
				Name      string  `json:"name"`
				Phone     *string `json:"phone"`
				Email     *string `json:"email"`
				KycStatus string  `json:"kyc_status"`
			} `json:"customer"`
			Purchase struct {
				Amount       string `json:"amount"`
				LogoURL      string `json:"logo_url"`
				Name         string `json:"name"`
				ChainID      string `json:"chain_id"`
				BaseCurrency string `json:"base_currency"`
				UsdPrice     string `json:"usd_price"`
			} `json:"purchase"`
			TotalPrice struct {
				Amount        string `json:"amount"`
				LogoURL       string `json:"logo_url"`
				QuoteCurrency string `json:"quote_currency"`
				UsdAmount     string `json:"usd_amount"`
			} `json:"total_price"`
			PaymentStatus   string `json:"payment_status"`
			ShipmentDetails *struct {
				TxHash    string `json:"tx_hash"`
				ShippedAt int64  `json:"shipped_at"`
			} `json:"shipment_details"`
			ShippingStatus string `json:"shipping_status"`
			OrderStatus    string `json:"order_status"`
		} `json:"data"`
		Paging struct {
			PageNumber int    `json:"page_number"`
			PageSize   int    `json:"page_size"`
			TotalCount int    `json:"total_count"`
			PageSort   string `json:"page_sort"`
		} `json:"paging"`
	}
	{ // query in org(1)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/orders?page_sort=created_at:a", nil)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal(3, response.Paging.TotalCount)

		s.Equal(3, len(response.Data))
		s.Equal("test-order-id-1", response.Data[0].OrderID)
		s.Equal(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC).Unix(), response.Data[0].CreateTime)
		s.Equal("Customer1", response.Data[0].Customer.Name)
		s.Equal("phone1", *response.Data[0].Customer.Phone)
		s.Equal("email1", *response.Data[0].Customer.Email)
		s.Equal("verified", response.Data[0].Customer.KycStatus)
		s.Equal("99", response.Data[0].Purchase.Amount)
		s.Equal("token_logo_url_usdt", response.Data[0].Purchase.LogoURL)
		s.Equal("test product", response.Data[0].Purchase.Name)
		s.Equal("eth", response.Data[0].Purchase.ChainID)
		s.Equal("USDT", response.Data[0].Purchase.BaseCurrency)
		s.Equal("99", response.Data[0].Purchase.UsdPrice)
		s.Equal("3000", response.Data[0].TotalPrice.Amount)
		s.Equal("quote_token_logo_url_ntd", response.Data[0].TotalPrice.LogoURL)
		s.Equal("TWD", response.Data[0].TotalPrice.QuoteCurrency)
		s.Equal("100", response.Data[0].TotalPrice.UsdAmount)
		s.Equal("paid", response.Data[0].PaymentStatus)
		s.Equal("******************************************111111111111111111111111", response.Data[0].ShipmentDetails.TxHash)
		s.Equal(time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC).Unix(), response.Data[0].ShipmentDetails.ShippedAt)
		s.Equal("send_success", response.Data[0].ShippingStatus)
		s.Equal("delivered", response.Data[0].OrderStatus)
	}
	{ // query in org(2)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/asset_pro/orders", nil)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal(1, len(response.Data))
		s.Equal("test-order-id-2", response.Data[0].OrderID)
		s.Equal(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC).Unix(), response.Data[0].CreateTime)
		s.Equal("Customer2", response.Data[0].Customer.Name)
		s.Equal("phone2", *response.Data[0].Customer.Phone)
		s.Equal("email2", *response.Data[0].Customer.Email)
		s.Equal("unverified", response.Data[0].Customer.KycStatus)
		s.Equal("99", response.Data[0].Purchase.Amount)
		s.Equal("token_logo_url_usdt", response.Data[0].Purchase.LogoURL)
		s.Equal("USDT/TWD", response.Data[0].Purchase.Name)
		s.Equal("eth", response.Data[0].Purchase.ChainID)
		s.Equal("USDT", response.Data[0].Purchase.BaseCurrency)
		s.Equal("99", response.Data[0].Purchase.UsdPrice)
		s.Equal("3000", response.Data[0].TotalPrice.Amount)
		s.Equal("quote_token_logo_url_ntd", response.Data[0].TotalPrice.LogoURL)
		s.Equal("TWD", response.Data[0].TotalPrice.QuoteCurrency)
		s.Equal("100", response.Data[0].TotalPrice.UsdAmount)
		s.Equal("unpaid", response.Data[0].PaymentStatus)
		s.Nil(response.Data[0].ShipmentDetails)
		s.Equal("not_shipped", response.Data[0].ShippingStatus)
		s.Equal("unpaid", response.Data[0].OrderStatus)
	}
}

func (s *testMarketSuite) TestListOrdersWithSortForMerchant() {
	type resp struct {
		Code int `json:"code"`
		Data []struct {
			CreateTime int64  `json:"create_time"`
			OrderID    string `json:"order_id"`
			Customer   struct {
				Name      string  `json:"name"`
				Phone     *string `json:"phone"`
				Email     *string `json:"email"`
				KycStatus string  `json:"kyc_status"`
			} `json:"customer"`
			Purchase struct {
				Amount       string `json:"amount"`
				LogoURL      string `json:"logo_url"`
				Name         string `json:"name"`
				ChainID      string `json:"chain_id"`
				BaseCurrency string `json:"base_currency"`
				UsdPrice     string `json:"usd_price"`
			} `json:"purchase"`
			TotalPrice struct {
				Amount        string `json:"amount"`
				LogoURL       string `json:"logo_url"`
				QuoteCurrency string `json:"quote_currency"`
				UsdAmount     string `json:"usd_amount"`
			} `json:"total_price"`
			PaymentStatus   string `json:"payment_status"`
			ShipmentDetails *struct {
				TxHash    string `json:"tx_hash"`
				ShippedAt int64  `json:"shipped_at"`
			} `json:"shipment_details"`
			ShippingStatus string `json:"shipping_status"`
			OrderStatus    string `json:"order_status"`
		} `json:"data"`
		Paging struct {
			PageNumber int    `json:"page_number"`
			PageSize   int    `json:"page_size"`
			TotalCount int    `json:"total_count"`
			PageSort   string `json:"page_sort"`
		} `json:"paging"`
	}
	{ // sort by price:d
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/orders?page_sort=total_cost:d", nil)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal(3, response.Paging.TotalCount)

		s.Equal("test-order-id-4", response.Data[0].OrderID)
		s.Equal("test-order-id-3", response.Data[1].OrderID)
		s.Equal("test-order-id-1", response.Data[2].OrderID)
	}
	{ // sort by price:a
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/orders?page_sort=total_cost:a", nil)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal(3, response.Paging.TotalCount)

		s.Equal("test-order-id-1", response.Data[0].OrderID)
		s.Equal("test-order-id-3", response.Data[1].OrderID)
		s.Equal("test-order-id-4", response.Data[2].OrderID)
	}
	{ // sort by created_at:d
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/orders?page_sort=created_at:d", nil)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal(3, response.Paging.TotalCount)

		s.Equal("test-order-id-4", response.Data[0].OrderID)
		s.Equal("test-order-id-3", response.Data[1].OrderID)
		s.Equal("test-order-id-1", response.Data[2].OrderID)
	}
	{ // sort by created_at:a
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/orders?page_sort=created_at:a", nil)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal(3, response.Paging.TotalCount)

		s.Equal("test-order-id-1", response.Data[0].OrderID)
		s.Equal("test-order-id-3", response.Data[1].OrderID)
		s.Equal("test-order-id-4", response.Data[2].OrderID)
	}
}

func (s *testMarketSuite) TestGetOrderDetailForMerchant() {
	type resp struct {
		Code int `json:"code"`
		Data struct {
			CreateTime  int64  `json:"create_time"`
			CancelledAt *int64 `json:"cancelled_at"`
			OrderID     string `json:"order_id"`
			Customer    struct {
				Name          string  `json:"name"`
				Phone         *string `json:"phone"`
				Email         *string `json:"email"`
				KycStatus     string  `json:"kyc_status"`
				WalletAddress string  `json:"wallet_address"`
			} `json:"customer"`
			Purchase struct {
				Amount       string `json:"amount"`
				LogoURL      string `json:"logo_url"`
				Name         string `json:"name"`
				ChainID      string `json:"chain_id"`
				BaseCurrency string `json:"base_currency"`
				UsdAmount    string `json:"usd_amount"`
				Price        string `json:"price"`
			} `json:"purchase"`
			TotalPrice struct {
				Amount                string `json:"amount"`
				LogoURL               string `json:"logo_url"`
				QuoteCurrency         string `json:"quote_currency"`
				UsdAmount             string `json:"usd_amount"`
				HandlingFee           string `json:"handling_fee"`
				HandlingFeePercentage string `json:"handling_fee_percentage"`
			} `json:"total_price"`
			PaymentStatus   string `json:"payment_status"`
			ShipmentDetails *struct {
				TxHash      string `json:"tx_hash"`
				ShippedAt   int64  `json:"shipped_at"`
				DeliveredAt int64  `json:"delivered_at"`
				TxID        int    `json:"tx_id"`
				SendTo      string `json:"send_to"`
				TxStatus    string `json:"tx_status"`
				ProcessedBy struct {
					Name       string  `json:"name"`
					Role       string  `json:"role"`
					ProfileImg *string `json:"profile_img"`
					Email      string  `json:"email"`
				} `json:"processed_by"`
			} `json:"shipment_details"`
			OrderStatus    string `json:"order_status"`
			PaymentDetails struct {
				Deadline   int64 `json:"deadline"`
				TransferTo struct {
					BankName          string `json:"bank_name"`
					BranchName        string `json:"branch_name"`
					AccountNumber     string `json:"account_number"`
					AccountHolderName string `json:"account_holder_name"`
				} `json:"transfer_to"`
				PaymentMethod        string   `json:"payment_method"`
				Note                 *string  `json:"note"`
				Attachments          []string `json:"attachments"`
				CustomerTransferTime int64    `json:"customer_transfer_time"`
				LastFiveDigits       *string  `json:"last_five_digits"`
				Customer             struct {
					Transferred struct {
						QuoteCurrency string   `json:"quote_currency"`
						UpdatedAt     int64    `json:"updated_at"`
						Attachments   []string `json:"attachments"`
					} `json:"transferred"`
				} `json:"customer"`
				LastEditedAt *int64 `json:"last_edited_at"`
				Editor       *struct {
					Name  string `json:"name"`
					Role  string `json:"role"`
					Email string `json:"email"`
				} `json:"editor"`
			} `json:"payment_details"`
			ShippingStatus string  `json:"shipping_status"`
			InternalNote   *string `json:"internal_note"`
		}
	}
	{ // uery order(test-order-id-1) in org(1)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/orders/test-order-id-1", nil)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal("test-order-id-1", response.Data.OrderID)
		s.Equal(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC).Unix(), response.Data.CreateTime)
		s.Nil(response.Data.CancelledAt)
		s.Equal("Customer1", response.Data.Customer.Name)
		s.Equal("phone1", *response.Data.Customer.Phone)
		s.Equal("email1", *response.Data.Customer.Email)
		s.Equal("******************************************", response.Data.Customer.WalletAddress)
		s.Equal("99", response.Data.Purchase.Amount)
		s.Equal("token_logo_url_usdt", response.Data.Purchase.LogoURL)
		s.Equal("test product", response.Data.Purchase.Name)
		s.Equal("eth", response.Data.Purchase.ChainID)
		s.Equal("USDT", response.Data.Purchase.BaseCurrency)
		s.Equal("30", response.Data.Purchase.Price)
		s.Equal("3000", response.Data.TotalPrice.Amount)
		s.Equal("quote_token_logo_url_ntd", response.Data.TotalPrice.LogoURL)
		s.Equal("TWD", response.Data.TotalPrice.QuoteCurrency)
		s.Equal("30", response.Data.TotalPrice.HandlingFee)
		s.Equal("1", response.Data.TotalPrice.HandlingFeePercentage)
		s.Equal("paid", response.Data.PaymentStatus)
		s.Equal("******************************************111111111111111111111111", response.Data.ShipmentDetails.TxHash)
		s.Equal(time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC).Unix(), response.Data.ShipmentDetails.ShippedAt)
		s.Equal(time.Date(2024, 1, 4, 1, 0, 0, 0, time.UTC).Unix(), response.Data.ShipmentDetails.DeliveredAt)
		s.Equal("******************************************", response.Data.ShipmentDetails.SendTo)
		s.Equal("send_success", response.Data.ShipmentDetails.TxStatus)
		s.Equal("operator1", response.Data.ShipmentDetails.ProcessedBy.Name)
		s.Equal("Admin", response.Data.ShipmentDetails.ProcessedBy.Role)
		s.Equal("https://lh3.googleusercontent.com/H4nD73fI_qNP_C4mn6d2pImSpHeQ9VRKZ5YTlpyYvYfGw00f_6NVhfvvXxLQt147_yjj-2XTODzI5B_MLFSBdGUbRX_di3Oezq59uQ", *response.Data.ShipmentDetails.ProcessedBy.ProfileImg)
		s.Equal("<EMAIL>", response.Data.ShipmentDetails.ProcessedBy.Email)
		s.Equal("delivered", response.Data.OrderStatus)
		s.Equal(time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC).Unix(), response.Data.PaymentDetails.Deadline)
		s.Equal("Mega Bank", response.Data.PaymentDetails.TransferTo.BankName)
		s.Equal("Xin Yi", response.Data.PaymentDetails.TransferTo.BranchName)
		s.Equal("55688", response.Data.PaymentDetails.TransferTo.AccountNumber)
		s.Equal("KryptoGO", response.Data.PaymentDetails.TransferTo.AccountHolderName)
		s.Equal("bank_transfer", response.Data.PaymentDetails.PaymentMethod)
		s.Equal("test payment note", *response.Data.PaymentDetails.Note)
		s.Contains(response.Data.PaymentDetails.Attachments, "payment attachment 1")
		s.Contains(response.Data.PaymentDetails.Attachments, "payment attachment 2")
		s.Equal("55555", *response.Data.PaymentDetails.LastFiveDigits)
		s.Equal("TWD", response.Data.PaymentDetails.Customer.Transferred.QuoteCurrency)
		s.Equal(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC).Unix(), response.Data.PaymentDetails.CustomerTransferTime)
		s.Equal(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC).Unix(), response.Data.PaymentDetails.Customer.Transferred.UpdatedAt)
		s.Contains(response.Data.PaymentDetails.Customer.Transferred.Attachments, "customer attachment 1")
		s.Contains(response.Data.PaymentDetails.Customer.Transferred.Attachments, "customer attachment 2")
		s.Equal(time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC).Unix(), *response.Data.PaymentDetails.LastEditedAt)
		s.Equal("operator1", response.Data.PaymentDetails.Editor.Name)
		s.Equal("Admin", response.Data.PaymentDetails.Editor.Role)
		s.Equal("<EMAIL>", response.Data.PaymentDetails.Editor.Email)
		s.Equal("send_success", response.Data.ShippingStatus)
		s.Equal("test internal note", *response.Data.InternalNote)
	}
	{ // query order(test-order-id-2) in org(1)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/orders/test-order-id-2", nil)
		req.Header.Set("UID", "customer1")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(1067, response.Code)
	}
	{ // query order(test-order-id-2) in org(2)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/asset_pro/orders/test-order-id-2", nil)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal("test-order-id-2", response.Data.OrderID)
		s.Equal(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC).Unix(), response.Data.CreateTime)
		s.Nil(response.Data.CancelledAt)
		s.Equal("Customer2", response.Data.Customer.Name)
		s.Equal("phone2", *response.Data.Customer.Phone)
		s.Equal("email2", *response.Data.Customer.Email)
		s.Equal("******************************************", response.Data.Customer.WalletAddress)
		s.Equal("99", response.Data.Purchase.Amount)
		s.Equal("token_logo_url_usdt", response.Data.Purchase.LogoURL)
		s.Equal("USDT/TWD", response.Data.Purchase.Name)
		s.Equal("eth", response.Data.Purchase.ChainID)
		s.Equal("USDT", response.Data.Purchase.BaseCurrency)
		s.Equal("30", response.Data.Purchase.Price)
		s.Equal("3000", response.Data.TotalPrice.Amount)
		s.Equal("quote_token_logo_url_ntd", response.Data.TotalPrice.LogoURL)
		s.Equal("TWD", response.Data.TotalPrice.QuoteCurrency)
		s.Equal("30", response.Data.TotalPrice.HandlingFee)
		s.Equal("1", response.Data.TotalPrice.HandlingFeePercentage)
		s.Nil(response.Data.ShipmentDetails)
		s.Equal("unpaid", response.Data.OrderStatus)
		s.Equal("unpaid", response.Data.PaymentStatus)
		s.Equal(time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC).Unix(), response.Data.PaymentDetails.Deadline)
		s.Empty(response.Data.PaymentDetails.TransferTo.BankName)
		s.Empty(response.Data.PaymentDetails.TransferTo.BranchName)
		s.Empty(response.Data.PaymentDetails.TransferTo.AccountNumber)
		s.Empty(response.Data.PaymentDetails.TransferTo.AccountHolderName)
		s.Equal("bank_transfer", response.Data.PaymentDetails.PaymentMethod)
		s.Nil(response.Data.PaymentDetails.Note)
		s.Len(response.Data.PaymentDetails.Attachments, 0)
		s.Nil(response.Data.PaymentDetails.LastFiveDigits)
		s.Empty(response.Data.PaymentDetails.Customer.Transferred.QuoteCurrency)
		s.Empty(response.Data.PaymentDetails.CustomerTransferTime)
		s.Empty(response.Data.PaymentDetails.Customer.Transferred.UpdatedAt)
		s.Len(response.Data.PaymentDetails.Customer.Transferred.Attachments, 0)
		s.Nil(response.Data.PaymentDetails.LastEditedAt)
		s.Nil(response.Data.PaymentDetails.Editor)
		s.Equal("not_shipped", response.Data.ShippingStatus)
		s.Nil(response.Data.InternalNote)
	}
	{ // query order(test-order-id-1) in org(2)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/asset_pro/orders/test-order-id-2", nil)
		req.Header.Set("UID", "customer1")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(1067, response.Code)
	}
}

func (s *testMarketSuite) TestListOrdersForCustomer() {
	type resp struct {
		Code int `json:"code"`
		Data []struct {
			OrderID  string `json:"order_id"`
			Purchase struct {
				Amount       string `json:"amount"`
				Image        string `json:"image"`
				LogoURL      string `json:"logo_url"`
				Name         string `json:"name"`
				BaseCurrency string `json:"base_currency"`
			} `json:"purchase"`
			OrderStatus   string `json:"order_status"`
			PaymentStatus string `json:"payment_status"`
			DeliveredAt   int64  `json:"delivered_at"`
		} `json:"data"`
		Message string `json:"message"`
		Paging  struct {
			NextPageToken *string `json:"next_page_token"`
		}
	}
	{ // query in org(1)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/orders", nil)
		req.Header.Set("UID", "customer1")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Empty(response.Message)
		s.Nil(response.Paging.NextPageToken)

		s.Equal(3, len(response.Data))
		s.Equal("test-order-id-1", response.Data[2].OrderID)
		s.Equal("test product", response.Data[2].Purchase.Name)
		s.Equal("USDT", response.Data[2].Purchase.BaseCurrency)
		s.Equal("image_url_usdt", response.Data[2].Purchase.Image)
		s.Equal("token_logo_url_usdt", response.Data[2].Purchase.LogoURL)
		s.Equal("99", response.Data[2].Purchase.Amount)
		s.Equal("delivered", response.Data[2].OrderStatus)
		s.Equal("paid", response.Data[2].PaymentStatus)
		s.Equal(time.Date(2024, 1, 4, 1, 0, 0, 0, time.UTC).Unix(), response.Data[2].DeliveredAt)
	}
	{ // query in org(2)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/orders", nil)
		req.Header.Set("UID", "customer2")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Nil(response.Paging.NextPageToken)

		s.Equal(1, len(response.Data))
		s.Equal("test-order-id-2", response.Data[0].OrderID)
		s.Equal("USDT/TWD", response.Data[0].Purchase.Name)
		s.Equal("USDT", response.Data[0].Purchase.BaseCurrency)
		s.Equal("image_url", response.Data[0].Purchase.Image)
		s.Equal("token_logo_url_usdt", response.Data[0].Purchase.LogoURL)
		s.Equal("99", response.Data[0].Purchase.Amount)
		s.Equal("unpaid", response.Data[0].OrderStatus)
		s.Equal("unpaid", response.Data[0].PaymentStatus)
		s.Empty(response.Data[0].DeliveredAt)
	}
	{ // query in org(2) with paging
		pageToken := util.URLSafeBase64EncodeFromInterface(struct {
			LastSeenID string `json:"last_seen_id"`
		}{LastSeenID: "test-order-id-2"})

		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			fmt.Sprintf("/v1/studio/organization/2/orders?page_token=%s", pageToken), nil)
		req.Header.Set("UID", "customer2")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal(0, len(response.Data))
	}
}

func (s *testMarketSuite) TestListActiveOrdersForCustomer() {
	type resp struct {
		Code int `json:"code"`
		Data []struct {
			OrderID  string `json:"order_id"`
			Purchase struct {
				Amount       string `json:"amount"`
				Image        string `json:"image"`
				LogoURL      string `json:"logo_url"`
				Name         string `json:"name"`
				BaseCurrency string `json:"base_currency"`
			} `json:"purchase"`
			OrderStatus   string `json:"order_status"`
			PaymentStatus string `json:"payment_status"`
			DeliveredAt   int64  `json:"delivered_at"`
		} `json:"data"`
		Message string `json:"message"`
		Paging  struct {
			NextPageToken *string `json:"next_page_token"`
		}
	}
	{ // query in org(1)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/orders?active=true", nil)
		req.Header.Set("UID", "customer1")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Empty(response.Message)
		s.Nil(response.Paging.NextPageToken)

		s.Equal(2, len(response.Data))
	}
	{ // query in org(2)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/orders?active=true", nil)
		req.Header.Set("UID", "customer2")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Nil(response.Paging.NextPageToken)

		s.Equal(1, len(response.Data))
		s.Equal("test-order-id-2", response.Data[0].OrderID)
		s.Equal("USDT/TWD", response.Data[0].Purchase.Name)
		s.Equal("USDT", response.Data[0].Purchase.BaseCurrency)
		s.Equal("image_url", response.Data[0].Purchase.Image)
		s.Equal("token_logo_url_usdt", response.Data[0].Purchase.LogoURL)
		s.Equal("99", response.Data[0].Purchase.Amount)
		s.Equal("unpaid", response.Data[0].OrderStatus)
		s.Equal("unpaid", response.Data[0].PaymentStatus)
		s.Empty(response.Data[0].DeliveredAt)
	}
}

func (s *testMarketSuite) TestGetOrderDetailForCustomer() {
	type resp struct {
		Code int `json:"code"`
		Data struct {
			OrderID     string `json:"order_id"`
			CreateTime  int64  `json:"create_time"`
			CancelledAt *int64 `json:"cancelled_at"`
			Customer    struct {
				Name          *string `json:"name"`
				Phone         *string `json:"phone"`
				Email         *string `json:"email"`
				WalletAddress string  `json:"wallet_address"`
			} `json:"customer"`
			Purchase struct {
				Amount       string `json:"amount"`
				LogoURL      string `json:"logo_url"`
				Name         string `json:"name"`
				ChainID      string `json:"chain_id"`
				BaseCurrency string `json:"base_currency"`
				Price        string `json:"price"`
			} `json:"purchase"`
			TotalPrice struct {
				Amount                string `json:"amount"`
				LogoURL               string `json:"logo_url"`
				QuoteCurrency         string `json:"quote_currency"`
				HandlingFee           string `json:"handling_fee"`
				HandlingFeePercentage string `json:"handling_fee_percentage"`
			} `json:"total_price"`
			ShipmentDetails struct {
				TxHash      string `json:"tx_hash"`
				ShippedAt   int64  `json:"shipped_at"`
				DeliveredAt int64  `json:"delivered_at"`
			} `json:"shipment_details"`
			OrderStatus    string `json:"order_status"`
			PaymentStatus  string `json:"payment_status"`
			PaymentDetails struct {
				Deadline   int64 `json:"deadline"`
				TransferTo struct {
					BankName          *string `json:"bank_name"`
					BranchName        *string `json:"branch_name"`
					AccountNumber     *string `json:"account_number"`
					AccountHolderName *string `json:"account_holder_name"`
				} `json:"transfer_to"`
				PaymentMethod        string `json:"payment_method"`
				CustomerTransferTime int64  `json:"customer_transfer_time"`
				Customer             struct {
					Transferred struct {
						QuoteCurrency string   `json:"quote_currency"`
						UpdatedAt     int64    `json:"updated_at"`
						Attachments   []string `json:"attachments"`
					} `json:"transferred"`
				} `json:"customer"`
			} `json:"payment_details"`
		} `json:"data"`
	}
	{ // customer(customer1) query order(test-order-id-1) in org(1)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/orders/test-order-id-1", nil)
		req.Header.Set("UID", "customer1")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal("test-order-id-1", response.Data.OrderID)
		s.Equal(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC).Unix(), response.Data.CreateTime)
		s.Nil(response.Data.CancelledAt)
		s.Equal("Customer1", *response.Data.Customer.Name)
		s.Equal("phone1", *response.Data.Customer.Phone)
		s.Equal("email1", *response.Data.Customer.Email)
		s.Equal("******************************************", response.Data.Customer.WalletAddress)
		s.Equal("99", response.Data.Purchase.Amount)
		s.Equal("token_logo_url_usdt", response.Data.Purchase.LogoURL)
		s.Equal("test product", response.Data.Purchase.Name)
		s.Equal("eth", response.Data.Purchase.ChainID)
		s.Equal("USDT", response.Data.Purchase.BaseCurrency)
		s.Equal("30", response.Data.Purchase.Price)
		s.Equal("3000", response.Data.TotalPrice.Amount)
		s.Equal("quote_token_logo_url_ntd", response.Data.TotalPrice.LogoURL)
		s.Equal("TWD", response.Data.TotalPrice.QuoteCurrency)
		s.Equal("30", response.Data.TotalPrice.HandlingFee)
		s.Equal("1", response.Data.TotalPrice.HandlingFeePercentage)
		s.Equal("******************************************111111111111111111111111", response.Data.ShipmentDetails.TxHash)
		s.Equal(time.Date(2024, 1, 4, 0, 0, 0, 0, time.UTC).Unix(), response.Data.ShipmentDetails.ShippedAt)
		s.Equal(time.Date(2024, 1, 4, 1, 0, 0, 0, time.UTC).Unix(), response.Data.ShipmentDetails.DeliveredAt)
		s.Equal("delivered", response.Data.OrderStatus)
		s.Equal("paid", response.Data.PaymentStatus)
		s.Equal(time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC).Unix(), response.Data.PaymentDetails.Deadline)
		s.Equal("Mega Bank", *response.Data.PaymentDetails.TransferTo.BankName)
		s.Equal("Xin Yi", *response.Data.PaymentDetails.TransferTo.BranchName)
		s.Equal("55688", *response.Data.PaymentDetails.TransferTo.AccountNumber)
		s.Equal("KryptoGO", *response.Data.PaymentDetails.TransferTo.AccountHolderName)
		s.Equal("bank_transfer", response.Data.PaymentDetails.PaymentMethod)
		s.Equal(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC).Unix(), response.Data.PaymentDetails.CustomerTransferTime)
		s.Equal(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC).Unix(), response.Data.PaymentDetails.Customer.Transferred.UpdatedAt)
		s.Contains(response.Data.PaymentDetails.Customer.Transferred.Attachments, "customer attachment 1")
		s.Contains(response.Data.PaymentDetails.Customer.Transferred.Attachments, "customer attachment 2")
	}
	{ // customer(customer1) query order(test-order-id-1) in org(2)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/orders/test-order-id-1", nil)
		req.Header.Set("UID", "customer1")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(1067, response.Code)
	}
	{ // customer(customer1) query order(test-order-id-2) in org(1)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/orders/test-order-id-1", nil)
		req.Header.Set("UID", "customer1")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(1067, response.Code)
	}
	{ // customer(customer1) query order(test-order-id-2) in org(2)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/orders/test-order-id-1", nil)
		req.Header.Set("UID", "customer1")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(1067, response.Code)
	}
	{ // customer(customer2) query order(test-order-id-2) in org(2)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/orders/test-order-id-2", nil)
		req.Header.Set("UID", "customer2")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		s.Equal("test-order-id-2", response.Data.OrderID)
		s.Equal(time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC).Unix(), response.Data.CreateTime)
		s.Nil(response.Data.CancelledAt)
		s.Equal("Customer2", *response.Data.Customer.Name)
		s.Equal("phone2", *response.Data.Customer.Phone)
		s.Equal("email2", *response.Data.Customer.Email)
		s.Equal("******************************************", response.Data.Customer.WalletAddress)
		s.Equal("99", response.Data.Purchase.Amount)
		s.Equal("token_logo_url_usdt", response.Data.Purchase.LogoURL)
		s.Equal("USDT/TWD", response.Data.Purchase.Name)
		s.Equal("eth", response.Data.Purchase.ChainID)
		s.Equal("USDT", response.Data.Purchase.BaseCurrency)
		s.Equal("30", response.Data.Purchase.Price)
		s.Equal("3000", response.Data.TotalPrice.Amount)
		s.Equal("quote_token_logo_url_ntd", response.Data.TotalPrice.LogoURL)
		s.Equal("TWD", response.Data.TotalPrice.QuoteCurrency)
		s.Equal("30", response.Data.TotalPrice.HandlingFee)
		s.Equal("1", response.Data.TotalPrice.HandlingFeePercentage)
		s.Empty(response.Data.ShipmentDetails.TxHash)
		s.Empty(response.Data.ShipmentDetails.ShippedAt)
		s.Empty(response.Data.ShipmentDetails.DeliveredAt)
		s.Equal("unpaid", response.Data.OrderStatus)
		s.Equal("unpaid", response.Data.PaymentStatus)
		s.Equal(time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC).Unix(), response.Data.PaymentDetails.Deadline)
		s.Nil(response.Data.PaymentDetails.TransferTo.BankName)
		s.Nil(response.Data.PaymentDetails.TransferTo.BranchName)
		s.Nil(response.Data.PaymentDetails.TransferTo.AccountNumber)
		s.Nil(response.Data.PaymentDetails.TransferTo.AccountHolderName)
		s.Equal("bank_transfer", response.Data.PaymentDetails.PaymentMethod)
		s.Empty(response.Data.PaymentDetails.CustomerTransferTime)
		s.Empty(response.Data.PaymentDetails.Customer.Transferred.UpdatedAt)
		s.Len(response.Data.PaymentDetails.Customer.Transferred.Attachments, 0)
	}
	{ // customer(customer2) query order(test-order-id-2) in org(1)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/orders/test-order-id-2", nil)
		req.Header.Set("UID", "customer2")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(1067, response.Code)
	}
	{ // customer(customer2) query order(test-order-id-1) in org(1)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/1/orders/test-order-id-1", nil)
		req.Header.Set("UID", "customer2")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(1067, response.Code)
	}
	{ // customer(customer2) query order(test-order-id-1) in org(2)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodGet,
			"/v1/studio/organization/2/orders/test-order-id-1", nil)
		req.Header.Set("UID", "customer2")

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(1067, response.Code)
	}
}

func TestMarketSuite(t *testing.T) {
	suite.Run(t, new(testMarketSuite))
}

type testTransferProductForMerchantSuite struct {
	suite.Suite

	operatorUID   string
	customerUID   string
	customerEmail string
	OrderID       string
	Url           string
	UrlWithParams string
	Request       func(urlWithParams string, headers http.Header, body map[string]interface{}) (httpStatus int, responseBody []byte, err error)
}

func (s *testTransferProductForMerchantSuite) SetupSuite() {
	s.Url = "/v1/studio/organization/:orgID/asset_pro/orders/:order_id/transfer"
	s.UrlWithParams = "/v1/studio/organization/%d/asset_pro/orders/%s/transfer"

	middlewareToGetOrgID := func(ctx *gin.Context) {
		orgID, _ := strconv.Atoi(ctx.Param("orgID"))
		ctx.Set("org_id", orgID)
		ctx.Next()
	}
	middlewareToGetCustomerID := func(ctx *gin.Context) {
		uid := ctx.GetHeader("UID")
		ctx.Set("uid", uid)
		ctx.Next()
	}

	s.Request = func(urlWithParams string, headers http.Header, body map[string]interface{}) (httpStatus int, responseBody []byte, err error) {
		return util.PostForTest(s.Url, urlWithParams, headers, body,
			middlewareToGetOrgID, middlewareToGetCustomerID, TransferProductForMerchant)
	}
	marketservice.Init(marketservice.InitParam{
		AssetProOrderRepo:   rdb.GormRepo(),
		AssetProProductRepo: rdb.GormRepo(),
	})
	assetpro.InitTransfer(rdb.GormRepo())
	organization.Init(organization.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	})
	customer.Init(customerRepo.NewCustomerRepo())
	alchemyapi.InitDefault()

	s.OrderID = fmt.Sprintf("%d:%d:%d", 1, time.Now().Unix(), 1)
	s.createTestOrderSeed()
}

func (s *testTransferProductForMerchantSuite) createTestOrderSeed() {
	{ // create firebase user which is used for testing
		rdb.Reset()
		users, uids := dbtest.Users()
		s.operatorUID = uids[0]
		s.customerUID = uids[1]
		s.customerEmail = *users[uids[1]].Email
		_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
		_, err := firebase.BatchCreateUsersBySeed(users)
		if err != nil {
			panic(err)
		}
	}

	db := rdb.Get()
	rdb.Reset()
	s.NoError(rdbtest.CreateStudioDefault(rdb.Get()))

	s.NoError(db.Create(&model.StudioUser{
		OrganizationID: 1,
		UID:            s.operatorUID,
		Name:           "operator1",
		Status:         "active",
		Email:          util.Ptr("<EMAIL>"),
		RoleBinding: []model.StudioRoleBinding{{
			RoleID:         4,
			OrganizationID: 1,
			UID:            s.operatorUID,
		}},
		StudioUserTransferLimitation: model.StudioUserTransferLimitation{
			DailyTransferLimit: decimal.NewFromInt(1000000),
		},
	}).Error)

	s.NoError(rdbtest.CreateAssetProProducts(rdb.Get(), 1))

	s.NoError(db.Create([]model.AssetProOrder{{
		ID:                          s.OrderID,
		OrganizationID:              1,
		CustomerUID:                 s.customerUID,
		WalletAddress:               "******************************************",
		ProductID:                   5,
		Amount:                      decimal.NewFromFloat(0.01),
		USDAmount:                   decimal.NewFromFloat(0.01),
		TotalCost:                   decimal.NewFromFloat(0.3),
		USDTotalCost:                decimal.NewFromFloat(0.3),
		Price:                       decimal.NewFromInt(30),
		ExchangeRate:                decimal.NewFromInt(30),
		FeeType:                     domain.AssetProProductFeeTypeNoFee,
		PaymentStatus:               domain.AssetProPaymentStatusPaid,
		OrderStatus:                 domain.AssetProOrderStatusDelivered,
		Deadline:                    time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
		TransferToBankName:          util.Ptr("Mega Bank"),
		TransferToBranchName:        util.Ptr("Xin Yi"),
		TransferToAccountNumber:     util.Ptr("55688"),
		TransferToAccountHolderName: util.Ptr("KryptoGO"),
		PaymentNote:                 util.Ptr("test payment note"),
		PaymentAttachments:          util.Ptr(model.StringArray{"payment attachment 1", "payment attachment 2"}),
		LastFiveDigits:              util.Ptr("55555"),
		CustomerAttachments:         util.Ptr(model.StringArray{"customer attachment 1", "customer attachment 2"}),
		InternalNote:                util.Ptr("test internal note"),
		CustomerUpdatedAt:           util.Ptr(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC)),
		CancelledAt:                 nil,
		RefundedAt:                  nil,
		CreatedAt:                   time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
		UpdatedAt:                   time.Date(2024, 1, 5, 0, 0, 0, 0, time.UTC),
		DeleteAt:                    nil,
		Customer: model.Customer{
			OrganizationID: 1,
			UID:            s.customerUID,
			Country:        util.Ptr("country1"),
			NationalID:     util.Ptr("national_id1"),
			Email:          util.Ptr(s.customerEmail),
			LegalName:      util.Ptr("Customer1"),
			KYCStatus:      domain.KycStatusVerified,
		},
	},
	}).Error)

}

func (s *testTransferProductForMerchantSuite) TestNormal() {
	testutil.RequireConfigOrSkip(s.T(), "TEST_3RD_PARTY_API")

	shutdownSigningServer := setupSigningServer(s.T())
	defer shutdownSigningServer()

	s.NoError(rdb.Get().Model(&model.AssetProOrder{}).Where("id = ?", s.OrderID).Updates(map[string]interface{}{
		"payment_status": domain.AssetProPaymentStatusPaid,
		"order_status":   domain.AssetProOrderStatusAwaitingShipment,
	}).Error)

	type resp struct {
		Code int `json:"code"`
	}
	{
		httpStatus, respBody, err := s.Request(fmt.Sprintf(s.UrlWithParams, 1, s.OrderID),
			http.Header{"UID": []string{s.operatorUID}}, nil)
		s.NoError(err)
		s.Equal(http.StatusOK, httpStatus)

		var response resp
		s.NoError(json.Unmarshal(respBody, &response))
		s.Equal(0, response.Code)
		// check order detail
		order := model.AssetProOrder{}
		s.NoError(rdb.Get().Where("id = ?", s.OrderID).Preload("TxLog").First(&order).Error)
		s.Equal(domain.AssetProOrderStatusShipping, order.OrderStatus)
		s.NotEmpty(order.TxLog.ID)
		s.NotEmpty(order.TxLog.TxHash)
		s.True(order.TxLog.SubmitTime > 0)
		s.Equal(s.operatorUID, order.TxLog.OperatorUID)
		s.True((*order.OperatorUpdatedAt).Unix() > 0)

		// check asset_pro tx
		assetProTx := model.AssetProTxLog{}
		s.NoError(rdb.Get().Where("id = ?", order.TxID).First(&assetProTx).Error)
		s.Equal(domain.KycStatusVerified, *assetProTx.RecipientKycStatus)
		s.Equal(s.customerEmail, *assetProTx.RecipientEmail)
	}
}

func TestTransferProductForMerchant(t *testing.T) {
	suite.Run(t, new(testTransferProductForMerchantSuite))
}

func setupSigningServer(t *testing.T) func() {
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	// signing server
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", signing.AuthorizeSigningServer, signing.SignEvmTransaction)
	// create HTTP server for graceful shutdown
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)

	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
	time.Sleep(1 * time.Second) // wait for server to start

	return func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}
}

type testUpdateOrderForMerchantSuite struct {
	suite.Suite

	operatorUID   string
	OrderID       string
	Url           string
	UrlWithParams string
	Request       func(urlWithParams string, headers http.Header, body map[string]interface{}) (httpStatus int, responseBody []byte, err error)
}

func (s *testUpdateOrderForMerchantSuite) SetupSuite() {
	s.Url = "/v1/studio/organization/:orgID/asset_pro/orders/:order_id"
	s.UrlWithParams = "/v1/studio/organization/%d/asset_pro/orders/%s"

	middlewareToGetOrgID := func(ctx *gin.Context) {
		orgID, _ := strconv.Atoi(ctx.Param("orgID"))
		ctx.Set("org_id", orgID)
		ctx.Next()
	}
	middlewareToGetCustomerID := func(ctx *gin.Context) {
		uid := ctx.GetHeader("UID")
		ctx.Set("uid", uid)
		ctx.Next()
	}

	s.Request = func(urlWithParams string, headers http.Header, body map[string]interface{}) (httpStatus int, responseBody []byte, err error) {
		return util.PatchForTest(s.Url, urlWithParams, headers, body,
			middlewareToGetOrgID, middlewareToGetCustomerID, UpdateOrderForMerchant)
	}
	marketservice.Init(marketservice.InitParam{
		AssetProOrderRepo: rdb.GormRepo(),
		// AssetProProductRepo: rdb.NewGormAssetProProductRepo(rdb.Get()),
	})

	organization.Init(organization.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	})

	s.OrderID = fmt.Sprintf("%d:%d:%d", 1, time.Now().Unix(), 1)
	s.createTestOrderSeed()
}

func (s *testUpdateOrderForMerchantSuite) createTestOrderSeed() {
	{ // create firebase user which is used for testing
		rdb.Reset()
		users, uids := dbtest.Users()
		s.operatorUID = uids[0]
		_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
		_, err := firebase.BatchCreateUsersBySeed(users)
		if err != nil {
			panic(err)
		}
	}

	db := rdb.Get()
	rdb.Reset()
	s.NoError(rdbtest.CreateStudioDefault(rdb.Get()))

	s.NoError(db.Create(&model.StudioUser{
		OrganizationID: 1,
		UID:            s.operatorUID,
		Name:           "operator1",
		Status:         "active",
		Email:          util.Ptr("<EMAIL>"),
		RoleBinding: []model.StudioRoleBinding{{
			RoleID:         4,
			OrganizationID: 1,
			UID:            s.operatorUID,
		}},
		StudioUserTransferLimitation: model.StudioUserTransferLimitation{
			DailyTransferLimit: decimal.NewFromInt(1000000),
		},
	}).Error)

	s.NoError(db.Create(&model.Customer{OrganizationID: 2, UID: "testuser2"}).Error)
	s.NoError(rdbtest.CreateAssetProProducts(rdb.Get(), 1))

	s.NoError(db.Create([]model.AssetProOrder{{
		ID:                          s.OrderID,
		OrganizationID:              1,
		CustomerUID:                 "customer1",
		WalletAddress:               "******************************************",
		ProductID:                   1,
		Amount:                      decimal.NewFromInt(99),
		USDAmount:                   decimal.NewFromInt(99),
		TotalCost:                   decimal.NewFromInt(3000),
		USDTotalCost:                decimal.NewFromInt(100),
		Price:                       decimal.NewFromInt(30),
		ExchangeRate:                decimal.NewFromInt(30),
		FeeType:                     domain.AssetProProductFeeTypeFeeIncluded,
		ProportionalFeePercentage:   util.Ptr(decimal.NewFromFloat(1.0)),
		ProportionalMinimumFee:      util.Ptr(decimal.NewFromInt(1)),
		PaymentStatus:               domain.AssetProPaymentStatusPaid,
		OrderStatus:                 domain.AssetProOrderStatusDelivered,
		Deadline:                    time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC),
		TransferToBankName:          util.Ptr("Mega Bank"),
		TransferToBranchName:        util.Ptr("Xin Yi"),
		TransferToAccountNumber:     util.Ptr("55688"),
		TransferToAccountHolderName: util.Ptr("KryptoGO"),
		PaymentNote:                 util.Ptr("test payment note"),
		PaymentAttachments:          util.Ptr(model.StringArray{"payment attachment 1", "payment attachment 2"}),
		LastFiveDigits:              util.Ptr("55555"),
		CustomerAttachments:         util.Ptr(model.StringArray{"customer attachment 1", "customer attachment 2"}),
		InternalNote:                util.Ptr("test internal note"),
		CustomerUpdatedAt:           util.Ptr(time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC)),
		CancelledAt:                 nil,
		RefundedAt:                  nil,
		CreatedAt:                   time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
		UpdatedAt:                   time.Date(2024, 1, 5, 0, 0, 0, 0, time.UTC),
		DeleteAt:                    nil,
		Customer: model.Customer{
			OrganizationID: 1,
			UID:            "customer1",
			Country:        util.Ptr("country1"),
			NationalID:     util.Ptr("national_id1"),
			Email:          util.Ptr("email1"),
			Phone:          util.Ptr("phone1"),
			LegalName:      util.Ptr("Customer1"),
			KYCStatus:      domain.KycStatusVerified,
		},
	},
	}).Error)

}

func (s *testUpdateOrderForMerchantSuite) TestNormal() {
	type resp struct {
		Code int `json:"code"`
	}
	{
		payload := map[string]interface{}{
			"internal_note": "test internal note updated",
		}
		httpStatus, respBody, err := s.Request(fmt.Sprintf(s.UrlWithParams, 1, s.OrderID),
			http.Header{"UID": []string{s.operatorUID}}, payload)
		s.NoError(err)
		s.Equal(http.StatusOK, httpStatus)

		var response resp
		s.NoError(json.Unmarshal(respBody, &response))
		s.Equal(0, response.Code)
		// check order detail
		order := model.AssetProOrder{}
		s.NoError(rdb.Get().Where("id = ?", s.OrderID).First(&order).Error)
		s.Equal("test internal note updated", *order.InternalNote)
		s.NotEmpty(*order.OperatorUpdatedAt)
		s.Equal(s.operatorUID, *order.OperatorUID)
	}
}

func TestUpdateOrderForMerchantSuite(t *testing.T) {
	suite.Run(t, new(testUpdateOrderForMerchantSuite))
}
