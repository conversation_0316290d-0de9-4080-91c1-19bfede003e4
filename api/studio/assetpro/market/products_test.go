package market

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	marketUsecase "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro/market"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestGetProductsForMerchantWithPublished(t *testing.T) {
	s := assert.New(t)

	rdb.Reset()
	orgID := 1
	marketUsecase.Init(marketUsecase.InitParam{
		AssetProProductRepo: rdb.GormRepo(),
	})
	s.NoError(rdbtest.CreateStudioOrganizations(rdb.Get()))
	s.NoError(rdbtest.CreateDefaultStudioUsers(rdb.Get()))
	s.NoError(rdbtest.CreateAssetProProducts(rdb.Get(), orgID))

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/asset_pro/products", auth.MockOrgID(orgID), GetProductsForMerchant)

	w := httptest.NewRecorder()
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/products?%s", orgID, "is_published=true"), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	type product struct {
		ID             int `json:"product_id"`
		OrganizationID int `json:"organization_id"`
		Operator       struct {
			Name string `json:"name"`
		} `json:"operator"`
		IsPublished               bool    `json:"is_published"`
		ChainID                   string  `json:"chain_id"`
		BaseCurrency              string  `json:"base_currency"`
		QuoteCurrency             string  `json:"quote_currency"`
		Type                      string  `json:"type"`
		LogoUrl                   string  `json:"logo_url"`
		Image                     string  `json:"image"`
		Name                      string  `json:"name"`
		Price                     *string `json:"price"`
		OrderLimitsFrom           *string `json:"order_limits_from"`
		OrderLimitsTo             *string `json:"order_limits_to"`
		FeeType                   string  `json:"fee_type"`
		ProportionalFeePercentage *string `json:"proportional_fee_percentage"`
		ProportionalMinimumFee    *string `json:"proportional_minimum_fee"`
		Stock                     *string `json:"stock"`
		CreatedAt                 int64   `json:"created_at"`
		UpdatedAt                 int64   `json:"updated_at"`
	}
	var response struct {
		Code int       `json:"code"`
		Data []product `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	s.Equal(0, response.Code)
	s.Len(response.Data, 4)
	s.Equal(0, response.Code)

	// order by stock desc, name asc
	// check product 1
	// check product 1
	s.Equal(1, response.Data[1].ID)
	s.Equal(orgID, response.Data[1].OrganizationID)
	s.Equal("matic", response.Data[1].ChainID)
	s.Equal("USDC", response.Data[1].BaseCurrency)
	s.Equal("TWD", response.Data[1].QuoteCurrency)
	s.Equal("buy_crypto", response.Data[1].Type)
	s.Equal("https://token-icons.s3.amazonaws.com/******************************************.png", response.Data[1].LogoUrl)
	s.Equal("new_image", response.Data[1].Image)
	s.Equal("new_name", response.Data[1].Name)
	s.Equal("28.5", *response.Data[1].Price)
	s.Equal("200.5", *response.Data[1].OrderLimitsFrom)
	s.Equal("1000.5", *response.Data[1].OrderLimitsTo)
	s.Equal("fee_included", response.Data[1].FeeType)
	s.Equal("1", *response.Data[1].ProportionalFeePercentage)
	s.Equal("50.12", *response.Data[1].ProportionalMinimumFee)
	s.Equal("1230.5", *response.Data[1].Stock)
	s.True(response.Data[1].CreatedAt > 0)
	s.True(response.Data[1].UpdatedAt > 0)
	s.Equal("testuser1", response.Data[1].Operator.Name)

	// check product 3
	s.Equal(3, response.Data[2].ID)
	s.Equal(orgID, response.Data[2].OrganizationID)
	s.Equal("tron", response.Data[2].ChainID)
	s.Equal("USDC", response.Data[2].BaseCurrency)
	s.Equal("TWD", response.Data[2].QuoteCurrency)
	s.Equal("buy_crypto", response.Data[2].Type)
	s.Equal("https://token-icons.s3.amazonaws.com/******************************************.png", response.Data[2].LogoUrl)
	s.Equal("https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_tron.png", response.Data[2].Image)
	s.Equal("USDC/TWD(Tron)", response.Data[2].Name)
	s.Equal("20.5", *response.Data[2].Price)
	s.Equal("200.5", *response.Data[2].OrderLimitsFrom)
	s.Equal("1000000000", *response.Data[2].OrderLimitsTo)
	s.Equal("no_fee", response.Data[2].FeeType)
	s.Nil(response.Data[2].ProportionalFeePercentage)
	s.Nil(response.Data[2].ProportionalMinimumFee)
	s.Equal("1000", *response.Data[2].Stock)
	s.True(response.Data[2].CreatedAt > 0)
	s.True(response.Data[2].UpdatedAt > 0)

	// check product 4
	s.Equal(4, response.Data[3].ID)
	s.Equal(orgID, response.Data[3].OrganizationID)
	s.Equal("tron", response.Data[3].ChainID)
	s.Equal("USDT", response.Data[3].BaseCurrency)
	s.Equal("TWD", response.Data[3].QuoteCurrency)
	s.Equal("buy_crypto", response.Data[3].Type)
	s.Equal("https://token-icons.s3.amazonaws.com/******************************************.png", response.Data[3].LogoUrl)
	s.Equal("https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_tron.png", response.Data[3].Image)
	s.Equal("A USDT", response.Data[3].Name)
	s.Nil(response.Data[3].Price)
	s.Nil(response.Data[3].OrderLimitsFrom)
	s.Nil(response.Data[3].OrderLimitsTo)
	s.Equal("fee_included", response.Data[3].FeeType)
	s.Equal("0.5", *response.Data[3].ProportionalFeePercentage)
	s.Equal("10", *response.Data[3].ProportionalMinimumFee)
	s.Equal("100", *response.Data[3].Stock)
	s.True(response.Data[3].CreatedAt > 0)
	s.True(response.Data[3].UpdatedAt > 0)
	s.Equal("testuser2", response.Data[3].Operator.Name)
}

func TestGetProductsForMerchantWithSort(t *testing.T) {
	s := assert.New(t)

	rdb.Reset()
	orgID := 1
	marketUsecase.Init(marketUsecase.InitParam{
		AssetProProductRepo: rdb.GormRepo(),
	})
	s.NoError(rdbtest.CreateStudioOrganizations(rdb.Get()))
	s.NoError(rdbtest.CreateDefaultStudioUsers(rdb.Get()))
	s.NoError(rdbtest.CreateAssetProProducts(rdb.Get(), orgID))

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/asset_pro/products", auth.MockOrgID(orgID), GetProductsForMerchant)

	w := httptest.NewRecorder()
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/products?page_sort=stock:a&is_published=true", orgID), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	type product struct {
		ID                        int     `json:"product_id"`
		OrganizationID            int     `json:"organization_id"`
		ChainID                   string  `json:"chain_id"`
		BaseCurrency              string  `json:"base_currency"`
		QuoteCurrency             string  `json:"quote_currency"`
		Type                      string  `json:"type"`
		LogoUrl                   string  `json:"logo_url"`
		Image                     string  `json:"image"`
		Name                      string  `json:"name"`
		Price                     *string `json:"price"`
		OrderLimitsFrom           *string `json:"order_limits_from"`
		OrderLimitsTo             *string `json:"order_limits_to"`
		FeeType                   string  `json:"fee_type"`
		ProportionalFeePercentage *string `json:"proportional_fee_percentage"`
		ProportionalMinimumFee    *string `json:"proportional_minimum_fee"`
		Stock                     *string `json:"stock"`
		CreatedAt                 int64   `json:"created_at"`
		UpdatedAt                 int64   `json:"updated_at"`
	}
	var response struct {
		Code int       `json:"code"`
		Data []product `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	s.Equal(0, response.Code)
	s.Len(response.Data, 4)
	s.Equal(0, response.Code)

	// order by stock desc, name asc
	s.Equal(4, response.Data[0].ID)
	s.Equal(3, response.Data[1].ID)
	s.Equal(1, response.Data[2].ID)
	s.Equal(5, response.Data[3].ID)
}

func TestGetProductsForMerchantWithPriceSort(t *testing.T) {
	s := assert.New(t)

	rdb.Reset()
	orgID := 1
	marketUsecase.Init(marketUsecase.InitParam{
		AssetProProductRepo: rdb.GormRepo(),
	})
	s.NoError(rdbtest.CreateStudioOrganizations(rdb.Get()))
	s.NoError(rdbtest.CreateDefaultStudioUsers(rdb.Get()))
	s.NoError(rdbtest.CreateAssetProProducts(rdb.Get(), orgID))

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/asset_pro/products", auth.MockOrgID(orgID), GetProductsForMerchant)

	w := httptest.NewRecorder()
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/products?page_sort=price:d&is_published=true", orgID), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	type product struct {
		ID                        int     `json:"product_id"`
		OrganizationID            int     `json:"organization_id"`
		ChainID                   string  `json:"chain_id"`
		BaseCurrency              string  `json:"base_currency"`
		QuoteCurrency             string  `json:"quote_currency"`
		Type                      string  `json:"type"`
		LogoUrl                   string  `json:"logo_url"`
		Image                     string  `json:"image"`
		Name                      string  `json:"name"`
		Price                     *string `json:"price"`
		OrderLimitsFrom           *string `json:"order_limits_from"`
		OrderLimitsTo             *string `json:"order_limits_to"`
		FeeType                   string  `json:"fee_type"`
		ProportionalFeePercentage *string `json:"proportional_fee_percentage"`
		ProportionalMinimumFee    *string `json:"proportional_minimum_fee"`
		Stock                     *string `json:"stock"`
		CreatedAt                 int64   `json:"created_at"`
		UpdatedAt                 int64   `json:"updated_at"`
	}
	var response struct {
		Code int       `json:"code"`
		Data []product `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	s.Equal(0, response.Code)
	s.Len(response.Data, 4)
	s.Equal(0, response.Code)

	// order by stock desc, name asc
	s.Equal(1, response.Data[0].ID)
	s.Equal(3, response.Data[1].ID)
	s.Equal(5, response.Data[2].ID)
	s.Equal(4, response.Data[3].ID)
}

func TestUpdateProductForMerchant(t *testing.T) {
	s := assert.New(t)
	rdb.Reset()
	orgID := 1
	productID := 2
	operatorUID := "uid1"
	marketUsecase.Init(marketUsecase.InitParam{
		AssetProProductRepo: rdb.GormRepo(),
	})
	s.NoError(rdbtest.CreateStudioOrganizations(rdb.Get()))
	s.NoError(rdbtest.CreateDefaultStudioUsers(rdb.Get()))
	s.NoError(rdbtest.CreateAssetProProducts(rdb.Get(), orgID))

	r := gin.Default()
	r.PUT("/v1/studio/organization/:orgID/asset_pro/products/:product_id", auth.MockOrgID(orgID), auth.MockAuthorize(operatorUID), UpdateProductForMerchant)

	payload := map[string]interface{}{
		"is_published":                true,
		"name":                        "USDT/TWD(Polygon)",
		"fee_type":                    "fee_included",
		"proportional_fee_percentage": "0.5",
		"proportional_minimum_fee":    "10",
		"price":                       "28.5",
		"order_limits_from":           "200",
		"order_limits_to":             "3000",
	}

	w := httptest.NewRecorder()
	body, _ := json.Marshal(payload)
	req, err := http.NewRequest("PUT", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/products/%d", orgID, productID), bytes.NewBuffer(body))
	s.NoError(err)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response struct {
		Code int `json:"code"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	s.Equal(0, response.Code)

	updatedData := &model.AssetProProduct{}
	s.NoError(rdb.Get().Where("organization_id = ? and id = ?", orgID, productID).First(updatedData).Error)
	s.Equal(payload["is_published"], updatedData.IsPublished)
	s.Equal(payload["price"], (*updatedData.Price).String())
	s.Equal(payload["order_limits_from"], (*updatedData.OrderLimitsFrom).String())
	s.Equal(payload["order_limits_to"], (*updatedData.OrderLimitsTo).String())
	s.Nil(updatedData.Stock)
	s.Equal(payload["name"], updatedData.Name)
	s.Equal("fee_included", updatedData.FeeType.String())
	s.True((*updatedData).ProportionalFeePercentage.Equal(decimal.NewFromFloat(0.5)))
	s.True((*updatedData).ProportionalMinimumFee.Equal(decimal.NewFromFloat(10.0)))
}

func TestGetProductsForCustomer(t *testing.T) {
	s := assert.New(t)
	rdb.Reset()
	orgID := 1
	marketUsecase.Init(marketUsecase.InitParam{
		AssetProProductRepo: rdb.GormRepo(),
	})
	s.NoError(rdbtest.CreateStudioOrganizations(rdb.Get()))
	s.NoError(rdbtest.CreateDefaultStudioUsers(rdb.Get()))
	s.NoError(rdbtest.CreateAssetProProducts(rdb.Get(), orgID))

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/products", GetProductsForCustomer)

	w := httptest.NewRecorder()
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/products", orgID), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response struct {
		Code int `json:"code"`
		Data []struct {
			ID                        int     `json:"product_id"`
			OrganizationID            int     `json:"organization_id"`
			ChainID                   string  `json:"chain_id"`
			BaseCurrency              string  `json:"base_currency"`
			QuoteCurrency             string  `json:"quote_currency"`
			Type                      string  `json:"type"`
			LogoUrl                   string  `json:"logo_url"`
			Image                     string  `json:"image"`
			Name                      string  `json:"name"`
			Price                     *string `json:"price"`
			OrderLimitsFrom           *string `json:"order_limits_from"`
			OrderLimitsTo             *string `json:"order_limits_to"`
			FeeType                   string  `json:"fee_type"`
			ProportionalFeePercentage *string `json:"proportional_fee_percentage"`
			ProportionalMinimumFee    *string `json:"proportional_minimum_fee"`
			Decimals                  int     `json:"decimals"`
		} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	s.Equal(0, response.Code)
	s.Len(response.Data, 4)

	// check product 1
	s.Equal(1, response.Data[1].ID)
	s.Equal(orgID, response.Data[1].OrganizationID)
	s.Equal("matic", response.Data[1].ChainID)
	s.Equal("USDC", response.Data[1].BaseCurrency)
	s.Equal("TWD", response.Data[1].QuoteCurrency)
	s.Equal("buy_crypto", response.Data[1].Type)
	s.Equal("https://token-icons.s3.amazonaws.com/******************************************.png", response.Data[1].LogoUrl)
	s.Equal("new_image", response.Data[1].Image)
	s.Equal("new_name", response.Data[1].Name)
	s.Equal("28.5", *response.Data[1].Price)
	s.Equal("200.5", *response.Data[1].OrderLimitsFrom)
	s.Equal("1000.5", *response.Data[1].OrderLimitsTo)
	s.Equal("fee_included", response.Data[1].FeeType)
	s.Equal("1", *response.Data[1].ProportionalFeePercentage)
	s.Equal("50.12", *response.Data[1].ProportionalMinimumFee)
	s.Equal(6, response.Data[1].Decimals)

	// check product 3
	s.Equal(3, response.Data[2].ID)
	s.Equal(orgID, response.Data[2].OrganizationID)
	s.Equal("tron", response.Data[2].ChainID)
	s.Equal("USDC", response.Data[2].BaseCurrency)
	s.Equal("TWD", response.Data[2].QuoteCurrency)
	s.Equal("buy_crypto", response.Data[2].Type)
	s.Equal("https://token-icons.s3.amazonaws.com/******************************************.png", response.Data[2].LogoUrl)
	s.Equal("https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdc_tron.png", response.Data[2].Image)
	s.Equal("USDC/TWD(Tron)", response.Data[2].Name)
	s.Equal("20.5", *response.Data[2].Price)
	s.Equal("200.5", *response.Data[2].OrderLimitsFrom)
	s.Equal("1000000000", *response.Data[2].OrderLimitsTo)
	s.Equal("no_fee", response.Data[2].FeeType)
	s.Nil(response.Data[2].ProportionalFeePercentage)
	s.Nil(response.Data[2].ProportionalMinimumFee)
	s.Equal(6, response.Data[2].Decimals)

	// check product 4
	s.Equal(4, response.Data[3].ID)
	s.Equal(orgID, response.Data[3].OrganizationID)
	s.Equal("tron", response.Data[3].ChainID)
	s.Equal("USDT", response.Data[3].BaseCurrency)
	s.Equal("TWD", response.Data[3].QuoteCurrency)
	s.Equal("buy_crypto", response.Data[3].Type)
	s.Equal("https://token-icons.s3.amazonaws.com/******************************************.png", response.Data[3].LogoUrl)
	s.Equal("https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/assets/images/usdt_tron.png", response.Data[3].Image)
	s.Equal("A USDT", response.Data[3].Name)
	s.Nil(response.Data[3].Price)
	s.Nil(response.Data[3].OrderLimitsFrom)
	s.Nil(response.Data[3].OrderLimitsTo)
	s.Equal("fee_included", response.Data[3].FeeType)
	s.Equal("0.5", *response.Data[3].ProportionalFeePercentage)
	s.Equal("10", *response.Data[3].ProportionalMinimumFee)
	s.Equal(6, response.Data[3].Decimals)
}

func TestSyncNewArrivalProduct(t *testing.T) {
	s := assert.New(t)

	rdb.Reset()
	orgID := 1
	marketUsecase.Init(marketUsecase.InitParam{
		AssetProProductRepo: rdb.GormRepo(),
	})
	organization.Init(organization.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})
	s.NoError(rdbtest.CreateStudioOrganizations(rdb.Get()))
	s.NoError(rdbtest.CreateDefaultStudioUsers(rdb.Get()))
	s.NoError(rdbtest.CreateAssetProProducts(rdb.Get(), orgID))

	db := rdb.Get()

	s.NoError(db.Create(&model.StudioMarket{
		OrganizationID:       1,
		MarketCode:           "kryptogo",
		ClientID:             "kryptogo-asset-pro-market",
		MarketURL:            "https://store-dev.kryptogo.com/kryptogo",
		Title:                "KryptoGO marketplace",
		Logo:                 "https://static.kryptogo.com/logo/market/kryptogo.png",
		Email:                util.Ptr("<EMAIL>"),
		Phone:                nil,
		LineID:               util.Ptr("kryptogo"),
		PaymentExpirationSec: 86400,
		Introduction:         util.Ptr("KryptoGO marketplace"),
		PaymentMethod:        "bank_transfer",
		PaymentCurrency:      "TWD",
		BankName:             util.Ptr("Mega Bank"),
		BranchName:           util.Ptr("Xin Yi"),
		BankAccount:          util.Ptr("168"),
		AccountHolderName:    util.Ptr("KryptoGO 168"),
	}).Error)

	var countOfProduct int64
	s.NoError(db.Model(&model.AssetProProduct{}).Where("organization_id = ?", orgID).Count(&countOfProduct).Error)
	s.NotZero(countOfProduct)

	newProduct := model.AssetProProductBaseInfo{
		Name:           "new arrival",
		ChainID:        "www",
		BaseCurrency:   domain.AssetProProductBaseCurrencyUSDC,
		QuoteCurrency:  domain.AssetProProductQuoteCurrencyTWD,
		Type:           domain.AssetProProductTypeBuyCrypto,
		Image:          "https://helloworld.png",
		TokenLogo:      "https://helloworld.png",
		QuoteTokenLogo: "https://helloworld.png",
	}

	s.NoError(db.Create(&newProduct).Error)

	r := gin.Default()
	r.POST("/_v/studio/asset_pro/market/product_arrival", SyncNewArrivalProduct)

	w := httptest.NewRecorder()
	req, err := http.NewRequest(http.MethodPost, "/_v/studio/asset_pro/market/product_arrival", nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var countOfProductAfter int64
	s.NoError(db.Model(&model.AssetProProduct{}).Where("organization_id = ?", orgID).Count(&countOfProductAfter).Error)
	s.Equal(countOfProduct+1, countOfProductAfter)

	var product model.AssetProProduct
	s.NoError(db.Where("organization_id = ? and product_base_info_id = ?", orgID, newProduct.ID).First(&product).Error)
}
