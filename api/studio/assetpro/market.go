package assetpro

import (
	"net/http"
	"net/url"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro/market"
	orgservice "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
)

type marketInfo struct {
	Title                string  `json:"title"`
	Logo                 string  `json:"logo"`
	Introduction         *string `json:"introduction"`
	Email                *string `json:"email"`
	Phone                *string `json:"phone"`
	Telegram             *string `json:"telegram"`
	Discord              *string `json:"discord"`
	Twitter              *string `json:"twitter"`
	LineID               *string `json:"line_id"`
	PaymentMethod        string  `json:"payment_method"`
	PaymentCurrency      string  `json:"payment_currency"`
	BankName             *string `json:"bank_name"`
	BranchName           *string `json:"branch_name"`
	BankAccount          *string `json:"bank_account"`
	AccountHolderName    *string `json:"bank_account_holder_name"`
	PaymentExpirationSec int     `json:"payment_expiration_sec"`
}

func (info *marketInfo) fromDomain(marketInfo *domain.StudioMarket) {
	info.Title = marketInfo.Title
	info.Logo = marketInfo.Logo
	info.Introduction = marketInfo.Introduction
	info.Email = marketInfo.Email
	info.Phone = marketInfo.Phone
	info.LineID = marketInfo.LineID
	info.Telegram = marketInfo.Telegram
	info.Discord = marketInfo.Discord
	info.Twitter = marketInfo.Twitter
	info.PaymentMethod = string(marketInfo.PaymentMethod)
	info.PaymentCurrency = marketInfo.PaymentCurrency
	info.BankName = marketInfo.BankName
	info.BranchName = marketInfo.BranchName
	info.BankAccount = marketInfo.BankAccount
	info.AccountHolderName = marketInfo.AccountHolderName
	info.PaymentExpirationSec = marketInfo.PaymentExpirationSec
}

type marketInfoForMerchant struct {
	marketInfo
	MarketURL string `json:"market_url"`
}

func (info *marketInfoForMerchant) fromDomain(marketInfo *domain.StudioMarket) {
	info.MarketURL = marketInfo.MarketURL
	info.marketInfo.fromDomain(marketInfo)
}

// GetMarketInfoForMerchant get market info.
func GetMarketInfoForMerchant(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")
	info, kgErr := orgservice.GetMarketByOrg(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	var resp marketInfoForMerchant
	resp.fromDomain(info)

	response.OK(ctx, resp)
}

type marketInfoForCustomer struct {
	OrganizationID int     `json:"organization_id"`
	OauthClientID  string  `json:"oauth_client_id"`
	ComplyflowURL  *string `json:"complyflow_url"`
	marketInfo
}

func (info *marketInfoForCustomer) fromDomain(marketInfo *domain.StudioMarket) {
	info.OrganizationID = marketInfo.OrganizationID
	info.OauthClientID = marketInfo.OauthClientID
	info.ComplyflowURL = marketInfo.ComplyflowURL
	info.marketInfo.fromDomain(marketInfo)
}

type getMarketInfoForCustomerParams struct {
	MarketCode string `form:"market_code" binding:"required"`
}

// GetMarketInfoForCustomer get market info.
func GetMarketInfoForCustomer(ctx *gin.Context) {
	params := getMarketInfoForCustomerParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	info, kgErr := orgservice.GetMarketByMarketCode(ctx.Request.Context(), params.MarketCode)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	modules, kgErr := orgservice.GetOrgEnabledModules(ctx.Request.Context(), info.OrganizationID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if !lo.Contains(modules.AssetPro, domain.AssetProMarket) {
		response.KGError(ctx, code.NewKGError(code.StudioMarketNotEnabled, http.StatusBadRequest, nil, nil))
		return
	}

	var resp marketInfoForCustomer
	resp.fromDomain(info)

	response.OK(ctx, resp)
}

type updateMarketInfoReq struct {
	Title                string  `json:"title" binding:"required"`
	Logo                 string  `json:"logo" binding:"required"`
	Email                *string `json:"email"`
	Phone                *string `json:"phone"`
	LineID               *string `json:"line_id"`
	Telegram             *string `json:"telegram"`
	Discord              *string `json:"discord" `
	Twitter              *string `json:"twitter"`
	Introduction         *string `json:"introduction"`
	PaymentExpirationSec int     `json:"payment_expiration_sec" binding:"required,gte=600,lte=86400"`
	PaymentMethod        string  `json:"payment_method" binding:"required"`
	PaymentCurrency      string  `json:"payment_currency" binding:"required"`
	BankName             string  `json:"bank_name" binding:"required"`
	BranchName           string  `json:"branch_name" binding:"required"`
	BankAccount          string  `json:"bank_account" binding:"required"`
	AccountHolderName    string  `json:"bank_account_holder_name" binding:"required"`
}

// SaveMarketInfoForMerchant updates market info.
func SaveMarketInfoForMerchant(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")

	req := updateMarketInfoReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	kgErr = orgservice.SaveMarketInfo(ctx.Request.Context(), domain.SaveStudioMarketInfoRequest{
		OrganizationID:       orgID,
		Title:                req.Title,
		Logo:                 req.Logo,
		Email:                req.Email,
		Phone:                req.Phone,
		LineID:               req.LineID,
		Telegram:             req.Telegram,
		Discord:              req.Discord,
		Twitter:              req.Twitter,
		Introduction:         req.Introduction,
		PaymentMethod:        domain.PaymentMethod(req.PaymentMethod),
		PaymentCurrency:      req.PaymentCurrency,
		BankName:             req.BankName,
		BranchName:           req.BranchName,
		BankAccount:          req.BankAccount,
		AccountHolderName:    req.AccountHolderName,
		PaymentExpirationSec: req.PaymentExpirationSec,
	})
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, nil)
}

type upsertMarketInfoReq struct {
	OauthClientID        string  `json:"oauth_client_id" binding:"required"`
	Title                string  `json:"title" binding:"required"`
	Logo                 string  `json:"logo" binding:"required"`
	MarketCode           string  `json:"market_code" binding:"required"`
	Email                *string `json:"email"`
	Phone                *string `json:"phone"`
	LineID               *string `json:"line_id"`
	Telegram             *string `json:"telegram"`
	Discord              *string `json:"discord" `
	Twitter              *string `json:"twitter"`
	Introduction         *string `json:"introduction"`
	PaymentExpirationSec int     `json:"payment_expiration_sec" binding:"required,gte=600,lte=86400"`
	PaymentMethod        string  `json:"payment_method" binding:"required"`
	PaymentCurrency      string  `json:"payment_currency" binding:"required"`
	BankName             *string `json:"bank_name" binding:"required"`
	BranchName           *string `json:"branch_name" binding:"required"`
	BankAccount          *string `json:"bank_account" binding:"required"`
	AccountHolderName    *string `json:"bank_account_holder_name" binding:"required"`
}

// UpsertMarketInfo upsert market info.
func UpsertMarketInfo(ctx *gin.Context) {
	orgID, err := strconv.Atoi(ctx.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid org id")
		return
	}

	req := upsertMarketInfoReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	marketURL, err := url.JoinPath(config.GetString("KGSTORE_BASE_URL"), req.MarketCode)
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid market code")
		return
	}

	newMarket, kgErr := orgservice.UpsertMarket(ctx.Request.Context(), domain.UpsertStudioMarketRequest{
		OrganizationID:       orgID,
		OauthClientID:        req.OauthClientID,
		Title:                util.Ptr(req.Title),
		Logo:                 util.Ptr(req.Logo),
		MarketCode:           util.Ptr(req.MarketCode),
		MarketURL:            util.Ptr(marketURL),
		Introduction:         req.Introduction,
		Email:                req.Email,
		Phone:                req.Phone,
		LineID:               req.LineID,
		Telegram:             req.Telegram,
		Discord:              req.Discord,
		Twitter:              req.Twitter,
		PaymentMethod:        util.Ptr(domain.PaymentMethod(req.PaymentMethod)),
		PaymentCurrency:      util.Ptr(req.PaymentCurrency),
		BankName:             req.BankName,
		BranchName:           req.BranchName,
		BankAccount:          req.BankAccount,
		AccountHolderName:    req.AccountHolderName,
		PaymentExpirationSec: util.Ptr(req.PaymentExpirationSec),
	})
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if !newMarket {
		response.OK(ctx, nil)
		return
	}

	if kgErr := market.CreateProducts(ctx.Request.Context(), orgID); kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, nil)
}
