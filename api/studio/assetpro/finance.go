package assetpro

import (
	"context"
	"fmt"
	"net/http"
	"strconv"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/samber/lo"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	assetproserv "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
)

type liquidity struct {
	LiquidityType   string  `json:"liquidity_type"`
	Symbol          string  `json:"symbol"`
	ChainID         string  `json:"chain_id"`
	TokenURL        string  `json:"token_url"`
	ContractAddress *string `json:"contract_address"`
	Price           string  `json:"price"`
	Unit            string  `json:"unit"`
	ProfitMargin    float64 `json:"profit_margin"`
	AlertThreshold  *string `json:"alert_threshold"`
}

func (l *liquidity) fromDomain(liquidity *domain.AssetProLiquidity, profitRate decimal.Decimal) {
	l.LiquidityType = liquidity.LiquidityType.String()
	l.Symbol = liquidity.Symbol
	l.ChainID = liquidity.ChainID
	l.TokenURL = liquidity.TokenURL
	l.ContractAddress = liquidity.ContractAddress
	l.Price = decimal.NewFromFloat(liquidity.Price).Round(8).String()
	l.Unit = liquidity.Unit
	l.ProfitMargin = profitRate.Round(8).InexactFloat64()
	if liquidity.AlertThreshold != nil {
		fmt.Println("alert threshold", liquidity.AlertThreshold.String())
		l.AlertThreshold = util.Ptr(liquidity.AlertThreshold.Round(8).String())
	}
}

// GetLiquidities .
func GetLiquidities(c *gin.Context) {
	orgID := c.GetInt("org_id")

	liquiditiesDomain, kgErr := assetproserv.GetLiquidities(c.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	profitRates, kgErr := assetproserv.GetProfitRates(c.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	mProfitRate := lo.SliceToMap(profitRates, func(pr *domain.AssetProProfitRate) (domain.ProfitRateServiceType, decimal.Decimal) {
		return pr.Service, pr.ProfitRate
	})

	liquidities := make([]*liquidity, 0, len(liquiditiesDomain))
	for _, liquidityDomain := range liquiditiesDomain {
		var liquidity liquidity
		liquidity.fromDomain(liquidityDomain, mProfitRate[liquidityDomain.LiquidityType.ToProfitRateServiceType()])
		liquidities = append(liquidities, &liquidity)
	}

	response.OK(c, liquidities)
}

type updateLiquiditiesRequest struct {
	ProfitMargin struct {
		BuyCrypto float64 `json:"buy_crypto"`
		GasSwap   float64 `json:"gas_swap"`
	} `json:"profit_margin"`
}

type profitRate struct {
	Service    string  `json:"service"`
	ProfitRate float64 `json:"profit_rate"`
}

// GetProfitRates query profit rates
func GetProfitRates(c *gin.Context) {
	orgID := c.GetInt("org_id")

	profitRates, kgErr := assetproserv.GetProfitRates(c.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	mProfitRate := lo.SliceToMap(profitRates, func(pr *domain.AssetProProfitRate) (domain.ProfitRateServiceType, float64) {
		return pr.Service, pr.ProfitRate.InexactFloat64()
	})

	// ordered services
	services := []domain.ProfitRateServiceType{
		domain.ProfitRateServiceTypeBuy,
		domain.ProfitRateServiceTypeSwapGas,
		domain.ProfitRateServiceTypeSwapDefi,
		domain.ProfitRateServiceTypeBridge,
		domain.ProfitRateServiceTypeSendWithFee,
		domain.ProfitRateServiceTypeSendGasless,
		domain.ProfitRateServiceTypeSendBatch,
	}

	result := make([]profitRate, 0, len(services))
	for _, service := range services {
		result = append(result, profitRate{
			Service:    service.String(),
			ProfitRate: mProfitRate[service],
		})
	}

	response.OK(c, result)
}

type upsertProfitRateRequest struct {
	orgID            int
	service          string
	profitRate       *float64
	profitShareRatio *float64
}

func updateProfitRate(ctx context.Context, req *upsertProfitRateRequest) *code.KGError {
	service, err := domain.ParseProfitRateServiceType(req.service)
	if err != nil {
		return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("invalid service"), nil)
	}

	max := decimal.NewFromFloat(1.0)
	if service == domain.ProfitRateServiceTypeSwapDefi {
		max = decimal.NewFromFloat(0.03)
	}

	var profitRate *decimal.Decimal
	if req.profitRate != nil {
		pr := decimal.NewFromFloat(*req.profitRate)
		if pr.LessThan(decimal.Zero) || pr.GreaterThan(max) {
			return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("profit_rate should be in range of [0, %f]", max.InexactFloat64()), nil)
		}

		if pr.RoundFloor(4).String() != pr.String() {
			return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("profit_rate should have at most 5 decimal places"), nil)
		}

		profitRate = &pr
	}

	var profitShareRatio *decimal.Decimal
	if req.profitShareRatio != nil {
		shareRatio := decimal.NewFromFloat(*req.profitShareRatio)
		if shareRatio.LessThan(decimal.Zero) || shareRatio.GreaterThan(decimal.NewFromFloat(1.0)) {
			return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("profit_share_ratio should be in range of [0, 1]"), nil)
		}

		profitShareRatio = &shareRatio
	}

	return assetproserv.UpsertProfitRate(ctx, domain.UpsertProfitRateParams{
		OrgID:            req.orgID,
		Service:          service,
		ProfitRate:       profitRate,
		ProfitShareRatio: profitShareRatio,
	})
}

// UpsertProfitRateRequest is the request of UpsertProfitRate.
type UpsertProfitRateRequest struct {
	Service    string   `json:"service" binding:"required"`
	ProfitRate *float64 `json:"profit_rate"`
}

// UpsertProfitRate upserts profit rates for an organization.
func UpsertProfitRate(c *gin.Context) {
	orgID := c.GetInt("org_id")

	params := &UpsertProfitRateRequest{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if params.ProfitRate == nil {
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("profit_rate should be provided"), nil))
		return
	}

	if kgErr := updateProfitRate(c.Request.Context(), &upsertProfitRateRequest{
		orgID:      orgID,
		service:    params.Service,
		profitRate: params.ProfitRate,
	}); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

type internalUpdateProfitRateRequest struct {
	Service          string   `json:"service" binding:"required"`
	ProfitRate       *float64 `json:"profit_rate"`
	ProfitShareRatio *float64 `json:"profit_share_ratio"`
}

// InternalUpsertProfitRate upserts profit rates for an organization.
func InternalUpsertProfitRate(c *gin.Context) {
	orgID, err := strconv.Atoi(c.Param("orgID"))
	if err != nil {
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("orgID is invalid"), nil))
		return
	}

	req := &internalUpdateProfitRateRequest{}
	kgErr := util.ToGinContextExt(c).BindJson(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if kgErr := updateProfitRate(c.Request.Context(), &upsertProfitRateRequest{
		orgID:            orgID,
		service:          req.Service,
		profitRate:       req.ProfitRate,
		profitShareRatio: req.ProfitShareRatio,
	}); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}
