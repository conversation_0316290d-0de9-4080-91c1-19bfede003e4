package assetpro

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
)

type testApprovalSuite struct {
	suite.Suite

	orgIDToTransferToken int
}

func (s *testApprovalSuite) SetupSuite() {
	s.orgIDToTransferToken = 2

	rdb.Reset()
	s.NoError(dbtest.CreateStudioOrganizations(rdb.Get()))
	s.NoError(dbtest.CreateStudioOrgModules(rdb.Get()))
	s.NoError(dbtest.CreateStudioRoles(rdb.Get()))
	s.NoError(dbtest.CreateStudioOrganizationWallets(rdb.Get()))
	organization.Init(organization.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	})
	s.NoError(rbac.Init(context.Background()))
	assetpro.InitTransfer(rdb.GormRepo())
	tx.Init(repo.Unified())
}

func (s *testApprovalSuite) apply(traderUID string, r *http.Request) *httptest.ResponseRecorder {
	w := httptest.NewRecorder()

	router := gin.Default()
	router.POST("/v1/studio/organization/:orgID/asset_pro/transfer",
		auth.MockOrgID(s.orgIDToTransferToken),
		auth.MockAuthorize(traderUID),
		TransferV2,
	)
	router.ServeHTTP(w, r)

	return w
}
func (s *testApprovalSuite) approve(approverUID string, r *http.Request) *httptest.ResponseRecorder {
	w := httptest.NewRecorder()

	router := gin.Default()
	router.POST("/v1/studio/organization/:orgID/asset_pro/transfer/histories/:serial_id/approve",
		auth.MockOrgID(s.orgIDToTransferToken),
		auth.MockAuthorize(approverUID),
		Approve,
	)
	router.ServeHTTP(w, r)

	return w
}

func (s *testApprovalSuite) release(financeManagerUID string, r *http.Request) *httptest.ResponseRecorder {
	w := httptest.NewRecorder()

	router := gin.Default()
	router.POST("/v1/studio/organization/:orgID/asset_pro/transfer/histories/:serial_id/release",
		auth.MockOrgID(s.orgIDToTransferToken),
		auth.MockAuthorize(financeManagerUID),
		Release,
	)
	router.ServeHTTP(w, r)

	return w
}

func (s *testApprovalSuite) TestIntegrationApproval() {
	signingServerShutdownFunction := setupSigningServer(s.T())
	defer signingServerShutdownFunction()
	traderUID := util.RandString(20)
	approverUID := util.RandString(20)
	financeManagerUID := util.RandString(20)

	{ // trader
		email := util.RandEmail()

		s.NoError(dbtest.CreateStudioUsers(rdb.Get(), traderUID, &email))
		s.NoError(rdb.Get().Model(&model.StudioUser{}).
			Where("organization_id = ? and uid = ?", s.orgIDToTransferToken, traderUID).
			Update("daily_transfer_limit", decimal.NewFromInt(100)).Error)
		// set daily transfer limit to zero, approval required
		s.NoError(rdb.Get().Model(&model.StudioUser{}).
			Where("organization_id = ? and uid = ?", s.orgIDToTransferToken, traderUID).
			Update("transfer_approval_threshold", decimal.Zero).Error)

		s.NoError(rdb.Get().Create(&model.StudioRoleBinding{
			OrganizationID: s.orgIDToTransferToken,
			UID:            traderUID,
			RoleID:         6,
		}).Error)
	}
	{ // approver
		email := util.RandEmail()

		s.NoError(dbtest.CreateStudioUsers(rdb.Get(), approverUID, &email))
		s.NoError(rdb.Get().Create(&model.StudioRoleBinding{
			OrganizationID: s.orgIDToTransferToken,
			UID:            approverUID,
			RoleID:         5,
		}).Error)
	}
	{ // finance manager
		email := util.RandEmail()

		s.NoError(dbtest.CreateStudioUsers(rdb.Get(), financeManagerUID, &email))
		s.NoError(rdb.Get().Create(&model.StudioRoleBinding{
			OrganizationID: s.orgIDToTransferToken,
			UID:            financeManagerUID,
			RoleID:         9,
		}).Error)
	}
	s.NoError(dbtest.CreateAssetPrices(rdb.Get()))

	now := util.NowInCST()

	s.Nil(organization.CacheRoleBindings(context.Background()))
	cache.Set(context.Background(), cache.ComposeDailyUsedLimitCacheKey(s.orgIDToTransferToken, traderUID, now), 0, time.Minute*10)
	s.testThreeStepApproval(traderUID, approverUID, financeManagerUID)
	s.testThreeStepApprovalButRejectedByApprover(traderUID, approverUID, financeManagerUID)
	s.testThreeStepApprovalButRejectedByFinanceManager(traderUID, approverUID, financeManagerUID)
}

func (s *testApprovalSuite) testThreeStepApproval(traderUID, approverUID, financeManagerUID string) {
	s.NoError(rdb.Get().Model(&model.StudioOrganization{}).
		Where("id = ?", s.orgIDToTransferToken).
		Update("asset_pro_approval_config", domain.AssetProApprovalConfigTraderApproverFinanceManager).Error)

	var serialID string

	getNow := func() int {
		return int(util.NowInCST().Unix())
	}

	timeCursor := getNow()

	s.assertDailyUsedLimit(traderUID, decimal.Zero)

	{ // trader apply transaction
		bs, err := json.Marshal(map[string]any{
			"chain_id":         "holesky",
			"contract_address": "******************************************", // usdt on localhost
			"amount":           "0.0001",
			"wallet_address":   "******************************************",
			"note":             "55688",
			"attachments":      []string{"test.png", "test2.png"},
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", s.orgIDToTransferToken),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.apply(traderUID, req)

		s.T().Logf("w.Body: %s", w.Body.String())
		s.Equal(http.StatusOK, w.Code)
		type responseType struct {
			Code int `json:"code"`
			Data struct {
				transferResp
			} `json:"data"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.NotEmpty(response.Data.ID)

		serialID = response.Data.ID

		var actualTx model.AssetProTxLog
		s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", serialID).Error, "query tx(%s) failed", serialID)
		s.Empty(actualTx.TxHash)
		s.Equal(domain.AssetProTxLogStatusAwaitingApproval, actualTx.Status)
		s.Equal(traderUID, actualTx.OperatorUID)
		s.True(actualTx.UpdateTime >= timeCursor)
		s.True(actualTx.UpdateTime <= getNow())
		timeCursor = actualTx.UpdateTime
		s.Equal(traderUID, actualTx.OperatorUID)
		s.assertDailyUsedLimit(traderUID, decimal.NewFromFloat(0.0001))
	}
	{ // approver approve transaction without payload
		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/approve", s.orgIDToTransferToken, serialID),
			nil,
		)
		s.NoError(err)

		w := s.approve(approverUID, req)

		s.Equal(http.StatusBadRequest, w.Code)
	}
	{ // approverUID approve transaction
		bs, err := json.Marshal(map[string]any{
			"operation": "approve",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/approve", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.approve(approverUID, req)

		s.Equal(http.StatusOK, w.Code)
		type responseType struct {
			Code int `json:"code"`
			Data struct {
				transferResp
			} `json:"data"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		var actualTx model.AssetProTxLog
		s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", serialID).Error, "query tx(%s) failed", serialID)
		s.Empty(actualTx.TxHash)
		s.Equal(domain.AssetProTxLogStatusAwaitingRelease, actualTx.Status)
		s.Equal(approverUID, *actualTx.ApproverUID)
		s.True(actualTx.UpdateTime >= timeCursor)
		s.True(actualTx.UpdateTime <= getNow())
		timeCursor = actualTx.UpdateTime
	}
	{ // approver tried to approve transaction, but already approved
		bs, err := json.Marshal(map[string]any{
			"operation": "approve",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/approve", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)
		w := s.approve(approverUID, req)

		s.Equal(http.StatusForbidden, w.Code)
	}
	{ // finance manager release transaction without payload
		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/release", s.orgIDToTransferToken, serialID),
			nil,
		)

		s.NoError(err)
		w := s.release(financeManagerUID, req)

		s.Equal(http.StatusBadRequest, w.Code)
	}
	{ // finance manager release transaction
		bs, err := json.Marshal(map[string]any{
			"operation": "release",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/release", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.release(financeManagerUID, req)

		s.Equal(http.StatusOK, w.Code)
		type responseType struct {
			Code int `json:"code"`
			Data struct {
				transferResp
			} `json:"data"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		var actualTx model.AssetProTxLog
		s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", serialID).Error, "query tx(%s) failed", serialID)
		s.NotEmpty(actualTx.TxHash)
		s.Equal(domain.AssetProTxLogStatusSending, actualTx.Status)
		s.Equal(financeManagerUID, *actualTx.FinanceManagerUID)
		s.True(actualTx.UpdateTime >= timeCursor)
		s.True(actualTx.UpdateTime <= getNow())
	}
	{ // trader tried to approve transaction, but already approved
		bs, err := json.Marshal(map[string]any{
			"operation": "approve",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/approve", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)
		w := s.approve(traderUID, req)

		s.Equal(http.StatusBadRequest, w.Code)
	}
	{ // finance manager tried to release transaction, but already released
		bs, err := json.Marshal(map[string]any{
			"operation": "release",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/release", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)
		w := s.release(financeManagerUID, req)

		s.Equal(http.StatusBadRequest, w.Code)
		s.assertDailyUsedLimit(traderUID, decimal.NewFromFloat(0.0001))
	}
}
func (s *testApprovalSuite) testThreeStepApprovalButRejectedByApprover(traderUID, approverUID, financeManagerUID string) {
	s.NoError(rdb.Get().Model(&model.StudioOrganization{}).
		Where("id = ?", s.orgIDToTransferToken).
		Update("asset_pro_approval_config", domain.AssetProApprovalConfigTraderApproverFinanceManager).Error)

	var serialID string

	s.assertDailyUsedLimit(traderUID, decimal.NewFromFloat(0.0001))

	{ // trader apply transaction
		bs, err := json.Marshal(map[string]any{
			"chain_id":         "holesky",
			"contract_address": "******************************************", // usdt on localhost
			"amount":           "0.0001",
			"wallet_address":   "******************************************",
			"note":             "55688",
			"attachments":      []string{"test.png", "test2.png"},
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", s.orgIDToTransferToken),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.apply(traderUID, req)

		s.Equal(http.StatusOK, w.Code)
		type responseType struct {
			Code int `json:"code"`
			Data struct {
				transferResp
			} `json:"data"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.NotEmpty(response.Data.ID)

		serialID = response.Data.ID

		var actualTx model.AssetProTxLog
		s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", serialID).Error, "query tx(%s) failed", serialID)
		s.Empty(actualTx.TxHash)
		s.Equal(domain.AssetProTxLogStatusAwaitingApproval, actualTx.Status)
		s.Equal(traderUID, actualTx.OperatorUID)
		s.assertDailyUsedLimit(traderUID, decimal.NewFromFloat(0.0002))
	}
	{ // approver reject transaction without note
		bs, err := json.Marshal(map[string]any{
			"operation": "reject",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/approve", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.approve(approverUID, req)

		s.Equal(http.StatusBadRequest, w.Code)
	}
	{ // approver reject transaction
		bs, err := json.Marshal(map[string]any{
			"operation": "reject",
			"note":      "55688",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/approve", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.approve(approverUID, req)

		s.Equal(http.StatusOK, w.Code)
		type responseType struct {
			Code int `json:"code"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		var actualTx model.AssetProTxLog
		s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", serialID).Error, "query tx(%s) failed", serialID)
		s.Empty(actualTx.TxHash)
		s.Equal(domain.AssetProTxLogStatusRejected, actualTx.Status)
		s.Equal("55688", *actualTx.RejectionNote)
		s.Equal(approverUID, *actualTx.ApproverUID)
		s.assertDailyUsedLimit(traderUID, decimal.NewFromFloat(0.0001))
	}
	{ // trader tried to reject transaction but got bad request
		bs, err := json.Marshal(map[string]any{
			"operation": "reject",
			"note":      "55688",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/approve", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)
		w := s.approve(approverUID, req)

		s.Equal(http.StatusBadRequest, w.Code)
	}
	{ // finance manager try to release transaction, but got bad request
		bs, err := json.Marshal(map[string]any{
			"operation": "release",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/release", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)
		w := s.release(financeManagerUID, req)

		s.Equal(http.StatusBadRequest, w.Code)
	}
}

func (s *testApprovalSuite) testThreeStepApprovalButRejectedByFinanceManager(traderUID, approverUID, financeManagerUID string) {
	s.NoError(rdb.Get().Model(&model.StudioOrganization{}).
		Where("id = ?", s.orgIDToTransferToken).
		Update("asset_pro_approval_config", domain.AssetProApprovalConfigTraderApproverFinanceManager).Error)

	var serialID string

	s.assertDailyUsedLimit(traderUID, decimal.NewFromFloat(0.0001))

	{ // trader apply transaction
		bs, err := json.Marshal(map[string]any{
			"chain_id":         "holesky",
			"contract_address": "******************************************", // usdt on localhost
			"amount":           "0.0001",
			"wallet_address":   "******************************************",
			"note":             "55688",
			"attachments":      []string{"test.png", "test2.png"},
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", s.orgIDToTransferToken),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.apply(traderUID, req)

		s.Equal(http.StatusOK, w.Code)
		type responseType struct {
			Code int `json:"code"`
			Data struct {
				transferResp
			} `json:"data"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.NotEmpty(response.Data.ID)

		serialID = response.Data.ID

		var actualTx model.AssetProTxLog
		s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", serialID).Error, "query tx(%s) failed", serialID)
		s.Empty(actualTx.TxHash)
		s.Equal(domain.AssetProTxLogStatusAwaitingApproval, actualTx.Status)
		s.Equal(traderUID, actualTx.OperatorUID)
		s.assertDailyUsedLimit(traderUID, decimal.NewFromFloat(0.0002))
	}
	{ // approver approve transaction
		bs, err := json.Marshal(map[string]any{
			"operation": "approve",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/approve", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.approve(approverUID, req)

		s.Equal(http.StatusOK, w.Code)
		type responseType struct {
			Code int `json:"code"`
			Data struct {
				transferResp
			} `json:"data"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		var actualTx model.AssetProTxLog
		s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", serialID).Error, "query tx(%s) failed", serialID)
		s.Empty(actualTx.TxHash)
		s.Equal(domain.AssetProTxLogStatusAwaitingRelease, actualTx.Status)
		s.Equal(approverUID, *actualTx.ApproverUID)
		s.assertDailyUsedLimit(traderUID, decimal.NewFromFloat(0.0002))
	}
	{ // approver tried to approve transaction but got forbidden
		bs, err := json.Marshal(map[string]any{
			"operation": "reject",
			"note":      "55688",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/approve", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.approve(approverUID, req)

		s.Equal(http.StatusForbidden, w.Code)
	}
	{ // finance manager release transaction without note
		bs, err := json.Marshal(map[string]any{
			"operation": "reject",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/release", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.release(financeManagerUID, req)

		s.Equal(http.StatusBadRequest, w.Code)
	}
	{ // finance manager reject transaction
		bs, err := json.Marshal(map[string]any{
			"operation": "reject",
			"note":      "55688",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/release", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.release(financeManagerUID, req)

		s.Equal(http.StatusOK, w.Code)
		type responseType struct {
			Code int `json:"code"`
			Data struct {
				transferResp
			} `json:"data"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)

		var actualTx model.AssetProTxLog
		s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", serialID).Error, "query tx(%s) failed", serialID)
		s.Empty(actualTx.TxHash)
		s.Equal(domain.AssetProTxLogStatusRejected, actualTx.Status)
		s.Equal("55688", *actualTx.RejectionNote)
		s.Equal(financeManagerUID, *actualTx.FinanceManagerUID)
		s.assertDailyUsedLimit(traderUID, decimal.NewFromFloat(0.0001))
	}
	{ // finance manager tried to release transaction but got bad request
		bs, err := json.Marshal(map[string]any{
			"operation": "release",
			"note":      "55688",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories/%s/release", s.orgIDToTransferToken, serialID),
			bytes.NewReader(bs),
		)

		s.NoError(err)

		w := s.release(financeManagerUID, req)

		s.Equal(http.StatusBadRequest, w.Code)
	}
}

func (s *testApprovalSuite) assertDailyUsedLimit(traderUID string, amount decimal.Decimal) {
	ctx := context.Background()

	dailyUsedLimit, err := cache.GetDailyUsedLimit(ctx, s.orgIDToTransferToken, traderUID, util.NowInCST())
	s.NoError(err)

	if dailyUsedLimit == nil {
		s.Equal(decimal.Zero.String(), amount.String())
		return
	}

	limit := dailyUsedLimit.String()

	s.Equal(amount.String(), limit)
}

func TestApprovalSuite(t *testing.T) {
	suite.Run(t, new(testApprovalSuite))
}
