package assetpro

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"

	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tokens"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	freetransfer "github.com/kryptogo/kg-wallet-backend/service/free-transfer"
)

type tokenInfo struct {
	ChainID         string `json:"chain_id"`
	ChainName       string `json:"chain_name"`
	ContractAddress string `json:"contract_address"`
	Name            string `json:"name"`
	Symbol          string `json:"symbol"`
	LogoUrl         string `json:"logo_url"`
	Decimals        int    `json:"decimals"`
	CoingeckoID     string `json:"coingecko_id"`
}

// Tokens get assetpro tokens
func Tokens(ctx *gin.Context) {
	supportedTokens := tokens.GetSupportedTokens()

	tokenInfos := make([]tokenInfo, 0, len(supportedTokens))
	for _, token := range supportedTokens {
		tokenInfos = append(tokenInfos, tokenInfo{
			ChainID:         token.ChainID,
			ChainName:       token.ChainName,
			ContractAddress: token.ContractAddress,
			Name:            token.Name,
			Symbol:          token.Symbol,
			LogoUrl:         token.LogoUrl,
			Decimals:        token.Decimals,
			CoingeckoID:     token.CoingeckoID,
		})
	}

	response.OK(ctx, tokenInfos)
}

// transferReq defines request to transfer
type transferReq struct {
	ChainID         string   `json:"chain_id" binding:"required"`
	WalletAddress   string   `json:"wallet_address" binding:"required"`
	ContractAddress string   `json:"contract_address"`
	Amount          string   `json:"amount" binding:"required"`
	Note            *string  `json:"note"`
	Attachments     []string `json:"attachments"`
	Email           *string  `json:"email"`
	Phone           *string  `json:"phone"`
	DisplayName     *string  `json:"display_name"`
	AutoGasExchange bool     `json:"auto_gas_exchange"`
}

type transferResp struct {
	ID           string `json:"id"`
	TransferTime int64  `json:"transfer_time"`
	TxHash       string `json:"tx_hash,omitempty"`
}

// TransferV2 transfer assetpro token v2 (with wallet address)
func TransferV2(c *gin.Context) {
	transferTime := time.Now().Unix()
	orgID := c.GetInt("org_id")
	uid := c.GetString("uid")

	req := transferReq{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	amount, err := decimal.NewFromString(req.Amount)
	if err != nil {
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, err, nil))
		return
	}

	chain := domain.IDToChain(req.ChainID)

	// Get organization's wallet address (the actual sender) to check gas status
	var walletType string
	if chain.IsTVM() {
		walletType = "tron"
	} else if chain.IsEVM() {
		walletType = "evm"
	} else {
		kglog.WarningWithDataCtx(c.Request.Context(), "Chain ID not supported for gasless transfer check", map[string]interface{}{
			"chain_id": req.ChainID,
		})
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("chainID not supported"), nil))
		return
	}

	orgWallet, err := organization.GetOrgWallet(c.Request.Context(), orgID, walletType)
	if err != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "Failed to get organization wallet for gas check", map[string]interface{}{
			"error":           err.Error(),
			"organization_id": orgID,
			"wallet_type":     walletType,
			"chain_id":        req.ChainID,
		})
		response.KGError(c, code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil))
		return
	}

	// Check if the organization's sender wallet has enough gas for transactions
	hasGas, kgErr := freetransfer.CheckWalletGasStatus(c.Request.Context(), chain, orgWallet.WalletAddress)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// If the organization's wallet doesn't have enough gas, handle gasless transfer
	if !hasGas {
		kglog.InfoWithDataCtx(c.Request.Context(), "Organization wallet has insufficient gas, initiating gasless transfer", map[string]interface{}{
			"organization_id":    orgID,
			"operator_uid":       uid,
			"org_wallet_address": orgWallet.WalletAddress,
			"recipient_address":  req.WalletAddress,
			"chain_id":           req.ChainID,
			"auto_gas_exchange":  req.AutoGasExchange,
		})

		_, kgErr := freetransfer.HandleFreeTransfer(c.Request.Context(), orgID, chain, req.AutoGasExchange)
		if kgErr != nil {
			response.KGError(c, kgErr)
			return
		}
	}

	serialID, kgError := assetpro.TransferFungibleTokenWithApproval(c.Request.Context(),
		assetpro.TransferFungibleTokenWithApprovalRequest{
			OrganizationID:  orgID,
			OperatorUID:     uid,
			ChainID:         req.ChainID,
			To:              req.WalletAddress,
			ContractAddress: req.ContractAddress,
			Amount:          amount,
			Note:            req.Note,
			Attachments:     req.Attachments,
			Recipient: assetpro.Recipient{
				Email:       req.Email,
				Phone:       req.Phone,
				DisplayName: req.DisplayName,
			},
		})
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	// Fetch the transaction log to get the txHash
	txLog, kgError := assetpro.HistoryBySerialID(c.Request.Context(), serialID)
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	resp := transferResp{
		ID:           serialID,
		TransferTime: transferTime,
		TxHash:       txLog.TxHash,
	}

	response.OK(c, resp)
}

// Resend resend transfer
func Resend(ctx *gin.Context) {
	serialID := ctx.Param("serial_id")
	orgID := ctx.GetInt("org_id")
	uid := ctx.GetString("uid")

	kgError := assetpro.Resend(ctx, assetpro.ResendRequest{
		OrgID:       orgID,
		OperatorUID: uid,
		SerialID:    serialID,
	})
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	response.OK(ctx, nil)
}

// FreeSendCountResp represents the response for free send count
type FreeSendCountResp struct {
	UsedCount      int `json:"used_count"`
	MaxCount       int `json:"max_count"`
	RemainingCount int `json:"remaining_count"`
}

// GetFreeSendCount returns the organization's free send count
func GetFreeSendCount(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")

	freeSendCount, maxCount, kgErr := freetransfer.GetOrgFreeSendCountWithMaximum(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	remainingCount := max(maxCount-freeSendCount.UsedCount, 0)

	resp := FreeSendCountResp{
		UsedCount:      freeSendCount.UsedCount,
		MaxCount:       maxCount,
		RemainingCount: remainingCount,
	}

	response.OK(ctx, resp)
}
