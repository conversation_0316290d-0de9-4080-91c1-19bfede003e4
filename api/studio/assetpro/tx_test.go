package assetpro

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"

	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tokens"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/repo"
)

func TestTokensWithActualValues(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	rdb.Reset()
	tx.Init(repo.Unified())

	s := assert.New(t)

	// Setup local chain

	// assert.NoError(t, rdb.Get().Create(&metadata).Error)
	model.ChainIDtoCoingeckoChainID[model.ChainIDSepolia] = "sepolia" //for testing
	model.ChainIDtoCoingeckoChainID[model.ChainIDShasta] = "shasta"
	model.ChainIDtoCoingeckoChainID[model.ChainIDHolesky] = "holesky"

	// Setup Gin router for testing
	r := gin.Default()
	r.GET("/tokens", Tokens)

	// Creating the request
	req, _ := http.NewRequest("GET", "/tokens", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	// Asserting the HTTP status code
	s.Equal(http.StatusOK, w.Code)

	// Parsing the response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	s.NoError(err, "Error while unmarshalling response: %v", err)

	// Extracting the data field
	responseData, ok := response["data"].([]interface{})
	s.True(ok, "Expected data to be a slice but got %T", response["data"])

	// Asserting the length of the data
	s.Equal(len(tokens.GetSupportedTokens()), len(responseData)+1, "Number of tokens in response should match (exclude localhost token)")

	// Convert responseData to a slice of tokenInfo for easier comparison
	type tokenInfo struct {
		ChainID         string `json:"chain_id"`
		ChainName       string `json:"chain_name"`
		ContractAddress string `json:"contract_address"`
		Name            string `json:"name"`
		Symbol          string `json:"symbol"`
		LogoUrl         string `json:"logo_url"`
		Decimals        int    `json:"decimals"`
		CoingeckoID     string `json:"coingecko_id"`
	}

	var responseTokens []tokenInfo
	for _, item := range responseData {
		var token tokenInfo
		b, err := json.Marshal(item)
		s.NoError(err, "Error while marshalling token")
		err = json.Unmarshal(b, &token)
		s.NoError(err, "Error while decoding token")
		responseTokens = append(responseTokens, token)
	}

	// Detailed assertions for each token
	for i, expectedToken := range tokens.GetSupportedTokens() {
		if expectedToken.ChainID == "holesky" {
			continue
		}
		actualToken := responseTokens[i]

		// Compare each field
		s.Equal(expectedToken.ChainID, actualToken.ChainID, "Chain ID should match")
		s.Equal(expectedToken.ChainName, actualToken.ChainName, "Chain Name should match")
		s.Equal(expectedToken.ContractAddress, actualToken.ContractAddress, "Contract Address should match")
		s.Equal(expectedToken.Name, actualToken.Name, "Name should match")
		s.Equal(expectedToken.Symbol, actualToken.Symbol, "Symbol should match")
		s.Equal(expectedToken.LogoUrl, actualToken.LogoUrl, "Logo URL should match")
		s.Equal(expectedToken.Decimals, actualToken.Decimals, "Decimals should match")
		// use symbol as coingeckoID
		if actualToken.Symbol == "USDT" {
			s.Equal("tether", actualToken.CoingeckoID, "coinGeckoID should match")
		} else if actualToken.Symbol == "USDC" {
			s.Equal("usd-coin", actualToken.CoingeckoID, "coinGeckoID should match")
		}
	}
}

type testTransferTokenSuite struct {
	suite.Suite

	transferGinEngine    *gin.Engine
	orgIDToTransferToken int
}

func (s *testTransferTokenSuite) SetupSuite() {
	s.orgIDToTransferToken = 2

	rdb.Reset()
	s.NoError(dbtest.CreateStudioOrganizations(rdb.Get()))
	s.NoError(dbtest.CreateStudioOrgModules(rdb.Get()))
	s.NoError(dbtest.CreateStudioRoles(rdb.Get()))
	s.NoError(dbtest.CreateStudioOrganizationWallets(rdb.Get()))
	organization.Init(organization.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	})
	s.NoError(rbac.Init(context.Background()))
	assetpro.InitTransfer(rdb.GormRepo())
	s.transferGinEngine = gin.Default()
}

func (s *testTransferTokenSuite) TestTransfer() {
	signingServerShutdownFunction := setupSigningServer(s.T())
	defer signingServerShutdownFunction()

	uidToTransferToken := util.RandString(20)

	{ //init
		tx.Init(rdb.GormRepo())
		email := util.RandEmail()

		s.transferGinEngine.POST("/v1/studio/organization/:orgID/asset_pro/transfer",
			auth.MockOrgID(s.orgIDToTransferToken),
			auth.MockAuthorize(uidToTransferToken),
			TransferV2,
		)

		s.NoError(dbtest.CreateAssetPrices(rdb.Get()))
		s.NoError(dbtest.CreateStudioUsers(rdb.Get(), uidToTransferToken, &email))
		s.NoError(rdb.Get().Model(&model.StudioUser{}).
			Where("organization_id = ? and uid = ?", s.orgIDToTransferToken, uidToTransferToken).
			Update("daily_transfer_limit", decimal.NewFromInt(100)).Error)
	}

	// no need to approve by
	{
		s.NoError(rdb.Get().Model(&model.StudioOrganization{}).
			Where("id = ?", s.orgIDToTransferToken).
			Update("asset_pro_approval_config", domain.AssetProApprovalConfigTrader).Error)
		s.testTransferNoNeedApprovalByOrgAndAmountIsNotGreaterThanApprovalThreshold(uidToTransferToken)
		s.testTransferNoNeedApprovalByOrgAndAmountIsGreaterThanApprovalThreshold(uidToTransferToken)
	}
	{
		s.NoError(rdb.Get().Model(&model.StudioOrganization{}).
			Where("id = ?", s.orgIDToTransferToken).
			Update("asset_pro_approval_config", domain.AssetProApprovalConfigTraderApproverFinanceManager).Error)
		s.testTransferNeedApprovalByOrgAndAmountIsNotGreaterThanApprovalThreshold(uidToTransferToken)
		s.testTransferNeedApprovalByOrgAndAmountIsGreaterThanApprovalThreshold(uidToTransferToken)
	}
}

func (s *testTransferTokenSuite) testTransferNoNeedApprovalByOrgAndAmountIsNotGreaterThanApprovalThreshold(uidToTransferToken string) {
	s.NoError(rdb.Get().Model(&model.StudioUser{}).
		Where("organization_id = ? and uid = ?", s.orgIDToTransferToken, uidToTransferToken).
		Update("transfer_approval_threshold", decimal.NewFromFloat(0.0001)).Error)

	bodyStr, err := json.Marshal(map[string]any{
		"chain_id":         "holesky",
		"contract_address": "******************************************", // usdt on localhost
		"amount":           "0.0001",
		"wallet_address":   "******************************************",
	})
	s.NoError(err)

	req, err := http.NewRequest(http.MethodPost,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", s.orgIDToTransferToken),
		strings.NewReader(string(bodyStr)),
	)

	s.NoError(err)
	w := httptest.NewRecorder()
	s.transferGinEngine.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	type responseType struct {
		Code int `json:"code"`
		Data struct {
			transferResp
		} `json:"data"`
	}

	var response responseType
	responseStr, _ := io.ReadAll(w.Body)
	s.NoError(json.Unmarshal(responseStr, &response))
	fmt.Printf("response: %+v\n", string(responseStr))
	fmt.Println("response", response)
	s.Equal(0, response.Code)
	s.NotEmpty(response.Data.ID)

	var actualTx model.AssetProTxLog
	s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", response.Data.ID).Error, "query tx(%s) failed", response.Data.ID)
	s.NotEmpty(actualTx.TxHash)
}

func (s *testTransferTokenSuite) testTransferNoNeedApprovalByOrgAndAmountIsGreaterThanApprovalThreshold(uidToTransferToken string) {
	s.NoError(rdb.Get().Model(&model.StudioUser{}).
		Where("organization_id = ? and uid = ?", s.orgIDToTransferToken, uidToTransferToken).
		Update("transfer_approval_threshold", decimal.Zero).Error)
	{ // must bring note and attachments
		bodyStr, err := json.Marshal(map[string]any{
			"chain_id":         "holesky",
			"contract_address": "******************************************", // usdt on localhost
			"amount":           "0.0001",
			"wallet_address":   "******************************************",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", s.orgIDToTransferToken),
			strings.NewReader(string(bodyStr)),
		)

		s.NoError(err)
		w := httptest.NewRecorder()
		s.transferGinEngine.ServeHTTP(w, req)

		s.Equal(http.StatusBadRequest, w.Code)

		type responseType struct {
			Code int `json:"code"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(6011, response.Code)
	}
	{
		bodyStr, err := json.Marshal(map[string]any{
			"chain_id":         "holesky",
			"contract_address": "******************************************", // usdt on localhost
			"amount":           "0.0001",
			"wallet_address":   "******************************************",
			"note":             "55688",
			"attachments":      []string{"test.png", "test2.png"},
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", s.orgIDToTransferToken),
			strings.NewReader(string(bodyStr)),
		)

		s.NoError(err)
		w := httptest.NewRecorder()
		s.transferGinEngine.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)
		type responseType struct {
			Code int `json:"code"`
			Data struct {
				transferResp
			} `json:"data"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.NotEmpty(response.Data.ID)

		var actualTx model.AssetProTxLog
		s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", response.Data.ID).Error, "query tx(%s) failed", response.Data.ID)
		s.NotEmpty(actualTx.TxHash)
	}
}

func (s *testTransferTokenSuite) testTransferNeedApprovalByOrgAndAmountIsNotGreaterThanApprovalThreshold(uidToTransferToken string) {
	s.NoError(rdb.Get().Model(&model.StudioUser{}).
		Where("organization_id = ? and uid = ?", s.orgIDToTransferToken, uidToTransferToken).
		Update("transfer_approval_threshold", decimal.NewFromFloat(0.0001)).Error)

	bodyStr, err := json.Marshal(map[string]any{
		"chain_id":         "holesky",
		"contract_address": "******************************************", // usdt on localhost
		"amount":           "0.0001",
		"wallet_address":   "******************************************",
	})
	s.NoError(err)

	req, err := http.NewRequest(http.MethodPost,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", s.orgIDToTransferToken),
		strings.NewReader(string(bodyStr)),
	)

	s.NoError(err)
	w := httptest.NewRecorder()
	s.transferGinEngine.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	type responseType struct {
		Code int `json:"code"`
		Data struct {
			transferResp
		} `json:"data"`
	}

	var response responseType
	responseStr, _ := io.ReadAll(w.Body)
	s.NoError(json.Unmarshal(responseStr, &response))
	fmt.Printf("response: %+v\n", string(responseStr))
	fmt.Println("response", response)
	s.Equal(0, response.Code)
	s.NotEmpty(response.Data.ID)

	var actualTx model.AssetProTxLog
	s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", response.Data.ID).Error, "query tx(%s) failed", response.Data.ID)
	s.NotEmpty(actualTx.TxHash)
}

func (s *testTransferTokenSuite) testTransferNeedApprovalByOrgAndAmountIsGreaterThanApprovalThreshold(uidToTransferToken string) {
	s.NoError(rdb.Get().Model(&model.StudioUser{}).
		Where("organization_id = ? and uid = ?", s.orgIDToTransferToken, uidToTransferToken).
		Update("transfer_approval_threshold", decimal.Zero).Error)
	{ // must bring note and attachments
		bodyStr, err := json.Marshal(map[string]any{
			"chain_id":         "holesky",
			"contract_address": "******************************************", // usdt on localhost
			"amount":           "0.0001",
			"wallet_address":   "******************************************",
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", s.orgIDToTransferToken),
			strings.NewReader(string(bodyStr)),
		)

		s.NoError(err)
		w := httptest.NewRecorder()
		s.transferGinEngine.ServeHTTP(w, req)

		s.Equal(http.StatusBadRequest, w.Code)

		type responseType struct {
			Code int `json:"code"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(6011, response.Code)
	}
	{
		bodyStr, err := json.Marshal(map[string]any{
			"chain_id":         "holesky",
			"contract_address": "******************************************", // usdt on localhost
			"amount":           "0.0001",
			"wallet_address":   "******************************************",
			"note":             "55688",
			"attachments":      []string{"test.png", "test2.png"},
		})
		s.NoError(err)

		req, err := http.NewRequest(http.MethodPost,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", s.orgIDToTransferToken),
			strings.NewReader(string(bodyStr)),
		)

		s.NoError(err)
		w := httptest.NewRecorder()
		s.transferGinEngine.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)
		type responseType struct {
			Code int `json:"code"`
			Data struct {
				transferResp
			} `json:"data"`
		}

		var response responseType
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.NotEmpty(response.Data.ID)

		var actualTx model.AssetProTxLog
		s.NoError(rdb.Get().First(&actualTx, "serial_id = ?", response.Data.ID).Error, "query tx(%s) failed", response.Data.ID)
		s.Empty(actualTx.TxHash)
	}
}

func setupSigningServer(t *testing.T) func() {
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	// signing server
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", signing.AuthorizeSigningServer, signing.SignEvmTransaction)
	// create HTTP server for graceful shutdown
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)

	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
	time.Sleep(1 * time.Second) // wait for server to start

	return func() {
		assert.Nil(t, srv.Shutdown(context.Background()))
	}
}

func TestTransferTokenSuite(t *testing.T) {
	suite.Run(t, new(testTransferTokenSuite))
}
