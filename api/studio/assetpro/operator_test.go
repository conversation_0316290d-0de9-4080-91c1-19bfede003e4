package assetpro

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
)

type testOperatorSuite struct {
	suite.Suite

	ginEngine            *gin.Engine
	orgIDToListOperators int
}

func (s *testOperatorSuite) SetupSuite() {
	rdb.Reset()
	s.NoError(rdbtest.CreateStudioDefault(rdb.Get()))
	organization.Init(organization.InitParam{
		StudioOrgRepo:  rdb.GormRepo(),
		StudioRoleRepo: rdb.GormRepo(),
	})
	tx.Init(repo.Unified())

	s.orgIDToListOperators = 10

	s.ginEngine = gin.Default()
	s.createTestOrgAssetProSeed()

	s.ginEngine.GET("/v1/studio/organization/:orgID/asset_pro/operators",
		auth.MockOrgID(int(s.orgIDToListOperators)), // use asset pro seed data
		ListOperators)

	s.ginEngine.PUT("/v1/studio/organization/:orgID/asset_pro/operators/:uid",
		auth.MockOrgID(1), // use rdb seed data
		UpdateOperator,
	)
}

func (s *testOperatorSuite) createTestOrgAssetProSeed() {

	db := rdb.Get()
	organization := model.StudioOrganization{
		ID: s.orgIDToListOperators,
	}

	s.NoError(db.Create(&organization).Error)

	now := time.Now()

	studioUsers := []model.StudioUser{
		{
			CreatedAt:      now,
			OrganizationID: s.orgIDToListOperators,
			UID:            "owner-uid",
			Name:           "owner",
			Email:          util.Ptr("<EMAIL>"),
			MemberID:       util.Ptr("owner-member-id"),
			RoleBinding: []model.StudioRoleBinding{
				{
					RoleID:         1,
					OrganizationID: s.orgIDToListOperators,
					UID:            "owner-uid",
				},
			},
			StudioUserTransferLimitation: model.StudioUserTransferLimitation{
				DailyTransferLimit: decimal.NewFromInt(100_000),
			},
			Status: model.StudioUserStatusActive,
		},
		{
			CreatedAt:      now.Add(time.Second * 1),
			OrganizationID: s.orgIDToListOperators,
			UID:            "admin1-uid",
			Name:           "assetProAdmin1",
			Email:          util.Ptr("<EMAIL>"),
			MemberID:       util.Ptr("admin1-member-id"),
			RoleBinding: []model.StudioRoleBinding{
				{
					RoleID:         4,
					OrganizationID: s.orgIDToListOperators,
					UID:            "admin1-uid",
				},
			},
			Status: model.StudioUserStatusActive,
		},
		{
			CreatedAt:      now.Add(time.Second * 2),
			OrganizationID: s.orgIDToListOperators,
			UID:            "admin2-uid",
			Name:           "assetProAdmin2",
			Email:          util.Ptr("<EMAIL>"),
			MemberID:       util.Ptr("admin2-member-id"),
			RoleBinding: []model.StudioRoleBinding{
				{
					RoleID:         4,
					OrganizationID: s.orgIDToListOperators,
					UID:            "admin2-uid",
				},
			},
			Status: model.StudioUserStatusActive,
		},
		{
			CreatedAt:      now.Add(time.Second * 3),
			OrganizationID: s.orgIDToListOperators,
			UID:            "trader-uid",
			Name:           "assetProTrader",
			Email:          util.Ptr("<EMAIL>"), // not assetpro.xyz
			MemberID:       util.Ptr("trader-member-id"),
			RoleBinding: []model.StudioRoleBinding{
				{
					RoleID:         4,
					OrganizationID: s.orgIDToListOperators,
					UID:            "trader-uid",
				},
			},
			Status: model.StudioUserStatusActive,
		},
		{
			CreatedAt:      now.Add(time.Second * 4),
			OrganizationID: s.orgIDToListOperators,
			UID:            "other-uid",
			Name:           "other-user",
			Email:          util.Ptr("<EMAIL>"),
			MemberID:       util.Ptr("other-member-id"),
			RoleBinding:    []model.StudioRoleBinding{},
			Status:         model.StudioUserStatusActive,
		},
	}

	s.NoError(db.Create(&studioUsers).Error)
}

func (s *testOperatorSuite) TestListOperatorsWithoutParameter() {
	// must have owner, admin1, admin2, trader
	w := httptest.NewRecorder()

	req, _ := http.NewRequest(http.MethodGet,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators", s.orgIDToListOperators), nil,
	)

	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type resp struct {
		Code      int        `json:"code"`
		Operators []operator `json:"data"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)
	s.Len(response.Operators, 4)
	expectedStudioUserNames := []string{
		"owner",
		"assetProAdmin1",
		"assetProAdmin2",
		"assetProTrader",
	}

	actualStudioUserNames := lo.Map(response.Operators, func(o operator, _ int) string {
		return o.Name
	})

	for _, expectedStudioUserName := range expectedStudioUserNames {
		s.Contains(actualStudioUserNames, expectedStudioUserName)
	}
}

func (s *testOperatorSuite) TestListOperatorsQueryByEmail() {
	// must have owner, admin1, admin2
	w := httptest.NewRecorder()

	req, _ := http.NewRequest(http.MethodGet,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators", s.orgIDToListOperators), nil,
	)

	q := req.URL.Query()
	q.Add("q", "assetpro.xyz")
	req.URL.RawQuery = q.Encode()

	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type resp struct {
		Code      int        `json:"code"`
		Operators []operator `json:"data"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)
	s.Len(response.Operators, 3)
	expectedStudioUserNames := []string{
		"owner",
		"assetProAdmin1",
		"assetProAdmin2",
	}

	actualStudioUserNames := lo.Map(response.Operators, func(o operator, _ int) string {
		return o.Name
	})

	for _, expectedStudioUserName := range expectedStudioUserNames {
		s.Contains(actualStudioUserNames, expectedStudioUserName)
	}
}

func (s *testOperatorSuite) TestListOperatorsAssertLimitation() {
	// must have owner, admin1, admin2
	w := httptest.NewRecorder()

	req, _ := http.NewRequest(http.MethodGet,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators", s.orgIDToListOperators), nil,
	)

	q := req.URL.Query()
	q.Add("q", "<EMAIL>")
	req.URL.RawQuery = q.Encode()

	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type resp struct {
		Code      int        `json:"code"`
		Operators []operator `json:"data"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)
	s.Len(response.Operators, 1)

	s.Equal("owner", response.Operators[0].Name)
	s.Equal(float64(100_000), response.Operators[0].DailyTransferLimit)
	s.Equal(float64(1), response.Operators[0].TransferApprovalThreshold)
}

// daily transfer limit is negative
func (s *testOperatorSuite) TestUpdateOperatorFailedDailyTransferLimitIsNegative() {
	bs := []byte(`{
			"daily_transfer_limit": -1,
			"transfer_approval_threshold": 0
			}`)
	w := httptest.NewRecorder()

	req, _ := http.NewRequest(http.MethodPut,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators/%s", 1, "uid1"),
		bytes.NewBuffer(bs),
	)

	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusBadRequest, w.Code)

	type resp struct {
		Code int `json:"code"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(1063, response.Code)
}

func (s *testOperatorSuite) TestUpdateOperatorFailedTransferApprovalThresholdIsNegative() {
	bs := []byte(`{
			"daily_transfer_limit": 0,
			"transfer_approval_threshold": -1
			}`)
	w := httptest.NewRecorder()

	req, _ := http.NewRequest(http.MethodPut,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators/%s", 1, "uid1"),
		bytes.NewBuffer(bs),
	)

	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusBadRequest, w.Code)

	type resp struct {
		Code int `json:"code"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(1063, response.Code)
}

func (s *testOperatorSuite) TestUpdateOperatorFailedTransferApprovalThresholdIsGreaterThanDailyTransferLimit() {
	{ // param incorrect
		bs := []byte(`{
				"daily_transfer_limit": 1,
				"transfer_approval_threshold": 10
				}`)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators/%s", 1, "uid1"),
			bytes.NewBuffer(bs),
		)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		type resp struct {
			Code int `json:"code"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7023, response.Code)
	}
	{ // want to set transfer_approval_threshold to 10, but daily_transfer_limit is zero
		bs := []byte(`{
			"transfer_approval_threshold": 10
		}`)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators/%s", 1, "uid1"),
			bytes.NewBuffer(bs),
		)

		s.NoError(rdb.Get().Model(&model.StudioUser{}).
			Where(&model.StudioUser{
				OrganizationID: 1,
				UID:            "uid1",
			}).
			Update("daily_transfer_limit", decimal.Zero).Error)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		type resp struct {
			Code int `json:"code"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7023, response.Code)
	}
	{ // want to set daily_transfer_limit, but it will be less than transfer_approval_threshold
		bs := []byte(`{
			"daily_transfer_limit": 100
		}`)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators/%s", 1, "uid1"),
			bytes.NewBuffer(bs),
		)

		s.NoError(rdb.Get().Model(&model.StudioUser{}).
			Where(&model.StudioUser{
				OrganizationID: 1,
				UID:            "uid1",
			}).Updates(map[string]any{
			"daily_transfer_limit":        decimal.NewFromInt(100_000),
			"transfer_approval_threshold": decimal.NewFromInt(100_000),
		}).Error)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		type resp struct {
			Code int `json:"code"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7023, response.Code)
	}
	{ // want to set transfer_approval_threshold to 10, but daily_transfer_limit is zero
		bs := []byte(`{
			"transfer_approval_threshold": 10
		}`)
		w := httptest.NewRecorder()

		req, _ := http.NewRequest(http.MethodPut,
			fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators/%s", 1, "uid1"),
			bytes.NewBuffer(bs),
		)

		s.NoError(rdb.Get().Model(&model.StudioUser{}).
			Where(&model.StudioUser{
				OrganizationID: 1,
				UID:            "uid1",
			}).
			Update("daily_transfer_limit", decimal.Zero).Error)

		s.ginEngine.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		type resp struct {
			Code int `json:"code"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(7023, response.Code)
	}
}

func (s *testOperatorSuite) TestUpdateOperatorFailedUserNotFound() {
	bs := []byte(`{
				"daily_transfer_limit": 1234.5,
				"transfer_approval_threshold": 123.45
			}`)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest(http.MethodPut,
		"/v1/studio/organization/1/asset_pro/operators/11111",
		bytes.NewBuffer(bs))

	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusNotFound, w.Code)

	type resp struct {
		Code int `json:"code"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(7014, response.Code)
}

func (s *testOperatorSuite) TestUpdateOperatorSuccessSetToNumber() {
	bs := []byte(`{
				"daily_transfer_limit": 1234.5,
				"transfer_approval_threshold": 123.45
			}`)

	w := httptest.NewRecorder()
	req, err := http.NewRequest(http.MethodPut,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators/%s", 1, "uid1"),
		bytes.NewBuffer(bs))
	s.NoError(err)

	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type resp struct {
		Code int `json:"code"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)

	actualStudioUser, kgError := rdb.GormRepo().GetStudioUser(
		context.Background(), 1, "uid1")
	s.Nil(kgError)
	s.Equal("1234.5", actualStudioUser.TransferLimitation.DailyTransferLimit.String())
	s.Equal("123.45", actualStudioUser.TransferLimitation.TransferApprovalThreshold.String())
}

func (s *testOperatorSuite) TestUpdateOperatorSuccessWithoutThreshold() {
	bs := []byte(`{
				"daily_transfer_limit": 556889,
				"transfer_approval_threshold": null
			}`)

	s.NoError(rdb.Get().Model(&model.StudioUser{}).Where("uid = ?", "uid1").UpdateColumns(map[string]any{
		"daily_transfer_limit":        decimal.Zero,
		"transfer_approval_threshold": decimal.Zero,
	}).Error)

	w := httptest.NewRecorder()
	req, err := http.NewRequest(http.MethodPut,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators/%s", 1, "uid1"),
		bytes.NewBuffer(bs))
	s.NoError(err)

	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type resp struct {
		Code int `json:"code"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)

	actualStudioUser, kgError := rdb.GormRepo().GetStudioUser(
		context.Background(), 1, "uid1")
	s.Nil(kgError)
	s.Equal("556889", actualStudioUser.TransferLimitation.DailyTransferLimit.String())
	s.Equal("0", actualStudioUser.TransferLimitation.TransferApprovalThreshold.String())
}

func (s *testOperatorSuite) TestUpdateOperatorSuccessSetToZero() {
	bs := []byte(`{
				"daily_transfer_limit": 55688,
				"transfer_approval_threshold": 0
			}`)

	w := httptest.NewRecorder()
	req, err := http.NewRequest(http.MethodPut,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators/%s", 1, "uid1"),
		bytes.NewBuffer(bs))
	s.NoError(err)

	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type resp struct {
		Code int `json:"code"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)

	actualStudioUser, kgError := rdb.GormRepo().GetStudioUser(
		context.Background(), 1, "uid1")
	s.Nil(kgError)
	s.Equal("55688", actualStudioUser.TransferLimitation.DailyTransferLimit.String())
	s.Equal("0", actualStudioUser.TransferLimitation.TransferApprovalThreshold.String())
}

func (s *testOperatorSuite) TestUpdateOwnerSuccessByItself() {
	r := gin.Default()
	r.PUT("/v1/studio/organization/:orgID/asset_pro/operators/:uid",
		auth.MockOrgID(int(s.orgIDToListOperators)), // use rdb seed data
		UpdateOperator,
	)

	// set to any
	bs := []byte(`{
				"daily_transfer_limit": 55688,
				"transfer_approval_threshold": 0
			}`)

	w := httptest.NewRecorder()
	req, err := http.NewRequest(http.MethodPut,
		fmt.Sprintf("/v1/studio/organization/%d/asset_pro/operators/%s", s.orgIDToListOperators, "owner-uid"),
		bytes.NewBuffer(bs))
	s.NoError(err)

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type resp struct {
		Code int `json:"code"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)

	actualStudioUser, kgError := rdb.GormRepo().GetStudioUser(
		context.Background(), s.orgIDToListOperators, "owner-uid")
	s.Nil(kgError)
	s.Equal("55688", actualStudioUser.TransferLimitation.DailyTransferLimit.String())
	s.Equal("0", actualStudioUser.TransferLimitation.TransferApprovalThreshold.String())

	// roles did not be changed
	s.Len(actualStudioUser.Roles, 1)
	s.Equal("owner", actualStudioUser.Roles[0].Name)
}

func TestOperatorSuite(t *testing.T) {
	suite.Run(t, new(testOperatorSuite))
}
