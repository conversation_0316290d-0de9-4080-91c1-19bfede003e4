package assetpro

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/shopspring/decimal"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
)

// Seed database with necessary data.
func setup(t *testing.T) (testPhone, testEmail string) {
	testPhone = util.RandPhone()
	testEmail = util.RandEmail()
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAssets(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAssetPrices(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAssetProTxLogsForAPITest(rdb.Get(), testPhone, testEmail))
	assert.Nil(t, rbac.Init(context.Background()))
	assetpro.InitTransfer(rdb.GormRepo())
	organization.Init(organization.InitParam{
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	})
	tx.Init(repo.Unified())

	return
}

func TestHistories(t *testing.T) {
	s := assert.New(t)
	testPhone, testEmil := setup(t)

	users, userIDs := dbtest.Users()
	uid1 := userIDs[0]
	uid2 := userIDs[1]
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	err := rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         uid1,
			PhoneNumber: testPhone,
		},
	})
	s.NoError(err)
	err = rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:   uid2,
			Email: &testEmil,
		},
	})
	s.NoError(err)

	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/asset_pro/transfer/histories", auth.MockOrgID(orgID), Histories)
	// without filter
	{
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code   int     `json:"code"`
			Data   []txLog `json:"data"`
			Paging struct {
				PageNumber int    `json:"page_number"`
				PageSize   int    `json:"page_size"`
				TotalCount int    `json:"total_count"`
				PageSort   string `json:"page_sort"`
			} `json:"paging"`
		}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Equal(6, len(response.Data))
		s.Equal(6, response.Paging.TotalCount)

		txLogsStatusAwaitingApproval := lo.Filter(response.Data, func(txLog txLog, _ int) bool {
			return txLog.Status == "awaiting_approval"
		})
		s.Equal(1, len(txLogsStatusAwaitingApproval))
		operatorNames := lo.Filter(response.Data, func(txLog txLog, _ int) bool {
			return util.Val(txLog.UpdatedBy) != ""
		})
		s.NotZero(len(operatorNames))
		recipientEmails := lo.Filter(response.Data, func(txLog txLog, _ int) bool {
			return txLog.Recipient.Email != nil && *txLog.Recipient.Email != ""
		})
		s.NotZero(len(recipientEmails))

		// check order: submit_time > status
		s.Equal(1626395200, int(response.Data[0].SubmitTime))
		s.Equal(1626385200, int(response.Data[1].SubmitTime))
		s.Equal(1626375200, int(response.Data[2].SubmitTime))
		s.Equal(1626365200, int(response.Data[3].SubmitTime))
		s.Equal(1626355200, int(response.Data[4].SubmitTime))

		s.Equal("", response.Data[0].Recipient.UID)
		s.Equal("", response.Data[1].Recipient.UID)
		s.Equal(uid1, response.Data[2].Recipient.UID)
		s.Equal(uid2, response.Data[3].Recipient.UID)
		s.Equal("", response.Data[4].Recipient.UID)
		s.Equal("", response.Data[5].Recipient.UID)
	}

	// with filter, token & chain_id
	{
		w := httptest.NewRecorder()
		queryString := "token=USDT&chain_id=sepolia"
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories?%s", orgID, queryString), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code int     `json:"code"`
			Data []txLog `json:"data"`
		}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.NotZero(len(response.Data))
		txLogsStatusAwaitingApproval := lo.Filter(response.Data, func(txLog txLog, _ int) bool {
			return txLog.Status == "awaiting_approval"
		})
		s.Zero(len(txLogsStatusAwaitingApproval))
		operatorNames := lo.Filter(response.Data, func(txLog txLog, _ int) bool {
			return util.Val(txLog.UpdatedBy) != ""
		})
		s.NotZero(len(operatorNames))
		recipientEmails := lo.Filter(response.Data, func(txLog txLog, _ int) bool {
			return txLog.Recipient.Email != nil && *txLog.Recipient.Email != ""
		})
		s.NotZero(len(recipientEmails))
		fmt.Println(response.Data[0])
		chainTokens := lo.Filter(response.Data, func(txLog txLog, _ int) bool {
			return txLog.ChainID == "sepolia" && txLog.TokenName == "Tether USD"
		})
		s.NotZero(len(chainTokens))
	}

	// with filter, status
	{
		w := httptest.NewRecorder()
		queryString := "status=awaiting_approval&status=sending"
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories?%s", orgID, queryString), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code int     `json:"code"`
			Data []txLog `json:"data"`
		}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.NotZero(len(response.Data))
		txLogsStatus := lo.Filter(response.Data, func(txLog txLog, _ int) bool {
			return txLog.Status == "awaiting_approval" || txLog.Status == "sending"
		})
		s.Equal(len(response.Data), len(txLogsStatus))
	}
}

func TestHistoriesFilterOptions(t *testing.T) {
	s := assert.New(t)
	setup(t)

	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/asset_pro/transfer/filter_options", auth.MockOrgID(orgID), HistoriesFilterOptions)
	{ // with seed data
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/filter_options", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code int           `json:"code"`
			Data filterOptions `json:"data"`
		}

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Len(response.Data.Status, 3)
		s.Len(response.Data.Submitter, 2)
		s.Equal(response.Data.Submitter[0].Name, "trader1")
		s.Equal(response.Data.Submitter[1].Name, "testuser1")
		s.Equal(response.Data.Submitter[0].UID, "trader1")
		s.Equal(response.Data.Submitter[1].UID, "uid1")
		s.Len(response.Data.Approver, 1)
		s.Equal(response.Data.Approver[0].Name, "testuser1")
		s.Equal(response.Data.Approver[0].UID, "uid1")
		s.Len(response.Data.FinanceManager, 1)
		s.Equal(response.Data.FinanceManager[0].Name, "testuser2")
		s.Equal(response.Data.FinanceManager[0].UID, "testuser2")
		s.Len(response.Data.Token, 2)
		s.Contains(response.Data.Token, "USDC")
		s.Contains(response.Data.Token, "USDT")
		s.Len(response.Data.BlockChain, 2)
		s.Contains(response.Data.BlockChain, "sepolia")
		s.Contains(response.Data.BlockChain, "shasta")
		s.Equal(int64(1626355200), *response.Data.SubmitTimeFrom)
		s.Equal(int64(1626395200), *response.Data.SubmitTimeTo)
		s.Equal(100.0, *response.Data.AmountFrom)
		s.Equal(200.0, *response.Data.AmountTo)
		s.Equal(100.0, *response.Data.UsdAmountFrom)
		s.Equal(200.0, *response.Data.UsdAmountTo)
	}
	{ // with no tx log
		s.NoError(rdb.Get().Where("1=1").Delete(&model.AssetProTxLog{}).Error)

		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/filter_options", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code int           `json:"code"`
			Data filterOptions `json:"data"`
		}

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Len(response.Data.Status, 0)
		s.NotNil(response.Data.Status)
		s.Len(response.Data.Submitter, 0)
		s.NotNil(response.Data.Submitter)
		s.Len(response.Data.Approver, 0)
		s.NotNil(response.Data.Approver)
		s.Len(response.Data.FinanceManager, 0)
		s.NotNil(response.Data.FinanceManager)
		s.Len(response.Data.Token, 0)
		s.NotNil(response.Data.Token)
		s.Len(response.Data.BlockChain, 0)
		s.NotNil(response.Data.BlockChain)
		s.Len(response.Data.Approver, 0)
		s.NotNil(response.Data.Approver)
		s.Nil(response.Data.AmountFrom)
		s.Nil(response.Data.AmountTo)
		s.Nil(response.Data.UsdAmountFrom)
		s.Nil(response.Data.UsdAmountTo)
	}
}

func TestHistoriesByDifferentRoles(t *testing.T) {

	s := assert.New(t)
	setup(t)

	orgID := 1
	uid := "uid1"
	traderUID := "trader1"

	kgErr := organization.CacheUserRoleBindings(context.Background(), orgID, uid)
	s.Nil(kgErr)
	kgErr = organization.CacheUserRoleBindings(context.Background(), orgID, traderUID)
	s.Nil(kgErr)

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/asset_pro/transfer/histories", auth.MockOrgID(orgID), auth.MockAuthorize(uid), Histories)
	r.GET("/v1/studio/organization/:orgID/asset_pro/transfer/histories_trader", auth.MockOrgID(orgID), auth.MockAuthorize(traderUID), Histories)
	{
		// owner can see all tx logs
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code int     `json:"code"`
			Data []txLog `json:"data"`
		}

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Len(response.Data, 6)
	}
	{
		// trader can see tx logs that he/she is operator
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories_trader", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code    int     `json:"code"`
			Data    []txLog `json:"data"`
			Message string  `json:"message"`
		}

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Len(response.Data, 1)
		s.Empty(response.Message)
	}
	{
		// trader can see tx logs that he/she is operator
		// but contradict with operator_uid in query string
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/histories_trader?operator_uid=123", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var response struct {
			Code int     `json:"code"`
			Data []txLog `json:"data"`
		}

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Len(response.Data, 1)
	}
}

func TestPendingHistoryCount(t *testing.T) {
	s := assert.New(t)
	setup(t)

	orgID := 1

	owner := "uid1"
	trader := "trader1"
	approver := "approver1"
	financeManager := "finance_manager1"
	admin := "admin1"

	s.Nil(rdb.Get().Create(&[]model.StudioUser{
		{
			OrganizationID: orgID,
			UID:            approver,
			Name:           "approver1",
			Status:         model.StudioUserStatusActive,
		},
		{
			OrganizationID: orgID,
			UID:            financeManager,
			Name:           "finance_manager1",
			Status:         model.StudioUserStatusActive,
		},
		{
			OrganizationID: orgID,
			UID:            admin,
			Name:           "admin1",
			Status:         model.StudioUserStatusActive,
		},
	}).Error)

	s.Nil(rdb.Get().Create(&[]model.StudioRoleBinding{
		{
			RoleID:         5,
			OrganizationID: orgID,
			UID:            approver,
		},
		{
			RoleID:         9,
			OrganizationID: orgID,
			UID:            financeManager,
		},
		{
			RoleID:         4,
			OrganizationID: orgID,
			UID:            admin,
		},
	}).Error)

	s.Nil(rdb.Get().Create(&[]model.AssetProTxLog{
		{
			OrganizationID:  orgID,
			OperatorUID:     trader,
			Token:           "USDT",
			SubmitTime:      1626395200,
			Status:          domain.AssetProTxLogStatusAwaitingApproval,
			ChainID:         model.ChainIDSepolia,
			ContractAddress: "0xB84E5D9DD454d8894A455C6d1A8ede9d29321D2e",
			FromAddress:     "0x9BAE3A9cBac6E769D980f6ce0fd52397dD45d361",
			ToAddress:       "0x31d1C7751EAA6374D4138597e8c7b5a1605cC43c",
			TxHash:          nil,
			TransferTime:    nil,
			Amount:          decimal.NewFromFloat(100),
			UsdAmount:       util.Ptr(decimal.NewFromFloat(100)),
		},
		{
			OrganizationID:  orgID,
			OperatorUID:     trader,
			Token:           "USDT",
			SubmitTime:      1626385200,
			Status:          domain.AssetProTxLogStatusAwaitingRelease,
			ChainID:         model.ChainIDSepolia,
			ContractAddress: "0xB84E5D9DD454d8894A455C6d1A8ede9d29321D2e",
			FromAddress:     "0x9BAE3A9cBac6E769D980f6ce0fd52397dD45d361",
			ToAddress:       "0x31d1C7751EAA6374D4138597e8c7b5a1605cC43c",
			TxHash:          nil,
			TransferTime:    nil,
			Amount:          decimal.NewFromFloat(100),
			UsdAmount:       util.Ptr(decimal.NewFromFloat(100)),
		},
		{
			OrganizationID:  orgID,
			OperatorUID:     owner,
			Token:           "USDT",
			SubmitTime:      1626375200,
			Status:          domain.AssetProTxLogStatusAwaitingApproval,
			ChainID:         model.ChainIDSepolia,
			ContractAddress: "0xB84E5D9DD454d8894A455C6d1A8ede9d29321D2e",
			FromAddress:     "0x9BAE3A9cBac6E769D980f6ce0fd52397dD45d361",
			ToAddress:       "0x31d1C7751EAA6374D4138597e8c7b5a1605cC43c",
			TxHash:          nil,
			TransferTime:    nil,
			Amount:          decimal.NewFromFloat(100),
			UsdAmount:       util.Ptr(decimal.NewFromFloat(100)),
		},
		{
			OrganizationID:  orgID,
			OperatorUID:     owner,
			Token:           "USDT",
			SubmitTime:      1626365200,
			Status:          domain.AssetProTxLogStatusSendFailed,
			ChainID:         model.ChainIDSepolia,
			ContractAddress: "0xB84E5D9DD454d8894A455C6d1A8ede9d29321D2e",
			FromAddress:     "0x9BAE3A9cBac6E769D980f6ce0fd52397dD45d361",
			ToAddress:       "0x31d1C7751EAA6374D4138597e8c7b5a1605cC43c",
			TxHash:          nil,
			TransferTime:    nil,
			Amount:          decimal.NewFromFloat(100),
			UsdAmount:       util.Ptr(decimal.NewFromFloat(100)),
		},
		{
			OrganizationID:  orgID,
			OperatorUID:     owner,
			Token:           "USDT",
			SubmitTime:      1626355200,
			Status:          domain.AssetProTxLogStatusSendSuccess,
			ChainID:         model.ChainIDSepolia,
			ContractAddress: "0xB84E5D9DD454d8894A455C6d1A8ede9d29321D2e",
			FromAddress:     "0x9BAE3A9cBac6E769D980f6ce0fd52397dD45d361",
			ToAddress:       "0x31d1C7751EAA6374D4138597e8c7b5a1605cC43c",
			TxHash:          nil,
			TransferTime:    nil,
			Amount:          decimal.NewFromFloat(100),
			UsdAmount:       util.Ptr(decimal.NewFromFloat(100)),
		},
		{
			OrganizationID:  orgID,
			OperatorUID:     owner,
			Token:           "USDT",
			SubmitTime:      1626345200,
			Status:          domain.AssetProTxLogStatusAwaitingRelease,
			ChainID:         model.ChainIDSepolia,
			ContractAddress: "0xB84E5D9DD454d8894A455C6d1A8ede9d29321D2e",
			FromAddress:     "0x9BAE3A9cBac6E769D980f6ce0fd52397dD45d361",
			ToAddress:       "0x31d1C7751EAA6374D4138597e8c7b5a1605cC43c",
			TxHash:          nil,
			TransferTime:    nil,
			Amount:          decimal.NewFromFloat(100),
			UsdAmount:       util.Ptr(decimal.NewFromFloat(100)),
			ApproverUID:     util.Ptr(approver),
		},
		{
			OrganizationID:  orgID,
			OperatorUID:     owner,
			Token:           "USDT",
			SubmitTime:      1626335200,
			Status:          domain.AssetProTxLogStatusAwaitingApproval,
			ChainID:         model.ChainIDSepolia,
			ContractAddress: "0xB84E5D9DD454d8894A455C6d1A8ede9d29321D2e",
		},
	}).Error)

	kgErr := organization.CacheUserRoleBindings(context.Background(), orgID, owner)
	s.Nil(kgErr)
	kgErr = organization.CacheUserRoleBindings(context.Background(), orgID, trader)
	s.Nil(kgErr)
	kgErr = organization.CacheUserRoleBindings(context.Background(), orgID, approver)
	s.Nil(kgErr)
	kgErr = organization.CacheUserRoleBindings(context.Background(), orgID, financeManager)
	s.Nil(kgErr)
	kgErr = organization.CacheUserRoleBindings(context.Background(), orgID, admin)
	s.Nil(kgErr)

	type res struct {
		Code int `json:"code"`
		Data struct {
			Count                     int `json:"count"`
			CountAwaitingApprovalSelf int `json:"count_awaiting_approval_self"`
			CountAwaitingApproval     int `json:"count_awaiting_approval"`
			CountAwaitingRelease      int `json:"count_awaiting_release"`
		} `json:"data"`
	}

	{
		// owner
		r := gin.Default()
		r.GET("/v1/studio/organization/:orgID/asset_pro/transfer/pending_history_count", auth.MockOrgID(orgID), auth.MockAuthorize(owner), PendingHistoryCount)
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/pending_history_count", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		var response res

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Equal(6, response.Data.Count)
		s.Equal(4, response.Data.CountAwaitingApprovalSelf)
		s.Equal(4, response.Data.CountAwaitingApproval)
		s.Equal(2, response.Data.CountAwaitingRelease)
	}
	{
		// trader
		r := gin.Default()
		r.GET("/v1/studio/organization/:orgID/asset_pro/transfer/pending_history_count", auth.MockOrgID(orgID), auth.MockAuthorize(trader), PendingHistoryCount)
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/pending_history_count", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response res

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Equal(2, response.Data.Count)
		s.Equal(2, response.Data.CountAwaitingApprovalSelf)
		s.Equal(0, response.Data.CountAwaitingApproval)
		s.Equal(0, response.Data.CountAwaitingRelease)
	}
	{
		// approver
		r := gin.Default()
		r.GET("/v1/studio/organization/:orgID/asset_pro/transfer/pending_history_count", auth.MockOrgID(orgID), auth.MockAuthorize(approver), PendingHistoryCount)
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/pending_history_count", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response res

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Equal(4, response.Data.Count)
		s.Equal(0, response.Data.CountAwaitingApprovalSelf)
		s.Equal(4, response.Data.CountAwaitingApproval)
		s.Equal(0, response.Data.CountAwaitingRelease)
	}
	{
		// finance manager
		r := gin.Default()
		r.GET("/v1/studio/organization/:orgID/asset_pro/transfer/pending_history_count", auth.MockOrgID(orgID), auth.MockAuthorize(financeManager), PendingHistoryCount)
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/pending_history_count", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response res

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Equal(2, response.Data.Count)
		s.Equal(0, response.Data.CountAwaitingApprovalSelf)
		s.Equal(0, response.Data.CountAwaitingApproval)
		s.Equal(2, response.Data.CountAwaitingRelease)
	}
	{
		// admin
		r := gin.Default()
		r.GET("/v1/studio/organization/:orgID/asset_pro/transfer/pending_history_count", auth.MockOrgID(orgID), auth.MockAuthorize(admin), PendingHistoryCount)
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/pending_history_count", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response res

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(0, response.Code)
		s.Equal(6, response.Data.Count)
		s.Equal(0, response.Data.CountAwaitingApprovalSelf)
		s.Equal(4, response.Data.CountAwaitingApproval)
		s.Equal(2, response.Data.CountAwaitingRelease)
	}
}

func TestHistoryDetail(t *testing.T) {
	s := assert.New(t)
	testPhone, testEmail := setup(t)

	users, userIDs := dbtest.Users()
	uid1 := userIDs[0]
	uid2 := userIDs[1]
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	err := rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         uid1,
			PhoneNumber: testPhone,
		},
	})
	s.NoError(err)
	err = rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:   uid2,
			Email: &testEmail,
		},
	})
	s.NoError(err)

	orgID := 1
	serialID := "serial1"

	type txLogDetail struct {
		TxInfo struct {
			ID        string `json:"id"`
			Status    string `json:"status"`
			Amount    string `json:"amount"`
			UsdAmount string `json:"usd_amount"`
			Token     struct {
				Name            string `json:"name"`
				Symbol          string `json:"symbol"`
				ChainID         string `json:"chain_id"`
				LogoURL         string `json:"logo_url"`
				CoingeckoID     string `json:"coingecko_id"`
				ContractAddress string `json:"contract_address"`
				Decimals        int    `json:"decimals"`
			} `json:"token"`
			Recipient struct {
				WalletAddress string `json:"wallet_address"`
				Customer      struct {
					UID        string  `json:"uid"`
					Name       string  `json:"name"`
					ProfileImg *string `json:"profile_img"`
					Phone      *string `json:"phone"`
					Email      *string `json:"email"`
					KycStatus  string  `json:"kyc_status"`
				} `json:"customer"`
			} `json:"recipient"`
			Notes struct {
				SubmissionNote *string `json:"submission_note"`
				RejectionNote  *string `json:"rejection_note"`
			} `json:"notes"`
			Attachments *[]string `json:"attachments"`
			TxHashes    *[]string `json:"tx_hashes"`
		} `json:"tx_info"`
		Operators struct {
			Trader         txLogOperator  `json:"trader"`
			Approver       *txLogOperator `json:"approver"`
			FinanceManager *txLogOperator `json:"finance_manager"`
		} `json:"operators"`
	}

	type txLogOperator struct {
		Operator operatorInfo `json:"operator"`
		Time     int          `json:"time"`
	}

	type operatorInfo struct {
		Name       string `json:"name"`
		ProfileImg string `json:"profile_img"`
		Email      string `json:"email"`
	}

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/asset_pro/transfer/history/:id", auth.MockOrgID(orgID), HistoryDetail)
	{
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/history/%s", orgID, serialID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response struct {
			Code int         `json:"code"`
			Data txLogDetail `json:"data"`
		}

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)

		s.Equal("serial1", response.Data.TxInfo.ID)
		s.Equal("send_success", response.Data.TxInfo.Status)
		s.Equal("200", response.Data.TxInfo.Amount)
		s.Equal("200", response.Data.TxInfo.UsdAmount)
		s.Equal("Tether USD", response.Data.TxInfo.Token.Name)
		s.Equal("USDT", response.Data.TxInfo.Token.Symbol)
		s.Equal("shasta", response.Data.TxInfo.Token.ChainID)
		s.Equal("https://token-icons.s3.amazonaws.com/******************************************.png", response.Data.TxInfo.Token.LogoURL)
		s.Equal("tether", response.Data.TxInfo.Token.CoingeckoID)
		s.Equal("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs", response.Data.TxInfo.Token.ContractAddress)
		s.Equal(6, response.Data.TxInfo.Token.Decimals)
		s.Equal("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7", response.Data.TxInfo.Recipient.WalletAddress)
		s.Equal(uid1, response.Data.TxInfo.Recipient.Customer.UID)
		s.Equal("陳美珠", response.Data.TxInfo.Recipient.Customer.Name)
		s.Equal("https://lh3.googleusercontent.com/H4nD73fI_qNP_C4mn6d2pImSpHeQ9VRKZ5YTlpyYvYfGw00f_6NVhfvvXxLQt147_yjj-2XTODzI5B_MLFSBdGUbRX_di3Oezq59uQ", *response.Data.TxInfo.Recipient.Customer.ProfileImg)
		s.Equal(testPhone, *response.Data.TxInfo.Recipient.Customer.Phone)
		s.Nil(response.Data.TxInfo.Recipient.Customer.Email)
		s.Equal("unverified", response.Data.TxInfo.Recipient.Customer.KycStatus)
		s.Nil(response.Data.TxInfo.Notes.SubmissionNote)
		s.Nil(response.Data.TxInfo.Notes.RejectionNote)
		s.Nil(response.Data.TxInfo.Attachments)
		s.Len(*response.Data.TxInfo.TxHashes, 1)
		s.Equal("******************************************", (*response.Data.TxInfo.TxHashes)[0])
		s.Equal("testuser1", response.Data.Operators.Trader.Operator.Name)
		s.Equal("", response.Data.Operators.Trader.Operator.ProfileImg)
		s.Equal("<EMAIL>", response.Data.Operators.Trader.Operator.Email)
		s.Equal(1626375200, response.Data.Operators.Trader.Time)
		s.Equal("testuser1", response.Data.Operators.Approver.Operator.Name)
		s.Equal("", response.Data.Operators.Approver.Operator.ProfileImg)
		s.Equal("<EMAIL>", response.Data.Operators.Approver.Operator.Email)
		s.Equal(1626376200, response.Data.Operators.Approver.Time)
		s.Equal("testuser2", response.Data.Operators.FinanceManager.Operator.Name)
		s.Equal("", response.Data.Operators.FinanceManager.Operator.ProfileImg)
		s.Equal("<EMAIL>", response.Data.Operators.FinanceManager.Operator.Email)
		s.Equal(1626396200, response.Data.Operators.FinanceManager.Time)
	}
	{
		w := httptest.NewRecorder()
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer/history/100", orgID), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
		var response struct {
			Code int         `json:"code"`
			Data txLogDetail `json:"data"`
		}

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.Nil(t, err)
		s.Equal(6010, response.Code)
		s.Zero(response.Data)
	}
}
