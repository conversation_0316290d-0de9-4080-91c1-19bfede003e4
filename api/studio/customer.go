package studio

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	customerService "github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type getCustomerReq struct {
	PhoneNumber string `form:"phone_number"`
	Email       string `form:"email"`
}

// GetCustomer .
func GetCustomer(ctx *gin.Context) {
	req := getCustomerReq{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")

	customer, kgErr := customerService.GetCustomer(ctx.Request.Context(), req.Phone<PERSON>umber, req.Email, orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, customer)
}

// GetCustomerProfile .
func GetCustomerProfile(ctx *gin.Context) {
	orgID, err := strconv.Atoi(ctx.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid organization")
		return
	}

	uid := ctx.GetString("uid")

	customer, kgErr := customerService.GetCustomerProfile(ctx.Request.Context(), uid, orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, customer)
}
