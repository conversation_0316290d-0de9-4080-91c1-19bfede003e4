package studio_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/kryptogo/kg-wallet-backend/api"
	authapi "github.com/kryptogo/kg-wallet-backend/api/auth"
	"github.com/kryptogo/kg-wallet-backend/api/kms"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	oauthapi "github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	organizationapi "github.com/kryptogo/kg-wallet-backend/api/studio/organization"
	userapi "github.com/kryptogo/kg-wallet-backend/api/studio/user"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	authservice "github.com/kryptogo/kg-wallet-backend/pkg/service/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/samber/lo"
	"github.com/sendgrid/rest"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"
)

type testIntegrationSuite struct {
	suite.Suite

	*validator.Validate

	shutdownSigningServer func()
	shutdownKmsServer     func()
}

func (s *testIntegrationSuite) SetupSuite() {
	s.Validate = validator.New()
	s.NoError(rbac.Init(context.Background()))
}

func (s *testIntegrationSuite) SetupTest() {
	rdb.Reset()
	user.Init(repo.Unified())

	s.NoError(dbtest.CreateStudioRoles(rdb.Get()))

	s.shutdownSigningServer = setupSigningServer(s.T())
	s.shutdownKmsServer = setupKmsServer(s.T())
}

func (s *testIntegrationSuite) TearDownTest() {
	s.shutdownSigningServer()
	s.shutdownKmsServer()
}

func setupSigningServer(t *testing.T) func() {
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	// signing server
	rSigning := gin.Default()
	rSigning.POST("/v1/wallets", signing.AuthorizeSigningServer, signing.CreateOrganizationWallet)
	// create HTTP server for graceful shutdown
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)

	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
	time.Sleep(1 * time.Second) // wait for server to start

	return func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}
}

func setupKmsServer(t *testing.T) func() {
	rKMS := gin.Default()
	rKMS.POST("/v1/kms/generateMnemonicAddresses", kms.GenerateMnemonicAddresses)

	srv := &http.Server{
		Addr:    ":" + config.GetString("TEST_KMS_PORT"),
		Handler: rKMS,
	}
	go func() {
		err := srv.ListenAndServe()
		t.Logf("KMS server terminated with error: %v\n", err)
	}()

	time.Sleep(100 * time.Millisecond)
	return func() {
		assert.NoError(t, srv.Shutdown(context.Background()))
		fmt.Println("Finish Shutdown")
	}
}

func (s *testIntegrationSuite) TestCreateOrgAndOwnerAndInviteUser() {
	// We assume that there is a kg user who has already bound email.
	// Use the kg uid and email to create a studio organization and invite the kg user to join the studio organization.

	ctx := context.Background()
	// inject mock
	ctrl := gomock.NewController(s.T())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	mockEmailClient := sendgrid.NewMockEmailClient(ctrl)
	{
		mockEmailClient.EXPECT().SendEmailWithSubject(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Times(1).Return(&rest.Response{StatusCode: 200}, nil)
	}
	{
		m := alchemyapi.NewMockIAlchemy(ctrl)
		countOfWebhook := lo.Reduce(lo.Values(config.GetStringMap("ALCHEMY_WEBHOOK_ID_MAP")), func(count int, v string, _ int) int {
			if len(v) == 0 {
				return count
			}

			return count + 1
		}, 0)

		m.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).
			Times(countOfWebhook).
			DoAndReturn(func(ctx context.Context, webhookID string, addressesToAdd []string) error {
				for _, address := range addressesToAdd {
					s.Len(address, 42)
				}
				return nil
			})
		alchemyapi.Set(m)
	}

	initParam := organization.InitParam{
		StudioOrgRepo:       rdb.GormRepo(),
		StudioRoleRepo:      rdb.GormRepo(),
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
		SendgridClient:      mockEmailClient,
	}

	organization.Init(initParam)

	user.Init(repo.Unified())
	application.Init(rdb.GormRepo())
	assetpro.InitTransfer(rdb.GormRepo())

	r := gin.Default()
	// create
	r.POST("/_v/studio/organizations", organizationapi.CreateStudioOrganization)
	r.POST("/_v/users", api.CreateUser)
	r.PUT("/_v/studio/organization/:orgID", organizationapi.UpdateStudioOrganization)
	r.PUT("/_v/oauth/configs", oauthapi.UpsertOAuthClientConfigs)
	// r.GET("/v1/studio/organization/:orgID/user_exist", auth.MockAuthorize(adminUID), UserExist)
	r.POST("/v1/login", authapi.Login)
	r.POST("/v1/studio/login_v2", auth.AuthorizeByKgToken(), userapi.LoginV2)
	r.GET("/v1/studio/organization/:orgID/me", middleware.AuthStudioTokenV2, middleware.OrganizationUserValidation(), userapi.UserInfo)
	r.GET("/v1/studio/organization/:orgID/info", middleware.AuthStudioTokenV2, middleware.OrganizationUserValidation(), organizationapi.GetOrganizationInfo)
	r.PUT("/v1/studio/organization/:orgID/info", middleware.AuthStudioTokenV2, middleware.OrganizationUserValidation(), organizationapi.SaveOrganizationInfo)
	r.GET("/v1/studio/organization/:orgID/users", middleware.AuthStudioTokenV2, middleware.OrganizationUserValidation(), userapi.ListUsers)
	r.POST("/v1/studio/organization/:orgID/users", middleware.AuthStudioTokenV2, middleware.OrganizationUserValidation(), userapi.InviteStudioUser)
	r.POST("/v1/studio/organization/:orgID/accept_invitation", auth.AuthorizeByKgToken(), userapi.AcceptInvitation)
	r.POST("/v1/studio/organization/:orgID/asset_pro/transfer", middleware.AuthStudioTokenV2, middleware.OrganizationUserValidation(),
		middleware.StudioPermissionRequired(rbac.ResourceTransaction, rbac.ActionApply),
		func(c *gin.Context) {
			response.OK(c, nil)
		})
	r.DELETE("/v1/studio/organization/:orgID/users/:userID", middleware.AuthStudioTokenV2, middleware.OrganizationUserValidation(),
		middleware.StudioPermissionRequired(rbac.ResourceMember, rbac.ActionEdit),
		userapi.DisableUser)

	var (
		orgName      = "test-org"
		ownerName    = fmt.Sprintf("owner-%d", time.Now().Unix())
		complianceID = 100
		ownerKGEmail = fmt.Sprintf("%<EMAIL>", ownerName)
		ownerEmail   = fmt.Sprintf("%<EMAIL>", ownerName)
		adminName    = fmt.Sprintf("admin-%d", time.Now().Unix())
		adminEmail   = fmt.Sprintf("%<EMAIL>", adminName)
		iconURL      = "http://www.kryptogo.com/icon.png"

		orgID            int32
		ownerUID         string
		ownerStudioToken string

		adminUID         string
		adminStudioToken string
	)

	{ // create firebase owner
		bs, err := json.Marshal(map[string]interface{}{
			"email": ownerKGEmail,
		})

		s.NoError(err)
		w := httptest.NewRecorder()
		req, err := http.NewRequest("POST", "/_v/users", bytes.NewReader(bs))
		s.NoError(err)
		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)

		var response struct {
			Code int `json:"code" validate:"eq=0"`
			Data struct {
				UID string `json:"uid" validate:"required"`
			} `json:"data" validate:"required"`
		}
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.NoError(s.Struct(response))
		s.Equal(0, response.Code)
		s.NotEmpty(response.Data.UID)
		ownerUID = response.Data.UID
	}

	{ // create org
		bs, err := json.Marshal(map[string]interface{}{
			"name":                       orgName,
			"uid":                        ownerUID,
			"admin_name":                 ownerName,
			"compliance_organization_id": complianceID,
			"email":                      ownerEmail,
			"icon_url":                   iconURL,
		})
		s.NoError(err)
		w := httptest.NewRecorder()
		req, err := http.NewRequest("POST", "/_v/studio/organizations", bytes.NewReader(bs))
		s.NoError(err)
		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)
		if w.Code != http.StatusOK {
			fmt.Printf("post /_v/studio/organizations body: %s\n", string(bs))
		}

		var response struct {
			Code int `json:"code" validate:"eq=0"`
			Data struct {
				ID int32 `json:"id" validate:"required"`
			} `json:"data" validate:"required"`
		}
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.NoError(s.Struct(response))
		s.NotZero(response.Data.ID)
		orgID = response.Data.ID
	}

	{ // create oauth client configs
		w := httptest.NewRecorder()
		bs, err := json.Marshal(struct {
			ID                   string   `json:"id"`
			Domain               string   `json:"domain"`
			IsPrivileged         bool     `json:"is_privileged"`
			Name                 string   `json:"name"`
			SupportAddress       string   `json:"support_address"`
			MainLogo             string   `json:"main_logo"`
			AppStoreLink         string   `json:"app_store_link"`
			GooglePlayLink       string   `json:"google_play_link"`
			SendbirdAppID        string   `json:"sendbird_app_id"`
			SendbirdApiTokenName string   `json:"sendbird_api_token_name"`
			OrganizationID       int      `json:"organization_id"`
			ApplicationType      string   `json:"application_type"`
			SquareLogo           string   `json:"square_logo"`
			WideLogo             string   `json:"wide_logo"`
			Scopes               []string `json:"scopes"`
			Secret               string   `json:"secret"`
		}{
			ID:                   "kryptogo-wallet-app",
			Domain:               "http://localhost:8040",
			IsPrivileged:         true,
			Name:                 "KryptoGO",
			SupportAddress:       "<EMAIL>",
			MainLogo:             "https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png",
			AppStoreLink:         "https://apps.apple.com/tw/app/kryptogo/id1593830910",
			GooglePlayLink:       "https://play.google.com/store/apps/details?id=com.kryptogo.walletapp",
			SendbirdAppID:        "sendbird-test-app-id",
			SendbirdApiTokenName: "SENDBIRD_KRYPTOGO_API_TOKEN",
			Secret:               "xxxxx",
			OrganizationID:       int(orgID),
			ApplicationType:      domain.OAuthApplicationTypeMobileWallet.String(),
		})
		s.NoError(err)
		req, _ := http.NewRequest(http.MethodPut, "/_v/oauth/configs", bytes.NewReader(bs))
		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)
	}

	{ // owner login
		s.NoError(dbtest.CreateEmailLogs(rdb.Get(), []string{
			ownerKGEmail,
		}, nil))

		w := httptest.NewRecorder()
		bs, err := json.Marshal(map[string]interface{}{
			"email":             ownerKGEmail,
			"verification_code": "123456",
		})
		s.NoError(err)
		req, _ := http.NewRequest(http.MethodPost, "/v1/login", bytes.NewBuffer(bs))

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type loginResp struct {
			Code int `json:"code" validate:"eq=0"`
			Data struct {
				// AccessToken string `json:"access_token"`
				// IDToken     string `json:"id_token"`
				KgToken string `json:"kg_token" required:"true"`
			} `json:"data" required:"true"`
			Msg string `json:"message"`
		}

		var loginResponse loginResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &loginResponse))
		s.NoError(s.Struct(loginResponse))
		s.Equal(0, loginResponse.Code)
		s.NotEmpty(loginResponse.Data.KgToken)
		s.Empty(loginResponse.Msg)

		w = httptest.NewRecorder()
		req, _ = http.NewRequest(http.MethodPost, "/v1/studio/login_v2", nil)
		req.Header.Set("KG-TOKEN", loginResponse.Data.KgToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type studioLoginResp struct {
			Code int `json:"code" validate:"eq=0"`
			Data struct {
				StudioToken string `json:"studio_token" required:"true"`
			} `json:"data"`
		}

		var studioLoginResponse studioLoginResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &studioLoginResponse))
		s.NoError(s.Struct(studioLoginResponse))
		s.Equal(0, studioLoginResponse.Code)
		s.NotEmpty(studioLoginResponse.Data.StudioToken)

		ownerStudioToken = studioLoginResponse.Data.StudioToken
	}

	{ // owner can get studio user, and resp contains owner
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("/v1/studio/organization/%d/users", orgID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", ownerStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type listUserResp struct {
			Code int `json:"code" validate:"eq=0"`
			Data []struct {
				UID    string `json:"uid" validate:"required"`
				Email  string `json:"email" validate:"required"`
				Status string `json:"status" validate:"required"`
			} `json:"data" validate:"required"`
		}

		var listUserResponse listUserResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &listUserResponse))
		s.NoError(s.Struct(listUserResponse))
		s.Len(listUserResponse.Data, 1)
		s.Equal(ownerUID, listUserResponse.Data[0].UID)
		s.Equal(ownerEmail, listUserResponse.Data[0].Email)
		s.Equal("active", listUserResponse.Data[0].Status)
	}

	{ // invite user, but studio module is empty
		s.NoError(dbtest.CreateEmailLogs(rdb.Get(), []string{
			adminEmail,
		}, nil))

		w := httptest.NewRecorder()
		bs, err := json.Marshal(map[string]interface{}{
			"roles": []string{"owner"},
			"email": adminEmail,
			"name":  adminName,
		})
		s.NoError(err)
		req, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("/v1/studio/organization/%d/users", orgID), bytes.NewBuffer(bs))
		req.Header.Set("KG-STUDIO-TOKEN-V2", ownerStudioToken)
		r.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		type createUserResp struct {
			Code int `json:"code" validate:"required"`
		}

		var resp createUserResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.NoError(s.Struct(resp))
		s.Equal(7024, resp.Code)
	}
	{ // update studio module
		w := httptest.NewRecorder()
		bs, err := json.Marshal(map[string]interface{}{
			"module": map[string]interface{}{
				"asset_pro":      []string{"treasury", "send_token", "transaction_history"},
				"nft_boost":      []string{"campaign", "reward"},
				"user_360":       []string{"data", "engage", "audience"},
				"wallet_builder": []string{"project", "configuration", "app_publish", "marketing_tools"},
				"compliance":     []string{"create_a_task", "case_management", "all_tasks"},
				"admin":          []string{"billing"},
			},
			"icon_url": iconURL,
		})
		s.NoError(err)
		req, _ := http.NewRequest(http.MethodPut, fmt.Sprintf("/_v/studio/organization/%d", orgID), bytes.NewBuffer(bs))
		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type updateOrgResp struct {
			Code int `json:"code" validate:"eq=0"`
		}

		var resp updateOrgResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.NoError(s.Struct(resp))
		s.Equal(0, resp.Code)
	}

	{ // invite user
		time.Sleep(1 * time.Second) // to make different created_at
		s.NoError(dbtest.CreateEmailLogs(rdb.Get(), []string{
			adminEmail,
		}, nil))

		w := httptest.NewRecorder()
		bs, err := json.Marshal(map[string]interface{}{
			"roles": []string{"owner"},
			"email": adminEmail,
			"name":  adminName,
		})
		s.NoError(err)
		req, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("/v1/studio/organization/%d/users", orgID), bytes.NewBuffer(bs))
		req.Header.Set("KG-STUDIO-TOKEN-V2", ownerStudioToken)
		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type createUserResp struct {
			Code int `json:"code" validate:"eq=0"`
		}

		var resp createUserResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.NoError(s.Struct(resp))
		s.Equal(0, resp.Code)
	}

	{ // owner can get studio user, and resp contains owner and admin, admin is not active
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("/v1/studio/organization/%d/users", orgID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", ownerStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type listUserResp struct {
			Code int `json:"code" validate:"eq=0"`
			Data []struct {
				UID    string `json:"uid" validate:"required"`
				Email  string `json:"email" validate:"required"`
				Status string `json:"status" validate:"required"`
			} `json:"data" validate:"required"`
		}

		var listUserResponse listUserResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &listUserResponse))
		s.NoError(s.Struct(listUserResponse))
		s.Len(listUserResponse.Data, 2)
		s.Equal(adminEmail, listUserResponse.Data[0].Email)
		s.Equal("pending", listUserResponse.Data[0].Status)
		s.Equal(ownerUID, listUserResponse.Data[1].UID)
		s.Equal(ownerEmail, listUserResponse.Data[1].Email)
		s.Equal("active", listUserResponse.Data[1].Status)
		adminUID = listUserResponse.Data[0].UID
	}

	{ // user accept invitation, we don't care about how does user login
		loginProvider := authservice.NewLoginProvider(&authservice.LoginReq{
			Email:            adminEmail,
			VerificationCode: "123456",
		})

		_, _, kgToken, _, kgErr := loginProvider.IssueTokens(context.Background())
		s.Nil(kgErr)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("/v1/studio/organization/%d/accept_invitation", orgID), nil)
		req.Header.Set("KG-TOKEN", kgToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type acceptInvitationResp struct {
			Code int `json:"code" validate:""`
			Data struct {
				StudioToken string `json:"studio_token" validate:"required"`
			} `json:"data" validate:"required"`
		}

		var acceptInvitationResponse acceptInvitationResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &acceptInvitationResponse))
		s.NoError(s.Struct(acceptInvitationResponse))
		s.Equal(0, acceptInvitationResponse.Code)
		s.NotEmpty(acceptInvitationResponse.Data.StudioToken)

		adminStudioToken = acceptInvitationResponse.Data.StudioToken
	}

	{ // owner can get studio user, and resp contains owner and admin, both are active
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("/v1/studio/organization/%d/users", orgID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", ownerStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type listUserResp struct {
			Code int `json:"code" validate:"eq=0"`
			Data []struct {
				UID    string `json:"uid" validate:"required"`
				Email  string `json:"email" validate:"required"`
				Status string `json:"status" validate:"required"`
			} `json:"data" validate:"required"`
		}

		var listUserResponse listUserResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &listUserResponse))
		s.NoError(s.Struct(listUserResponse))
		s.Len(listUserResponse.Data, 2)
		s.Equal(adminUID, listUserResponse.Data[0].UID)
		s.Equal(adminEmail, listUserResponse.Data[0].Email)
		s.Equal("active", listUserResponse.Data[0].Status)
		s.Equal(ownerUID, listUserResponse.Data[1].UID)
		s.Equal(ownerEmail, listUserResponse.Data[1].Email)
		s.Equal("active", listUserResponse.Data[1].Status)
	}

	{ // admin can get studio user, and resp contains owner and admin, admin is not active
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("/v1/studio/organization/%d/users", orgID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", adminStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type listUserResp struct {
			Code int `json:"code" validate:"eq=0"`
			Data []struct {
				UID    string `json:"uid" validate:"required"`
				Email  string `json:"email" validate:"required"`
				Status string `json:"status" validate:"required"`
			} `json:"data" validate:"required"`
		}

		var listUserResponse listUserResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &listUserResponse))
		s.NoError(s.Struct(listUserResponse))
		s.Len(listUserResponse.Data, 2)
		s.Equal(adminUID, listUserResponse.Data[0].UID)
		s.Equal(adminEmail, listUserResponse.Data[0].Email)
		s.Equal("active", listUserResponse.Data[0].Status)
		s.Equal(ownerUID, listUserResponse.Data[1].UID)
		s.Equal(ownerEmail, listUserResponse.Data[1].Email)
		s.Equal("active", listUserResponse.Data[1].Status)
	}

	{ // admin can get org info
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("/v1/studio/organization/%d/info", orgID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", adminStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type orgInfoResp struct {
			Code int `json:"code" validate:"eq=0"`
			Data struct {
				ID      int32  `json:"id" validate:"required"`
				Name    string `json:"name" validate:"required"`
				Modules struct {
					User360       []string `json:"user_360" validate:"required"`
					WalletBuilder []string `json:"wallet_builder" validate:"required"`
					AssetPro      []string `json:"asset_pro" validate:"required"`
					NFTBoost      []string `json:"nft_boost" validate:"required"`
					Compliance    []string `json:"compliance" validate:"required"`
					Admin         []string `json:"admin" validate:"required"`
				} `json:"modules" validate:"required"`
				Owners    []string `json:"owners" validate:"required"`
				CreatedAt int64    `json:"created_at" validate:"required"`
				IconURL   *string  `json:"icon_url"`
			} `json:"data" validate:"required"`
		}

		var orgInfoResponse orgInfoResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &orgInfoResponse))
		s.NoError(s.Struct(orgInfoResponse))
		s.Equal(0, orgInfoResponse.Code)
		s.Equal(orgID, orgInfoResponse.Data.ID)
		s.Equal(orgName, orgInfoResponse.Data.Name)
		s.Len(orgInfoResponse.Data.Owners, 2)
		s.Contains(orgInfoResponse.Data.Owners, ownerName)
		s.Contains(orgInfoResponse.Data.Owners, adminName)
		{ // modules contains all modules
			s.Len(orgInfoResponse.Data.Modules.AssetPro, 3)
			s.Contains(orgInfoResponse.Data.Modules.AssetPro, "treasury")
			s.Contains(orgInfoResponse.Data.Modules.AssetPro, "send_token")
			s.Contains(orgInfoResponse.Data.Modules.AssetPro, "transaction_history")

			s.Len(orgInfoResponse.Data.Modules.NFTBoost, 2)
			s.Contains(orgInfoResponse.Data.Modules.NFTBoost, "campaign")
			s.Contains(orgInfoResponse.Data.Modules.NFTBoost, "reward")

			s.Len(orgInfoResponse.Data.Modules.User360, 3)
			s.Contains(orgInfoResponse.Data.Modules.User360, "data")
			s.Contains(orgInfoResponse.Data.Modules.User360, "engage")
			s.Contains(orgInfoResponse.Data.Modules.User360, "audience")

			s.Len(orgInfoResponse.Data.Modules.WalletBuilder, 4)
			s.Contains(orgInfoResponse.Data.Modules.WalletBuilder, "project")
			s.Contains(orgInfoResponse.Data.Modules.WalletBuilder, "configuration")
			s.Contains(orgInfoResponse.Data.Modules.WalletBuilder, "app_publish")
			s.Contains(orgInfoResponse.Data.Modules.WalletBuilder, "marketing_tools")

			s.Len(orgInfoResponse.Data.Modules.Compliance, 3)
			s.Contains(orgInfoResponse.Data.Modules.Compliance, "create_a_task")
			s.Contains(orgInfoResponse.Data.Modules.Compliance, "case_management")
			s.Contains(orgInfoResponse.Data.Modules.Compliance, "all_tasks")

			s.Len(orgInfoResponse.Data.Modules.Admin, 1)
			s.Contains(orgInfoResponse.Data.Modules.Admin, "billing")
		}
		s.NotZero(orgInfoResponse.Data.CreatedAt)
		s.Equal(iconURL, *orgInfoResponse.Data.IconURL)
	}

	{ // admin can get studio me
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("/v1/studio/organization/%d/me", orgID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", adminStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type studioMeResp struct {
			Code int `json:"code" validate:"eq=0"`
			Data struct {
				UID      string `json:"uid" validate:"required"`
				Name     string `json:"name" validate:"required"`
				MemberID string `json:"member_id"`
				Email    string `json:"email" validate:"required"`
				Roles    struct {
					User360       []string `json:"user_360"`
					WalletBuilder []string `json:"wallet_builder"`
					AssetPro      []string `json:"asset_pro"`
					NFTBoost      []string `json:"nft_boost"`
					Compliance    []string `json:"compliance"`
					Admin         []string `json:"admin" validate:"required"`
				} `json:"roles"`
				Status  string `json:"status"`
				Phone   string `json:"phone"`
				Modules struct {
					User360       []string `json:"user_360" validate:"required"`
					WalletBuilder []string `json:"wallet_builder" validate:"required"`
					AssetPro      []string `json:"asset_pro" validate:"required"`
					NFTBoost      []string `json:"nft_boost" validate:"required"`
					Compliance    []string `json:"compliance" validate:"required"`
					Admin         []string `json:"admin" validate:"required"`
				} `json:"modules" validate:"required"`
				Permissions []struct {
					Resource string `json:"resource"`
					Action   string `json:"action"`
				} `json:"permissions"`
				AssetPro struct {
					RemainLimit               float64  `json:"remain_limit"`
					DailyTransferLimit        *float64 `json:"daily_transfer_limit"`
					TransferApprovalThreshold *float64 `json:"transfer_approval_threshold"`
				} `json:"asset_pro"`
			} `json:"data" validate:"required"`
		}

		var studioMeResponse studioMeResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &studioMeResponse))
		s.NoError(s.Struct(studioMeResponse))
		s.Equal(0, studioMeResponse.Code)
		s.Equal(adminUID, studioMeResponse.Data.UID)
		s.Equal(adminName, studioMeResponse.Data.Name)
		s.Equal(adminEmail, studioMeResponse.Data.Email)
		s.Equal("active", studioMeResponse.Data.Status)
		s.Equal("owner", studioMeResponse.Data.Roles.Admin[0])
		{ // modules contains all modules
			s.Len(studioMeResponse.Data.Modules.AssetPro, 3)
			s.Contains(studioMeResponse.Data.Modules.AssetPro, "treasury")
			s.Contains(studioMeResponse.Data.Modules.AssetPro, "send_token")
			s.Contains(studioMeResponse.Data.Modules.AssetPro, "transaction_history")

			s.Len(studioMeResponse.Data.Modules.NFTBoost, 2)
			s.Contains(studioMeResponse.Data.Modules.NFTBoost, "campaign")
			s.Contains(studioMeResponse.Data.Modules.NFTBoost, "reward")

			s.Len(studioMeResponse.Data.Modules.User360, 3)
			s.Contains(studioMeResponse.Data.Modules.User360, "data")
			s.Contains(studioMeResponse.Data.Modules.User360, "engage")
			s.Contains(studioMeResponse.Data.Modules.User360, "audience")

			s.Len(studioMeResponse.Data.Modules.WalletBuilder, 4)
			s.Contains(studioMeResponse.Data.Modules.WalletBuilder, "project")
			s.Contains(studioMeResponse.Data.Modules.WalletBuilder, "configuration")
			s.Contains(studioMeResponse.Data.Modules.WalletBuilder, "app_publish")
			s.Contains(studioMeResponse.Data.Modules.WalletBuilder, "marketing_tools")

			s.Len(studioMeResponse.Data.Modules.Compliance, 3)
			s.Contains(studioMeResponse.Data.Modules.Compliance, "create_a_task")
			s.Contains(studioMeResponse.Data.Modules.Compliance, "case_management")
			s.Contains(studioMeResponse.Data.Modules.Compliance, "all_tasks")

			s.Len(studioMeResponse.Data.Modules.Admin, 1)
			s.Contains(studioMeResponse.Data.Modules.Admin, "billing")
		}
		s.NotEmpty(studioMeResponse.Data.Permissions)
	}

	{ // admin can apply transaction, because admin has permission
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", orgID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", adminStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type applyTransactionResp struct {
			Code int `json:"code" validate:"eq=0"`
		}

		var resp applyTransactionResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.NoError(s.Struct(resp))
		s.Equal(0, resp.Code)
	}

	{ // owner delete admin
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodDelete, fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, adminUID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", ownerStudioToken)
		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type deleteUserResp struct {
			Code int `json:"code" validate:"eq=0"`
		}

		var resp deleteUserResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.NoError(s.Struct(resp))
		s.Equal(0, resp.Code)
	}

	{ // owner can get studio user, and resp contains owner, admin is deleted
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("/v1/studio/organization/%d/users", orgID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", ownerStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type listUserResp struct {
			Code int `json:"code" validate:"eq=0"`
			Data []struct {
				UID    string `json:"uid" validate:"required"`
				Email  string `json:"email" validate:"required"`
				Status string `json:"status" validate:"required"`
			} `json:"data" validate:"required"`
		}

		var listUserResponse listUserResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &listUserResponse))
		s.NoError(s.Struct(listUserResponse))
		s.Len(listUserResponse.Data, 2)
		s.Equal(adminUID, listUserResponse.Data[0].UID)
		s.Equal(adminEmail, listUserResponse.Data[0].Email)
		s.Equal("inactive", listUserResponse.Data[0].Status)
		s.Equal(ownerUID, listUserResponse.Data[1].UID)
		s.Equal(ownerEmail, listUserResponse.Data[1].Email)
		s.Equal("active", listUserResponse.Data[1].Status)
	}

	{ // admin can't call api, because admin is deleted
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodPost, fmt.Sprintf("/v1/studio/organization/%d/asset_pro/transfer", orgID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", adminStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusForbidden, w.Code)
	}

	{ // owner delete owner himself, but owner can't delete the only one owner in org {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodDelete, fmt.Sprintf("/v1/studio/organization/%d/users/%s", orgID, ownerUID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", ownerStudioToken)
		r.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		type deleteUserResp struct {
			Code int `json:"code" validate:"required"`
		}

		var resp deleteUserResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.NoError(s.Struct(resp))
		s.Equal(7021, resp.Code)
	}

	{ // admin can update org info, but error icon url
		w := httptest.NewRecorder()

		bs, err := json.Marshal(struct {
			Name    string `json:"name"`
			IconURL string `json:"icon_url"`
		}{
			Name:    "new name",
			IconURL: "data:image/png;base64,iVBORw...",
		})
		s.NoError(err)

		req, _ := http.NewRequest(http.MethodPut, fmt.Sprintf("/v1/studio/organization/%d/info", orgID), bytes.NewReader(bs))
		req.Header.Set("KG-STUDIO-TOKEN-V2", adminStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusBadRequest, w.Code)

		type resp struct {
			Code int `json:"code"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.NoError(s.Struct(response))
		s.Equal(code.ParamIncorrect, response.Code)
	}
	{ // admin can update org info
		w := httptest.NewRecorder()

		bs, err := json.Marshal(struct {
			Name string `json:"name"`
		}{
			Name: "new name",
		})
		s.NoError(err)

		req, _ := http.NewRequest(http.MethodPut, fmt.Sprintf("/v1/studio/organization/%d/info", orgID), bytes.NewReader(bs))
		req.Header.Set("KG-STUDIO-TOKEN-V2", adminStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type resp struct {
			Code int `json:"code" validate:"eq=0"`
		}

		var response resp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.NoError(s.Struct(response))
		s.Equal(0, response.Code)
	}

	{ // admin can get org info again
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, fmt.Sprintf("/v1/studio/organization/%d/info", orgID), nil)
		req.Header.Set("KG-STUDIO-TOKEN-V2", adminStudioToken)

		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		type orgInfoResp struct {
			Code int `json:"code" validate:"eq=0"`
			Data struct {
				ID      int32  `json:"id" validate:"required"`
				Name    string `json:"name" validate:"required"`
				Modules struct {
					User360       []string `json:"user_360" validate:"required"`
					WalletBuilder []string `json:"wallet_builder" validate:"required"`
					AssetPro      []string `json:"asset_pro" validate:"required"`
					NFTBoost      []string `json:"nft_boost" validate:"required"`
					Compliance    []string `json:"compliance" validate:"required"`
					Admin         []string `json:"admin" validate:"required"`
				} `json:"modules" validate:"required"`
				Owners    []string `json:"owners" validate:"required"`
				CreatedAt int64    `json:"created_at" validate:"required"`
				IconURL   *string  `json:"icon_url"`
			} `json:"data" validate:"required"`
		}

		var orgInfoResponse orgInfoResp
		s.NoError(json.Unmarshal(w.Body.Bytes(), &orgInfoResponse))
		s.NoError(s.Struct(orgInfoResponse))
		s.Equal(0, orgInfoResponse.Code)
		s.Equal(orgID, orgInfoResponse.Data.ID)
		s.Equal("new name", orgInfoResponse.Data.Name)
		s.Len(orgInfoResponse.Data.Owners, 1)
		s.Contains(orgInfoResponse.Data.Owners, ownerName)
		{ // modules contains all modules
			s.Len(orgInfoResponse.Data.Modules.AssetPro, 3)
			s.Contains(orgInfoResponse.Data.Modules.AssetPro, "treasury")
			s.Contains(orgInfoResponse.Data.Modules.AssetPro, "send_token")
			s.Contains(orgInfoResponse.Data.Modules.AssetPro, "transaction_history")

			s.Len(orgInfoResponse.Data.Modules.NFTBoost, 2)
			s.Contains(orgInfoResponse.Data.Modules.NFTBoost, "campaign")
			s.Contains(orgInfoResponse.Data.Modules.NFTBoost, "reward")

			s.Len(orgInfoResponse.Data.Modules.User360, 3)
			s.Contains(orgInfoResponse.Data.Modules.User360, "data")
			s.Contains(orgInfoResponse.Data.Modules.User360, "engage")
			s.Contains(orgInfoResponse.Data.Modules.User360, "audience")

			s.Len(orgInfoResponse.Data.Modules.WalletBuilder, 4)
			s.Contains(orgInfoResponse.Data.Modules.WalletBuilder, "project")
			s.Contains(orgInfoResponse.Data.Modules.WalletBuilder, "configuration")
			s.Contains(orgInfoResponse.Data.Modules.WalletBuilder, "app_publish")
			s.Contains(orgInfoResponse.Data.Modules.WalletBuilder, "marketing_tools")

			s.Len(orgInfoResponse.Data.Modules.Compliance, 3)
			s.Contains(orgInfoResponse.Data.Modules.Compliance, "create_a_task")
			s.Contains(orgInfoResponse.Data.Modules.Compliance, "case_management")
			s.Contains(orgInfoResponse.Data.Modules.Compliance, "all_tasks")

			s.Len(orgInfoResponse.Data.Modules.Admin, 1)
			s.Contains(orgInfoResponse.Data.Modules.Admin, "billing")
		}
		s.NotZero(orgInfoResponse.Data.CreatedAt)
		s.Nil(orgInfoResponse.Data.IconURL)
	}
}

func TestIntegration(t *testing.T) {
	suite.Run(t, new(testIntegrationSuite))
}
