package linebot

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// GetExchangeRates get exchange rates of an organization
func GetExchangeRates(c *gin.Context) {
	orgID := c.GetInt("org_id")

	rates, kgError := organization.GetExchangeRates(c.Request.Context(), orgID)
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	response.OK(c, rates)
}

// UpsertExchangeRateReq defines the parameters for upserting an exchange rate
type UpsertExchangeRateReq struct {
	Base        string   `json:"base" binding:"required"`
	Quote       string   `json:"quote" binding:"required"`
	ImageURL    *string  `json:"image_url"`
	Description *string  `json:"description"`
	BuyPrice    *float64 `json:"buy_price"`
	SellPrice   *float64 `json:"sell_price"`
}

// UpsertExchangeRate upserts an exchange rate
func UpsertExchangeRate(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")

	var req UpsertExchangeRateReq
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	params := &organization.UpsertExchangeRateParams{
		Base:        req.Base,
		Quote:       req.Quote,
		ImageURL:    req.ImageURL,
		Description: req.Description,
		BuyPrice:    req.BuyPrice,
		SellPrice:   req.SellPrice,
	}
	created, kgError := organization.UpsertExchangeRate(ctx.Request.Context(), orgID, params)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	if created {
		response.Created(ctx, nil)
		return
	}
	response.OK(ctx, nil)
}

// DeleteExchangeRate deletes an exchange rate
func DeleteExchangeRate(c *gin.Context) {
	orgID := c.GetInt("org_id")
	rateID := c.Param("exchange_rate_id")

	kgError := organization.DeleteExchangeRate(c.Request.Context(), orgID, rateID)
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	response.OK(c, nil)
}
