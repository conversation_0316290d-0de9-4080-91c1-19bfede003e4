package linebot

import (
	"crypto/rand"
	"encoding/base64"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	linebotservice "github.com/kryptogo/kg-wallet-backend/pkg/service/linebot"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/line/line-bot-sdk-go/v7/linebot"
)

// generateNonce creates a unique nonce
func generateNonce() (string, error) {
	// Create a byte slice of a specific length for the random nonce
	nonceBytes := make([]byte, 16) // 16 bytes will generate a 24-character nonce when base64 encoded

	// Use the seed to generate a cryptographic random byte slice
	_, err := rand.Read(nonceBytes)
	if err != nil {
		return "", err
	}

	// Encode the byte slice to a base64 string
	nonce := base64.URLEncoding.EncodeToString(nonceBytes)

	// Return the nonce
	return nonce, nil
}

// LineAccountLinkRedirect redirects user to LINE account link page (after user logs in and gets oauth access token)
func LineAccountLinkRedirect(c *gin.Context) {
	oauthAccessToken := c.Query("access_token")
	linkToken := c.Query("link_token")
	if oauthAccessToken == "" || linkToken == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "oauth_access_token or link_token is empty")
		return
	}

	userID, _, _, err := oauth.ValidateOAuthAccessToken(c.Request.Context(), "Bearer "+oauthAccessToken, []string{})
	if err != nil {
		response.ErrorWithMsg(c, http.StatusUnauthorized, code.OAuthTokenInvalid, err.Error(), nil)
		return
	}

	nonce, err := generateNonce()
	if err != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "generateNonce failed", map[string]interface{}{
			"error": err.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, err.Error())
		return
	}
	stateKey := cache.ComposeLINEOAAccountNonceCacheKey(nonce)
	cache.Set(c.Request.Context(), stateKey, userID, time.Minute*30)

	// Redirect user to LINE account link page
	c.Redirect(http.StatusFound, linebotservice.LineAccessLinkEndpoint+"?linkToken="+linkToken+"&nonce="+nonce)
}

// ConfigResponse is the response for get line bot config
type ConfigResponse struct {
	OrganizationID     int     `json:"organization_id"`
	ChannelID          *string `json:"channel_id"`
	ChannelSecret      *string `json:"channel_secret"`
	ChannelAccessToken *string `json:"channel_access_token"`
	Enabled            *bool   `json:"enabled"`
	Status             *string `json:"status"`
	CallbackURL        string  `json:"callback_url"`
}

// GetLinebotConfig get studio organization's line bot config
func GetLinebotConfig(c *gin.Context) {
	orgID := c.GetInt("org_id")

	cfg, kgError := organization.GetOrgLinebotConfig(c.Request.Context(), orgID)
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	res := ConfigResponse{
		OrganizationID:     orgID,
		ChannelID:          cfg.ChannelID,
		ChannelSecret:      cfg.ChannelSecret,
		ChannelAccessToken: cfg.ChannelAccessToken,
		Enabled:            cfg.Enabled,
		Status:             cfg.Status,
	}
	// Obfuscate by only keeping the first 4 characters and the last 4 characters
	if res.ChannelSecret != nil {
		*res.ChannelSecret = (*res.ChannelSecret)[:4] + "********" + (*res.ChannelSecret)[len(*res.ChannelSecret)-4:]
	}
	if res.ChannelAccessToken != nil {
		*res.ChannelAccessToken = (*res.ChannelAccessToken)[:4] + "********" + (*res.ChannelAccessToken)[len(*res.ChannelAccessToken)-4:]
	}
	if res.ChannelID != nil && res.Enabled != nil && *res.Enabled {
		// If line bot is enabled, return callback url
		urlPrefix := config.GetString("LINE_BOT_CALLBACK_URL")
		res.CallbackURL = urlPrefix + "/" + *res.ChannelID
	}

	response.OK(c, res)
}

// UpsertConfigRequest is the request for upsert line bot config
type UpsertConfigRequest struct {
	ChannelID          *string `json:"channel_id"`
	ChannelSecret      *string `json:"channel_secret"`
	ChannelAccessToken *string `json:"channel_access_token"`
	Enabled            *bool   `json:"enabled"`
	Status             *string `json:"status"`
}

func upsertLinebotConfig(c *gin.Context, orgID int, req UpsertConfigRequest, isAdmin bool) {
	params := organization.UpsertStudioOrgLinebotConfigParams{
		OrgID:              orgID,
		ChannelID:          req.ChannelID,
		ChannelSecret:      req.ChannelSecret,
		ChannelAccessToken: req.ChannelAccessToken,
		Status:             req.Status,
	}
	if isAdmin {
		params.Enabled = req.Enabled
	}
	isNewConfig, kgError := organization.UpsertOrgLinebotConfig(c.Request.Context(), &params)

	if kgError != nil {
		response.KGError(c, kgError)
		return
	}

	if isNewConfig {
		response.Created(c, nil)
		return
	}
	response.OK(c, nil)
}

// UpsertLinebotConfigAdmin upsert studio organization's line bot config with admin privilege
func UpsertLinebotConfigAdmin(ctx *gin.Context) {
	var req UpsertConfigRequest
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID, err := strconv.Atoi(ctx.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "orgID is invalid")
		return
	}
	upsertLinebotConfig(ctx, orgID, req, true)
}

// UpsertLinebotConfig upsert studio organization's line bot config
func UpsertLinebotConfig(ctx *gin.Context) {
	var req UpsertConfigRequest
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	orgID := ctx.GetInt("org_id")
	upsertLinebotConfig(ctx, orgID, req, false)
}

func notLinkedReply(c *gin.Context, bot *linebot.Client, event *linebot.Event) {
	if _, err := bot.ReplyMessage(
		event.ReplyToken,
		linebot.NewTextMessage("目前尚未登入，請先按下面的按鈕登入後取得KYC資訊!").
			WithQuickReplies(linebot.NewQuickReplyItems(
				linebot.NewQuickReplyButton(
					"",
					linebot.NewMessageAction("登入", linebotservice.AccountLinkMessage)),
			)),
	).Do(); err != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "ReplyMessage failed", map[string]interface{}{
			"error": err.Error(),
		})
		response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotReplyMessageFailure, err.Error(), nil)
		return
	}
}

func getLinkedCustomer(c *gin.Context, channelID, lineUID string) (*domain.Customer, *code.KGError) {
	kgCustomer, kgError := customer.GetCustomerByLineUID(c.Request.Context(), channelID, lineUID)
	if kgError != nil {
		// Wallet user might delete his own account in wallet, so we'll get a not found error here.
		// When that happens, we automatically unlink the line user here.
		if kgError.Code == code.CustomerNotFound {
			kglog.InfoWithDataCtx(c.Request.Context(), "GetCustomerByLineUID Failed. Customer not found.", map[string]interface{}{
				"channelID": channelID,
				"lineUID":   lineUID,
			})
			err := customer.UnlinkLineUser(c, channelID, lineUID)
			if err != nil {
				kglog.ErrorWithDataCtx(c.Request.Context(), "UnlinkLineUser failed for not found customer.", map[string]interface{}{
					"err": err.String(),
				})
			}
			return nil, kgError
		} else if kgError.Code != code.StudioLineLinkedUserNotFound {
			return nil, kgError
		}
	}
	return kgCustomer, nil
}

// Callback is the callback for line bot messages and events
//
// Note that we add many logging here to help us debug line bot issues,
// since we won't directly see the response from our server to line bot.
// cuz line bot doesn't care about our response, so we sometimes will respond empty response.
// FIXME: refactor this function to make it more readable, and make coding style consistent.
func Callback(c *gin.Context) {
	channelID := c.Param("channel_id")
	linebotConfig, kgError := organization.GetLinebotConfigByChannelID(c, channelID)
	if kgError != nil {
		response.KGError(c, kgError)
		return
	}
	bot, kgError := linebotservice.GetLinebot(c, channelID)
	if kgError != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "GetLinebot failed", map[string]interface{}{
			"err": kgError,
		})
		response.KGError(c, kgError)
		return
	}
	events, err := bot.ParseRequest(c.Request)

	if err != nil {
		if err == linebot.ErrInvalidSignature {
			kglog.DebugWithDataCtx(c.Request.Context(), "Invalid message signature", map[string]interface{}{
				"error": err.Error(),
			})
			response.ErrorWithMsg(c, http.StatusBadRequest, code.StudioLinebotInvalidSignature, err.Error(), nil)
		} else {
			kglog.ErrorWithDataCtx(c.Request.Context(), "Parse request failure", map[string]interface{}{
				"error": err.Error(),
			})
			response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotParseRequestFailure, err.Error(), nil)
		}
		return
	}

	for _, event := range events {
		var lineUID string
		if event.Source != nil {
			lineUID = event.Source.UserID
		}
		if config.IsLocal() {
			kglog.InfoWithDataCtx(c.Request.Context(), "LinebotCallback", map[string]interface{}{
				"event": event,
			})
		}
		kgCustomer, kgError := getLinkedCustomer(c, channelID, lineUID)
		if kgError != nil {
			if kgError.Code == code.CustomerNotFound {
				if _, err = bot.ReplyMessage(
					event.ReplyToken,
					linebot.NewTextMessage("原綁定錢包已失效，已為您解綁，請重新登入！")).Do(); err != nil {
					kglog.ErrorWithDataCtx(c.Request.Context(), "Unlink line user replyMessage failed", map[string]interface{}{
						"error": err.Error(),
					})
					response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotReplyMessageFailure, err.Error(), nil)
					return
				}
			} else {
				kglog.ErrorWithDataCtx(c.Request.Context(), "GetCustomerByLineUID Failed", map[string]interface{}{
					"event":   event,
					"kgError": kgError,
				})
				response.KGError(c, kgError)
				return
			}
		}

		if event.Type == linebot.EventTypePostback {
			switch {
			case strings.HasPrefix(event.Postback.Data, linebotservice.RateCheckPostbackPrefix):
				kgError = linebotservice.RateCalculatePostbackHandler(c, bot, event)
				if kgError != nil {
					kglog.ErrorWithDataCtx(c.Request.Context(), "RateCalculatePostbackHandler failed", map[string]interface{}{
						"err": kgError,
					})
					response.KGError(c, kgError)
					return
				}
			case strings.HasPrefix(event.Postback.Data, linebotservice.CreateOrderPostbackPrefix):
				kgError = linebotservice.CreateOrderPostbackHandler(c, bot, event, kgCustomer)
				if kgError != nil {
					kglog.ErrorWithDataCtx(c.Request.Context(), "CreateOrderPostbackHandler failed", map[string]interface{}{
						"err": kgError,
					})
					response.KGError(c, kgError)
					return
				}
			case strings.HasPrefix(event.Postback.Data, linebotservice.ConfirmOrderPostbackPrefix):
				kgError = linebotservice.ConfirmOrderPostbackHandler(c, bot, event, kgCustomer)
				if kgError != nil {
					kglog.ErrorWithDataCtx(c.Request.Context(), "ConfirmOrderPostbackHandler failed", map[string]interface{}{
						"err": kgError,
					})
					response.KGError(c, kgError)
					return
				}
			case strings.HasPrefix(event.Postback.Data, linebotservice.CancelOrderPostbackPrefix):
				if _, err = bot.ReplyMessage(
					event.ReplyToken,
					linebot.NewTextMessage("訂單已取消")).Do(); err != nil {
					kglog.ErrorWithDataCtx(c.Request.Context(), "CancelOrderPostbackPrefix ReplyMessage failed", map[string]interface{}{
						"error": err.Error(),
					})
					response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotReplyMessageFailure, err.Error(), nil)
					return
				}
			}
		} else if event.Type == linebot.EventTypeMessage {
			switch message := event.Message.(type) {
			case *linebot.TextMessage:
				switch {
				case strings.EqualFold(message.Text, linebotservice.AccountUnlinkMessage):
					if kgCustomer == nil {
						notLinkedReply(c, bot, event)
						return
					}
					kgError := customer.UnlinkLineUser(c.Request.Context(), channelID, lineUID)
					if kgError != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "UnlinkLineUser failed", map[string]interface{}{
							"err": kgError,
						})
						response.KGError(c, kgError)
						return
					}
					if _, err = bot.ReplyMessage(
						event.ReplyToken,
						linebot.NewTextMessage("成功登出!")).Do(); err != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "AccountUnlinkMessage ReplyMessage failed", map[string]interface{}{
							"error": err.Error(),
						})
						response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotReplyMessageFailure, err.Error(), nil)
					}
					return
				case strings.EqualFold(message.Text, linebotservice.AccountLinkMessage):
					// If already linked
					if kgCustomer != nil {
						if _, err = bot.ReplyMessage(
							event.ReplyToken,
							linebot.NewTextMessage("Hi "+kgCustomer.DisplayName+". 您已成功登入。")).Do(); err != nil {
							kglog.ErrorWithDataCtx(c.Request.Context(), "AccountLinkMessage ReplyMessage failed", map[string]interface{}{
								"error": err.Error(),
							})
							response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotReplyMessageFailure, err.Error(), nil)
						}
						return
					}
					// If not already linked
					cfg, kgError := organization.GetOAuthConfigByLineChannelID(c, channelID)
					if kgError != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "GetOAuthConfigByLineChannelID failed", map[string]interface{}{
							"err": kgError,
						})
						response.KGError(c, kgError)
						return
					}
					err := linebotservice.AccountLinkInit(bot, event, cfg.ID, lineUID)
					if err != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "AccountLinkInit failed", map[string]interface{}{
							"error": err.Error(),
						})
						response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotAccountLinkInitFailure, err.Error(), nil)
						return
					}
					return
				case strings.EqualFold(message.Text, linebotservice.CheckRateMessage):
					kgError = linebotservice.ListRateMessageHandler(c, bot, event, linebotConfig.OrganizationID, linebotservice.RateCheckTypeAll)
					if kgError != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "CheckRateMessageHandler failed", map[string]interface{}{
							"err": kgError,
						})
						response.KGError(c, kgError)
						return
					}
					return
				case strings.EqualFold(message.Text, linebotservice.CheckBuyRateMessage):
					kgError = linebotservice.ListRateMessageHandler(c, bot, event, linebotConfig.OrganizationID, linebotservice.RateCheckTypeBuy)
					if kgError != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "CheckRateMessageHandler failed", map[string]interface{}{
							"err": kgError,
						})
						response.KGError(c, kgError)
						return
					}
					return
				case strings.EqualFold(message.Text, linebotservice.CheckSellRateMessage):
					kgError = linebotservice.ListRateMessageHandler(c, bot, event, linebotConfig.OrganizationID, linebotservice.RateCheckTypeSell)
					if kgError != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "CheckRateMessageHandler failed", map[string]interface{}{
							"err": kgError,
						})
						response.KGError(c, kgError)
						return
					}
					return
				case strings.EqualFold(message.Text, linebotservice.CheckHistoryMessage):
					if kgCustomer == nil {
						notLinkedReply(c, bot, event)
						return
					}
					// TODO(beans): Finish check history message
					if _, err = bot.ReplyMessage(
						event.ReplyToken,
						linebot.NewTextMessage("實作中><")).Do(); err != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "CheckHistoryMessage ReplyMessage failed", map[string]interface{}{
							"error": err.Error(),
						})
						response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotReplyMessageFailure, err.Error(), nil)
					}
					return
				case strings.EqualFold(message.Text, linebotservice.KYCMessage):
					if kgCustomer == nil {
						notLinkedReply(c, bot, event)
						return
					}
					message, kgError := linebotservice.GetKYCStatusResponse(c, bot, event, kgCustomer)
					if kgError != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "GetKYCStatusResponse failed", map[string]interface{}{
							"err": kgError,
						})
						response.KGError(c, kgError)
						return
					}
					if _, err := bot.ReplyMessage(
						event.ReplyToken,
						linebot.NewTextMessage(*message)).Do(); err != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "KYCMessage ReplyMessage failed", map[string]interface{}{
							"error": err.Error(),
						})
						response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotReplyMessageFailure, err.Error(), nil)
						return
					}
				case strings.HasPrefix(message.Text, linebotservice.CreateOrderMessage):
					if kgCustomer == nil {
						notLinkedReply(c, bot, event)
						return
					}
					if kgCustomer.KycStatus != domain.KycStatusVerified {
						message, kgError := linebotservice.GetKYCStatusResponse(c, bot, event, kgCustomer)
						if kgError != nil {
							kglog.ErrorWithDataCtx(c.Request.Context(), "GetKYCStatusResponse failed", map[string]interface{}{
								"err": kgError,
							})
							response.KGError(c, kgError)
							return
						}
						if _, err := bot.ReplyMessage(
							event.ReplyToken,
							linebot.NewTextMessage("因KYC未通過，尚無法下單！"), linebot.NewTextMessage(*message)).Do(); err != nil {
							kglog.ErrorWithDataCtx(c.Request.Context(), "CreateOrderMessage ReplyMessage failed", map[string]interface{}{
								"error": err.Error(),
							})
							response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotReplyMessageFailure, err.Error(), nil)
						}
						return
					}
					kgError = linebotservice.CreateOrderMessageHandler(c, bot, event, kgCustomer)
					if kgError != nil {
						kglog.ErrorWithDataCtx(c.Request.Context(), "CreateOrderMessageHandler failed", map[string]interface{}{
							"err": kgError,
						})
						response.KGError(c, kgError)
						return
					}
				}
			}
		} else if event.Type == linebot.EventTypeAccountLink {
			//11. The LINE Platform sends an event (which includes the LINE user ID and nonce) via webhook to the bot server.
			if kgCustomer != nil {
				// already linked.
				if _, err = bot.ReplyMessage(
					event.ReplyToken,
					linebot.NewTextMessage("登入失敗，請聯繫客服處理。")).Do(); err != nil {
					kglog.ErrorWithDataCtx(c.Request.Context(), "EventTypeAccountLink ReplyMessage failed", map[string]interface{}{
						"error": err.Error(),
					})
					response.ErrorWithMsg(c, http.StatusInternalServerError, code.StudioLinebotReplyMessageFailure, err.Error(), nil)
				}
				return
			}
			kgError = linebotservice.AccountLinkEventHandler(c, bot, event, channelID, lineUID)
			if kgError != nil {
				kglog.ErrorWithDataCtx(c.Request.Context(), "AccountLinkEventHandler failed", map[string]interface{}{
					"err": kgError,
				})
				response.KGError(c, kgError)
				return
			}
		}
	}
}
