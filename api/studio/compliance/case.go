package compliance

import (
	"github.com/gin-gonic/gin"
	request "github.com/kryptogo/kg-wallet-backend/api/paging"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/compliance"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// getLatestCasesParams is the params for GetLatestCases
type getLatestCasesParams struct {
	SubmittedAtFrom *int                        `form:"submitted_at_from"`
	SubmittedAtTo   *int                        `form:"submitted_at_to"`
	RiskLabel       *domain.RiskLabel           `form:"risk_label"`
	Sanctioned      *bool                       `form:"sanctioned"`
	IdvStatus       *domain.IdvStatusSimplified `form:"idv_status"`
	KycStatus       *domain.KycStatus           `form:"kyc_status"`
	request.PagingRequest
}

type kycCaseDetail struct {
	OrganizationID      int                         `json:"organization_id"`
	UID                 string                      `json:"uid"`
	CaseID              int                         `json:"case_id"`
	Name                *string                     `json:"name"`
	Phone               *string                     `json:"phone"`
	Email               *string                     `json:"email"`
	Country             *string                     `json:"country"`
	NationalID          *string                     `json:"national_id"`
	Birthday            *string                     `json:"birthday"`
	LineID              *string                     `json:"line_id"`
	SubmittedAt         int64                       `json:"submitted_at"`
	UpdatedAt           int64                       `json:"updated_at"`
	IdvStatusSimplified *domain.IdvStatusSimplified `json:"idv_status"`
	RiskScore           *int                        `json:"risk_score"`
	RiskLabel           *domain.RiskLabel           `json:"risk_label"`
	KycStatus           *domain.KycStatus           `json:"kyc_status"`
	FormID              *int                        `json:"form_id"`
	CddID               *int                        `json:"cdd_id"`
	IdvID               *int                        `json:"idv_id"`
	SanctionMatched     *bool                       `json:"sanction_matched"`
	ReviewerName        *string                     `json:"reviewer_name"`
	ReviewedAt          *int64                      `json:"reviewed_at"`
	RejectReasons       *domain.RejectReasons       `json:"reject_reasons"`
	InternalNotes       *string                     `json:"internal_notes"`
}

func (k *kycCaseDetail) fromDomain(d *domain.KycCaseDetail) {
	k.OrganizationID = d.OrganizationID
	k.UID = d.UID
	k.CaseID = d.CaseID
	k.Name = d.Name
	k.Phone = d.Phone
	k.Email = d.Email
	k.Country = d.Country
	k.NationalID = d.NationalID
	k.Birthday = d.Birthday
	k.LineID = d.LineID
	k.SubmittedAt = d.SubmittedAt
	k.UpdatedAt = d.UpdatedAt
	k.IdvStatusSimplified = d.IdvStatusSimplified
	k.RiskScore = d.RiskScore
	k.RiskLabel = d.RiskLabel
	k.KycStatus = d.KycStatus
	k.FormID = d.FormID
	k.CddID = d.CddID
	k.IdvID = d.IdvID
	k.SanctionMatched = d.SanctionMatched
	k.ReviewerName = d.ReviewerName
	k.ReviewedAt = d.ReviewedAt
	k.RejectReasons = d.RejectReasons
	k.InternalNotes = d.InternalNotes
}

// GetLatestCaseByUsers returns a list of submissions for a case. Each case is the latest submission for a user.
func GetLatestCaseByUsers(ctx *gin.Context) {
	params := &getLatestCasesParams{
		PagingRequest: request.PagingRequest{
			PageNumber: 1,
			PageSize:   10,
			PageSort:   "submitted_at:d",
		},
	}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	query, kgError := params.ParseQuery([]string{"submitted_at", "updated_at", "kyc_status"})
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	orgID := ctx.GetInt("org_id")
	cases, paging, kgErr := compliance.GetLatestCaseByUsers(ctx.Request.Context(), orgID, &compliance.GetLatestCasesParams{
		SubmittedAtFrom: params.SubmittedAtFrom,
		SubmittedAtTo:   params.SubmittedAtTo,
		RiskLabel:       params.RiskLabel,
		Sanctioned:      params.Sanctioned,
		IdvStatus:       params.IdvStatus,
		KycStatus:       params.KycStatus,
	}, query)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	vCases := make([]*kycCaseDetail, len(cases))
	for i := range cases {
		vCases[i] = &kycCaseDetail{}
		c := cases[i]
		vCases[i].fromDomain(c)
	}

	response.OKWithPaging(ctx, vCases, response.Paging{
		PageNumber: paging.PageNumber,
		PageSize:   paging.PageSize,
		TotalCount: paging.TotalCount,
		PageSort:   string(paging.PageSort),
	})
}

type getCaseFiltersResponse struct {
	SubmittedAtFrom int `json:"submitted_at_from"`
	SubmittedAtTo   int `json:"submitted_at_to"`
}

// GetCaseFilters returns the filters
func GetCaseFilters(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")
	filters, kgErr := compliance.GetFilters(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, &getCaseFiltersResponse{
		SubmittedAtFrom: filters.SubmittedAtFrom,
		SubmittedAtTo:   filters.SubmittedAtTo,
	})
}

// GetPendingCaseCount returns the pending case count
func GetPendingCaseCount(ctx *gin.Context) {
	orgID := ctx.GetInt("org_id")
	count, kgErr := compliance.GetPendingCaseCount(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	response.OK(ctx, gin.H{
		"count": count,
	})
}
