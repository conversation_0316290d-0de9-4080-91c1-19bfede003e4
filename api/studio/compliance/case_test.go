package compliance

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/compliance"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type complyFlowTestSuite struct {
	suite.Suite
}

func (suite *complyFlowTestSuite) SetupTest() {
	rdb.Reset()
	assert.Nil(suite.T(), dbtest.CreateCustomersWithDefault(rdb.Get()))
	assert.Nil(suite.T(), dbtest.CreateCaseSubmissions(rdb.Get()))
	assert.Nil(suite.T(), dbtest.CreateKycAuditLog(rdb.Get()))
	auditorUID := "testuser2"
	assert.Nil(suite.T(), dbtest.CreateStudioUsers(rdb.Get(), auditorUID, nil))

	compliance.Init(rdb.GormRepo())

	organization.Init(organization.InitParam{
		StudioOrgRepo:  rdb.GormRepo(),
		StudioRoleRepo: rdb.GormRepo(),
	})
}

type casesResponse struct {
	Code int `json:"code"`
	Data []struct {
		CaseID          int       `json:"case_id"`
		UID             string    `json:"uid"`
		Name            *string   `json:"name"`
		Phone           *string   `json:"phone"`
		Email           *string   `json:"email"`
		Country         *string   `json:"country"`
		NationalID      *string   `json:"national_id"`
		Birthday        *string   `json:"birthday"`
		LineID          *string   `json:"line_id"`
		SubmittedAt     int64     `json:"submitted_at"`
		UpdatedAt       int64     `json:"updated_at"`
		IdvStatus       *string   `json:"idv_status"`
		RiskScore       *int      `json:"risk_score"`
		RiskLabel       *string   `json:"risk_label"`
		KycStatus       *string   `json:"kyc_status"`
		FormID          *int      `json:"form_id"`
		CddID           *int      `json:"cdd_id"`
		IdvID           *int      `json:"idv_id"`
		SanctionMatched *bool     `json:"sanction_matched"`
		ReviewerName    *string   `json:"reviewer_name"`
		ReviewedAt      *int64    `json:"reviewed_at"`
		RejectReasons   *[]string `json:"reject_reasons"`
		InternalNotes   *string   `json:"internal_notes"`
	} `json:"data"`
	Paging struct {
		PageNumber int    `json:"page_number"`
		PageSize   int    `json:"page_size"`
		TotalCount int    `json:"total_count"`
		PageSort   string `json:"page_sort"`
	} `json:"paging"`
}

type caseFiltersResponse struct {
	Code int `json:"code"`
	Data struct {
		SubmittedAtFrom int `json:"submitted_at_from"`
		SubmittedAtTo   int `json:"submitted_at_to"`
	} `json:"data"`
}

func (suite *complyFlowTestSuite) TestGetAll() {
	s := assert.New(suite.T())
	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/cases", auth.MockOrgID(orgID), GetLatestCaseByUsers)

	w := httptest.NewRecorder()
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/cases", orgID), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var resp casesResponse

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	s.Nil(err)
	s.Equal(0, resp.Code)
	s.Equal(3, resp.Paging.TotalCount)

	// order by submitted_at desc
	// 1st case
	s.Equal(6, resp.Data[0].CaseID)
	s.Equal("侯大大", *resp.Data[0].Name)
	s.Equal("user3", resp.Data[0].UID)
	s.Equal("333333333", *resp.Data[0].Phone)
	s.Equal("china", *resp.Data[0].Country)
	s.Equal("C333333333", *resp.Data[0].NationalID)
	s.Equal("line-3", *resp.Data[0].LineID)
	s.True(resp.Data[0].SubmittedAt > 0)
	s.True(resp.Data[0].UpdatedAt > 0)
	s.NotEmpty(*resp.Data[0].Birthday)
	s.Equal("failed", *resp.Data[0].IdvStatus)
	s.Equal(67, *resp.Data[0].RiskScore)
	s.Equal("high", *resp.Data[0].RiskLabel)
	s.Equal("rejected", *resp.Data[0].KycStatus)
	s.Equal(4, *resp.Data[0].FormID)
	s.Equal(5, *resp.Data[0].IdvID)
	s.Equal(6, *resp.Data[0].CddID)
	s.Equal(true, *resp.Data[0].SanctionMatched)
	s.Equal("internal notes 4", *resp.Data[0].InternalNotes)
	s.Equal([]string{"sanctioned", "high_risk_money_laundering"}, *resp.Data[0].RejectReasons)
	s.True(*resp.Data[0].ReviewedAt > 0)
	s.Equal("testuser2", *resp.Data[0].ReviewerName)

	// 2nd case
	s.Equal(7, resp.Data[1].CaseID)
	s.Equal("林美美", *resp.Data[1].Name)
	s.Equal("user2", resp.Data[1].UID)
	s.Equal("222222222", *resp.Data[1].Phone)
	s.Equal("taiwan", *resp.Data[1].Country)
	s.Equal("B222222222", *resp.Data[1].NationalID)
	s.Equal("line-2", *resp.Data[1].LineID)
	s.True(resp.Data[1].SubmittedAt > 0)
	s.True(resp.Data[1].UpdatedAt > 0)
	s.NotEmpty(*resp.Data[1].Birthday)
	s.Nil(resp.Data[1].IdvStatus)
	s.Nil(resp.Data[1].RiskScore)
	s.Nil(resp.Data[1].RiskLabel)
	s.Equal("pending", *resp.Data[1].KycStatus)
	s.Equal(7, *resp.Data[1].FormID)
	s.Nil(resp.Data[1].IdvID)
	s.Nil(resp.Data[1].CddID)
	s.Nil(resp.Data[1].SanctionMatched)
	// 3rd case
	s.Equal(2, resp.Data[2].CaseID)
	s.Equal("王小玲", *resp.Data[2].Name)
	s.Equal("user1", resp.Data[2].UID)
	s.Equal("123456789", *resp.Data[2].Phone)
	s.Equal("japan", *resp.Data[2].Country)
	s.Equal("A111111111", *resp.Data[2].NationalID)
	s.Equal("line-1", *resp.Data[2].LineID)
	s.True(resp.Data[2].SubmittedAt > 0)
	s.True(resp.Data[2].UpdatedAt > 0)
	s.NotEmpty(*resp.Data[2].Birthday)
	s.Equal("pass", *resp.Data[2].IdvStatus)
	s.Equal(32, *resp.Data[2].RiskScore)
	s.Equal("low", *resp.Data[2].RiskLabel)
	s.Equal("verified", *resp.Data[2].KycStatus)
	s.Equal(2, *resp.Data[2].FormID)
	s.Equal(2, *resp.Data[2].IdvID)
	s.Equal(2, *resp.Data[2].CddID)
	s.Equal(false, *resp.Data[2].SanctionMatched)
}

func (suite *complyFlowTestSuite) TestWithFuzzySearch() {
	s := assert.New(suite.T())
	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/cases", auth.MockOrgID(orgID), GetLatestCaseByUsers)

	queryList := []string{"q=侯", "q=333333333", "q=<EMAIL>", "q=C333333333", "q=line-3"}
	for _, query := range queryList {
		w := httptest.NewRecorder()

		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/cases?%s", orgID, query), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)
		var resp casesResponse
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		s.Nil(err)
		s.Equal(0, resp.Code)
		s.Equal(1, resp.Paging.TotalCount)
		s.Equal("user3", resp.Data[0].UID)
	}
}

func (suite *complyFlowTestSuite) TestQueryKycStatus() {
	s := assert.New(suite.T())
	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/cases", auth.MockOrgID(orgID), GetLatestCaseByUsers)

	w := httptest.NewRecorder()
	query := "kyc_status=verified"
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/cases?%s", orgID, query), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var resp casesResponse

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	s.Nil(err)
	s.Equal(0, resp.Code)
	s.Equal(1, resp.Paging.TotalCount)
	s.Equal("user1", resp.Data[0].UID)
}

func (suite *complyFlowTestSuite) TestQueryRiskLabel() {
	s := assert.New(suite.T())
	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/cases", auth.MockOrgID(orgID), GetLatestCaseByUsers)

	w := httptest.NewRecorder()
	query := "risk_label=high"
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/cases?%s", orgID, query), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var resp casesResponse

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	s.Nil(err)
	s.Equal(0, resp.Code)
	s.Equal(1, resp.Paging.TotalCount)
	s.Equal("user3", resp.Data[0].UID)
}

func (suite *complyFlowTestSuite) TestQuerySanctioned() {
	s := assert.New(suite.T())
	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/cases", auth.MockOrgID(orgID), GetLatestCaseByUsers)

	w := httptest.NewRecorder()
	query := "sanctioned=false"
	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/cases?%s", orgID, query), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var resp casesResponse

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	s.Nil(err)
	s.Equal(0, resp.Code)
	s.Equal(1, resp.Paging.TotalCount)
	s.Equal("user1", resp.Data[0].UID)
}

func (suite *complyFlowTestSuite) TestQueryIdvStatus() {
	s := assert.New(suite.T())
	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/cases", auth.MockOrgID(orgID), GetLatestCaseByUsers)
	{
		w := httptest.NewRecorder()
		query := "idv_status=pass"
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/cases?%s", orgID, query), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)
		var resp casesResponse

		err = json.Unmarshal(w.Body.Bytes(), &resp)
		s.Nil(err)
		s.Equal(0, resp.Code)
		s.Equal(1, resp.Paging.TotalCount)
		s.Equal("user1", resp.Data[0].UID)
	}
	{
		w := httptest.NewRecorder()
		query := "idv_status=failed"
		req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/cases?%s", orgID, query), nil)
		s.NoError(err)
		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)
		var resp casesResponse

		err = json.Unmarshal(w.Body.Bytes(), &resp)
		s.Nil(err)
		s.Equal(0, resp.Code)
		s.Equal(1, resp.Paging.TotalCount)
		s.Equal("user3", resp.Data[0].UID)

	}
}

func (suite *complyFlowTestSuite) TestQuerySubmittedAt() {
	s := assert.New(suite.T())
	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/comply_flow/cases", auth.MockOrgID(orgID), GetLatestCaseByUsers)

	w := httptest.NewRecorder()
	from := time.Now().Unix()
	to := time.Now().AddDate(0, 0, 2).Add(10 * time.Minute).Unix()
	query := fmt.Sprintf("submitted_at_from=%d&submitted_at_to=%d", from, to)

	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/comply_flow/cases?%s", orgID, query), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var resp casesResponse

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	s.Nil(err)
	s.Equal(0, resp.Code)
	s.Equal(1, resp.Paging.TotalCount)
	s.Equal("user1", resp.Data[0].UID)
}

func (suite *complyFlowTestSuite) TestCaseFilters() {
	s := assert.New(suite.T())
	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/comply_flow/cases/filter_options", auth.MockOrgID(orgID), GetCaseFilters)

	w := httptest.NewRecorder()

	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/comply_flow/cases/filter_options", orgID), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var resp caseFiltersResponse
	err = json.Unmarshal(w.Body.Bytes(), &resp)
	s.Nil(err)
	s.Equal(0, resp.Code)
	s.True(int(time.Now().AddDate(0, 0, 1).Add(10*time.Minute).Unix()) > resp.Data.SubmittedAtFrom)
	s.True(int(time.Now().Unix()) < resp.Data.SubmittedAtTo)
}

func (suite *complyFlowTestSuite) TestPendingCaseCount() {
	s := assert.New(suite.T())
	orgID := 1

	r := gin.Default()
	r.GET("/v1/studio/organization/:orgID/comply_flow/cases/pending_count", auth.MockOrgID(orgID), GetPendingCaseCount)

	w := httptest.NewRecorder()

	req, err := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/comply_flow/cases/pending_count", orgID), nil)
	s.NoError(err)
	r.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	type response struct {
		Code int `json:"code"`
		Data struct {
			Count int `json:"count"`
		} `json:"data"`
	}
	var resp response

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	s.Nil(err)
	s.Equal(0, resp.Code)
	s.Equal(1, resp.Data.Count)
}

func TestGetLatestCaseByUsers(t *testing.T) {
	suite.Run(t, new(complyFlowTestSuite))
}
