package compliance

import (
	"errors"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	complianceapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/compliance"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/compliance"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"

	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/pariz/gountries"
)

func parseCountryCodeToName(countryCode string) *string {
	country, err := gountries.New().FindCountryByAlpha(countryCode)
	if err != nil {
		return nil
	}

	return util.Ptr(strings.ToLower(country.Name.Common))
}

type getLatestCaseSubmissionByUserRequest struct {
	ComplianceOrgID int    `form:"org_id" required:"true"`
	KGUserID        string `form:"kg_user_id" required:"true"`
}

// GetKycStatusForCompliance returns the kyc status for compliance.
func GetKycStatusForCompliance(ctx *gin.Context) {
	req := getLatestCaseSubmissionByUserRequest{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	studioOrg, kgError := organization.GetStudioOrgRepo().
		GetOrganizationByComplianceOrgID(ctx.Request.Context(), req.ComplianceOrgID)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	kycStatus, kgError := customer.GetCustomerKycStatusFromDB(ctx.Request.Context(), req.KGUserID, studioOrg.ID)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	response.OK(ctx, gin.H{
		"kyc_status": kycStatus,
	})
}

type createCaseSubmissionRequest struct {
	ComplianceOrgID int    `json:"org_id" required:"true"`
	KGUserID        string `json:"kg_user_id" required:"true"`
	PII             struct {
		Country           *string `json:"country"`
		NationalID        *string `json:"national_id"`
		Birthday          *int64  `json:"birthday"`
		PassportNumber    *string `json:"passport_number"`
		Email             *string `json:"email"`
		Phone             *string `json:"phone"`
		LineID            *string `json:"line_id"`
		Gender            *string `json:"gender"`
		PhysicalAddress   *string `json:"physical_address"`
		LegalName         *string `json:"legal_name"`
		BankName          *string `json:"bank_name"`
		BranchName        *string `json:"branch_name"`
		AccountNumber     *string `json:"account_number"`
		AccountHolderName *string `json:"account_holder_name"`
	} `json:"pii" required:"true"`
	FormID int  `json:"form_id" required:"true"`
	IdvID  *int `json:"idv_id"`
	CddID  *int `json:"cdd_id"`
}

// CreateCaseSubmissionByCompliance will be called when compliance form be submitted.
// Compliance backend will call this API to create case submission.
func CreateCaseSubmissionByCompliance(ctx *gin.Context) {
	req := createCaseSubmissionRequest{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	studioOrg, kgError := organization.GetStudioOrgRepo().
		GetOrganizationByComplianceOrgID(ctx.Request.Context(), req.ComplianceOrgID)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	isCustomerExist, kgError := customer.GetCustomerRepo().DoesCustomerExist(ctx.Request.Context(), studioOrg.ID, req.KGUserID)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	if !isCustomerExist {
		response.KGError(ctx, code.NewKGError(code.CustomerNotFound, http.StatusNotFound, errors.New("Customer not found"), nil))
		return
	}

	kglog.DebugWithDataCtx(ctx.Request.Context(), "CaseSubmission", map[string]interface{}{
		"compliance_org_id": req.ComplianceOrgID,
		"org_id":            studioOrg.ID,
		"uid":               req.KGUserID,
		"form_id":           req.FormID,
		"idv_id":            req.IdvID,
		"cdd_id":            req.CddID,
	})

	if kgErr := compliance.CreateCaseSubmission(ctx.Request.Context(), domain.CreateCaseSubmissionParam{
		UID:            req.KGUserID,
		OrganizationID: studioOrg.ID,
		FormID:         &req.FormID,
		CddID:          req.CddID,
		IdvID:          req.IdvID,
	}); kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	var (
		country   *string
		birthday  *string
		idvStatus *domain.IdvStatus
		gender    *string
	)

	if req.IdvID != nil {
		idvStatus = util.Ptr(domain.IdvStatusPending)
	}

	if req.PII.Birthday != nil {
		t := time.Unix(*req.PII.Birthday, 0)
		birthday = util.Ptr(t.In(time.UTC).Format("2006-01-02"))
	}

	if req.PII.Gender != nil {
		gender = complianceapi.ParseGenderFromCompliance(*req.PII.Gender)
	}

	if req.PII.Country != nil {
		if country = parseCountryCodeToName(*req.PII.Country); country == nil {
			kglog.ErrorfCtx(ctx.Request.Context(), "country code %s not found", *req.PII.Country)
		}
	}

	if err := customer.GetCustomerRepo().UpdateCustomerOfStudioOrg(
		ctx.Request.Context(), domain.UpdateCustomerOfStudioOrgParams{
			UID:               req.KGUserID,
			OrganizationID:    studioOrg.ID,
			Country:           country,
			NationalID:        req.PII.NationalID,
			Birthday:          birthday,
			PassportNumber:    req.PII.PassportNumber,
			Email:             req.PII.Email,
			Phone:             req.PII.Phone,
			LineID:            req.PII.LineID,
			Gender:            gender,
			PhysicalAddress:   req.PII.PhysicalAddress,
			LegalName:         req.PII.LegalName,
			BankName:          req.PII.BankName,
			BranchName:        req.PII.BranchName,
			AccountNumber:     req.PII.AccountNumber,
			AccountHolderName: req.PII.AccountHolderName,
			KYCStatus:         util.Ptr(domain.KycStatusPending),
			IDVStatus:         idvStatus,
		}); err != nil {
		response.KGError(ctx, err)
		return
	}

	// TODO: update pii

	response.OK(ctx, nil)
}
