package compliance_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/studio/compliance"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer/repo"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	complianceSvc "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/compliance"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type formSubmissionRequest struct {
	ComplianceOrgID int                      `json:"org_id" required:"true"`
	KGUserID        string                   `json:"kg_user_id" required:"true"`
	PII             formSubmissionPIIRequest `json:"pii" required:"true"`
	FormID          int                      `json:"form_id" required:"true"`
	IdvID           *int                     `json:"idv_id"`
	CddID           *int                     `json:"cdd_id"`
}

type formSubmissionPIIRequest struct {
	Country         *string `json:"country"`
	NationalID      *string `json:"national_id"`
	Birthday        *int64  `json:"birthday"`
	PassportNumber  *string `json:"passport_number"`
	Email           *string `json:"email"`
	Phone           *string `json:"phone"`
	LineID          *string `json:"line_id"`
	Gender          *string `json:"gender"`
	PhysicalAddress *string `json:"physical_address"`
	LegalName       *string `json:"legal_name"`
}

type testCaseSubmissionByComplianceSuite struct {
	suite.Suite

	ginEngine *gin.Engine
}

func (s *testCaseSubmissionByComplianceSuite) SetupSuite() {
	customer.Init(repo.NewCustomerRepo())
	complianceSvc.Init(rdb.GormRepo())

	s.ginEngine = gin.Default()

	s.ginEngine.POST("/_v/studio/organization/case_submissions",
		compliance.CreateCaseSubmissionByCompliance)
}

func (s *testCaseSubmissionByComplianceSuite) SetupTest() {
	rdb.Reset()
	s.NoError(rdbtest.CreateStudioDefault(rdb.Get()))
}

func (s *testCaseSubmissionByComplianceSuite) TestFailedOrgNotFound() {
	birthday, _ := time.Parse("2006-01-02", "1997-01-01")

	bs, err := json.Marshal(formSubmissionRequest{
		ComplianceOrgID: 100000,
		KGUserID:        "55688",
		PII: formSubmissionPIIRequest{
			Country:         util.Ptr("TWN"),
			NationalID:      util.Ptr("A123456789"),
			Birthday:        util.Ptr(birthday.Unix()),
			PassportNumber:  util.Ptr("**********123"),
			Email:           util.Ptr("<EMAIL>"),
			Phone:           util.Ptr("**********"),
			LineID:          util.Ptr("lineid"),
			Gender:          util.Ptr("M"),
			PhysicalAddress: util.Ptr("台北市信義區桃豬街1號桃豬隱猿"),
			LegalName:       util.Ptr("legal_name"),
		},
		FormID: 1,
		IdvID:  util.Ptr(1),
		CddID:  util.Ptr(1),
	})
	s.NoError(err)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest(http.MethodPost, "/_v/studio/organization/case_submissions", bytes.NewBuffer(bs))
	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusNotFound, w.Code)

	type resp struct {
		Code int `json:"code"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(7009, response.Code)
}

func (s *testCaseSubmissionByComplianceSuite) TestFailedCustomerNotFound() {
	birthday, _ := time.ParseInLocation("2006-01-02", "1997-01-01", time.UTC)

	var count int64
	s.NoError(rdb.Get().Model(&model.Customer{}).Where("uid = ?", "55688").Count(&count).Error)
	s.Zero(count)

	bs, err := json.Marshal(formSubmissionRequest{
		ComplianceOrgID: 10,
		KGUserID:        "55688",
		PII: formSubmissionPIIRequest{
			Country:         util.Ptr("TWN"),
			NationalID:      util.Ptr("A123456789"),
			Birthday:        util.Ptr(birthday.Unix()),
			PassportNumber:  util.Ptr("**********123"),
			Email:           util.Ptr("<EMAIL>"),
			Phone:           util.Ptr("**********"),
			LineID:          util.Ptr("lineid"),
			Gender:          util.Ptr("M"),
			PhysicalAddress: util.Ptr("台北市信義區桃豬街1號桃豬隱猿"),
			LegalName:       util.Ptr("legal_name"),
		},
		FormID: 1,
		IdvID:  util.Ptr(1),
		CddID:  util.Ptr(1),
	})
	s.NoError(err)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest(http.MethodPost, "/_v/studio/organization/case_submissions", bytes.NewBuffer(bs))
	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusNotFound, w.Code)

	type resp struct {
		Code int `json:"code"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(7016, response.Code)
}

func (s *testCaseSubmissionByComplianceSuite) TestSuccessWithFullInfo() {
	birthday, _ := time.ParseInLocation("2006-01-02", "1997-01-01", time.UTC)

	var countOfCaseSubmission int64
	s.NoError(rdb.Get().Model(&model.CaseSubmission{}).Where("uid = ?", "testuser1").Count(&countOfCaseSubmission).Error)
	s.Zero(countOfCaseSubmission)

	var c model.Customer
	s.NoError(rdb.Get().Where("uid = ?", "testuser1").First(&c).Error)
	s.Equal("testuser1", c.UID)
	s.Nil(c.Email)
	s.Nil(c.Country)
	s.Nil(c.NationalID)
	s.Nil(c.Birthday)
	s.Nil(c.PassportNumber)
	s.Nil(c.Email)
	s.Nil(c.Phone)
	s.Nil(c.LineID)
	s.Nil(c.Gender)
	s.Nil(c.PhysicalAddress)
	s.Nil(c.LegalName)

	bs, err := json.Marshal(formSubmissionRequest{
		ComplianceOrgID: 10,
		KGUserID:        "testuser1",
		PII: formSubmissionPIIRequest{
			Country:         util.Ptr("TWN"),
			NationalID:      util.Ptr("A123456789"),
			Birthday:        util.Ptr(birthday.Unix()),
			PassportNumber:  util.Ptr("**********123"),
			Email:           util.Ptr("<EMAIL>"),
			Phone:           util.Ptr("**********"),
			LineID:          util.Ptr("lineid"),
			Gender:          util.Ptr("M"),
			PhysicalAddress: util.Ptr("台北市信義區桃豬街1號桃豬隱猿"),
			LegalName:       util.Ptr("legal_name"),
		},
		FormID: 1,
		IdvID:  util.Ptr(1),
		CddID:  util.Ptr(1),
	})
	s.NoError(err)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest(http.MethodPost, "/_v/studio/organization/case_submissions", bytes.NewBuffer(bs))
	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type resp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)
	s.Empty(response.Message)

	// assert case submission
	var actualCaseSubmission model.CaseSubmission
	s.NoError(rdb.Get().Where("uid = ?", "testuser1").First(&actualCaseSubmission).Error)
	s.Equal("testuser1", actualCaseSubmission.UID)
	s.Equal(1, actualCaseSubmission.OrganizationID)
	s.Equal(1, *actualCaseSubmission.FormID)
	s.Equal(1, *actualCaseSubmission.IdvID)
	s.Equal(1, *actualCaseSubmission.CddID)

	// assert customer
	var actualCustomer model.Customer
	s.NoError(rdb.Get().Where("id = ?", c.ID).First(&actualCustomer).Error)
	s.Equal("testuser1", actualCustomer.UID)
	s.Equal("taiwan", *actualCustomer.Country)
	s.Equal("A123456789", *actualCustomer.NationalID)
	s.Equal("1997-01-01", *actualCustomer.Birthday)
	s.Equal("**********123", *actualCustomer.PassportNumber)
	s.Equal("<EMAIL>", *actualCustomer.Email)
	s.Equal("**********", *actualCustomer.Phone)
	s.Equal("lineid", *actualCustomer.LineID)
	s.Equal("male", *actualCustomer.Gender)
	s.Equal(`台北市信義區桃豬街1號桃豬隱猿`, *actualCustomer.PhysicalAddress)
	s.Equal("legal_name", *actualCustomer.LegalName)
}

func (s *testCaseSubmissionByComplianceSuite) TestSuccessJustForm() {

	var countOfCaseSubmission int64
	s.NoError(rdb.Get().Model(&model.CaseSubmission{}).Where("uid = ?", "testuser1").Count(&countOfCaseSubmission).Error)
	s.Zero(countOfCaseSubmission)

	var c model.Customer
	s.NoError(rdb.Get().Where("uid = ?", "testuser1").First(&c).Error)
	s.Equal("testuser1", c.UID)
	s.Nil(c.Email)
	s.Nil(c.Country)
	s.Nil(c.NationalID)
	s.Nil(c.Birthday)
	s.Nil(c.PassportNumber)
	s.Nil(c.Email)
	s.Nil(c.Phone)
	s.Nil(c.LineID)
	s.Nil(c.Gender)
	s.Nil(c.PhysicalAddress)
	s.Nil(c.LegalName)

	bs, err := json.Marshal(formSubmissionRequest{
		ComplianceOrgID: 10,
		KGUserID:        "testuser1",
		PII: formSubmissionPIIRequest{
			Country:         util.Ptr("TWN"),
			NationalID:      util.Ptr("A123456789"),
			PhysicalAddress: util.Ptr("台北市信義區桃豬街1號桃豬隱猿"),
			LegalName:       util.Ptr("legal_name"),
		},
		FormID: 1,
	})
	s.NoError(err)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest(http.MethodPost, "/_v/studio/organization/case_submissions", bytes.NewBuffer(bs))
	s.ginEngine.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type resp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)
	s.Empty(response.Message)

	// assert case submission
	var actualCaseSubmission model.CaseSubmission
	s.NoError(rdb.Get().Where("uid = ?", "testuser1").First(&actualCaseSubmission).Error)
	s.Equal("testuser1", actualCaseSubmission.UID)
	s.Equal(1, actualCaseSubmission.OrganizationID)
	s.Equal(1, *actualCaseSubmission.FormID)
	s.Nil(actualCaseSubmission.IdvID)
	s.Nil(actualCaseSubmission.CddID)

	// assert customer
	var actualCustomer model.Customer
	s.NoError(rdb.Get().Where("id = ?", c.ID).First(&actualCustomer).Error)
	s.Equal("testuser1", actualCustomer.UID)
	s.Equal("taiwan", *actualCustomer.Country)
	s.Equal("A123456789", *actualCustomer.NationalID)
	s.Nil(actualCustomer.Birthday)
	s.Nil(actualCustomer.PassportNumber)
	s.Nil(actualCustomer.Email)
	s.Nil(actualCustomer.Phone)
	s.Nil(actualCustomer.LineID)
	s.Nil(actualCustomer.Gender)
	s.Equal(`台北市信義區桃豬街1號桃豬隱猿`, *actualCustomer.PhysicalAddress)
	s.Equal("legal_name", *actualCustomer.LegalName)
}

func TestCaseSubmissionByCompliance(t *testing.T) {
	suite.Run(t, new(testCaseSubmissionByComplianceSuite))
}

func TestGetKycStatusForCompliance(t *testing.T) {
	s := assert.New(t)
	rdb.Reset()
	s.NoError(rdbtest.CreateStudioDefault(rdb.Get()))

	router := gin.Default()
	router.GET("/_v/studio/organization/kyc_status", compliance.GetKycStatusForCompliance)

	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/_v/studio/organization/kyc_status", nil)
		query := req.URL.Query()
		query.Add("kg_user_id", "testuser1")
		query.Add("org_id", strconv.Itoa(10))
		req.URL.RawQuery = query.Encode()

		router.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response struct {
			Code int `json:"code"`
			Data struct {
				KycStatus string `json:"kyc_status"`
			} `json:"data"`
		}

		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal("unverified", response.Data.KycStatus)
	}

	s.NoError(rdb.Get().Model(&model.Customer{}).Where("uid = ?", "testuser1").Update("kyc_status", "verified").Error)

	{
		w := httptest.NewRecorder()
		req, _ := http.NewRequest(http.MethodGet, "/_v/studio/organization/kyc_status", nil)
		query := req.URL.Query()
		query.Add("kg_user_id", "testuser1")
		query.Add("org_id", strconv.Itoa(10))
		req.URL.RawQuery = query.Encode()

		router.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response struct {
			Code int `json:"code"`
			Data struct {
				KycStatus string `json:"kyc_status"`
			} `json:"data"`
		}

		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Equal("verified", response.Data.KycStatus)
	}
}
