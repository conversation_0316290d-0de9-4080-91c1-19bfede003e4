package compliance

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/compliance"
)

// ComplianceToken represents a compliance token.
func ComplianceToken(c *gin.Context) {
	orgID := c.GetInt("org_id")

	// get session data
	sesssionData, kgErr := compliance.GetSessionData(c.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// get compliance token
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeStudioCompliance)
	token, _, err := strategy.CreateToken(sesssionData)
	if err != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "failed to create compliance token", err)
		response.KGError(c, code.NewKGError(code.FailedToCreateStudioComplianceToken, http.StatusInternalServerError, err, nil))
		return
	}

	response.OK(c, gin.H{
		"studio_compliance_token": token,
	})
}
