package compliance

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/compliance"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type auditParams struct {
	KycStatus     string   `json:"kyc_status" binding:"required"`
	RejectReasons []string `json:"reject_reasons"`
	InternalNotes *string  `json:"internal_notes"`
}

// Audit audits compliance data.
func Audit(c *gin.Context) {
	var params auditParams
	kgErr := util.ToGinContextExt(c).BindJson(&params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	uid := c.Param("uid")
	if uid == "" {
		kgErr = code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("uid is required"), nil)
		response.KGError(c, kgErr)
		return
	}
	orgID := c.GetInt("org_id")
	auditorUID := c.GetString("uid")

	kgErr = compliance.Audit(c.Request.Context(), &compliance.AuditParams{
		UID:            uid,
		AuditorUID:     auditorUID,
		OrganizationID: orgID,
		KycStatus:      params.KycStatus,
		RejectReasons:  params.RejectReasons,
		InternalNotes:  params.InternalNotes,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}
