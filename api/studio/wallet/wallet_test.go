package wallet

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestGetWalletProjects(t *testing.T) {
	orgID := 1
	url := "/v1/studio/organization/:orgID/wallet/projects"

	rdb.Reset()
	setupStudioWalletProject(t)

	// Setup test server
	router := gin.Default()
	router.GET(url, auth.MockOrgID(orgID), GetWalletProjects)

	// Mock the request
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", fmt.Sprintf("/v1/studio/organization/%d/wallet/projects", orgID)+"?page_number=1&page_size=10", nil)

	// Set the organization in the request context
	ctx := req.Context()
	req = req.WithContext(ctx)

	// Perform the request
	router.ServeHTTP(w, req)

	// Assert the response
	assert.Equal(t, http.StatusOK, w.Code)

	var response struct {
		Code int `json:"code"`
		Data []struct {
			ChainID      string  `json:"chain_id"`
			Address      string  `json:"address"`
			ProjectID    uint    `json:"project_id"`
			Organization string  `json:"organization"`
			Status       string  `json:"status"`
			ProjectName  string  `json:"project_name"`
			ProjectImage *string `json:"project_image"`
			Version      *string `json:"version"`
			CreatedAt    string  `json:"created_at"`
			UpdatedAt    string  `json:"updated_at"`
			DeletedAt    *string `json:"deleted_at"`
		} `json:"data"`
		Paging struct {
			PageNumber int `json:"page_number"`
			PageSize   int `json:"page_size"`
			TotalCount int `json:"total_count"`
		} `json:"paging"`
	}

	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)

	// Assert the response data
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, 1, len(response.Data))
	assert.Equal(t, "publish", response.Data[0].Status)
	assert.Equal(t, "KryptoGO Yacht Club", response.Data[0].ProjectName)
	assert.Equal(t, "https://wallet-static.kryptogo.com/public/assets/images/NFT_KGYC.gif", *response.Data[0].ProjectImage)
	assert.Equal(t, "1.0.0", *response.Data[0].Version)
}

func TestUpsertWalletProject(t *testing.T) {
	orgID := 1
	url := "/v1/studio/organization/:orgID/wallet/projects"

	rdb.Reset()
	setupStudioWalletProject(t)
	walletProjects := []model.StudioWalletProject{
		{
			ProjectID:    2,
			Organization: "KryptoGO",
			Status:       "draft",
			ProjectName:  util.RandString(10),
			Version:      util.Ptr("1.0.0"),
		},
	}
	err := rdb.Get().Create(&walletProjects).Error
	assert.Nil(t, err)

	// Setup test server
	router := gin.Default()
	router.PUT(url, auth.MockOrgID(orgID), UpsertWalletProject)

	// Mock the request
	w := httptest.NewRecorder()
	body := map[string]interface{}{
		"project_id": 2,
		"version":    "2.0.0",
	}
	jsonStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("PUT", fmt.Sprintf("/v1/studio/organization/%d/wallet/projects", orgID), bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)

	// Set the organization in the request context
	ctx := req.Context()
	req = req.WithContext(ctx)

	// Perform the request
	router.ServeHTTP(w, req)

	// Assert the response
	assert.Equal(t, http.StatusOK, w.Code)

	var response struct {
		Code int `json:"code"`
		Data struct {
			ProjectID uint `json:"project_id"`
		} `json:"data"`
	}

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)

	// Assert the response data
	assert.Equal(t, 0, response.Code)
	orgInfo, kgErr := organization.GetOrgInfo(context.Background(), orgID)
	assert.Nil(t, kgErr)
	p, err := rdb.GetStudioWalletProjectByID(ctx, model.Organization(strings.ToLower(orgInfo.Name)), response.Data.ProjectID)
	assert.Nil(t, err)
	assert.Equal(t, "2.0.0", *p.Version)
}

func setupStudioWalletProject(t *testing.T) {
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	organization.Init(organization.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})
}
