package wallet

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/keymanagement"
	"github.com/kryptogo/kg-wallet-backend/service/studio"
)

// MaxImportedAddressesPerChain is the maximum number of addresses that can be imported per chain
const MaxImportedAddressesPerChain = 5

// WalletType represents the type of wallet supported
// Only 'evm', 'sol', or 'tron' are valid
type WalletType string

const (
	WalletTypeEvm  WalletType = "evm"
	WalletTypeSol  WalletType = "sol"
	WalletTypeTron WalletType = "tron"
)

func (w WalletType) IsValid() bool {
	switch w {
	case WalletTypeEvm, WalletTypeSol, WalletTypeTron:
		return true
	default:
		return false
	}
}

// ImportAddressRequest represents the request to import an address
type ImportAddressRequest struct {
	Chain   string `json:"chain" binding:"required"`
	Address string `json:"address" binding:"required"`
}

// SetDefaultImportedAddressRequest represents the request to set an imported address as default
type SetDefaultImportedAddressRequest struct {
	Default bool `json:"default"`
}

// RetrieveMnemonicRequest represents the request to retrieve a mnemonic
type RetrieveMnemonicRequest struct {
	WalletType WalletType `json:"wallet_type" binding:"required"`
	PublicKey  string     `json:"public_key" binding:"required"`
}

// GetImportedAddresses returns all imported addresses for an organization
func GetImportedAddresses(c *gin.Context) {
	orgIDStr := c.Param("orgID")
	orgID, err := strconv.Atoi(orgIDStr)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid organization ID format.")
		return
	}

	ctx := c.Request.Context()
	addresses, err := studio.GetImportedAddresses(ctx, orgID)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get imported addresses", map[string]interface{}{
			"error": err.Error(),
			"orgID": orgID,
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, "Failed to retrieve imported addresses.")
		return
	}

	response.OK(c, addresses)
}

// ImportAddress adds a new address for an organization
func ImportAddress(c *gin.Context) {
	orgIDStr := c.Param("orgID")
	orgID, err := strconv.Atoi(orgIDStr)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid organization ID format.")
		return
	}

	userID, exists := c.Get("uid")
	if !exists {
		response.UnauthorizedWithMsg(c, code.KgTokenNotProvided, "User ID not found in token.")
		return
	}

	var req ImportAddressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid request payload for importing address: "+err.Error())
		return
	}

	ctx := c.Request.Context()

	// Check if max addresses limit reached
	count, err := studio.CountImportedAddresses(ctx, orgID, req.Chain)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to count imported addresses", map[string]interface{}{
			"error": err.Error(),
			"orgID": orgID,
			"chain": req.Chain,
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, "Failed to count imported addresses.")
		return
	}

	if count >= MaxImportedAddressesPerChain {
		response.BadRequestWithMsg(c, code.ParamIncorrect, fmt.Sprintf("Maximum number of imported addresses (%d) per chain reached.", MaxImportedAddressesPerChain))
		return
	}

	// Validate address format
	if !isValidAddress(req.Chain, req.Address) {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid address format for the specified chain.")
		return
	}

	if err := studio.ImportAddress(ctx, orgID, req.Chain, req.Address, userID.(string)); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to import address", map[string]interface{}{
			"error":   err.Error(),
			"orgID":   orgID,
			"chain":   req.Chain,
			"address": req.Address,
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, "Failed to import address.")
		return
	}

	response.OK(c, gin.H{"message": "Address imported successfully"})
}

// DeleteImportedAddress removes an imported address
func DeleteImportedAddress(c *gin.Context) {
	orgIDStr := c.Param("orgID")
	orgID, err := strconv.Atoi(orgIDStr)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid organization ID format.")
		return
	}

	addressIDStr := c.Param("addressID")
	addressID, err := strconv.Atoi(addressIDStr)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid address ID format.")
		return
	}

	ctx := c.Request.Context()
	if err := studio.DeleteImportedAddress(ctx, orgID, addressID); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to delete imported address", map[string]interface{}{
			"error":     err.Error(),
			"orgID":     orgID,
			"addressID": addressID,
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, "Failed to delete imported address.")
		return
	}

	response.OK(c, gin.H{"message": "Address deleted successfully"})
}

// SetDefaultImportedAddress sets an imported address as the default for its chain
func SetDefaultImportedAddress(c *gin.Context) {
	orgIDStr := c.Param("orgID")
	orgID, err := strconv.Atoi(orgIDStr)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid organization ID format.")
		return
	}

	addressIDStr := c.Param("addressID")
	addressID, err := strconv.Atoi(addressIDStr)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid address ID format.")
		return
	}

	var req SetDefaultImportedAddressRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid request payload: "+err.Error())
		return
	}

	ctx := c.Request.Context()
	// Call the service function which now returns *code.KGError
	kgErr := studio.SetDefaultImportedAddress(ctx, orgID, addressID, req.Default)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, gin.H{"message": "Default imported address updated successfully"})
}

// RetrieveOrganizationWalletMnemonic retrieves the mnemonic for an organization wallet
func RetrieveOrganizationWalletMnemonic(c *gin.Context) {
	// Get the user ID for security audit logging
	uid := c.GetString("uid")
	orgIDStr := c.Param("orgID")
	orgID, err := strconv.Atoi(orgIDStr)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid organization ID format.")
		return
	}

	// Log security sensitive action attempt
	kglog.InfoWithDataCtx(c.Request.Context(), "Organization owner attempting to retrieve wallet mnemonic", map[string]interface{}{
		"uid":   uid,
		"orgID": orgID,
		"ip":    c.ClientIP(),
	})

	req := RetrieveMnemonicRequest{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		kglog.WarningWithDataCtx(c.Request.Context(), "Failed to parse mnemonic retrieval request", map[string]interface{}{
			"error": kgErr,
			"uid":   uid,
			"orgID": orgID,
		})
		response.KGError(c, kgErr)
		return
	}

	ctx := c.Request.Context()

	// Validate WalletType
	if !req.WalletType.IsValid() {
		kglog.WarningWithDataCtx(ctx, "Invalid wallet_type in mnemonic retrieval request", map[string]interface{}{
			"wallet_type": string(req.WalletType),
			"uid":         uid,
			"orgID":       orgID,
		})
		err := code.NewKGError(code.ParamIncorrect, 400, fmt.Errorf("invalid wallet_type: %s", req.WalletType), map[string]interface{}{
			"wallet_type": string(req.WalletType),
		})
		response.KGError(c, err)
		return
	}

	kmReq := keymanagement.RetrieveMnemonicRequest{
		WalletType: string(req.WalletType),
		PublicKey:  req.PublicKey,
	}

	kmResp, err := keymanagement.RetrieveOrganizationMnemonic(ctx, orgID, kmReq)

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Keymanagement service unexpectedly failed to retrieve mnemonic", map[string]interface{}{
			"error":      err.Error(),
			"orgID":      orgID,
			"walletType": req.WalletType,
			"uid":        uid,
		})
		response.InternalServerErrorWithMsg(c, code.InternalError, "An unexpected error occurred while trying to retrieve the mnemonic.")
		return
	}

	// If kmResp.Code is not OK, it means the keymanagement service handled an error and packaged it.
	if kmResp.Code != code.OK {
		// Log the specific error details from the keymanagement service response for internal tracking
		kglog.WarningWithDataCtx(ctx, "Retrieving mnemonic failed with known error from keymanagement service", map[string]interface{}{
			"orgID":         orgID,
			"walletType":    req.WalletType,
			"responseCode":  kmResp.Code,
			"responseError": kmResp.Error,
			"uid":           uid,
		})

		// Respond to the client with the specific error code and message from the keymanagement service response.
		// The http status is OK here because the error is in the business logic response body, not an HTTP-level error.
		c.JSON(http.StatusOK, gin.H{
			"code":  kmResp.Code,
			"error": kmResp.Error,
			"data":  kmResp.Data, // Usually nil in error cases from keymanagement service
		})
		return
	}

	// Log successful mnemonic retrieval for audit purposes
	kglog.InfoWithDataCtx(ctx, "Organization owner successfully retrieved wallet mnemonic", map[string]interface{}{
		"orgID":      orgID,
		"walletType": req.WalletType,
		"uid":        uid,
		"ip":         c.ClientIP(),
	})

	// Success case
	response.OK(c, kmResp.Data)
}

// isValidAddress validates if the given address is valid for the specified chain
func isValidAddress(chain, address string) bool {
	// Simple validation for common chain addresses
	switch chain {
	case "eth", "ethereum", "polygon", "bsc", "arbitrum", "optimism", "base":
		// For EVM chains, check if address is 42 chars long and starts with 0x
		return len(address) == 42 && address[:2] == "0x"
	case "sol", "solana":
		// For Solana addresses, just check length
		return len(address) == 44 || len(address) == 43
	// Add more chains as needed
	default:
		// Unknown chain, return false
		return false
	}
}
