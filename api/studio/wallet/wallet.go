package wallet

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	walletservice "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/wallet"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type getWalletProjectsParams struct {
	Status      *string `form:"status"`
	ProjectName *string `form:"project_name"`
	PageNumber  int     `form:"page_number"`
	PageSize    int     `form:"page_size"`
}

// GetWalletProjects get studio wallet projects
func GetWalletProjects(ctx *gin.Context) {
	params := getWalletProjectsParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	orgID := ctx.GetInt("org_id")
	orgInfo, kgErr := organization.GetOrgInfo(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	organizationName := strings.ToLower(orgInfo.Name)
	// Call service to fetch data
	projects, paging, err := rdb.GetStudioWalletProjects(ctx.Request.Context(), rdb.GetWalletProjectsParams{
		Status:       params.Status,
		ProjectName:  params.ProjectName,
		Organization: organizationName,
		PagingParams: rdb.PagingParams{
			PageNumber: params.PageNumber,
			PageSize:   params.PageSize,
		},
	})

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "get wallet projects failed", err.Error())
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	// Return response
	ctx.JSON(http.StatusOK, gin.H{
		"code":   code.OK,
		"data":   projects,
		"paging": paging,
	})
}

type upsertWalletProject struct {
	ProjectID uint `json:"project_id"`
}

// UpsertWalletProject upsert studio wallet project
func UpsertWalletProject(ctx *gin.Context) {
	req := model.StudioWalletProject{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	orgID := ctx.GetInt("org_id")
	orgInfo, kgErr := organization.GetOrgInfo(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	// Call service to handle upsert logic
	organizationName := model.Organization(strings.ToLower(orgInfo.Name))
	projectID, kgError := walletservice.UpsertWalletProject(ctx, &req, organizationName)
	if kgError != nil {
		response.InternalServerErrorWithMsg(ctx, kgError.Code, kgError.String())
		return
	}

	result := upsertWalletProject{
		ProjectID: projectID,
	}

	// Return success response
	response.OK(ctx, result)
}
