package wallet

import (
	"bytes"
	"context"
	"crypto/ecdh"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/service/keymanagement"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/hkdf"
)

// mockKeyManagementService mocks the keymanagement service
type mockKeyManagementService struct {
	expectedMnemonic string
}

// RetrieveOrganizationMnemonic mocks the keymanagement service's function
func (m *mockKeyManagementService) RetrieveOrganizationMnemonic(ctx context.Context, orgID int, req keymanagement.RetrieveMnemonicRequest) (*keymanagement.RetrieveMnemonicResponse, error) {
	// Return mock response with the expected mnemonic
	return &keymanagement.RetrieveMnemonicResponse{
		Code: 0, // 0 means success
		Data: map[string]interface{}{
			"mnemonic": m.expectedMnemonic,
		},
	}, nil
}

// setupMockRouter creates a Gin router with mocked keymanagement service
func setupMockRouter(t *testing.T, expectedMnemonic string) (*gin.Engine, *mockKeyManagementService) {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(gin.Recovery())

	mockKMS := &mockKeyManagementService{
		expectedMnemonic: expectedMnemonic,
	}

	// Create a custom handler that uses our mock service
	retrieveHandler := func(c *gin.Context) {
		// Extract orgID from path
		orgIDStr := c.Param("orgID")
		orgID, err := strconv.Atoi(orgIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"code": 400, "error": "Invalid organization ID"})
			return
		}

		// Get user ID from context (normally set by auth middleware)
		c.Set("uid", "test-user-id") // Mock the auth middleware

		// Parse request
		var req RetrieveMnemonicRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"code": 400, "error": "Invalid request format"})
			return
		}

		// Convert to keymanagement request
		kmsReq := keymanagement.RetrieveMnemonicRequest{
			WalletType: string(req.WalletType),
			PublicKey:  req.PublicKey,
		}

		// Call our mock service
		resp, err := mockKMS.RetrieveOrganizationMnemonic(c.Request.Context(), orgID, kmsReq)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"code": 500, "error": "Internal server error"})
			return
		}

		// Check response code
		if resp.Code != 0 { // 0 is success
			c.JSON(http.StatusOK, gin.H{
				"code":  resp.Code,
				"error": resp.Error,
			})
			return
		}

		// Return successful response
		c.JSON(http.StatusOK, gin.H{
			"code": 0, // 0 is success
			"data": resp.Data,
		})
	}

	// Register our custom handler
	router.POST("/api/v1/studio/orgs/:orgID/wallet/mnemonic", retrieveHandler)

	return router, mockKMS
}

func TestRetrieveOrganizationWalletMnemonic_HappyPath(t *testing.T) {
	// The expected mnemonic we want to retrieve
	expectedMnemonic := "test mnemonic phrase for wallet encryption decryption test"

	// Setup router with mock service
	router, _ := setupMockRouter(t, expectedMnemonic)

	// Generate ECDH key pair for the test client
	curve := ecdh.P256()
	clientPrivateKey, err := curve.GenerateKey(rand.Reader)
	require.NoError(t, err)

	clientPublicKey := clientPrivateKey.PublicKey()
	clientPubKeyBytes := clientPublicKey.Bytes()
	clientPubKeyHex := hex.EncodeToString(clientPubKeyBytes)

	// Create server key pair (in real world, this would be on server side)
	serverPrivateKey, err := curve.GenerateKey(rand.Reader)
	require.NoError(t, err)
	serverPublicKey := serverPrivateKey.PublicKey()

	// Client derives shared secret (client side)
	clientSharedSecret, err := clientPrivateKey.ECDH(serverPublicKey)
	require.NoError(t, err)

	// Derive encryption key using HKDF (client side)
	clientKdf := hkdf.New(sha256.New, clientSharedSecret, nil, []byte("DHKE-Mnemonic-Encryption"))
	clientDerivedKey := make([]byte, 32) // 256-bit key
	_, err = clientKdf.Read(clientDerivedKey)
	require.NoError(t, err)

	// Create test request
	reqBody, err := json.Marshal(RetrieveMnemonicRequest{
		WalletType: "ethereum",
		PublicKey:  clientPubKeyHex,
	})
	require.NoError(t, err)

	// Create HTTP request
	req, err := http.NewRequest(http.MethodPost, "/api/v1/studio/orgs/1/wallet/mnemonic", bytes.NewBuffer(reqBody))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")

	// Record HTTP response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assert response code
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse response body
	var responseBody struct {
		Code int                    `json:"code"`
		Data map[string]interface{} `json:"data"`
	}
	err = json.Unmarshal(w.Body.Bytes(), &responseBody)
	require.NoError(t, err)

	// Check status code
	assert.Equal(t, 0, responseBody.Code) // 0 means success

	// Extract mnemonic
	mnemonicFromResponse, ok := responseBody.Data["mnemonic"].(string)
	require.True(t, ok)

	// Verify mnemonic
	assert.Equal(t, expectedMnemonic, mnemonicFromResponse)

	// In a real implementation, the client would now:
	// 1. Parse the server's public key
	// 2. Derive the shared secret
	// 3. Use the derived key to decrypt the mnemonic

	// Additional test: Server-side perspective simulation
	t.Run("ECDH_SharedKey_Verification", func(t *testing.T) {
		// Server would receive client's public key (from request)
		parsedClientPubKeyBytes, err := hex.DecodeString(clientPubKeyHex)
		require.NoError(t, err)

		// Server parses client's public key
		unmarshaledClientPubKey, err := curve.NewPublicKey(parsedClientPubKeyBytes)
		require.NoError(t, err)

		// Server derives shared secret
		serverSharedSecret, err := serverPrivateKey.ECDH(unmarshaledClientPubKey)
		require.NoError(t, err)

		// Server derives encryption key
		serverKdf := hkdf.New(sha256.New, serverSharedSecret, nil, []byte("DHKE-Mnemonic-Encryption"))
		serverDerivedKey := make([]byte, 32)
		_, err = serverKdf.Read(serverDerivedKey)
		require.NoError(t, err)

		// Verify client and server have the same key
		assert.Equal(t, clientDerivedKey, serverDerivedKey, "Client and server derived keys should match")

		// In a real implementation:
		// - Server would encrypt: encryptedMnemonic = AES-GCM(serverDerivedKey, mnemonic)
		// - Client would decrypt: mnemonic = AES-GCM-Decrypt(clientDerivedKey, encryptedMnemonic)
	})
}
