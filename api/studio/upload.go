package studio

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/nft"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type createSignedUrlsParams struct {
	Module string              `json:"module" binding:"required"`
	Files  map[string][]string `json:"files" binding:"required"`
}

type createSignedUrlsResponse struct {
	SignedURLs map[string][]string `json:"signed_urls"`
}

// CreateSignedUrls create signed urls
func CreateSignedUrls(ctx *gin.Context) {
	req := createSignedUrlsParams{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	orgID := ctx.GetInt("org_id")
	orgInfo, kgErr := organization.GetOrgInfo(ctx.Request.Context(), orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	respData := &createSignedUrlsResponse{
		SignedURLs: make(map[string][]string),
	}

	for k, v := range req.Files {
		for _, object := range v {
			signedUrl, err := nft.GenerateSignedUrl(req.Module, orgInfo.Name, object)
			if err != nil {
				response.InternalServerErrorWithMsg(ctx, code.GCSError, err.Error())
				return
			}

			respData.SignedURLs[k] = append(respData.SignedURLs[k], signedUrl)
		}
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": respData,
	})
}
