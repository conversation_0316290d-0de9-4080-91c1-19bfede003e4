package defiswap

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/bridge"
	bridgefee "github.com/kryptogo/kg-wallet-backend/service/bridge-fee"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func setupDefiSwapTest(t *testing.T) (*gin.Engine, string) {
	rdb.Reset()
	uRepo := repo.Unified()
	oauth.Init(uRepo)
	application.Init(uRepo)
	bridge.Init(uRepo, nil)
	bridgefee.Init(uRepo)
	tokenmeta.Init(uRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	assert.Nil(t, rdb.Get().Create([]model.AssetPrice{
		{
			ChainID:    domain.Arbitrum.ID(),
			AssetGroup: "******************************************",
			Price:      "1800",
		},
	}).Error)
	// Create test organizations and OAuth applications
	assert.Nil(t, rdb.Get().Create([]model.StudioOrganization{
		{ID: 1, Name: "KryptoGO"},
		{ID: 2, Name: "Test App"},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.OAuthClientConfig{
		{
			ID:           "test_client_id",
			Domain:       "http://test.com",
			IsPrivileged: true,
			Name:         "Test App",
			Secret:       "test_secret",
			IsCustomAuth: false,
		},
		{
			ID:           "another_client_id",
			Domain:       "http://another.com",
			IsPrivileged: true,
			Name:         "Another App",
			Secret:       "another_secret",
			IsCustomAuth: false,
		},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.StudioOrganizationClient{
		{
			OrganizationID:  1,
			ClientID:        "test_client_id",
			ApplicationType: util.Ptr("mobile_wallet"),
		},
		{
			OrganizationID:  2,
			ClientID:        "another_client_id",
			ApplicationType: util.Ptr("mobile_wallet"),
		},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.StudioOrganizationWallet{
		{
			OrganizationID: 1,
			WalletType:     "evm",
			WalletAddress:  "******************************************",
		},
		{
			OrganizationID: 1,
			WalletType:     "tron",
			WalletAddress:  "TJ11111111111111111111111111111111",
		},
	}).Error)

	assert.Nil(t, rdb.Get().Create([]model.AssetProProfitRate{
		{
			OrganizationID:   1,
			Service:          string(domain.ProfitRateServiceTypeSwapDefi),
			ProfitRate:       decimal.NewFromFloat(0.2),
			ProfitShareRatio: decimal.NewFromFloat(0.6),
		},
	}).Error)

	server := gin.Default()
	server.GET("/v1/defi_swap/fee_info", GetFeeInfo)
	server.POST("/v1/defi_swap/record", CreateDefiSwapRecord)

	return server, "/v1/defi_swap"
}

func TestGetFeeInfo(t *testing.T) {
	server, url := setupDefiSwapTest(t)

	tests := []struct {
		name           string
		chainID        string
		clientID       string
		requestBody    map[string]interface{}
		expectedStatus int
		validateResp   func(t *testing.T, resp map[string]interface{})
	}{
		{
			name:           "eth success",
			clientID:       "test_client_id",
			chainID:        "eth",
			expectedStatus: http.StatusOK,
			validateResp: func(t *testing.T, resp map[string]interface{}) {
				data := resp["data"].(map[string]interface{})
				assert.Equal(t, "eth", data["chain_id"])
				assert.Equal(t, "******************************************", data["fee_receive_address"])
				assert.Equal(t, 0.2015, data["fee_rate"])
			},
		},
		{
			name:           "tron success",
			clientID:       "test_client_id",
			chainID:        "tron",
			expectedStatus: http.StatusOK,
			validateResp: func(t *testing.T, resp map[string]interface{}) {
				data := resp["data"].(map[string]interface{})
				assert.Equal(t, "tron", data["chain_id"])
				assert.Equal(t, "TJ11111111111111111111111111111111", data["fee_receive_address"])
				assert.Equal(t, 0.2015, data["fee_rate"])
			},
		},
		{
			name:           "missing client ID",
			clientID:       "",
			chainID:        "eth",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "invalid chain ID",
			clientID:       "test_client_id",
			chainID:        "abc",
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", fmt.Sprintf("%s/fee_info?chain_id=%s", url, tt.chainID), nil)
			if tt.clientID != "" {
				req.Header.Set("X-Client-ID", tt.clientID)
			}
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			server.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			var resp map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &resp)
			t.Logf("resp: %+v", resp)

			if tt.validateResp != nil {
				assert.NoError(t, err)
				tt.validateResp(t, resp)
			}
		})
	}
}

func TestCreateDefiSwapRecord(t *testing.T) {
	server, url := setupDefiSwapTest(t)

	requestBody := map[string]interface{}{
		"chain_id":             "arb",
		"from_address":         "0x5755Cf8Ccc2ba950fF833Bb30AC0607281b113b4",
		"from_token_address":   "******************************************",
		"from_amount":          "0.0000987",
		"tx_hash":              "0xdeb3403fc6bd887536ffe232c30765e0d3d322c2ce3ec1d05fb76f472633e80a",
		"to_address":           "0x5755Cf8Ccc2ba950fF833Bb30AC0607281b113b4",
		"to_token_address":     "0xfd086bc7cd5c481dcc9c85ebe478a1c0b69fcbb9",
		"to_amount":            "0.269514",
		"fee_receive_address":  "******************************************",
		"fee_token_address":    "******************************************",
		"estimated_fee_amount": "0.0000013",
	}
	tests := []struct {
		name           string
		chainID        string
		clientID       string
		uid            string
		requestBody    map[string]interface{}
		expectedStatus int
	}{
		{
			name:           "missing client ID",
			clientID:       "",
			chainID:        "eth",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Arb ETH -> USDT success with uid",
			clientID:       "test_client_id",
			uid:            "uid123",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// truncate bridge record table
			assert.Nil(t, rdb.Get().Exec("DELETE FROM studio_organization_bridge_records").Error)
			newBody := requestBody
			newBody["uid"] = tt.uid
			body, _ := json.Marshal(newBody)
			req, _ := http.NewRequest("POST", fmt.Sprintf("%s/record", url), bytes.NewBuffer(body))
			if tt.clientID != "" {
				req.Header.Set("X-Client-ID", tt.clientID)
			}
			req.Header.Set("Content-Type", "application/json")

			w := httptest.NewRecorder()
			server.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
			t.Logf("body: %s", w.Body.String())
		})
	}
}
