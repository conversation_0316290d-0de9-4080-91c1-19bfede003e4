package defiswap

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	bridgefee "github.com/kryptogo/kg-wallet-backend/service/bridge-fee"
	"github.com/shopspring/decimal"
)

type getFeeInfoReq struct {
	ChainID string `form:"chain_id" binding:"required"`
}

type getFeeRateResponse struct {
	ChainID          string  `json:"chain_id"`
	FeeReceiveWallet string  `json:"fee_receive_address"`
	FeeRate          float64 `json:"fee_rate"`
}

func GetFeeInfo(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.defi-swap.GetFeeInfo")
	defer span.End()

	var req getFeeInfoReq
	if kgErr := util.ToGinContextExt(c).BindQuery(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Get client ID from header
	clientID := c.GetHeader("X-Client-ID")
	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing X-Client-ID header")
		return
	}

	orgID, kgErr := application.GetApplicationOrgId(ctx, clientID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	chain := domain.IDToChain(req.ChainID)
	if chain == nil {
		response.BadRequestWithMsg(c, code.ChainIDNotSupported, "invalid chain id")
		return
	}

	feeRate, wallet, kgErr := bridgefee.GetFeeInfo(ctx, orgID, chain, domain.ProfitRateServiceTypeSwapDefi)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp := getFeeRateResponse{
		ChainID:          chain.ID(),
		FeeRate:          feeRate,
		FeeReceiveWallet: wallet,
	}
	response.OK(c, resp)
}

type createDefiSwapRecord struct {
	UID     *string `json:"kg_uid"` // If user is not logged in, this field won't be provided
	ChainID string  `json:"chain_id" binding:"required"`
	TxHash  string  `json:"tx_hash" binding:"required"`

	FromAddress      string          `json:"from_address" binding:"required"`
	FromTokenAddress string          `json:"from_token_address" binding:"required"`
	FromAmount       decimal.Decimal `json:"from_amount" binding:"required"`

	ToAddress      string          `json:"to_address" binding:"required"`
	ToTokenAddress string          `json:"to_token_address" binding:"required"`
	ToAmount       decimal.Decimal `json:"to_amount" binding:"required"`

	FeeReceiveAddress  string          `json:"fee_receive_address" binding:"required"`
	FeeTokenAddress    string          `json:"fee_token_address" binding:"required"`
	EstimatedFeeAmount decimal.Decimal `json:"estimated_fee_amount" binding:"required"`
}

func CreateDefiSwapRecord(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.defi-swap.CreateDefiSwapRecord")
	defer span.End()

	var req createDefiSwapRecord

	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// If applicable, get UID and client ID from context (from access token)
	uid := req.UID
	if uidFromContext := auth.GetUID(c); uidFromContext != "" {
		uid = &uidFromContext
	}

	clientID := auth.GetClientID(c)
	if clientID == "" {
		clientID = c.GetHeader("X-Client-ID")
	}

	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing X-Client-ID header")
		return
	}

	orgID, kgErr := application.GetApplicationOrgId(ctx, clientID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	chain := domain.IDToChain(req.ChainID)
	if chain == nil {
		response.BadRequestWithMsg(c, code.ChainIDNotSupported, "invalid chain id")
		return
	}

	fromAddress := domain.NewAddressByChain(chain, req.FromAddress)
	feeReceiveAddress := domain.NewAddressByChain(chain, req.FeeReceiveAddress)
	toAddress := domain.NewAddressByChain(chain, req.ToAddress)

	params := &domain.CreateBridgeRecordParams{
		OrgID: orgID,
		UID:   uid,

		FromChain:        chain,
		FromAddress:      fromAddress,
		FromTokenAddress: req.FromTokenAddress,
		FromAmount:       req.FromAmount,
		FromTxHash:       req.TxHash,

		ToChain:        chain,
		ToAddress:      toAddress,
		ToTokenAddress: req.ToTokenAddress,
		ToAmount:       req.ToAmount,
		ToTxHash:       &req.TxHash,

		FeeChain:           chain,
		FeeReceiveAddress:  feeReceiveAddress,
		FeeTokenAddress:    req.FeeTokenAddress,
		EstimatedFeeAmount: req.EstimatedFeeAmount,
		FeeTxHash:          req.TxHash,
	}

	kgErr = bridgefee.CreateBridgeRecord(ctx, params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}
