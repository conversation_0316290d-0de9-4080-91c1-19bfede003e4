package chatroom

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type chatroomSignedUrlsParams struct {
	Channel string              `json:"channel" binding:"required"`
	Files   map[string][]string `json:"files" binding:"required"`
}

type chatroomSignedUrlsResponse struct {
	Code int                     `json:"code"`
	Data *chatroomSignedUrlsData `json:"data"`
}

type chatroomSignedUrlsData struct {
	SignedURLs map[string][]string `json:"signed_urls"`
}

// SignedUrls create signed urls
func SignedUrls(c *gin.Context) {
	ctx := c.Request.Context()
	req := chatroomSignedUrlsParams{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	uid := auth.GetUID(c)
	clientID := auth.GetClientID(c)
	oauthApp, kgErr := application.GetOAuthApplication(ctx, clientID)
	if kgErr != nil {
		kglog.ErrorfCtx(ctx, "get oauth application error: %v", kgErr)
		response.KGError(c, kgErr)
		return
	}
	clientName := oauthApp.Name

	respData := &chatroomSignedUrlsData{
		SignedURLs: make(map[string][]string),
	}

	for k, v := range req.Files {
		for _, object := range v {
			signedUrl, err := chatroom.GenerateSignedUrl(clientName, req.Channel, uid, object)
			if err != nil {
				response.InternalServerErrorWithMsg(c, code.GCSError, err.Error())
				return
			}

			respData.SignedURLs[k] = append(respData.SignedURLs[k], signedUrl)
		}
	}

	resp := &chatroomSignedUrlsResponse{
		Code: 0,
		Data: respData,
	}

	c.JSON(http.StatusOK, resp)
}
