package chatroom

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
)

type chatroomListResponse struct {
	Code int                  `json:"code"`
	Data []*chatroom.Chatroom `json:"data"`
}

// List list chatroom by uid
func List(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)
	if uid == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "uid is required")
		return
	}

	clientID := auth.GetClientID(c)
	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "client_id is required")
		return
	}

	applicationID, apiTokenName, _ := application.GetSendbirdAppIDAndAPIToken(ctx, clientID)
	if applicationID == "" || apiTokenName == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid client_id")
		return
	}
	sendbirdClient := sendbirdapi.NewClient(applicationID, apiTokenName)
	sendbirdService := chatroom.GetService(sendbirdClient)
	chatrooms, _, err := sendbirdService.ListChatrooms(ctx, uid)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "sendbird list chatrooms error", map[string]interface{}{
			"err": err.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.SendbirdError, err.Error())
		return
	}

	resp := &chatroomListResponse{
		Code: 0,
		Data: chatrooms,
	}

	c.JSON(200, resp)
}
