package chatroom

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
)

type chatroomSessionTokenResp struct {
	Code int                      `json:"code"`
	Data chatroomSessionTokenData `json:"data"`
}

type chatroomSessionTokenData struct {
	Token     string `json:"token"`
	ExpiresAt int64  `json:"expires_at"`
}

// SessionToken issue a session token for chatroom
func SessionToken(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)
	if uid == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "uid is required")
		return
	}

	clientID := auth.GetClientID(c)
	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "client_id is required")
		return
	}

	applicationID, apiTokenName, _ := application.GetSendbirdAppIDAndAPIToken(ctx, clientID)
	if applicationID == "" || apiTokenName == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid client_id")
		return
	}
	sendbirdClient := sendbirdapi.NewClient(applicationID, apiTokenName)
	sendbirdService := chatroom.GetService(sendbirdClient)
	ttlInMinutes := time.Duration(config.GetInt("SENDBIRD_SESSION_TOKEN_TTL_IN_MINUTES"))
	expiresAt := time.Now().Add(ttlInMinutes * time.Minute).UnixMilli()
	tokenResp, restyResp, err := sendbirdService.IssueASessionToken(ctx, uid, expiresAt)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "sendbird issue a session token error", map[string]interface{}{
			"uid":        uid,
			"token_resp": tokenResp,
			"resty_resp": restyResp,
			"err":        err.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.SendbirdError, err.Error())
		return
	}

	c.JSON(200, chatroomSessionTokenResp{
		Code: 0,
		Data: chatroomSessionTokenData{
			Token:     tokenResp.Token,
			ExpiresAt: tokenResp.ExpiresAt,
		},
	})
}
