package chatroom

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"

	"github.com/gin-gonic/gin"
	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func mockIssueASessionToken(ctrl *gomock.Controller) func(client sendbirdapi.SendbirdClientI) chatroom.Service {
	return func(client sendbirdapi.SendbirdClientI) chatroom.Service {
		m := chatroom.NewMockService(ctrl)
		m.EXPECT().IssueASessionToken(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&sendbirdapi.IssueASessionTokenResponse{
			Token:     "token",
			ExpiresAt: 123,
		}, &resty.Response{}, nil)
		return m
	}
}

func TestChatroomSessionToken(t *testing.T) {
	uid := "user1"
	clientID := "20991a3ae83233d6de85d62906d71fd3"
	url := "/v1/chatroom/session_token"
	r := gin.Default()
	r.POST(url, auth.MockAuthorize(uid), auth.MockClientID(clientID), SessionToken)

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	fn := mockIssueASessionToken(ctrl)
	chatroom.GetService = fn

	req, err := http.NewRequest("POST", url, nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var resp chatroomSessionTokenResp

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	assert.Nil(t, err)
	assert.Equal(t, 0, resp.Code)
	assert.Equal(t, "token", resp.Data.Token)
	assert.Equal(t, int64(123), resp.Data.ExpiresAt)
}

func mockUpdateAUser(ctrl *gomock.Controller) func(client sendbirdapi.SendbirdClientI) chatroom.Service {
	return func(client sendbirdapi.SendbirdClientI) chatroom.Service {
		m := chatroom.NewMockService(ctrl)
		m.EXPECT().UpdateAUser(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&sendbirdapi.User{
			UserID:     "user1",
			Nickname:   "nickname",
			ProfileURL: "profile_url",
		}, &resty.Response{}, nil)
		return m
	}
}

func TestChatroomUpdateUser(t *testing.T) {
	uid := "user1"
	clientID := "20991a3ae83233d6de85d62906d71fd3"
	url := "/v1/chatrooms/user"
	r := gin.Default()
	r.PATCH(url, auth.MockAuthorize(uid), auth.MockClientID(clientID), UpdateUser)

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	fn := mockUpdateAUser(ctrl)
	chatroom.GetService = fn

	addBody := map[string]interface{}{
		"nickname":    "nickname",
		"profile_url": "profile_url",
	}
	jsonStr, err := json.Marshal(addBody)
	assert.Nil(t, err)
	req, err := http.NewRequest("PATCH", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	res := map[string]interface{}{}

	err = json.Unmarshal(w.Body.Bytes(), &res)
	assert.Nil(t, err)
	assert.Equal(t, 0.0, res["code"].(float64))
}

func mockCreateAUser(ctrl *gomock.Controller) func(client sendbirdapi.SendbirdClientI) chatroom.Service {
	return func(client sendbirdapi.SendbirdClientI) chatroom.Service {
		m := chatroom.NewMockService(ctrl)
		m.EXPECT().CreateAUser(gomock.Any(), gomock.Any()).AnyTimes().Return(&sendbirdapi.User{
			UserID:     "user1",
			Nickname:   "nickname",
			ProfileURL: "profile_url",
		}, &resty.Response{}, nil)
		return m
	}
}

func TestCreateChatroomUser(t *testing.T) {
	uid := "user1"
	clientID := "20991a3ae83233d6de85d62906d71fd3"
	url := "/_v/chatrooms/users"
	r := gin.Default()
	r.POST(url, auth.MockAuthorize(uid), CreateUser)

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	fn := mockCreateAUser(ctrl)
	chatroom.GetService = fn

	addBody := map[string]interface{}{
		"uid":         "uid",
		"nickname":    "nickname",
		"profile_url": "profile_url",
	}
	jsonStr, err := json.Marshal(addBody)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	req.Header.Set("X-Client-ID", clientID)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	res := map[string]interface{}{}

	err = json.Unmarshal(w.Body.Bytes(), &res)
	assert.Nil(t, err)
	assert.Equal(t, 0.0, res["code"].(float64))
}

func mockListChatrooms(ctrl *gomock.Controller) func(client sendbirdapi.SendbirdClientI) chatroom.Service {
	return func(client sendbirdapi.SendbirdClientI) chatroom.Service {
		m := chatroom.NewMockService(ctrl)
		m.EXPECT().ListChatrooms(gomock.Any(), gomock.Any()).AnyTimes().Return([]*chatroom.Chatroom{
			{
				Name:        "name1",
				ChannelURL:  "channel1",
				CoverURL:    "cover1",
				CustomType:  "custom1",
				Data:        chatroom.Data{BackgroundImage: "background1", Description: "description1"},
				Role:        chatroom.RoleMod,
				IsQualified: true,
			},
			{
				Name:        "name2",
				ChannelURL:  "channel2",
				CoverURL:    "cover2",
				CustomType:  "custom2",
				Data:        chatroom.Data{BackgroundImage: "background2", Description: "description2"},
				Role:        chatroom.RoleMember,
				IsQualified: true,
			},
		}, &resty.Response{}, nil)
		return m
	}
}

func TestChatroomList(t *testing.T) {
	uid := "user1"
	clientID := "20991a3ae83233d6de85d62906d71fd3"
	url := "/v1/chatrooms"
	r := gin.Default()
	r.GET(url, auth.MockAuthorize(uid), auth.MockClientID(clientID), List)

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	fn := mockListChatrooms(ctrl)
	chatroom.GetService = fn

	req, err := http.NewRequest("GET", url, nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var resp chatroomListResponse

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	assert.Nil(t, err)
	assert.Equal(t, 0, resp.Code)
	assert.Equal(t, 2, len(resp.Data))

	chatroom1 := resp.Data[0]
	chatroom2 := resp.Data[1]

	assert.Equal(t, "name1", chatroom1.Name)
	assert.Equal(t, "channel1", chatroom1.ChannelURL)
	assert.Equal(t, "cover1", chatroom1.CoverURL)
	assert.Equal(t, "custom1", chatroom1.CustomType)
	assert.Equal(t, "background1", chatroom1.Data.BackgroundImage)
	assert.Equal(t, "description1", chatroom1.Data.Description)
	assert.Equal(t, chatroom.RoleMod, chatroom1.Role)
	assert.Equal(t, true, chatroom1.IsQualified)

	assert.Equal(t, "name2", chatroom2.Name)
	assert.Equal(t, "channel2", chatroom2.ChannelURL)
	assert.Equal(t, "cover2", chatroom2.CoverURL)
	assert.Equal(t, "custom2", chatroom2.CustomType)
	assert.Equal(t, "background2", chatroom2.Data.BackgroundImage)
	assert.Equal(t, "description2", chatroom2.Data.Description)
	assert.Equal(t, chatroom.RoleMember, chatroom2.Role)
	assert.Equal(t, true, chatroom2.IsQualified)
}
