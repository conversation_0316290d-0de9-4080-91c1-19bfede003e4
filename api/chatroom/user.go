package chatroom

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type chatroomUpdateUserRequest struct {
	Nickname   string `json:"nickname"`
	ProfileURL string `json:"profile_url"`
}

// UpdateUser update user info in chatroom
func UpdateUser(c *gin.Context) {
	ctx := c.Request.Context()
	req := chatroomUpdateUserRequest{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	uid := auth.GetUID(c)
	if uid == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "uid is required")
		return
	}

	clientID := auth.GetClientID(c)
	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "client_id is required")
		return
	}

	applicationID, apiTokenName, _ := application.GetSendbirdAppIDAndAPIToken(ctx, clientID)
	if applicationID == "" || apiTokenName == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid client_id")
		return
	}
	sendbirdClient := sendbirdapi.NewClient(applicationID, apiTokenName)
	sendbirdService := chatroom.GetService(sendbirdClient)
	_, _, err := sendbirdService.UpdateAUser(ctx, uid, util.Ptr(req.Nickname), util.Ptr(req.ProfileURL))
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "sendbird update a user error", map[string]interface{}{
			"uid": uid,
			"err": err.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.SendbirdError, err.Error())
		return
	}
	c.JSON(200, map[string]interface{}{
		"code": 0,
	})
}

type createChatroomUserRequest struct {
	UID        string `json:"uid"`
	Nickname   string `json:"nickname"`
	ProfileURL string `json:"profile_url"`
}

// CreateUser create a user in chatroom
func CreateUser(c *gin.Context) {
	ctx := c.Request.Context()
	req := createChatroomUserRequest{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	clientID := oauth.ClientID(c)
	if clientID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "client_id is required")
		return
	}

	applicationID, apiTokenName, _ := application.GetSendbirdAppIDAndAPIToken(ctx, clientID)
	if applicationID == "" || apiTokenName == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid client_id")
		return
	}
	sendbirdClient := sendbirdapi.NewClient(applicationID, apiTokenName)
	sendbirdService := chatroom.GetService(sendbirdClient)
	_, _, err := sendbirdService.CreateAUser(ctx, &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         req.UID,
			DisplayName: req.Nickname,
			Avatar: &domain.Avatar{
				AvatarURL: req.ProfileURL,
			},
		},
	})
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "sendbird create a user error", map[string]interface{}{
			"uid": req.UID,
			"err": err.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.SendbirdError, err.Error())
		return
	}
	c.JSON(200, map[string]interface{}{
		"code": 0,
	})
}
