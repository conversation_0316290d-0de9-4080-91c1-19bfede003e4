package sendwithrent

import (
	"encoding/json"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/chain/tron"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	sendwithrent "github.com/kryptogo/kg-wallet-backend/service/send-with-rent"
)

// InitReq represents the request payload for initializing a send-with-rent transaction.
type InitReq struct {
	ChainID   string   `json:"chain_id" binding:"required"`
	From      string   `json:"from" binding:"required"`
	SignedTxs []string `json:"signed_txs" binding:"required,min=2"`

	// parsed fields
	ParsedTxs []*tron.Transaction `json:"-"`
}

func (r *InitReq) AfterBinding(c *gin.Context) error {
	parsedTxs := make([]*tron.Transaction, len(r.SignedTxs))
	for i, tx := range r.SignedTxs {
		parsedTx := &tron.Transaction{}
		if err := json.Unmarshal([]byte(tx), parsedTx); err != nil {
			return err
		}
		parsedTxs[i] = parsedTx
	}
	r.ParsedTxs = parsedTxs
	return nil
}

// Init initializes a new send-with-rent transaction.
func Init(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)
	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	var req InitReq
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}
	if req.ChainID != "tron" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "only tron is supported")
		return
	}
	chain := domain.Tron

	// Create the send-with-rent transaction
	sendWithRentID, kgErr := sendwithrent.Create(ctx, &sendwithrent.CreateParams{
		OrgID:     orgID,
		UID:       uid,
		Chain:     chain,
		From:      domain.NewTronAddress(req.From),
		SignedTxs: req.ParsedTxs,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Respond with the created send-with-rent ID
	response.OK(c, gin.H{
		"id": sendWithRentID,
	})
}

// Handle processes a send-with-rent request based on its ID.
func Handle(c *gin.Context) {
	ctx := c.Request.Context()
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}

	kgErr := sendwithrent.Handle(ctx, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}

// GetByID retrieves a send-with-rent transaction by its ID.
func GetByID(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid id")
		return
	}

	resp, kgErr := sendwithrent.Get(ctx, uid, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, resp)
}
