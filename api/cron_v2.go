package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	chainsync "github.com/kryptogo/kg-wallet-backend/service/chain-sync"
)

// CheckAndUpdateAssetPricesV2 is the v2 api for cronjob to check and update asset prices
func CheckAndUpdateAssetPricesV2(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "CheckAndUpdateAssetPricesV2")
	defer span.End()

	var req struct {
		Timeout int `json:"timeout" binding:"required"`
	}

	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Create a context with timeout based on requestBody.Timeout
	ctxWithTimeout, cancel := tracing.WithTimeoutAndTrace(ctx, time.Duration(req.Timeout)*time.Second)
	defer cancel()

	err := asset.CheckAndUpdatePrices(ctxWithTimeout)
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}
	c.String(http.StatusOK, "done")
}

// UpdateAllAssetPricesV2 is the v2 api for cronjob to update all asset prices
func UpdateAllAssetPricesV2(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "UpdateAllAssetPricesV2")
	defer span.End()

	var req struct {
		Timeout int `json:"timeout" binding:"required"`
	}

	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Create a context with timeout based on requestBody.Timeout
	ctxWithTimeout, cancel := tracing.WithTimeoutAndTrace(ctx, time.Duration(req.Timeout)*time.Second)
	defer cancel()

	err := asset.UpdatePrices(ctxWithTimeout, nil, true)
	if err != nil {
		c.String(http.StatusInternalServerError, err.Error())
		return
	}
	c.String(http.StatusOK, "done")
}

// SyncChain is used to continuously sync chain data in given timeout
func SyncChain(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "SyncChain")
	defer span.End()

	var req struct {
		Timeout int `json:"timeout" binding:"required"`
	}

	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Create a context with timeout based on requestBody.Timeout
	ctxWithTimeout, cancel := tracing.WithTimeoutAndTrace(ctx, time.Duration(req.Timeout)*time.Second)
	defer cancel()

	chainsync.Sync(ctxWithTimeout)
	c.String(http.StatusOK, "done")
}
