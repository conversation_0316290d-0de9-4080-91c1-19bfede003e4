package api_test

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func setupNotificationTest(t *testing.T) (*gin.Engine, string, string) {
	repo := rdb.GormRepo()
	oauth.Init(repo)
	application.Init(repo)
	rdb.Reset()
	users, uids := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	assert.Nil(t, rdb.Get().Create([]model.StudioOrganization{
		{ID: 1, Name: "KryptoGO"},
		{ID: 3, Name: "Tag"},
	}).Error)
	assert.Nil(t, rdb.Get().Create([]model.OAuthClientConfig{
		{
			ID:           "20991a3ae83233d6de85d62906d71fd3",
			Domain:       "http://localhost:8040",
			IsPrivileged: true,
			Name:         "KryptoGO",
			Secret:       "vgWdVMPILGukPcdhqr547XKmpy2iRGN5",
			IsCustomAuth: false,
		},
		{
			ID:           "de85d62906d71fd320991a3ae83233d6",
			Domain:       "http://localhost:8040",
			IsPrivileged: true,
			Name:         "Tag",
			Secret:       "IASEBX4mNTQKEDvW33iAq0UotQAKPbgV",
			IsCustomAuth: false,
		},
	}).Error)
	assert.Nil(t, rdb.Get().Create([]model.StudioOrganizationClient{
		{
			OrganizationID:  1,
			ClientID:        "20991a3ae83233d6de85d62906d71fd3",
			ApplicationType: util.Ptr("mobile_wallet"),
		},
		{
			OrganizationID:  3,
			ClientID:        "de85d62906d71fd320991a3ae83233d6",
			ApplicationType: util.Ptr("mobile_wallet"),
		},
	}).Error)
	assert.Nil(t, rdb.Get().Create([]model.Notification{
		{
			ID:          1,
			Receiver:    uids[0],
			ContentType: "html",
			MessageType: "transaction",
			Title:       "{\"en_US\":\"Test Tx Notification\",\"zh_TW\":\"測試交易通知\"}",
			Summary:     "{\"en_US\":\"This is a test notification\",\"zh_TW\":\"這是測試通知\"}",
			Message:     "{\"en_US\":\"This is a test notification\",\"zh_TW\":\"這是測試通知訊息\"}",
			ClientID:    util.Ptr("20991a3ae83233d6de85d62906d71fd3"),
		},
		{
			ID:          2,
			Receiver:    "topic/announcement",
			ContentType: "html",
			MessageType: "announcement",
			Title:       "Test Announcement Notification",
			Summary:     "This is a test announcement notification",
			Message:     "This is a test announcement notification",
			ClientID:    util.Ptr("20991a3ae83233d6de85d62906d71fd3"),
		},
		{
			ID:          3,
			Receiver:    uids[0],
			ContentType: "html",
			MessageType: "system",
			Title:       "Test System Notification",
			Summary:     "This is a test system notification",
			Message:     "This is a test system notification",
			ClientID:    util.Ptr("20991a3ae83233d6de85d62906d71fd3"),
		},
		{
			ID:          4,
			Receiver:    uids[0],
			ContentType: "html",
			MessageType: "transaction",
			Title:       "Test Tx Notification for Tag",
			Summary:     "This is a test notification for Tag",
			Message:     "This is a test notification for Tag",
			ClientID:    util.Ptr("de85d62906d71fd320991a3ae83233d6"),
		},
		{
			ID:          5,
			Receiver:    "topic/announcement",
			ContentType: "html",
			MessageType: "announcement",
			Title:       "Test Announcement Notification for Tag",
			Summary:     "This is a test announcement notification for Tag",
			Message:     "This is a test announcement notification for Tag",
			ClientID:    util.Ptr("de85d62906d71fd320991a3ae83233d6"),
		},
		{
			ID:          6,
			Receiver:    uids[0],
			ContentType: "html",
			MessageType: "system",
			Title:       "Test System Notification for Tag",
			Summary:     "This is a test system notification for Tag",
			Message:     "This is a test system notification for Tag",
			ClientID:    util.Ptr("de85d62906d71fd320991a3ae83233d6"),
		},
		{
			ID:          7,
			Receiver:    uids[0],
			ContentType: "html",
			MessageType: "system",
			Title:       "Test System Notification for KryptoGO",
			Summary:     "This is a test system notification for KryptoGO",
			Message:     "This is a test system notification for KryptoGO",
			ClientID:    util.Ptr("20991a3ae83233d6de85d62906d71fd3"),
		},
	}).Error)

	url := "/v1/notifications"
	server := gin.Default()
	server.GET(url, auth.MockAuthorize(uids[0]), api.Notifications)
	server.GET(url+"/unread", auth.MockAuthorize(uids[0]), api.NotificationUnreadCount)
	server.POST(url+"/read-all", auth.MockAuthorize(uids[0]), api.NotificationReadAll) // Add this line

	return server, uids[0], url
}

type notificationsResponse struct {
	Code int `json:"code"`
	Data []struct {
		ID                int32  `json:"id"`
		ContentType       string `json:"content_type,omitempty"`
		MessageType       string `json:"message_type"`
		NotificationType  string `json:"notification_type"`
		Title             string `json:"title"`
		Summary           string `json:"summary"`
		Message           string `json:"message,omitempty"`
		PrimaryLink       string `json:"primary_link,omitempty"`
		PrimaryText       string `json:"primary_text,omitempty"`
		PrimaryOpenWith   string `json:"primary_open_with,omitempty"`
		SecondaryLink     string `json:"secondary_link,omitempty"`
		SecondaryText     string `json:"secondary_text,omitempty"`
		SecondaryOpenWith string `json:"secondary_open_with,omitempty"`
		CreatedTimestamp  int64  `json:"created_timestamp"`
		IsRead            bool   `json:"is_read"`
		PreviewImageURL   string `json:"preview_image_url,omitempty"`
	} `json:"data"`
	Paging struct {
		NextToken string `json:"next_token"`
		Size      int    `json:"size"`
	} `json:"paging"`
}

func TestNotifications(t *testing.T) {
	server, uid, url := setupNotificationTest(t)

	// Test case 1: default client ID
	{
		// Test notifications list
		req, _ := http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", "20991a3ae83233d6de85d62906d71fd3")
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var responseBody notificationsResponse
		err := json.Unmarshal(w.Body.Bytes(), &responseBody)
		assert.Nil(t, err)
		assert.Equal(t, 0, responseBody.Code)
		assert.Equal(t, 5, len(responseBody.Data))

		// ID 7 - System notification for KryptoGO
		assert.Equal(t, int32(7), responseBody.Data[0].ID)
		assert.Equal(t, "system", responseBody.Data[0].NotificationType)
		assert.Equal(t, "Test System Notification for KryptoGO", responseBody.Data[0].Title)
		assert.Equal(t, "This is a test system notification for KryptoGO", responseBody.Data[0].Summary)

		// ID 4 - Transaction notification for Tag
		assert.Equal(t, int32(4), responseBody.Data[1].ID)
		assert.Equal(t, "transaction", responseBody.Data[1].NotificationType)
		assert.Equal(t, "Test Tx Notification for Tag", responseBody.Data[1].Title)
		assert.Equal(t, "This is a test notification for Tag", responseBody.Data[1].Summary)

		// ID 3 - System notification
		assert.Equal(t, int32(3), responseBody.Data[2].ID)
		assert.Equal(t, "system", responseBody.Data[2].NotificationType)
		assert.Equal(t, "Test System Notification", responseBody.Data[2].Title)
		assert.Equal(t, "This is a test system notification", responseBody.Data[2].Summary)

		// ID 2 - Announcement notification
		assert.Equal(t, int32(2), responseBody.Data[3].ID)
		assert.Equal(t, "announcement", responseBody.Data[3].NotificationType)
		assert.Equal(t, "Test Announcement Notification", responseBody.Data[3].Title)
		assert.Equal(t, "This is a test announcement notification", responseBody.Data[3].Summary)

		// ID 1 - Transaction notification
		assert.Equal(t, int32(1), responseBody.Data[4].ID)
		assert.Equal(t, "transaction", responseBody.Data[4].NotificationType)
		assert.Equal(t, "Test Tx Notification", responseBody.Data[4].Title)
		assert.Equal(t, "This is a test notification", responseBody.Data[4].Summary)

		// Test unread count
		req, _ = http.NewRequest("GET", url+"/unread", nil)
		req.Header.Set("X-Client-ID", "20991a3ae83233d6de85d62906d71fd3")
		w = httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var unreadResponse struct {
			Code int `json:"code"`
			Cnt  int `json:"cnt"`
		}
		err = json.Unmarshal(w.Body.Bytes(), &unreadResponse)
		assert.Nil(t, err)
		assert.Equal(t, 0, unreadResponse.Code)
		assert.Equal(t, 5, unreadResponse.Cnt)
	}

	// Test case 2: another client ID
	{
		req, _ := http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", "de85d62906d71fd320991a3ae83233d6")
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var responseBody notificationsResponse
		err := json.Unmarshal(w.Body.Bytes(), &responseBody)
		assert.Nil(t, err)
		assert.Equal(t, 0, responseBody.Code)
		assert.Equal(t, 4, len(responseBody.Data))
		assert.Equal(t, int32(6), responseBody.Data[0].ID)
		assert.Equal(t, int32(5), responseBody.Data[1].ID)
		assert.Equal(t, int32(4), responseBody.Data[2].ID)
		assert.Equal(t, int32(1), responseBody.Data[3].ID)

		// Test unread count
		req, _ = http.NewRequest("GET", url+"/unread", nil)
		req.Header.Set("X-Client-ID", "de85d62906d71fd320991a3ae83233d6")
		w = httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var unreadResponse struct {
			Code int `json:"code"`
			Cnt  int `json:"cnt"`
		}
		err = json.Unmarshal(w.Body.Bytes(), &unreadResponse)
		assert.Nil(t, err)
		assert.Equal(t, 0, unreadResponse.Code)
		assert.Equal(t, 4, unreadResponse.Cnt)
	}

	// Test case 3: default client ID with zh_TW locale
	{
		assert.Nil(t, rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:    uid,
				Locale: "zh_TW",
				LocaleMap: map[string]string{
					"20991a3ae83233d6de85d62906d71fd3": "zh_TW",
				},
			},
		}))

		// Test notifications list
		req, _ := http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", "20991a3ae83233d6de85d62906d71fd3")
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var responseBody notificationsResponse
		err := json.Unmarshal(w.Body.Bytes(), &responseBody)
		assert.Nil(t, err)
		assert.Equal(t, 0, responseBody.Code)
		assert.Equal(t, 5, len(responseBody.Data))

		// ID 1 - Transaction notification
		assert.Equal(t, int32(1), responseBody.Data[4].ID)
		assert.Equal(t, "transaction", responseBody.Data[4].NotificationType)
		assert.Equal(t, "測試交易通知", responseBody.Data[4].Title)
		assert.Equal(t, "這是測試通知", responseBody.Data[4].Summary)
	}

	// Test case 3: default client ID with other locale
	{
		assert.Nil(t, rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:    uid,
				Locale: "ja_JP",
				LocaleMap: map[string]string{
					"20991a3ae83233d6de85d62906d71fd3": "ja_JP",
				},
			},
		}))

		// Test notifications list
		req, _ := http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", "20991a3ae83233d6de85d62906d71fd3")
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var responseBody notificationsResponse
		err := json.Unmarshal(w.Body.Bytes(), &responseBody)
		assert.Nil(t, err)
		assert.Equal(t, 0, responseBody.Code)
		assert.Equal(t, 5, len(responseBody.Data))

		// ID 1 - Transaction notification
		assert.Equal(t, int32(1), responseBody.Data[4].ID)
		assert.Equal(t, "transaction", responseBody.Data[4].NotificationType)
		assert.Equal(t, "Test Tx Notification", responseBody.Data[4].Title)
		assert.Equal(t, "This is a test notification", responseBody.Data[4].Summary)
	}
}

func TestNotificationReadAll(t *testing.T) {
	server, _, url := setupNotificationTest(t)

	clientID1 := "20991a3ae83233d6de85d62906d71fd3" // KryptoGO
	clientID2 := "de85d62906d71fd320991a3ae83233d6" // Tag

	// Verify initially all notifications are unread for both clients
	{
		// Check client 1
		req, _ := http.NewRequest("GET", url+"/unread", nil)
		req.Header.Set("X-Client-ID", clientID1)
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var unreadResponse struct {
			Code int `json:"code"`
			Cnt  int `json:"cnt"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &unreadResponse)
		assert.Nil(t, err)
		assert.Equal(t, 5, unreadResponse.Cnt) // Client 1 should have 5 unread notifications

		// Check client 2
		req, _ = http.NewRequest("GET", url+"/unread", nil)
		req.Header.Set("X-Client-ID", clientID2)
		w = httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		err = json.Unmarshal(w.Body.Bytes(), &unreadResponse)
		assert.Nil(t, err)
		assert.Equal(t, 4, unreadResponse.Cnt) // Client 2 should have 4 unread notifications
	}

	// Mark all notifications as read for client 1
	{
		time.Sleep(time.Second)
		req, _ := http.NewRequest("POST", url+"/read-all", nil)
		req.Header.Set("X-Client-ID", clientID1)
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var readAllResponse struct {
			Code int `json:"code"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &readAllResponse)
		assert.Nil(t, err)
		assert.Equal(t, 0, readAllResponse.Code)
	}

	// Verify client 1's notifications are read but client 2's are still unread
	{
		// Check client 1 unread count (should be 0)
		req, _ := http.NewRequest("GET", url+"/unread", nil)
		req.Header.Set("X-Client-ID", clientID1)
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)

		var unreadResponse struct {
			Code int `json:"code"`
			Cnt  int `json:"cnt"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &unreadResponse)
		assert.Nil(t, err)
		assert.Equal(t, 0, unreadResponse.Cnt) // Should be 0 after read-all

		// Check client 1 notifications are marked as read
		req, _ = http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", clientID1)
		w = httptest.NewRecorder()
		server.ServeHTTP(w, req)

		var responseBody notificationsResponse
		err = json.Unmarshal(w.Body.Bytes(), &responseBody)
		assert.Nil(t, err)
		for _, notification := range responseBody.Data {
			assert.True(t, notification.IsRead, "Notification %d should be marked as read for client 1", notification.ID)
		}

		// Check client 2 unread count (should still be 4)
		req, _ = http.NewRequest("GET", url+"/unread", nil)
		req.Header.Set("X-Client-ID", clientID2)
		w = httptest.NewRecorder()
		server.ServeHTTP(w, req)

		err = json.Unmarshal(w.Body.Bytes(), &unreadResponse)
		assert.Nil(t, err)
		assert.Equal(t, 4, unreadResponse.Cnt) // Should still be 4 for client 2

		// Check client 2 notifications are still unread
		req, _ = http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", clientID2)
		w = httptest.NewRecorder()
		server.ServeHTTP(w, req)

		err = json.Unmarshal(w.Body.Bytes(), &responseBody)
		assert.Nil(t, err)
		for _, notification := range responseBody.Data {
			assert.False(t, notification.IsRead, "Notification %d should still be unread for client 2", notification.ID)
		}
	}

	// Mark all notifications as read for client 2
	{
		req, _ := http.NewRequest("POST", url+"/read-all", nil)
		req.Header.Set("X-Client-ID", clientID2)
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		// Verify client 2's unread count is now 0
		req, _ = http.NewRequest("GET", url+"/unread", nil)
		req.Header.Set("X-Client-ID", clientID2)
		w = httptest.NewRecorder()
		server.ServeHTTP(w, req)

		var unreadResponse struct {
			Code int `json:"code"`
			Cnt  int `json:"cnt"`
		}
		err := json.Unmarshal(w.Body.Bytes(), &unreadResponse)
		assert.Nil(t, err)
		assert.Equal(t, 0, unreadResponse.Cnt) // Should now be 0 for client 2

		// Verify client 2's notifications are marked as read
		req, _ = http.NewRequest("GET", url, nil)
		req.Header.Set("X-Client-ID", clientID2)
		w = httptest.NewRecorder()
		server.ServeHTTP(w, req)

		var responseBody notificationsResponse
		err = json.Unmarshal(w.Body.Bytes(), &responseBody)
		assert.Nil(t, err)
		for _, notification := range responseBody.Data {
			assert.True(t, notification.IsRead, "Notification %d should now be marked as read for client 2", notification.ID)
		}
	}
}
