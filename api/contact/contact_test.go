package contact

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"sort"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/contact"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/contact/repo"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestContacts(t *testing.T) {
	// create uid user
	// create uid-friend1 user
	// create friendPhoneNumber user
	rdb.Reset()
	users, userIDs := dbtest.Users()
	uid := userIDs[0]
	friendUID := uid + "-friend1"
	friend1 := users[uid]
	friend1.UID = friendUID
	friend1.Handle = util.Ptr("handle2-" + friendUID)
	users[friendUID] = friend1
	friendPhoneNumber := util.RandPhone()
	userData := users[uid]
	// copy to a new map to avoid changing the original map
	friend2 := userData
	friend2.PhoneNumber = friendPhoneNumber
	friend2UID := util.RandString(20)
	friend2.UID = friend2UID
	friend2.Handle = util.Ptr("handle2-" + friend2UID)
	users[friend2UID] = friend2
	assert.Nil(t, rdb.GormRepo().BatchSetUsers(context.Background(), users))

	assert.Nil(t, rdbtest.CreateContacts(rdb.Get(), uid, friendPhoneNumber))

	listURL := "/v1/contacts"
	deleteURL := "/v1/contacts/:contact_id"
	server := gin.Default()
	server.GET(listURL, auth.MockAuthorize(uid), ListContacts)
	server.DELETE(deleteURL, auth.MockAuthorize(uid), DeleteContact)
	server.PUT(listURL, auth.MockAuthorize(uid), UpsertContacts)

	contact.Init(repo.NewContactRepo())

	req, err := http.NewRequest("GET", "/v1/contacts", nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	type listContactsResp struct {
		Code int `json:"code"`
		Data []struct {
			UID             string  `json:"uid"`
			UserID          *string `json:"user_id"`
			PhoneNumber     *string `json:"phone_number"`
			Nickname        *string `json:"nickname"`
			Name            string  `json:"name"`
			Handle          string  `json:"handle"`
			Btc             string  `json:"btc"`
			Eth             string  `json:"eth"`
			Bsc             string  `json:"bsc"`
			Matic           string  `json:"matic"`
			Tron            string  `json:"tron"`
			Sol             string  `json:"sol"`
			Kcc             string  `json:"kcc"`
			Arb             string  `json:"arb"`
			Ronin           string  `json:"ronin"`
			Oasys           string  `json:"oasys"`
			AvatarURL       *string `json:"avatar_url"`
			IsManualAddress *bool   `json:"is_manual_address"`
			IsAddedByQR     *bool   `json:"is_added_by_qr"`
			IsFavorite      bool    `json:"is_favorite"`
		} `json:"data"`
	}

	assert.Equal(t, http.StatusOK, w.Code)
	var resp listContactsResp
	responseStr, err := io.ReadAll(w.Body)
	assert.Nil(t, err)
	assert.Nil(t, json.Unmarshal(responseStr, &resp))
	t.Logf("resp: %+v", resp)
	assert.Equal(t, 0, resp.Code)
	assert.Equal(t, 4, len(resp.Data))
	sort.Slice(resp.Data, func(i, j int) bool {
		if len(resp.Data[i].UID) == len(resp.Data[j].UID) {
			return resp.Data[i].UID < resp.Data[j].UID
		}
		return len(resp.Data[i].UID) < len(resp.Data[j].UID)
	})
	contact0 := resp.Data[0]
	contact1 := resp.Data[1]
	contact2 := resp.Data[2]
	contact3 := resp.Data[3]
	assert.Equal(t, "uid1", contact0.UID)
	assert.Nil(t, contact0.UserID)
	assert.Equal(t, "uid2", contact1.UID)
	assert.Equal(t, friendUID, *contact1.UserID)
	assert.Equal(t, *friend1.Handle, contact1.Handle)
	assert.Equal(t, "uid3", contact2.UID)
	assert.Equal(t, friend2UID, *contact2.UserID)
	assert.Equal(t, *friend2.Handle, contact2.Handle)
	assert.Equal(t, "uid4", contact3.UID)
	assert.Nil(t, contact3.UserID)

	// delete contact
	req, err = http.NewRequest("DELETE", "/v1/contacts/uid4", nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// delete not owner's contact
	req, err = http.NewRequest("DELETE", "/v1/contacts/uid5", nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	req, err = http.NewRequest("GET", "/v1/contacts", nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, err = io.ReadAll(w.Body)
	assert.Nil(t, err)
	assert.Nil(t, json.Unmarshal(responseStr, &resp))
	assert.Equal(t, 0, resp.Code)
	assert.Equal(t, 3, len(resp.Data))
	sort.Slice(resp.Data, func(i, j int) bool {
		if len(resp.Data[i].UID) == len(resp.Data[j].UID) {
			return resp.Data[i].UID < resp.Data[j].UID
		}
		return len(resp.Data[i].UID) < len(resp.Data[j].UID)
	})
	contact0 = resp.Data[0]
	contact1 = resp.Data[1]
	contact2 = resp.Data[2]
	assert.Equal(t, "uid1", contact0.UID)
	assert.Nil(t, contact0.UserID)
	assert.Equal(t, "name1", contact0.Name)
	assert.Equal(t, "btc1", contact0.Btc)
	assert.Equal(t, "eth1", contact0.Eth)
	assert.Equal(t, "bsc1", contact0.Bsc)
	assert.Equal(t, "matic1", contact0.Matic)
	assert.Equal(t, "tron1", contact0.Tron)
	assert.Equal(t, "sol1", contact0.Sol)
	assert.Equal(t, "kcc1", contact0.Kcc)
	assert.Equal(t, "arb1", contact0.Arb)
	assert.Equal(t, "ronin1", contact0.Ronin)
	assert.Equal(t, "oasys1", contact0.Oasys)
	assert.Equal(t, true, *contact0.IsManualAddress)
	assert.Equal(t, false, contact0.IsFavorite)
	assert.Equal(t, "uid2", contact1.UID)
	assert.Equal(t, friendUID, *contact1.UserID)
	assert.Equal(t, "******************************************", contact1.Btc)
	assert.Equal(t, "******************************************", contact1.Eth)
	assert.Equal(t, "******************************************", contact1.Bsc)
	assert.Equal(t, "******************************************", contact1.Matic)
	assert.Equal(t, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", contact1.Tron)
	assert.Equal(t, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7", contact1.Sol)
	assert.Equal(t, "******************************************", contact1.Kcc)
	assert.Equal(t, "******************************************", contact1.Arb)
	assert.Equal(t, "******************************************", contact1.Ronin)
	assert.Equal(t, "******************************************", contact1.Oasys)
	assert.Equal(t, true, *contact1.IsAddedByQR)
	assert.Equal(t, true, contact1.IsFavorite)
	assert.Equal(t, "uid3", contact2.UID)
	assert.Equal(t, friend2UID, *contact2.UserID)
	assert.Equal(t, "******************************************", contact2.Btc)
	assert.Equal(t, "******************************************", contact2.Eth)
	assert.Equal(t, "******************************************", contact2.Bsc)
	assert.Equal(t, "******************************************", contact2.Matic)
	assert.Equal(t, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", contact2.Tron)
	assert.Equal(t, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7", contact2.Sol)
	assert.Equal(t, "******************************************", contact2.Kcc)
	assert.Equal(t, "******************************************", contact2.Arb)
	assert.Equal(t, "******************************************", contact2.Ronin)
	assert.Equal(t, "******************************************", contact2.Oasys)
	assert.Equal(t, false, contact2.IsFavorite)

	// upsert contacts
	type upsertContact struct {
		UID             *string `json:"uid"`
		UserID          *string `json:"user_id"`
		PhoneNumber     *string `json:"phone_number"`
		Nickname        *string `json:"nickname"`
		Name            *string `json:"name"`
		Btc             *string `json:"btc"`
		Eth             *string `json:"eth"`
		Bsc             *string `json:"bsc"`
		Matic           *string `json:"matic"`
		Tron            *string `json:"tron"`
		Sol             *string `json:"sol"`
		Kcc             *string `json:"kcc"`
		Arb             *string `json:"arb"`
		Ronin           *string `json:"ronin"`
		Oasys           *string `json:"oasys"`
		IsManualAddress *bool   `json:"is_manual_address"`
		IsAddedByQR     *bool   `json:"is_added_by_qr"`
		IsFavorite      *bool   `json:"is_favorite"`
	}

	type upsertContactReq struct {
		Items []*upsertContact `json:"items"`
	}

	reqBody := upsertContactReq{
		Items: []*upsertContact{
			{
				// insert manually added contact
				Name:            util.Ptr("random name1"),
				Btc:             util.Ptr("random btc1"),
				Eth:             util.Ptr("random eth1"),
				Bsc:             util.Ptr("random bsc1"),
				Matic:           util.Ptr("random matic1"),
				Tron:            util.Ptr("random tron1"),
				Sol:             util.Ptr("random sol1"),
				Kcc:             util.Ptr("random kcc1"),
				Arb:             util.Ptr("random arb1"),
				Ronin:           util.Ptr("random ronin1"),
				Oasys:           util.Ptr("random oasys1"),
				IsManualAddress: util.Ptr(true),
				IsFavorite:      util.Ptr(true),
			},
			{
				// insert contact added by QR
				UserID:      util.Ptr(uid + "-friend2"),
				IsAddedByQR: util.Ptr(true),
			},
			{
				// insert contact added by phone number
				PhoneNumber: util.Ptr(util.RandPhone()),
			},
			{
				// update contact
				UID:             util.Ptr("uid1"),
				Name:            util.Ptr("new name1"),
				Btc:             util.Ptr("new btc1"),
				Eth:             util.Ptr("new eth1"),
				Bsc:             util.Ptr("new bsc1"),
				Matic:           util.Ptr("new matic1"),
				Tron:            util.Ptr("new tron1"),
				Sol:             util.Ptr("new sol1"),
				Kcc:             util.Ptr("new kcc1"),
				Arb:             util.Ptr("new arb1"),
				Ronin:           util.Ptr("new ronin1"),
				Oasys:           util.Ptr("new oasys1"),
				IsManualAddress: util.Ptr(true),
				IsAddedByQR:     util.Ptr(false),
			},
			{
				// update contact
				UID:        util.Ptr("uid3"),
				Nickname:   util.Ptr("new nickname3"),
				IsFavorite: util.Ptr(true),
			},
		},
	}

	reqBodyBytes, err := json.Marshal(reqBody)
	assert.Nil(t, err)
	req, err = http.NewRequest("PUT", "/v1/contacts", io.NopCloser(bytes.NewReader(reqBodyBytes)))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// list contacts
	req, err = http.NewRequest("GET", "/v1/contacts", nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, err = io.ReadAll(w.Body)
	assert.Nil(t, err)
	assert.Nil(t, json.Unmarshal(responseStr, &resp))
	assert.Equal(t, 0, resp.Code)
	assert.Equal(t, 4, len(resp.Data))
	sort.Slice(resp.Data, func(i, j int) bool {
		if len(resp.Data[i].UID) == len(resp.Data[j].UID) {
			return resp.Data[i].UID < resp.Data[j].UID
		}
		return len(resp.Data[i].UID) < len(resp.Data[j].UID)
	})
	contact0 = resp.Data[0]
	contact1 = resp.Data[1]
	contact2 = resp.Data[2]
	contact3 = resp.Data[3]
	assert.Equal(t, "uid1", contact0.UID)
	assert.Nil(t, contact0.UserID)
	assert.Equal(t, "new name1", contact0.Name)
	assert.Equal(t, "new btc1", contact0.Btc)
	assert.Equal(t, "new eth1", contact0.Eth)
	assert.Equal(t, "new bsc1", contact0.Bsc)
	assert.Equal(t, "new matic1", contact0.Matic)
	assert.Equal(t, "new tron1", contact0.Tron)
	assert.Equal(t, "new sol1", contact0.Sol)
	assert.Equal(t, "new kcc1", contact0.Kcc)
	assert.Equal(t, "new arb1", contact0.Arb)
	assert.Equal(t, "new ronin1", contact0.Ronin)
	assert.Equal(t, "new oasys1", contact0.Oasys)
	assert.Equal(t, true, *contact0.IsManualAddress)
	assert.Equal(t, false, contact0.IsFavorite)
	assert.Equal(t, "uid2", contact1.UID)
	assert.Equal(t, friendUID, *contact1.UserID)
	assert.Equal(t, "******************************************", contact1.Btc)
	assert.Equal(t, "******************************************", contact1.Eth)
	assert.Equal(t, "******************************************", contact1.Bsc)
	assert.Equal(t, "******************************************", contact1.Matic)
	assert.Equal(t, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", contact1.Tron)
	assert.Equal(t, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7", contact1.Sol)
	assert.Equal(t, "******************************************", contact1.Kcc)
	assert.Equal(t, "******************************************", contact1.Arb)
	assert.Equal(t, "******************************************", contact1.Ronin)
	assert.Equal(t, "******************************************", contact1.Oasys)
	assert.Equal(t, true, *contact1.IsAddedByQR)
	assert.Equal(t, true, contact1.IsFavorite)
	assert.Equal(t, "uid3", contact2.UID)
	assert.Equal(t, friend2UID, *contact2.UserID)
	assert.Equal(t, "new nickname3", *contact2.Nickname)
	assert.Equal(t, "******************************************", contact2.Btc)
	assert.Equal(t, "******************************************", contact2.Eth)
	assert.Equal(t, "******************************************", contact2.Bsc)
	assert.Equal(t, "******************************************", contact2.Matic)
	assert.Equal(t, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", contact2.Tron)
	assert.Equal(t, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7", contact2.Sol)
	assert.Equal(t, "******************************************", contact2.Kcc)
	assert.Equal(t, "******************************************", contact2.Arb)
	assert.Equal(t, "******************************************", contact2.Ronin)
	assert.Equal(t, "******************************************", contact2.Oasys)
	assert.Equal(t, true, contact2.IsFavorite)
	assert.Equal(t, 36, len(contact3.UID)) // uid is generated
	assert.Nil(t, contact3.UserID)
	assert.Equal(t, "random name1", contact3.Name)
	assert.Equal(t, "random btc1", contact3.Btc)
	assert.Equal(t, "random eth1", contact3.Eth)
	assert.Equal(t, "random bsc1", contact3.Bsc)
	assert.Equal(t, "random matic1", contact3.Matic)
	assert.Equal(t, "random tron1", contact3.Tron)
	assert.Equal(t, "random sol1", contact3.Sol)
	assert.Equal(t, "random kcc1", contact3.Kcc)
	assert.Equal(t, "random arb1", contact3.Arb)
	assert.Equal(t, "random ronin1", contact3.Ronin)
	assert.Equal(t, "random oasys1", contact3.Oasys)
	assert.Equal(t, true, *contact3.IsManualAddress)
	assert.Equal(t, true, contact3.IsFavorite)
}
