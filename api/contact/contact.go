package contact

import (
	"encoding/json"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/contact"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	lo "github.com/samber/lo"
)

type contactItem struct {
	UID             string                  `json:"uid"`
	UserID          *string                 `json:"user_id"`
	PhoneNumber     *string                 `json:"phone_number"`
	Nickname        *string                 `json:"nickname"`
	Name            string                  `json:"name"`
	Handle          *string                 `json:"handle"`
	Optimism        string                  `json:"optimism"`
	AvatarURL       *string                 `json:"avatar_url"`
	IsManualAddress *bool                   `json:"is_manual_address"`
	IsAddedByQR     *bool                   `json:"is_added_by_qr"`
	IsFavorite      bool                    `json:"is_favorite"`
	Addresses       map[domain.Chain]string `json:"-"`
}

func (c contactItem) MarshalJSON() ([]byte, error) {
	type Alias contactItem
	m := make(map[string]interface{})

	// Marshal base fields
	aliasBytes, err := json.Marshal(Alias(c))
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(aliasBytes, &m); err != nil {
		return nil, err
	}

	// Add addresses using chain IDs as keys
	for chain, addr := range c.Addresses {
		m[chain.ID()] = addr
	}

	// Add empty addresses for remaining chains
	for _, chain := range domain.ChainsMainnet {
		if _, exists := m[chain.ID()]; !exists {
			m[chain.ID()] = ""
		}
	}

	return json.Marshal(m)
}

func (c *contactItem) fromService(contact *domain.Contact) {
	c.UID = contact.UID
	c.UserID = contact.UserID
	c.PhoneNumber = contact.PhoneNumber
	c.Nickname = contact.Nickname
	c.Handle = contact.Handle
	c.Name = util.Val(contact.Name)
	c.Addresses = contact.Addresses
	c.AvatarURL = contact.AvatarURL
	c.IsManualAddress = contact.IsManualAddress
	c.IsAddedByQR = contact.IsAddedByQR
	c.IsFavorite = contact.IsFavorite
}

// ListContacts is the handler of GET /v1/contacts
func ListContacts(c *gin.Context) {
	ownerID := c.GetString("uid")
	if ownerID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "uid is empty")
		return
	}

	contacts, kgErr := contact.ListContacts(c.Request.Context(), &contact.ListContactsParams{
		OwnerID: ownerID,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	vContact := make([]contactItem, len(contacts))
	for i, contact := range contacts {
		vContact[i].fromService(contact)
	}

	response.OK(c, vContact)
}

type upsertContactItem struct {
	UID             *string                  `json:"uid"`
	UserID          *string                  `json:"user_id"`
	PhoneNumber     *string                  `json:"phone_number"`
	Nickname        *string                  `json:"nickname"`
	Name            *string                  `json:"name"`
	IsManualAddress *bool                    `json:"is_manual_address"`
	IsAddedByQR     *bool                    `json:"is_added_by_qr"`
	IsFavorite      *bool                    `json:"is_favorite"`
	Addresses       map[domain.Chain]*string `json:"-"`
}

func (i *upsertContactItem) UnmarshalJSON(data []byte) error {
	type Alias upsertContactItem
	aux := &struct {
		*Alias
		RawAddresses map[string]*string `json:"-"`
	}{
		Alias: (*Alias)(i),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// Create a map to store all fields from json
	var raw map[string]interface{}
	if err := json.Unmarshal(data, &raw); err != nil {
		return err
	}

	// Initialize addresses map
	i.Addresses = make(map[domain.Chain]*string)

	// Copy all fields that could be chain IDs
	for key, value := range raw {
		if chain := domain.IDToChain(key); chain != nil {
			if str, ok := value.(string); ok {
				i.Addresses[chain] = &str
			} else if strPtr, ok := value.(*string); ok {
				i.Addresses[chain] = strPtr
			}
		}
	}

	return nil
}

type upsertContactRequest struct {
	Items []*upsertContactItem `json:"items"`
}

// UpsertContacts is the handler of PUT /v1/contacts
func UpsertContacts(c *gin.Context) {
	var req upsertContactRequest
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	ownerID := c.GetString("uid")
	if ownerID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "uid is empty")
		return
	}

	params := &contact.UpsertContactsParams{
		OwnerID: ownerID,
	}

	items := lo.Map(req.Items, func(item *upsertContactItem, _ int) *contact.UpsertContactItem {
		addresses := make(map[domain.Chain]string)
		for chain, addr := range item.Addresses {
			if chain != nil && addr != nil {
				addresses[chain] = *addr
			}
		}

		return &contact.UpsertContactItem{
			UID:             item.UID,
			UserID:          item.UserID,
			PhoneNumber:     item.PhoneNumber,
			Nickname:        item.Nickname,
			Name:            item.Name,
			Addresses:       addresses,
			IsManualAddress: item.IsManualAddress,
			IsAddedByQR:     item.IsAddedByQR,
			IsFavorite:      item.IsFavorite,
		}
	})
	params.Items = items

	contacts, kgErr := contact.UpsertContacts(c.Request.Context(), params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	vContact := make([]contactItem, len(contacts))
	for i, contact := range contacts {
		vContact[i].fromService(contact)
	}

	response.OK(c, vContact)
}

// DeleteContact is the handler of DELETE /v1/contacts/:contact_id
func DeleteContact(c *gin.Context) {
	ownerID := c.GetString("uid")
	if ownerID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "uid is empty")
		return
	}
	contactID := c.Param("contact_id")
	if contactID == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "contact_id is empty")
		return
	}
	kgErr := contact.DeleteContact(c.Request.Context(), ownerID, contactID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}
