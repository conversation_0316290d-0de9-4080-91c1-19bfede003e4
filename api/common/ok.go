package common

import (
	"encoding/base64"
	"net/http"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/ip"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

const faviconData = "AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP9zegD/cl4A5XdFANZ7OwDUfjoI1n06MNZ7OjHWejcI2H08ANJ+SQDAfmQAq3h1AAAAAAAAAAAAAAAAANd6OQDfeT4A3no/ANp6PAC8fSgB03s5M9R7OqHVezqi1Ho5NNNxKALReTwA0Xs+ANJ7PgDVezkAAAAAANV8OQDWezkA1Xo4ANN6NgD/ef8A03o4JtR6OZnVezrv1Xs68NV6OpvWezgnAAD/ANd8NwDWezgA1Xs5ANV7OQDSezkA1Xs5ANd7OQDLfzsA1no5HdV6OYXVezrn1Xs6/9V7Ov/Vezro1ns5idZ8OR/dgDkA1Xs5ANV7OQDVejkA0Ho5ANF8OQDTdTsA1ns5FdZ7OnLVezrf1Xs6/9V7Ov/Vezr/1Xs6/9V7OuHVezl51Xs5GNZ4OQDVejgA1Hk4AMp2OADdezgA03s5D9V7OmDVezrV1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr/1ns52dV7OWjVejkR0n08ANF2NgDSfDwAz3k5CdN6OlHVejrK1Xs6/dV7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov7VejrO1Xo5VtN5OAvZfDkAznM3BtR6OULUezq91Xs6+tV7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6+9V7OsHUejlFzng2BtN5OkDVezqs1Xs69tV7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr31Xs6r9R6OUHVezql1Xs66tV7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7OuvVezqm1Hs6i9V7OuLVezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezri1ns6i9V7OC3Vezmf1Xs69NV7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr01Xs5n9Z7OC3ZdjAD1Xo4PtV7OcDVezr81Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr/1Xs6/9V7Ov/Vezr81Xs5wNV6OD7XdjAD14A7ANR6OAvVezpc1ns60NV7OvLVezr21Xs6/NV7Ov/Vezr/1Xs6+9V7OvbVezry1ns60NV7OlzUejgL14A7ANR6OQDWcjQA1Xs6FNV7OVzVezmM1Xs6otV7ObfVezrI1Xs6xtV7ObPVezmg1Xs5jNV7OV3VezkV1nQyANR5OQDVezkA1Xs5ANR3NwHVejkM1Xs5GNV7Oh/Vezkl1Xs6KtV7OinVezkj1Xs5HtV7ORjVezgN1Ho1AdV8OQDVezkAwAMAAIABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=="

var favicon []byte

func init() {
	favicon, _ = base64.StdEncoding.DecodeString(faviconData)
}

// GetOk response ok
func GetOk(c *gin.Context) {
	c.String(http.StatusOK, "ok")
}

// FavIcon FavIcon for browser
func FavIcon(c *gin.Context) {
	c.Header("Cache-Control", "max-age=315360000")
	c.Data(http.StatusOK, "image/x-icon", favicon)
}

// ServerIP Show Server IP
func ServerIP(c *gin.Context) {
	ip, err := ip.GetIPFromAkamai()
	if err != nil {
		kglog.Errorf("GetIPFromAkamai error: %v", err)
	}
	c.String(http.StatusOK, "server ip: %s", ip)
}

// Health Show services status
func Health(c *gin.Context) {
	projectID := config.GetString("PROJECT_ID")
	env := config.GetString("ENV")
	ip, _ := ip.GetIPFromAkamai()

	c.String(http.StatusOK, "Project ID: "+projectID+"\n"+
		"Env: "+env+"\n"+
		"Server IP: "+ip+"\n"+
		strings.Join(os.Environ(), "\n"))
}
