package balance

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/balance"
)

// BalancesSummaryV2Req is the request of BalancesSummaryV2
type BalancesSummaryV2Req struct {
	AssetTypeIDs    []string `form:"types"`
	ChainIDs        []string `form:"chain_ids"`
	ExcludeObserver bool     `form:"exclude_observer"`

	// parsed fields
	AssetTypes []domain.AssetType `form:"-"`
	Chains     []domain.Chain     `form:"-"`
}

func (r *BalancesSummaryV2Req) AfterBinding(c *gin.Context) error {
	var err error

	// parse and validate asset types, chain ids
	if r.AssetTypes, err = api.ParseAssetTypes(r.AssetTypeIDs); err != nil {
		return err
	}
	if r.Chains, err = api.ParseChainIDs(r.ChainIDs); err != nil {
		return err
	}
	return nil
}

// BalancesSummaryV2 returns the balances summary of the user
func BalancesSummaryV2(c *gin.Context) {
	ctx := c.Request.Context()
	req := &BalancesSummaryV2Req{}
	if kgErr := util.ToGinContextExt(c).BindQuery(req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	uid := auth.GetUID(c)

	resp, err := balance.Summary(ctx, uid, req.AssetTypes, req.Chains, req.ExcludeObserver)
	if err != nil {
		response.KGError(c, err)
		return
	}
	response.OK(c, resp)
}

// BalancesByPathReq is the request of BalancesByPath
type BalancesByPathReq struct {
	AssetTypeIDs []string `form:"types"`
	ChainIDs     []string `form:"chain_ids"`
	Path         string   `form:"path"`

	// parsed fields
	AssetTypes []domain.AssetType `form:"-"`
	Chains     []domain.Chain     `form:"-"`
}

func (r *BalancesByPathReq) AfterBinding(c *gin.Context) error {
	var err error

	// parse and validate asset types, chain ids
	if r.AssetTypes, err = api.ParseAssetTypes(r.AssetTypeIDs); err != nil {
		return err
	}
	if r.Chains, err = api.ParseChainIDs(r.ChainIDs); err != nil {
		return err
	}
	return nil
}

// BalancesByPath returns balances of a single path
func BalancesByPath(c *gin.Context) {
	ctx := c.Request.Context()
	req := &BalancesByPathReq{}
	if kgErr := util.ToGinContextExt(c).BindQuery(req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	uid := auth.GetUID(c)

	resp, err := balance.ByPath(ctx, uid, req.Path, req.Chains, req.AssetTypes)
	if err != nil {
		response.KGError(c, err)
		return
	}
	response.OK(c, resp)
}

// BalancesAllReq is the request of BalancesAll
type BalancesAllReq struct {
	ChainIDs []string `form:"chain_ids"`

	// parsed fields
	Chains []domain.Chain `form:"-"`
}

func (r *BalancesAllReq) AfterBinding(c *gin.Context) error {
	var err error
	if r.Chains, err = api.ParseChainIDs(r.ChainIDs); err != nil {
		return err
	}
	return nil
}

// BalancesAll returns balances of each path+chain+asset_type pair
func BalancesAll(c *gin.Context) {
	ctx := c.Request.Context()
	req := &BalancesAllReq{}
	if kgErr := util.ToGinContextExt(c).BindQuery(req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	uid := auth.GetUID(c)

	resp, err := balance.AllPaths(ctx, uid, req.Chains)
	if err != nil {
		response.KGError(c, err)
		return
	}
	response.OK(c, resp)
}
