package balance

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/suite"
)

type JSONMap = map[string]interface{}

// BalanceTestSuite is the test suite for the Balance API
type BalanceTestSuite struct {
	suite.Suite
}

func (suite *BalanceTestSuite) SetupTest() {
	rdb.Reset()
	suite.Nil(rdb.CreateSeedData())
	repo := rdb.GormRepo()
	oauth.Init(repo)
	application.Init(repo)
}

func TestGetBalanceSuite(t *testing.T) {
	suite.Run(t, new(BalanceTestSuite))
}

func (s *BalanceTestSuite) TestGetBalancesUserNotExist() {
	url := "/balances"
	server := gin.Default()
	server.GET(url, Balances)

	req, err := http.NewRequest("GET", url, nil)
	s.Nil(err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	s.Equal(http.StatusBadRequest, w.Code)
	var responseBody JSONMap
	err = json.Unmarshal(w.Body.Bytes(), &responseBody)
	s.Nil(err)
	s.Equal("user does not exist", responseBody["message"])
}

func (s *BalanceTestSuite) TestGetBalancesNoAssetType() {
	users, uid, _, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	s.Nil(err)

	// Get all balances with path not specified
	url := "/balances"
	server := gin.Default()
	server.GET(url, auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}), Balances)

	// GET request with header having KG-DEV-UID
	fullUrl := url + "?path=&get_all=true&no_cache=true&chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=bsc&chain_ids=kcc&chain_ids=ronin&chain_ids=oasys&chain_ids=btc&chain_ids=sol&chain_ids=tron"
	req, err := http.NewRequest("GET", fullUrl, nil)
	s.Nil(err)
	req.Header.Set("KG-WALLET-TOKEN", "KG-DEV:123456")
	req.Header.Set("KG-DEV-UID", uid)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var responseBody JSONMap
	err = json.Unmarshal(w.Body.Bytes(), &responseBody)
	s.Nil(err)

	// The following is used to write the response body to a json file for debugging purposes (Use it to cross check the response)
	// Uncomment the following lines to update the file when you need to update the expected response
	// os.WriteFile("test/get_balance_no_asset_type.json", w.Body.Bytes(), 0644)

	balances, ok := responseBody["data"].([]interface{})
	s.True(ok, "Balances should be a slice")
	s.Equal(15, len(balances), "Expected 15 paths")

	// All balances should be empty
	for _, balance := range balances {
		balanceMap, ok := balance.(map[string]interface{})
		s.True(ok, "Balance should be a map")
		s.Equal(0, len(balanceMap["balance"].([]interface{})), "Expected 0 balances")
	}
}

func (s *BalanceTestSuite) TestGetBalancesNoChainIDs() {
	users, uid, _, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	s.Nil(err)

	// Get all balances with path not specified
	url := "/balances"
	server := gin.Default()
	server.GET(url, auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}), Balances)

	// GET request with header having KG-DEV-UID
	fullUrl := url + "?path=&get_all=true&no_cache=true&types=token&types=nft&types=defi"
	req, err := http.NewRequest("GET", fullUrl, nil)
	s.Nil(err)
	req.Header.Set("KG-WALLET-TOKEN", "KG-DEV:123456")
	req.Header.Set("KG-DEV-UID", uid)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)
	var responseBody JSONMap
	err = json.Unmarshal(w.Body.Bytes(), &responseBody)
	s.Nil(err)

	// The following is used to write the response body to a json file for debugging purposes (Use it to cross check the response)
	// Uncomment the following lines to update the file when you need to update the expected response
	// os.WriteFile("test/get_balance_no_chain_ids.json", w.Body.Bytes(), 0644)

	balances, ok := responseBody["data"].([]interface{})
	s.True(ok, "Balances should be a slice")
	s.Equal(15, len(balances), "Expected 15 paths")

	// All balances should be empty
	for _, balance := range balances {
		balanceMap, ok := balance.(map[string]interface{})
		s.True(ok, "Balance should be a map")
		s.Equal(0, len(balanceMap["balance"].([]interface{})), "Expected 0 balances")
	}
}

func (s *BalanceTestSuite) TestGetAllBalances() {
	users, uid, _, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	s.Nil(err)

	// Get all balances with path not specified
	url := "/balances"
	server := gin.Default()
	server.GET(url, auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}), Balances)

	// GET request with header having KG-DEV-UID
	fullUrl := url + "?path=&get_all=true&no_cache=true&types=token&types=nft&types=defi&chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=bsc&chain_ids=kcc&chain_ids=ronin&chain_ids=oasys&chain_ids=btc&chain_ids=sol&chain_ids=tron"
	req, err := http.NewRequest("GET", fullUrl, nil)
	s.Nil(err)
	req.Header.Set("KG-WALLET-TOKEN", "KG-DEV:123456")
	req.Header.Set("KG-DEV-UID", uid)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)

	var responseBody JSONMap
	err = json.Unmarshal(w.Body.Bytes(), &responseBody)
	s.Nil(err)

	// The following is used to write the response body to a json file for debugging purposes (Use it to cross check the response)
	// Uncomment the following lines to update the file when you need to update the expected response
	// os.WriteFile("test/get_all_balances.json", w.Body.Bytes(), 0644)

	balances, ok := responseBody["data"].([]interface{})
	s.True(ok, "Balances should be a slice")
	s.Equal(15, len(balances), "Expected 15 paths")

	// Check path names
	expectedPaths := []string{"evm-0", "evm-1", "group-0", "group-0-btc-0", "group-0-btc-1", "group-0-evm-0", "group-0-evm-1", "group-0-evm-2", "group-0-sol-0", "group-0-tron-0", "group-0-tron-1", "group-0-tron-2", "group-0-tron-3", "group-0-tron-4", "group-0-tron-5"}
	for i, balance := range balances {
		balanceMap, ok := balance.(map[string]interface{})
		s.True(ok, "Balance should be a map")
		s.Equal(expectedPaths[i], balanceMap["path"], "Path name incorrect")
	}

	// Check balances
	evm0, ok := balances[0].(map[string]interface{})
	s.True(ok, "Balance should be a map")
	s.Equal(0, len(evm0["balance"].([]interface{})), "Expected 0 balances for evm-0")

	// group-0
	_, ok = balances[2].(map[string]interface{})
	s.True(ok, "Balance should be a map")

	// group-0-btc-0
	group0Btc0, ok := balances[3].(map[string]interface{})
	s.True(ok, "Balance should be a map")
	balance := group0Btc0["balance"].([]interface{})[0].(map[string]interface{})
	s.Equal("btc", balance["chain_id"], "Chain ID incorrect")
	s.Equal("token", balance["asset_type"], "Asset type incorrect")
	s.Equal("******************************************", balance["wallet_address"], "Wallet address incorrect")
	s.Equal(39999600.0, balance["usd_value"], "USD value incorrect")
	s.Equal(0.0, balance["usd_value_24h_ago"], "USD value 24h ago incorrect")

	// group-0-evm-0
	group0Evm0, ok := balances[5].(map[string]interface{})
	s.True(ok, "Balance should be a map")
	balance = group0Evm0["balance"].([]interface{})[0].(map[string]interface{})
	s.Equal("bsc", balance["chain_id"], "Chain ID incorrect")
	s.Equal("token", balance["asset_type"], "Asset type incorrect")
	s.Equal("******************************************", balance["wallet_address"], "Wallet address incorrect")
	s.Equal(54.3, balance["usd_value"], "USD value incorrect")
	s.Equal(0.0, balance["usd_value_24h_ago"], "USD value 24h ago incorrect")

	// group-0-evm-1
	group0Evm1, ok := balances[6].(map[string]interface{})
	s.True(ok, "Balance should be a map")
	tokenCnt := 0
	nftCnt := 0
	defiCnt := 0
	for _, balance := range group0Evm1["balance"].([]interface{}) {
		balanceData := balance.(map[string]interface{})
		switch balanceData["asset_type"] {
		case "token":
			tokenCnt++
			s.Equal("eth", balanceData["chain_id"], "Chain ID incorrect")
			s.Equal("token", balanceData["asset_type"], "Asset type incorrect")
			s.Equal("******************************************", balanceData["wallet_address"], "Wallet address incorrect")
			s.Equal(108.667037151, balanceData["usd_value"], "USD value incorrect")
			s.Equal(0.0, balanceData["usd_value_24h_ago"], "USD value 24h ago incorrect")
		case "nft":
			nftCnt++
			s.Equal("eth", balanceData["chain_id"], "Chain ID incorrect")
			s.Equal("nft", balanceData["asset_type"], "Asset type incorrect")
			s.Equal("******************************************", balanceData["wallet_address"], "Wallet address incorrect")
			s.Equal(14244.0, balanceData["usd_value"], "USD value incorrect")
			s.Equal(0.0, balanceData["usd_value_24h_ago"], "USD value 24h ago incorrect")
		case "defi":
			defiCnt++
			s.Equal("arb", balanceData["chain_id"], "Chain ID incorrect")
			s.Equal("defi", balanceData["asset_type"], "Asset type incorrect")
			s.Equal("******************************************", balanceData["wallet_address"], "Wallet address incorrect")
			s.Equal(27.233, balanceData["usd_value"], "USD value incorrect")
			s.Equal(0.0, balanceData["usd_value_24h_ago"], "USD value 24h ago incorrect")
		}
	}
	s.Equal(1, tokenCnt, "Expected 1 token balance")
	s.Equal(1, nftCnt, "Expected 1 nft balance")
	s.Equal(1, defiCnt, "Expected 1 defi balance")

	// group-0-sol-0
	group0Sol0, ok := balances[8].(map[string]interface{})
	s.True(ok, "Balance should be a map")
	balance = group0Sol0["balance"].([]interface{})[0].(map[string]interface{})
	s.Equal("sol", balance["chain_id"], "Chain ID incorrect")
	s.Equal("token", balance["asset_type"], "Asset type incorrect")
	s.Equal("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7", balance["wallet_address"], "Wallet address incorrect")
	s.Equal(300.0, balance["usd_value"], "USD value incorrect")
	s.Equal(0.0, balance["usd_value_24h_ago"], "USD value 24h ago incorrect")

	// group-0-tron-0
	group0Tron0, ok := balances[9].(map[string]interface{})
	s.True(ok, "Balance should be a map")
	balance = group0Tron0["balance"].([]interface{})[0].(map[string]interface{})
	s.Equal("tron", balance["chain_id"], "Chain ID incorrect")
	s.Equal("token", balance["asset_type"], "Asset type incorrect")
	s.Equal("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", balance["wallet_address"], "Wallet address incorrect")
	s.Equal(500.0, balance["usd_value"], "USD value incorrect")
	s.Equal(0.0, balance["usd_value_24h_ago"], "USD value 24h ago incorrect")
}

func (s *BalanceTestSuite) TestGetBalancesByPath() {
	users, uid, _, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	s.Nil(err)

	// Get balance with specified path group-0-evm-0
	url := "/balances"
	server := gin.Default()
	server.GET(url, auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}), Balances)

	// GET request with header having KG-DEV-UID
	fullURL := url + "?path=group-0-evm-0&no_cache=true&types=token&types=nft&types=defi&chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=bsc&chain_ids=kcc&chain_ids=ronin&chain_ids=oasys&chain_ids=btc&chain_ids=sol&chain_ids=tron"
	req, err := http.NewRequest("GET", fullURL, nil)
	s.Nil(err)
	req.Header.Set("KG-WALLET-TOKEN", "KG-DEV:123456")
	req.Header.Set("KG-DEV-UID", uid)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)

	var responseBody JSONMap
	err = json.Unmarshal(w.Body.Bytes(), &responseBody)
	s.Nil(err)

	// The following is used to write the response body to a json file for debugging purposes (Use it to cross check the response)
	// Uncomment the following lines to update the file when you need to update the expected response
	// os.WriteFile("test/get_balance_by_path.json", w.Body.Bytes(), 0644)

	balances, ok := responseBody["data"].([]interface{})
	s.True(ok, "Balances should be a slice")
	s.Equal(1, len(balances), "Expected only 1 asset")

	// Check balance data
	balance := balances[0].(map[string]interface{})
	s.Equal("bsc", balance["chain_id"], "Chain ID incorrect")
	s.Equal("token", balance["asset_type"], "Asset type incorrect")
	s.Equal(54.3, balance["usd_value"], "USD value incorrect")
	s.Equal(0.0, balance["usd_value_24h_ago"], "USD value 24h ago incorrect")
}

func (s *BalanceTestSuite) TestGetBalancesByAssetType() {
	users, uid, _, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	s.Nil(err)

	// Get balance with specified path group-0-evm-0
	url := "/balances"
	server := gin.Default()
	server.GET(url, auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}), Balances)

	// GET request with header having KG-DEV-UID
	fullURL := url + "?path=&get_all=true&types=nft&no_cache=true&chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=bsc&chain_ids=kcc&chain_ids=ronin&chain_ids=oasys&chain_ids=btc&chain_ids=sol&chain_ids=tron"
	req, err := http.NewRequest("GET", fullURL, nil)
	s.Nil(err)
	req.Header.Set("KG-WALLET-TOKEN", "KG-DEV:123456")
	req.Header.Set("KG-DEV-UID", uid)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)

	var responseBody JSONMap
	err = json.Unmarshal(w.Body.Bytes(), &responseBody)
	s.Nil(err)

	// The following is used to write the response body to a json file for debugging purposes (Use it to cross check the response)
	// Uncomment the following lines to update the file when you need to update the expected response
	// os.WriteFile("test/get_balance_by_asset_type.json", w.Body.Bytes(), 0644)

	balances, ok := responseBody["data"].([]interface{})
	s.True(ok, "Balances should be a slice")
	s.Equal(15, len(balances), "Expected 15 paths")

	// Check balance data
	balance := balances[6].(map[string]interface{})
	s.Equal("group-0-evm-1", balance["path"], "Path name incorrect")
	balanceData := balance["balance"].([]interface{})[0].(map[string]interface{})
	s.Equal("eth", balanceData["chain_id"], "Chain ID incorrect")
	s.Equal("nft", balanceData["asset_type"], "Asset type incorrect")
	s.Equal("******************************************", balanceData["wallet_address"], "Wallet address incorrect")
	s.Equal(14244.0, balanceData["usd_value"], "USD value incorrect")
	s.Equal(0.0, balanceData["usd_value_24h_ago"], "USD value 24h ago incorrect")
}

func (s *BalanceTestSuite) TestGetBalancesByPathAndChainIDs() {
	users, uid, _, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	s.Nil(err)

	// Get balance with specified path group-0-evm-0
	url := "/balances"
	server := gin.Default()
	server.GET(url, auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead}), Balances)

	// GET request with header having KG-DEV-UID
	fullURL := url + "?path=group-0-evm-1&chain_ids=arb&no_cache=true&types=token&types=nft&types=defi"
	req, err := http.NewRequest("GET", fullURL, nil)
	s.Nil(err)
	req.Header.Set("KG-WALLET-TOKEN", "KG-DEV:123456")
	req.Header.Set("KG-DEV-UID", uid)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	s.Equal(http.StatusOK, w.Code)

	var responseBody JSONMap
	err = json.Unmarshal(w.Body.Bytes(), &responseBody)
	s.Nil(err)

	// The following is used to write the response body to a json file for debugging purposes (Use it to cross check the response)
	// Uncomment the following lines to update the file when you need to update the expected response
	// os.WriteFile("test/get_balance_by_path_and_chain_id.json", w.Body.Bytes(), 0644)

	balances, ok := responseBody["data"].([]interface{})
	s.True(ok, "Balances should be a slice")
	s.Equal(1, len(balances), "Expected only 1 asset")

	// Check balance data
	balance := balances[0].(map[string]interface{})
	s.Equal("arb", balance["chain_id"], "Chain ID incorrect")
	s.Equal("defi", balance["asset_type"], "Asset type incorrect")
	s.Equal(27.233, balance["usd_value"], "USD value incorrect")
	s.Equal(0.0, balance["usd_value_24h_ago"], "USD value 24h ago incorrect")
}
