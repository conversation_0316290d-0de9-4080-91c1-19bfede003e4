package balance

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
)

type balancesResp struct {
	Code int             `json:"code"`
	Data []*rdb.VBalance `json:"data"`
}

type singleBalance struct {
	Path string          `json:"path"`
	Data []*rdb.VBalance `json:"balance"`
}

type multipleBalancesResp struct {
	Code int              `json:"code"`
	Data []*singleBalance `json:"data"`
}

// Retrieve results from cache
//
// Cache concept:
//
// 1. We store the balance of each address in cache, with all paths, asset types, and chain ids
//
// 2. If cache presents, use the result from cache. Otherwise, query with all paths, asset types, and chain_ids from db
//
// 3. We filter the results by the current query params and return the result
func getBalancesFromCache(ctx context.Context, walletAddresses []string) ([]*rdb.VBalance, []string) {
	cacheBalancesRaw := cache.HMGet(ctx, cache.BalancesHKey, walletAddresses)
	balancesFromCache := make([]*rdb.VBalance, 0)
	addressesWithNoCache := make([]string, 0)
	for i, cacheBalanceRaw := range cacheBalancesRaw {
		if cacheBalanceRaw == nil {
			addressesWithNoCache = append(addressesWithNoCache, walletAddresses[i])
		} else {
			balanceStr := cacheBalanceRaw.(string)
			curBalance := make([]*rdb.VBalance, 0)
			err := json.Unmarshal([]byte(balanceStr), &curBalance)
			if err != nil {
				kglog.Errorf("[Balances] unmarshal balance error: %v", err)
				addressesWithNoCache = append(addressesWithNoCache, walletAddresses[i])
			} else {
				balancesFromCache = append(balancesFromCache, curBalance...)
			}
		}
	}

	return balancesFromCache, addressesWithNoCache
}

func getBalancesFromDB(ctx context.Context, addressesWithNoCache []string) (*[]*rdb.VBalance, int, error) {
	balancesFromDB, code, err := rdb.GetBalances(ctx, &rdb.BalancesParams{
		AssetTypes:      dbmodel.AllAssetTypes,
		ChainIDs:        dbmodel.DefaultChainOrder,
		WalletAddresses: addressesWithNoCache,
	})
	if err != nil {
		return nil, code, err
	}

	pastBalances, code, err := rdb.GetPastBalances(ctx, &rdb.PastBalancesParams{
		WalletAddresses: addressesWithNoCache,
		AssetTypes:      dbmodel.AllAssetTypes,
		From:            time.Now().Add(-24 * time.Hour),
	})
	if err != nil {
		return nil, code, err
	}

	for _, balance := range balancesFromDB {
		key := fmt.Sprintf("%s;%s;%s", balance.ChainID, balance.AssetType, balance.WalletAddress)
		balance.UsdValue24hAgo = pastBalances[key]
	}

	return &balancesFromDB, 0, nil
}

// Balances returns the balances of the specified path, asset_types, and chain_ids of the user
//
// If path is not specified, it'll return all paths' data of the user
func Balances(ctx *gin.Context) {
	params := &rdb.BalancesParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	for _, assetType := range params.AssetTypes {
		if _, ok := dbmodel.AssetTypesSet[assetType]; !ok {
			response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid type")
			return
		}
	}

	for _, chainID := range params.ChainIDs {
		if _, ok := dbmodel.ChainIDSet[chainID]; !ok {
			response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid chain_id")
			return
		}
	}

	if params.GetAll && params.Path != "" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "cannot specify path when get_all is true")
		return
	}

	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if user == nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "user does not exist")
		return
	}

	pathAddresses := user.AddressByPath(params.Path, params.ExcludeObserver)
	walletAddresses := make([]string, 0, len(pathAddresses))
	for _, addr := range pathAddresses {
		walletAddresses = append(walletAddresses, addr.String())
	}

	if params.NoCache {
		cache.HDel(cache.BalancesHKey, walletAddresses)
	}

	balancesFromCache, addressesWithNoCache := getBalancesFromCache(ctx.Request.Context(), walletAddresses)
	balancesFromDB, code, err := getBalancesFromDB(ctx.Request.Context(), addressesWithNoCache)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	// Construct full data and set cache
	allBalances := append(balancesFromCache, *balancesFromDB...)
	allData := []*singleBalance{}
	dataToSetCache := make(map[string]interface{})
	for path, address := range pathAddresses {
		singleBalance := &singleBalance{
			Path: path,
			Data: []*rdb.VBalance{},
		}
		for _, balance := range allBalances {
			if strings.EqualFold(balance.WalletAddress, address.String()) {
				singleBalance.Data = append(singleBalance.Data, balance)
			}
		}

		// If address is in addressesWithNoCache, we set the result to cache
		if lo.Contains(addressesWithNoCache, address.String()) {
			dataToSetCache[address.String()] = util.ToJSONString(singleBalance.Data)
		}
		allData = append(allData, singleBalance)
	}
	err = cache.HMSet(ctx.Request.Context(), cache.BalancesHKey, dataToSetCache)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx.Request.Context(), "[Balances] set cache error", map[string]interface{}{
			"error":          err.Error(),
			"dataToSetCache": dataToSetCache,
		})
	}

	// Finally, we filter the results by the current query params and return the result
	if params.GetAll {
		resp := new(multipleBalancesResp)
		resp.Code = 0

		// Filter by asset types and chain ids
		filteredBalances := make([]*singleBalance, 0)
		groupBalances := make(map[string]*singleBalance)
		for _, sb := range allData {
			curBalance := new(singleBalance)
			curBalance.Path = sb.Path
			curBalance.Data = []*rdb.VBalance{}
			for _, b := range sb.Data {
				if lo.Contains(params.AssetTypes, b.AssetType) &&
					lo.Contains(params.ChainIDs, b.ChainID) {
					curBalance.Data = append(curBalance.Data, b)
				}
			}
			filteredBalances = append(filteredBalances, curBalance)

			if strings.HasPrefix(curBalance.Path, "group") {
				// group by group name
				pathParts := strings.Split(curBalance.Path, "-")
				if len(pathParts) >= 2 {
					groupName := "group-" + pathParts[1]
					if _, ok := groupBalances[groupName]; !ok {
						groupBalances[groupName] = &singleBalance{
							Path: groupName,
							Data: []*rdb.VBalance{},
						}
					}
					groupBalances[groupName].Data = append(groupBalances[groupName].Data, curBalance.Data...)
				}
			}
		}

		for _, groupBalance := range groupBalances {
			filteredBalances = append(filteredBalances, groupBalance)
		}

		for _, sb := range filteredBalances {
			// sort by usd value
			sort.SliceStable(sb.Data, func(i, j int) bool {
				return sb.Data[i].UsdValue > sb.Data[j].UsdValue
			})
		}

		// Sort data by alphabetical order of path name for better readability and consistency
		sort.SliceStable(filteredBalances, func(i, j int) bool {
			return (filteredBalances)[i].Path < (filteredBalances)[j].Path
		})

		resp.Data = filteredBalances

		ctx.JSON(http.StatusOK, resp)
	} else {
		resp := new(balancesResp)
		resp.Code = 0
		aggregatedBalances := make(map[string]*rdb.VBalance)

		for _, sb := range allData {
			for _, b := range sb.Data {
				if lo.Contains(params.AssetTypes, b.AssetType) &&
					lo.Contains(params.ChainIDs, b.ChainID) {
					key := fmt.Sprintf("%s;%s", b.ChainID, b.AssetType)
					if _, ok := aggregatedBalances[key]; !ok {
						aggregatedBalances[key] = &rdb.VBalance{
							ChainID:        b.ChainID,
							AssetType:      b.AssetType,
							UsdValue:       0,
							UsdValue24hAgo: 0,
						}
					}
					aggregatedBalances[key].UsdValue += b.UsdValue
					aggregatedBalances[key].UsdValue24hAgo += b.UsdValue24hAgo
				}
			}
		}

		filteredBalances := lo.MapToSlice(aggregatedBalances, func(_ string, v *rdb.VBalance) *rdb.VBalance {
			return v
		})

		sort.SliceStable(filteredBalances, func(i, j int) bool {
			return filteredBalances[i].UsdValue > filteredBalances[j].UsdValue
		})

		resp.Data = filteredBalances

		ctx.JSON(http.StatusOK, resp)
	}
}

type balancesSummaryResp struct {
	Code int              `json:"code"`
	Data *vBalanceSummary `json:"data"`
}

type vBalanceSummary struct {
	WalletGroups  []*vBalanceSummaryItem `json:"wallet_groups"`
	EvmWallets    []*vBalanceSummaryItem `json:"evm_wallets"`
	SingleWallets []*vBalanceSummaryItem `json:"single_wallets"`
	Total         vBalanceSummaryItem    `json:"total"`
}

type vBalanceSummaryItem struct {
	UsdValue       float64 `json:"usd_value"`
	UsdValue24hAgo float64 `json:"usd_value_24h_ago"`
}

// BalancesSummaryReq is the request of BalancesSummary
type BalancesSummaryReq struct {
	AssetTypes      []string `form:"types"`
	ChainIDs        []string `form:"chain_ids"`
	ExcludeObserver bool     `form:"exclude_observer"`
}

// BalancesSummary returns the balances summary of the user with the given phone number
func BalancesSummary(ctx *gin.Context) {
	req := &BalancesSummaryReq{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	for _, assetType := range req.AssetTypes {
		if _, ok := dbmodel.AssetTypesSet[assetType]; !ok {
			response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid type")
			return
		}
	}

	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if user == nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "user does not exist")
		return
	}

	walletAddresses := user.UniqueAddresses(req.ExcludeObserver)
	params := &rdb.BalancesSummaryParams{
		AssetTypes:      req.AssetTypes,
		ChainIDs:        req.ChainIDs,
		WalletAddresses: walletAddresses,
	}
	summary, code, err := rdb.GetBalancesSummary(ctx.Request.Context(), params)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	pastSummary, code, err := rdb.GetPastBalanceSummary(ctx.Request.Context(), &rdb.PastBalanceSummaryParams{
		AssetTypes:      params.AssetTypes,
		ChainIDs:        params.ChainIDs,
		WalletAddresses: params.WalletAddresses,
		From:            time.Now().Add(-24 * time.Hour),
	})
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}

	balancesSummary := mergeBalancesSummary(summary, pastSummary, user)

	resp := new(balancesSummaryResp)
	resp.Code = 0
	resp.Data = balancesSummary

	ctx.JSON(http.StatusOK, resp)
}

func mergeBalancesSummary(summary, pastSummary map[string]float64, user *domain.UserData) *vBalanceSummary {
	balancesSummary := &vBalanceSummary{}
	if user == nil || user.Wallets == nil {
		return balancesSummary
	}
	for _, wallet := range user.Wallets.EvmWallets {
		lowerAddress := strings.ToLower(wallet.Address)
		balancesSummary.EvmWallets = append(balancesSummary.EvmWallets, &vBalanceSummaryItem{
			UsdValue:       summary[lowerAddress],
			UsdValue24hAgo: pastSummary[lowerAddress],
		})
		balancesSummary.Total.UsdValue += summary[lowerAddress]
		balancesSummary.Total.UsdValue24hAgo += pastSummary[lowerAddress]
	}
	for _, walletGroup := range user.Wallets.WalletGroups {
		item := &vBalanceSummaryItem{}
		for _, wallet := range walletGroup.BtcWallets {
			lowerAddress := strings.ToLower(wallet.Address)
			item.UsdValue += summary[lowerAddress]
			item.UsdValue24hAgo += pastSummary[lowerAddress]
			balancesSummary.Total.UsdValue += summary[lowerAddress]
			balancesSummary.Total.UsdValue24hAgo += pastSummary[lowerAddress]
		}
		for _, wallet := range walletGroup.EvmWallets {
			lowerAddress := strings.ToLower(wallet.Address)
			item.UsdValue += summary[lowerAddress]
			item.UsdValue24hAgo += pastSummary[lowerAddress]
			balancesSummary.Total.UsdValue += summary[lowerAddress]
			balancesSummary.Total.UsdValue24hAgo += pastSummary[lowerAddress]
		}
		for _, wallet := range walletGroup.SolanaWallets {
			lowerAddress := strings.ToLower(wallet.Address)
			item.UsdValue += summary[lowerAddress]
			item.UsdValue24hAgo += pastSummary[lowerAddress]
			balancesSummary.Total.UsdValue += summary[lowerAddress]
			balancesSummary.Total.UsdValue24hAgo += pastSummary[lowerAddress]
		}
		for _, wallet := range walletGroup.TronWallets {
			lowerAddress := strings.ToLower(wallet.Address)
			item.UsdValue += summary[lowerAddress]
			item.UsdValue24hAgo += pastSummary[lowerAddress]
			balancesSummary.Total.UsdValue += summary[lowerAddress]
			balancesSummary.Total.UsdValue24hAgo += pastSummary[lowerAddress]
		}
		balancesSummary.WalletGroups = append(balancesSummary.WalletGroups, item)
	}
	for _, wallet := range user.Wallets.Wallets {
		lowerAddress := strings.ToLower(wallet.Address)
		balancesSummary.SingleWallets = append(balancesSummary.SingleWallets, &vBalanceSummaryItem{
			UsdValue:       summary[lowerAddress],
			UsdValue24hAgo: pastSummary[lowerAddress],
		})
		balancesSummary.Total.UsdValue += summary[lowerAddress]
		balancesSummary.Total.UsdValue24hAgo += pastSummary[lowerAddress]
	}
	return balancesSummary
}
