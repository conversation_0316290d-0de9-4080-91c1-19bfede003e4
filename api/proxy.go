package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/proxy"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

var openseaURL *url.URL

func init() {
	var err error
	openseaURL, err = url.Parse("https://api.opensea.io/")
	if err != nil {
		panic(err)
	}
}

// ProxyMetadata proxy pass to opensea
func ProxyMetadata(c *gin.Context) {
	path := strings.Split(c.Request.URL.Path, "/")
	authProxy := httputil.NewSingleHostReverseProxy(openseaURL)
	authProxy.Director = func(req *http.Request) {
		req.Header = c.Request.Header
		req.Host = openseaURL.Host
		req.URL.Scheme = openseaURL.Scheme
		req.URL.Host = openseaURL.Host
		req.URL.Path = "/api/v2/metadata/" + strings.Join(path[3:], "/")
		req.Body = c.Request.Body
	}
	authProxy.ServeHTTP(c.Writer, c.Request)
}

type proxy3rdPartyReq struct {
	Path   string            `json:"path" binding:"required"`
	Method string            `json:"method" binding:"required"`
	Header map[string]string `json:"header"`
	Body   interface{}       `json:"body"`
}

// Proxy3rdParty proxy pass to 3rd party
func Proxy3rdParty(ctx *gin.Context) {
	ctxSpan, span := tracing.Start(ctx.Request.Context(), "Proxy3rdParty")
	defer span.End()

	params := proxy3rdPartyReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	clientID := oauth.ClientID(ctx)

	// Validate the request
	if !proxy.IsRequestAllowed(params.Method, params.Path) {
		kglog.WarningWithDataCtx(ctxSpan, "Invalid request method/path combination", map[string]interface{}{
			"method": params.Method,
			"path":   params.Path,
		})
		response.KGError(ctx, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("invalid request method/path combination"), nil))
		return
	}

	resp, kgErr := proxy.NewRequestWithRetry(ctxSpan, &proxy.NewRequestParams{
		Path:     params.Path,
		Method:   params.Method,
		Body:     params.Body,
		ClientID: clientID,
		Header:   params.Header,
	})
	if kgErr != nil {
		kglog.WarningWithDataCtx(ctxSpan, "Failed to proxy request", map[string]interface{}{
			"err code":    kgErr.Code,
			"http status": kgErr.HttpStatus,
			"err":         kgErr.String(),
		})
		response.KGError(ctx, kgErr)
		return
	}
	var respBody interface{}
	if resp.StatusCode() != http.StatusOK {
		kglog.WarningWithDataCtx(ctxSpan, "3rd proxy, response status not ok", map[string]interface{}{
			"status": resp.StatusCode(),
			"body":   string(resp.Body()),
		})
		// if response status is not ok, replace the response body with the error to avoid leaking sensitive data
		respBody = map[string]interface{}{
			"code":    resp.StatusCode(),
			"message": http.StatusText(resp.StatusCode()),
		}
	} else {
		err := json.Unmarshal(resp.Body(), &respBody)
		if err != nil {
			kglog.WarningWithDataCtx(ctxSpan, "Failed to unmarshal response body", map[string]interface{}{
				"body": string(resp.Body()),
				"err":  err.Error(),
			})
			response.KGError(ctx, code.NewKGError(code.JSONError, http.StatusInternalServerError, err, nil))
			return
		}
	}
	ctx.JSON(resp.StatusCode(), respBody)
}
