package api

import (
	"encoding/json"
	"math/big"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestProxy3rdPartyWithGet(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	body := map[string]interface{}{
		"path":   "https://api.etherscan.io/v2/api?chainid=1&module=account&action=txlist&address=******************************************&startblock=********&endblock=********",
		"method": "GET",
	}

	// gin server
	r := gin.Default()
	url := "/v1/proxy_3rd_party"
	clientID := "41902cd3a636c7eb0af0fe9b" // stickey
	r.POST(url, auth.MockClientID(clientID), Proxy3rdParty)

	// request
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Log(w.Body.String())
}

func TestProxy3rdPartyCoingecko(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	body := map[string]interface{}{
		"path":   "https://api.coingecko.com/api/v3/simple/token_price/ethereum?vs_currencies=usd&contract_addresses=******************************************",
		"method": "GET",
	}

	// gin server
	r := gin.Default()
	url := "/v1/proxy_3rd_party"
	clientID := "41902cd3a636c7eb0af0fe9b" // stickey
	r.POST(url, auth.MockClientID(clientID), Proxy3rdParty)

	// request
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Log(w.Body.String())
}

func TestProxy3rdPartyInfura(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	txHash := "0x71adb57375b1482cd14f80fcbcfe358d2b2b4f774b39a931f84a2278b378d812"

	body := map[string]interface{}{
		"path":   "https://polygon-mainnet.infura.io/v3/{api_key_from_backend}",
		"method": "POST",
		"body": map[string]interface{}{
			"jsonrpc": "2.0",
			"method":  "eth_getTransactionByHash",
			"params":  []string{txHash},
			"id":      1,
		},
	}

	// gin server
	r := gin.Default()
	url := "/v1/proxy_3rd_party"
	clientID := "41902cd3a636c7eb0af0fe9b" // stickey
	r.POST(url, auth.MockClientID(clientID), Proxy3rdParty)

	// request
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Log(w.Body.String())
}

func TestProxy3rdPartyCrystal(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	addr := "******************************************"

	body := map[string]interface{}{
		"path":   "https://apiexpert.crystalblockchain.com/monitor/tx/add",
		"method": "POST",
		"body": map[string]string{
			"address":   addr,
			"name":      "name",       // any name would work
			"direction": "withdrawal", // only withdrawal direction doesn't need tx hash
			"currency":  "eth",
		},
	}

	// gin server
	r := gin.Default()
	url := "/v1/proxy_3rd_party"
	clientID := "41902cd3a636c7eb0af0fe9b" // stickey
	r.POST(url, auth.MockClientID(clientID), Proxy3rdParty)

	// request
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	// Owner structure.
	type Owner struct {
		ID   *int64 `json:"id"`
		Name string `json:"name"`
		Slug string `json:"slug"`
		Type string `json:"type"`
	}
	// AddressRiskResponseData structure.
	type AddressRiskResponseData struct {
		Address      string             `json:"address"`
		AlertGrade   string             `json:"alert_grade"`
		Owner        *Owner             `json:"counterparty"`
		Riskscore    float64            `json:"riskscore"`
		Signals      map[string]float64 `json:"signals"`
		SnapshotedAt *big.Int           `json:"snapshoted_at"`
		UpdatedAt    *big.Int           `json:"updated_at"`
	}

	// Meta structure.
	type Meta struct {
		CallsLeft        int64       `json:"calls_left"`
		CallsUsed        int64       `json:"calls_used"`
		ErrorCode        int64       `json:"error_code"`
		ErrorMessage     string      `json:"error_message"`
		ServerTime       int64       `json:"server_time"`
		ValidationErrors interface{} `json:"validation_errors"`
	}

	// AddressRiskResponse structure.
	type AddressRiskResponse struct {
		Data AddressRiskResponseData `json:"data"`
		Meta Meta                    `json:"meta"`
	}

	var resp AddressRiskResponse
	assert.Nil(t, json.Unmarshal(w.Body.Bytes(), &resp))
	assert.Equal(t, strings.ToLower(addr), resp.Data.Address)
	assert.NotZero(t, resp.Data.Riskscore)
}

func TestProxy3rdPartyZerion(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ZERION_API_KEY_V2"})

	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())
	repo := rdb.GormRepo()
	application.Init(repo)

	body := map[string]interface{}{
		"path":   "https://api.zerion.io/v1/wallets/******************************************/portfolio?filter[positions]=only_simple&currency=usd",
		"method": "GET",
	}

	// gin server
	r := gin.Default()
	url := "/v1/proxy_3rd_party"
	clientID := "41902cd3a636c7eb0af0fe9b" // stickey
	r.POST(url, auth.MockClientID(clientID), Proxy3rdParty)

	// request
	bodyStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	t.Log(w.Body.String())
}
