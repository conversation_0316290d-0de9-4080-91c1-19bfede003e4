package tokenanalysis

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPurchaseCredits(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Test data
	walletAddress := "********************************************"
	txHash := "5uqVPGTB6qCBpGrTVBw9uNV7K6v3hC6duwC3BgfwpXm5zBBRyXvZR4WRHqGTzx5nwfPMhiiRWneV5ExQwGq6ygtj"

	tests := []struct {
		name           string
		requestBody    interface{}
		expectedStatus int
		expectedFields map[string]interface{}
	}{
		{
			name: "Invalid request body - empty wallet address",
			requestBody: map[string]interface{}{
				"wallet_address": "", // Empty wallet address
				"tx_hash":        txHash,
			},
			expectedStatus: http.StatusBadRequest,
			expectedFields: map[string]interface{}{
				"status": float64(400),
				"code":   float64(1004), // ParamIncorrect
			},
		},
		{
			name: "Invalid request body - missing wallet address",
			requestBody: map[string]interface{}{
				"tx_hash": txHash,
			},
			expectedStatus: http.StatusBadRequest,
			expectedFields: map[string]interface{}{
				"status": float64(400),
				"code":   float64(1004), // ParamIncorrect
			},
		},
		{
			name: "Invalid request body - missing tx hash",
			requestBody: map[string]interface{}{
				"wallet_address": walletAddress,
			},
			expectedStatus: http.StatusBadRequest,
			expectedFields: map[string]interface{}{
				"status": float64(400),
				"code":   float64(1004), // ParamIncorrect
			},
		},
		{
			name: "Invalid request body - empty tx hash",
			requestBody: map[string]interface{}{
				"wallet_address": walletAddress,
				"tx_hash":        "", // Empty tx hash
			},
			expectedStatus: http.StatusBadRequest,
			expectedFields: map[string]interface{}{
				"status": float64(400),
				"code":   float64(1004), // ParamIncorrect
			},
		},
		{
			name:           "Invalid JSON",
			requestBody:    "invalid json",
			expectedStatus: http.StatusBadRequest,
			expectedFields: map[string]interface{}{
				"status": float64(400),
				"code":   float64(1004), // ParamIncorrect
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create request
			var reqBody []byte
			var err error

			if tc.requestBody != nil {
				if str, ok := tc.requestBody.(string); ok {
					reqBody = []byte(str)
				} else {
					reqBody, err = json.Marshal(tc.requestBody)
					require.NoError(t, err)
				}
			}

			req := httptest.NewRequest(http.MethodPost, "/api/v1/token-analysis/purchase-credits", bytes.NewBuffer(reqBody))
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			w := httptest.NewRecorder()

			// Create Gin context
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// Call the handler
			PurchaseCredits(c)

			// Check status code
			assert.Equal(t, tc.expectedStatus, w.Code)

			// Check response body
			var response map[string]interface{}
			err = json.Unmarshal(w.Body.Bytes(), &response)
			require.NoError(t, err)

			// Check expected fields
			for key, expectedValue := range tc.expectedFields {
				actualValue := response[key]
				assert.Equal(t, expectedValue, actualValue, "Field %s mismatch", key)
			}
		})
	}
}
