package tokenanalysis

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	tokenanalysis "github.com/kryptogo/kg-wallet-backend/service/token-analysis"
)

// AnalyzeTokenRequest represents the request for token analysis
type AnalyzeTokenRequest struct {
	WalletAddress string `json:"wallet_address" binding:"required"`
	TokenAddress  string `json:"token_address" binding:"required"`
	Message       string `json:"message" binding:"required"`
	Signature     string `json:"signature" binding:"required"`
}

// AddCreditsRequest represents the request for adding credits
type AddCreditsRequest struct {
	WalletAddress string `json:"wallet_address" binding:"required"`
	Credits       int    `json:"credits" binding:"required"`
}

// ProcessTradingVolumeRequest represents the request for processing trading volume
type ProcessTradingVolumeRequest struct {
	TxHash string `json:"tx_hash" binding:"required"`
}

// PurchaseCreditsRequest represents the request for purchasing credits with SOL
type PurchaseCreditsRequest struct {
	WalletAddress string `json:"wallet_address" binding:"required"`
	TxHash        string `json:"tx_hash" binding:"required"`
}

// AnalyzeToken handles the token analysis request
func AnalyzeToken(c *gin.Context) {
	var req AnalyzeTokenRequest
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	resp, kgErr := tokenanalysis.AnalyzeToken(c.Request.Context(), req.WalletAddress, req.TokenAddress, req.Message, req.Signature)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, resp)
}

// GetCredits handles the get credits request
func GetCredits(c *gin.Context) {
	walletAddress := c.Param("wallet_address")
	if walletAddress == "" {
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("wallet address is required"), nil))
		return
	}

	resp, kgErr := tokenanalysis.GetCredits(c.Request.Context(), walletAddress)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, resp)
}

// AddCredits handles the add credits request
func AddCredits(c *gin.Context) {
	var req AddCreditsRequest
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	kgErr := tokenanalysis.AddCredits(c.Request.Context(), req.WalletAddress, req.Credits)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

// ProcessTradingVolume handles the process trading volume request
func ProcessTradingVolume(c *gin.Context) {
	var req ProcessTradingVolumeRequest
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	kgErr := tokenanalysis.ProcessTradingVolume(c.Request.Context(), req.TxHash)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

// PurchaseCredits handles the purchase credits request
func PurchaseCredits(c *gin.Context) {
	var req PurchaseCreditsRequest
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	resp, kgErr := tokenanalysis.PurchaseCredits(c.Request.Context(), req.WalletAddress, req.TxHash)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, resp)
}

// GetFreeCredit handles the get free credit request
func GetFreeCredit(c *gin.Context) {
	walletAddress := c.Param("wallet_address")
	if walletAddress == "" {
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("wallet address is required"), nil))
		return
	}

	kgErr := tokenanalysis.GetFreeCredit(c.Request.Context(), walletAddress)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}
