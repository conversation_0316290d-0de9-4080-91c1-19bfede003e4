package api

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"math/big"

	"github.com/gin-gonic/gin"
	"github.com/jpillora/backoff"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/erc4337"
	"github.com/shopspring/decimal"
)

// When the balance in entrypoint is less than threshold, deposit amount to paymaster
// Upon receiving the native token, paymaster will deposit it to the entrypoint
type DepositParams struct {
	Threshold string
	Amount    string
}

// Use 10 ~ 100 USD value as threshold and amount
var depositMap = map[domain.Chain]DepositParams{
	// 10 USD
	domain.Arbitrum: {
		Threshold: "0.0025",
		Amount:    "0.0025",
	},
	// 2.6 USD
	domain.BaseChain: {
		Threshold: "0.001",
		Amount:    "0.001",
	},
	// 2.6 USD
	domain.Optimism: {
		Threshold: "0.001",
		Amount:    "0.001",
	},
	// 10 USD
	domain.BNBChain: {
		Threshold: "0.15",
		Amount:    "0.15",
	},
	// 10 USD
	domain.Polygon: {
		Threshold: "150",
		Amount:    "150",
	},
	// 100 USD
	domain.Ethereum: {
		Threshold: "0.025",
		Amount:    "0.025",
	},
}

// retryDepositWithBackoff performs exponential backoff retry for deposit operation
func retryDepositWithBackoff(ctx context.Context, chain domain.Chain, threshold, amount *big.Int) (string, error) {
	const maxRetries = 5

	b := &backoff.Backoff{
		Min:    1 * time.Second,
		Max:    3 * time.Second,
		Factor: 1.5,
		Jitter: true,
	}

	var lastErr error
	for attempt := 0; attempt < maxRetries; attempt++ {
		txHash, err := erc4337.DepositIfNotEnoughBalanceInEntrypoint(ctx, chain, threshold, amount)
		if err == nil {
			return txHash, nil
		}

		lastErr = err
		kglog.WarningWithDataCtx(ctx, "DepositIfNotEnoughBalanceInEntrypoint failed, retrying", map[string]any{
			"chain":      chain,
			"attempt":    attempt + 1,
			"maxRetries": maxRetries,
			"error":      err.Error(),
		})

		// If this is the last attempt, don't sleep
		if attempt == maxRetries-1 {
			break
		}

		// Wait before next retry
		select {
		case <-ctx.Done():
			return "", ctx.Err()
		case <-time.After(b.Duration()):
		}
	}

	return "", fmt.Errorf("deposit failed after %d attempts: %w", maxRetries, lastErr)
}

func DepositIfNeeded(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.DepositIfNeeded")
	defer span.End()

	var req struct {
		Timeout int    `json:"timeout" binding:"required"`
		Chain   string `json:"chain" binding:"required"`
	}

	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Create a context with timeout based on requestBody.Timeout
	ctxWithTimeout, cancel := tracing.WithTimeoutAndTrace(ctx, time.Duration(req.Timeout)*time.Second)
	defer cancel()

	chain := domain.IDToChain(req.Chain)
	if chain == nil {
		c.String(http.StatusBadRequest, "invalid chain")
		return
	}
	_, err := erc4337.PaymasterAddress(chain)
	if err != nil {
		c.String(http.StatusBadRequest, fmt.Sprintf("no paymaster address available for %s: %v", chain.ID(), err))
		return
	}
	param, ok := depositMap[chain]
	if !ok {
		c.String(http.StatusBadRequest, fmt.Sprintf("no deposit params available for %s", chain.ID()))
		return
	}
	threshold, err := decimal.NewFromString(param.Threshold)
	if err != nil {
		kglog.ErrorWithDataCtx(ctxWithTimeout, "convert threshold to decimal error", map[string]any{
			"chain": chain,
			"error": err.Error(),
		})
		c.String(http.StatusInternalServerError, fmt.Sprintf("convert threshold to decimal error for %s: %v", chain.ID(), err))
		return
	}
	amount, err := decimal.NewFromString(param.Amount)
	if err != nil {
		kglog.ErrorWithDataCtx(ctxWithTimeout, "convert amount to decimal error", map[string]any{
			"chain": chain,
			"error": err.Error(),
		})
		c.String(http.StatusInternalServerError, fmt.Sprintf("convert amount to decimal error for %s: %v", chain.ID(), err))
		return
	}
	decimals := chain.MainToken().Decimals()
	threshold = threshold.Shift(int32(decimals))
	amount = amount.Shift(int32(decimals))

	txHash, err := retryDepositWithBackoff(ctxWithTimeout, chain, threshold.BigInt(), amount.BigInt())
	if err != nil {
		kglog.ErrorWithDataCtx(ctxWithTimeout, "payment.DepositIfNotEnoughBalanceInEntrypoint", map[string]any{
			"chain":  chain,
			"error":  err.Error(),
			"txHash": txHash,
		})
		c.String(http.StatusInternalServerError, fmt.Sprintf("payment.DepositIfNotEnoughBalanceInEntrypoint error for %s: %v", chain.ID(), err))
		return
	}
	kglog.DebugWithDataCtx(ctxWithTimeout, "DepositIfNeeded", map[string]any{
		"chain":  chain,
		"txHash": txHash,
	})

	c.JSON(http.StatusOK, map[string]any{
		"txHash": txHash,
	})
}
