package binance

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/binance"
)

type QueryDepositAddressRequest struct {
	RequestID     string `json:"request_id" binding:"required"`
	TransactionID string `json:"transaction_id" binding:"required"`
}

type QueryDepositAddressResponse struct {
	Authorized bool   `json:"authorized"`
	Address    string `json:"address"`
}

func QueryDepositAddress(c *gin.Context) {
	ctx := c.Request.Context()

	var req QueryDepositAddressRequest
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	address, err := binance.QueryDepositAddress(ctx, req.RequestID, req.TransactionID)
	if err != nil {
		response.KGError(c, err)
		return
	}

	response.OK(c, QueryDepositAddressResponse{
		Authorized: address.Authorized,
		Address:    address.Address,
	})
}
