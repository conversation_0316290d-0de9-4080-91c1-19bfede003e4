package binance

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/binance"
)

// ReportDepositRequest represents the request body for reporting a deposit transaction
type ReportDepositRequest struct {
	Amount        string `json:"amount" binding:"required"`
	TxID          string `json:"tx_id" binding:"required"`
	TransactionID string `json:"transaction_id" binding:"required"`
}

// ReportDeposit handles the API request to report a deposit transaction to Binance Pay
func ReportDeposit(c *gin.Context) {
	ctx := c.Request.Context()

	var req ReportDepositRequest
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	// Convert the request to service layer format
	serviceReq := &binance.ReportDepositRequest{
		Amount:        req.Amount,
		TxID:          req.TxID,
		TransactionID: req.TransactionID,
	}

	if err := binance.ReportDeposit(ctx, serviceReq); err != nil {
		response.KGError(c, err)
		return
	}

	response.OK(c, gin.H{})
}
