package binance

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/binance"
)

type CreateWithdrawURLRequest struct {
	ChainID       string `json:"chain_id" binding:"required"`
	TokenID       string `json:"token_id"` // sometimes token id is empty
	WalletAddress string `json:"wallet_address" binding:"required"`

	// parsed fields
	Chain   domain.Chain   `json:"-"`
	Address domain.Address `json:"-"`
}

func (r *CreateWithdrawURLRequest) AfterBinding(c *gin.Context) error {
	chain := domain.IDToChain(r.ChainID)
	if chain == nil {
		return fmt.Errorf("invalid chain")
	}
	r.Chain = chain
	r.Address = domain.NewAddressByChain(chain, r.<PERSON>)
	return nil
}

func CreateWithdrawURL(c *gin.Context) {
	ctx := c.Request.Context()

	var req CreateWithdrawURLRequest
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	urls, err := binance.CreateWithdrawURL(ctx, req.Chain, req.TokenID, req.Address)
	if err != nil {
		response.KGError(c, err)
		return
	}
	response.OK(c, urls)
}
