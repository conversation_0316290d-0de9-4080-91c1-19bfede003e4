package binance

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/binance"
)

type CreateDepositURLRequest struct {
	ChainID string `json:"chain_id" binding:"required"`
	TokenID string `json:"token_id"` // sometimes token id is empty

	// parsed fields
	Chain domain.Chain `json:"-"`
}

func (r *CreateDepositURLRequest) AfterBinding(c *gin.Context) error {
	chain := domain.IDToChain(r.ChainID)
	if chain == nil {
		return fmt.Errorf("invalid chain")
	}
	r.Chain = chain
	return nil
}

func CreateDepositURL(c *gin.Context) {
	ctx := c.Request.Context()

	var req CreateDepositURLRequest
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	urls, err := binance.CreateDepositURL(ctx, req.Chain, req.TokenID)
	if err != nil {
		response.KGError(c, err)
		return
	}
	response.OK(c, urls)
}
