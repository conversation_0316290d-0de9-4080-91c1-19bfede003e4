package response

import (
	"fmt"
	"net/http"
	"time"

	em "emperror.dev/errors"
	"github.com/gin-contrib/requestid"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// Fail returns Rspfail.
func Fail(err error) (int, interface{}) {
	return http.StatusInternalServerError, &map[string]interface{}{
		"message": err.Error(),
		"status":  http.StatusInternalServerError,
	}
}

// Abort responses the failed info in JSON format and abort this session.
func Abort(c *gin.Context, err error) {
	c.JSON(Fail(err))
	c.Abort()
}

// AbortByAny .
func AbortByAny(c *gin.Context, anyError interface{}) {
	switch errObject := anyError.(type) {
	case validator.ValidationErrors:
		ErrorWithMsg(c, http.StatusBadRequest, code.ParamIncorrect, errObject.Error(), nil)
	case error:
		unwrappedErr := em.Unwrap(errObject)
		if unwrappedErr != nil {
			AbortByAny(c, unwrappedErr)
			return
		}
		ErrorWithMsg(c, http.StatusInternalServerError, code.UnknownError, errObject.Error(), nil)
	default:
		ErrorWithMsg(c, http.StatusInternalServerError, code.UnknownError, fmt.Sprintf("%v", anyError), nil)
	}
}

// OKResp is the ok response struct
type OKResp struct {
	Code int         `json:"code"`
	Data interface{} `json:"data,omitempty"`
}

// OK responses code and data in JSON format.
func OK(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusOK, &OKResp{
		Code: 0,
		Data: data,
	})
}

// CreatedResp is the created response struct
type CreatedResp struct {
	Code int         `json:"code"`
	Data interface{} `json:"data,omitempty"`
}

// Created responses code and data in JSON format.
func Created(ctx *gin.Context, data interface{}) {
	ctx.JSON(http.StatusCreated, &CreatedResp{
		Code: 0,
		Data: data,
	})
}

// Paging is the paging struct
type Paging struct {
	PageNumber int    `json:"page_number"`
	PageSize   int    `json:"page_size"`
	TotalCount int    `json:"total_count"`
	PageSort   string `json:"page_sort"`
}

// OKWithPagingResp is the ok response struct
type OKWithPagingResp struct {
	OKResp
	Paging Paging `json:"paging,omitempty"`
}

// OKWithPaging responses code and data and paging in JSON format.
func OKWithPaging(ctx *gin.Context, data any, paging Paging) {
	ctx.JSON(http.StatusOK, &OKWithPagingResp{
		OKResp: OKResp{
			Code: 0,
			Data: data,
		},
		Paging: paging,
	})
}

// TokenPaging is the token paging struct
type TokenPaging struct {
	NextPageToken *string `json:"next_page_token"`
}

// OKWithTokenPagingResp is the ok response struct
type OKWithTokenPagingResp struct {
	OKResp
	TokenPaging TokenPaging `json:"paging,omitempty"`
}

// OKWithTokenPaging responses code and data and paging in JSON format.
func OKWithTokenPaging(ctx *gin.Context, data any, paging TokenPaging) {
	ctx.JSON(http.StatusOK, &OKWithTokenPagingResp{
		OKResp: OKResp{
			Code: 0,
			Data: data,
		},
		TokenPaging: paging,
	})
}

// ErrorResp is the error response struct.
type ErrorResp struct {
	Status    int                    `json:"status"`
	Code      int                    `json:"code"`
	RequestID string                 `json:"request_id"`
	Message   string                 `json:"message"`
	Path      string                 `json:"path"`
	Timestamp int64                  `json:"timestamp"`
	Data      map[string]interface{} `json:"data,omitempty"`
}

// ForbiddenError .
func ForbiddenError(ctx *gin.Context, code int) {
	ForbiddenErrorWithMsg(ctx, code, "")
}

// ForbiddenErrorWithMsg .
func ForbiddenErrorWithMsg(ctx *gin.Context, code int, msg string) {
	if msg == "" {
		msg = "403 FORBIDDEN"
	}
	ErrorWithMsg(ctx, http.StatusForbidden, code, msg, nil)
}

// NotFoundWithMsg .
func NotFoundWithMsg(ctx *gin.Context, code int, msg string) {
	if msg == "" {
		msg = "404 NOT FOUND"
	}
	ErrorWithMsg(ctx, http.StatusNotFound, code, msg, nil)
}

// BadRequest .
func BadRequest(ctx *gin.Context, code int) {
	ErrorWithMsg(ctx, http.StatusBadRequest, code, "", nil)
}

// BadRequestWithMsg .
func BadRequestWithMsg(ctx *gin.Context, code int, msg string) {
	if msg == "" {
		msg = "400 BAD REQUEST"
	}
	ErrorWithMsg(ctx, http.StatusBadRequest, code, msg, nil)
}

// Unauthorized .
func Unauthorized(ctx *gin.Context, code int) {
	ErrorWithMsg(ctx, http.StatusUnauthorized, code, "", nil)
}

// UnauthorizedWithMsg .
func UnauthorizedWithMsg(ctx *gin.Context, code int, msg string) {
	if msg == "" {
		msg = "401 UNAUTHORIZED"
	}
	ErrorWithMsg(ctx, http.StatusUnauthorized, code, msg, nil)
}

// TooManyRequestsWithMsg .
func TooManyRequestsWithMsg(ctx *gin.Context, code int, msg string) {
	if msg == "" {
		msg = "429 TOO MANY REQUESTS"
	}
	ErrorWithMsg(ctx, http.StatusTooManyRequests, code, msg, nil)
}

// InternalServerError .
func InternalServerError(ctx *gin.Context, code int) {
	ErrorWithMsg(ctx, http.StatusInternalServerError, code, "", nil)
}

// InternalServerErrorWithMsg .
func InternalServerErrorWithMsg(ctx *gin.Context, code int, msg string) {
	if msg == "" {
		msg = "500 INTERNAL SERVER ERROR"
	}
	ErrorWithMsg(ctx, http.StatusInternalServerError, code, msg, nil)
}

// ErrorWithMsg .
func ErrorWithMsg(ctx *gin.Context, status int, code int, msg string, data map[string]interface{}) {
	if msg == "" {
		msg = "ERROR"
	}
	var err = &ErrorResp{
		Status:    status,
		Code:      code,
		RequestID: requestid.Get(ctx),
		Message:   msg,
		Path:      ctx.Request.RequestURI,
		Timestamp: time.Now().Unix(),
		Data:      data,
	}
	ctx.AbortWithStatusJSON(status, err)
}

// AcceptedWithMsg .
func AcceptedWithMsg(ctx *gin.Context, code int, msg string) {
	if msg == "" {
		msg = "202 ACCEPTED"
	}
	var err = &ErrorResp{
		Status:    http.StatusAccepted,
		Code:      code,
		RequestID: requestid.Get(ctx),
		Message:   msg,
		Path:      ctx.Request.RequestURI,
		Timestamp: time.Now().Unix(),
	}
	ctx.AbortWithStatusJSON(http.StatusAccepted, err)
}

// ConflictWithMsg .
func ConflictWithMsg(ctx *gin.Context, code int, msg string) {
	if msg == "" {
		msg = "409 CONFLICT"
	}
	var err = &ErrorResp{
		Status:    http.StatusConflict,
		Code:      code,
		RequestID: requestid.Get(ctx),
		Message:   msg,
		Path:      ctx.Request.RequestURI,
		Timestamp: time.Now().Unix(),
	}
	ctx.AbortWithStatusJSON(http.StatusConflict, err)
}

// KGError .
func KGError(ctx *gin.Context, err *code.KGError) {
	ErrorWithMsg(ctx, err.HttpStatus, err.Code, err.String(), err.Data)
}
