package api

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/user"
)

const (
	walletConnectTTL = 5 * time.Minute
	sessionPrefix    = "wallet_connect:"
)

// CreateSession creates a new session and returns the uri to web
func CreateSession(ctx *gin.Context) {
	status := "created"
	// 組出header 作為 redis的key
	sid := uuid.NewString()

	if cache.Client == nil {
		response.InternalServerErrorWithMsg(ctx, code.RedisError, "redisClient is null, not able to control session of wallet connect")
		return
	}

	err := cache.Set(ctx.Request.Context(), sessionPrefix+sid, status, walletConnectTTL)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.RedisError, "redisClient.Set error:"+err.Error())
		return
	}
	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": map[string]interface{}{
			"uri":        fmt.Sprintf("%s/scanner?routePath=kgc&uuid=%s", config.GetString("DYNAMIC_LINK_HOST"), sid),
			"expires_at": time.Now().Add(walletConnectTTL).Unix(),
		},
	})
}

// GetSessionStatus returns the status of the session for web polling
func GetSessionStatus(ctx *gin.Context) {
	sid := ctx.Param("sid")
	// cache value like "to_confirm:uid"
	value := strings.Split(cache.String(sessionPrefix+sid), ":")
	if len(value) == 0 {
		response.NotFoundWithMsg(ctx, code.WalletConnectInvalidStatus, "session is not connected")
		return
	}
	status := value[0]
	switch status {
	case "created", "connected":
		ctx.JSON(http.StatusOK, map[string]interface{}{
			"code": 0,
			"data": map[string]interface{}{
				"status": status,
			},
		})
		return
	case "to_confirm":
		authUID := value[1]
		user, _ := user.GetByUID(ctx.Request.Context(), authUID, "", false, &domain.UserPreloads{
			WithAvatar: true,
		})
		avatarURL := ""
		if user.Avatar != nil {
			avatarURL = user.Avatar.AvatarURL
		}
		ctx.JSON(http.StatusOK, map[string]interface{}{
			"code": 0,
			"data": map[string]interface{}{
				"status":       "to_confirm",
				"phone_number": user.PhoneNumber,
				"display_name": user.DisplayName,
				"avatar_url":   avatarURL,
			},
		})
		return
	case "":
		ctx.JSON(http.StatusOK, map[string]interface{}{
			"code": 0,
			"data": map[string]interface{}{
				"status": "not_exist",
			},
		})
		return
	default:
		response.InternalServerErrorWithMsg(ctx, code.WalletConnectInvalidStatus, "session status:"+status)
		return
	}
}

type wcParams struct {
	Confirm bool `form:"confirm"`
}

// WalletConnect app side connect to web side
func WalletConnect(ctx *gin.Context) {
	params := &wcParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	sid := ctx.Param("sid")
	status := cache.String(sessionPrefix + sid)

	if status == "" || strings.HasPrefix(status, "connected:") {
		response.BadRequestWithMsg(ctx, code.WalletConnectInvalidStatus, "session status:"+status)
		return
	}
	uid := auth.GetUID(ctx)
	if params.Confirm {
		cache.Set(ctx.Request.Context(), sessionPrefix+sid, "connected:"+uid, walletConnectTTL)
	} else {
		cache.Set(ctx.Request.Context(), sessionPrefix+sid, "to_confirm:"+uid, walletConnectTTL)
	}
	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
	})
}

// GetCustomToken returns the custom token for firebase auth
func GetCustomToken(ctx *gin.Context) {
	sid := ctx.Param("sid")
	status := cache.String(sessionPrefix + sid)
	if !strings.HasPrefix(status, "connected:") {
		response.BadRequestWithMsg(ctx, code.WalletConnectInvalidStatus, "session is not connected")
		return
	}
	authUID := strings.Split(status, ":")[1]
	tokenString, err := firebase.CustomToken(authUID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.FirebaseFailed, "firebase.CustomToken error:"+err.Error())
		return
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": map[string]interface{}{
			"custom_token": tokenString,
		},
	})
}
