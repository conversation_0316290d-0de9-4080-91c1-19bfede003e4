// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package api

import (
	"errors"
	"fmt"
)

const (
	// PaymentIntentStatusPaymentIntentcanceled is a PaymentIntentStatus of type payment_intent.canceled.
	PaymentIntentStatusPaymentIntentcanceled PaymentIntentStatus = "payment_intent.canceled"
	// PaymentIntentStatusPaymentIntentpaymentFailed is a PaymentIntentStatus of type payment_intent.payment_failed.
	PaymentIntentStatusPaymentIntentpaymentFailed PaymentIntentStatus = "payment_intent.payment_failed"
	// PaymentIntentStatusPaymentIntentsucceeded is a PaymentIntentStatus of type payment_intent.succeeded.
	PaymentIntentStatusPaymentIntentsucceeded PaymentIntentStatus = "payment_intent.succeeded"
)

var ErrInvalidPaymentIntentStatus = errors.New("not a valid PaymentIntentStatus")

// String implements the Stringer interface.
func (x PaymentIntentStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x PaymentIntentStatus) IsValid() bool {
	_, err := ParsePaymentIntentStatus(string(x))
	return err == nil
}

var _PaymentIntentStatusValue = map[string]PaymentIntentStatus{
	"payment_intent.canceled":       PaymentIntentStatusPaymentIntentcanceled,
	"payment_intent.payment_failed": PaymentIntentStatusPaymentIntentpaymentFailed,
	"payment_intent.succeeded":      PaymentIntentStatusPaymentIntentsucceeded,
}

// ParsePaymentIntentStatus attempts to convert a string to a PaymentIntentStatus.
func ParsePaymentIntentStatus(name string) (PaymentIntentStatus, error) {
	if x, ok := _PaymentIntentStatusValue[name]; ok {
		return x, nil
	}
	return PaymentIntentStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidPaymentIntentStatus)
}
