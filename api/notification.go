package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	oauthapi "github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/notification"
	lo "github.com/samber/lo"
)

// NotificationUnreadCount returns amount of unread notifications
func NotificationUnreadCount(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)
	clientID := oauthapi.ClientID(c)
	if clientID == "" {
		clientID = application.GetDefaultClientID(c)
	}
	resp := gin.H{}
	resp["code"] = 0
	resp["cnt"] = rdb.NotificationUnreadCount(ctx, uid, clientID)
	c.JSON(http.StatusOK, resp)
}

type notificationsResp struct {
	Code   int                    `json:"code"`
	Data   []*rdb.VNotification   `json:"data"`
	Paging *rdb.TokenPagingParams `json:"paging"`
}

// Notifications returns user notifications
func Notifications(c *gin.Context) {
	ctx := c.Request.Context()
	params := &rdb.NotificationParams{}
	kgErr := util.ToGinContextExt(c).BindQuery(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	params.UID = auth.GetUID(c)
	params.ClientID = oauthapi.ClientID(c)
	if params.ClientID == "" {
		params.ClientID = application.GetDefaultClientID(c)
	}
	notifications, paging, code, err := rdb.Notifications(ctx, params)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code, err.Error())
		return
	}
	resp := new(notificationsResp)
	resp.Code = code
	resp.Data = notifications
	resp.Paging = paging
	c.JSON(http.StatusOK, resp)
}

// NotificationDetail returns specific notification
func NotificationDetail(c *gin.Context) {
	ctx := c.Request.Context()
	nidStr := c.Param("nid")
	if nidStr == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "nid is empty")
		return
	}
	nid, _ := strconv.Atoi(nidStr)
	uid := auth.GetUID(c)
	defaultClientID := application.GetDefaultClientID(ctx)
	notification, kgErr := rdb.Notification(ctx, uid, defaultClientID, int32(nid))
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, notification)
}

// NotificationReadAll set read all
func NotificationReadAll(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)
	clientID := oauthapi.ClientID(c)
	err := rdb.GormRepo().UserNotificationReadAll(ctx, uid, clientID)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.DBError, err.Error())
		return
	}
	rdb.NotificationReadAll(ctx, uid, clientID)
	c.JSON(http.StatusOK, gin.H{"code": 0})
}

type announcementAddReq struct {
	SendToLocales            []string          `json:"send_to_locales"`
	ContentType              string            `json:"content_type"`
	MultiLocaleTitle         map[string]string `json:"multi_locale_title" binding:"required"`
	MultiLocaleSummary       map[string]string `json:"multi_locale_summary" binding:"required"`
	MultiLocaleMessage       map[string]string `json:"multi_locale_message" binding:"required"`
	MultiLocalePrimaryText   map[string]string `json:"multi_locale_primary_text" binding:"required"`
	MultiLocalePrimaryLink   map[string]string `json:"multi_locale_primary_link"`
	PrimaryOpenWith          string            `json:"primary_open_with"`
	MultiLocaleSecondaryText map[string]string `json:"multi_locale_secondary_text"`
	MultiLocaleSecondaryLink map[string]string `json:"multi_locale_secondary_link"`
	SecondaryOpenWith        string            `json:"secondary_open_with"`
	PreviewImageURL          map[string]string `json:"preview_image_url"`
	ClientID                 string            `json:"client_id" binding:"required"`
}

// AnnouncementAdd add announcement
func AnnouncementAdd(c *gin.Context) {
	ctx := c.Request.Context()
	req := announcementAddReq{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	noti := domain.Notification{
		Receiver:          rdb.ReceiverAll,
		MessageType:       domain.NotificationMessageTypeAnnouncement,
		ContentType:       domain.NotificationContentType(req.ContentType),
		Title:             domain.MultiLocaleText(req.MultiLocaleTitle),
		Summary:           domain.MultiLocaleText(req.MultiLocaleSummary),
		Message:           domain.MultiLocaleText(req.MultiLocaleMessage),
		PrimaryText:       domain.MultiLocaleText(req.MultiLocalePrimaryText),
		PrimaryLink:       domain.MultiLocaleText(req.MultiLocalePrimaryLink),
		PrimaryOpenWith:   domain.NotificationLinkType(req.PrimaryOpenWith),
		SecondaryText:     domain.MultiLocaleText(req.MultiLocaleSecondaryText),
		SecondaryLink:     domain.MultiLocaleText(req.MultiLocaleSecondaryLink),
		SecondaryOpenWith: domain.NotificationLinkType(req.SecondaryOpenWith),
		PreviewImageURL:   domain.MultiLocaleText(req.PreviewImageURL),
		ClientID:          &req.ClientID,
		CreatedAt:         time.Now(),
	}
	kgErr = notification.Get().SendAnnouncement(ctx, &noti, req.SendToLocales)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	c.JSON(http.StatusOK, gin.H{"code": 0, "message": "ok"})
}

// SendNotificationsReq send notifications request
type SendNotificationsReq struct {
	PhoneNumbers             []string          `json:"phone_numbers"`
	UIDs                     []string          `json:"uids"`
	Emails                   []string          `json:"emails"`
	WalletAddresses          []string          `json:"wallet_addresses"`
	MessageType              string            `json:"message_type"`
	ContentType              string            `json:"content_type"`
	MultiLocaleTitle         map[string]string `json:"multi_locale_title" binding:"required"`
	MultiLocaleSummary       map[string]string `json:"multi_locale_summary" binding:"required"`
	MultiLocaleMessage       map[string]string `json:"multi_locale_message" binding:"required"`
	MultiLocalePrimaryText   map[string]string `json:"multi_locale_primary_text" binding:"required"`
	MultiLocalePrimaryLink   map[string]string `json:"multi_locale_primary_link"`
	PrimaryOpenWith          string            `json:"primary_open_with"`
	MultiLocaleSecondaryText map[string]string `json:"multi_locale_secondary_text"`
	MultiLocaleSecondaryLink map[string]string `json:"multi_locale_secondary_link"`
	SecondaryOpenWith        string            `json:"secondary_open_with"`
	PreviewImageURL          map[string]string `json:"preview_image_url"`
	ClientID                 string            `json:"client_id" binding:"required"`
}

// SendNotifications send notifications
func SendNotifications(c *gin.Context) {
	ctx := c.Request.Context()
	req := SendNotificationsReq{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	clientID := req.ClientID
	var uids []string

	// get users
	if len(req.PhoneNumbers) == 0 &&
		len(req.UIDs) == 0 &&
		len(req.Emails) == 0 &&
		len(req.WalletAddresses) == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "phone_numbers, uids, emails, wallet_addresses at least one is required")
		return
	}

	if len(req.PhoneNumbers) > 0 {
		phoneNumbers := lo.Uniq(req.PhoneNumbers)
		users, kgErr := rdb.GormRepo().GetUsersByPhoneNumbers(ctx, phoneNumbers, clientID, false, nil)
		if kgErr != nil {
			response.KGError(c, kgErr)
			return
		}
		uids = append(uids, lo.Map(users, func(user *domain.UserData, _ int) string {
			return user.UID
		})...)
	}

	if len(req.UIDs) > 0 {
		uids = append(uids, req.UIDs...)
	}

	if len(req.Emails) > 0 {
		emails := lo.Uniq(req.Emails)
		users, kgErr := rdb.GormRepo().GetUsersByEmails(ctx, emails, clientID, false, nil)
		if kgErr != nil {
			response.KGError(c, kgErr)
			return
		}
		uids = append(uids, lo.Map(users, func(user *domain.UserData, _ int) string {
			return user.UID
		})...)
	}

	if len(req.WalletAddresses) > 0 {
		walletAddresses := lo.Uniq(req.WalletAddresses)
		walletUIDs, err := rdb.GetUIDsByChainAndAddresses(ctx, dbmodel.ChainIDEthereum, walletAddresses)
		if err != nil {
			response.InternalServerErrorWithMsg(c, code.DBError, err.Error())
			return
		}
		uids = append(uids, walletUIDs...)
	}

	uids = lo.Uniq(uids)

	// send notifications & fcm
	for _, uid := range uids {
		// add notification
		noti := domain.Notification{
			Receiver:          uid,
			ContentType:       domain.NotificationContentType(req.ContentType),
			MessageType:       domain.NotificationMessageType(req.MessageType),
			Title:             domain.MultiLocaleText(req.MultiLocaleTitle),
			Summary:           domain.MultiLocaleText(req.MultiLocaleSummary),
			Message:           domain.MultiLocaleText(req.MultiLocaleMessage),
			PrimaryText:       domain.MultiLocaleText(req.MultiLocalePrimaryText),
			PrimaryLink:       domain.MultiLocaleText(req.MultiLocalePrimaryLink),
			PrimaryOpenWith:   domain.NotificationLinkType(req.PrimaryOpenWith),
			SecondaryText:     domain.MultiLocaleText(req.MultiLocaleSecondaryText),
			SecondaryLink:     domain.MultiLocaleText(req.MultiLocaleSecondaryLink),
			SecondaryOpenWith: domain.NotificationLinkType(req.SecondaryOpenWith),
			PreviewImageURL:   domain.MultiLocaleText(req.PreviewImageURL),
			ClientID:          &clientID,
			CreatedAt:         time.Now(),
		}
		_, kgErr := notification.Get().Send(ctx, &noti)
		if kgErr != nil {
			kglog.ErrorfCtx(c, "[SendNotifications] send notification error: %v, notification: %v", kgErr, noti)
		}
	}
}
