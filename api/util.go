package api

import (
	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	lo "github.com/samber/lo"
)

func ParseAssetTypes(assetTypeIDs []string) ([]domain.AssetType, error) {
	types := []domain.AssetType{}
	for _, strType := range assetTypeIDs {
		if assetType, err := domain.ParseAssetType(strType); err != nil {
			return nil, err
		} else {
			types = append(types, assetType)
		}
	}
	return types, nil
}

func ParseChainIDs(chainIDs []string) ([]domain.Chain, error) {
	supportedChains := lo.Map(domain.Chains, func(chain domain.Chain, _ int) string {
		return chain.ID()
	})
	chains := []domain.Chain{}
	for _, chainID := range chainIDs {
		if !lo.Contains(supportedChains, chainID) {
			return nil, fmt.Errorf("invalid chain_id '%s'", chainID)
		}
		chains = append(chains, domain.IDToChain(chainID))
	}
	if len(chains) == 0 {
		chains = asset.SupportedChains
	}
	return chains, nil
}
