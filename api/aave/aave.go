package aave

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/aave"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type withdrawInfoReq struct {
	WalletAddress string `form:"wallet_address" binding:"required"`
	Token         string `form:"token" binding:"required"`
	ChainID       string `form:"chain_id" binding:"required"`
}

// GetWithdrawInfo .
func GetWithdrawInfo(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")

	params := &withdrawInfoReq{}
	if kgErr := util.ToGinContextExt(c).BindQuery(params); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	res, kgErr := aave.GetWithdrawInfo(ctx, orgID, uid, params.ChainID, params.WalletAddress, params.Token)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, res)
}

type createFeeTxReq struct {
	WalletAddress string `json:"wallet_address" binding:"required"`
	Token         string `json:"token" binding:"required"`
	ChainID       string `json:"chain_id" binding:"required"`
	TxHash        string `json:"tx_hash" binding:"required"`
}

// CreateFeeTransaction .
func CreateFeeTransaction(c *gin.Context) {
	ctx := c.Request.Context()
	uid := c.GetString("uid")

	params := &createFeeTxReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if kgErr := aave.CreateFeeTransaction(ctx, &domain.AaveInvestFeeRecord{
		OrgID:         orgID,
		UID:           uid,
		ChainID:       params.ChainID,
		WalletAddress: params.WalletAddress,
		TokenAddress:  params.Token,
		TxHash:        params.TxHash,
	}); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}
