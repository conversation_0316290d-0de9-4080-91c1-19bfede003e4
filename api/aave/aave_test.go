package aave

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/aave"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/stretchr/testify/assert"
)

type withdrawInfoRes struct {
	Code int `json:"code"`
	Data struct {
		WithdrawnProfit float64 `json:"withdrawn_profit"`
		FeeRate         float64 `json:"fee_rate"`
		FeeWallet       string  `json:"fee_wallet"`
	} `json:"data"`
}

type createFeeTransactionRes struct {
	Code int `json:"code"`
}

func TestWithdrawInfoAndCreateFeeTransaction(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY"})
	r, _ := setup(t)

	// first request of withdraw info should be 0
	req, _ := http.NewRequest("GET", "/withdraw_info?chain_id=arb&wallet_address=******************************************&token=******************************************", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ := io.ReadAll(w.Body)
	t.Logf("response 1: %v", string(responseStr))
	withdrawInfo := &withdrawInfoRes{}
	assert.Nil(t, json.Unmarshal(responseStr, withdrawInfo))
	assert.Equal(t, 0, withdrawInfo.Code)
	assert.Equal(t, float64(0), withdrawInfo.Data.WithdrawnProfit)
	assert.Equal(t, 0.2, withdrawInfo.Data.FeeRate)
	assert.NotEmpty(t, withdrawInfo.Data.FeeWallet)

	// create a fee tx
	body := map[string]interface{}{
		"chain_id":       "arb",
		"tx_hash":        "0x7d54e3d680eaab5aca5b75dbec1c7be89f9874b631126e2f3b4c819fe00f6d19",
		"wallet_address": "******************************************",
		"token":          "******************************************",
	}
	bodyStr, _ := json.Marshal(body)
	req, _ = http.NewRequest("POST", "/fee_transaction", bytes.NewBuffer(bodyStr))
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	t.Logf("response 2: %v", string(responseStr))
	feeTxRes := &createFeeTransactionRes{}
	assert.Nil(t, json.Unmarshal(responseStr, feeTxRes))
	assert.Equal(t, 0, feeTxRes.Code)

	// second request of withdraw info: should be updated after fee tx
	req, _ = http.NewRequest("GET", "/withdraw_info?chain_id=arb&wallet_address=******************************************&token=******************************************", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	t.Logf("response 3: %v", string(responseStr))
	withdrawInfo = &withdrawInfoRes{}
	assert.Nil(t, json.Unmarshal(responseStr, withdrawInfo))
	assert.Equal(t, 0, withdrawInfo.Code)
	assert.Equal(t, 0.01, withdrawInfo.Data.WithdrawnProfit)
	assert.Equal(t, 0.2, withdrawInfo.Data.FeeRate)
	assert.NotEmpty(t, withdrawInfo.Data.FeeWallet)

	// create same fee tx: should return error
	body = map[string]interface{}{
		"chain_id":       "arb",
		"tx_hash":        "0x7d54e3d680eaab5aca5b75dbec1c7be89f9874b631126e2f3b4c819fe00f6d19",
		"wallet_address": "******************************************",
		"token":          "******************************************",
	}
	bodyStr, _ = json.Marshal(body)
	req, _ = http.NewRequest("POST", "/fee_transaction", bytes.NewBuffer(bodyStr))
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)
	responseStr, _ = io.ReadAll(w.Body)
	t.Logf("response 4: %v", string(responseStr))
	feeTxRes = &createFeeTransactionRes{}
	assert.Nil(t, json.Unmarshal(responseStr, feeTxRes))
	assert.Equal(t, 14001, feeTxRes.Code)
}

func setup(t *testing.T) (*gin.Engine, string) {
	// setup rdb
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationWallets(rdb.Get()))
	clientID := "20991a3ae83233d6de85d62906d71fd3"

	// init services
	application.Init(rdb.GormRepo())
	aave.Init(repo.Unified())
	alchemyapi.InitDefault()

	// setup firebase
	users, uid, _, _ := dbtest.User()
	kgErr := rdb.GormRepo().BatchSetUsers(context.Background(), users)
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Fatal(kgErr.Error)
	}
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	r := gin.Default()
	r.GET("/withdraw_info", auth.MockAuthorize(uid), auth.MockClientID(clientID), GetWithdrawInfo)
	r.POST("/fee_transaction", auth.MockAuthorize(uid), auth.MockClientID(clientID), CreateFeeTransaction)
	return r, uid
}
