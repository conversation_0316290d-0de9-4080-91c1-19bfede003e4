package user

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
)

func TestGetAddresses(t *testing.T) {
	rdb.Reset()
	// Create test users with different identifiers
	users, uids := dbtest.Users()
	phoneUser := uids[0]

	// Initialize user service
	user.Init(repo.Unified())

	// Save users to DB
	err := rdb.GormRepo().BatchSetUsers(context.Background(), users)
	assert.Nil(t, err)

	// Setup test router
	r := gin.Default()
	r.GET("/v1/studio/api/wallet/getAddresses", auth.MockAuthorize(""), GetAddresses)

	// Get the first user's information for validation
	userData, kgErr := rdb.GormRepo().GetUser(context.Background(), phoneUser, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	assert.Nil(t, kgErr)
	assert.NotNil(t, userData)
	assert.NotNil(t, userData.Wallets)
	assert.NotNil(t, userData.Wallets.DefaultReceiveWallets)

	phoneNumber := userData.PhoneNumber
	emailPtr := userData.Email

	t.Run("Success - Get by Phone Number", func(t *testing.T) {
		// Build query parameters
		query := url.Values{}
		query.Set("phone_number", phoneNumber)

		// Create request
		req, err := http.NewRequest("GET", "/v1/studio/api/wallet/getAddresses?"+query.Encode(), nil)
		assert.Nil(t, err)

		// Record response
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assert successful response
		assert.Equal(t, http.StatusOK, w.Code)

		// Verify response structure
		var resp AddressesByPhoneResp
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, 0, resp.Code)
		assert.Empty(t, resp.Error)
		assert.NotEmpty(t, resp.Data)

		// Verify addresses match expected format
		for chain, addr := range userData.Wallets.DefaultReceiveWallets {
			assert.Equal(t, addr.String(), resp.Data[chain.ID()])
		}
	})

	if emailPtr != nil {
		t.Run("Success - Get by Email", func(t *testing.T) {
			// Build query parameters
			query := url.Values{}
			query.Set("email", *emailPtr)

			// Create request
			req, err := http.NewRequest("GET", "/v1/studio/api/wallet/getAddresses?"+query.Encode(), nil)
			assert.Nil(t, err)

			// Record response
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			// Assert successful response
			assert.Equal(t, http.StatusOK, w.Code)

			// Verify response structure
			var resp AddressesByPhoneResp
			err = json.Unmarshal(w.Body.Bytes(), &resp)
			assert.Nil(t, err)
			assert.Equal(t, 0, resp.Code)
			assert.Empty(t, resp.Error)
			assert.NotEmpty(t, resp.Data)

			// Verify addresses match expected format
			for chain, addr := range userData.Wallets.DefaultReceiveWallets {
				assert.Equal(t, addr.String(), resp.Data[chain.ID()])
			}
		})
	}

	t.Run("Error - Missing Identifier", func(t *testing.T) {
		// Create request with no parameters
		req, err := http.NewRequest("GET", "/v1/studio/api/wallet/getAddresses", nil)
		assert.Nil(t, err)

		// Record response
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assert error response
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Verify response structure
		var resp map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, float64(code.ParamIncorrect), resp["code"])
		assert.Equal(t, "Missing identifier. Provide either phone_number or email query parameter.", resp["message"])
	})

	t.Run("Error - User Not Found", func(t *testing.T) {
		// Build query parameters with non-existent phone
		query := url.Values{}
		query.Set("phone_number", "+11112223333")

		// Create request
		req, err := http.NewRequest("GET", "/v1/studio/api/wallet/getAddresses?"+query.Encode(), nil)
		assert.Nil(t, err)

		// Record response
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assert error response
		assert.Equal(t, http.StatusNotFound, w.Code)

		// Verify response structure
		var resp map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, float64(code.UserNotFound), resp["code"])
		assert.Equal(t, "user not found", resp["message"])
	})
}
