package user

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type getUserShareKeyResp struct {
	Code int                 `json:"code"`
	Data getUserShareKeyData `json:"data"`
}

type getUserShareKeyData struct {
	ShareKey string `json:"share_key"`
}

// GetUserShareKey gets user share key
func GetUserShareKey(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", true, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	var shareKey string
	if user.ShareKey != nil {
		shareKey = *user.ShareKey
	}

	ctx.JSON(http.StatusOK, getUserShareKeyResp{
		Code: code.OK,
		Data: getUserShareKeyData{
			ShareKey: shareKey,
		},
	})
}

type upsertUserShareKeyReq struct {
	ShareKey string `json:"share_key"`
}

// UpsertUserShareKey upserts user share key
func UpsertUserShareKey(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	req := &upsertUserShareKeyReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	err := rdb.GormRepo().SaveUserShareKey(ctx.Request.Context(), uid, req.ShareKey)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, "upsert user share key error")
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}

// DeleteUserShareKey delete user share key
func DeleteUserShareKey(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	err := rdb.GormRepo().DeleteUserShareKey(ctx.Request.Context(), uid)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, "delete user share key error")
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}
