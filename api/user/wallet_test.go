package user

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/kryptogo/kg-wallet-backend/api/kms"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	ws "github.com/kryptogo/kg-wallet-backend/pkg/websocket"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	"github.com/kryptogo/kg-wallet-backend/service/user"
)

func TestUpsertUserWallets(t *testing.T) {
	rdb.Reset()
	users, _ := dbtest.Users()

	user.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	// init asset service
	mockAssetFetcher := domain.NewMockAssetFetcher(gomock.NewController(t))
	mockAssetFetcher.EXPECT().SupportedChains().Return(domain.ChainsMainnet).AnyTimes()
	mockAssetFetcher.EXPECT().SupportedTypes().Return([]domain.AssetType{domain.AssetTypeToken}).AnyTimes()
	mockAssetFetcher.EXPECT().GetAssets(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
		t.Logf("get assets for %v , %v , %v", address.String(), chains, types)

		// Assert address is one of expected
		assert.Contains(t, []string{
			"******************************************",
			"TK3RWzMqvPQgqME1kBugWN4tYTYXQPetW6",
			"******************************************",
			"******************************************",
			"TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb",
			"24KMp9UKpgPYgs1vU67Bt6imJ9W2yf4BZYfQjEwjXXQX",
		}, address.String())

		// Assert chains match expected combinations
		expectedChainMap := map[string][]domain.Chain{
			"******************************************":   {domain.Bitcoin},
			"TK3RWzMqvPQgqME1kBugWN4tYTYXQPetW6":           {domain.Tron},
			"******************************************":   domain.ChainsEVMMainnet,
			"******************************************":   domain.ChainsEVMMainnet,
			"TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb":           {domain.Tron},
			"24KMp9UKpgPYgs1vU67Bt6imJ9W2yf4BZYfQjEwjXXQX": {domain.Solana},
		}
		assert.Equal(t, expectedChainMap[address.String()], chains)
		assert.Equal(t, []domain.AssetType{domain.AssetTypeToken}, types)

		return &domain.AggregatedAssets{}, nil
	}).Times(6)
	mockChainClient := domain.NewMockChainClient(gomock.NewController(t))
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{domain.Tron: mockChainClient}, []domain.AssetFetcher{mockAssetFetcher}, []domain.TokenAmountsFetcher{}, nil)
	ws.Init(rdb.GormRepo())

	var uid, clientID string
	for userID := range users {
		uid = userID
		break
	}
	clientID = "test-client-id"

	// setup db and gin server
	url := "/v1/user/wallets/all"
	r := gin.Default()
	r.PUT(url, auth.MockAuthorize(uid), auth.MockClientID(clientID), UpsertUserWallets)

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	m := alchemyapi.NewMockIAlchemy(ctrl)
	{
		countOfWebhook := lo.Reduce(lo.Values(config.GetStringMap("ALCHEMY_WEBHOOK_ID_MAP")), func(count int, v string, _ int) int {
			if len(v) == 0 {
				return count
			}

			return count + 1
		}, 0)

		m.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), []string{
			"******************************************",
		}).
			Times(countOfWebhook * 2).
			DoAndReturn(func(ctx context.Context, webhookID string, addressesToAdd []string) error {
				t.Log("add", webhookID, addressesToAdd)
				return nil
			})
	}
	alchemyapi.Set(m)

	payload := []byte(`{
		"wallet_groups": [{
			"evm_wallets": [{
				"address": "******************************************",
				"display_name": "",
				"address_index": 0,
				"encrypted_private_key": "08191cf46579bb80a8982e926eae4bab1d0cb3added666bf989286357ed5ee8baa892efc011e19ca9fb3e27f0d116a7c83ec03a68ca8bd60f6b4f4bc5525b9d7b117866338798c4783d516aba5716906",
				"create_timestamp": 1687849415557,
				"import_timestamp": null,
				"icon_url": null,
				"wallet_type": "normal"
			}],
			"btc_wallets": [{
				"chain": "btc",
				"address": "******************************************",
				"display_name": "",
				"address_index": 0,
				"encrypted_private_key": "4a7a69b10048c16f8c6f3657a56ed831da6b528fe4ae4086a3b947ff23bcf9939b64a7efd2b4c830dff681e01ce1780a0c3e5967eb573ef560b5c381e93da0e97fbabb54b78c5abfd7a0c2c88b8465f8",
				"create_timestamp": 1687849415554,
				"import_timestamp": null,
				"icon_url": null,
				"wallet_type": "normal"
			}],
			"solana_wallets": [{
				"chain": "sol",
				"address": "24KMp9UKpgPYgs1vU67Bt6imJ9W2yf4BZYfQjEwjXXQX",
				"display_name": "",
				"address_index": 0,
				"encrypted_private_key": "ccb41dd8fd148407d72b1394e5ae53613958feb1df5dabcc12a1b2c013e14e266f4aded249b04a6adb564fa41bd3bf213750594bf96aaa97aff520b77c448c532b822a48057c0eb97f65da645ad2dec8",
				"create_timestamp": 1687849415554,
				"import_timestamp": null,
				"icon_url": null,
				"wallet_type": "normal"
			}],
			"tron_wallets": [{
				"chain": "tron",
				"address": "TK3RWzMqvPQgqME1kBugWN4tYTYXQPetW6",
				"display_name": "",
				"address_index": 0,
				"encrypted_private_key": "945ec95e54cab4e7970f52a1559f517fcbf93dea35971977a7d411d5dec97d8e8c132c2dcfdc3c34bc5df6f7474c03f5526722f46928b51845854e5d32ceaf44f3b830561aa87d6a6ce577ce1f931337",
				"create_timestamp": 1687849415555,
				"import_timestamp": null,
				"icon_url": null,
				"wallet_type": "normal"
			}],
			"create_timestamp": 1687778610716,
			"import_timestamp": 1687778610716,
			"encrypted_seed_phrase": "1417911081f96afd89c1637d7ea9801a07fac352a8b059e49886577a9e779c21a411826a99e29e9f4c4d03b6ee796da47f61b3bf05d7642735d10cda1430a5e2e3a7a45d0d2c0dc18e2025904505c57212b9268f1a219870a4596a8c7522ff8dd98ed051e7bd82fb7c22f68b6a4473d904cf0f072c522e8c4259e18d1a39",
			"display_name": "KryptoGO Dev",
			"icon_url": "https://wallet-static.kryptogo.com/public/assets/walletItemIcon/Doge-DarkBlue.png"
		}],
		"evm_wallets": [{
			"private_key": null,
			"address": "******************************************",
			"display_name": "",
			"address_index": 0,
			"create_timestamp": 1701062440102,
			"import_timestamp": null,
			"icon_url": null,
			"wallet_type": "observer"
		}],
		"wallets": [{
			"chain": "tron",
			"address": "TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb",
			"display_name": "",
			"address_index": 0,
			"encrypted_private_key": "945ec95e54cab4e7970f52a1559f517fcbf93dea35971977a7d411d5dec97d8e8c132c2dcfdc3c34bc5df6f7474c03f5526722f46928b51845854e5d32ceaf44f3b830561aa87d6a6ce577ce1f931337",
			"create_timestamp": 1687849415555,
			"import_timestamp": null,
			"icon_url": null,
			"wallet_type": "normal"
		}],
		"default_receive_wallets": {
			"arb": "******************************************",
			"bsc": "******************************************",
			"btc": "******************************************",
			"eth": "******************************************",
			"kcc": "******************************************",
			"matic": "******************************************",
			"sol": "24KMp9UKpgPYgs1vU67Bt6imJ9W2yf4BZYfQjEwjXXQX",
			"tron": "TK3RWzMqvPQgqME1kBugWN4tYTYXQPetW6",
			"ronin": "******************************************",
			"oasys": "******************************************",
			"base": "******************************************",
			"optimism": "******************************************"
		}
	}`)
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(payload))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	time.After(3 * time.Second)

	assert.Equal(t, http.StatusOK, w.Code)

	type resp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}

	var response resp

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Empty(t, response.Message)

	user, _ := rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	firstWallets := user.Wallets
	assert.NotNil(t, user)
	assert.NotNil(t, firstWallets)
	assert.True(t, len(firstWallets.WalletGroups) > 0)
	assert.Equal(t, "KryptoGO Dev", firstWallets.WalletGroups[0].DisplayName)
	assert.Equal(t, "https://wallet-static.kryptogo.com/public/assets/walletItemIcon/Doge-DarkBlue.png", user.Wallets.WalletGroups[0].IconURL)
	assert.Equal(t, "******************************************", user.Wallets.DefaultReceiveWallets[domain.BaseChain].String())

	// save 2nd time
	req, err = http.NewRequest("PUT", url, bytes.NewBuffer(payload))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Empty(t, response.Message)

	user, _ = rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	secondWallets := user.Wallets
	assert.NotNil(t, user)
	assert.NotNil(t, secondWallets)
	assert.True(t, len(secondWallets.WalletGroups) > 0)
	assert.Equal(t, "KryptoGO Dev", secondWallets.WalletGroups[0].DisplayName)
	assert.Equal(t, "https://wallet-static.kryptogo.com/public/assets/walletItemIcon/Doge-DarkBlue.png", user.Wallets.WalletGroups[0].IconURL)

	// compare 1st and 2nd wallets
	assert.Equal(t, firstWallets.DefaultReceiveWallets, secondWallets.DefaultReceiveWallets)
	assert.Len(t, firstWallets.WalletGroups, 1)
	assert.Len(t, secondWallets.WalletGroups, 1)
	assert.Equal(t, firstWallets.WalletGroups[0].EvmWallets[0].Address, secondWallets.WalletGroups[0].EvmWallets[0].Address)
	assert.Equal(t, firstWallets.WalletGroups[0].BtcWallets[0].Address, secondWallets.WalletGroups[0].BtcWallets[0].Address)
	assert.Equal(t, firstWallets.WalletGroups[0].SolanaWallets[0].Address, secondWallets.WalletGroups[0].SolanaWallets[0].Address)
	assert.Equal(t, firstWallets.WalletGroups[0].TronWallets[0].Address, secondWallets.WalletGroups[0].TronWallets[0].Address)
	assert.Len(t, firstWallets.EvmWallets, 1)
	assert.Len(t, secondWallets.EvmWallets, 1)
	assert.Equal(t, firstWallets.EvmWallets[0].Address, secondWallets.EvmWallets[0].Address)
}

// TestUpsertUserWalletsAddNewAddress tests the case where a new address is added to an existing wallet group
//
// 2024-08-21: This test is added since Charles found a bug where creating new addresses in the same wallet group fails.
func TestUpsertUserWalletsAddNewAddress(t *testing.T) {
	rdb.Reset()
	users, _ := dbtest.Users()

	user.Init(repo.Unified())
	asset.Init(repo.Unified(), map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, nil)
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	m := alchemyapi.NewMockIAlchemy(ctrl)
	{
		countOfWebhook := lo.Reduce(lo.Values(config.GetStringMap("ALCHEMY_WEBHOOK_ID_MAP")), func(count int, v string, _ int) int {
			if len(v) == 0 {
				return count
			}

			return count + 1
		}, 0)

		m.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), []string{
			"******************************************",
		}).
			Times(countOfWebhook * 2).
			DoAndReturn(func(ctx context.Context, webhookID string, addressesToAdd []string) error {
				t.Log("add", webhookID, addressesToAdd)
				return nil
			})
	}
	alchemyapi.Set(m)

	var uid, clientID string
	for userID := range users {
		uid = userID
		break
	}
	clientID = "test-client-id"

	// setup db and gin server
	url := "/v1/user/wallets/all"
	r := gin.Default()
	r.PUT(url, auth.MockAuthorize(uid), auth.MockClientID(clientID), UpsertUserWallets)

	// Initial payload with two wallet groups, second group has a Tron wallet only
	payload := testutil.ReadJsonFile("api/user/test/wallet_data_tron.json")
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(payload))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	type resp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}

	var response resp

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Empty(t, response.Message)

	user, _ := rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	firstWallets := user.Wallets
	assert.NotNil(t, user)
	assert.NotNil(t, firstWallets)
	assert.True(t, len(firstWallets.WalletGroups) > 0)

	// Verify the second wallet group has only Tron wallets
	assert.Len(t, firstWallets.WalletGroups[1].SolanaWallets, 0)
	assert.Len(t, firstWallets.WalletGroups[1].TronWallets, 1)
	assert.Equal(t, "TRufE59QRCUtpmfgeobsSVLa9eXsYTSzRb", firstWallets.WalletGroups[1].TronWallets[0].Address)

	// Updated payload with two wallet groups, second group has a Solana wallet and a Tron wallet
	payload2 := testutil.ReadJsonFile("api/user/test/wallet_data_sol_tron.json")
	req, err = http.NewRequest("PUT", url, bytes.NewBuffer(payload2))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Empty(t, response.Message)

	user, _ = rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	secondWallets := user.Wallets
	assert.NotNil(t, user)
	assert.NotNil(t, secondWallets)
	assert.True(t, len(secondWallets.WalletGroups) > 0)

	// Verify the second wallet group has both Solana and Tron wallets
	t.Logf("secondWallets 0: %+v", secondWallets.WalletGroups[0])
	t.Logf("secondWallets 1: %+v", secondWallets.WalletGroups[1])
	assert.Len(t, secondWallets.WalletGroups[1].SolanaWallets, 1)
	assert.Len(t, secondWallets.WalletGroups[1].TronWallets, 1)
	assert.Equal(t, "6vctU4CJ1LL5K3e2Lfk62eWQ4pxaWHceMjmTuyRsHtwv", secondWallets.WalletGroups[1].SolanaWallets[0].Address)
	assert.Equal(t, "TRufE59QRCUtpmfgeobsSVLa9eXsYTSzRb", secondWallets.WalletGroups[1].TronWallets[0].Address)
}

type wallet struct {
	Address   string  `json:"address"`
	Amount    float64 `json:"amount"`
	AmountStr string  `json:"amount_str"`
	UsdValue  float64 `json:"usd_value"`
}

func TestGetUserWalletsSortReceivingWallets(t *testing.T) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAssets(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAssetPrices(rdb.Get()))
	application.Init(rdb.GormRepo())

	users, uid, _, _ := dbtest.User()

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	url := "/v1/user/wallets"
	r := gin.Default()
	r.GET(url, auth.MockAuthorize(uid), GetUserWallets)

	// matic
	req, err := http.NewRequest("GET", url+"?chain_id=matic&sort_receiving_wallets=true", nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	type resp struct {
		Code int      `json:"code"`
		Data []wallet `json:"data"`
	}

	var response resp

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "******************************************", response.Data[0].Address)

	// tron
	req, err = http.NewRequest("GET", url+"?chain_id=tron&sort_receiving_wallets=true&balance=true&exclude_observer=true", nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", response.Data[0].Address)

	// shasta
	req, err = http.NewRequest("GET", url+"?chain_id=shasta&sort_receiving_wallets=true&exclude_observer=true", nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", response.Data[0].Address)
}

func setupKmsServer(t *testing.T) func() {
	// kms server
	rKMS := gin.Default()
	rKMS.POST("/v1/kms/generateMnemonicAddresses", kms.GenerateMnemonicAddresses)

	srv := &http.Server{
		Addr:    ":" + config.GetString("TEST_KMS_PORT"),
		Handler: rKMS,
	}
	go func() {
		err := srv.ListenAndServe()
		t.Logf("KMS server terminated with error: %v\n", err)
	}()

	time.Sleep(1 * time.Second) // wait for server to start
	return func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}

}

func TestDeleteUserWallets(t *testing.T) {
	rdb.Reset()
	users, _ := dbtest.Users()

	kgErr := rdb.GormRepo().BatchSetUsers(context.Background(), users)
	assert.Nil(t, kgErr)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	setupKmsServer(t)

	var uid, clientID string
	for userID := range users {
		uid = userID
		break
	}
	clientID = "test-client-id"

	// setup db and gin server
	url := "/v1/user/wallets"
	r := gin.Default()
	r.DELETE(url, auth.MockAuthorize(uid), auth.MockClientID(clientID), DeleteUserWallets)
	r.POST(url, auth.MockAuthorize(uid), auth.MockClientID(clientID), CreateUserWallets)

	req, err := http.NewRequest("DELETE", url, nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	type resp struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
	}

	var response resp

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Empty(t, response.Message)

	user, _ := rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	assert.NotNil(t, user)
	assert.Nil(t, user.Wallets)

	// create wallets
	req, err = http.NewRequest("POST", url, nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Empty(t, response.Message)

	user, _ = rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	assert.NotNil(t, user)
	assert.NotNil(t, user.Wallets)
}
