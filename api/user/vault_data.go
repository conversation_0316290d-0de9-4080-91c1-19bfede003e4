package user

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type getUserVaultDataResp struct {
	Code int            `json:"code"`
	Data *userVaultData `json:"data"`
}

type userVaultData struct {
	EncryptSalt       string            `json:"encrypt_salt"`
	EncryptedMnemonic string            `json:"encrypted_mnemonic"`
	VaultData         *domain.VaultData `json:"vault_data"`
}

// GetUserVaultData get user vault data
func GetUserVaultData(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", true, &domain.UserPreloads{
		WithVaultData: true,
	})
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	var encryptSalt, encryptedMnemonic string
	if user.EncryptSalt != nil {
		encryptSalt = *user.EncryptSalt
	}
	if user.EncryptedMnemonic != nil {
		encryptedMnemonic = *user.EncryptedMnemonic
	}

	data := userVaultData{
		EncryptSalt:       encryptSalt,
		EncryptedMnemonic: encryptedMnemonic,
		VaultData:         user.VaultData,
	}

	resp := getUserVaultDataResp{
		Code: code.OK,
		Data: &data,
	}

	ctx.JSON(http.StatusOK, resp)
}

// UpsertUserVaultData upsert user vault data
func UpsertUserVaultData(ctx *gin.Context) {
	req := &domain.VaultData{}
	kgErr := util.ToGinContextExt(ctx).BindJson(req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if len(req.AccountPublicKey) == 0 || len(req.EncryptSalt) == 0 || len(req.EncryptedAccountPrivateKey) == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid params")
		return
	}

	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", false, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	if err := rdb.GormRepo().SaveUserVaultData(ctx.Request.Context(), user.UID, req); err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}

// DeleteUserVaultData delete user vault data
func DeleteUserVaultData(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", false, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	if err := rdb.GormRepo().DeleteUserVaultData(ctx.Request.Context(), user.UID); err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}
