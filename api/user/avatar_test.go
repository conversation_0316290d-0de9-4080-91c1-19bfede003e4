package user

import (
	"bytes"
	"context"
	"encoding/json"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/google/storage"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/assert"
)

type testUploadAvatarResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		ImageURL string `json:"image_url"`
	} `json:"data"`
}

type testSetAvatarResp struct {
	Code      int     `json:"code"`
	Message   string  `json:"message"`
	AvatarURL *string `json:"avatar_url"`
}

// To upload to real google storage bucket, remove the STORAGE_EMULATOR_HOST environment variable
// and set the PROJECT_ID=kryptogo-wallet-app-dev
func TestUpdateUserAvatar(t *testing.T) {
	// Setup
	rdb.Reset()
	users, _ := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())
	storage.InitDefault(domain.NewAllPassRateLimiter())
	var uid string
	for userID := range users {
		uid = userID
		break
	}
	// Setup db and gin server
	url := "/v1/user/avatar"
	r := gin.Default()
	r.PUT(url, auth.MockAuthorize(uid), SetAvatar)
	r.POST(url, auth.MockAuthorize(uid), UploadAvatar)

	t.Run("upload avatar success", func(t *testing.T) {
		// Prepare request body
		testImagePath := "test/test.webp"
		image, err := os.ReadFile(testImagePath)
		if err != nil {
			t.Fatalf("failed to read image: %v", err)
		}

		// Create multipart form
		var requestBody bytes.Buffer
		writer := multipart.NewWriter(&requestBody)
		part, err := writer.CreateFormFile("image", "test.webp")
		if err != nil {
			t.Fatalf("failed to create form file: %v", err)
		}
		part.Write(image)
		writer.Close()

		// Make request
		req, err := http.NewRequest("POST", url, &requestBody)
		req.Header.Set("Content-Type", writer.FormDataContentType())
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assertions
		assert.Equal(t, http.StatusOK, w.Code)
		var resp testUploadAvatarResp
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, 0, resp.Code)
		assert.NotEmpty(t, resp.Data.ImageURL)
		assert.Contains(t, resp.Data.ImageURL, "https://storage.googleapis.com/")
		assert.Contains(t, resp.Data.ImageURL, "public/avatar/")
		assert.Contains(t, resp.Data.ImageURL, ".webp")
	})

	t.Run("set avatar failed: bad avatar url", func(t *testing.T) {
		var requestBody = struct {
			AvatarURL string `json:"avatar_url"`
		}{
			AvatarURL: "https://unknown-origin.com/avatar.webp",
		}
		jsonBody, err := json.Marshal(requestBody)
		assert.Nil(t, err)
		req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonBody))
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
		var resp testSetAvatarResp
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, code.ParamIncorrect, resp.Code)
		assert.Equal(t, "Bad avatar url.", resp.Message)
		assert.Nil(t, resp.AvatarURL)
	})

	t.Run("set avatar success", func(t *testing.T) {
		var requestBody = struct {
			AvatarURL string `json:"avatar_url"`
		}{
			AvatarURL: "https://storage.googleapis.com/kryptogo-wallet-app.appspot.com/public/avatar/kryptogo-ci-test.webp",
		}
		jsonBody, err := json.Marshal(requestBody)
		assert.Nil(t, err)
		req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonBody))
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
		var resp testSetAvatarResp
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, 0, resp.Code)
		assert.Empty(t, resp.Message)
		assert.NotNil(t, resp.AvatarURL)
		assert.Equal(t, requestBody.AvatarURL, *resp.AvatarURL)
	})
}
