package user

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	userservice "github.com/kryptogo/kg-wallet-backend/service/user"
)

type updatePasswordReq struct {
	Password             string            `json:"password" binding:"required"`
	IsRandomPassword     *bool             `json:"is_random_password"`
	PasswordSaltFrontend *string           `json:"password_salt_frontend"`
	VaultData            *domain.VaultData `json:"vault_data" binding:"required"`
}

// UpdatePassword update user password
func UpdatePassword(ctx *gin.Context) {
	req := &updatePasswordReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if len(req.Password) == 0 || req.VaultData == nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid params")
		return
	}

	uid := auth.GetUID(ctx)

	user, _ := userservice.GetByUID(ctx.Request.Context(), uid, "", false, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	if err := userservice.UpdatePassword(ctx.Request.Context(),
		user.UID,
		req.Password,
		req.PasswordSaltFrontend,
		req.IsRandomPassword,
		req.VaultData); err != nil {
		kglog.ErrorWithData("save password failed", map[string]interface{}{
			"uid": uid,
		})
		response.KGError(ctx, err)
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}

type getPasswordSaltReq struct {
	PhoneNumber string `form:"phone_number"`
	Email       string `form:"email"`
	Handle      string `form:"handle"`
}

// GetPasswordSaltFrontend get user password salt frontend
func GetPasswordSaltFrontend(ctx *gin.Context) {
	req := &getPasswordSaltReq{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if req.PhoneNumber == "" && req.Email == "" && req.Handle == "" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid params")
		return
	}

	ip := ctx.ClientIP()

	// check ratelimit
	if !cache.CheckPasswordSaltFrontendRateLimit(ctx.Request.Context(), ip) {
		kglog.Debugf("GetPasswordSaltFrontend, rate limit, ip: %s", ip)
		response.TooManyRequestsWithMsg(ctx, code.RateLimit, "reach get password salt rate limit")
		return

	}

	if len(req.PhoneNumber) > 0 {
		if !cache.CheckPasswordSaltFrontendByPhone(ctx.Request.Context(), req.PhoneNumber) {
			kglog.Debugf("GetPasswordSaltFrontend, rate limit, phone: %s", req.PhoneNumber)
			response.TooManyRequestsWithMsg(ctx, code.RateLimit, "reach get password salt rate limit")
			return
		}
	}

	if len(req.Email) > 0 {
		if !cache.CheckPasswordSaltFrontendByEmail(ctx.Request.Context(), req.Email) {
			kglog.Debugf("GetPasswordSaltFrontend, rate limit, email: %s", req.Email)
			response.TooManyRequestsWithMsg(ctx, code.RateLimit, "reach get password salt rate limit")
			return
		}
	}

	if len(req.Handle) > 0 {
		if !cache.CheckPasswordSaltFrontendByHandle(ctx.Request.Context(), req.Handle) {
			kglog.Debugf("GetPasswordSaltFrontend, rate limit, handle: %s", req.Handle)
			response.TooManyRequestsWithMsg(ctx, code.RateLimit, "reach get password salt rate limit")
			return
		}
	}

	var user *domain.UserData
	if req.PhoneNumber != "" {
		user, _ = userservice.GetByPhoneNumber(ctx.Request.Context(), req.PhoneNumber, "", true, nil)
	} else if req.Email != "" {
		user, _ = userservice.GetByEmail(ctx.Request.Context(), req.Email, "", true, nil)
	} else if req.Handle != "" {
		user, _ = userservice.GetByHandle(ctx.Request.Context(), req.Handle, "", true, nil)
	}

	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	var salt string
	if user.PasswordSaltFrontend != nil && *user.PasswordSaltFrontend != "" {
		salt = *user.PasswordSaltFrontend
	} else {
		salt = domain.PasswordSaltFrontendOldVersion
	}

	response.OK(ctx, salt)
}
