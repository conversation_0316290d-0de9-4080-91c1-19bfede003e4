package user

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

type decentralizedApp struct {
	Title    string `json:"title"`
	Desc     string `json:"desc"`
	ImageURL string `json:"image_url"`
	SiteURL  string `json:"site_url"`
}

type organizationResp struct {
	Name              string             `json:"name"`
	DecentralizedApps []decentralizedApp `json:"decentralized_apps"`
}

// Organizations returns user organizations
func Organizations(c *gin.Context) {
	ctx := c.Request.Context()
	userID := c.GetString("uid")

	organizations, err := rdb.GetOrgsByCustomer(ctx, userID)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.DBError, err.Error())
		return
	}

	orgs := make([]organizationResp, 0, len(organizations))

	// only for kg wallet
	if !application.IsKgWallet(ctx, auth.GetClientID(c)) {
		response.OK(c, orgs)
		return
	}

	for _, org := range organizations {
		orgResp := organizationResp{
			Name: org.Name,
		}

		kycStatus, kgErr := customer.GetCustomerKycStatusFromDB(ctx, userID, org.ID)
		if kgErr != nil {
			kglog.WarningWithDataCtx(ctx, "GetCustomerKycStatusFromDB", kgErr)
			continue
		}

		// only for verified
		if kycStatus != domain.KycStatusVerified.String() {
			continue
		}

		dapps, kgErr := rdb.GormRepo().GetActiveDapps(ctx, org.ID)
		if kgErr != nil {
			kglog.WarningWithDataCtx(ctx, "GetActiveDapps", kgErr)
			continue
		}
		if len(dapps) == 0 {
			continue
		}

		orgResp.DecentralizedApps = make([]decentralizedApp, 0, len(dapps))
		for _, dapp := range dapps {
			orgResp.DecentralizedApps = append(orgResp.DecentralizedApps, decentralizedApp{
				Title:    dapp.Title,
				Desc:     dapp.Desc,
				ImageURL: dapp.ImageURL,
				SiteURL:  dapp.SiteURL,
			})
		}

		orgs = append(orgs, orgResp)
	}

	response.OK(c, orgs)
}
