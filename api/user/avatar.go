package user

import (
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"strings"

	"mime"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/google/storage"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/user"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type setAvatarRequest struct {
	ChainID         string `json:"chain_id"`
	ContractAddress string `json:"contract_address"`
	TokenID         string `json:"token_id"`
	AvatarURL       string `json:"avatar_url"`
}

type setAvatarResp struct {
	Code      int     `json:"code"`
	AvatarURL *string `json:"avatar_url"`
}

// SetAvatar set avatar
func SetAvatar(c *gin.Context) {
	ctx := c.Request.Context()
	req := setAvatarRequest{}
	kgErr := util.ToGinContextExt(c).BindJson(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if req.AvatarURL != "" {
		if !strings.Contains(req.AvatarURL, "https://storage.googleapis.com/kryptogo") {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "Bad avatar url.")
			return
		}
	} else if req.ChainID == "" || req.ContractAddress == "" || req.TokenID == "" {
		// Either provide AvatarURL or (ChainID, ContractAddress, TokenID)
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Either avatar_url or (chain_id, contract_address, token_id) must be provided")
		return
	}

	uid := auth.GetUID(c)
	clientID := oauth.ClientID(c)
	url, errCode, err := user.SetUserAvatar(ctx, uid, clientID, user.SetUserAvatarParams{
		AvatarURL:       req.AvatarURL,
		ChainID:         req.ChainID,
		ContractAddress: req.ContractAddress,
		TokenID:         req.TokenID,
	})
	if err != nil {
		response.BadRequestWithMsg(c, errCode, err.Error())
		return
	}

	applicationID, apiTokenName, _ := application.GetSendbirdAppIDAndAPIToken(ctx, clientID)
	if applicationID != "" && apiTokenName != "" {
		// update sendbird user
		sendbirdClient := sendbirdapi.NewClient(applicationID, apiTokenName)
		sendbirdService := chatroom.GetService(sendbirdClient)
		_, _, err := sendbirdService.UpdateAUser(ctx, uid, nil, util.Ptr(url))
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "sendbird update a user error", map[string]interface{}{
				"uid": uid,
				"err": err.Error(),
			})
		}
	}

	resp := setAvatarResp{
		AvatarURL: &url,
	}
	c.JSON(http.StatusOK, resp)
}

// UploadAvatar upload avatar image
func UploadAvatar(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.user.UploadAvatar")
	defer span.End()

	// backward compatibility: if content is json, call SetAvatar instead
	if c.ContentType() == "application/json" {
		SetAvatar(c)
		return
	}

	// Parse the multipart form to retrieve the image file
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to retrieve image file", map[string]interface{}{
			"error": err.Error(),
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid image file")
		return
	}
	defer file.Close()

	fileBytes, err := io.ReadAll(file)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to read image file", map[string]interface{}{
			"error": err.Error(),
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid image file")
		return
	}

	// Determine the MIME type by inspecting the file content
	mimeType := mime.TypeByExtension(header.Filename)
	if mimeType == "" {
		mimeType = http.DetectContentType(fileBytes)
	}

	if !strings.HasPrefix(mimeType, "image/") {
		response.BadRequestWithMsg(c, code.ParamIncorrect, fmt.Sprintf("Invalid image mime type: %s", mimeType))
		return
	}

	// encode file to base64
	encodedImage := base64.StdEncoding.EncodeToString(fileBytes)
	uid := auth.GetUID(c)
	fileName := uid
	imageURL, err := storage.Get().UploadImage(ctx, "public/avatar", fileName, encodedImage, mimeType)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Image upload failed", map[string]interface{}{
			"error": err.Error(),
		})
		response.InternalServerErrorWithMsg(c, code.FileUploadFailed, err.Error())
		return
	}

	// Create the response payload with the image URL
	resp := map[string]interface{}{
		"code": 0,
		"data": map[string]interface{}{
			"image_url": imageURL,
		},
	}

	// Send the successful response
	c.JSON(http.StatusOK, resp)
}
