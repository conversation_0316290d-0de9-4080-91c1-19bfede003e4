package user

import (
	"encoding/json"
	"io"
	"math"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	orgservice "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	userservice "github.com/kryptogo/kg-wallet-backend/service/user"
)

var (
	region    = config.GetString("REGION")
	projectID = config.GetString("PROJECT_ID")
)

type getUsersByPhoneNumbersRequest struct {
	PhoneNumbers []string `json:"phone_numbers"`
}

type getUsersByPhoneNumbersResp struct {
	Code   int                                   `json:"code"`
	Data   getUsersByPhoneNumbersData            `json:"data"`
	Result map[string]getUsersByPhoneNumbersData `json:"result"`
}

type getUsersByPhoneNumbersData map[string]userContactInfo
type userContactInfo map[string]string

// GetUsersByPhoneNumbers get users by phone numbers
func GetUsersByPhoneNumbers(ctx *gin.Context) {
	c := ctx.Request.Context()
	params := &getUsersByPhoneNumbersRequest{}
	kgErr := util.ToGinContextExt(ctx).BindJson(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if len(params.PhoneNumbers) == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "phone_numbers is required")
		return
	}

	data := getUsersByPhoneNumbersData{}
	batchCnt := 10
	total := len(params.PhoneNumbers)
	for i := 0; i < ((total-1)/batchCnt)+1; i++ {
		from := i * batchCnt
		to := int(math.Min(float64((i+1)*batchCnt), float64(total)))
		users, kgErr := rdb.GormRepo().GetUsersByPhoneNumbers(c, params.PhoneNumbers[from:to], "", true, &domain.UserPreloads{
			WithAvatar:  true,
			WithWallets: true,
		})
		if kgErr != nil {
			response.KGError(ctx, kgErr)
			return
		}

		for _, user := range users {
			if user == nil {
				continue
			}
			info := userContactInfo{}
			info["uid"] = user.UID
			if user.Avatar != nil {
				info["avatar_url"] = user.Avatar.AvatarURL
			}
			if user.Wallets == nil || user.Wallets.DefaultReceiveWallets == nil {
				for _, chainID := range model.EVMChainIDs {
					if user.EthereumAddress != nil {
						info[chainID] = *user.EthereumAddress
					}
				}
				if user.BitcoinAddress != nil {
					info[model.ChainIDBitcoin] = *user.BitcoinAddress
				}
				if user.SolanaAddress != nil {
					info[model.ChainIDSolana] = *user.SolanaAddress
				}
				if user.TronAddress != nil {
					info[model.ChainIDTron] = *user.TronAddress
				}
			} else {
				receiveWallet := user.Wallets.DefaultReceiveWallets
				for chain, addr := range receiveWallet {
					info[chain.ID()] = addr.String()
				}
			}
			data[user.PhoneNumber] = info
		}
	}

	resp := getUsersByPhoneNumbersResp{
		Code: code.OK,
		Data: data,
		Result: map[string]getUsersByPhoneNumbersData{
			"data": data,
		},
	}

	ctx.JSON(http.StatusOK, resp)
}

type getUserRequest struct {
	UID         string `form:"uid"`
	PhoneNumber string `form:"phone_number"`
}

type getUserResponse struct {
	Code int              `json:"code"`
	Data *domain.UserData `json:"data"`
}

// GetUser get user by uid or phone number
func GetUser(ctx *gin.Context) {
	c := ctx.Request.Context()
	resp := new(getUserResponse)
	params := &getUserRequest{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if len(params.UID) == 0 && len(params.PhoneNumber) == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "uid or phone_number is required")
		return
	}

	clientID := oauth.ClientID(ctx)

	var user *domain.UserData
	if len(params.UID) > 0 {
		user, _ = rdb.GormRepo().GetUser(c, params.UID, clientID, true, &domain.UserPreloads{
			WithFcm:                    true,
			WithAvatar:                 true,
			WithLocale:                 true,
			WithReadAllTimeStamp:       true,
			WithPrivacyPolicyAgreement: true,
			WithWallets:                true,
			WithVaultData:              true,
			WithGoogleAccessTokens:     true,
		})
		if user == nil {
			response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
			return
		}
	} else if len(params.PhoneNumber) > 0 {
		user, _ = rdb.GormRepo().GetUserByPhoneNumber(c, params.PhoneNumber, clientID, true, &domain.UserPreloads{
			WithFcm:                    true,
			WithAvatar:                 true,
			WithLocale:                 true,
			WithReadAllTimeStamp:       true,
			WithPrivacyPolicyAgreement: true,
			WithWallets:                true,
			WithVaultData:              true,
			WithGoogleAccessTokens:     true,
		})
		if user == nil {
			response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
			return
		}
	}
	resp.Data = user
	ctx.JSON(http.StatusOK, resp)
}

type getUserInfoResp struct {
	Code int             `json:"code"`
	Data domain.UserInfo `json:"data"`
}

// GetUserInfo get user info
func GetUserInfo(ctx *gin.Context) {
	c := ctx.Request.Context()
	uid := auth.GetUID(ctx)
	clientID := auth.GetClientID(ctx)
	user, _ := rdb.GormRepo().GetUser(c, uid, clientID, false, &domain.UserPreloads{
		WithFcm:                    true,
		WithAvatar:                 true,
		WithLocale:                 true,
		WithReadAllTimeStamp:       true,
		WithPrivacyPolicyAgreement: true,
	})
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	firebaseUser, err := firebase.GetUserByUID(c, uid)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.FirebaseFailed, err.Error())
		return
	}
	_, hasLinkedGoogle := firebase.HasLinkedGoogle(firebaseUser)
	user.HasLinkedGoogle = util.Ptr(hasLinkedGoogle)

	domain.FilterClientFcmTokens(user, clientID)

	resp := &getUserInfoResp{
		Code: 0,
		Data: user.UserInfo,
	}
	ctx.JSON(http.StatusOK, resp)
}

// UpdateUserInfo update user info
func UpdateUserInfo(ctx *gin.Context) {
	c := ctx.Request.Context()
	uid := auth.GetUID(ctx)
	clientID := oauth.ClientID(ctx)
	user, _ := rdb.GormRepo().GetUser(c, uid, "", false, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	data, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, err.Error())
		return
	}
	params := map[string]interface{}{}
	err = json.Unmarshal(data, &params)
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, err.Error())
		return
	}
	if user.Handle != nil && params["handle"] != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "handle can only be set once")
		return
	}

	kgErr := userservice.UpdateUserInfo(c, uid, clientID, params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	if displayName, ok := params["display_name"]; ok {
		applicationID, apiTokenName, _ := application.GetSendbirdAppIDAndAPIToken(c, clientID)
		if applicationID != "" && apiTokenName != "" {
			// update sendbird user
			sendbirdClient := sendbirdapi.NewClient(applicationID, apiTokenName)
			sendbirdService := chatroom.GetService(sendbirdClient)
			_, _, err := sendbirdService.UpdateAUser(c, uid, util.Ptr(displayName.(string)), nil)
			if err != nil {
				kglog.ErrorWithDataCtx(c, "sendbird update a user error", map[string]interface{}{
					"uid": uid,
					"err": err.Error(),
				})
			}
		}
	}
	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}

// DeleteUser delete user in firebase and fireauth
func DeleteUser(c *gin.Context) {
	ctx := c.Request.Context()
	uid := auth.GetUID(c)

	u, _ := rdb.GormRepo().GetUser(c, uid, "", false, nil)
	if u == nil {
		response.BadRequestWithMsg(c, code.UserNotFound, "user not found")
		return
	}

	// TODO: check is user is only one owner in studio org

	if kgErr := userservice.DeleteUser(ctx, uid); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// disables studio user on all orgs
	kgErr := orgservice.DisableUserInAllOrgs(ctx, uid)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 0})
}

type dataExistResp struct {
	Code int           `json:"code"`
	Data dataExistData `json:"data"`
}

type dataExistData struct {
	HasPassword bool `json:"has_password"`
	HasWallet   bool `json:"has_wallet"`
	HasShareKey bool `json:"has_share_key"`
}

// DataExist check if user data exist
func DataExist(ctx *gin.Context) {
	c := ctx.Request.Context()
	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(c, uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	resp := &dataExistResp{
		Code: code.OK,
		Data: dataExistData{
			HasPassword: user.Password != nil && len(*user.Password) > 0,
			HasWallet:   user.Wallets != nil,
			HasShareKey: user.ShareKey != nil && len(*user.ShareKey) > 0,
		},
	}

	ctx.JSON(http.StatusOK, resp)
}
