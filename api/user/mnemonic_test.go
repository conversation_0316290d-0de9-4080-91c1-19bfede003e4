package user

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/kms"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// setupMockKmsServer sets up a mock KMS server for testing
func setupMockKmsServer(t *testing.T) func() {
	// kms server
	rKMS := gin.Default()
	rKMS.POST("/v1/kms/generateMnemonicAddresses", kms.GenerateMnemonicAddresses)

	srv := &http.Server{
		Addr:    ":" + config.GetString("TEST_KMS_PORT"),
		Handler: rKMS,
	}
	go func() {
		err := srv.ListenAndServe()
		t.Logf("KMS server terminated with error: %v\n", err)
	}()

	time.Sleep(1 * time.Second) // wait for server to start
	return func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}
}

// TestCreateAndGetAddressesByEmail tests the complete API flow of:
// 1. Creating a user with email via the CreateUserWithMnemonic endpoint
// 2. Retrieving the user's addresses using the GetAddresses endpoint
func TestCreateAndGetAddressesByEmail(t *testing.T) {
	// Set up test environment
	rdb.Reset()
	user.Init(repo.Unified())

	// Setup mock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Mock Alchemy service
	mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
	mockAlchemy.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil)
	alchemyapi.Set(mockAlchemy)

	// Start the KMS server mock and ensure it's shutdown after test
	shutdownKms := setupMockKmsServer(t)
	defer shutdownKms()

	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Register the API handlers
	router.POST("/api/user/createUserWithMnemonic", CreateUserWithMnemonic)
	router.GET("/api/user/getAddresses", GetAddresses)

	// Generate a random email for testing
	testEmail := "test-" + util.RandString(8) + "@example.com"

	// Step 1: Test user creation with email
	t.Run("Create User With Email", func(t *testing.T) {
		// Prepare request body
		requestBody := map[string]interface{}{
			"email": testEmail,
		}
		jsonData, err := json.Marshal(requestBody)
		assert.NoError(t, err)

		// Create a test HTTP request
		req, err := http.NewRequest("POST", "/api/user/createUserWithMnemonic", bytes.NewBuffer(jsonData))
		assert.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")

		// Create a response recorder
		resp := httptest.NewRecorder()

		// Serve the request
		router.ServeHTTP(resp, req)

		// Check response
		assert.Equal(t, http.StatusOK, resp.Code, "Expected HTTP 200 OK status")

		// Parse response
		var responseBody map[string]interface{}
		err = json.Unmarshal(resp.Body.Bytes(), &responseBody)
		assert.NoError(t, err, "Should be able to parse JSON response")

		// Verify success response
		assert.Equal(t, float64(0), responseBody["code"], "Should return code 0 for success")
	})

	// Step 2: Get addresses by email - with retries since it might take time for the database to update
	t.Run("Get Addresses By Email", func(t *testing.T) {
		var responseBody struct {
			Code int               `json:"code"`
			Data map[string]string `json:"data"`
		}

		// Retry getting addresses a few times with a delay
		maxRetries := 3
		var gotAddresses bool

		for i := 0; i < maxRetries; i++ {
			// Create a test HTTP request
			req, err := http.NewRequest("GET", "/api/user/getAddresses?email="+testEmail, nil)
			assert.NoError(t, err)

			// Create a response recorder
			resp := httptest.NewRecorder()

			// Serve the request
			router.ServeHTTP(resp, req)

			// Check response
			if resp.Code != http.StatusOK {
				t.Logf("Attempt %d: Got non-200 status code: %d, waiting and retrying...", i+1, resp.Code)
				time.Sleep(1 * time.Second)
				continue
			}

			// Try to parse response
			err = json.Unmarshal(resp.Body.Bytes(), &responseBody)
			if err != nil {
				t.Logf("Attempt %d: Failed to parse response: %v, waiting and retrying...", i+1, err)
				time.Sleep(1 * time.Second)
				continue
			}

			// Check for success code
			if responseBody.Code != 0 {
				t.Logf("Attempt %d: Got non-zero code: %d, waiting and retrying...", i+1, responseBody.Code)
				time.Sleep(1 * time.Second)
				continue
			}

			// Check if data is empty
			if len(responseBody.Data) == 0 {
				t.Logf("Attempt %d: Got empty data, waiting and retrying...", i+1)
				time.Sleep(1 * time.Second)
				continue
			}

			// If we get here, we have successful data
			gotAddresses = true
			break
		}

		// Assert that we got addresses
		assert.True(t, gotAddresses, "Should eventually get addresses")

		if gotAddresses {
			// Verify success response
			assert.Equal(t, 0, responseBody.Code, "Should return code 0 for success")

			// Check if addresses were returned
			assert.NotEmpty(t, responseBody.Data, "Should return non-empty addresses")

			// Check for specific chains
			chains := []string{
				domain.Ethereum.ID(),
				domain.Bitcoin.ID(),
				domain.Solana.ID(),
				domain.Tron.ID(),
			}

			for _, chainID := range chains {
				address, exists := responseBody.Data[chainID]
				assert.True(t, exists, "Should have address for chain "+chainID)
				if exists {
					assert.NotEmpty(t, address, "Address for chain "+chainID+" should not be empty")
				}
			}
		}
	})
}
