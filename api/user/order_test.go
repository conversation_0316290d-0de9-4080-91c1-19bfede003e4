package user

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
)

func TestCreateCryptoTOS(t *testing.T) {
	s := assert.New(t)
	rdb.Reset()
	users, uids := dbtest.Users()
	uid := uids[0]
	user.Init(repo.Unified())
	organization.Init(organization.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})
	s.NoError(rdb.CreateSeedData())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	r := gin.Default()
	r.GET("/v1/user/crypto/agree_tos", auth.MockAuthorize(uid), auth.MockClientID("20991a3ae83233d6de85d62906d71fd3"), GetCryptoAgreeTos)
	r.POST("/v1/user/crypto/agree_tos", auth.MockAuthorize(uid), auth.MockClientID("20991a3ae83233d6de85d62906d71fd3"), CreateCryptoAgreeTos)

	db := rdb.Get()
	now := time.Now()
	{ // create tos V1
		s.NoError(db.Create(&model.CryptoTosContent{
			OrganizationID: 1,
			Version:        "V1",
			Content:        "V1 Content",
			CreatedAt:      now,
		}).Error)
	}
	{ // get V1 tos -> not accepted yet
		req, err := http.NewRequest(http.MethodGet, "/v1/user/crypto/agree_tos", nil)
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		var resp struct {
			Code int `json:"code"`
			Data struct {
				AgreeVersion  string `json:"agree_version"`
				LatestVersion string `json:"latest_version"`
				TosContent    string `json:"tos_content"`
			} `json:"data"`
		}

		s.Equal(http.StatusOK, w.Code)
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.Equal(0, resp.Code)
		s.Equal("", resp.Data.AgreeVersion)
		s.Equal("V1", resp.Data.LatestVersion)
		s.Equal("V1 Content", resp.Data.TosContent)
	}
	{ // accept tos V1
		req, err := http.NewRequest(http.MethodPost, "/v1/user/crypto/agree_tos", nil)
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)
	}
	{ // get tos ->  V1 accepted
		req, err := http.NewRequest(http.MethodGet, "/v1/user/crypto/agree_tos", nil)
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		var resp struct {
			Code int `json:"code"`
			Data struct {
				AgreeVersion  string `json:"agree_version"`
				LatestVersion string `json:"latest_version"`
				TosContent    string `json:"tos_content"`
			} `json:"data"`
		}

		s.Equal(http.StatusOK, w.Code)
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.Equal(0, resp.Code)
		s.Equal("V1", resp.Data.AgreeVersion)
		s.Equal("V1", resp.Data.LatestVersion)
		s.Equal("", resp.Data.TosContent)
	}
	{ // accept V1 tos -> already accepted
		req, err := http.NewRequest(http.MethodPost, "/v1/user/crypto/agree_tos", nil)
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		var resp struct {
			Code int `json:"code"`
			Data struct {
				AgreeVersion  string `json:"agree_version"`
				LatestVersion string `json:"latest_version"`
				TosContent    string `json:"tos_content"`
			} `json:"data"`
		}

		s.Equal(http.StatusBadRequest, w.Code)
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.Equal(2105, resp.Code)
	}
	{ // create tos V2
		s.NoError(db.Create(&model.CryptoTosContent{
			OrganizationID: 1,
			Version:        "V2",
			Content:        "V2 Content",
			CreatedAt:      now.Add(time.Second),
		}).Error)

		var contents []model.CryptoTosContent
		s.NoError(db.Find(&contents).Error)
		s.Len(contents, 2)
	}
	{ // get v2 tos -> not accepted yet
		req, err := http.NewRequest(http.MethodGet, "/v1/user/crypto/agree_tos", nil)
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		var resp struct {
			Code int `json:"code"`
			Data struct {
				AgreeVersion  string `json:"agree_version"`
				LatestVersion string `json:"latest_version"`
				TosContent    string `json:"tos_content"`
			} `json:"data"`
		}

		s.Equal(http.StatusOK, w.Code)
		s.NoError(json.Unmarshal(w.Body.Bytes(), &resp))
		s.Equal(0, resp.Code)
		s.Equal("V1", resp.Data.AgreeVersion)
		s.Equal("V2", resp.Data.LatestVersion)
		s.Equal("V2 Content", resp.Data.TosContent)
	}
	{ // create tos V2 -> accepted
		req, err := http.NewRequest(http.MethodPost, "/v1/user/crypto/agree_tos", nil)
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		s.Equal(http.StatusOK, w.Code)
	}
}
