package user

import (
	"errors"
	"net/http"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	thegraphapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/thegraph-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/user"
)

type getUserWalletsParams struct {
	ChainID              string `form:"chain_id" binding:"required"`
	Balance              bool   `form:"balance"`
	ExcludeObserver      *bool  `form:"exclude_observer"`
	SortReceivingWallets *bool  `form:"sort_receiving_wallets"`
}

// GetUserWallets returns user wallets
func GetUserWallets(ctx *gin.Context) {
	params := &getUserWalletsParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	var chainIDSet = model.ChainIDSet
	if config.IsDev() {
		chainIDSet = model.DevChainIDSet
	}

	if _, ok := chainIDSet[params.ChainID]; !ok {
		response.BadRequestWithMsg(ctx, code.ChainIDNotSupported, "chain id not supported")
		return
	}

	uid := auth.GetUID(ctx)
	userData, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if userData == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	data := userData.AddressesByChains("", []string{params.ChainID}, util.Val(params.ExcludeObserver))
	resp := []rdb.Wallet{}
	if params.Balance {
		balanceParams := &rdb.BalancesParams{}
		balanceParams.ChainIDs = []string{params.ChainID}
		balanceParams.WalletAddresses = data[params.ChainID]
		balanceParams.AssetTypes = model.AllAssetTypes
		walletBalances, err := rdb.GetBalancesSumByAddresses(ctx.Request.Context(), balanceParams)
		if err != nil {
			kglog.Warnf("get balances failed: %v", err)
			response.BadRequestWithMsg(ctx, code.DBError, err.Error())
			return
		}
		resp = *walletBalances
	} else {
		for _, walletAddress := range data[params.ChainID] {
			resp = append(resp, rdb.Wallet{
				Address: walletAddress,
			})
		}
	}

	if util.Val(params.SortReceivingWallets) {
		// list receiving wallets first
		chain := domain.IDToChain(params.ChainID)
		defaultAddresses, kgErr := rdb.GormRepo().GetUserDefaultAddresses(ctx, uid)
		if kgErr != nil {
			kglog.WarningfCtx(ctx, "get default receive addresses failed: %v", kgErr.Error)
			response.KGError(ctx, kgErr)
			return
		}
		if defaultAddresses[chain] == nil {
			kglog.WarningfCtx(ctx, "default receive addresses not found")
			response.BadRequestWithMsg(ctx, code.UserWalletNotFound, "default receive addresses not found")
			return
		}
		defaultReceiveAddresses := defaultAddresses[chain]
		sort.SliceStable(resp, func(i, j int) bool {
			if strings.EqualFold(resp[i].Address, defaultReceiveAddresses.String()) {
				return true
			}
			if strings.EqualFold(resp[j].Address, defaultReceiveAddresses.String()) {
				return false
			}
			return i < j
		})
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": resp,
	})
}

type getAllUserWalletsResp struct {
	Code int             `json:"code"`
	Data *domain.Wallets `json:"data"`
}

// GetAllUserWallets returns all user wallets
func GetAllUserWallets(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}
	wallets := user.Wallets
	if wallets == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "wallets not found")
		return
	}

	resp := &getAllUserWalletsResp{
		Code: code.OK,
		Data: wallets,
	}
	ctx.JSON(http.StatusOK, resp)
}

// UpsertUserWallets upserts user wallets
func UpsertUserWallets(c *gin.Context) {
	ctx := c.Request.Context()
	req := &domain.Wallets{}
	kgErr := util.ToGinContextExt(c).BindJson(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	uid := auth.GetUID(c)
	u, _ := rdb.GormRepo().GetUser(c, uid, "", false, nil)
	if u == nil {
		response.BadRequestWithMsg(c, code.UserNotFound, "user not found")
		return
	}

	// validate wallet groups
	if err := validateWalletGroups(req.WalletGroups); err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
		return
	}

	// validate evm wallets
	if err := validateWallets(req.EvmWallets, false); err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
		return
	}

	// validate wallets
	if err := validateWallets(req.Wallets, true); err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
		return
	}

	if kgErr := user.UpsertWalletAddresses(ctx, uid, req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 0})
}

// DeleteUserWallets deletes all user wallets
func DeleteUserWallets(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", false, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	err := rdb.GormRepo().DeleteUserWallets(ctx.Request.Context(), user.UID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}

func validateWalletGroups(walletGroups []*domain.WalletGroup) error {
	if walletGroups == nil {
		return errors.New("wallet groups is nil")
	}

	for _, walletGroup := range walletGroups {
		wallets := append(walletGroup.BtcWallets, walletGroup.SolanaWallets...)
		wallets = append(wallets, walletGroup.TronWallets...)
		err := validateWallets(wallets, true)
		if err != nil {
			return err
		}
		err = validateWallets(walletGroup.EvmWallets, false)
		if err != nil {
			return err
		}
		if len(walletGroup.EncryptedSeedPhrase) == 0 {
			return errors.New("encrypted seed phrase is empty")
		}
	}

	return nil
}

func validateWallets(wallets []*domain.UserWallet, willCheckChain bool) error {
	if wallets == nil {
		return errors.New("wallets is nil")
	}

	for _, wallet := range wallets {
		if wallet.Address == "" {
			return errors.New("wallet address is empty")
		}
		if wallet.AddressIndex < 0 {
			return errors.New("wallet address index is invalid")
		}
		if wallet.WalletType != nil &&
			*wallet.WalletType != domain.WalletTypeObserver &&
			wallet.EncryptedPrivateKey == "" {
			return errors.New("encrypted private key is empty")
		}
		if willCheckChain {
			if wallet.Chain == "" {
				return errors.New("wallet chain is empty")
			}
		}
	}

	return nil
}

// GetReceiveWalletEns returns user receive wallet ens
func GetReceiveWalletEns(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}
	if user.Wallets == nil || user.Wallets.DefaultReceiveWallets == nil {
		response.BadRequestWithMsg(ctx, code.UserWalletNotFound, "wallet not found")
		return
	}

	wallet := user.Wallets.DefaultReceiveWallets[domain.Ethereum]
	if wallet == nil {
		response.BadRequestWithMsg(ctx, code.UserWalletNotFound, "wallet not found")
		return
	}
	ensNames, err := thegraphapi.GetENSNames(ctx.Request.Context(), wallet.String())
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.TheGraphFailed, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": ensNames,
	})
}

// CreateUserWallets creates wallet for existing user
func CreateUserWallets(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	errCode, err := user.CreateWalletForExistingUser(ctx.Request.Context(), &firebase.UserParams{
		UID: uid,
	})
	if err != nil {
		if errCode == code.AlreadyCreatedWallet {
			kglog.InfoWithDataCtx(ctx.Request.Context(), "CreateWalletForExistingUser, already created wallet", map[string]interface{}{
				"uid": uid,
			})
			response.ConflictWithMsg(ctx, code.AlreadyCreatedWallet, err.Error())
		} else {
			kglog.ErrorWithDataCtx(ctx.Request.Context(), "CreateWalletForExistingUser, ERROR: "+err.Error(), map[string]interface{}{
				"uid": uid,
			})
			response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		}
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}
