package user

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/domain"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	userservice "github.com/kryptogo/kg-wallet-backend/service/user"
	"gorm.io/gorm"
)

type getUserOrderCheckData struct {
	AllowOrder bool `json:"allow_order"`
}

// GetUserOrderCheck check if user is allowed to order
func GetUserOrderCheck(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	if len(uid) == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "uid is empty")
		return
	}

	user, _ := userservice.GetByUID(ctx.Request.Context(), uid, "", false, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	response.OK(ctx, &getUserOrderCheckData{
		AllowOrder: !util.IsAppleTester(user.PhoneNumber),
	})
}

// CreateCryptoAgreeTos create crypto agree tos
func CreateCryptoAgreeTos(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	user, _ := userservice.GetByUID(ctx.Request.Context(), uid, "", false, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	clientID := auth.GetClientID(ctx)
	org, kgErr := organization.GetStudioOrgRepo().GetOrganizationByOAuthClientID(ctx.Request.Context(), clientID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	tosLog, err := rdb.GetLatestCryptoAgreeTos(ctx.Request.Context(), org.ID, uid)
	if err != gorm.ErrRecordNotFound && err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	currentTosContent, err := rdb.GetCurrentCryptoTOSContent(ctx.Request.Context(), org.ID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	if tosLog != nil && tosLog.Version == currentTosContent.Version {
		response.BadRequestWithMsg(ctx, code.AlreadyAgreedTOS, "user has already agreed the latest TOS")
		return
	}

	err = rdb.CreateCryptoAgreeTos(ctx.Request.Context(), org.ID, uid, currentTosContent.Version)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	response.OK(ctx, nil)
}

type getCryptoAgreeTosData struct {
	AgreeVersion  string `json:"agree_version"`
	LatestVersion string `json:"latest_version"`
	TosContent    string `json:"tos_content"`
}

// GetCryptoAgreeTos get crypto agree tos
func GetCryptoAgreeTos(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	user, _ := userservice.GetByUID(ctx.Request.Context(), uid, "", false, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	clientID := auth.GetClientID(ctx)
	org, kgErr := organization.GetStudioOrgRepo().GetOrganizationByOAuthClientID(ctx.Request.Context(), clientID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	tosLog, err := rdb.GetLatestCryptoAgreeTos(ctx.Request.Context(), org.ID, uid)
	if err != gorm.ErrRecordNotFound && err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	currentTosContent, err := rdb.GetCurrentCryptoTOSContent(ctx.Request.Context(), org.ID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	data := getCryptoAgreeTosData{}
	data.LatestVersion = currentTosContent.Version
	if tosLog == nil {
		data.AgreeVersion = ""
	} else {
		data.AgreeVersion = tosLog.Version
	}

	if tosLog != nil && tosLog.Version == currentTosContent.Version {
		data.TosContent = ""
	} else {
		data.TosContent = currentTosContent.Content
	}

	response.OK(ctx, data)
}

// IsUserAllowedOrder check if user is allowed to order
func IsUserAllowedOrder(ctx context.Context, user *domain.UserData) bool {
	return user != nil && user.KycState != nil &&
		*user.KycState == domain.KycStatusVerified.String() &&
		!util.IsAppleTester(user.PhoneNumber)
}
