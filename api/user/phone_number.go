package user

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	authmiddleware "github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/l10n"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/user"
	"github.com/kryptogo/kg-wallet-backend/pkg/sms"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	userservice "github.com/kryptogo/kg-wallet-backend/service/user"
)

// AddressesByPhoneResp response
type AddressesByPhoneResp struct {
	Code  int               `json:"code"`
	Data  map[string]string `json:"data"`
	Error string            `json:"error,omitempty"`
}

type updatePhoneNumberReq struct {
	PhoneNumber string `json:"phone_number" binding:"required"`
	SmsCode     string `json:"sms_code" binding:"required"`
}

// UpdatePhoneNumber update phone number
func UpdatePhoneNumber(c *gin.Context) {
	ctx := c.Request.Context()
	uid := authmiddleware.GetUID(c)
	req := &updatePhoneNumberReq{}
	kgErr := util.ToGinContextExt(c).BindJson(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	authUser, err := firebase.GetUserByUID(ctx, uid)
	if err != nil {
		if firebase.IsUserNotFound(err) {
			response.BadRequestWithMsg(c, code.UserNotFound, "user not found")
			return
		}
		response.InternalServerErrorWithMsg(c, code.FirebaseFailed, err.Error())
		return
	}

	oldPhoneNumber := authUser.PhoneNumber
	newPhoneNumber := strings.TrimSpace(req.PhoneNumber)

	// check if the phone number is same as before
	if oldPhoneNumber == newPhoneNumber {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "phone_number is same as before")
		return
	}

	// check if the phone number is used
	used, err := user.PhoneIsUsed(ctx, newPhoneNumber)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.FirebaseFailed, err.Error())
		return
	}
	if used {
		response.BadRequestWithMsg(c, code.PhoneNumberUsed, "phone_number is used")
		return
	}

	// verify sms code
	loginProvider := auth.NewLoginProvider(&auth.LoginReq{
		PhoneNumber: newPhoneNumber,
		SmsCode:     req.SmsCode,
	})
	if kgErr := loginProvider.Verify(ctx); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	clientID := oauth.ClientID(c)
	dbUser, _ := userservice.GetByUID(ctx, uid, clientID, false, &domain.UserPreloads{
		WithLocale: true,
	})
	if dbUser == nil {
		response.BadRequestWithMsg(c, code.UserNotFound, "user not found")
		return
	}
	locale := dbUser.GetLocale(clientID)

	// update phone number
	errCode, err := user.UpdatePhoneNumber(ctx, uid, newPhoneNumber)
	if err != nil {
		response.InternalServerErrorWithMsg(c, errCode, err.Error())
		return
	}

	// notify user old phone number
	oauthApp := application.GetOAuthApplicationOrDefault(ctx, clientID)
	if oldPhoneNumber != "" {
		unbindSmsContent := l10n.StringWithParamsInTxtTemp("unbind-phone", locale, map[string]string{
			"wallet_name": oauthApp.Name,
			"new_phone":   newPhoneNumber,
		})
		kgErr := sms.Send(ctx, oldPhoneNumber, unbindSmsContent)
		if kgErr != nil {
			kglog.ErrorWithData("SendSMS, ERROR: "+kgErr.String(), map[string]interface{}{
				"phone_number": oldPhoneNumber,
				"content":      unbindSmsContent,
			})
			response.KGError(c, kgErr)
			return
		}
	}

	// notify user new phone number
	bindSmsContent := l10n.StringWithParamsInTxtTemp("bind-phone", locale, map[string]string{
		"wallet_name": oauthApp.Name,
		"new_phone":   newPhoneNumber,
	})
	kgErr = sms.Send(ctx, newPhoneNumber, bindSmsContent)
	if kgErr != nil {
		kglog.ErrorWithData("SendSMS, ERROR: "+kgErr.String(), map[string]interface{}{
			"phone_number": newPhoneNumber,
			"content":      bindSmsContent,
		})
		response.KGError(c, kgErr)
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 0})
}

// Identifier represents a query for user identification by phone or email
type Identifier struct {
	Phone string `form:"phone_number" json:"phone_number"`
	Email string `form:"email" json:"email"`
}

// GetAddresses returns wallet addresses associated with a user identified by phone number or email
func GetAddresses(c *gin.Context) {
	ctx := c.Request.Context()
	req := Identifier{}
	kgErr := util.ToGinContextExt(c).BindQuery(&req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Validate that at least one identifier is provided
	if req.Phone == "" && req.Email == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "Missing identifier. Provide either phone_number or email query parameter.")
		return
	}

	// Get addresses using the service layer
	addresses, kgErr := userservice.GetAddressesByIdentifier(ctx, req.Phone, req.Email)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Format the response
	resp := &AddressesByPhoneResp{
		Code: 0,
		Data: make(map[string]string),
	}

	for chain, addr := range addresses {
		resp.Data[chain.ID()] = addr.String()
	}

	c.JSON(http.StatusOK, resp)
}
