package user

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	userService "github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
)

func TestUpdatePassword(t *testing.T) {
	rdb.Reset()
	users, _ := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	userService.Init(repo.Unified())

	var uid, clientID string

	for userID := range users {
		uid = userID
		break
	}
	clientID = "test_client_id"

	// setup db and gin server
	url := "/v1/user/password"
	r := gin.Default()
	r.PUT(url, auth.MockAuthorize(uid), auth.MockClientID(clientID), UpdatePassword)

	// old version update password without password salt frontend
	body := map[string]interface{}{
		"password":   "updated value",
		"vault_data": map[string]interface{}{"test": "test"},
	}
	jsonStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// verify user vault data
	user, _ := rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithVaultData: true,
	})
	assert.NotNil(t, user.VaultData)
	assert.NotNil(t, user.Password)
	assert.Nil(t, user.PasswordSaltFrontend)
	assert.NotNil(t, user.PasswordSalt)
	matched, err := util.CompareArgonPassword("updated value", *user.Password, *user.PasswordSalt)
	assert.Nil(t, err)
	assert.True(t, matched)

	// update password salt frontend with empty string
	body = map[string]interface{}{
		"password":               "pd updated value",
		"password_salt_frontend": "",
		"vault_data":             map[string]interface{}{"test": "test"},
	}
	jsonStr, err = json.Marshal(body)
	assert.Nil(t, err)
	req, err = http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// verify user vault data
	user, _ = rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithVaultData: true,
	})
	assert.NotNil(t, user.VaultData)
	assert.NotNil(t, user.Password)
	assert.Nil(t, user.PasswordSaltFrontend)
	assert.NotNil(t, user.PasswordSalt)
	matched, err = util.CompareArgonPassword("pd updated value", *user.Password, *user.PasswordSalt)
	assert.Nil(t, err)
	assert.True(t, matched)

	// new version update password with password salt frontend
	body = map[string]interface{}{
		"password":               "password updated value",
		"password_salt_frontend": "salt updated value",
		"vault_data":             map[string]interface{}{"test": "test"},
	}
	jsonStr, err = json.Marshal(body)
	assert.Nil(t, err)
	req, err = http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// verify user vault data
	user, _ = rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithVaultData: true,
	})
	assert.NotNil(t, user.VaultData)
	assert.NotNil(t, user.Password)
	assert.Equal(t, "salt updated value", *user.PasswordSaltFrontend)
	assert.NotNil(t, user.PasswordSalt)
	matched, err = util.CompareArgonPassword("password updated value", *user.Password, *user.PasswordSalt)
	assert.Nil(t, err)
	assert.True(t, matched)
}

func TestGetPasswordSaltFrontendByPhone(t *testing.T) {
	rdb.Reset()
	users, uid, phone, _ := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	userService.Init(repo.Unified())

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	// clear ratelimit cache for testing
	// local test ip is ""
	ip := ""
	cache.Del("rate_limit:password_salt_frontend:min:" + ip)

	clientID := "test_client_id"

	// setup db and gin server
	saltPath := "/v1/user/password_salt_frontend"
	passwordPath := "/v1/user/password"
	r := gin.Default()
	r.GET(saltPath, GetPasswordSaltFrontend)
	r.PUT(passwordPath, auth.MockAuthorize(uid), auth.MockClientID(clientID), UpdatePassword)

	query := url.Values{}
	query.Add("phone_number", phone)
	req, err := http.NewRequest("GET", saltPath+"?"+query.Encode(), nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response struct {
		Code int    `json:"code"`
		Data string `json:"data"`
	}

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "b0ebcb03-a72b-4a30-81d9-ff88404f1d4c", response.Data)

	// update firestore user password salt frontend to empty string
	err = rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID: uid,
		},
		PasswordSaltFrontend: util.Ptr(""),
	})
	assert.Nil(t, err)

	req, err = http.NewRequest("GET", saltPath+"?"+query.Encode(), nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "b0ebcb03-a72b-4a30-81d9-ff88404f1d4c", response.Data)

	body := map[string]interface{}{
		"password":               "password updated value",
		"password_salt_frontend": "salt updated value",
		"vault_data":             map[string]interface{}{"test": "test"},
	}
	jsonStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err = http.NewRequest("PUT", passwordPath, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	req, err = http.NewRequest("GET", saltPath+"?"+query.Encode(), nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "salt updated value", response.Data)
}

func TestGetPasswordSaltFrontendByEmail(t *testing.T) {
	rdb.Reset()
	users, uid, _, email := dbtest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)
	userService.Init(repo.Unified())

	// clear ratelimit cache for testing
	// local test ip is ""
	ip := ""
	cache.Del("rate_limit:password_salt_frontend:min:" + ip)

	clientID := "test_client_id"

	// setup db and gin server
	saltPath := "/v1/user/password_salt_frontend"
	passwordPath := "/v1/user/password"
	r := gin.Default()
	r.GET(saltPath, GetPasswordSaltFrontend)
	r.PUT(passwordPath, auth.MockAuthorize(uid), auth.MockClientID(clientID), UpdatePassword)

	query := url.Values{}
	query.Add("email", email)
	req, err := http.NewRequest("GET", saltPath+"?"+query.Encode(), nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response struct {
		Code int    `json:"code"`
		Data string `json:"data"`
	}

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "b0ebcb03-a72b-4a30-81d9-ff88404f1d4c", response.Data)

	// update firestore user password salt frontend to empty string
	err = rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID: uid,
		},
		PasswordSaltFrontend: util.Ptr(""),
	})
	assert.Nil(t, err)

	req, err = http.NewRequest("GET", saltPath+"?"+query.Encode(), nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "b0ebcb03-a72b-4a30-81d9-ff88404f1d4c", response.Data)

	body := map[string]interface{}{
		"password":               "password updated value",
		"password_salt_frontend": "salt updated value",
		"vault_data":             map[string]interface{}{"test": "test"},
	}
	jsonStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err = http.NewRequest("PUT", passwordPath, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	req, err = http.NewRequest("GET", saltPath+"?"+query.Encode(), nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "salt updated value", response.Data)
}

func TestGetPasswordSaltFrontendByHandle(t *testing.T) {
	rdb.Reset()
	userData, _ := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), userData)
	_, err := firebase.BatchCreateUsersBySeed(userData)
	assert.Nil(t, err)

	userService.Init(repo.Unified())

	// find an user with handle
	uid := dbtest.UIDWithHandle(userData)
	handle := userData[uid].Handle
	assert.NotNil(t, handle)
	t.Logf("handle: %s", *handle)

	// clear ratelimit cache for testing
	// local test ip is ""
	ip := ""
	cache.Del("rate_limit:password_salt_frontend:min:" + ip)

	clientID := "test_client_id"

	// setup db and gin server
	saltPath := "/v1/user/password_salt_frontend"
	passwordPath := "/v1/user/password"
	r := gin.Default()
	r.GET(saltPath, GetPasswordSaltFrontend)
	r.PUT(passwordPath, auth.MockAuthorize(uid), auth.MockClientID(clientID), UpdatePassword)

	query := url.Values{}
	query.Add("handle", *handle)
	req, err := http.NewRequest("GET", saltPath+"?"+query.Encode(), nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response struct {
		Code int    `json:"code"`
		Data string `json:"data"`
	}

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "b0ebcb03-a72b-4a30-81d9-ff88404f1d4c", response.Data)

	// update firestore user password salt frontend to empty string
	err = rdb.GormRepo().SetUser(context.Background(), &domain.UserData{
		UserInfo: domain.UserInfo{
			UID: uid,
		},
		PasswordSaltFrontend: util.Ptr(""),
	})
	assert.Nil(t, err)

	req, err = http.NewRequest("GET", saltPath+"?"+query.Encode(), nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "b0ebcb03-a72b-4a30-81d9-ff88404f1d4c", response.Data)

	body := map[string]interface{}{
		"password":               "password updated value",
		"password_salt_frontend": "salt updated value",
		"vault_data":             map[string]interface{}{"test": "test"},
	}
	jsonStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err = http.NewRequest("PUT", passwordPath, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	req, err = http.NewRequest("GET", saltPath+"?"+query.Encode(), nil)
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, "salt updated value", response.Data)
}
