package user

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	authmiddleware "github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/user"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type updateEmailReq struct {
	Email            string `json:"email" binding:"required"`
	VerificationCode string `json:"verification_code" binding:"required"`
}

// UpdateEmail updates user email
func UpdateEmail(c *gin.Context) {
	ctx := c.Request.Context()
	uid := authmiddleware.GetUID(c)
	req := &updateEmailReq{}
	kgErr := util.ToGinContextExt(c).BindJson(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	authUser, err := firebase.GetUserByUID(ctx, uid)
	if err != nil {
		if firebase.IsUserNotFound(err) {
			response.BadRequestWithMsg(c, code.UserNotFound, "user not found")
			return
		}
		response.InternalServerErrorWithMsg(c, code.FirebaseFailed, err.Error())
		return
	}

	oldEmail := authUser.Email
	newEmail := strings.TrimSpace(req.Email)

	// check if the email is same as before
	if oldEmail == newEmail {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "email is same as before")
		return
	}

	// check if the email is used
	used, err := user.EmailIsUsed(ctx, newEmail)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.FirebaseFailed, err.Error())
		return
	}
	if used {
		response.BadRequestWithMsg(c, code.EmailUsed, "email is used")
		return
	}

	// verify email code
	clientID := oauth.ClientID(c)
	loginProvider := auth.NewLoginProvider(&auth.LoginReq{
		Email:            newEmail,
		VerificationCode: req.VerificationCode,
		ClientID:         clientID,
	})
	if kgErr := loginProvider.Verify(ctx); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	dbUser, _ := rdb.GormRepo().GetUser(ctx, uid, "", false, nil)
	if dbUser == nil {
		response.BadRequestWithMsg(c, code.UserNotFound, "user not found")
		return
	}

	// update email
	errCode, err := user.UpdateEmail(ctx, uid, newEmail)
	if err != nil {
		response.InternalServerErrorWithMsg(c, errCode, err.Error())
		return
	}

	// notify user old email
	oauthApp := application.GetOAuthApplicationOrDefault(ctx, clientID)
	if len(oldEmail) > 0 {
		sendgridClient := sendgrid.NewClient()
		resp, err := sendgridClient.SendEmail(ctx, oldEmail, oauthApp.Name, sendgrid.EmailTypeUnbind, map[string]string{
			"main_logo":        oauthApp.MainLogo,
			"email":            oldEmail,
			"support_address":  oauthApp.SupportAddress,
			"app_store_link":   oauthApp.AppStoreLink,
			"google_play_link": oauthApp.GooglePlayLink,
		})
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "[user.email] Notify old email error: "+err.Error(), map[string]interface{}{
				"email": oldEmail,
				"resp":  resp,
			})
			response.InternalServerErrorWithMsg(c, code.EmailSendFailed, err.Error())
			return
		}
	}

	// notify user new email
	sendgridClient := sendgrid.NewClient()
	resp, err := sendgridClient.SendEmail(ctx, newEmail, oauthApp.Name, sendgrid.EmailTypeBind, map[string]string{
		"main_logo":        oauthApp.MainLogo,
		"email":            newEmail,
		"client_name":      oauthApp.Name,
		"support_address":  oauthApp.SupportAddress,
		"app_store_link":   oauthApp.AppStoreLink,
		"google_play_link": oauthApp.GooglePlayLink,
	})
	if err != nil {
		kglog.ErrorWithData("[user.email] Notify new email error: "+err.Error(), map[string]interface{}{
			"email": newEmail,
			"resp":  resp,
		})
		response.InternalServerErrorWithMsg(c, code.EmailSendFailed, err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 0})
}
