package user

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/gcp"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/service/user"

	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type retrieveAndDeleteMnemonicReq struct {
	PublicKey string `json:"public_key"`
	Uid       string `json:"uid"`
}
type retrieveAndDeleteMnemonicResp struct {
	Code  int                 `json:"code"`
	Error string              `json:"error"`
	Data  retrieveMnemonicReq `json:"data"`
}

type retrieveMnemonicReq struct {
	EncryptedMnemonic string `json:"encrypted_mnemonic"`
	PublicKey         string `json:"public_key"`
}

// RetrieveAndDeleteMnemonic returns the encrypted mnemonic of the user with the shared secret, and delete the mnemonic from the database
func RetrieveAndDeleteMnemonic(ctx *gin.Context) {
	req := retrieveAndDeleteMnemonicReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if user == nil {
		ctx.JSON(http.StatusOK, &retrieveAndDeleteMnemonicResp{
			Code:  code.UserNotFound,
			Error: code.ErrUserNotFound.Error(),
		})
		return
	}

	var audience string
	var err error
	if !config.IsLocal() {
		audience, err = gcp.GetCloudRunUrl(region, projectID, "kms")
		if err != nil {
			ctx.JSON(http.StatusOK, &retrieveAndDeleteMnemonicResp{
				Code:  code.KmsFailed,
				Error: err.Error(),
			})
			return
		}
	} else {
		audience = fmt.Sprintf("http://%s:%s", config.GetString("TEST_KMS_HOST"), config.GetString("TEST_KMS_PORT"))
	}

	targetURL := fmt.Sprintf("%s/%s", audience, "v1/kms/retrieveMnemonic")
	targetReq := retrieveMnemonicReq{user.Wallets.WalletGroups[0].EncryptedSeedPhrase, req.PublicKey}

	s, err := json.Marshal(targetReq)
	if err != nil {
		ctx.JSON(http.StatusOK, &retrieveAndDeleteMnemonicResp{
			Code:  code.JSONError,
			Error: err.Error(),
		})
		return
	}
	b := bytes.NewBuffer(s)
	resp, bodyBytes, err := gcp.Client.MakeRequestWithIDToken(ctx.Request.Context(), audience, targetURL, http.MethodPost, b, nil)
	if err != nil {
		ctx.JSON(http.StatusOK, &retrieveAndDeleteMnemonicResp{
			Code:  code.KmsFailed,
			Error: err.Error(),
		})
		return
	}

	if resp.StatusCode != http.StatusOK {
		ctx.JSON(http.StatusOK, &retrieveAndDeleteMnemonicResp{
			Code:  code.KmsFailed,
			Error: fmt.Sprintf("status code: %d", resp.StatusCode),
		})
		return
	}

	respData := retrieveAndDeleteMnemonicResp{}
	err = json.Unmarshal(bodyBytes, &respData)
	if err != nil {
		ctx.JSON(http.StatusOK, &retrieveAndDeleteMnemonicResp{
			Code:  code.JSONError,
			Error: err.Error(),
		})
		return
	}

	if respData.Error != "" {
		ctx.JSON(http.StatusOK, &retrieveAndDeleteMnemonicResp{
			Code:  respData.Code,
			Error: respData.Error,
		})
		return
	}

	// hotfix: not delete mnemonic to prevent failure of recovery
	// err = db.DeleteUserMnemonic(uid)
	// if err != nil {
	// 	ctx.JSON(http.StatusOK, &retrieveAndDeleteMnemonicResp{
	// 		Code:  code.FirestoreFailed,
	// 		Error: err.Error(),
	// 	})
	// 	return
	// }
	respData.Code = 0
	ctx.JSON(http.StatusOK, respData)
}

type userIdentifier struct {
	Phone string `form:"phone_number" json:"phone_number"`
	Email string `form:"email" json:"email"`
}

// CreateUserWithMnemonic creates a new user and their mnemonic
func CreateUserWithMnemonic(ctx *gin.Context) {
	req := userIdentifier{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// Validate that at least one identifier is provided
	if req.Phone == "" && req.Email == "" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "Provide either phone_number or email")
		return
	}

	// Create the user with the provided identifier using service layer
	params := &firebase.UserParams{
		Phone: req.Phone,
		Email: req.Email,
	}

	_, errCode, err := user.CreateUserIfNotExists(ctx.Request.Context(), params, true)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}
