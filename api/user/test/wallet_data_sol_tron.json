{"wallet_groups": [{"evm_wallets": [{"address": "******************************************", "display_name": "0xa7eb...c636", "address_index": 0, "encrypted_private_key": "1ef6cc4f2aebfe7d28d3f28100d47de5fcfd6b95ec4a8fa8f44d93092a2eaf604f9655787786b2ff9e72d47568d7be103ad5fc5d8e94a1233db0818d366f7b6d556a8d1ef1407126b7154bee07a89eae", "create_timestamp": 1724138256408, "import_timestamp": null, "icon_url": null, "wallet_type": "normal"}], "btc_wallets": [{"chain": "btc", "address": "******************************************", "display_name": null, "address_index": 0, "encrypted_private_key": "ef52f4b2987c82f00efc0c35ac59c9f05c7477c5b15411dae4308ab5aaeddba560a635140f06520a6c31d59f2a606df4645f89dfce6a2eebf30357d544bde31c52f1c454a0fb0f456a362d80a2afedc4", "create_timestamp": 1724138256377, "import_timestamp": null, "icon_url": null, "wallet_type": "normal"}, {"chain": "btc", "address": "******************************************", "display_name": null, "address_index": 2, "encrypted_private_key": "81f95672763effefe7e2bdc8d263a5ec9ad8156a06cb76f2101556d43af45e00111fa91111fdc230d8979e4becebdac647693972328d510f921c529f1b9eabd040dd0b6be1c56691abeb0eedb86654c6", "create_timestamp": 1724138256388, "import_timestamp": null, "icon_url": null, "wallet_type": "normal"}], "solana_wallets": [{"chain": "sol", "address": "********************************************", "display_name": null, "address_index": 0, "encrypted_private_key": "9766797442efc8acec402630f107ffd313a58aaf73b788be47ce49fe138bf231041ebe3ee9270abfe48e859f9ec798fafd8215bf79dcc362e44ab4a35dbd6d8dbd88cdf0c4c073e60ca0b9d3ee43f26d", "create_timestamp": 1724138256396, "import_timestamp": null, "icon_url": null, "wallet_type": "normal"}], "tron_wallets": [{"chain": "tron", "address": "TD2fqUhM1YpBkAYHF6NGKMEP3h8EKu8hEH", "display_name": null, "address_index": 0, "encrypted_private_key": "233f811f957f63f9e2f517e092042031d0654d572ca2c93887dbb26d868bb6e09824f4e771eab87837f84044c8e74f89515afaf4171b26a212ed3c0bf1cd5fc31b6478ea55f2a139d5901c0a2fafebee", "create_timestamp": 1724138256401, "import_timestamp": null, "icon_url": null, "wallet_type": "normal"}], "create_timestamp": 1723540455000, "import_timestamp": 1723540455000, "encrypted_seed_phrase": "34251cb090f182b1f4f4ee024774ef076dd9a248fd5b6257a4824e3cfed96eadb946910fa664dfa0d4ca8ad7187b937d4c7c93512bdabf725b0d289a5fb3e1ac45f27cb5f47dcaa2fbcb6815c4ee80249c74262f2b41cdaa1062cb993ae06ccd24e25ed7b2a45f3a913c7c74f6c5f98ddc2a9c2628321fbba9", "display_name": null, "icon_url": null}, {"evm_wallets": [], "btc_wallets": [], "solana_wallets": [{"chain": "sol", "address": "6vctU4CJ1LL5K3e2Lfk62eWQ4pxaWHceMjmTuyRsHtwv", "display_name": null, "address_index": 0, "encrypted_private_key": "dbc70add88b8ac3cae500ecc622d682c7d8e7bcdac2d06e10782109d33e4fdc0d61492db2e8397427f91efb4625760df2b922d5a0211c2f24bb0dc3cd36b9b24241811b8e98482801fdaf4f2c9f62c4f", "create_timestamp": 1724138256417, "import_timestamp": null, "icon_url": null, "wallet_type": "normal"}], "tron_wallets": [{"chain": "tron", "address": "TRufE59QRCUtpmfgeobsSVLa9eXsYTSzRb", "display_name": null, "address_index": 0, "encrypted_private_key": "39a6080f2fd56cffcd95695eaa66c4b513ace82c6ddb5b0f591163099e6591cea56c2931df416f0a31bf79065a532424e83b01b4269e8ad8a67a27aafd39755324f7d228531515ec38f80d5be5bd2ce7", "create_timestamp": 1724138256383, "import_timestamp": null, "icon_url": null, "wallet_type": "normal"}], "create_timestamp": 1724138247000, "import_timestamp": 1724138247000, "encrypted_seed_phrase": "7dc233cdac873857afdcf1e732fb8f69c26a52d6d9e3beff137a9b0ff32bb0460bfe414defb530e0a776d33fdfa02a9a460ce0036060c93674cfa54617e495f788bcdde103e823e57a78b7ffb9218692d757f2c19a4142c16b60813e8ba18a76755be33702127b5b716887061b1cf85a618150a02b6967331a3930962b86a285", "display_name": "Imported Group", "icon_url": null}], "evm_wallets": [], "wallets": [], "default_receive_wallets": {"arb": "******************************************", "bsc": "******************************************", "btc": "******************************************", "eth": "******************************************", "kcc": "******************************************", "matic": "******************************************", "sol": "********************************************", "tron": "TD2fqUhM1YpBkAYHF6NGKMEP3h8EKu8hEH", "ronin": "******************************************", "oasys": "******************************************"}}