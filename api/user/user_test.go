package user

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"

	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	orgservice "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

type testUserContactInfo map[string]string

type testGetUsersByPhoneNumbersData map[string]testUserContactInfo

type testGetUsersByPhoneNumbersResponse struct {
	Code   int                                       `json:"code"`
	Data   testGetUsersByPhoneNumbersData            `json:"data"`
	Result map[string]testGetUsersByPhoneNumbersData `json:"result"`
}

const defaultClientID = "20991a3ae83233d6de85d62906d71fd3"

func TestGetUsersByPhoneNumbers(t *testing.T) {
	// setup db and gin server
	rdb.Reset()
	url := "/v1/phone_numbers"
	users, uid, phone, _ := dbtest.User()

	assert.Nil(t, rdb.GormRepo().BatchSetUsers(context.Background(), users))

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	r := gin.Default()
	r.POST(url, GetUsersByPhoneNumbers)

	// test error case 1
	body := map[string]interface{}{
		"phone_numbers": []string{phone},
	}
	jsonStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var resp testGetUsersByPhoneNumbersResponse

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(resp.Data))
	user, ok := resp.Data[phone]
	assert.True(t, ok)
	assert.Equal(t, uid, user["uid"])
	assert.Equal(t, "https://lh3.googleusercontent.com/H4nD73fI_qNP_C4mn6d2pImSpHeQ9VRKZ5YTlpyYvYfGw00f_6NVhfvvXxLQt147_yjj-2XTODzI5B_MLFSBdGUbRX_di3Oezq59uQ", user["avatar_url"])
	assert.Equal(t, "******************************************", user["arb"])
	assert.Equal(t, "******************************************", user["bsc"])
	assert.Equal(t, "******************************************", user["btc"])
	assert.Equal(t, "******************************************", user["eth"])
	assert.Equal(t, "******************************************", user["kcc"])
	assert.Equal(t, "******************************************", user["matic"])
	assert.Equal(t, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7", user["sol"])
	assert.Equal(t, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", user["tron"])
	assert.Equal(t, "******************************************", user["ronin"])
	assert.Equal(t, "******************************************", user["oasys"])
}

// TestDeleteUser test DeleteUser in firestore emulator
func TestDeleteUser(t *testing.T) {
	if !config.IsLocal() {
		t.Skip("Skip test in non-local env")
	}
	if os.Getenv("FIRESTORE_EMULATOR_HOST") == "" {
		t.Skip("Skip test if FIRESTORE_EMULATOR_HOST is not set")
	}
	if os.Getenv("FIREBASE_AUTH_EMULATOR_HOST") == "" {
		t.Skip("Skip test if FIREBASE_AUTH_EMULATOR_HOST is not set")
	}

	rdb.Reset()
	users, _ := dbtest.Users()

	user.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	db := rdb.Get()

	assert.Nil(t, rdbtest.CreateStudioOrganizations(db))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(db))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(db))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	var uid string
	for userID := range users {
		uid = userID
		break
	}

	orgservice.Init(orgservice.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})

	// setup db and gin server
	url := "/v1/user"
	r := gin.Default()
	r.DELETE(url, auth.MockAuthorize(uid), DeleteUser)

	req, err := http.NewRequest("DELETE", url, nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// user should be empty when deleted
	user, _ := rdb.GormRepo().GetUser(context.Background(), uid, "", false, nil)
	assert.Nil(t, user)
	authUser, err := firebase.GetUser(context.Background(), &firebase.UserParams{UID: uid})
	assert.Nil(t, authUser)
	assert.NotNil(t, err)

	var u model.User
	assert.NoError(t, db.Unscoped().First(&u, "uid = ?", uid).Error)
	assert.True(t, u.DeletedAt.Valid)
}

// TestGetUserInfo test GetUserInfo in firestore emulator
func TestGetUserInfo(t *testing.T) {
	if !config.IsLocal() {
		t.Skip("Skip test in non-local env")
	}
	if os.Getenv("FIRESTORE_EMULATOR_HOST") == "" {
		t.Skip("Skip test if FIRESTORE_EMULATOR_HOST is not set")
	}
	if os.Getenv("FIREBASE_AUTH_EMULATOR_HOST") == "" {
		t.Skip("Skip test if FIREBASE_AUTH_EMULATOR_HOST is not set")
	}

	rdb.Reset()
	users, uid, phone, _ := dbtest.User()

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	// setup db and gin server
	url := "/v1/user/info"
	r := gin.Default()
	r.GET(url, auth.MockAuthorize(uid), auth.MockClientID(defaultClientID), GetUserInfo)

	req, err := http.NewRequest("GET", url, nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	t.Logf("body: %s", w.Body.String())

	var resp getUserInfoResp

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	assert.Nil(t, err)

	assert.Equal(t, "哈里", resp.Data.DisplayName)
	assert.Equal(t, phone, resp.Data.PhoneNumber)
	assert.Equal(t, "2.0.0(456)", *resp.Data.AppVersion)
	assert.Equal(t, "2.0.0(456)", resp.Data.AppVersionMap[defaultClientID])
}

type testPutUserInfoResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func TestPutUserInfo(t *testing.T) {
	rdb.Reset()
	users, _ := dbtest.Users()

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	var uid string
	for userID := range users {
		uid = userID
		break
	}

	// setup db and gin server
	url := "/v1/user/info"
	r := gin.Default()
	r.PUT(url, auth.MockAuthorize(uid), UpdateUserInfo)

	fcmToken := map[string]interface{}{
		"timestamp": 1682588525679,
		"token":     "fc59B0_mW03qj6W7jN0HAd:APA91bHNNurvvBRRxzbtL2fxSgDpp1MoFt0QMfehJa1QUMLWQor-NWag_RJxDbSo0gg9fZIzA9DzvFYAu_YNG9TAW4SFfuv1R_7eJuXY8pIIBbB9N_laRj-T_DMppZg7_YcdYcJgwO9V",
	}
	fcmTokens := []map[string]interface{}{
		fcmToken,
	}
	body := map[string]interface{}{
		"fcm_tokens":  fcmTokens,
		"locale":      "zh-tw",
		"app_version": "2.1.0(478)",
	}
	jsonStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var resp testPutUserInfoResp

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	assert.Nil(t, err)
	assert.Equal(t, 0, resp.Code)
	assert.Empty(t, resp.Message)

	user, _ := rdb.GormRepo().GetUser(context.Background(), uid, defaultClientID, false, nil)
	assert.NotNil(t, user)
	assert.Equal(t, "2.1.0(478)", *user.AppVersion)
	assert.Equal(t, "2.1.0(478)", user.AppVersionMap[defaultClientID])
}

func TestPutUserInfoFcmTokens(t *testing.T) {
	rdb.Reset()
	users, _ := dbtest.Users()

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	var uid string
	for userID := range users {
		uid = userID
		break
	}

	// setup db and gin server
	url := "/v1/user/info"
	r := gin.Default()
	r.PUT(url, auth.MockAuthorize(uid), UpdateUserInfo)

	fcmTokens := []map[string]interface{}{
		{
			"timestamp": 1682588525679,
			"token":     "fc59B0_mW03qj6W7jN0HAd:APA91bHNNurvvBRRxzbtL2fxSgDpp1MoFt0QMfehJa1QUMLWQor-NWag_RJxDbSo0gg9fZIzA9DzvFYAu_YNG9TAW4SFfuv1R_7eJuXY8pIIBbB9N_laRj-T_DMppZg7_YcdYcJgwO9V",
		},
		{
			"timestamp": 1692588525679,
			"token":     "dZMpu-n9NkR-iNojWAOEp0:APA91bEoac5TrnyUjcu-N2aDSXyK2Srw8qt3S7c1VsEPjixyapuM8DZsrfPNH7L0DBMiev58L3XffdobmUD7XrEAkjaMhHBRXT1L9FXY5ujh1wuaSoZUFLqrTgJSweu30Ia6DVQ39pFT",
		},
	}
	body := map[string]interface{}{
		"fcm_tokens": fcmTokens,
		"locale":     "zh-tw",
	}
	jsonStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var resp testPutUserInfoResp

	err = json.Unmarshal(w.Body.Bytes(), &resp)
	assert.Nil(t, err)
	assert.Equal(t, 0, resp.Code)
	assert.Empty(t, resp.Message)

	userData, _ := rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithFcm: true,
	})
	assert.NotNil(t, userData)
	assert.Equal(t, 2, len(userData.FcmTokenMap[defaultClientID]))

	// set to empty token map
	body = map[string]interface{}{
		"fcm_tokens": []interface{}{},
	}
	jsonStr, err = json.Marshal(body)
	assert.Nil(t, err)
	req, err = http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)
	err = json.Unmarshal(w.Body.Bytes(), &resp)
	assert.Nil(t, err)
	assert.Equal(t, 0, resp.Code)
	assert.Empty(t, resp.Message)

	userData, _ = rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithFcm: true,
	})
	assert.NotNil(t, userData)
	assert.Equal(t, 0, len(userData.FcmTokenMap[defaultClientID]))
}

// TestUpsertUserHideSpamNftState test upsert user hide spam nft state
func TestUpsertUserHideSpamNftState(t *testing.T) {
	// given
	rdb.Reset()
	users, userIDs := dbtest.Users()

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)
	uid := userIDs[0]

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	// before
	{
		url := "/v1/user/info"
		r := gin.Default()
		r.GET(url, auth.MockAuthorize(uid), GetUserInfo)
		req, err := http.NewRequest("GET", url, nil)
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		var resp getUserInfoResp
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, 0, resp.Code)
		assert.Nil(t, resp.Data.HideSpamNft)
	}

	// update
	{
		url := "/v1/user/info"
		r := gin.Default()
		r.PUT(url, auth.MockAuthorize(uid), UpdateUserInfo)

		body := map[string]interface{}{
			"hide_spam_nft": true,
		}

		bodyJson, err := json.Marshal(body)
		assert.Nil(t, err)

		req, err := http.NewRequest("PUT", url, strings.NewReader(string(bodyJson)))
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		var resp testPutUserInfoResp

		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, 0, resp.Code)
		assert.Empty(t, resp.Message)
	}

	// after
	{
		url := "/v1/user/info"
		r := gin.Default()
		r.GET(url, auth.MockAuthorize(uid), GetUserInfo)
		req, err := http.NewRequest("GET", url, nil)
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		var resp getUserInfoResp
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.NoError(t, err)
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, 0, resp.Code)
		assert.Equal(t, true, *resp.Data.HideSpamNft)
	}
}

func TestUpdateUserInfoHandle(t *testing.T) {
	// Setup
	rdb.Reset()
	users, _ := dbtest.Users()

	user.Init(repo.Unified())
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	var uidWithoutHandle, uidWithHandle string
	for userID := range users {
		if users[userID].Handle == nil {
			uidWithoutHandle = userID
		} else {
			uidWithHandle = userID
		}
	}
	assert.NotEmpty(t, uidWithoutHandle)
	assert.NotEmpty(t, uidWithHandle)

	t.Run("User handle too long", func(t *testing.T) {
		// Setup db and gin server
		url := "/v1/user/info"
		r := gin.Default()
		r.PUT(url, auth.MockAuthorize(uidWithoutHandle), UpdateUserInfo)

		// Prepare request body with too long handle
		body := map[string]interface{}{
			"handle": strings.Repeat("a", 101), // Assuming max length is 100
		}
		jsonStr, err := json.Marshal(body)
		assert.Nil(t, err)

		// Make request
		req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assertions
		assert.Equal(t, http.StatusBadRequest, w.Code) // Assuming 400 Bad Request for too long
		var resp testPutUserInfoResp
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, code.UserHandleTooLong, resp.Code)
		assert.NotEmpty(t, resp.Message)
		t.Logf("resp: %+v", resp)
	})

	t.Run("User handle update duplicated", func(t *testing.T) {
		url := "/v1/user/info"
		r := gin.Default()
		r.PUT(url, auth.MockAuthorize(uidWithoutHandle), UpdateUserInfo)

		body := map[string]interface{}{
			"handle": users[uidWithHandle].Handle,
		}
		jsonStr, err := json.Marshal(body)
		assert.Nil(t, err)

		req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assertions
		assert.Equal(t, http.StatusConflict, w.Code) // Assuming 409 Conflict for duplicates
		var resp testPutUserInfoResp
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, code.UserHandleDuplicated, resp.Code)
		t.Logf("resp: %+v", resp)
	})

	t.Run("User handle cannot be updated again", func(t *testing.T) {
		url := "/v1/user/info"
		r := gin.Default()
		r.PUT(url, auth.MockAuthorize(uidWithHandle), UpdateUserInfo)

		body := map[string]interface{}{
			"handle": "some-other-handle",
		}
		jsonStr, err := json.Marshal(body)
		assert.Nil(t, err)

		req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assertions
		assert.Equal(t, http.StatusBadRequest, w.Code) // Assuming 409 Conflict for duplicates
		var resp testPutUserInfoResp
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, code.ParamIncorrect, resp.Code)
		assert.Equal(t, "handle can only be set once", resp.Message)
		t.Logf("resp: %+v", resp)
	})

	t.Run("User handle update success", func(t *testing.T) {
		url := "/v1/user/info"
		r := gin.Default()
		r.PUT(url, auth.MockAuthorize(uidWithoutHandle), UpdateUserInfo)

		handle := "test-handle-123454231"
		body := map[string]interface{}{
			"handle": handle,
		}
		jsonStr, err := json.Marshal(body)
		assert.Nil(t, err)

		// Make request
		req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
		assert.Nil(t, err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assertions
		assert.Equal(t, http.StatusOK, w.Code)
		var resp testPutUserInfoResp
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)
		assert.Equal(t, 0, resp.Code)
		assert.Empty(t, resp.Message)
		t.Logf("resp: %+v", resp)
	})
}
