package user

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

// GetUserKycState get user kyc state
func GetUserKycState(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	user, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), uid, "", true, nil)
	if user == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}

	var kycState string
	if user.KycState != nil {
		kycState = *user.KycState
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 0,
		"data": kycState,
	})
}
