package user

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	thegraphapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/thegraph-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
)

type profileHomeReq struct {
	UID      string   `form:"uid" binding:"required"`
	ChainIDs []string `form:"chain_ids"`
}

type profileHomeResp struct {
	Code int             `json:"code"`
	Data profileHomeData `json:"data"`
}

type profileHomeData struct {
	DisplayName     string               `json:"display_name"`
	Handle          string               `json:"handle"`
	AvatarURL       string               `json:"avatar_url"`
	TotalBalance    float32              `json:"total_balance"`
	BalancesByChain []rdb.BalanceByChain `json:"balances_by_chain"`
	Nfts            *[]rdb.VNft          `json:"nfts"`
	ENSNames        []string             `json:"ens_names"`
	Addresses       map[string]string    `json:"addresses"`
	Bio             string               `json:"bio"`
	Twitter         string               `json:"twitter"`
	Youtube         string               `json:"youtube"`
	Instagram       string               `json:"instagram"`
	Discord         string               `json:"discord"`
	CustomLink      string               `json:"custom_link"`
}

// ProfileHome returns the profile of the user
func ProfileHome(ctx *gin.Context) {
	AssetsNumber := 4
	NftsNumber := 9

	params := &profileHomeReq{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	clientID := oauth.ClientID(ctx)

	if len(params.ChainIDs) == 0 {
		params.ChainIDs = model.DefaultChainOrder
	}
	chains := lo.Map(params.ChainIDs, func(chainID string, _ int) domain.Chain {
		return domain.IDToChain(chainID)
	})

	// get default receive addresses
	receiveAddress, kgErr := rdb.GormRepo().GetUserDefaultAddresses(ctx, params.UID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}
	// default receive addresses
	addresses := map[string]string{}

	// assets balance
	balancesParams := &rdb.BalancesParams{}
	walletAddresses := []string{}
	for _, chain := range chains {
		addr := receiveAddress[chain].String()
		walletAddresses = append(walletAddresses, addr)
		addresses[chain.ID()] = addr
	}
	balancesParams.WalletAddresses = walletAddresses
	balancesParams.ChainIDs = params.ChainIDs
	balancesByChain, err := rdb.GetBalancesByChains(ctx.Request.Context(), balancesParams)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	totalBalance := float32(0)
	for _, balance := range balancesByChain {
		totalBalance += balance.UsdValue
	}
	for i, balance := range balancesByChain {
		if totalBalance == 0 {
			balancesByChain[i].Percentage = 0
		} else {
			balancesByChain[i].Percentage = balance.UsdValue / totalBalance * 100
		}
	}
	// show the first 4 chains
	if len(balancesByChain) > AssetsNumber {
		balancesByChain = balancesByChain[:AssetsNumber]
	}

	// nfts
	nftParams := rdb.NftParams{}
	nftParams.ChainIDs = params.ChainIDs
	nftParams.Tagtype = "ALL"
	nftParams.PageSize = NftsNumber
	nftParams.Sortings = []rdb.Sorting{
		{
			Col: "floor_price",
			Dir: "DESC",
		},
	}
	nftParams.ClientID = clientID
	nfts := &[]rdb.VNft{}
	var errCode int
	walletAddressesByChainID := make(map[string][]string)
	for chain, addr := range receiveAddress {
		walletAddressesByChainID[chain.ID()] = []string{addr.String()}
	}
	if len(walletAddressesByChainID) > 0 {
		nfts, _, errCode, err = rdb.Nfts(ctx.Request.Context(), &nftParams, walletAddressesByChainID)
		if err != nil {
			response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
			return
		}
	}

	// ens
	var ensNames []string
	if lo.Contains(params.ChainIDs, model.ChainIDEthereum) &&
		len(walletAddressesByChainID[model.ChainIDEthereum]) > 0 {
		ensNames, err = thegraphapi.GetENSNames(ctx.Request.Context(), walletAddressesByChainID[model.ChainIDEthereum][0])
		if err != nil {
			kglog.WarningWithDataCtx(ctx.Request.Context(), "get ens names failed", map[string]interface{}{
				"err": err.Error(),
			})
		}
	}

	avatarURL := ""
	userData, _ := rdb.GormRepo().GetUser(ctx.Request.Context(), params.UID, "", true, &domain.UserPreloads{
		WithAvatar: true,
	})
	if userData == nil {
		response.BadRequestWithMsg(ctx, code.UserNotFound, "user not found")
		return
	}
	if userData.Avatar != nil {
		avatarURL = userData.Avatar.AvatarURL
	}
	resp := &profileHomeResp{
		Code: 0,
		Data: profileHomeData{
			DisplayName:     userData.DisplayName,
			Handle:          util.Val(userData.Handle),
			AvatarURL:       avatarURL,
			BalancesByChain: balancesByChain,
			TotalBalance:    totalBalance,
			Nfts:            nfts,
			ENSNames:        ensNames,
			Addresses:       addresses,
			Bio:             util.Val(userData.Bio),
			Twitter:         util.Val(userData.Twitter),
			Youtube:         util.Val(userData.Youtube),
			Instagram:       util.Val(userData.Instagram),
			Discord:         util.Val(userData.Discord),
			CustomLink:      util.Val(userData.CustomLink),
		},
	}
	ctx.JSON(http.StatusOK, resp)
}
