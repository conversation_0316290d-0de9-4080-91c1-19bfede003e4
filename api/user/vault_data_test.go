package user

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/stretchr/testify/assert"
)

func TestDeletetUserVaultData(t *testing.T) {
	if !config.IsLocal() {
		t.Skip("Skip test in non-local env")
	}
	if os.Getenv("FIRESTORE_EMULATOR_HOST") == "" {
		t.Skip("Skip test if FIRESTORE_EMULATOR_HOST is not set")
	}
	if os.Getenv("FIREBASE_AUTH_EMULATOR_HOST") == "" {
		t.Skip("Skip test if FIREBASE_AUTH_EMULATOR_HOST is not set")
	}
	rdb.Reset()
	users, _ := dbtest.Users()

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	var uid string

	for userID := range users {
		uid = userID
		break
	}

	// setup db and gin server
	url := "/v1/user/vault_data"
	r := gin.Default()
	r.DELETE(url, auth.MockAuthorize(uid), DeleteUserVaultData)
	req, err := http.NewRequest("DELETE", url, nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// verify user vault data
	user, _ := rdb.GormRepo().GetUser(context.Background(), uid, "", true, &domain.UserPreloads{
		WithVaultData: true,
	})
	assert.Nil(t, user.EncryptSalt)
	assert.Nil(t, user.EncryptedMnemonic)
	assert.Nil(t, user.VaultData)
}

func TestUpsertUserVaultData(t *testing.T) {
	if !config.IsLocal() {
		t.Skip("Skip test in non-local env")
	}
	if os.Getenv("FIRESTORE_EMULATOR_HOST") == "" {
		t.Skip("Skip test if FIRESTORE_EMULATOR_HOST is not set")
	}
	if os.Getenv("FIREBASE_AUTH_EMULATOR_HOST") == "" {
		t.Skip("Skip test if FIREBASE_AUTH_EMULATOR_HOST is not set")
	}
	rdb.Reset()
	users, _ := dbtest.Users()

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	var uid string

	for userID := range users {
		uid = userID
		break
	}

	// setup db and gin server
	url := "/v1/user/vault_data"
	r := gin.Default()
	r.PUT(url, auth.MockAuthorize(uid), UpsertUserVaultData)

	body := map[string]interface{}{
		"account_public_key":            "updated value",
		"encrypt_salt":                  "updated value",
		"encrypted_account_private_key": "updated value",
	}
	jsonStr, err := json.Marshal(body)
	assert.Nil(t, err)
	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(jsonStr))
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// verify user vault data
	user, _ := rdb.GormRepo().GetUser(context.Background(), uid, "", false, &domain.UserPreloads{
		WithVaultData: true,
	})
	assert.NotNil(t, user.VaultData)
	assert.Equal(t, "updated value", user.VaultData.AccountPublicKey)
	assert.Equal(t, "updated value", user.VaultData.EncryptSalt)
	assert.Equal(t, "updated value", user.VaultData.EncryptedAccountPrivateKey)
}
