package tx_watch

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	chainsync "github.com/kryptogo/kg-wallet-backend/service/chain-sync"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func setup(t *testing.T) (*gin.Engine, *domain.MockAsyncTaskExecutor) {
	ctrl := gomock.NewController(t)

	executor := domain.NewMockAsyncTaskExecutor(ctrl)
	chainsync.Init(nil, nil, nil, executor, "/handle/url")

	r := gin.Default()

	r.POST("/v1/tx_watch/notify", Create)

	return r, executor
}

func TestCreateWatchTxTaskBadParams(t *testing.T) {
	r, _ := setup(t)

	// Invalid parameters to trigger bad request
	params := CreateWatchTxTaskParams{
		ChainID: "invalid-chain",
		TxHash:  "short",
	}
	bodyStr, _ := json.Marshal(params)

	req, _ := http.NewRequest("POST", "/v1/tx_watch/notify", bytes.NewBuffer(bodyStr))
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestCreateWatchTxTask(t *testing.T) {
	r, executor := setup(t)

	// Valid parameters
	params := CreateWatchTxTaskParams{
		ChainID: "tron",
		TxHash:  "0x468a05eaf1846a7b624de164e4cbddcfcd63eae7ad12fda2b3f0fc53e4d524d5",
	}
	bodyStr, _ := json.Marshal(params)

	executor.EXPECT().Execute(gomock.Any(), gomock.Any(), gomock.Cond(
		func(v any) bool {
			task, ok := v.(*domain.HttpTask)
			assert.True(t, ok)
			assert.Contains(t, task.URL, "/handle/url")
			assert.Equal(t, "POST", task.Method)

			var res struct {
				ChainID string `json:"chain_id"`
				TxHash  string `json:"tx_hash"`
			}
			err := json.Unmarshal(task.Body, &res)
			assert.NoError(t, err)
			assert.Equal(t, domain.Tron.ID(), res.ChainID)
			assert.Equal(t, "468a05eaf1846a7b624de164e4cbddcfcd63eae7ad12fda2b3f0fc53e4d524d5", res.TxHash)
			return true
		}), gomock.Cond(
		func(v any) bool {
			taskName, ok := v.(string)
			assert.True(t, ok)
			assert.Equal(t, "tron-468a05eaf1846a7b624de164e4cbddcfcd63eae7ad12fda2b3f0fc53e4d524d5", taskName)
			return true
		})).Return(nil)

	req, _ := http.NewRequest("POST", "/v1/tx_watch/notify", bytes.NewBuffer(bodyStr))
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}
