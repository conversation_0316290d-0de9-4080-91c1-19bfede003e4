package tx_watch

import (
	"fmt"
	"net/http"
	"regexp"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	chainsync "github.com/kryptogo/kg-wallet-backend/service/chain-sync"
	"github.com/samber/lo"
)

// HandleParams contains the parameters for handling a tx watch request.
type HandleParams struct {
	TxHash  string `json:"tx_hash" binding:"required"`
	ChainID string `json:"chain_id" binding:"required"`
}

type TxStatusResponse struct {
	TxStatus string `json:"tx_status"`
}

// isValidTxHash checks if the given transaction hash is valid.
func isValidTxHash(txHash string) bool {
	validCharsPattern := `^[0-9a-zA-Z]+$`
	return len(txHash) >= 20 && regexp.MustCompile(validCharsPattern).MatchString(txHash)
}

// isValidFormat checks if the given parameters are in the correct format.
func isValidFormat(txHash, chainID string) error {
	chain := domain.IDToChain(chainID)
	if chain == nil {
		return fmt.Errorf("invalid chain id: %s", chainID)
	}

	if !isValidTxHash(txHash) {
		return fmt.Errorf("invalid txHash format: %s", txHash)
	}

	return nil
}

var unsupportedChains = []string{domain.Ronin.ID(), domain.Oasys.ID(), domain.Kcc.ID()}

type CreateWatchTxTaskParams struct {
	ChainID string `json:"chain_id" binding:"required"`
	TxHash  string `json:"tx_hash" binding:"required"`
}

// Create creates a new task to watch a transaction on a given chain ID.
func Create(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "tx_watch.Create")
	defer span.End()

	params := CreateWatchTxTaskParams{}
	kgErr := util.ToGinContextExt(c).BindJson(&params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if lo.Contains(unsupportedChains, params.ChainID) {
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("chain %s is not supported", params.ChainID), nil))
		return
	}

	if err := isValidFormat(params.TxHash, params.ChainID); err != nil {
		response.KGError(c, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, err, nil))
		return
	}

	kgErr = chainsync.Enqueue(ctx, domain.IDToChain(params.ChainID), params.TxHash)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}
