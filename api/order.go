//go:generate go-enum
package api

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	userapi "github.com/kryptogo/kg-wallet-backend/api/user"
	"github.com/kryptogo/kg-wallet-backend/domain"
	slackapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/slack"
	stripeapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/stripe"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/market"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	studioassetpro "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/notification"
	"github.com/shopspring/decimal"
	"github.com/stripe/stripe-go"
	"github.com/stripe/stripe-go/webhook"
	"gorm.io/gorm"
)

// PaymentIntentStatus is the payment intent status
// ENUM(payment_intent.canceled, payment_intent.payment_failed, payment_intent.succeeded)
type PaymentIntentStatus string

const (
	// TOTAL_COST_TOLERANCE is the tolerance of total cost
	TOTAL_COST_TOLERANCE = 0.004
)

type createdOrderRequest struct {
	PaymentGateway  string `json:"payment_gateway" binding:"required"`
	Amount          string `json:"amount" binding:"required"`
	Price           string `json:"price" binding:"required"`
	TotalCost       string `json:"total_cost" binding:"required"`
	WalletAddress   string `json:"wallet_address" binding:"required"`
	ChainID         string `json:"chain_id" binding:"required"`
	ContractAddress string `json:"contract_address"`
}

type createOrderData struct {
	OrderID            string `json:"order_id"`
	StripeClientSecret string `json:"stripe_client_secret"`
}

// CreateOrder create order
func CreateOrder(c *gin.Context) {
	ctx := c.Request.Context()
	params := &createdOrderRequest{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	uid := auth.GetUID(c)

	kglog.DebugWithDataCtx(ctx,
		"CreateOrder", map[string]interface{}{
			"uid":              uid,
			"payment_gateway":  params.PaymentGateway,
			"amount":           params.Amount,
			"price":            params.Price,
			"total_cost":       params.TotalCost,
			"wallet_address":   params.WalletAddress,
			"chain_id":         params.ChainID,
			"contract_address": params.ContractAddress,
		})

	kgErr = checkServiceAvailability(params.PaymentGateway)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	amount, err := decimal.NewFromString(params.Amount)
	if err != nil || amount.LessThanOrEqual(decimal.Zero) {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid amount")
		return
	}

	// check if token is available
	clientID := oauth.ClientID(c)
	tokenInfo, kgErr := validateTokenInfo(c.Request.Context(), clientID, params, amount)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// if eth, round amount to 4 decimal places
	if tokenInfo.Symbol == "ETH" {
		amount = amount.Round(4)
	}
	// if matic, round amount to 1 decimal places
	if tokenInfo.Symbol == "MATIC" || tokenInfo.Symbol == "POL" {
		amount = amount.Round(1)
	}

	orgID, kgErr := auth.GetOrganizationIDByClientID(c)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	profitRate, kgErr := studioassetpro.GetProfitRate(ctx, orgID, domain.ProfitRateServiceTypeBuy)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// validate price and total cost
	totalCost, networkFee, processingFee, profitMargin, kgErr := validateOrderPriceAndTotalCost(ctx, params, tokenInfo, amount, profitRate)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// check user existence
	user, _ := rdb.GormRepo().GetUser(ctx, uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if user == nil {
		response.ForbiddenErrorWithMsg(c, code.UserNotFound, "user not found")
		return
	}

	// check user's wallets
	walletAddress, kgErr := validateWalletAddress(user, params.ChainID, params.WalletAddress)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// user validation
	if !userapi.IsUserAllowedOrder(ctx, user) {
		response.ForbiddenErrorWithMsg(c, code.UserNotVerified, "user not verified")
		return
	}

	// check if user has not exceeded the limit
	now := time.Now()
	kgErr = checkBoughtThisWeek(ctx, uid, amount, now)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// TODO add test case to assert the profit rate
	orderID, clientSecret, kgErr := processOrder(ctx,
		processOrderParams{
			ClientID:         clientID,
			OrgID:            orgID,
			UID:              uid,
			TotalCost:        totalCost,
			Amount:           amount,
			Price:            params.Price,
			NetworkFee:       networkFee,
			ProcessingFee:    processingFee,
			ProfitMarginRate: profitRate.ProfitRate,
			ProfitShareRatio: profitRate.ProfitShareRatio,
			ProfitMargin:     profitMargin,
			WalletAddress:    walletAddress,
			ChainID:          params.ChainID,
			ContractAddress:  params.ContractAddress,
			PaymentGateway:   params.PaymentGateway,
			Now:              now,
		})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp := &createOrderData{
		OrderID:            orderID,
		StripeClientSecret: clientSecret,
	}
	response.OK(c, resp)
}

func checkServiceAvailability(paymentGateway string) *code.KGError {
	// check if order is enabled
	if !config.GetBool("ORDER_ENABLED") {
		return code.NewKGError(code.OrderDisabled, http.StatusBadRequest, errors.New("order is disabled"), nil)
	}
	// check if payment gateway is enabled
	if !config.GetBool("STRIPE_ENABLED") {
		return code.NewKGError(code.PaymentGatewayDisabled, http.StatusBadRequest, errors.New("payment gateway is disabled"), nil)
	}
	// check if exchange is enabled
	if !config.GetBool("BINANCE_ENABLED") {
		return code.NewKGError(code.ExchangeDisabled, http.StatusBadRequest, errors.New("exchange is disabled"), nil)
	}
	if market.PaymentGateway(paymentGateway) != market.PaymentGatewayStripe {
		return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid payment gateway"), nil)
	}
	return nil
}

func validateTokenInfo(ctx context.Context, clientID string, params *createdOrderRequest, amount decimal.Decimal) (*market.TokenInfo, *code.KGError) {
	// check if token is available
	tokenInfo, err := market.GetSingleTokenInfo(ctx, clientID, params.ChainID, params.ContractAddress)
	if err != nil {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, err, nil)
	}

	// check if token is enabled (binance)
	if !tokenInfo.WithdrawEnable {
		return nil, code.NewKGError(code.TokenDisabled, http.StatusBadRequest, errors.New("token is disabled"), nil)
	}

	amountV, _ := amount.Float64()
	if tokenInfo.WithdrawMin > amountV {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("amount is less than withdraw min"), nil)
	}
	return tokenInfo, nil
}

func validateOrderPriceAndTotalCost(ctx context.Context, params *createdOrderRequest, tokenInfo *market.TokenInfo, amount decimal.Decimal, profitRate *domain.AssetProProfitRate) (decimal.Decimal, decimal.Decimal, decimal.Decimal, decimal.Decimal, *code.KGError) {
	price, err := decimal.NewFromString(params.Price)
	if err != nil || price.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid price"), nil)
	}

	// validate price
	if !market.IsPriceValid(ctx, params.ChainID, params.ContractAddress, price) {
		return decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid price"), nil)
	}

	// calculate total cost
	networkFee := decimal.NewFromFloat(tokenInfo.NetworkFee)
	processingFeeRate, ok := tokenInfo.ProcessingFee[params.PaymentGateway]
	if !ok {
		return decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid payment gateway"), nil)
	}
	expectedTotalCost, processingFee := calculateTotalCostAndProcessingFee(amount, price, networkFee, processingFeeRate)

	// validate total cost
	totalCost, err := decimal.NewFromString(params.TotalCost)
	if err != nil || totalCost.LessThanOrEqual(decimal.Zero) {
		return decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid total cost"), nil)
	}

	// validate total cost
	tolerance := decimal.NewFromFloat(TOTAL_COST_TOLERANCE)
	if (totalCost.Sub(expectedTotalCost).Div(expectedTotalCost)).Abs().GreaterThan(tolerance) {
		kglog.DebugWithDataCtx(ctx, "total cost not match", map[string]interface{}{
			"expected_total_cost": expectedTotalCost.String(),
			"total_cost":          totalCost.String(),
			"tolerance":           tolerance.String(),
		})
		return decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("total cost not match"), nil)
	}

	if totalCost.LessThan(decimal.NewFromFloat(config.GetFloat64("SINGLE_ORDER_LOWER_LIMIT"))) {
		return decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("total cost less than lower limit"), nil)
	}

	profitMargin := amount.Mul(price).Mul(profitRate.ProfitRate).Mul(decimal.NewFromInt(1).Sub(profitRate.ProfitShareRatio))

	return totalCost, networkFee, processingFee, profitMargin, nil
}

func validateWalletAddress(user *domain.UserData, chainID, walletAddress string) (string, *code.KGError) {
	walletAddressMap := user.AddressesByChains("", []string{chainID}, true)
	walletAddresses := walletAddressMap[chainID]

	var err error
	if chainID == dbmodel.ChainIDEthereum {
		walletAddress, err = util.ToChecksumAddress(walletAddress)
	}
	if err != nil {
		return "", code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid wallet address"), nil)
	}
	if len(walletAddresses) == 0 || !util.ContainsInsensitive(walletAddresses, walletAddress) {
		return "", code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("wallet address not found"), nil)
	}
	return walletAddress, nil
}

func checkBoughtThisWeek(ctx context.Context, uid string, amount decimal.Decimal, now time.Time) *code.KGError {
	prevSunday := util.PrevSunday(now)
	totalValueBought, err := rdb.TotalValueBoughtByUser(ctx, uid, prevSunday, now)
	if err != nil {
		return code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("failed to get total value bought: %v", err), nil)
	}
	totalValueBoughtDec := decimal.NewFromFloat(totalValueBought)
	if totalValueBoughtDec.Add(amount).GreaterThan(decimal.NewFromFloat(config.GetFloat64("WEEK_ORDER_UPPER_LIMIT"))) {
		return code.NewKGError(code.OrderExceedLimit, http.StatusForbidden, errors.New("order exceed limit"), nil)
	}
	return nil
}

type processOrderParams struct {
	ClientID         string
	OrgID            int
	UID              string
	TotalCost        decimal.Decimal
	Amount           decimal.Decimal
	Price            string
	NetworkFee       decimal.Decimal
	ProcessingFee    decimal.Decimal
	ProfitMarginRate decimal.Decimal
	ProfitShareRatio decimal.Decimal
	ProfitMargin     decimal.Decimal
	WalletAddress    string
	ChainID          string
	ContractAddress  string
	PaymentGateway   string
	Now              time.Time
}

func processOrder(ctx context.Context, params processOrderParams) (string, string, *code.KGError) {
	// stripe create order (payment intent)
	// stripe accepts amount in cents
	totalCostInCent := params.TotalCost.Mul(decimal.NewFromInt(100))
	paymentIntentParams := &stripe.PaymentIntentParams{
		Amount:   stripe.Int64(totalCostInCent.IntPart()),
		Currency: stripe.String(string(stripe.CurrencyUSD)),
		PaymentMethodTypes: stripe.StringSlice([]string{
			"card",
		}),
	}
	paymentIntentParams.AddMetadata("env", config.GetString("ENV"))
	paymentIntent, err := stripeapi.NewClient().PaymentIntents.New(paymentIntentParams)
	if err != nil {
		return "", "", code.NewKGError(code.StripeFailed, http.StatusInternalServerError, err, nil)
	}

	// rdb create order
	incrementalID, err := cache.GetIncrementalID(ctx, params.Now)
	if err != nil {
		return "", "", code.NewKGError(code.InternalError, http.StatusInternalServerError, fmt.Errorf("failed to get incremental id: %v", err), nil)
	}

	serialNo := util.GenerateOrderSerialNo(params.Now, incrementalID)
	order := &dbmodel.Order{
		ID:               serialNo,
		ClientID:         &params.ClientID,
		OrganizationID:   &params.OrgID,
		UID:              params.UID,
		Amount:           params.Amount.String(),
		Price:            params.Price,
		TotalCost:        params.TotalCost.String(),
		NetworkFee:       params.NetworkFee.String(),
		ProcessingFee:    params.ProcessingFee.String(),
		WalletAddress:    params.WalletAddress,
		ChainID:          params.ChainID,
		ContractAddress:  params.ContractAddress,
		PaymentGateway:   params.PaymentGateway,
		StripeID:         &paymentIntent.ID,
		Status:           dbmodel.OrderStatusInit,
		ProfitMarginRate: util.Ptr(params.ProfitMarginRate),
		ProfitShareRatio: util.Ptr(params.ProfitShareRatio),
		ProfitMargin:     util.Ptr(params.ProfitMargin),
		CreatedAt:        params.Now,
		UpdatedAt:        params.Now,
	}
	if err := rdb.CreateOrder(ctx, order); err != nil {
		return "", "", code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("failed to create order: %v", err), nil)
	}

	return order.ID, paymentIntent.ClientSecret, nil
}

func calculateTotalCostAndProcessingFee(amount, price, networkFee decimal.Decimal, processingFeeRate float64) (decimal.Decimal, decimal.Decimal) {
	totalValueWithNetworkFee := amount.Add(networkFee).Mul(price).RoundCeil(2)
	processingFee := totalValueWithNetworkFee.Mul(decimal.NewFromFloat(processingFeeRate)).RoundCeil(2)
	totalCost := totalValueWithNetworkFee.Add(processingFee)
	return totalCost, processingFee
}

// StripeOrderWebhook stripe order webhook
func StripeOrderWebhook(c *gin.Context) {
	ctx := c.Request.Context()
	payload, err := c.GetRawData()
	if err != nil {
		kgErr := code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("failed to read request body: %v", err), nil)
		response.KGError(c, kgErr)
		return
	}

	kglog.DebugWithDataCtx(ctx,
		"stripe webhook payload", map[string]interface{}{
			"payload": string(payload),
		})

	endpointSecret := config.GetString("STRIPE_WEBHOOK_SECRET")
	if endpointSecret == "" {
		response.InternalServerErrorWithMsg(c, code.InternalError, "stripe webhook secret not found")
		return
	}

	signatureHeader := c.GetHeader("Stripe-Signature")
	event, err := webhook.ConstructEvent(payload, signatureHeader, endpointSecret)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.InternalError, "failed to construct event")
		return
	}

	kglog.DebugWithDataCtx(ctx,
		"stripe webhook event", map[string]interface{}{
			"event": event,
		})

	// check env from metadata
	metadata, ok := event.Data.Object["metadata"].(map[string]interface{})
	if !ok {
		kglog.DebugCtx(ctx, "stripe webhook event metadata not found")
		kgErr := code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("metadata not found"), nil)
		response.KGError(c, kgErr)
		return
	}
	env, ok := metadata["env"].(string)
	if !ok {
		kglog.DebugCtx(ctx, "stripe webhook event env not found")
		kgErr := code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("env not found"), nil)
		response.KGError(c, kgErr)
		return
	}
	if env != config.GetString("ENV") {
		kglog.DebugfCtx(ctx, "stripe webhook event env mismatch, expected %s, got %s", config.GetString("ENV"), env)
		response.OK(c, nil)
		return
	}

	// get order from rdb
	paymentIntentID, ok := event.Data.Object["id"].(string)
	if !ok {
		kglog.DebugCtx(ctx, "stripe webhook event id not found")
		kgErr := code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("id not found"), nil)
		response.KGError(c, kgErr)
		return
	}
	order, err := rdb.GetOrderByStripeID(ctx, paymentIntentID)
	if err != nil {
		response.InternalServerErrorWithMsg(c, code.DBError, fmt.Sprintf("failed to get order by stripe id: %v", err))
		return
	}

	clientID := order.ClientID
	user, kgErr := rdb.GormRepo().GetUser(ctx, order.UID, *clientID, false, &domain.UserPreloads{
		WithFcm:    true,
		WithLocale: true,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	tokenInfo := market.GetTokenInfoByChainIDContractAddress(order.ChainID, order.ContractAddress)
	if tokenInfo == nil {
		kglog.ErrorCtx(ctx, "token not found")
		kgErr := code.NewKGError(code.TokenNotSupported, http.StatusInternalServerError, errors.New("token not found"), nil)
		response.KGError(c, kgErr)
		return
	}

	var noti *domain.Notification
	updates := map[string]interface{}{}
	paymentIntentStatus, err := ParsePaymentIntentStatus(event.Type)
	if err != nil {
		kglog.DebugCtx(ctx, fmt.Sprintf("stripe webhook event type not supported: %v", event.Type))
		response.OK(c, nil)
		return
	}
	switch paymentIntentStatus {
	case PaymentIntentStatusPaymentIntentcanceled:
		// // update order to cancelled
		// updates["status"] = dbmodel.OrderStatusCancelled
		// notification = rdb.PopulateCancelledOrderNotification(c, user, order, tokenInfo.Symbol, tokenInfo.NotificationChain, clientID)
	case PaymentIntentStatusPaymentIntentpaymentFailed:
		// update order to failed
		updates["status"] = dbmodel.OrderStatusFailed
		noti = rdb.PopulateFailedOrderNotification(ctx, user.UID, order, tokenInfo.Symbol, tokenInfo.NotificationChain, *clientID)
	case PaymentIntentStatusPaymentIntentsucceeded:
		// update order to pending && store card last4 to order
		updates["status"] = dbmodel.OrderStatusPendingForPaymentCompleted
		charges, ok := event.Data.Object["charges"].(map[string]interface{})
		if !ok {
			kglog.DebugCtx(ctx, "stripe webhook event charges not found")
			kgErr := code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("charges not found"), nil)
			response.KGError(c, kgErr)
			return
		}
		data, ok := charges["data"].([]interface{})
		if !ok {
			kglog.DebugCtx(ctx, "stripe webhook event charges data not found")
			kgErr := code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("charges data not found"), nil)
			response.KGError(c, kgErr)
			return
		}
		if len(data) > 0 {
			charge := data[0].(map[string]interface{})
			paymentMethodDetails := charge["payment_method_details"].(map[string]interface{})
			card := paymentMethodDetails["card"].(map[string]interface{})
			updates["card_last4"] = card["last4"]
		}
	default:
		kglog.DebugfCtx(ctx, "stripe webhook event type not supported: %s", event.Type)
		response.OK(c, nil)
	}

	// rdb update order
	if len(updates) > 0 {
		if err := rdb.UpdateOrder(ctx, order.ID, updates); err != nil {
			response.InternalServerErrorWithMsg(c, code.DBError, "failed to update order")
			return
		}
	}

	// send notification to user
	if noti != nil {
		_, kgErr := notification.Get().Send(ctx, noti)
		if kgErr != nil {
			kglog.ErrorWithDataCtx(ctx,
				"Failed to send notification", map[string]interface{}{
					"notification": noti,
					"error":        kgErr.String(),
				})
		}
	}

	if paymentIntentStatus != PaymentIntentStatusPaymentIntentsucceeded {
		response.OK(c, nil)
		return
	}

	// send notification to slack
	totalCost, err := decimal.NewFromString(order.TotalCost)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx,
			"Failed to parse total cost", map[string]interface{}{
				"order": order,
				"error": err.Error(),
			})
		response.InternalServerErrorWithMsg(c, code.InternalError, "failed to parse total cost")
		return
	}
	if totalCost.GreaterThanOrEqual(decimal.NewFromFloat(config.GetFloat64("LARGE_TRANSACTION_AMOUNT"))) {
		err := slackapi.SendLargeTransactionNotification(order, tokenInfo.Symbol)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx,
				"Failed to send large transaction notification", map[string]interface{}{
					"order": order,
					"error": err.Error(),
				})
		}
	}

	response.OK(c, nil)
}

type listOrdersResp struct {
	Code   int               `json:"code"`
	Data   []*rdb.Order      `json:"data"`
	Paging *rdb.PagingParams `json:"paging"`
}

// ListOrders list orders
func ListOrders(c *gin.Context) {
	ctx := c.Request.Context()
	params := &rdb.ListOrdersReq{}
	kgErr := util.ToGinContextExt(c).BindQuery(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp := new(listOrdersResp)
	uid := auth.GetUID(c)
	if uid == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "uid not found")
		return
	}
	params.UID = uid

	orders, pagingParams, errCode, err := rdb.ListOrders(ctx, params)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx,
			"failed to list orders", map[string]interface{}{
				"params": params,
				"error":  err.Error(),
			})
		response.InternalServerErrorWithMsg(c, errCode, "failed to list orders")
		return
	}

	for _, order := range *orders {
		tokenInfo := market.GetTokenInfoByChainIDContractAddress(order.ChainID, order.ContractAddress)
		if tokenInfo == nil {
			kglog.ErrorfCtx(ctx, "failed to get token info, chain_id: %s, contract_address: %s", order.ChainID, order.ContractAddress)
			response.InternalServerErrorWithMsg(c, code.TokenNotSupported, "failed to get token info")
			return
		}
		order.Symbol = tokenInfo.Symbol
		order.LogoUrls = tokenInfo.LogoUrls
	}

	resp.Code = 0
	resp.Data = *orders
	resp.Paging = pagingParams
	c.JSON(http.StatusOK, resp)
}

// GetOrderDetail get order detail
func GetOrderDetail(c *gin.Context) {
	ctx := c.Request.Context()
	params := &rdb.GetOrderDetailReq{}
	uid := auth.GetUID(c)
	if uid == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "uid not found")
		return
	}
	params.UID = uid

	id := c.Param("order_id")
	if id == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "order_id not found")
		return
	}
	params.ID = id

	order, err := rdb.GetOrderDetail(ctx, params)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.BadRequestWithMsg(c, code.OrderNotFound, "order not found")
			return
		}
		response.InternalServerErrorWithMsg(c, code.DBError, err.Error())
		return
	}

	tokenInfo := market.GetTokenInfoByChainIDContractAddress(order.ChainID, order.ContractAddress)
	if tokenInfo == nil {
		kglog.ErrorfCtx(ctx, "failed to get token info, chain_id: %s, contract_address: %s", order.ChainID, order.ContractAddress)
		response.InternalServerErrorWithMsg(c, code.TokenNotSupported, "failed to get token info")
		return
	}

	order.Symbol = tokenInfo.Symbol
	order.LogoUrls = tokenInfo.LogoUrls
	order.NotificationChain = tokenInfo.NotificationChain

	response.OK(c, order)
}
