package request

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/paging"
	"github.com/samber/lo"
)

// PagingRequest is the request for paging
type PagingRequest struct {
	FuzzySearch string `form:"q"`
	PageNumber  int    `form:"page_number"`
	PageSize    int    `form:"page_size"`
	PageSort    string `form:"page_sort"`
	PageToken   string `form:"page_token"`
}

func (req PagingRequest) sortingList(allowedColumns []string) (paging.SortingList, error) {
	if req.PageSort == "" {
		return nil, nil
	}

	mAllowedColumns := lo.SliceToMap(allowedColumns, func(column string) (string, struct{}) {
		return column, struct{}{}
	})

	sortPairs := strings.Split(req.PageSort, ",")

	sortingList := make(paging.SortingList, 0, len(sortPairs))
	for _, sortPair := range sortPairs {
		arr := strings.Split(sortPair, ":")
		if len(arr) != 2 {
			return nil, fmt.Errorf("invalid sort pair: %s", sortPair)
		}

		if _, ok := mAllowedColumns[arr[0]]; !ok {
			return nil, fmt.Errorf("invalid sort column: %s", arr[0])
		}

		direction, err := paging.ParseDirectionFromAbb(arr[1])
		if err != nil {
			return nil, err
		}

		sortingList = append(sortingList, paging.Sorting{
			Column:    arr[0],
			Direction: direction,
		})
	}

	return sortingList, nil
}

// ParseQuery validates paging
func (req PagingRequest) ParseQuery(allowedColumns []string) (*paging.Query, *code.KGError) {
	sorting, err := req.sortingList(allowedColumns)
	if err != nil {
		return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, err, nil)
	}

	return &paging.Query{
		Paging: &paging.Paging{
			PageNumber: req.PageNumber,
			PageSize:   req.PageSize,
		},
		Sorting:     sorting,
		FuzzySearch: req.FuzzySearch,
	}, nil
}
