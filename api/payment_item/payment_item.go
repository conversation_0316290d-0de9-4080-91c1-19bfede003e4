package payment_item

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/payment_item"
	"github.com/shopspring/decimal"
)

// CreatePaymentItem handles the creation of a new payment item
func CreatePaymentItem(c *gin.Context) {
	ctx := c.Request.Context()

	// Get organization ID from context
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing organization ID")
		return
	}

	// Parse request body
	var req CreatePaymentItemReq
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	// Validate chain ID
	if req.ChainID != nil && *req.ChainID != "" {
		chain := domain.IDToChain(*req.ChainID)
		if chain == nil {
			kglog.WarningWithDataCtx(ctx, "Invalid chain ID", map[string]interface{}{
				"chain_id": *req.ChainID,
			})
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid chain ID")
			return
		}
	}

	// Parse price string to decimal
	price, err := decimal.NewFromString(req.Price)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid price format")
		return
	}

	// Convert DTO order data fields to domain model
	domainOrderDataFields := ToDomainOrderDataFields(req.OrderDataFields)

	// Create payment item
	paymentItem, kgErr := payment_item.CreatePaymentItem(ctx, &domain.PaymentItemCreate{
		Name:            req.Name,
		Description:     req.Description,
		Price:           price,
		Currency:        req.Currency,
		Image:           req.Image,
		SuccessURL:      req.SuccessURL,
		ErrorURL:        req.ErrorURL,
		CallbackURL:     req.CallbackURL,
		PayToken:        req.PayToken,
		SuccessMessage:  req.SuccessMessage,
		ChainID:         req.ChainID,
		OrderDataFields: domainOrderDataFields,
		Config:          req.Config,
		OrganizationID:  orgID,
		ClientID:        req.ClientID,
	})

	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Get organization information
	org, kgErr := organization.GetStudioOrgRepo().GetOrganizationByID(ctx, orgID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get organization info", map[string]interface{}{
			"error": kgErr.Error.Error(),
			"orgID": orgID,
		})
		response.KGError(c, kgErr)
		return
	}

	// Convert domain model to DTO for response
	paymentItemDTO := FromDomain(paymentItem, org)
	response.OK(c, paymentItemDTO)
}

// GetPaymentItems retrieves all payment items for an organization with pagination
func GetPaymentItems(c *gin.Context) {
	ctx := c.Request.Context()

	// Get organization ID from context
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing organization ID")
		return
	}

	// Get client ID from query parameter (optional)
	clientID := c.Query("client_id")
	// No validation needed, as client_id is optional

	// Get pagination parameters
	page, pageSize, _ := middleware.GetPaginationFromContext(c)

	// Get payment items
	items, total, kgErr := payment_item.GetPaymentItems(ctx, orgID, clientID, page, pageSize)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Get organization information
	org, kgErr := organization.GetStudioOrgRepo().GetOrganizationByID(ctx, orgID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get organization info", map[string]interface{}{
			"error": kgErr.Error.Error(),
			"orgID": orgID,
		})
		response.KGError(c, kgErr)
		return
	}

	// Convert domain models to DTOs
	itemDTOs := FromDomainItems(items, org)

	// Create paging info
	paging := response.Paging{
		PageNumber: page,
		PageSize:   pageSize,
		TotalCount: total,
	}

	response.OKWithPaging(c, itemDTOs, paging)
}

// UpdatePaymentItem updates a payment item
func UpdatePaymentItem(c *gin.Context) {
	ctx := c.Request.Context()

	// Get organization ID from context
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing organization ID")
		return
	}

	// Get payment item ID from path
	id := c.Param("id")
	if id == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing payment item ID")
		return
	}

	// Parse request body
	var req UpdatePaymentItemReq
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		response.KGError(c, err)
		return
	}

	// Prepare update object
	update := &domain.PaymentItemUpdate{
		Name:           req.Name,
		Description:    req.Description,
		Image:          req.Image,
		SuccessURL:     req.SuccessURL,
		ErrorURL:       req.ErrorURL,
		CallbackURL:    req.CallbackURL,
		PayToken:       req.PayToken,
		SuccessMessage: req.SuccessMessage,
		ChainID:        req.ChainID,
	}

	// Parse price if provided
	if req.Price != nil {
		price, err := decimal.NewFromString(*req.Price)
		if err != nil {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid price format")
			return
		}
		update.Price = &price
	}

	// Set currency if provided
	if req.Currency != nil {
		update.Currency = req.Currency
	}

	// Convert order data fields if provided
	if req.OrderDataFields != nil {
		domainOrderDataFields := ToDomainOrderDataFields(*req.OrderDataFields)
		update.OrderDataFields = &domainOrderDataFields
	}

	// Set config if provided
	if req.Config != nil {
		update.Config = req.Config
	}

	// Update payment item
	updatedItem, kgErr := payment_item.UpdatePaymentItem(ctx, id, update)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Get organization information
	org, kgErr := organization.GetStudioOrgRepo().GetOrganizationByID(ctx, updatedItem.OrganizationID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get organization info", map[string]interface{}{
			"error": kgErr.Error.Error(),
			"orgID": updatedItem.OrganizationID,
		})
		response.KGError(c, kgErr)
		return
	}

	// Convert to DTO for response
	updatedItemDTO := FromDomain(updatedItem, org)
	response.OK(c, updatedItemDTO)
}

// DeletePaymentItem deletes a payment item
func DeletePaymentItem(c *gin.Context) {
	ctx := c.Request.Context()

	// Get item ID from path
	id := c.Param("id")
	if id == "" {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing payment item id")
		return
	}

	// Get organization ID from context for authorization
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "missing organization ID")
		return
	}

	// Verify ownership of payment item
	paymentItem, kgErr := payment_item.GetPaymentItemByID(ctx, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if paymentItem.OrganizationID != orgID {
		kglog.ErrorWithDataCtx(ctx, "Organization ID mismatch during delete", map[string]interface{}{
			"requestOrgID": orgID,
			"itemOrgID":    paymentItem.OrganizationID,
		})
		response.ForbiddenErrorWithMsg(c, code.NotInOrganization, "item does not belong to this organization")
		return
	}

	// Delete the payment item
	kgErr = payment_item.DeletePaymentItem(ctx, id)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	response.OK(c, nil)
}

// GetPaymentItemHandler returns a gin handler function that retrieves a payment item by ID
// returnSuccessMessage controls whether to include success_message in the response
func GetPaymentItemHandler(returnSuccessMessage bool) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()

		// Get item ID from path
		id := c.Param("id")
		if id == "" {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "missing payment item id")
			return
		}

		paymentItem, kgErr := payment_item.GetPaymentItemByID(ctx, id)
		if kgErr != nil {
			response.KGError(c, kgErr)
			return
		}

		// Get organization information
		org, kgErr := organization.GetStudioOrgRepo().GetOrganizationByID(ctx, paymentItem.OrganizationID)
		if kgErr != nil {
			kglog.ErrorWithDataCtx(ctx, "Failed to get organization info", map[string]interface{}{
				"error": kgErr.Error.Error(),
				"orgID": paymentItem.OrganizationID,
			})
			response.KGError(c, kgErr)
			return
		}

		// Convert domain model to DTO
		paymentItemDTO := FromDomain(paymentItem, org)
		
		// Hide success_message if requested (for v1 API compatibility)
		if !returnSuccessMessage {
			paymentItemDTO.SuccessMessage = nil
		}
		
		response.OK(c, paymentItemDTO)
	}
}
