package payment_item

import (
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/shopspring/decimal"
)

// OrderDataFieldDTO represents the API representation of a payment item field
type OrderDataFieldDTO struct {
	FieldName    string `json:"field_name"`
	FieldLabel   string `json:"field_label"`
	Required     bool   `json:"required"`
	FieldType    string `json:"field_type"`
	DefaultValue any    `json:"default_value,omitempty"`
}

// PaymentItemDTO is the API representation of a payment item
type PaymentItemDTO struct {
	ID               string              `json:"id"`
	Name             string              `json:"name"`
	Description      *string             `json:"description,omitempty"`
	Price            decimal.Decimal     `json:"price"`
	Currency         string              `json:"currency"`
	Image            *string             `json:"image,omitempty"`
	SuccessURL       *string             `json:"success_url,omitempty"`
	ErrorURL         *string             `json:"error_url,omitempty"`
	CallbackURL      *string             `json:"callback_url,omitempty"`
	PayToken         *string             `json:"pay_token,omitempty"`
	SuccessMessage   *string             `json:"success_message,omitempty"`
	ChainID          *string             `json:"chain_id,omitempty"`
	OrderDataFields  []OrderDataFieldDTO `json:"order_data_fields"`
	Config           map[string]any      `json:"config,omitempty"`
	OrganizationID   int                 `json:"organization_id"`
	OrganizationName string              `json:"organization_name,omitempty"`
	OrganizationIcon string              `json:"organization_icon,omitempty"`
	ClientID         string              `json:"client_id"`
	CreatedAt        time.Time           `json:"created_at"`
	UpdatedAt        time.Time           `json:"updated_at"`
}

// CreatePaymentItemReq contains fields for payment item creation request
type CreatePaymentItemReq struct {
	Name            string              `json:"name" binding:"required"`
	Description     *string             `json:"description"`
	Price           string              `json:"price" binding:"required"`
	Currency        string              `json:"currency" binding:"required"`
	Image           *string             `json:"image"`
	SuccessURL      *string             `json:"success_url"`
	ErrorURL        *string             `json:"error_url"`
	CallbackURL     *string             `json:"callback_url"`
	PayToken        *string             `json:"pay_token"`
	SuccessMessage  *string             `json:"success_message"`
	ChainID         *string             `json:"chain_id"`
	OrderDataFields []OrderDataFieldDTO `json:"order_data_fields"`
	Config          map[string]any      `json:"config,omitempty"`
	ClientID        string              `json:"client_id" binding:"required"`
}

// UpdatePaymentItemReq contains fields for payment item update request
type UpdatePaymentItemReq struct {
	Name            *string              `json:"name"`
	Description     *string              `json:"description"`
	Price           *string              `json:"price"`
	Currency        *string              `json:"currency"`
	Image           *string              `json:"image"`
	SuccessURL      *string              `json:"success_url"`
	ErrorURL        *string              `json:"error_url"`
	CallbackURL     *string              `json:"callback_url"`
	PayToken        *string              `json:"pay_token"`
	SuccessMessage  *string              `json:"success_message"`
	ChainID         *string              `json:"chain_id"`
	OrderDataFields *[]OrderDataFieldDTO `json:"order_data_fields"`
	Config          *map[string]any      `json:"config,omitempty"`
	ClientID        *string              `json:"client_id"`
}

// FromDomain converts a domain PaymentItem to a DTO
func FromDomain(item *domain.PaymentItem, org *domain.StudioOrganization) *PaymentItemDTO {
	if item == nil || org == nil {
		return nil
	}

	orderFields := make([]OrderDataFieldDTO, len(item.OrderDataFields))
	for i, field := range item.OrderDataFields {
		orderFields[i] = OrderDataFieldDTO{
			FieldName:    field.FieldName,
			FieldLabel:   field.FieldLabel,
			Required:     field.Required,
			FieldType:    field.FieldType,
			DefaultValue: field.DefaultValue,
		}
	}

	return &PaymentItemDTO{
		ID:               item.ID,
		Name:             item.Name,
		Description:      item.Description,
		Price:            item.Price,
		Currency:         item.Currency,
		Image:            item.Image,
		SuccessURL:       item.SuccessURL,
		ErrorURL:         item.ErrorURL,
		CallbackURL:      item.CallbackURL,
		PayToken:         item.PayToken,
		SuccessMessage:   item.SuccessMessage,
		ChainID:          item.ChainID,
		OrderDataFields:  orderFields,
		Config:           item.Config,
		OrganizationID:   item.OrganizationID,
		OrganizationName: org.Name,
		OrganizationIcon: getOrgIcon(org),
		ClientID:         item.ClientID,
		CreatedAt:        item.CreatedAt,
		UpdatedAt:        item.UpdatedAt,
	}
}

// getOrgIcon safely retrieves organization icon URL, handling nil values
func getOrgIcon(org *domain.StudioOrganization) string {
	if org.IconURL == nil {
		return ""
	}
	return *org.IconURL
}

// ToDomainOrderDataFields converts DTO order data fields to domain model
func ToDomainOrderDataFields(fields []OrderDataFieldDTO) []domain.OrderDataField {
	domainFields := make([]domain.OrderDataField, len(fields))
	for i, field := range fields {
		domainFields[i] = domain.OrderDataField{
			FieldName:    field.FieldName,
			FieldLabel:   field.FieldLabel,
			Required:     field.Required,
			FieldType:    field.FieldType,
			DefaultValue: field.DefaultValue,
		}
	}
	return domainFields
}


// FromDomainItems converts a slice of domain PaymentItems to DTOs
func FromDomainItems(items []*domain.PaymentItem, org *domain.StudioOrganization) []*PaymentItemDTO {
	dtos := make([]*PaymentItemDTO, len(items))
	for i, item := range items {
		dtos[i] = FromDomain(item, org)
	}
	return dtos
}
