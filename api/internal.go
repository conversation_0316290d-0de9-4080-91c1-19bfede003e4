package api

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/linebot"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/user"
)

// CreateTestUsersReq create test users request
type CreateTestUsersReq struct {
	Number   int    `json:"number"`
	ClientID string `json:"client_id"`
}

// TestUser test user
type TestUser struct {
	UID         string `json:"uid"`
	AccessToken string `json:"access_token"`
	IDToken     string `json:"id_token"`
	KgToken     string `json:"kg_token"`
}

// CreateTestUsers create test users
func CreateTestUsers(ctx *gin.Context) {
	req := CreateTestUsersReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	testUsers := []TestUser{}
	for i := 0; i < req.Number; i++ {
		randomUID := "testuser-" + strings.ToLower(util.RandString(10))
		accessToken, idToken, kgToken, _, kgErr := auth.CreateTestUsers(ctx.Request.Context(), randomUID)
		if kgErr != nil {
			response.KGError(ctx, kgErr)
			return
		}

		testUsers = append(testUsers, TestUser{
			UID:         randomUID,
			AccessToken: accessToken,
			IDToken:     idToken,
			KgToken:     kgToken,
		})
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": testUsers,
	})
}

// ResetDB reset db and insert seed data for testing
func ResetDB(ctx *gin.Context) {
	if !config.IsLocal() {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":  code.NotAllowed,
			"error": "only allow in local development",
		})
		return
	}
	rdb.Reset()
	kglog.Debug("Creating seed data for rdb")
	if err := rdb.CreateSeedData(); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":  code.DBError,
			"error": err.Error(),
		})
		return
	}
	oauth.NotifyClientConfigChanges()
	kglog.Debug("Resetting firebase")
	db.Reset()
	kglog.Debug("Resetting cache")
	if kgError := organization.CacheRoleBindings(ctx.Request.Context()); kgError != nil {
		response.KGError(ctx, kgError)
		return
	}
	kglog.Debug("All database reset finished.")
	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}

type createUserReq struct {
	Email string `json:"email"`
	Phone string `json:"phone_number"`
}

type createUserResp struct {
	UID string `json:"uid"`
}

// CreateUser create user
func CreateUser(ctx *gin.Context) {
	req := createUserReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// create user
	uid, kgError := user.CreateUser(ctx.Request.Context(), &firebase.UserParams{
		Email: req.Email,
		Phone: req.Phone,
	}, true)

	if kgError != nil {
		response.ErrorWithMsg(ctx, kgError.HttpStatus, kgError.Code, kgError.String(), nil)
		return
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": createUserResp{
			UID: uid,
		},
	})
}

type kycStatusChangedReq struct {
	ComplianceOrgID int    `json:"org_id"`
	KGUserID        string `json:"kg_user_id"`
}

// KycStatusChangedCallback will be called when one of the user's task (IDV, CDD, Form) status
// is changed. Compliance backend will call this API to notify us.
func KycStatusChangedCallback(ctx *gin.Context) {
	req := kycStatusChangedReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	studioOrg, kgError := organization.GetStudioOrgRepo().
		GetOrganizationByComplianceOrgID(ctx.Request.Context(), req.ComplianceOrgID)
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	var customerRepo = customer.GetCustomerRepo()
	kgCustomer, kgError := customerRepo.GetCustomerByUID(ctx.Request.Context(), req.KGUserID, int(studioOrg.ID))
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}

	kglog.DebugWithDataCtx(ctx.Request.Context(), "KycStatusChangedCallback", map[string]interface{}{
		"kgCustomer": kgCustomer,
		"status":     kgCustomer.KycStatus,
		"formStatus": kgCustomer.FormStatus,
		"cddStatus":  kgCustomer.CDDStatus,
		"idvStatus":  kgCustomer.IDVStatus,
	})
	switch kgCustomer.KycStatus {
	case domain.KycStatusUnverified:
		break
	case domain.KycStatusPending:
		// If kyc status is pending, we need to check whether it's the first time
		// for this user to submit any of IDD, CDV, or Form. Since we only want to
		// send the notification to user once.
		if kgCustomer.FormStatus == domain.KycStatusPending &&
			(kgCustomer.CDDStatus == domain.KycStatusUnverified || kgCustomer.CDDStatus == domain.KycStatusPending) &&
			(kgCustomer.IDVStatus == domain.KycStatusUnverified || kgCustomer.IDVStatus == domain.KycStatusPending) {
			kgError = linebot.NotifyKycStatusChanged(ctx, kgCustomer)
		}
	case domain.KycStatusVerified:
		kgError = linebot.NotifyKycStatusChanged(ctx, kgCustomer)
	case domain.KycStatusRejected:
		// If kyc status is rejected, we need to check whether it's the first time
		// for this user to have any of IDD, CDV, or Form rejected. Since we only
		// want to send the notification to user once.
		rejectedCnt := 0
		if kgCustomer.FormStatus == domain.KycStatusRejected {
			rejectedCnt++
		}
		if kgCustomer.CDDStatus == domain.KycStatusRejected {
			rejectedCnt++
		}
		if kgCustomer.IDVStatus == domain.KycStatusRejected {
			rejectedCnt++
		}
		if rejectedCnt == 1 {
			kgError = linebot.NotifyKycStatusChanged(ctx, kgCustomer)
		}
	}
	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}
	response.OK(ctx, nil)
}
