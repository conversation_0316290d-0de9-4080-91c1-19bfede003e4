package middleware

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis_rate/v9"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// RateLimitByUserUID is a middleware that rate limits requests by user UID.
func RateLimitByUserUID(rateKeyPrefix string, rate redis_rate.Limit) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := auth.GetUID(c)
		if uid == "" {
			response.Unauthorized(c, code.UserNotFound)
			return
		}
		res, err := cache.Limiter.Allow(c.Request.Context(), cache.ComposeRateLimitByUIDKey(rateKeyPrefix, uid), rate)
		if err != nil {
			response.InternalServerErrorWithMsg(c, code.RateLimitCheckFailedError, err.Error())
			return
		}

		c.Header("RateLimit-Remaining", strconv.Itoa(res.Remaining))

		if res.Allowed == 0 {
			seconds := int(res.RetryAfter / time.Second)
			c.Header("RateLimit-RetryAfter", strconv.Itoa(seconds))
			response.TooManyRequestsWithMsg(c, code.RateLimit, code.ErrRateLimitExceeded.Error())
			return
		}

		c.Next()
	}
}
