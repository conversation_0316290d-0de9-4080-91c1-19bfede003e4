package middleware

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

type HeaderValidation struct {
	Key      string
	Name     string
	Required bool
}

func PublicHeaderValidation(headers ...HeaderValidation) gin.HandlerFunc {
	return func(c *gin.Context) {
		for _, header := range headers {
			if header.Required && c.GetHeader(header.Key) == "" {
				response.BadRequestWithMsg(c, code.ParamIncorrect, fmt.Sprintf("missing %s header", header.Key))
				return
			}
			c.Set(header.Name, c.GetHeader(header.Key))
		}
		c.Next()
	}
}
