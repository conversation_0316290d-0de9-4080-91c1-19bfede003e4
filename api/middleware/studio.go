package middleware

import (
	"context"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
)

// AuthStudioTokenV2 auth studio jwt token
func AuthStudioTokenV2(ctx *gin.Context) {
	tokenOk, tokenErr := auth.VerifyStudioToken(ctx)
	if !tokenOk {
		if tokenErr.Error() == "studio token not provided" {
			response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.StudioTokenNotProvided, "token not provided", nil)
		} else {
			response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.StudioTokenInvalid, tokenErr.Error(), nil)
		}
		return
	}

	ctx.Next()
}

func orgValidationDecorator(valid func(ctx context.Context, orgID int, uid string) (bool, *code.KGError)) gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := auth.GetUID(c)
		if uid == "" {
			response.UnauthorizedWithMsg(c, code.StudioTokenInvalid, "uid not found")
			return
		}

		orgID, err := strconv.Atoi(c.Param("orgID"))
		if err != nil {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid organization")
			return
		}

		valid, kgErr := valid(c.Request.Context(), orgID, uid)
		if kgErr != nil {
			response.KGError(c, kgErr)
			return
		}

		if valid {
			c.Set("org_id", orgID)
			c.Next()
			return
		}

		response.ForbiddenErrorWithMsg(c, code.NotInOrganization, "user is not in the organization")
	}
}

// OrganizationUserValidation validate organization and check if studio user is in the organization
func OrganizationUserValidation() gin.HandlerFunc {
	return orgValidationDecorator(organization.IsUserInOrganization)
}

// OrganizationCustomerValidation validate organization and check if customer is in the organization
func OrganizationCustomerValidation() gin.HandlerFunc {
	return orgValidationDecorator(customer.GetCustomerRepo().DoesCustomerExist)
}

// OrganizationUserOrCustomerValidation validate organization and check if studio user or customer is in the organization
func OrganizationUserOrCustomerValidation() gin.HandlerFunc {
	return orgValidationDecorator(func(ctx context.Context, orgID int, uid string) (bool, *code.KGError) {
		studioUserExists, kgErr := organization.IsUserInOrganization(ctx, orgID, uid)
		if kgErr != nil {
			return false, kgErr
		}

		if studioUserExists {
			return true, nil
		}

		customerExists, kgErr := customer.GetCustomerRepo().DoesCustomerExist(ctx, orgID, uid)
		if kgErr != nil {
			return false, kgErr
		}

		return customerExists, nil
	})
}

// OrganizationOauthClientValidation validate organization and check if customer is in the organization
func OrganizationOauthClientValidation(c *gin.Context) {
	clientID := c.GetString("client_id")
	if clientID == "" {
		response.UnauthorizedWithMsg(c, code.InternalError, "client id from middleware not found")
		return
	}

	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.UnauthorizedWithMsg(c, code.InternalError, "org id from middleware not found")
		return
	}

	exist, kgErr := organization.IsOAuthClientInOrganization(c.Request.Context(), orgID, clientID)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	if !exist {
		response.ForbiddenErrorWithMsg(c, code.NotInOrganization, "client is not in the organization")
		return
	}

	c.Next()
}

// AuthByTokenOrStudioTokenV2 auth by studio token_v2 or oauth token
func AuthByTokenOrStudioTokenV2(ctx *gin.Context) {
	token := ctx.GetHeader("KG-STUDIO-TOKEN-V2")
	if token != "" || ctx.GetHeader("KG-STUDIO-TOKEN") != "" {
		AuthStudioTokenV2(ctx)
		return
	}

	auth.AuthorizeByTokenOrOAuth([]string{oauth.ScopeAssetRead})(ctx)
}

// KycValidation validate kyc
func KycValidation(ctx *gin.Context) {
	uid := auth.GetUID(ctx)
	if uid == "" {
		response.UnauthorizedWithMsg(ctx, code.OAuthTokenInvalid, "uid not found")
		return
	}

	orgID, err := strconv.Atoi(ctx.Param("orgID"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "invalid organization")
		return
	}

	// check if customer pass kyc
	pass, kgErr := customer.DoesCustomerPassKyc(ctx.Request.Context(), uid, orgID)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	if !pass {
		response.ForbiddenErrorWithMsg(ctx, code.KycStateError, "user does not pass kyc")
		return
	}

	ctx.Set("org_id", orgID)
	ctx.Next()
}
