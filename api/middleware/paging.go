package middleware

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

const (
	// Pagination context keys
	ContextKeyPageNumber = "page_number_int"
	ContextKeyPageSize   = "page_size_int"

	// Default pagination values
	DefaultPageNumber = 1
	DefaultPageSize   = 10

	// Query parameter names
	QueryPageNumber = "page_number"
	QueryPageSize   = "page_size"
)

// Pagination is a middleware that validates and sets pagination parameters in the context
func Pagination() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()

		// Get query parameters for pagination with defaults
		page := c.DefaultQuery(QueryPageNumber, strconv.Itoa(DefaultPageNumber))
		pageSize := c.DefaultQuery(QueryPageSize, strconv.Itoa(DefaultPageSize))

		// Parse and validate page number
		pageNum, err := strconv.Atoi(page)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "Invalid page parameter", map[string]interface{}{
				"error": err.Error(),
			})
			response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid page parameter")
			c.Abort()
			return
		} else if pageNum < 1 {
			kglog.ErrorWithDataCtx(ctx, "Page parameter must be greater than 0", nil)
			response.BadRequestWithMsg(c, code.ParamIncorrect, "Page parameter must be greater than 0")
			c.Abort()
			return
		}

		// Parse and validate page size
		pageSizeNum, err := strconv.Atoi(pageSize)
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "Invalid page_size parameter", map[string]interface{}{
				"error": err.Error(),
			})
			response.BadRequestWithMsg(c, code.ParamIncorrect, "Invalid page_size parameter")
			c.Abort()
			return
		} else if pageSizeNum < 1 {
			kglog.ErrorWithDataCtx(ctx, "Page size parameter must be greater than 0", nil)
			response.BadRequestWithMsg(c, code.ParamIncorrect, "Page size parameter must be greater than 0")
			c.Abort()
			return
		}

		// Set validated values in context
		c.Set(ContextKeyPageNumber, pageNum)
		c.Set(ContextKeyPageSize, pageSizeNum)

		c.Next()
	}
}

// GetPaginationFromContext retrieves pagination parameters from the gin context
// Returns page number, page size, and a boolean indicating if both values were found
func GetPaginationFromContext(c *gin.Context) (int, int, bool) {
	pageNum, pageNumExists := c.Get(ContextKeyPageNumber)
	pageSize, pageSizeExists := c.Get(ContextKeyPageSize)

	if !pageNumExists || !pageSizeExists {
		return DefaultPageNumber, DefaultPageSize, false
	}

	return pageNum.(int), pageSize.(int), true
}
