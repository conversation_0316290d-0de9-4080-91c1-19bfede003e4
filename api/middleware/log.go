package middleware

import (
	"bytes"
	"io"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// AddRequestInfo log request info
func AddRequestInfo(c *gin.Context) {
	var bodyBytes []byte
	if c.Request.Body != nil {
		bodyBytes, _ = io.ReadAll(c.Request.Body)
	}
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// Try to parse the authorization bearer token and log the payload
	payload := make(map[string]interface{})
	headerToLog := c.Request.Header.Clone()
	for k := range headerToLog {
		if k == "Authorization" {
			for i, token := range headerToLog[k] {
				if strings.HasPrefix(token, "Bearer ") {
					token = strings.TrimPrefix(token, "Bearer ")
					// Parse the JWT token without verifying the signature
					jwtToken, _, err := new(jwt.Parser).ParseUnverified(token, jwt.MapClaims{})
					if err == nil {
						if claims, ok := jwtToken.Claims.(jwt.MapClaims); ok {
							for key, value := range claims {
								payload[key] = value
							}
						}
					}
					headerToLog[k][i] = "Bearer [REDACTED]"
				}
			}
		}
	}

	kglog.DebugWithDataCtx(c.Request.Context(), "request: "+c.Request.RequestURI, map[string]interface{}{
		"method":             c.Request.Method,
		"body":               string(bodyBytes),
		"header":             headerToLog,
		"bearerTokenPayload": payload,
	})

	c.Next()
}

// customResponseWriter captures the response data.
type customResponseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// Write overrides the default Write method to capture the response body.
func (w *customResponseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// AddResponseInfo is the middleware to capture and log response info.
func AddResponseInfo() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create a new custom writer
		bufferedLogWriter := &customResponseWriter{body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = bufferedLogWriter

		// Process request
		c.Next()

		// Log response details
		kglog.DebugWithDataCtx(c.Request.Context(), "response: "+c.Request.RequestURI, map[string]interface{}{
			"status": c.Writer.Status(),
			"body":   bufferedLogWriter.body.String(),
		})
	}
}

// AddAuditLog log request info
func AddAuditLog(c *gin.Context) {
	var bodyBytes []byte
	if c.Request.Body != nil {
		bodyBytes, _ = io.ReadAll(c.Request.Body)
	}
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	kglog.DebugWithDataCtx(c.Request.Context(), "request: "+c.Request.RequestURI, map[string]interface{}{
		"method": c.Request.Method,
		"body":   string(bodyBytes),
		"header": c.Request.Header,
		"ip":     c.ClientIP(),
	})

	c.Next()
}
