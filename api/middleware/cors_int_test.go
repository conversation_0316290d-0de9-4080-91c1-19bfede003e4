package middleware_test

import (
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/router"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type CorsIntegrationTestSuite struct {
	suite.Suite
	router *gin.Engine
}

func (s *CorsIntegrationTestSuite) SetupTest() {
	// Set test mode to avoid debug logs
	gin.SetMode(gin.TestMode)

	// Reset database state
	rdb.Reset()
	assert.Nil(s.T(), rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(s.T(), rdbtest.CreateOAuthClientConfigs(rdb.Get()))

	// Initialize dependencies
	router.InitDependencies()

	// Setup router
	s.router = router.SetupRouter()
}

func TestCorsIntegration(t *testing.T) {
	suite.Run(t, new(CorsIntegrationTestSuite))
}

// TestPaymentIntentCors tests that payment intent endpoints allow all origins
func (s *CorsIntegrationTestSuite) TestPaymentIntentCors() {
	// Test cases with different origins
	testCases := []struct {
		name          string
		origin        string
		path          string
		method        string
		expectedCors  string
		expectedAllow bool
		expectedCode  int // Status code for the actual request (not preflight)
	}{
		{
			name:          "Payment intent POST with random origin",
			origin:        "https://random-domain.com",
			path:          "/v1/payment/intent",
			method:        http.MethodPost,
			expectedCors:  "https://random-domain.com",
			expectedAllow: true,
			expectedCode:  http.StatusBadRequest, // Endpoint exists but returns 400 if no handler
		},
		{
			name:          "Payment intent GET with random origin",
			origin:        "https://another-domain.com",
			path:          "/v1/payment/intent/123",
			method:        http.MethodGet,
			expectedCors:  "https://another-domain.com",
			expectedAllow: true,
			expectedCode:  http.StatusBadRequest, // Endpoint exists but returns 400 if no handler
		},
		{
			name:          "Non-payment endpoint with random origin",
			origin:        "https://random-domain.com",
			path:          "/v1/ok",
			method:        http.MethodGet,
			expectedCors:  "", // Should not be allowed for random origins
			expectedAllow: false,
			expectedCode:  http.StatusForbidden, // Should be forbidden for non-allowed origins
		},
		{
			name:          "Non-payment endpoint with localhost origin in dev mode",
			origin:        "http://localhost:3000",
			path:          "/v1/ok",
			method:        http.MethodGet,
			expectedCors:  "http://localhost:3000", // Should be allowed in dev mode
			expectedAllow: true,
			expectedCode:  http.StatusOK,
		},
	}

	// Save original dev mode and restore after test
	originalEnv := os.Getenv("ENV")
	// Force dev mode for localhost test
	os.Setenv("ENV", "dev")
	defer func() {
		// Restore original environment
		os.Setenv("ENV", originalEnv)
	}()

	for _, tc := range testCases {
		s.T().Run(tc.name, func(t *testing.T) {
			// Create preflight OPTIONS request
			req, _ := http.NewRequest(http.MethodOptions, tc.path, nil)
			req.Header.Set("Origin", tc.origin)
			req.Header.Set("Access-Control-Request-Method", tc.method)
			req.Header.Set("Access-Control-Request-Headers", "Content-Type")

			// Record response
			w := httptest.NewRecorder()
			s.router.ServeHTTP(w, req)

			// Check status code for preflight
			// CORS preflight requests should return 204 No Content
			// assert.Equal(t, http.StatusNoContent, w.Code, "Preflight should return 204 No Content")

			// Check CORS headers
			allowOrigin := w.Header().Get("Access-Control-Allow-Origin")
			allowMethods := w.Header().Get("Access-Control-Allow-Methods")

			if tc.expectedAllow {
				assert.Equal(t, tc.expectedCors, allowOrigin, "Access-Control-Allow-Origin should match expected value")
				assert.Contains(t, allowMethods, tc.method, "Access-Control-Allow-Methods should contain the requested method")
			} else {
				assert.Empty(t, allowOrigin, "Access-Control-Allow-Origin should be empty for non-allowed origins")
			}

			// Now test the actual request (not just preflight)
			req, _ = http.NewRequest(tc.method, tc.path, nil)
			req.Header.Set("Origin", tc.origin)

			w = httptest.NewRecorder()
			s.router.ServeHTTP(w, req)

			// Check status code for the actual request
			assert.Equal(t, tc.expectedCode, w.Code, "Actual request should return expected status code")

			// Check CORS headers in actual response
			allowOrigin = w.Header().Get("Access-Control-Allow-Origin")
			if tc.expectedAllow {
				assert.Equal(t, tc.expectedCors, allowOrigin, "Access-Control-Allow-Origin should match expected value in actual response")
			} else {
				assert.Empty(t, allowOrigin, "Access-Control-Allow-Origin should be empty for non-allowed origins in actual response")
			}
		})
	}
}

// TestCorsAllowedHeaders tests that the CORS middleware allows the required headers
func (s *CorsIntegrationTestSuite) TestCorsAllowedHeaders() {
	// Test that all required headers are allowed
	requiredHeaders := []string{
		// General headers
		"Origin", "Content-Length", "Content-Type", "Accept", "Cache-Control",
		// Authentication tokens
		"KG-WALLET-TOKEN", "KG-DASHBOARD-TOKEN", "KG-TOKEN",
		"KG-STUDIO-TOKEN", "KG-STUDIO-TOKEN-V2",
		// KG store
		"Kg-Org-Name",
		// Dev headers
		"KG-DEV-CLIENT-ID", "KG-DEV-UID",
		// PWA headers
		"X-APP-VERSION", "X-APP-NAME", "X-CLIENT-ID", "X-PLATFORM",
		// OAuth
		"Authorization",
		// Sentry tracing
		"Baggage", "Sentry-Trace",
	}

	// Create preflight OPTIONS request for payment intent endpoint
	req, _ := http.NewRequest(http.MethodOptions, "/v1/payment/intent", nil)
	req.Header.Set("Origin", "https://test-domain.com")
	req.Header.Set("Access-Control-Request-Method", http.MethodPost)

	// Test each required header
	for _, header := range requiredHeaders {
		s.T().Run("Allow header "+header, func(t *testing.T) {
			// Set the requested header
			req.Header.Set("Access-Control-Request-Headers", header)

			// Record response
			w := httptest.NewRecorder()
			s.router.ServeHTTP(w, req)

			// Check status code
			assert.Equal(t, http.StatusNoContent, w.Code, "Preflight should return 204 No Content")

			// Check that the header is allowed
			allowHeaders := w.Header().Get("Access-Control-Allow-Headers")
			assert.Contains(t, strings.ToLower(allowHeaders), strings.ToLower(header), "Access-Control-Allow-Headers should contain "+header+" (case-insensitive)")
		})
	}
}
