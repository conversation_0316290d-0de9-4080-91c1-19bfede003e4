package middleware_test

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
	"github.com/stretchr/testify/suite"
)

type testStudioPermissionRequiredSuite struct {
	suite.Suite

	okHandler            gin.HandlerFunc
	orgIDToListOperators int
}

func (s *testStudioPermissionRequiredSuite) SetupSuite() {
	organization.Init(organization.InitParam{
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
	})

	s.orgIDToListOperators = 1
	s.okHandler = func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"code": 0,
		})
	}
	s.NoError(rbac.Init(context.Background()))
}

func (s *testStudioPermissionRequiredSuite) TestStudioPermissionRequiredFailed() {
	uid := "55688"

	cache.NewRedisStudioRoleCacheRepo(cache.Client).SetUserRoles(context.Background(),
		s.orgIDToListOperators, uid, []domain.StudioRole{})

	r := gin.Default()
	r.GET("/test",
		auth.MockOrgID(int(s.orgIDToListOperators)),
		auth.MockAuthorize(uid),
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOperator, rbac.ActionRead),
		s.okHandler,
	)

	// must have owner, admin1, admin2, trader
	w := httptest.NewRecorder()

	req, _ := http.NewRequest(http.MethodGet, "/test", nil)

	r.ServeHTTP(w, req)
	s.Equal(http.StatusForbidden, w.Code)
}

func (s *testStudioPermissionRequiredSuite) TestStudioPermissionRequiredSuccess() {
	uid := "55688"

	cache.NewRedisStudioRoleCacheRepo(cache.Client).SetUserRoles(context.Background(),
		s.orgIDToListOperators, uid, []domain.StudioRole{{Module: "asset_pro", Name: "admin"}})

	r := gin.Default()
	r.GET("/test",
		auth.MockOrgID(int(s.orgIDToListOperators)),
		auth.MockAuthorize(uid),
		middleware.StudioPermissionRequired(rbac.ResourceAssetProOperator, rbac.ActionRead),
		s.okHandler,
	)

	// must have owner, admin1, admin2, trader
	w := httptest.NewRecorder()

	req, _ := http.NewRequest(http.MethodGet, "/test", nil)

	r.ServeHTTP(w, req)
	s.Equal(http.StatusOK, w.Code)

	type resp struct {
		Code int `json:"code"`
	}

	var response resp
	s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
	s.Equal(0, response.Code)
}

func TestStudioPermissionRequiredSuite(t *testing.T) {
	suite.Run(t, new(testStudioPermissionRequiredSuite))
}
