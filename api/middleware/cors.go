package middleware

import (
	"strings"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	lo "github.com/samber/lo"
)

var corsMiddleware gin.HandlerFunc

// CorsMiddlewareFunc is a middleware function that handles CORS
func CorsMiddlewareFunc() gin.HandlerFunc {
	return func(c *gin.Context) {
		// init every time to make sure the config is up to date
		initCors(c)
		corsMiddleware(c)
	}
}

func initCors(c *gin.Context) {
	corsAllowOrigins := append(config.GetStringSlice("CORS_ALLOW_ORIGINS"), oauth.GetDomains()...)
	origin := c.Request.Header.Get("Origin")
	if isAllowedOriginDev(origin) {
		corsAllowOrigins = append(corsAllowOrigins, origin)
	}

	// Add Galxe domains for quest endpoints
	if strings.HasPrefix(c.Request.URL.Path, "/v1/quest") {
		corsAllowOrigins = append(corsAllowOrigins,
			"https://galxe.com",
			"https://app.galxe.com",
			"https://dashboard.galxe.com",
		)
	}
	// Allow all origin for payment intent
	if strings.HasPrefix(c.Request.URL.Path, "/v1/payment") ||
		strings.HasPrefix(c.Request.URL.Path, "/v1/token_signal") ||
		strings.HasPrefix(c.Request.URL.Path, "/v1/studio/api") ||
		strings.HasPrefix(c.Request.URL.Path, "/v1/universal_swap") {
		corsAllowOrigins = append(corsAllowOrigins, origin)
	}

	corsConfig := cors.DefaultConfig()
	corsConfig.AllowMethods = []string{"OPTIONS", "HEAD", "GET", "POST", "PUT", "DELETE", "PATCH"}
	corsConfig.AllowHeaders = []string{
		"Origin", "Content-Length", "Content-Type", "Accept", "Cache-Control", // general
		"x-goog-acl",                            // google cloud storage
		"KG-WALLET-TOKEN",                       // firebase auth token
		"KG-DASHBOARD-TOKEN",                    // dashboard token
		"KG-TOKEN",                              // token for all services
		"KG-STUDIO-TOKEN", "KG-STUDIO-TOKEN-V2", // studio token
		"Kg-Org-Name",                    // kgstore
		"KG-DEV-CLIENT-ID", "KG-DEV-UID", // only for dev
		"X-APP-VERSION", "X-APP-NAME", "X-CLIENT-ID", "X-PLATFORM", // for pwa
		"Authorization",           // OAuth2 token
		"Baggage", "Sentry-Trace", // Sentry tracing
		"X-STUDIO-API-KEY", // studio api key
	}
	corsConfig.AllowBrowserExtensions = true
	if len(corsAllowOrigins) > 0 {
		// trim and remove empty, then unique
		for i, origin := range corsAllowOrigins {
			corsAllowOrigins[i] = strings.TrimSpace(origin)
		}
		corsAllowOrigins = lo.WithoutEmpty(corsAllowOrigins)
		corsAllowOrigins = lo.Uniq(corsAllowOrigins)
		kglog.DebugWithDataCtx(c.Request.Context(), "CORS allow origins", corsAllowOrigins)
		corsConfig.AllowOrigins = corsAllowOrigins
	}
	corsMiddleware = cors.New(corsConfig)
}

func isAllowedOriginDev(origin string) bool {
	if !config.IsDev() {
		return false
	}

	if strings.HasPrefix(origin, "http://localhost:") {
		return true
	}

	return false
}
