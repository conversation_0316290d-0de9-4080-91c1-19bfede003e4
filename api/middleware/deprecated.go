package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// DeprecatedWarn show warning message
func DeprecatedWarn(ctx *gin.Context) {
	kglog.WarningCtx(ctx.Request.Context(), "Deprecated:"+ctx.Request.URL.Path)
	ctx.Next()
}

// DeprecatedInfo show info message
func DeprecatedInfo(ctx *gin.Context) {
	kglog.InfoCtx(ctx.Request.Context(), "Deprecated:"+ctx.Request.URL.Path)
	ctx.Next()
}
