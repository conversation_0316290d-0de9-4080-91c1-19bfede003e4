package middleware

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// RequestLatency logs the request latency
func RequestLatency() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// Process the request
		c.Next()

		latency := time.Since(startTime)

		// Gather additional request information
		data := map[string]interface{}{
			"path":          c.Request.URL.Path,
			"fullPath":      c.FullPath(),
			"latency":       latency.Milliseconds(),
			"status":        c.Writer.Status(),
			"method":        c.Request.Method,
			"clientIP":      c.ClientIP(),
			"userAgent":     c.Request.UserAgent(),
			"referer":       c.Request.Referer(),
			"requestLength": c.Request.ContentLength,
		}

		kglog.DebugWithDataCtx(c.Request.Context(), "HTTP Request Information", data)
	}
}
