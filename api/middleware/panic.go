package middleware

import (
	em "emperror.dev/emperror"
	"emperror.dev/errors"
	ee "emperror.dev/errors"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

type stackTracer interface {
	StackTrace() errors.StackTrace
}

// HandlePanic that recovers from any panics and handles the error
func HandlePanic(c *gin.Context) {
	handleError := em.ErrorHandlerFunc(func(err error) {
		kglog.ErrorCtx(c.Request.Context(), err.Error())
		errTracer, ok := err.(stackTracer) // ok is false if errors doesn't implement stackTracer
		if ok {
			kglog.ErrorWithDataCtx(c.Request.Context(), "stack trace", errTracer.StackTrace())
		}
		response.AbortByAny(c, ee.WithStackDepth(err, 10))
	})
	defer em.<PERSON><PERSON><PERSON><PERSON>over(handleError)
	c.Next()
}
