package middleware

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/api/session"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// AuthDashboardToken auth dashboard session token
func AuthDashboardToken(ctx *gin.Context) {
	token := ctx.GetHeader("KG-DASHBOARD-TOKEN")
	if token == "" {
		response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.DashboardTokenNotProvided, "token not provided", nil)
		return
	}
	sessionData := session.GetSession(token)
	if sessionData == nil {
		response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.DashboardTokenExpired, "token expired", nil)
		return
	}

	// check client ip
	clientIP := ctx.ClientIP()
	if sessionData.ClientIP != clientIP {
		log.Println("User changed IP from", sessionData.ClientIP, " to", clientIP)
		sessionData.ClientIP = clientIP
		session.UpdateSession(ctx.Request.Context(), token, sessionData) // or return error
	}

	ctx.Set("dashboard:uid", int(sessionData.UID)) // convert to int for easier get
	ctx.Set("dashboard:ownerAddress", sessionData.OwnerAddress)
	ctx.Next()
}
