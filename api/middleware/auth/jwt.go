package auth

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
)

// ContractAddressJWTToken auth jwt token
func ContractAddressJWTToken(ctx *gin.Context) {
	idToken := ctx.GetHeader("Authorization")
	if idToken == "" {
		response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.IDTokenNotProvided, "id token not provided", nil)
		return
	}
	if idToken == "KG-DEV:123456" {
		env := config.GetString("ENV")
		if env != "local" && env != "dev" {
			fmt.Println("only allow in local/dev development")
			response.ForbiddenErrorWithMsg(ctx, code.IDTokenNotValid, "for local/dev development only")
			return
		}
		ctx.Next()
		return
	}
	parts := strings.SplitN(idToken, " ", 2)
	if !(len(parts) == 2 && parts[0] == "Bearer") {
		response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.IDTokenNotValid, "invalid auth token", nil)
		return
	}

	claims, errCode, err := validateToken(parts[1])
	if err != nil {
		response.ErrorWithMsg(ctx, http.StatusUnauthorized, errCode, err.Error(), nil)
		return
	}
	ctx.Set("jwt_claims", claims)
	ctx.Next()
}

// ContractAddressClaims jwt claims including contract address list
type ContractAddressClaims struct {
	jwt.StandardClaims
	ContractAddressList []string `json:"contract_address_list"`
}

func validateToken(tokenString string) (*ContractAddressClaims, int, error) {
	token, err := jwt.ParseWithClaims(tokenString, &ContractAddressClaims{}, func(token *jwt.Token) (interface{}, error) {
		encryptKey := config.GetString("ENCRYPTION_KEY")
		return []byte(encryptKey), nil
	})
	if err != nil {
		return nil, code.IDTokenNotValid, err
	}

	if !token.Valid {
		return nil, code.IDTokenNotValid, fmt.Errorf("Invalid JWT Token")
	}

	if time.Now().Unix() > token.Claims.(*ContractAddressClaims).ExpiresAt {
		return nil, code.IDTokenNotValid, fmt.Errorf("token expired")
	}

	if claims, ok := token.Claims.(*ContractAddressClaims); ok {
		return claims, 0, nil
	}
	return nil, code.IDTokenNotValid, fmt.Errorf("Invalid JWT Token")
}
