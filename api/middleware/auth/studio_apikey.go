package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/service/studio"
)

const (
	// StudioAPIKeyHeader is the header name for Studio API key
	StudioAPIKeyHeader = "X-STUDIO-API-KEY"
)

// ValidateStudioAPIKey
//
// 1. verifies the Studio API key and sets the user ID and organization ID in the context
// 2. checks if the client ID is in the organization
func ValidateStudioAPIKey(c *gin.Context) {
	apiKey := c.GetHeader(StudioAPIKeyHeader)
	if apiKey == "" {
		response.ErrorWithMsg(c, http.StatusUnauthorized, code.APIKeyNotProvided, "API key not provided", nil)
		c.Abort()
		return
	}

	// Verify the API key
	apiKeyModel, kgErr := studio.GetStudioUserAPIKey(c.Request.Context(), apiKey)
	if kgErr != nil {
		// Return consistent unauthorized response for any API key validation failure
		response.ErrorWithMsg(c, http.StatusUnauthorized, code.APIKeyNotValid, "Invalid API key", nil)
		c.Abort()
		return
	}

	// Update last used timestamp
	kgErr = studio.UpdateAPIKeyLastUsed(c.Request.Context(), apiKeyModel.ID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "Failed to update API key last used timestamp", map[string]any{
			"error": kgErr.String(),
			"keyId": apiKeyModel.ID,
		})
		// We don't want to fail the request if updating the timestamp fails
	}

	// Set user ID and organization ID in context
	c.Set("uid", apiKeyModel.UID)
	c.Set("org_id", apiKeyModel.OrgID)

	// Set client ID if it exists
	clientID := c.GetHeader("X-Client-ID")
	if clientID != "" {
		// Check client id in org
		clientInOrg, kgErr := organization.IsOAuthClientInOrganization(c.Request.Context(), apiKeyModel.OrgID, clientID)
		if kgErr != nil {
			// System error while checking client
			kglog.ErrorWithDataCtx(c.Request.Context(), "System error while checking client in organization", map[string]any{
				"error": kgErr.String(),
			})
			response.ErrorWithMsg(c, http.StatusForbidden, code.ClientForbidden, "Client access forbidden", nil)
			c.Abort()
			return
		}
		if !clientInOrg {
			response.ErrorWithMsg(c, http.StatusForbidden, code.ClientForbidden, "Client access forbidden", nil)
			c.Abort()
			return
		}
		c.Set("client_id", clientID)
	}

	kglog.InfoWithDataCtx(c.Request.Context(), "Valid Studio API key", map[string]interface{}{
		"keyId":         apiKeyModel.ID,
		"uid":           apiKeyModel.UID,
		"organization":  apiKeyModel.OrgID,
		"requestMethod": c.Request.Method,
		"requestURI":    c.Request.RequestURI,
	})

	c.Next()
}
