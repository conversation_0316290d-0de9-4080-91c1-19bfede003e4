package auth

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
)

// AuthorizeCompatible returns the auth middleware based on the config
func AuthorizeCompatible() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// verify id token if set
		err := VerifyIDTokenOrOauth(ctx)
		if err == nil {
			ctx.Next()
		} else if err == code.ErrIdTokenNotValid {
			response.ErrorWithMsg(ctx, http.StatusForbidden, code.IDTokenNotProvided, err.Error(), nil)
			return
		} else if err == code.ErrOauthTokenNotValid {
			response.ErrorWithMsg(ctx, http.StatusForbidden, code.KgTokenNotProvided, err.Error(), nil)
			return
		} else {
			response.ErrorWithMsg(ctx, http.StatusForbidden, code.InternalError, err.Error(), nil)
			return
		}
	}
}

// VerifyIDTokenOrOauth verifies the id token or oauth token to be compatible with old version. The old version is firebase auth, and the new version is oauth.
func VerifyIDTokenOrOauth(ctx *gin.Context) error {
	// firebase auth
	firebaseAuthToken := ctx.GetHeader("KG-WALLET-TOKEN")
	oauthToken := ctx.GetHeader("Authorization")

	// KW-1728 compatible version: if firebaseAuthToken is empty, use oauthToken
	if firebaseAuthToken == "" && oauthToken == "" {
		return nil
	} else if oauthToken == "" {
		oauthToken = firebaseAuthToken
		if !strings.HasPrefix(oauthToken, "Bearer ") {
			oauthToken = "Bearer " + oauthToken
		}
	}

	if firebaseAuthToken == "KG-DEV:123456" {
		devUID := ctx.GetHeader("KG-DEV-UID")
		devClientID := ctx.GetHeader("KG-DEV-CLIENT-ID")
		if !config.IsDev() && !strings.HasPrefix(ctx.GetHeader("KG-DEV-UID"), "testuser-") {
			kglog.WarningWithDataCtx(ctx.Request.Context(), "only allow in local/dev development", map[string]interface{}{
				"uid":       devUID,
				"client_id": devClientID,
			})
			return code.ErrIdTokenNotValid
		} else {
			ctx.Set("uid", devUID)
			ctx.Set("client_id", devClientID)
			return nil
		}
	}

	token, walletErr := firebase.VerifyIDToken(firebaseAuthToken)
	if walletErr == nil {
		ctx.Set("uid", token.UID)
		return nil
	}

	uid, scopes, clientID, oauthErr := oauth.ValidateOAuthAccessToken(ctx.Request.Context(), oauthToken, []string{})
	if oauthErr == nil {
		ctx.Set(oauthScopesKey, scopes)
		ctx.Set("uid", uid)
		ctx.Set("client_id", clientID)
		return nil
	} else {
		kglog.WarningWithDataCtx(ctx.Request.Context(), "oauth token not valid", map[string]interface{}{
			"oauth_token": oauthToken,
			"err":         oauthErr.Error(),
		})
	}

	return nil
}
