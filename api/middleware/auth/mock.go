package auth

import (
	"github.com/gin-gonic/gin"
)

// MockAuthorize mocks the authorize middleware for testing
func MockAuthorize(uid string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Set("uid", uid)
		ctx.Next()
	}
}

// MockMemberID mocks the member id middleware for testing
func MockMemberID(memberID string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Set("member_id", memberID)
		ctx.Next()
	}
}

// MockRole mocks the role middleware for testing
func MockRole(role string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Set("role", role)
		ctx.Next()
	}
}

// MockClientID mocks the client id middleware for testing
func MockClientID(clientID string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Set("client_id", clientID)
		ctx.Next()
	}
}

// MockOrgID mocks the org id middleware for testing
func MockOrgID(orgID int) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Set("org_id", orgID)
		ctx.Next()
	}
}
