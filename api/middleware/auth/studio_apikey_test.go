package auth_test

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/studio"
	"github.com/stretchr/testify/assert"
)

func setupStudioAPIKeyTest(t *testing.T) (uid string, orgID int, router *gin.Engine) {
	// Setup test environment
	gin.SetMode(gin.TestMode)
	rdb.Reset()

	// Create organization data
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))

	// Create test user
	users, uid, _, _ := dbtest.User()
	assert.Nil(t, rdb.GormRepo().BatchSetUsers(context.Background(), users))

	// Initialize service
	studio.Init(repo.Unified())
	organization.Init(organization.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})

	// Create test router
	r := gin.New()
	r.Use(gin.Recovery())

	// Get first organization ID (should be 1 based on test data)
	orgID = 1

	return uid, orgID, r
}

func TestValidateStudioAPIKey(t *testing.T) {
	uid, orgID, r := setupStudioAPIKeyTest(t)
	ctx := context.Background()

	// Create a test API key directly in the DB
	_, apiKey, err := rdbtest.CreateTestAPIKeyForOrg(rdb.Get(), orgID, uid)
	assert.Nil(t, err)
	assert.NotEmpty(t, apiKey)

	// Create client
	clientID := "20991a3ae83233d6de85d62906d71fd3" // Use existing client ID from test data

	// Setup test endpoint
	r.GET("/test", auth.ValidateStudioAPIKey, func(c *gin.Context) {
		// Return success with stored context values
		c.JSON(http.StatusOK, gin.H{
			"uid":       c.GetString("uid"),
			"org_id":    c.GetInt("org_id"),
			"client_id": c.GetString("client_id"),
		})
	})

	t.Run("Valid API Key", func(t *testing.T) {
		// Create request with API key
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set(auth.StudioAPIKeyHeader, apiKey)

		// Record response
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assert successful response
		assert.Equal(t, http.StatusOK, w.Code)

		// Check if correct values were set in context
		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)

		assert.Equal(t, uid, resp["uid"])
		assert.Equal(t, float64(orgID), resp["org_id"])
		assert.Empty(t, resp["client_id"]) // No client ID was provided
	})

	t.Run("Valid API Key with Client ID", func(t *testing.T) {
		// No need to insert a new record as this client ID already exists in the organization
		// from the test setup

		// Create request with API key and client ID
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set(auth.StudioAPIKeyHeader, apiKey)
		req.Header.Set("X-Client-ID", clientID)

		// Record response
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assert successful response
		assert.Equal(t, http.StatusOK, w.Code)

		// Check if correct values were set in context
		var resp map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)

		assert.Equal(t, uid, resp["uid"])
		assert.Equal(t, float64(orgID), resp["org_id"])
		assert.Equal(t, clientID, resp["client_id"])
	})

	t.Run("No API Key", func(t *testing.T) {
		// Create request without API key
		req, _ := http.NewRequest("GET", "/test", nil)

		// Record response
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assert error response
		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)

		assert.Equal(t, float64(code.APIKeyNotProvided), resp["code"]) // APIKeyNotProvided code
	})

	t.Run("Invalid API Key", func(t *testing.T) {
		// Create request with invalid API key
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set(auth.StudioAPIKeyHeader, "invalid.apikey")

		// Record response
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assert error response
		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)

		assert.Equal(t, float64(code.APIKeyNotValid), resp["code"])
		assert.Equal(t, "Invalid API key", resp["message"])
	})

	t.Run("Client Not in Organization", func(t *testing.T) {
		// Create request with API key and invalid client ID
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set(auth.StudioAPIKeyHeader, apiKey)
		req.Header.Set("X-Client-ID", "invalid-client-id")

		// Record response
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assert error response
		assert.Equal(t, http.StatusForbidden, w.Code)

		var resp map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		assert.Nil(t, err)

		assert.Equal(t, float64(code.ClientForbidden), resp["code"])
		assert.Equal(t, "Client access forbidden", resp["message"])
	})

	t.Run("Updates Last Used Timestamp", func(t *testing.T) {
		// Get initial API key
		initialApiKeyModel, kgErr := studio.GetStudioUserAPIKey(ctx, apiKey)
		assert.Nil(t, kgErr)
		initialLastUsed := initialApiKeyModel.LastUsedAt

		// Wait a moment to ensure timestamps differ
		time.Sleep(time.Second * 2)

		// Create request with API key
		req, _ := http.NewRequest("GET", "/test", nil)
		req.Header.Set(auth.StudioAPIKeyHeader, apiKey)

		// Record response
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Assert successful response
		assert.Equal(t, http.StatusOK, w.Code)

		// Check if last used timestamp was updated
		updatedApiKeyModel, kgErr := studio.GetStudioUserAPIKey(ctx, apiKey)
		assert.Nil(t, kgErr)

		// LastUsedAt should now be populated and greater than before
		assert.NotNil(t, updatedApiKeyModel.LastUsedAt)
		if initialLastUsed != nil {
			assert.Greater(t, *updatedApiKeyModel.LastUsedAt, *initialLastUsed)
		}
	})
}
