package auth_test

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/router"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
)

func setupIntegrationTestEnv(t *testing.T) (uid string) {
	gin.SetMode(gin.TestMode)
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))

	router.InitDependencies()

	users, uid, _, _ := dbtest.User()
	assert.Nil(t, rdb.GormRepo().BatchSetUsers(context.Background(), users))
	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)
	user.Init(repo.Unified())

	return
}

func generateOAuthToken(t *testing.T, uid string, exp time.Duration, scope string) string {
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeOAuth)
	access, _, err := strategy.CreateToken(&jwtservice.AccessTokenData{
		UserID:       uid,
		ClientID:     "20991a3ae83233d6de85d62906d71fd3",
		Scopes:       scope,
		IsGenRefresh: false,
		ExpiresAt:    time.Now().Add(exp).Unix(),
	})
	assert.NoError(t, err)
	return access
}

func TestOAuthAccessTokenIntegration(t *testing.T) {
	uid := setupIntegrationTestEnv(t)

	r := router.SetupRouter()

	t.Run("Invalid OAuth Access Token", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/v1/user/info", nil)
		req.Header.Set("Authorization", "Bearer invalid-token")

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		var res map[string]interface{}
		assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &res))
		assert.Equal(t, float64(401), res["status"].(float64))
		assert.Equal(t, float64(1034), res["code"].(float64))
		assert.Equal(t, "token contains an invalid number of segments", res["message"])
	})

	t.Run("No Token", func(t *testing.T) {
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/v1/user/info", nil)

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		var res map[string]interface{}
		assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &res))
		assert.Equal(t, float64(401), res["status"].(float64))
		assert.Equal(t, float64(1034), res["code"].(float64))
		assert.Equal(t, "oauth token not provided", res["message"])
	})

	t.Run("Expired OAuth Access Token", func(t *testing.T) {
		access := generateOAuthToken(t, uid, 1*time.Second, oauth.ScopeUserInfoRead)
		time.Sleep(3 * time.Second)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/v1/user/info", nil)
		req.Header.Set("Authorization", "Bearer "+access)

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		var res map[string]interface{}
		assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &res))
		assert.Equal(t, float64(401), res["status"].(float64))
		assert.Equal(t, float64(1034), res["code"].(float64))
		assert.Equal(t, res["message"], "jwt expired")
	})

	t.Run("Valid OAuth Access Token", func(t *testing.T) {
		access := generateOAuthToken(t, uid, time.Hour, oauth.ScopeUserInfoRead)
		w := httptest.NewRecorder()
		req, _ := http.NewRequest("GET", "/v1/user/info", nil)
		req.Header.Set("Authorization", "Bearer "+access)

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		var res struct {
			Code int             `json:"code"`
			Data domain.UserInfo `json:"data"`
		}
		assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &res))
		assert.Equal(t, 0, res.Code)
		assert.Equal(t, "哈里", res.Data.DisplayName)
	})
}

type refreshTokenReq struct {
	RefreshToken string `json:"refresh_token"`
}

type refreshTokenData struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

func TestOAuthRefreshTokenIntegration(t *testing.T) {
	uid := setupIntegrationTestEnv(t)

	r := router.SetupRouter()

	t.Run("Invalid Refresh Token", func(t *testing.T) {
		w := httptest.NewRecorder()
		var body refreshTokenReq
		body.RefreshToken = "invalid token"
		reqBody, _ := json.Marshal(body)
		req, _ := http.NewRequest("POST", "/v1/oauth/refresh", strings.NewReader(string(reqBody)))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer invalid-token")

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
		var res map[string]interface{}
		assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &res))
		assert.Equal(t, float64(403), res["status"].(float64))
		assert.Equal(t, float64(1034), res["code"].(float64))
		assert.Equal(t, "token contains an invalid number of segments", res["message"])
	})

	t.Run("Expired Refresh Token", func(t *testing.T) {
		refresh := generateOAuthToken(t, uid, 1*time.Second, oauth.ScopeUserInfoRead)
		time.Sleep(2 * time.Second)
		w := httptest.NewRecorder()
		var body refreshTokenReq
		body.RefreshToken = refresh
		reqBody, _ := json.Marshal(body)
		req, _ := http.NewRequest("POST", "/v1/oauth/refresh", strings.NewReader(string(reqBody)))
		req.Header.Set("Content-Type", "application/json")

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		if w.Code != http.StatusOK {
			t.Errorf("Response body: %s\n", w.Body.String())
		}
		var res struct {
			Code int              `json:"code"`
			Data refreshTokenData `json:"data"`
		}
		assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &res))
		assert.Equal(t, 0, res.Code)
		assert.NotEmpty(t, res.Data.AccessToken)
		assert.NotEmpty(t, res.Data.RefreshToken)
	})

	t.Run("Valid Refresh Token", func(t *testing.T) {
		refresh := generateOAuthToken(t, uid, time.Hour, oauth.ScopeUserInfoRead)
		w := httptest.NewRecorder()
		var body refreshTokenReq
		body.RefreshToken = refresh
		reqBody, _ := json.Marshal(body)
		req, _ := http.NewRequest("POST", "/v1/oauth/refresh", strings.NewReader(string(reqBody)))
		req.Header.Set("Content-Type", "application/json")

		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		if w.Code != http.StatusOK {
			t.Errorf("Response body: %s\n", w.Body.String())
		}
		var res struct {
			Code int              `json:"code"`
			Data refreshTokenData `json:"data"`
		}
		assert.NoError(t, json.Unmarshal(w.Body.Bytes(), &res))
		assert.Equal(t, 0, res.Code)
		assert.NotEmpty(t, res.Data.AccessToken)
		assert.NotEmpty(t, res.Data.RefreshToken)
	})
}
