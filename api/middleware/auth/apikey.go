package auth

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

// APIKey verify whether the API key is valid
func APIKey(ctx *gin.Context) {
	apiKey := ctx.GetHeader("KG-WALLET-API-KEY")
	if apiKey == "" {
		response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.APIKeyNotProvided, "API key not provided", nil)
		return
	}
	key, err := rdb.GetAPIKey(ctx.Request.Context(), apiKey)
	if err != nil || key == nil {
		response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.APIKeyNotValid, "API key not valid", nil)
		return
	}
	kglog.InfoWithDataCtx(ctx.Request.Context(), "Valid API key", map[string]interface{}{
		"keyId":      key.ID,
		"requestURI": ctx.Request.RequestURI,
	})
	ctx.Next()
}
