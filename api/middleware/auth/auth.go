package auth

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	jwtservice "github.com/kryptogo/kg-wallet-backend/pkg/service/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"google.golang.org/api/idtoken"
)

// Constants for auth middleware
const (
	// GoogleClientID is the client id of gcloud cli
	GoogleClientID = "32555940559.apps.googleusercontent.com"
	oauthScopesKey = "oauthScopes"
)

// authMode defines the authentication mode to use
type authMode int

const (
	// AuthModeInternalOnly allows only internal GCP tokens
	AuthModeInternalOnly authMode = iota
	// AuthModeKGToken requires a valid KG token
	AuthModeKGToken
	// AuthModeIDToken requires a valid ID token
	AuthModeIDToken
	// AuthModeOptionalIDToken accepts but doesn't require an ID token
	AuthModeOptionalIDToken
	// AuthModeOAuth requires a valid OAuth token with specified scopes
	AuthModeOAuth
	// AuthModeOptionalOAuth accepts but doesn't require an OAuth token
	AuthModeOptionalOAuth
	// AuthModeIDTokenOrOAuth accepts either ID token or OAuth token
	AuthModeIDTokenOrOAuth
	// AuthModeKGTokenOrOAuth accepts either KG token or OAuth token
	AuthModeKGTokenOrOAuth
	// AuthModeKGTokenOrStudioToken accepts either KG token or KG Studio token
	AuthModeKGTokenOrStudioToken
)

// Config holds the configuration for auth middleware
type Config struct {
	// Mode specifies the authentication mode
	Mode authMode
	// OAuthScopes lists OAuth scopes required for validation
	OAuthScopes []string
}

// AuthorizeByTokenOrOAuth returns the auth middleware for token or oauth.
// If KG-WALLET-TOKEN is provided, it will be verified first. Otherwise it will try to verify the oauth access token
func AuthorizeByTokenOrOAuth(scopes []string) gin.HandlerFunc {
	return Authorize(&Config{
		Mode:        AuthModeKGTokenOrOAuth,
		OAuthScopes: scopes,
	})
}

// AuthorizeByToken returns the auth middleware for id token
func AuthorizeByToken() gin.HandlerFunc {
	return Authorize(&Config{
		Mode: AuthModeIDToken,
	})
}

// AuthorizeByOptionalToken returns the auth middleware for optional id token
func AuthorizeByOptionalToken() gin.HandlerFunc {
	return Authorize(&Config{
		Mode: AuthModeOptionalIDToken,
	})
}

// AuthorizeOnlyInternal returns the auth middleware for internal usage only
func AuthorizeOnlyInternal() gin.HandlerFunc {
	return Authorize(&Config{
		Mode: AuthModeInternalOnly,
	})
}

// AuthorizeByKgToken returns the auth middleware for kg token
func AuthorizeByKgToken() gin.HandlerFunc {
	return Authorize(&Config{
		Mode: AuthModeKGToken,
	})
}

// AuthorizeByKgTokenOrOAuth returns the auth middleware for kg token or oauth
// Either token can be valid for the request to proceed
func AuthorizeByKgTokenOrOAuth(scopes []string) gin.HandlerFunc {
	return Authorize(&Config{
		Mode:        AuthModeKGTokenOrOAuth,
		OAuthScopes: scopes,
	})
}

// AuthorizeByOAuth returns the auth middleware for oauth
func AuthorizeByOAuth(scopes []string) gin.HandlerFunc {
	return Authorize(&Config{
		Mode:        AuthModeOAuth,
		OAuthScopes: scopes,
	})
}

// AuthorizeByOptionalOAuth returns the auth middleware for optional oauth
func AuthorizeByOptionalOAuth(scopes []string) gin.HandlerFunc {
	return Authorize(&Config{
		Mode:        AuthModeOptionalOAuth,
		OAuthScopes: scopes,
	})
}

// AuthorizeByKgTokenOrKgStudioToken returns the auth middleware that accepts either KG token or KG Studio token
func AuthorizeByKgTokenOrKgStudioToken() gin.HandlerFunc {
	return Authorize(&Config{
		Mode: AuthModeKGTokenOrStudioToken,
	})
}

// AuthorizeBySharedSecret returns the auth middleware for shared secret
// between compliance and kg-wallet-backend
func AuthorizeBySharedSecret(sharedKey string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		body, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			response.ErrorWithMsg(ctx, http.StatusBadRequest, code.InvalidBody, err.Error(), nil)
			return
		}
		// Reassigning the body for future use in the handler
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))

		method := ctx.Request.Method
		fullURL := ctx.Request.URL.String() // Includes query parameters
		timestamp := ctx.GetHeader("X-Timestamp")
		receivedSignature := ctx.GetHeader("X-Signature")
		expectedSignature := createSignature(method, fullURL, string(body), timestamp, sharedKey)

		if hmac.Equal([]byte(receivedSignature), []byte(expectedSignature)) {
			ctx.Next()
			return
		}

		response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.InvalidSignature, "invalid signature", nil)
	}
}

// Authorize returns the auth middleware based on the auth mode
func Authorize(authConfig *Config) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Always check internal token first
		if verifyInternalToken(ctx) {
			ctx.Next()
			return
		}

		// Handle internal-only mode
		if authConfig.Mode == AuthModeInternalOnly {
			response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.IDTokenNotProvided, "id token not valid", nil)
			return
		}

		// Handle KG token mode
		if authConfig.Mode == AuthModeKGToken || authConfig.Mode == AuthModeKGTokenOrOAuth || authConfig.Mode == AuthModeKGTokenOrStudioToken || authConfig.Mode == AuthModeOptionalIDToken || authConfig.Mode == AuthModeOptionalOAuth {
			kgTokenOk, kgTokenErr := verifyKgToken(ctx)
			if kgTokenOk {
				ctx.Next()
				return
			}

			// If KG token is required and not in OR mode, reject request
			if authConfig.Mode == AuthModeKGToken {
				response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.KgTokenInvalid, kgTokenErr.Error(), nil)
				return
			}
		}

		// Handle Studio token for KGTokenOrStudioToken mode
		if authConfig.Mode == AuthModeKGTokenOrStudioToken {
			studioTokenOk, studioTokenErr := VerifyStudioToken(ctx)
			if studioTokenOk {
				ctx.Next()
				return
			}

			// If we reach here, neither KG token nor Studio token is valid
			kgTokenErr := errors.New("kg token not provided or invalid")
			errData := map[string]interface{}{
				"kg_token_error":     kgTokenErr.Error(),
				"studio_token_error": studioTokenErr.Error(),
			}
			response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.KgTokenInvalid, "authentication failed", errData)
			return
		}

		// Handle ID token mode
		if authConfig.Mode == AuthModeIDToken || authConfig.Mode == AuthModeOptionalIDToken || authConfig.Mode == AuthModeIDTokenOrOAuth {
			idTokenOk, idTokenErr := verifyIDToken(ctx)
			if idTokenOk {
				ctx.Next()
				return
			}

			// If ID token is required and not optional or OR mode, reject request
			if authConfig.Mode == AuthModeIDToken {
				response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.IDTokenNotValid, idTokenErr.Error(), nil)
				return
			}
		}

		// Handle OAuth modes
		if hasOAuthMode(authConfig.Mode) {
			// Skip validation if OAuth is optional and no Authorization header is present
			if isOptionalOAuthMode(authConfig.Mode) && ctx.GetHeader("Authorization") == "" {
				ctx.Next()
				return
			}

			oauthOk, oauthErr := verifyOAuthToken(ctx, authConfig.OAuthScopes)
			if oauthOk {
				ctx.Next()
				return
			}

			// For OR modes, return combined error for both token types
			if authConfig.Mode == AuthModeKGTokenOrOAuth {
				kgTokenErr := errors.New("kg token not provided or invalid")
				errData := map[string]interface{}{
					"kg_token_error":    kgTokenErr.Error(),
					"oauth_token_error": oauthErr.Error(),
				}
				response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.OAuthTokenInvalid, oauthErr.Error(), errData)
				return
			}

			if authConfig.Mode == AuthModeIDTokenOrOAuth {
				idTokenErr := errors.New("id token not provided or invalid")
				errData := map[string]interface{}{
					"id_token_error":    idTokenErr.Error(),
					"oauth_token_error": oauthErr.Error(),
				}
				response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.OAuthTokenInvalid, oauthErr.Error(), errData)
				return
			}

			// If OAuth is required and failed, reject request
			if !isOptionalOAuthMode(authConfig.Mode) {
				response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.OAuthTokenInvalid, oauthErr.Error(), nil)
				return
			}
		}

		// If we reach here, allow the request for optional modes
		if isOptionalMode(authConfig.Mode) {
			ctx.Next()
			return
		}

		// If we reach here with no authentication, reject the request
		response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.OAuthTokenInvalid, "authentication required", nil)
	}
}

// isOptionalMode checks if the auth mode is optional
func isOptionalMode(mode authMode) bool {
	return mode == AuthModeOptionalIDToken || mode == AuthModeOptionalOAuth
}

// isOptionalOAuthMode checks if the auth mode includes optional OAuth
func isOptionalOAuthMode(mode authMode) bool {
	return mode == AuthModeOptionalOAuth
}

// hasOAuthMode checks if the auth mode includes OAuth
func hasOAuthMode(mode authMode) bool {
	return mode == AuthModeOAuth || mode == AuthModeOptionalOAuth ||
		mode == AuthModeIDTokenOrOAuth || mode == AuthModeKGTokenOrOAuth
}

// Token verification functions

// verifyInternalToken verifies the internal token and sets user info if valid
func verifyInternalToken(ctx *gin.Context) bool {
	internalToken := ctx.GetHeader("KG-INTERNAL-TOKEN")
	if internalToken == "" {
		return false
	}

	devUID := ctx.GetHeader("KG-DEV-UID")
	devClientID := ctx.GetHeader("KG-DEV-CLIENT-ID")
	email, ok := verifyGcpIDToken(ctx.Request.Context(), internalToken)
	if ok {
		ctx.Set("uid", devUID)
		ctx.Set("gcp_email", email)
		ctx.Set("client_id", devClientID)
		return true
	}
	return false
}

// verifyDevToken verifies development token (KG-DEV:123456)
func verifyDevToken(ctx *gin.Context, token, tokenType string) bool {
	if token != "KG-DEV:123456" {
		return false
	}

	devUID := ctx.GetHeader("KG-DEV-UID")
	devClientID := ctx.GetHeader("KG-DEV-CLIENT-ID")

	if !config.IsDev() && !strings.HasPrefix(devUID, "testuser-") {
		kglog.WarningWithDataCtx(ctx.Request.Context(), "only allow in local/dev development", map[string]interface{}{
			"uid":       devUID,
			"client_id": devClientID,
		})
		return false
	}

	ctx.Set("uid", devUID)
	ctx.Set("client_id", devClientID)
	return true
}

// verifyKgToken verifies the KG token and sets user info if valid
func verifyKgToken(ctx *gin.Context) (bool, error) {
	kgToken := ctx.GetHeader("KG-TOKEN")
	if kgToken == "" {
		// Also check for KG-WALLET-TOKEN as fallback (this matches the old behavior)
		kgToken = ctx.GetHeader("KG-WALLET-TOKEN")
		if kgToken == "" {
			return false, errors.New("kg token not provided")
		}
	}

	if verifyDevToken(ctx, kgToken, "kg") {
		return true, nil
	}

	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeKg)
	claimsI, _, err := strategy.Parse(kgToken)
	if err != nil {
		return false, err
	}

	claims, ok := claimsI.(*jwt.StandardClaims)
	if !ok {
		kgAccessTokenClaims, ok := claimsI.(*jwtservice.KGTokenClaims)
		if !ok {
			return false, errors.New("invalid token claims type")
		}
		// Handle KGAccessTokenClaims
		userID := kgAccessTokenClaims.Subject
		ctx.Set("uid", userID)
		return true, nil
	}

	// Handle standard claims
	userID := claims.Subject
	ctx.Set("uid", userID)
	return true, nil
}

// verifyIDToken verifies the Firebase ID token and sets user info if valid
func verifyIDToken(ctx *gin.Context) (bool, error) {
	idToken := ctx.GetHeader("KG-WALLET-TOKEN")
	if idToken == "" {
		// Also check for KG-TOKEN as fallback
		idToken = ctx.GetHeader("KG-TOKEN")
		if idToken == "" {
			return false, errors.New("id token not provided")
		}
	}

	if verifyDevToken(ctx, idToken, "id") {
		return true, nil
	}

	token, err := firebase.VerifyIDToken(idToken)
	if err != nil {
		return false, err
	}

	ctx.Set("uid", token.UID)
	return true, nil
}

// verifyOAuthToken verifies the OAuth token with required scopes and sets user info if valid
func verifyOAuthToken(ctx *gin.Context, requiredScopes []string) (bool, error) {
	authorization := ctx.GetHeader("Authorization")
	if authorization == "" {
		return false, errors.New("oauth token not provided")
	}

	uid, scopes, clientID, err := oauth.ValidateOAuthAccessToken(ctx.Request.Context(), authorization, requiredScopes)
	if err != nil {
		return false, err
	}

	ctx.Set(oauthScopesKey, scopes)
	ctx.Set("uid", uid)
	ctx.Set("client_id", clientID)
	return true, nil
}

// verifyGcpIDToken return email and whether it's verified
func verifyGcpIDToken(ctx context.Context, idToken string) (string, bool) {
	if idToken == "" {
		return "", false
	}
	payload, err := idtoken.Validate(context.Background(), idToken, GoogleClientID)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "[verifyGcpIDToken] id token validate error: "+err.Error(), idToken)
		return "", false
	}
	if emailI, ok := payload.Claims["email"]; ok {
		if email, ok := emailI.(string); ok {
			if verifiedI, ok := payload.Claims["email_verified"]; ok {
				if verified, ok := verifiedI.(bool); ok {
					if !verified {
						kglog.ErrorfCtx(ctx, "[verifyGcpIDToken] email not verified: %s", email)
						return "", false
					} else if strings.HasSuffix(email, "@kryptogo.com") {
						kglog.DebugfCtx(ctx, "[verifyGcpIDToken] id token validate success: %s", email)
						return email, true
					}
				}
			}
		}
	}
	kglog.ErrorfCtx(ctx, "[verifyGcpIDToken] fail to validate id token: %s", idToken)
	return "", false
}

// VerifyStudioToken verifies the Studio token (KG-STUDIO-TOKEN-V2 or KG-STUDIO-TOKEN) and sets user info if valid.
// It handles both the production token and development token (KG-DEV:123456) cases.
// Returns bool indicating if the token is valid, and an error with details if not valid.
// This function is exported for use in other middleware packages to avoid duplication.
func VerifyStudioToken(ctx *gin.Context) (bool, error) {
	studioToken := ctx.GetHeader("KG-STUDIO-TOKEN-V2")
	if studioToken == "" {
		// We also check KG-STUDIO-TOKEN for backwards compatibility
		studioToken = ctx.GetHeader("KG-STUDIO-TOKEN")
		if studioToken == "" {
			return false, errors.New("studio token not provided")
		}
	}

	// Verify dev token first
	if studioToken == "KG-DEV:123456" {
		if !config.IsDev() {
			kglog.WarningCtx(ctx.Request.Context(), "KG-DEV token only allowed in local/dev environment")
			return false, errors.New("dev token not allowed in production")
		}

		devUID := ctx.GetHeader("KG-DEV-UID")
		if devUID == "" {
			return false, errors.New("dev uid not provided")
		}
		ctx.Set("uid", devUID)
		return true, nil
	}

	// Verify Studio token
	strategy := jwtservice.NewJwtStrategy(jwtservice.SecretTypeStudioV2)
	claimsI, _, err := strategy.Parse(studioToken)
	if err != nil {
		return false, err
	}

	claims := claimsI.(*jwtservice.StudioAccessTokenClaimsV2)
	userID := claims.Subject
	ctx.Set("uid", userID)
	return true, nil
}

// Context getter functions

// GetUID returns the uid from context
func GetUID(ctx *gin.Context) string {
	return ctx.GetString("uid")
}

// GetClientID returns the client id from context
func GetClientID(ctx *gin.Context) string {
	return ctx.GetString("client_id")
}

// GetGcpEmail returns the gcp email from context
func GetGcpEmail(ctx *gin.Context) string {
	return ctx.GetString("gcp_email")
}

// GetOrganizationIDByClientID returns the organization id from context by client id.
func GetOrganizationIDByClientID(c *gin.Context) (int, *code.KGError) {
	clientID := c.GetString("client_id")
	if clientID == "" {
		return 0, code.NewKGError(code.ClientForbidden, http.StatusUnauthorized, errors.New("client_id is empty"), nil)
	}

	ctx := c.Request.Context()

	orgID, kgErr := application.GetApplicationOrgId(ctx, clientID)
	if kgErr != nil {
		return 0, kgErr
	}

	return orgID, nil
}

// GetDashboardOwnerAddress returns the dashboard owner address from context
func GetDashboardOwnerAddress(ctx *gin.Context) string {
	return ctx.GetString("dashboard:ownerAddress")
}

// AuthorizeByOIDCToken returns the auth middleware for oidc token
//
// For Google cloud task, the token is in the format "Bearer <token>"
func AuthorizeByOIDCToken() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// Bypass the auth middleware in local environment.
		if config.IsLocal() {
			ctx.Next()
			return
		}

		authHeader := ctx.GetHeader("Authorization")
		bearerPrefix := "Bearer "
		if authHeader == "" {
			response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.OIDCTokenNotProvided, "oidc token not provided", nil)
			return
		} else if len(authHeader) < len(bearerPrefix) || authHeader[:len(bearerPrefix)] != bearerPrefix {
			response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.OIDCTokenBadFormat, "oidc token bad format", nil)
			return
		}

		// The token is in the format "Bearer <token>", so we need to strip the "Bearer " prefix.
		token := authHeader[len(bearerPrefix):]

		audience := config.GetString("SELF_INTERNAL_HOST")
		payload, err := idtoken.Validate(ctx.Request.Context(), token, audience)
		if err != nil {
			response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.OIDCTokenInvalid, err.Error(), nil)
			return
		}

		// Additional validation can be done here using the payload.
		// For example, check the issuer and audience.
		if payload.Claims["iss"] != "https://accounts.google.com" {
			response.ErrorWithMsg(ctx, http.StatusUnauthorized, code.OIDCTokenInvalid, fmt.Sprintf("invalid issuer: %s", payload.Claims["iss"]), nil)
			return
		}

		ctx.Next()
	}
}

// createSignature creates an HMAC signature for request validation
func createSignature(method, url, body, timestamp, key string) string {
	message := method + url + body + timestamp
	hmac := hmac.New(sha256.New, []byte(key))
	hmac.Write([]byte(message))
	return hex.EncodeToString(hmac.Sum(nil))
}
