package middleware

import (
	"strings"

	"emperror.dev/errors"
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
)

// StudioPermissionRequired checks if the user has the permission to access the resource.
func StudioPermissionRequired(resource rbac.Resource, action rbac.Action) func(*gin.Context) {
	return func(c *gin.Context) {
		orgID := c.GetInt("org_id")
		if orgID == 0 {
			kglog.WarningCtx(c.Request.Context(), "org_id not found, wrong middleware order?")
			response.UnauthorizedWithMsg(c, code.StudioTokenInvalid, "org_id not found")
			return
		}

		uid := c.GetString("uid")
		if uid == "" {
			kglog.WarningCtx(c.Request.Context(), "uid not found, wrong middleware order?")
			response.UnauthorizedWithMsg(c, code.StudioTokenInvalid, "uid not found")
			return
		}

		studioRoles, kgError := organization.GetStudioRoleByUserFromCache(
			c.Request.Context(), orgID, uid)
		if kgError != nil {
			kglog.ErrorCtx(c.Request.Context(), errors.Wrapf(
				kgError.Error, "can't get studio roles from cache, org(%d) user(%s) want to %s %s", orgID, uid, action, resource))
			response.InternalServerError(c, code.RedisError)
			return
		}

		roles := make([]rbac.Role, 0, len(studioRoles))
		for _, studioRole := range studioRoles {
			roles = append(roles, rbac.Role(studioRole.String()))
		}

		if !rbac.RBACService.IsGranted(c.Request.Context(), rbac.IsGrantRequest{
			Roles:    roles,
			Resource: resource,
			Action:   action,
		}) {
			response.ForbiddenError(c, code.StudioPermissionDeny)
			return
		}

		c.Next()
	}
}

// StudioPermissionRequiredOneOf checks if the user has one of the permission to access the resource.
func StudioPermissionRequiredOneOf(grants []rbac.Grant) func(*gin.Context) {
	return func(c *gin.Context) {
		orgID := c.GetInt("org_id")
		if orgID == 0 {
			kglog.WarningCtx(c.Request.Context(), "org_id not found, wrong middleware order?")
			response.UnauthorizedWithMsg(c, code.StudioTokenInvalid, "org_id not found")
			return
		}

		uid := c.GetString("uid")
		if uid == "" {
			kglog.WarningCtx(c.Request.Context(), "uid not found, wrong middleware order?")
			response.UnauthorizedWithMsg(c, code.StudioTokenInvalid, "uid not found")
			return
		}

		studioRoles, kgError := organization.GetStudioRoleByUserFromCache(
			c.Request.Context(), orgID, uid)
		if kgError != nil {
			output := make([]string, len(grants))

			for i, grant := range grants {
				output[i] = grant.String()
			}

			kglog.ErrorCtx(c.Request.Context(), errors.Wrapf(
				kgError.Error, "can't get studio roles from cache, org(%d) user(%s) need one of %s",
				orgID, uid, strings.Join(output, ", ")))
			response.InternalServerError(c, code.RedisError)
			return
		}

		roles := make([]rbac.Role, 0, len(studioRoles))
		for _, studioRole := range studioRoles {
			roles = append(roles, rbac.Role(studioRole.String()))
		}

		for _, grant := range grants {
			if rbac.RBACService.IsGranted(c.Request.Context(), rbac.IsGrantRequest{
				Roles:    roles,
				Resource: grant.Resource,
				Action:   grant.Action,
			}) {
				c.Next()
				return
			}
		}

		response.ForbiddenError(c, code.StudioPermissionDeny)
	}
}
