package assetprice

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	coingeckoapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/coingecko-api"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

type responseType struct {
	Code int `json:"code"`
	Data map[string]struct {
		Price                 float64 `json:"price"`
		PriceChangePercent24h float64 `json:"price_change_percentage_24h"`
	} `json:"data"`
}

func TestAssetPrices(t *testing.T) {
	// setup db and gin server
	url := "/v1/asset_prices"
	rdb.Reset()

	// create testdata: asset price and price history
	if err := dbtest.CreateAssetPrices(rdb.Get()); err != nil {
		t.Errorf("err: %v", err)
	}
	if err := dbtest.CreateAssetPriceHistories(rdb.Get()); err != nil {
		t.Errorf("err: %v", err)
	}

	r := gin.Default()
	r.GET(url, AssetPrices)

	// test error case 1
	urlWithParam := url + "?assets=eth-eth&assets=123"
	req, _ := http.NewRequest("GET", urlWithParam, nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// test error case 2
	urlWithParam = url + "?assets=eth,eth"
	req, _ = http.NewRequest("GET", urlWithParam, nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusBadRequest, w.Code)

	// test non-existing asset
	urlWithParam = url + "?assets=abc-123"
	req, _ = http.NewRequest("GET", urlWithParam, nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var response responseType
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, 1, len(response.Data))
	assert.Contains(t, response.Data, "abc-123")
	assert.Equal(t, 0.0, response.Data["abc-123"].Price)
	assert.Equal(t, 0.0, response.Data["abc-123"].PriceChangePercent24h)

	// test existing 2 assets
	urlWithParam = url + "?assets=eth-******************************************&assets=ronin-ronin"
	req, _ = http.NewRequest("GET", urlWithParam, nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	response = responseType{}
	responseStr, _ = io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, 2, len(response.Data))
	// t.Logf("data: %v\n", response.Data)
	for _, key := range []string{"eth-******************************************", "ronin-ronin"} {
		assert.Contains(t, response.Data, key)
		assert.True(t, response.Data[key].Price > 0.0)
		assert.NotEqual(t, 0.0, response.Data[key].PriceChangePercent24h)
	}

	// test existing 1 assets with uppercase
	urlWithParam = url + "?assets=eth-******************************************"
	req, _ = http.NewRequest("GET", urlWithParam, nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	response = responseType{}
	responseStr, _ = io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, 1, len(response.Data))
	for _, key := range []string{"eth-******************************************"} {
		assert.Contains(t, response.Data, key)
		assert.True(t, response.Data[key].Price > 0.0)
		assert.NotEqual(t, 0.0, response.Data[key].PriceChangePercent24h)
	}
}

func TestAssetPriceInCoingeckoID(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	// setup db and gin server
	url := "/v1/asset_prices"
	rdb.Reset()
	coingeckoapi.InitDefault(domain.NewAllPassRateLimiter())

	r := gin.Default()
	r.GET(url, AssetPrices)

	// test non-existing asset
	urlWithParam := url + "?assets_cid=abc123"
	req, _ := http.NewRequest("GET", urlWithParam, nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	var response responseType
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, 0, len(response.Data))

	// test existing 2 assets
	urlWithParam = url + "?assets_cid=weth&assets_cid=revv"
	req, _ = http.NewRequest("GET", urlWithParam, nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)
	assert.Equal(t, http.StatusOK, w.Code)

	response = responseType{}
	responseStr, _ = io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, 2, len(response.Data))
	for _, key := range []string{"weth", "revv"} {
		assert.Contains(t, response.Data, key)
		assert.True(t, response.Data[key].Price > 0.0)
	}
}
