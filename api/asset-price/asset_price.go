package assetprice

import (
	"context"
	"errors"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/asset"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/coingecko"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

const (
	platformDelimiter = "|"
)

type assetPricesReq struct {
	Assets                    []string `form:"assets"`
	AssetsInCoingeckoID       []string `form:"assets_cid"`
	AssetsInCoingeckoPlatform []string `form:"assets_cp_addr"`
}

type assetPricesResp struct {
	Code int                    `json:"code"`
	Data map[string]*assetPrice `json:"data"`
}

type assetPrice struct {
	Price                 float64 `json:"price"`
	PriceChangePercent24h float64 `json:"price_change_percentage_24h,omitempty"`
}

func validateAssetPricesReq(params *assetPricesReq) error {
	numProvided := 0
	if len(params.Assets) != 0 {
		numProvided++
	}
	if len(params.AssetsInCoingeckoID) != 0 {
		numProvided++
	}
	if len(params.AssetsInCoingeckoPlatform) != 0 {
		numProvided++
	}

	if numProvided == 0 {
		return errors.New("one of assets, assets_cid, or assets_cp_addr should be provided")
	} else if numProvided > 1 {
		return errors.New("only one of assets, assets_cid, or assets_cp_addr should be provided")
	}
	return nil
}

// example: ["arb-0xff970a61a04b1ca14834a43f5de4533ebddb5cc8", "matic-0x7ceb23fd6bc0add59e62ac25578270cff1b9f619"]
func getPriceFromDB(ctxSpan context.Context, assets []string) (*assetPricesResp, *code.KGError) {
	tokens := []*rdb.TokenInfo{}
	resp := &assetPricesResp{}
	resp.Data = map[string]*assetPrice{}
	for _, asset := range assets {
		splitted := strings.Split(asset, "-")
		if len(splitted) != 2 || splitted[0] == "" || splitted[1] == "" {
			return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid asset: "+asset), nil)
		}
		tokens = append(tokens, &rdb.TokenInfo{
			ChainID:    splitted[0],
			AssetGroup: strings.ToLower(splitted[1]),
		})
	}
	// Get prices from cache/database
	prices, err := asset.PricesAndChangePercent(ctxSpan, tokens)
	if err != nil {
		return nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}
	for i := range assets {
		resp.Data[assets[i]] = &assetPrice{
			Price:                 prices[i].Price,
			PriceChangePercent24h: prices[i].ChangePercent,
		}
	}
	return resp, nil
}

// example: ["revv", "weth"]
func getPriceFromCoingeckoID(ctx context.Context, assets []string) (*assetPricesResp, *code.KGError) {
	resp := &assetPricesResp{}
	resp.Data = map[string]*assetPrice{}
	coingeckoPrices, err := coingecko.Get().QuotesInUSD(ctx, assets)
	if err != nil {
		return nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}
	for coingeckoID, price := range coingeckoPrices {
		result := assetPrice{
			Price: price,
		}
		resp.Data[coingeckoID] = &result
	}
	return resp, nil
}

// example: ["binance-smart-chain|******************************************", "ethereum|******************************************"]
func getPriceFromCoingeckoPlatform(ctx context.Context, assets []string) (*assetPricesResp, *code.KGError) {
	resp := &assetPricesResp{}
	resp.Data = map[string]*assetPrice{}

	coingeckoIDsToQuery := []string{}
	coingeckoIDToAsset := map[string]string{}

	assetsNeedQuery := []string{}
	for _, asset := range assets {
		splitted := strings.Split(asset, platformDelimiter)
		if len(splitted) != 2 || splitted[0] == "" || splitted[1] == "" {
			return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("invalid asset: "+asset), nil)
		}
		platform := splitted[0]
		address := splitted[1]
		coingeckoID := coingecko.ContractAddressToCoingeckoID(ctx, platform, address)
		if coingeckoID != "" {
			coingeckoIDsToQuery = append(coingeckoIDsToQuery, coingeckoID)
			coingeckoIDToAsset[coingeckoID] = asset
		} else {
			assetsNeedQuery = append(assetsNeedQuery, asset)
		}
	}

	coingeckoPrices, err := coingecko.Get().QuotesInUSD(ctx, coingeckoIDsToQuery)
	if err != nil {
		return nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}
	for coingeckoID, price := range coingeckoPrices {
		asset := coingeckoIDToAsset[coingeckoID]
		resp.Data[asset] = &assetPrice{
			Price: price,
		}
	}

	if len(assetsNeedQuery) > 0 {
		kglog.WarningWithDataCtx(ctx, "[getPriceFromCoingeckoPlatform] Some assets have no corresponding coingecko ID", map[string]interface{}{
			"assets": assetsNeedQuery,
			"len":    len(assetsNeedQuery),
		})

		for _, asset := range assetsNeedQuery {
			splitted := strings.Split(asset, platformDelimiter)
			platform := splitted[0]
			address := splitted[1]

			chain, ok := coingecko.GetChainByPlatform(platform)
			if !ok {
				kglog.ErrorfCtx(ctx, "[getPriceFromCoingeckoPlatform] Chain not supported: %s", platform)
				continue
			}

			price, err := coingecko.Get().GetTokenPriceByAddress(ctx, chain, address)
			if err == code.ErrQuoteNotFound {
				return nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, errors.New("quote not found for asset: "+asset), nil)
			} else if err != nil {
				return nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
			}
			resp.Data[asset] = &assetPrice{
				Price: price,
			}
		}
	}

	return resp, nil
}

// AssetPrices returns the prices of the given assets
func AssetPrices(ctx *gin.Context) {
	ctxSpan, span := tracing.Start(ctx.Request.Context(), "api.AssetPrices")
	defer span.End()

	params := &assetPricesReq{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// one and only one of the three should be provided
	err := validateAssetPricesReq(params)
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, err.Error())
		return
	}

	var (
		result  *assetPricesResp
		kgError *code.KGError
	)
	if len(params.Assets) != 0 {
		result, kgError = getPriceFromDB(ctxSpan, params.Assets)
	} else if len(params.AssetsInCoingeckoID) != 0 {
		result, kgError = getPriceFromCoingeckoID(ctxSpan, params.AssetsInCoingeckoID)
	} else if len(params.AssetsInCoingeckoPlatform) != 0 {
		result, kgError = getPriceFromCoingeckoPlatform(ctxSpan, params.AssetsInCoingeckoPlatform)
	}

	if kgError != nil {
		response.KGError(ctx, kgError)
		return
	}
	ctx.JSON(200, result)
}
