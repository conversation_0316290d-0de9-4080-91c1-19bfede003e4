package api

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/api/session"
	openseaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/opensea-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	d "github.com/kryptogo/kg-wallet-backend/pkg/service/dashboard"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/eth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type dashboardLoginReq struct {
	OwnerAddress string `json:"owner_address" binding:"required"`
	Signature    string `json:"signature" binding:"required"`
	Timestamp    int    `json:"timestamp" binding:"required"`
}

type dashboardLoginResp struct {
	UID   int32  `json:"uid"`
	Token string `json:"token"`
	Code  int    `json:"code"`
}

// DashboardLogin by owner address
func DashboardLogin(ctx *gin.Context) {
	req := dashboardLoginReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	devMode := false
	if ctx.GetHeader("KG-WALLET-TOKEN") == "KG-DEV:123456" {
		env := config.GetString("ENV")
		if env != "local" && env != "dev" {
			response.BadRequestWithMsg(ctx, code.NotAllowed, "for local/dev development only")
			return
		}
		devMode = true
	}

	if !devMode {
		timeDiff := time.Now().Unix() - int64(req.Timestamp)
		if timeDiff > 600 || timeDiff < -600 {
			response.BadRequestWithMsg(ctx, code.DashboardSignatureExpired, "signature expired")
			return
		}

		msg := strings.ToLower(req.OwnerAddress) + ":" + strconv.Itoa(req.Timestamp)
		valid := eth.VerifySignature(req.OwnerAddress, req.Signature, msg)
		if !valid {
			response.BadRequestWithMsg(ctx, code.DashboardSignatureVerifyFailed, "signature verify failed")
			return
		}
	}

	uid, err := rdb.UpdateDashboardUser(ctx.Request.Context(), req.OwnerAddress, ctx.ClientIP(), ctx.Request.UserAgent())
	if err != nil {
		response.BadRequestWithMsg(ctx, code.DBError, err.Error())
		return
	}

	token := session.SetSession(ctx.Request.Context(), uid, req.OwnerAddress, ctx.ClientIP())
	resp := dashboardLoginResp{
		UID:   uid,
		Token: token,
	}
	ctx.JSON(http.StatusOK, resp)
}

type createProjectParams struct {
	ChainID         string `json:"chain_id" binding:"required"`
	ContractAddress string `json:"contract_address" binding:"required"`
	CollectionSlug  string `json:"collection_slug" binding:"required"`
	IsOfficial      bool   `json:"is_official"`
}

type projectResp struct {
	ID              int32   `json:"id"`
	Name            *string `json:"name"`
	Description     *string `json:"description"`
	ImageURL        string  `json:"image_url"`
	ChainID         string  `json:"chain_id"`
	ContractAddress string  `json:"contract_address"`
	IsOfficial      bool    `json:"is_official"`
}

// CreateProject new a project
func CreateProject(ctx *gin.Context) {
	ctxSpan, span := tracing.Start(ctx.Request.Context(), "api.CreateProject")
	defer span.End()

	ownerAddress := auth.GetDashboardOwnerAddress(ctx)
	params := createProjectParams{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	userRole := "unofficial"

	// Verify the contract owner is the current user
	if params.IsOfficial {
		// operator whitelist
		OPWhiteList := strings.Split(config.GetString("DASHBOARD_WHITELIST"), ",")
		if util.ContainsInsensitive(OPWhiteList, ownerAddress) {
			userRole = "kryptogo"
		} else if eth.VerifyContractOwner(ctxSpan, params.ChainID, params.ContractAddress, ownerAddress) {
			userRole = "official"
		} else {
			response.InternalServerErrorWithMsg(ctx, code.ContractOwnerVerifyFailed, "The contract owner verify failed")
			return
		}
	}

	// Verify the contract address and collection slug is valid
	collection, err := openseaapi.Get().GetCollection(ctxSpan, params.CollectionSlug)
	if err != nil {
		response.BadRequestWithMsg(ctx, code.CollectionNotFound, err.Error())
		return
	}
	assetContracts := collection.Contracts
	validSlug := false
	for _, contract := range assetContracts {
		if strings.EqualFold(contract["address"].(string), params.ContractAddress) {
			validSlug = true
			break
		}
	}
	if !validSlug {
		response.BadRequestWithMsg(ctx, code.NotAllowed, "Wrong collection slug")
		return
	}
	data := &dbmodel.DashboardProject{
		ChainID:         params.ChainID,
		ContractAddress: params.ContractAddress,
		CollectionSlug:  params.CollectionSlug,
		Name:            &collection.Name,
		Description:     &collection.Description,
		ImageURL:        collection.ImageURL,
		LargeImageURL:   collection.LargeImageURL,
		OwnerAddress:    ownerAddress,
		UserRole:        userRole,
	}
	errCode, err := rdb.CreateProject(ctxSpan, data)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}
	err = rdb.TransferPrizeOwnership(ctxSpan, data.ID, ownerAddress)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	resp := projectResp{}
	_ = copier.Copy(&resp, data)
	resp.IsOfficial = rdb.OfficialRole[data.UserRole]
	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": resp,
	})
}

// Projects get all projects by owner address
func Projects(ctx *gin.Context) {
	params := &rdb.ProjectParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	params.OwnerAddress = auth.GetDashboardOwnerAddress(ctx)

	projects, paging, code, err := rdb.Projects(ctx.Request.Context(), params)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code, err.Error())
		return
	}
	resp := make([]projectResp, 0)
	_ = copier.Copy(&resp, projects)
	for i := range resp {
		resp[i].IsOfficial = rdb.OfficialRole[(*projects)[i].UserRole]
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code":   code,
		"data":   resp,
		"paging": paging,
	})
}

// ProjectByID get project by project id
func ProjectByID(ctx *gin.Context) {
	params := &rdb.ProjectParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	projectID, err := strconv.Atoi(ctx.Param("project_id"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "project_id incorrect")
		return
	}

	if projectID == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "project_id is empty")
		return
	}
	project, errCode, err := rdb.ProjectByID(ctx.Request.Context(), int32(projectID))
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	resp := projectResp{}
	_ = copier.Copy(&resp, project)
	resp.IsOfficial = rdb.OfficialRole[project.UserRole]

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": errCode,
		"data": resp,
	})
}

type updatePrizeReq struct {
	ID              int32   `json:"id"`
	Title           string  `json:"title"`
	ImageMimeType   string  `json:"image_mime_type"`
	ImageBase64     string  `json:"image_base64"`
	Detail          *string `json:"detail"`
	Limitation      *string `json:"limitation"`
	MerchantName    *string `json:"merchant_name"`
	MerchantContact *string `json:"merchant_contact"`
	Amount          int32   `json:"amount"`
	StartTimeInt    int64   `json:"start_time"`
	EndTimeInt      int64   `json:"end_time"`
	ChainID         string  `json:"chain_id"`
	ContractAddress string  `json:"contract_address"`
	ProjectID       int32   `json:"project_id"`
	OwnerAddress    string  `json:"owner_address"`
	DeleteImage     bool    `json:"delete_image"`
}

func (req *updatePrizeReq) StartTime() time.Time {
	if req.StartTimeInt == 0 {
		return time.Time{}
	}
	return time.Unix(req.StartTimeInt, 0)
}

func (req *updatePrizeReq) EndTime() time.Time {
	if req.EndTimeInt == 0 {
		return time.Time{}
	}
	return time.Unix(req.EndTimeInt, 0)
}

type prizeResp struct {
	ID              int32   `json:"id"`
	ProjectID       int32   `json:"project_id"`
	ProjectName     string  `json:"project_name"`
	Title           string  `json:"title"`
	ImageURL        string  `json:"image_url"`
	Detail          *string `json:"detail"`
	Limitation      *string `json:"limitation"`
	MerchantName    *string `json:"merchant_name"`
	MerchantContact *string `json:"merchant_contact"`
	Amount          int32   `json:"amount"`
	StartTimeInt    int64   `json:"start_time"`
	EndTimeInt      int64   `json:"end_time"`
	Status          int32   `json:"status"`
	PublishTimeInt  int64   `json:"publish_time"`
	OwnerAddress    string  `json:"owner_address"`
	UserRole        string  `json:"user_role"`
	IsPrizeOwner    bool    `json:"is_prize_owner"`
	IsOfficial      bool    `json:"is_official"`
}

type dashboardPrize struct {
	dbmodel.DashboardPrize
}

func (data *dashboardPrize) StartTimeInt() int64 {
	return data.StartTime.Unix()
}

func (data *dashboardPrize) EndTimeInt() int64 {
	return data.EndTime.Unix()
}

func (data *dashboardPrize) PublishTimeInt() int64 {
	if data.PublishTime == nil {
		return 0
	}
	return data.PublishTime.Unix()
}

func (data *dashboardPrize) Status() int32 {
	if data.PublishTime == nil {
		return 0
	}
	return 1
}

// AddPrize Add a prize
func AddPrize(ctx *gin.Context) {
	req := updatePrizeReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	projectID, err := strconv.Atoi(ctx.Param("project_id"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "project_id incorrect")
		return
	}

	project, errCode, err := rdb.ProjectByID(ctx.Request.Context(), int32(projectID))
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}
	// You can't add prize to a project that is not yours
	if !strings.EqualFold(project.OwnerAddress, auth.GetDashboardOwnerAddress(ctx)) {
		response.ForbiddenErrorWithMsg(ctx, code.NotAllowed, fmt.Errorf("not Allowed").Error())
		return
	}

	prize := &dbmodel.DashboardPrize{}
	if req.ImageBase64 != "" {
		storedFile, err := d.StoragePrizeImage(int32(projectID), req.ImageBase64, req.ImageMimeType)
		prize.ImageURL = &storedFile
		if err != nil {
			response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
			return
		}
	}

	req.ChainID = project.ChainID
	req.ContractAddress = project.ContractAddress
	_ = copier.Copy(&prize, &req)
	prize.ProjectID = int32(projectID)
	prize.ProjectName = project.Name
	prize.OwnerAddress = auth.GetDashboardOwnerAddress(ctx)
	prize.UserRole = project.UserRole
	errCode, err = rdb.AddPrize(ctx.Request.Context(), prize)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	resp := prizeResp{}
	_ = copier.Copy(&resp, &dashboardPrize{*prize})
	resp.IsOfficial = rdb.OfficialRole[project.UserRole]
	resp.IsPrizeOwner = true
	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": errCode,
		"data": resp,
	})
}

// UpdatePrize update it by prize id
func UpdatePrize(ctx *gin.Context) {
	params := &rdb.ProjectParams{}
	params.OwnerAddress = auth.GetDashboardOwnerAddress(ctx)

	req := updatePrizeReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	prizeID, err := strconv.Atoi(ctx.Param("prize_id"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "prize_id incorrect")
		return
	}
	req.ID = int32(prizeID)
	prize := &dbmodel.DashboardPrize{}
	_ = copier.Copy(&prize, &req)

	previousPrize, errCode, err := rdb.PrizeByID(ctx.Request.Context(), prize.ID)
	if err != nil {
		if errCode == code.DashboardPrizeNotFound {
			response.NotFoundWithMsg(ctx, code.DashboardPrizeNotFound, "Prize Not Found")
			return
		}
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}
	if !strings.EqualFold(previousPrize.OwnerAddress, auth.GetDashboardOwnerAddress(ctx)) {
		response.ForbiddenErrorWithMsg(ctx, code.NotAllowed, fmt.Errorf("not Allowed").Error())
		return
	}

	if req.ImageBase64 != "" {
		storedFile, err := d.StoragePrizeImage(params.ProjectID, req.ImageBase64, req.ImageMimeType)
		prize.ImageURL = &storedFile
		if err != nil {
			response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
			return
		}
	}
	if req.DeleteImage {
		prize.ImageURL = nil
		err := rdb.DeletePrizeImageUrl(ctx.Request.Context(), prize.ID)
		if err != nil {
			response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
			return
		}
	}

	prize.OwnerAddress = params.OwnerAddress
	err = rdb.UpdatePrize(ctx.Request.Context(), prize)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}
	// re-publish
	if prize.PublishTime != nil {
		errCode, err := db.SavePrizeData(prize)
		if err != nil {
			response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
			return
		}
		*prize.PublishTime = time.Now()
		err = rdb.UpdatePrizePublishTime(ctx.Request.Context(), prize.ID, *prize.PublishTime)
		if err != nil {
			response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
			return
		}
	}

	resp := &prizeResp{}
	_ = copier.Copy(&resp, &dashboardPrize{*prize})
	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": resp,
	})
}

// Prizes get pizes by project id, and return all prizes with the same contract address
func Prizes(ctx *gin.Context) {
	paging := &rdb.PagingParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(paging)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	projectID, err := strconv.Atoi(ctx.Param("project_id"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "project_id incorrect")
		return
	}
	if projectID == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "project_id is empty")
		return
	}
	project, errCode, err := rdb.ProjectByID(ctx.Request.Context(), int32(projectID))
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	// Get all prizes with the same contract address
	params := &rdb.PrizeParams{
		ChainID:         project.ChainID,
		ContractAddress: project.ContractAddress,
		PagingParams:    *paging,
	}
	prizes, paging, errCode, err := rdb.Prizes(ctx.Request.Context(), params)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}
	resp := make([]prizeResp, 0)
	dp := make([]dashboardPrize, 0)
	_ = copier.Copy(&dp, prizes)
	_ = copier.Copy(&resp, dp)
	for i, p := range resp {
		resp[i].IsPrizeOwner = strings.EqualFold(p.OwnerAddress, auth.GetDashboardOwnerAddress(ctx))
		resp[i].IsOfficial = rdb.OfficialRole[p.UserRole]
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code":   errCode,
		"data":   resp,
		"paging": paging,
	})
}

// PrizeByID get pizes by prize id
func PrizeByID(ctx *gin.Context) {
	prizeID, err := strconv.Atoi(ctx.Param("prize_id"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "prize_id incorrect")
		return
	}

	prize, errCode, err := rdb.PrizeByID(ctx.Request.Context(), int32(prizeID))
	if err != nil {
		if errCode == code.DashboardPrizeNotFound {
			response.NotFoundWithMsg(ctx, code.DashboardPrizeNotFound, "Prize Not Found")
			return
		}
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	resp := &prizeResp{}
	dp := &dashboardPrize{}
	_ = copier.Copy(&dp, prize)
	_ = copier.Copy(&resp, dp)
	resp.IsPrizeOwner = strings.EqualFold(prize.OwnerAddress, auth.GetDashboardOwnerAddress(ctx))
	resp.IsOfficial = rdb.OfficialRole[prize.UserRole]
	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": errCode,
		"data": resp,
	})
}

// DeletePrize delete prize by prize id
func DeletePrize(ctx *gin.Context) {
	prizeID, err := strconv.Atoi(ctx.Param("prize_id"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "prize_id incorrect")
		return
	}

	prize, errCode, err := rdb.PrizeByID(ctx.Request.Context(), int32(prizeID))
	if err != nil {
		if errCode == code.DashboardPrizeNotFound {
			response.NotFoundWithMsg(ctx, code.DashboardPrizeNotFound, "Prize Not Found")
			return
		}
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}
	if prize.OwnerAddress != auth.GetDashboardOwnerAddress(ctx) {
		response.ForbiddenErrorWithMsg(ctx, code.NotAllowed, fmt.Errorf("not Allowed").Error())
		return
	}

	if prize.PublishTime != nil {
		response.BadRequestWithMsg(ctx, code.NotAllowed, fmt.Errorf("published prize cannot be deleted").Error())
		return
	}

	err = rdb.DeletePrize(ctx.Request.Context(), auth.GetDashboardOwnerAddress(ctx), int32(prizeID))
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"code": 0})
}

// RedeemSummary get redeem summary by prize it
func RedeemSummary(ctx *gin.Context) {
	prizeID, err := strconv.Atoi(ctx.Param("prize_id"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "prize_id incorrect")
		return
	}
	if prizeID == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "prize_id is empty")
		return
	}

	countTotal, countLastMonth, countThisMonth, err := rdb.RedeemSummary(ctx.Request.Context(), prizeID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": map[string]interface{}{
			"total":      countTotal,
			"last_month": countLastMonth,
			"this_month": countThisMonth,
		},
	})
}

// RedeemDetail download the redeem detail as a csv file
func RedeemDetail(ctx *gin.Context) {
	prizeID, err := strconv.Atoi(ctx.Param("prize_id"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "prize_id incorrect")
		return
	}
	if prizeID == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "prize_id is empty")
		return
	}

	fileName := fmt.Sprintf("RedeemedList_%s.csv", time.Now().Format("202204111526"))
	detail, err := rdb.RedeemDetail(ctx.Request.Context(), prizeID)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	header := []string{"Token_id", "Quantity", "Redeemed_time"}
	content := make([][]string, len(*detail))
	for idx, row := range *detail {
		content[idx] = []string{
			row.TokenID,
			strconv.Itoa(int(row.Quantity)),
			row.RedeemedTime.String()}
	}
	util.DownloadCSVFile(ctx, fileName, header, content)
}

// PublishPrize save prize data to firestore
func PublishPrize(ctx *gin.Context) {
	prizeID, err := strconv.Atoi(ctx.Param("prize_id"))
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "prize_id incorrect")
		return
	}
	if prizeID == 0 {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "prize_id is empty")
		return
	}

	prize, errCode, err := rdb.PrizeByID(ctx.Request.Context(), int32(prizeID))
	if err != nil {
		if errCode == code.DashboardPrizeNotFound {
			response.NotFoundWithMsg(ctx, code.DashboardPrizeNotFound, "Prize Not Found")
			return
		}
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}
	if prize.OwnerAddress != auth.GetDashboardOwnerAddress(ctx) {
		response.ForbiddenErrorWithMsg(ctx, code.NotAllowed, fmt.Errorf("not Allowed").Error())
		return
	}

	// TODO: Remove this
	errCode, err = db.SavePrizeData(prize)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	// add prize->token table
	err = rdb.AddPrizeToken(ctx.Request.Context(), prize.ID, "all", prize.ChainID, prize.ContractAddress)
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	err = rdb.UpdatePrizePublishTime(ctx.Request.Context(), int32(prizeID), time.Now())
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, code.DBError, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": errCode,
	})
}

// TBD remove it
func TBD(ctx *gin.Context) {
	ctx.String(http.StatusOK, "Not Implemented Yet")
}
