package api

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/common"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/nft"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	lo "github.com/samber/lo"
)

type assetsV2Req struct {
	OtherUserID           string              `form:"uid"`
	AssetTypeIDs          []string            `form:"types"`
	ChainIDs              []string            `form:"chain_ids"`
	Path                  string              `form:"path"`
	IncludeUnverified     bool                `form:"include_unverified"`
	IncludePriceHistories bool                `form:"include_price_histories"`
	Query                 string              `form:"q"`
	IncludeTokenIDs       map[string][]string `form:"include"`
	ExcludeTokenIDs       map[string][]string `form:"exclude"`
	ExcludeObserver       bool                `form:"exclude_observer"`
	common.PagingParams

	// parsed fields
	Chains        []domain.Chain            `form:"-"`
	AssetTypes    []domain.AssetType        `form:"-"`
	IncludeTokens map[domain.Chain][]string `form:"-"`
	ExcludeTokens map[domain.Chain][]string `form:"-"`
}

func parseChainTokensMap(chainTokensMap map[string][]string) (map[domain.Chain][]string, error) {
	supportedChains := lo.Map(domain.Chains, func(chain domain.Chain, _ int) string {
		return chain.ID()
	})
	result := make(map[domain.Chain][]string)
	for chainID, tokenIDs := range chainTokensMap {
		if !lo.Contains(supportedChains, chainID) {
			return nil, fmt.Errorf("invalid chain_id '%s'", chainID)
		}
		chain := domain.IDToChain(chainID)
		result[chain] = tokenIDs
	}
	return result, nil
}

func (r *assetsV2Req) AfterBinding(c *gin.Context) error {
	var err error

	// parse and validate chains, asset types
	if r.Chains, err = ParseChainIDs(r.ChainIDs); err != nil {
		return err
	}
	if r.AssetTypes, err = ParseAssetTypes(r.AssetTypeIDs); err != nil {
		return err
	}

	// parse and validate include/exclude tokens
	if r.IncludeTokens, err = parseChainTokensMap(r.IncludeTokenIDs); err != nil {
		return err
	}
	if r.ExcludeTokens, err = parseChainTokensMap(r.ExcludeTokenIDs); err != nil {
		return err
	}

	// validate paging params
	if r.PageNumber < 0 || r.PageSize < 0 {
		return errors.New("invalid paging params")
	}
	if r.PageNumber == 0 {
		r.PageNumber = 1
	}
	if r.PageSize == 0 {
		r.PageSize = 10
	}
	return nil
}

// AssetsV2 returns user assets
func AssetsV2(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.AssetsV2")
	defer span.End()

	req := &assetsV2Req{}
	kgErr := util.ToGinContextExt(c).BindQuery(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// parse uid to query
	queryForOther, uidToQuery := false, auth.GetUID(c)
	if req.OtherUserID != "" {
		queryForOther = true
		uidToQuery = req.OtherUserID
	}

	// get addresses to query
	addressesToQuery := make(map[domain.Chain][]domain.Address)
	if queryForOther {
		// only can query default addresses for other users
		addresses, err := user.GetDefaultAddresses(ctx, uidToQuery)
		if err != nil {
			response.KGError(c, err)
			return
		}
		for _, chain := range req.Chains {
			if address, ok := addresses[chain]; ok && address != nil {
				addressesToQuery[chain] = append(addressesToQuery[chain], address)
			}
		}
	} else {
		var kgErr *code.KGError
		addressesToQuery, kgErr = user.GetWalletAddresses(ctx, uidToQuery, req.Path, req.Chains, req.ExcludeObserver)
		if kgErr != nil {
			response.KGError(c, kgErr)
			return
		}
	}

	// get paging params
	assets, kgErr := asset.List(ctx, &asset.ListParam{
		Addresses:             addressesToQuery,
		Types:                 req.AssetTypes,
		IncludeUnverified:     req.IncludeUnverified,
		IncludePriceHistories: req.IncludePriceHistories,
		Query:                 req.Query,
		IncludeTokens:         req.IncludeTokens,
		ExcludeTokens:         req.ExcludeTokens,
		PageNumber:            req.PageNumber,
		PageSize:              req.PageSize,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, assets)
}

type singleAssetV2Req struct {
	ChainID         string `form:"chain_id"`
	AssetID         string `form:"asset_group"`
	AssetTypeID     string `form:"type"`
	ExcludeObserver bool   `form:"exclude_observer"`

	// parsed fields
	Chain     domain.Chain     `form:"-"`
	AssetType domain.AssetType `form:"-"`
}

func (r *singleAssetV2Req) AfterBinding(c *gin.Context) error {
	chain := lo.FindOrElse(domain.Chains, nil, func(chain domain.Chain) bool {
		return chain.ID() == r.ChainID
	})
	if chain == nil {
		return errors.New("invalid chain_id")
	}
	r.Chain = chain

	if assetType, err := domain.ParseAssetType(r.AssetTypeID); err != nil {
		return errors.New("invalid type")
	} else {
		r.AssetType = assetType
	}
	return nil
}

func SingleAssetV2(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.SingleAssetV2")
	defer span.End()

	req := &singleAssetV2Req{}
	kgErr := util.ToGinContextExt(c).BindQuery(req)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	uid := auth.GetUID(c)
	addresses, kgErr := user.GetWalletAddresses(ctx, uid, "", []domain.Chain{req.Chain}, req.ExcludeObserver)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	uniqAddresses := lo.Uniq(lo.Flatten(lo.Values(addresses)))
	asset, kgErr := asset.GetSingle(ctx, req.Chain, req.AssetType, req.AssetID, uniqAddresses)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, asset)
}

func UpdateAssetsV2(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.UpdateAssetsV2")
	defer span.End()

	uid := auth.GetUID(c)

	// get all addresses to update, including observer
	addresses, kgErr := user.GetWalletAddresses(ctx, uid, user.WalletPathAll, asset.SupportedChains, false)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	addressesToUpdate := make(map[domain.Address][]domain.Chain)
	for chain := range addresses {
		for _, address := range addresses[chain] {
			addressesToUpdate[address] = append(addressesToUpdate[address], chain)
		}
	}

	chainAddresses := lo.FlatMap(lo.Entries(addressesToUpdate), func(entry lo.Entry[domain.Address, []domain.Chain], _ int) []domain.ChainAddress {
		address, chains := entry.Key, entry.Value
		return lo.Map(chains, func(chain domain.Chain, _ int) domain.ChainAddress {
			return domain.ChainAddress{
				Chain:   chain,
				Address: address,
			}
		})
	})
	chainAddresses = lo.Uniq(chainAddresses)
	err := asset.AddUpdateAssetPricesJob(ctx, chainAddresses)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to add update asset prices job", map[string]any{"error": err.Error()})
	}

	tracing.AsyncOp(ctx, "asset.Update", func(ctx context.Context) {
		// Split addresses into Solana and non-Solana
		solanaAddressesToUpdate := make(map[domain.Address][]domain.Chain)
		otherAddressesToUpdate := make(map[domain.Address][]domain.Chain)
		for addr, chains := range addressesToUpdate {
			if lo.ContainsBy(chains, func(chain domain.Chain) bool { return chain.ID() == domain.Solana.ID() }) {
				solanaAddressesToUpdate[addr] = chains
			} else {
				otherAddressesToUpdate[addr] = chains
			}
		}

		// run solana and other chains in parallel for better performance on updating solana assets
		var wg sync.WaitGroup
		var solanaKgErr, nonSolanaKgErr *code.KGError
		wg.Add(2)
		go func() {
			defer wg.Done()
			solanaKgErr = asset.Update(ctx, solanaAddressesToUpdate, asset.SupportedTypes)
		}()
		go func() {
			defer wg.Done()
			nonSolanaKgErr = asset.Update(ctx, otherAddressesToUpdate, asset.SupportedTypes)
		}()
		wg.Wait()

		// Check for errors from either update
		if solanaKgErr != nil {
			kglog.ErrorfCtx(ctx, "Failed to update solana assets: %s", solanaKgErr.String())
		}
		if nonSolanaKgErr != nil {
			kglog.ErrorfCtx(ctx, "Failed to update other assets: %s", nonSolanaKgErr.String())
		}

		// because new asset service hasn't handled NFT, here is a workaround to call legacy service
		updateNFTAssets(ctx, addressesToUpdate)
	})

	response.OK(c, nil)
}

func updateNFTAssets(ctx context.Context, addressesToUpdate map[domain.Address][]domain.Chain) {
	wg := sync.WaitGroup{}
	addresses := []string{}
	startSyncedAt := time.Now()
	expired := startSyncedAt.Add(-time.Second * 3)
	for addr := range addressesToUpdate {
		if evmAddr, ok := addr.(domain.EvmAddress); ok {
			evmAddrStr := evmAddr.String()
			addresses = append(addresses, evmAddrStr)
			wg.Add(1)
			go func() {
				defer wg.Done()
				kglog.DebugfCtx(ctx, "[start] update nft assets by address: %s", evmAddrStr)
				nft.UpdateNftAssetsByAddress(ctx, evmAddrStr, time.Hour*24, nft.UpdatePriorityHigh, []string{"eth", "matic", "oasys", "ronin"})
				kglog.DebugfCtx(ctx, "[end] update nft assets by address: %s", evmAddrStr)
			}()
		}
	}
	wg.Wait()
	err := nft.SyncNftAssets(ctx, addresses, []string{"eth", "matic", "oasys", "ronin"}, []string{"nft"}, expired)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "[UpdateAssets] nft.SyncNftAssets error", map[string]interface{}{"addresses": addresses, "err": err.Error()})
	}
	kglog.InfoWithDataCtx(ctx, "[UpdateAssets] nft done!", map[string]interface{}{"addresses": addresses})
}

// GetPinnedTokens get pinned tokens by org
func GetPinnedTokens(c *gin.Context) {
	ctx := c.Request.Context()
	params := struct {
		ChainIDs []string `form:"chain_ids"`
	}{}

	if kgErr := util.ToGinContextExt(c).BindQuery(&params); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	chainIDs, err := ParseChainIDs(params.ChainIDs)
	if err != nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid chain ids")
		return
	}

	// only in ocean wallet
	if !application.IsTongBao(ctx, c.GetString("client_id")) {
		response.OK(c, []asset.Asset{})
		return
	}

	mIncludedTokens := map[domain.Chain][]string{
		domain.Tron: {"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"},
	}

	chainIDs = lo.Filter(chainIDs, func(chain domain.Chain, _ int) bool {
		_, ok := mIncludedTokens[chain]
		return ok
	})

	addressesToQuery, kgErr := user.GetWalletAddresses(ctx, auth.GetUID(c), "", chainIDs, false)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// get paging params
	assets, kgErr := asset.List(ctx, &asset.ListParam{
		Addresses:             addressesToQuery,
		Types:                 []domain.AssetType{domain.AssetTypeToken},
		IncludeUnverified:     false,
		IncludePriceHistories: true,
		Query:                 "",
		IncludeTokens:         mIncludedTokens,
		ExcludeTokens:         nil,
		PageNumber:            1,
		PageSize:              100,
	})
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp := make([]asset.Asset, 0, len(assets.Assets)+len(assets.MainTokens))
	for _, asset := range assets.Assets {
		resp = append(resp, *asset)
	}
	for _, asset := range assets.MainTokens {
		resp = append(resp, *asset)
	}

	response.OK(c, resp)
}
