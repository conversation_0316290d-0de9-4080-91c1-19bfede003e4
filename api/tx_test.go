package api

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestUpdateTxListEndpoint(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	repo := rdb.GormRepo()
	oauth.Init(repo)
	application.Init(repo)
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())
	users, uids := dbtest.Users()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	mockTxService := tx.NewMockIService(ctrl)
	tx.Set(mockTxService)

	url := "/v1/txlists/update"
	server := gin.Default()
	server.POST(url, auth.MockAuthorize(uids[0]), UpdateTxList)

	{ // Normal case
		requestBody := map[string]interface{}{}
		body, err := json.Marshal(requestBody)
		assert.Nil(t, err)

		mockTxService.EXPECT().UpdateTxByUIDAndWait(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, uid string, excludeObserver bool) {
				assert.Equal(t, uids[0], uid)
				assert.False(t, excludeObserver)
			},
		)

		req, _ := http.NewRequest("POST", url, bytes.NewBuffer(body))
		w := httptest.NewRecorder()
		server.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
	}
}
