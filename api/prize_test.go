package api

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/assert"
)

func TestPrizesByUser(t *testing.T) {
	rdb.Reset()
	users, userIDs := dbtest.Users()
	uid := userIDs[0]
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	assert.Nil(t, rdbtest.CreateNftAsset(rdb.Get()))
	assert.Nil(t, rdbtest.CreateDashboardProjects(rdb.Get()))
	assert.Nil(t, rdbtest.CreateDashboardPrizes(rdb.Get()))
	assert.Nil(t, rdbtest.CreateDashboardPrizeTokens(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	prizesUrl := "/v1/prizes"

	server := gin.Default()
	server.GET(prizesUrl, auth.MockAuthorize(uid), PrizesByUser)

	prizesUrlWithParam := prizesUrl + "?type=ACTIVE"
	req, err := http.NewRequest("GET", prizesUrlWithParam, nil)
	assert.Nil(t, err)
	w := httptest.NewRecorder()
	server.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var response prizesByUserResp
	responseStr, _ := io.ReadAll(w.Body)
	assert.Nil(t, json.Unmarshal(responseStr, &response))
	fmt.Println("response", response)
	assert.Equal(t, 0, response.Code)
	assert.Equal(t, 2, len(*response.Data))
}
