package api

import (
	"io"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
)

// MockSignedUrl .
func MockSignedUrl(ctx *gin.Context) {
	objectPath := ctx.Param("object_path")
	data, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, err.Error())
		return
	}

	err = db.GoogleStorageServiceObj.UploadObjectAsPublic(data, objectPath)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "upload object to google storage failed", map[string]interface{}{
			"object_path": objectPath,
			"error":       err.<PERSON><PERSON>r(),
		})
		response.InternalServerErrorWithMsg(ctx, code.InternalError, err.Error())
		return
	}

	ctx.JSON(200, map[string]interface{}{
		"code": 0,
	})
}
