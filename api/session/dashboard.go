package session

import (
	"context"
	"crypto/sha1"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
)

var sessionPrefix = "dashboard:sess:"
var sessionExpires = time.Hour * 24 * 30

// DashboardSession session data
type DashboardSession struct {
	UID          int32  `json:"uid"`
	OwnerAddress string `json:"owner_address"`
	ClientIP     string `json:"client_ip"`
}

// SetSession generate token and set session to redis
func SetSession(ctx context.Context, uid int32, ownerAddress, clientIP string) string {
	h := sha1.New()
	h.Write([]byte(strconv.Itoa(int(uid)) + ownerAddress + clientIP + time.Now().String()))
	token := fmt.Sprintf("%x", h.Sum(nil))

	key := sessionPrefix + token
	data := DashboardSession{
		UID:          uid,
		OwnerAddress: ownerAddress,
		ClientIP:     clientIP,
	}
	dataStr, _ := json.Marshal(data)
	cache.Set(ctx, key, string(dataStr), sessionExpires)
	return token
}

// UpdateSession update session data
func UpdateSession(ctx context.Context, token string, data *DashboardSession) {
	dataStr, _ := json.Marshal(data)
	cache.Set(ctx, token, string(dataStr), redis.KeepTTL)
}

// GetSession get session from redis
func GetSession(token string) *DashboardSession {
	key := sessionPrefix + token
	dataStr := cache.String(key)
	if dataStr == "" {
		return nil
	}
	data := new(DashboardSession)
	err := json.Unmarshal([]byte(dataStr), data)
	if err != nil {
		return nil
	}
	return data
}
