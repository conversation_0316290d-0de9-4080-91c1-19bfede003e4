package api

import (
	"context"
	"fmt"
	"net/http"

	"github.com/kryptogo/kg-wallet-backend/domain"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/asset"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	userservice "github.com/kryptogo/kg-wallet-backend/service/user"
	lo "github.com/samber/lo"
)

type assetsReq struct {
	UID string `form:"uid"`
	rdb.AssetParams
}
type assetsResp struct {
	Code   int                      `json:"code"`
	Data   map[string][]*rdb.VAsset `json:"data"`
	Paging *rdb.PagingParams        `json:"paging"`
}

// Assets returns user assets
func Assets(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.Assets")
	defer span.End()

	resp := new(assetsResp)
	params := &assetsReq{}
	kgErr := util.ToGinContextExt(c).BindQuery(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	uid := auth.GetUID(c)

	// view other user's assets
	watchingUID := ""
	if params.UID != "" {
		watchingUID = params.UID
	} else {
		params.UID = uid
	}

	// get default receive address if uid is specified
	if len(params.ChainIDs) == 0 {
		params.ChainIDs = dbmodel.DefaultChainOrder
	}
	walletAddresses := map[domain.Chain][]domain.Address{}
	if watchingUID != "" {
		params.AssetTypes = dbmodel.AllAssetTypes
		receiveAddresses, kgErr := userservice.GetDefaultAddresses(ctx, params.UID)
		if kgErr != nil {
			response.KGError(c, kgErr)
			return
		}
		for chain, addr := range receiveAddresses {
			if !chain.IsTestnet() {
				walletAddresses[chain] = append(walletAddresses[chain], addr)
			}
		}
	} else {
		var err *code.KGError
		walletAddresses, err = userservice.AddressesByChains(ctx, params.UID, params.Path, params.ChainIDs, params.ExcludeObserver)
		if err != nil {
			response.KGError(c, err)
			return
		}
	}

	for _, assetType := range params.AssetTypes {
		if _, ok := dbmodel.AssetTypesSet[assetType]; !ok {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid type")
			return
		}
	}
	for _, chainID := range params.ChainIDs {
		if _, ok := dbmodel.ChainIDSet[chainID]; !ok {
			response.BadRequestWithMsg(c, code.ParamIncorrect, "invalid chain_id")
			return
		}
	}

	// convert to legacy format
	var allAddresses, ethAddresses, solAddresses, btcAddresses, tronAddresses []string
	for chain, addresses := range walletAddresses {
		for _, addr := range addresses {
			allAddresses = append(allAddresses, addr.String())
			if chain.IsEVM() {
				ethAddresses = append(ethAddresses, addr.String())
			} else if chain.IsTVM() {
				tronAddresses = append(tronAddresses, addr.String())
			} else if chain == domain.Bitcoin {
				btcAddresses = append(btcAddresses, addr.String())
			} else if chain == domain.Solana {
				solAddresses = append(solAddresses, addr.String())
			}
		}
	}
	allAddresses, ethAddresses, tronAddresses, btcAddresses, solAddresses = lo.Uniq(allAddresses), lo.Uniq(ethAddresses), lo.Uniq(tronAddresses), lo.Uniq(btcAddresses), lo.Uniq(solAddresses)

	params.WalletAddresses = allAddresses
	if params.PageNumber == 0 {
		params.PageNumber = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	getAssetParams := &asset.GetAssetsParams{
		UID:           params.UID,
		AssetParams:   params.AssetParams,
		EthAddresses:  ethAddresses,
		SolAddresses:  solAddresses,
		BtcAddresses:  btcAddresses,
		TronAddresses: tronAddresses,
		AllAddresses:  allAddresses,
		ClientID:      auth.GetClientID(c),
	}
	assetsData, kgErr := asset.Get().GetAssets(ctx, getAssetParams)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp.Code = 0
	resp.Data = map[string][]*rdb.VAsset{
		"assets":      assetsData.Assets,
		"main_tokens": assetsData.MainTokens,
	}
	params.PagingParams.TotalCount = int64(assetsData.TotalCount)
	resp.Paging = &params.PagingParams
	c.JSON(http.StatusOK, resp)
}

type updateAssetsParams struct {
	ForceUpdate     bool     `json:"force_update"`
	ChainIDs        []string `json:"chain_ids"`
	AssetTypes      []string `json:"asset_types"`
	Path            string   `json:"path"`
	ExcludeObserver bool     `json:"exclude_observer"`
	UID             string
}

func updateAssets(ctx context.Context, params *updateAssetsParams) *code.KGError {
	for _, assetType := range params.AssetTypes {
		if _, ok := dbmodel.AssetTypesSet[assetType]; !ok {
			return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("invalid type"), nil)
		}
	}
	for _, chainID := range params.ChainIDs {
		if _, ok := dbmodel.ChainIDSet[chainID]; !ok {
			return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("invalid chain_id"), nil)
		}
	}

	user, _ := userservice.GetByUID(ctx, params.UID, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if user == nil {
		kglog.DebugfCtx(ctx, "user does not exist: %s", params.UID)
		return code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("user does not exist"), nil)
	}

	var walletAddresses, ethAddresses, solAddresses, btcAddresses, tronAddresses []string
	allAddresses := user.AddressesByChains(params.Path, params.ChainIDs, params.ExcludeObserver)
	for chainID, addresses := range allAddresses {
		chain := domain.IDToChain(chainID)
		if chain.IsEVM() {
			ethAddresses = append(ethAddresses, addresses...)
		} else if chain.IsTVM() {
			tronAddresses = append(tronAddresses, addresses...)
		} else if chain == domain.Bitcoin {
			btcAddresses = append(btcAddresses, addresses...)
		} else if chain == domain.Solana {
			solAddresses = append(solAddresses, addresses...)
		}
		walletAddresses = append(walletAddresses, addresses...)
	}
	walletAddresses, ethAddresses, solAddresses, btcAddresses, tronAddresses = lo.Uniq(walletAddresses), lo.Uniq(ethAddresses), lo.Uniq(solAddresses), lo.Uniq(btcAddresses), lo.Uniq(tronAddresses)

	_, mainTokenKey, kgErr := asset.ComposeAssetsCacheFieldKey(params.UID, &rdb.AssetParams{
		ChainIDs:        params.ChainIDs,
		AssetTypes:      params.AssetTypes,
		Path:            params.Path,
		ExcludeObserver: params.ExcludeObserver,
		WalletAddresses: walletAddresses,
	})
	if kgErr != nil {
		return kgErr
	}

	// force update assets
	assetHKey := cache.ComposeAssetHKey(params.UID)
	asset.Get().TryToUpdateAssetsAndWait(ctx, &asset.UpdateAssetsParam{
		ForceUpdate:    params.ForceUpdate,
		AssetKey:       assetHKey,
		MainTokenKey:   mainTokenKey,
		EthAddresses:   ethAddresses,
		SolAddresses:   solAddresses,
		BtcAddresses:   btcAddresses,
		TronAddresses:  tronAddresses,
		AllAddresses:   walletAddresses,
		SyncAssetTypes: params.AssetTypes,
		SyncChainIDs:   params.ChainIDs,
	})

	return nil
}

// UpdateAssets triggers assets update
func UpdateAssets(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.UpdateAssets")
	defer span.End()

	params := updateAssetsParams{}
	kgErr := util.ToGinContextExt(c).BindRawBody(&params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	params.UID = auth.GetUID(c)
	kgErr = updateAssets(ctx, &params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	c.JSON(http.StatusOK, gin.H{"code": 0})
}

type syncAssetsReq struct {
	UID        string           `json:"uid"`
	BeforeData *domain.UserData `json:"beforeData"`
	AfterData  *domain.UserData `json:"afterData"`
}

// SyncAssetsByAddresses syncs assets by addresses
func SyncAssetsByAddresses(ctx *gin.Context) {
	req := syncAssetsReq{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	walletAddresses := make([]string, 0)
	ethAddresses := make([]string, 0)
	solAddresses := make([]string, 0)
	btcAddresses := make([]string, 0)
	tronAddresses := make([]string, 0)

	chains := []string{
		dbmodel.ChainIDBitcoin,
		dbmodel.ChainIDEthereum,
		dbmodel.ChainIDTron,
		dbmodel.ChainIDSolana,
	}

	beforeAddresses := make(map[string][]string)
	if req.BeforeData != nil {
		beforeAddresses = req.BeforeData.AddressesByChains("", chains, false)
	}

	afterAddresses := make(map[string][]string)
	if req.AfterData != nil {
		afterAddresses = req.AfterData.AddressesByChains("", chains, false)
	}

	for _, chain := range chains {
		before := beforeAddresses[chain]
		after := afterAddresses[chain]
		diff, _ := lo.Difference(after, before)
		switch chain {
		case dbmodel.ChainIDBitcoin:
			btcAddresses = diff
			walletAddresses = append(walletAddresses, btcAddresses...)
		case dbmodel.ChainIDEthereum:
			ethAddresses = diff
			walletAddresses = append(walletAddresses, ethAddresses...)
		case dbmodel.ChainIDTron:
			tronAddresses = diff
			walletAddresses = append(walletAddresses, tronAddresses...)
		case dbmodel.ChainIDSolana:
			solAddresses = diff
			walletAddresses = append(walletAddresses, solAddresses...)
		}
	}

	asset.Get().UpdateAssets(ctx.Request.Context(), &asset.UpdateAssetsParam{
		ForceUpdate:    true,
		AssetKey:       "",
		MainTokenKey:   "",
		EthAddresses:   ethAddresses,
		SolAddresses:   solAddresses,
		BtcAddresses:   btcAddresses,
		TronAddresses:  tronAddresses,
		AllAddresses:   walletAddresses,
		SyncAssetTypes: dbmodel.AllAssetTypes,
		SyncChainIDs:   dbmodel.EVMChainIDs,
	})

	ctx.JSON(http.StatusOK, gin.H{"code": 0, "message": "ok"})
}

type singleAssetsResp struct {
	Code int         `json:"code"`
	Data *rdb.VAsset `json:"data"`
}

// SingleAsset returns single asset
func SingleAsset(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.SingleAsset")
	defer span.End()

	resp := new(singleAssetsResp)
	params := &rdb.SingleAssetParams{}

	kgErr := util.ToGinContextExt(c).BindQuery(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	uid := auth.GetUID(c)
	user, _ := userservice.GetByUID(ctx, uid, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	if user == nil {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "user does not exist")
		return
	}

	if params.AssetType != dbmodel.AssetTypeToken {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "type is incorrect")
		return
	}

	asset, kgErr := asset.Get().SingleAsset(ctx, user, params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	resp.Code = 0
	resp.Data = asset
	c.JSON(http.StatusOK, resp)
}
