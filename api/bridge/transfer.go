package bridge

import (
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/bridge"
)

// CreateBridgeTransfer creates a new bridge transfer
func CreateBridgeTransfer(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.bridge.CreateBridgeTransfer")
	defer span.End()

	var req domain.CreateBridgeTransferRequest
	if kgErr := util.ToGinContextExt(c).Bind<PERSON>son(&req); kgErr != nil {
		response.KGError(c, kgErr)
		return
	}

	// Get organization ID from context (set by middleware)
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "organization ID not found in context")
		return
	}
	req.OrganizationID = orgID

	// Validate payment rails and currencies
	if err := validateCreateBridgeTransferRequest(&req); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Invalid bridge transfer request", map[string]interface{}{
			"error": err.Error(),
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
		return
	}

	kglog.InfoWithDataCtx(ctx, "Creating bridge transfer via API", map[string]interface{}{
		"organization_id": req.OrganizationID,
		"amount": req.Amount,
		"source_payment_rail": req.Source.PaymentRail,
		"source_currency": req.Source.Currency,
		"source_from_address": req.Source.FromAddress,
		"dest_payment_rail": req.Destination.PaymentRail,
		"dest_currency": req.Destination.Currency,
		"dest_external_account_id": req.Destination.ExternalAccountID,
	})

	// Call service
	resp, kgErr := bridge.CreateBridgeTransfer(ctx, &req)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to create bridge transfer", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"amount": req.Amount,
			"error": kgErr.Error.Error(),
		})
		response.KGError(c, kgErr)
		return
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created bridge transfer via API", map[string]interface{}{
		"organization_id": req.OrganizationID,
		"transfer_id": resp.ID,
		"state": resp.State,
		"amount": resp.Amount,
		"currency": resp.Currency,
	})

	response.OK(c, resp)
}

// GetBridgeTransfers retrieves all bridge transfers for an organization
func GetBridgeTransfers(c *gin.Context) {
	ctx, span := tracing.Start(c.Request.Context(), "api.bridge.GetBridgeTransfers")
	defer span.End()

	// Get organization ID from context (set by middleware)
	orgID := c.GetInt("org_id")
	if orgID == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "organization ID not found in context")
		return
	}

	kglog.InfoWithDataCtx(ctx, "Getting bridge transfers via API", map[string]interface{}{
		"organization_id": orgID,
	})

	transfers, kgErr := bridge.GetBridgeTransfersByOrgID(ctx, orgID)
	if kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get bridge transfers", map[string]interface{}{
			"organization_id": orgID,
			"error": kgErr.Error.Error(),
		})
		response.KGError(c, kgErr)
		return
	}

	kglog.InfoWithDataCtx(ctx, "Successfully retrieved bridge transfers via API", map[string]interface{}{
		"organization_id": orgID,
		"count": len(transfers),
	})

	response.OK(c, map[string]interface{}{
		"transfers": transfers,
	})
}

// validateCreateBridgeTransferRequest validates the bridge transfer request
func validateCreateBridgeTransferRequest(req *domain.CreateBridgeTransferRequest) error {
	// Validate amount is not empty and is a valid number
	if req.Amount == "" {
		return errors.New("amount is required")
	}

	if _, err := strconv.ParseFloat(req.Amount, 64); err != nil {
		return errors.New("amount must be a valid number")
	}

	// Validate source
	if req.Source == nil {
		return errors.New("source is required")
	}

	// Validate source payment rail
	validSourceRails := map[string]bool{
		"arbitrum": true,
		"optimism": true,
		"base":     true,
	}
	if !validSourceRails[req.Source.PaymentRail] {
		return errors.New("source payment_rail must be one of: arbitrum, optimism, base")
	}

	// Validate source currency
	validSourceCurrencies := map[string]bool{
		"usdt": true,
		"usdc": true,
	}
	if !validSourceCurrencies[req.Source.Currency] {
		return errors.New("source currency must be one of: usdt, usdc")
	}

	// Validate from_address is not empty
	if req.Source.FromAddress == "" {
		return errors.New("source from_address is required")
	}

	// Validate destination
	if req.Destination == nil {
		return errors.New("destination is required")
	}

	// Validate destination payment rail
	validDestRails := map[string]bool{
		"ach":  true,
		"wire": true,
	}
	if !validDestRails[req.Destination.PaymentRail] {
		return errors.New("destination payment_rail must be one of: ach, wire")
	}

	// Validate destination currency
	if req.Destination.Currency != "usd" {
		return errors.New("destination currency must be usd")
	}

	// Validate external_account_id is not empty
	if req.Destination.ExternalAccountID == "" {
		return errors.New("destination external_account_id is required")
	}

	return nil
} 