package tokensignal

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	tokensignal "github.com/kryptogo/kg-wallet-backend/service/token-signal"
	"github.com/stretchr/testify/assert"
)

// Setup test router and initialize service
func setupTest() *gin.Engine {
	// Reset the database
	rdb.Reset()

	// Initialize the token signal service with the repository
	tokensignal.Init(rdb.GormRepo())

	// Setup router
	gin.SetMode(gin.TestMode)
	r := gin.New()

	return r
}

// Test UpsertBuySignal handler
func TestUpsertBuySignal(t *testing.T) {
	r := setupTest()

	// Setup route with the actual handler
	r.POST("/token_signal/buy", UpsertBuySignal)

	// Test successful upsert
	t.Run("Success", func(t *testing.T) {
		// Create request
		reqBody := UpsertBuySignalReq{
			TokenAddress:     "token123",
			SmartWalletCount: 10,
			BuyEntryPrice:    0.001,
			HighestPrice:     0.002,
			EmitTime:         time.Now().Unix(),
			TelegramLink:     "https://t.me/example",
			WinRate:          0.75,
			AverageHolding:   48.5,
			AverageWinRate:   0.65,
		}
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/token_signal/buy", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// Perform request
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusOK, w.Code)

		// Verify the data was saved in the database
		ctx := req.Context()
		repo := rdb.GormRepo()
		signal, err := repo.GetBuySignalByTokenAddress(ctx, reqBody.TokenAddress)
		assert.NoError(t, err)
		assert.Equal(t, reqBody.TokenAddress, signal.TokenAddress)
		assert.Equal(t, reqBody.SmartWalletCount, signal.SmartWalletCount)
		assert.Equal(t, reqBody.BuyEntryPrice, signal.BuyEntryPrice)
		assert.Equal(t, reqBody.HighestPrice, signal.HighestPrice)
		assert.Equal(t, reqBody.TelegramLink, signal.TelegramLink)
		assert.Equal(t, reqBody.WinRate, signal.WinRate)
		assert.Equal(t, reqBody.AverageHolding, signal.AverageHolding)
		assert.Equal(t, reqBody.AverageWinRate, signal.AverageWinRate)
	})

	// Test invalid request
	t.Run("InvalidRequest", func(t *testing.T) {
		// Create invalid request (missing required fields)
		reqBody := map[string]interface{}{
			"token_address": "token123",
			// Missing other required fields
		}
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/token_signal/buy", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// Perform request
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

// Test AddSellSignal handler
func TestAddSellSignal(t *testing.T) {
	r := setupTest()

	// Setup route with the actual handler
	r.POST("/token_signal/sell", AddSellSignal)

	// Test successful add with buy signal already in DB
	t.Run("Success", func(t *testing.T) {
		ctx := context.Background()
		repo := rdb.GormRepo()

		// First, create a buy signal
		buySignal := &domain.TokenBuySignal{
			TokenAddress:     "token123",
			SmartWalletCount: 10,
			BuyEntryPrice:    0.001,
			HighestPrice:     0.002,
			EmitTime:         time.Now().Add(-24 * time.Hour).Truncate(time.Second), // 1 day ago
			TelegramLink:     "https://t.me/example-buy",
			WinRate:          0.75,
			AverageHolding:   48.5,
			AverageWinRate:   0.65,
		}

		err := repo.UpsertBuySignal(ctx, buySignal)
		assert.NoError(t, err)

		// Create request
		reqBody := AddSellSignalReq{
			TokenAddress: "token123",
			TelegramLink: "https://t.me/example",
			HighestGain:  0.5,
		}
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/token_signal/sell", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// Perform request
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusOK, w.Code)

		// Verify the data was saved in the database
		signals, err := repo.ListSellSignals(ctx, 10)
		assert.NoError(t, err)

		// Find the sell signal
		found := false
		for _, signal := range signals {
			if signal.TokenAddress == reqBody.TokenAddress {
				found = true
				assert.Equal(t, reqBody.TelegramLink, signal.TelegramLink)
				assert.Equal(t, reqBody.HighestGain, signal.HighestGain)
				// Check BuyEntryTime was correctly set from the buy signal
				assert.NotNil(t, signal.BuyEntryTime)
				assert.Equal(t, buySignal.EmitTime.Unix(), signal.BuyEntryTime.Unix())
				break
			}
		}
		assert.True(t, found, "Sell signal not found in database")
	})

	// Test when buy signal doesn't exist
	t.Run("BuySignalNotFound", func(t *testing.T) {
		// Create request for a token that doesn't have a buy signal
		reqBody := AddSellSignalReq{
			TokenAddress: "nonexistent-token",
			TelegramLink: "https://t.me/example-nonexistent",
			HighestGain:  0.7,
		}
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/token_signal/sell", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// Perform request
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Check response - should fail with not found
		assert.Equal(t, http.StatusOK, w.Code)
	})

	// Test invalid request
	t.Run("InvalidRequest", func(t *testing.T) {
		// Create invalid request (missing required fields)
		reqBody := map[string]interface{}{
			"token_address": "token123",
			// Missing telegram_link and highest_gain
		}
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/token_signal/sell", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// Perform request
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

// Test ListBuySignals handler
func TestListBuySignals(t *testing.T) {
	r := setupTest()

	// Setup route with the actual handler
	r.GET("/token_signal/buy", ListBuySignals)

	// Insert test data
	ctx := context.Background()
	repo := rdb.GormRepo()

	// Create test buy signals
	now := time.Now()
	buySignal1 := &domain.TokenBuySignal{
		TokenAddress:     "token123",
		SmartWalletCount: 10,
		BuyEntryPrice:    0.001,
		HighestPrice:     0.002,
		EmitTime:         now,
		TelegramLink:     "https://t.me/example",
		WinRate:          0.75,
		AverageHolding:   48.5,
		AverageWinRate:   0.65,
	}
	buySignal2 := &domain.TokenBuySignal{
		TokenAddress:     "token456",
		SmartWalletCount: 15,
		BuyEntryPrice:    0.002,
		HighestPrice:     0.003,
		EmitTime:         now.Add(-time.Hour),
		TelegramLink:     "https://t.me/example2",
		WinRate:          0.85,
		AverageHolding:   72.3,
		AverageWinRate:   0.78,
	}

	// Insert buy signals
	err := repo.UpsertBuySignal(ctx, buySignal1)
	assert.NoError(t, err)
	err = repo.UpsertBuySignal(ctx, buySignal2)
	assert.NoError(t, err)

	// Test successful list
	t.Run("Success", func(t *testing.T) {
		// Create request
		req, _ := http.NewRequest("GET", "/token_signal/buy", nil)

		// Perform request
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusOK, w.Code)

		// Parse response
		var response struct {
			Code int                 `json:"code"`
			Data []BuySignalResponse `json:"data"`
		}
		_ = json.Unmarshal(w.Body.Bytes(), &response)

		// Verify response
		assert.Equal(t, 0, response.Code)
		assert.GreaterOrEqual(t, len(response.Data), 2)

		// Check if both signals are in the response
		found1 := false
		found2 := false
		for _, signal := range response.Data {
			if signal.TokenAddress == "token123" {
				found1 = true
				assert.Equal(t, 10, signal.SmartWalletCount)
				assert.Equal(t, 0.001, signal.BuyEntryPrice)
				assert.Equal(t, 0.002, signal.HighestPrice)
				assert.Equal(t, "https://t.me/example", signal.TelegramLink)
				assert.Equal(t, 0.75, signal.WinRate)
				assert.Equal(t, 48.5, signal.AverageHolding)
				assert.Equal(t, 0.65, signal.AverageWinRate)
			}
			if signal.TokenAddress == "token456" {
				found2 = true
				assert.Equal(t, 15, signal.SmartWalletCount)
				assert.Equal(t, 0.002, signal.BuyEntryPrice)
				assert.Equal(t, 0.003, signal.HighestPrice)
				assert.Equal(t, "https://t.me/example2", signal.TelegramLink)
				assert.Equal(t, 0.85, signal.WinRate)
				assert.Equal(t, 72.3, signal.AverageHolding)
				assert.Equal(t, 0.78, signal.AverageWinRate)
			}
		}
		assert.True(t, found1, "First buy signal not found in response")
		assert.True(t, found2, "Second buy signal not found in response")
	})
}

// Test ListSellSignals handler
func TestListSellSignals(t *testing.T) {
	r := setupTest()

	// Setup route with the actual handler
	r.GET("/token_signal/sell", ListSellSignals)

	// Insert test data
	ctx := context.Background()
	repo := rdb.GormRepo()

	// Create a buy signal first to get its emit time
	buyTime := time.Now().Add(-48 * time.Hour).Truncate(time.Second) // 2 days ago
	buySignal := &domain.TokenBuySignal{
		TokenAddress: "token456",
		EmitTime:     buyTime,
		// Other required fields
		SmartWalletCount: 10,
		BuyEntryPrice:    0.001,
		HighestPrice:     0.002,
		TelegramLink:     "https://t.me/example-buy",
		WinRate:          0.75,
		AverageHolding:   48.5,
		AverageWinRate:   0.65,
	}
	err := repo.UpsertBuySignal(ctx, buySignal)
	assert.NoError(t, err)

	// Create test sell signals
	now := time.Now()
	// First with BuyEntryTime
	sellSignal1 := &domain.TokenSellSignal{
		TokenAddress: "token123",
		EmitTime:     now,
		TelegramLink: "https://t.me/example",
		HighestGain:  1.5,
		BuyEntryTime: &buyTime, // Set BuyEntryTime
	}
	// Second with nil BuyEntryTime
	sellSignal2 := &domain.TokenSellSignal{
		TokenAddress: "token456",
		EmitTime:     now.Add(-time.Hour),
		TelegramLink: "https://t.me/example2",
		HighestGain:  2.0,
		BuyEntryTime: nil, // Nil BuyEntryTime
	}

	// Insert sell signals
	err = repo.AddSellSignal(ctx, sellSignal1)
	assert.NoError(t, err)
	err = repo.AddSellSignal(ctx, sellSignal2)
	assert.NoError(t, err)

	// Test successful list
	t.Run("Success", func(t *testing.T) {
		// Create request
		req, _ := http.NewRequest("GET", "/token_signal/sell", nil)

		// Perform request
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Check response
		assert.Equal(t, http.StatusOK, w.Code)

		// Parse response
		var response struct {
			Code int                  `json:"code"`
			Data []SellSignalResponse `json:"data"`
		}
		_ = json.Unmarshal(w.Body.Bytes(), &response)

		// Verify response
		assert.Equal(t, 0, response.Code)
		assert.GreaterOrEqual(t, len(response.Data), 2)

		// Check if both signals are in the response
		found1 := false
		found2 := false
		for _, signal := range response.Data {
			if signal.TokenAddress == "token123" {
				found1 = true
				assert.Equal(t, "https://t.me/example", signal.TelegramLink)
				assert.Equal(t, 1.5, signal.HighestGain)
				assert.NotNil(t, signal.BuyEntryTime)
				assert.Equal(t, buyTime.Unix(), *signal.BuyEntryTime)
			}
			if signal.TokenAddress == "token456" {
				found2 = true
				assert.Equal(t, "https://t.me/example2", signal.TelegramLink)
				assert.Equal(t, 2.0, signal.HighestGain)
				assert.Nil(t, signal.BuyEntryTime) // Should be nil
			}
		}
		assert.True(t, found1, "First sell signal not found in response")
		assert.True(t, found2, "Second sell signal not found in response")
	})
}
