package tokensignal

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	tokensignal "github.com/kryptogo/kg-wallet-backend/service/token-signal"
)

// UpsertBuySignalReq is the request for upserting a buy signal
type UpsertBuySignalReq struct {
	TokenAddress     string  `json:"token_address" binding:"required"`
	SmartWalletCount int     `json:"smart_wallet_count" binding:"required"`
	BuyEntryPrice    float64 `json:"buy_entry_price" binding:"required"`
	HighestPrice     float64 `json:"highest_price" binding:"required"`
	EmitTime         int64   `json:"emit_time" binding:"required"`
	TelegramLink     string  `json:"telegram_link" binding:"required"`
	WinRate          float64 `json:"win_rate" binding:"required"`
	AverageHolding   float64 `json:"average_holding" binding:"required"`
	AverageWinRate   float64 `json:"average_win_rate" binding:"required"`
	Symbol           string  `json:"symbol"`
}

// UpsertBuySignalBatchReq is the request for upserting multiple buy signals
type UpsertBuySignalBatchReq struct {
	Signals []UpsertBuySignalReq `json:"signals" binding:"required"`
}

// UpsertBuySignal handles the request to upsert a buy signal
func UpsertBuySignal(c *gin.Context) {
	ctx := c.Request.Context()

	var req UpsertBuySignalReq
	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to bind request", map[string]interface{}{
			"error": kgErr,
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, kgErr.String())
		return
	}
	emitTime := time.Unix(req.EmitTime, 0)

	// Call service
	params := &tokensignal.UpsertBuySignalParams{
		TokenAddress:     req.TokenAddress,
		SmartWalletCount: req.SmartWalletCount,
		BuyEntryPrice:    req.BuyEntryPrice,
		HighestPrice:     req.HighestPrice,
		EmitTime:         emitTime,
		TelegramLink:     req.TelegramLink,
		WinRate:          req.WinRate,
		AverageHolding:   req.AverageHolding,
		AverageWinRate:   req.AverageWinRate,
		Symbol:           req.Symbol,
	}

	if err := tokensignal.UpsertBuySignal(ctx, params); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to upsert buy signal", map[string]interface{}{
			"error":        err,
			"tokenAddress": req.TokenAddress,
		})
		response.KGError(c, err)
		return
	}

	response.OK(c, gin.H{
		"message": "Buy signal upserted successfully",
	})
}

// UpsertBuySignalBatch handles the request to upsert multiple buy signals at once
func UpsertBuySignalBatch(c *gin.Context) {
	ctx := c.Request.Context()

	var req UpsertBuySignalBatchReq
	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to bind batch request", map[string]interface{}{
			"error": kgErr,
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, kgErr.String())
		return
	}

	// Convert request to service params
	paramSignals := make([]tokensignal.UpsertBuySignalParams, len(req.Signals))
	for i, signal := range req.Signals {
		paramSignals[i] = tokensignal.UpsertBuySignalParams{
			TokenAddress:     signal.TokenAddress,
			SmartWalletCount: signal.SmartWalletCount,
			BuyEntryPrice:    signal.BuyEntryPrice,
			HighestPrice:     signal.HighestPrice,
			EmitTime:         time.Unix(signal.EmitTime, 0),
			TelegramLink:     signal.TelegramLink,
			WinRate:          signal.WinRate,
			AverageHolding:   signal.AverageHolding,
			AverageWinRate:   signal.AverageWinRate,
			Symbol:           signal.Symbol,
		}
	}

	// Call service
	batchParams := &tokensignal.UpsertBuySignalBatchParams{
		Signals: paramSignals,
	}

	if err := tokensignal.UpsertBuySignalBatch(ctx, batchParams); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to batch upsert buy signals", map[string]interface{}{
			"error":       err,
			"signalCount": len(req.Signals),
		})
		response.KGError(c, err)
		return
	}

	response.OK(c, gin.H{
		"message": "Buy signals batch upserted successfully",
		"count":   len(req.Signals),
	})
}

// AddSellSignalReq is the request for adding a sell signal
type AddSellSignalReq struct {
	TokenAddress string  `json:"token_address" binding:"required"`
	TelegramLink string  `json:"telegram_link" binding:"required"`
	HighestGain  float64 `json:"highest_gain"`
}

// AddSellSignal handles the request to add a sell signal
func AddSellSignal(c *gin.Context) {
	ctx := c.Request.Context()

	var req AddSellSignalReq
	if kgErr := util.ToGinContextExt(c).BindJson(&req); kgErr != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to bind request", map[string]interface{}{
			"error": kgErr,
		})
		response.BadRequestWithMsg(c, code.ParamIncorrect, kgErr.String())
		return
	}

	// Call service
	params := &tokensignal.AddSellSignalParams{
		TokenAddress: req.TokenAddress,
		TelegramLink: req.TelegramLink,
		HighestGain:  req.HighestGain,
	}

	if err := tokensignal.AddSellSignal(ctx, params); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to add sell signal", map[string]interface{}{
			"error":        err,
			"tokenAddress": req.TokenAddress,
		})
		response.KGError(c, err)
		return
	}

	response.OK(c, gin.H{
		"message": "Sell signal added successfully",
	})
}

// BuySignalResponse represents a buy signal in the response
type BuySignalResponse struct {
	TokenAddress     string  `json:"token_address"`
	SmartWalletCount int     `json:"smart_wallet_count"`
	BuyEntryPrice    float64 `json:"buy_entry_price"`
	HighestPrice     float64 `json:"highest_price"`
	EmitTime         int64   `json:"emit_time"`
	TelegramLink     string  `json:"telegram_link"`
	WinRate          float64 `json:"win_rate"`
	AverageHolding   float64 `json:"average_holding"`
	AverageWinRate   float64 `json:"average_win_rate"`
}

// ListBuySignals handles the request to list buy signals
func ListBuySignals(c *gin.Context) {
	ctx := c.Request.Context()

	// Call service
	signals, err := tokensignal.ListBuySignals(ctx)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to list buy signals", map[string]interface{}{
			"error": err,
		})
		response.KGError(c, err)
		return
	}

	// Convert to response format
	resp := make([]BuySignalResponse, len(signals))
	for i, signal := range signals {
		resp[i] = BuySignalResponse{
			TokenAddress:     signal.TokenAddress,
			SmartWalletCount: signal.SmartWalletCount,
			BuyEntryPrice:    signal.BuyEntryPrice,
			HighestPrice:     signal.HighestPrice,
			EmitTime:         signal.EmitTime.Unix(),
			TelegramLink:     signal.TelegramLink,
			WinRate:          signal.WinRate,
			AverageHolding:   signal.AverageHolding,
			AverageWinRate:   signal.AverageWinRate,
		}
	}

	response.OK(c, resp)
}

// SellSignalResponse represents a sell signal in the response
type SellSignalResponse struct {
	TokenAddress string  `json:"token_address"`
	EmitTime     int64   `json:"emit_time"`
	TelegramLink string  `json:"telegram_link"`
	HighestGain  float64 `json:"highest_gain"`
	BuyEntryTime *int64  `json:"buy_entry_time"`
}

// ListSellSignals handles the request to list sell signals
func ListSellSignals(c *gin.Context) {
	ctx := c.Request.Context()

	// Call service with limit 100
	signals, err := tokensignal.ListSellSignals(ctx, 200)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to list sell signals", map[string]interface{}{
			"error": err,
		})
		response.KGError(c, err)
		return
	}

	// Convert to response format
	resp := make([]SellSignalResponse, len(signals))
	for i, signal := range signals {
		var buyEntryTime *int64
		if signal.BuyEntryTime != nil {
			buyEntryTime = util.Ptr(signal.BuyEntryTime.Unix())
		}
		resp[i] = SellSignalResponse{
			TokenAddress: signal.TokenAddress,
			EmitTime:     signal.EmitTime.Unix(),
			TelegramLink: signal.TelegramLink,
			HighestGain:  signal.HighestGain,
			BuyEntryTime: buyEntryTime,
		}
	}

	response.OK(c, resp)
}

// GetSignalStats handles the request to get signal statistics
func GetSignalStats(c *gin.Context) {
	ctx := c.Request.Context()
	stats, kgErr := tokensignal.GetSignalStats(ctx)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, stats)
}
