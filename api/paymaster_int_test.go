package api_test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/router"
	"github.com/stretchr/testify/assert"
)

func setupIntegrationTestEnv() {
	gin.SetMode(gin.TestMode)
	router.InitDependencies()
}

func TestDepositIfNeeded(t *testing.T) {
	setupIntegrationTestEnv()
	r := router.SetupRouter()

	t.Run("Bad Chain ID", func(t *testing.T) {
		param := struct {
			Timeout int    `json:"timeout"`
			Chain   string `json:"chain"`
		}{
			Timeout: 60,
			Chain:   "123",
		}
		body, _ := json.Marshal(param)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/v2/paymaster/depositIfNeeded", bytes.NewBuffer(body))
		r.<PERSON>(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "invalid chain")
	})
}
