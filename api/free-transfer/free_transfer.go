package free_transfer

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	freetransfer "github.com/kryptogo/kg-wallet-backend/service/free-transfer"
)

// Response represents the free transfer status response
type Response struct {
	Used      int `json:"used"`
	Maximum   int `json:"maximum"`
	Remaining int `json:"remaining"`
}

// GetFreeSendStatus returns the free send status for an organization
func GetFreeSendStatus(c *gin.Context) {
	// Get organization ID from context
	orgID, exists := c.Get("org_id")
	if !exists {
		c.JSON(http.StatusBadRequest, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest,
			nil, map[string]interface{}{"error": "organization_id is required"}))
		return
	}

	// Convert to int
	orgIDInt, ok := orgID.(int)
	if !ok {
		kglog.ErrorWithDataCtx(c.Request.Context(), "Failed to parse organization ID", map[string]interface{}{
			"error":  "invalid org_id type",
			"org_id": orgID,
		})
		c.JSON(http.StatusBadRequest, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest,
			nil, map[string]interface{}{"error": "invalid organization_id"}))
		return
	}

	// Get free send count and maximum
	freeSendCount, maxCount, kgErr := freetransfer.GetOrgFreeSendCountWithMaximum(c.Request.Context(), orgIDInt)
	if kgErr != nil {
		errMsg := "Unknown error"
		if kgErr.Error != nil {
			errMsg = kgErr.Error.Error()
		}

		kglog.ErrorWithDataCtx(c.Request.Context(), "Failed to get free send count", map[string]interface{}{
			"error":  errMsg,
			"org_id": orgIDInt,
		})
		c.JSON(kgErr.HttpStatus, kgErr)
		return
	}

	// Check for nil pointer
	if freeSendCount == nil {
		kglog.ErrorWithDataCtx(c.Request.Context(), "Free send count is nil", map[string]interface{}{
			"org_id": orgIDInt,
		})
		c.JSON(http.StatusInternalServerError, code.NewKGError(code.InternalError, http.StatusInternalServerError,
			errors.New("free send count is nil"), nil))
		return
	}

	// Calculate remaining count
	remaining := maxCount - freeSendCount.UsedCount
	if remaining < 0 {
		remaining = 0
	}

	// Return response
	c.JSON(http.StatusOK, Response{
		Used:      freeSendCount.UsedCount,
		Maximum:   maxCount,
		Remaining: remaining,
	})
}
