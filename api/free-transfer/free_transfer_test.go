package free_transfer

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	freetransfer "github.com/kryptogo/kg-wallet-backend/service/free-transfer"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// Setup API test with mock service
func setupTest(t *testing.T) (*gin.Engine, *domain.MockStudioFreeTransferRepo) {
	// Initialize Gin in test mode
	gin.SetMode(gin.TestMode)
	r := gin.Default()

	// Create a mock controller
	ctrl := gomock.NewController(t)

	// Create a mock repo
	mockRepo := domain.NewMockStudioFreeTransferRepo(ctrl)

	// Initialize free transfer service with the mock repo
	freetransfer.Init(mockRepo)

	return r, mockRepo
}

func TestGetFreeSendStatus(t *testing.T) {
	// We'll run each test case as a separate subtest for better clarity
	t.Run("Success case - available free transfers", testAvailableFreeTransfers)
	t.Run("Maximum usage reached", testMaximumUsageReached)
	t.Run("Usage exceeds maximum", testUsageExceedsMaximum)
	t.Run("Database error", testDatabaseError)
	t.Run("Nil data returned", testNilDataReturned)
}

func testAvailableFreeTransfers(t *testing.T) {
	r, mockRepo := setupTest(t)
	maxCount := freetransfer.MaxFreeSendCount
	orgID := 1
	uid := "test-user"

	// Register the endpoint handler
	r.GET("/free_transfer/status", auth.MockOrgID(orgID), auth.MockAuthorize(uid), GetFreeSendStatus)

	mockRepo.EXPECT().
		GetOrgFreeSendCount(gomock.Any(), orgID).
		Return(&domain.OrgFreeSendCount{
			OrganizationID: orgID,
			UsedCount:      1,
		}, nil)

	// Create request
	req, _ := http.NewRequest("GET", "/free_transfer/status", nil)
	w := httptest.NewRecorder()

	// Serve the request
	r.ServeHTTP(w, req)

	// Check status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Log the actual response for debugging
	kglog.DebugWithData("Response body", map[string]interface{}{
		"body": w.Body.String(),
	})

	var response Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, 1, response.Used)
	assert.Equal(t, maxCount, response.Maximum)
	assert.Equal(t, maxCount-1, response.Remaining)
}

func testMaximumUsageReached(t *testing.T) {
	r, mockRepo := setupTest(t)
	maxCount := freetransfer.MaxFreeSendCount
	orgID := 1
	uid := "test-user"

	// Register the endpoint handler
	r.GET("/free_transfer/status", auth.MockOrgID(orgID), auth.MockAuthorize(uid), GetFreeSendStatus)

	mockRepo.EXPECT().
		GetOrgFreeSendCount(gomock.Any(), orgID).
		Return(&domain.OrgFreeSendCount{
			OrganizationID: orgID,
			UsedCount:      maxCount,
		}, nil)

	// Create request
	req, _ := http.NewRequest("GET", "/free_transfer/status", nil)
	w := httptest.NewRecorder()

	// Serve the request
	r.ServeHTTP(w, req)

	// Check status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Log the actual response for debugging
	kglog.DebugWithData("Response body", map[string]interface{}{
		"body": w.Body.String(),
	})

	var response Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, maxCount, response.Used)
	assert.Equal(t, maxCount, response.Maximum)
	assert.Equal(t, 0, response.Remaining)
}

func testUsageExceedsMaximum(t *testing.T) {
	r, mockRepo := setupTest(t)
	maxCount := freetransfer.MaxFreeSendCount
	orgID := 1
	uid := "test-user"

	// Register the endpoint handler
	r.GET("/free_transfer/status", auth.MockOrgID(orgID), auth.MockAuthorize(uid), GetFreeSendStatus)

	mockRepo.EXPECT().
		GetOrgFreeSendCount(gomock.Any(), orgID).
		Return(&domain.OrgFreeSendCount{
			OrganizationID: orgID,
			UsedCount:      maxCount + 2,
		}, nil)

	// Create request
	req, _ := http.NewRequest("GET", "/free_transfer/status", nil)
	w := httptest.NewRecorder()

	// Serve the request
	r.ServeHTTP(w, req)

	// Check status code
	assert.Equal(t, http.StatusOK, w.Code)

	// Log the actual response for debugging
	kglog.DebugWithData("Response body", map[string]interface{}{
		"body": w.Body.String(),
	})

	var response Response
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, maxCount+2, response.Used)
	assert.Equal(t, maxCount, response.Maximum)
	assert.Equal(t, 0, response.Remaining) // Should cap at 0
}

func testDatabaseError(t *testing.T) {
	r, mockRepo := setupTest(t)
	orgID := 1
	uid := "test-user"

	// Register the endpoint handler
	r.GET("/free_transfer/status", auth.MockOrgID(orgID), auth.MockAuthorize(uid), GetFreeSendStatus)

	// Setup mock with database error
	dbErr := errors.New("database error")
	mockRepo.EXPECT().
		GetOrgFreeSendCount(gomock.Any(), orgID).
		Return(nil, dbErr)

	// Create request
	req, _ := http.NewRequest("GET", "/free_transfer/status", nil)
	w := httptest.NewRecorder()

	// Serve the request
	r.ServeHTTP(w, req)

	// Check status code
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	// Log the actual response for debugging
	kglog.DebugWithData("Response body", map[string]interface{}{
		"body": w.Body.String(),
	})

	// Parse the error response
	var errorResponse struct {
		Code       int                    `json:"code"`
		Error      map[string]interface{} `json:"error"`
		HttpStatus int                    `json:"http_status"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &errorResponse)
	assert.NoError(t, err)

	// Check code and http status
	assert.Equal(t, code.DBError, errorResponse.Code)
	assert.Equal(t, http.StatusInternalServerError, errorResponse.HttpStatus)
}

func testNilDataReturned(t *testing.T) {
	r, mockRepo := setupTest(t)
	orgID := 1
	uid := "test-user"

	// Register the endpoint handler
	r.GET("/free_transfer/status", auth.MockOrgID(orgID), auth.MockAuthorize(uid), GetFreeSendStatus)

	// Setup mock to return nil data
	mockRepo.EXPECT().
		GetOrgFreeSendCount(gomock.Any(), orgID).
		Return(nil, nil)

	// Create request
	req, _ := http.NewRequest("GET", "/free_transfer/status", nil)
	w := httptest.NewRecorder()

	// Serve the request
	r.ServeHTTP(w, req)

	// Check status code
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	// Log the actual response for debugging
	kglog.DebugWithData("Response body", map[string]interface{}{
		"body": w.Body.String(),
	})

	// Parse the error response
	var errorResponse struct {
		Code       int                    `json:"code"`
		Error      map[string]interface{} `json:"error"`
		HttpStatus int                    `json:"http_status"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &errorResponse)
	assert.NoError(t, err)

	// Check code and http status
	assert.Equal(t, code.InternalError, errorResponse.Code)
	assert.Equal(t, http.StatusInternalServerError, errorResponse.HttpStatus)
}
