package airdrop

import (
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/api/oauth"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	userapi "github.com/kryptogo/kg-wallet-backend/api/user"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/airdrop"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/airdrop/module"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/kms"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/nyaruka/phonenumbers"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

type receiveParams struct {
	Phone       string `json:"phone_number"`
	EventID     string `json:"event_id" binding:"required"`
	TraitID     string `json:"trait_id"`
	DisableSMS  bool   `json:"disable_sms"`
	TokenID     *int32 `json:"token_id"`
	TokenAmount int32  `json:"token_amount"`
}

// Receive is the handler for airdrop receive
func Receive(ctx *gin.Context) {
	req := receiveParams{}
	kgErr := util.ToGinContextExt(ctx).BindJson(&req)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	kglog.DebugWithDataCtx(ctx, "Receive ", req)

	if req.Phone == "" && auth.GetUID(ctx) == "" {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, "param incorrect")
		return
	}
	locale := util.GetRegionLocaleFromPhoneNumber(req.Phone)
	// Check if the event is valid
	event, errCode, err := rdb.GetAirdropEvent(ctx.Request.Context(), req.EventID, locale)
	if err == gorm.ErrRecordNotFound {
		response.BadRequestWithMsg(ctx, code.AirdropEventNotFound, err.Error())
		return
	} else if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}
	errCode, err = airdrop.ValidateEvent(event)
	if err != nil {
		response.BadRequestWithMsg(ctx, errCode, err.Error())
		return
	}

	phoneNumber, kgErr := getFormattedPhoneNumber(ctx, req.Phone)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	// Support for old version to airdrop erc1155
	if event.ContractSchemaName == model.SchemaERC1155 {
		if req.TokenAmount == 0 {
			req.TokenAmount = 1
		}
		if req.TokenID == nil {
			req.TokenID = util.Ptr(int32(0))
		}
	}

	// get user address. If user not exist, create one
	clientID := oauth.ClientID(ctx)
	userAddress, kgErr := airdrop.GetUserAddress(ctx.Request.Context(), event.ChainID, phoneNumber, clientID)
	if kgErr != nil {
		if kgErr.HttpStatus == http.StatusBadRequest {
			kglog.InfoWithDataCtx(ctx, "GetUserAddress, ERROR: "+kgErr.String(), map[string]interface{}{
				"phone_number": phoneNumber,
				"client_id":    clientID,
			})
			response.KGError(ctx, kgErr)
			return
		} else {
			kglog.ErrorWithDataCtx(ctx, "GetUserAddress, ERROR: "+kgErr.String(), map[string]interface{}{
				"phone_number": phoneNumber,
				"client_id":    clientID,
			})
			response.KGError(ctx, kgErr)
			return
		}
	}

	// Check if user already received
	txHash, errCode, err := airdrop.IsUserReceived(ctx.Request.Context(), event, userAddress, phoneNumber, req.TokenID, req.TokenAmount)
	if errCode == code.AirdropUserAlreadyReceived {
		ctx.JSON(http.StatusOK, map[string]interface{}{
			"code":  errCode,
			"error": err.Error(),
			"data": map[string]string{
				"address": userAddress,
				"tx_hash": txHash,
			},
		})
		return
	} else if err != nil {
		kglog.ErrorCtx(ctx, "GetUserStatus, ERROR: "+err.Error()+", phone: "+phoneNumber)
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	moduleData := &module.Data{
		Event:       event,
		PhoneNumber: phoneNumber,
		TraitID:     req.TraitID,
		TokenID:     req.TokenID,
		TokenAmount: req.TokenAmount,
	}

	txHash, errCode, err = airdrop.NFT(ctx.Request.Context(), event, moduleData, userAddress, phoneNumber)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Airdrop, ERROR: "+err.Error(), map[string]interface{}{
			"event":        event.EventID,
			"phone number": phoneNumber,
			"userAddress":  userAddress,
			"error code":   errCode,
			"error":        err.Error(),
		})
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}
	// Notify user of the result
	if !req.DisableSMS {
		SMSData := airdrop.SMSData{
			PhoneNumber:    phoneNumber,
			TxHash:         txHash,
			CollectionName: event.EventID,
		}
		SMSContent, kgErr := airdrop.SendAirdropSMS(ctx.Request.Context(), event, &SMSData)
		if kgErr != nil {
			kglog.ErrorWithData("SendSMS, ERROR: "+kgErr.String(), map[string]interface{}{
				"phone_number": phoneNumber,
				"content":      SMSContent,
			})
		}
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": map[string]string{
			"address": userAddress,
			"tx_hash": txHash,
		},
	})
}

func getFormattedPhoneNumber(ctx *gin.Context, phone string) (string, *code.KGError) {
	if phone != "" {
		phoneNumber, err := parsePhoneNumber(phone)
		if err != nil {
			return "", code.NewKGError(code.PhoneFormatError, http.StatusBadRequest, fmt.Errorf("phone number format incorrect"), nil)
		}
		return phoneNumber, nil
	}

	uid := auth.GetUID(ctx)
	user, err := user.GetInfo(ctx, uid, "", false, nil)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "getFormattedPhoneNumber, error", map[string]interface{}{
			"uid":   uid,
			"error": err.String(),
		})
		return "", err
	}
	return user.PhoneNumber, nil
}

func parsePhoneNumber(phone string) (string, error) {
	num, err := phonenumbers.Parse(phone, "TW")
	if err != nil {
		return "", err
	}
	return phonenumbers.Format(num, phonenumbers.E164), nil
}

type airdropStatusParams struct {
	Phone   string `form:"phone_number" json:"phone_number"`
	EventID string `form:"event_id" json:"event_id"`
}

// Event is the handler for airdrop event
type Event struct {
	model.AirdropEvent
	EventValid bool `json:"event_valid"`
	CoverImage *struct {
		GIF  string `json:"gif,omitempty"`
		PNG  string `json:"png,omitempty"`
		SVG  string `json:"svg,omitempty"`
		JPG  string `json:"jpg,omitempty"`
		JPEG string `json:"jpeg,omitempty"`
	} `json:"cover_image,omitempty"`
	CoverVideo *struct {
		MP4  string `json:"mp4,omitempty"`
		WEBM string `json:"webm,omitempty"`
	} `json:"cover_video,omitempty"`
}

// EventStatus is the handler for airdrop event status
func EventStatus(ctx *gin.Context) {
	params := airdropStatusParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	event, errCode, err := rdb.GetAirdropEvent(ctx.Request.Context(), params.EventID, "")
	if err != nil {
		ctx.JSON(http.StatusOK, &userapi.AddressesByPhoneResp{
			Code:  errCode,
			Error: err.Error(),
		})
		return
	}

	data := &Event{}
	data.AirdropEvent = *event
	data.EventValid = (event.StartTime.Before(time.Now()) && event.EndTime.After(time.Now()))
	if event.ImageURL != nil {
		err = json.Unmarshal([]byte(*event.ImageURL), &data.CoverImage)
		if err != nil {
			ctx.JSON(http.StatusOK, &userapi.AddressesByPhoneResp{
				Code:  code.JSONError,
				Error: "cover image data error",
			})
			return
		}
		if reflect.ValueOf(*data.CoverImage).IsZero() {
			data.CoverImage = nil
		}
		err = json.Unmarshal([]byte(*event.ImageURL), &data.CoverVideo)
		if err != nil {
			ctx.JSON(http.StatusOK, &userapi.AddressesByPhoneResp{
				Code:  code.JSONError,
				Error: "cover video data error",
			})
			return
		}
		if reflect.ValueOf(*data.CoverVideo).IsZero() {
			data.CoverVideo = nil
		}
	}

	if event.ChainID == "matic" {
		data.ChainID = "polygon"
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": data,
	})
}

type userStatusResp struct {
	Code  int               `json:"code"`
	Error string            `json:"error"`
	Data  map[string]string `json:"data"`
}

// UserStatus is the handler for airdrop user status
func UserStatus(ctx *gin.Context) {
	params := airdropStatusParams{}
	kgErr := util.ToGinContextExt(ctx).BindQuery(&params)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	clientID := oauth.ClientID(ctx)

	// Check if the event is valid
	event, errCode, err := rdb.GetAirdropEvent(ctx.Request.Context(), params.EventID, "")
	if err == gorm.ErrRecordNotFound {
		response.BadRequestWithMsg(ctx, code.ParamIncorrect, err.Error())
		return
	} else if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	phoneNumber, kgErr := getFormattedPhoneNumber(ctx, params.Phone)
	if kgErr != nil {
		response.KGError(ctx, kgErr)
		return
	}

	addresses, kgErr := kms.GetAddressesByPhone(ctx, phoneNumber, clientID)
	if kgErr != nil {
		kglog.WarningWithDataCtx(ctx, "GetAddressesByPhone, ERROR", map[string]interface{}{
			"phone_number": phoneNumber,
			"client_id":    clientID,
			"error":        kgErr.String(),
		})
		response.KGError(ctx, kgErr)
		return
	}

	receiveWallets := lo.MapEntries(addresses, func(chain domain.Chain, address domain.Address) (string, string) {
		return chain.ID(), address.String()
	})

	userAddress := receiveWallets[event.ChainID]
	if event.ChainID == "goerli" {
		userAddress = receiveWallets["eth"]
	}

	txHash, errCode, err := airdrop.IsUserReceived(ctx.Request.Context(), event, userAddress, phoneNumber, nil, 0)
	if errCode == code.AirdropUserAlreadyReceived {
		ctx.JSON(http.StatusOK, &userStatusResp{
			Code:  errCode,
			Error: err.Error(),
			Data: map[string]string{
				"address": userAddress,
				"tx_hash": txHash,
			},
		})
		return
	} else if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, &userStatusResp{
		Code: 0,
	})
}

// Events is the handler for airdrop events
func Events(ctx *gin.Context) {
	eventIDs, errCode, err := rdb.GetAirdropEventIDs(ctx.Request.Context())
	if err != nil {
		response.InternalServerErrorWithMsg(ctx, errCode, err.Error())
		return
	}

	ctx.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": map[string]interface{}{
			"event_id": eventIDs,
		},
	})
}
