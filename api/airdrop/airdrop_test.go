package airdrop

import (
	"testing"

	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"

	"github.com/stretchr/testify/assert"
)

// func TestReceive(t *testing.T) {
// 	ctx := context.Background()

// 	// setup firestore, firebase auth, db
// 	rdb.Reset()
// 	users, _, phoneNumber, _ := firestoretest.User()
// 	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

// 	_, err := firebase.BatchCreateUsersBySeed(users)
// 	assert.Nil(t, err)

// 	setupStudio(t)

// 	assert.Nil(t, rdbtest.CreateAirdropEvents(rdb.Get()))
// 	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
// 	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
// 	oauth.Init(rdb.GormRepo())
// 	application.Init(rdb.GormRepo())
// 	apis.InitDefault()
// 	tx.Init(rdb.GormRepo())
// 	user.Init(repo.Unified())

// 	// sign server
// 	TEST_SIGNING_PORT := testutil.UnusedPort(t)
// 	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)

// 	// test data
// 	eventID := "kryptogo-yacht-club"
// 	userAddress := "0x31d1C7751EAA6374D4138597e8c7b5a1605cC43c"
// 	type responseType struct {
// 		Code int `json:"code"`
// 		Data struct {
// 			UserAddress string `json:"address"`
// 			TxHash      string `json:"tx_hash"`
// 		} `json:"data"`
// 	}

// 	// gin server
// 	r := gin.Default()
// 	url := "/v1/airdrop/receive"
// 	r.POST(url, Receive)

// 	// signing server
// 	rSigning := gin.Default()
// 	rSigning.POST("/v1/sign/evm", signing.SignEvmTransaction)
// 	// create HTTP server for graceful shutdown
// 	srv := &http.Server{
// 		Addr:    ":" + TEST_SIGNING_PORT,
// 		Handler: rSigning,
// 	}
// 	go func() {
// 		err := srv.ListenAndServe()
// 		if err != nil && err != http.ErrServerClosed {
// 			t.Logf("Signing server terminated with error: %v\n", err)
// 		}
// 	}()
// 	defer func() {
// 		err = srv.Shutdown(context.Background())
// 		assert.Nil(t, err)
// 		fmt.Println("Finish Shutdown")
// 	}()

// 	time.Sleep(1 * time.Second) // wait for server to start

// 	// request
// 	body := map[string]interface{}{
// 		"event_id":     eventID,
// 		"phone_number": phoneNumber,
// 		"disable_sms":  true,
// 	}
// 	bodyStr, err := json.Marshal(body)
// 	assert.Nil(t, err)
// 	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
// 	assert.Nil(t, err)

// 	w := httptest.NewRecorder()
// 	r.ServeHTTP(w, req)
// 	assert.Equal(t, http.StatusOK, w.Code)

// 	// check response
// 	var response responseType
// 	responseStr, err := io.ReadAll(w.Body)
// 	fmt.Println("responseStr", string(responseStr))
// 	assert.Nil(t, err)
// 	assert.Nil(t, json.Unmarshal(responseStr, &response))
// 	assert.Equal(t, 0, response.Code)
// 	assert.NotEmpty(t, response.Data.TxHash)

// 	// check update is correct after tx confirmed
// 	event, _, err := rdb.GetAirdropEvent(ctx, eventID, "en")
// 	assert.Nil(t, err)
// 	waitTimes := 0
// 	var txHash string
// 	for {
// 		if waitTimes > 120 {
// 			t.Error("wait too long")
// 			break
// 		}
// 		time.Sleep(1 * time.Second)
// 		waitTimes++
// 		txHashes, _ := rdb.GetAirdropHashByUser(ctx, event, userAddress, phoneNumber, nil)
// 		if len(*txHashes) > 0 {
// 			fmt.Println("Finish AfterTxConfirmed, hash:", *txHashes)
// 			txHash = (*txHashes)[0].TxHash
// 			break
// 		}
// 	}
// 	tx.Get().WaitTxConfirmed(ctx, event.ChainID, txHash, 10)
// 	fmt.Println("Finish Test")

// }

// func TestReceiveERC1155(t *testing.T) {
// 	ctx := context.Background()

// 	// setup firestore, firebase auth, db
// 	rdb.Reset()
// 	users, _, phoneNumber, _ := firestoretest.User()
// 	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

// 	_, err := firebase.BatchCreateUsersBySeed(users)
// 	assert.Nil(t, err)

// 	setupStudio(t)
// 	assert.Nil(t, rdbtest.CreateAirdropEvents(rdb.Get()))
// 	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
// 	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
// 	oauth.Init(rdb.GormRepo())
// 	application.Init(rdb.GormRepo())
// 	apis.InitDefault()
// 	tx.Init(rdb.GormRepo())

// 	TEST_SIGNING_PORT := testutil.UnusedPort(t)
// 	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)

// 	// test data
// 	eventID := "gacha"
// 	tokenID := int32(1)
// 	tokenAmount := 3
// 	userAddress := "0x31d1C7751EAA6374D4138597e8c7b5a1605cC43c"
// 	type responseType struct {
// 		Code int `json:"code"`
// 		Data struct {
// 			UserAddress string `json:"address"`
// 			TxHash      string `json:"tx_hash"`
// 		} `json:"data"`
// 	}

// 	// gin server
// 	r := gin.Default()
// 	url := "/v1/airdrop/receive"
// 	r.POST(url, Receive)

// 	// signing server
// 	rSigning := gin.Default()
// 	rSigning.POST("/v1/sign/evm", signing.SignEvmTransaction)
// 	// create HTTP server for graceful shutdown
// 	srv := &http.Server{
// 		Addr:    ":" + TEST_SIGNING_PORT,
// 		Handler: rSigning,
// 	}
// 	go func() {
// 		err := srv.ListenAndServe()
// 		if err != nil && err != http.ErrServerClosed {
// 			t.Logf("Signing server terminated with error: %v\n", err)
// 		}
// 	}()
// 	defer func() {
// 		err = srv.Shutdown(context.Background())
// 		assert.Nil(t, err)
// 		fmt.Println("Finish Shutdown")
// 	}()

// 	time.Sleep(1 * time.Second) // wait for server to start

// 	// request
// 	body := map[string]interface{}{
// 		"event_id":     eventID,
// 		"phone_number": phoneNumber,
// 		"disable_sms":  true,
// 		"token_amount": tokenAmount,
// 		"token_id":     tokenID,
// 	}

// 	bodyStr, err := json.Marshal(body)
// 	assert.Nil(t, err)
// 	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
// 	assert.Nil(t, err)

// 	w := httptest.NewRecorder()
// 	r.ServeHTTP(w, req)
// 	assert.Equal(t, http.StatusOK, w.Code)

// 	// check response
// 	var response responseType
// 	responseStr, err := io.ReadAll(w.Body)
// 	fmt.Println("responseStr", string(responseStr))
// 	assert.Nil(t, err)
// 	assert.Nil(t, json.Unmarshal(responseStr, &response))
// 	assert.Equal(t, 0, response.Code)
// 	assert.NotEmpty(t, response.Data.TxHash)

// 	// check update is correct after tx confirmed
// 	event, _, err := rdb.GetAirdropEvent(ctx, eventID, "en")
// 	assert.Nil(t, err)
// 	waitTimes := 0
// 	var txHash string
// 	for {
// 		if waitTimes > 120 {
// 			t.Error("wait too long")
// 			break
// 		}
// 		time.Sleep(1 * time.Second)
// 		waitTimes++
// 		txHashes, _ := rdb.GetAirdropHashByUser(ctx, event, userAddress, phoneNumber, &tokenID)
// 		if txHashes != nil && len(*txHashes) > 0 {
// 			fmt.Println("Finish AfterTxConfirmed, hash:", *txHashes)
// 			txHash = (*txHashes)[0].TxHash
// 			break
// 		}
// 	}
// 	tx.Get().WaitTxConfirmed(ctx, event.ChainID, txHash, 10)
// 	fmt.Println("Finish Test")

// 	// over the total limit
// 	req2, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
// 	w = httptest.NewRecorder()
// 	r.ServeHTTP(w, req2)
// 	assert.Equal(t, http.StatusOK, w.Code)
// 	var response2 responseType
// 	responseStr, err = io.ReadAll(w.Body)
// 	fmt.Println("responseStr", string(responseStr))
// 	assert.Nil(t, err)
// 	assert.Nil(t, json.Unmarshal(responseStr, &response2))
// 	assert.Equal(t, 4006, response2.Code)
// }

// func TestReceiveWithTrait(t *testing.T) {
// 	ctx := context.Background()

// 	// setup firestore, firebase auth, db
// 	rdb.Reset()
// 	users, _, phoneNumber, _ := firestoretest.User()
// 	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

// 	_, err := firebase.BatchCreateUsersBySeed(users)
// 	assert.Nil(t, err)

// 	setupStudio(t)
// 	assert.Nil(t, rdbtest.CreateAirdropEvents(rdb.Get()))
// 	assert.Nil(t, rdbtest.CreateAirdropNftTraits(rdb.Get()))
// 	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
// 	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
// 	oauth.Init(rdb.GormRepo())
// 	application.Init(rdb.GormRepo())
// 	apis.InitDefault()
// 	tx.Init(rdb.GormRepo())

// 	TEST_SIGNING_PORT := testutil.UnusedPort(t)
// 	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)

// 	// test data
// 	eventID := "kryptogo-yacht-club-2"
// 	userAddress := "0x31d1C7751EAA6374D4138597e8c7b5a1605cC43c"

// 	// gin server
// 	r := gin.Default()
// 	url := "/v1/airdrop/receive"
// 	r.POST(url, Receive)

// 	// signing server
// 	rSigning := gin.Default()
// 	rSigning.POST("/v1/sign/evm", signing.SignEvmTransaction)
// 	// create HTTP server for graceful shutdown
// 	srv := &http.Server{
// 		Addr:    ":" + TEST_SIGNING_PORT,
// 		Handler: rSigning,
// 	}
// 	go func() {
// 		err := srv.ListenAndServe()
// 		if err != nil && err != http.ErrServerClosed {
// 			t.Logf("Signing server terminated with error: %v\n", err)
// 		}
// 	}()
// 	defer func() {
// 		err = srv.Shutdown(context.Background())
// 		assert.Nil(t, err)
// 		fmt.Println("Finish Shutdown")
// 	}()
// 	time.Sleep(1 * time.Second) // wait for server to start

// 	// request
// 	body := map[string]interface{}{
// 		"event_id":     eventID,
// 		"phone_number": phoneNumber,
// 		"disable_sms":  true,
// 		"trait_id":     "1",
// 	}
// 	bodyStr, err := json.Marshal(body)
// 	assert.Nil(t, err)
// 	req, err := http.NewRequest("POST", url, strings.NewReader(string(bodyStr)))
// 	assert.Nil(t, err)
// 	w := httptest.NewRecorder()
// 	r.ServeHTTP(w, req)
// 	assert.Equal(t, http.StatusOK, w.Code)

// 	// check response
// 	type responseType struct {
// 		Code int `json:"code"`
// 		Data struct {
// 			UserAddress string `json:"address"`
// 			TxHash      string `json:"tx_hash"`
// 		} `json:"data"`
// 	}
// 	var response responseType
// 	responseStr, err := io.ReadAll(w.Body)
// 	fmt.Println("responseStr", string(responseStr))
// 	assert.Nil(t, err)
// 	assert.Nil(t, json.Unmarshal(responseStr, &response))
// 	assert.Equal(t, 0, response.Code)
// 	assert.NotEmpty(t, response.Data.TxHash)

// 	// check update is correct after tx confirmed
// 	event, _, err := rdb.GetAirdropEvent(ctx, eventID, "en")
// 	assert.Nil(t, err)
// 	waitTimes := 0
// 	var txHash string
// 	for {
// 		if waitTimes > 120 {
// 			t.Error("wait too long")
// 			break
// 		}
// 		time.Sleep(1 * time.Second)
// 		waitTimes++
// 		txHashes, _ := rdb.GetAirdropHashByUser(ctx, event, userAddress, phoneNumber, nil)
// 		if len(*txHashes) > 0 {
// 			fmt.Println("Finish AfterTxConfirmed, hash:", *txHashes)
// 			txHash = (*txHashes)[0].TxHash
// 			break
// 		}
// 	}
// 	tx.Get().WaitTxConfirmed(ctx, event.ChainID, txHash, 10)
// 	fmt.Println("Finish Test")
// }

func TestParsePhoneNumber(t *testing.T) {
	phone, err := parsePhoneNumber("0908123123")
	assert.Nil(t, err)
	assert.Equal(t, "+886908123123", phone)

	phone, err = parsePhoneNumber("908123123")
	assert.Nil(t, err)
	assert.Equal(t, "+886908123123", phone)

	phone, err = parsePhoneNumber("886908123123")
	assert.Nil(t, err)
	assert.Equal(t, "+886908123123", phone)

	phone, err = parsePhoneNumber("+886908123123")
	assert.Nil(t, err)
	assert.Equal(t, "+886908123123", phone)

	// JP phone
	phone, err = parsePhoneNumber("+819081231232")
	assert.Nil(t, err)
	assert.Equal(t, "+819081231232", phone)

	// US phone
	phone, err = parsePhoneNumber("+15555551234")
	assert.Nil(t, err)
	assert.Equal(t, "+15555551234", phone)
}

func setupStudio(t *testing.T) {
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationWallets(rdb.Get()))
	organization.Init(organization.InitParam{
		StudioOrgRepo:  rdb.GormRepo(),
		StudioRoleRepo: rdb.GormRepo(),
	})
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
	application.Init(rdb.GormRepo())
}
