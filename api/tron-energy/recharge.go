package tronenergyapi

import (
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	tronenergy "github.com/kryptogo/kg-wallet-backend/pkg/service/tron-energy"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type rechargeReq struct {
	Amount float64 `json:"amount" binding:"required"`
}

// Recharge recharge given amount of TRX to feee
func Recharge(c *gin.Context) {
	ctx := c.Request.Context()
	params := &rechargeReq{}
	kgErr := util.ToGinContextExt(c).BindJson(params)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	txHash, kgErr := tronenergy.Recharge(ctx, params.Amount)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	kglog.InfoWithDataCtx(ctx, "Feee Recharge Success", map[string]interface{}{"amount": params.Amount, "tx": txHash})
	response.OK(c, nil)
}

// AutoRecharge automatically recharges feee when balance is below threshold
func AutoRecharge(c *gin.Context) {
	ctx := c.Request.Context()
	kgErr := tronenergy.AutoRecharge(ctx)
	if kgErr != nil {
		response.KGError(c, kgErr)
		return
	}
	response.OK(c, nil)
}
