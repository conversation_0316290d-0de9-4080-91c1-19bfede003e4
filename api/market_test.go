package api

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/middleware/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	assetpro "github.com/kryptogo/kg-wallet-backend/pkg/service/studio/asset_pro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestGetSwapConfig(t *testing.T) {
	s := assert.New(t)

	rdb.Reset()
	s.NoError(dbtest.CreateStudioDefault(rdb.Get()))
	refererAddress := "******************************************" // inserted by dbtest.CreateStudioOrganizationWallets
	clientID := "20991a3ae83233d6de85d62906d71fd3"                 // inserted by dbtest.CreateOAuthClientConfigs

	organization.Init(organization.InitParam{
		StudioOrgRepo:  rdb.GormRepo(),
		StudioRoleRepo: rdb.GormRepo(),
	})
	application.Init(rdb.GormRepo())
	assetpro.InitFinance(assetpro.InitParam{
		AssetProFinanceRepo: rdb.GormRepo(),
		StudioOrgRepo:       rdb.GormRepo(),
	})

	r := gin.Default()
	r.GET("/v1/market/swap_config",
		auth.MockClientID(clientID),
		GetSwapConfig)

	type swapConfigResponse struct {
		Code int `json:"code"`
		Data struct {
			OneInch struct {
				Referrer string  `json:"referrer"`
				Fee      float64 `json:"fee"`
			} `json:"1inch"`
		} `json:"data"`
		Message string `json:"message"`
	}

	t.Run("get swap config before setting profit rate", func(t *testing.T) {
		req, err := http.NewRequest(http.MethodGet, "/v1/market/swap_config", nil)
		s.NoError(err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		s.Equal(http.StatusNotFound, w.Code)

		var response swapConfigResponse
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(1071, response.Code)
	})

	s.NoError(rdb.Get().Create(&model.AssetProProfitRate{
		OrganizationID:   1,
		Service:          "swap_defi",
		ProfitRate:       decimal.Zero,
		ProfitShareRatio: decimal.Zero,
	}).Error)

	t.Run("get swap config after setting profit rate", func(t *testing.T) {
		req, err := http.NewRequest(http.MethodGet, "/v1/market/swap_config", nil)
		s.NoError(err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response swapConfigResponse
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Empty(response.Message)
		s.Equal(0.0015, response.Data.OneInch.Fee)
		s.Equal(refererAddress, response.Data.OneInch.Referrer)
	})

	s.NoError(rdb.Get().Model(&model.AssetProProfitRate{}).
		Where("organization_id = ? AND service = ?", 1, "swap_defi").
		Updates(map[string]any{
			"profit_rate":        decimal.NewFromFloat(0.02),
			"profit_share_ratio": decimal.NewFromFloat(0.5),
		}).Error)

	t.Run("get swap config after setting profit rate with non zero profit rate", func(t *testing.T) {
		req, err := http.NewRequest(http.MethodGet, "/v1/market/swap_config", nil)
		s.NoError(err)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)
		s.Equal(http.StatusOK, w.Code)

		var response swapConfigResponse
		s.NoError(json.Unmarshal(w.Body.Bytes(), &response))
		s.Equal(0, response.Code)
		s.Empty(response.Message)
		s.Equal(0.0215, response.Data.OneInch.Fee)
		s.Equal(refererAddress, response.Data.OneInch.Referrer)
	})
}
