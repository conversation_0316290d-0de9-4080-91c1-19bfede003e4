package api

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/response"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
)

type ChainTokenPair struct {
	ChainID         string `json:"chain_id" binding:"required"`
	ContractAddress string `json:"contract_address" binding:"required"`
}

type GetAssetPricesReq struct {
	Tokens []ChainTokenPair `json:"tokens" binding:"required"`
}

// GetAssetPricesByContract handles the API request to get asset prices by contract addresses
func GetAssetPricesByContract(c *gin.Context) {
	ctx := c.Request.Context()
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()
	var req GetAssetPricesReq
	if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
		kglog.ErrorWithDataCtx(c, "Failed to bind request", map[string]interface{}{
			"error": err.String(),
		})
		response.KGError(c, err)
		return
	}

	// Validate request
	if len(req.Tokens) == 0 {
		response.BadRequestWithMsg(c, code.ParamIncorrect, "No token pairs provided")
		return
	}

	// Convert request to domain.ChainToken format
	chainTokens := make([]domain.ChainToken, 0, len(req.Tokens))
	for _, pair := range req.Tokens {
		chain := domain.IDToChain(pair.ChainID)
		if chain == nil {
			kglog.WarningWithDataCtx(ctx, "Invalid chain ID", map[string]interface{}{
				"chain_id": pair.ChainID,
			})
			continue
		}
		chainTokens = append(chainTokens, domain.ChainToken{
			Chain:   chain,
			TokenID: pair.ContractAddress,
		})
	}

	// Get prices from service
	prices, err := asset.GetPricesByContract(ctx, chainTokens)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get prices", map[string]interface{}{
			"error": err.String(),
		})
		response.KGError(c, err)
		return
	}

	// Format response
	result := make(map[string]float64)
	for token, price := range prices {
		key := token.Chain.ID() + ":" + token.TokenID
		result[key] = float64(price)
	}

	response.OK(c, result)
}
