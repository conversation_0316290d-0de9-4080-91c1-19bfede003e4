#!/bin/bash

# Check if all config variables are defined in config/*.sh
find . -name '*.go' | grep -v './cmd/dev/*' | grep -v './cmd/daemon/*' | grep -v './cmd/cron-*' | xargs grep -i -E -o 'config.Get\w+\("(\w+)"\)' | grep -Eo '".*"' | cut -d '"' -f 2 | sort | uniq | while read -r line; do

    if [[ "$line" == "CI" ]]; then
        continue
    fi

    # check whether this variable is in config/*.sh
    for f in config/*.sh; do
        if [[ "$line" == "MODE" ]]; then
            continue
        fi
        if ! grep -Eq "^export $line=" "$f"; then
            # skip test vars
            if [[ "$line" =~ ^TEST_* ]] && ! [[ "$f" =~ ^local* ]]; then
                continue
            fi
            echo "Missing config $line in $f"
            exit 1
        fi
    done

    # skip test vars
    if [[ "$line" =~ ^TEST_* ]]; then
        continue
    fi

    # check whether this variable is in deploy yaml file
    if ! grep -Eq "^- name: $line\$" gcloud/run/env.yaml.in; then
        if [[ "$line" = "MODE" ]] || [[ "$line" = "OTEL_COLLECTOR_SERVICE" ]]; then
            continue
        fi
        echo "Missing env config $line in gcloud/run/env.yaml.in"
        exit 1
    fi
done