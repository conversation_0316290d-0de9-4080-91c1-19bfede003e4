#!/bin/bash

# This script checks the currently deployed version of a Cloud Run service and compares it with the version specified in the input branch name.
# The input branch name should be in the format "release-RXX", where XX is a number.

# Extract the current deployed image URL from the json output
CURRENT_IMAGE=$(gcloud run services describe api --region asia-east1 --format json | jq -r ".spec.template.spec.containers[0].image")
echo "Current deployed image: $CURRENT_IMAGE"

# Extract the version number from the image URL
CURRENT_VERSION=$(echo "$CURRENT_IMAGE" | grep -o 'release-R[0-9]*' | cut -d'R' -f2)
echo "Current deployed version: $CURRENT_VERSION"

# Extract the version number from the GitHub branch name
BRANCH_NAME=$1
BRANCH_VERSION=$(echo "$BRANCH_NAME" | grep -o 'release-R[0-9]*' | cut -d'R' -f2)
echo "GitHub branch version: $BRANCH_VERSION"

# Compare versions and decide whether to deploy
if [[ "$CURRENT_VERSION" -gt "$BRANCH_VERSION" ]]; then
  echo "The current deployed version ($CURRENT_VERSION) is greater than the GitHub branch version ($BRANCH_VERSION). Deployment will not proceed."
  exit 1
else
  echo "The current deployed version ($CURRENT_VERSION) is less than or equal to the GitHub branch version ($BRANCH_VERSION). Deployment will proceed."
  exit 0
fi