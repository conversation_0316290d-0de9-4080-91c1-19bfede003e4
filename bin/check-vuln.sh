#!/bin/bash

set -e

REPO=asia-east1-docker.pkg.dev
KO_DOCKER_REPO_DEV="$REPO/kryptogo-wallet-app-dev/cloud-run-source-deploy"
if [ -z "$VERSION" ]; then
    VERSION=dev
fi

KO_DOCKER_REPO="${KO_DOCKER_REPO_DEV}/api"

# wait for vulnerability scan to complete
cve_json=""
resource_uri=""
MAX_RETRIES=5
retries=0
while true; do
    cve_json=$(gcloud artifacts docker images describe "$KO_DOCKER_REPO:$VERSION" --show-package-vulnerability --format json)
    resource_uri=$(echo "$cve_json" | jq -r '.discovery_summary.discovery[0].resourceUri')
    analysis_status=$(echo "$cve_json" | jq -r '.discovery_summary.discovery[0].discovery.analysisStatus')

    if [ "$analysis_status" == "FINISHED_SUCCESS" ]; then
        break
    elif [ $retries -eq $MAX_RETRIES ]; then
        echo "Maximum vulnerabilities scanning retries reached, exiting..."
        exit 1
    else
        echo "Analysis not yet completed, waiting..."
        sleep 10  # wait for 10 seconds before the next check
        ((retries++))
    fi
done

# function to filter out the ignored CVE
# ignore CVE-2023-42319, CVE-2024-38365, CVE-2025-22869 because there is no current fix version available for the package (go-ethereum)
filter_cve() {
    jq 'map(select(.noteName != "projects/goog-vulnz/notes/CVE-2023-42319" and .noteName != "projects/goog-vulnz/notes/CVE-2024-38365" and .noteName != "projects/goog-vulnz/notes/CVE-2025-22869"))'
}

# check critical vulnerabilities
exit_code=0
critical_cve=$(echo "$cve_json" | jq '.package_vulnerability_summary.vulnerabilities.CRITICAL')
if [[ "$critical_cve" != "null" ]]; then
    cve_list=$(echo "$critical_cve" | filter_cve | jq '.[].noteName')
    if [[ -n "$cve_list" && "$cve_list" != "[]" ]]; then
        echo "Critical Vulnerabilities Found:"
        echo "$cve_list" | tr -d '"' | sed 's/^/- /'
        exit_code=1
    fi
fi

# check high vulnerabilities
high_cve=$(echo "$cve_json" | jq '.package_vulnerability_summary.vulnerabilities.HIGH')
if [[ "$high_cve" != "null" ]]; then
    cve_list=$(echo "$high_cve" | filter_cve | jq '.[].noteName' )
    if [[ -n "$cve_list" && "$cve_list" != "[]" ]]; then
        echo "High Vulnerabilities Found:"
        echo "$cve_list" | tr -d '"' | sed 's/^/- /'
        exit_code=1
    fi
fi

if [[ "$exit_code" != "0" ]]; then
    echo "Vulnerabilities details: $resource_uri"
    exit 1
fi