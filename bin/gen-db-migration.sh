#!/bin/bash

set -e

# if current git workspace has uncommitted changes, exit
if [ -n "$(git status --porcelain)" ]; then
  echo "You have uncommitted changes. Please commit or stash them before running this script."
  exit 1
fi

# get current git branch
current_branch=$(git rev-parse --abbrev-ref HEAD)

# run reset DB in base branch
echo "Checkout to main branch"
git checkout main -q
git pull origin main -q
echo "Running reset DB in main branch"
go clean -testcache
go test -v ./pkg/service/rdb -run "^TestDBReset$" > /dev/null

# run reset DB in current branch
echo "Checkout back to $current_branch"
git checkout "$current_branch" -q
echo "Running reset DB in $current_branch branch"
DEBUG_DATABASE=true go test -v ./pkg/service/rdb -run "^TestDBMigrate$" > migration.log 2>&1

# final output
printf "\n====== Final DB Migration SQLs (Please review before using it):\n"
grep "rows:" ./migration.log | grep -v " SELECT " | cut -d ' ' -f 3- | sed 's/$/;/'
