import http from 'k6/http';
import { sleep } from 'k6';
// export const options = {
//     stages: [
//       { iterations: 1, vus: 10 },
//     ],
//   };
import exec from 'k6/execution';

export const options = {
    iterations: 500,
    vus: 10,
  };
const testData = [914347941,914347942,914347943,914347944,914347945,
    914347946,914347947,914347948,914347949,914347950,
]
export default function () {
        const params = {
          headers: {
            'Content-Type': 'application/json',
          },
        };

        let payload =  JSON.stringify({
            "event_id": "aama-10th-dev-001",
            "phone_number": "0"+ (testData[exec.vu.idInInstance-1]+172*exec.vu.iterationInInstance).toString()
          });

        let url = 'https://wallet-dev.kryptogo.app/v1/airdrop/receive'
      let res = http.post(url, payload, params);
      if (res.status != 200) {
          console.log("VU:"+ exec.vu.idInInstance, " response: " + res.body, "status: "+res.status, "error: "+res.error);
      }
      sleep(1);
}