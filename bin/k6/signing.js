// usage: k6 run bin/k6/signing.js
// set gcp id token in env var: export K6_AUTH_TOKEN=xxxx. Get it by running: gcloud auth print-identity-token
import http from "k6/http";
import { sleep } from "k6";
import exec from "k6/execution";

export const options = {
  iterations: 1000,
  vus: 10,
};

export default function () {
  const params = {
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + __ENV.K6_AUTH_TOKEN,
    },
  };

  let payload = JSON.stringify({
    chain_id: 137,
    transaction: {
      type: "0x0",
      nonce: "0x4ce",
      gasPrice: "0x2b579f1f12",
      maxPriorityFeePerGas: null,
      maxFeePerGas: null,
      gas: "0x30d40",
      value: "0x0",
      input:
        "0x7eb70bf7000000000000000000000000fb2866ad6caaa7731ac19c5f6961c636e204e4020000000000000000000000000000000000000000000000000000000000000001",
      v: "0x0",
      r: "0x0",
      s: "0x0",
      to: "0x659e7a2e681ae01042bbb7a9b9925c65a244ac75",
      hash: "0x5c08873157e716daabd9d5ccc0fd1b6cb5b2f967c767735b853c956f254fb3a7",
    },
  });

  let url = "https://signing-ku5zuvnrca-de.a.run.app/v1/sign_transaction";
  let res = http.post(url, payload, params);
  if (res.status != 200) {
    console.log(
      "VU:" + exec.vu.idInInstance,
      " response: " + res.body,
      "status: " + res.status,
      "error: " + res.error
    );
  }
  sleep(1);
}
