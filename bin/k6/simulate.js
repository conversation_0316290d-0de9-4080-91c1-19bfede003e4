import http from 'k6/http';
import { check, sleep } from 'k6';

var uids = [
    'xNig8rsH3Jfd1L5rvWvEIH8cWN22', // savanna
    'DtVag2GfqFVoXlDvewMPB8dRPUu1', // kordan
    'ycZFiYxcelZvJFSo9wR2fQV81MC3', // harry
    'brkdrRimTvMXoIcjFjPiPTW7gYF3', // hunter
    'xPeAwL2BGpcekirBehJng6uMzkw1', // jean
    'uEu0VCo76PO9X1A2jOYTLNAJGIp2', // dorara
    '47YaaBsEXyUTG6r972PSWvrW5JP2', // jason
    'AVQ8H0HQVabsY6f2ocBSqQyHND82', // alan
];
var uid = uids[Math.floor(Math.random() * uids.length)];
console.log(uid);

var urls = [
    // weight, url
    [137, 'https://api-ku5zuvnrca-de.a.run.app/v1/assets?chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=sol&chain_ids=btc&chain_ids=bsc&chain_ids=tron&chain_ids=kcc&include_price_histories=true&include_unverified=false&page_number=1&page_size=20&path=&types=token&types=defi&types=nft'],
    [128, 'https://api-ku5zuvnrca-de.a.run.app/v1/balances/summary?chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=sol&chain_ids=btc&chain_ids=bsc&chain_ids=tron&chain_ids=kcc&types=token&types=defi&types=nft'],
    [124, 'https://api-ku5zuvnrca-de.a.run.app/v1/balances?chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=sol&chain_ids=btc&chain_ids=bsc&chain_ids=tron&chain_ids=kcc&types=token&types=defi&types=nft'],
    [85, 'https://api-ku5zuvnrca-de.a.run.app/v1/balances/historical?chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=sol&chain_ids=btc&chain_ids=bsc&chain_ids=tron&chain_ids=kcc&interval_type=week'],
    [69, 'https://api-ku5zuvnrca-de.a.run.app/v1/nfts?chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=sol&chain_ids=btc&chain_ids=bsc&chain_ids=tron&chain_ids=kcc&page_number=1&page_size=50&path=&type=OTHERS'],
    [33, 'https://api-ku5zuvnrca-de.a.run.app/v1/txlists?chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=sol&chain_ids=btc&chain_ids=bsc&chain_ids=tron&chain_ids=kcc&page_size=20&path='],
    [27, 'https://api-ku5zuvnrca-de.a.run.app/v1/notifications/unread/count'],
    [23, 'https://api-ku5zuvnrca-de.a.run.app/v1/notifications'],

    // force_update
    // [26, 'https://api-ku5zuvnrca-de.a.run.app/v1/assets?force_update=true&chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=sol&chain_ids=btc&chain_ids=bsc&chain_ids=tron&chain_ids=kcc&include_price_histories=true&include_unverified=false&page_number=1&page_size=20&path=&types=token&types=defi&types=nft'],
    // [10, 'https://api-ku5zuvnrca-de.a.run.app/v1/txlists?force_update=true&chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=sol&chain_ids=btc&chain_ids=bsc&chain_ids=tron&chain_ids=kcc&page_size=20&path='],

    // _v/syncAssets -> force_update
    // [46, 'https://api-ku5zuvnrca-de.a.run.app/v1/assets?force_update=true&chain_ids=eth&chain_ids=matic&chain_ids=arb&chain_ids=sol&chain_ids=btc&chain_ids=bsc&chain_ids=tron&chain_ids=kcc&include_price_histories=true&include_unverified=false&page_number=1&page_size=20&path=&types=token&types=defi&types=nft'],
];
// sum url weights
var total = urls.reduce((sum, [weight, _]) => sum + weight, 0);

export default function () {
    // random pick url
    let idx = Math.floor(Math.random() * total);
    let url = '';
    for (let [weight, theURL] of urls) {
        url = theURL;
        if (idx < weight) {
            break;
        }
        idx -= weight;
    }

    // request
    const options = {
        headers: {
            'KG-WALLET-TOKEN': 'KG-DEV:123456',
            'KG-DEV-UID': uid,
        },
    };
    let res = http.get(url, options);
    check(res, {
        'status is 200': (r) => r.status === 200,
        'reponse code = 0': (r) => r.json().code === 0,
    });

    // random sleep 1~5 sec
    sleep(Math.floor(Math.random() * 5) + 1);
}
