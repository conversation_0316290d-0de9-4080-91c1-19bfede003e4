#!/bin/sh

if [ "$( docker container inspect -f '{{.State.Running}}' firebase-kg 2>/dev/null )" != "true" ]; then
		# firebase-emulator only supports linux/amd64
		docker pull --platform=linux/amd64 kryptogodev/firebase-emulator:latest
		docker run --platform=linux/amd64 --name firebase-kg \
		  --restart=unless-stopped \
			-p=8080:8080 \
			-p=4000:4000 \
			-p=9099:9099 \
			-p=9199:9199 \
			--env "GCP_PROJECT=testing-kryptogo-wallet-app" \
			--env "FIRESTORE_EMULATOR_PORT=8080" \
			--env "UI_EMULATOR_PORT=4000" \
			--env "AUTH_EMULATOR_PORT=9099" \
			--env "STORAGE_EMULATOR_PORT=9199" \
			--env "ENABLE_UI=true" \
			-d \
			kryptogodev/firebase-emulator:latest
fi
