#!/bin/bash

# Environment variables         
# MYSQL_USERNAME="your_mysql_username"
# MYSQL_PASSWORD="your_mysql_password"
SLACK_WEBHOOK_URL="*******************************************************************************"

# Log file to track previous profit
LOG_FILE="/tmp/kryptogo_profit_log.txt"        

# Ensure log file exists        
if [ ! -f "$LOG_FILE" ]; then   
  echo "0" > "$LOG_FILE"        
fi

# Function to send message to Slack                  
send_slack_message() {    
  local message="$1"            
  curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"$message\"}" "$SLACK_WEBHOOK_URL"
}         

# Main loop
while true; do
  # Step 1: Start port forwarding              
  echo "Setting up port forwarding..."
  gcloud compute ssh --zone "asia-east1-a" "otel-collector" \
      --project "kryptogo-wallet-app" \
      --ssh-key-file ~/.ssh/id_rsa_no_passphrase \
      -- -N -L 3307:10.92.224.3:3306 >/dev/null 2>&1 &
  sleep 5

  # Step 2: Run MySQL queries
  echo "Running MySQL queries..."
  PROFIT_QUERY="SELECT SUM(fee * actual_cost_usd / NULLIF(energy_rent_cost, 0) - actual_cost_usd) - SUM(CASE WHEN organization_id = 1 THEN 0 ELSE profit_margin END) as kg_org_profit FROM studio_organization_send_with_rents;"
  PROFIT_5_MIN_QUERY="SELECT SUM(fee * actual_cost_usd / NULLIF(energy_rent_cost, 0) - actual_cost_usd) - SUM(CASE WHEN organization_id = 1 THEN 0 ELSE profit_margin END) as kg_org_profit FROM studio_organization_send_with_rents WHERE created_at <= NOW() - INTERVAL 5 MINUTE
;"
   
  PROFIT_ALL_TIME=$(mysql -h 127.0.0.1 -P 3307 -u "$MYSQL_USERNAME" -p"$MYSQL_PASSWORD" wallet -sse "$PROFIT_QUERY")
  PROFIT_5_MIN=$(mysql -h 127.0.0.1 -P 3307 -u "$MYSQL_USERNAME" -p"$MYSQL_PASSWORD" wallet -sse "$PROFIT_5_MIN_QUERY")
  echo "Current profit: $PROFIT_ALL_TIME , Profit 5 minutes ago: $PROFIT_5_MIN"

  # Kill the port forwarding process
  # echo "Stopping port forwarding..."
  pkill -f "ssh.*otel-collector"

  # Step 3: Logarithmic rounding and comparison
  # echo "Calculating logarithms and comparing..."
  CURRENT_LOG_INT=$(echo "l($PROFIT_ALL_TIME)/l(1.05)" | bc -l | xargs printf "%.0f")
  PREVIOUS_LOG_INT=$(echo "l($PROFIT_5_MIN)/l(1.05)" | bc -l | xargs printf "%.0f")
  echo "Current log int: $CURRENT_LOG_INT , Previous log int: $PREVIOUS_LOG_INT"

  if [ "$CURRENT_LOG_INT" -gt "$PREVIOUS_LOG_INT" ]; then
    # echo "Profit difference detected. Logging and notifying Slack..."
    ROUNDED_PROFIT=$(printf "%.2f" "$PROFIT_ALL_TIME")
    SLACK_MESSAGE=":tada: KryptoGO is making a total profit of *$ROUNDED_PROFIT USD* as of now! Keep up the great work! :rocket:"
    send_slack_message "$SLACK_MESSAGE"
    # else
    # echo "No significant profit difference detected."
  fi

  # Wait 5 minutes before the next iteration
  # echo "Sleeping for 5 minutes..."
  sleep 295
done