#!/bin/sh

if [ "$(docker images -q liquibase/liquibase-mysql:latest 2> /dev/null)" = "" ]; then
  echo \
"FROM liquibase/liquibase
RUN lpm add mysql --global" | \
docker build \
    -t "liquibase/liquibase-mysql" --quiet -f - \
    . || exit 1
fi

if [ "$CI" = "true" ]; then
  liquibase update \
		--contexts="${ENV}" \
		--changelog-file=res/liquibase/changeset.mysql.sql \
		--url=jdbc:"mysql://${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE}" \
		--username="${MYSQL_USERNAME}" \
		--password="${MYSQL_PASSWORD}"
else
  docker run --rm --net host \
    -e MYSQL_DATABASE="${MYSQL_DATABASE}" \
    -v "$(pwd)/res/liquibase/changeset.mysql.sql:/liquibase/changelog/changeset.mysql.sql" \
    liquibase/liquibase-mysql \
    --searchPath=/liquibase/changelog \
    --contexts="${ENV}" \
    --changelog-file=changeset.mysql.sql \
    --url="jdbc:mysql://${MYSQL_HOST}:${MYSQL_PORT}/${MYSQL_DATABASE}" \
    --username="${MYSQL_USERNAME}" \
    --password="${MYSQL_PASSWORD}" \
    update
fi
