#!/bin/bash

set -e

case "$CLUSTER_ENV" in
  prod)
    export MYSQL_INSTANCE=kryptogo-wallet-app:asia-east1:prod-8
    # PROJECT_ID="kryptogo-wallet-app"
    ;;
  staging)
    export MYSQL_INSTANCE=kryptogo-wallet-app-staging:asia-east1:staging-8
    # PROJECT_ID="kryptogo-wallet-app-staging"
    ;;
  dev)
    export MYSQL_INSTANCE=kryptogo-wallet-app-dev:asia-east1:dev-8
    # PROJECT_ID="kryptogo-wallet-app-dev"
    ;;
esac

# Start the cloud_sql_proxy in the background and disown the process
export PORT="$((RANDOM%2601+3400))"
cloud_sql_proxy -instances="$MYSQL_INSTANCE=tcp:$PORT" &
proxy_pid=$!
disown # Remove it from this shell's job table, so the wait command doesn't wait for it
trap 'kill $proxy_pid' EXIT # Kill the proxy when the script exits unexpectedly

export MYSQL_DATABASE=wallet && liquibase update \
  --contexts="$CLUSTER_ENV" \
  --changelog-file=res/liquibase/changeset.mysql.sql --url="********************************************" \
  --username="root" \
  --password="$(gcloud secrets versions access latest --secret="MYSQL_PASSWORD")"

# Function to deploy a service
deploy_service() {
    local service="$1"
    local image=""
    if [[ "$service" = "kms" ]]; then
        image="$KMS_IMAGE"
    elif [[ "$service" = "signing" ]]; then
        image="$SIGNING_IMAGE"
    else
        image="$API_IMAGE"
    fi

    gcloud run services replace "gcloud/run/${service}.yaml" --region asia-east1
    gcloud run deploy "$service" --image="$image" --region asia-east1

    if [[ "$service" = "api" ]] || [[ "$service" = "dashboard" ]]; then
        echo "y" | gcloud run services set-iam-policy "$service" "gcloud/run/policy.yaml" --region asia-east1
    fi
}

# Loop over services and deploy them in parallel
services=("api" "kms" "cronjob" "signing")
pids=()
for service in "${services[@]}"; do
    (
        deploy_service "$service" > "deploy_${service}.log" 2>&1
    ) &
    pids+=("$!")
done

# Check the exit status of each job
failed_services=()
for i in "${!pids[@]}"; do
    if ! wait "${pids[$i]}"; then
        failed_services+=("${services[$i]}")
    fi
done

# Output logs for failed services and exit if any failed
if [ ${#failed_services[@]} -ne 0 ]; then
    echo "Deployment failed for the following services:"
    for service in "${failed_services[@]}"; do
        echo "===== Start deploy_${service}.log ====="
        cat "deploy_${service}.log"
        echo "===== End deploy_${service}.log ====="
        rm "deploy_${service}.log"
    done
    exit 1
fi
