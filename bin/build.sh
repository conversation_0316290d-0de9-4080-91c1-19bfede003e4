#!/bin/bash

set -e

REPO=asia-east1-docker.pkg.dev
DOCKER_REPO_DEV="$REPO/kryptogo-wallet-app-dev/cloud-run-source-deploy"
DOCKER_REPO_PROD="$REPO/kryptogo-wallet-app/cloud-run-source-deploy"
if [ -z "$VERSION" ]; then
    VERSION=dev
fi

echo "Y" | gcloud auth configure-docker "$REPO"

mkdir -p build
RAND_ID=$(openssl rand -hex 12)
BUILD_CONTAINER_NAME="temp-build-$RAND_ID"

# Define the services and their paths
services=(
    "api:."
    "api-kms:./cmd/api-kms"
    "api-signing:./cmd/api-signing"
)

# Reuse the build container
docker run -d --name "$BUILD_CONTAINER_NAME" --platform=linux/amd64 \
    -v "$(go env GOPATH)/pkg/mod":"/go/pkg/mod" \
    -v "$(go env GOCACHE)":"/root/.cache/go-build" \
    -v "${PWD}":"/go/src/app" \
    -w "/go/src/app" \
    "kryptogodev/golang-build-base:latest" tail -f /dev/null

# Build and copy the binaries
for service in "${services[@]}"; do
    IFS=":" read -r name build_path <<< "$service"
    
    # Build and copy the binary to host
    docker exec "$BUILD_CONTAINER_NAME" sh -c "go mod download && go build -o /go/build/$name -buildvcs=false $build_path"
    docker cp "$BUILD_CONTAINER_NAME:/go/build/$name" "./build/$name"
done

docker rm -f "$BUILD_CONTAINER_NAME"

# Build and push images
for service in "api" "api-kms" "api-signing"; do
    if [[ "$service" == "api-kms" ]]; then
        IMAGE_NAME="kms"
    elif [[ "$service" == "api-signing" ]]; then
        IMAGE_NAME="signing"
    else
        IMAGE_NAME="api"
    fi
    docker build --platform linux/amd64 --build-arg CMD_NAME="$service" -t "${DOCKER_REPO_DEV}/$IMAGE_NAME:latest" -t "${DOCKER_REPO_DEV}/$IMAGE_NAME:${VERSION}" .
    docker push "${DOCKER_REPO_DEV}/$IMAGE_NAME:latest"
    docker push "${DOCKER_REPO_DEV}/$IMAGE_NAME:${VERSION}"
done

if [ -n "$IN_BUILDBOT" ]; then
    for image in api kms signing; do
        docker pull "${DOCKER_REPO_DEV}/${image}:${VERSION}"

        docker tag "${DOCKER_REPO_DEV}/${image}:${VERSION}" "${DOCKER_REPO_PROD}/${image}:${VERSION}"
        docker tag "${DOCKER_REPO_PROD}/${image}:${VERSION}" "${DOCKER_REPO_PROD}/${image}:latest"

        docker push "${DOCKER_REPO_PROD}/${image}:${VERSION}"
        docker push "${DOCKER_REPO_PROD}/${image}:latest"
    done
fi