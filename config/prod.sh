#!/bin/sh

# general
export ENV=prod
export APP_PORT=8080
export CORS_ALLOW_ORIGINS='["https://wallet.kryptogo.com","https://dashboard.kryptogo.com","https://www.kryptogo.com","chrome-extension://bgaihnkooadagpjddlcaleaopmkjadfl","https://accounts.kryptogo.com","https://studio.kryptogo.com","https://pwa.kryptogo.com","https://store.kryptogo.com","https://sdk-doc.kryptogo.com","https://www.kryptogo.xyz","https://app.kryptogo.xyz","https://sweep.kryptogo.xyz","http://localhost:3000","https://pay.kryptogo.com"]'
export PROJECT_ID="kryptogo-wallet-app"
export REGION=asia-east1

# database
export MYSQL_HOST=***********
export MYSQL_PORT=3306
export MYSQL_USERNAME=root
export MYSQL_PASSWORD=REDACTED
export MYSQL_OPTIONS="charset=utf8mb4&parseTime=True&loc=UTC"
export MYSQL_DATABASE=wallet
export MYSQL_SLOW_THRESHOLD=2000
export MYSQL_DSN="root:__SECRETMANAGER:MYSQL_PASSWORD__@tcp(***********:3306)/wallet?charset=utf8mb4&parseTime=True&loc=UTC"
export DEBUG_DATABASE=false

# redis
export REDIS_HOST=***********
export REDIS_PORT=6379
export REDIS_AUTH=REDACTED

# GCP
export CDN_HOST="https://wallet-static.kryptogo.com/"
export GS_BUCKET_NAME="kryptogo-wallet-app.appspot.com"
export FIREBASE_API_KEY=REDACTED
export IOS_RECAPTCHA_SITE_KEY=REDACTED
export ANDROID_RECAPTCHA_SITE_KEY=REDACTED
export WEB_RECAPTCHA_SITE_KEY=REDACTED

# GOOGLE OAUTH
export GOOGLE_OAUTH_CLIENT_ID="307061773427-9urr020jhkqfn146kp4r023k8i74gr2t.apps.googleusercontent.com"
export GOOGLE_OAUTH_CLIENT_SECRET=REDACTED

# auth
export JWT_SECRET_KEY=REDACTED
export STUDIO_JWT_SECRET_KEY=REDACTED
export ACCESS_TOKEN_EXP_MINUTES=1440
_OAUTH_CLIENTS="$(cat config/$ENV/oauth-clients.json)"
export OAUTH_CLIENTS="$_OAUTH_CLIENTS"
export KG_TOKEN_SECRET=REDACTED

# stickey
export STICKEY_INTERNAL_HOST="https://api-6aylhsna7q-de.a.run.app"

# signing
export CLEF_HOST="http://**********"
export CLEF_PORT_MATIC=8550
export CLEF_PORT_POLYGON=8550
export CLEF_PORT_GOERLI=8551
export CLEF_PORT_MUMBAI=8552
## signing-wallet
export KG_EVM_PRIVATE_KEY=REDACTED
export KG_EVM_ADDRESS=******************************************
export KG_TRON_PRIVATE_KEY=REDACTED
export KG_TRON_ADDRESS=TSSH8dtg57tjJPaXQgpncbsmWHwfAjArUR
export TAG_EVM_PRIVATE_KEY=REDACTED
export TAG_EVM_ADDRESS=******************************************
export TAG_LOCALHOST_PRIVATE_KEY=
export TAG_LOCALHOST_ADDRESS=
export TAG_TRON_PRIVATE_KEY=REDACTED
export TAG_TRON_ADDRESS=TQC1dFNKcpvCaszkjQtpAKWxvyperBi8SF

# sms
export TWILIO_ACCOUNT_SID="REDACTED"
export TWILIO_AUTH_TOKEN="REDACTED"
export TWILIO_SENDER="+***********"
export ENABLE_TWILIO_VERIFY=true
export TWM_USERNAME="VCSN061100"
export TWM_PASSWORD="REDACTED"
export TWM_SRC_ADDR="8706154"
_SMS_TESTERS="$(cat config/$ENV/sms-tester.json)"
export SMS_TESTERS="$_SMS_TESTERS"
_SMS_QA="$(cat config/$ENV/sms-qa.json)"
export SMS_QA="$_SMS_QA"
_DEVELOPER_PHONES="$(cat config/$ENV/developer-phone.json)"
export DEVELOPER_PHONES="$_DEVELOPER_PHONES"
_SMS_WHITELIST="$(cat config/sms-whitelist.json)"
export SMS_WHITELIST="$_SMS_WHITELIST"

# email
export SENDGRID_API_KEY=REDACTED
_EMAIL_TESTERS="$(cat config/$ENV/email-tester.json)"
export EMAIL_TESTERS="$_EMAIL_TESTERS"
_EMAIL_QA="$(cat config/$ENV/email-qa.json)"
export EMAIL_QA="$_EMAIL_QA"
_PM_EMAILS="$(cat config/$ENV/email-pm.json)"
export PM_EMAILS="$_PM_EMAILS"

# instant messaging
export SENDBIRD_KRYPTOGO_API_TOKEN=REDACTED
export SENDBIRD_STICKEY_API_TOKEN=REDACTED
export SENDBIRD_SESSION_TOKEN_TTL_IN_MINUTES=10080

# host
export DYNAMIC_LINK_HOST="https://kryptogo.page.link"
export COMPLIANCE_HOST="https://api.kryptogo.com"
export SELF_HOST="https://wallet.kryptogo.app"
export SELF_INTERNAL_HOST="https://api-q4nbern77q-de.a.run.app"
export ACCOUNT_HOST="https://accounts.kryptogo.com"
export STUDIO_HOST="https://studio.kryptogo.com"
export PWA_HOST="https://pwa.kryptogo.com"

# 3rd party API
export ALCHEMY_WEBHOOK_ID_MAP='{"eth":"wh_bvxql8o6yp2prmpw","matic":"wh_xsrpxxi9nvw0nsp1","arb":"wh_7km9hfthk37akd60","bsc":"wh_kntlyttbnsonppo0","base":"wh_mpkx6ik0wrecw6yq","optimism":"wh_oe9o76ewcjh3nsyb"}'
export ALCHEMY_API_KEY=REDACTED
export STICKEY_ALCHEMY_API_KEY=REDACTED
export ALCHEMY_TOKEN=REDACTED
export ARBISCAN_API_KEY=REDACTED
export STICKEY_ARBISCAN_API_KEY=REDACTED
export BINANCE_API_KEY=REDACTED
export BINANCE_SECRET_KEY=REDACTED
export BLOCKCHAIR_API_KEY=REDACTED
export BSCSCAN_API_KEY=REDACTED
export STICKEY_BSCSCAN_API_KEY=REDACTED
export COVALENT_API_KEY=REDACTED
export ENCRYPTION_KEY=REDACTED
export ETHERSCAN_API_KEY=REDACTED
export STICKEY_ETHERSCAN_API_KEY=REDACTED
export INFURA_PROJECT_ID=REDACTED
export STICKEY_INFURA_PROJECT_ID=REDACTED
export JUMIO_API_KEY=REDACTED
export KCC_API_KEY=REDACTED
export MORALIS_API_KEY=REDACTED
export OPENSEA_API_KEY=REDACTED
export POLYGONSCAN_API_KEY=REDACTED
export STICKEY_POLYGONSCAN_API_KEY=REDACTED
export STRIPE_SECRET_KEY=REDACTED
export STRIPE_WEBHOOK_SECRET=REDACTED
export ZERION_API_KEY=REDACTED
export ZERION_API_KEY_V2=REDACTED
export SOLSCAN_API_KEY=REDACTED
export SOLSCAN_API_KEY_V2=REDACTED
export POAP_API_KEY=REDACTED
export BLOWFISH_API_KEY=REDACTED
export COINGECKO_API_KEY=REDACTED
export TRANSPOSE_API_KEY=REDACTED
export CRYSTAL_API_KEY=REDACTED
export TRONSCAN_PRO_API_KEY=REDACTED
export FEEE_API_KEY=REDACTED
export TRONGRID_API_KEY=REDACTED
export QUICKNODE_API_KEY=REDACTED
export QUICKNODE_API_KEY_V2=REDACTED
export SCAM_SNIFFER_API_KEY=REDACTED
export OPSCAN_API_KEY=REDACTED
export BASESCAN_API_KEY=REDACTED

# KYC
export COMPLIANCE_API_KEY=REDACTED
export DD_TASK_SEARCH_SETTING_ID_TW=1
export DD_TASK_SEARCH_SETTING_ID_EN=88
export COMPLIANCE_SHARED_SECRET=REDACTED

# dashboard and nft event
export DASHBOARD_WHITELIST="0xCfa8DB790B7d1176674cBc0D245C8f783784f7A3,0x7A4a5EEee0dbad2ADAB6dC83F495143184349850"
export WATCHING_NFT_CONTRACT_ADDRESS=""
export AIRDROP_EVENT_SETTING='{"shawarma-monsters": {"ttl": "", "contract_schema_name": "ERC721"}}'
export AIRDROP_SHEET_INFO='{"shawarma-monsters":[{"spreadsheet_id": "1IfOzqgALSDc7fZP2LwLklu5J0Bshj6gEarBD89_jq2k", "token_id": 0, "sheet_name": "Google Sheet", "column_phone": 0, "column_hash": 1, "action": "airdrop"},{"spreadsheet_id": "1LU6KEMS7A61B6r6625CBP49A3z8wyM0NyBjqSCaEnv4", "token_id": 0, "sheet_name": "Sheet1", "column_phone": 1, "column_hash": 2, "action": "airdrop"}]}'

# buy crypto
export SLACK_WEBHOOK_URL="*******************************************************************************"
export ORDER_ENABLED=false
export STRIPE_ENABLED=true
export BINANCE_ENABLED=true
export BUY_CRYPTO_URL="https://wallet.kryptogo.com/buy-crypto"
export ORDER_DETAIL_URL="https://wallet.kryptogo.com/order-history"
export SINGLE_ORDER_LOWER_LIMIT=100
export WEEK_ORDER_UPPER_LIMIT=1000
export LARGE_TRANSACTION_AMOUNT=500

# Open telemetry tracing
export OTEL_COLLECTOR_HOST=**********
export OTEL_COLLECTOR_PORT=4317
export OTEL_COLLECTOR_SERVICE=api

# For deployment
export CLUSTER_ENV=prod
export SERVICE_ACCOUNT_EMAIL=<EMAIL>
export KMS_SERVICE_ACCOUNT_EMAIL=<EMAIL>
export API_IMAGE=asia-east1-docker.pkg.dev/kryptogo-wallet-app-dev/cloud-run-source-deploy/api:"$VERSION"
export KMS_IMAGE=asia-east1-docker.pkg.dev/kryptogo-wallet-app-dev/cloud-run-source-deploy/kms:"$VERSION"
export SIGNING_IMAGE=asia-east1-docker.pkg.dev/kryptogo-wallet-app-dev/cloud-run-source-deploy/signing:"$VERSION"

# studio
export WEB_HOST=https://wallet.kryptogo.com
export KGSTORE_BASE_URL=https://store.kryptogo.com

# studio line bot
export LINE_ACCOUNT_LINK_LOGIN_URL="$ACCOUNT_HOST/auth/login"
export LINE_BOT_CALLBACK_URL="$SELF_HOST/v1/studio/line_bot/callback"

# proxy server
export PROXY_SERVERS='["**************:50100","**************:50100","*************:50100","**************:50100","*************:50100","**************:50100","**************:50100"]'
export PROXY_USERNAME=devjzdb
export PROXY_PASSWORD=REDACTED

# gas swap service
export GAS_SWAP_CONTRACTS='{"tron":"TRzh2k4psPBbQrK4Xv3iWSF4KZcU9QLgjW"}'
export GAS_SWAP_FEE_PERCENT=10

# gasless send service
export GASLESS_SEND_CONTRACTS='{"tron":"TUczsU3C9wxeZukXCGtUaCUzEif1nSBzb4"}'

# ephemeral note service
export EPHEMERAL_NOTES_CONTRACTS='{"arb":"******************************************","tron":"TWGcz41pu4WKuuS4NXE6opDXFLc6mtfexY"}'
export EPHEMERAL_NOTES_FEES='{"arb":"0.1","tron":"3.5"}'

# send with fee service
export SEND_WITH_FEE_CONTRACTS='{"tron":"TCxUypP7WpQYsCHEj6k3eXB2mL5ToFf1R7"}'

# cloud task
export CLOUD_TASK_API_QUEUE=api-async-tasks-queue
export CLOUD_TASK_TX_WATCH_QUEUE=watch-tx-task-queue
# user repo implementation
export USER_REPO=only_mysql

# Asset Price Update frequrency parameters
export ASSET_PRICE_UPDATE_JOB_BLANK_SEC=10
export ASSET_PRICE_UPDATE_JOB_REGULAR_SEC=300

# chain sync service
export CLOUD_TASK_CHAIN_SYNC_QUEUE=chain-sync-task-queue

# tron energy recharge
export TRON_ENERGY_RECHARGE_THRESHOLD=200
export TRON_ENERGY_RECHARGE_AMOUNT=1000

# binance pay
export BINANCE_PAY_SOURCE=kryptogo
export BINANCE_PAY_PRIVATE_KEY=REDACTED
export BINANCE_PAY_REDIRECT_URL=kryptogo://deposit

# send link campaign
export SEND_LINK_CAMPAIGN_ENABLED=false

# payment
export PAYMASTER_CONTRACTS='{"arb":"******************************************","bsc":"******************************************","matic":"******************************************","eth":"******************************************","base":"******************************************","optimism":"******************************************"}'
export ACCOUNT_FACTORY_ADDRESS='******************************************'

# okx
export OKX_ACCESS_KEY=REDACTED
export OKX_SECRET_KEY=REDACTED
export OKX_PASSPHRASE=REDACTED

# kryptogo.xyz
export XYZ_TRADING_FEE=0.005
export XYZ_TRADING_VOLUME_PER_CREDIT=500
export XYZ_CREDIT_SINGLE_PRICE=0.01
export XYZ_CREDIT_10_PACKAGE_PRICE=0.008
export XYZ_CREDIT_50_PACKAGE_PRICE=0.0065

# unset local emulator env
unset FIRESTORE_EMULATOR_HOST
unset FIREBASE_AUTH_EMULATOR_HOST
unset FIREBASE_STORAGE_EMULATOR_HOST
unset STORAGE_EMULATOR_HOST

# bridge
export BRIDGE_API_KEY=REDACTED
export BRIDGE_API_URL="https://api.bridge.xyz"