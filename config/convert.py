def convert_script_to_env(input_file, output_file):
    with open(input_file, 'r') as f:
        lines = f.readlines()

    with open(output_file, 'w') as f:
        for line in lines:
            # Remove lines starting with "#!" or "export "
            if line.startswith("#!/"):
                continue
            elif line.startswith("export "):
                line = line.replace("export ", "")
            if line.startswith("TEST_SIGNING_HOST="):
                line = "TEST_SIGNING_HOST=signing\n"
            if line.startswith("TEST_SIGNING_PORT="):
                line = "TEST_SIGNING_PORT=8080\n"
            if line.startswith("TEST_KMS_HOST="):
                line = "TEST_KMS_HOST=kms\n"
            if line.startswith("TEST_KMS_PORT="):
                line = "TEST_KMS_PORT=8080\n"
            line = line.replace("config/$ENV-ci/", "")
            line = line.replace("config/$ENV/", "")
            line = line.replace("config/", "")

            # Convert escaped ampersand sequences
            line = line.replace("\\&", "&")

            f.write(line)


if __name__ == "__main__":
    convert_script_to_env("local-ci.sh", ".env")
