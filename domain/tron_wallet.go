package domain

import (
	"context"
	"crypto/ecdsa"
	"crypto/sha256"
	"fmt"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/gotron-sdk/pkg/address"
	"github.com/kryptogo/gotron-sdk/pkg/proto/core"
	"google.golang.org/protobuf/proto"
)

// TronWallet contains the tron wallet private key and address
type TronWallet struct {
	EncryptedPrivateKey string
	Address             address.Address
	privateKey          *ecdsa.PrivateKey
}

// NewTronWallet creates a new tron wallet
func NewTronWallet(encryptedPrivateKey string, privateKey *ecdsa.PrivateKey) *TronWallet {
	address := address.PubkeyToAddress(privateKey.PublicKey)
	return &TronWallet{
		EncryptedPrivateKey: encryptedPrivateKey,
		Address:             address,
		privateKey:          privateKey,
	}
}

// NewRandomTronWallet creates a new random tron wallet
func NewRandomTronWallet(ctx context.Context, e PrivateKeyEncryptor) (*TronWallet, error) {
	privKey, err := crypto.GenerateKey()
	if err != nil {
		return nil, err
	}
	encryptedPrivateKey, err := e.Encrypt(ctx, privKey)
	if err != nil {
		return nil, err
	}
	return NewTronWallet(encryptedPrivateKey, privKey), nil
}

// SignTransaction signs a transaction
func (t *TronWallet) SignTransaction(tx *core.Transaction) (*core.Transaction, error) {
	rawData, err := proto.Marshal(tx.GetRawData())
	if err != nil {
		return nil, fmt.Errorf("proto marshal tx raw data error: %v", err)
	}
	h256h := sha256.New()
	h256h.Write(rawData)
	hash := h256h.Sum(nil)
	signature, err := crypto.Sign(hash, t.privateKey)
	if err != nil {
		return nil, err
	}
	tx.Signature = append(tx.Signature, signature)
	return tx, nil
}

// TronAccountResource holds the resource information for a Tron account
type TronAccountResource struct {
	FreeNetLimit int64
	FreeNetUsed  int64
	NetLimit     int64
	NetUsed      int64
	EnergyLimit  int64
	EnergyUsed   int64
}

type TronGasInfo struct {
	// How much energy used for the transaction
	EnergyUsed int64
	// How much more energy to rent for the transaction
	EnergyToRent int64
	// Energy fee per unit of energy
	EnergyFee int64
	// Transaction size
	TxSize int64
	// Bandwidth fee per unit of bandwidth
	BandwidthFee int64
	// Bandwidth cost. 0 if freeNetRemaining is enough, otherwise it's TxSize * BandwidthFee
	BandwidthCost int64
	// Recipient account exists or not
	AccountExists *bool
	// Account creation fee
	AccountCreationFee *int64
	// Total cost in Sun
	TotalCost int64
}
