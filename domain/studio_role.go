package domain

import (
	"context"
	"errors"
	"strings"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// StudioRole is a role of studio organization module
type StudioRole struct {
	Module string
	Name   string
}

// StudioRoleBinding defines the role binding of the user in the organization.
type StudioRoleBinding struct {
	OrganizationID int
	UID            string
	StudioRole
}

// String returns the string representation of StudioRole.
func (role StudioRole) String() string {
	if role.Name == "owner" {
		return role.Name
	}

	return role.Module + ":" + role.Name
}

// ParseStudioRole parses the string representation of StudioRole.
func ParseStudioRole(role string) (StudioRole, error) {
	if role == "owner" {
		return StudioRole{
			Name: role,
		}, nil
	}

	moduleRole := strings.Split(role, ":")
	if len(moduleRole) != 2 {
		return StudioRole{}, errors.New("invalid role")
	}

	return StudioRole{
		Module: moduleRole[0],
		Name:   moduleRole[1],
	}, nil
}

// GetRoleBindingsRequest defines the request for GetRoleBindings.
type GetRoleBindingsRequest struct {
	OrgID int
	UID   string
	Roles []StudioRole
}

// SaveUserRolesRequest defines the request for SaveUserRoles.
type SaveUserRolesRequest struct {
	OrgID int
	UID   string
	Roles []StudioRole
}

// StudioRoleRepo defines the interface for studio role.
type StudioRoleRepo interface {
	GetRoleBindings(ctx context.Context, req GetRoleBindingsRequest) ([]StudioRoleBinding, *code.KGError)
	SaveUserRoles(ctx context.Context, req SaveUserRolesRequest) *code.KGError
}

// StudioRoleCacheRepo defines the interface for studio role cache.
type StudioRoleCacheRepo interface {
	GetRolesByUser(ctx context.Context, orgID int, userID string) ([]StudioRole, *code.KGError)
	SetRoles(ctx context.Context, roleBindings []StudioRoleBinding) *code.KGError
	SetUserRoles(ctx context.Context, orgID int, uid string, roles []StudioRole) *code.KGError
}
