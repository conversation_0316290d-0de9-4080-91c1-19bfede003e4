//go:generate go-enum
package domain

import (
	"errors"

	"github.com/shopspring/decimal"
)

// LiquidityType represents liquidity type
// ENUM(buy_crypto, gas_swap)
type LiquidityType string

func (l LiquidityType) ToProfitRateServiceType() ProfitRateServiceType {
	switch l {
	case LiquidityTypeBuyCrypto:
		return ProfitRateServiceTypeBuy
	case LiquidityTypeGasSwap:
		return ProfitRateServiceTypeSwapGas
	}

	return ""
}

// AssetProLiquidity represents asset pro liquidity
type AssetProLiquidity struct {
	OrganizationID  int
	LiquidityType   LiquidityType
	ChainID         string
	ContractAddress *string
	Symbol          string
	TokenURL        string
	Price           float64
	Unit            string
	AlertThreshold  *decimal.Decimal
}

// ProfitRateServiceType represents type of profit rate service
// ENUM(buy, swap_gas, swap_defi, send_with_fee, send_gasless, send_batch, bridge)
type ProfitRateServiceType string

// ToLiquidityType converts ProfitRateServiceType to LiquidityType
func (s ProfitRateServiceType) ToLiquidityType() LiquidityType {
	switch s {
	case ProfitRateServiceTypeBuy:
		return LiquidityTypeBuyCrypto
	case ProfitRateServiceTypeSwapGas:
		return LiquidityTypeGasSwap
	}

	return ""
}

// AssetProProfitRate represents asset pro profit rate
type AssetProProfitRate struct {
	Service          ProfitRateServiceType
	ProfitRate       decimal.Decimal
	ProfitShareRatio decimal.Decimal
}

func (p *AssetProProfitRate) GetKgMinimumRevenueRate() decimal.Decimal {
	switch p.Service {
	case ProfitRateServiceTypeBridge:
		return decimal.NewFromFloat(0.003)
	case ProfitRateServiceTypeBuy, ProfitRateServiceTypeSwapGas, ProfitRateServiceTypeSwapDefi:
		return decimal.NewFromFloat(0.0015)
	case ProfitRateServiceTypeSendWithFee, ProfitRateServiceTypeSendGasless, ProfitRateServiceTypeSendBatch:
		return decimal.NewFromFloat(0.1)
	}

	return decimal.Zero
}

// Any amount should subtract kg minimum revenue first, then proceed to other calculation
func (p *AssetProProfitRate) GetKgMinimumRevenue(amount *decimal.Decimal, gasAmount *decimal.Decimal) (decimal.Decimal, error) {
	switch p.Service {
	case ProfitRateServiceTypeBuy, ProfitRateServiceTypeSwapGas, ProfitRateServiceTypeSwapDefi, ProfitRateServiceTypeBridge:
		if amount == nil {
			return decimal.Zero, errors.New("amount is required")
		}
		return amount.Mul(p.GetKgMinimumRevenueRate()), nil
	case ProfitRateServiceTypeSendWithFee, ProfitRateServiceTypeSendGasless, ProfitRateServiceTypeSendBatch:
		if gasAmount == nil {
			return decimal.Zero, errors.New("gas amount is required")
		}
		return gasAmount.Mul(p.GetKgMinimumRevenueRate()), nil
	}

	return decimal.Zero, errors.New("invalid service type: " + string(p.Service))
}
