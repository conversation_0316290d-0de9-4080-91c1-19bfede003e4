// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// NotificationContentTypeText is a NotificationContentType of type text.
	NotificationContentTypeText NotificationContentType = "text"
	// NotificationContentTypeHtml is a NotificationContentType of type html.
	NotificationContentTypeHtml NotificationContentType = "html"
)

var ErrInvalidNotificationContentType = errors.New("not a valid NotificationContentType")

// String implements the Stringer interface.
func (x NotificationContentType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x NotificationContentType) IsValid() bool {
	_, err := ParseNotificationContentType(string(x))
	return err == nil
}

var _NotificationContentTypeValue = map[string]NotificationContentType{
	"text": NotificationContentTypeText,
	"html": NotificationContentTypeHtml,
}

// ParseNotificationContentType attempts to convert a string to a NotificationContentType.
func ParseNotificationContentType(name string) (NotificationContentType, error) {
	if x, ok := _NotificationContentTypeValue[name]; ok {
		return x, nil
	}
	return NotificationContentType(""), fmt.Errorf("%s is %w", name, ErrInvalidNotificationContentType)
}

const (
	// NotificationLinkTypeDeepLink is a NotificationLinkType of type deep_link.
	NotificationLinkTypeDeepLink NotificationLinkType = "deep_link"
	// NotificationLinkTypeSystem is a NotificationLinkType of type system.
	NotificationLinkTypeSystem NotificationLinkType = "system"
	// NotificationLinkTypeDapp is a NotificationLinkType of type dapp.
	NotificationLinkTypeDapp NotificationLinkType = "dapp"
)

var ErrInvalidNotificationLinkType = errors.New("not a valid NotificationLinkType")

// String implements the Stringer interface.
func (x NotificationLinkType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x NotificationLinkType) IsValid() bool {
	_, err := ParseNotificationLinkType(string(x))
	return err == nil
}

var _NotificationLinkTypeValue = map[string]NotificationLinkType{
	"deep_link": NotificationLinkTypeDeepLink,
	"system":    NotificationLinkTypeSystem,
	"dapp":      NotificationLinkTypeDapp,
}

// ParseNotificationLinkType attempts to convert a string to a NotificationLinkType.
func ParseNotificationLinkType(name string) (NotificationLinkType, error) {
	if x, ok := _NotificationLinkTypeValue[name]; ok {
		return x, nil
	}
	return NotificationLinkType(""), fmt.Errorf("%s is %w", name, ErrInvalidNotificationLinkType)
}

const (
	// NotificationMessageTypeTransaction is a NotificationMessageType of type transaction.
	NotificationMessageTypeTransaction NotificationMessageType = "transaction"
	// NotificationMessageTypeAnnouncement is a NotificationMessageType of type announcement.
	NotificationMessageTypeAnnouncement NotificationMessageType = "announcement"
	// NotificationMessageTypeSystem is a NotificationMessageType of type system.
	NotificationMessageTypeSystem NotificationMessageType = "system"
)

var ErrInvalidNotificationMessageType = errors.New("not a valid NotificationMessageType")

// String implements the Stringer interface.
func (x NotificationMessageType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x NotificationMessageType) IsValid() bool {
	_, err := ParseNotificationMessageType(string(x))
	return err == nil
}

var _NotificationMessageTypeValue = map[string]NotificationMessageType{
	"transaction":  NotificationMessageTypeTransaction,
	"announcement": NotificationMessageTypeAnnouncement,
	"system":       NotificationMessageTypeSystem,
}

// ParseNotificationMessageType attempts to convert a string to a NotificationMessageType.
func ParseNotificationMessageType(name string) (NotificationMessageType, error) {
	if x, ok := _NotificationMessageTypeValue[name]; ok {
		return x, nil
	}
	return NotificationMessageType(""), fmt.Errorf("%s is %w", name, ErrInvalidNotificationMessageType)
}
