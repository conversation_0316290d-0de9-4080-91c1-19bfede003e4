//go:generate go-enum
package domain

import (
	"time"
)

// UniversalSwapStatus represents status of universal swap
// ENUM(pending, success, failed)
type UniversalSwapStatus string

// SourceTxStatus represents status of source transaction
// ENUM(init, broadcasted, confirmed, failed)
type SourceTxStatus string

// SourceTransaction represents a source transaction in universal swap
type SourceTransaction struct {
	Chain      Chain
	From       Address
	To         Address
	TokenID    string
	RawAmount  string
	SignedTx   string
	PermitData *PermitData
	TxHash     string
	Status     SourceTxStatus
	FundsSent  bool // true if the funds are sent to the destination address
}

// PermitData represents EIP-2612 permit data for token approvals
type PermitData struct {
	Owner    EvmAddress
	Spender  EvmAddress
	Value    string
	Deadline string
	R        string
	S        string
	V        string
}

// Destination represents the destination of universal swap
type Destination struct {
	Chain             Chain
	WalletAddress     Address
	TokenID           string
	ReceivedRawAmount *string
}

// SponsorTransaction represents a sponsor transaction in universal swap
type SponsorTransaction struct {
	Chain     Chain
	From      Address
	To        Address
	RawAmount string
	TxHash    string
	Status    SourceTxStatus
}

// UniversalSwap represents a universal swap request
type UniversalSwap struct {
	ID                  int
	UID                 string
	SourceTransactions  []*SourceTransaction
	Destination         *Destination
	Status              UniversalSwapStatus
	SponsorTransactions []*SponsorTransaction
	FeeRate             float64
	EstimatedFinishAt   time.Time
	RetryCount          int
	CreatedAt           time.Time
}

// UpdateUniversalSwapRequest is used to update universal swap request's status in repo
type UpdateUniversalSwapRequest struct {
	ID                int
	Source            map[string]*UpdateUniversalSwapSourceTx
	Sponsor           map[string]*SourceTxStatus
	ReceivedRawAmount *string
	RetryCount        *int
	Status            *UniversalSwapStatus
}

// UpdateUniversalSwapSourceTx is used to update source transaction's status in repo
type UpdateUniversalSwapSourceTx struct {
	Status    *SourceTxStatus
	FundsSent *bool
	TxHash    *string // For updating the transaction hash when it was unknown at creation time
}
