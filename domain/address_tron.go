package domain

import (
	"crypto/sha256"
	"fmt"

	"github.com/ethereum/go-ethereum/common"
	"github.com/shengdoushi/base58"
)

type TronAddress struct {
	common.Address
}

const tronPrefixMainnet = 0x41

func (a TronAddress) String() string {
	input := append([]byte{tronPrefixMainnet}, a.Bytes()...)

	h256h0 := sha256.New()
	h256h0.Write(input)
	h0 := h256h0.Sum(nil)
	h256h1 := sha256.New()
	h256h1.Write(h0)
	h1 := h256h1.Sum(nil)

	inputCheck := append(input, h1[:4]...)
	return base58.Encode(inputCheck, base58.BitcoinAlphabet)
}

func (a TronAddress) Equal(b Address) bool {
	if b, ok := b.(TronAddress); ok {
		return a.Address == b.Address
	}
	return false
}

func (a TronAddress) MarshalText() ([]byte, error) {
	return []byte(a.String()), nil
}

func (a TronAddress) IsEmpty() bool {
	return a.Address == common.Address{}
}

func NewTronAddress(address string) TronAddress {
	decodeData, err := decodeBase58Check(address)
	if err == nil {
		return TronAddress{common.BytesToAddress(decodeData)}
	}

	// Check if address is raw hex (starts with '41' and is 42 characters long)
	if len(address) == 42 && address[:2] == "41" {
		return TronAddress{common.HexToAddress("0x" + address[2:])}
	}

	// Check if address is hex (with or without '0x' prefix)
	if common.IsHexAddress(address) {
		return TronAddress{common.HexToAddress(address)}
	}

	return TronAddress{}
}

func decodeBase58Check(input string) ([]byte, error) {
	decodeCheck, err := base58.Decode(input, base58.BitcoinAlphabet)
	if err != nil {
		return nil, err
	}

	// tron address should should have 20 bytes + 4 checksum + 1 Prefix
	if len(decodeCheck) != 25 {
		return nil, fmt.Errorf("invalid address length: %d", len(decodeCheck))
	}

	// check prefix
	if decodeCheck[0] != tronPrefixMainnet {
		return nil, fmt.Errorf("invalid prefix")
	}

	decodeData := decodeCheck[:len(decodeCheck)-4]

	h256h0 := sha256.New()
	h256h0.Write(decodeData)
	h0 := h256h0.Sum(nil)

	h256h1 := sha256.New()
	h256h1.Write(h0)
	h1 := h256h1.Sum(nil)

	if h1[0] == decodeCheck[len(decodeData)] &&
		h1[1] == decodeCheck[len(decodeData)+1] &&
		h1[2] == decodeCheck[len(decodeData)+2] &&
		h1[3] == decodeCheck[len(decodeData)+3] {

		return decodeData[1:], nil
	}

	return nil, fmt.Errorf("b58 check error")
}
