package domain

import (
	"context"
	"time"
)

type RateLimitParams struct {
	Rate   int
	Burst  int
	Period time.Duration
}

type RateLimiterResult struct {
	// Allowed is the number of events that may happen at time now.
	Allowed int

	// Remaining is the maximum number of requests that could be
	// permitted instantaneously for this key given the current
	// state. For example, if a rate limiter allows 10 requests per
	// second and has already received 6 requests for this key this
	// second, Remaining would be 4.
	Remaining int

	// RetryAfter is the time until the next request will be permitted.
	// It should be -1 unless the rate limit has been exceeded.
	RetryAfter time.Duration

	// ResetAfter is the time until the RateLimiter returns to its
	// initial state for a given key. For example, if a rate limiter
	// manages requests per second and received one request 200ms ago,
	// Reset would return 800ms. You can also think of this as the time
	// until Limit and Remaining will be equal.
	ResetAfter time.Duration
}

type RateLimiter interface {
	Wait(ctx context.Context, key string, timeout time.Duration) error
}

// NewAllPassRateLimiter is a rate limiter that allows all requests
func NewAllPassRateLimiter() RateLimiter {
	return &allPassRateLimiter{}
}

type allPassRateLimiter struct {
}

func (r *allPassRateLimiter) Allow(ctx context.Context, key string) (RateLimiterResult, error) {
	return RateLimiterResult{
		Allowed:    1,
		Remaining:  1,
		RetryAfter: -1,
		ResetAfter: -1,
	}, nil
}

func (r *allPassRateLimiter) Wait(ctx context.Context, key string, timeout time.Duration) error {
	return nil
}
