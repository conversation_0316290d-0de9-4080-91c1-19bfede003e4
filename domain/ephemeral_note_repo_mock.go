// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: EphemeralNoteRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=ephemeral_note_repo_mock.go . EphemeralNoteRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"
	time "time"

	common "github.com/ethereum/go-ethereum/common"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockEphemeralNoteRepo is a mock of EphemeralNoteRepo interface.
type MockEphemeralNoteRepo struct {
	ctrl     *gomock.Controller
	recorder *MockEphemeralNoteRepoMockRecorder
}

// MockEphemeralNoteRepoMockRecorder is the mock recorder for MockEphemeralNoteRepo.
type MockEphemeralNoteRepoMockRecorder struct {
	mock *MockEphemeralNoteRepo
}

// NewMockEphemeralNoteRepo creates a new mock instance.
func NewMockEphemeralNoteRepo(ctrl *gomock.Controller) *MockEphemeralNoteRepo {
	mock := &MockEphemeralNoteRepo{ctrl: ctrl}
	mock.recorder = &MockEphemeralNoteRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEphemeralNoteRepo) EXPECT() *MockEphemeralNoteRepoMockRecorder {
	return m.recorder
}

// AcquireLock mocks base method.
func (m *MockEphemeralNoteRepo) AcquireLock(arg0 context.Context, arg1 string, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLock indicates an expected call of AcquireLock.
func (mr *MockEphemeralNoteRepoMockRecorder) AcquireLock(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLock", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).AcquireLock), arg0, arg1, arg2)
}

// AcquireLockWithRetry mocks base method.
func (m *MockEphemeralNoteRepo) AcquireLockWithRetry(arg0 context.Context, arg1 string, arg2, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLockWithRetry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLockWithRetry indicates an expected call of AcquireLockWithRetry.
func (mr *MockEphemeralNoteRepoMockRecorder) AcquireLockWithRetry(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLockWithRetry", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).AcquireLockWithRetry), arg0, arg1, arg2, arg3)
}

// AddNotification mocks base method.
func (m *MockEphemeralNoteRepo) AddNotification(arg0 context.Context, arg1 *Notification, arg2 bool) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNotification", arg0, arg1, arg2)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNotification indicates an expected call of AddNotification.
func (mr *MockEphemeralNoteRepoMockRecorder) AddNotification(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNotification", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).AddNotification), arg0, arg1, arg2)
}

// BatchGetTokenPrices mocks base method.
func (m *MockEphemeralNoteRepo) BatchGetTokenPrices(arg0 context.Context, arg1 []ChainToken) (map[ChainToken]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPrices", arg0, arg1)
	ret0, _ := ret[0].(map[ChainToken]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPrices indicates an expected call of BatchGetTokenPrices.
func (mr *MockEphemeralNoteRepoMockRecorder) BatchGetTokenPrices(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPrices", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).BatchGetTokenPrices), arg0, arg1)
}

// BatchGetTokenPricesIn24H mocks base method.
func (m *MockEphemeralNoteRepo) BatchGetTokenPricesIn24H(arg0 context.Context, arg1 []ChainToken) (map[ChainToken][]*PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPricesIn24H", arg0, arg1)
	ret0, _ := ret[0].(map[ChainToken][]*PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPricesIn24H indicates an expected call of BatchGetTokenPricesIn24H.
func (mr *MockEphemeralNoteRepoMockRecorder) BatchGetTokenPricesIn24H(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPricesIn24H", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).BatchGetTokenPricesIn24H), arg0, arg1)
}

// CreateEphemeralNote mocks base method.
func (m *MockEphemeralNoteRepo) CreateEphemeralNote(arg0 context.Context, arg1 *EphemeralNote) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateEphemeralNote", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateEphemeralNote indicates an expected call of CreateEphemeralNote.
func (mr *MockEphemeralNoteRepoMockRecorder) CreateEphemeralNote(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEphemeralNote", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).CreateEphemeralNote), arg0, arg1)
}

// CreateSendLinkCampaignUsers mocks base method.
func (m *MockEphemeralNoteRepo) CreateSendLinkCampaignUsers(arg0 context.Context, arg1 []*SendLinkCampaignUser) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSendLinkCampaignUsers", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateSendLinkCampaignUsers indicates an expected call of CreateSendLinkCampaignUsers.
func (mr *MockEphemeralNoteRepoMockRecorder) CreateSendLinkCampaignUsers(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSendLinkCampaignUsers", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).CreateSendLinkCampaignUsers), arg0, arg1)
}

// GetActiveNotesByAddresses mocks base method.
func (m *MockEphemeralNoteRepo) GetActiveNotesByAddresses(arg0 context.Context, arg1 []string) ([]*EphemeralNote, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveNotesByAddresses", arg0, arg1)
	ret0, _ := ret[0].([]*EphemeralNote)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveNotesByAddresses indicates an expected call of GetActiveNotesByAddresses.
func (mr *MockEphemeralNoteRepoMockRecorder) GetActiveNotesByAddresses(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveNotesByAddresses", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetActiveNotesByAddresses), arg0, arg1)
}

// GetActiveNotesByEphemeralOwner mocks base method.
func (m *MockEphemeralNoteRepo) GetActiveNotesByEphemeralOwner(arg0 context.Context, arg1 common.Address) ([]*EphemeralNote, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveNotesByEphemeralOwner", arg0, arg1)
	ret0, _ := ret[0].([]*EphemeralNote)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveNotesByEphemeralOwner indicates an expected call of GetActiveNotesByEphemeralOwner.
func (mr *MockEphemeralNoteRepoMockRecorder) GetActiveNotesByEphemeralOwner(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveNotesByEphemeralOwner", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetActiveNotesByEphemeralOwner), arg0, arg1)
}

// GetAllEphemeralNotes mocks base method.
func (m *MockEphemeralNoteRepo) GetAllEphemeralNotes(arg0 context.Context, arg1 []string) ([]*EphemeralNote, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllEphemeralNotes", arg0, arg1)
	ret0, _ := ret[0].([]*EphemeralNote)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllEphemeralNotes indicates an expected call of GetAllEphemeralNotes.
func (mr *MockEphemeralNoteRepoMockRecorder) GetAllEphemeralNotes(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllEphemeralNotes", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetAllEphemeralNotes), arg0, arg1)
}

// GetAssetPrice mocks base method.
func (m *MockEphemeralNoteRepo) GetAssetPrice(arg0 context.Context, arg1, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockEphemeralNoteRepoMockRecorder) GetAssetPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetAssetPrice), arg0, arg1, arg2)
}

// GetDefaultReceiveAddress mocks base method.
func (m *MockEphemeralNoteRepo) GetDefaultReceiveAddress(arg0 context.Context, arg1, arg2 string) (string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultReceiveAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetDefaultReceiveAddress indicates an expected call of GetDefaultReceiveAddress.
func (mr *MockEphemeralNoteRepoMockRecorder) GetDefaultReceiveAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultReceiveAddress", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetDefaultReceiveAddress), arg0, arg1, arg2)
}

// GetEphemeralNoteByDepositTx mocks base method.
func (m *MockEphemeralNoteRepo) GetEphemeralNoteByDepositTx(arg0 context.Context, arg1 common.Hash) (*EphemeralNote, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEphemeralNoteByDepositTx", arg0, arg1)
	ret0, _ := ret[0].(*EphemeralNote)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEphemeralNoteByDepositTx indicates an expected call of GetEphemeralNoteByDepositTx.
func (mr *MockEphemeralNoteRepoMockRecorder) GetEphemeralNoteByDepositTx(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEphemeralNoteByDepositTx", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetEphemeralNoteByDepositTx), arg0, arg1)
}

// GetEphemeralNoteByID mocks base method.
func (m *MockEphemeralNoteRepo) GetEphemeralNoteByID(arg0 context.Context, arg1 string) (*EphemeralNote, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEphemeralNoteByID", arg0, arg1)
	ret0, _ := ret[0].(*EphemeralNote)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEphemeralNoteByID indicates an expected call of GetEphemeralNoteByID.
func (mr *MockEphemeralNoteRepoMockRecorder) GetEphemeralNoteByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEphemeralNoteByID", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetEphemeralNoteByID), arg0, arg1)
}

// GetEphemeralNotesByAddress mocks base method.
func (m *MockEphemeralNoteRepo) GetEphemeralNotesByAddress(arg0 context.Context, arg1 string) ([]*EphemeralNote, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEphemeralNotesByAddress", arg0, arg1)
	ret0, _ := ret[0].([]*EphemeralNote)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEphemeralNotesByAddress indicates an expected call of GetEphemeralNotesByAddress.
func (mr *MockEphemeralNoteRepoMockRecorder) GetEphemeralNotesByAddress(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEphemeralNotesByAddress", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetEphemeralNotesByAddress), arg0, arg1)
}

// GetEphemeralOwner mocks base method.
func (m *MockEphemeralNoteRepo) GetEphemeralOwner(arg0 context.Context, arg1 common.Address) (*EphemeralOwner, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEphemeralOwner", arg0, arg1)
	ret0, _ := ret[0].(*EphemeralOwner)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEphemeralOwner indicates an expected call of GetEphemeralOwner.
func (mr *MockEphemeralNoteRepoMockRecorder) GetEphemeralOwner(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEphemeralOwner", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetEphemeralOwner), arg0, arg1)
}

// GetFcmTokens mocks base method.
func (m *MockEphemeralNoteRepo) GetFcmTokens(arg0 context.Context, arg1, arg2 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFcmTokens", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetFcmTokens indicates an expected call of GetFcmTokens.
func (mr *MockEphemeralNoteRepoMockRecorder) GetFcmTokens(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFcmTokens", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetFcmTokens), arg0, arg1, arg2)
}

// GetNativeAssetPrice mocks base method.
func (m *MockEphemeralNoteRepo) GetNativeAssetPrice(arg0 context.Context, arg1 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockEphemeralNoteRepoMockRecorder) GetNativeAssetPrice(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetNativeAssetPrice), arg0, arg1)
}

// GetSendLinkCampaignAnnouncementUser mocks base method.
func (m *MockEphemeralNoteRepo) GetSendLinkCampaignAnnouncementUser(arg0 context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSendLinkCampaignAnnouncementUser", arg0)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSendLinkCampaignAnnouncementUser indicates an expected call of GetSendLinkCampaignAnnouncementUser.
func (mr *MockEphemeralNoteRepoMockRecorder) GetSendLinkCampaignAnnouncementUser(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendLinkCampaignAnnouncementUser", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetSendLinkCampaignAnnouncementUser), arg0)
}

// GetSendLinkCampaignUser mocks base method.
func (m *MockEphemeralNoteRepo) GetSendLinkCampaignUser(arg0 context.Context, arg1 string) (*SendLinkCampaignUser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSendLinkCampaignUser", arg0, arg1)
	ret0, _ := ret[0].(*SendLinkCampaignUser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSendLinkCampaignUser indicates an expected call of GetSendLinkCampaignUser.
func (mr *MockEphemeralNoteRepoMockRecorder) GetSendLinkCampaignUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendLinkCampaignUser", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetSendLinkCampaignUser), arg0, arg1)
}

// GetTokenPrice mocks base method.
func (m *MockEphemeralNoteRepo) GetTokenPrice(arg0 context.Context, arg1 Chain, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPrice indicates an expected call of GetTokenPrice.
func (mr *MockEphemeralNoteRepoMockRecorder) GetTokenPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPrice", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetTokenPrice), arg0, arg1, arg2)
}

// GetTokenPricesIn24H mocks base method.
func (m *MockEphemeralNoteRepo) GetTokenPricesIn24H(arg0 context.Context, arg1 Chain, arg2 string) ([]*PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPricesIn24H", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPricesIn24H indicates an expected call of GetTokenPricesIn24H.
func (mr *MockEphemeralNoteRepoMockRecorder) GetTokenPricesIn24H(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPricesIn24H", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetTokenPricesIn24H), arg0, arg1, arg2)
}

// GetUnusedEphemeralOwner mocks base method.
func (m *MockEphemeralNoteRepo) GetUnusedEphemeralOwner(arg0 context.Context, arg1 EphemeralOwnerType) (common.Address, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnusedEphemeralOwner", arg0, arg1)
	ret0, _ := ret[0].(common.Address)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnusedEphemeralOwner indicates an expected call of GetUnusedEphemeralOwner.
func (mr *MockEphemeralNoteRepoMockRecorder) GetUnusedEphemeralOwner(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnusedEphemeralOwner", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetUnusedEphemeralOwner), arg0, arg1)
}

// GetUserClientIDs mocks base method.
func (m *MockEphemeralNoteRepo) GetUserClientIDs(arg0 context.Context, arg1 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserClientIDs", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserClientIDs indicates an expected call of GetUserClientIDs.
func (mr *MockEphemeralNoteRepoMockRecorder) GetUserClientIDs(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserClientIDs", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetUserClientIDs), arg0, arg1)
}

// GetUserFirstTransactionTime mocks base method.
func (m *MockEphemeralNoteRepo) GetUserFirstTransactionTime(arg0 context.Context, arg1 string) (*time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFirstTransactionTime", arg0, arg1)
	ret0, _ := ret[0].(*time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserFirstTransactionTime indicates an expected call of GetUserFirstTransactionTime.
func (mr *MockEphemeralNoteRepoMockRecorder) GetUserFirstTransactionTime(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFirstTransactionTime", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetUserFirstTransactionTime), arg0, arg1)
}

// GetUserLocale mocks base method.
func (m *MockEphemeralNoteRepo) GetUserLocale(arg0 context.Context, arg1, arg2 string) (string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLocale", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserLocale indicates an expected call of GetUserLocale.
func (mr *MockEphemeralNoteRepoMockRecorder) GetUserLocale(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLocale", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetUserLocale), arg0, arg1, arg2)
}

// GetUserWalletAddresses mocks base method.
func (m *MockEphemeralNoteRepo) GetUserWalletAddresses(arg0 context.Context, arg1 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWalletAddresses", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserWalletAddresses indicates an expected call of GetUserWalletAddresses.
func (mr *MockEphemeralNoteRepoMockRecorder) GetUserWalletAddresses(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWalletAddresses", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetUserWalletAddresses), arg0, arg1)
}

// GetUsers mocks base method.
func (m *MockEphemeralNoteRepo) GetUsers(arg0 context.Context, arg1 []string, arg2 string, arg3 bool, arg4 *UserPreloads) ([]*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsers", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsers indicates an expected call of GetUsers.
func (mr *MockEphemeralNoteRepoMockRecorder) GetUsers(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsers", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetUsers), arg0, arg1, arg2, arg3, arg4)
}

// GetUsersWithBalanceAbove mocks base method.
func (m *MockEphemeralNoteRepo) GetUsersWithBalanceAbove(arg0 context.Context, arg1 float64) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersWithBalanceAbove", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUsersWithBalanceAbove indicates an expected call of GetUsersWithBalanceAbove.
func (mr *MockEphemeralNoteRepoMockRecorder) GetUsersWithBalanceAbove(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersWithBalanceAbove", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetUsersWithBalanceAbove), arg0, arg1)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockEphemeralNoteRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockEphemeralNoteRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// ReleaseLock mocks base method.
func (m *MockEphemeralNoteRepo) ReleaseLock(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseLock", arg0, arg1)
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockEphemeralNoteRepoMockRecorder) ReleaseLock(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).ReleaseLock), arg0, arg1)
}

// SaveEphemeralOwner mocks base method.
func (m *MockEphemeralNoteRepo) SaveEphemeralOwner(arg0 context.Context, arg1 *EphemeralOwner) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveEphemeralOwner", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveEphemeralOwner indicates an expected call of SaveEphemeralOwner.
func (mr *MockEphemeralNoteRepoMockRecorder) SaveEphemeralOwner(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveEphemeralOwner", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).SaveEphemeralOwner), arg0, arg1)
}

// SetEphemeralOwnerStatus mocks base method.
func (m *MockEphemeralNoteRepo) SetEphemeralOwnerStatus(arg0 context.Context, arg1 common.Address, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEphemeralOwnerStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetEphemeralOwnerStatus indicates an expected call of SetEphemeralOwnerStatus.
func (mr *MockEphemeralNoteRepoMockRecorder) SetEphemeralOwnerStatus(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEphemeralOwnerStatus", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).SetEphemeralOwnerStatus), arg0, arg1, arg2)
}

// SetSendLinkCampaignAnnouncementSent mocks base method.
func (m *MockEphemeralNoteRepo) SetSendLinkCampaignAnnouncementSent(arg0 context.Context, arg1 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSendLinkCampaignAnnouncementSent", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetSendLinkCampaignAnnouncementSent indicates an expected call of SetSendLinkCampaignAnnouncementSent.
func (mr *MockEphemeralNoteRepoMockRecorder) SetSendLinkCampaignAnnouncementSent(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSendLinkCampaignAnnouncementSent", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).SetSendLinkCampaignAnnouncementSent), arg0, arg1)
}

// SetUserFcmTokens mocks base method.
func (m *MockEphemeralNoteRepo) SetUserFcmTokens(arg0 context.Context, arg1, arg2 string, arg3 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserFcmTokens", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserFcmTokens indicates an expected call of SetUserFcmTokens.
func (mr *MockEphemeralNoteRepoMockRecorder) SetUserFcmTokens(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserFcmTokens", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).SetUserFcmTokens), arg0, arg1, arg2, arg3)
}

// UpdateEphemeralNoteClaimed mocks base method.
func (m *MockEphemeralNoteRepo) UpdateEphemeralNoteClaimed(arg0 context.Context, arg1 string, arg2 common.Hash) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEphemeralNoteClaimed", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEphemeralNoteClaimed indicates an expected call of UpdateEphemeralNoteClaimed.
func (mr *MockEphemeralNoteRepoMockRecorder) UpdateEphemeralNoteClaimed(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEphemeralNoteClaimed", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).UpdateEphemeralNoteClaimed), arg0, arg1, arg2)
}

// UpdateEphemeralNoteStatus mocks base method.
func (m *MockEphemeralNoteRepo) UpdateEphemeralNoteStatus(arg0 context.Context, arg1 string, arg2 EphemeralNoteStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateEphemeralNoteStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEphemeralNoteStatus indicates an expected call of UpdateEphemeralNoteStatus.
func (mr *MockEphemeralNoteRepoMockRecorder) UpdateEphemeralNoteStatus(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEphemeralNoteStatus", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).UpdateEphemeralNoteStatus), arg0, arg1, arg2)
}

// UpdateSendLinkCampaignUser mocks base method.
func (m *MockEphemeralNoteRepo) UpdateSendLinkCampaignUser(arg0 context.Context, arg1 *SendLinkCampaignUserUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSendLinkCampaignUser", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSendLinkCampaignUser indicates an expected call of UpdateSendLinkCampaignUser.
func (mr *MockEphemeralNoteRepoMockRecorder) UpdateSendLinkCampaignUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSendLinkCampaignUser", reflect.TypeOf((*MockEphemeralNoteRepo)(nil).UpdateSendLinkCampaignUser), arg0, arg1)
}
