// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: EnergyRentCostRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=energy_rent_cost_repo_mock.go . EnergyRentCostRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockEnergyRentCostRepo is a mock of EnergyRentCostRepo interface.
type MockEnergyRentCostRepo struct {
	ctrl     *gomock.Controller
	recorder *MockEnergyRentCostRepoMockRecorder
}

// MockEnergyRentCostRepoMockRecorder is the mock recorder for MockEnergyRentCostRepo.
type MockEnergyRentCostRepoMockRecorder struct {
	mock *MockEnergyRentCostRepo
}

// NewMockEnergyRentCostRepo creates a new mock instance.
func NewMockEnergyRentCostRepo(ctrl *gomock.Controller) *MockEnergyRentCostRepo {
	mock := &MockEnergyRentCostRepo{ctrl: ctrl}
	mock.recorder = &MockEnergyRentCostRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEnergyRentCostRepo) EXPECT() *MockEnergyRentCostRepoMockRecorder {
	return m.recorder
}

// GetEnergyRentUnitCost mocks base method.
func (m *MockEnergyRentCostRepo) GetEnergyRentUnitCost(arg0 context.Context) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnergyRentUnitCost", arg0)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnergyRentUnitCost indicates an expected call of GetEnergyRentUnitCost.
func (mr *MockEnergyRentCostRepoMockRecorder) GetEnergyRentUnitCost(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnergyRentUnitCost", reflect.TypeOf((*MockEnergyRentCostRepo)(nil).GetEnergyRentUnitCost), arg0)
}

// SetEnergyRentUnitCost mocks base method.
func (m *MockEnergyRentCostRepo) SetEnergyRentUnitCost(arg0 context.Context, arg1 float64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEnergyRentUnitCost", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetEnergyRentUnitCost indicates an expected call of SetEnergyRentUnitCost.
func (mr *MockEnergyRentCostRepoMockRecorder) SetEnergyRentUnitCost(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEnergyRentUnitCost", reflect.TypeOf((*MockEnergyRentCostRepo)(nil).SetEnergyRentUnitCost), arg0, arg1)
}
