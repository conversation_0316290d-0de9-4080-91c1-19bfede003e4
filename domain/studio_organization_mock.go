// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: StudioOrgRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=studio_organization_mock.go . StudioOrgRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	paging "github.com/kryptogo/kg-wallet-backend/pkg/db/paging"
	gomock "go.uber.org/mock/gomock"
)

// MockStudioOrgRepo is a mock of StudioOrgRepo interface.
type MockStudioOrgRepo struct {
	ctrl     *gomock.Controller
	recorder *MockStudioOrgRepoMockRecorder
}

// MockStudioOrgRepoMockRecorder is the mock recorder for MockStudioOrgRepo.
type MockStudioOrgRepoMockRecorder struct {
	mock *MockStudioOrgRepo
}

// NewMockStudioOrgRepo creates a new mock instance.
func NewMockStudioOrgRepo(ctrl *gomock.Controller) *MockStudioOrgRepo {
	mock := &MockStudioOrgRepo{ctrl: ctrl}
	mock.recorder = &MockStudioOrgRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStudioOrgRepo) EXPECT() *MockStudioOrgRepoMockRecorder {
	return m.recorder
}

// AcceptInvitation mocks base method.
func (m *MockStudioOrgRepo) AcceptInvitation(arg0 context.Context, arg1 int, arg2 string) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcceptInvitation", arg0, arg1, arg2)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// AcceptInvitation indicates an expected call of AcceptInvitation.
func (mr *MockStudioOrgRepoMockRecorder) AcceptInvitation(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptInvitation", reflect.TypeOf((*MockStudioOrgRepo)(nil).AcceptInvitation), arg0, arg1, arg2)
}

// CountImportedAddresses mocks base method.
func (m *MockStudioOrgRepo) CountImportedAddresses(arg0 context.Context, arg1 int, arg2 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountImportedAddresses", arg0, arg1, arg2)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountImportedAddresses indicates an expected call of CountImportedAddresses.
func (mr *MockStudioOrgRepoMockRecorder) CountImportedAddresses(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountImportedAddresses", reflect.TypeOf((*MockStudioOrgRepo)(nil).CountImportedAddresses), arg0, arg1, arg2)
}

// CreateDapps mocks base method.
func (m *MockStudioOrgRepo) CreateDapps(arg0 context.Context, arg1 int, arg2 []StudioOrganizationDapp) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDapps", arg0, arg1, arg2)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// CreateDapps indicates an expected call of CreateDapps.
func (mr *MockStudioOrgRepoMockRecorder) CreateDapps(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDapps", reflect.TypeOf((*MockStudioOrgRepo)(nil).CreateDapps), arg0, arg1, arg2)
}

// CreateOrganization mocks base method.
func (m *MockStudioOrgRepo) CreateOrganization(arg0 context.Context, arg1 StudioOrganizationConfig) (int, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrganization", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// CreateOrganization indicates an expected call of CreateOrganization.
func (mr *MockStudioOrgRepoMockRecorder) CreateOrganization(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrganization", reflect.TypeOf((*MockStudioOrgRepo)(nil).CreateOrganization), arg0, arg1)
}

// CreateStudioAdmin mocks base method.
func (m *MockStudioOrgRepo) CreateStudioAdmin(arg0 context.Context, arg1 int, arg2, arg3, arg4 string) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateStudioAdmin", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// CreateStudioAdmin indicates an expected call of CreateStudioAdmin.
func (mr *MockStudioOrgRepoMockRecorder) CreateStudioAdmin(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStudioAdmin", reflect.TypeOf((*MockStudioOrgRepo)(nil).CreateStudioAdmin), arg0, arg1, arg2, arg3, arg4)
}

// CreateStudioUser mocks base method.
func (m *MockStudioOrgRepo) CreateStudioUser(arg0 context.Context, arg1 SaveStudioUserDataParams) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateStudioUser", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// CreateStudioUser indicates an expected call of CreateStudioUser.
func (mr *MockStudioOrgRepoMockRecorder) CreateStudioUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStudioUser", reflect.TypeOf((*MockStudioOrgRepo)(nil).CreateStudioUser), arg0, arg1)
}

// DeleteExchangeRate mocks base method.
func (m *MockStudioOrgRepo) DeleteExchangeRate(arg0 context.Context, arg1 int, arg2 string) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteExchangeRate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// DeleteExchangeRate indicates an expected call of DeleteExchangeRate.
func (mr *MockStudioOrgRepoMockRecorder) DeleteExchangeRate(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteExchangeRate", reflect.TypeOf((*MockStudioOrgRepo)(nil).DeleteExchangeRate), arg0, arg1, arg2)
}

// DeleteImportedAddress mocks base method.
func (m *MockStudioOrgRepo) DeleteImportedAddress(arg0 context.Context, arg1, arg2 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteImportedAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteImportedAddress indicates an expected call of DeleteImportedAddress.
func (mr *MockStudioOrgRepoMockRecorder) DeleteImportedAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteImportedAddress", reflect.TypeOf((*MockStudioOrgRepo)(nil).DeleteImportedAddress), arg0, arg1, arg2)
}

// DisableStudioUser mocks base method.
func (m *MockStudioOrgRepo) DisableStudioUser(arg0 context.Context, arg1 int, arg2 string) (*StudioUser, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DisableStudioUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(*StudioUser)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// DisableStudioUser indicates an expected call of DisableStudioUser.
func (mr *MockStudioOrgRepoMockRecorder) DisableStudioUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisableStudioUser", reflect.TypeOf((*MockStudioOrgRepo)(nil).DisableStudioUser), arg0, arg1, arg2)
}

// GetActiveDapps mocks base method.
func (m *MockStudioOrgRepo) GetActiveDapps(arg0 context.Context, arg1 int) ([]StudioOrganizationDapp, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveDapps", arg0, arg1)
	ret0, _ := ret[0].([]StudioOrganizationDapp)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetActiveDapps indicates an expected call of GetActiveDapps.
func (mr *MockStudioOrgRepoMockRecorder) GetActiveDapps(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveDapps", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetActiveDapps), arg0, arg1)
}

// GetDefaultImportedAddress mocks base method.
func (m *MockStudioOrgRepo) GetDefaultImportedAddress(arg0 context.Context, arg1 int, arg2 string) (*ImportedAddress, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultImportedAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(*ImportedAddress)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDefaultImportedAddress indicates an expected call of GetDefaultImportedAddress.
func (mr *MockStudioOrgRepoMockRecorder) GetDefaultImportedAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultImportedAddress", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetDefaultImportedAddress), arg0, arg1, arg2)
}

// GetExchangeRates mocks base method.
func (m *MockStudioOrgRepo) GetExchangeRates(arg0 context.Context, arg1 int) ([]*ExchangeRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExchangeRates", arg0, arg1)
	ret0, _ := ret[0].([]*ExchangeRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetExchangeRates indicates an expected call of GetExchangeRates.
func (mr *MockStudioOrgRepoMockRecorder) GetExchangeRates(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExchangeRates", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetExchangeRates), arg0, arg1)
}

// GetImportedAddressesByOrgID mocks base method.
func (m *MockStudioOrgRepo) GetImportedAddressesByOrgID(arg0 context.Context, arg1 int) ([]*ImportedAddress, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetImportedAddressesByOrgID", arg0, arg1)
	ret0, _ := ret[0].([]*ImportedAddress)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetImportedAddressesByOrgID indicates an expected call of GetImportedAddressesByOrgID.
func (mr *MockStudioOrgRepoMockRecorder) GetImportedAddressesByOrgID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetImportedAddressesByOrgID", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetImportedAddressesByOrgID), arg0, arg1)
}

// GetLinebotConfigByChannelID mocks base method.
func (m *MockStudioOrgRepo) GetLinebotConfigByChannelID(arg0 context.Context, arg1 string) (*StudioOrgLinebotConfig, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLinebotConfigByChannelID", arg0, arg1)
	ret0, _ := ret[0].(*StudioOrgLinebotConfig)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetLinebotConfigByChannelID indicates an expected call of GetLinebotConfigByChannelID.
func (mr *MockStudioOrgRepoMockRecorder) GetLinebotConfigByChannelID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLinebotConfigByChannelID", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetLinebotConfigByChannelID), arg0, arg1)
}

// GetMarketByMarketCode mocks base method.
func (m *MockStudioOrgRepo) GetMarketByMarketCode(arg0 context.Context, arg1 string) (*StudioMarket, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarketByMarketCode", arg0, arg1)
	ret0, _ := ret[0].(*StudioMarket)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetMarketByMarketCode indicates an expected call of GetMarketByMarketCode.
func (mr *MockStudioOrgRepoMockRecorder) GetMarketByMarketCode(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarketByMarketCode", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetMarketByMarketCode), arg0, arg1)
}

// GetMarketByOrg mocks base method.
func (m *MockStudioOrgRepo) GetMarketByOrg(arg0 context.Context, arg1 int) (*StudioMarket, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarketByOrg", arg0, arg1)
	ret0, _ := ret[0].(*StudioMarket)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetMarketByOrg indicates an expected call of GetMarketByOrg.
func (mr *MockStudioOrgRepoMockRecorder) GetMarketByOrg(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarketByOrg", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetMarketByOrg), arg0, arg1)
}

// GetMarkets mocks base method.
func (m *MockStudioOrgRepo) GetMarkets(arg0 context.Context) ([]*StudioMarket, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarkets", arg0)
	ret0, _ := ret[0].([]*StudioMarket)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetMarkets indicates an expected call of GetMarkets.
func (mr *MockStudioOrgRepoMockRecorder) GetMarkets(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarkets", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetMarkets), arg0)
}

// GetOAuthConfig mocks base method.
func (m *MockStudioOrgRepo) GetOAuthConfig(arg0 context.Context, arg1 int, arg2 OAuthApplicationType) (*OAuthConfig, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOAuthConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(*OAuthConfig)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetOAuthConfig indicates an expected call of GetOAuthConfig.
func (mr *MockStudioOrgRepoMockRecorder) GetOAuthConfig(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOAuthConfig", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOAuthConfig), arg0, arg1, arg2)
}

// GetOAuthConfigByClientID mocks base method.
func (m *MockStudioOrgRepo) GetOAuthConfigByClientID(arg0 context.Context, arg1 string) (*OAuthConfig, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOAuthConfigByClientID", arg0, arg1)
	ret0, _ := ret[0].(*OAuthConfig)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetOAuthConfigByClientID indicates an expected call of GetOAuthConfigByClientID.
func (mr *MockStudioOrgRepoMockRecorder) GetOAuthConfigByClientID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOAuthConfigByClientID", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOAuthConfigByClientID), arg0, arg1)
}

// GetOAuthConfigByLineChannelID mocks base method.
func (m *MockStudioOrgRepo) GetOAuthConfigByLineChannelID(arg0 context.Context, arg1 string) (*OAuthConfig, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOAuthConfigByLineChannelID", arg0, arg1)
	ret0, _ := ret[0].(*OAuthConfig)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetOAuthConfigByLineChannelID indicates an expected call of GetOAuthConfigByLineChannelID.
func (mr *MockStudioOrgRepoMockRecorder) GetOAuthConfigByLineChannelID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOAuthConfigByLineChannelID", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOAuthConfigByLineChannelID), arg0, arg1)
}

// GetOrgEnabledModules mocks base method.
func (m *MockStudioOrgRepo) GetOrgEnabledModules(arg0 context.Context, arg1 int) (*StudioOrganizationModule, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrgEnabledModules", arg0, arg1)
	ret0, _ := ret[0].(*StudioOrganizationModule)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetOrgEnabledModules indicates an expected call of GetOrgEnabledModules.
func (mr *MockStudioOrgRepoMockRecorder) GetOrgEnabledModules(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrgEnabledModules", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOrgEnabledModules), arg0, arg1)
}

// GetOrgLinebotConfig mocks base method.
func (m *MockStudioOrgRepo) GetOrgLinebotConfig(arg0 context.Context, arg1 int) (*StudioOrgLinebotConfig, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrgLinebotConfig", arg0, arg1)
	ret0, _ := ret[0].(*StudioOrgLinebotConfig)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetOrgLinebotConfig indicates an expected call of GetOrgLinebotConfig.
func (mr *MockStudioOrgRepoMockRecorder) GetOrgLinebotConfig(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrgLinebotConfig", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOrgLinebotConfig), arg0, arg1)
}

// GetOrgWallet mocks base method.
func (m *MockStudioOrgRepo) GetOrgWallet(arg0 context.Context, arg1 int, arg2 string) (*StudioOrganizationWallet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrgWallet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*StudioOrganizationWallet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrgWallet indicates an expected call of GetOrgWallet.
func (mr *MockStudioOrgRepoMockRecorder) GetOrgWallet(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrgWallet", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOrgWallet), arg0, arg1, arg2)
}

// GetOrgWallets mocks base method.
func (m *MockStudioOrgRepo) GetOrgWallets(arg0 context.Context, arg1 int) ([]*StudioOrganizationWallet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrgWallets", arg0, arg1)
	ret0, _ := ret[0].([]*StudioOrganizationWallet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrgWallets indicates an expected call of GetOrgWallets.
func (mr *MockStudioOrgRepoMockRecorder) GetOrgWallets(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrgWallets", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOrgWallets), arg0, arg1)
}

// GetOrganizationByComplianceOrgID mocks base method.
func (m *MockStudioOrgRepo) GetOrganizationByComplianceOrgID(arg0 context.Context, arg1 int) (*StudioOrganization, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrganizationByComplianceOrgID", arg0, arg1)
	ret0, _ := ret[0].(*StudioOrganization)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetOrganizationByComplianceOrgID indicates an expected call of GetOrganizationByComplianceOrgID.
func (mr *MockStudioOrgRepoMockRecorder) GetOrganizationByComplianceOrgID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrganizationByComplianceOrgID", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOrganizationByComplianceOrgID), arg0, arg1)
}

// GetOrganizationByID mocks base method.
func (m *MockStudioOrgRepo) GetOrganizationByID(arg0 context.Context, arg1 int) (*StudioOrganization, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrganizationByID", arg0, arg1)
	ret0, _ := ret[0].(*StudioOrganization)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetOrganizationByID indicates an expected call of GetOrganizationByID.
func (mr *MockStudioOrgRepoMockRecorder) GetOrganizationByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrganizationByID", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOrganizationByID), arg0, arg1)
}

// GetOrganizationByOAuthClientID mocks base method.
func (m *MockStudioOrgRepo) GetOrganizationByOAuthClientID(arg0 context.Context, arg1 string) (*StudioOrganization, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrganizationByOAuthClientID", arg0, arg1)
	ret0, _ := ret[0].(*StudioOrganization)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetOrganizationByOAuthClientID indicates an expected call of GetOrganizationByOAuthClientID.
func (mr *MockStudioOrgRepoMockRecorder) GetOrganizationByOAuthClientID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrganizationByOAuthClientID", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOrganizationByOAuthClientID), arg0, arg1)
}

// GetOrganizationsByActiveUser mocks base method.
func (m *MockStudioOrgRepo) GetOrganizationsByActiveUser(arg0 context.Context, arg1 string) ([]StudioOrganization, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrganizationsByActiveUser", arg0, arg1)
	ret0, _ := ret[0].([]StudioOrganization)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrganizationsByActiveUser indicates an expected call of GetOrganizationsByActiveUser.
func (mr *MockStudioOrgRepoMockRecorder) GetOrganizationsByActiveUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrganizationsByActiveUser", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOrganizationsByActiveUser), arg0, arg1)
}

// GetOrganizationsByUser mocks base method.
func (m *MockStudioOrgRepo) GetOrganizationsByUser(arg0 context.Context, arg1 string) ([]StudioOrganization, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrganizationsByUser", arg0, arg1)
	ret0, _ := ret[0].([]StudioOrganization)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetOrganizationsByUser indicates an expected call of GetOrganizationsByUser.
func (mr *MockStudioOrgRepoMockRecorder) GetOrganizationsByUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrganizationsByUser", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetOrganizationsByUser), arg0, arg1)
}

// GetStudioAdmin mocks base method.
func (m *MockStudioOrgRepo) GetStudioAdmin(arg0 context.Context, arg1 int) (*StudioUser, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStudioAdmin", arg0, arg1)
	ret0, _ := ret[0].(*StudioUser)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetStudioAdmin indicates an expected call of GetStudioAdmin.
func (mr *MockStudioOrgRepoMockRecorder) GetStudioAdmin(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioAdmin", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetStudioAdmin), arg0, arg1)
}

// GetStudioUser mocks base method.
func (m *MockStudioOrgRepo) GetStudioUser(arg0 context.Context, arg1 int, arg2 string) (*StudioUser, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStudioUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(*StudioUser)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetStudioUser indicates an expected call of GetStudioUser.
func (mr *MockStudioOrgRepoMockRecorder) GetStudioUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioUser", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetStudioUser), arg0, arg1, arg2)
}

// GetStudioUserByEmail mocks base method.
func (m *MockStudioOrgRepo) GetStudioUserByEmail(arg0 context.Context, arg1 int, arg2 string) (*StudioUser, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStudioUserByEmail", arg0, arg1, arg2)
	ret0, _ := ret[0].(*StudioUser)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetStudioUserByEmail indicates an expected call of GetStudioUserByEmail.
func (mr *MockStudioOrgRepoMockRecorder) GetStudioUserByEmail(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioUserByEmail", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetStudioUserByEmail), arg0, arg1, arg2)
}

// GetStudioUsers mocks base method.
func (m *MockStudioOrgRepo) GetStudioUsers(arg0 context.Context, arg1 int, arg2 []StudioRole, arg3 *paging.Query) ([]*StudioUser, *paging.Resp, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStudioUsers", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*StudioUser)
	ret1, _ := ret[1].(*paging.Resp)
	ret2, _ := ret[2].(*code.KGError)
	return ret0, ret1, ret2
}

// GetStudioUsers indicates an expected call of GetStudioUsers.
func (mr *MockStudioOrgRepoMockRecorder) GetStudioUsers(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioUsers", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetStudioUsers), arg0, arg1, arg2, arg3)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockStudioOrgRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockStudioOrgRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockStudioOrgRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// HasKgUserAuthorizedOAuthClient mocks base method.
func (m *MockStudioOrgRepo) HasKgUserAuthorizedOAuthClient(arg0 context.Context, arg1, arg2 string) (bool, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasKgUserAuthorizedOAuthClient", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// HasKgUserAuthorizedOAuthClient indicates an expected call of HasKgUserAuthorizedOAuthClient.
func (mr *MockStudioOrgRepoMockRecorder) HasKgUserAuthorizedOAuthClient(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasKgUserAuthorizedOAuthClient", reflect.TypeOf((*MockStudioOrgRepo)(nil).HasKgUserAuthorizedOAuthClient), arg0, arg1, arg2)
}

// InsertImportedAddress mocks base method.
func (m *MockStudioOrgRepo) InsertImportedAddress(arg0 context.Context, arg1 int, arg2, arg3, arg4 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertImportedAddress", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertImportedAddress indicates an expected call of InsertImportedAddress.
func (mr *MockStudioOrgRepoMockRecorder) InsertImportedAddress(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertImportedAddress", reflect.TypeOf((*MockStudioOrgRepo)(nil).InsertImportedAddress), arg0, arg1, arg2, arg3, arg4)
}

// IsOAuthClientInOrganization mocks base method.
func (m *MockStudioOrgRepo) IsOAuthClientInOrganization(arg0 context.Context, arg1 int, arg2 string) (bool, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsOAuthClientInOrganization", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// IsOAuthClientInOrganization indicates an expected call of IsOAuthClientInOrganization.
func (mr *MockStudioOrgRepoMockRecorder) IsOAuthClientInOrganization(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsOAuthClientInOrganization", reflect.TypeOf((*MockStudioOrgRepo)(nil).IsOAuthClientInOrganization), arg0, arg1, arg2)
}

// SaveMarketInfo mocks base method.
func (m *MockStudioOrgRepo) SaveMarketInfo(arg0 context.Context, arg1 SaveStudioMarketInfoRequest) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveMarketInfo", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// SaveMarketInfo indicates an expected call of SaveMarketInfo.
func (mr *MockStudioOrgRepoMockRecorder) SaveMarketInfo(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveMarketInfo", reflect.TypeOf((*MockStudioOrgRepo)(nil).SaveMarketInfo), arg0, arg1)
}

// SaveOrgEnabledModules mocks base method.
func (m *MockStudioOrgRepo) SaveOrgEnabledModules(arg0 context.Context, arg1 int, arg2 *StudioOrganizationModule) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveOrgEnabledModules", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveOrgEnabledModules indicates an expected call of SaveOrgEnabledModules.
func (mr *MockStudioOrgRepoMockRecorder) SaveOrgEnabledModules(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveOrgEnabledModules", reflect.TypeOf((*MockStudioOrgRepo)(nil).SaveOrgEnabledModules), arg0, arg1, arg2)
}

// SaveOrganization mocks base method.
func (m *MockStudioOrgRepo) SaveOrganization(arg0 context.Context, arg1 int, arg2 SaveOrganizationRequest) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveOrganization", arg0, arg1, arg2)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// SaveOrganization indicates an expected call of SaveOrganization.
func (mr *MockStudioOrgRepoMockRecorder) SaveOrganization(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveOrganization", reflect.TypeOf((*MockStudioOrgRepo)(nil).SaveOrganization), arg0, arg1, arg2)
}

// SaveStudioUserData mocks base method.
func (m *MockStudioOrgRepo) SaveStudioUserData(arg0 context.Context, arg1 SaveStudioUserDataParams) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveStudioUserData", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// SaveStudioUserData indicates an expected call of SaveStudioUserData.
func (mr *MockStudioOrgRepoMockRecorder) SaveStudioUserData(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveStudioUserData", reflect.TypeOf((*MockStudioOrgRepo)(nil).SaveStudioUserData), arg0, arg1)
}

// SetDefaultImportedAddress mocks base method.
func (m *MockStudioOrgRepo) SetDefaultImportedAddress(arg0 context.Context, arg1, arg2 int, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDefaultImportedAddress", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDefaultImportedAddress indicates an expected call of SetDefaultImportedAddress.
func (mr *MockStudioOrgRepoMockRecorder) SetDefaultImportedAddress(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDefaultImportedAddress", reflect.TypeOf((*MockStudioOrgRepo)(nil).SetDefaultImportedAddress), arg0, arg1, arg2, arg3)
}

// UpdateOrganization mocks base method.
func (m *MockStudioOrgRepo) UpdateOrganization(arg0 context.Context, arg1 int, arg2 StudioOrganizationConfig) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrganization", arg0, arg1, arg2)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// UpdateOrganization indicates an expected call of UpdateOrganization.
func (mr *MockStudioOrgRepoMockRecorder) UpdateOrganization(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrganization", reflect.TypeOf((*MockStudioOrgRepo)(nil).UpdateOrganization), arg0, arg1, arg2)
}

// UpsertExchangeRate mocks base method.
func (m *MockStudioOrgRepo) UpsertExchangeRate(arg0 context.Context, arg1 int, arg2 ExchangeRate) (bool, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertExchangeRate", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// UpsertExchangeRate indicates an expected call of UpsertExchangeRate.
func (mr *MockStudioOrgRepoMockRecorder) UpsertExchangeRate(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertExchangeRate", reflect.TypeOf((*MockStudioOrgRepo)(nil).UpsertExchangeRate), arg0, arg1, arg2)
}

// UpsertMarketInfo mocks base method.
func (m *MockStudioOrgRepo) UpsertMarketInfo(arg0 context.Context, arg1 UpsertStudioMarketRequest) (bool, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertMarketInfo", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// UpsertMarketInfo indicates an expected call of UpsertMarketInfo.
func (mr *MockStudioOrgRepoMockRecorder) UpsertMarketInfo(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertMarketInfo", reflect.TypeOf((*MockStudioOrgRepo)(nil).UpsertMarketInfo), arg0, arg1)
}

// UpsertOrgLinebotConfig mocks base method.
func (m *MockStudioOrgRepo) UpsertOrgLinebotConfig(arg0 context.Context, arg1 StudioOrgLinebotConfig) (bool, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertOrgLinebotConfig", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// UpsertOrgLinebotConfig indicates an expected call of UpsertOrgLinebotConfig.
func (mr *MockStudioOrgRepoMockRecorder) UpsertOrgLinebotConfig(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertOrgLinebotConfig", reflect.TypeOf((*MockStudioOrgRepo)(nil).UpsertOrgLinebotConfig), arg0, arg1)
}
