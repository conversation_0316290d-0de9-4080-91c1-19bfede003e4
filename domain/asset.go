//go:generate go-enum
package domain

import (
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
)

// AssetType is the asset type
// ENUM(token, nft, defi)
type AssetType string

// AssetTypeAll is all of asset types
var AssetTypeAll = []AssetType{AssetTypeToken, AssetTypeNft, AssetTypeDefi}

// NftType is the nft scheme type
// ENUM(erc721, erc1155)
type NftType string

type Asset interface {
	Chain() Chain
	ID() string
	Type() AssetType
	Name() string
	LogoUrls() []string
	UsdValue() float64
	IsVerified() bool
}

var (
	_ Asset = &TokenAsset{}
	_ Asset = &SolanaTokenAsset{}
	_ Asset = &NftAsset{}
	_ Asset = &DefiAsset{}
)

// TokenAsset is the asset type for tokens
type TokenAsset struct {
	Token
	Price         float64
	WalletAmounts []*WalletTokenAmount
}

// WalletTokenAmount represent amount of asset in a wallet
type WalletTokenAmount struct {
	Address Address
	Amount  decimal.Decimal
}

// PricePoint represent price of asset at a specific time
type PricePoint struct {
	Time  time.Time
	Price float64
}

func (t *TokenAsset) Chain() Chain {
	return t.Token.Chain()
}

func (t *TokenAsset) Type() AssetType {
	return AssetTypeToken
}

func (t *TokenAsset) Name() string {
	return t.Token.Name()
}

func (t *TokenAsset) LogoUrls() []string {
	if t.LogoUrl() != "" {
		return []string{t.LogoUrl()}
	}
	return []string{}
}

func (t *TokenAsset) UsdValue() float64 {
	amountDec := decimal.Zero
	for _, walletAmount := range t.WalletAmounts {
		amountDec = amountDec.Add(walletAmount.Amount)
	}
	amount, _ := amountDec.Float64()
	return t.Price * amount
}

func (t *TokenAsset) IsVerified() bool {
	return t.Token.IsVerified()
}

type SolanaTokenAsset struct {
	TokenAsset
	Accounts []*SolanaTokenAccount
}

type SolanaTokenAccount struct {
	Address string
	Account string
}

func (s *SolanaTokenAsset) Chain() Chain       { return s.TokenAsset.Chain() }
func (s *SolanaTokenAsset) Type() AssetType    { return s.TokenAsset.Type() }
func (s *SolanaTokenAsset) Name() string       { return s.TokenAsset.Name() }
func (s *SolanaTokenAsset) LogoUrls() []string { return s.TokenAsset.LogoUrls() }
func (s *SolanaTokenAsset) UsdValue() float64  { return s.TokenAsset.UsdValue() }
func (s *SolanaTokenAsset) IsVerified() bool   { return s.TokenAsset.IsVerified() }

// NftAsset is the asset type for nfts
type NftAsset struct {
	chain      Chain
	id         string
	name       string
	logoUrl    string
	usdValue   float64
	isVerified bool

	// ERC 721 or 1155
	NftType         NftType
	ContractAddress Address
	FloorPriceETH   float64
	WalletAmounts   []*WalletNftAmount
}

func NewNftAsset(chain Chain, id string, name string, logoUrl string, isVerified bool) *NftAsset {
	return &NftAsset{
		chain:      chain,
		id:         id,
		name:       name,
		logoUrl:    logoUrl,
		isVerified: isVerified,
	}
}

type WalletNftAmount struct {
	Address Address
	Amount  uint
}

func (n *NftAsset) Chain() Chain {
	return n.chain
}

func (n *NftAsset) ID() string {
	return n.id
}

func (n *NftAsset) Type() AssetType {
	return AssetTypeNft
}

func (n *NftAsset) Name() string {
	return n.name
}

func (n *NftAsset) LogoUrls() []string {
	if n.logoUrl != "" {
		return []string{n.logoUrl}
	}
	return []string{}
}

func (n *NftAsset) UsdValue() float64 {
	return n.usdValue
}

func (n *NftAsset) IsVerified() bool {
	return n.isVerified
}

func (n *NftAsset) Amount() uint {
	return lo.Reduce(n.WalletAmounts, func(acc uint, walletAmount *WalletNftAmount, _ int) uint {
		return acc + walletAmount.Amount
	}, uint(0))
}

// DefiAsset is the asset type for defi assets
type DefiAsset struct {
	chain        Chain
	id           string
	name         string
	SiteUrl      string
	SupplyTokens []*DefiToken
	RewardTokens []*DefiToken
	BorrowTokens []*DefiToken
	WalletValues []*WalletValue
}

type WalletValue struct {
	Address  Address
	UsdValue decimal.Decimal
}

func (d *DefiAsset) ID() string {
	return d.id
}

func NewDefiAsset(chain Chain, id string, name string, siteUrl string) *DefiAsset {
	return &DefiAsset{
		chain:        chain,
		id:           id,
		name:         name,
		SiteUrl:      siteUrl,
		SupplyTokens: make([]*DefiToken, 0),
		RewardTokens: make([]*DefiToken, 0),
		BorrowTokens: make([]*DefiToken, 0),
	}
}

type DefiToken struct {
	ID      string
	Name    string
	Symbol  string
	LogoUrl *string
	Amount  decimal.Decimal
	Price   float64
}

func (d *DefiToken) UsdValue() float64 {
	amount, _ := d.Amount.Float64()
	return amount * d.Price
}

func (d *DefiAsset) Chain() Chain {
	return d.chain
}

func (d *DefiAsset) Type() AssetType {
	return AssetTypeDefi
}

func (d *DefiAsset) Name() string {
	return d.name
}

func (d *DefiAsset) LogoUrls() []string {
	urls := make([]string, 0)
	for _, token := range d.SupplyTokens {
		if token.LogoUrl != nil {
			urls = append(urls, *token.LogoUrl)
		}
	}
	return urls
}

func (d *DefiAsset) UsdValue() float64 {
	usdValue := 0.0
	for _, token := range d.SupplyTokens {
		usdValue += token.UsdValue()
	}
	for _, token := range d.RewardTokens {
		usdValue += token.UsdValue()
	}
	for _, token := range d.BorrowTokens {
		usdValue -= token.UsdValue()
	}
	return usdValue
}

func (d *DefiAsset) IsVerified() bool {
	return true
}
