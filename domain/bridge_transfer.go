package domain

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// BridgeTransferService defines the service interface for bridge transfer operations
type BridgeTransferService interface {
	CreateBridgeTransfer(ctx context.Context, req *CreateBridgeTransferRequest) (*CreateBridgeTransferResponse, *code.KGError)
	GetBridgeTransfersByOrgID(ctx context.Context, organizationID int) ([]*BridgeTransferData, *code.KGError)
}

// BridgeTransferRepo defines the repository interface for bridge transfer data persistence
type BridgeTransferRepo interface {
	CreateBridgeTransfer(ctx context.Context, transfer *CreateBridgeTransferData) error
	GetBridgeTransfersByOrgID(ctx context.Context, organizationID int) ([]*BridgeTransferData, error)
}

// CreateBridgeTransferRequest represents the request to create a bridge transfer
type CreateBridgeTransferRequest struct {
	OrganizationID      int                              `json:"-"` // Will be set from context
	Amount              string                           `json:"amount" binding:"required"`
	Source              *BridgeTransferSource            `json:"source" binding:"required"`
	Destination         *BridgeTransferDestination       `json:"destination" binding:"required"`
}

// BridgeTransferSource represents the source of a bridge transfer
type BridgeTransferSource struct {
	PaymentRail string `json:"payment_rail" binding:"required,oneof=arbitrum optimism base"`
	Currency    string `json:"currency" binding:"required,oneof=usdt usdc"`
	FromAddress string `json:"from_address" binding:"required"`
}

// BridgeTransferDestination represents the destination of a bridge transfer
type BridgeTransferDestination struct {
	PaymentRail       string `json:"payment_rail" binding:"required,oneof=ach wire"`
	Currency          string `json:"currency" binding:"required,oneof=usd"`
	ExternalAccountID string `json:"external_account_id" binding:"required"`
}

// CreateBridgeTransferResponse represents the response after creating a bridge transfer
type CreateBridgeTransferResponse struct {
	ID                        string                                   `json:"id"`
	ClientReferenceID         *string                                  `json:"client_reference_id"`
	State                     string                                   `json:"state"`
	OnBehalfOf                string                                   `json:"on_behalf_of"`
	Currency                  string                                   `json:"currency"`
	Amount                    string                                   `json:"amount"`
	DeveloperFee              string                                   `json:"developer_fee"`
	Source                    *BridgeTransferSourceResponse            `json:"source"`
	Destination               *BridgeTransferDestinationResponse       `json:"destination"`
	CreatedAt                 time.Time                                `json:"created_at"`
	UpdatedAt                 time.Time                                `json:"updated_at"`
	Receipt                   *BridgeTransferReceipt                   `json:"receipt,omitempty"`
	SourceDepositInstructions *BridgeTransferSourceDepositInstructions `json:"source_deposit_instructions,omitempty"`
}

// BridgeTransferSourceResponse represents the source in bridge transfer response
type BridgeTransferSourceResponse struct {
	PaymentRail string `json:"payment_rail"`
	Currency    string `json:"currency"`
	FromAddress string `json:"from_address"`
}

// BridgeTransferDestinationResponse represents the destination in bridge transfer response
type BridgeTransferDestinationResponse struct {
	PaymentRail       string  `json:"payment_rail"`
	Currency          string  `json:"currency"`
	ExternalAccountID string  `json:"external_account_id"`
	IMAD              *string `json:"imad,omitempty"`
	OMAD              *string `json:"omad,omitempty"`
	TraceNumber       *string `json:"trace_number,omitempty"`
	UETR              *string `json:"uetr,omitempty"`
}

// BridgeTransferReceipt represents the receipt information
type BridgeTransferReceipt struct {
	InitialAmount  string `json:"initial_amount"`
	DeveloperFee   string `json:"developer_fee"`
	ExchangeFee    string `json:"exchange_fee"`
	SubtotalAmount string `json:"subtotal_amount"`
	GasFee         string `json:"gas_fee"`
	FinalAmount    string `json:"final_amount"`
}

// BridgeTransferSourceDepositInstructions represents deposit instructions
type BridgeTransferSourceDepositInstructions struct {
	PaymentRail string `json:"payment_rail"`
	Amount      string `json:"amount"`
	Currency    string `json:"currency"`
	ToAddress   string `json:"to_address"`
	FromAddress string `json:"from_address"`
}

// CreateBridgeTransferData represents the data to be stored in database
type CreateBridgeTransferData struct {
	BridgeTransferID        string  `json:"bridge_transfer_id"`
	OrganizationID          int     `json:"organization_id"`
	BridgeExternalAccountID *string `json:"bridge_external_account_id"`
	Chain                   string  `json:"chain"`
	FromAddress             string  `json:"from_address"`
	Amount                  string  `json:"amount"`
	Currency                string  `json:"currency"`
	Status                  string  `json:"status"`
	DepositToAddress        string  `json:"deposit_to_address"`
}

// BridgeTransferData represents bridge transfer data from database
type BridgeTransferData struct {
	BridgeTransferID        string    `json:"bridge_transfer_id"`
	OrganizationID          int       `json:"organization_id"`
	BridgeExternalAccountID *string   `json:"bridge_external_account_id"`
	Chain                   string    `json:"chain"`
	FromAddress             string    `json:"from_address"`
	Amount                  string    `json:"amount"`
	Currency                string    `json:"currency"`
	Status                  string    `json:"status"`
	DepositToAddress        string    `json:"deposit_to_address"`
	CreatedAt               time.Time `json:"created_at"`
	UpdatedAt               time.Time `json:"updated_at"`
}

// Bridge API structures for creating transfers

// BridgeCreateTransferRequest represents the request to Bridge API for creating transfer
type BridgeCreateTransferRequest struct {
	Amount      string                     `json:"amount"`
	OnBehalfOf  string                     `json:"on_behalf_of"`
	Source      *BridgeTransferSource      `json:"source"`
	Destination *BridgeTransferDestination `json:"destination"`
}

// BridgeCreateTransferResponse represents the response from Bridge API
type BridgeCreateTransferResponse struct {
	ID                        string                                   `json:"id"`
	ClientReferenceID         *string                                  `json:"client_reference_id"`
	State                     string                                   `json:"state"`
	OnBehalfOf                string                                   `json:"on_behalf_of"`
	Currency                  string                                   `json:"currency"`
	Amount                    string                                   `json:"amount"`
	DeveloperFee              string                                   `json:"developer_fee"`
	Source                    *BridgeTransferSourceResponse            `json:"source"`
	Destination               *BridgeTransferDestinationResponse       `json:"destination"`
	CreatedAt                 string                                   `json:"created_at"`
	UpdatedAt                 string                                   `json:"updated_at"`
	Receipt                   *BridgeTransferReceipt                   `json:"receipt,omitempty"`
	SourceDepositInstructions *BridgeTransferSourceDepositInstructions `json:"source_deposit_instructions,omitempty"`
}
