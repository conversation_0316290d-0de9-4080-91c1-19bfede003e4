//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=tron_energy_repo_mock.go . TronEnergyRepo

package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// TronEnergyRepo .
type TronEnergyRepo interface {
	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *OrganizationWallets, kgErr *code.KGError)
}
