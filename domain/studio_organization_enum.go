// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// AssetProApprovalConfigTrader is a AssetProApprovalConfig of type trader.
	AssetProApprovalConfigTrader AssetProApprovalConfig = "trader"
	// AssetProApprovalConfigTraderApproverFinanceManager is a AssetProApprovalConfig of type trader-approver-finance_manager.
	AssetProApprovalConfigTraderApproverFinanceManager AssetProApprovalConfig = "trader-approver-finance_manager"
	// AssetProApprovalConfigTraderApprover is a AssetProApprovalConfig of type trader-approver.
	AssetProApprovalConfigTraderApprover AssetProApprovalConfig = "trader-approver"
)

var ErrInvalidAssetProApprovalConfig = errors.New("not a valid AssetProApprovalConfig")

// String implements the Stringer interface.
func (x AssetProApprovalConfig) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetProApprovalConfig) IsValid() bool {
	_, err := ParseAssetProApprovalConfig(string(x))
	return err == nil
}

var _AssetProApprovalConfigValue = map[string]AssetProApprovalConfig{
	"trader":                          AssetProApprovalConfigTrader,
	"trader-approver-finance_manager": AssetProApprovalConfigTraderApproverFinanceManager,
	"trader-approver":                 AssetProApprovalConfigTraderApprover,
}

// ParseAssetProApprovalConfig attempts to convert a string to a AssetProApprovalConfig.
func ParseAssetProApprovalConfig(name string) (AssetProApprovalConfig, error) {
	if x, ok := _AssetProApprovalConfigValue[name]; ok {
		return x, nil
	}
	return AssetProApprovalConfig(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetProApprovalConfig)
}

const (
	// StudioUserStatusPending is a StudioUserStatus of type pending.
	StudioUserStatusPending StudioUserStatus = "pending"
	// StudioUserStatusActive is a StudioUserStatus of type active.
	StudioUserStatusActive StudioUserStatus = "active"
	// StudioUserStatusInactive is a StudioUserStatus of type inactive.
	StudioUserStatusInactive StudioUserStatus = "inactive"
	// StudioUserStatusExpired is a StudioUserStatus of type expired.
	StudioUserStatusExpired StudioUserStatus = "expired"
)

var ErrInvalidStudioUserStatus = errors.New("not a valid StudioUserStatus")

// String implements the Stringer interface.
func (x StudioUserStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x StudioUserStatus) IsValid() bool {
	_, err := ParseStudioUserStatus(string(x))
	return err == nil
}

var _StudioUserStatusValue = map[string]StudioUserStatus{
	"pending":  StudioUserStatusPending,
	"active":   StudioUserStatusActive,
	"inactive": StudioUserStatusInactive,
	"expired":  StudioUserStatusExpired,
}

// ParseStudioUserStatus attempts to convert a string to a StudioUserStatus.
func ParseStudioUserStatus(name string) (StudioUserStatus, error) {
	if x, ok := _StudioUserStatusValue[name]; ok {
		return x, nil
	}
	return StudioUserStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidStudioUserStatus)
}
