//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=address_repo_mock.go . AddressRepo
package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// AddressRepo defines the repository interface for wallet address operations.
type AddressRepo interface {
	// CheckAddressOwnedByUser checks if the address' private key is owned by a user.
	CheckAddressOwnedByUser(ctx context.Context, chain Chain, address Address) (bool, error)
	// BatchCheckAddressOwnedByUser checks if the addresses' private keys are owned by a user.
	BatchCheckAddressOwnedByUser(ctx context.Context, chain Chain, addresses []Address) (map[Address]bool, error)
	// BatchCheckAddressIsActivePaymentAddress checks if the addresses are active payment addresses.
	BatchCheckAddressIsActivePaymentAddress(ctx context.Context, chain Chain, addresses []Address) (map[Address]bool, error)
	// GetUsersForAddressNotification returns users with valid FCM tokens for a given address and client ID. The wallet should be user's normal wallet, not observer wallet.
	GetUsersForAddressNotification(ctx context.Context, chainID, address, clientID string) ([]*UserData, *code.KGError)
}
