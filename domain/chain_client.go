//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=chain_client_mock.go . ChainClient
package domain

import (
	"context"
	"math/big"
)

// ChainClient is an interface to interact with a chain's RPC. This should be implemented for each chain
type ChainClient interface {
	// BlockNumber gets the current block number
	BlockNumber(ctx context.Context) (uint64, error)
	// NativeBalance gets the raw native balance of an address
	NativeBalance(ctx context.Context, address Address) (*big.Int, error)
	// TokenBalance gets the raw token balance of an address
	TokenBalance(ctx context.Context, address Address, tokenID string) (*big.Int, error)
	// WaitUntilTransactionConfirmed waits until a transaction is confirmed.
	// It should typically be used with a context with timeout so that it won't wait forever
	WaitUntilTransactionConfirmed(ctx context.Context, txHash string) (TransactionStatus, error)
	// TransactionDetail gets full transaction details by tx hash
	TransactionDetail(ctx context.Context, txHash string) (*TransactionDetail, error)
	// GetTransactionStatus gets the status of a transaction
	GetTransactionStatus(ctx context.Context, txHash string) (TransactionStatus, error)
	// BroadcastRawTransaction broadcasts a raw transaction
	BroadcastRawTransaction(ctx context.Context, signedTx string) (string, error)
}
