package domain

import (
	"context"
)

// TokenAnalysisCredit represents a user's token analysis credits
type TokenAnalysisCredit struct {
	WalletAddress string
	Credits       int
}

// TokenAnalysisTradingVolume represents a record of trading volume for token analysis credits
type TokenAnalysisTradingVolume struct {
	ID            int
	WalletAddress string
	TxHash        string
	VolumeUSD     float64
}

// TokenAnalysis represents a token analysis record
type TokenAnalysis struct {
	ID            int
	WalletAddress string
	TokenAddress  string
	Analysis      string
}

// TokenAnalysisCreditPurchase represents a record of credit purchase with SOL
type TokenAnalysisCreditPurchase struct {
	ID               int
	WalletAddress    string
	SolAmount        float64
	CreditsPurchased int
	TxHash           string
}

// TokenAnalysisRepo defines the interface for token analysis data operations
type TokenAnalysisRepo interface {
	// GetCredits gets a wallet's token analysis credits
	GetCredits(ctx context.Context, walletAddress string) (*TokenAnalysisCredit, error)

	// AddCredits adds credits to a wallet
	AddCredits(ctx context.Context, walletAddress string, credits int) error

	// UseCredit uses one credit from a wallet
	UseCredit(ctx context.Context, walletAddress string) error

	// RecordTradingVolume records a trading volume for a wallet
	RecordTradingVolume(ctx context.Context, volume *TokenAnalysisTradingVolume) error

	// GetTradingVolume gets a wallet's total trading volume
	GetTradingVolume(ctx context.Context, walletAddress string) (float64, error)

	// CreateAnalysis creates a new token analysis record
	CreateAnalysis(ctx context.Context, analysis *TokenAnalysis) (int, error)

	// GetAnalysis gets a token analysis record
	GetAnalysis(ctx context.Context, id int) (*TokenAnalysis, error)

	// TradingVolumeExists checks if a transaction hash already exists
	TradingVolumeExists(ctx context.Context, txHash string) (bool, error)

	// RecordCreditPurchase records a credit purchase transaction
	RecordCreditPurchase(ctx context.Context, purchase *TokenAnalysisCreditPurchase) error

	// CreditPurchaseExists checks if a transaction hash already exists in credit purchases
	CreditPurchaseExists(ctx context.Context, txHash string) (bool, error)

	// HasReceivedFreeCreditToday checks if a wallet has received free credit today (UTC+8)
	HasReceivedFreeCreditToday(ctx context.Context, walletAddress string) (bool, error)

	// RecordFreeCreditToday records that a wallet has received free credit today (UTC+8)
	RecordFreeCreditToday(ctx context.Context, walletAddress string) error
}
