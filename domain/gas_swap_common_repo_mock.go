// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: GasSwapCommonRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=gas_swap_common_repo_mock.go . GasSwapCommonRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"
	time "time"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockGasSwapCommonRepo is a mock of GasSwapCommonRepo interface.
type MockGasSwapCommonRepo struct {
	ctrl     *gomock.Controller
	recorder *MockGasSwapCommonRepoMockRecorder
}

// MockGasSwapCommonRepoMockRecorder is the mock recorder for MockGasSwapCommonRepo.
type MockGasSwapCommonRepoMockRecorder struct {
	mock *MockGasSwapCommonRepo
}

// NewMockGasSwapCommonRepo creates a new mock instance.
func NewMockGasSwapCommonRepo(ctrl *gomock.Controller) *MockGasSwapCommonRepo {
	mock := &MockGasSwapCommonRepo{ctrl: ctrl}
	mock.recorder = &MockGasSwapCommonRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGasSwapCommonRepo) EXPECT() *MockGasSwapCommonRepoMockRecorder {
	return m.recorder
}

// AcquireLock mocks base method.
func (m *MockGasSwapCommonRepo) AcquireLock(arg0 context.Context, arg1 string, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLock indicates an expected call of AcquireLock.
func (mr *MockGasSwapCommonRepoMockRecorder) AcquireLock(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLock", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).AcquireLock), arg0, arg1, arg2)
}

// AcquireLockWithRetry mocks base method.
func (m *MockGasSwapCommonRepo) AcquireLockWithRetry(arg0 context.Context, arg1 string, arg2, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLockWithRetry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLockWithRetry indicates an expected call of AcquireLockWithRetry.
func (mr *MockGasSwapCommonRepoMockRecorder) AcquireLockWithRetry(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLockWithRetry", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).AcquireLockWithRetry), arg0, arg1, arg2, arg3)
}

// BatchGetTokenPrices mocks base method.
func (m *MockGasSwapCommonRepo) BatchGetTokenPrices(arg0 context.Context, arg1 []ChainToken) (map[ChainToken]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPrices", arg0, arg1)
	ret0, _ := ret[0].(map[ChainToken]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPrices indicates an expected call of BatchGetTokenPrices.
func (mr *MockGasSwapCommonRepoMockRecorder) BatchGetTokenPrices(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPrices", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).BatchGetTokenPrices), arg0, arg1)
}

// BatchGetTokenPricesIn24H mocks base method.
func (m *MockGasSwapCommonRepo) BatchGetTokenPricesIn24H(arg0 context.Context, arg1 []ChainToken) (map[ChainToken][]*PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPricesIn24H", arg0, arg1)
	ret0, _ := ret[0].(map[ChainToken][]*PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPricesIn24H indicates an expected call of BatchGetTokenPricesIn24H.
func (mr *MockGasSwapCommonRepoMockRecorder) BatchGetTokenPricesIn24H(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPricesIn24H", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).BatchGetTokenPricesIn24H), arg0, arg1)
}

// CreateGasSwap mocks base method.
func (m *MockGasSwapCommonRepo) CreateGasSwap(arg0 context.Context, arg1 *GasSwap) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGasSwap", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGasSwap indicates an expected call of CreateGasSwap.
func (mr *MockGasSwapCommonRepoMockRecorder) CreateGasSwap(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGasSwap", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).CreateGasSwap), arg0, arg1)
}

// GetAllGasSwapSupportedTokens mocks base method.
func (m *MockGasSwapCommonRepo) GetAllGasSwapSupportedTokens(arg0 context.Context) ([]*GasSwapToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllGasSwapSupportedTokens", arg0)
	ret0, _ := ret[0].([]*GasSwapToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGasSwapSupportedTokens indicates an expected call of GetAllGasSwapSupportedTokens.
func (mr *MockGasSwapCommonRepoMockRecorder) GetAllGasSwapSupportedTokens(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGasSwapSupportedTokens", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetAllGasSwapSupportedTokens), arg0)
}

// GetAssetPrice mocks base method.
func (m *MockGasSwapCommonRepo) GetAssetPrice(arg0 context.Context, arg1, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockGasSwapCommonRepoMockRecorder) GetAssetPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetAssetPrice), arg0, arg1, arg2)
}

// GetGasSwapAvailableAssets mocks base method.
func (m *MockGasSwapCommonRepo) GetGasSwapAvailableAssets(arg0 context.Context, arg1 int, arg2, arg3, arg4 string) ([]*GasSwapAsset, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasSwapAvailableAssets", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*GasSwapAsset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasSwapAvailableAssets indicates an expected call of GetGasSwapAvailableAssets.
func (mr *MockGasSwapCommonRepoMockRecorder) GetGasSwapAvailableAssets(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasSwapAvailableAssets", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetGasSwapAvailableAssets), arg0, arg1, arg2, arg3, arg4)
}

// GetGasSwapAvailableChains mocks base method.
func (m *MockGasSwapCommonRepo) GetGasSwapAvailableChains(arg0 context.Context, arg1 int) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasSwapAvailableChains", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasSwapAvailableChains indicates an expected call of GetGasSwapAvailableChains.
func (mr *MockGasSwapCommonRepoMockRecorder) GetGasSwapAvailableChains(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasSwapAvailableChains", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetGasSwapAvailableChains), arg0, arg1)
}

// GetGasSwapByID mocks base method.
func (m *MockGasSwapCommonRepo) GetGasSwapByID(arg0 context.Context, arg1 int) (*GasSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasSwapByID", arg0, arg1)
	ret0, _ := ret[0].(*GasSwap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasSwapByID indicates an expected call of GetGasSwapByID.
func (mr *MockGasSwapCommonRepoMockRecorder) GetGasSwapByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasSwapByID", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetGasSwapByID), arg0, arg1)
}

// GetGasSwapOptions mocks base method.
func (m *MockGasSwapCommonRepo) GetGasSwapOptions(arg0 context.Context, arg1, arg2 string) ([]*GasSwapOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasSwapOptions", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*GasSwapOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasSwapOptions indicates an expected call of GetGasSwapOptions.
func (mr *MockGasSwapCommonRepoMockRecorder) GetGasSwapOptions(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasSwapOptions", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetGasSwapOptions), arg0, arg1, arg2)
}

// GetGasSwapSupportedTokens mocks base method.
func (m *MockGasSwapCommonRepo) GetGasSwapSupportedTokens(arg0 context.Context, arg1 int) ([]*GasSwapToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasSwapSupportedTokens", arg0, arg1)
	ret0, _ := ret[0].([]*GasSwapToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasSwapSupportedTokens indicates an expected call of GetGasSwapSupportedTokens.
func (mr *MockGasSwapCommonRepoMockRecorder) GetGasSwapSupportedTokens(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasSwapSupportedTokens", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetGasSwapSupportedTokens), arg0, arg1)
}

// GetNativeAssetPrice mocks base method.
func (m *MockGasSwapCommonRepo) GetNativeAssetPrice(arg0 context.Context, arg1 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockGasSwapCommonRepoMockRecorder) GetNativeAssetPrice(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetNativeAssetPrice), arg0, arg1)
}

// GetTokenPrice mocks base method.
func (m *MockGasSwapCommonRepo) GetTokenPrice(arg0 context.Context, arg1 Chain, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPrice indicates an expected call of GetTokenPrice.
func (mr *MockGasSwapCommonRepoMockRecorder) GetTokenPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPrice", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetTokenPrice), arg0, arg1, arg2)
}

// GetTokenPricesIn24H mocks base method.
func (m *MockGasSwapCommonRepo) GetTokenPricesIn24H(arg0 context.Context, arg1 Chain, arg2 string) ([]*PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPricesIn24H", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPricesIn24H indicates an expected call of GetTokenPricesIn24H.
func (mr *MockGasSwapCommonRepoMockRecorder) GetTokenPricesIn24H(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPricesIn24H", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetTokenPricesIn24H), arg0, arg1, arg2)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockGasSwapCommonRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockGasSwapCommonRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// HasProcessingGasSwap mocks base method.
func (m *MockGasSwapCommonRepo) HasProcessingGasSwap(arg0 context.Context, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasProcessingGasSwap", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasProcessingGasSwap indicates an expected call of HasProcessingGasSwap.
func (mr *MockGasSwapCommonRepoMockRecorder) HasProcessingGasSwap(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasProcessingGasSwap", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).HasProcessingGasSwap), arg0, arg1)
}

// IsGasSwapTokenSupported mocks base method.
func (m *MockGasSwapCommonRepo) IsGasSwapTokenSupported(arg0 context.Context, arg1 int, arg2, arg3 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsGasSwapTokenSupported", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsGasSwapTokenSupported indicates an expected call of IsGasSwapTokenSupported.
func (mr *MockGasSwapCommonRepoMockRecorder) IsGasSwapTokenSupported(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsGasSwapTokenSupported", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).IsGasSwapTokenSupported), arg0, arg1, arg2, arg3)
}

// PendingGasSwapCostSum mocks base method.
func (m *MockGasSwapCommonRepo) PendingGasSwapCostSum(arg0 context.Context, arg1 int, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PendingGasSwapCostSum", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PendingGasSwapCostSum indicates an expected call of PendingGasSwapCostSum.
func (mr *MockGasSwapCommonRepoMockRecorder) PendingGasSwapCostSum(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PendingGasSwapCostSum", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).PendingGasSwapCostSum), arg0, arg1, arg2)
}

// ReleaseLock mocks base method.
func (m *MockGasSwapCommonRepo) ReleaseLock(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseLock", arg0, arg1)
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockGasSwapCommonRepoMockRecorder) ReleaseLock(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).ReleaseLock), arg0, arg1)
}

// SaveGasSwapSupportedTokens mocks base method.
func (m *MockGasSwapCommonRepo) SaveGasSwapSupportedTokens(arg0 context.Context, arg1 int, arg2 []*GasSwapToken) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveGasSwapSupportedTokens", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveGasSwapSupportedTokens indicates an expected call of SaveGasSwapSupportedTokens.
func (mr *MockGasSwapCommonRepoMockRecorder) SaveGasSwapSupportedTokens(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveGasSwapSupportedTokens", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).SaveGasSwapSupportedTokens), arg0, arg1, arg2)
}

// UpdateGasSwap mocks base method.
func (m *MockGasSwapCommonRepo) UpdateGasSwap(arg0 context.Context, arg1 *GasSwapUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGasSwap", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGasSwap indicates an expected call of UpdateGasSwap.
func (mr *MockGasSwapCommonRepoMockRecorder) UpdateGasSwap(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGasSwap", reflect.TypeOf((*MockGasSwapCommonRepo)(nil).UpdateGasSwap), arg0, arg1)
}
