// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: RealtimeAssetPriceSetRepo)
//
// Generated by this command:
//
//	mockgen -package=cache -destination=realtime_price_repo_mock.go github.com/kryptogo/kg-wallet-backend/domain RealtimeAssetPriceSetRepo
//

// Package cache is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockRealtimeAssetPriceSetRepo is a mock of RealtimeAssetPriceSetRepo interface.
type MockRealtimeAssetPriceSetRepo struct {
	ctrl     *gomock.Controller
	recorder *MockRealtimeAssetPriceSetRepoMockRecorder
	isgomock struct{}
}

// MockRealtimeAssetPriceSetRepoMockRecorder is the mock recorder for MockRealtimeAssetPriceSetRepo.
type MockRealtimeAssetPriceSetRepoMockRecorder struct {
	mock *MockRealtimeAssetPriceSetRepo
}

// NewMockRealtimeAssetPriceSetRepo creates a new mock instance.
func NewMockRealtimeAssetPriceSetRepo(ctrl *gomock.Controller) *MockRealtimeAssetPriceSetRepo {
	mock := &MockRealtimeAssetPriceSetRepo{ctrl: ctrl}
	mock.recorder = &MockRealtimeAssetPriceSetRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRealtimeAssetPriceSetRepo) EXPECT() *MockRealtimeAssetPriceSetRepoMockRecorder {
	return m.recorder
}

// AcquireRealtimeTokenPriceTask mocks base method.
func (m *MockRealtimeAssetPriceSetRepo) AcquireRealtimeTokenPriceTask(ctx context.Context, limit int) ([]RealtimeTokenPriceTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireRealtimeTokenPriceTask", ctx, limit)
	ret0, _ := ret[0].([]RealtimeTokenPriceTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcquireRealtimeTokenPriceTask indicates an expected call of AcquireRealtimeTokenPriceTask.
func (mr *MockRealtimeAssetPriceSetRepoMockRecorder) AcquireRealtimeTokenPriceTask(ctx, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireRealtimeTokenPriceTask", reflect.TypeOf((*MockRealtimeAssetPriceSetRepo)(nil).AcquireRealtimeTokenPriceTask), ctx, limit)
}

// AddRealtimeTokenPriceResponse mocks base method.
func (m *MockRealtimeAssetPriceSetRepo) AddRealtimeTokenPriceResponse(ctx context.Context, info []RealtimeTokenPriceResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRealtimeTokenPriceResponse", ctx, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRealtimeTokenPriceResponse indicates an expected call of AddRealtimeTokenPriceResponse.
func (mr *MockRealtimeAssetPriceSetRepoMockRecorder) AddRealtimeTokenPriceResponse(ctx, info any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRealtimeTokenPriceResponse", reflect.TypeOf((*MockRealtimeAssetPriceSetRepo)(nil).AddRealtimeTokenPriceResponse), ctx, info)
}

// AddRealtimeTokenPriceTask mocks base method.
func (m *MockRealtimeAssetPriceSetRepo) AddRealtimeTokenPriceTask(ctx context.Context, info []RealtimeTokenPriceTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRealtimeTokenPriceTask", ctx, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRealtimeTokenPriceTask indicates an expected call of AddRealtimeTokenPriceTask.
func (mr *MockRealtimeAssetPriceSetRepoMockRecorder) AddRealtimeTokenPriceTask(ctx, info any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRealtimeTokenPriceTask", reflect.TypeOf((*MockRealtimeAssetPriceSetRepo)(nil).AddRealtimeTokenPriceTask), ctx, info)
}

// GetRealtimeTokenPrice mocks base method.
func (m *MockRealtimeAssetPriceSetRepo) GetRealtimeTokenPrice(ctx context.Context, chainTokens []ChainToken) ([]RealtimeTokenPriceResponse, []map[string]any, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRealtimeTokenPrice", ctx, chainTokens)
	ret0, _ := ret[0].([]RealtimeTokenPriceResponse)
	ret1, _ := ret[1].([]map[string]any)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRealtimeTokenPrice indicates an expected call of GetRealtimeTokenPrice.
func (mr *MockRealtimeAssetPriceSetRepoMockRecorder) GetRealtimeTokenPrice(ctx, chainTokens any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRealtimeTokenPrice", reflect.TypeOf((*MockRealtimeAssetPriceSetRepo)(nil).GetRealtimeTokenPrice), ctx, chainTokens)
}
