package domain

import (
	"context"
	"errors"
)

// ErrGaslessSendNotFound .
var ErrGaslessSendNotFound = errors.New("gasless send not found")

// GaslessSendRepo .
type GaslessSendRepo interface {
	CreateGaslessSend(ctx context.Context, gaslessSend *GaslessSend) (int, error)
	UpdateGaslessSend(ctx context.Context, update *UpdateGaslessSendRequest) error
	// GetGaslessSendByID should return ErrGaslessSendNotFound if gasless send not found
	GetGaslessSendByID(ctx context.Context, id int) (*GaslessSend, error)

	CreateGaslessSendV2(ctx context.Context, gaslessSend *GaslessSendV2) (int, error)
	UpdateGaslessSendV2(ctx context.Context, update *UpdateGaslessSendV2Request) error
	// GetGaslessSendByID should return ErrGaslessSendNotFound if gasless send not found
	GetGaslessSendV2ByID(ctx context.Context, id int) (*GaslessSendV2, error)
}
