package domain

import (
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/stretchr/testify/assert"
)

func TestStrAddress(t *testing.T) {
	addr := NewStrAddress("test_address")
	assert.Equal(t, "test_address", addr.String(), "StrAddress.String() should return the correct string")

	addr2 := NewStrAddress("test_address")
	assert.True(t, addr.Equal(addr2), "StrAddress.Equal() should return true for identical addresses")

	addr3 := NewStrAddress("different_address")
	assert.False(t, addr.Equal(addr3), "StrAddress.Equal() should return false for different addresses")
}

func TestEvmAddress(t *testing.T) {
	addr := NewEvmAddress("******************************************")
	assert.Equal(t, "******************************************", addr.String(), "EvmAddress.String() should return the correct string")

	addr2 := NewEvmAddress("******************************************")
	assert.True(t, addr.Equal(addr2), "EvmAddress.Equal() should return true for identical addresses")

	addr3 := NewEvmAddress("0x1234567890123456789012345678901234567890")
	assert.False(t, addr.Equal(addr3), "EvmAddress.Equal() should return false for different addresses")

	// Test case insensitivity
	addrLower := NewEvmAddress("0x742d35cc6634c0532925a3b844bc454e4438f44e")
	assert.True(t, addr.Equal(addrLower), "EvmAddress.Equal() should return true for same address with different case")

	// Test invalid address
	invalidAddr := NewEvmAddress("0xinvalid")
	assert.Zero(t, invalidAddr.Big().Cmp(big.NewInt(0)), "EvmAddress() should return nil for invalid address")
}

func TestTronAddress(t *testing.T) {
	addr := NewTronAddress("TJRabPrwbZy45sbavfcjinPJC18kjpRTv8")
	assert.NotNil(t, addr, "TronAddress() should not return nil for valid address")
	assert.Equal(t, "TJRabPrwbZy45sbavfcjinPJC18kjpRTv8", addr.String(), "TronAddress.String() should return the correct string")
	t.Logf("addr: %s", addr.Hex())

	addr2 := NewTronAddress("TJRabPrwbZy45sbavfcjinPJC18kjpRTv8")
	assert.True(t, addr.Equal(addr2), "TronAddress.Equal() should return true for identical addresses")

	addr3 := NewTronAddress("TN3W4H6rK2ce4vX9YnFQHwKENnHjoxb3m9")
	assert.False(t, addr.Equal(addr3), "TronAddress.Equal() should return false for different addresses")

	addr4 := NewTronAddress("0x5CbDd86a2FA8Dc4bDdd8a8f69dBa48572EeC07FB")
	assert.True(t, addr.Equal(addr4), "TronAddress.Equal() should return true for identical addresses")

	invalidAddr := NewTronAddress("invalid_address")
	assert.Zero(t, invalidAddr.Big().Cmp(big.NewInt(0)), "TronAddress() should return nil for invalid address")
}

func TestDecodeCheck(t *testing.T) {
	validAddress := "TJRabPrwbZy45sbavfcjinPJC18kjpRTv8"
	decoded, err := decodeBase58Check(validAddress)
	assert.NoError(t, err, "DecodeCheck() should not return error for valid address")
	assert.Len(t, decoded, 20, "DecodeCheck() should return 20 bytes for valid address")

	invalidAddress := "invalid_address"
	_, err = decodeBase58Check(invalidAddress)
	assert.Error(t, err, "DecodeCheck() should return error for invalid address")
}

func TestAddressMap(t *testing.T) {
	addressMap := make(map[Address]string)

	strAddr := NewStrAddress("test_str_address")
	evmAddr := NewEvmAddress("******************************************")
	evmAddr2 := NewEvmAddress("0x742d35cc6634c0532925a3b844bc454e4438f44e")
	tronAddr := NewTronAddress("TJRabPrwbZy45sbavfcjinPJC18kjpRTv8")

	addressMap[strAddr] = "String Address"
	addressMap[evmAddr] = "EVM Address"
	addressMap[evmAddr2] = "EVM Address 2"
	addressMap[tronAddr] = "Tron Address"

	assert.Len(t, addressMap, 3, "Address map should contain 3 different entries")

	assert.Equal(t, "String Address", addressMap[strAddr], "String address should be correctly stored and retrieved")
	assert.Equal(t, "EVM Address 2", addressMap[evmAddr], "EVM address should be correctly stored and retrieved")
	assert.Equal(t, "EVM Address 2", addressMap[evmAddr2], "EVM address with different case should retrieve the same value")
	assert.Equal(t, "Tron Address", addressMap[tronAddr], "Tron address should be correctly stored and retrieved")

	differentStrAddr := NewStrAddress("different_str_address")
	assert.NotEqual(t, addressMap[strAddr], addressMap[differentStrAddr], "Different string addresses should not be equal")

	differentEvmAddr := NewEvmAddress("0x1234567890123456789012345678901234567890")
	assert.NotEqual(t, addressMap[evmAddr], addressMap[differentEvmAddr], "Different EVM addresses should not be equal")

	differentTronAddr := NewTronAddress("TN3W4H6rK2ce4vX9YnFQHwKENnHjoxb3m9")
	assert.NotEqual(t, addressMap[tronAddr], addressMap[differentTronAddr], "Different Tron addresses should not be equal")

	evmAddr = EvmAddress{common.HexToAddress("0x1234")}
	tronAddr = TronAddress{common.HexToAddress("0x1234")}
	assert.NotEqual(t, evmAddr, tronAddr, "EVM address and Tron address should not be equal")
}
