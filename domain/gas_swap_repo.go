//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=gas_swap_common_repo_mock.go . GasSwapCommonRepo
//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=gas_swap_v2_repo_mock.go . GasSwapV2Repo

package domain

import (
	"context"
	"errors"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// ErrAssetNotFound .
var ErrAssetNotFound = errors.New("asset not found")

// ErrGasSwapNotFound .
var ErrGasSwapNotFound = errors.New("gas swap not found")

// ErrChainNotSupported .
var ErrChainNotSupported = errors.New("chain not supported")

// GasSwapCommonRepo has methods shared by both v1 and v2 gas swap
type GasSwapCommonRepo interface {
	LockManager
	AssetPriceRepo

	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *OrganizationWallets, kgErr *code.KGError)
	GetGasSwapSupportedTokens(ctx context.Context, orgID int) ([]*GasSwapToken, error)
	GetAllGasSwapSupportedTokens(ctx context.Context) ([]*GasSwapToken, error)
	SaveGasSwapSupportedTokens(ctx context.Context, orgID int, tokens []*GasSwapToken) error
	IsGasSwapTokenSupported(ctx context.Context, orgID int, chainID, tokenAddress string) (bool, error)
	GetGasSwapAvailableChains(ctx context.Context, orgID int) ([]string, error)
	// GetGasSwapAvailableAssets should return ErrChainNotSupported if chain not supported
	GetGasSwapAvailableAssets(ctx context.Context, orgID int, uid, chainID, path string) ([]*GasSwapAsset, error)
	// GetGasSwapOptions should return ErrChainNotSupported if chain not supported
	GetGasSwapOptions(ctx context.Context, chainID string, tokenAddress string) ([]*GasSwapOption, error)

	HasProcessingGasSwap(ctx context.Context, uid string) (bool, error)
	// PendingGasSwapCostSum should return ErrChainNotSupported if chain not supported
	PendingGasSwapCostSum(ctx context.Context, orgID int, chainID string) (float64, error)

	GetGasSwapByID(ctx context.Context, id int) (*GasSwap, error)
	CreateGasSwap(ctx context.Context, gasSwap *GasSwap) (int, error)
	UpdateGasSwap(ctx context.Context, update *GasSwapUpdate) error
}

type GasSwapV2Repo interface {
	GasSwapCommonRepo
	AssetProFinanceRepo
	EnergyRentCostRepo
}
