// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// AssetProProductBaseCurrencyUSDT is a AssetProProductBaseCurrency of type USDT.
	AssetProProductBaseCurrencyUSDT AssetProProductBaseCurrency = "USDT"
	// AssetProProductBaseCurrencyUSDC is a AssetProProductBaseCurrency of type USDC.
	AssetProProductBaseCurrencyUSDC AssetProProductBaseCurrency = "USDC"
)

var ErrInvalidAssetProProductBaseCurrency = errors.New("not a valid AssetProProductBaseCurrency")

// String implements the Stringer interface.
func (x AssetProProductBaseCurrency) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetProProductBaseCurrency) IsValid() bool {
	_, err := ParseAssetProProductBaseCurrency(string(x))
	return err == nil
}

var _AssetProProductBaseCurrencyValue = map[string]AssetProProductBaseCurrency{
	"USDT": AssetProProductBaseCurrencyUSDT,
	"USDC": AssetProProductBaseCurrencyUSDC,
}

// ParseAssetProProductBaseCurrency attempts to convert a string to a AssetProProductBaseCurrency.
func ParseAssetProProductBaseCurrency(name string) (AssetProProductBaseCurrency, error) {
	if x, ok := _AssetProProductBaseCurrencyValue[name]; ok {
		return x, nil
	}
	return AssetProProductBaseCurrency(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetProProductBaseCurrency)
}

const (
	// AssetProProductFeeTypeNoFee is a AssetProProductFeeType of type no_fee.
	AssetProProductFeeTypeNoFee AssetProProductFeeType = "no_fee"
	// AssetProProductFeeTypeFeeIncluded is a AssetProProductFeeType of type fee_included.
	AssetProProductFeeTypeFeeIncluded AssetProProductFeeType = "fee_included"
)

var ErrInvalidAssetProProductFeeType = errors.New("not a valid AssetProProductFeeType")

// String implements the Stringer interface.
func (x AssetProProductFeeType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetProProductFeeType) IsValid() bool {
	_, err := ParseAssetProProductFeeType(string(x))
	return err == nil
}

var _AssetProProductFeeTypeValue = map[string]AssetProProductFeeType{
	"no_fee":       AssetProProductFeeTypeNoFee,
	"fee_included": AssetProProductFeeTypeFeeIncluded,
}

// ParseAssetProProductFeeType attempts to convert a string to a AssetProProductFeeType.
func ParseAssetProProductFeeType(name string) (AssetProProductFeeType, error) {
	if x, ok := _AssetProProductFeeTypeValue[name]; ok {
		return x, nil
	}
	return AssetProProductFeeType(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetProProductFeeType)
}

const (
	// AssetProProductQuoteCurrencyTWD is a AssetProProductQuoteCurrency of type TWD.
	AssetProProductQuoteCurrencyTWD AssetProProductQuoteCurrency = "TWD"
)

var ErrInvalidAssetProProductQuoteCurrency = errors.New("not a valid AssetProProductQuoteCurrency")

// String implements the Stringer interface.
func (x AssetProProductQuoteCurrency) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetProProductQuoteCurrency) IsValid() bool {
	_, err := ParseAssetProProductQuoteCurrency(string(x))
	return err == nil
}

var _AssetProProductQuoteCurrencyValue = map[string]AssetProProductQuoteCurrency{
	"TWD": AssetProProductQuoteCurrencyTWD,
}

// ParseAssetProProductQuoteCurrency attempts to convert a string to a AssetProProductQuoteCurrency.
func ParseAssetProProductQuoteCurrency(name string) (AssetProProductQuoteCurrency, error) {
	if x, ok := _AssetProProductQuoteCurrencyValue[name]; ok {
		return x, nil
	}
	return AssetProProductQuoteCurrency(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetProProductQuoteCurrency)
}

const (
	// AssetProProductTypeBuyCrypto is a AssetProProductType of type buy_crypto.
	AssetProProductTypeBuyCrypto AssetProProductType = "buy_crypto"
)

var ErrInvalidAssetProProductType = errors.New("not a valid AssetProProductType")

// String implements the Stringer interface.
func (x AssetProProductType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetProProductType) IsValid() bool {
	_, err := ParseAssetProProductType(string(x))
	return err == nil
}

var _AssetProProductTypeValue = map[string]AssetProProductType{
	"buy_crypto": AssetProProductTypeBuyCrypto,
}

// ParseAssetProProductType attempts to convert a string to a AssetProProductType.
func ParseAssetProProductType(name string) (AssetProProductType, error) {
	if x, ok := _AssetProProductTypeValue[name]; ok {
		return x, nil
	}
	return AssetProProductType(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetProProductType)
}
