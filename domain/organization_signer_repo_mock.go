// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: OrganizationWalletRepo,OrganizationSignerRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=organization_signer_repo_mock.go . OrganizationWalletRepo,OrganizationSignerRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockOrganizationWalletRepo is a mock of OrganizationWalletRepo interface.
type MockOrganizationWalletRepo struct {
	ctrl     *gomock.Controller
	recorder *MockOrganizationWalletRepoMockRecorder
}

// MockOrganizationWalletRepoMockRecorder is the mock recorder for MockOrganizationWalletRepo.
type MockOrganizationWalletRepoMockRecorder struct {
	mock *MockOrganizationWalletRepo
}

// NewMockOrganizationWalletRepo creates a new mock instance.
func NewMockOrganizationWalletRepo(ctrl *gomock.Controller) *MockOrganizationWalletRepo {
	mock := &MockOrganizationWalletRepo{ctrl: ctrl}
	mock.recorder = &MockOrganizationWalletRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrganizationWalletRepo) EXPECT() *MockOrganizationWalletRepoMockRecorder {
	return m.recorder
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockOrganizationWalletRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockOrganizationWalletRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockOrganizationWalletRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// MockOrganizationSignerRepo is a mock of OrganizationSignerRepo interface.
type MockOrganizationSignerRepo struct {
	ctrl     *gomock.Controller
	recorder *MockOrganizationSignerRepoMockRecorder
}

// MockOrganizationSignerRepoMockRecorder is the mock recorder for MockOrganizationSignerRepo.
type MockOrganizationSignerRepoMockRecorder struct {
	mock *MockOrganizationSignerRepo
}

// NewMockOrganizationSignerRepo creates a new mock instance.
func NewMockOrganizationSignerRepo(ctrl *gomock.Controller) *MockOrganizationSignerRepo {
	mock := &MockOrganizationSignerRepo{ctrl: ctrl}
	mock.recorder = &MockOrganizationSignerRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrganizationSignerRepo) EXPECT() *MockOrganizationSignerRepoMockRecorder {
	return m.recorder
}

// GetOrganizationName mocks base method.
func (m *MockOrganizationSignerRepo) GetOrganizationName(arg0 context.Context, arg1 int) (string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrganizationName", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetOrganizationName indicates an expected call of GetOrganizationName.
func (mr *MockOrganizationSignerRepoMockRecorder) GetOrganizationName(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrganizationName", reflect.TypeOf((*MockOrganizationSignerRepo)(nil).GetOrganizationName), arg0, arg1)
}

// GetSignerByOrganizationId mocks base method.
func (m *MockOrganizationSignerRepo) GetSignerByOrganizationId(arg0 context.Context, arg1 int, arg2 PrivateKeyEncryptor) (*OrganizationSigner, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSignerByOrganizationId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*OrganizationSigner)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetSignerByOrganizationId indicates an expected call of GetSignerByOrganizationId.
func (mr *MockOrganizationSignerRepoMockRecorder) GetSignerByOrganizationId(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignerByOrganizationId", reflect.TypeOf((*MockOrganizationSignerRepo)(nil).GetSignerByOrganizationId), arg0, arg1, arg2)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockOrganizationSignerRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockOrganizationSignerRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockOrganizationSignerRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// SaveAuditLog mocks base method.
func (m *MockOrganizationSignerRepo) SaveAuditLog(arg0 context.Context, arg1 *SigningAuditLog) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveAuditLog", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// SaveAuditLog indicates an expected call of SaveAuditLog.
func (mr *MockOrganizationSignerRepoMockRecorder) SaveAuditLog(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveAuditLog", reflect.TypeOf((*MockOrganizationSignerRepo)(nil).SaveAuditLog), arg0, arg1)
}

// SaveOrganizationSigner mocks base method.
func (m *MockOrganizationSignerRepo) SaveOrganizationSigner(arg0 context.Context, arg1 int, arg2 *OrganizationSigner) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveOrganizationSigner", arg0, arg1, arg2)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// SaveOrganizationSigner indicates an expected call of SaveOrganizationSigner.
func (mr *MockOrganizationSignerRepoMockRecorder) SaveOrganizationSigner(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveOrganizationSigner", reflect.TypeOf((*MockOrganizationSignerRepo)(nil).SaveOrganizationSigner), arg0, arg1, arg2)
}
