//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=ephemeral_note_repo_mock.go . EphemeralNoteRepo

package domain

import (
	"context"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

type EphemeralNoteRepo interface {
	LockManager
	NotificationRepo
	AssetPriceRepo

	// GetUnusedEphemeralOwner returns empty address with no err if no unused ephemeral owner is available
	GetUnusedEphemeralOwner(ctx context.Context, ownerType EphemeralOwnerType) (common.Address, error)
	SetEphemeralOwnerStatus(ctx context.Context, address common.Address, isUsing bool) error
	SaveEphemeralOwner(ctx context.Context, owner *EphemeralOwner) error
	GetEphemeralOwner(ctx context.Context, address common.Address) (*EphemeralOwner, error)

	GetUsers(ctx context.Context, uids []string, clientID string, withUserData bool, preloads *UserPreloads) ([]*UserData, *code.KGError)
	GetUserWalletAddresses(ctx context.Context, uid string) ([]string, *code.KGError)
	GetDefaultReceiveAddress(ctx context.Context, uid string, chainID string) (string, *code.KGError)
	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *OrganizationWallets, kgErr *code.KGError)

	CreateEphemeralNote(ctx context.Context, note *EphemeralNote) error
	// GetEphemeralNoteByID should return ErrRecordNotFound if note not found
	GetEphemeralNoteByID(ctx context.Context, id string) (*EphemeralNote, error)
	// GetEphemeralNoteByDepositTx should return ErrRecordNotFound if note not found
	GetEphemeralNoteByDepositTx(ctx context.Context, txHash common.Hash) (*EphemeralNote, error)
	UpdateEphemeralNoteClaimed(ctx context.Context, id string, claimTxHash common.Hash) error
	GetActiveNotesByEphemeralOwner(ctx context.Context, ephemeralOwner common.Address) ([]*EphemeralNote, error)
	UpdateEphemeralNoteStatus(ctx context.Context, noteID string, status EphemeralNoteStatus) error
	GetEphemeralNotesByAddress(ctx context.Context, userAddress string) ([]*EphemeralNote, error)
	GetActiveNotesByAddresses(ctx context.Context, addresses []string) ([]*EphemeralNote, error)
	GetAllEphemeralNotes(ctx context.Context, addresses []string) ([]*EphemeralNote, error)

	// for send link campaign
	GetUsersWithBalanceAbove(ctx context.Context, minBalance float64) ([]string, error)
	// CreateSendLinkCampaignUsers creates multiple SendLinkCampaignUser records. If a user already exists, it will be skipped.
	CreateSendLinkCampaignUsers(ctx context.Context, users []*SendLinkCampaignUser) error
	// GetSendLinkCampaignUser should return ErrRecordNotFound if user not found
	GetSendLinkCampaignUser(ctx context.Context, uid string) (*SendLinkCampaignUser, error)
	UpdateSendLinkCampaignUser(ctx context.Context, update *SendLinkCampaignUserUpdate) error
	// GetUserFirstTransactionTime returns nil if user has no transactions
	GetUserFirstTransactionTime(ctx context.Context, uid string) (*time.Time, error)
	// GetSendLinkCampaignAnnouncementUser get those who haven't been sent notification about campaign
	GetSendLinkCampaignAnnouncementUser(ctx context.Context) ([]string, error)
	// SetSendLinkCampaignAnnouncementSent set the user as sent the notification
	SetSendLinkCampaignAnnouncementSent(ctx context.Context, uids []string) error
}
