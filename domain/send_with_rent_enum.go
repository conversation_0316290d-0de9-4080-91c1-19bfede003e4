// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// SendWithRentStatusSuccess is a SendWithRentStatus of type success.
	SendWithRentStatusSuccess SendWithRentStatus = "success"
	// SendWithRentStatusFailed is a SendWithRentStatus of type failed.
	SendWithRentStatusFailed SendWithRentStatus = "failed"
	// SendWithRentStatusProcessing is a SendWithRentStatus of type processing.
	SendWithRentStatusProcessing SendWithRentStatus = "processing"
)

var ErrInvalidSendWithRentStatus = errors.New("not a valid SendWithRentStatus")

// String implements the Stringer interface.
func (x SendWithRentStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x SendWithRentStatus) IsValid() bool {
	_, err := ParseSendWithRentStatus(string(x))
	return err == nil
}

var _SendWithRentStatusValue = map[string]SendWithRentStatus{
	"success":    SendWithRentStatusSuccess,
	"failed":     SendWithRentStatusFailed,
	"processing": SendWithRentStatusProcessing,
}

// ParseSendWithRentStatus attempts to convert a string to a SendWithRentStatus.
func ParseSendWithRentStatus(name string) (SendWithRentStatus, error) {
	if x, ok := _SendWithRentStatusValue[name]; ok {
		return x, nil
	}
	return SendWithRentStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidSendWithRentStatus)
}
