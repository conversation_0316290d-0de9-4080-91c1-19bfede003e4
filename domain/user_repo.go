//go:generate go-enum
//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=user_repo_mock.go . UserRepo
package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// UserPreloads .
type UserPreloads struct {
	WithFcm                    bool
	WithAvatar                 bool
	WithLocale                 bool
	WithReadAllTimeStamp       bool
	WithPrivacyPolicyAgreement bool
	WithWallets                bool
	WithVaultData              bool
	WithGoogleAccessTokens     bool
}

// SupportedUserField display_name, fcm_tokens, locale, app_version, device_identifier, privacy_policy_agreement, bio, twitter, youtube, instagram, discord, custom_link, hide_spam_nft, handle
// ENUM(display_name, fcm_tokens, locale, app_version, device_identifier, privacy_policy_agreement, bio, twitter, youtube, instagram, discord, custom_link, hide_spam_nft, handle)
type Supported<PERSON><PERSON><PERSON>ield string

// AllUserDataField all user data fields
// ENUM(email, display_name, phone_number, referral_code, fcm_tokens, fcm_token_map, avatar, locale, locale_map, read_all_timestamp, read_all_timestamp_map, SSCreateTime, alchemy_notify_address, device_identifier, created_at, privacy_policy_agreement, bio, twitter, youtube, instagram, discord, custom_link, hide_spam_nft, has_linked_google, is_random_password, bitcoin_address, ethereum_address, solana_address, tron_address, encrypt_salt, encrypted_mnemonic, wallets, kyc_state, vault_data, password, password_salt, password_salt_frontend, share_key, google_access_tokens)
type AllUserDataField string

// SavePasswordParams save password params
type SavePasswordParams struct {
	Password             string
	IsRandomPassword     *bool
	PasswordSalt         string
	PasswordSaltFrontend *string
	VaultData            *VaultData
}

// UserReadRepo .
type UserReadRepo interface {
	// GetUser returns a user by uid
	GetUser(ctx context.Context, uid, clientID string, withUserData bool, preloads *UserPreloads) (*UserData, *code.KGError)
	// GetUsers returns a list of users
	GetUsers(ctx context.Context, uids []string, clientID string, withUserData bool, preloads *UserPreloads) ([]*UserData, *code.KGError)
	// GetUserByPhoneNumber returns a user by phone number
	GetUserByPhoneNumber(ctx context.Context, phoneNumber, clientID string, withUserData bool, preloads *UserPreloads) (*UserData, *code.KGError)
	// GetUsersByPhoneNumbers returns a list of users by phone numbers
	GetUsersByPhoneNumbers(ctx context.Context, phoneNumbers []string, clientID string, withUserData bool, preloads *UserPreloads) ([]*UserData, *code.KGError)
	// GetUserByEmail returns a user by email
	GetUserByEmail(ctx context.Context, email, clientID string, withUserData bool, preloads *UserPreloads) (*UserData, *code.KGError)
	// GetUsersByEmails returns a list of users by emails
	GetUsersByEmails(ctx context.Context, emails []string, clientID string, withUserData bool, preloads *UserPreloads) ([]*UserData, *code.KGError)
	// GetUserByHandle returns a user by handle
	GetUserByHandle(ctx context.Context, handle, clientID string, withUserData bool, preloads *UserPreloads) (*UserData, *code.KGError)
	// UserAll returns all users
	UserAll(ctx context.Context, clientID string, withUserData bool, preloads *UserPreloads) ([]*UserData, *code.KGError)
	// UserListAfterWithLimit returns a list of users after a uid with limit
	UserListAfterWithLimit(ctx context.Context, uid *string, limit int, clientID string, withUserData bool, preloads *UserPreloads) ([]*UserData, *code.KGError)
	// GetUsersByEthAddressWithValidFcmToken returns a list of users by eth address with valid fcm token
	GetUsersByEthAddressWithValidFcmToken(ctx context.Context, ethAddress, clientID string, withUserData bool, preloads *UserPreloads) ([]*UserData, *code.KGError)
	// GetUsersWithValidFcmToken returns a list of users with valid fcm token
	GetUsersWithValidFcmToken(ctx context.Context, uids []string, clientID string, withUserData bool, preloads *UserPreloads) ([]*UserData, *code.KGError)
	// CountByUidWithRegisterWallet counts by uid with register wallet
	CountByUidWithRegisterWallet(ctx context.Context, uids []string) (int, error)
	// GetAllEvmAddresses returns all evm addresses
	GetAllEvmAddresses(ctx context.Context, excludeObserver bool) ([]string, error)
}

// UserWriteRepo .
type UserWriteRepo interface {
	// CreateUser creates a user
	CreateUser(ctx context.Context, uid string, userData *UserData) *code.KGError
	// BatchSetUsers sets users
	//
	// WARNING: DANGEROUS OPERATION - This function allows bulk modification of user data
	// and should be used only in testing environments.
	BatchSetUsers(ctx context.Context, users map[string]UserData) *code.KGError
	// SetUser sets a user
	SetUser(ctx context.Context, user *UserData) error
	// UserNotificationReadAll marks all notifications as read
	UserNotificationReadAll(ctx context.Context, uid, clientID string) error
	// DeleteUserFields deletes user fields
	DeleteUserFields(ctx context.Context, uid string, fields []string) error
	// SaveUserAvatar saves user avatar
	SaveUserAvatar(ctx context.Context, uid, chainID, contractAddress, tokenID, avatarURL string) error
	// RemoveAvatar removes user avatar
	RemoveAvatar(ctx context.Context, chainID, contractAddress, tokenID string) error
	// DeleteUserMnemonic deletes user mnemonic
	DeleteUserMnemonic(ctx context.Context, uid string) error
	// DeleteUser deletes a user
	DeleteUser(ctx context.Context, uid string) error
	// SaveUserVaultData saves user vault data
	SaveUserVaultData(ctx context.Context, uid string, data *VaultData) error
	// DeleteUserVaultData deletes user vault data
	DeleteUserVaultData(ctx context.Context, uid string) error
	// SaveUserWallets saves user wallets
	SaveUserWallets(ctx context.Context, userUID string, wallets *Wallets) error
	// DeleteUserWallets deletes user wallets
	DeleteUserWallets(ctx context.Context, uid string) error
	// UpdateUserInfo updates user info
	UpdateUserInfo(ctx context.Context, uid string, clientID string, params map[string]interface{}) (int, error)
	// UpdateUserFcmTokenTimestamp updates user fcm token timestamp
	UpdateUserFcmTokenTimestamp(ctx context.Context, uid, clientID, token string, timestamp float64) (int, error)
	// SavePassword saves user password
	SavePassword(ctx context.Context, uid string, params *SavePasswordParams) error
	// UpdateUserPhoneNumber updates user phone number
	UpdateUserPhoneNumber(ctx context.Context, uid, phoneNumber string) error
	// UpdateUserEmail updates user email
	UpdateUserEmail(ctx context.Context, uid, email string) error
	// SaveUserShareKey saves user share key
	SaveUserShareKey(ctx context.Context, uid, shareKey string) error
	// SaveUserHideSpamNft saves user hide spam nft
	SaveUserHideSpamNft(ctx context.Context, uid string, hideSpamNft bool) error
	// DeleteUserShareKey deletes user share key
	DeleteUserShareKey(ctx context.Context, uid string) error
	// SaveGoogleInfo saves google info
	SaveGoogleInfo(ctx context.Context, uid, accessToken, refreshToken string) error
}

// UserRepo .
type UserRepo interface {
	UserReadRepo
	UserWriteRepo
	GetUserWalletAddresses(ctx context.Context, uid string) ([]string, *code.KGError)
	GetUserDefaultAddresses(ctx context.Context, uid string) (map[Chain]Address, *code.KGError)
	GetDefaultReceiveAddress(ctx context.Context, uid string, chainID string) (string, *code.KGError)
	GetUserClientIDs(ctx context.Context, uid string) ([]string, *code.KGError)
	GetUserLocale(ctx context.Context, uid, clientID string) (string, *code.KGError)
	GetFcmTokens(ctx context.Context, uid, clientID string) ([]string, *code.KGError)
	SetUserFcmTokens(ctx context.Context, uid string, clientID string, tokens []string) error
}
