// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// AssetProOrderStatusUnpaid is a AssetProOrderStatus of type unpaid.
	AssetProOrderStatusUnpaid AssetProOrderStatus = "unpaid"
	// AssetProOrderStatusAwaitingConfirmation is a AssetProOrderStatus of type awaiting_confirmation.
	AssetProOrderStatusAwaitingConfirmation AssetProOrderStatus = "awaiting_confirmation"
	// AssetProOrderStatusAwaitingShipment is a AssetProOrderStatus of type awaiting_shipment.
	AssetProOrderStatusAwaitingShipment AssetProOrderStatus = "awaiting_shipment"
	// AssetProOrderStatusShipping is a AssetProOrderStatus of type shipping.
	AssetProOrderStatusShipping AssetProOrderStatus = "shipping"
	// AssetProOrderStatusDelivered is a AssetProOrderStatus of type delivered.
	AssetProOrderStatusDelivered AssetProOrderStatus = "delivered"
	// AssetProOrderStatusCancelled is a AssetProOrderStatus of type cancelled.
	AssetProOrderStatusCancelled AssetProOrderStatus = "cancelled"
)

var ErrInvalidAssetProOrderStatus = errors.New("not a valid AssetProOrderStatus")

// String implements the Stringer interface.
func (x AssetProOrderStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetProOrderStatus) IsValid() bool {
	_, err := ParseAssetProOrderStatus(string(x))
	return err == nil
}

var _AssetProOrderStatusValue = map[string]AssetProOrderStatus{
	"unpaid":                AssetProOrderStatusUnpaid,
	"awaiting_confirmation": AssetProOrderStatusAwaitingConfirmation,
	"awaiting_shipment":     AssetProOrderStatusAwaitingShipment,
	"shipping":              AssetProOrderStatusShipping,
	"delivered":             AssetProOrderStatusDelivered,
	"cancelled":             AssetProOrderStatusCancelled,
}

// ParseAssetProOrderStatus attempts to convert a string to a AssetProOrderStatus.
func ParseAssetProOrderStatus(name string) (AssetProOrderStatus, error) {
	if x, ok := _AssetProOrderStatusValue[name]; ok {
		return x, nil
	}
	return AssetProOrderStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetProOrderStatus)
}

const (
	// AssetProPaymentStatusUnpaid is a AssetProPaymentStatus of type unpaid.
	AssetProPaymentStatusUnpaid AssetProPaymentStatus = "unpaid"
	// AssetProPaymentStatusPaid is a AssetProPaymentStatus of type paid.
	AssetProPaymentStatusPaid AssetProPaymentStatus = "paid"
	// AssetProPaymentStatusAwaitingRefund is a AssetProPaymentStatus of type awaiting_refund.
	AssetProPaymentStatusAwaitingRefund AssetProPaymentStatus = "awaiting_refund"
	// AssetProPaymentStatusRefunded is a AssetProPaymentStatus of type refunded.
	AssetProPaymentStatusRefunded AssetProPaymentStatus = "refunded"
)

var ErrInvalidAssetProPaymentStatus = errors.New("not a valid AssetProPaymentStatus")

// String implements the Stringer interface.
func (x AssetProPaymentStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetProPaymentStatus) IsValid() bool {
	_, err := ParseAssetProPaymentStatus(string(x))
	return err == nil
}

var _AssetProPaymentStatusValue = map[string]AssetProPaymentStatus{
	"unpaid":          AssetProPaymentStatusUnpaid,
	"paid":            AssetProPaymentStatusPaid,
	"awaiting_refund": AssetProPaymentStatusAwaitingRefund,
	"refunded":        AssetProPaymentStatusRefunded,
}

// ParseAssetProPaymentStatus attempts to convert a string to a AssetProPaymentStatus.
func ParseAssetProPaymentStatus(name string) (AssetProPaymentStatus, error) {
	if x, ok := _AssetProPaymentStatusValue[name]; ok {
		return x, nil
	}
	return AssetProPaymentStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetProPaymentStatus)
}

const (
	// AssetProShippingStatusNotShipped is a AssetProShippingStatus of type not_shipped.
	AssetProShippingStatusNotShipped AssetProShippingStatus = "not_shipped"
	// AssetProShippingStatusSendSuccess is a AssetProShippingStatus of type send_success.
	AssetProShippingStatusSendSuccess AssetProShippingStatus = "send_success"
	// AssetProShippingStatusSendFailed is a AssetProShippingStatus of type send_failed.
	AssetProShippingStatusSendFailed AssetProShippingStatus = "send_failed"
	// AssetProShippingStatusPending is a AssetProShippingStatus of type pending.
	AssetProShippingStatusPending AssetProShippingStatus = "pending"
)

var ErrInvalidAssetProShippingStatus = errors.New("not a valid AssetProShippingStatus")

// String implements the Stringer interface.
func (x AssetProShippingStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetProShippingStatus) IsValid() bool {
	_, err := ParseAssetProShippingStatus(string(x))
	return err == nil
}

var _AssetProShippingStatusValue = map[string]AssetProShippingStatus{
	"not_shipped":  AssetProShippingStatusNotShipped,
	"send_success": AssetProShippingStatusSendSuccess,
	"send_failed":  AssetProShippingStatusSendFailed,
	"pending":      AssetProShippingStatusPending,
}

// ParseAssetProShippingStatus attempts to convert a string to a AssetProShippingStatus.
func ParseAssetProShippingStatus(name string) (AssetProShippingStatus, error) {
	if x, ok := _AssetProShippingStatusValue[name]; ok {
		return x, nil
	}
	return AssetProShippingStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetProShippingStatus)
}
