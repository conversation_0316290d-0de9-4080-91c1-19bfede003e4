// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// IdvStatusPending is a IdvStatus of type pending.
	IdvStatusPending IdvStatus = "pending"
	// IdvStatusReject is a IdvStatus of type reject.
	IdvStatusReject IdvStatus = "reject"
	// IdvStatusAccept is a IdvStatus of type accept.
	IdvStatusAccept IdvStatus = "accept"
	// IdvStatusReview is a IdvStatus of type review.
	IdvStatusReview IdvStatus = "review"
	// IdvStatusInitial is a IdvStatus of type initial.
	IdvStatusInitial IdvStatus = "initial"
)

var ErrInvalidIdvStatus = errors.New("not a valid IdvStatus")

// String implements the Stringer interface.
func (x IdvStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x IdvStatus) IsValid() bool {
	_, err := ParseIdvStatus(string(x))
	return err == nil
}

var _IdvStatusValue = map[string]IdvStatus{
	"pending": IdvStatusPending,
	"reject":  IdvStatusReject,
	"accept":  IdvStatusAccept,
	"review":  IdvStatusReview,
	"initial": IdvStatusInitial,
}

// ParseIdvStatus attempts to convert a string to a IdvStatus.
func ParseIdvStatus(name string) (IdvStatus, error) {
	if x, ok := _IdvStatusValue[name]; ok {
		return x, nil
	}
	return IdvStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidIdvStatus)
}

const (
	// IdvStatusSimplifiedPass is a IdvStatusSimplified of type pass.
	IdvStatusSimplifiedPass IdvStatusSimplified = "pass"
	// IdvStatusSimplifiedFailed is a IdvStatusSimplified of type failed.
	IdvStatusSimplifiedFailed IdvStatusSimplified = "failed"
)

var ErrInvalidIdvStatusSimplified = errors.New("not a valid IdvStatusSimplified")

// String implements the Stringer interface.
func (x IdvStatusSimplified) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x IdvStatusSimplified) IsValid() bool {
	_, err := ParseIdvStatusSimplified(string(x))
	return err == nil
}

var _IdvStatusSimplifiedValue = map[string]IdvStatusSimplified{
	"pass":   IdvStatusSimplifiedPass,
	"failed": IdvStatusSimplifiedFailed,
}

// ParseIdvStatusSimplified attempts to convert a string to a IdvStatusSimplified.
func ParseIdvStatusSimplified(name string) (IdvStatusSimplified, error) {
	if x, ok := _IdvStatusSimplifiedValue[name]; ok {
		return x, nil
	}
	return IdvStatusSimplified(""), fmt.Errorf("%s is %w", name, ErrInvalidIdvStatusSimplified)
}

const (
	// KycStatusRejected is a KycStatus of type rejected.
	KycStatusRejected KycStatus = "rejected"
	// KycStatusVerified is a KycStatus of type verified.
	KycStatusVerified KycStatus = "verified"
	// KycStatusUnverified is a KycStatus of type unverified.
	KycStatusUnverified KycStatus = "unverified"
	// KycStatusPending is a KycStatus of type pending.
	KycStatusPending KycStatus = "pending"
	// KycStatusProcessing is a KycStatus of type processing.
	KycStatusProcessing KycStatus = "processing"
)

var ErrInvalidKycStatus = errors.New("not a valid KycStatus")

// String implements the Stringer interface.
func (x KycStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x KycStatus) IsValid() bool {
	_, err := ParseKycStatus(string(x))
	return err == nil
}

var _KycStatusValue = map[string]KycStatus{
	"rejected":   KycStatusRejected,
	"verified":   KycStatusVerified,
	"unverified": KycStatusUnverified,
	"pending":    KycStatusPending,
	"processing": KycStatusProcessing,
}

// ParseKycStatus attempts to convert a string to a KycStatus.
func ParseKycStatus(name string) (KycStatus, error) {
	if x, ok := _KycStatusValue[name]; ok {
		return x, nil
	}
	return KycStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidKycStatus)
}

const (
	// RejectReasonSanctioned is a RejectReason of type sanctioned.
	RejectReasonSanctioned RejectReason = "sanctioned"
	// RejectReasonHighRiskMoneyLaundering is a RejectReason of type high_risk_money_laundering.
	RejectReasonHighRiskMoneyLaundering RejectReason = "high_risk_money_laundering"
	// RejectReasonFakedDocument is a RejectReason of type faked_document.
	RejectReasonFakedDocument RejectReason = "faked_document"
	// RejectReasonNotTheDocumentHolder is a RejectReason of type not_the_document_holder.
	RejectReasonNotTheDocumentHolder RejectReason = "not_the_document_holder"
	// RejectReasonIncorrectDocumentInformation is a RejectReason of type incorrect_document_information.
	RejectReasonIncorrectDocumentInformation RejectReason = "incorrect_document_information"
)

var ErrInvalidRejectReason = errors.New("not a valid RejectReason")

// String implements the Stringer interface.
func (x RejectReason) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x RejectReason) IsValid() bool {
	_, err := ParseRejectReason(string(x))
	return err == nil
}

var _RejectReasonValue = map[string]RejectReason{
	"sanctioned":                     RejectReasonSanctioned,
	"high_risk_money_laundering":     RejectReasonHighRiskMoneyLaundering,
	"faked_document":                 RejectReasonFakedDocument,
	"not_the_document_holder":        RejectReasonNotTheDocumentHolder,
	"incorrect_document_information": RejectReasonIncorrectDocumentInformation,
}

// ParseRejectReason attempts to convert a string to a RejectReason.
func ParseRejectReason(name string) (RejectReason, error) {
	if x, ok := _RejectReasonValue[name]; ok {
		return x, nil
	}
	return RejectReason(""), fmt.Errorf("%s is %w", name, ErrInvalidRejectReason)
}

const (
	// RiskLabelLow is a RiskLabel of type low.
	RiskLabelLow RiskLabel = "low"
	// RiskLabelMid is a RiskLabel of type mid.
	RiskLabelMid RiskLabel = "mid"
	// RiskLabelHigh is a RiskLabel of type high.
	RiskLabelHigh RiskLabel = "high"
)

var ErrInvalidRiskLabel = errors.New("not a valid RiskLabel")

// String implements the Stringer interface.
func (x RiskLabel) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x RiskLabel) IsValid() bool {
	_, err := ParseRiskLabel(string(x))
	return err == nil
}

var _RiskLabelValue = map[string]RiskLabel{
	"low":  RiskLabelLow,
	"mid":  RiskLabelMid,
	"high": RiskLabelHigh,
}

// ParseRiskLabel attempts to convert a string to a RiskLabel.
func ParseRiskLabel(name string) (RiskLabel, error) {
	if x, ok := _RiskLabelValue[name]; ok {
		return x, nil
	}
	return RiskLabel(""), fmt.Errorf("%s is %w", name, ErrInvalidRiskLabel)
}
