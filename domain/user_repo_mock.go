// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: UserRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=user_repo_mock.go . UserRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockUserRepo is a mock of UserRepo interface.
type MockUserRepo struct {
	ctrl     *gomock.Controller
	recorder *MockUserRepoMockRecorder
}

// MockUserRepoMockRecorder is the mock recorder for MockUserRepo.
type MockUserRepoMockRecorder struct {
	mock *MockUserRepo
}

// NewMockUserRepo creates a new mock instance.
func NewMockUserRepo(ctrl *gomock.Controller) *MockUserRepo {
	mock := &MockUserRepo{ctrl: ctrl}
	mock.recorder = &MockUserRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserRepo) EXPECT() *MockUserRepoMockRecorder {
	return m.recorder
}

// BatchSetUsers mocks base method.
func (m *MockUserRepo) BatchSetUsers(arg0 context.Context, arg1 map[string]UserData) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetUsers", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// BatchSetUsers indicates an expected call of BatchSetUsers.
func (mr *MockUserRepoMockRecorder) BatchSetUsers(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUsers", reflect.TypeOf((*MockUserRepo)(nil).BatchSetUsers), arg0, arg1)
}

// CountByUidWithRegisterWallet mocks base method.
func (m *MockUserRepo) CountByUidWithRegisterWallet(arg0 context.Context, arg1 []string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByUidWithRegisterWallet", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByUidWithRegisterWallet indicates an expected call of CountByUidWithRegisterWallet.
func (mr *MockUserRepoMockRecorder) CountByUidWithRegisterWallet(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByUidWithRegisterWallet", reflect.TypeOf((*MockUserRepo)(nil).CountByUidWithRegisterWallet), arg0, arg1)
}

// CreateUser mocks base method.
func (m *MockUserRepo) CreateUser(arg0 context.Context, arg1 string, arg2 *UserData) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// CreateUser indicates an expected call of CreateUser.
func (mr *MockUserRepoMockRecorder) CreateUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUser", reflect.TypeOf((*MockUserRepo)(nil).CreateUser), arg0, arg1, arg2)
}

// DeleteUser mocks base method.
func (m *MockUserRepo) DeleteUser(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUser", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUser indicates an expected call of DeleteUser.
func (mr *MockUserRepoMockRecorder) DeleteUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUser", reflect.TypeOf((*MockUserRepo)(nil).DeleteUser), arg0, arg1)
}

// DeleteUserFields mocks base method.
func (m *MockUserRepo) DeleteUserFields(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserFields", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserFields indicates an expected call of DeleteUserFields.
func (mr *MockUserRepoMockRecorder) DeleteUserFields(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserFields", reflect.TypeOf((*MockUserRepo)(nil).DeleteUserFields), arg0, arg1, arg2)
}

// DeleteUserMnemonic mocks base method.
func (m *MockUserRepo) DeleteUserMnemonic(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserMnemonic", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserMnemonic indicates an expected call of DeleteUserMnemonic.
func (mr *MockUserRepoMockRecorder) DeleteUserMnemonic(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserMnemonic", reflect.TypeOf((*MockUserRepo)(nil).DeleteUserMnemonic), arg0, arg1)
}

// DeleteUserShareKey mocks base method.
func (m *MockUserRepo) DeleteUserShareKey(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserShareKey", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserShareKey indicates an expected call of DeleteUserShareKey.
func (mr *MockUserRepoMockRecorder) DeleteUserShareKey(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserShareKey", reflect.TypeOf((*MockUserRepo)(nil).DeleteUserShareKey), arg0, arg1)
}

// DeleteUserVaultData mocks base method.
func (m *MockUserRepo) DeleteUserVaultData(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserVaultData", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserVaultData indicates an expected call of DeleteUserVaultData.
func (mr *MockUserRepoMockRecorder) DeleteUserVaultData(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserVaultData", reflect.TypeOf((*MockUserRepo)(nil).DeleteUserVaultData), arg0, arg1)
}

// DeleteUserWallets mocks base method.
func (m *MockUserRepo) DeleteUserWallets(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserWallets", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserWallets indicates an expected call of DeleteUserWallets.
func (mr *MockUserRepoMockRecorder) DeleteUserWallets(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserWallets", reflect.TypeOf((*MockUserRepo)(nil).DeleteUserWallets), arg0, arg1)
}

// GetAllEvmAddresses mocks base method.
func (m *MockUserRepo) GetAllEvmAddresses(arg0 context.Context, arg1 bool) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllEvmAddresses", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllEvmAddresses indicates an expected call of GetAllEvmAddresses.
func (mr *MockUserRepoMockRecorder) GetAllEvmAddresses(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllEvmAddresses", reflect.TypeOf((*MockUserRepo)(nil).GetAllEvmAddresses), arg0, arg1)
}

// GetDefaultReceiveAddress mocks base method.
func (m *MockUserRepo) GetDefaultReceiveAddress(arg0 context.Context, arg1, arg2 string) (string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultReceiveAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetDefaultReceiveAddress indicates an expected call of GetDefaultReceiveAddress.
func (mr *MockUserRepoMockRecorder) GetDefaultReceiveAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultReceiveAddress", reflect.TypeOf((*MockUserRepo)(nil).GetDefaultReceiveAddress), arg0, arg1, arg2)
}

// GetFcmTokens mocks base method.
func (m *MockUserRepo) GetFcmTokens(arg0 context.Context, arg1, arg2 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFcmTokens", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetFcmTokens indicates an expected call of GetFcmTokens.
func (mr *MockUserRepoMockRecorder) GetFcmTokens(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFcmTokens", reflect.TypeOf((*MockUserRepo)(nil).GetFcmTokens), arg0, arg1, arg2)
}

// GetUser mocks base method.
func (m *MockUserRepo) GetUser(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 *UserPreloads) (*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUser", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUser indicates an expected call of GetUser.
func (mr *MockUserRepoMockRecorder) GetUser(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUser", reflect.TypeOf((*MockUserRepo)(nil).GetUser), arg0, arg1, arg2, arg3, arg4)
}

// GetUserByEmail mocks base method.
func (m *MockUserRepo) GetUserByEmail(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 *UserPreloads) (*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByEmail", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserByEmail indicates an expected call of GetUserByEmail.
func (mr *MockUserRepoMockRecorder) GetUserByEmail(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByEmail", reflect.TypeOf((*MockUserRepo)(nil).GetUserByEmail), arg0, arg1, arg2, arg3, arg4)
}

// GetUserByHandle mocks base method.
func (m *MockUserRepo) GetUserByHandle(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 *UserPreloads) (*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByHandle", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserByHandle indicates an expected call of GetUserByHandle.
func (mr *MockUserRepoMockRecorder) GetUserByHandle(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByHandle", reflect.TypeOf((*MockUserRepo)(nil).GetUserByHandle), arg0, arg1, arg2, arg3, arg4)
}

// GetUserByPhoneNumber mocks base method.
func (m *MockUserRepo) GetUserByPhoneNumber(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 *UserPreloads) (*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByPhoneNumber", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserByPhoneNumber indicates an expected call of GetUserByPhoneNumber.
func (mr *MockUserRepoMockRecorder) GetUserByPhoneNumber(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByPhoneNumber", reflect.TypeOf((*MockUserRepo)(nil).GetUserByPhoneNumber), arg0, arg1, arg2, arg3, arg4)
}

// GetUserClientIDs mocks base method.
func (m *MockUserRepo) GetUserClientIDs(arg0 context.Context, arg1 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserClientIDs", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserClientIDs indicates an expected call of GetUserClientIDs.
func (mr *MockUserRepoMockRecorder) GetUserClientIDs(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserClientIDs", reflect.TypeOf((*MockUserRepo)(nil).GetUserClientIDs), arg0, arg1)
}

// GetUserDefaultAddresses mocks base method.
func (m *MockUserRepo) GetUserDefaultAddresses(arg0 context.Context, arg1 string) (map[Chain]Address, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDefaultAddresses", arg0, arg1)
	ret0, _ := ret[0].(map[Chain]Address)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserDefaultAddresses indicates an expected call of GetUserDefaultAddresses.
func (mr *MockUserRepoMockRecorder) GetUserDefaultAddresses(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDefaultAddresses", reflect.TypeOf((*MockUserRepo)(nil).GetUserDefaultAddresses), arg0, arg1)
}

// GetUserLocale mocks base method.
func (m *MockUserRepo) GetUserLocale(arg0 context.Context, arg1, arg2 string) (string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLocale", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserLocale indicates an expected call of GetUserLocale.
func (mr *MockUserRepoMockRecorder) GetUserLocale(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLocale", reflect.TypeOf((*MockUserRepo)(nil).GetUserLocale), arg0, arg1, arg2)
}

// GetUserWalletAddresses mocks base method.
func (m *MockUserRepo) GetUserWalletAddresses(arg0 context.Context, arg1 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWalletAddresses", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserWalletAddresses indicates an expected call of GetUserWalletAddresses.
func (mr *MockUserRepoMockRecorder) GetUserWalletAddresses(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWalletAddresses", reflect.TypeOf((*MockUserRepo)(nil).GetUserWalletAddresses), arg0, arg1)
}

// GetUsers mocks base method.
func (m *MockUserRepo) GetUsers(arg0 context.Context, arg1 []string, arg2 string, arg3 bool, arg4 *UserPreloads) ([]*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsers", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsers indicates an expected call of GetUsers.
func (mr *MockUserRepoMockRecorder) GetUsers(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsers", reflect.TypeOf((*MockUserRepo)(nil).GetUsers), arg0, arg1, arg2, arg3, arg4)
}

// GetUsersByEmails mocks base method.
func (m *MockUserRepo) GetUsersByEmails(arg0 context.Context, arg1 []string, arg2 string, arg3 bool, arg4 *UserPreloads) ([]*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersByEmails", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersByEmails indicates an expected call of GetUsersByEmails.
func (mr *MockUserRepoMockRecorder) GetUsersByEmails(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersByEmails", reflect.TypeOf((*MockUserRepo)(nil).GetUsersByEmails), arg0, arg1, arg2, arg3, arg4)
}

// GetUsersByEthAddressWithValidFcmToken mocks base method.
func (m *MockUserRepo) GetUsersByEthAddressWithValidFcmToken(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 *UserPreloads) ([]*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersByEthAddressWithValidFcmToken", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersByEthAddressWithValidFcmToken indicates an expected call of GetUsersByEthAddressWithValidFcmToken.
func (mr *MockUserRepoMockRecorder) GetUsersByEthAddressWithValidFcmToken(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersByEthAddressWithValidFcmToken", reflect.TypeOf((*MockUserRepo)(nil).GetUsersByEthAddressWithValidFcmToken), arg0, arg1, arg2, arg3, arg4)
}

// GetUsersByPhoneNumbers mocks base method.
func (m *MockUserRepo) GetUsersByPhoneNumbers(arg0 context.Context, arg1 []string, arg2 string, arg3 bool, arg4 *UserPreloads) ([]*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersByPhoneNumbers", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersByPhoneNumbers indicates an expected call of GetUsersByPhoneNumbers.
func (mr *MockUserRepoMockRecorder) GetUsersByPhoneNumbers(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersByPhoneNumbers", reflect.TypeOf((*MockUserRepo)(nil).GetUsersByPhoneNumbers), arg0, arg1, arg2, arg3, arg4)
}

// GetUsersWithValidFcmToken mocks base method.
func (m *MockUserRepo) GetUsersWithValidFcmToken(arg0 context.Context, arg1 []string, arg2 string, arg3 bool, arg4 *UserPreloads) ([]*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersWithValidFcmToken", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersWithValidFcmToken indicates an expected call of GetUsersWithValidFcmToken.
func (mr *MockUserRepoMockRecorder) GetUsersWithValidFcmToken(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersWithValidFcmToken", reflect.TypeOf((*MockUserRepo)(nil).GetUsersWithValidFcmToken), arg0, arg1, arg2, arg3, arg4)
}

// RemoveAvatar mocks base method.
func (m *MockUserRepo) RemoveAvatar(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveAvatar", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveAvatar indicates an expected call of RemoveAvatar.
func (mr *MockUserRepoMockRecorder) RemoveAvatar(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveAvatar", reflect.TypeOf((*MockUserRepo)(nil).RemoveAvatar), arg0, arg1, arg2, arg3)
}

// SaveGoogleInfo mocks base method.
func (m *MockUserRepo) SaveGoogleInfo(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveGoogleInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveGoogleInfo indicates an expected call of SaveGoogleInfo.
func (mr *MockUserRepoMockRecorder) SaveGoogleInfo(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveGoogleInfo", reflect.TypeOf((*MockUserRepo)(nil).SaveGoogleInfo), arg0, arg1, arg2, arg3)
}

// SavePassword mocks base method.
func (m *MockUserRepo) SavePassword(arg0 context.Context, arg1 string, arg2 *SavePasswordParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SavePassword", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SavePassword indicates an expected call of SavePassword.
func (mr *MockUserRepoMockRecorder) SavePassword(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SavePassword", reflect.TypeOf((*MockUserRepo)(nil).SavePassword), arg0, arg1, arg2)
}

// SaveUserAvatar mocks base method.
func (m *MockUserRepo) SaveUserAvatar(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserAvatar", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserAvatar indicates an expected call of SaveUserAvatar.
func (mr *MockUserRepoMockRecorder) SaveUserAvatar(arg0, arg1, arg2, arg3, arg4, arg5 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserAvatar", reflect.TypeOf((*MockUserRepo)(nil).SaveUserAvatar), arg0, arg1, arg2, arg3, arg4, arg5)
}

// SaveUserHideSpamNft mocks base method.
func (m *MockUserRepo) SaveUserHideSpamNft(arg0 context.Context, arg1 string, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserHideSpamNft", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserHideSpamNft indicates an expected call of SaveUserHideSpamNft.
func (mr *MockUserRepoMockRecorder) SaveUserHideSpamNft(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserHideSpamNft", reflect.TypeOf((*MockUserRepo)(nil).SaveUserHideSpamNft), arg0, arg1, arg2)
}

// SaveUserShareKey mocks base method.
func (m *MockUserRepo) SaveUserShareKey(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserShareKey", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserShareKey indicates an expected call of SaveUserShareKey.
func (mr *MockUserRepoMockRecorder) SaveUserShareKey(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserShareKey", reflect.TypeOf((*MockUserRepo)(nil).SaveUserShareKey), arg0, arg1, arg2)
}

// SaveUserVaultData mocks base method.
func (m *MockUserRepo) SaveUserVaultData(arg0 context.Context, arg1 string, arg2 *VaultData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserVaultData", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserVaultData indicates an expected call of SaveUserVaultData.
func (mr *MockUserRepoMockRecorder) SaveUserVaultData(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserVaultData", reflect.TypeOf((*MockUserRepo)(nil).SaveUserVaultData), arg0, arg1, arg2)
}

// SaveUserWallets mocks base method.
func (m *MockUserRepo) SaveUserWallets(arg0 context.Context, arg1 string, arg2 *Wallets) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserWallets", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserWallets indicates an expected call of SaveUserWallets.
func (mr *MockUserRepoMockRecorder) SaveUserWallets(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserWallets", reflect.TypeOf((*MockUserRepo)(nil).SaveUserWallets), arg0, arg1, arg2)
}

// SetUser mocks base method.
func (m *MockUserRepo) SetUser(arg0 context.Context, arg1 *UserData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUser", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUser indicates an expected call of SetUser.
func (mr *MockUserRepoMockRecorder) SetUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUser", reflect.TypeOf((*MockUserRepo)(nil).SetUser), arg0, arg1)
}

// SetUserFcmTokens mocks base method.
func (m *MockUserRepo) SetUserFcmTokens(arg0 context.Context, arg1, arg2 string, arg3 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserFcmTokens", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserFcmTokens indicates an expected call of SetUserFcmTokens.
func (mr *MockUserRepoMockRecorder) SetUserFcmTokens(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserFcmTokens", reflect.TypeOf((*MockUserRepo)(nil).SetUserFcmTokens), arg0, arg1, arg2, arg3)
}

// UpdateUserEmail mocks base method.
func (m *MockUserRepo) UpdateUserEmail(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserEmail", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserEmail indicates an expected call of UpdateUserEmail.
func (mr *MockUserRepoMockRecorder) UpdateUserEmail(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserEmail", reflect.TypeOf((*MockUserRepo)(nil).UpdateUserEmail), arg0, arg1, arg2)
}

// UpdateUserFcmTokenTimestamp mocks base method.
func (m *MockUserRepo) UpdateUserFcmTokenTimestamp(arg0 context.Context, arg1, arg2, arg3 string, arg4 float64) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserFcmTokenTimestamp", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserFcmTokenTimestamp indicates an expected call of UpdateUserFcmTokenTimestamp.
func (mr *MockUserRepoMockRecorder) UpdateUserFcmTokenTimestamp(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserFcmTokenTimestamp", reflect.TypeOf((*MockUserRepo)(nil).UpdateUserFcmTokenTimestamp), arg0, arg1, arg2, arg3, arg4)
}

// UpdateUserInfo mocks base method.
func (m *MockUserRepo) UpdateUserInfo(arg0 context.Context, arg1, arg2 string, arg3 map[string]any) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserInfo indicates an expected call of UpdateUserInfo.
func (mr *MockUserRepoMockRecorder) UpdateUserInfo(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserInfo", reflect.TypeOf((*MockUserRepo)(nil).UpdateUserInfo), arg0, arg1, arg2, arg3)
}

// UpdateUserPhoneNumber mocks base method.
func (m *MockUserRepo) UpdateUserPhoneNumber(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserPhoneNumber", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserPhoneNumber indicates an expected call of UpdateUserPhoneNumber.
func (mr *MockUserRepoMockRecorder) UpdateUserPhoneNumber(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserPhoneNumber", reflect.TypeOf((*MockUserRepo)(nil).UpdateUserPhoneNumber), arg0, arg1, arg2)
}

// UserAll mocks base method.
func (m *MockUserRepo) UserAll(arg0 context.Context, arg1 string, arg2 bool, arg3 *UserPreloads) ([]*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserAll", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// UserAll indicates an expected call of UserAll.
func (mr *MockUserRepoMockRecorder) UserAll(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserAll", reflect.TypeOf((*MockUserRepo)(nil).UserAll), arg0, arg1, arg2, arg3)
}

// UserListAfterWithLimit mocks base method.
func (m *MockUserRepo) UserListAfterWithLimit(arg0 context.Context, arg1 *string, arg2 int, arg3 string, arg4 bool, arg5 *UserPreloads) ([]*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserListAfterWithLimit", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// UserListAfterWithLimit indicates an expected call of UserListAfterWithLimit.
func (mr *MockUserRepoMockRecorder) UserListAfterWithLimit(arg0, arg1, arg2, arg3, arg4, arg5 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserListAfterWithLimit", reflect.TypeOf((*MockUserRepo)(nil).UserListAfterWithLimit), arg0, arg1, arg2, arg3, arg4, arg5)
}

// UserNotificationReadAll mocks base method.
func (m *MockUserRepo) UserNotificationReadAll(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserNotificationReadAll", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UserNotificationReadAll indicates an expected call of UserNotificationReadAll.
func (mr *MockUserRepoMockRecorder) UserNotificationReadAll(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserNotificationReadAll", reflect.TypeOf((*MockUserRepo)(nil).UserNotificationReadAll), arg0, arg1, arg2)
}
