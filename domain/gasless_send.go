//go:generate go-enum
package domain

import (
	"time"

	"github.com/shopspring/decimal"
)

// Deprecated: remove this after all client upgraded to use GaslessSendV2

// GaslessSendStatus represents status of gasless send
// ENUM(success, failed, processing)
type GaslessSendStatus string

// GaslessSend is a gasless send request from user
type GaslessSend struct {
	ID           int
	OrgID        int
	UID          string
	ChainID      string
	From         string
	Recipient    string
	TokenAddress string
	Amount       string
	Fee          string
	SignedTxs    []string

	// following fields aren't supplied by user
	CreatedAt         time.Time
	FeeUsd            float64
	EstimatedFinishAt time.Time
	Status            GaslessSendStatus
	EnergyRentCost    *float64 // unit is in TRX
	ActualCostUsd     *float64
	GasFaucetTxHash   *string
	UserApproveTxHash *string
	GaslessSendTxHash *string
	RetryCount        int
}

// UpdateGaslessSendRequest is used to update gasless send request's status in repo
type UpdateGaslessSendRequest struct {
	ID                int
	Status            *GaslessSendStatus
	EnergyRentCost    *float64
	NativeTokenPrice  *float64
	ActualCostUsd     *float64
	GasFaucetTxHash   *string
	UserApproveTxHash *string
	GaslessSendTxHash *string
	RetryCount        *int
	ProfitMargin      *decimal.Decimal
}
