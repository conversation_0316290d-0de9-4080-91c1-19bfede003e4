package domain

import (
	"time"
)

// ENUM(success, failed)
type CallbackStatus string

// ENUM(payment, test)
type CallbackType string

// CallbackLog represents a log of a callback that was sent
type CallbackLog struct {
	ID              string
	PaymentIntentID *string // May be nil for test callbacks
	ClientID        *string // Client ID associated with the callback
	OrgID           *int    // Organization ID associated with the callback
	URL             string
	Type            CallbackType
	Status          CallbackStatus
	StatusCode      *int
	CallbackPayload string
	Error           *string
	Duration        time.Duration
	CreatedAt       time.Time
}

// CallbackLogFilter represents the filtering options for callback logs
type CallbackLogFilter struct {
	OrgID           *int
	ClientID        *string
	PaymentIntentID *string
	Type            []CallbackType
	Status          []CallbackStatus
	FromDate        *time.Time
	ToDate          *time.Time
	Page            int
	PageSize        int
}

// CallbackExecutionContext contains the context information needed for callback execution
type CallbackExecutionContext struct {
	ClientID        string
	OrgID           int
	PaymentIntentID *string
}

// TestCallbackRequest represents a request to test a callback
type TestCallbackRequest struct {
	URL         string
	Payload     map[string]any
	ClientID    string
	SignPayload bool
}
