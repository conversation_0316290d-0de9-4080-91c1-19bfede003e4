//go:generate mockgen -source=bridge_organization.go -destination=bridge_api_client_mock.go -package=domain

package domain

import (
	"context"
	"reflect"

	"github.com/golang/mock/gomock"
)

// MockBridgeAPIClient is a mock implementation of BridgeAPIClient
type MockBridgeAPIClient struct {
	ctrl     *gomock.Controller
	recorder *MockBridgeAPIClientMockRecorder
}

type MockBridgeAPIClientMockRecorder struct {
	mock *MockBridgeAPIClient
}

func NewMockBridgeAPIClient(ctrl *gomock.Controller) *MockBridgeAPIClient {
	mock := &MockBridgeAPIClient{ctrl: ctrl}
	mock.recorder = &MockBridgeAPIClientMockRecorder{mock}
	return mock
}

func (m *MockBridgeAPIClient) EXPECT() *MockBridgeAPIClientMockRecorder {
	return m.recorder
}

func (m *MockBridgeAPIClient) CreateKYCLink(ctx context.Context, req *BridgeCreateKYCLinkRequest) (*BridgeCreateKYCLinkResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateKYCLink", ctx, req)
	ret0, _ := ret[0].(*BridgeCreateKYCLinkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockBridgeAPIClientMockRecorder) CreateKYCLink(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateKYCLink", reflect.TypeOf((*MockBridgeAPIClient)(nil).CreateKYCLink), ctx, req)
}

func (m *MockBridgeAPIClient) CreateExternalAccount(ctx context.Context, customerID string, req *BridgeCreateExternalAccountRequest) (*BridgeCreateExternalAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateExternalAccount", ctx, customerID, req)
	ret0, _ := ret[0].(*BridgeCreateExternalAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockBridgeAPIClientMockRecorder) CreateExternalAccount(ctx, customerID, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExternalAccount", reflect.TypeOf((*MockBridgeAPIClient)(nil).CreateExternalAccount), ctx, customerID, req)
}

func (m *MockBridgeAPIClient) CreateTransfer(ctx context.Context, req *BridgeCreateTransferRequest) (*BridgeCreateTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTransfer", ctx, req)
	ret0, _ := ret[0].(*BridgeCreateTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockBridgeAPIClientMockRecorder) CreateTransfer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransfer", reflect.TypeOf((*MockBridgeAPIClient)(nil).CreateTransfer), ctx, req)
}