package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/shopspring/decimal"
)

// DefiSwapRepo defines the interface for defi swap repository
type DefiSwapRepo interface {
	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *OrganizationWallets, kgErr *code.KGError)
	CreateDefiSwap(ctx context.Context, tx CreateDefiSwapRequest) (int, *code.KGError)
	UpdateDefiSwap(ctx context.Context, params UpdateDefiSwapRequest) *code.KGError
	GetNativeAssetPrice(ctx context.Context, chainID string) (float64, error)
}

// DefiSwap defines the
type DefiSwap struct {
	ID           int
	OrgID        int
	UID          string
	TxHash       string
	ChainID      string
	Amount       *string
	AmountUSD    *string
	TokenAddress *string
	TokenPrice   *decimal.Decimal
	FeeAmount    *string
	ProfitMargin *decimal.Decimal
}

// CreateDefiSwapRequest defines the request for creating a defi swap
type CreateDefiSwapRequest struct {
	OrgID   int
	ChainID string
	TxHash  string
	UID     string
}

// UpdateDefiSwapRequest defines the request for updating a defi swap
type UpdateDefiSwapRequest struct {
	ID           int
	Amount       *decimal.Decimal
	AmountUSD    *decimal.Decimal
	FeeAmount    *decimal.Decimal
	TokenAddress *string
	TokenPrice   *decimal.Decimal
	ProfitMargin *decimal.Decimal
}
