//go:generate go-enum
package domain

import (
	"bytes"
	"context"
	"database/sql/driver"
	"errors"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/paging"
)

// KycCaseDetail is the kyc case detail
type KycCaseDetail struct {
	UID                 string
	OrganizationID      int
	CaseID              int
	Name                *string
	Phone               *string
	Email               *string
	Country             *string
	NationalID          *string
	Birthday            *string
	LineID              *string
	SubmittedAt         int64
	UpdatedAt           int64
	IdvStatusSimplified *IdvStatusSimplified
	IdvStatus           *IdvStatus
	RiskScore           *int
	RiskLabel           *RiskLabel
	KycStatus           *KycStatus
	FormID              *int
	CddID               *int
	IdvID               *int
	SanctionMatched     *bool
	ReviewerName        *string
	ReviewedAt          *int64
	RejectReasons       *RejectReasons
	InternalNotes       *string
}

// CaseSubmission represents a case submission
type CaseSubmission struct {
	ID             int
	OrganizationID int
	UID            string
	FormID         *int
	IdvID          *int
	CddID          *int
	SubmittedAt    time.Time
	UpdatedAt      time.Time
}

// KycCaseFilters is the kyc case filters
type KycCaseFilters struct {
	SubmittedAtFrom int
	SubmittedAtTo   int
}

// ComplianceRepository is the kyc case repository
type ComplianceRepository interface {
	CreateCaseSubmission(ctx context.Context, params CreateCaseSubmissionParam) *code.KGError
	GetLatestCaseByUsers(ctx context.Context, organizationID int, params *GetLatestCaseByUsersParams, query *paging.Query) ([]*KycCaseDetail, *paging.Resp, error)
	GetFilters(ctx context.Context, organizationID int) (*KycCaseFilters, error)
	Audit(ctx context.Context, params *AuditParams) *code.KGError
	GetCaseSubmission(ctx context.Context, params *GetCasesParams) (*CaseSubmission, *code.KGError)
	GetPendingCaseCount(ctx context.Context, organizationID int) (int, *code.KGError)
}

// CreateCaseSubmissionParam defines the params for CreateCaseSubmission.
type CreateCaseSubmissionParam struct {
	UID            string
	OrganizationID int
	FormID         *int
	CddID          *int
	IdvID          *int
}

// GetLatestCaseByUsersParams is the params for GetLatestCaseByUsers
type GetLatestCaseByUsersParams struct {
	SubmittedAtFrom *int
	SubmittedAtTo   *int
	RiskScoreFrom   *int
	RiskScoreTo     *int
	Sanctioned      *bool
	IdvStatus       []*IdvStatus
	KycStatus       *KycStatus
}

// GetCasesParams is the params for GetCases
type GetCasesParams struct {
	IdvID *int
	CddID *int
}

// KycStatus is the audit status
// ENUM(rejected, verified, unverified, pending, processing)
type KycStatus string

// RiskLabel is the risk label
// ENUM(low, mid, high)
type RiskLabel string

// IdvStatus is the idv status
// ENUM(pending, reject, accept, review, initial)
type IdvStatus string

// IdvStatusSimplified is the idv status simplified
// ENUM(pass, failed)
type IdvStatusSimplified string

const (
	// CddRiskScoreUpperBoundLow is the low risk score
	CddRiskScoreUpperBoundLow = 33
	// CddRiskScoreUpperBoundMedium is the medium risk score
	CddRiskScoreUpperBoundMedium = 66
	// CddRiskScoreUpperBoundHigh is the high risk score
	CddRiskScoreUpperBoundHigh = 100
)

// RejectReason is the reject reason
// ENUM(sanctioned, high_risk_money_laundering, faked_document, not_the_document_holder, incorrect_document_information)
type RejectReason string

// RejectReasons is a slice of RejectReason.
type RejectReasons []RejectReason

// Scan implements the Scanner interface for RejectReasons.
func (rr *RejectReasons) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	if v, ok := value.([]byte); !ok {
		return errors.New("invalid data for RejectReasons")
	} else if len(v) == 0 {
		return nil
	}

	var reasons []RejectReason
	for _, byte := range bytes.Split(value.([]byte), []byte(",")) {
		reasons = append(reasons, RejectReason(byte))
	}
	*rr = reasons
	return nil
}

// Value implements the driver Valuer interface for RejectReasons.
func (rr RejectReasons) Value() (driver.Value, error) {
	var strReasons []string
	for _, reason := range rr {
		strReasons = append(strReasons, string(reason))
	}
	return strings.Join(strReasons, ","), nil
}

// AuditParams audit params
type AuditParams struct {
	UID            string
	OrganizationID int
	AuditorUID     string
	KycStatus      KycStatus
	RejectReasons  []RejectReason
	InternalNotes  *string
}

// ComplianceSessionData is the compliance session data
type ComplianceSessionData struct {
	EmployeeID     int
	Role           int
	OrganizationID int
}
