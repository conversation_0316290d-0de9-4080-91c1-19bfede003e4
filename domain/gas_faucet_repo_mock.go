// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: GasFaucetRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=gas_faucet_repo_mock.go . GasFaucetRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"
	time "time"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockGasFaucetRepo is a mock of GasFaucetRepo interface.
type MockGasFaucetRepo struct {
	ctrl     *gomock.Controller
	recorder *MockGasFaucetRepoMockRecorder
}

// MockGasFaucetRepoMockRecorder is the mock recorder for MockGasFaucetRepo.
type MockGasFaucetRepoMockRecorder struct {
	mock *MockGasFaucetRepo
}

// NewMockGasFaucetRepo creates a new mock instance.
func NewMockGasFaucetRepo(ctrl *gomock.Controller) *MockGasFaucetRepo {
	mock := &MockGasFaucetRepo{ctrl: ctrl}
	mock.recorder = &MockGasFaucetRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGasFaucetRepo) EXPECT() *MockGasFaucetRepoMockRecorder {
	return m.recorder
}

// AcquireLock mocks base method.
func (m *MockGasFaucetRepo) AcquireLock(arg0 context.Context, arg1 string, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLock indicates an expected call of AcquireLock.
func (mr *MockGasFaucetRepoMockRecorder) AcquireLock(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLock", reflect.TypeOf((*MockGasFaucetRepo)(nil).AcquireLock), arg0, arg1, arg2)
}

// AcquireLockWithRetry mocks base method.
func (m *MockGasFaucetRepo) AcquireLockWithRetry(arg0 context.Context, arg1 string, arg2, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLockWithRetry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLockWithRetry indicates an expected call of AcquireLockWithRetry.
func (mr *MockGasFaucetRepoMockRecorder) AcquireLockWithRetry(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLockWithRetry", reflect.TypeOf((*MockGasFaucetRepo)(nil).AcquireLockWithRetry), arg0, arg1, arg2, arg3)
}

// CreateGasFaucet mocks base method.
func (m *MockGasFaucetRepo) CreateGasFaucet(arg0 context.Context, arg1 *GasFaucet) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGasFaucet", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGasFaucet indicates an expected call of CreateGasFaucet.
func (mr *MockGasFaucetRepoMockRecorder) CreateGasFaucet(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGasFaucet", reflect.TypeOf((*MockGasFaucetRepo)(nil).CreateGasFaucet), arg0, arg1)
}

// GetNativeAssetPrice mocks base method.
func (m *MockGasFaucetRepo) GetNativeAssetPrice(arg0 context.Context, arg1 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockGasFaucetRepoMockRecorder) GetNativeAssetPrice(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockGasFaucetRepo)(nil).GetNativeAssetPrice), arg0, arg1)
}

// GetUserWalletAddresses mocks base method.
func (m *MockGasFaucetRepo) GetUserWalletAddresses(arg0 context.Context, arg1 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWalletAddresses", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserWalletAddresses indicates an expected call of GetUserWalletAddresses.
func (mr *MockGasFaucetRepoMockRecorder) GetUserWalletAddresses(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWalletAddresses", reflect.TypeOf((*MockGasFaucetRepo)(nil).GetUserWalletAddresses), arg0, arg1)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockGasFaucetRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockGasFaucetRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockGasFaucetRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// ReleaseLock mocks base method.
func (m *MockGasFaucetRepo) ReleaseLock(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseLock", arg0, arg1)
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockGasFaucetRepoMockRecorder) ReleaseLock(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockGasFaucetRepo)(nil).ReleaseLock), arg0, arg1)
}

// SentUsdToday mocks base method.
func (m *MockGasFaucetRepo) SentUsdToday(arg0 context.Context, arg1 int) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SentUsdToday", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SentUsdToday indicates an expected call of SentUsdToday.
func (mr *MockGasFaucetRepoMockRecorder) SentUsdToday(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SentUsdToday", reflect.TypeOf((*MockGasFaucetRepo)(nil).SentUsdToday), arg0, arg1)
}

// SentUsdTodayByWallet mocks base method.
func (m *MockGasFaucetRepo) SentUsdTodayByWallet(arg0 context.Context, arg1 int, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SentUsdTodayByWallet", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SentUsdTodayByWallet indicates an expected call of SentUsdTodayByWallet.
func (mr *MockGasFaucetRepoMockRecorder) SentUsdTodayByWallet(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SentUsdTodayByWallet", reflect.TypeOf((*MockGasFaucetRepo)(nil).SentUsdTodayByWallet), arg0, arg1, arg2)
}

// UpdateGasFaucet mocks base method.
func (m *MockGasFaucetRepo) UpdateGasFaucet(arg0 context.Context, arg1 *GasFaucetUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGasFaucet", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGasFaucet indicates an expected call of UpdateGasFaucet.
func (mr *MockGasFaucetRepoMockRecorder) UpdateGasFaucet(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGasFaucet", reflect.TypeOf((*MockGasFaucetRepo)(nil).UpdateGasFaucet), arg0, arg1)
}
