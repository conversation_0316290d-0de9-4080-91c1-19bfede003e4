package domain

import "context"

// ChainSyncRepo defines the repository interface for chain synchronization.
type ChainSyncRepo interface {
	// GetLastSyncedBlockNo retrieves the last synced block number for a given chain. If the block number is not found, it returns 0.
	GetLastSyncedBlockNo(ctx context.Context, chain Chain) (uint64, error)

	// SetLastSyncedBlockNo sets the last synced block number for a given chain.
	SetLastSyncedBlockNo(ctx context.Context, chain Chain, blockNo uint64) error
}
