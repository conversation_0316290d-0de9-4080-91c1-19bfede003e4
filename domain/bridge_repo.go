//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=bridge_repo_mock.go . BridgeRepo

package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/shopspring/decimal"
)

// BridgeRepo defines the interface for bridge related database operations
type BridgeRepo interface {
	ProfitRateRepo
	AssetPriceRepo
	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *OrganizationWallets, kgErr *code.KGError)
	CreateBridgeRecord(ctx context.Context, params *BridgeRecord) error
	UpdateBridgeRecord(ctx context.Context, fromTxHash string, params *BridgeRecord) error
	
	// Bridge Organization methods
	CreateBridgeOrganization(ctx context.Context, bridgeOrg *CreateBridgeOrganizationData) error
	GetBridgeOrganizationByOrgID(ctx context.Context, organizationID int) (*BridgeOrganizationData, error)
	
	// Bridge Transfer methods
	CreateBridgeTransfer(ctx context.Context, transfer *CreateBridgeTransferData) error
	GetBridgeTransfersByOrgID(ctx context.Context, organizationID int) ([]*BridgeTransferData, error)
}

type CreateBridgeRecordParams struct {
	OrgID int
	UID   *string

	FromChain        Chain
	FromAddress      Address
	FromTokenAddress string
	FromAmount       decimal.Decimal
	FromTxHash       string

	ToChain        Chain
	ToAddress      Address
	ToTokenAddress string
	ToAmount       decimal.Decimal
	ToTxHash       *string // Usually unknown when creating a record

	FeeChain           Chain
	FeeReceiveAddress  Address
	FeeTokenAddress    string
	EstimatedFeeAmount decimal.Decimal
	FeeTxHash          string
}
