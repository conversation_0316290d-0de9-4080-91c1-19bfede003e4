//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=gas_faucet_repo_mock.go . GasFaucetRepo

package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// GasFaucetRepo .
type GasFaucetRepo interface {
	LockManager

	GetUserWalletAddresses(ctx context.Context, uid string) ([]string, *code.KGError)
	CreateGasFaucet(ctx context.Context, gasFaucet *GasFaucet) (int, error)
	UpdateGasFaucet(ctx context.Context, update *GasFaucetUpdate) error
	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *OrganizationWallets, kgErr *code.KGError)
	GetNativeAssetPrice(ctx context.Context, chainID string) (float64, error)
	SentUsdTodayByWallet(ctx context.Context, orgID int, wallet string) (float64, error)
	SentUsdToday(ctx context.Context, orgID int) (float64, error)
}
