// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: BridgeRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=bridge_repo_mock.go . BridgeRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockBridgeRepo is a mock of BridgeRepo interface.
type MockBridgeRepo struct {
	ctrl     *gomock.Controller
	recorder *MockBridgeRepoMockRecorder
	isgomock struct{}
}

// MockBridgeRepoMockRecorder is the mock recorder for MockBridgeRepo.
type MockBridgeRepoMockRecorder struct {
	mock *MockBridgeRepo
}

// NewMockBridgeRepo creates a new mock instance.
func NewMockBridgeRepo(ctrl *gomock.Controller) *MockBridgeRepo {
	mock := &MockBridgeRepo{ctrl: ctrl}
	mock.recorder = &MockBridgeRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBridgeRepo) EXPECT() *MockBridgeRepoMockRecorder {
	return m.recorder
}

// BatchGetTokenPrices mocks base method.
func (m *MockBridgeRepo) BatchGetTokenPrices(ctx context.Context, tokens []ChainToken) (map[ChainToken]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPrices", ctx, tokens)
	ret0, _ := ret[0].(map[ChainToken]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPrices indicates an expected call of BatchGetTokenPrices.
func (mr *MockBridgeRepoMockRecorder) BatchGetTokenPrices(ctx, tokens any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPrices", reflect.TypeOf((*MockBridgeRepo)(nil).BatchGetTokenPrices), ctx, tokens)
}

// BatchGetTokenPricesIn24H mocks base method.
func (m *MockBridgeRepo) BatchGetTokenPricesIn24H(ctx context.Context, tokens []ChainToken) (map[ChainToken][]*PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPricesIn24H", ctx, tokens)
	ret0, _ := ret[0].(map[ChainToken][]*PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPricesIn24H indicates an expected call of BatchGetTokenPricesIn24H.
func (mr *MockBridgeRepoMockRecorder) BatchGetTokenPricesIn24H(ctx, tokens any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPricesIn24H", reflect.TypeOf((*MockBridgeRepo)(nil).BatchGetTokenPricesIn24H), ctx, tokens)
}

// CreateBridgeOrganization mocks base method.
func (m *MockBridgeRepo) CreateBridgeOrganization(ctx context.Context, bridgeOrg *CreateBridgeOrganizationData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBridgeOrganization", ctx, bridgeOrg)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBridgeOrganization indicates an expected call of CreateBridgeOrganization.
func (mr *MockBridgeRepoMockRecorder) CreateBridgeOrganization(ctx, bridgeOrg any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBridgeOrganization", reflect.TypeOf((*MockBridgeRepo)(nil).CreateBridgeOrganization), ctx, bridgeOrg)
}

// CreateBridgeRecord mocks base method.
func (m *MockBridgeRepo) CreateBridgeRecord(ctx context.Context, params *BridgeRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBridgeRecord", ctx, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBridgeRecord indicates an expected call of CreateBridgeRecord.
func (mr *MockBridgeRepoMockRecorder) CreateBridgeRecord(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBridgeRecord", reflect.TypeOf((*MockBridgeRepo)(nil).CreateBridgeRecord), ctx, params)
}

// CreateBridgeTransfer mocks base method.
func (m *MockBridgeRepo) CreateBridgeTransfer(ctx context.Context, transfer *CreateBridgeTransferData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBridgeTransfer", ctx, transfer)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBridgeTransfer indicates an expected call of CreateBridgeTransfer.
func (mr *MockBridgeRepoMockRecorder) CreateBridgeTransfer(ctx, transfer any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBridgeTransfer", reflect.TypeOf((*MockBridgeRepo)(nil).CreateBridgeTransfer), ctx, transfer)
}

// GetAssetPrice mocks base method.
func (m *MockBridgeRepo) GetAssetPrice(ctx context.Context, chainID, contractAddress string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", ctx, chainID, contractAddress)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockBridgeRepoMockRecorder) GetAssetPrice(ctx, chainID, contractAddress any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockBridgeRepo)(nil).GetAssetPrice), ctx, chainID, contractAddress)
}

// GetBridgeOrganizationByOrgID mocks base method.
func (m *MockBridgeRepo) GetBridgeOrganizationByOrgID(ctx context.Context, organizationID int) (*BridgeOrganizationData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBridgeOrganizationByOrgID", ctx, organizationID)
	ret0, _ := ret[0].(*BridgeOrganizationData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBridgeOrganizationByOrgID indicates an expected call of GetBridgeOrganizationByOrgID.
func (mr *MockBridgeRepoMockRecorder) GetBridgeOrganizationByOrgID(ctx, organizationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBridgeOrganizationByOrgID", reflect.TypeOf((*MockBridgeRepo)(nil).GetBridgeOrganizationByOrgID), ctx, organizationID)
}

// GetBridgeTransfersByOrgID mocks base method.
func (m *MockBridgeRepo) GetBridgeTransfersByOrgID(ctx context.Context, organizationID int) ([]*BridgeTransferData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBridgeTransfersByOrgID", ctx, organizationID)
	ret0, _ := ret[0].([]*BridgeTransferData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBridgeTransfersByOrgID indicates an expected call of GetBridgeTransfersByOrgID.
func (mr *MockBridgeRepoMockRecorder) GetBridgeTransfersByOrgID(ctx, organizationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBridgeTransfersByOrgID", reflect.TypeOf((*MockBridgeRepo)(nil).GetBridgeTransfersByOrgID), ctx, organizationID)
}

// GetNativeAssetPrice mocks base method.
func (m *MockBridgeRepo) GetNativeAssetPrice(ctx context.Context, chainID string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", ctx, chainID)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockBridgeRepoMockRecorder) GetNativeAssetPrice(ctx, chainID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockBridgeRepo)(nil).GetNativeAssetPrice), ctx, chainID)
}

// GetProfitRate mocks base method.
func (m *MockBridgeRepo) GetProfitRate(ctx context.Context, orgID int, serviceType ProfitRateServiceType) (*AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRate", ctx, orgID, serviceType)
	ret0, _ := ret[0].(*AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRate indicates an expected call of GetProfitRate.
func (mr *MockBridgeRepoMockRecorder) GetProfitRate(ctx, orgID, serviceType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRate", reflect.TypeOf((*MockBridgeRepo)(nil).GetProfitRate), ctx, orgID, serviceType)
}

// GetProfitRates mocks base method.
func (m *MockBridgeRepo) GetProfitRates(ctx context.Context, orgID int) ([]*AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRates", ctx, orgID)
	ret0, _ := ret[0].([]*AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRates indicates an expected call of GetProfitRates.
func (mr *MockBridgeRepoMockRecorder) GetProfitRates(ctx, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRates", reflect.TypeOf((*MockBridgeRepo)(nil).GetProfitRates), ctx, orgID)
}

// GetTokenPrice mocks base method.
func (m *MockBridgeRepo) GetTokenPrice(ctx context.Context, chain Chain, tokenID string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPrice", ctx, chain, tokenID)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPrice indicates an expected call of GetTokenPrice.
func (mr *MockBridgeRepoMockRecorder) GetTokenPrice(ctx, chain, tokenID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPrice", reflect.TypeOf((*MockBridgeRepo)(nil).GetTokenPrice), ctx, chain, tokenID)
}

// GetTokenPricesIn24H mocks base method.
func (m *MockBridgeRepo) GetTokenPricesIn24H(ctx context.Context, chain Chain, tokenID string) ([]*PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPricesIn24H", ctx, chain, tokenID)
	ret0, _ := ret[0].([]*PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPricesIn24H indicates an expected call of GetTokenPricesIn24H.
func (mr *MockBridgeRepoMockRecorder) GetTokenPricesIn24H(ctx, chain, tokenID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPricesIn24H", reflect.TypeOf((*MockBridgeRepo)(nil).GetTokenPricesIn24H), ctx, chain, tokenID)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockBridgeRepo) GetWalletsByOrganizationId(ctx context.Context, orgID int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", ctx, orgID)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockBridgeRepoMockRecorder) GetWalletsByOrganizationId(ctx, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockBridgeRepo)(nil).GetWalletsByOrganizationId), ctx, orgID)
}

// UpdateBridgeRecord mocks base method.
func (m *MockBridgeRepo) UpdateBridgeRecord(ctx context.Context, fromTxHash string, params *BridgeRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBridgeRecord", ctx, fromTxHash, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBridgeRecord indicates an expected call of UpdateBridgeRecord.
func (mr *MockBridgeRepoMockRecorder) UpdateBridgeRecord(ctx, fromTxHash, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBridgeRecord", reflect.TypeOf((*MockBridgeRepo)(nil).UpdateBridgeRecord), ctx, fromTxHash, params)
}

// UpsertProfitRate mocks base method.
func (m *MockBridgeRepo) UpsertProfitRate(ctx context.Context, req UpsertProfitRateParams) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertProfitRate", ctx, req)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// UpsertProfitRate indicates an expected call of UpsertProfitRate.
func (mr *MockBridgeRepoMockRecorder) UpsertProfitRate(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertProfitRate", reflect.TypeOf((*MockBridgeRepo)(nil).UpsertProfitRate), ctx, req)
}
