//go:generate go-enum

package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/shopspring/decimal"
)

// SendWithFeeRepo defines the interface for sending with fee repository
type SendWithFeeRepo interface {
	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *OrganizationWallets, kgErr *code.KGError)
	GetSendWithFeeTx(ctx context.Context, id int) (*SendWithFeeTransaction, *code.KGError)
	CreateSendWithFeeTx(ctx context.Context, tx SendWithFeeTransaction) (int, *code.KGError)
	UpdateSendWithFeeTx(ctx context.Context, req UpdateSendWithFeeTxRequest) *code.KGError
	GetNativeAssetPrice(ctx context.Context, chainID string) (float64, error)
}

// SendWithFeeTransactionType defines the type of transaction
// ENUM(main_token, token)
// Main token defines the transaction is for main token and token address should be empty.
// <PERSON><PERSON> defines the transaction is for ERC20/TRC20 token.
type SendWithFeeTransactionType string

// SendWithFeeTransaction defines the transaction information for sending with fee
type SendWithFeeTransaction struct {
	OrganizationID   int
	UID              string
	TxHash           string
	ChainID          string
	TokenAddress     string
	ProfitMarginRate decimal.Decimal
	Type             SendWithFeeTransactionType
	Amount           *string
	Sender           *string
	Recipient        *string
	FeeAddress       *string
	FeeAmount        *decimal.Decimal
	ProfitMargin     *decimal.Decimal
	TokenPrice       *decimal.Decimal

	// TODO: R44 feature
	// ProfitShareRatio *decimal.Decimal
}

// UpdateSendWithFeeTxRequest defines the request for updating a transaction
type UpdateSendWithFeeTxRequest struct {
	ID           int
	Amount       decimal.Decimal
	Sender       string
	Recipient    string
	FeeAddress   string
	FeeAmount    decimal.Decimal
	ProfitMargin decimal.Decimal
	TokenPrice   decimal.Decimal
}
