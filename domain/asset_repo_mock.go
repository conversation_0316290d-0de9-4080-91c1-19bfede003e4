// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: AssetRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=asset_repo_mock.go . AssetRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockAssetRepo is a mock of AssetRepo interface.
type MockAssetRepo struct {
	ctrl     *gomock.Controller
	recorder *MockAssetRepoMockRecorder
}

// MockAssetRepoMockRecorder is the mock recorder for MockAssetRepo.
type MockAssetRepoMockRecorder struct {
	mock *MockAssetRepo
}

// NewMockAssetRepo creates a new mock instance.
func NewMockAssetRepo(ctrl *gomock.Controller) *MockAssetRepo {
	mock := &MockAssetRepo{ctrl: ctrl}
	mock.recorder = &MockAssetRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssetRepo) EXPECT() *MockAssetRepoMockRecorder {
	return m.recorder
}

// DeleteTokenAmounts mocks base method.
func (m *MockAssetRepo) DeleteTokenAmounts(arg0 context.Context, arg1 map[ChainAddress][]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTokenAmounts", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTokenAmounts indicates an expected call of DeleteTokenAmounts.
func (mr *MockAssetRepoMockRecorder) DeleteTokenAmounts(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTokenAmounts", reflect.TypeOf((*MockAssetRepo)(nil).DeleteTokenAmounts), arg0, arg1)
}

// GetAllTokens mocks base method.
func (m *MockAssetRepo) GetAllTokens(arg0 context.Context) ([]ChainToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTokens", arg0)
	ret0, _ := ret[0].([]ChainToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTokens indicates an expected call of GetAllTokens.
func (mr *MockAssetRepoMockRecorder) GetAllTokens(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTokens", reflect.TypeOf((*MockAssetRepo)(nil).GetAllTokens), arg0)
}

// GetSingleAsset mocks base method.
func (m *MockAssetRepo) GetSingleAsset(arg0 context.Context, arg1 Chain, arg2 AssetType, arg3 string, arg4 []Address) (Asset, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingleAsset", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(Asset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSingleAsset indicates an expected call of GetSingleAsset.
func (mr *MockAssetRepoMockRecorder) GetSingleAsset(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingleAsset", reflect.TypeOf((*MockAssetRepo)(nil).GetSingleAsset), arg0, arg1, arg2, arg3, arg4)
}

// GetTokensByAddress mocks base method.
func (m *MockAssetRepo) GetTokensByAddress(arg0 context.Context, arg1 map[Chain][]Address) ([]ChainToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokensByAddress", arg0, arg1)
	ret0, _ := ret[0].([]ChainToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokensByAddress indicates an expected call of GetTokensByAddress.
func (mr *MockAssetRepoMockRecorder) GetTokensByAddress(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokensByAddress", reflect.TypeOf((*MockAssetRepo)(nil).GetTokensByAddress), arg0, arg1)
}

// ListAssets mocks base method.
func (m *MockAssetRepo) ListAssets(arg0 context.Context, arg1 *ListAssetsParam) ([]Asset, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAssets", arg0, arg1)
	ret0, _ := ret[0].([]Asset)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListAssets indicates an expected call of ListAssets.
func (mr *MockAssetRepoMockRecorder) ListAssets(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAssets", reflect.TypeOf((*MockAssetRepo)(nil).ListAssets), arg0, arg1)
}

// ListMainTokens mocks base method.
func (m *MockAssetRepo) ListMainTokens(arg0 context.Context, arg1 map[Chain][]Address) ([]Asset, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMainTokens", arg0, arg1)
	ret0, _ := ret[0].([]Asset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMainTokens indicates an expected call of ListMainTokens.
func (mr *MockAssetRepoMockRecorder) ListMainTokens(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMainTokens", reflect.TypeOf((*MockAssetRepo)(nil).ListMainTokens), arg0, arg1)
}

// SetDefiAssets mocks base method.
func (m *MockAssetRepo) SetDefiAssets(arg0 context.Context, arg1 map[ChainAddress][]*DefiAsset) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDefiAssets", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDefiAssets indicates an expected call of SetDefiAssets.
func (mr *MockAssetRepoMockRecorder) SetDefiAssets(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDefiAssets", reflect.TypeOf((*MockAssetRepo)(nil).SetDefiAssets), arg0, arg1)
}

// SetNftAmounts mocks base method.
func (m *MockAssetRepo) SetNftAmounts(arg0 context.Context, arg1 map[ChainAddress][]*NftAmount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNftAmounts", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetNftAmounts indicates an expected call of SetNftAmounts.
func (mr *MockAssetRepoMockRecorder) SetNftAmounts(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNftAmounts", reflect.TypeOf((*MockAssetRepo)(nil).SetNftAmounts), arg0, arg1)
}

// SetTokenAmounts mocks base method.
func (m *MockAssetRepo) SetTokenAmounts(arg0 context.Context, arg1 map[ChainAddress][]*TokenAmount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTokenAmounts", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTokenAmounts indicates an expected call of SetTokenAmounts.
func (mr *MockAssetRepoMockRecorder) SetTokenAmounts(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTokenAmounts", reflect.TypeOf((*MockAssetRepo)(nil).SetTokenAmounts), arg0, arg1)
}

// UpdateDefiAssets mocks base method.
func (m *MockAssetRepo) UpdateDefiAssets(arg0 context.Context, arg1 map[ChainAddress][]*DefiAsset) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDefiAssets", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDefiAssets indicates an expected call of UpdateDefiAssets.
func (mr *MockAssetRepoMockRecorder) UpdateDefiAssets(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDefiAssets", reflect.TypeOf((*MockAssetRepo)(nil).UpdateDefiAssets), arg0, arg1)
}

// UpdateTokenAmounts mocks base method.
func (m *MockAssetRepo) UpdateTokenAmounts(arg0 context.Context, arg1 map[ChainAddress][]*TokenAmount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTokenAmounts", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTokenAmounts indicates an expected call of UpdateTokenAmounts.
func (mr *MockAssetRepoMockRecorder) UpdateTokenAmounts(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTokenAmounts", reflect.TypeOf((*MockAssetRepo)(nil).UpdateTokenAmounts), arg0, arg1)
}
