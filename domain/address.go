package domain

import (
	"github.com/ethereum/go-ethereum/common"
	"github.com/samber/lo"
)

type Address interface {
	String() string
	MarshalText() ([]byte, error)
	Equal(b Address) bool
	IsEmpty() bool
}

func NewAddressByChain(chain Chain, address string) Address {
	if chain == nil {
		return NewStrAddress("")
	}
	if chain.IsEVM() {
		return NewEvmAddress(address)
	} else if chain.IsTVM() {
		return NewTronAddress(address)
	} else {
		return NewStrAddress(address)
	}
}

type StrAddress string

func (a StrAddress) String() string {
	return string(a)
}

func (a StrAddress) MarshalText() ([]byte, error) {
	return []byte(a), nil
}

func (a StrAddress) Equal(b Address) bool {
	if b, ok := b.(StrAddress); ok {
		return a == b
	}
	return false
}

func (a StrAddress) IsEmpty() bool {
	return a == ""
}

func NewStrAddress(address string) Address {
	return StrAddress(address)
}

type EvmAddress struct {
	common.Address
}

func (a EvmAddress) String() string {
	return a.Address.String()
}

func (a EvmAddress) MarshalText() ([]byte, error) {
	return []byte(a.Address.String()), nil
}

func (a EvmAddress) Equal(b Address) bool {
	if b, ok := b.(EvmAddress); ok {
		return a.Address == b.Address
	}
	return false
}

func (a EvmAddress) IsEmpty() bool {
	return a.Address == common.Address{}
}

func NewEvmAddress(address string) EvmAddress {
	return EvmAddress{common.HexToAddress(address)}
}

type ChainAddress struct {
	Chain   Chain
	Address Address
}

// ToChainAddresses converts a list of addresses to a list of chain addresses.
func ToChainAddresses(chain Chain, addresses []Address) []ChainAddress {
	return lo.Map(addresses, func(address Address, _ int) ChainAddress {
		return ChainAddress{Chain: chain, Address: address}
	})
}
