//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=send_with_rent_repo_mock.go . SendWithRentRepo

package domain

import (
	"context"
)

// SendWithRentRepo .
type SendWithRentRepo interface {
	CreateSendWithRent(ctx context.Context, sendWithRent *SendWithRent) (int, error)
	UpdateSendWithRent(ctx context.Context, update *UpdateSendWithRentRequest) error
	// GetSendWithRentByID should return ErrRecordNotFound if send-with-rent not found
	GetSendWithRentByID(ctx context.Context, id int) (*SendWithRent, error)
}
