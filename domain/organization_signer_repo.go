//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=organization_signer_repo_mock.go . OrganizationWalletRepo,OrganizationSignerRepo
package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// OrganizationWalletRepo interface for organization wallets repository
type OrganizationWalletRepo interface {
	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *OrganizationWallets, kgErr *code.KGError)
}

// OrganizationSignerRepo interface for organization signer repository
type OrganizationSignerRepo interface {
	OrganizationWalletRepo

	GetSignerByOrganizationId(ctx context.Context, orgID int, encryptor PrivateKeyEncryptor) (s *OrganizationSigner, kgErr *code.KGError)
	SaveOrganizationSigner(ctx context.Context, orgID int, signer *OrganizationSigner) *code.KGError
	GetOrganizationName(ctx context.Context, orgID int) (name string, kgErr *code.KGError)
	SaveAuditLog(context.Context, *SigningAuditLog) *code.KGError
}
