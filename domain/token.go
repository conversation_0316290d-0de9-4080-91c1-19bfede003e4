//go:generate go-enum
package domain

type Token interface {
	Chain() Chain
	ID() string // can be contract address or main token id. It forms a unique token with chain and id
	Name() string
	Symbol() string
	LogoUrl() string
	Decimals() uint
	IsVerified() bool
	IsMainToken() bool
	BinanceTicker() string
}

type TronToken interface {
	Token
	TrcType() TrcType
}

// commonToken defines common attributes shared across tokens.
type commonToken struct {
	chain         Chain
	id            string
	name          string
	symbol        string
	logoUrl       string
	decimals      uint
	isVerified    bool
	binanceTicker string
}

// Common methods for all tokens.
func (t commonToken) Chain() Chain          { return t.chain }
func (t commonToken) ID() string            { return t.id }
func (t commonToken) Name() string          { return t.name }
func (t commonToken) Symbol() string        { return t.symbol }
func (t commonToken) LogoUrl() string       { return t.logoUrl }
func (t commonToken) Decimals() uint        { return t.decimals }
func (t commonToken) IsVerified() bool      { return t.isVerified }
func (t commonToken) IsMainToken() bool     { return t.Chain().MainToken().ID() == t.ID() }
func (t commonToken) BinanceTicker() string { return t.binanceTicker }

// TrcType is the tron token type
// ENUM(trc10, trc20)
type TrcType string

type tronToken struct {
	commonToken
	trcType TrcType
}

func (t tronToken) TrcType() TrcType { return t.trcType }

func NewToken(chain Chain, id, name, symbol, logoUrl string, decimals uint, isVerified bool) Token {
	return &commonToken{
		chain:      chain,
		id:         id,
		name:       name,
		symbol:     symbol,
		logoUrl:    logoUrl,
		decimals:   decimals,
		isVerified: isVerified,
	}
}

func NewTokenWithTicker(chain Chain, id, name, symbol, logoUrl string, decimals uint, isVerified bool, binanceTicker string) Token {
	return &commonToken{
		chain:         chain,
		id:            id,
		name:          name,
		symbol:        symbol,
		logoUrl:       logoUrl,
		decimals:      decimals,
		isVerified:    isVerified,
		binanceTicker: binanceTicker,
	}
}

func NewTronToken(chain Chain, id, name, symbol, logoUrl string, decimals uint, isVerified bool, trcType TrcType) TronToken {
	return &tronToken{
		commonToken: commonToken{
			chain:      chain,
			id:         id,
			name:       name,
			symbol:     symbol,
			logoUrl:    logoUrl,
			decimals:   decimals,
			isVerified: isVerified,
		},
		trcType: trcType,
	}
}

func NewTronTokenWithTicker(chain Chain, id, name, symbol, logoUrl string, decimals uint, isVerified bool, trcType TrcType, binanceTicker string) TronToken {
	return &tronToken{
		commonToken: commonToken{
			chain:         chain,
			id:            id,
			name:          name,
			symbol:        symbol,
			logoUrl:       logoUrl,
			decimals:      decimals,
			isVerified:    isVerified,
			binanceTicker: binanceTicker,
		},
		trcType: trcType,
	}
}

type WalletToken struct {
	Wallet Address
	Token
}

type WalletTokenID struct {
	Wallet  Address
	TokenID string
}

type TokenPrice struct {
	Chain Chain
	ID    string // can be contract address or main token id. It forms a unique token with chain and id
	Price float64
}

type ChainToken struct {
	Chain   Chain
	TokenID string
}

func (c *ChainToken) IsMainToken() bool {
	return c.TokenID == c.Chain.MainToken().ID()
}
