//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=application_repo_mock.go . ApplicationRepo
package domain

import (
	"context"
)

// ApplicationRepo provides methods for working with applications
type ApplicationRepo interface {
	// GetApplication fetches a single Application by ID
	GetApplication(ctx context.Context, id string) (*Application, error)

	// GetAllApplications returns all Applications
	GetAllApplications(ctx context.Context) ([]*Application, error)

	// GetAllOAuthApplications returns all OAuthApplications
	GetAllOAuthApplications(ctx context.Context) ([]*OAuthApplication, error)

	// GetAllCustomAuthApplications returns all CustomAuthApplications
	GetAllCustomAuthApplications(ctx context.Context) ([]*CustomAuthApplication, error)

	// UpsertOAuthApplication creates or updates an OAuthApplication and associates it with an organization
	UpsertOAuthApplication(ctx context.Context, orgID int, app *OAuthApplication) error

	// UpsertCustomAuthApplication creates or updates a CustomAuthApplication and associates it with an organization
	UpsertCustomAuthApplication(ctx context.Context, orgID int, app *CustomAuthApplication) error

	// GetOAuthApplication returns an OAuthApplication by id
	GetOAuthApplication(ctx context.Context, id string) (*OAuthApplication, error)

	// GetOAuthApplicationInOrg returns all OAuthApplications for an organization with pagination
	GetOAuthApplicationInOrg(ctx context.Context, params GetOAuthApplicationInOrgParams) ([]*OAuthApplication, int, error)

	// GetApplicationOrgId returns the organization ID for an application
	GetApplicationOrgId(ctx context.Context, id string) (int, error)

	// GetOAuthApplicationByName returns an OAuthApplication by name
	GetOAuthApplicationByName(ctx context.Context, name string) (*OAuthApplication, error)

	// GetOAuthApplicationByDomain returns an OAuthApplication by domain
	GetOAuthApplicationByDomain(ctx context.Context, domain string) (*OAuthApplication, error)

	// GetCustomAuthApplication returns a CustomAuthApplication by id
	GetCustomAuthApplication(ctx context.Context, id string) (*CustomAuthApplication, error)
}

// GetOAuthApplicationInOrgParams defines parameters for paginating OAuth applications in an organization
type GetOAuthApplicationInOrgParams struct {
	OrgID    int
	Page     int
	PageSize int
}
