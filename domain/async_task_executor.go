//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=async_task_executor_mock.go . AsyncTaskExecutor
package domain

import (
	"context"
	"time"
)

// HttpTask .
type HttpTask struct {
	Method  string
	URL     string
	Headers map[string]string
	Body    []byte
	Timeout time.Duration
}

// AsyncTaskExecutor .
type AsyncTaskExecutor interface {
	Execute(ctx context.Context, queueID string, task *HttpTask, taskName string) error
}
