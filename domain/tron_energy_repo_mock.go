// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: TronEnergyRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=tron_energy_repo_mock.go . TronEnergyRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockTronEnergyRepo is a mock of TronEnergyRepo interface.
type MockTronEnergyRepo struct {
	ctrl     *gomock.Controller
	recorder *MockTronEnergyRepoMockRecorder
}

// MockTronEnergyRepoMockRecorder is the mock recorder for MockTronEnergyRepo.
type MockTronEnergyRepoMockRecorder struct {
	mock *MockTronEnergyRepo
}

// NewMockTronEnergyRepo creates a new mock instance.
func NewMockTronEnergyRepo(ctrl *gomock.Controller) *MockTronEnergyRepo {
	mock := &MockTronEnergyRepo{ctrl: ctrl}
	mock.recorder = &MockTronEnergyRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTronEnergyRepo) EXPECT() *MockTronEnergyRepoMockRecorder {
	return m.recorder
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockTronEnergyRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockTronEnergyRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockTronEnergyRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}
