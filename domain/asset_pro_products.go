//go:generate go-enum
package domain

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/paging"
	"github.com/shopspring/decimal"
)

// AssetProProduct is a domain model that represents a asset pro product
type AssetProProduct struct {
	AssetProProductBaseInfo
	AssetProProductInfo
}

// AssetProProductBaseInfo is a domain model that represents a asset pro product base info
type AssetProProductBaseInfo struct {
	ID            int
	Name          string
	Image         string
	ChainID       string
	BaseCurrency  AssetProProductBaseCurrency
	QuoteCurrency AssetProProductQuoteCurrency
	Type          AssetProProductType
	TokenLogo     string
	CreatedAt     time.Time
	UpdatedAt     time.Time
	DeletedAt     *time.Time
}

// AssetProProductInfo is a domain model that represents a asset pro product info
type AssetProProductInfo struct {
	ProductBaseInfoID         int
	ID                        int
	OrganizationID            int
	Operator                  *StudioOperator
	IsPublished               bool
	Image                     string
	Name                      string
	Price                     *decimal.Decimal
	OrderLimitsFrom           *decimal.Decimal
	OrderLimitsTo             *decimal.Decimal
	Stock                     *decimal.Decimal
	FeeType                   AssetProProductFeeType
	ProportionalFeePercentage *decimal.Decimal
	ProportionalMinimumFee    *decimal.Decimal
	Decimals                  int
	CreatedAt                 time.Time
	UpdatedAt                 time.Time
	DeletedAt                 *time.Time
}

// AssetProProductRepo is a repository interface that represents a asset pro product repository
type AssetProProductRepo interface {
	CreateBaseInfos(ctx context.Context, products []*AssetProProductBaseInfo) error
	CreateProduct(ctx context.Context, organizationID int, params *CreateProductParams) error
	UpdateProductByOperator(ctx context.Context, organizationID int, operatorUID string, productID int, params *UpdateProductParams) *code.KGError
	GetProductsByOrganizationID(ctx context.Context, organizationID int, params *GetProductsParams, query *paging.Query) ([]*AssetProProduct, *paging.Resp, error)
	GetProductByID(ctx context.Context, productID int) (*AssetProProduct, *code.KGError)
}

// CreateProductParams is a domain model that represents a create product params
type CreateProductParams struct {
	FeeType                   AssetProProductFeeType
	ProportionalFeePercentage *decimal.Decimal
	ProportionalMinimumFee    *decimal.Decimal
}

// GetProductsParams is a domain model that represents a get products params
type GetProductsParams struct {
	IsPublished *bool
}

// UpdateProductParams is a domain model that represents a update store info params
type UpdateProductParams struct {
	IsPublished               bool
	Image                     *string
	Name                      string
	Price                     decimal.Decimal
	OrderLimitsFrom           *decimal.Decimal
	OrderLimitsTo             *decimal.Decimal
	Stock                     *decimal.Decimal
	FeeType                   AssetProProductFeeType
	ProportionalFeePercentage *decimal.Decimal
	ProportionalMinimumFee    *decimal.Decimal
}

// AssetProProductType is a domain model that represents a product type
// ENUM(buy_crypto)
type AssetProProductType string

// AssetProProductFeeType is a domain model that represents a fee type
// ENUM(no_fee, fee_included)
type AssetProProductFeeType string

// AssetProProductBaseCurrency is a domain model that represents a base currency
// ENUM(USDT, USDC)
type AssetProProductBaseCurrency string

// AssetProProductQuoteCurrency is a domain model that represents a quote currency
// ENUM(TWD)
type AssetProProductQuoteCurrency string
