package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// CustomerRepo interface for customer repository
type CustomerRepo interface {
	DoesCustomerExist(ctx context.Context, orgID int, uid string) (bool, *code.KGError)
	GetCustomers(ctx context.Context, orgID int) ([]*Customer, *code.KGError)
	GetCustomerByUID(ctx context.Context, uid string, orgID int) (*Customer, *code.KGError)
	GetCustomer(ctx context.Context, uid, phoneNumber, email string, orgID int) (*Customer, *code.KGError)
	GetCustomersForUser360(ctx context.Context, params *GetCustomersForUser360Params) ([]CustomerUser360, *code.KGError)
	GetCustomerForUser360(ctx context.Context, params *GetCustomerForUser360Params) (*CustomerUser360Detail, *code.KGError)
	CreateCustomer(ctx context.Context, uid, clientID string) *code.KGError
	UpdateCustomerOfStudioOrg(ctx context.Context, params UpdateCustomerOfStudioOrgParams) *code.KGError
	GetLineUIDByKGUID(ctx context.Context, channelID string, orgID int, kgUID string) (*string, *code.KGError)
	GetCustomerByLineUID(ctx context.Context, channelID string, lineUserID string) (*Customer, *code.KGError)
	LinkLineUser(ctx context.Context, channelID, lineUserID string, orgID int, kgUserID string) *code.KGError
	UnlinkLineUser(ctx context.Context, channelID, lineUserID string) *code.KGError
	CountForComplianceStatistic(ctx context.Context, orgID int) (*CountOfComplianceStatistic, *code.KGError)
}
