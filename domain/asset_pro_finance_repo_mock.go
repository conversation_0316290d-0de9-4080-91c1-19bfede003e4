// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: AssetProFinanceRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=asset_pro_finance_repo_mock.go . AssetProFinanceRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockAssetProFinanceRepo is a mock of AssetProFinanceRepo interface.
type MockAssetProFinanceRepo struct {
	ctrl     *gomock.Controller
	recorder *MockAssetProFinanceRepoMockRecorder
}

// MockAssetProFinanceRepoMockRecorder is the mock recorder for MockAssetProFinanceRepo.
type MockAssetProFinanceRepoMockRecorder struct {
	mock *MockAssetProFinanceRepo
}

// NewMockAssetProFinanceRepo creates a new mock instance.
func NewMockAssetProFinanceRepo(ctrl *gomock.Controller) *MockAssetProFinanceRepo {
	mock := &MockAssetProFinanceRepo{ctrl: ctrl}
	mock.recorder = &MockAssetProFinanceRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssetProFinanceRepo) EXPECT() *MockAssetProFinanceRepoMockRecorder {
	return m.recorder
}

// GetAllLiquidities mocks base method.
func (m *MockAssetProFinanceRepo) GetAllLiquidities(arg0 context.Context) ([]*AssetProLiquidity, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLiquidities", arg0)
	ret0, _ := ret[0].([]*AssetProLiquidity)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetAllLiquidities indicates an expected call of GetAllLiquidities.
func (mr *MockAssetProFinanceRepoMockRecorder) GetAllLiquidities(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLiquidities", reflect.TypeOf((*MockAssetProFinanceRepo)(nil).GetAllLiquidities), arg0)
}

// GetLiquidities mocks base method.
func (m *MockAssetProFinanceRepo) GetLiquidities(arg0 context.Context, arg1 int) ([]*AssetProLiquidity, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiquidities", arg0, arg1)
	ret0, _ := ret[0].([]*AssetProLiquidity)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetLiquidities indicates an expected call of GetLiquidities.
func (mr *MockAssetProFinanceRepoMockRecorder) GetLiquidities(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiquidities", reflect.TypeOf((*MockAssetProFinanceRepo)(nil).GetLiquidities), arg0, arg1)
}

// GetLiquidity mocks base method.
func (m *MockAssetProFinanceRepo) GetLiquidity(arg0 context.Context, arg1 int, arg2 LiquidityType, arg3 string, arg4 *string) (*AssetProLiquidity, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiquidity", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*AssetProLiquidity)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetLiquidity indicates an expected call of GetLiquidity.
func (mr *MockAssetProFinanceRepoMockRecorder) GetLiquidity(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiquidity", reflect.TypeOf((*MockAssetProFinanceRepo)(nil).GetLiquidity), arg0, arg1, arg2, arg3, arg4)
}

// GetProfitRate mocks base method.
func (m *MockAssetProFinanceRepo) GetProfitRate(arg0 context.Context, arg1 int, arg2 ProfitRateServiceType) (*AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRate indicates an expected call of GetProfitRate.
func (mr *MockAssetProFinanceRepoMockRecorder) GetProfitRate(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRate", reflect.TypeOf((*MockAssetProFinanceRepo)(nil).GetProfitRate), arg0, arg1, arg2)
}

// GetProfitRates mocks base method.
func (m *MockAssetProFinanceRepo) GetProfitRates(arg0 context.Context, arg1 int) ([]*AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRates", arg0, arg1)
	ret0, _ := ret[0].([]*AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRates indicates an expected call of GetProfitRates.
func (mr *MockAssetProFinanceRepoMockRecorder) GetProfitRates(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRates", reflect.TypeOf((*MockAssetProFinanceRepo)(nil).GetProfitRates), arg0, arg1)
}

// UpsertProfitRate mocks base method.
func (m *MockAssetProFinanceRepo) UpsertProfitRate(arg0 context.Context, arg1 UpsertProfitRateParams) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertProfitRate", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// UpsertProfitRate indicates an expected call of UpsertProfitRate.
func (mr *MockAssetProFinanceRepoMockRecorder) UpsertProfitRate(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertProfitRate", reflect.TypeOf((*MockAssetProFinanceRepo)(nil).UpsertProfitRate), arg0, arg1)
}
