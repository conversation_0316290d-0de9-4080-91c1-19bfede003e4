// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// LiquidityTypeBuyCrypto is a LiquidityType of type buy_crypto.
	LiquidityTypeBuyCrypto LiquidityType = "buy_crypto"
	// LiquidityTypeGasSwap is a LiquidityType of type gas_swap.
	LiquidityTypeGasSwap LiquidityType = "gas_swap"
)

var ErrInvalidLiquidityType = errors.New("not a valid LiquidityType")

// String implements the Stringer interface.
func (x LiquidityType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x LiquidityType) IsValid() bool {
	_, err := ParseLiquidityType(string(x))
	return err == nil
}

var _LiquidityTypeValue = map[string]LiquidityType{
	"buy_crypto": LiquidityTypeBuyCrypto,
	"gas_swap":   LiquidityTypeGasSwap,
}

// ParseLiquidityType attempts to convert a string to a LiquidityType.
func ParseLiquidityType(name string) (LiquidityType, error) {
	if x, ok := _LiquidityTypeValue[name]; ok {
		return x, nil
	}
	return LiquidityType(""), fmt.Errorf("%s is %w", name, ErrInvalidLiquidityType)
}

const (
	// ProfitRateServiceTypeBuy is a ProfitRateServiceType of type buy.
	ProfitRateServiceTypeBuy ProfitRateServiceType = "buy"
	// ProfitRateServiceTypeSwapGas is a ProfitRateServiceType of type swap_gas.
	ProfitRateServiceTypeSwapGas ProfitRateServiceType = "swap_gas"
	// ProfitRateServiceTypeSwapDefi is a ProfitRateServiceType of type swap_defi.
	ProfitRateServiceTypeSwapDefi ProfitRateServiceType = "swap_defi"
	// ProfitRateServiceTypeSendWithFee is a ProfitRateServiceType of type send_with_fee.
	ProfitRateServiceTypeSendWithFee ProfitRateServiceType = "send_with_fee"
	// ProfitRateServiceTypeSendGasless is a ProfitRateServiceType of type send_gasless.
	ProfitRateServiceTypeSendGasless ProfitRateServiceType = "send_gasless"
	// ProfitRateServiceTypeSendBatch is a ProfitRateServiceType of type send_batch.
	ProfitRateServiceTypeSendBatch ProfitRateServiceType = "send_batch"
	// ProfitRateServiceTypeBridge is a ProfitRateServiceType of type bridge.
	ProfitRateServiceTypeBridge ProfitRateServiceType = "bridge"
)

var ErrInvalidProfitRateServiceType = errors.New("not a valid ProfitRateServiceType")

// String implements the Stringer interface.
func (x ProfitRateServiceType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x ProfitRateServiceType) IsValid() bool {
	_, err := ParseProfitRateServiceType(string(x))
	return err == nil
}

var _ProfitRateServiceTypeValue = map[string]ProfitRateServiceType{
	"buy":           ProfitRateServiceTypeBuy,
	"swap_gas":      ProfitRateServiceTypeSwapGas,
	"swap_defi":     ProfitRateServiceTypeSwapDefi,
	"send_with_fee": ProfitRateServiceTypeSendWithFee,
	"send_gasless":  ProfitRateServiceTypeSendGasless,
	"send_batch":    ProfitRateServiceTypeSendBatch,
	"bridge":        ProfitRateServiceTypeBridge,
}

// ParseProfitRateServiceType attempts to convert a string to a ProfitRateServiceType.
func ParseProfitRateServiceType(name string) (ProfitRateServiceType, error) {
	if x, ok := _ProfitRateServiceTypeValue[name]; ok {
		return x, nil
	}
	return ProfitRateServiceType(""), fmt.Errorf("%s is %w", name, ErrInvalidProfitRateServiceType)
}
