package domain

import (
	"testing"
)

func TestCreateBridgeExternalAccountRequest_ValidateUSAccount(t *testing.T) {
	// Test valid US account
	validReq := &CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "usd",
		BankName:         "Chase Bank",
		AccountOwnerName: "John Doe",
		AccountOwnerType: "individual",
		AccountType:      "us",
		US: &USDetails{
			Account: &USAccount{
				AccountNumber: "************",
				RoutingNumber: "*********",
			},
		},
		Address: &AddressDetails{
			Country:     "USA",
			City:        "New York",
			StreetLine1: "270 Park Ave",
			StreetLine2: "Floor 10",
			PostalCode:  "10001",
			State:       &[]string{"NY"}[0],
		},
	}

	err := validReq.ValidateAccountTypeSpecificFields()
	if err != nil {
		t.Errorf("Expected validation to pass for valid US account, but got error: %v", err)
	}

	// Test missing US details
	missingUSReq := &CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "usd",
		BankName:         "Chase Bank",
		AccountOwnerName: "John Doe",
		AccountOwnerType: "individual",
		AccountType:      "us",
		// US field is nil
	}

	err = missingUSReq.ValidateAccountTypeSpecificFields()
	if err == nil {
		t.Error("Expected validation to fail when US details are missing")
	}
	expectedMsg := "us account details are required when account_type is 'us'"
	if err.Error() != expectedMsg {
		t.Errorf("Expected error message '%s', got '%s'", expectedMsg, err.Error())
	}

	// Test invalid street line 1 (too short)
	invalidStreetReq := &CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "usd",
		BankName:         "Chase Bank",
		AccountOwnerName: "John Doe",
		AccountOwnerType: "individual",
		AccountType:      "us",
		US: &USDetails{
			Account: &USAccount{
				AccountNumber: "************",
				RoutingNumber: "*********",
			},
		},
		Address: &AddressDetails{
			Country:     "USA",
			City:        "New York",
			StreetLine1: "123", // Too short
			PostalCode:  "10001",
		},
	}

	err = invalidStreetReq.ValidateAccountTypeSpecificFields()
	if err == nil {
		t.Error("Expected validation to fail when street_line_1 is too short")
	}
	expectedMsg = "street_line_1 must be between 4 and 35 characters"
	if err.Error() != expectedMsg {
		t.Errorf("Expected error message '%s', got '%s'", expectedMsg, err.Error())
	}

	// Test missing account number
	missingAccountReq := &CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "usd",
		BankName:         "Chase Bank",
		AccountOwnerName: "John Doe",
		AccountOwnerType: "individual",
		AccountType:      "us",
		US: &USDetails{
			Account: &USAccount{
				// AccountNumber is missing
				RoutingNumber: "*********",
			},
		},
		Address: &AddressDetails{
			Country:     "USA",
			City:        "New York",
			StreetLine1: "270 Park Ave",
			PostalCode:  "10001",
		},
	}

	err = missingAccountReq.ValidateAccountTypeSpecificFields()
	if err == nil {
		t.Error("Expected validation to fail when account_number is missing")
	}
	expectedMsg = "account_number is required for US accounts"
	if err.Error() != expectedMsg {
		t.Errorf("Expected error message '%s', got '%s'", expectedMsg, err.Error())
	}
}

func TestCreateBridgeExternalAccountRequest_ValidateIBANAccount(t *testing.T) {
	// Test valid IBAN account
	validReq := &CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "usd",
		BankName:         "Test Bank",
		AccountOwnerName: "Test User",
		AccountOwnerType: "individual",
		AccountType:      "iban",
		Swift: &SwiftDetails{
			Account: &SwiftAccount{
				AccountNumber: "12345",
				BIC:           "TESTBIC123",
			},
		},
	}

	err := validReq.ValidateAccountTypeSpecificFields()
	if err != nil {
		t.Errorf("Expected validation to pass for valid IBAN account, but got error: %v", err)
	}

	// Test missing SWIFT details
	missingSwiftReq := &CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "usd",
		BankName:         "Test Bank",
		AccountOwnerName: "Test User",
		AccountOwnerType: "individual",
		AccountType:      "iban",
		// Swift field is nil
	}

	err = missingSwiftReq.ValidateAccountTypeSpecificFields()
	if err == nil {
		t.Error("Expected validation to fail when SWIFT details are missing")
	}
	expectedMsg := "swift details are required when account_type is 'iban'"
	if err.Error() != expectedMsg {
		t.Errorf("Expected error message '%s', got '%s'", expectedMsg, err.Error())
	}
}

func TestCreateBridgeExternalAccountRequest_ValidateUnknownAccount(t *testing.T) {
	// Test that unknown account type requires Swift details with account information
	validReq := &CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "usd",
		BankName:         "Mega International Commercial Bank",
		AccountOwnerName: "KryptoGO Co., Ltd.",
		AccountOwnerType: "business",
		AccountType:      "unknown",
		BusinessName:     &[]string{"KryptoGO Co., Ltd."}[0],
		Swift: &SwiftDetails{
			Account: &SwiftAccount{
				AccountNumber: "***********",
				BIC:           "ICBCTWTP216",
			},
			Address: &AddressDetails{
				Country:     "TWN",
				City:        "Taipei City",
				StreetLine1: "No. 333, Section 1",
				StreetLine2: "Keelung Rd, Xinyi District",
				PostalCode:  "110",
			},
			PurposeOfFunds:           []string{"invoice_for_goods_and_services"},
			Category:                 "supplier",
			ShortBusinessDescription: "operation",
		},
		Address: &AddressDetails{
			Country:     "TWN",
			City:        "Taipei City",
			StreetLine1: "12F., No. 161, Songde Rd.",
			StreetLine2: "Xinyi Dist.",
			PostalCode:  "110",
		},
	}

	err := validReq.ValidateAccountTypeSpecificFields()
	if err != nil {
		t.Errorf("Expected validation to pass for valid unknown account, but got error: %v", err)
	}

	// Test missing SWIFT details for unknown account
	missingSwiftReq := &CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "usd",
		BankName:         "Test Bank",
		AccountOwnerName: "Test User",
		AccountOwnerType: "individual",
		AccountType:      "unknown",
		// Swift field is nil
	}

	err = missingSwiftReq.ValidateAccountTypeSpecificFields()
	if err == nil {
		t.Error("Expected validation to fail when SWIFT details are missing for unknown account")
	}
	expectedMsg := "swift details are required when account_type is 'unknown'"
	if err.Error() != expectedMsg {
		t.Errorf("Expected error message '%s', got '%s'", expectedMsg, err.Error())
	}

	// Test missing SWIFT account details for unknown account
	missingSwiftAccountReq := &CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "usd",
		BankName:         "Test Bank",
		AccountOwnerName: "Test User",
		AccountOwnerType: "individual",
		AccountType:      "unknown",
		Swift: &SwiftDetails{
			// Account field is nil
			PurposeOfFunds: []string{"invoice_for_goods_and_services"},
		},
	}

	err = missingSwiftAccountReq.ValidateAccountTypeSpecificFields()
	if err == nil {
		t.Error("Expected validation to fail when SWIFT account details are missing for unknown account")
	}
	expectedMsg = "swift account details are required when account_type is 'unknown'"
	if err.Error() != expectedMsg {
		t.Errorf("Expected error message '%s', got '%s'", expectedMsg, err.Error())
	}
}

func TestCreateBridgeExternalAccountRequest_ValidateClabeAccount(t *testing.T) {
	// Test that clabe account type doesn't require specific validation
	req := &CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "mxn",
		BankName:         "Banco Santander México",
		AccountOwnerName: "Test User",
		AccountOwnerType: "individual",
		AccountType:      "clabe",
		// No specific fields required for clabe
	}

	err := req.ValidateAccountTypeSpecificFields()
	if err != nil {
		t.Errorf("Expected validation to pass for clabe account type, but got error: %v", err)
	}
} 