// Code generated by go-enum DO NOT EDIT.
// 
// 
//
// 
// Enum file generated for CallbackStatus and CallbackType
//

package domain

import (
	"fmt"
	"strings"
)

const (
	// CallbackStatusSuccess is a CallbackStatus of type success.
	CallbackStatusSuccess CallbackStatus = "success"
	// CallbackStatusFailed is a CallbackStatus of type failed.
	CallbackStatusFailed CallbackStatus = "failed"
)

// CallbackStatusValues returns all values of the enum
func CallbackStatusValues() []CallbackStatus {
	return []CallbackStatus{
		CallbackStatusSuccess,
		CallbackStatusFailed,
	}
}

// CallbackStatusStrings returns all values of the enum as strings
func CallbackStatusStrings() []string {
	return []string{
		string(CallbackStatusSuccess),
		string(CallbackStatusFailed),
	}
}

// ParseCallbackStatus attempts to convert a string to a CallbackStatus
func ParseCallbackStatus(name string) (CallbackStatus, error) {
	switch strings.ToLower(name) {
	case "success":
		return CallbackStatusSuccess, nil
	case "failed":
		return CallbackStatusFailed, nil
	}
	return "", fmt.Errorf("%s is not a valid CallbackStatus", name)
}

// String implements the Stringer interface.
func (x CallbackStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CallbackStatus) IsValid() bool {
	_, err := ParseCallbackStatus(string(x))
	return err == nil
}

// MarshalText implements the text marshaller method
func (x CallbackStatus) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method
func (x *CallbackStatus) UnmarshalText(text []byte) error {
	tmp, err := ParseCallbackStatus(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
}

const (
	// CallbackTypePayment is a CallbackType of type payment.
	CallbackTypePayment CallbackType = "payment"
	// CallbackTypeTest is a CallbackType of type test.
	CallbackTypeTest CallbackType = "test"
)

// CallbackTypeValues returns all values of the enum
func CallbackTypeValues() []CallbackType {
	return []CallbackType{
		CallbackTypePayment,
		CallbackTypeTest,
	}
}

// CallbackTypeStrings returns all values of the enum as strings
func CallbackTypeStrings() []string {
	return []string{
		string(CallbackTypePayment),
		string(CallbackTypeTest),
	}
}

// ParseCallbackType attempts to convert a string to a CallbackType
func ParseCallbackType(name string) (CallbackType, error) {
	switch strings.ToLower(name) {
	case "payment":
		return CallbackTypePayment, nil
	case "test":
		return CallbackTypeTest, nil
	}
	return "", fmt.Errorf("%s is not a valid CallbackType", name)
}

// String implements the Stringer interface.
func (x CallbackType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CallbackType) IsValid() bool {
	_, err := ParseCallbackType(string(x))
	return err == nil
}

// MarshalText implements the text marshaller method
func (x CallbackType) MarshalText() ([]byte, error) {
	return []byte(string(x)), nil
}

// UnmarshalText implements the text unmarshaller method
func (x *CallbackType) UnmarshalText(text []byte) error {
	tmp, err := ParseCallbackType(string(text))
	if err != nil {
		return err
	}
	*x = tmp
	return nil
} 