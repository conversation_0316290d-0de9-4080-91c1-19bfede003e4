//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=token_metadata_mock.go . TokenMetadataRepo
package domain

import "context"

// TokenMetadata represents metadata about a fungible token or NFT. For NFT, decimals is 0 and coingeckoID is empty
type TokenMetadata struct {
	Name          string
	Symbol        string
	Decimals      uint
	CoingeckoID   CoingeckoID
	LogoUrl       string
	IsVerified    bool
	BinanceTicker string
}

// MainTokenMetadata returns the metadata of the main token of a chain
func MainTokenMetadata(chain Chain) *TokenMetadata {
	return &TokenMetadata{
		Name:          chain.MainToken().Name(),
		Symbol:        chain.MainToken().Symbol(),
		Decimals:      chain.MainToken().Decimals(),
		CoingeckoID:   chain.MainCoingeckoID(),
		LogoUrl:       chain.MainToken().LogoUrl(),
		IsVerified:    true,
		BinanceTicker: chain.MainBinanceTicker(),
	}
}

// SymbolToCoingeckoID maps some common token symbol to coingecko ID
var SymbolToCoingeckoID = map[string]CoingeckoID{
	"ETH":    "ethereum",
	"BTC":    "bitcoin",
	"POL":    "matic-network",
	"BNB":    "binancecoin",
	"SOL":    "solana",
	"TRX":    "tron",
	"ARB":    "arbitrum",
	"RON":    "ronin",
	"OAS":    "oasys",
	"KCS":    "kucoin-shares",
	"USDT":   "tether",
	"USDC":   "usd-coin",
	"USDC.e": "usd-coin",
	"DAI":    "dai",
}

type TokenMetadataRepo interface {
	// BatchGetTokenMetadata get multiple token or NFT's metadata by chain and token ID. If any token's metadata can't be found, the result map will not have its key
	BatchGetTokenMetadata(ctx context.Context, tokens []ChainToken) (map[ChainToken]*TokenMetadata, error)

	// UpsertTokenMetadata saves a single token metadata
	UpsertTokenMetadata(ctx context.Context, chain Chain, tokenID string, metadata *TokenMetadata) error
	// BatchUpsertTokenMetadata batch upserts token metadata, only updating non-empty fields
	BatchUpsertTokenMetadata(ctx context.Context, metadatas map[ChainToken]*TokenMetadata) error

	// BatchUpdateTokenMetadata batch updates token metadata, only updating non-empty fields
	BatchUpdateTokenMetadata(ctx context.Context, metadatas map[ChainToken]*TokenMetadata) error

	// IsTokenSpam check if a token is spam. Returns ErrRecordNotFound if token not found
	IsTokenSpam(ctx context.Context, chain Chain, tokenID string) (bool, error)
	// SetTokenSpam set a token as spam or not
	SetTokenSpam(ctx context.Context, chain Chain, tokenID string, isSpam bool) error

	// IsAirdropEventByAddress check if airdrop event exists by contract address
	IsAirdropEventByAddress(ctx context.Context, chain Chain, contractAddress string) bool
}

// WalletTokenMetadata represents metadata about a token owned by a wallet
type WalletTokenMetadata interface{}

// TokenMetadataFetcher can fetch a token's metadata on some chains
type TokenMetadataFetcher interface {
	SupportedChains() []Chain
	FetchMetadata(ctx context.Context, chain Chain, tokenID string) (*TokenMetadata, error)
}

// TokenMetadataUpdater can fetch token metadata from a source
type TokenMetadataUpdater interface {
	FetchMetadatas(ctx context.Context) (map[ChainToken]*TokenMetadata, error)
}
