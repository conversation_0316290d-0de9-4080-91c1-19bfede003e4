package domain_test

import (
	"errors"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
)

func TestUserAddressByPath(t *testing.T) {
	tests := []struct {
		name            string
		user            *domain.UserData
		path            string
		excludeObserver bool
		want            map[string]domain.Address
		wantErr         error
	}{
		{
			name:            "User is nil",
			user:            nil,
			path:            "",
			excludeObserver: false,
			want:            map[string]domain.Address{},
			wantErr:         errors.New("user is nil"),
		},
		{
			name: "Wallets are nil",
			user: &domain.UserData{
				Wallets: nil,
			},
			path:            "",
			excludeObserver: false,
			want:            map[string]domain.Address{},
			wantErr:         errors.New("wallets is nil"),
		},
		{
			name: "Empty path returns all addresses",
			user: &domain.UserData{
				Wallets: &domain.Wallets{
					EvmWallets: []*domain.UserWallet{
						{Address: "******************************************"},
					},
					Wallets: []*domain.UserWallet{
						{Chain: "btc", Address: "btc1"},
					},
					WalletGroups: []*domain.WalletGroup{
						{
							EvmWallets: []*domain.UserWallet{
								{Address: "******************************************"},
							},
							BtcWallets: []*domain.UserWallet{
								{Chain: "btc", Address: "group-btc1"},
							},
							SolanaWallets: []*domain.UserWallet{
								{Chain: "sol", Address: "group-sol1"},
							},
							TronWallets: []*domain.UserWallet{
								{Chain: "tron", Address: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"},
							},
						},
					},
				},
			},
			path:            "",
			excludeObserver: false,
			want: map[string]domain.Address{
				"group-0-btc-0":  domain.NewStrAddress("group-btc1"),
				"group-0-evm-0":  domain.NewEvmAddress("0x1234"),
				"group-0-sol-0":  domain.NewStrAddress("group-sol1"),
				"group-0-tron-0": domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
				"evm-0":          domain.NewEvmAddress("0x5678"),
				"single-0":       domain.NewStrAddress("btc1"),
			},
			wantErr: nil,
		},
		{
			name: "Valid group path",
			user: &domain.UserData{
				Wallets: &domain.Wallets{
					WalletGroups: []*domain.WalletGroup{
						{
							EvmWallets: []*domain.UserWallet{
								{Address: "******************************************"},
							},
						},
					},
				},
			},
			path:            "group-0-evm-0",
			excludeObserver: false,
			want: map[string]domain.Address{
				"group-0-evm-0": domain.NewEvmAddress("0x6666"),
			},
			wantErr: nil,
		},
		{
			name: "Invalid path format",
			user: &domain.UserData{
				Wallets: &domain.Wallets{
					WalletGroups: []*domain.WalletGroup{
						{
							EvmWallets: []*domain.UserWallet{
								{Address: "group-evm1"},
							},
						},
					},
				},
			},
			path:            "invalid-path",
			excludeObserver: false,
			want:            map[string]domain.Address{},
			wantErr:         errors.New("invalid path"),
		},
		{
			name: "Group-1 only retrieves group-1 addresses",
			user: &domain.UserData{
				Wallets: &domain.Wallets{
					WalletGroups: []*domain.WalletGroup{
						{
							EvmWallets: []*domain.UserWallet{
								{Address: "******************************************"},
							},
							BtcWallets: []*domain.UserWallet{
								{Chain: "btc", Address: "group-0-btc1"},
							},
							SolanaWallets: []*domain.UserWallet{
								{Chain: "sol", Address: "group-0-sol1"},
							},
							TronWallets: []*domain.UserWallet{
								{Chain: "tron", Address: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"},
							},
						},
						{
							EvmWallets: []*domain.UserWallet{
								{Address: "******************************************"},
							},
							BtcWallets: []*domain.UserWallet{
								{Chain: "btc", Address: "group-1-btc1"},
							},
							SolanaWallets: []*domain.UserWallet{
								{Chain: "sol", Address: "group-1-sol1"},
							},
							TronWallets: []*domain.UserWallet{
								{Chain: "tron", Address: "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"},
							},
						},
					},
				},
			},
			path:            "group-1",
			excludeObserver: false,
			want: map[string]domain.Address{
				"group-1-btc-0":  domain.NewStrAddress("group-1-btc1"),
				"group-1-evm-0":  domain.NewEvmAddress("0x6666"),
				"group-1-sol-0":  domain.NewStrAddress("group-1-sol1"),
				"group-1-tron-0": domain.NewTronAddress("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"),
			},
			wantErr: nil,
		},
		{
			name: "Evm-2 only retrieves evm-2 addresses",
			user: &domain.UserData{
				Wallets: &domain.Wallets{
					EvmWallets: []*domain.UserWallet{
						{Address: "******************************************"},
						{Address: "******************************************"},
						{Address: "******************************************"},
					},
					WalletGroups: []*domain.WalletGroup{
						{
							EvmWallets: []*domain.UserWallet{
								{Address: "******************************************"},
							},
							BtcWallets: []*domain.UserWallet{
								{Chain: "btc", Address: "group-0-btc1"},
							},
							SolanaWallets: []*domain.UserWallet{
								{Chain: "sol", Address: "group-0-sol1"},
							},
							TronWallets: []*domain.UserWallet{
								{Chain: "tron", Address: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"},
							},
						},
						{
							EvmWallets: []*domain.UserWallet{
								{Address: "******************************************"},
							},
							BtcWallets: []*domain.UserWallet{
								{Chain: "btc", Address: "group-1-btc1"},
							},
							SolanaWallets: []*domain.UserWallet{
								{Chain: "sol", Address: "group-1-sol1"},
							},
							TronWallets: []*domain.UserWallet{
								{Chain: "tron", Address: "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"},
							},
						},
					},
				},
			},
			path:            "evm-2",
			excludeObserver: false,
			want: map[string]domain.Address{
				"evm-2": domain.NewEvmAddress("0x3333"),
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.user.AddressByPath(tt.path, tt.excludeObserver)
			if len(got) != len(tt.want) {
				t.Errorf("AddressByPath() = %v, want %v", got, tt.want)
				return
			}
			for i := range got {
				if got[i] != tt.want[i] {
					t.Errorf("AddressByPath[%s] = %v, want %v", i, got[i], tt.want[i])
				}
			}
		})
	}
}
