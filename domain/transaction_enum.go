// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// TransactionStatusSuccess is a TransactionStatus of type success.
	TransactionStatusSuccess TransactionStatus = "success"
	// TransactionStatusFailed is a TransactionStatus of type failed.
	TransactionStatusFailed TransactionStatus = "failed"
	// TransactionStatusUnknown is a TransactionStatus of type unknown.
	TransactionStatusUnknown TransactionStatus = "unknown"
)

var ErrInvalidTransactionStatus = errors.New("not a valid TransactionStatus")

// String implements the Stringer interface.
func (x TransactionStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x TransactionStatus) IsValid() bool {
	_, err := ParseTransactionStatus(string(x))
	return err == nil
}

var _TransactionStatusValue = map[string]TransactionStatus{
	"success": TransactionStatusSuccess,
	"failed":  TransactionStatusFailed,
	"unknown": TransactionStatusUnknown,
}

// ParseTransactionStatus attempts to convert a string to a TransactionStatus.
func ParseTransactionStatus(name string) (TransactionStatus, error) {
	if x, ok := _TransactionStatusValue[name]; ok {
		return x, nil
	}
	return TransactionStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidTransactionStatus)
}
