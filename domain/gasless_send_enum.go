// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// GaslessSendStatusSuccess is a GaslessSendStatus of type success.
	GaslessSendStatusSuccess GaslessSendStatus = "success"
	// GaslessSendStatusFailed is a GaslessSendStatus of type failed.
	GaslessSendStatusFailed GaslessSendStatus = "failed"
	// GaslessSendStatusProcessing is a GaslessSendStatus of type processing.
	GaslessSendStatusProcessing GaslessSendStatus = "processing"
)

var ErrInvalidGaslessSendStatus = errors.New("not a valid GaslessSendStatus")

// String implements the Stringer interface.
func (x GaslessSendStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x GaslessSendStatus) IsValid() bool {
	_, err := ParseGaslessSendStatus(string(x))
	return err == nil
}

var _GaslessSendStatusValue = map[string]GaslessSendStatus{
	"success":    GaslessSendStatusSuccess,
	"failed":     GaslessSendStatusFailed,
	"processing": GaslessSendStatusProcessing,
}

// ParseGaslessSendStatus attempts to convert a string to a GaslessSendStatus.
func ParseGaslessSendStatus(name string) (GaslessSendStatus, error) {
	if x, ok := _GaslessSendStatusValue[name]; ok {
		return x, nil
	}
	return GaslessSendStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidGaslessSendStatus)
}
