// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// CustomAuthVerifierTypeGoogle is a CustomAuthVerifierType of type google.
	CustomAuthVerifierTypeGoogle CustomAuthVerifierType = "google"
	// CustomAuthVerifierTypeAuth0 is a CustomAuthVerifierType of type auth0.
	CustomAuthVerifierTypeAuth0 CustomAuthVerifierType = "auth0"
	// CustomAuthVerifierTypeJwt is a CustomAuthVerifierType of type jwt.
	CustomAuthVerifierTypeJwt CustomAuthVerifierType = "jwt"
	// CustomAuthVerifierTypeCustomTwm is a CustomAuthVerifierType of type custom_twm.
	CustomAuthVerifierTypeCustomTwm CustomAuthVerifierType = "custom_twm"
)

var ErrInvalidCustomAuthVerifierType = errors.New("not a valid CustomAuthVerifierType")

// String implements the Stringer interface.
func (x CustomAuthVerifierType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x CustomAuthVerifierType) IsValid() bool {
	_, err := ParseCustomAuthVerifierType(string(x))
	return err == nil
}

var _CustomAuthVerifierTypeValue = map[string]CustomAuthVerifierType{
	"google":     CustomAuthVerifierTypeGoogle,
	"auth0":      CustomAuthVerifierTypeAuth0,
	"jwt":        CustomAuthVerifierTypeJwt,
	"custom_twm": CustomAuthVerifierTypeCustomTwm,
}

// ParseCustomAuthVerifierType attempts to convert a string to a CustomAuthVerifierType.
func ParseCustomAuthVerifierType(name string) (CustomAuthVerifierType, error) {
	if x, ok := _CustomAuthVerifierTypeValue[name]; ok {
		return x, nil
	}
	return CustomAuthVerifierType(""), fmt.Errorf("%s is %w", name, ErrInvalidCustomAuthVerifierType)
}

const (
	// LoginMethodEmail is a LoginMethod of type email.
	LoginMethodEmail LoginMethod = "email"
	// LoginMethodPhone is a LoginMethod of type phone.
	LoginMethodPhone LoginMethod = "phone"
	// LoginMethodGoogle is a LoginMethod of type google.
	LoginMethodGoogle LoginMethod = "google"
)

var ErrInvalidLoginMethod = errors.New("not a valid LoginMethod")

// String implements the Stringer interface.
func (x LoginMethod) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x LoginMethod) IsValid() bool {
	_, err := ParseLoginMethod(string(x))
	return err == nil
}

var _LoginMethodValue = map[string]LoginMethod{
	"email":  LoginMethodEmail,
	"phone":  LoginMethodPhone,
	"google": LoginMethodGoogle,
}

// ParseLoginMethod attempts to convert a string to a LoginMethod.
func ParseLoginMethod(name string) (LoginMethod, error) {
	if x, ok := _LoginMethodValue[name]; ok {
		return x, nil
	}
	return LoginMethod(""), fmt.Errorf("%s is %w", name, ErrInvalidLoginMethod)
}

const (
	// OAuthApplicationTypeWebApp is a OAuthApplicationType of type web_app.
	OAuthApplicationTypeWebApp OAuthApplicationType = "web_app"
	// OAuthApplicationTypeMobileWallet is a OAuthApplicationType of type mobile_wallet.
	OAuthApplicationTypeMobileWallet OAuthApplicationType = "mobile_wallet"
	// OAuthApplicationTypeComplyflow is a OAuthApplicationType of type complyflow.
	OAuthApplicationTypeComplyflow OAuthApplicationType = "complyflow"
	// OAuthApplicationTypeLinebot is a OAuthApplicationType of type linebot.
	OAuthApplicationTypeLinebot OAuthApplicationType = "linebot"
	// OAuthApplicationTypeMarket is a OAuthApplicationType of type market.
	OAuthApplicationTypeMarket OAuthApplicationType = "market"
)

var ErrInvalidOAuthApplicationType = errors.New("not a valid OAuthApplicationType")

// String implements the Stringer interface.
func (x OAuthApplicationType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x OAuthApplicationType) IsValid() bool {
	_, err := ParseOAuthApplicationType(string(x))
	return err == nil
}

var _OAuthApplicationTypeValue = map[string]OAuthApplicationType{
	"web_app":       OAuthApplicationTypeWebApp,
	"mobile_wallet": OAuthApplicationTypeMobileWallet,
	"complyflow":    OAuthApplicationTypeComplyflow,
	"linebot":       OAuthApplicationTypeLinebot,
	"market":        OAuthApplicationTypeMarket,
}

// ParseOAuthApplicationType attempts to convert a string to a OAuthApplicationType.
func ParseOAuthApplicationType(name string) (OAuthApplicationType, error) {
	if x, ok := _OAuthApplicationTypeValue[name]; ok {
		return x, nil
	}
	return OAuthApplicationType(""), fmt.Errorf("%s is %w", name, ErrInvalidOAuthApplicationType)
}
