//go:generate go-enum
package domain

import "github.com/samber/lo"

// StudioOrganizationModule studio organization module
type StudioOrganizationModule struct {
	User360       []User360
	WalletBuilder []WalletBuilder
	AssetPro      []AssetPro
	NFTBoost      []NFTBoost
	Compliance    []Compliance
	Admin         []Admin
}

// User360 .
// ENUM(data, engage, audience)
type User360 string

// WalletBuilder .
// ENUM(project, configuration, app_publish, marketing_tools)
type WalletBuilder string

// AssetPro .
// ENUM(treasury, send_token, transaction_history, operators, market, revenue)
type AssetPro string

// NFTBoost .
// ENUM(campaign, reward)
type NFTBoost string

// Compliance .
// ENUM(create_a_task, case_management, all_tasks)
type Compliance string

// Admin .
// ENUM(members, settings, billing)
type Admin string

// IsModuleEnabled check if module is enabled.
func (modules StudioOrganizationModule) IsModuleEnabled(module string) bool {
	switch module {
	case "user_360":
		return len(modules.User360) > 0
	case "wallet_builder":
		return len(modules.WalletBuilder) > 0
	case "asset_pro":
		return len(modules.AssetPro) > 0
	case "nft_boost":
		return len(modules.NFTBoost) > 0
	case "compliance":
		return len(modules.Compliance) > 0
	case "admin":
		return len(modules.Admin) > 0
	}

	return false
}

// FilterByRole filters modules by role.
func (module *StudioOrganizationModule) FilterByRole(roles []StudioRole) {
	var isOwner bool
	mModule := lo.SliceToMap(roles, func(role StudioRole) (string, struct{}) {
		if role.Name == "owner" {
			isOwner = true
		}
		return role.Module, struct{}{}
	})

	// show all modules if user is admin
	if isOwner {
		return
	}

	module.Admin = nil

	if _, ok := mModule["user_360"]; !ok {
		module.User360 = nil
	}
	if _, ok := mModule["wallet_builder"]; !ok {
		module.WalletBuilder = nil
	}
	if _, ok := mModule["asset_pro"]; !ok {
		module.AssetPro = nil
	}
	if _, ok := mModule["nft_boost"]; !ok {
		module.NFTBoost = nil
	}
	if _, ok := mModule["compliance"]; !ok {
		module.Compliance = nil
	}
}
