//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=payment_item_repo_mock.go . PaymentItemRepo

package domain

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/shopspring/decimal"
)

// OrderDataField represents a customizable field that can be collected from users when using a payment item
type OrderDataField struct {
	FieldName    string
	FieldLabel   string
	Required     bool
	FieldType    string
	DefaultValue any
}

// PaymentItem represents a product/item that can be paid for using payment intents
type PaymentItem struct {
	ID              string
	Name            string
	Description     *string
	Price           decimal.Decimal
	Currency        string
	Image           *string
	SuccessURL      *string
	ErrorURL        *string
	CallbackURL     *string
	PayToken        *string
	SuccessMessage  *string
	ChainID         *string
	OrderDataFields []OrderDataField
	Config          map[string]any
	OrganizationID  int
	ClientID        string
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

// PaymentItemCreate contains data needed to create a new payment item
type PaymentItemCreate struct {
	Name            string
	Description     *string
	Price           decimal.Decimal
	Currency        string
	Image           *string
	SuccessURL      *string
	ErrorURL        *string
	CallbackURL     *string
	PayToken        *string
	SuccessMessage  *string
	ChainID         *string
	OrderDataFields []OrderDataField
	Config          map[string]any
	OrganizationID  int
	ClientID        string
}

// PaymentItemUpdate contains data that can be updated on a payment item
type PaymentItemUpdate struct {
	Name            *string
	Description     *string
	Price           *decimal.Decimal
	Currency        *string
	Image           *string
	SuccessURL      *string
	ErrorURL        *string
	CallbackURL     *string
	PayToken        *string
	SuccessMessage  *string
	ChainID         *string
	OrderDataFields *[]OrderDataField
	Config          *map[string]any
}

// GetPaymentItemsParams contains parameters for fetching payment items
type GetPaymentItemsParams struct {
	OrganizationID int
	ClientID       string
	Page           int
	PageSize       int
}

// PaymentItemRepo defines operations for payment items
type PaymentItemRepo interface {
	// Create a new payment item
	CreatePaymentItem(ctx context.Context, item *PaymentItemCreate) (*PaymentItem, *code.KGError)

	// Get a payment item by ID
	GetPaymentItemByID(ctx context.Context, id string) (*PaymentItem, *code.KGError)

	// Get all payment items for an organization with pagination
	GetPaymentItems(ctx context.Context, params GetPaymentItemsParams) ([]*PaymentItem, int, *code.KGError)

	// Update a payment item
	UpdatePaymentItem(ctx context.Context, id string, update *PaymentItemUpdate) (*PaymentItem, *code.KGError)

	// Delete a payment item
	DeletePaymentItem(ctx context.Context, id string) *code.KGError

	// Get a payment item by organization ID, client ID, and name
	GetPaymentItemByOrgClientName(ctx context.Context, organizationID int, clientID, name string) (*PaymentItem, *code.KGError)
}
