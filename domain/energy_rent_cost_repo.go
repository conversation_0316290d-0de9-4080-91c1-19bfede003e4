//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=energy_rent_cost_repo_mock.go . EnergyRentCostRepo
package domain

import "context"

// EnergyRentCostRepo is for estimating Tron energy rent cost
type EnergyRentCostRepo interface {
	GetEnergyRentUnitCost(ctx context.Context) (float64, error)
	SetEnergyRentUnitCost(ctx context.Context, cost float64) error
}
