// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// SourceTxStatusInit is a SourceTxStatus of type init.
	SourceTxStatusInit SourceTxStatus = "init"
	// SourceTxStatusBroadcasted is a SourceTxStatus of type broadcasted.
	SourceTxStatusBroadcasted SourceTxStatus = "broadcasted"
	// SourceTxStatusConfirmed is a SourceTxStatus of type confirmed.
	SourceTxStatusConfirmed SourceTxStatus = "confirmed"
	// SourceTxStatusFailed is a SourceTxStatus of type failed.
	SourceTxStatusFailed SourceTxStatus = "failed"
)

var ErrInvalidSourceTxStatus = errors.New("not a valid SourceTxStatus")

// String implements the Stringer interface.
func (x SourceTxStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x SourceTxStatus) IsValid() bool {
	_, err := ParseSourceTxStatus(string(x))
	return err == nil
}

var _SourceTxStatusValue = map[string]SourceTxStatus{
	"init":        SourceTxStatusInit,
	"broadcasted": SourceTxStatusBroadcasted,
	"confirmed":   SourceTxStatusConfirmed,
	"failed":      SourceTxStatusFailed,
}

// ParseSourceTxStatus attempts to convert a string to a SourceTxStatus.
func ParseSourceTxStatus(name string) (SourceTxStatus, error) {
	if x, ok := _SourceTxStatusValue[name]; ok {
		return x, nil
	}
	return SourceTxStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidSourceTxStatus)
}

const (
	// UniversalSwapStatusPending is a UniversalSwapStatus of type pending.
	UniversalSwapStatusPending UniversalSwapStatus = "pending"
	// UniversalSwapStatusSuccess is a UniversalSwapStatus of type success.
	UniversalSwapStatusSuccess UniversalSwapStatus = "success"
	// UniversalSwapStatusFailed is a UniversalSwapStatus of type failed.
	UniversalSwapStatusFailed UniversalSwapStatus = "failed"
)

var ErrInvalidUniversalSwapStatus = errors.New("not a valid UniversalSwapStatus")

// String implements the Stringer interface.
func (x UniversalSwapStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x UniversalSwapStatus) IsValid() bool {
	_, err := ParseUniversalSwapStatus(string(x))
	return err == nil
}

var _UniversalSwapStatusValue = map[string]UniversalSwapStatus{
	"pending": UniversalSwapStatusPending,
	"success": UniversalSwapStatusSuccess,
	"failed":  UniversalSwapStatusFailed,
}

// ParseUniversalSwapStatus attempts to convert a string to a UniversalSwapStatus.
func ParseUniversalSwapStatus(name string) (UniversalSwapStatus, error) {
	if x, ok := _UniversalSwapStatusValue[name]; ok {
		return x, nil
	}
	return UniversalSwapStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidUniversalSwapStatus)
}
