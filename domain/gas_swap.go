//go:generate go-enum
package domain

import (
	"time"

	"github.com/shopspring/decimal"
)

// GasSwapToken is the supported token for gas swap service
type GasSwapToken struct {
	ChainID         string
	ContractAddress string
}

// GasSwapAsset is user's available asset with balance to do gas swap
type GasSwapAsset struct {
	ChainID       string
	AssetGroup    string
	Name          string
	Symbol        string
	Amount        string
	UsdValue      float64
	Decimals      int
	Price         float64
	Wallets       []*WalletBalance
	LogoUrls      []string
	MinimumAmount float64
}

// WalletBalance is the wallet balance info holding specific asset
type WalletBalance struct {
	Address   string
	AmountStr string
	Amount    float64
	UsdValue  float64
}

// GasSwapOption is the option for gas swap
type GasSwapOption struct {
	Symbol         string
	ImageURL       string
	Amount         string
	ApproveSpender string
}

// GasSwapStatus represents status of gas swap
// ENUM(success, failed, processing)
type GasSwapStatus string

// GasSwap is a gas swap request from user
type GasSwap struct {
	ID                  int
	OrgID               int
	UID                 *string
	ChainID             string
	From                string
	TokenAddress        string
	Amount              string
	EstimatedReceive    float64
	EstimatedReceiveUsd float64
	EstimatedCost       float64
	GasFaucetTxAmount   float64
	GasSwapTxAmount     float64
	ReceiveWallet       string
	SignedTxs           []string
	CreatedAt           time.Time
	EstimatedFinishAt   time.Time
	Status              GasSwapStatus
	ActualReceive       *float64
	ActualCost          *float64
	RentCost            *float64
	GasFaucetTxHash     *string
	UserApproveTxHash   *string
	UserTransferTxHash  *string
	GasSwapTxHash       *string
	RetryCount          int
	ProfitMarginRate    *decimal.Decimal
	ProfitMargin        *decimal.Decimal
	NativeTokenPrice    *float64
	PaidTokenPrice      *float64
	Version             int
}

// GasSwapUpdate is used to update gas swap request's status in repo
type GasSwapUpdate struct {
	ID              int
	Status          *GasSwapStatus
	GasFaucetTxHash *string
	// Approve only used in v1
	UserApproveTxHash *string
	// Transfer only used in v2
	UserTransferTxHash *string
	GasSwapTxHash      *string
	RetryCount         *int
	ActualReceive      *float64
	ActualCost         *float64
	RentCost           *float64
	ProfitMargin       *decimal.Decimal
}
