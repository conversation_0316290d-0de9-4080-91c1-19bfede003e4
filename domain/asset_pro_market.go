package domain

import "github.com/shopspring/decimal"

// StudioUserTransferLimitation defines the transfer limit of a studio user
type StudioUserTransferLimitation struct {
	DailyTransferLimit        decimal.Decimal
	TransferApprovalThreshold decimal.Decimal
}

// UpsertStudioMarketRequest defines the request for upserting studio market.
type UpsertStudioMarketRequest struct {
	OrganizationID       int
	OauthClientID        string
	MarketCode           *string
	MarketURL            *string
	Title                *string
	Logo                 *string
	Email                *string
	Phone                *string
	LineID               *string
	Telegram             *string
	Discord              *string
	Twitter              *string
	Introduction         *string
	PaymentMethod        *PaymentMethod
	PaymentCurrency      *string
	BankName             *string
	BranchName           *string
	BankAccount          *string
	AccountHolderName    *string
	PaymentExpirationSec *int
}

// SaveStudioMarketInfoRequest defines the request for saving studio market info.
type SaveStudioMarketInfoRequest struct {
	OrganizationID       int
	Title                string
	Logo                 string
	Email                *string
	Phone                *string
	LineID               *string
	Telegram             *string
	Discord              *string
	Twitter              *string
	Introduction         *string
	PaymentMethod        PaymentMethod
	PaymentCurrency      string
	BankName             string
	BranchName           string
	BankAccount          string
	AccountHolderName    string
	PaymentExpirationSec int
}

// StudioMarket defines studio market struct.
type StudioMarket struct {
	ID                   int
	OrganizationID       int
	OauthClientID        string
	MarketCode           string
	MarketURL            string
	Title                string
	Logo                 string
	ComplyflowURL        *string
	Email                *string
	Phone                *string
	LineID               *string
	Telegram             *string
	Discord              *string
	Twitter              *string
	Introduction         *string
	PaymentMethod        PaymentMethod
	PaymentCurrency      string
	BankName             *string
	BranchName           *string
	BankAccount          *string
	AccountHolderName    *string
	PaymentExpirationSec int
}

// PaymentMethod defines the payment method used in the studio market.
type PaymentMethod string

// define payment method enum, v1 only support bank_transfer.
const (
	BankTransfer PaymentMethod = "bank_transfer"
)
