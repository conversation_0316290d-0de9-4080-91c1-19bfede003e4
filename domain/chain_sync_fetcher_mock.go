// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: ChainSyncFetcher)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=chain_sync_fetcher_mock.go . ChainSyncFetcher
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockChainSyncFetcher is a mock of ChainSyncFetcher interface.
type MockChainSyncFetcher struct {
	ctrl     *gomock.Controller
	recorder *MockChainSyncFetcherMockRecorder
}

// MockChainSyncFetcherMockRecorder is the mock recorder for MockChainSyncFetcher.
type MockChainSyncFetcherMockRecorder struct {
	mock *MockChainSyncFetcher
}

// NewMockChainSyncFetcher creates a new mock instance.
func NewMockChainSyncFetcher(ctrl *gomock.Controller) *MockChainSyncFetcher {
	mock := &MockChainSyncFetcher{ctrl: ctrl}
	mock.recorder = &MockChainSyncFetcherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChainSyncFetcher) EXPECT() *MockChainSyncFetcherMockRecorder {
	return m.recorder
}

// FetchTxsInRange mocks base method.
func (m *MockChainSyncFetcher) FetchTxsInRange(arg0 context.Context, arg1 Chain, arg2, arg3 uint64) (uint64, []*TransactionWithAddresses, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchTxsInRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].([]*TransactionWithAddresses)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// FetchTxsInRange indicates an expected call of FetchTxsInRange.
func (mr *MockChainSyncFetcherMockRecorder) FetchTxsInRange(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchTxsInRange", reflect.TypeOf((*MockChainSyncFetcher)(nil).FetchTxsInRange), arg0, arg1, arg2, arg3)
}
