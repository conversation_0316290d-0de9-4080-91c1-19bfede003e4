package domain

// Log is event log on chain
type Log struct {
	Address         string
	Data            string
	Topics          []string
	BlockNumber     string
	TransactionHash string
}

// TxReceipt is transaction receipt
type TxReceipt struct {
	TransactionHash         string
	From                    string
	To                      string
	DeployedContractAddress *string
	Logs                    []*Log
	Status                  string
}
