// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// GasSwapStatusSuccess is a GasSwapStatus of type success.
	GasSwapStatusSuccess GasSwapStatus = "success"
	// GasSwapStatusFailed is a GasSwapStatus of type failed.
	GasSwapStatusFailed GasSwapStatus = "failed"
	// GasSwapStatusProcessing is a GasSwapStatus of type processing.
	GasSwapStatusProcessing GasSwapStatus = "processing"
)

var ErrInvalidGasSwapStatus = errors.New("not a valid GasSwapStatus")

// String implements the Stringer interface.
func (x GasSwapStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x GasSwapStatus) IsValid() bool {
	_, err := ParseGasSwapStatus(string(x))
	return err == nil
}

var _GasSwapStatusValue = map[string]GasSwapStatus{
	"success":    GasSwapStatusSuccess,
	"failed":     GasSwapStatusFailed,
	"processing": GasSwapStatusProcessing,
}

// ParseGasSwapStatus attempts to convert a string to a GasSwapStatus.
func ParseGasSwapStatus(name string) (GasSwapStatus, error) {
	if x, ok := _GasSwapStatusValue[name]; ok {
		return x, nil
	}
	return GasSwapStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidGasSwapStatus)
}
