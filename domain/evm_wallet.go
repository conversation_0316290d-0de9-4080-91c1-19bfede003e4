package domain

import (
	"context"
	"crypto/ecdsa"
	"encoding/hex"
	"math/big"

	"github.com/ethereum/go-ethereum/accounts"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
)

// EvmWallet contains the evm wallet private key and address
type EvmWallet struct {
	EncryptedPrivateKey string
	Address             common.Address
	privateKey          *ecdsa.PrivateKey
}

// NewEvmWallet creates a new evm wallet
func NewEvmWallet(encryptedPrivateKey string, privateKey *ecdsa.PrivateKey) *EvmWallet {
	address := crypto.PubkeyToAddress(privateKey.PublicKey)
	return &EvmWallet{
		EncryptedPrivateKey: encryptedPrivateKey,
		Address:             address,
		privateKey:          privateKey,
	}
}

// NewRandomEvmWallet creates a new random evm wallet
func NewRandomEvmWallet(ctx context.Context, e PrivateKeyEncryptor) (*EvmWallet, error) {
	privKey, err := crypto.GenerateKey()
	if err != nil {
		return nil, err
	}
	encryptedPrivateKey, err := e.Encrypt(ctx, privKey)
	if err != nil {
		return nil, err
	}
	return NewEvmWallet(encryptedPrivateKey, privKey), nil
}

// SignTransaction signs a transaction
func (e *EvmWallet) SignTransaction(chainID int, tx *types.Transaction) (*types.Transaction, error) {
	signer := types.NewLondonSigner(big.NewInt(int64(chainID)))
	return types.SignTx(tx, signer, e.privateKey)
}

// SignMessage signs a hex encoded message
func (e *EvmWallet) SignMessage(message []byte) (string, error) {
	hash := accounts.TextHash(message)

	signature, err := crypto.Sign(hash, e.privateKey)
	if err != nil {
		return "", err
	}
	signature[64] += 27
	return hex.EncodeToString(signature), nil
}

// DeriveFromSalt derives a new EVM wallet from a salt deterministically
func (e *EvmWallet) DeriveFromSalt(ctx context.Context, salt [32]byte, enc PrivateKeyEncryptor) (*EvmWallet, error) {
	// Generate deterministic private key from org private key and salt
	seed := append([]byte{}, salt[:]...)
	seed = append(seed, []byte("kryptogo")...)
	seed = append(seed, crypto.FromECDSA(e.privateKey)...)
	hashSeed := crypto.Keccak256(seed)

	// Create private key from hash
	newPrivKey, err := crypto.ToECDSA(hashSeed)
	if err != nil {
		return nil, err
	}

	// Encrypt the private key
	encryptedPrivateKey, err := enc.Encrypt(ctx, newPrivKey)
	if err != nil {
		return nil, err
	}

	return NewEvmWallet(encryptedPrivateKey, newPrivKey), nil
}
