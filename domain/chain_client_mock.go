// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: ChainClient)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=chain_client_mock.go . ChainClient
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	big "math/big"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockChainClient is a mock of ChainClient interface.
type MockChainClient struct {
	ctrl     *gomock.Controller
	recorder *MockChainClientMockRecorder
}

// MockChainClientMockRecorder is the mock recorder for MockChainClient.
type MockChainClientMockRecorder struct {
	mock *MockChainClient
}

// NewMockChainClient creates a new mock instance.
func NewMockChainClient(ctrl *gomock.Controller) *MockChainClient {
	mock := &MockChainClient{ctrl: ctrl}
	mock.recorder = &MockChainClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChainClient) EXPECT() *MockChainClientMockRecorder {
	return m.recorder
}

// BlockNumber mocks base method.
func (m *MockChainClient) BlockNumber(arg0 context.Context) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlockNumber", arg0)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BlockNumber indicates an expected call of BlockNumber.
func (mr *MockChainClientMockRecorder) BlockNumber(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlockNumber", reflect.TypeOf((*MockChainClient)(nil).BlockNumber), arg0)
}

// BroadcastRawTransaction mocks base method.
func (m *MockChainClient) BroadcastRawTransaction(arg0 context.Context, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BroadcastRawTransaction", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BroadcastRawTransaction indicates an expected call of BroadcastRawTransaction.
func (mr *MockChainClientMockRecorder) BroadcastRawTransaction(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BroadcastRawTransaction", reflect.TypeOf((*MockChainClient)(nil).BroadcastRawTransaction), arg0, arg1)
}

// GetTransactionStatus mocks base method.
func (m *MockChainClient) GetTransactionStatus(arg0 context.Context, arg1 string) (TransactionStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(TransactionStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionStatus indicates an expected call of GetTransactionStatus.
func (mr *MockChainClientMockRecorder) GetTransactionStatus(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionStatus", reflect.TypeOf((*MockChainClient)(nil).GetTransactionStatus), arg0, arg1)
}

// NativeBalance mocks base method.
func (m *MockChainClient) NativeBalance(arg0 context.Context, arg1 Address) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NativeBalance", arg0, arg1)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NativeBalance indicates an expected call of NativeBalance.
func (mr *MockChainClientMockRecorder) NativeBalance(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NativeBalance", reflect.TypeOf((*MockChainClient)(nil).NativeBalance), arg0, arg1)
}

// TokenBalance mocks base method.
func (m *MockChainClient) TokenBalance(arg0 context.Context, arg1 Address, arg2 string) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TokenBalance", arg0, arg1, arg2)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TokenBalance indicates an expected call of TokenBalance.
func (mr *MockChainClientMockRecorder) TokenBalance(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TokenBalance", reflect.TypeOf((*MockChainClient)(nil).TokenBalance), arg0, arg1, arg2)
}

// TransactionDetail mocks base method.
func (m *MockChainClient) TransactionDetail(arg0 context.Context, arg1 string) (*TransactionDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TransactionDetail", arg0, arg1)
	ret0, _ := ret[0].(*TransactionDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TransactionDetail indicates an expected call of TransactionDetail.
func (mr *MockChainClientMockRecorder) TransactionDetail(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TransactionDetail", reflect.TypeOf((*MockChainClient)(nil).TransactionDetail), arg0, arg1)
}

// WaitUntilTransactionConfirmed mocks base method.
func (m *MockChainClient) WaitUntilTransactionConfirmed(arg0 context.Context, arg1 string) (TransactionStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitUntilTransactionConfirmed", arg0, arg1)
	ret0, _ := ret[0].(TransactionStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WaitUntilTransactionConfirmed indicates an expected call of WaitUntilTransactionConfirmed.
func (mr *MockChainClientMockRecorder) WaitUntilTransactionConfirmed(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilTransactionConfirmed", reflect.TypeOf((*MockChainClient)(nil).WaitUntilTransactionConfirmed), arg0, arg1)
}
