package domain

import (
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
)

type Chain interface {
	ID() string
	Number() int64 // returns the numeric chain ID.
	IsTestnet() bool
	IsEVM() bool
	IsTVM() bool
	HasRPCUrl() bool
	MainToken() Token
	// DefaultTokens needs to be shown no matter user has them or not
	DefaultTokens() []Token
	MarshalText() ([]byte, error)
	// MainCoingeckoID returns the coingecko ID of the main token
	MainCoingeckoID() CoingeckoID
	// CoingeckoID returns the coingecko platform ID of the chain
	CoingeckoID() string
	// BlockTime returns the average block time of the chain
	BlockTime() time.Duration
	BinanceNetwork() string
	MainBinanceTicker() string
	// AlchemyRpcBase returns the base URL for Alchemy RPC endpoints
	AlchemyRpcBase() string
	// QuicknodeRpcBase returns the base URL for Quicknode RPC endpoints
	QuicknodeRpcBase() string
	// DefaultRpcURL returns the default public RPC URL
	DefaultRpcURL() string
	// AlchemyNetwork returns the network name used by Alchemy
	AlchemyNetwork() string
	// TransactionURL returns the block explorer URL for a transaction
	TransactionURL(hash string) string
	// AddressURL returns the block explorer URL for an address
	AddressURL(address string) string
	// WrappedToken returns the wrapped token address for EVM chains
	WrappedToken() string
}

// commonChain defines common attributes shared across chains.
type commonChain struct {
	id                 string
	number             int64
	isTestnet          bool
	isEVM              bool
	isTVM              bool
	hasRPCUrl          bool
	coingeckoID        string
	mainCoingeckoID    string
	blockTime          time.Duration
	binanceNetwork     string
	mainBinanceTicker  string
	txUrlTemplate      string
	addressUrlTemplate string
	wrappedToken       string
}

// Common methods for all chains.
func (c commonChain) ID() string                   { return c.id }
func (c commonChain) Number() int64                { return c.number }
func (c commonChain) IsTestnet() bool              { return c.isTestnet }
func (c commonChain) IsEVM() bool                  { return c.isEVM }
func (c commonChain) IsTVM() bool                  { return c.isTVM }
func (c commonChain) HasRPCUrl() bool              { return c.hasRPCUrl }
func (c commonChain) MarshalText() ([]byte, error) { return []byte(c.id), nil }
func (c commonChain) String() string               { return c.id }
func (c commonChain) CoingeckoID() string          { return c.coingeckoID }
func (c commonChain) MainCoingeckoID() CoingeckoID { return CoingeckoID(c.mainCoingeckoID) }
func (c commonChain) BlockTime() time.Duration     { return c.blockTime }
func (c commonChain) BinanceNetwork() string       { return c.binanceNetwork }
func (c commonChain) MainBinanceTicker() string    { return c.mainBinanceTicker }
func (c commonChain) AlchemyRpcBase() string       { return "" }
func (c commonChain) QuicknodeRpcBase() string     { return "" }
func (c commonChain) DefaultRpcURL() string        { return "" }
func (c commonChain) AlchemyNetwork() string       { return "" }
func (c commonChain) WrappedToken() string         { return c.wrappedToken }
func (c commonChain) TransactionURL(hash string) string {
	if c.txUrlTemplate == "" {
		return ""
	}
	if c.isTVM && strings.HasPrefix(hash, "0x") {
		hash = hash[2:]
	}
	return fmt.Sprintf(c.txUrlTemplate, hash)
}
func (c commonChain) AddressURL(address string) string {
	if c.addressUrlTemplate == "" {
		return ""
	}
	return fmt.Sprintf(c.addressUrlTemplate, address)
}

var Chains = []Chain{
	Bitcoin,
	Ethereum,
	Polygon,
	BNBChain,
	Solana,
	Tron,
	Kcc,
	Arbitrum,
	Ronin,
	Oasys,
	Optimism,
	BaseChain,
	Hyperliquid,
	Sepolia,
	Holesky,
	Hoodi,
	Shasta,
}

var ChainsMainnet = lo.Filter(Chains, func(chain Chain, _ int) bool {
	return !chain.IsTestnet()
})

var ChainsEVM = lo.Filter(Chains, func(chain Chain, _ int) bool {
	return chain.IsEVM()
})

var ChainsEVMMainnet = lo.Filter(ChainsEVM, func(chain Chain, _ int) bool {
	return !chain.IsTestnet()
})

var chainMap = lo.SliceToMap(Chains, func(chain Chain) (string, Chain) {
	return chain.ID(), chain
})

// IDToChain returns a Chain given its ID.
func IDToChain(id string) Chain {
	return chainMap[id]
}

type bitcoinChain struct{ commonChain }

func (c bitcoinChain) MainToken() Token {
	return NewTokenWithTicker(c, "", "Bitcoin", "BTC", btcLogoUrl, 8, true, "BTC")
}

func (c bitcoinChain) DefaultTokens() []Token {
	return []Token{c.MainToken()}
}

var Bitcoin = bitcoinChain{commonChain: commonChain{
	id:                 "btc",
	number:             0,
	isTestnet:          false,
	isEVM:              false,
	isTVM:              false,
	hasRPCUrl:          false,
	coingeckoID:        "",
	mainCoingeckoID:    "bitcoin",
	blockTime:          10 * time.Minute,
	binanceNetwork:     "BTC",
	mainBinanceTicker:  "BTC",
	txUrlTemplate:      "https://mempool.space/tx/%s",
	addressUrlTemplate: "https://mempool.space/address/%s",
}}

type ethereumChain struct{ commonChain }

func (c ethereumChain) MainToken() Token {
	return NewTokenWithTicker(c, "eth", "Ether", "ETH", ethLogoUrl, 18, true, "ETH")
}

func (c ethereumChain) DefaultTokens() []Token {
	return []Token{
		c.MainToken(),
		NewTokenWithTicker(c, "******************************************", "Tether USD", "USDT", usdtLogoUrl, 6, true, "USDT"),
		NewTokenWithTicker(c, "******************************************", "USD Coin", "USDC", usdcLogoUrl, 6, true, "USDC"),
		NewTokenWithTicker(c, "******************************************", "Dai", "DAI", daiLogoUrl, 18, true, "DAI"),
	}
}

func (c ethereumChain) AlchemyRpcBase() string   { return "https://eth-mainnet.g.alchemy.com/v2" }
func (c ethereumChain) QuicknodeRpcBase() string { return "https://wiser-smart-firefly.quiknode.pro" }
func (c ethereumChain) DefaultRpcURL() string {
	return "https://rpc.ankr.com/eth/03ddcf54ff4c63f702fabf86ff86b155e238ddd2b6cf1248b5678a9732c8aec7"
}
func (c ethereumChain) AlchemyNetwork() string { return "ETH_MAINNET" }

var Ethereum = ethereumChain{commonChain: commonChain{
	id:                 "eth",
	number:             1,
	isTestnet:          false,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "ethereum",
	mainCoingeckoID:    "ethereum",
	blockTime:          12 * time.Second,
	binanceNetwork:     "ETH",
	mainBinanceTicker:  "ETH",
	txUrlTemplate:      "https://etherscan.io/tx/%s",
	addressUrlTemplate: "https://etherscan.io/address/%s",
	wrappedToken:       "******************************************", // WETH
}}

type polygonChain struct{ commonChain }

func (c polygonChain) MainToken() Token {
	return NewTokenWithTicker(c, "matic", "Polygon", "POL", polLogoUrl, 18, true, "POL")
}

func (c polygonChain) DefaultTokens() []Token {
	return []Token{
		c.MainToken(),
		NewTokenWithTicker(c, "******************************************", "Tether USD", "USDT", usdtLogoUrl, 6, true, "USDT"),
		NewTokenWithTicker(c, "******************************************", "USD Coin", "USDC", usdcLogoUrl, 6, true, "USDC"),
		NewTokenWithTicker(c, "******************************************", "Dai", "DAI", daiLogoUrl, 18, true, "DAI"),
	}
}

func (c polygonChain) AlchemyRpcBase() string { return "https://polygon-mainnet.g.alchemy.com/v2" }
func (c polygonChain) QuicknodeRpcBase() string {
	return "https://wiser-smart-firefly.matic.quiknode.pro"
}
func (c polygonChain) DefaultRpcURL() string {
	return "https://rpc.ankr.com/polygon/03ddcf54ff4c63f702fabf86ff86b155e238ddd2b6cf1248b5678a9732c8aec7"
}
func (c polygonChain) AlchemyNetwork() string { return "MATIC_MAINNET" }

var Polygon = polygonChain{commonChain: commonChain{
	id:                 "matic",
	number:             137,
	isTestnet:          false,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "polygon-pos",
	mainCoingeckoID:    "matic-network",
	blockTime:          2 * time.Second,
	binanceNetwork:     "MATIC",
	mainBinanceTicker:  "MATIC",
	txUrlTemplate:      "https://polygonscan.com/tx/%s",
	addressUrlTemplate: "https://polygonscan.com/address/%s",
	wrappedToken:       "******************************************", // WMATIC
}}

type bnbChain struct{ commonChain }

func (c bnbChain) MainToken() Token {
	return NewTokenWithTicker(c, "bsc", "Binance Coin", "BNB", bnbLogoUrl, 18, true, "BNB")
}

func (c bnbChain) DefaultTokens() []Token {
	return []Token{
		c.MainToken(),
		NewTokenWithTicker(c, "******************************************", "Tether USD", "USDT", usdtLogoUrl, 18, true, "USDT"),
		NewTokenWithTicker(c, "******************************************", "USD Coin", "USDC", usdcLogoUrl, 18, true, "USDC"),
		NewTokenWithTicker(c, "******************************************", "Dai", "DAI", daiLogoUrl, 18, true, "DAI"),
	}
}

func (c bnbChain) AlchemyRpcBase() string   { return "https://bnb-mainnet.g.alchemy.com/v2" }
func (c bnbChain) QuicknodeRpcBase() string { return "https://wiser-smart-firefly.bsc.quiknode.pro" }
func (c bnbChain) DefaultRpcURL() string {
	return "https://rpc.ankr.com/bsc/03ddcf54ff4c63f702fabf86ff86b155e238ddd2b6cf1248b5678a9732c8aec7"
}
func (c bnbChain) AlchemyNetwork() string { return "BNB_MAINNET" }

var BNBChain = bnbChain{commonChain: commonChain{
	id:                 "bsc",
	number:             56,
	isTestnet:          false,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "binance-smart-chain",
	mainCoingeckoID:    "binancecoin",
	blockTime:          2 * time.Second,
	binanceNetwork:     "BSC",
	mainBinanceTicker:  "BNB",
	txUrlTemplate:      "https://bscscan.com/tx/%s",
	addressUrlTemplate: "https://bscscan.com/address/%s",
	wrappedToken:       "******************************************", // WBNB
}}

type solanaChain struct{ commonChain }

func (c solanaChain) MainToken() Token {
	return NewTokenWithTicker(c, "", "Solana", "SOL", solLogoUrl, 9, true, "SOL")
}

func (c solanaChain) DefaultTokens() []Token {
	return []Token{
		c.MainToken(),
		NewTokenWithTicker(c, "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "Tether USD", "USDT", usdtLogoUrl, 6, true, "USDT"),
		NewTokenWithTicker(c, "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "USD Coin", "USDC", usdcLogoUrl, 6, true, "USDC"),
	}
}

func (c solanaChain) QuicknodeRpcBase() string {
	return "https://wiser-smart-firefly.solana-mainnet.quiknode.pro"
}

var Solana = solanaChain{commonChain: commonChain{
	id:                 "sol",
	number:             0,
	isTestnet:          false,
	isEVM:              false,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "solana",
	mainCoingeckoID:    "solana",
	blockTime:          1 * time.Second,
	binanceNetwork:     "SOL",
	mainBinanceTicker:  "SOL",
	txUrlTemplate:      "https://solscan.io/tx/%s",
	addressUrlTemplate: "https://solscan.io/account/%s",
}}

type tronChain struct{ commonChain }

func (c tronChain) MainToken() Token {
	return NewTronTokenWithTicker(c, "_", "Tron", "TRX", trxLogoUrl, 6, true, TrcTypeTrc10, "TRX")
}

func (c tronChain) DefaultTokens() []Token {
	return []Token{
		c.MainToken(),
		NewTronTokenWithTicker(c, "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "Tether USD", "USDT", usdtLogoUrl, 6, true, TrcTypeTrc20, "USDT"),
		NewTronTokenWithTicker(c, "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8", "USD Coin", "USDC", usdcLogoUrl, 6, true, TrcTypeTrc20, "USDC"),
	}
}

func (c tronChain) QuicknodeRpcBase() string {
	return "https://wiser-smart-firefly.tron-mainnet.quiknode.pro"
}

var Tron = tronChain{commonChain: commonChain{
	id:                 "tron",
	number:             *********,
	isTestnet:          false,
	isEVM:              false,
	isTVM:              true,
	hasRPCUrl:          true,
	coingeckoID:        "tron",
	mainCoingeckoID:    "tron",
	blockTime:          3 * time.Second,
	binanceNetwork:     "TRX",
	mainBinanceTicker:  "TRX",
	txUrlTemplate:      "https://tronscan.org/#/transaction/%s",
	addressUrlTemplate: "https://tronscan.org/#/address/%s",
}}

type arbitrumChain struct{ commonChain }

func (c arbitrumChain) MainToken() Token {
	return NewTokenWithTicker(c, "eth", "Ether", "ETH", ethLogoUrl, 18, true, "ETH")
}

func (c arbitrumChain) DefaultTokens() []Token {
	return []Token{
		c.MainToken(),
		NewTokenWithTicker(c, "******************************************", "Tether USD", "USDT", usdtLogoUrl, 6, true, "USDT"),
		NewTokenWithTicker(c, "******************************************", "USD Coin", "USDC", usdcLogoUrl, 6, true, "USDC"),
		NewTokenWithTicker(c, "******************************************", "Bridged USDC", "USDC.e", usdcLogoUrl, 6, true, "USDC"),
		NewTokenWithTicker(c, "******************************************", "Dai", "DAI", daiLogoUrl, 18, true, "DAI"),
		NewTokenWithTicker(c, "******************************************", "Arbitrum", "ARB", arbLogoUrl, 18, true, "ARB"),
	}
}

func (c arbitrumChain) AlchemyRpcBase() string { return "https://arb-mainnet.g.alchemy.com/v2" }
func (c arbitrumChain) QuicknodeRpcBase() string {
	return "https://wiser-smart-firefly.arbitrum-mainnet.quiknode.pro"
}
func (c arbitrumChain) DefaultRpcURL() string {
	return "https://rpc.ankr.com/arbitrum/03ddcf54ff4c63f702fabf86ff86b155e238ddd2b6cf1248b5678a9732c8aec7"
}
func (c arbitrumChain) AlchemyNetwork() string { return "ARB_MAINNET" }

var Arbitrum = arbitrumChain{commonChain: commonChain{
	id:                 "arb",
	number:             42161,
	isTestnet:          false,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "arbitrum-one",
	mainCoingeckoID:    "ethereum",
	blockTime:          1 * time.Second,
	binanceNetwork:     "ARBITRUM",
	mainBinanceTicker:  "ETH",
	txUrlTemplate:      "https://arbiscan.io/tx/%s",
	addressUrlTemplate: "https://arbiscan.io/address/%s",
	wrappedToken:       "******************************************", // WETH
}}

type roninChain struct{ commonChain }

func (c roninChain) MainToken() Token {
	return NewTokenWithTicker(c, "ronin", "Ronin Token", "RON", ronLogoUrl, 18, true, "RONIN")
}

func (c roninChain) DefaultTokens() []Token {
	return []Token{c.MainToken()}
}

var Ronin = roninChain{commonChain: commonChain{
	id:                 "ronin",
	number:             2020,
	isTestnet:          false,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          false,
	coingeckoID:        "ronin",
	mainCoingeckoID:    "ronin",
	blockTime:          3 * time.Second,
	binanceNetwork:     "RON",
	mainBinanceTicker:  "RONIN",
	txUrlTemplate:      "https://explorer.roninchain.com/tx/%s",
	addressUrlTemplate: "https://explorer.roninchain.com/address/%s",
}}

type oasysChain struct{ commonChain }

func (c oasysChain) MainToken() Token {
	return NewToken(c, "oasys", "Oasys Token", "OAS", oasLogoUrl, 18, true)
}

func (c oasysChain) DefaultTokens() []Token {
	return []Token{c.MainToken()}
}

var Oasys = oasysChain{commonChain: commonChain{
	id:                 "oasys",
	number:             248,
	isTestnet:          false,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          false,
	coingeckoID:        "oasys",
	mainCoingeckoID:    "oasys",
	blockTime:          3 * time.Second,
	binanceNetwork:     "", // not supported
	mainBinanceTicker:  "", // not supported
	txUrlTemplate:      "https://explorer.oasys.games/tx/%s",
	addressUrlTemplate: "https://explorer.oasys.games/address/%s",
}}

type kccChain struct{ commonChain }

func (c kccChain) MainToken() Token {
	return NewToken(c, "kcc", "KCS", "KCS", kcsLogoUrl, 18, true)
}

func (c kccChain) DefaultTokens() []Token {
	return []Token{c.MainToken()}
}

var Kcc = kccChain{commonChain: commonChain{
	id:                 "kcc",
	number:             321,
	isTestnet:          false,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          false,
	coingeckoID:        "kucoin-community-chain",
	mainCoingeckoID:    "kucoin-shares",
	blockTime:          3 * time.Second,
	binanceNetwork:     "", // not supported
	mainBinanceTicker:  "", // not supported
	txUrlTemplate:      "https://explorer.kcc.io/en/tx/%s",
	addressUrlTemplate: "https://explorer.kcc.io/en/address/%s",
}}

type optimismChain struct{ commonChain }

func (c optimismChain) MainToken() Token {
	return NewTokenWithTicker(c, "eth", "Ether", "ETH", ethLogoUrl, 18, true, "ETH")
}

func (c optimismChain) DefaultTokens() []Token {
	return []Token{
		c.MainToken(),
		NewTokenWithTicker(c, "******************************************", "Bridged Tether USD", "USDT", usdtLogoUrl, 6, true, "USDT"),
		NewTokenWithTicker(c, "******************************************", "Tether USD", "USDT", usdtLogoUrl, 6, true, "USDT"),
		NewTokenWithTicker(c, "******************************************", "USD Coin", "USDC", usdcLogoUrl, 6, true, "USDC"),
		NewTokenWithTicker(c, "******************************************", "Bridged USDC", "USDC.e", usdcLogoUrl, 6, true, "USDC"),
		NewTokenWithTicker(c, "******************************************", "Dai", "DAI", daiLogoUrl, 18, true, "DAI"),
	}
}

func (c optimismChain) AlchemyRpcBase() string {
	return "https://opt-mainnet.g.alchemy.com/v2"
}
func (c optimismChain) QuicknodeRpcBase() string {
	return "https://wiser-smart-firefly.optimism.quiknode.pro"
}
func (c optimismChain) DefaultRpcURL() string {
	return "https://rpc.ankr.com/optimism/03ddcf54ff4c63f702fabf86ff86b155e238ddd2b6cf1248b5678a9732c8aec7"
}
func (c optimismChain) AlchemyNetwork() string { return "OPT_MAINNET" }

var Optimism = optimismChain{commonChain: commonChain{
	id:                 "optimism",
	number:             10,
	isTestnet:          false,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "optimistic-ethereum",
	mainCoingeckoID:    "ethereum",
	blockTime:          2 * time.Second,
	binanceNetwork:     "OPTIMISM",
	mainBinanceTicker:  "OP",
	txUrlTemplate:      "https://optimistic.etherscan.io/tx/%s",
	addressUrlTemplate: "https://optimistic.etherscan.io/address/%s",
	wrappedToken:       "******************************************", // WETH
}}

type baseChain struct{ commonChain }

func (c baseChain) MainToken() Token {
	return NewTokenWithTicker(c, "eth", "Ether", "ETH", ethLogoUrl, 18, true, "ETH")
}

func (c baseChain) DefaultTokens() []Token {
	return []Token{
		c.MainToken(),
		NewTokenWithTicker(c, "******************************************", "USD Coin", "USDC", usdcLogoUrl, 6, true, "USDC"),
	}
}

func (c baseChain) AlchemyRpcBase() string {
	return "https://base-mainnet.g.alchemy.com/v2"
}
func (c baseChain) QuicknodeRpcBase() string {
	return "https://wiser-smart-firefly.base-mainnet.quiknode.pro"
}
func (c baseChain) DefaultRpcURL() string {
	return "https://rpc.ankr.com/base/03ddcf54ff4c63f702fabf86ff86b155e238ddd2b6cf1248b5678a9732c8aec7"
}
func (c baseChain) AlchemyNetwork() string { return "BASE_MAINNET" }

var BaseChain = baseChain{commonChain: commonChain{
	id:                 "base",
	number:             8453,
	isTestnet:          false,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "base",
	mainCoingeckoID:    "ethereum",
	blockTime:          2 * time.Second,
	binanceNetwork:     "BASE",
	mainBinanceTicker:  "ETH",
	txUrlTemplate:      "https://basescan.org/tx/%s",
	addressUrlTemplate: "https://basescan.org/address/%s",
	wrappedToken:       "******************************************", // WETH
}}

type hyperliquidChain struct{ commonChain }

func (c hyperliquidChain) MainToken() Token {
	return NewToken(c, "hype", "HYPE", "HYPE", hypeLogoUrl, 18, true)
}

func (c hyperliquidChain) DefaultTokens() []Token {
	return []Token{c.MainToken()}
}

var Hyperliquid = hyperliquidChain{commonChain: commonChain{
	id:                 "hyperliquid",
	number:             998,
	isTestnet:          false,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "",
	mainCoingeckoID:    "hyperliquid",
	blockTime:          1 * time.Second,
	binanceNetwork:     "",
	mainBinanceTicker:  "",
	txUrlTemplate:      "https://app.hyperliquid.xyz/explorer/tx/%s",
	addressUrlTemplate: "https://app.hyperliquid.xyz/explorer/address/%s",
}}

type sepoliaChain struct{ commonChain }

func (c sepoliaChain) MainToken() Token {
	return NewToken(c, "eth", "Ether", "ETH", ethLogoUrl, 18, true)
}

func (c sepoliaChain) DefaultTokens() []Token {
	return []Token{c.MainToken()}
}

func (c sepoliaChain) AlchemyRpcBase() string { return "https://eth-sepolia.g.alchemy.com/v2" }
func (c sepoliaChain) QuicknodeRpcBase() string {
	return "https://wiser-smart-firefly.ethereum-sepolia.quiknode.pro"
}
func (c sepoliaChain) DefaultRpcURL() string  { return "https://1rpc.io/sepolia" }
func (c sepoliaChain) AlchemyNetwork() string { return "ETH_SEPOLIA" }

var Sepolia = sepoliaChain{commonChain: commonChain{
	id:                 "sepolia",
	number:             11155111,
	isTestnet:          true,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "",
	mainCoingeckoID:    "ethereum",
	blockTime:          1 * time.Second,
	binanceNetwork:     "", // not supported
	mainBinanceTicker:  "", // not supported
	txUrlTemplate:      "https://sepolia.etherscan.io/tx/%s",
	addressUrlTemplate: "https://sepolia.etherscan.io/address/%s",
}}

type holeskyChain struct{ commonChain }

func (c holeskyChain) MainToken() Token {
	return NewToken(c, "eth", "Ether", "ETH", ethLogoUrl, 18, true)
}

func (c holeskyChain) DefaultTokens() []Token {
	return []Token{c.MainToken()}
}

func (c holeskyChain) AlchemyRpcBase() string { return "https://eth-holesky.g.alchemy.com/v2" }
func (c holeskyChain) QuicknodeRpcBase() string {
	return "https://wiser-smart-firefly.ethereum-holesky.quiknode.pro"
}
func (c holeskyChain) DefaultRpcURL() string {
	return "https://rpc.ankr.com/eth_holesky/03ddcf54ff4c63f702fabf86ff86b155e238ddd2b6cf1248b5678a9732c8aec7"
}
func (c holeskyChain) AlchemyNetwork() string { return "ETH_HOLESKY" }

var Holesky = holeskyChain{commonChain: commonChain{
	id:                 "holesky",
	number:             17000,
	isTestnet:          true,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "",
	mainCoingeckoID:    "ethereum",
	blockTime:          1 * time.Second,
	binanceNetwork:     "", // not supported
	mainBinanceTicker:  "", // not supported
	txUrlTemplate:      "https://holesky.etherscan.io/tx/%s",
	addressUrlTemplate: "https://holesky.etherscan.io/address/%s",
}}

type hoodiChain struct{ commonChain }

func (c hoodiChain) MainToken() Token {
	return NewToken(c, "eth", "Ether", "ETH", ethLogoUrl, 18, true)
}

func (c hoodiChain) DefaultTokens() []Token {
	return []Token{c.MainToken()}
}

func (c hoodiChain) DefaultRpcURL() string { return "https://rpc.hoodi.ethpandaops.io" }

var Hoodi = hoodiChain{commonChain: commonChain{
	id:                 "hoodi",
	number:             560048,
	isTestnet:          true,
	isEVM:              true,
	isTVM:              false,
	hasRPCUrl:          true,
	coingeckoID:        "",
	mainCoingeckoID:    "ethereum",
	blockTime:          12 * time.Second,
	binanceNetwork:     "", // not supported
	mainBinanceTicker:  "", // not supported
	txUrlTemplate:      "https://hoodi.etherscan.io/tx/%s",
	addressUrlTemplate: "https://hoodi.etherscan.io/address/%s",
}}

type shastaChain struct{ commonChain }

func (c shastaChain) MainToken() Token {
	return NewTronToken(c, "_", "Tron", "TRX", trxLogoUrl, 6, true, TrcTypeTrc10)
}

func (c shastaChain) DefaultTokens() []Token {
	return []Token{
		c.MainToken(),
		NewTronToken(c, "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs", "Tether USD", "USDT", usdtLogoUrl, 6, true, TrcTypeTrc20),
		NewTronToken(c, "TSdZwNqpHofzP6BsBKGQUWdBeJphLmF6id", "USD Coin", "USDC", usdcLogoUrl, 6, true, TrcTypeTrc20),
	}
}

var Shasta = shastaChain{commonChain: commonChain{
	id:                 "shasta",
	number:             2494104990,
	isTestnet:          true,
	isEVM:              false,
	isTVM:              true,
	hasRPCUrl:          true,
	coingeckoID:        "",
	mainCoingeckoID:    "tron",
	blockTime:          3 * time.Second,
	binanceNetwork:     "", // not supported
	mainBinanceTicker:  "", // not supported
	txUrlTemplate:      "https://shasta.tronscan.org/#/transaction/%s",
	addressUrlTemplate: "https://shasta.tronscan.org/#/address/%s",
}}

type suiChain struct{ commonChain }

func (c suiChain) MainToken() Token {
	return NewToken(c, "sui", "SUI", "SUI", suiLogoUrl, 9, true) // 1 SUI = 1,000,000,000 MIST
}

func (c suiChain) DefaultTokens() []Token {
	return []Token{c.MainToken()}
}

var Sui = suiChain{commonChain: commonChain{
	id:                 "sui",
	number:             -1, // not supported in chainlist
	isTestnet:          false,
	isEVM:              false,
	isTVM:              false,
	hasRPCUrl:          false,
	coingeckoID:        "sui",
	mainCoingeckoID:    "sui",
	blockTime:          400 * time.Millisecond, // 400 - 600 ms
	binanceNetwork:     "",                     // not supported
	mainBinanceTicker:  "",                     // not supported
	txUrlTemplate:      "https://suiscan.xyz/mainnet/tx/%s",
	addressUrlTemplate: "https://suiscan.xyz/mainnet/accounts/%s", // FIXME: there's also modules on SUI
}}

type tonChain struct{ commonChain }

func (c tonChain) MainToken() Token {
	return NewToken(c, "ton", "TON", "TON", tonLogoUrl, 9, true) // 1 TON = 1,000,000,000 nanoTON
}

func (c tonChain) DefaultTokens() []Token {
	return []Token{c.MainToken()}
}

var Ton = tonChain{commonChain: commonChain{
	id:                 "ton",
	number:             -1,
	isTestnet:          false,
	isEVM:              false,
	isTVM:              false,
	hasRPCUrl:          false,
	coingeckoID:        "ton",
	mainCoingeckoID:    "ton",
	blockTime:          4 * time.Second, // 4 - 6 seconds
	binanceNetwork:     "",              // not supported
	mainBinanceTicker:  "",              // not supported
	txUrlTemplate:      "https://tonscan.org/tx/%s",
	addressUrlTemplate: "https://tonscan.org/address/%s",
}}
