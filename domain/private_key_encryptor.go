package domain

import (
	"context"
	"crypto/ecdsa"
	"crypto/ed25519"
)

// PrivateKeyEncryptor is the interface for encrypting and decrypting private key
type PrivateKeyEncryptor interface {
	Encrypt(ctx context.Context, key *ecdsa.PrivateKey) (string, error)
	Decrypt(ctx context.Context, encryptedPrivateKey string) (*ecdsa.Private<PERSON>ey, error)
	EncryptEd25519(ctx context.Context, key ed25519.<PERSON><PERSON><PERSON>) (string, error)
	DecryptEd25519(ctx context.Context, encryptedPrivateKey string) (ed25519.PrivateKey, error)
}
