//go:generate go-enum
package domain

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/paging"
	"github.com/shopspring/decimal"
)

// AssetProPaymentStatus is a type to represent the status of a payment
// ENUM(unpaid, paid, awaiting_refund, refunded)
type AssetProPaymentStatus string

// AssetProOrderStatus is a type to represent the status of an order
// ENUM(unpaid, awaiting_confirmation, awaiting_shipment, shipping, delivered, cancelled)
type AssetProOrderStatus string

// AssetProShippingStatus is a type to represent the shipping status of an order
// ENUM(not_shipped, send_success, send_failed, pending)
type AssetProShippingStatus string

// FromTxLogStatus converts tx log status to asset pro order status.
func (s *AssetProShippingStatus) FromTxLogStatus(status string) {
	switch status {
	case "init", "pending":
		*s = AssetProShippingStatusPending
	case "send_success":
		*s = AssetProShippingStatusSendSuccess
	case "send_failed":
		*s = AssetProShippingStatusSendFailed
	}
}

// ListOrdersReq is a domain model that represents a list orders request
type ListOrdersReq struct {
	CustomerUID *string
	LastSeenID  *string
	IsActive    *bool
}

// AssetProOrderRepo is a repository interface that represents a asset pro order repository
type AssetProOrderRepo interface {
	CountOfPendingOrdersByOrg(ctx context.Context, orgID int) (int64, *code.KGError)
	ListOrders(ctx context.Context, orgID int, req ListOrdersReq, query *paging.Query) ([]*AssetProOrder, *paging.Resp, *code.KGError)
	GetOrderByID(ctx context.Context, orgID int, orderID string) (*AssetProOrder, *code.KGError)
	UpdateOrderByID(ctx context.Context, orgID int, orderID string, params *UpdateOrderParams) *code.KGError
	UpdateOrderByIDForMerchant(ctx context.Context, orgID int, operatorUID, orderID string, params *UpdateOrderParams) *code.KGError
	ValidateAndCreateOrder(ctx context.Context, uid string, params *CreateAssetProOrderParams, auditParams *CreateAssetProOrderAuditLogParams) *code.KGError
	ConfirmPaymentForMerchant(ctx context.Context, organizationID int, orderID string, params *ConfirmPaymentForMerchantParams, auditParams *CreateAssetProOrderAuditLogParams) *code.KGError
	CancelOrderForMerchant(ctx context.Context, organizationID int, orderID string, params *CancelOrderForMerchantParams) *code.KGError
	ConfirmPaymentForCustomer(ctx context.Context, organizationID int, orderID string, params *ConfirmPaymentForCustomerParams, auditParams *CreateAssetProOrderAuditLogParams) *code.KGError
	TransferStatusToShipping(ctx context.Context, organizationID int, orderID string, params *TransferStatusToShippingParams, auditParams *CreateAssetProOrderAuditLogParams) (*AssetProOrder, *code.KGError)
	BackoffShippingToAwaitingShipment(ctx context.Context, organizationID int, orderID string, auditParams *CreateAssetProOrderAuditLogParams) (*AssetProOrder, *code.KGError)
	GetShippingOrders(ctx context.Context, due time.Time) ([]AssetProOrder, *code.KGError)
}

// CreateAssetProOrderParams is a domain model that represents a create asset pro order params
type CreateAssetProOrderParams struct {
	ID                        string
	ProductID                 int
	Name                      string
	WalletAddress             string
	Price                     decimal.Decimal
	Amount                    decimal.Decimal
	TotalCost                 decimal.Decimal
	USDAmount                 decimal.Decimal
	USDTotalCost              decimal.Decimal
	ExchangeRate              decimal.Decimal
	TransferTo                AssetProOrderBankAccount
	FeeType                   AssetProProductFeeType
	ProportionalFeePercentage decimal.Decimal
	ProportionalMinimumFee    decimal.Decimal
	PaymentStatus             AssetProPaymentStatus
	OrderStatus               AssetProOrderStatus
}

// CreateAssetProOrderAuditLogParams is a domain model that represents a create asset pro order audit log params
type CreateAssetProOrderAuditLogParams struct {
	OrderID          string
	OrganizationID   int
	CustomerUID      *string
	OperatorUID      *string
	PaymentStatusOld *AssetProPaymentStatus
	PaymentStatusNew *AssetProPaymentStatus
	OrderStatusOld   *AssetProOrderStatus
	OrderStatusNew   *AssetProOrderStatus
}

// ConfirmPaymentForMerchantParams is a domain model that represents a confirm payment for merchant params
type ConfirmPaymentForMerchantParams struct {
	OperatorUID          string
	CustomerTransferTime int64
	TransferAmount       decimal.Decimal
	QuoteCurrency        string
	LastFiveDigits       string
	Note                 *string
	Attachments          []string
}

// CancelOrderForMerchantParams is a domain model that represents a cancel order for merchant params
type CancelOrderForMerchantParams struct {
	OperatorUID string
}

// TransferStatusToShippingParams is a domain model that represents a transfer status to shipping params
type TransferStatusToShippingParams struct {
	OperatorUID string
}

// AssetProOrderBankAccount is a domain model that represents a transfer to
type AssetProOrderBankAccount struct {
	BankName          *string
	BranchName        *string
	AccountNumber     *string
	AccountHolderName *string
}

// AssetProOrderCustomer is a domain model that represents a customer
type AssetProOrderCustomer struct {
	UID           string
	KycStatus     KycStatus
	Name          *string
	Phone         *string
	Email         *string
	WalletAddress string
	BankAccount   *AssetProOrderBankAccount
}

// AssetProOrderPurchase is a domain model that represents a purchase
type AssetProOrderPurchase struct {
	Amount       decimal.Decimal
	LogoURL      string
	Image        string
	Name         string
	ChainID      string
	BaseCurrency AssetProProductBaseCurrency
	UsdAmount    decimal.Decimal
	Price        decimal.Decimal
}

// AssetProOrderTotalPrice is a domain model that represents a total price
type AssetProOrderTotalPrice struct {
	Amount                decimal.Decimal
	LogoURL               string
	QuoteCurrency         AssetProProductQuoteCurrency
	UsdAmount             decimal.Decimal
	HandlingFee           decimal.Decimal
	HandlingFeePercentage *decimal.Decimal
}

// AssetProOrderOperator is a domain model that represents an operator
type AssetProOrderOperator struct {
	UID        string
	Role       string
	Name       string
	ProfileImg *string
	Email      string
}

// AssetProOrderShipmentDetail is a domain model that represents a shipment detail
type AssetProOrderShipmentDetail struct {
	TxHash      string
	ShippedAt   time.Time
	DeliveredAt *time.Time
	TxID        int
	SendTo      string
	TxStatus    string
	ProcessedBy *AssetProOrderOperator
}

// AssetProOrderPaymentDetailCustomer is a domain model that represents a payment detail customer
type AssetProOrderPaymentDetailCustomer struct {
	Transferred *struct {
		QuoteCurrency AssetProProductQuoteCurrency
		UpdatedAt     *time.Time
		Attachments   []string
	}
}

// AssetProOrderPaymentDetail is a domain model that represents a payment detail
type AssetProOrderPaymentDetail struct {
	Deadline             time.Time
	TransferTo           AssetProOrderBankAccount
	PaymentMethod        string
	Note                 *string
	Attachments          []string
	CustomerTransferTime *time.Time
	LastFiveDigits       *string
	Customer             AssetProOrderPaymentDetailCustomer
	LastEditedAt         *time.Time
	Editor               *AssetProOrderOperator
	Amount               *decimal.Decimal
}

// AssetProOrder is a domain model that represents a asset pro order
type AssetProOrder struct {
	ID             string
	OrganizationID int
	CreatedAt      time.Time
	CancelledAt    *time.Time
	Customer       AssetProOrderCustomer
	Purchase       AssetProOrderPurchase
	TotalPrice     AssetProOrderTotalPrice
	PaymentStatus  AssetProPaymentStatus
	ShipmentDetail *AssetProOrderShipmentDetail
	OrderStatus    AssetProOrderStatus
	PaymentDetail  AssetProOrderPaymentDetail
	ShippingStatus AssetProShippingStatus
	InternalNote   *string
}

// ConfirmPaymentForCustomerParams is a domain model that represents a confirm payment for customer params
type ConfirmPaymentForCustomerParams struct {
	CustomerUID string
	Attachments []string
}

// UpdateOrderParams is a domain model that represents an update order params
type UpdateOrderParams struct {
	OperatorUID       *string
	OperatorUpdatedAt *time.Time
	OrderStatus       *AssetProOrderStatus
	TxID              *int
	TxHash            *string
	InternalNote      *string
	PaymentDetails    UpdateOrderPaymentDetailsParams
}

// UpdateOrderPaymentDetailsParams is a domain model that represents an update order payment details params
type UpdateOrderPaymentDetailsParams struct {
	CustomerTransferTime *int64
	TransferAmount       *decimal.Decimal
	QuoteCurrency        *string
	LastFiveDigits       *string
	Note                 *string
	Attachments          []string
}
