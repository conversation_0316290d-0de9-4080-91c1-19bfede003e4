// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: CallbackExecutor)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=callback_executor_mock.go . CallbackExecutor
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	resty "github.com/go-resty/resty/v2"
	gomock "go.uber.org/mock/gomock"
)

// MockCallbackExecutor is a mock of CallbackExecutor interface.
type MockCallbackExecutor struct {
	ctrl     *gomock.Controller
	recorder *MockCallbackExecutorMockRecorder
}

// MockCallbackExecutorMockRecorder is the mock recorder for MockCallbackExecutor.
type MockCallbackExecutorMockRecorder struct {
	mock *MockCallbackExecutor
}

// NewMockCallbackExecutor creates a new mock instance.
func NewMockCallbackExecutor(ctrl *gomock.Controller) *MockCallbackExecutor {
	mock := &MockCallbackExecutor{ctrl: ctrl}
	mock.recorder = &MockCallbackExecutorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCallbackExecutor) EXPECT() *MockCallbackExecutorMockRecorder {
	return m.recorder
}

// SendRequest mocks base method.
func (m *MockCallbackExecutor) SendRequest(arg0 context.Context, arg1 string, arg2 []byte, arg3 CallbackExecutionContext) (*resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendRequest", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*resty.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendRequest indicates an expected call of SendRequest.
func (mr *MockCallbackExecutorMockRecorder) SendRequest(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRequest", reflect.TypeOf((*MockCallbackExecutor)(nil).SendRequest), arg0, arg1, arg2, arg3)
}

// SendTestCallback mocks base method.
func (m *MockCallbackExecutor) SendTestCallback(arg0 context.Context, arg1 *TestCallbackRequest, arg2 CallbackExecutionContext) (*CallbackLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendTestCallback", arg0, arg1, arg2)
	ret0, _ := ret[0].(*CallbackLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendTestCallback indicates an expected call of SendTestCallback.
func (mr *MockCallbackExecutorMockRecorder) SendTestCallback(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendTestCallback", reflect.TypeOf((*MockCallbackExecutor)(nil).SendTestCallback), arg0, arg1, arg2)
}
