package domain

import (
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/kryptogo/gotron-sdk/pkg/proto/core"
)

// AlertMsgContent represents alert message content
type AlertMsgContent struct {
	KeyName string
	Value   string
}

// AlertSender sends alert message
type AlertSender interface {
	SendAlert(orgName string, contents []AlertMsgContent) error
}

// EvmAlertPolicy represents alert policy for ethereum
type EvmAlertPolicy interface {
	AlertMessage(chainID int, tx *types.Transaction) []AlertMsgContent
}

// TronAlertPolicy represents alert policy for tron
type TronAlertPolicy interface {
	AlertMessage(tx *core.Transaction) []AlertMsgContent
}
