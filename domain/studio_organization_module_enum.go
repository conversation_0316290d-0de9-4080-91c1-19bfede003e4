// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// AdminMembers is a Admin of type members.
	AdminMembers Admin = "members"
	// AdminSettings is a Admin of type settings.
	AdminSettings Admin = "settings"
	// AdminBilling is a Admin of type billing.
	AdminBilling Admin = "billing"
)

var ErrInvalidAdmin = errors.New("not a valid Admin")

// String implements the Stringer interface.
func (x Admin) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x Admin) IsValid() bool {
	_, err := ParseAdmin(string(x))
	return err == nil
}

var _AdminValue = map[string]Admin{
	"members":  AdminMembers,
	"settings": AdminSettings,
	"billing":  AdminBilling,
}

// ParseAdmin attempts to convert a string to a Admin.
func ParseAdmin(name string) (Admin, error) {
	if x, ok := _AdminValue[name]; ok {
		return x, nil
	}
	return Admin(""), fmt.Errorf("%s is %w", name, ErrInvalidAdmin)
}

const (
	// AssetProTreasury is a AssetPro of type treasury.
	AssetProTreasury AssetPro = "treasury"
	// AssetProSendToken is a AssetPro of type send_token.
	AssetProSendToken AssetPro = "send_token"
	// AssetProTransactionHistory is a AssetPro of type transaction_history.
	AssetProTransactionHistory AssetPro = "transaction_history"
	// AssetProOperators is a AssetPro of type operators.
	AssetProOperators AssetPro = "operators"
	// AssetProMarket is a AssetPro of type market.
	AssetProMarket AssetPro = "market"
	// AssetProRevenue is a AssetPro of type revenue.
	AssetProRevenue AssetPro = "revenue"
)

var ErrInvalidAssetPro = errors.New("not a valid AssetPro")

// String implements the Stringer interface.
func (x AssetPro) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetPro) IsValid() bool {
	_, err := ParseAssetPro(string(x))
	return err == nil
}

var _AssetProValue = map[string]AssetPro{
	"treasury":            AssetProTreasury,
	"send_token":          AssetProSendToken,
	"transaction_history": AssetProTransactionHistory,
	"operators":           AssetProOperators,
	"market":              AssetProMarket,
	"revenue":             AssetProRevenue,
}

// ParseAssetPro attempts to convert a string to a AssetPro.
func ParseAssetPro(name string) (AssetPro, error) {
	if x, ok := _AssetProValue[name]; ok {
		return x, nil
	}
	return AssetPro(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetPro)
}

const (
	// ComplianceCreateATask is a Compliance of type create_a_task.
	ComplianceCreateATask Compliance = "create_a_task"
	// ComplianceCaseManagement is a Compliance of type case_management.
	ComplianceCaseManagement Compliance = "case_management"
	// ComplianceAllTasks is a Compliance of type all_tasks.
	ComplianceAllTasks Compliance = "all_tasks"
)

var ErrInvalidCompliance = errors.New("not a valid Compliance")

// String implements the Stringer interface.
func (x Compliance) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x Compliance) IsValid() bool {
	_, err := ParseCompliance(string(x))
	return err == nil
}

var _ComplianceValue = map[string]Compliance{
	"create_a_task":   ComplianceCreateATask,
	"case_management": ComplianceCaseManagement,
	"all_tasks":       ComplianceAllTasks,
}

// ParseCompliance attempts to convert a string to a Compliance.
func ParseCompliance(name string) (Compliance, error) {
	if x, ok := _ComplianceValue[name]; ok {
		return x, nil
	}
	return Compliance(""), fmt.Errorf("%s is %w", name, ErrInvalidCompliance)
}

const (
	// NFTBoostCampaign is a NFTBoost of type campaign.
	NFTBoostCampaign NFTBoost = "campaign"
	// NFTBoostReward is a NFTBoost of type reward.
	NFTBoostReward NFTBoost = "reward"
)

var ErrInvalidNFTBoost = errors.New("not a valid NFTBoost")

// String implements the Stringer interface.
func (x NFTBoost) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x NFTBoost) IsValid() bool {
	_, err := ParseNFTBoost(string(x))
	return err == nil
}

var _NFTBoostValue = map[string]NFTBoost{
	"campaign": NFTBoostCampaign,
	"reward":   NFTBoostReward,
}

// ParseNFTBoost attempts to convert a string to a NFTBoost.
func ParseNFTBoost(name string) (NFTBoost, error) {
	if x, ok := _NFTBoostValue[name]; ok {
		return x, nil
	}
	return NFTBoost(""), fmt.Errorf("%s is %w", name, ErrInvalidNFTBoost)
}

const (
	// User360Data is a User360 of type data.
	User360Data User360 = "data"
	// User360Engage is a User360 of type engage.
	User360Engage User360 = "engage"
	// User360Audience is a User360 of type audience.
	User360Audience User360 = "audience"
)

var ErrInvalidUser360 = errors.New("not a valid User360")

// String implements the Stringer interface.
func (x User360) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x User360) IsValid() bool {
	_, err := ParseUser360(string(x))
	return err == nil
}

var _User360Value = map[string]User360{
	"data":     User360Data,
	"engage":   User360Engage,
	"audience": User360Audience,
}

// ParseUser360 attempts to convert a string to a User360.
func ParseUser360(name string) (User360, error) {
	if x, ok := _User360Value[name]; ok {
		return x, nil
	}
	return User360(""), fmt.Errorf("%s is %w", name, ErrInvalidUser360)
}

const (
	// WalletBuilderProject is a WalletBuilder of type project.
	WalletBuilderProject WalletBuilder = "project"
	// WalletBuilderConfiguration is a WalletBuilder of type configuration.
	WalletBuilderConfiguration WalletBuilder = "configuration"
	// WalletBuilderAppPublish is a WalletBuilder of type app_publish.
	WalletBuilderAppPublish WalletBuilder = "app_publish"
	// WalletBuilderMarketingTools is a WalletBuilder of type marketing_tools.
	WalletBuilderMarketingTools WalletBuilder = "marketing_tools"
)

var ErrInvalidWalletBuilder = errors.New("not a valid WalletBuilder")

// String implements the Stringer interface.
func (x WalletBuilder) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x WalletBuilder) IsValid() bool {
	_, err := ParseWalletBuilder(string(x))
	return err == nil
}

var _WalletBuilderValue = map[string]WalletBuilder{
	"project":         WalletBuilderProject,
	"configuration":   WalletBuilderConfiguration,
	"app_publish":     WalletBuilderAppPublish,
	"marketing_tools": WalletBuilderMarketingTools,
}

// ParseWalletBuilder attempts to convert a string to a WalletBuilder.
func ParseWalletBuilder(name string) (WalletBuilder, error) {
	if x, ok := _WalletBuilderValue[name]; ok {
		return x, nil
	}
	return WalletBuilder(""), fmt.Errorf("%s is %w", name, ErrInvalidWalletBuilder)
}
