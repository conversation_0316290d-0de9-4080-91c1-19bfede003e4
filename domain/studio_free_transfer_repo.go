//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=studio_free_transfer_repo_mock.go . StudioFreeTransferRepo

package domain

import (
	"context"
)

// StudioFreeTransferRepo defines methods for free transfer data persistence
type StudioFreeTransferRepo interface {
	// GetOrgFreeSendCount gets the organization's free send count
	GetOrgFreeSendCount(ctx context.Context, orgID int) (*OrgFreeSendCount, error)

	// UpdateOrgFreeSendCount updates the organization's free send count
	UpdateOrgFreeSendCount(ctx context.Context, orgID int, usedCount int) error
}

// OrgFreeSendCount represents an organization's free send count
type OrgFreeSendCount struct {
	ID             int
	OrganizationID int
	UsedCount      int
}
