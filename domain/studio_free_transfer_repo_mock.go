// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: StudioFreeTransferRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=studio_free_transfer_repo_mock.go . StudioFreeTransferRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockStudioFreeTransferRepo is a mock of StudioFreeTransferRepo interface.
type MockStudioFreeTransferRepo struct {
	ctrl     *gomock.Controller
	recorder *MockStudioFreeTransferRepoMockRecorder
}

// MockStudioFreeTransferRepoMockRecorder is the mock recorder for MockStudioFreeTransferRepo.
type MockStudioFreeTransferRepoMockRecorder struct {
	mock *MockStudioFreeTransferRepo
}

// NewMockStudioFreeTransferRepo creates a new mock instance.
func NewMockStudioFreeTransferRepo(ctrl *gomock.Controller) *MockStudioFreeTransferRepo {
	mock := &MockStudioFreeTransferRepo{ctrl: ctrl}
	mock.recorder = &MockStudioFreeTransferRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStudioFreeTransferRepo) EXPECT() *MockStudioFreeTransferRepoMockRecorder {
	return m.recorder
}

// GetOrgFreeSendCount mocks base method.
func (m *MockStudioFreeTransferRepo) GetOrgFreeSendCount(arg0 context.Context, arg1 int) (*OrgFreeSendCount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrgFreeSendCount", arg0, arg1)
	ret0, _ := ret[0].(*OrgFreeSendCount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrgFreeSendCount indicates an expected call of GetOrgFreeSendCount.
func (mr *MockStudioFreeTransferRepoMockRecorder) GetOrgFreeSendCount(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrgFreeSendCount", reflect.TypeOf((*MockStudioFreeTransferRepo)(nil).GetOrgFreeSendCount), arg0, arg1)
}

// UpdateOrgFreeSendCount mocks base method.
func (m *MockStudioFreeTransferRepo) UpdateOrgFreeSendCount(arg0 context.Context, arg1, arg2 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrgFreeSendCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrgFreeSendCount indicates an expected call of UpdateOrgFreeSendCount.
func (mr *MockStudioFreeTransferRepoMockRecorder) UpdateOrgFreeSendCount(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrgFreeSendCount", reflect.TypeOf((*MockStudioFreeTransferRepo)(nil).UpdateOrgFreeSendCount), arg0, arg1, arg2)
}
