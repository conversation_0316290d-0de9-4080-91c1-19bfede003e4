package domain

// StudioOperator is a struct that represents a studio operator
type StudioOperator struct {
	Name string
	UID  string
}

// StudioUserAPIKey represents a studio user API key
type StudioUserAPIKey struct {
	ID          int
	OrgID       int
	UID         string
	Name        string
	KeyPrefix   string
	KeyHash     string
	Description *string
	LastUsedAt  *int64
	CreatedAt   int64
	DeletedAt   *int64
}
