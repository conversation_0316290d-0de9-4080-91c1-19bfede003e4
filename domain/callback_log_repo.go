//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=callback_log_repo_mock.go . CallbackLogRepo

package domain

import (
	"context"
)

// CallbackLogRepo defines the interface for callback log repository
type CallbackLogRepo interface {
	// CreateCallbackLog creates a new callback log
	CreateCallbackLog(ctx context.Context, log *CallbackLog) (*CallbackLog, error)

	// GetCallbackLogs retrieves callback logs with filtering and pagination
	GetCallbackLogs(ctx context.Context, filter CallbackLogFilter) ([]*CallbackLog, int, error)

	// GetCallbackLogByID retrieves a callback log by its ID
	GetCallbackLogByID(ctx context.Context, id string) (*CallbackLog, error)
}
