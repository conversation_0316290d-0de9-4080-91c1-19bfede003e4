package domain

import "errors"

var (
	// ErrNotCustomAuthApplication is returned when getting/updating a custom auth application, but an oauth application is found
	ErrNotCustomAuthApplication = errors.New("not a custom auth application")
	// ErrVerifierNotSupported is returned when the verifier type is not supported
	ErrVerifierNotSupported = errors.New("verifier type not supported")
	// ErrNotOAuthApplication is returned when getting/updating an oauth application, but a custom auth application is found
	ErrNotOAuthApplication = errors.New("not an oauth application")
)
