package domain

const (
	btcLogoUrl  = "https://logos.covalenthq.com/tokens/56/******************************************.png"
	ethLogoUrl  = "https://token-icons.s3.amazonaws.com/eth.png"
	polLogoUrl  = "https://wallet-static.kryptogo.com/public/assets/images/polygon-matic-logo.webp"
	bnbLogoUrl  = "https://token-icons.s3.amazonaws.com/******************************************.png"
	solLogoUrl  = "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png"
	trxLogoUrl  = "https://static.tronscan.org/production/logo/trx.png"
	kcsLogoUrl  = "https://static.debank.com/image/kcc_token/logo_url/kcc/7fca710b626725fc67f02be57f71c597.png"
	arbLogoUrl  = "https://token-icons.s3.amazonaws.com/a8c975bb-32d0-4922-9952-574528c9ac3f.png"
	ronLogoUrl  = "https://wallet-static.kryptogo.com/public/assets/images/icon_ronin.png"
	oasLogoUrl  = "https://www.datocms-assets.com/86369/**********-oasys-colour.png"
	usdtLogoUrl = "https://token-icons.s3.amazonaws.com/******************************************.png"
	usdcLogoUrl = "https://token-icons.s3.amazonaws.com/******************************************.png"
	daiLogoUrl  = "https://s3.amazonaws.com/token-icons/******************************************.png"
	hypeLogoUrl = "https://wallet-static.kryptogo.com/public/assets/images/icon_hyperliquid.png"
	suiLogoUrl  = "https://assets.coingecko.com/coins/images/26375/standard/sui-ocean-square.png?**********"
	tonLogoUrl  = "https://assets.coingecko.com/coins/images/17980/standard/photo_2024-09-10_17.09.00.jpeg?**********"
)
