// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// WalletTypeNormal is a WalletType of type normal.
	WalletTypeNormal WalletType = "normal"
	// WalletTypeObserver is a WalletType of type observer.
	WalletTypeObserver WalletType = "observer"
)

var ErrInvalidWalletType = errors.New("not a valid WalletType")

// String implements the Stringer interface.
func (x WalletType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x WalletType) IsValid() bool {
	_, err := ParseWalletType(string(x))
	return err == nil
}

var _WalletTypeValue = map[string]WalletType{
	"normal":   WalletTypeNormal,
	"observer": WalletTypeObserver,
}

// ParseWalletType attempts to convert a string to a WalletType.
func ParseWalletType(name string) (WalletType, error) {
	if x, ok := _WalletTypeValue[name]; ok {
		return x, nil
	}
	return WalletType(""), fmt.Errorf("%s is %w", name, ErrInvalidWalletType)
}
