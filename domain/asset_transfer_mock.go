// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: AssetTransferRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=asset_transfer_mock.go . AssetTransferRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"
	time "time"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	paging "github.com/kryptogo/kg-wallet-backend/pkg/db/paging"
	decimal "github.com/shopspring/decimal"
	gomock "go.uber.org/mock/gomock"
)

// MockAssetTransferRepo is a mock of AssetTransferRepo interface.
type MockAssetTransferRepo struct {
	ctrl     *gomock.Controller
	recorder *MockAssetTransferRepoMockRecorder
}

// MockAssetTransferRepoMockRecorder is the mock recorder for MockAssetTransferRepo.
type MockAssetTransferRepoMockRecorder struct {
	mock *MockAssetTransferRepo
}

// NewMockAssetTransferRepo creates a new mock instance.
func NewMockAssetTransferRepo(ctrl *gomock.Controller) *MockAssetTransferRepo {
	mock := &MockAssetTransferRepo{ctrl: ctrl}
	mock.recorder = &MockAssetTransferRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssetTransferRepo) EXPECT() *MockAssetTransferRepoMockRecorder {
	return m.recorder
}

// CreateAssetProTxLog mocks base method.
func (m *MockAssetTransferRepo) CreateAssetProTxLog(arg0 context.Context, arg1 *AssetProTxLog) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAssetProTxLog", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAssetProTxLog indicates an expected call of CreateAssetProTxLog.
func (mr *MockAssetTransferRepoMockRecorder) CreateAssetProTxLog(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAssetProTxLog", reflect.TypeOf((*MockAssetTransferRepo)(nil).CreateAssetProTxLog), arg0, arg1)
}

// GetAssetPrice mocks base method.
func (m *MockAssetTransferRepo) GetAssetPrice(arg0 context.Context, arg1, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockAssetTransferRepoMockRecorder) GetAssetPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockAssetTransferRepo)(nil).GetAssetPrice), arg0, arg1, arg2)
}

// GetAssetProTxLogBySerialID mocks base method.
func (m *MockAssetTransferRepo) GetAssetProTxLogBySerialID(arg0 context.Context, arg1 string) (*AssetProTxLog, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetProTxLogBySerialID", arg0, arg1)
	ret0, _ := ret[0].(*AssetProTxLog)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetAssetProTxLogBySerialID indicates an expected call of GetAssetProTxLogBySerialID.
func (mr *MockAssetTransferRepoMockRecorder) GetAssetProTxLogBySerialID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetProTxLogBySerialID", reflect.TypeOf((*MockAssetTransferRepo)(nil).GetAssetProTxLogBySerialID), arg0, arg1)
}

// GetAssetProTxLogByTxHash mocks base method.
func (m *MockAssetTransferRepo) GetAssetProTxLogByTxHash(arg0 context.Context, arg1 string) (*AssetProTxLog, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetProTxLogByTxHash", arg0, arg1)
	ret0, _ := ret[0].(*AssetProTxLog)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetAssetProTxLogByTxHash indicates an expected call of GetAssetProTxLogByTxHash.
func (mr *MockAssetTransferRepoMockRecorder) GetAssetProTxLogByTxHash(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetProTxLogByTxHash", reflect.TypeOf((*MockAssetTransferRepo)(nil).GetAssetProTxLogByTxHash), arg0, arg1)
}

// GetAssetProTxLogDetail mocks base method.
func (m *MockAssetTransferRepo) GetAssetProTxLogDetail(arg0 context.Context, arg1 int, arg2 string) (*AssetProTxLogDetail, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetProTxLogDetail", arg0, arg1, arg2)
	ret0, _ := ret[0].(*AssetProTxLogDetail)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetAssetProTxLogDetail indicates an expected call of GetAssetProTxLogDetail.
func (mr *MockAssetTransferRepoMockRecorder) GetAssetProTxLogDetail(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetProTxLogDetail", reflect.TypeOf((*MockAssetTransferRepo)(nil).GetAssetProTxLogDetail), arg0, arg1, arg2)
}

// GetAssetProTxLogs mocks base method.
func (m *MockAssetTransferRepo) GetAssetProTxLogs(arg0 context.Context, arg1 *GetAssetProTxLogsParams) ([]AssetProTxLog, *paging.Resp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetProTxLogs", arg0, arg1)
	ret0, _ := ret[0].([]AssetProTxLog)
	ret1, _ := ret[1].(*paging.Resp)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAssetProTxLogs indicates an expected call of GetAssetProTxLogs.
func (mr *MockAssetTransferRepoMockRecorder) GetAssetProTxLogs(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetProTxLogs", reflect.TypeOf((*MockAssetTransferRepo)(nil).GetAssetProTxLogs), arg0, arg1)
}

// GetDailyUsedLimit mocks base method.
func (m *MockAssetTransferRepo) GetDailyUsedLimit(arg0 context.Context, arg1 int, arg2 string, arg3 time.Time) (decimal.Decimal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDailyUsedLimit", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDailyUsedLimit indicates an expected call of GetDailyUsedLimit.
func (mr *MockAssetTransferRepoMockRecorder) GetDailyUsedLimit(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDailyUsedLimit", reflect.TypeOf((*MockAssetTransferRepo)(nil).GetDailyUsedLimit), arg0, arg1, arg2, arg3)
}

// GetPendingHistoryCount mocks base method.
func (m *MockAssetTransferRepo) GetPendingHistoryCount(arg0 context.Context, arg1 AssetProTxLogPendingCountParams) (AssetProTxLogPendingCount, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPendingHistoryCount", arg0, arg1)
	ret0, _ := ret[0].(AssetProTxLogPendingCount)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetPendingHistoryCount indicates an expected call of GetPendingHistoryCount.
func (mr *MockAssetTransferRepoMockRecorder) GetPendingHistoryCount(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingHistoryCount", reflect.TypeOf((*MockAssetTransferRepo)(nil).GetPendingHistoryCount), arg0, arg1)
}

// GetTxLogFilterOptions mocks base method.
func (m *MockAssetTransferRepo) GetTxLogFilterOptions(arg0 context.Context, arg1 int) (*FilterOptions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxLogFilterOptions", arg0, arg1)
	ret0, _ := ret[0].(*FilterOptions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxLogFilterOptions indicates an expected call of GetTxLogFilterOptions.
func (mr *MockAssetTransferRepoMockRecorder) GetTxLogFilterOptions(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxLogFilterOptions", reflect.TypeOf((*MockAssetTransferRepo)(nil).GetTxLogFilterOptions), arg0, arg1)
}

// UpdateAssetProTxLog mocks base method.
func (m *MockAssetTransferRepo) UpdateAssetProTxLog(arg0 context.Context, arg1 *UpdateAssetProTxLogRequest) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAssetProTxLog", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// UpdateAssetProTxLog indicates an expected call of UpdateAssetProTxLog.
func (mr *MockAssetTransferRepoMockRecorder) UpdateAssetProTxLog(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAssetProTxLog", reflect.TypeOf((*MockAssetTransferRepo)(nil).UpdateAssetProTxLog), arg0, arg1)
}
