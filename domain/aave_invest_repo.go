package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// AaveInvestRepo .
type AaveInvestRepo interface {
	GetUserWalletAddresses(ctx context.Context, uid string) ([]string, *code.KGError)
	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *OrganizationWallets, kgErr *code.KGError)
	GetWithdrawnProfit(ctx context.Context, orgID int, chainID, walletAddress, token string) (float64, *code.KGError)
	// CreateFeeRecord will return error if tx hash already exists
	CreateFeeRecord(ctx context.Context, record *AaveInvestFeeRecord) *code.KGError
}
