//go:generate go-enum
package domain

import (
	"math/big"
	"time"

	"github.com/shopspring/decimal"
)

// SendWithRentStatus represents status of send-with-rent
// ENUM(success, failed, processing)
type SendWithRentStatus string

// SendWithRent is a send-with-rent request from user
type SendWithRent struct {
	ID           int
	OrgID        int
	UID          string
	Chain        Chain
	From         TronAddress
	Recipient    TronAddress
	TokenAddress TronAddress
	Amount       *big.Int
	Fee          decimal.Decimal // unit is in TRX
	SignedTxs    []string        // First is token transfer, second is TRX transfer

	// following fields aren't supplied by user
	Status              SendWithRentStatus
	EnergyRentCost      *float64 // unit is in TRX
	ActualCostUsd       *float64
	TokenTransferTxHash *string
	FeeTransferTxHash   *string
	RetryCount          int
	EstimatedFinishAt   time.Time
	CreatedAt           time.Time
}

// UpdateSendWithRentRequest is used to update send-with-rent request's status in repo
type UpdateSendWithRentRequest struct {
	ID                  int
	Status              *SendWithRentStatus
	EnergyRentCost      *float64
	ActualCostUsd       *float64
	TokenTransferTxHash *string
	FeeTransferTxHash   *string
	RetryCount          *int
	ProfitMarginRate    *decimal.Decimal
	ProfitShareRatio    *decimal.Decimal
	ProfitMargin        *decimal.Decimal
}
