//go:generate go-enum
package domain

import (
	"math/big"
	"time"

	"github.com/samber/lo"
)

// Transaction is transaction info. We need this for native token transactions.
type Transaction struct {
	Hash  string
	From  string
	Input string
	To    string
	Value string
}

// InternalTx is internal transaction on chain. We need this for non-native token transactions.
type InternalTx struct {
	From            string
	To              string
	Value           string
	ContractAddress string
}

// ENUM(success, failed, unknown)
type TransactionStatus string

// NativeTokenTransfer is a transfer of native token in transaction. It contains external transaction and internal transaction.
type NativeTokenTransfer struct {
	From   Address
	To     Address
	Amount *big.Int
}

// Compare compares two NativeTokenTransfer objects and returns an integer based on the comparison.
// It returns -1 if n is less than other, 0 if they are equal, and 1 if n is greater than other.
func (n NativeTokenTransfer) Compare(other NativeTokenTransfer) int {
	if n.From.String() != other.From.String() {
		if n.From.String() < other.From.String() {
			return -1
		}
		return 1
	}
	if n.To.String() != other.To.String() {
		if n.To.String() < other.To.String() {
			return -1
		}
		return 1
	}
	return n.Amount.Cmp(other.Amount)
}

// TokenTransfer is a transfer of fungible token in transaction.
type TokenTransfer struct {
	Contract Address
	From     Address
	To       Address
	Amount   *big.Int
}

// Compare compares two TokenTransfer objects and returns an integer based on the comparison.
// It returns -1 if t is less than other, 0 if they are equal, and 1 if t is greater than other.
func (t TokenTransfer) Compare(other TokenTransfer) int {
	if t.Contract.String() != other.Contract.String() {
		if t.Contract.String() < other.Contract.String() {
			return -1
		}
		return 1
	}
	if t.From.String() != other.From.String() {
		if t.From.String() < other.From.String() {
			return -1
		}
		return 1
	}
	if t.To.String() != other.To.String() {
		if t.To.String() < other.To.String() {
			return -1
		}
		return 1
	}
	return t.Amount.Cmp(other.Amount)
}

// NftTransfer is a transfer of non-fungible token in transaction. Only support EVM chains for now. If it's ERC721, amount should be nil. Otherwise it's ERC1155
type NftTransfer struct {
	Contract Address
	TokenID  *big.Int
	From     Address
	To       Address
	Amount   *big.Int
}

// IsERC721 returns true if the NFT transfer is an ERC721 token
func (n *NftTransfer) IsERC721() bool {
	return n.Amount == nil
}

// IsERC1155 returns true if the NFT transfer is an ERC1155 token
func (n *NftTransfer) IsERC1155() bool {
	return n.Amount != nil
}

// TransactionTransfers is a collection of all asset transfers in a transaction (external transaction's native transfer isn't included)
type TransactionTransfers struct {
	InternalTransfers []*NativeTokenTransfer
	TokenTransfers    []*TokenTransfer
	NftTransfers      []*NftTransfer
}

// TransactionDetail contains all details of a transaction
type TransactionDetail struct {
	Chain            Chain
	Hash             string
	BlockNum         uint32
	IsError          bool
	From             Address
	To               Address
	DeployedContract Address
	Value            *big.Int
	Data             string
	GasPrice         *big.Int
	GasUsed          *big.Int
	MethodID         string
	FunctionName     string
	Timestamp        time.Time
	TransactionTransfers
}

func (t *TransactionDetail) GasPriceStr() string {
	if t.GasPrice == nil {
		return ""
	}
	return t.GasPrice.String()
}

func (t *TransactionDetail) GasUsedStr() string {
	if t.GasUsed == nil {
		return ""
	}
	return t.GasUsed.String()
}

func (t *TransactionDetail) IsErrorInt() int32 {
	if t.IsError {
		return 1
	}
	return 0
}

// UniqueAddresses returns unique related addresses in a transaction
func (t *TransactionDetail) UniqueAddresses() []Address {
	addresses := []Address{t.From, t.To}
	for _, transfer := range t.InternalTransfers {
		addresses = append(addresses, transfer.From, transfer.To)
	}
	for _, transfer := range t.TokenTransfers {
		addresses = append(addresses, transfer.From, transfer.To)
	}
	for _, transfer := range t.NftTransfers {
		addresses = append(addresses, transfer.From, transfer.To)
	}
	return lo.Uniq(addresses)
}
