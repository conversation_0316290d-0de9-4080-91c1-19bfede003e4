//go:generate go-enum
package domain

import (
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

// NotificationLinkType represents the type of link for a notification
// ENUM(deep_link, system, dapp)
type NotificationLinkType string

// NotificationMessageType represents the type of message for a notification
// ENUM(transaction, announcement, system)
type NotificationMessageType string

// NotificationContentType represents the type of content for a notification
// ENUM(text, html)
type NotificationContentType string

// LocalizedText represents a string that can be either a single value for all locales or a map of localized strings
type LocalizedText interface {
	GetLocaleString(locale string) string
	ToString() string
}

// LocalizedTextToString converts LocalizedText to string, checking for nil
func LocalizedTextToString(lt LocalizedText) string {
	if lt == nil {
		return ""
	}
	return lt.ToString()
}

// SingleLocaleText represents a string that is the same for all locales
type SingleLocaleText string

func (s SingleLocaleText) GetLocaleString(locale string) string {
	return string(s)
}

func (s SingleLocaleText) ToString() string {
	return string(s)
}

// MultiLocaleText represents a map of localized strings
type MultiLocaleText map[string]string

func (m MultiLocaleText) GetLocaleString(locale string) string {
	if text, ok := m[locale]; ok {
		return text
	}
	return m["default"]
}

func (m MultiLocaleText) ToString() string {
	return util.ToJSONString(m)
}

// Notification represents a notification in the domain model
type Notification struct {
	ID                int32
	Receiver          string
	ContentType       NotificationContentType
	MessageType       NotificationMessageType
	Title             LocalizedText
	Summary           LocalizedText
	Message           LocalizedText
	PrimaryText       LocalizedText
	PrimaryLink       LocalizedText
	PrimaryOpenWith   NotificationLinkType
	SecondaryText     LocalizedText
	SecondaryLink     LocalizedText
	SecondaryOpenWith NotificationLinkType
	PreviewImageURL   LocalizedText
	ClientID          *string
	CreatedAt         time.Time
}
