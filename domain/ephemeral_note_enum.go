// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// EphemeralNoteStatusActive is a EphemeralNoteStatus of type active.
	EphemeralNoteStatusActive EphemeralNoteStatus = "active"
	// EphemeralNoteStatusClaimed is a EphemeralNoteStatus of type claimed.
	EphemeralNoteStatusClaimed EphemeralNoteStatus = "claimed"
	// EphemeralNoteStatusCancelled is a EphemeralNoteStatus of type cancelled.
	EphemeralNoteStatusCancelled EphemeralNoteStatus = "cancelled"
)

var ErrInvalidEphemeralNoteStatus = errors.New("not a valid EphemeralNoteStatus")

// String implements the Stringer interface.
func (x EphemeralNoteStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x EphemeralNoteStatus) IsValid() bool {
	_, err := ParseEphemeralNoteStatus(string(x))
	return err == nil
}

var _EphemeralNoteStatusValue = map[string]EphemeralNoteStatus{
	"active":    EphemeralNoteStatusActive,
	"claimed":   EphemeralNoteStatusClaimed,
	"cancelled": EphemeralNoteStatusCancelled,
}

// ParseEphemeralNoteStatus attempts to convert a string to a EphemeralNoteStatus.
func ParseEphemeralNoteStatus(name string) (EphemeralNoteStatus, error) {
	if x, ok := _EphemeralNoteStatusValue[name]; ok {
		return x, nil
	}
	return EphemeralNoteStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidEphemeralNoteStatus)
}

const (
	// EphemeralOwnerTypeEvm is a EphemeralOwnerType of type evm.
	EphemeralOwnerTypeEvm EphemeralOwnerType = "evm"
	// EphemeralOwnerTypeTron is a EphemeralOwnerType of type tron.
	EphemeralOwnerTypeTron EphemeralOwnerType = "tron"
)

var ErrInvalidEphemeralOwnerType = errors.New("not a valid EphemeralOwnerType")

// String implements the Stringer interface.
func (x EphemeralOwnerType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x EphemeralOwnerType) IsValid() bool {
	_, err := ParseEphemeralOwnerType(string(x))
	return err == nil
}

var _EphemeralOwnerTypeValue = map[string]EphemeralOwnerType{
	"evm":  EphemeralOwnerTypeEvm,
	"tron": EphemeralOwnerTypeTron,
}

// ParseEphemeralOwnerType attempts to convert a string to a EphemeralOwnerType.
func ParseEphemeralOwnerType(name string) (EphemeralOwnerType, error) {
	if x, ok := _EphemeralOwnerTypeValue[name]; ok {
		return x, nil
	}
	return EphemeralOwnerType(""), fmt.Errorf("%s is %w", name, ErrInvalidEphemeralOwnerType)
}
