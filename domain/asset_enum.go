// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// AssetTypeToken is a AssetType of type token.
	AssetTypeToken AssetType = "token"
	// AssetTypeNft is a AssetType of type nft.
	AssetTypeNft AssetType = "nft"
	// AssetTypeDefi is a AssetType of type defi.
	AssetTypeDefi AssetType = "defi"
)

var ErrInvalidAssetType = errors.New("not a valid AssetType")

// String implements the Stringer interface.
func (x AssetType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetType) IsValid() bool {
	_, err := ParseAssetType(string(x))
	return err == nil
}

var _AssetTypeValue = map[string]AssetType{
	"token": AssetTypeToken,
	"nft":   AssetTypeNft,
	"defi":  AssetTypeDefi,
}

// ParseAssetType attempts to convert a string to a AssetType.
func ParseAssetType(name string) (AssetType, error) {
	if x, ok := _AssetTypeValue[name]; ok {
		return x, nil
	}
	return AssetType(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetType)
}

const (
	// NftTypeErc721 is a NftType of type erc721.
	NftTypeErc721 NftType = "erc721"
	// NftTypeErc1155 is a NftType of type erc1155.
	NftTypeErc1155 NftType = "erc1155"
)

var ErrInvalidNftType = errors.New("not a valid NftType")

// String implements the Stringer interface.
func (x NftType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x NftType) IsValid() bool {
	_, err := ParseNftType(string(x))
	return err == nil
}

var _NftTypeValue = map[string]NftType{
	"erc721":  NftTypeErc721,
	"erc1155": NftTypeErc1155,
}

// ParseNftType attempts to convert a string to a NftType.
func ParseNftType(name string) (NftType, error) {
	if x, ok := _NftTypeValue[name]; ok {
		return x, nil
	}
	return NftType(""), fmt.Errorf("%s is %w", name, ErrInvalidNftType)
}
