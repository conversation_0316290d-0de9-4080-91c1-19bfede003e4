// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: PaymentItemRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=payment_item_repo_mock.go . PaymentItemRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockPaymentItemRepo is a mock of PaymentItemRepo interface.
type MockPaymentItemRepo struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentItemRepoMockRecorder
}

// MockPaymentItemRepoMockRecorder is the mock recorder for MockPaymentItemRepo.
type MockPaymentItemRepoMockRecorder struct {
	mock *MockPaymentItemRepo
}

// NewMockPaymentItemRepo creates a new mock instance.
func NewMockPaymentItemRepo(ctrl *gomock.Controller) *MockPaymentItemRepo {
	mock := &MockPaymentItemRepo{ctrl: ctrl}
	mock.recorder = &MockPaymentItemRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentItemRepo) EXPECT() *MockPaymentItemRepoMockRecorder {
	return m.recorder
}

// CreatePaymentItem mocks base method.
func (m *MockPaymentItemRepo) CreatePaymentItem(arg0 context.Context, arg1 *PaymentItemCreate) (*PaymentItem, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePaymentItem", arg0, arg1)
	ret0, _ := ret[0].(*PaymentItem)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// CreatePaymentItem indicates an expected call of CreatePaymentItem.
func (mr *MockPaymentItemRepoMockRecorder) CreatePaymentItem(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentItem", reflect.TypeOf((*MockPaymentItemRepo)(nil).CreatePaymentItem), arg0, arg1)
}

// DeletePaymentItem mocks base method.
func (m *MockPaymentItemRepo) DeletePaymentItem(arg0 context.Context, arg1 string) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePaymentItem", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// DeletePaymentItem indicates an expected call of DeletePaymentItem.
func (mr *MockPaymentItemRepoMockRecorder) DeletePaymentItem(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePaymentItem", reflect.TypeOf((*MockPaymentItemRepo)(nil).DeletePaymentItem), arg0, arg1)
}

// GetPaymentItemByID mocks base method.
func (m *MockPaymentItemRepo) GetPaymentItemByID(arg0 context.Context, arg1 string) (*PaymentItem, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentItemByID", arg0, arg1)
	ret0, _ := ret[0].(*PaymentItem)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetPaymentItemByID indicates an expected call of GetPaymentItemByID.
func (mr *MockPaymentItemRepoMockRecorder) GetPaymentItemByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentItemByID", reflect.TypeOf((*MockPaymentItemRepo)(nil).GetPaymentItemByID), arg0, arg1)
}

// GetPaymentItemByOrgClientName mocks base method.
func (m *MockPaymentItemRepo) GetPaymentItemByOrgClientName(arg0 context.Context, arg1 int, arg2, arg3 string) (*PaymentItem, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentItemByOrgClientName", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*PaymentItem)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetPaymentItemByOrgClientName indicates an expected call of GetPaymentItemByOrgClientName.
func (mr *MockPaymentItemRepoMockRecorder) GetPaymentItemByOrgClientName(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentItemByOrgClientName", reflect.TypeOf((*MockPaymentItemRepo)(nil).GetPaymentItemByOrgClientName), arg0, arg1, arg2, arg3)
}

// GetPaymentItems mocks base method.
func (m *MockPaymentItemRepo) GetPaymentItems(arg0 context.Context, arg1 GetPaymentItemsParams) ([]*PaymentItem, int, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentItems", arg0, arg1)
	ret0, _ := ret[0].([]*PaymentItem)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(*code.KGError)
	return ret0, ret1, ret2
}

// GetPaymentItems indicates an expected call of GetPaymentItems.
func (mr *MockPaymentItemRepoMockRecorder) GetPaymentItems(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentItems", reflect.TypeOf((*MockPaymentItemRepo)(nil).GetPaymentItems), arg0, arg1)
}

// UpdatePaymentItem mocks base method.
func (m *MockPaymentItemRepo) UpdatePaymentItem(arg0 context.Context, arg1 string, arg2 *PaymentItemUpdate) (*PaymentItem, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePaymentItem", arg0, arg1, arg2)
	ret0, _ := ret[0].(*PaymentItem)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// UpdatePaymentItem indicates an expected call of UpdatePaymentItem.
func (mr *MockPaymentItemRepoMockRecorder) UpdatePaymentItem(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePaymentItem", reflect.TypeOf((*MockPaymentItemRepo)(nil).UpdatePaymentItem), arg0, arg1, arg2)
}
