//go:generate go-enum
package domain

import "time"

// SendLinkCampaignUserStatus represents status of send link campaign user
// ENUM(not_started, link_created, link_shared, link_claimed, reward_sent)
type SendLinkCampaignUserStatus string

// SendLinkCampaignUser represents an eligible user in the send link campaign
type Send<PERSON>inkCampaignUser struct {
	UID                   string
	Status                SendLinkCampaignUserStatus
	AnnouncementSent      bool
	SenderRewardTxHash    *string
	RecipientRewardTxHash *string
	RecipientUID          *string
	CreatedAt             time.Time
}

// SendLinkCampaignUserUpdate represents an update in the send link campaign
type SendLinkCampaignUserUpdate struct {
	UID                   string
	Status                *SendLinkCampaignUserStatus
	AnnouncementSent      *bool
	SenderRewardTxHash    *string
	RecipientRewardTxHash *string
	RecipientUID          *string
	Reward                *float64
	RewardSentAt          *time.Time
}
