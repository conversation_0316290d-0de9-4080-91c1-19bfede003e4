// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: GasSwapV2Repo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=gas_swap_v2_repo_mock.go . GasSwapV2Repo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"
	time "time"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockGasSwapV2Repo is a mock of GasSwapV2Repo interface.
type MockGasSwapV2Repo struct {
	ctrl     *gomock.Controller
	recorder *MockGasSwapV2RepoMockRecorder
}

// MockGasSwapV2RepoMockRecorder is the mock recorder for MockGasSwapV2Repo.
type MockGasSwapV2RepoMockRecorder struct {
	mock *MockGasSwapV2Repo
}

// NewMockGasSwapV2Repo creates a new mock instance.
func NewMockGasSwapV2Repo(ctrl *gomock.Controller) *MockGasSwapV2Repo {
	mock := &MockGasSwapV2Repo{ctrl: ctrl}
	mock.recorder = &MockGasSwapV2RepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGasSwapV2Repo) EXPECT() *MockGasSwapV2RepoMockRecorder {
	return m.recorder
}

// AcquireLock mocks base method.
func (m *MockGasSwapV2Repo) AcquireLock(arg0 context.Context, arg1 string, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLock indicates an expected call of AcquireLock.
func (mr *MockGasSwapV2RepoMockRecorder) AcquireLock(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLock", reflect.TypeOf((*MockGasSwapV2Repo)(nil).AcquireLock), arg0, arg1, arg2)
}

// AcquireLockWithRetry mocks base method.
func (m *MockGasSwapV2Repo) AcquireLockWithRetry(arg0 context.Context, arg1 string, arg2, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLockWithRetry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLockWithRetry indicates an expected call of AcquireLockWithRetry.
func (mr *MockGasSwapV2RepoMockRecorder) AcquireLockWithRetry(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLockWithRetry", reflect.TypeOf((*MockGasSwapV2Repo)(nil).AcquireLockWithRetry), arg0, arg1, arg2, arg3)
}

// BatchGetTokenPrices mocks base method.
func (m *MockGasSwapV2Repo) BatchGetTokenPrices(arg0 context.Context, arg1 []ChainToken) (map[ChainToken]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPrices", arg0, arg1)
	ret0, _ := ret[0].(map[ChainToken]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPrices indicates an expected call of BatchGetTokenPrices.
func (mr *MockGasSwapV2RepoMockRecorder) BatchGetTokenPrices(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPrices", reflect.TypeOf((*MockGasSwapV2Repo)(nil).BatchGetTokenPrices), arg0, arg1)
}

// BatchGetTokenPricesIn24H mocks base method.
func (m *MockGasSwapV2Repo) BatchGetTokenPricesIn24H(arg0 context.Context, arg1 []ChainToken) (map[ChainToken][]*PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPricesIn24H", arg0, arg1)
	ret0, _ := ret[0].(map[ChainToken][]*PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPricesIn24H indicates an expected call of BatchGetTokenPricesIn24H.
func (mr *MockGasSwapV2RepoMockRecorder) BatchGetTokenPricesIn24H(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPricesIn24H", reflect.TypeOf((*MockGasSwapV2Repo)(nil).BatchGetTokenPricesIn24H), arg0, arg1)
}

// CreateGasSwap mocks base method.
func (m *MockGasSwapV2Repo) CreateGasSwap(arg0 context.Context, arg1 *GasSwap) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateGasSwap", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateGasSwap indicates an expected call of CreateGasSwap.
func (mr *MockGasSwapV2RepoMockRecorder) CreateGasSwap(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateGasSwap", reflect.TypeOf((*MockGasSwapV2Repo)(nil).CreateGasSwap), arg0, arg1)
}

// GetAllGasSwapSupportedTokens mocks base method.
func (m *MockGasSwapV2Repo) GetAllGasSwapSupportedTokens(arg0 context.Context) ([]*GasSwapToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllGasSwapSupportedTokens", arg0)
	ret0, _ := ret[0].([]*GasSwapToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllGasSwapSupportedTokens indicates an expected call of GetAllGasSwapSupportedTokens.
func (mr *MockGasSwapV2RepoMockRecorder) GetAllGasSwapSupportedTokens(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllGasSwapSupportedTokens", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetAllGasSwapSupportedTokens), arg0)
}

// GetAllLiquidities mocks base method.
func (m *MockGasSwapV2Repo) GetAllLiquidities(arg0 context.Context) ([]*AssetProLiquidity, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLiquidities", arg0)
	ret0, _ := ret[0].([]*AssetProLiquidity)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetAllLiquidities indicates an expected call of GetAllLiquidities.
func (mr *MockGasSwapV2RepoMockRecorder) GetAllLiquidities(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLiquidities", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetAllLiquidities), arg0)
}

// GetAssetPrice mocks base method.
func (m *MockGasSwapV2Repo) GetAssetPrice(arg0 context.Context, arg1, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockGasSwapV2RepoMockRecorder) GetAssetPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetAssetPrice), arg0, arg1, arg2)
}

// GetEnergyRentUnitCost mocks base method.
func (m *MockGasSwapV2Repo) GetEnergyRentUnitCost(arg0 context.Context) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnergyRentUnitCost", arg0)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnergyRentUnitCost indicates an expected call of GetEnergyRentUnitCost.
func (mr *MockGasSwapV2RepoMockRecorder) GetEnergyRentUnitCost(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnergyRentUnitCost", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetEnergyRentUnitCost), arg0)
}

// GetGasSwapAvailableAssets mocks base method.
func (m *MockGasSwapV2Repo) GetGasSwapAvailableAssets(arg0 context.Context, arg1 int, arg2, arg3, arg4 string) ([]*GasSwapAsset, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasSwapAvailableAssets", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*GasSwapAsset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasSwapAvailableAssets indicates an expected call of GetGasSwapAvailableAssets.
func (mr *MockGasSwapV2RepoMockRecorder) GetGasSwapAvailableAssets(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasSwapAvailableAssets", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetGasSwapAvailableAssets), arg0, arg1, arg2, arg3, arg4)
}

// GetGasSwapAvailableChains mocks base method.
func (m *MockGasSwapV2Repo) GetGasSwapAvailableChains(arg0 context.Context, arg1 int) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasSwapAvailableChains", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasSwapAvailableChains indicates an expected call of GetGasSwapAvailableChains.
func (mr *MockGasSwapV2RepoMockRecorder) GetGasSwapAvailableChains(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasSwapAvailableChains", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetGasSwapAvailableChains), arg0, arg1)
}

// GetGasSwapByID mocks base method.
func (m *MockGasSwapV2Repo) GetGasSwapByID(arg0 context.Context, arg1 int) (*GasSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasSwapByID", arg0, arg1)
	ret0, _ := ret[0].(*GasSwap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasSwapByID indicates an expected call of GetGasSwapByID.
func (mr *MockGasSwapV2RepoMockRecorder) GetGasSwapByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasSwapByID", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetGasSwapByID), arg0, arg1)
}

// GetGasSwapOptions mocks base method.
func (m *MockGasSwapV2Repo) GetGasSwapOptions(arg0 context.Context, arg1, arg2 string) ([]*GasSwapOption, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasSwapOptions", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*GasSwapOption)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasSwapOptions indicates an expected call of GetGasSwapOptions.
func (mr *MockGasSwapV2RepoMockRecorder) GetGasSwapOptions(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasSwapOptions", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetGasSwapOptions), arg0, arg1, arg2)
}

// GetGasSwapSupportedTokens mocks base method.
func (m *MockGasSwapV2Repo) GetGasSwapSupportedTokens(arg0 context.Context, arg1 int) ([]*GasSwapToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasSwapSupportedTokens", arg0, arg1)
	ret0, _ := ret[0].([]*GasSwapToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasSwapSupportedTokens indicates an expected call of GetGasSwapSupportedTokens.
func (mr *MockGasSwapV2RepoMockRecorder) GetGasSwapSupportedTokens(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasSwapSupportedTokens", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetGasSwapSupportedTokens), arg0, arg1)
}

// GetLiquidities mocks base method.
func (m *MockGasSwapV2Repo) GetLiquidities(arg0 context.Context, arg1 int) ([]*AssetProLiquidity, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiquidities", arg0, arg1)
	ret0, _ := ret[0].([]*AssetProLiquidity)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetLiquidities indicates an expected call of GetLiquidities.
func (mr *MockGasSwapV2RepoMockRecorder) GetLiquidities(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiquidities", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetLiquidities), arg0, arg1)
}

// GetLiquidity mocks base method.
func (m *MockGasSwapV2Repo) GetLiquidity(arg0 context.Context, arg1 int, arg2 LiquidityType, arg3 string, arg4 *string) (*AssetProLiquidity, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiquidity", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*AssetProLiquidity)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetLiquidity indicates an expected call of GetLiquidity.
func (mr *MockGasSwapV2RepoMockRecorder) GetLiquidity(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiquidity", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetLiquidity), arg0, arg1, arg2, arg3, arg4)
}

// GetNativeAssetPrice mocks base method.
func (m *MockGasSwapV2Repo) GetNativeAssetPrice(arg0 context.Context, arg1 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockGasSwapV2RepoMockRecorder) GetNativeAssetPrice(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetNativeAssetPrice), arg0, arg1)
}

// GetProfitRate mocks base method.
func (m *MockGasSwapV2Repo) GetProfitRate(arg0 context.Context, arg1 int, arg2 ProfitRateServiceType) (*AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRate indicates an expected call of GetProfitRate.
func (mr *MockGasSwapV2RepoMockRecorder) GetProfitRate(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRate", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetProfitRate), arg0, arg1, arg2)
}

// GetProfitRates mocks base method.
func (m *MockGasSwapV2Repo) GetProfitRates(arg0 context.Context, arg1 int) ([]*AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRates", arg0, arg1)
	ret0, _ := ret[0].([]*AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRates indicates an expected call of GetProfitRates.
func (mr *MockGasSwapV2RepoMockRecorder) GetProfitRates(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRates", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetProfitRates), arg0, arg1)
}

// GetTokenPrice mocks base method.
func (m *MockGasSwapV2Repo) GetTokenPrice(arg0 context.Context, arg1 Chain, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPrice indicates an expected call of GetTokenPrice.
func (mr *MockGasSwapV2RepoMockRecorder) GetTokenPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPrice", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetTokenPrice), arg0, arg1, arg2)
}

// GetTokenPricesIn24H mocks base method.
func (m *MockGasSwapV2Repo) GetTokenPricesIn24H(arg0 context.Context, arg1 Chain, arg2 string) ([]*PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPricesIn24H", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPricesIn24H indicates an expected call of GetTokenPricesIn24H.
func (mr *MockGasSwapV2RepoMockRecorder) GetTokenPricesIn24H(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPricesIn24H", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetTokenPricesIn24H), arg0, arg1, arg2)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockGasSwapV2Repo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockGasSwapV2RepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockGasSwapV2Repo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// HasProcessingGasSwap mocks base method.
func (m *MockGasSwapV2Repo) HasProcessingGasSwap(arg0 context.Context, arg1 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasProcessingGasSwap", arg0, arg1)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasProcessingGasSwap indicates an expected call of HasProcessingGasSwap.
func (mr *MockGasSwapV2RepoMockRecorder) HasProcessingGasSwap(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasProcessingGasSwap", reflect.TypeOf((*MockGasSwapV2Repo)(nil).HasProcessingGasSwap), arg0, arg1)
}

// IsGasSwapTokenSupported mocks base method.
func (m *MockGasSwapV2Repo) IsGasSwapTokenSupported(arg0 context.Context, arg1 int, arg2, arg3 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsGasSwapTokenSupported", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsGasSwapTokenSupported indicates an expected call of IsGasSwapTokenSupported.
func (mr *MockGasSwapV2RepoMockRecorder) IsGasSwapTokenSupported(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsGasSwapTokenSupported", reflect.TypeOf((*MockGasSwapV2Repo)(nil).IsGasSwapTokenSupported), arg0, arg1, arg2, arg3)
}

// PendingGasSwapCostSum mocks base method.
func (m *MockGasSwapV2Repo) PendingGasSwapCostSum(arg0 context.Context, arg1 int, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PendingGasSwapCostSum", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PendingGasSwapCostSum indicates an expected call of PendingGasSwapCostSum.
func (mr *MockGasSwapV2RepoMockRecorder) PendingGasSwapCostSum(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PendingGasSwapCostSum", reflect.TypeOf((*MockGasSwapV2Repo)(nil).PendingGasSwapCostSum), arg0, arg1, arg2)
}

// ReleaseLock mocks base method.
func (m *MockGasSwapV2Repo) ReleaseLock(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseLock", arg0, arg1)
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockGasSwapV2RepoMockRecorder) ReleaseLock(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockGasSwapV2Repo)(nil).ReleaseLock), arg0, arg1)
}

// SaveGasSwapSupportedTokens mocks base method.
func (m *MockGasSwapV2Repo) SaveGasSwapSupportedTokens(arg0 context.Context, arg1 int, arg2 []*GasSwapToken) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveGasSwapSupportedTokens", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveGasSwapSupportedTokens indicates an expected call of SaveGasSwapSupportedTokens.
func (mr *MockGasSwapV2RepoMockRecorder) SaveGasSwapSupportedTokens(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveGasSwapSupportedTokens", reflect.TypeOf((*MockGasSwapV2Repo)(nil).SaveGasSwapSupportedTokens), arg0, arg1, arg2)
}

// SetEnergyRentUnitCost mocks base method.
func (m *MockGasSwapV2Repo) SetEnergyRentUnitCost(arg0 context.Context, arg1 float64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEnergyRentUnitCost", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetEnergyRentUnitCost indicates an expected call of SetEnergyRentUnitCost.
func (mr *MockGasSwapV2RepoMockRecorder) SetEnergyRentUnitCost(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEnergyRentUnitCost", reflect.TypeOf((*MockGasSwapV2Repo)(nil).SetEnergyRentUnitCost), arg0, arg1)
}

// UpdateGasSwap mocks base method.
func (m *MockGasSwapV2Repo) UpdateGasSwap(arg0 context.Context, arg1 *GasSwapUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGasSwap", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGasSwap indicates an expected call of UpdateGasSwap.
func (mr *MockGasSwapV2RepoMockRecorder) UpdateGasSwap(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGasSwap", reflect.TypeOf((*MockGasSwapV2Repo)(nil).UpdateGasSwap), arg0, arg1)
}

// UpsertProfitRate mocks base method.
func (m *MockGasSwapV2Repo) UpsertProfitRate(arg0 context.Context, arg1 UpsertProfitRateParams) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertProfitRate", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// UpsertProfitRate indicates an expected call of UpsertProfitRate.
func (mr *MockGasSwapV2RepoMockRecorder) UpsertProfitRate(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertProfitRate", reflect.TypeOf((*MockGasSwapV2Repo)(nil).UpsertProfitRate), arg0, arg1)
}
