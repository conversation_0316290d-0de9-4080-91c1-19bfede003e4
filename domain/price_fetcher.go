//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=price_fetcher_mock.go . PriceFetcher
package domain

import (
	"context"
)

type PriceFetcher interface {
	GetPrices(ctx context.Context, tokens []CoingeckoID) (map[CoingeckoID]Price, error)
	GetPricesByContract(ctx context.Context, tokens []ChainToken) (map[ChainToken]Price, error)
	PricesByContractSupportedChains() []Chain
}
