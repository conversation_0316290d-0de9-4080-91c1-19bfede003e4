// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: AssetFetcher,TokenAmountsFetcher)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=asset_fetcher_mock.go . AssetFetcher,TokenAmountsFetcher
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	decimal "github.com/shopspring/decimal"
	gomock "go.uber.org/mock/gomock"
)

// MockAssetFetcher is a mock of AssetFetcher interface.
type MockAssetFetcher struct {
	ctrl     *gomock.Controller
	recorder *MockAssetFetcherMockRecorder
}

// MockAssetFetcherMockRecorder is the mock recorder for MockAssetFetcher.
type MockAssetFetcherMockRecorder struct {
	mock *MockAssetFetcher
}

// NewMockAssetFetcher creates a new mock instance.
func NewMockAssetFetcher(ctrl *gomock.Controller) *MockAssetFetcher {
	mock := &MockAssetFetcher{ctrl: ctrl}
	mock.recorder = &MockAssetFetcherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssetFetcher) EXPECT() *MockAssetFetcherMockRecorder {
	return m.recorder
}

// GetAssets mocks base method.
func (m *MockAssetFetcher) GetAssets(arg0 context.Context, arg1 Address, arg2 []Chain, arg3 []AssetType) (*AggregatedAssets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssets", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*AggregatedAssets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssets indicates an expected call of GetAssets.
func (mr *MockAssetFetcherMockRecorder) GetAssets(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssets", reflect.TypeOf((*MockAssetFetcher)(nil).GetAssets), arg0, arg1, arg2, arg3)
}

// SupportedChains mocks base method.
func (m *MockAssetFetcher) SupportedChains() []Chain {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedChains")
	ret0, _ := ret[0].([]Chain)
	return ret0
}

// SupportedChains indicates an expected call of SupportedChains.
func (mr *MockAssetFetcherMockRecorder) SupportedChains() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedChains", reflect.TypeOf((*MockAssetFetcher)(nil).SupportedChains))
}

// SupportedTypes mocks base method.
func (m *MockAssetFetcher) SupportedTypes() []AssetType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedTypes")
	ret0, _ := ret[0].([]AssetType)
	return ret0
}

// SupportedTypes indicates an expected call of SupportedTypes.
func (mr *MockAssetFetcherMockRecorder) SupportedTypes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedTypes", reflect.TypeOf((*MockAssetFetcher)(nil).SupportedTypes))
}

// MockTokenAmountsFetcher is a mock of TokenAmountsFetcher interface.
type MockTokenAmountsFetcher struct {
	ctrl     *gomock.Controller
	recorder *MockTokenAmountsFetcherMockRecorder
}

// MockTokenAmountsFetcherMockRecorder is the mock recorder for MockTokenAmountsFetcher.
type MockTokenAmountsFetcherMockRecorder struct {
	mock *MockTokenAmountsFetcher
}

// NewMockTokenAmountsFetcher creates a new mock instance.
func NewMockTokenAmountsFetcher(ctrl *gomock.Controller) *MockTokenAmountsFetcher {
	mock := &MockTokenAmountsFetcher{ctrl: ctrl}
	mock.recorder = &MockTokenAmountsFetcherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTokenAmountsFetcher) EXPECT() *MockTokenAmountsFetcherMockRecorder {
	return m.recorder
}

// FetchAmounts mocks base method.
func (m *MockTokenAmountsFetcher) FetchAmounts(arg0 context.Context, arg1 Chain, arg2 []*WalletToken) (map[Address]map[string]decimal.Decimal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchAmounts", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[Address]map[string]decimal.Decimal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchAmounts indicates an expected call of FetchAmounts.
func (mr *MockTokenAmountsFetcherMockRecorder) FetchAmounts(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchAmounts", reflect.TypeOf((*MockTokenAmountsFetcher)(nil).FetchAmounts), arg0, arg1, arg2)
}

// SupportedChains mocks base method.
func (m *MockTokenAmountsFetcher) SupportedChains() []Chain {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedChains")
	ret0, _ := ret[0].([]Chain)
	return ret0
}

// SupportedChains indicates an expected call of SupportedChains.
func (mr *MockTokenAmountsFetcherMockRecorder) SupportedChains() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedChains", reflect.TypeOf((*MockTokenAmountsFetcher)(nil).SupportedChains))
}
