package domain

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// BridgeOrganizationService defines the service interface for bridge organization operations
type BridgeOrganizationService interface {
	CreateBridgeOrganization(ctx context.Context, req *CreateBridgeOrganizationRequest) (*CreateBridgeOrganizationResponse, *code.KGError)
}

// BridgeOrganizationRepo defines the repository interface for bridge organization data persistence
type BridgeOrganizationRepo interface {
	CreateBridgeOrganization(ctx context.Context, bridgeOrg *CreateBridgeOrganizationData) error
	GetBridgeOrganizationByOrgID(ctx context.Context, organizationID int) (*BridgeOrganizationData, error)
}

// CreateBridgeOrganizationRequest represents the request to create a bridge organization
type CreateBridgeOrganizationRequest struct {
	OrganizationID int    `json:"-"` // Will be set from context
	Email          string `json:"email" binding:"required"`
	FullName       string `json:"full_name" binding:"required"`
}

// CreateBridgeOrganizationResponse represents the response after creating a bridge organization
type CreateBridgeOrganizationResponse struct {
	OrganizationID   int                 `json:"organization_id"`
	CustomerID       string              `json:"customer_id"`
	FullName         string              `json:"full_name"`
	Email            string              `json:"email"`
	Type             string              `json:"type"`
	KYCLink          *string             `json:"kyc_link"`
	TOSLink          *string             `json:"tos_link"`
	KYCStatus        string              `json:"kyc_status"`
	TOSStatus        string              `json:"tos_status"`
	RejectionReasons []string            `json:"rejection_reasons"`
	CreatedAt        time.Time           `json:"created_at"`
}

// CreateBridgeOrganizationData represents the data to be stored in database
type CreateBridgeOrganizationData struct {
	OrganizationID   int      `json:"organization_id"`
	CustomerID       string   `json:"customer_id"`
	FullName         string   `json:"full_name"`
	Email            string   `json:"email"`
	Type             string   `json:"type"`
	KYCLink          *string  `json:"kyc_link"`
	TOSLink          *string  `json:"tos_link"`
	KYCStatus        string   `json:"kyc_status"`
	TOSStatus        string   `json:"tos_status"`
	RejectionReasons []string `json:"rejection_reasons"`
}

// BridgeOrganizationData represents bridge organization data from database
type BridgeOrganizationData struct {
	OrganizationID   int       `json:"organization_id"`
	CustomerID       string    `json:"customer_id"`
	FullName         string    `json:"full_name"`
	Email            string    `json:"email"`
	Type             string    `json:"type"`
	KYCLink          *string   `json:"kyc_link"`
	TOSLink          *string   `json:"tos_link"`
	KYCStatus        string    `json:"kyc_status"`
	TOSStatus        string    `json:"tos_status"`
	RejectionReasons []string  `json:"rejection_reasons"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// BridgeAPIClient defines the interface for interacting with Bridge API
type BridgeAPIClient interface {
	CreateKYCLink(ctx context.Context, req *BridgeCreateKYCLinkRequest) (*BridgeCreateKYCLinkResponse, error)
	CreateExternalAccount(ctx context.Context, customerID string, req *BridgeCreateExternalAccountRequest) (*BridgeCreateExternalAccountResponse, error)
	CreateTransfer(ctx context.Context, req *BridgeCreateTransferRequest) (*BridgeCreateTransferResponse, error)
}

// BridgeCreateKYCLinkRequest represents the request to Bridge API for creating KYC link
type BridgeCreateKYCLinkRequest struct {
	FullName string `json:"full_name"`
	Email    string `json:"email"`
	Type     string `json:"type"`
}

// BridgeCreateKYCLinkResponse represents the response from Bridge API
type BridgeCreateKYCLinkResponse struct {
	ID               string   `json:"id"`
	FullName         string   `json:"full_name"`
	Email            string   `json:"email"`
	Type             string   `json:"type"`
	KYCLink          string   `json:"kyc_link"`
	TOSLink          string   `json:"tos_link"`
	KYCStatus        string   `json:"kyc_status"`
	RejectionReasons []string `json:"rejection_reasons"`
	TOSStatus        string   `json:"tos_status"`
	CreatedAt        string   `json:"created_at"`
	CustomerID       string   `json:"customer_id"`
} 