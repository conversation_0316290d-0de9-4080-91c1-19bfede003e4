package domain

import (
	"context"
	"fmt"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// BridgeExternalAccountService defines the service interface for bridge external account operations
type BridgeExternalAccountService interface {
	CreateBridgeExternalAccount(ctx context.Context, req *CreateBridgeExternalAccountRequest) (*CreateBridgeExternalAccountResponse, *code.KGError)
	GetBridgeExternalAccountsByOrgID(ctx context.Context, organizationID int) ([]*BridgeExternalAccountData, *code.KGError)
	GetBridgeExternalAccountByID(ctx context.Context, externalAccountID string) (*BridgeExternalAccountData, *code.KGError)
}

// BridgeExternalAccountRepo defines the repository interface for bridge external account data persistence
type BridgeExternalAccountRepo interface {
	CreateBridgeExternalAccount(ctx context.Context, externalAccount *CreateBridgeExternalAccountData) error
	GetBridgeExternalAccountsByOrgID(ctx context.Context, organizationID int) ([]*BridgeExternalAccountData, error)
	GetBridgeExternalAccountByID(ctx context.Context, externalAccountID string) (*BridgeExternalAccountData, error)
}

// CreateBridgeExternalAccountRequest represents the request to create a bridge external account
type CreateBridgeExternalAccountRequest struct {
	OrganizationID      int                     `json:"-"` // Will be set from context
	Currency            string                  `json:"currency" binding:"required"`
	BankName            string                  `json:"bank_name" binding:"required"`
	AccountOwnerName    string                  `json:"account_owner_name" binding:"required"`
	AccountOwnerType    string                  `json:"account_owner_type" binding:"required,oneof=individual business"`
	AccountType         string                  `json:"account_type" binding:"required,oneof=us iban clabe unknown"`
	BusinessName        *string                 `json:"business_name,omitempty"`
	Swift               *SwiftDetails           `json:"swift,omitempty"`
	US                  *USDetails              `json:"us,omitempty"`
	Address             *AddressDetails         `json:"address,omitempty"`
}

// ValidateAccountTypeSpecificFields validates fields based on account type
func (req *CreateBridgeExternalAccountRequest) ValidateAccountTypeSpecificFields() error {
	switch req.AccountType {
	case "us":
		return req.validateUSAccount()
	case "iban", "unknown":
		return req.validateIBANAccount()
	case "clabe":
		// For clabe, no specific validation required
		return nil
	default:
		return nil
	}
}

// validateUSAccount validates US account specific fields
func (req *CreateBridgeExternalAccountRequest) validateUSAccount() error {
	if req.US == nil {
		return fmt.Errorf("us account details are required when account_type is 'us'")
	}

	// Validate US account fields
	if req.US.Account == nil {
		return fmt.Errorf("us account information is required")
	}
	
	if req.US.Account.AccountNumber == "" {
		return fmt.Errorf("account_number is required for US accounts")
	}
	
	if req.US.Account.RoutingNumber == "" {
		return fmt.Errorf("routing_number is required for US accounts")
	}

	// Validate address for US accounts
	if req.Address == nil {
		return fmt.Errorf("address is required for US accounts")
	}

	// Validate street_line_1: required, length between 4 and 35
	if len(req.Address.StreetLine1) < 4 || len(req.Address.StreetLine1) > 35 {
		return fmt.Errorf("street_line_1 must be between 4 and 35 characters")
	}

	// Validate street_line_2: length ≤ 35 (optional)
	if len(req.Address.StreetLine2) > 35 {
		return fmt.Errorf("street_line_2 must be 35 characters or less")
	}

	// Validate city: required, length ≥ 1
	if len(req.Address.City) < 1 {
		return fmt.Errorf("city is required and must be at least 1 character")
	}

	// Validate state: length between 1 and 3 (optional but recommended for US)
	if req.Address.State != nil && (len(*req.Address.State) < 1 || len(*req.Address.State) > 3) {
		return fmt.Errorf("state must be between 1 and 3 characters when provided")
	}

	// Validate postal_code: length ≥ 1
	if len(req.Address.PostalCode) < 1 {
		return fmt.Errorf("postal_code is required and must be at least 1 character")
	}

	// Validate country: required, length exactly 3
	if len(req.Address.Country) != 3 {
		return fmt.Errorf("country must be exactly 3 characters")
	}

	return nil
}

// validateIBANAccount validates IBAN account specific fields
func (req *CreateBridgeExternalAccountRequest) validateIBANAccount() error {
	if req.Swift == nil {
		return fmt.Errorf("swift details are required when account_type is '%s'", req.AccountType)
	}
	
	// For unknown account type, also validate root level account_number
	if req.AccountType == "unknown" {
		if req.Swift.Account == nil || req.Swift.Account.AccountNumber == "" {
			return fmt.Errorf("swift account details are required when account_type is 'unknown'")
		}
	}
	
	return nil
}

// SwiftDetails represents SWIFT account information
type SwiftDetails struct {
	Account                *SwiftAccount     `json:"account,omitempty"`
	Address                *AddressDetails   `json:"address,omitempty"`
	PurposeOfFunds         []string          `json:"purpose_of_funds,omitempty"`
	Category               string            `json:"category,omitempty"`
	ShortBusinessDescription string          `json:"short_business_description,omitempty"`
}

// SwiftAccount represents SWIFT account details
type SwiftAccount struct {
	AccountNumber string `json:"account_number,omitempty"`
	BIC           string `json:"bic,omitempty"`
}

// USDetails represents US account information
type USDetails struct {
	Account *USAccount `json:"account,omitempty"`
}

// USAccount represents US account details
type USAccount struct {
	AccountNumber  string `json:"account_number"`
	RoutingNumber  string `json:"routing_number"`
}

// AddressDetails represents address information
type AddressDetails struct {
	Country       string  `json:"country,omitempty"`
	City          string  `json:"city,omitempty"`
	StreetLine1   string  `json:"street_line_1,omitempty"`
	StreetLine2   string  `json:"street_line_2,omitempty"`
	PostalCode    string  `json:"postal_code,omitempty"`
	State         *string `json:"state,omitempty"` // Added state field for US addresses
}

// CreateBridgeExternalAccountResponse represents the response after creating a bridge external account
type CreateBridgeExternalAccountResponse struct {
	ID               string            `json:"id"`
	CustomerID       string            `json:"customer_id"`
	OrganizationID   int               `json:"organization_id"`
	BankName         string            `json:"bank_name"`
	AccountOwnerName string            `json:"account_owner_name"`
	Active           bool              `json:"active"`
	Currency         string            `json:"currency"`
	AccountOwnerType string            `json:"account_owner_type"`
	AccountType      string            `json:"account_type"`
	BusinessName     *string           `json:"business_name,omitempty"`
	Account          *AccountInfo      `json:"account,omitempty"`
	CreatedAt        time.Time         `json:"created_at"`
}

// AccountInfo represents account information in the response
type AccountInfo struct {
	Last4 string `json:"last_4,omitempty"`
	BIC   string `json:"bic,omitempty"`
}

// CreateBridgeExternalAccountData represents the data to be stored in database
type CreateBridgeExternalAccountData struct {
	BridgeExternalAccountID string    `json:"bridge_external_account_id"`
	CustomerID              string    `json:"customer_id"`
	OrganizationID          int       `json:"organization_id"`
	BankName                string    `json:"bank_name"`
	AccountOwnerName        string    `json:"account_owner_name"`
	Active                  bool      `json:"active"`
	Currency                string    `json:"currency"`
	AccountOwnerType        string    `json:"account_owner_type"`
	AccountType             string    `json:"account_type"`
	BusinessName            *string   `json:"business_name,omitempty"`
	AccountLast4            *string   `json:"account_last_4,omitempty"`
	AccountBIC              *string   `json:"account_bic,omitempty"`
}

// BridgeExternalAccountData represents bridge external account data from database
type BridgeExternalAccountData struct {
	BridgeExternalAccountID string    `json:"bridge_external_account_id"`
	CustomerID              string    `json:"customer_id"`
	OrganizationID          int       `json:"organization_id"`
	BankName                string    `json:"bank_name"`
	AccountOwnerName        string    `json:"account_owner_name"`
	Active                  bool      `json:"active"`
	Currency                string    `json:"currency"`
	AccountOwnerType        string    `json:"account_owner_type"`
	AccountType             string    `json:"account_type"`
	BusinessName            *string   `json:"business_name,omitempty"`
	AccountLast4            *string   `json:"account_last_4,omitempty"`
	AccountBIC              *string   `json:"account_bic,omitempty"`
	CreatedAt               time.Time `json:"created_at"`
	UpdatedAt               time.Time `json:"updated_at"`
}

// BridgeCreateExternalAccountRequest represents the request to Bridge API for creating external account
type BridgeCreateExternalAccountRequest struct {
	Currency            string                  `json:"currency"`
	BankName            string                  `json:"bank_name"`
	AccountOwnerName    string                  `json:"account_owner_name"`
	AccountOwnerType    string                  `json:"account_owner_type"`
	AccountType         string                  `json:"account_type"`
	AccountNumber       *string                 `json:"account_number,omitempty"`
	BusinessName        *string                 `json:"business_name,omitempty"`
	Swift               *SwiftDetails           `json:"swift,omitempty"`
	US                  *USDetails              `json:"us,omitempty"`
	Address             *AddressDetails         `json:"address,omitempty"`
}

// BridgeCreateExternalAccountResponse represents the response from Bridge API
type BridgeCreateExternalAccountResponse struct {
	ID               string       `json:"id"`
	CustomerID       string       `json:"customer_id"`
	CreatedAt        string       `json:"created_at"`
	UpdatedAt        string       `json:"updated_at"`
	BankName         string       `json:"bank_name"`
	AccountName      *string      `json:"account_name"`
	AccountOwnerName string       `json:"account_owner_name"`
	Active           bool         `json:"active"`
	Currency         string       `json:"currency"`
	AccountOwnerType string       `json:"account_owner_type"`
	AccountType      string       `json:"account_type"`
	FirstName        *string      `json:"first_name"`
	LastName         *string      `json:"last_name"`
	BusinessName     *string      `json:"business_name"`
	Account          *AccountInfo `json:"account"`
}

// BridgeDuplicateAccountError represents a specific error for duplicate external accounts
type BridgeDuplicateAccountError struct {
	ExistingAccountID string
	Message           string
}

func (e *BridgeDuplicateAccountError) Error() string {
	return e.Message
} 