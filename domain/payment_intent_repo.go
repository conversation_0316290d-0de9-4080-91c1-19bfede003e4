//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=payment_intent_repo_mock.go . PaymentIntentRepo

package domain

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/shopspring/decimal"
)

type GetPaymentIntentsParams struct {
	OrgID    int
	Page     int
	PageSize int
	ClientID *string
	Status   []PaymentIntentStatus
	GroupKey *string
	ChainID  *string
}

// PaymentIntentStats represents the statistics for an organization's payment intents
type PaymentIntentStats struct {
	OrganizationID      int
	TotalRevenue        map[ChainToken]decimal.Decimal
	RateMap             map[ChainToken]decimal.Decimal
	ValidOrderCount     int
	UniqueCustomerCount int
	UpdatedAt           time.Time
}

// GetPaymentIntentStatsParams represents parameters for querying payment intent statistics
type GetPaymentIntentStatsParams struct {
	OrgID int
	Days  *int // Number of days to look back (e.g., 7, 14, 30). If nil, returns all-time stats
}

// PaymentIntentRepo defines the interface for payment intent-related database operations
type PaymentIntentRepo interface {
	LockManager

	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *OrganizationWallets, kgErr *code.KGError)
	CreatePaymentIntent(ctx context.Context, intent *PaymentIntent) (*PaymentIntent, error)
	GetPaymentIntentByID(ctx context.Context, id string) (*PaymentIntent, error)
	GetPaymentIntents(ctx context.Context, params GetPaymentIntentsParams) ([]*PaymentIntent, int, error)
	GetPaymentIntentByChainAddress(ctx context.Context, ca ChainAddress) (*PaymentIntent, error)
	UpdatePaymentIntent(ctx context.Context, id string, update *PaymentIntentUpdate) error
	GetPendingIntentsByPaymentAddresses(ctx context.Context, chain Chain, addresses []Address) (map[Address]*PaymentIntent, error)
	GetPendingIntents(ctx context.Context) ([]*PaymentIntent, error)
	GetPaymentIntentStats(ctx context.Context, orgID int) (*PaymentIntentStats, error)
	GetPaymentIntentStatsWithTimeRange(ctx context.Context, params GetPaymentIntentStatsParams) (*PaymentIntentStats, error)
}
