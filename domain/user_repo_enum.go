// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// AllUserDataFieldEmail is a AllUserDataField of type email.
	AllUserDataFieldEmail AllUserDataField = "email"
	// AllUserDataFieldDisplayName is a AllUserDataField of type display_name.
	AllUserDataFieldDisplayName AllUserDataField = "display_name"
	// AllUserDataFieldPhoneNumber is a AllUserDataField of type phone_number.
	AllUserDataFieldPhoneNumber AllUserDataField = "phone_number"
	// AllUserDataFieldReferralCode is a AllUserDataField of type referral_code.
	AllUserDataFieldReferralCode AllUserDataField = "referral_code"
	// AllUserDataFieldFcmTokens is a AllUserDataField of type fcm_tokens.
	AllUserDataFieldFcmTokens AllUserDataField = "fcm_tokens"
	// AllUserDataFieldFcmTokenMap is a AllUserDataField of type fcm_token_map.
	AllUserDataFieldFcmTokenMap AllUserDataField = "fcm_token_map"
	// AllUserDataFieldAvatar is a AllUserDataField of type avatar.
	AllUserDataFieldAvatar AllUserDataField = "avatar"
	// AllUserDataFieldLocale is a AllUserDataField of type locale.
	AllUserDataFieldLocale AllUserDataField = "locale"
	// AllUserDataFieldLocaleMap is a AllUserDataField of type locale_map.
	AllUserDataFieldLocaleMap AllUserDataField = "locale_map"
	// AllUserDataFieldReadAllTimestamp is a AllUserDataField of type read_all_timestamp.
	AllUserDataFieldReadAllTimestamp AllUserDataField = "read_all_timestamp"
	// AllUserDataFieldReadAllTimestampMap is a AllUserDataField of type read_all_timestamp_map.
	AllUserDataFieldReadAllTimestampMap AllUserDataField = "read_all_timestamp_map"
	// AllUserDataFieldSSCreateTime is a AllUserDataField of type SSCreateTime.
	AllUserDataFieldSSCreateTime AllUserDataField = "SSCreateTime"
	// AllUserDataFieldAlchemyNotifyAddress is a AllUserDataField of type alchemy_notify_address.
	AllUserDataFieldAlchemyNotifyAddress AllUserDataField = "alchemy_notify_address"
	// AllUserDataFieldDeviceIdentifier is a AllUserDataField of type device_identifier.
	AllUserDataFieldDeviceIdentifier AllUserDataField = "device_identifier"
	// AllUserDataFieldCreatedAt is a AllUserDataField of type created_at.
	AllUserDataFieldCreatedAt AllUserDataField = "created_at"
	// AllUserDataFieldPrivacyPolicyAgreement is a AllUserDataField of type privacy_policy_agreement.
	AllUserDataFieldPrivacyPolicyAgreement AllUserDataField = "privacy_policy_agreement"
	// AllUserDataFieldBio is a AllUserDataField of type bio.
	AllUserDataFieldBio AllUserDataField = "bio"
	// AllUserDataFieldTwitter is a AllUserDataField of type twitter.
	AllUserDataFieldTwitter AllUserDataField = "twitter"
	// AllUserDataFieldYoutube is a AllUserDataField of type youtube.
	AllUserDataFieldYoutube AllUserDataField = "youtube"
	// AllUserDataFieldInstagram is a AllUserDataField of type instagram.
	AllUserDataFieldInstagram AllUserDataField = "instagram"
	// AllUserDataFieldDiscord is a AllUserDataField of type discord.
	AllUserDataFieldDiscord AllUserDataField = "discord"
	// AllUserDataFieldCustomLink is a AllUserDataField of type custom_link.
	AllUserDataFieldCustomLink AllUserDataField = "custom_link"
	// AllUserDataFieldHideSpamNft is a AllUserDataField of type hide_spam_nft.
	AllUserDataFieldHideSpamNft AllUserDataField = "hide_spam_nft"
	// AllUserDataFieldHasLinkedGoogle is a AllUserDataField of type has_linked_google.
	AllUserDataFieldHasLinkedGoogle AllUserDataField = "has_linked_google"
	// AllUserDataFieldIsRandomPassword is a AllUserDataField of type is_random_password.
	AllUserDataFieldIsRandomPassword AllUserDataField = "is_random_password"
	// AllUserDataFieldBitcoinAddress is a AllUserDataField of type bitcoin_address.
	AllUserDataFieldBitcoinAddress AllUserDataField = "bitcoin_address"
	// AllUserDataFieldEthereumAddress is a AllUserDataField of type ethereum_address.
	AllUserDataFieldEthereumAddress AllUserDataField = "ethereum_address"
	// AllUserDataFieldSolanaAddress is a AllUserDataField of type solana_address.
	AllUserDataFieldSolanaAddress AllUserDataField = "solana_address"
	// AllUserDataFieldTronAddress is a AllUserDataField of type tron_address.
	AllUserDataFieldTronAddress AllUserDataField = "tron_address"
	// AllUserDataFieldEncryptSalt is a AllUserDataField of type encrypt_salt.
	AllUserDataFieldEncryptSalt AllUserDataField = "encrypt_salt"
	// AllUserDataFieldEncryptedMnemonic is a AllUserDataField of type encrypted_mnemonic.
	AllUserDataFieldEncryptedMnemonic AllUserDataField = "encrypted_mnemonic"
	// AllUserDataFieldWallets is a AllUserDataField of type wallets.
	AllUserDataFieldWallets AllUserDataField = "wallets"
	// AllUserDataFieldKycState is a AllUserDataField of type kyc_state.
	AllUserDataFieldKycState AllUserDataField = "kyc_state"
	// AllUserDataFieldVaultData is a AllUserDataField of type vault_data.
	AllUserDataFieldVaultData AllUserDataField = "vault_data"
	// AllUserDataFieldPassword is a AllUserDataField of type password.
	AllUserDataFieldPassword AllUserDataField = "password"
	// AllUserDataFieldPasswordSalt is a AllUserDataField of type password_salt.
	AllUserDataFieldPasswordSalt AllUserDataField = "password_salt"
	// AllUserDataFieldPasswordSaltFrontend is a AllUserDataField of type password_salt_frontend.
	AllUserDataFieldPasswordSaltFrontend AllUserDataField = "password_salt_frontend"
	// AllUserDataFieldShareKey is a AllUserDataField of type share_key.
	AllUserDataFieldShareKey AllUserDataField = "share_key"
	// AllUserDataFieldGoogleAccessTokens is a AllUserDataField of type google_access_tokens.
	AllUserDataFieldGoogleAccessTokens AllUserDataField = "google_access_tokens"
)

var ErrInvalidAllUserDataField = errors.New("not a valid AllUserDataField")

// String implements the Stringer interface.
func (x AllUserDataField) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AllUserDataField) IsValid() bool {
	_, err := ParseAllUserDataField(string(x))
	return err == nil
}

var _AllUserDataFieldValue = map[string]AllUserDataField{
	"email":                    AllUserDataFieldEmail,
	"display_name":             AllUserDataFieldDisplayName,
	"phone_number":             AllUserDataFieldPhoneNumber,
	"referral_code":            AllUserDataFieldReferralCode,
	"fcm_tokens":               AllUserDataFieldFcmTokens,
	"fcm_token_map":            AllUserDataFieldFcmTokenMap,
	"avatar":                   AllUserDataFieldAvatar,
	"locale":                   AllUserDataFieldLocale,
	"locale_map":               AllUserDataFieldLocaleMap,
	"read_all_timestamp":       AllUserDataFieldReadAllTimestamp,
	"read_all_timestamp_map":   AllUserDataFieldReadAllTimestampMap,
	"SSCreateTime":             AllUserDataFieldSSCreateTime,
	"alchemy_notify_address":   AllUserDataFieldAlchemyNotifyAddress,
	"device_identifier":        AllUserDataFieldDeviceIdentifier,
	"created_at":               AllUserDataFieldCreatedAt,
	"privacy_policy_agreement": AllUserDataFieldPrivacyPolicyAgreement,
	"bio":                      AllUserDataFieldBio,
	"twitter":                  AllUserDataFieldTwitter,
	"youtube":                  AllUserDataFieldYoutube,
	"instagram":                AllUserDataFieldInstagram,
	"discord":                  AllUserDataFieldDiscord,
	"custom_link":              AllUserDataFieldCustomLink,
	"hide_spam_nft":            AllUserDataFieldHideSpamNft,
	"has_linked_google":        AllUserDataFieldHasLinkedGoogle,
	"is_random_password":       AllUserDataFieldIsRandomPassword,
	"bitcoin_address":          AllUserDataFieldBitcoinAddress,
	"ethereum_address":         AllUserDataFieldEthereumAddress,
	"solana_address":           AllUserDataFieldSolanaAddress,
	"tron_address":             AllUserDataFieldTronAddress,
	"encrypt_salt":             AllUserDataFieldEncryptSalt,
	"encrypted_mnemonic":       AllUserDataFieldEncryptedMnemonic,
	"wallets":                  AllUserDataFieldWallets,
	"kyc_state":                AllUserDataFieldKycState,
	"vault_data":               AllUserDataFieldVaultData,
	"password":                 AllUserDataFieldPassword,
	"password_salt":            AllUserDataFieldPasswordSalt,
	"password_salt_frontend":   AllUserDataFieldPasswordSaltFrontend,
	"share_key":                AllUserDataFieldShareKey,
	"google_access_tokens":     AllUserDataFieldGoogleAccessTokens,
}

// ParseAllUserDataField attempts to convert a string to a AllUserDataField.
func ParseAllUserDataField(name string) (AllUserDataField, error) {
	if x, ok := _AllUserDataFieldValue[name]; ok {
		return x, nil
	}
	return AllUserDataField(""), fmt.Errorf("%s is %w", name, ErrInvalidAllUserDataField)
}

const (
	// SupportedUserFieldDisplayName is a SupportedUserField of type display_name.
	SupportedUserFieldDisplayName SupportedUserField = "display_name"
	// SupportedUserFieldFcmTokens is a SupportedUserField of type fcm_tokens.
	SupportedUserFieldFcmTokens SupportedUserField = "fcm_tokens"
	// SupportedUserFieldLocale is a SupportedUserField of type locale.
	SupportedUserFieldLocale SupportedUserField = "locale"
	// SupportedUserFieldAppVersion is a SupportedUserField of type app_version.
	SupportedUserFieldAppVersion SupportedUserField = "app_version"
	// SupportedUserFieldDeviceIdentifier is a SupportedUserField of type device_identifier.
	SupportedUserFieldDeviceIdentifier SupportedUserField = "device_identifier"
	// SupportedUserFieldPrivacyPolicyAgreement is a SupportedUserField of type privacy_policy_agreement.
	SupportedUserFieldPrivacyPolicyAgreement SupportedUserField = "privacy_policy_agreement"
	// SupportedUserFieldBio is a SupportedUserField of type bio.
	SupportedUserFieldBio SupportedUserField = "bio"
	// SupportedUserFieldTwitter is a SupportedUserField of type twitter.
	SupportedUserFieldTwitter SupportedUserField = "twitter"
	// SupportedUserFieldYoutube is a SupportedUserField of type youtube.
	SupportedUserFieldYoutube SupportedUserField = "youtube"
	// SupportedUserFieldInstagram is a SupportedUserField of type instagram.
	SupportedUserFieldInstagram SupportedUserField = "instagram"
	// SupportedUserFieldDiscord is a SupportedUserField of type discord.
	SupportedUserFieldDiscord SupportedUserField = "discord"
	// SupportedUserFieldCustomLink is a SupportedUserField of type custom_link.
	SupportedUserFieldCustomLink SupportedUserField = "custom_link"
	// SupportedUserFieldHideSpamNft is a SupportedUserField of type hide_spam_nft.
	SupportedUserFieldHideSpamNft SupportedUserField = "hide_spam_nft"
	// SupportedUserFieldHandle is a SupportedUserField of type handle.
	SupportedUserFieldHandle SupportedUserField = "handle"
)

var ErrInvalidSupportedUserField = errors.New("not a valid SupportedUserField")

// String implements the Stringer interface.
func (x SupportedUserField) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x SupportedUserField) IsValid() bool {
	_, err := ParseSupportedUserField(string(x))
	return err == nil
}

var _SupportedUserFieldValue = map[string]SupportedUserField{
	"display_name":             SupportedUserFieldDisplayName,
	"fcm_tokens":               SupportedUserFieldFcmTokens,
	"locale":                   SupportedUserFieldLocale,
	"app_version":              SupportedUserFieldAppVersion,
	"device_identifier":        SupportedUserFieldDeviceIdentifier,
	"privacy_policy_agreement": SupportedUserFieldPrivacyPolicyAgreement,
	"bio":                      SupportedUserFieldBio,
	"twitter":                  SupportedUserFieldTwitter,
	"youtube":                  SupportedUserFieldYoutube,
	"instagram":                SupportedUserFieldInstagram,
	"discord":                  SupportedUserFieldDiscord,
	"custom_link":              SupportedUserFieldCustomLink,
	"hide_spam_nft":            SupportedUserFieldHideSpamNft,
	"handle":                   SupportedUserFieldHandle,
}

// ParseSupportedUserField attempts to convert a string to a SupportedUserField.
func ParseSupportedUserField(name string) (SupportedUserField, error) {
	if x, ok := _SupportedUserFieldValue[name]; ok {
		return x, nil
	}
	return SupportedUserField(""), fmt.Errorf("%s is %w", name, ErrInvalidSupportedUserField)
}
