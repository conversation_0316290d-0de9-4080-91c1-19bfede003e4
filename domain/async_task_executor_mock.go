// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: AsyncTaskExecutor)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=async_task_executor_mock.go . AsyncTaskExecutor
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockAsyncTaskExecutor is a mock of AsyncTaskExecutor interface.
type MockAsyncTaskExecutor struct {
	ctrl     *gomock.Controller
	recorder *MockAsyncTaskExecutorMockRecorder
}

// MockAsyncTaskExecutorMockRecorder is the mock recorder for MockAsyncTaskExecutor.
type MockAsyncTaskExecutorMockRecorder struct {
	mock *MockAsyncTaskExecutor
}

// NewMockAsyncTaskExecutor creates a new mock instance.
func NewMockAsyncTaskExecutor(ctrl *gomock.Controller) *MockAsyncTaskExecutor {
	mock := &MockAsyncTaskExecutor{ctrl: ctrl}
	mock.recorder = &MockAsyncTaskExecutorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAsyncTaskExecutor) EXPECT() *MockAsyncTaskExecutorMockRecorder {
	return m.recorder
}

// Execute mocks base method.
func (m *MockAsyncTaskExecutor) Execute(arg0 context.Context, arg1 string, arg2 *HttpTask, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// Execute indicates an expected call of Execute.
func (mr *MockAsyncTaskExecutorMockRecorder) Execute(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*MockAsyncTaskExecutor)(nil).Execute), arg0, arg1, arg2, arg3)
}
