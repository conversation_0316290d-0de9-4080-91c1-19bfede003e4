// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: StudioUserAPIKeyRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=studio_api_key_repo_mock.go . StudioUserAPIKeyRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockStudioUserAPIKeyRepo is a mock of StudioUserAPIKeyRepo interface.
type MockStudioUserAPIKeyRepo struct {
	ctrl     *gomock.Controller
	recorder *MockStudioUserAPIKeyRepoMockRecorder
}

// MockStudioUserAPIKeyRepoMockRecorder is the mock recorder for MockStudioUserAPIKeyRepo.
type MockStudioUserAPIKeyRepoMockRecorder struct {
	mock *MockStudioUserAPIKeyRepo
}

// NewMockStudioUserAPIKeyRepo creates a new mock instance.
func NewMockStudioUserAPIKeyRepo(ctrl *gomock.Controller) *MockStudioUserAPIKeyRepo {
	mock := &MockStudioUserAPIKeyRepo{ctrl: ctrl}
	mock.recorder = &MockStudioUserAPIKeyRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStudioUserAPIKeyRepo) EXPECT() *MockStudioUserAPIKeyRepoMockRecorder {
	return m.recorder
}

// CreateStudioUserAPIKey mocks base method.
func (m *MockStudioUserAPIKeyRepo) CreateStudioUserAPIKey(arg0 context.Context, arg1 *StudioUserAPIKey) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateStudioUserAPIKey", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// CreateStudioUserAPIKey indicates an expected call of CreateStudioUserAPIKey.
func (mr *MockStudioUserAPIKeyRepoMockRecorder) CreateStudioUserAPIKey(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateStudioUserAPIKey", reflect.TypeOf((*MockStudioUserAPIKeyRepo)(nil).CreateStudioUserAPIKey), arg0, arg1)
}

// DeleteStudioUserAPIKey mocks base method.
func (m *MockStudioUserAPIKeyRepo) DeleteStudioUserAPIKey(arg0 context.Context, arg1, arg2 int, arg3 string) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteStudioUserAPIKey", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// DeleteStudioUserAPIKey indicates an expected call of DeleteStudioUserAPIKey.
func (mr *MockStudioUserAPIKeyRepoMockRecorder) DeleteStudioUserAPIKey(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteStudioUserAPIKey", reflect.TypeOf((*MockStudioUserAPIKeyRepo)(nil).DeleteStudioUserAPIKey), arg0, arg1, arg2, arg3)
}

// GetStudioUserAPIKeyByHash mocks base method.
func (m *MockStudioUserAPIKeyRepo) GetStudioUserAPIKeyByHash(arg0 context.Context, arg1 string) (*StudioUserAPIKey, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStudioUserAPIKeyByHash", arg0, arg1)
	ret0, _ := ret[0].(*StudioUserAPIKey)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetStudioUserAPIKeyByHash indicates an expected call of GetStudioUserAPIKeyByHash.
func (mr *MockStudioUserAPIKeyRepoMockRecorder) GetStudioUserAPIKeyByHash(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStudioUserAPIKeyByHash", reflect.TypeOf((*MockStudioUserAPIKeyRepo)(nil).GetStudioUserAPIKeyByHash), arg0, arg1)
}

// ListStudioUserAPIKeys mocks base method.
func (m *MockStudioUserAPIKeyRepo) ListStudioUserAPIKeys(arg0 context.Context, arg1 GetStudioUserAPIKeysParams) ([]*StudioUserAPIKey, int, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListStudioUserAPIKeys", arg0, arg1)
	ret0, _ := ret[0].([]*StudioUserAPIKey)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(*code.KGError)
	return ret0, ret1, ret2
}

// ListStudioUserAPIKeys indicates an expected call of ListStudioUserAPIKeys.
func (mr *MockStudioUserAPIKeyRepoMockRecorder) ListStudioUserAPIKeys(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListStudioUserAPIKeys", reflect.TypeOf((*MockStudioUserAPIKeyRepo)(nil).ListStudioUserAPIKeys), arg0, arg1)
}

// UpdateAPIKeyLastUsed mocks base method.
func (m *MockStudioUserAPIKeyRepo) UpdateAPIKeyLastUsed(arg0 context.Context, arg1 int) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAPIKeyLastUsed", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// UpdateAPIKeyLastUsed indicates an expected call of UpdateAPIKeyLastUsed.
func (mr *MockStudioUserAPIKeyRepoMockRecorder) UpdateAPIKeyLastUsed(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAPIKeyLastUsed", reflect.TypeOf((*MockStudioUserAPIKeyRepo)(nil).UpdateAPIKeyLastUsed), arg0, arg1)
}
