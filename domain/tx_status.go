//go:generate go-enum
package domain

// TxStatus on chain transaction status type
// ENUM(success, failed, unknown)
type TxStatus string

var TxStatusReceiptToStatus = map[string]TxStatus{
	"0x0": TxStatusFailed,
	"0":   TxStatusFailed,
	"0x1": TxStatusSuccess,
	"1":   TxStatusSuccess,
}

func TxStatusFromEtherscan(status string) TxStatus {
	if s, ok := TxStatusReceiptToStatus[status]; ok {
		return s
	}
	return TxStatusUnknown
}
