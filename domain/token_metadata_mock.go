// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: TokenMetadataRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=token_metadata_mock.go . TokenMetadataRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockTokenMetadataRepo is a mock of TokenMetadataRepo interface.
type MockTokenMetadataRepo struct {
	ctrl     *gomock.Controller
	recorder *MockTokenMetadataRepoMockRecorder
}

// MockTokenMetadataRepoMockRecorder is the mock recorder for MockTokenMetadataRepo.
type MockTokenMetadataRepoMockRecorder struct {
	mock *MockTokenMetadataRepo
}

// NewMockTokenMetadataRepo creates a new mock instance.
func NewMockTokenMetadataRepo(ctrl *gomock.Controller) *MockTokenMetadataRepo {
	mock := &MockTokenMetadataRepo{ctrl: ctrl}
	mock.recorder = &MockTokenMetadataRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTokenMetadataRepo) EXPECT() *MockTokenMetadataRepoMockRecorder {
	return m.recorder
}

// BatchGetTokenMetadata mocks base method.
func (m *MockTokenMetadataRepo) BatchGetTokenMetadata(arg0 context.Context, arg1 []ChainToken) (map[ChainToken]*TokenMetadata, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenMetadata", arg0, arg1)
	ret0, _ := ret[0].(map[ChainToken]*TokenMetadata)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenMetadata indicates an expected call of BatchGetTokenMetadata.
func (mr *MockTokenMetadataRepoMockRecorder) BatchGetTokenMetadata(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenMetadata", reflect.TypeOf((*MockTokenMetadataRepo)(nil).BatchGetTokenMetadata), arg0, arg1)
}

// BatchUpdateTokenMetadata mocks base method.
func (m *MockTokenMetadataRepo) BatchUpdateTokenMetadata(arg0 context.Context, arg1 map[ChainToken]*TokenMetadata) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateTokenMetadata", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateTokenMetadata indicates an expected call of BatchUpdateTokenMetadata.
func (mr *MockTokenMetadataRepoMockRecorder) BatchUpdateTokenMetadata(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateTokenMetadata", reflect.TypeOf((*MockTokenMetadataRepo)(nil).BatchUpdateTokenMetadata), arg0, arg1)
}

// BatchUpsertTokenMetadata mocks base method.
func (m *MockTokenMetadataRepo) BatchUpsertTokenMetadata(arg0 context.Context, arg1 map[ChainToken]*TokenMetadata) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpsertTokenMetadata", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpsertTokenMetadata indicates an expected call of BatchUpsertTokenMetadata.
func (mr *MockTokenMetadataRepoMockRecorder) BatchUpsertTokenMetadata(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpsertTokenMetadata", reflect.TypeOf((*MockTokenMetadataRepo)(nil).BatchUpsertTokenMetadata), arg0, arg1)
}

// IsAirdropEventByAddress mocks base method.
func (m *MockTokenMetadataRepo) IsAirdropEventByAddress(arg0 context.Context, arg1 Chain, arg2 string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsAirdropEventByAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsAirdropEventByAddress indicates an expected call of IsAirdropEventByAddress.
func (mr *MockTokenMetadataRepoMockRecorder) IsAirdropEventByAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsAirdropEventByAddress", reflect.TypeOf((*MockTokenMetadataRepo)(nil).IsAirdropEventByAddress), arg0, arg1, arg2)
}

// IsTokenSpam mocks base method.
func (m *MockTokenMetadataRepo) IsTokenSpam(arg0 context.Context, arg1 Chain, arg2 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsTokenSpam", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsTokenSpam indicates an expected call of IsTokenSpam.
func (mr *MockTokenMetadataRepoMockRecorder) IsTokenSpam(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTokenSpam", reflect.TypeOf((*MockTokenMetadataRepo)(nil).IsTokenSpam), arg0, arg1, arg2)
}

// SetTokenSpam mocks base method.
func (m *MockTokenMetadataRepo) SetTokenSpam(arg0 context.Context, arg1 Chain, arg2 string, arg3 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTokenSpam", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTokenSpam indicates an expected call of SetTokenSpam.
func (mr *MockTokenMetadataRepoMockRecorder) SetTokenSpam(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTokenSpam", reflect.TypeOf((*MockTokenMetadataRepo)(nil).SetTokenSpam), arg0, arg1, arg2, arg3)
}

// UpsertTokenMetadata mocks base method.
func (m *MockTokenMetadataRepo) UpsertTokenMetadata(arg0 context.Context, arg1 Chain, arg2 string, arg3 *TokenMetadata) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertTokenMetadata", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertTokenMetadata indicates an expected call of UpsertTokenMetadata.
func (mr *MockTokenMetadataRepoMockRecorder) UpsertTokenMetadata(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTokenMetadata", reflect.TypeOf((*MockTokenMetadataRepo)(nil).UpsertTokenMetadata), arg0, arg1, arg2, arg3)
}
