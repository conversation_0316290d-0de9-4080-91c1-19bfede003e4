package domain

import (
	"context"
	"time"
)

// LockManager is an interface for acquiring and releasing locks
type LockManager interface {
	// AcquireLock acquires a lock for a key. If the lock is already acquired, it will return immediately
	AcquireLock(ctx context.Context, key string, duration time.Duration) error
	// AcquireLockWithRetry attempts to acquire a lock with the given key and retries at regular intervals until the context is canceled.
	// It returns nil if the lock is successfully acquired, or an error if the context is canceled or if there's an unexpected error.
	AcquireLockWithRetry(ctx context.Context, key string, duration, retryInterval time.Duration) error
	// ReleaseLock releases a lock for a key
	ReleaseLock(ctx context.Context, key string)
}
