package domain

import (
	"context"
	"math/big"
)

// ReferralReward represents a referral reward record
type ReferralReward struct {
	ID          int
	ReferrerUID string   // User ID of the referrer
	From        Address  // Solana address that made the transaction
	TxHash      string   // Transaction hash on Solana blockchain
	Amount      *big.Int // Reward amount in lamports
}

// ReferralBalance represents a user's total referral rewards
type ReferralBalance struct {
	UID              string
	TotalRewards     *big.Int // Total accumulated rewards in lamports
	AvailableRewards *big.Int // Available rewards for withdrawal in lamports
	WithdrawnRewards *big.Int // Total withdrawn rewards in lamports
}

// ReferralWithdrawal represents a withdrawal of referral rewards
type ReferralWithdrawal struct {
	ID        int
	UID       string
	Amount    *big.Int // Amount in lamports
	Recipient Address  // Solana address to receive rewards
	TxHash    string   // Transaction hash of the withdrawal
}

// ReferralRepo defines the interface for referral data operations
type ReferralRepo interface {

	// GetUserByReferralCode gets user ID by referral code
	GetUserByReferralCode(ctx context.Context, code string) (string, error)

	// CreateReferralReward creates a new referral reward record
	CreateReferralReward(ctx context.Context, reward *ReferralReward) (int, error)

	// GetReferralBalance gets a user's referral balance
	GetReferralBalance(ctx context.Context, uid string) (*ReferralBalance, error)

	// CreateReferralWithdrawal creates a new withdrawal record
	CreateReferralWithdrawal(ctx context.Context, withdrawal *ReferralWithdrawal) (int, error)

	// UpdateReferralWithdrawalTx updates tx hash of a withdrawal record
	UpdateReferralWithdrawalTx(ctx context.Context, withdrawalId int, txHash string) error
}

