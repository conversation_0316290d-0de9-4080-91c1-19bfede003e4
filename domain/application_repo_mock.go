// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: ApplicationRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=application_repo_mock.go . ApplicationRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockApplicationRepo is a mock of ApplicationRepo interface.
type MockApplicationRepo struct {
	ctrl     *gomock.Controller
	recorder *MockApplicationRepoMockRecorder
}

// MockApplicationRepoMockRecorder is the mock recorder for MockApplicationRepo.
type MockApplicationRepoMockRecorder struct {
	mock *MockApplicationRepo
}

// NewMockApplicationRepo creates a new mock instance.
func NewMockApplicationRepo(ctrl *gomock.Controller) *MockApplicationRepo {
	mock := &MockApplicationRepo{ctrl: ctrl}
	mock.recorder = &MockApplicationRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockApplicationRepo) EXPECT() *MockApplicationRepoMockRecorder {
	return m.recorder
}

// GetAllApplications mocks base method.
func (m *MockApplicationRepo) GetAllApplications(arg0 context.Context) ([]*Application, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllApplications", arg0)
	ret0, _ := ret[0].([]*Application)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllApplications indicates an expected call of GetAllApplications.
func (mr *MockApplicationRepoMockRecorder) GetAllApplications(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllApplications", reflect.TypeOf((*MockApplicationRepo)(nil).GetAllApplications), arg0)
}

// GetAllCustomAuthApplications mocks base method.
func (m *MockApplicationRepo) GetAllCustomAuthApplications(arg0 context.Context) ([]*CustomAuthApplication, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllCustomAuthApplications", arg0)
	ret0, _ := ret[0].([]*CustomAuthApplication)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllCustomAuthApplications indicates an expected call of GetAllCustomAuthApplications.
func (mr *MockApplicationRepoMockRecorder) GetAllCustomAuthApplications(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllCustomAuthApplications", reflect.TypeOf((*MockApplicationRepo)(nil).GetAllCustomAuthApplications), arg0)
}

// GetAllOAuthApplications mocks base method.
func (m *MockApplicationRepo) GetAllOAuthApplications(arg0 context.Context) ([]*OAuthApplication, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllOAuthApplications", arg0)
	ret0, _ := ret[0].([]*OAuthApplication)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllOAuthApplications indicates an expected call of GetAllOAuthApplications.
func (mr *MockApplicationRepoMockRecorder) GetAllOAuthApplications(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllOAuthApplications", reflect.TypeOf((*MockApplicationRepo)(nil).GetAllOAuthApplications), arg0)
}

// GetApplication mocks base method.
func (m *MockApplicationRepo) GetApplication(arg0 context.Context, arg1 string) (*Application, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplication", arg0, arg1)
	ret0, _ := ret[0].(*Application)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplication indicates an expected call of GetApplication.
func (mr *MockApplicationRepoMockRecorder) GetApplication(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplication", reflect.TypeOf((*MockApplicationRepo)(nil).GetApplication), arg0, arg1)
}

// GetApplicationOrgId mocks base method.
func (m *MockApplicationRepo) GetApplicationOrgId(arg0 context.Context, arg1 string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplicationOrgId", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplicationOrgId indicates an expected call of GetApplicationOrgId.
func (mr *MockApplicationRepoMockRecorder) GetApplicationOrgId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicationOrgId", reflect.TypeOf((*MockApplicationRepo)(nil).GetApplicationOrgId), arg0, arg1)
}

// GetCustomAuthApplication mocks base method.
func (m *MockApplicationRepo) GetCustomAuthApplication(arg0 context.Context, arg1 string) (*CustomAuthApplication, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomAuthApplication", arg0, arg1)
	ret0, _ := ret[0].(*CustomAuthApplication)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomAuthApplication indicates an expected call of GetCustomAuthApplication.
func (mr *MockApplicationRepoMockRecorder) GetCustomAuthApplication(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomAuthApplication", reflect.TypeOf((*MockApplicationRepo)(nil).GetCustomAuthApplication), arg0, arg1)
}

// GetOAuthApplication mocks base method.
func (m *MockApplicationRepo) GetOAuthApplication(arg0 context.Context, arg1 string) (*OAuthApplication, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOAuthApplication", arg0, arg1)
	ret0, _ := ret[0].(*OAuthApplication)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOAuthApplication indicates an expected call of GetOAuthApplication.
func (mr *MockApplicationRepoMockRecorder) GetOAuthApplication(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOAuthApplication", reflect.TypeOf((*MockApplicationRepo)(nil).GetOAuthApplication), arg0, arg1)
}

// GetOAuthApplicationByDomain mocks base method.
func (m *MockApplicationRepo) GetOAuthApplicationByDomain(arg0 context.Context, arg1 string) (*OAuthApplication, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOAuthApplicationByDomain", arg0, arg1)
	ret0, _ := ret[0].(*OAuthApplication)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOAuthApplicationByDomain indicates an expected call of GetOAuthApplicationByDomain.
func (mr *MockApplicationRepoMockRecorder) GetOAuthApplicationByDomain(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOAuthApplicationByDomain", reflect.TypeOf((*MockApplicationRepo)(nil).GetOAuthApplicationByDomain), arg0, arg1)
}

// GetOAuthApplicationByName mocks base method.
func (m *MockApplicationRepo) GetOAuthApplicationByName(arg0 context.Context, arg1 string) (*OAuthApplication, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOAuthApplicationByName", arg0, arg1)
	ret0, _ := ret[0].(*OAuthApplication)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOAuthApplicationByName indicates an expected call of GetOAuthApplicationByName.
func (mr *MockApplicationRepoMockRecorder) GetOAuthApplicationByName(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOAuthApplicationByName", reflect.TypeOf((*MockApplicationRepo)(nil).GetOAuthApplicationByName), arg0, arg1)
}

// GetOAuthApplicationInOrg mocks base method.
func (m *MockApplicationRepo) GetOAuthApplicationInOrg(arg0 context.Context, arg1 GetOAuthApplicationInOrgParams) ([]*OAuthApplication, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOAuthApplicationInOrg", arg0, arg1)
	ret0, _ := ret[0].([]*OAuthApplication)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetOAuthApplicationInOrg indicates an expected call of GetOAuthApplicationInOrg.
func (mr *MockApplicationRepoMockRecorder) GetOAuthApplicationInOrg(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOAuthApplicationInOrg", reflect.TypeOf((*MockApplicationRepo)(nil).GetOAuthApplicationInOrg), arg0, arg1)
}

// UpsertCustomAuthApplication mocks base method.
func (m *MockApplicationRepo) UpsertCustomAuthApplication(arg0 context.Context, arg1 int, arg2 *CustomAuthApplication) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertCustomAuthApplication", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertCustomAuthApplication indicates an expected call of UpsertCustomAuthApplication.
func (mr *MockApplicationRepoMockRecorder) UpsertCustomAuthApplication(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertCustomAuthApplication", reflect.TypeOf((*MockApplicationRepo)(nil).UpsertCustomAuthApplication), arg0, arg1, arg2)
}

// UpsertOAuthApplication mocks base method.
func (m *MockApplicationRepo) UpsertOAuthApplication(arg0 context.Context, arg1 int, arg2 *OAuthApplication) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertOAuthApplication", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertOAuthApplication indicates an expected call of UpsertOAuthApplication.
func (mr *MockApplicationRepoMockRecorder) UpsertOAuthApplication(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertOAuthApplication", reflect.TypeOf((*MockApplicationRepo)(nil).UpsertOAuthApplication), arg0, arg1, arg2)
}
