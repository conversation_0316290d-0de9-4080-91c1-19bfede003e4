//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=asset_repo_mock.go . AssetRepo
package domain

import (
	"context"
	"time"

	"github.com/shopspring/decimal"
)

type TokenAmount struct {
	Token
	Amount   decimal.Decimal
	Price    *float64
	Metadata WalletTokenMetadata // represents metadata about a token owned by a wallet
}

type NftAmount struct {
	Nft
	Amount uint
}

type ListAssetsParam struct {
	// If true, it will allow getting all assets even if addresses are not provided
	WithAllAddresses  bool
	Addresses         map[Chain][]Address
	Types             []AssetType
	IncludeUnverified bool
	Query             string
	IncludeTokens     map[Chain][]string
	ExcludeTokens     map[Chain][]string
	PageNumber        int
	PageSize          int
}

type AssetRepo interface {
	// SetTokenAmounts sets the token amounts for the given address on the given chain, and remove the tokens of given address that are not in the given map
	SetTokenAmounts(ctx context.Context, amounts map[ChainAddress][]*TokenAmount) error
	// UpdateTokenAmounts update/create the token amounts for the given address on the given chain. It won't remove other tokens owned by the address
	UpdateTokenAmounts(ctx context.Context, amounts map[ChainAddress][]*TokenAmount) error
	// DeleteTokenAmounts deletes the token amounts for the given address on the given chain
	DeleteTokenAmounts(ctx context.Context, amounts map[ChainAddress][]string) error
	// SetNftAmounts sets the nft assets for the given address on the given chain, and remove the nfts of given address that are not in the given map
	SetNftAmounts(ctx context.Context, amounts map[ChainAddress][]*NftAmount) error
	// SetDefiAssets sets the defi assets for the given address on the given chain, and remove the defi assets of given address that are not in the given map
	SetDefiAssets(ctx context.Context, amounts map[ChainAddress][]*DefiAsset) error
	// UpdateDefiAssets updates the defi assets for the given addresses
	UpdateDefiAssets(ctx context.Context, defiAssetMap map[ChainAddress][]*DefiAsset) error
	// ListAssets lists the assets for the given params
	ListAssets(ctx context.Context, params *ListAssetsParam) ([]Asset, int, error)
	// ListMainTokens lists the main tokens for the given addresses
	ListMainTokens(ctx context.Context, addresses map[Chain][]Address) ([]Asset, error)
	// GetSingleAsset gets the single asset for the given params
	GetSingleAsset(ctx context.Context, chain Chain, assetType AssetType, assetID string, addresses []Address) (Asset, error)
	// GetTokensByAddress gets the chain token list for the given addresses
	GetTokensByAddress(ctx context.Context, addresses map[Chain][]Address) ([]ChainToken, error)
	// GetAllTokens gets all the tokens
	GetAllTokens(ctx context.Context) ([]ChainToken, error)
}

type AssetBalanceRepo interface {
	// GetAssetsTotalUsdValue gets the total usd value of the assets for the given chains, asset types and addresses
	GetAssetsTotalUsdValue(ctx context.Context, chains []Chain, addresses []Address, types []AssetType) (float64, error)
	// GetPastAssetsTotalUsdValue gets the total usd value of the assets for the given chains, asset types and addresses of the first record after a given time
	GetPastAssetsTotalUsdValue(ctx context.Context, chains []Chain, addresses []Address, types []AssetType, from time.Time) (float64, error)
	// GetAssetBalances gets the asset balances for the given chains, addresses and asset types
	GetAssetBalances(ctx context.Context, chains []Chain, addresses []Address, types []AssetType) (map[ChainAddress]map[AssetType]float64, error)
	// GetPastAssetBalances gets the asset balances for the given chains, addresses and asset types of the first record after a given time
	GetPastAssetBalances(ctx context.Context, chains []Chain, addresses []Address, types []AssetType, from time.Time) (map[ChainAddress]map[AssetType]float64, error)
}
