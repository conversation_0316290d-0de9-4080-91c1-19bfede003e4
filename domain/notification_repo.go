//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=notification_repo_mock.go . NotificationRepo

package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// NotificationRepo defines the interface for notification-related database operations
type NotificationRepo interface {
	AddNotification(ctx context.Context, notification *Notification, allClients bool) (int, error)
	GetUserClientIDs(ctx context.Context, uid string) ([]string, *code.KGError)
	GetUserLocale(ctx context.Context, uid, clientID string) (string, *code.KGError)
	GetFcmTokens(ctx context.Context, uid, clientID string) ([]string, *code.KGError)
	SetUserFcmTokens(ctx context.Context, uid string, clientID string, tokens []string) error
}
