// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: NotificationRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=notification_repo_mock.go . NotificationRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockNotificationRepo is a mock of NotificationRepo interface.
type MockNotificationRepo struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationRepoMockRecorder
}

// MockNotificationRepoMockRecorder is the mock recorder for MockNotificationRepo.
type MockNotificationRepoMockRecorder struct {
	mock *MockNotificationRepo
}

// NewMockNotificationRepo creates a new mock instance.
func NewMockNotificationRepo(ctrl *gomock.Controller) *MockNotificationRepo {
	mock := &MockNotificationRepo{ctrl: ctrl}
	mock.recorder = &MockNotificationRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationRepo) EXPECT() *MockNotificationRepoMockRecorder {
	return m.recorder
}

// AddNotification mocks base method.
func (m *MockNotificationRepo) AddNotification(arg0 context.Context, arg1 *Notification, arg2 bool) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddNotification", arg0, arg1, arg2)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddNotification indicates an expected call of AddNotification.
func (mr *MockNotificationRepoMockRecorder) AddNotification(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddNotification", reflect.TypeOf((*MockNotificationRepo)(nil).AddNotification), arg0, arg1, arg2)
}

// GetFcmTokens mocks base method.
func (m *MockNotificationRepo) GetFcmTokens(arg0 context.Context, arg1, arg2 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFcmTokens", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetFcmTokens indicates an expected call of GetFcmTokens.
func (mr *MockNotificationRepoMockRecorder) GetFcmTokens(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFcmTokens", reflect.TypeOf((*MockNotificationRepo)(nil).GetFcmTokens), arg0, arg1, arg2)
}

// GetUserClientIDs mocks base method.
func (m *MockNotificationRepo) GetUserClientIDs(arg0 context.Context, arg1 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserClientIDs", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserClientIDs indicates an expected call of GetUserClientIDs.
func (mr *MockNotificationRepoMockRecorder) GetUserClientIDs(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserClientIDs", reflect.TypeOf((*MockNotificationRepo)(nil).GetUserClientIDs), arg0, arg1)
}

// GetUserLocale mocks base method.
func (m *MockNotificationRepo) GetUserLocale(arg0 context.Context, arg1, arg2 string) (string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLocale", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserLocale indicates an expected call of GetUserLocale.
func (mr *MockNotificationRepoMockRecorder) GetUserLocale(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLocale", reflect.TypeOf((*MockNotificationRepo)(nil).GetUserLocale), arg0, arg1, arg2)
}

// SetUserFcmTokens mocks base method.
func (m *MockNotificationRepo) SetUserFcmTokens(arg0 context.Context, arg1, arg2 string, arg3 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserFcmTokens", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserFcmTokens indicates an expected call of SetUserFcmTokens.
func (mr *MockNotificationRepoMockRecorder) SetUserFcmTokens(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserFcmTokens", reflect.TypeOf((*MockNotificationRepo)(nil).SetUserFcmTokens), arg0, arg1, arg2, arg3)
}
