package domain

// OrganizationWallets contains a organization's wallet info
type OrganizationWallets struct {
	EvmAddress         string
	TronAddress        string
	SolanaAddress      string
	SignAlertThreshold float64
}

// OrganizationSigner represents wallets signer of an organization
type OrganizationSigner struct {
	SignAlertThreshold float64
	EvmWallet          *EvmWallet
	TronWallet         *TronWallet
	SolanaWallet       *SolanaWallet
	EvmAlertPolicy     EvmAlertPolicy
	TronAlertPolicy    TronAlertPolicy
}

// NewOrganizationSigner creates a new organization signer
func NewOrganizationSigner(alertThreshold float64, evmWallet *EvmWallet, tronWallet *TronWallet, solanaWallet *SolanaWallet, evmAlertPolicy EvmAlertPolicy, tronAlertPolicy TronAlertPolicy) *OrganizationSigner {
	return &OrganizationSigner{
		SignAlertThreshold: alertThreshold,
		EvmWallet:          evmWallet,
		TronWallet:         tronWallet,
		SolanaWallet:       solanaWallet,
		EvmAlertPolicy:     evmAlertPolicy,
		TronAlertPolicy:    tronAlertPolicy,
	}
}
