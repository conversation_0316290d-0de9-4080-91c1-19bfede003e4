// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// SendWithFeeTransactionTypeMainToken is a SendWithFeeTransactionType of type main_token.
	SendWithFeeTransactionTypeMainToken SendWithFeeTransactionType = "main_token"
	// SendWithFeeTransactionTypeToken is a SendWithFeeTransactionType of type token.
	SendWithFeeTransactionTypeToken SendWithFeeTransactionType = "token"
)

var ErrInvalidSendWithFeeTransactionType = errors.New("not a valid SendWithFeeTransactionType")

// String implements the Stringer interface.
func (x SendWithFeeTransactionType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x SendWithFeeTransactionType) IsValid() bool {
	_, err := ParseSendWithFeeTransactionType(string(x))
	return err == nil
}

var _SendWithFeeTransactionTypeValue = map[string]SendWithFeeTransactionType{
	"main_token": SendWithFeeTransactionTypeMainToken,
	"token":      SendWithFeeTransactionTypeToken,
}

// ParseSendWithFeeTransactionType attempts to convert a string to a SendWithFeeTransactionType.
func ParseSendWithFeeTransactionType(name string) (SendWithFeeTransactionType, error) {
	if x, ok := _SendWithFeeTransactionTypeValue[name]; ok {
		return x, nil
	}
	return SendWithFeeTransactionType(""), fmt.Errorf("%s is %w", name, ErrInvalidSendWithFeeTransactionType)
}
