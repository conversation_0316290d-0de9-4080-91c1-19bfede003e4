//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=chain_sync_fetcher_mock.go . ChainSyncFetcher
package domain

import "context"

// TransactionWithAddresses contains a transaction and its related addresses.
// Note that the Addresses may not be complete, but up to chain sync fetcher to support which kinds of parsing are included,
// e.g. internal tx, erc20, erc721, etc.
type TransactionWithAddresses struct {
	Chain     Chain
	Hash      string
	Addresses []Address
}

// ChainSyncFetcher is an interface to fetch all transactions with related addresses in a block range from a chain's RPC.
type ChainSyncFetcher interface {
	// FetchTxsInRange fetches all transactions (token transfers, smart contract calls, etc.) with addresses in a block range. If toBlock is greater than the current block number, it will only fetch up to the current block number. It returns the last block number fetched and the transactions.
	FetchTxsInRange(ctx context.Context, chain Chain, fromBlock, toBlock uint64) (uint64, []*TransactionWithAddresses, error)
}
