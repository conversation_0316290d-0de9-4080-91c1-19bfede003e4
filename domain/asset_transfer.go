//go:generate go-enum
//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=asset_transfer_mock.go . AssetTransferRepo

package domain

import (
	"context"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/paging"
	"github.com/shopspring/decimal"
)

// AssetTransferRepo interface for asset transfer
type AssetTransferRepo interface {
	// histories
	CreateAssetProTxLog(ctx context.Context, txLog *AssetProTxLog) (int, error)
	UpdateAssetProTxLog(ctx context.Context, req *UpdateAssetProTxLogRequest) *code.KGError
	GetTxLogFilterOptions(ctx context.Context, organizationID int) (*FilterOptions, error)
	GetAssetProTxLogs(ctx context.Context, params *GetAssetProTxLogsParams) ([]AssetProTxLog, *paging.Resp, error)
	GetAssetProTxLogDetail(ctx context.Context, orgID int, serialID string) (*AssetProTxLogDetail, *code.KGError)
	GetAssetProTxLogByTxHash(ctx context.Context, txHash string) (*AssetProTxLog, *code.KGError)
	GetAssetProTxLogBySerialID(ctx context.Context, serialID string) (*AssetProTxLog, *code.KGError)
	GetPendingHistoryCount(ctx context.Context, params AssetProTxLogPendingCountParams) (AssetProTxLogPendingCount, *code.KGError)
	GetDailyUsedLimit(ctx context.Context, organizationID int, operatorUID string, today time.Time) (decimal.Decimal, error)
	GetAssetPrice(ctx context.Context, chainID, contractAddress string) (float64, error)
}

// AssetProTxLogDetail is a domain model that represents a asset pro tx log detail
type AssetProTxLogDetail struct {
	ID             int
	SerialID       string
	OrganizationID int
	OperatorUID    string
	SubmitTime     int
	Status         AssetProTxLogStatus
	FromAddress    string
	TxHashes       *[]string
	TransferTime   *int
	Amount         decimal.Decimal
	UsdAmount      decimal.Decimal

	// Token info
	ChainID          string
	ContractAddress  string
	TokenName        string
	TokenSymbol      string
	TokenLogoURL     string
	TokenCoingeckoID string
	TokenDecimals    int

	// Recipient info
	ToAddress           string
	RecipientName       *string
	RecipientPhone      *string
	RecipientEmail      *string
	RecipientUID        *string
	RecipientKycStatus  *KycStatus
	RecipientProfileImg *string

	// Notes
	SubmissionNote *string
	RejectionNote  *string
	Attachments    *[]string

	// Operators
	Trader         AssetProTxOperator
	Approver       *AssetProTxOperator
	FinanceManager *AssetProTxOperator
}

// AssetProTxOperator is a domain model that represents asset pro tx operator
type AssetProTxOperator struct {
	UID           string
	Name          string
	ProfileImg    string
	Email         string
	OperationTime int
}

// AssetProTxLog is a domain model that represents a asset pro tx log
type AssetProTxLog struct {
	ID             int
	SerialID       string
	OrganizationID int
	OperatorUID    string
	Token          string
	SubmitTime     int
	Status         AssetProTxLogStatus
	UpdateTime     int

	// Trader
	SubmissionNote        *string
	SubmissionAttachments []string

	// Tx info
	ChainID          string
	ContractAddress  string
	FromAddress      string
	ToAddress        string
	TxHash           string
	TransferTime     *int
	Amount           decimal.Decimal
	UsdAmount        decimal.Decimal
	TokenName        string
	TokenLogoURL     string
	TokenCoingeckoID string

	// Recipient info
	RecipientName      *string
	RecipientPhone     *string
	RecipientEmail     *string
	RecipientUID       *string
	RecipientKycStatus *KycStatus

	// Studio user info
	UpdatedBy string
}

// UpdateAssetProTxLogRequest is a domain model that represents params for updating asset pro tx log
type UpdateAssetProTxLogRequest struct {
	ID                int
	TxHash            *string
	Status            *AssetProTxLogStatus
	ApproverUID       *string
	FinanceManagerUID *string
	ApproveTime       *int
	ReleaseTime       *int
	RejectionNote     *string
	TransferTime      *int
}

// AssetProTxLogStatus is the status of asset pro tx log
// ENUM(awaiting_approval, awaiting_release, sending, send_success, send_failed, rejected)
type AssetProTxLogStatus string

// GetAssetProTxLogsParams is a domain model that represents params for getting asset pro tx logs
type GetAssetProTxLogsParams struct {
	OrganizationID    int
	SubmitterUID      string
	ApproverUID       string
	FinanceManagerUID string
	RejecterUID       string
	StatusList        []string
	Token             string
	ChainID           string
	SubmitTimeFrom    int64
	SubmitTimeTo      int64
	AmountFrom        float64
	AmountTo          float64
	UsdAmountFrom     float64
	UsdAmountTo       float64
	TransferTimeFrom  int64
	TransferTimeTo    int64
	TxHash            string
	TraderUID         string
	*paging.Query
}

// FilterOptionUser is a domain model that represents filter option operator.
type FilterOptionUser struct {
	Name string
	UID  string
}

// FilterOptions is a domain model that represents filter options.
type FilterOptions struct {
	Submitter        []FilterOptionUser
	Approver         []FilterOptionUser
	FinanceManager   []FilterOptionUser
	Rejecter         []FilterOptionUser
	Status           []AssetProTxLogStatus
	Token            []string
	BlockChain       []string
	SubmitTimeFrom   *int64
	SubmitTimeTo     *int64
	TransferTimeFrom *int64
	TransferTimeTo   *int64
	AmountFrom       *decimal.Decimal
	AmountTo         *decimal.Decimal
	UsdAmountFrom    *decimal.Decimal
	UsdAmountTo      *decimal.Decimal
}

// AssetProTxLogPendingCount is a domain model that represents asset pro tx log pending count
type AssetProTxLogPendingCount struct {
	Count                     int
	CountAwaitingApprovalSelf int
	CountAwaitingApproval     int
	CountAwaitingRelease      int
}

// AssetProTxLogPendingCountParams is a domain model that represents params for getting asset pro tx log pending count
type AssetProTxLogPendingCountParams struct {
	OrganizationID   int
	UID              string
	IsTrader         bool
	IsApprover       bool
	IsFinanceManager bool
}
