// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: CallbackLogRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=callback_log_repo_mock.go . CallbackLogRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockCallbackLogRepo is a mock of CallbackLogRepo interface.
type MockCallbackLogRepo struct {
	ctrl     *gomock.Controller
	recorder *MockCallbackLogRepoMockRecorder
}

// MockCallbackLogRepoMockRecorder is the mock recorder for MockCallbackLogRepo.
type MockCallbackLogRepoMockRecorder struct {
	mock *MockCallbackLogRepo
}

// NewMockCallbackLogRepo creates a new mock instance.
func NewMockCallbackLogRepo(ctrl *gomock.Controller) *MockCallbackLogRepo {
	mock := &MockCallbackLogRepo{ctrl: ctrl}
	mock.recorder = &MockCallbackLogRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCallbackLogRepo) EXPECT() *MockCallbackLogRepoMockRecorder {
	return m.recorder
}

// CreateCallbackLog mocks base method.
func (m *MockCallbackLogRepo) CreateCallbackLog(arg0 context.Context, arg1 *CallbackLog) (*CallbackLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCallbackLog", arg0, arg1)
	ret0, _ := ret[0].(*CallbackLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCallbackLog indicates an expected call of CreateCallbackLog.
func (mr *MockCallbackLogRepoMockRecorder) CreateCallbackLog(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCallbackLog", reflect.TypeOf((*MockCallbackLogRepo)(nil).CreateCallbackLog), arg0, arg1)
}

// GetCallbackLogByID mocks base method.
func (m *MockCallbackLogRepo) GetCallbackLogByID(arg0 context.Context, arg1 string) (*CallbackLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCallbackLogByID", arg0, arg1)
	ret0, _ := ret[0].(*CallbackLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCallbackLogByID indicates an expected call of GetCallbackLogByID.
func (mr *MockCallbackLogRepoMockRecorder) GetCallbackLogByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallbackLogByID", reflect.TypeOf((*MockCallbackLogRepo)(nil).GetCallbackLogByID), arg0, arg1)
}

// GetCallbackLogs mocks base method.
func (m *MockCallbackLogRepo) GetCallbackLogs(arg0 context.Context, arg1 CallbackLogFilter) ([]*CallbackLog, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCallbackLogs", arg0, arg1)
	ret0, _ := ret[0].([]*CallbackLog)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetCallbackLogs indicates an expected call of GetCallbackLogs.
func (mr *MockCallbackLogRepoMockRecorder) GetCallbackLogs(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCallbackLogs", reflect.TypeOf((*MockCallbackLogRepo)(nil).GetCallbackLogs), arg0, arg1)
}
