//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=callback_executor_mock.go . CallbackExecutor

package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

type CallbackExecutor interface {
	// SendRequest sends a callback request to the specified URL with execution context
	SendRequest(ctx context.Context, url string, payload []byte, execCtx CallbackExecutionContext) (*resty.Response, error)

	// SendTestCallback sends a test callback to the specified URL and returns a log of the request
	SendTestCallback(ctx context.Context, request *TestCallbackRequest, execCtx CallbackExecutionContext) (*CallbackLog, error)
}
