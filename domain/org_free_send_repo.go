package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// OrgFreeSendCountRepo defines the repository interface for organization free send count
type OrgFreeSendCountRepo interface {
	// GetOrgFreeSendCount gets the organization free send count
	GetOrgFreeSendCount(ctx context.Context, orgID int) (*OrgFreeSendCount, *code.KGError)

	// UpdateOrgFreeSendCount updates the organization free send count
	UpdateOrgFreeSendCount(ctx context.Context, count *OrgFreeSendCount) *code.KGError
}
