package domain

import (
	"github.com/shopspring/decimal"
)

type BridgeRecord struct {
	OrgID int
	UID   *string

	FromChain        Chain
	FromAddress      Address
	FromTokenAddress string
	FromAmount       decimal.Decimal
	FromTxHash       string
	ToChain          Chain
	ToAddress        Address
	ToTokenAddress   string
	ToAmount         decimal.Decimal
	ToTxHash         *string

	FeeChain          Chain
	FeeReceiveAddress Address
	FeeTokenAddress   string
	FeeAmount         decimal.Decimal
	FeeTxHash         string
	FeeTokenPrice     decimal.Decimal
	FeeUSD            decimal.Decimal

	ProfitKgMinimumRate decimal.Decimal
	ProfitRate          decimal.Decimal
	ProfitShareRatio    decimal.Decimal
	ProfitMargin        decimal.Decimal
}
