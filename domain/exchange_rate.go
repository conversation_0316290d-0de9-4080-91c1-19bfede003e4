package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// ExchangeRateServiceI is the interface that wraps the basic exchange rate operations.
type ExchangeRateServiceI interface {
	FetchRate(ctx context.Context, base, quote string) (float64, *code.KGError)
	SetRate(ctx context.Context, base, quote string, rate float64) *code.KGError
	GetRate(ctx context.Context, base, quote string) (float64, *code.KGError)
}

// ExchangeRateCacheRepo is the interface that wraps the basic exchange rate cache operations.
type ExchangeRateCacheRepo interface {
	SetRate(ctx context.Context, key string, rate float64) *code.KGError
	GetRate(ctx context.Context, key string) (float64, *code.KGError)
}

// ExchangeRateClientI is the interface that wraps the basic exchange rate client operations.
type ExchangeRateClientI interface {
	FetchRate(ctx context.Context, base, quote string) (float64, error)
}

// ExchangeRatePair is the struct that wraps the base and quote currency.
type ExchangeRatePair struct {
	Base  string
	Quote string
}
