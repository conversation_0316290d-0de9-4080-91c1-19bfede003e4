// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// SendLinkCampaignUserStatusNotStarted is a SendLinkCampaignUserStatus of type not_started.
	SendLinkCampaignUserStatusNotStarted SendLinkCampaignUserStatus = "not_started"
	// SendLinkCampaignUserStatusLinkCreated is a SendLinkCampaignUserStatus of type link_created.
	SendLinkCampaignUserStatusLinkCreated SendLinkCampaignUserStatus = "link_created"
	// SendLinkCampaignUserStatusLinkShared is a SendLinkCampaignUserStatus of type link_shared.
	SendLinkCampaignUserStatusLinkShared SendLinkCampaignUserStatus = "link_shared"
	// SendLinkCampaignUserStatusLinkClaimed is a SendLinkCampaignUserStatus of type link_claimed.
	SendLinkCampaignUserStatusLinkClaimed SendLinkCampaignUserStatus = "link_claimed"
	// SendLinkCampaignUserStatusRewardSent is a SendLinkCampaignUserStatus of type reward_sent.
	SendLinkCampaignUserStatusRewardSent SendLinkCampaignUserStatus = "reward_sent"
)

var ErrInvalidSendLinkCampaignUserStatus = errors.New("not a valid SendLinkCampaignUserStatus")

// String implements the Stringer interface.
func (x SendLinkCampaignUserStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x SendLinkCampaignUserStatus) IsValid() bool {
	_, err := ParseSendLinkCampaignUserStatus(string(x))
	return err == nil
}

var _SendLinkCampaignUserStatusValue = map[string]SendLinkCampaignUserStatus{
	"not_started":  SendLinkCampaignUserStatusNotStarted,
	"link_created": SendLinkCampaignUserStatusLinkCreated,
	"link_shared":  SendLinkCampaignUserStatusLinkShared,
	"link_claimed": SendLinkCampaignUserStatusLinkClaimed,
	"reward_sent":  SendLinkCampaignUserStatusRewardSent,
}

// ParseSendLinkCampaignUserStatus attempts to convert a string to a SendLinkCampaignUserStatus.
func ParseSendLinkCampaignUserStatus(name string) (SendLinkCampaignUserStatus, error) {
	if x, ok := _SendLinkCampaignUserStatusValue[name]; ok {
		return x, nil
	}
	return SendLinkCampaignUserStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidSendLinkCampaignUserStatus)
}
