// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: SendWithRentRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=send_with_rent_repo_mock.go . SendWithRentRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockSendWithRentRepo is a mock of SendWithRentRepo interface.
type MockSendWithRentRepo struct {
	ctrl     *gomock.Controller
	recorder *MockSendWithRentRepoMockRecorder
}

// MockSendWithRentRepoMockRecorder is the mock recorder for MockSendWithRentRepo.
type MockSendWithRentRepoMockRecorder struct {
	mock *MockSendWithRentRepo
}

// NewMockSendWithRentRepo creates a new mock instance.
func NewMockSendWithRentRepo(ctrl *gomock.Controller) *MockSendWithRentRepo {
	mock := &MockSendWithRentRepo{ctrl: ctrl}
	mock.recorder = &MockSendWithRentRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSendWithRentRepo) EXPECT() *MockSendWithRentRepoMockRecorder {
	return m.recorder
}

// CreateSendWithRent mocks base method.
func (m *MockSendWithRentRepo) CreateSendWithRent(arg0 context.Context, arg1 *SendWithRent) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSendWithRent", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSendWithRent indicates an expected call of CreateSendWithRent.
func (mr *MockSendWithRentRepoMockRecorder) CreateSendWithRent(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSendWithRent", reflect.TypeOf((*MockSendWithRentRepo)(nil).CreateSendWithRent), arg0, arg1)
}

// GetSendWithRentByID mocks base method.
func (m *MockSendWithRentRepo) GetSendWithRentByID(arg0 context.Context, arg1 int) (*SendWithRent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSendWithRentByID", arg0, arg1)
	ret0, _ := ret[0].(*SendWithRent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSendWithRentByID indicates an expected call of GetSendWithRentByID.
func (mr *MockSendWithRentRepoMockRecorder) GetSendWithRentByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendWithRentByID", reflect.TypeOf((*MockSendWithRentRepo)(nil).GetSendWithRentByID), arg0, arg1)
}

// UpdateSendWithRent mocks base method.
func (m *MockSendWithRentRepo) UpdateSendWithRent(arg0 context.Context, arg1 *UpdateSendWithRentRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSendWithRent", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSendWithRent indicates an expected call of UpdateSendWithRent.
func (mr *MockSendWithRentRepoMockRecorder) UpdateSendWithRent(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSendWithRent", reflect.TypeOf((*MockSendWithRentRepo)(nil).UpdateSendWithRent), arg0, arg1)
}
