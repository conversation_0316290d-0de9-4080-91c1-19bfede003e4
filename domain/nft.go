package domain

import "time"

type Nft struct {
	Chain              Chain
	ContractAddress    Address
	Type               NftType
	TokenID            string
	Name               string
	Description        *string
	IsSpam             bool
	ImageUrl           string
	ImagePreviewUrl    string
	CollectionSlug     *string
	CollectionName     *string
	CollectionImageUrl *string
	AnimationUrl       *string
	LastPrice          *float64
	LastPriceSymbol    *string
	Creator            *NftCreator
	Traits             []*Trait
	ListingTime        time.Time
}

type NftCreator struct {
	Name     string
	Address  Address
	ImageUrl string
}

type Trait struct {
	Type  string
	Value string
}
