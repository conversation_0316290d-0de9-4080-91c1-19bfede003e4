//go:generate go-enum

package domain

import (
	"time"

	"github.com/ethereum/go-ethereum/common"
)

type EphemeralNote struct {
	ID             string
	ChainID        string
	From           common.Address
	TokenAddress   common.Address
	Amount         string
	DepositTxHash  common.Hash
	ClaimTxHash    *common.Hash
	EphemeralOwner common.Address
	Status         EphemeralNoteStatus
	Symbol         string
	TokenDecimals  uint8
	SenderUID      string
	CreatedAt      time.Time
}

type EnrichedEphemeralNote struct {
	EphemeralNote
	SenderAvatar      string
	SenderDisplayName string
	SenderHandle      string
	TokenImage        string
	TokenPrice        float64
}

// EphemeralNoteStatus represents status of ephemeral note
// ENUM(active, claimed, cancelled)
type EphemeralNoteStatus string

// EphemeralOwner represents an ephemeral owner in the system
type EphemeralOwner struct {
	Address    common.Address
	PrivateKey []byte
	IsUsing    bool
	Type       EphemeralOwnerType
}

// EphemeralOwnerType represents type of ephemeral owner
// ENUM(evm, tron)
type EphemeralOwnerType string
