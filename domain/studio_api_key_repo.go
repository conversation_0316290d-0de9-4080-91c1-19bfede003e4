//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=studio_api_key_repo_mock.go . StudioUserAPIKeyRepo

package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// GetStudioUserAPIKeysParams defines parameters for retrieving studio user API keys with pagination
type GetStudioUserAPIKeysParams struct {
	OrgID    int
	UID      string
	Page     int
	PageSize int
}

// StudioUserAPIKeyRepo interface for studio user API key operations
type StudioUserAPIKeyRepo interface {
	// CreateStudioUserAPIKey creates a new API key
	CreateStudioUserAPIKey(ctx context.Context, apiKey *StudioUserAPIKey) *code.KGError

	// GetStudioUserAPIKeyByHash retrieves an API key by its hash
	GetStudioUserAPIKeyByHash(ctx context.Context, keyHash string) (*StudioUserAPIKey, *code.KGError)

	// ListStudioUserAPIKeys lists all API keys for a user in an organization with params
	ListStudioUserAPIKeys(ctx context.Context, params GetStudioUserAPIKeysParams) ([]*StudioUserAPIKey, int, *code.KGError)

	// DeleteStudioUserAPIKey soft deletes an API key (sets deleted_at)
	DeleteStudioUserAPIKey(ctx context.Context, orgID, apiKeyID int, uid string) *code.KGError

	// UpdateAPIKeyLastUsed updates the last_used_at timestamp for an API key
	UpdateAPIKeyLastUsed(ctx context.Context, apiKeyID int) *code.KGError
}
