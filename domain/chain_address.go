package domain

import (
	"encoding/json"
	"fmt"
)

type ChainAddressMap map[Chain]Address

// Implement UnmarshalJSON for DefaultReceiveWallets
func (w *ChainAddressMap) UnmarshalJSON(data []byte) error {
	var temp map[string]json.RawMessage
	if err := json.Unmarshal(data, &temp); err != nil {
		return err
	}

	result := make(ChainAddressMap)
	for key, value := range temp {
		chain := IDToChain(key)
		if chain == nil {
			return fmt.Errorf("chain %s not found", key)
		}
		var addrStr string
		if err := json.Unmarshal(value, &addrStr); err != nil {
			return err
		}
		if chain.IsTVM() {
			result[chain] = NewTronAddress(addrStr)
		} else if chain.IsEVM() {
			result[chain] = NewEvmAddress(addrStr)
		} else {
			result[chain] = NewStrAddress(addrStr)
		}
	}
	*w = result
	return nil
}

// MarshalJSON implements json.Marshaler
func (w ChainAddressMap) MarshalJSON() ([]byte, error) {
	result := make(map[string]string)
	for chain, addr := range w {
		result[chain.ID()] = addr.String()
	}
	return json.<PERSON>(result)
}
