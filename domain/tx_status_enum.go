// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// TxStatusSuccess is a TxStatus of type success.
	TxStatusSuccess TxStatus = "success"
	// TxStatusFailed is a TxStatus of type failed.
	TxStatusFailed TxStatus = "failed"
	// TxStatusUnknown is a TxStatus of type unknown.
	TxStatusUnknown TxStatus = "unknown"
)

var ErrInvalidTxStatus = errors.New("not a valid TxStatus")

// String implements the Stringer interface.
func (x TxStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x TxStatus) IsValid() bool {
	_, err := ParseTxStatus(string(x))
	return err == nil
}

var _TxStatusValue = map[string]TxStatus{
	"success": TxStatusSuccess,
	"failed":  TxStatusFailed,
	"unknown": TxStatusUnknown,
}

// ParseTxStatus attempts to convert a string to a TxStatus.
func ParseTxStatus(name string) (TxStatus, error) {
	if x, ok := _TxStatusValue[name]; ok {
		return x, nil
	}
	return TxStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidTxStatus)
}
