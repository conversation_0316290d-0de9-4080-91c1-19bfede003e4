//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=asset_fetcher_mock.go . AssetFetcher,TokenAmountsFetcher
package domain

import (
	"context"

	"github.com/shopspring/decimal"
)

type AggregatedAssets struct {
	Tokens []*TokenAmount
	Nfts   []*NftAmount
	Defi   []*DefiAsset
}

type AssetFetcher interface {
	SupportedChains() []Chain
	SupportedTypes() []AssetType
	GetAssets(ctx context.Context, address Address, chains []Chain, types []AssetType) (*AggregatedAssets, error)
}

// TokenAmountsFetcher is a fetcher on given chain to batch fetch token amounts
type TokenAmountsFetcher interface {
	SupportedChains() []Chain
	// FetchAmounts fetches the token amounts for the given tokens. Returns map of wallet to token id to amount
	FetchAmounts(ctx context.Context, chain Chain, tokens []*WalletToken) (map[Address]map[string]decimal.Decimal, error)
}
