package domain

import (
	"context"
	"crypto/ed25519"
	"crypto/sha256"

	"github.com/gagliardetto/solana-go"
	"github.com/mr-tron/base58"
)

// SolanaWallet contains the solana wallet private key and address
type SolanaWallet struct {
	EncryptedPrivateKey string
	Address             string
	privateKey          ed25519.PrivateKey
}

// NewSolanaWallet creates a new solana wallet
func NewSolanaWallet(encryptedPrivateKey string, privateKey ed25519.PrivateKey) *SolanaWallet {
	publicKey := privateKey.Public().(ed25519.PublicKey)
	address := base58.Encode(publicKey[:])
	return &SolanaWallet{
		EncryptedPrivateKey: encryptedPrivateKey,
		Address:             address,
		privateKey:          privateKey,
	}
}

// NewRandomSolanaWallet creates a new random solana wallet
func NewRandomSolanaWallet(ctx context.Context, e PrivateKeyEncryptor) (*SolanaWallet, error) {
	pub, priv, err := ed25519.GenerateKey(nil)
	if err != nil {
		return nil, err
	}
	_ = pub // we'll derive this from private key when needed

	encryptedPrivateKey, err := e.EncryptEd25519(ctx, priv)
	if err != nil {
		return nil, err
	}

	return NewSolanaWallet(encryptedPrivateKey, priv), nil
}

// DeriveFromSalt derives a new solana wallet from a salt deterministically
func (s *SolanaWallet) DeriveFromSalt(ctx context.Context, salt [32]byte, e PrivateKeyEncryptor) (*SolanaWallet, error) {
	seed := append([]byte{}, salt[:]...)
	seed = append(seed, []byte("kryptogo")...)
	seed = append(seed, s.privateKey[:]...)
	hashSeed := sha256.Sum256(seed)
	solPrivateKey := ed25519.NewKeyFromSeed(hashSeed[:])
	encryptedPrivateKey, err := e.EncryptEd25519(ctx, solPrivateKey)
	if err != nil {
		return nil, err
	}
	return NewSolanaWallet(encryptedPrivateKey, solPrivateKey), nil
}

// SignTransaction signs a Solana transaction in place
func (s *SolanaWallet) SignTransaction(tx *solana.Transaction) error {
	_, err := tx.Sign(func(key solana.PublicKey) *solana.PrivateKey {
		requestedAddress := base58.Encode(key[:])
		if requestedAddress == s.Address {
			privKey := solana.PrivateKey(s.privateKey)
			return &privKey
		}
		return nil
	})
	return err
}
