//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=asset_pro_finance_repo_mock.go . AssetProFinanceRepo
package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/shopspring/decimal"
)

// UpsertProfitRateParams is the params for upserting profit rate
type UpsertProfitRateParams struct {
	OrgID            int
	Service          ProfitRateServiceType
	ProfitRate       *decimal.Decimal
	ProfitShareRatio *decimal.Decimal
}

// ProfitRateRepo is for managing profit rates
type ProfitRateRepo interface {
	// GetProfitRates returns profit rates for an organization.
	GetProfitRate(ctx context.Context, orgID int, serviceType ProfitRateServiceType) (*AssetProProfitRate, *code.KGError)
	// GetProfitRates returns profit rates for an organization.
	GetProfitRates(ctx context.Context, orgID int) ([]*AssetProProfitRate, *code.KGError)
	// UpsertProfitRates upserts profit rates for an organization.
	UpsertProfitRate(ctx context.Context, req UpsertProfitRateParams) *code.KGError
}

// AssetProFinanceRepo is the repository for asset pro finance including liquidities and profit rates
type AssetProFinanceRepo interface {
	ProfitRateRepo

	// GetLiquidities returns all liquidities for an organization
	GetLiquidities(ctx context.Context, orgID int) ([]*AssetProLiquidity, *code.KGError)
	// GetLiquidity returns a liquidity for an organization
	GetLiquidity(ctx context.Context, orgID int, liquidityType LiquidityType, chainID string, contractAddress *string) (*AssetProLiquidity, *code.KGError)
	// GetAllLiquidities returns all liquidities
	GetAllLiquidities(ctx context.Context) ([]*AssetProLiquidity, *code.KGError)
}
