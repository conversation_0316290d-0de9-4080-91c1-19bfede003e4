package domain

import (
	"time"
)

// Customer is the customer
type Customer struct {
	OrganizationID          int
	CreatedAt               time.Time
	UpdatedAt               time.Time
	UID                     string
	DisplayName             string
	Handle                  *string
	Name                    *string
	DefaultReceivingWallets []*Wallet
	Wallets                 []*Wallet
	AvatarURL               string
	Email                   *string
	PhoneNumber             string
	KycStatus               KycStatus
	FormStatus              KycStatus
	CDDStatus               KycStatus
	IDVStatus               KycStatus
	KycUpdatedAt            *int64
	RegisterWallet          bool

	// Personal Information
	Country         *string
	Birthday        *string
	NationalID      *string
	PassportNumber  *string
	LineID          *string
	Gender          *string
	PhysicalAddress *string
	LegalName       *string
	CDDRiskScore    *uint8
}

// CountOfComplianceStatistic is the count of compliance statistic.
type CountOfComplianceStatistic struct {
	TotalFormSubmission int
	TotalIDVTasks       int
	TotalCDDTasks       int
}

// CustomerUser360 is the customer user 360
type CustomerUser360 struct {
	UID        string
	Name       *string
	Email      *string
	Phone      *string
	CreatedAt  int64
	ComplyFlow struct {
		KycUpdatedAt *int64
		KycStatus    string
		FormID       *int
	}
}

// Wallet is the wallet
type Wallet struct {
	ChainID string
	Address string
}

// GetCustomersForUser360Params is the params for GetCustomersForUser360
type GetCustomersForUser360Params struct {
	OrgID int
}

// GetCustomerForUser360Params is the params for GetCustomerForUser360
type GetCustomerForUser360Params struct {
	OrgID int
	UID   string
}

// CustomerUser360Detail is the customer user 360 detail
type CustomerUser360Detail struct {
	UID       string
	CreatedAt int64
	Name      *string
	ComplyFlow
}

// ComplyFlow is the comply flow
type ComplyFlow struct {
	KycStatus     string
	PotentialRisk *int
	RiskLevel     *string
	AppliedAt     *int64
	AuditedAt     *int64
	FormID        *int
	Email         *string
	RealName      string
	NationName    string
	NationalID    string
	DOB           *int64
}

// UpdateCustomerOfStudioOrgParams defines the params for updating customer of studio org.
type UpdateCustomerOfStudioOrgParams struct {
	UID            string
	OrganizationID int

	PhotoURL          *string
	Country           *string
	NationalID        *string
	Birthday          *string
	PassportNumber    *string
	Email             *string
	Phone             *string
	LineID            *string
	Gender            *string
	PhysicalAddress   *string
	LegalName         *string
	BankName          *string
	BranchName        *string
	AccountNumber     *string
	AccountHolderName *string
	KYCStatus         *KycStatus
	IDVStatus         *IdvStatus
	CDDRiskScore      *uint8
	SanctionMatched   *bool
}
