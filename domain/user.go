//go:generate go-enum
package domain

import (
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

const (
	// BatchSize batch size
	BatchSize = 100

	// PasswordSaltFrontendOldVersion is the salt hard coded in app, should be deprecated after users have dynamic password_salt_frontend
	PasswordSaltFrontendOldVersion = "b0ebcb03-a72b-4a30-81d9-ff88404f1d4c"
)

// UserInfo is UserData frontend can access
type UserInfo struct {
	UID                    string  `json:"uid"`
	Email                  *string `mapstructure:"email" json:"email,omitempty" firestore:"email,omitempty"`
	Handle                 *string `mapstructure:"handle" json:"handle,omitempty" firestore:"handle,omitempty"`
	DisplayName            string  `mapstructure:"display_name" json:"display_name" firestore:"display_name"`
	PhoneNumber            string  `mapstructure:"phone_number" json:"phone_number" firestore:"phone_number"`
	ReferralCode           string  `mapstructure:"referral_code" json:"referral_code" firestore:"referral_code"`
	TxReferralCode         *string `mapstructure:"tx_referral_code,omitempty" json:"tx_referral_code,omitempty" firestore:"tx_referral_code,omitempty"`
	FcmTokens              `mapstructure:"fcm_tokens" json:"fcm_tokens,omitempty" firestore:"fcm_tokens,omitempty"`
	FcmTokenMap            `mapstructure:"fcm_token_map" json:"fcm_token_map,omitempty" firestore:"fcm_token_map,omitempty"`
	Avatar                 *Avatar                     `mapstructure:"avatar" json:"avatar,omitempty" firestore:"avatar,omitempty"`
	Locale                 string                      `mapstructure:"locale" json:"locale" firestore:"locale"`
	LocaleMap              map[string]string           `mapstructure:"locale_map" json:"locale_map" firestore:"locale_map"`
	ReadAllTimeStamp       *util.CustomTime            `mapstructure:"read_all_timestamp" json:"read_all_timestamp" firestore:"read_all_timestamp"`
	ReadAllTimeStampMap    map[string]*util.CustomTime `mapstructure:"read_all_timestamp_map" json:"read_all_timestamp_map" firestore:"read_all_timestamp_map"`
	SSCreateTime           util.CustomTime             `mapstructure:"SSCreateTime" json:"SSCreateTime,omitempty" firestore:"SSCreateTime,omitempty"` // from Snapshot.CreateTime
	AlchemyNotifyAddress   *string                     `mapstructure:"alchemy_notify_address" json:"alchemy_notify_address,omitempty" firestore:"alchemy_notify_address,omitempty"`
	DeviceIdentifier       *string                     `mapstructure:"device_identifier" json:"device_identifier,omitempty" firestore:"device_identifier,omitempty"`
	AppVersion             *string                     `mapstructure:"app_version" json:"app_version,omitempty" firestore:"app_version,omitempty"`
	AppVersionMap          map[string]string           `json:"app_version_map"`
	CreatedAt              *int64                      `mapstructure:"created_at" json:"created_at,omitempty" firestore:"created_at,omitempty"`
	DeletedAt              *int64                      `mapstructure:"deleted_at" json:"deleted_at,omitempty" firestore:"deleted_at,omitempty"`
	PrivacyPolicyAgreement *PrivacyPolicyAgreement     `mapstructure:"privacy_policy_agreement" json:"privacy_policy_agreement,omitempty" firestore:"privacy_policy_agreement,omitempty"`
	Bio                    *string                     `mapstructure:"bio" json:"bio,omitempty" firestore:"bio,omitempty"`
	Twitter                *string                     `mapstructure:"twitter" json:"twitter,omitempty" firestore:"twitter,omitempty"`
	Youtube                *string                     `mapstructure:"youtube" json:"youtube,omitempty" firestore:"youtube,omitempty"`
	Instagram              *string                     `mapstructure:"instagram" json:"instagram,omitempty" firestore:"instagram,omitempty"`
	Discord                *string                     `mapstructure:"discord" json:"discord,omitempty" firestore:"discord,omitempty"`
	CustomLink             *string                     `mapstructure:"custom_link" json:"custom_link,omitempty" firestore:"custom_link,omitempty"`
	HideSpamNft            *bool                       `mapstructure:"hide_spam_nft" json:"hide_spam_nft,omitempty" firestore:"hide_spam_nft,omitempty"`
	HasLinkedGoogle        *bool                       `mapstructure:"has_linked_google" json:"has_linked_google,omitempty" firestore:"has_linked_google,omitempty"`
	IsRandomPassword       *bool                       `mapstructure:"is_random_password" json:"is_random_password,omitempty" firestore:"is_random_password,omitempty"`
}

// UserData use separate endpoint to access
type UserData struct {
	UserInfo             `mapstructure:",squash"`
	BitcoinAddress       *string            `mapstructure:"bitcoin_address" json:"bitcoin_address,omitempty" firestore:"bitcoin_address,omitempty"`
	EthereumAddress      *string            `mapstructure:"ethereum_address" json:"ethereum_address,omitempty" firestore:"ethereum_address,omitempty"`
	SolanaAddress        *string            `mapstructure:"solana_address" json:"solana_address,omitempty" firestore:"solana_address,omitempty"`
	TronAddress          *string            `mapstructure:"tron_address" json:"tron_address,omitempty" firestore:"tron_address,omitempty"`
	EncryptSalt          *string            `mapstructure:"encrypt_salt" json:"encrypt_salt,omitempty" firestore:"encrypt_salt,omitempty"`
	EncryptedMnemonic    *string            `mapstructure:"encrypted_mnemonic" json:"encrypted_mnemonic,omitempty" firestore:"encrypted_mnemonic,omitempty"`
	Wallets              *Wallets           `mapstructure:"wallets" json:"wallets,omitempty" firestore:"wallets,omitempty"`
	KycState             *string            `mapstructure:"kyc_state" json:"kyc_state,omitempty" firestore:"kyc_state,omitempty"`
	VaultData            *VaultData         `mapstructure:"vault_data" json:"vault_data,omitempty" firestore:"vault_data,omitempty"`
	Password             *string            `mapstructure:"password" json:"password,omitempty" firestore:"password,omitempty"`
	PasswordSalt         *string            `mapstructure:"password_salt" json:"password_salt,omitempty" firestore:"password_salt,omitempty"`
	PasswordSaltFrontend *string            `mapstructure:"password_salt_frontend" json:"password_salt_frontend,omitempty" firestore:"password_salt_frontend,omitempty"`
	ShareKey             *string            `mapstructure:"share_key" json:"share_key,omitempty" firestore:"share_key,omitempty"`
	GoogleAccessTokens   *GoogleAccessToken `mapstructure:"google_access_tokens" json:"google_access_tokens,omitempty" firestore:"google_access_tokens,omitempty"`
}

// Avatar avatar
type Avatar struct {
	AvatarURL       string `mapstructure:"avatar_url" json:"avatar_url" firestore:"avatar_url"`
	ChainID         string `mapstructure:"chain_id" json:"chain_id" firestore:"chain_id"`
	ContractAddress string `mapstructure:"contract_address" json:"contract_address" firestore:"contract_address"`
	TokenID         string `mapstructure:"token_id" json:"token_id" firestore:"token_id"`
}

// Wallets wallets
type Wallets struct {
	DefaultReceiveWallets ChainAddressMap `mapstructure:"default_receive_wallets" json:"default_receive_wallets,omitempty" firestore:"default_receive_wallets,omitempty"`
	EvmWallets            []*UserWallet   `mapstructure:"evm_wallets" json:"evm_wallets,omitempty" firestore:"evm_wallets,omitempty"`
	WalletGroups          []*WalletGroup  `mapstructure:"wallet_groups" json:"wallet_groups,omitempty" firestore:"wallet_groups,omitempty"`
	Wallets               []*UserWallet   `mapstructure:"wallets" json:"wallets,omitempty" firestore:"wallets,omitempty"`
	RetrieveMnemonic      bool            `mapstructure:"retrieve_mnemonic" json:"retrieve_mnemonic" firestore:"retrieve_mnemonic"`
}

// UserWallet user wallet
type UserWallet struct {
	Address             string      `mapstructure:"address" json:"address" firestore:"address"`
	AddressIndex        int         `mapstructure:"address_index" json:"address_index" firestore:"address_index"`
	CreateTimestamp     *int64      `mapstructure:"create_timestamp" json:"create_timestamp,omitempty" firestore:"create_timestamp"`
	DisplayName         string      `mapstructure:"display_name" json:"display_name,omitempty" firestore:"display_name"`
	EncryptedPrivateKey string      `mapstructure:"encrypted_private_key" json:"encrypted_private_key,omitempty" firestore:"encrypted_private_key,omitempty"`
	ImportTimestamp     *int64      `mapstructure:"import_timestamp" json:"import_timestamp,omitempty" firestore:"import_timestamp"`
	Chain               string      `mapstructure:"chain" json:"chain" firestore:"chain"`
	IconURL             string      `mapstructure:"icon_url" json:"icon_url,omitempty" firestore:"icon_url"`
	WalletType          *WalletType `mapstructure:"wallet_type" json:"wallet_type,omitempty" firestore:"wallet_type"`
}

func (wallet *UserWallet) IsObserver() bool {
	if wallet == nil || wallet.WalletType == nil {
		return false
	}
	return *(wallet.WalletType) == WalletTypeObserver
}

// WalletType wallet type
// ENUM(normal, observer)
type WalletType string

// WalletGroup wallet group
type WalletGroup struct {
	ID                  int           `json:"-"`
	BtcWallets          []*UserWallet `mapstructure:"btc_wallets" json:"btc_wallets,omitempty" firestore:"btc_wallets,omitempty"`
	EvmWallets          []*UserWallet `mapstructure:"evm_wallets" json:"evm_wallets,omitempty" firestore:"evm_wallets,omitempty"`
	SolanaWallets       []*UserWallet `mapstructure:"solana_wallets" json:"solana_wallets,omitempty" firestore:"solana_wallets,omitempty"`
	TronWallets         []*UserWallet `mapstructure:"tron_wallets" json:"tron_wallets,omitempty" firestore:"tron_wallets,omitempty"`
	CreateTimestamp     *int64        `mapstructure:"create_timestamp" json:"create_timestamp,omitempty" firestore:"create_timestamp"`
	ImportTimestamp     *int64        `mapstructure:"import_timestamp" json:"import_timestamp,omitempty" firestore:"import_timestamp"`
	EncryptedSeedPhrase string        `mapstructure:"encrypted_seed_phrase" json:"encrypted_seed_phrase" firestore:"encrypted_seed_phrase"`
	DisplayName         string        `mapstructure:"display_name" json:"display_name,omitempty" firestore:"display_name"`
	IconURL             string        `mapstructure:"icon_url" json:"icon_url,omitempty" firestore:"icon_url"`
}

// VaultData vault data
type VaultData struct {
	AccountPublicKey           string `mapstructure:"account_public_key" json:"account_public_key,omitempty" firestore:"account_public_key,omitempty"`
	EncryptSalt                string `mapstructure:"encrypt_salt" json:"encrypt_salt,omitempty" firestore:"encrypt_salt,omitempty"`
	EncryptedAccountPrivateKey string `mapstructure:"encrypted_account_private_key" json:"encrypted_account_private_key,omitempty" firestore:"encrypted_account_private_key,omitempty"`
	HashVersion                string `mapstructure:"hash_version" json:"hash_version,omitempty" firestore:"hash_version,omitempty"`
}

// PrivacyPolicyAgreement privacy policy agreement
type PrivacyPolicyAgreement struct {
	AgreementTime    time.Time `mapstructure:"agreement_time" json:"agreement_time,omitempty" firestore:"agreement_time"`
	AgreementVersion string    `mapstructure:"agreement_version" json:"agreement_version,omitempty" firestore:"agreement_version"`
}

// GoogleAccessToken google access tokens
type GoogleAccessToken struct {
	AccessToken  string `mapstructure:"access_token" json:"access_token,omitempty" firestore:"access_token,omitempty"`
	RefreshToken string `mapstructure:"refresh_token" json:"refresh_token,omitempty" firestore:"refresh_token,omitempty"`
}

// FcmTokenMap fcm token map: client id -> fcm tokens
type FcmTokenMap map[string]FcmTokens

// FcmTokens fcm tokens
type FcmTokens []FcmToken

// FcmToken data structure
type FcmToken struct {
	Timestamp util.CustomTime `mapstructure:"timestamp" json:"timestamp" firestore:"timestamp"`
	Token     string          `mapstructure:"token" json:"token" firestore:"token"`
}

// AnyValid check if any fcm token is valid
func (fcmTokenMap *FcmTokenMap) AnyValid() bool {
	if fcmTokenMap == nil {
		return false
	}
	for _, fcmTokens := range *fcmTokenMap {
		if fcmTokens.anyValid() {
			return true
		}
	}
	return false
}

func (fcmTokens *FcmTokens) anyValid() bool {
	if fcmTokens == nil {
		return false
	}
	for _, fcmToken := range *fcmTokens {
		if fcmToken.isValid() {
			return true
		}
	}
	return false
}

func (fcmToken *FcmToken) isValid() bool {
	if fcmToken.Token == "" {
		return false
	}
	if fcmToken.Timestamp.Time.Add(time.Hour * 24 * 60).Before(time.Now()) {
		return false
	}
	return true
}

// FilterClientFcmTokens filter client fcm tokens
func FilterClientFcmTokens(user *UserData, clientID string) {
	if user.FcmTokenMap == nil {
		return
	}
	user.FcmTokens = user.FcmTokenMap[clientID]
}

// GetAvatarURL get avatar url
func (user *UserData) GetAvatarURL() string {
	if user.Avatar == nil {
		return ""
	}
	return user.Avatar.AvatarURL
}

// GetLocale get locale
func (user *UserData) GetLocale(clientID string) string {
	if len(clientID) == 0 {
		return user.Locale
	}
	if user.LocaleMap == nil {
		return user.Locale
	}
	if locale, ok := user.LocaleMap[clientID]; ok {
		return locale
	}
	return user.Locale
}
