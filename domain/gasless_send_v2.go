//go:generate go-enum
package domain

import (
	"time"

	"github.com/shopspring/decimal"
)

// GaslessSendV2 is a gasless send request from user
type GaslessSendV2 struct {
	ID           int
	OrgID        int
	UID          string
	Chain        Chain
	From         TronAddress
	Recipient    TronAddress
	TokenAddress TronAddress
	Amount       decimal.Decimal
	Fee          decimal.Decimal // unit is in token
	FeeUSD       decimal.Decimal // unit is in USD
	SignedTxs    []string        // must have two txs, both are signed token transfer from user.

	// following fields aren't supplied by user
	Status              GaslessSendStatus
	EnergyRentCost      *float64 // unit is in TRX
	ActualCostUsd       *float64
	NativeTokenPrice    *float64
	TokenPrice          *float64
	GasFaucetTxHash     *string
	TokenTransferTxHash *string
	FeeTransferTxHash   *string
	RetryCount          int
	EstimatedFinishAt   time.Time
	CreatedAt           time.Time
}

// UpdateGaslessSendV2Request is used to update gasless send request's status in repo
type UpdateGaslessSendV2Request struct {
	ID                  int
	Status              *GaslessSendStatus
	EnergyRentCost      *float64
	NativeTokenPrice    *float64
	TokenPrice          *float64
	ActualCostUsd       *float64
	GasFaucetTxHash     *string
	TokenTransferTxHash *string
	FeeTransferTxHash   *string
	RetryCount          *int
	ProfitMarginRate    *decimal.Decimal
	ProfitShareRatio    *decimal.Decimal
	ProfitMargin        *decimal.Decimal
}
