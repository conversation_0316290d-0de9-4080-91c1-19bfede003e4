package domain

import (
	"context"
	"errors"
	"time"
)

var ErrTokenPriceNotFound = errors.New("token price not found")

type AssetPriceRepo interface {
	// GetAssetPrice should return ErrAssetNotFound if asset not found
	GetAssetPrice(ctx context.Context, chainID string, contractAddress string) (float64, error)
	// GetNativeAssetPrice should return ErrAssetNotFound if asset not found
	GetNativeAssetPrice(ctx context.Context, chainID string) (float64, error)

	// GetTokenPrice returns the price for the given token id. It can be main token or a contract address
	GetTokenPrice(ctx context.Context, chain Chain, tokenID string) (float64, error)
	// BatchGetTokenPrices returns the prices for the given tokens. Returns a map of token to price
	BatchGetTokenPrices(ctx context.Context, tokens []ChainToken) (map[ChainToken]float64, error)

	// GetTokenPricesIn24H returns the prices in 24h (ascending timestamp order) for the given token. It can be main token or a contract address
	GetTokenPricesIn24H(ctx context.Context, chain Chain, tokenID string) ([]*PricePoint, error)
	// BatchGetTokenPricesIn24H returns the prices in 24h (ascending timestamp order) for the given tokens. Returns a slice of prices for each token. It can be main token or a contract address
	BatchGetTokenPricesIn24H(ctx context.Context, tokens []ChainToken) (map[ChainToken][]*PricePoint, error)
}

type CoingeckoID string
type Price float64

type AssetPriceUpdateRepo interface {
	// GetAssetPriceHistories gets the asset price histories for the given chain and id
	BatchCreateAssetPriceHistories(ctx context.Context, assetPrices []*TokenPrice) error
	// DeleteAssetPriceHistories deletes the asset price histories for the given created at time
	DeleteAssetPriceHistories(ctx context.Context, createdAt time.Time) error
	// SetAssetPrices sets the asset prices for the given chain and id
	SetAssetPrices(ctx context.Context, prices []*TokenPrice) error
}

type AssetPriceQueueInfo struct {
	LastAdded  *time.Time
	LastPopped *time.Time
}

type AssetPriceQueueRepo interface {
	// AddAssetPricesUpdateJob adds a job(addresses) to update asset prices
	AddAssetPricesUpdateJob(ctx context.Context, walletAddresses []ChainAddress) error
	// PopAllAssetPriceUpdateJobs pops all addresses from the asset price update job queue
	PopAllAssetPriceUpdateJobs(ctx context.Context) ([]ChainAddress, error)

	// GetAssetPriceQueueInfo gets the asset price queue info
	GetAssetPriceQueueInfo(ctx context.Context) (*AssetPriceQueueInfo, error)
	// SetAssetPriceQueueInfo sets the asset price queue info
	SetAssetPriceQueueInfo(ctx context.Context, info AssetPriceQueueInfo) error
}

type RealtimeTokenPriceTask struct {
	ChainToken ChainToken `json:"chain_token"`
	RetryCount int        `json:"retry_count"`
}

type RealtimeTokenPriceResponse struct {
	ChainToken     ChainToken `json:"chain_token"`
	PriceUSD       Price      `json:"price_usd"`
	LastUpdateTime time.Time  `json:"last_update_time"`
}

type RealtimeAssetPriceSetRepo interface {
	// Task key
	//
	// For price requester
	AddRealtimeTokenPriceTask(ctx context.Context, info []RealtimeTokenPriceTask) error
	// For price updater
	AcquireRealtimeTokenPriceTask(ctx context.Context, limit int) ([]RealtimeTokenPriceTask, error)

	// Price Info Serving key
	//
	// For price updater
	AddRealtimeTokenPriceResponse(ctx context.Context, info []RealtimeTokenPriceResponse) error
	// For price requester, service level query this multiple times
	// Returns hot prices (≤3s), warm prices (3s-30s), and error
	GetRealtimeTokenPrice(ctx context.Context, chainTokens []ChainToken) ([]RealtimeTokenPriceResponse, []RealtimeTokenPriceResponse, error)
}
