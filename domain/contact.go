package domain

// ListContactsParams list contacts params
type ListContactsParams struct {
	OwnerID string
}

// Contact is the contact
type Contact struct {
	UID             string
	Handle          *string
	UserID          *string
	PhoneNumber     *string
	Nickname        *string
	Name            *string
	Addresses       map[Chain]string
	AvatarURL       *string
	IsManualAddress *bool
	IsAddedByQR     *bool
	IsFavorite      bool
}

// UpsertContactsParams upsert contact params
type UpsertContactsParams struct {
	Items []*UpsertContactItem
}

// UpsertContactItem upsert contact item
type UpsertContactItem struct {
	OwnerID         string
	UID             *string
	UserID          *string
	PhoneNumber     *string
	Nickname        *string
	Name            *string
	Addresses       map[Chain]string
	IsManualAddress *bool
	IsAddedByQR     *bool
	IsFavorite      *bool
}
