// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: UniversalSwapRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=universal_swap_repo_mock.go . UniversalSwapRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockUniversalSwapRepo is a mock of UniversalSwapRepo interface.
type MockUniversalSwapRepo struct {
	ctrl     *gomock.Controller
	recorder *MockUniversalSwapRepoMockRecorder
}

// MockUniversalSwapRepoMockRecorder is the mock recorder for MockUniversalSwapRepo.
type MockUniversalSwapRepoMockRecorder struct {
	mock *MockUniversalSwapRepo
}

// NewMockUniversalSwapRepo creates a new mock instance.
func NewMockUniversalSwapRepo(ctrl *gomock.Controller) *MockUniversalSwapRepo {
	mock := &MockUniversalSwapRepo{ctrl: ctrl}
	mock.recorder = &MockUniversalSwapRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUniversalSwapRepo) EXPECT() *MockUniversalSwapRepoMockRecorder {
	return m.recorder
}

// CreateUniversalSwap mocks base method.
func (m *MockUniversalSwapRepo) CreateUniversalSwap(arg0 context.Context, arg1 *UniversalSwap) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUniversalSwap", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUniversalSwap indicates an expected call of CreateUniversalSwap.
func (mr *MockUniversalSwapRepoMockRecorder) CreateUniversalSwap(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUniversalSwap", reflect.TypeOf((*MockUniversalSwapRepo)(nil).CreateUniversalSwap), arg0, arg1)
}

// GetUniversalSwapByDestinationWallet mocks base method.
func (m *MockUniversalSwapRepo) GetUniversalSwapByDestinationWallet(arg0 context.Context, arg1, arg2 string) (*UniversalSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUniversalSwapByDestinationWallet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*UniversalSwap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUniversalSwapByDestinationWallet indicates an expected call of GetUniversalSwapByDestinationWallet.
func (mr *MockUniversalSwapRepoMockRecorder) GetUniversalSwapByDestinationWallet(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUniversalSwapByDestinationWallet", reflect.TypeOf((*MockUniversalSwapRepo)(nil).GetUniversalSwapByDestinationWallet), arg0, arg1, arg2)
}

// GetUniversalSwapByID mocks base method.
func (m *MockUniversalSwapRepo) GetUniversalSwapByID(arg0 context.Context, arg1 int) (*UniversalSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUniversalSwapByID", arg0, arg1)
	ret0, _ := ret[0].(*UniversalSwap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUniversalSwapByID indicates an expected call of GetUniversalSwapByID.
func (mr *MockUniversalSwapRepoMockRecorder) GetUniversalSwapByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUniversalSwapByID", reflect.TypeOf((*MockUniversalSwapRepo)(nil).GetUniversalSwapByID), arg0, arg1)
}

// GetUniversalSwapsByUID mocks base method.
func (m *MockUniversalSwapRepo) GetUniversalSwapsByUID(arg0 context.Context, arg1 string) ([]*UniversalSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUniversalSwapsByUID", arg0, arg1)
	ret0, _ := ret[0].([]*UniversalSwap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUniversalSwapsByUID indicates an expected call of GetUniversalSwapsByUID.
func (mr *MockUniversalSwapRepoMockRecorder) GetUniversalSwapsByUID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUniversalSwapsByUID", reflect.TypeOf((*MockUniversalSwapRepo)(nil).GetUniversalSwapsByUID), arg0, arg1)
}

// UpdateUniversalSwap mocks base method.
func (m *MockUniversalSwapRepo) UpdateUniversalSwap(arg0 context.Context, arg1 *UpdateUniversalSwapRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUniversalSwap", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUniversalSwap indicates an expected call of UpdateUniversalSwap.
func (mr *MockUniversalSwapRepoMockRecorder) UpdateUniversalSwap(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUniversalSwap", reflect.TypeOf((*MockUniversalSwapRepo)(nil).UpdateUniversalSwap), arg0, arg1)
}
