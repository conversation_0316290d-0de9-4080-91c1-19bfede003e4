//go:generate mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=universal_swap_repo_mock.go . UniversalSwapRepo

package domain

import (
	"context"
)

// UniversalSwapRepo defines the interface for universal swap persistence
type UniversalSwapRepo interface {
	// CreateUniversalSwap creates a new universal swap request
	CreateUniversalSwap(ctx context.Context, swap *UniversalSwap) (int, error)

	// UpdateUniversalSwap updates an existing universal swap request
	UpdateUniversalSwap(ctx context.Context, update *UpdateUniversalSwapRequest) error

	// GetUniversalSwapByID returns a universal swap request by ID
	// Should return ErrRecordNotFound if request not found
	GetUniversalSwapByID(ctx context.Context, id int) (*UniversalSwap, error)

	// GetUniversalSwapsByUID returns all universal swap requests for a user
	GetUniversalSwapsByUID(ctx context.Context, uid string) ([]*UniversalSwap, error)

	// GetUniversalSwapByDestinationWallet returns a universal swap request by destination chain ID and wallet address.
	// It should return ErrRecordNotFound if no such swap is found.
	GetUniversalSwapByDestinationWallet(ctx context.Context, destChainID string, destWalletAddress string) (*UniversalSwap, error)
}
