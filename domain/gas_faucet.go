package domain

import "time"

// GasFaucet is gas faucet request from user
type GasFaucet struct {
	ID            int
	OrgID         int
	UID           string
	ChainID       string
	ReceiveWallet string
	GasAmount     int64    // It's gas limit for EVM compatible chains. For non-EVM chains, it's gas fee in native token
	GasPriceGwei  *float64 // Only meaningful for EVM compatible chains
	CreatedAt     time.Time

	// Fields that are only meaningful after transaction confirmed
	Status       *TxStatus
	TxHash       *string
	TotalCost    *float64
	TotalCostUsd *float64
}

// GasFaucetUpdate is used to update gas faucet request's status in repo
type GasFaucetUpdate struct {
	ID           int
	Status       *TxStatus
	TxHash       *string
	TotalCost    *float64
	TotalCostUsd *float64
}
