package domain

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// ContactRepo interface for contact repository
type ContactRepo interface {
	ListContacts(ctx context.Context, params *ListContactsParams) ([]*Contact, *code.KGError)
	UpsertContacts(ctx context.Context, params *UpsertContactsParams) ([]*Contact, *code.KGError)
	DeleteContact(ctx context.Context, ownerID, contactID string) *code.KGError
}
