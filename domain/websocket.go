package domain

import "time"

type WsEvent struct {
	RequestUUID    []byte
	UID            string
	ChainID        string
	JsonrpcID      int64
	JsonrpcVersion string
	Method         string
	Params         string
	Result         *string
	Error          *string
	Canceled       bool
	CreatedAt      time.Time
}

type WsEventUpdate struct {
	RequestUUID []byte
	Method      string
	Result      *string
	Error       *string
}
