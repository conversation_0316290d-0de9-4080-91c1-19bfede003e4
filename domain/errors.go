package domain

import "errors"

// Common errors
var ErrRecordNotFound = errors.New("record not found")

var ErrUnsupportedChain = errors.New("unsupported chain")

var ErrUnsupportedAssetType = errors.New("unsupported asset type")

var Err<PERSON>ockNotAcquired = errors.New("lock not acquired")

var ErrBlockNotFound = errors.New("block not found")

var ErrAddressNotFound = errors.New("address not found")

var ErrInsufficientBalance = errors.New("insufficient balance")
