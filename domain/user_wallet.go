package domain

import (
	"fmt"
	"strings"

	"github.com/samber/lo"
)

// DefaultAddresses get default receive addresses by chain
func (user *UserData) DefaultAddresses(includeTestnet bool) map[Chain]Address {
	chains := Chains
	if !includeTestnet {
		chains = lo.Filter(chains, func(chain Chain, _ int) bool {
			return !chain.IsTestnet()
		})
	}

	defaultAddresses := map[Chain]Address{}
	var defaultEVMAddress, defaultTVMAddress Address
	if user.Wallets == nil {
		// legacy wallet format. Not extensible for new non-evm chains.
		var btcAddress, ethAddress, solAddress, tronAddress Address
		if user.BitcoinAddress != nil {
			btcAddress = NewStrAddress(*user.BitcoinAddress)
		}
		if user.EthereumAddress != nil {
			ethAddress = NewEvmAddress(*user.EthereumAddress)
			defaultEVMAddress = ethAddress
		}
		if user.SolanaAddress != nil {
			solAddress = NewStrAddress(*user.SolanaAddress)
		}
		if user.TronAddress != nil {
			tronAddress = NewTronAddress(*user.TronAddress)
			defaultTVMAddress = tronAddress
		}
		wallets := map[Chain]Address{
			Bitcoin:  btcAddress,
			Ethereum: ethAddress,
			Solana:   solAddress,
			Tron:     tronAddress,
		}
		for _, chain := range chains {
			if wallet, ok := wallets[chain]; ok {
				defaultAddresses[chain] = wallet
			}
		}
	} else if user.Wallets != nil && user.Wallets.DefaultReceiveWallets != nil {
		wallets := user.Wallets.DefaultReceiveWallets
		for _, chain := range chains {
			if wallet, ok := wallets[chain]; ok {
				defaultAddresses[chain] = wallet
			}
		}
		defaultEVMAddress, defaultTVMAddress = wallets[Ethereum], wallets[Tron]
	}

	// fallback to eth or tron address for evm / tvm chains.
	for _, chain := range chains {
		if addr, ok := defaultAddresses[chain]; !ok || addr == nil || addr.String() == "" {
			if chain.IsEVM() {
				defaultAddresses[chain] = defaultEVMAddress
			}
			if chain.IsTVM() {
				defaultAddresses[chain] = defaultTVMAddress
			}
		}

		// if the return value is still nil, set as empty string
		if addr, ok := defaultAddresses[chain]; !ok || addr == nil {
			defaultAddresses[chain] = NewAddressByChain(chain, "")
		}
	}
	return defaultAddresses
}

type WalletWithPath struct {
	Address    Address
	Path       string
	IsObserver bool
}

func (user *UserData) AllWalletsWithPath() map[Chain][]WalletWithPath {
	walletsWithPath := make(map[Chain][]WalletWithPath)
	if user == nil {
		return walletsWithPath
	}

	// handle legacy wallets
	if user.Wallets == nil {
		return user.LegacyWalletsWithPath()
	}

	appendWallet := func(chain Chain, wallets []*UserWallet, groupIndex int, walletType string) {
		prefix := ""
		if groupIndex >= 0 {
			prefix = fmt.Sprintf("group-%d-", groupIndex)
		}
		for walletIndex, wallet := range wallets {
			if wallet.Address == "" {
				continue
			}
			path := fmt.Sprintf("%s%s-%d", prefix, walletType, walletIndex)
			walletsWithPath[chain] = append(walletsWithPath[chain], WalletWithPath{
				Address:    NewAddressByChain(chain, wallet.Address),
				Path:       path,
				IsObserver: wallet.IsObserver(),
			})
		}
	}

	for _, chain := range Chains {
		// process wallet group
		for groupIndex, walletGroup := range user.Wallets.WalletGroups {
			if walletGroup == nil {
				continue
			}
			if chain.IsEVM() {
				appendWallet(chain, walletGroup.EvmWallets, groupIndex, "evm")
			}
			if chain.IsTVM() {
				appendWallet(chain, walletGroup.TronWallets, groupIndex, "tron")
			}
			switch chain {
			case Bitcoin:
				appendWallet(Bitcoin, walletGroup.BtcWallets, groupIndex, "btc")
			case Solana:
				appendWallet(Solana, walletGroup.SolanaWallets, groupIndex, "sol")
			}
		}

		// process evm wallets
		if chain.IsEVM() {
			appendWallet(chain, user.Wallets.EvmWallets, -1, "evm")
		} else {
			// process single wallet
			for index, wallet := range user.Wallets.Wallets {
				if chain.ID() == wallet.Chain || (chain.IsTVM() && Tron.ID() == wallet.Chain) {
					// For shasta it can get Tron's addresses
					walletsWithPath[chain] = append(walletsWithPath[chain], WalletWithPath{
						Address:    NewAddressByChain(chain, wallet.Address),
						Path:       fmt.Sprintf("single-%d", index),
						IsObserver: wallet.IsObserver(),
					})
				}
			}
		}
	}
	return walletsWithPath
}

func (user *UserData) LegacyWalletsWithPath() map[Chain][]WalletWithPath {
	walletsWithPath := make(map[Chain][]WalletWithPath)
	if user.EthereumAddress != nil && *user.EthereumAddress != "" {
		for _, chain := range Chains {
			if chain.IsEVM() {
				walletsWithPath[chain] = append(walletsWithPath[chain], WalletWithPath{
					Address:    NewStrAddress(*user.EthereumAddress),
					Path:       "legacy-evm",
					IsObserver: false,
				})
			}
		}
	}
	if user.BitcoinAddress != nil && *user.BitcoinAddress != "" {
		walletsWithPath[Bitcoin] = append(walletsWithPath[Bitcoin], WalletWithPath{
			Address:    NewStrAddress(*user.BitcoinAddress),
			Path:       "legacy-btc",
			IsObserver: false,
		})
	}
	if user.SolanaAddress != nil && *user.SolanaAddress != "" {
		walletsWithPath[Solana] = append(walletsWithPath[Solana], WalletWithPath{
			Address:    NewStrAddress(*user.SolanaAddress),
			Path:       "legacy-sol",
			IsObserver: false,
		})
	}
	if user.TronAddress != nil && *user.TronAddress != "" {
		walletsWithPath[Tron] = append(walletsWithPath[Tron], WalletWithPath{
			Address:    NewStrAddress(*user.TronAddress),
			Path:       "legacy-tron",
			IsObserver: false,
		})
	}
	return walletsWithPath
}

// AddressesByPath get all addresses with path prefixed by given path, e.g. group-0 path contains group-0-evm-0, group-0-btc-0
func (user *UserData) AddressesByPath(path string, excludeObserver bool) map[Chain][]Address {
	addresses := make(map[Chain][]Address)
	if user == nil || user.Wallets == nil {
		return addresses
	}
	walletsWithPath := user.AllWalletsWithPath()
	for chain, wallets := range walletsWithPath {
		for _, wallet := range wallets {
			if excludeObserver && wallet.IsObserver {
				continue
			}
			if strings.HasPrefix(wallet.Path, path) {
				addresses[chain] = append(addresses[chain], wallet.Address)
			}
		}
	}
	uniqueAddresses := make(map[Chain][]Address)
	for chain, addrs := range addresses {
		uniqueAddresses[chain] = lo.Uniq(addrs)
	}
	return uniqueAddresses
}

func (user *UserData) AddressesByChains(path string, chainIDs []string, excludeObserver bool) map[string][]string {
	chainAddresses := make(map[string][]string, 0)
	allAddresses := user.AddressesByPath(path, excludeObserver)
	for _, chainID := range chainIDs {
		chain := IDToChain(chainID)
		if chain == nil {
			continue
		}
		for _, addr := range allAddresses[chain] {
			chainAddresses[chainID] = append(chainAddresses[chainID], addr.String())
		}
	}
	uniqueAddresses := make(map[string][]string)
	for chainID, addrs := range chainAddresses {
		uniqueAddresses[chainID] = lo.Uniq(addrs)
	}
	return uniqueAddresses
}

// GetObserverWalletAddresses get user's observed wallet addresses by chain IDs
func (user *UserData) GetObserverWalletAddresses(chainIDs []string) map[string][]string {
	chainAddresses := make(map[string][]string, 0)
	if user.Wallets == nil {
		return chainAddresses
	}

	walletsWithPath := user.AllWalletsWithPath()
	for chain, wallets := range walletsWithPath {
		for _, wallet := range wallets {
			if wallet.IsObserver {
				chainAddresses[chain.ID()] = append(chainAddresses[chain.ID()], wallet.Address.String())
			}
		}
	}
	uniqueAddresses := make(map[string][]string)
	for _, chainID := range chainIDs {
		if addrs, ok := chainAddresses[chainID]; ok {
			uniqueAddresses[chainID] = lo.Uniq(addrs)
		}
	}
	return uniqueAddresses
}

func (user *UserData) AddressByPath(path string, excludeObserver bool) map[string]Address {
	addresses := make(map[string]Address, 0)
	allWallets := user.AllWalletsWithPath()
	for _, wallets := range allWallets {
		for _, wallet := range wallets {
			if wallet.IsObserver && excludeObserver {
				continue
			}
			if strings.HasPrefix(wallet.Path, path) {
				addresses[wallet.Path] = wallet.Address
			}
		}
	}
	return addresses
}

// UniqueAddressesByChains get user wallet addresses
func (user *UserData) UniqueAddressesByChains(path string, chainIDs []string, excludeObserver bool) ([]string, error) {
	chainWalletAddresses := user.AddressesByChains(path, chainIDs, excludeObserver)
	ownerAddresses := make([]string, 0, len(chainWalletAddresses))
	for _, addresses := range chainWalletAddresses {
		ownerAddresses = append(ownerAddresses, addresses...)
	}
	return lo.Uniq(ownerAddresses), nil
}

// UniqueAddresses get user unique addresses
func (user *UserData) UniqueAddresses(excludeObserver bool) []string {
	addressesByChain := user.AddressesByPath("", excludeObserver)
	uniqueAddresses := make([]string, 0, len(addressesByChain))
	for _, addresses := range addressesByChain {
		for _, address := range addresses {
			uniqueAddresses = append(uniqueAddresses, address.String())
		}
	}
	return lo.Uniq(uniqueAddresses)
}

// GetAddressDiff returns the addresses that are in after but not in before
func GetAddressDiff(before, after map[Chain][]Address) map[Chain][]Address {
	diff := make(map[Chain][]Address)
	for chain, afterAddresses := range after {
		beforeAddresses := before[chain]
		_, diffAddresses := lo.Difference(beforeAddresses, afterAddresses)
		if len(diffAddresses) > 0 {
			diff[chain] = diffAddresses
		}
	}
	return diff
}
