package domain

import (
	"context"
	"time"
)

// TokenBuySignal represents a buy signal for a Solana token
type TokenBuySignal struct {
	ID               int       `json:"id"`
	TokenAddress     string    `json:"token_address"`
	SmartWalletCount int       `json:"smart_wallet_count"`
	BuyEntryPrice    float64   `json:"buy_entry_price"`
	HighestPrice     float64   `json:"highest_price"`
	EmitTime         time.Time `json:"emit_time"`
	TelegramLink     string    `json:"telegram_link"`
	WinRate          float64   `json:"win_rate"`
	AverageHolding   float64   `json:"average_holding"`
	AverageWinRate   float64   `json:"average_win_rate"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// TokenSellSignal represents a sell signal for a Solana token
type TokenSellSignal struct {
	ID           int        `json:"id"`
	TokenAddress string     `json:"token_address"`
	EmitTime     time.Time  `json:"emit_time"`
	TelegramLink string     `json:"telegram_link"`
	HighestGain  float64    `json:"highest_gain"`
	BuyEntryTime *time.Time `json:"buy_entry_time"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// TokenSignalRepo defines the interface for token signal repository operations
type TokenSignalRepo interface {
	// Buy signal operations
	UpsertBuySignal(ctx context.Context, signal *TokenBuySignal) error
	UpsertBuySignalBatch(ctx context.Context, signals []*TokenBuySignal) error
	GetBuySignalByTokenAddress(ctx context.Context, tokenAddress string) (*TokenBuySignal, error)
	ListBuySignals(ctx context.Context) ([]*TokenBuySignal, error)
	DeleteBuySignalByTokenAddress(ctx context.Context, tokenAddress string) error

	// Sell signal operations
	AddSellSignal(ctx context.Context, signal *TokenSellSignal) error
	ListSellSignals(ctx context.Context, limit int) ([]*TokenSellSignal, error)
	DeleteSellSignalByTokenAddress(ctx context.Context, tokenAddress string) error

	// Signal stats operations
	GetPastTopSignals(ctx context.Context) ([]*TokenSellSignal, error)
	GetLast7d2xStats(ctx context.Context) (totalCount int, twoXCount int, avgGain float64, err error)
	GetTodayTopSignal(ctx context.Context) (*TokenSellSignal, *TokenBuySignal, error)
}
