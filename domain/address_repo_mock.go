// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: AddressRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=address_repo_mock.go . AddressRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockAddressRepo is a mock of AddressRepo interface.
type MockAddressRepo struct {
	ctrl     *gomock.Controller
	recorder *MockAddressRepoMockRecorder
}

// MockAddressRepoMockRecorder is the mock recorder for MockAddressRepo.
type MockAddressRepoMockRecorder struct {
	mock *MockAddressRepo
}

// NewMockAddressRepo creates a new mock instance.
func NewMockAddressRepo(ctrl *gomock.Controller) *MockAddressRepo {
	mock := &MockAddressRepo{ctrl: ctrl}
	mock.recorder = &MockAddressRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAddressRepo) EXPECT() *MockAddressRepoMockRecorder {
	return m.recorder
}

// BatchCheckAddressIsActivePaymentAddress mocks base method.
func (m *MockAddressRepo) BatchCheckAddressIsActivePaymentAddress(arg0 context.Context, arg1 Chain, arg2 []Address) (map[Address]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckAddressIsActivePaymentAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[Address]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckAddressIsActivePaymentAddress indicates an expected call of BatchCheckAddressIsActivePaymentAddress.
func (mr *MockAddressRepoMockRecorder) BatchCheckAddressIsActivePaymentAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckAddressIsActivePaymentAddress", reflect.TypeOf((*MockAddressRepo)(nil).BatchCheckAddressIsActivePaymentAddress), arg0, arg1, arg2)
}

// BatchCheckAddressOwnedByUser mocks base method.
func (m *MockAddressRepo) BatchCheckAddressOwnedByUser(arg0 context.Context, arg1 Chain, arg2 []Address) (map[Address]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckAddressOwnedByUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[Address]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckAddressOwnedByUser indicates an expected call of BatchCheckAddressOwnedByUser.
func (mr *MockAddressRepoMockRecorder) BatchCheckAddressOwnedByUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckAddressOwnedByUser", reflect.TypeOf((*MockAddressRepo)(nil).BatchCheckAddressOwnedByUser), arg0, arg1, arg2)
}

// CheckAddressOwnedByUser mocks base method.
func (m *MockAddressRepo) CheckAddressOwnedByUser(arg0 context.Context, arg1 Chain, arg2 Address) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAddressOwnedByUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAddressOwnedByUser indicates an expected call of CheckAddressOwnedByUser.
func (mr *MockAddressRepoMockRecorder) CheckAddressOwnedByUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAddressOwnedByUser", reflect.TypeOf((*MockAddressRepo)(nil).CheckAddressOwnedByUser), arg0, arg1, arg2)
}

// GetUsersForAddressNotification mocks base method.
func (m *MockAddressRepo) GetUsersForAddressNotification(arg0 context.Context, arg1, arg2, arg3 string) ([]*UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersForAddressNotification", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersForAddressNotification indicates an expected call of GetUsersForAddressNotification.
func (mr *MockAddressRepoMockRecorder) GetUsersForAddressNotification(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersForAddressNotification", reflect.TypeOf((*MockAddressRepo)(nil).GetUsersForAddressNotification), arg0, arg1, arg2, arg3)
}
