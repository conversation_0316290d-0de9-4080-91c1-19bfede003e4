// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// TrcTypeTrc10 is a TrcType of type trc10.
	TrcTypeTrc10 TrcType = "trc10"
	// TrcTypeTrc20 is a TrcType of type trc20.
	TrcTypeTrc20 TrcType = "trc20"
)

var ErrInvalidTrcType = errors.New("not a valid TrcType")

// String implements the Stringer interface.
func (x TrcType) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x TrcType) IsValid() bool {
	_, err := ParseTrcType(string(x))
	return err == nil
}

var _TrcTypeValue = map[string]TrcType{
	"trc10": TrcTypeTrc10,
	"trc20": TrcTypeTrc20,
}

// ParseTrcType attempts to convert a string to a TrcType.
func ParseTrcType(name string) (TrcType, error) {
	if x, ok := _TrcTypeValue[name]; ok {
		return x, nil
	}
	return TrcType(""), fmt.Errorf("%s is %w", name, ErrInvalidTrcType)
}
