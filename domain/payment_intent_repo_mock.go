// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: PaymentIntentRepo)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=payment_intent_repo_mock.go . PaymentIntentRepo
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"
	time "time"

	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockPaymentIntentRepo is a mock of PaymentIntentRepo interface.
type MockPaymentIntentRepo struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentIntentRepoMockRecorder
}

// MockPaymentIntentRepoMockRecorder is the mock recorder for MockPaymentIntentRepo.
type MockPaymentIntentRepoMockRecorder struct {
	mock *MockPaymentIntentRepo
}

// NewMockPaymentIntentRepo creates a new mock instance.
func NewMockPaymentIntentRepo(ctrl *gomock.Controller) *MockPaymentIntentRepo {
	mock := &MockPaymentIntentRepo{ctrl: ctrl}
	mock.recorder = &MockPaymentIntentRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentIntentRepo) EXPECT() *MockPaymentIntentRepoMockRecorder {
	return m.recorder
}

// AcquireLock mocks base method.
func (m *MockPaymentIntentRepo) AcquireLock(arg0 context.Context, arg1 string, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLock indicates an expected call of AcquireLock.
func (mr *MockPaymentIntentRepoMockRecorder) AcquireLock(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLock", reflect.TypeOf((*MockPaymentIntentRepo)(nil).AcquireLock), arg0, arg1, arg2)
}

// AcquireLockWithRetry mocks base method.
func (m *MockPaymentIntentRepo) AcquireLockWithRetry(arg0 context.Context, arg1 string, arg2, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLockWithRetry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLockWithRetry indicates an expected call of AcquireLockWithRetry.
func (mr *MockPaymentIntentRepoMockRecorder) AcquireLockWithRetry(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLockWithRetry", reflect.TypeOf((*MockPaymentIntentRepo)(nil).AcquireLockWithRetry), arg0, arg1, arg2, arg3)
}

// CreatePaymentIntent mocks base method.
func (m *MockPaymentIntentRepo) CreatePaymentIntent(arg0 context.Context, arg1 *PaymentIntent) (*PaymentIntent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePaymentIntent", arg0, arg1)
	ret0, _ := ret[0].(*PaymentIntent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePaymentIntent indicates an expected call of CreatePaymentIntent.
func (mr *MockPaymentIntentRepoMockRecorder) CreatePaymentIntent(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentIntent", reflect.TypeOf((*MockPaymentIntentRepo)(nil).CreatePaymentIntent), arg0, arg1)
}

// GetPaymentIntentByChainAddress mocks base method.
func (m *MockPaymentIntentRepo) GetPaymentIntentByChainAddress(arg0 context.Context, arg1 ChainAddress) (*PaymentIntent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentIntentByChainAddress", arg0, arg1)
	ret0, _ := ret[0].(*PaymentIntent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentIntentByChainAddress indicates an expected call of GetPaymentIntentByChainAddress.
func (mr *MockPaymentIntentRepoMockRecorder) GetPaymentIntentByChainAddress(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentIntentByChainAddress", reflect.TypeOf((*MockPaymentIntentRepo)(nil).GetPaymentIntentByChainAddress), arg0, arg1)
}

// GetPaymentIntentByID mocks base method.
func (m *MockPaymentIntentRepo) GetPaymentIntentByID(arg0 context.Context, arg1 string) (*PaymentIntent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentIntentByID", arg0, arg1)
	ret0, _ := ret[0].(*PaymentIntent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentIntentByID indicates an expected call of GetPaymentIntentByID.
func (mr *MockPaymentIntentRepoMockRecorder) GetPaymentIntentByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentIntentByID", reflect.TypeOf((*MockPaymentIntentRepo)(nil).GetPaymentIntentByID), arg0, arg1)
}

// GetPaymentIntentStats mocks base method.
func (m *MockPaymentIntentRepo) GetPaymentIntentStats(arg0 context.Context, arg1 int) (*PaymentIntentStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentIntentStats", arg0, arg1)
	ret0, _ := ret[0].(*PaymentIntentStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentIntentStats indicates an expected call of GetPaymentIntentStats.
func (mr *MockPaymentIntentRepoMockRecorder) GetPaymentIntentStats(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentIntentStats", reflect.TypeOf((*MockPaymentIntentRepo)(nil).GetPaymentIntentStats), arg0, arg1)
}

// GetPaymentIntentStatsWithTimeRange mocks base method.
func (m *MockPaymentIntentRepo) GetPaymentIntentStatsWithTimeRange(arg0 context.Context, arg1 GetPaymentIntentStatsParams) (*PaymentIntentStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentIntentStatsWithTimeRange", arg0, arg1)
	ret0, _ := ret[0].(*PaymentIntentStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentIntentStatsWithTimeRange indicates an expected call of GetPaymentIntentStatsWithTimeRange.
func (mr *MockPaymentIntentRepoMockRecorder) GetPaymentIntentStatsWithTimeRange(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentIntentStatsWithTimeRange", reflect.TypeOf((*MockPaymentIntentRepo)(nil).GetPaymentIntentStatsWithTimeRange), arg0, arg1)
}

// GetPaymentIntents mocks base method.
func (m *MockPaymentIntentRepo) GetPaymentIntents(arg0 context.Context, arg1 GetPaymentIntentsParams) ([]*PaymentIntent, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentIntents", arg0, arg1)
	ret0, _ := ret[0].([]*PaymentIntent)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPaymentIntents indicates an expected call of GetPaymentIntents.
func (mr *MockPaymentIntentRepoMockRecorder) GetPaymentIntents(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentIntents", reflect.TypeOf((*MockPaymentIntentRepo)(nil).GetPaymentIntents), arg0, arg1)
}

// GetPendingIntents mocks base method.
func (m *MockPaymentIntentRepo) GetPendingIntents(arg0 context.Context) ([]*PaymentIntent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPendingIntents", arg0)
	ret0, _ := ret[0].([]*PaymentIntent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingIntents indicates an expected call of GetPendingIntents.
func (mr *MockPaymentIntentRepoMockRecorder) GetPendingIntents(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingIntents", reflect.TypeOf((*MockPaymentIntentRepo)(nil).GetPendingIntents), arg0)
}

// GetPendingIntentsByPaymentAddresses mocks base method.
func (m *MockPaymentIntentRepo) GetPendingIntentsByPaymentAddresses(arg0 context.Context, arg1 Chain, arg2 []Address) (map[Address]*PaymentIntent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPendingIntentsByPaymentAddresses", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[Address]*PaymentIntent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingIntentsByPaymentAddresses indicates an expected call of GetPendingIntentsByPaymentAddresses.
func (mr *MockPaymentIntentRepoMockRecorder) GetPendingIntentsByPaymentAddresses(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingIntentsByPaymentAddresses", reflect.TypeOf((*MockPaymentIntentRepo)(nil).GetPendingIntentsByPaymentAddresses), arg0, arg1, arg2)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockPaymentIntentRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockPaymentIntentRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockPaymentIntentRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// ReleaseLock mocks base method.
func (m *MockPaymentIntentRepo) ReleaseLock(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseLock", arg0, arg1)
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockPaymentIntentRepoMockRecorder) ReleaseLock(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockPaymentIntentRepo)(nil).ReleaseLock), arg0, arg1)
}

// UpdatePaymentIntent mocks base method.
func (m *MockPaymentIntentRepo) UpdatePaymentIntent(arg0 context.Context, arg1 string, arg2 *PaymentIntentUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePaymentIntent", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePaymentIntent indicates an expected call of UpdatePaymentIntent.
func (mr *MockPaymentIntentRepoMockRecorder) UpdatePaymentIntent(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePaymentIntent", reflect.TypeOf((*MockPaymentIntentRepo)(nil).UpdatePaymentIntent), arg0, arg1, arg2)
}
