// Code generated by MockGen. DO NOT EDIT.
// Source: domain/websocket_repo.go
//
// Generated by this command:
//
//	mockgen -source=domain/websocket_repo.go -destination=domain/websocket_repo_mock.go -package=domain
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockWebsocketRepo is a mock of WebsocketRepo interface.
type MockWebsocketRepo struct {
	ctrl     *gomock.Controller
	recorder *MockWebsocketRepoMockRecorder
}

// MockWebsocketRepoMockRecorder is the mock recorder for MockWebsocketRepo.
type MockWebsocketRepoMockRecorder struct {
	mock *MockWebsocketRepo
}

// NewMockWebsocketRepo creates a new mock instance.
func NewMockWebsocketRepo(ctrl *gomock.Controller) *MockWebsocketRepo {
	mock := &MockWebsocketRepo{ctrl: ctrl}
	mock.recorder = &MockWebsocketRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWebsocketRepo) EXPECT() *MockWebsocketRepoMockRecorder {
	return m.recorder
}

// GetUIDsByAddresses mocks base method.
func (m *MockWebsocketRepo) GetUIDsByAddresses(ctx context.Context, addresses []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUIDsByAddresses", ctx, addresses)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUIDsByAddresses indicates an expected call of GetUIDsByAddresses.
func (mr *MockWebsocketRepoMockRecorder) GetUIDsByAddresses(ctx, addresses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUIDsByAddresses", reflect.TypeOf((*MockWebsocketRepo)(nil).GetUIDsByAddresses), ctx, addresses)
}

// GetWsEvent mocks base method.
func (m *MockWebsocketRepo) GetWsEvent(uuid []byte) (*WsEvent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWsEvent", uuid)
	ret0, _ := ret[0].(*WsEvent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWsEvent indicates an expected call of GetWsEvent.
func (mr *MockWebsocketRepoMockRecorder) GetWsEvent(uuid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWsEvent", reflect.TypeOf((*MockWebsocketRepo)(nil).GetWsEvent), uuid)
}

// InsertWsEvent mocks base method.
func (m *MockWebsocketRepo) InsertWsEvent(event *WsEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertWsEvent", event)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertWsEvent indicates an expected call of InsertWsEvent.
func (mr *MockWebsocketRepoMockRecorder) InsertWsEvent(event any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertWsEvent", reflect.TypeOf((*MockWebsocketRepo)(nil).InsertWsEvent), event)
}

// SaveWsEventResult mocks base method.
func (m *MockWebsocketRepo) SaveWsEventResult(update *WsEventUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveWsEventResult", update)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveWsEventResult indicates an expected call of SaveWsEventResult.
func (mr *MockWebsocketRepoMockRecorder) SaveWsEventResult(update any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveWsEventResult", reflect.TypeOf((*MockWebsocketRepo)(nil).SaveWsEventResult), update)
}

// SetWsEventCanceled mocks base method.
func (m *MockWebsocketRepo) SetWsEventCanceled(requestUUID []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetWsEventCanceled", requestUUID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetWsEventCanceled indicates an expected call of SetWsEventCanceled.
func (mr *MockWebsocketRepoMockRecorder) SetWsEventCanceled(requestUUID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetWsEventCanceled", reflect.TypeOf((*MockWebsocketRepo)(nil).SetWsEventCanceled), requestUUID)
}
