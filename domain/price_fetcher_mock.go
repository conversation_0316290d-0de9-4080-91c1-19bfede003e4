// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/domain (interfaces: PriceFetcher)
//
// Generated by this command:
//
//	mockgen -package=domain -self_package=github.com/kryptogo/kg-wallet-backend/domain -destination=price_fetcher_mock.go . PriceFetcher
//

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockPriceFetcher is a mock of PriceFetcher interface.
type MockPriceFetcher struct {
	ctrl     *gomock.Controller
	recorder *MockPriceFetcherMockRecorder
}

// MockPriceFetcherMockRecorder is the mock recorder for MockPriceFetcher.
type MockPriceFetcherMockRecorder struct {
	mock *MockPriceFetcher
}

// NewMockPriceFetcher creates a new mock instance.
func NewMockPriceFetcher(ctrl *gomock.Controller) *MockPriceFetcher {
	mock := &MockPriceFetcher{ctrl: ctrl}
	mock.recorder = &MockPriceFetcherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPriceFetcher) EXPECT() *MockPriceFetcherMockRecorder {
	return m.recorder
}

// GetPrices mocks base method.
func (m *MockPriceFetcher) GetPrices(arg0 context.Context, arg1 []CoingeckoID) (map[CoingeckoID]Price, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrices", arg0, arg1)
	ret0, _ := ret[0].(map[CoingeckoID]Price)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrices indicates an expected call of GetPrices.
func (mr *MockPriceFetcherMockRecorder) GetPrices(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrices", reflect.TypeOf((*MockPriceFetcher)(nil).GetPrices), arg0, arg1)
}

// GetPricesByContract mocks base method.
func (m *MockPriceFetcher) GetPricesByContract(arg0 context.Context, arg1 []ChainToken) (map[ChainToken]Price, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPricesByContract", arg0, arg1)
	ret0, _ := ret[0].(map[ChainToken]Price)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPricesByContract indicates an expected call of GetPricesByContract.
func (mr *MockPriceFetcherMockRecorder) GetPricesByContract(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPricesByContract", reflect.TypeOf((*MockPriceFetcher)(nil).GetPricesByContract), arg0, arg1)
}

// PricesByContractSupportedChains mocks base method.
func (m *MockPriceFetcher) PricesByContractSupportedChains() []Chain {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PricesByContractSupportedChains")
	ret0, _ := ret[0].([]Chain)
	return ret0
}

// PricesByContractSupportedChains indicates an expected call of PricesByContractSupportedChains.
func (mr *MockPriceFetcherMockRecorder) PricesByContractSupportedChains() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PricesByContractSupportedChains", reflect.TypeOf((*MockPriceFetcher)(nil).PricesByContractSupportedChains))
}
