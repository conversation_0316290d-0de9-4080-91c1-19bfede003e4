package domain

import (
	"context"
	"math/big"

	"github.com/go-resty/resty/v2"
)

// GetNftSalesParams is the params struct for GetNftSales
type GetNftSalesParams struct {
	ChainID         string
	ContractAddress string
	TokenID         string
	Order           string
	Limit           int
}

// GetNftSalesResp is the response struct for GetNftSales
type GetNftSalesResp struct {
	Results []GetNftSalesData `json:"results"`
}

// GetNftSalesData is the response struct for GetNftSales
type GetNftSalesData struct {
	Price *big.Int `json:"price"`
}

// NftMarketClientI is the interface for NftMarketClient
type NftMarketClientI interface {
	GetNftSales(ctx context.Context, params *GetNftSalesParams) (*GetNftSalesData, *resty.Response, error)
}
