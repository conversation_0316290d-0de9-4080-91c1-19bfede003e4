//go:generate go-enum
package domain

import (
	"time"
)

// OAuthApplicationType represents the type of OAuth application
// ENUM(web_app, mobile_wallet, complyflow, linebot, market)
type OAuthApplicationType string

// LoginMethod represents login method for end user
// ENUM(email, phone, google)
type LoginMethod string

// LoginMethodList list of login methods
type LoginMethodList []LoginMethod

// ToStrings converts login methods to string slice
func (l LoginMethodList) ToStrings() []string {
	var result []string
	for _, m := range l {
		result = append(result, string(m))
	}
	return result
}

// CustomAuthVerifierType represents the type of custom auth verifier
// ENUM(google, auth0, jwt, custom_twm)
type CustomAuthVerifierType string

// Verifier token verifier interface for custom auth application. Returns user ID if token is valid
type Verifier interface {
	Verify(token string) (string, error)
}

// TokenIssuer for issuing KG token after custom auth login
type TokenIssuer interface {
	IssueToken(uid string) (string, error)
}

// Application .
type Application struct {
	ClientID     string
	ClientSecret string
	Name         string
	Domain       string
}

// OAuthApplication .
type OAuthApplication struct {
	Application
	IsPrivileged         bool
	SupportAddress       string
	MainLogo             string
	AppStoreLink         string
	GooglePlayLink       string
	PrivacyPolicyLink    string
	SendbirdAppID        string
	SendbirdApiTokenName string
	LineChannelID        *string
	SquareLogo           *string
	WideLogo             *string
	Scopes               []string
	LoginMethods         LoginMethodList
	Type                 *OAuthApplicationType // FIXME: Should be non-nil after fixing data in db
	TwilioVerifySID      *string
	CreatedAt            time.Time
}

// CustomAuthApplication .
type CustomAuthApplication struct {
	Application
	VerifierType            CustomAuthVerifierType
	GoogleVerifierParams    *GoogleVerifierParams
	Auth0VerifierParams     *Auth0VerifierParams
	JwtVerifierParams       *JwtVerifierParams
	CustomTwmVerifierParams *CustomTwmVerifierParams
}

// GoogleVerifierParams .
type GoogleVerifierParams struct {
	GoogleClientID string
}

// Auth0VerifierParams .
type Auth0VerifierParams struct {
	Auth0ClientID string
	Auth0Domain   string
}

// JwtVerifierParams .
type JwtVerifierParams struct {
	JwkURL   string
	Audience string
	Issuer   string
	UIDField string
}

// CustomTwmVerifierParams .
type CustomTwmVerifierParams struct {
	Domain string
}
