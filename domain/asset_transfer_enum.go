// Code generated by go-enum DO NOT EDIT.
// Version: 0.6.0
// Revision: 919e61c0174b91303753ee3898569a01abb32c97
// Build Date: 2023-12-18T15:54:43Z
// Built By: goreleaser

package domain

import (
	"errors"
	"fmt"
)

const (
	// AssetProTxLogStatusAwaitingApproval is a AssetProTxLogStatus of type awaiting_approval.
	AssetProTxLogStatusAwaitingApproval AssetProTxLogStatus = "awaiting_approval"
	// AssetProTxLogStatusAwaitingRelease is a AssetProTxLogStatus of type awaiting_release.
	AssetProTxLogStatusAwaitingRelease AssetProTxLogStatus = "awaiting_release"
	// AssetProTxLogStatusSending is a AssetProTxLogStatus of type sending.
	AssetProTxLogStatusSending AssetProTxLogStatus = "sending"
	// AssetProTxLogStatusSendSuccess is a AssetProTxLogStatus of type send_success.
	AssetProTxLogStatusSendSuccess AssetProTxLogStatus = "send_success"
	// AssetProTxLogStatusSendFailed is a AssetProTxLogStatus of type send_failed.
	AssetProTxLogStatusSendFailed AssetProTxLogStatus = "send_failed"
	// AssetProTxLogStatusRejected is a AssetProTxLogStatus of type rejected.
	AssetProTxLogStatusRejected AssetProTxLogStatus = "rejected"
)

var ErrInvalidAssetProTxLogStatus = errors.New("not a valid AssetProTxLogStatus")

// String implements the Stringer interface.
func (x AssetProTxLogStatus) String() string {
	return string(x)
}

// IsValid provides a quick way to determine if the typed value is
// part of the allowed enumerated values
func (x AssetProTxLogStatus) IsValid() bool {
	_, err := ParseAssetProTxLogStatus(string(x))
	return err == nil
}

var _AssetProTxLogStatusValue = map[string]AssetProTxLogStatus{
	"awaiting_approval": AssetProTxLogStatusAwaitingApproval,
	"awaiting_release":  AssetProTxLogStatusAwaitingRelease,
	"sending":           AssetProTxLogStatusSending,
	"send_success":      AssetProTxLogStatusSendSuccess,
	"send_failed":       AssetProTxLogStatusSendFailed,
	"rejected":          AssetProTxLogStatusRejected,
}

// ParseAssetProTxLogStatus attempts to convert a string to a AssetProTxLogStatus.
func ParseAssetProTxLogStatus(name string) (AssetProTxLogStatus, error) {
	if x, ok := _AssetProTxLogStatusValue[name]; ok {
		return x, nil
	}
	return AssetProTxLogStatus(""), fmt.Errorf("%s is %w", name, ErrInvalidAssetProTxLogStatus)
}
