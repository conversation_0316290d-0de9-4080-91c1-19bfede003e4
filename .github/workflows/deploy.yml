concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    tags:
      - v[0-9]+.[0-9]+.[0-9]+
    branches:
      - main
      - release-*
  workflow_dispatch:
    inputs:
      env:
        description: "Environment"
        required: true
        type: choice
        options:
          - dev
          - staging
        default: "dev"
      version:
        description: "Version string (e.g., R40). Required for staging environments and no effect for dev environment."
        required: false
        type: string

name: Build and Deploy to Cloud Run

jobs:
  # dev jobs
  build-dev:
    runs-on: [self-hosted, Linux, golang]
    if: ${{ github.ref == 'refs/heads/main' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - id: "auth"
        uses: "google-github-actions/auth@v0"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY_DEV }}"
      - name: Fix permission
        run: git config --global --add safe.directory $PWD
      # assumes self hosted runner has go installed
      # - uses: actions/setup-go@v4
      #   with:
      #     go-version-file: "go.mod"
      - name: Build
        run: |
          export PATH="$PATH:/usr/local/go/bin"
          which go
          go version
          make build
  deploy-dev:
    needs: build-dev
    runs-on: [self-hosted, Linux]
    container:
      image: kryptogodev/ci-build-base:latest
    if: ${{ github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.env == 'dev') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - id: "auth"
        uses: "google-github-actions/auth@v0"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY_DEV }}"
      - name: Fix permission
        run: git config --global --add safe.directory $PWD
      - name: Deploy
        run: |
          make deploy CLUSTER_ENV=dev
      - name: Cleanup
        if: always()
        run: ./bin/cleanup-github.sh

  # staging jobs
  build-staging:
    runs-on: [self-hosted, Linux, golang]
    if: ${{ startsWith(github.ref, 'refs/heads/release-') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - id: "auth"
        uses: "google-github-actions/auth@v0"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY_STAGING }}"
      - name: Fix permission
        run: git config --global --add safe.directory $PWD
      # assumes self hosted runner has go installed
      # - uses: actions/setup-go@v4
      #   with:
      #     go-version-file: "go.mod"
      - name: Build
        run: |
          export PATH="$PATH:/usr/local/go/bin"
          make build VERSION=$GITHUB_REF_NAME

  check-vuln-staging:
    needs: build-staging
    if: ${{ startsWith(github.ref, 'refs/heads/release-') }}
    uses: ./.github/workflows/check-vulnerability.yml
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY_STAGING }}
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_STAGING }}
    with:
      VERSION: "${{ github.ref_name }}"

  deploy-staging:
    needs: check-vuln-staging
    runs-on: [self-hosted, Linux]
    container:
      image: kryptogodev/ci-build-base:latest
    if: ${{ startsWith(github.ref, 'refs/heads/release-') || (github.event_name == 'workflow_dispatch' && github.event.inputs.env == 'staging') }}
    steps:
      - name: Check version input
        if: ${{ github.event_name == 'workflow_dispatch' }}
        run: |
          if [ -z "${{ github.event.inputs.version }}" ]; then
            echo "Version input is required for staging environment"
            exit 1
          elif ! [[ "${{ github.event.inputs.version }}" =~ ^R[0-9]+$ ]]; then
            echo "Version input must be in the format 'RXX' where XX is a number"
            exit 1
          fi
      - name: Checkout
        uses: actions/checkout@v3
      - id: "auth"
        uses: "google-github-actions/auth@v0"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY_STAGING }}"
      - name: Fix permission
        run: git config --global --add safe.directory $PWD
      - name: Check current deployed version
        if: ${{ github.event_name != 'workflow_dispatch' }}
        run: ./bin/check-image-version.sh ${{ github.ref_name }}
      - name: Deploy
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            VERSION="release-${{ github.event.inputs.version }}"
          else
            VERSION="${{ github.ref_name }}"
          fi
          make deploy CLUSTER_ENV=staging VERSION=$VERSION
      - name: Cleanup
        if: always()
        run: ./bin/cleanup-github.sh

  # prod jobs
  build-prod:
    runs-on: [self-hosted, Linux, golang]
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - id: "auth"
        uses: "google-github-actions/auth@v0"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY }}"
      - name: Fix permission
        run: git config --global --add safe.directory $PWD
      # assumes self hosted runner has go installed
      # - uses: actions/setup-go@v4
      #   with:
      #     go-version-file: "go.mod"
      - name: Build
        run: |
          export PATH="$PATH:/usr/local/go/bin"
          make build VERSION=$GITHUB_REF_NAME

  check-vuln-prod:
    needs: build-prod
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    uses: ./.github/workflows/check-vulnerability.yml
    secrets:
      GCP_SA_KEY: ${{ secrets.GCP_SA_KEY }}
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
    with:
      VERSION: "${{ github.ref_name }}"

  deploy-prod:
    needs: check-vuln-prod
    runs-on: [self-hosted, Linux]
    container:
      image: kryptogodev/ci-build-base:latest
    if: ${{ startsWith(github.ref, 'refs/tags/v') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - id: "auth"
        uses: "google-github-actions/auth@v0"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY }}"
      - name: Fix permission
        run: git config --global --add safe.directory $PWD
      - name: Deploy
        run: |
          VERSION="${{ github.ref_name }}"
          make deploy CLUSTER_ENV=prod VERSION=$VERSION
      - name: Cleanup
        if: always()
        run: ./bin/cleanup-github.sh

  # trigger e2e test job
  trigger-integration-test:
    runs-on: ubuntu-latest
    needs: deploy-dev
    if: ${{ github.ref == 'refs/heads/main' }}
    steps:
      - name: Trigger Integration Test
        run: |
          curl -X POST --fail -u "${{ secrets.PAT_USERNAME }}:${{ secrets.PAT_TOKEN }}" -H "Accept: application/vnd.github.everest-preview+json" -H "Content-Type: application/json" https://api.github.com/repos/kryptogo/integration-test/dispatches --data '{"event_type": "run_frontend_e2e_tests"}'
