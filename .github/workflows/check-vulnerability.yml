name: "Check Vulnerability"
on:
  workflow_call:
    inputs:
      VERSION:
        type: string
        required: false
    secrets:
      GCP_SA_KEY:
        required: true
      SLACK_WEBHOOK_URL:
        required: true
jobs:
  check-vulnerability:
    runs-on: [self-hosted, Linux]
    container:
      image: kryptogodev/ci-build-base:latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - id: "auth"
        uses: "google-github-actions/auth@v0"
        with:
          credentials_json: "${{ secrets.GCP_SA_KEY }}"
      - name: Check vulnerability
        id: check-vuln
        run: |
          VERSION="${{ inputs.VERSION }}" make check-vulnerability > vuln-output.txt
      - name: Set failure message
        if: |
          failure() && steps.check-vuln.outcome == 'failure'
        run: |
          # Use # as temporary newline character
          echo "fail_message=$(cat vuln-output.txt | tr '\n' '#')" >> $GITHUB_ENV
      - name: Send Slack notification on failure
        if: |
          failure() && steps.check-vuln.outcome == 'failure'
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          fields: workflow,job,commit,repo,ref,author,took
          custom_payload: |
            {
              text: "Failed Container Image Security Check",
              attachments: [{
                color: 'danger',
                title: `${process.env.AS_WORKFLOW}`,
                text: `${process.env.AS_JOB} (${process.env.AS_COMMIT}) of ${process.env.AS_REPO} ${{ job.status }}\n` + "${{ env.fail_message }}".replace(/#/g, '\n')
              }]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
