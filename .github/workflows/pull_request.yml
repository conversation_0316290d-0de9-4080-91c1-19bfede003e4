concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - main
      - release-*

name: Static check and test

jobs:
  static-check-test:
    runs-on: [self-hosted, Linux, golang]
    container:
      image: kryptogodev/ci-build-base:latest
    defaults:
      run:
        shell: bash
    services:
      firebase:
        image: kryptogodev/firebase-emulator:latest
        env:
          GCP_PROJECT: testing-kryptogo-wallet-app
          FIRESTORE_EMULATOR_PORT: 8080
          UI_EMULATOR_PORT: 4000
          AUTH_EMULATOR_PORT: 9099
          STORAGE_EMULATOR_PORT: 9199
          ENABLE_UI: false
        options: >-
          --health-cmd "curl http://localhost:8080"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 5
          --health-start-period 30s
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: wallet
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
          --tmpfs /var/lib/mysql:rw,noexec,nosuid,size=4096m
      redis:
        image: redis:7.0
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Run shellcheck
        run: |
          make shellcheck

      - name: Setup DB
        run: |
          . config/local-ci.sh
          CI=true make mig-db

      - name: Run test
        run: |
          export CGO_ENABLED=1
          . config/local-ci.sh
          export PATH=$PATH:/usr/local/go/bin
          make test

      - name: Cleanup
        if: always()
        run: ./bin/cleanup-github.sh
