package asset

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func Test_GetRealtimeTokenPrice(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum}).AnyTimes()
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)
	tokens := domain.ChainToken{
		Chain:   domain.Ethereum,
		TokenID: "******************************************",
	}

	expectedHotPrices := []domain.RealtimeTokenPriceResponse{
		{
			ChainToken: tokens,
			PriceUSD:   1.00,
		},
	}

	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), []domain.ChainToken{tokens}).Return(expectedHotPrices, []domain.RealtimeTokenPriceResponse{}, nil).Times(1)

	ctx := context.Background()

	// Call the function under test
	result, err := GetRealtimeTokenPrice(ctx, []domain.ChainToken{tokens})
	assert.Nil(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, expectedHotPrices[0].PriceUSD, result[0].PriceUSD)

	t.Run("requeue_missing_tokens_and_verify_requeue_call", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		tokens := []domain.ChainToken{
			{Chain: domain.Ethereum, TokenID: "token1"},
			{Chain: domain.Ethereum, TokenID: "token2"},
		}

		// Always return only one hot price (token2 is always missing/expired)
		partialHotPrices := []domain.RealtimeTokenPriceResponse{
			{
				ChainToken:     tokens[0],
				PriceUSD:       domain.Price(1.23),
				LastUpdateTime: time.Now(),
			},
		}

		// Mock calls - always return partial hot prices to trigger timeout
		mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).Return(partialHotPrices, []domain.RealtimeTokenPriceResponse{}, nil).AnyTimes()

		// Expect requeue call for missing token
		mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, tasks []domain.RealtimeTokenPriceTask) error {
				assert.Equal(t, 1, len(tasks))
				assert.Equal(t, tokens[1], tasks[0].ChainToken)
				assert.Equal(t, 0, tasks[0].RetryCount) // Reset retry count
				return nil
			},
		).MinTimes(1).MaxTimes(11) // 500 ms timeout and 100 ms ticker interval 2 tokens 10	times

		// Use a short timeout context to speed up the test
		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()

		_, kgErr := GetRealtimeTokenPrice(ctx, tokens)

		assert.NotNil(t, kgErr) // timeout is considered as error in this case
	})

	t.Run("fallback_to_warm_prices_on_timeout", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		tokens := []domain.ChainToken{
			{Chain: domain.Ethereum, TokenID: "token1"},
			{Chain: domain.Ethereum, TokenID: "token2"},
		}

		// Return warm prices for both tokens (no hot prices)
		warmPrices := []domain.RealtimeTokenPriceResponse{
			{
				ChainToken:     tokens[0],
				PriceUSD:       domain.Price(1.23),
				LastUpdateTime: time.Now().Add(-5 * time.Second), // 5 seconds old
			},
			{
				ChainToken:     tokens[1],
				PriceUSD:       domain.Price(4.56),
				LastUpdateTime: time.Now().Add(-10 * time.Second), // 10 seconds old
			},
		}

		// Mock calls - return no hot prices but warm prices
		mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).Return([]domain.RealtimeTokenPriceResponse{}, warmPrices, nil).AnyTimes()

		// Use a short timeout context to trigger fallback
		ctx, cancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
		defer cancel()

		result, kgErr := GetRealtimeTokenPrice(ctx, tokens)

		// Should succeed using warm prices as fallback
		assert.Nil(t, kgErr)
		assert.Len(t, result, 2)

		// Verify both warm prices were used
		pricesByToken := make(map[domain.ChainToken]domain.Price)
		for _, price := range result {
			pricesByToken[price.ChainToken] = price.PriceUSD
		}
		assert.Equal(t, domain.Price(1.23), pricesByToken[tokens[0]])
		assert.Equal(t, domain.Price(4.56), pricesByToken[tokens[1]])
	})

	t.Run("requeue_failure_handling", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Ethereum}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		tokens := []domain.ChainToken{
			{Chain: domain.Ethereum, TokenID: "token1"},
		}

		// Mock returns no hot or warm prices (all expired/missing)
		mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), tokens).Return([]domain.RealtimeTokenPriceResponse{}, []domain.RealtimeTokenPriceResponse{}, domain.ErrRecordNotFound).AnyTimes()

		// Expect requeue call but it fails
		mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(errors.New("requeue failed")).AnyTimes()

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()
		result, kgErr := GetRealtimeTokenPrice(ctx, tokens)

		// Should still return timeout error even if requeue fails
		assert.NotNil(t, kgErr)
		assert.Contains(t, kgErr.Error.Error(), "timeout while waiting for prices")
		assert.Equal(t, 0, len(result))
	})
}

func Test_AcquireAndProcessTasks(t *testing.T) {
	t.Run("success_single_batch", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Ethereum}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		task := domain.RealtimeTokenPriceTask{
			ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"},
			RetryCount: 0,
		}
		tasks := []domain.RealtimeTokenPriceTask{task}
		chainTokens := []domain.ChainToken{task.ChainToken}
		priceMap := map[domain.ChainToken]domain.Price{task.ChainToken: 1.23}

		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
		mockPriceFetcher.EXPECT().GetPricesByContract(gomock.Any(), chainTokens).Return(priceMap, nil).Times(1)
		mockRepo.EXPECT().AddRealtimeTokenPriceResponse(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, prices []domain.RealtimeTokenPriceResponse) error {
				assert.Equal(t, 1, len(prices))
				assert.Equal(t, 1.23, float64(prices[0].PriceUSD))
				return nil
			},
		).Times(1)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()
		AcquireAndProcessTasks(ctx)
	})

	t.Run("success_multiple_tokens", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum, domain.BNBChain}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		// Multiple tokens with different prices
		tasks := []domain.RealtimeTokenPriceTask{
			{
				ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"},
				RetryCount: 0,
			},
			{
				ChainToken: domain.ChainToken{Chain: domain.BNBChain, TokenID: "******************************************"},
				RetryCount: 1,
			},
		}
		chainTokens := []domain.ChainToken{tasks[0].ChainToken, tasks[1].ChainToken}
		priceMap := map[domain.ChainToken]domain.Price{
			tasks[0].ChainToken: 1.23,
			tasks[1].ChainToken: 99.87,
		}

		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
		mockPriceFetcher.EXPECT().GetPricesByContract(gomock.Any(), chainTokens).Return(priceMap, nil).Times(1)
		mockRepo.EXPECT().AddRealtimeTokenPriceResponse(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, prices []domain.RealtimeTokenPriceResponse) error {
				assert.Equal(t, 2, len(prices))
				// Verify both prices are set correctly
				pricesByToken := make(map[domain.ChainToken]domain.Price)
				for _, price := range prices {
					pricesByToken[price.ChainToken] = price.PriceUSD
				}
				assert.Equal(t, domain.Price(1.23), pricesByToken[tasks[0].ChainToken])
				assert.Equal(t, domain.Price(99.87), pricesByToken[tasks[1].ChainToken])
				return nil
			},
		).Times(1)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()
		AcquireAndProcessTasks(ctx)
	})

	t.Run("empty_queue", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return([]domain.RealtimeTokenPriceTask{}, nil).Times(1)
		ctx, cancel := context.WithTimeout(context.Background(), 200*time.Millisecond)
		defer cancel()
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer wg.Done()
			AcquireAndProcessTasks(ctx)
		}()
		wg.Wait()
	})

	t.Run("acquire_task_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		// Simulate database connection error
		expectedError := errors.New("database connection failed")
		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil, expectedError).Times(1)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer wg.Done()
			AcquireAndProcessTasks(ctx)
		}()
		wg.Wait()
	})

	t.Run("price_fetcher_error_should_requeue", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum, domain.BNBChain}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		task := domain.RealtimeTokenPriceTask{
			ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"},
			RetryCount: 0,
		}
		tasks := []domain.RealtimeTokenPriceTask{task}
		chainTokens := []domain.ChainToken{task.ChainToken}

		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
		mockPriceFetcher.EXPECT().GetPricesByContract(gomock.Any(), chainTokens).Return(nil, assert.AnError).Times(1)
		mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), tasks).Return(nil).Times(1)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer wg.Done()
			AcquireAndProcessTasks(ctx)
		}()
		wg.Wait()
	})

	t.Run("add_price_response_error_should_requeue", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		task := domain.RealtimeTokenPriceTask{
			ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"},
			RetryCount: 0,
		}
		tasks := []domain.RealtimeTokenPriceTask{task}
		chainTokens := []domain.ChainToken{task.ChainToken}
		priceMap := map[domain.ChainToken]domain.Price{task.ChainToken: 1.23}

		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
		mockPriceFetcher.EXPECT().GetPricesByContract(gomock.Any(), chainTokens).Return(priceMap, nil).Times(1)

		// First call to AddRealtimeTokenPriceResponse fails
		addPriceResponseError := errors.New("Redis connection failed")
		mockRepo.EXPECT().AddRealtimeTokenPriceResponse(gomock.Any(), gomock.Any()).Return(addPriceResponseError).Times(1)

		// Task should be requeued due to the error
		mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), tasks).Return(nil).Times(1)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer wg.Done()
			AcquireAndProcessTasks(ctx)
		}()
		wg.Wait()
	})

	t.Run("partial_price_fetching_success", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum, domain.BNBChain}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		// Two tasks but only one gets a price
		tasks := []domain.RealtimeTokenPriceTask{
			{
				ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"},
				RetryCount: 0,
			},
			{
				ChainToken: domain.ChainToken{Chain: domain.BNBChain, TokenID: "******************************************"},
				RetryCount: 1,
			},
		}
		chainTokens := []domain.ChainToken{tasks[0].ChainToken, tasks[1].ChainToken}

		// Only first token has a price, second token gets 0 (default value)
		priceMap := map[domain.ChainToken]domain.Price{
			tasks[0].ChainToken: 1.23,
			// tasks[1].ChainToken not included, should get default 0 value
		}

		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
		mockPriceFetcher.EXPECT().GetPricesByContract(gomock.Any(), chainTokens).Return(priceMap, nil).Times(1)
		mockRepo.EXPECT().AddRealtimeTokenPriceResponse(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, prices []domain.RealtimeTokenPriceResponse) error {
				assert.Equal(t, 2, len(prices))
				// Verify that both tokens are processed, but second one has 0 price
				pricesByToken := make(map[domain.ChainToken]domain.Price)
				for _, price := range prices {
					pricesByToken[price.ChainToken] = price.PriceUSD
				}
				assert.Equal(t, domain.Price(1.23), pricesByToken[tasks[0].ChainToken])
				assert.Equal(t, domain.Price(0), pricesByToken[tasks[1].ChainToken])
				return nil
			},
		).Times(1)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()
		AcquireAndProcessTasks(ctx)
	})

	t.Run("context_cancel_during_acquire", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum, domain.BNBChain}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, batch int) ([]domain.RealtimeTokenPriceTask, error) {
				return nil, context.Canceled
			},
		).Times(1)
		ctx, cancel := context.WithCancel(context.Background())
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer wg.Done()
			AcquireAndProcessTasks(ctx)
		}()
		time.Sleep(100 * time.Millisecond)
		cancel()
		wg.Wait()
	})

	t.Run("context_cancel_before_main_loop", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum, domain.BNBChain}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		// Cancel context before starting
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		// No repo calls should be made since context is already cancelled
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer wg.Done()
			AcquireAndProcessTasks(ctx)
		}()
		wg.Wait()
	})

	t.Run("multiple_iterations_until_timeout", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		task := domain.RealtimeTokenPriceTask{
			ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"},
			RetryCount: 0,
		}
		tasks := []domain.RealtimeTokenPriceTask{task}
		chainTokens := []domain.ChainToken{task.ChainToken}
		priceMap := map[domain.ChainToken]domain.Price{task.ChainToken: 1.23}

		// First iteration returns tasks
		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
		mockPriceFetcher.EXPECT().GetPricesByContract(gomock.Any(), chainTokens).Return(priceMap, nil).Times(1)
		mockRepo.EXPECT().AddRealtimeTokenPriceResponse(gomock.Any(), gomock.Any()).Return(nil).Times(1)

		// Subsequent iterations return empty (no more tasks) - allow multiple calls
		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return([]domain.RealtimeTokenPriceTask{}, nil).Times(2)

		// Allow sufficient time for multiple iterations
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		defer cancel()
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer wg.Done()
			AcquireAndProcessTasks(ctx)
		}()
		wg.Wait()
	})

	t.Run("requeue_error_on_price_fetch_failure", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum, domain.BNBChain}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		task := domain.RealtimeTokenPriceTask{
			ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"},
			RetryCount: 0,
		}
		tasks := []domain.RealtimeTokenPriceTask{task}
		chainTokens := []domain.ChainToken{task.ChainToken}

		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
		mockPriceFetcher.EXPECT().GetPricesByContract(gomock.Any(), chainTokens).Return(nil, fmt.Errorf("API rate limit exceeded")).Times(1)

		// Task should be requeued, but requeue also fails
		requeueError := errors.New("failed to requeue task")
		mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), tasks).Return(requeueError).Times(1)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer wg.Done()
			AcquireAndProcessTasks(ctx)
		}()
		wg.Wait()
	})

	t.Run("zero_price_handling", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mockRepo := NewMockIRepo(ctrl)
		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum, domain.BNBChain}).AnyTimes()
		Init(mockRepo, nil, nil, nil, mockPriceFetcher)

		task := domain.RealtimeTokenPriceTask{
			ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"},
			RetryCount: 0,
		}
		tasks := []domain.RealtimeTokenPriceTask{task}
		chainTokens := []domain.ChainToken{task.ChainToken}

		// Empty price map should result in 0 price
		priceMap := map[domain.ChainToken]domain.Price{}

		mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(tasks, nil).Times(1)
		mockPriceFetcher.EXPECT().GetPricesByContract(gomock.Any(), chainTokens).Return(priceMap, nil).Times(1)
		mockRepo.EXPECT().AddRealtimeTokenPriceResponse(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, prices []domain.RealtimeTokenPriceResponse) error {
				assert.Equal(t, 1, len(prices))
				assert.Equal(t, domain.Price(0), prices[0].PriceUSD)
				assert.Equal(t, task.ChainToken, prices[0].ChainToken)
				return nil
			},
		).Times(1)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()
		AcquireAndProcessTasks(ctx)
	})
}
