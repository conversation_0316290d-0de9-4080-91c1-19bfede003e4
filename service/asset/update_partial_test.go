package asset

import (
	"context"
	"errors"
	"math/big"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	ws "github.com/kryptogo/kg-wallet-backend/pkg/websocket"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// TestUpdateNativeBalances tests the UpdateNativeBalances function with success and error cases.
func TestUpdateNativeBalances(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock Repo
	mockRepo := NewMockIRepo(ctrl)

	// Create mock ChainClient
	mockChainClient := domain.NewMockChainClient(ctrl)

	// Initialize the Repo and ChainClient in the asset service
	Init(mockRepo, map[domain.Chain]domain.ChainClient{
		domain.Ethereum: mockChainClient,
	}, nil, nil, nil)

	// Initialize tokenmeta with mock Repo
	tokenmetaMock := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(tokenmetaMock, nil, nil)

	// Define test addresses
	addresses := []domain.Address{
		domain.NewEvmAddress("0x1111"),
		domain.NewEvmAddress("0x2222"),
		domain.NewEvmAddress("0x3333"),
	}

	wsRepoMock := domain.NewMockWebsocketRepo(ctrl)
	ws.Init(wsRepoMock)

	t.Run("UpdateNativeBalances_Success", func(t *testing.T) {
		chain := domain.Ethereum

		// Set up mock ChainClient to return balances for each address
		for _, addr := range addresses {
			rawAmount := big.NewInt(1000) // Example raw amount
			mockChainClient.EXPECT().NativeBalance(gomock.Any(), addr).Return(rawAmount, nil)
		}

		// Expect UpdateTokenAmounts to be called with the correct parameters
		expectedAmounts := make(map[domain.ChainAddress][]*domain.TokenAmount)
		for _, addr := range addresses {
			key := domain.ChainAddress{Chain: chain, Address: addr}
			expectedAmounts[key] = []*domain.TokenAmount{
				{
					Token:  chain.MainToken(),
					Amount: decimal.NewFromBigInt(big.NewInt(1000), -int32(chain.MainToken().Decimals())),
				},
			}
		}
		mockRepo.EXPECT().UpdateTokenAmounts(gomock.Any(), expectedAmounts).Return(nil)

		wsRepoMock.EXPECT().GetUIDsByAddresses(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, addresses []string) ([]string, error) {
			assert.ElementsMatch(t, addresses, []string{
				"******************************************",
				"******************************************",
				"******************************************",
			})
			return []string{""}, nil
		})

		// Call the function
		err := UpdateNativeBalances(context.Background(), chain, addresses)

		// Assertions
		assert.Nil(t, err)
	})

	t.Run("UpdateNativeBalances_Error", func(t *testing.T) {
		chain := domain.Ethereum

		// Set up mock ChainClient to return an error for NativeBalance
		mockChainClient.EXPECT().NativeBalance(gomock.Any(), addresses[0]).Return(nil, errors.New("NativeBalance error"))

		// Call the function
		err := UpdateNativeBalances(context.Background(), chain, addresses)

		// Assertions
		assert.NotNil(t, err)
		assert.EqualError(t, err, "NativeBalance error")
	})
}

// TestUpdateTokenBalances tests the UpdateTokenBalances function with success and error cases.
func TestUpdateTokenBalances(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock Repo
	mockRepo := NewMockIRepo(ctrl)

	// Create mock ChainClient
	mockChainClient := domain.NewMockChainClient(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum, domain.BNBChain}).AnyTimes()

	// Initialize the Repo and ChainClient in the asset service
	Init(mockRepo, map[domain.Chain]domain.ChainClient{
		domain.Solana: mockChainClient,
	}, nil, nil, mockPriceFetcher)

	// Initialize tokenmeta with mock Repo
	tokenmetaMock := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(tokenmetaMock, nil, nil)

	// Define test tokens
	tokens := []domain.WalletTokenID{
		{Wallet: domain.NewStrAddress("wallet1"), TokenID: "token1"},
		{Wallet: domain.NewStrAddress("wallet2"), TokenID: "token2"},
		{Wallet: domain.NewStrAddress("wallet3"), TokenID: "token3"},
	}

	wsRepoMock := domain.NewMockWebsocketRepo(ctrl)
	ws.Init(wsRepoMock)

	t.Run("UpdateTokenBalances_Success", func(t *testing.T) {
		chain := domain.Solana

		// Mock BatchGetTokens to return token metadata
		tokenMap := map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: chain, TokenID: "token1"}: {
				Name:        "Token One",
				Symbol:      "TK1",
				Decimals:    18,
				CoingeckoID: "",
				LogoUrl:     "https://token1.logo",
				IsVerified:  true,
			},
			{Chain: chain, TokenID: "token2"}: {
				Name:        "Token Two",
				Symbol:      "TK2",
				Decimals:    18,
				CoingeckoID: "",
				LogoUrl:     "https://token2.logo",
				IsVerified:  true,
			},
			{Chain: chain, TokenID: "token3"}: {
				Name:        "Token Three",
				Symbol:      "TK3",
				Decimals:    18,
				CoingeckoID: "",
				LogoUrl:     "https://token3.logo",
				IsVerified:  true,
			},
		}
		tokenmetaMock.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).Return(tokenMap, nil)

		// Mock TokenBalance for each token
		for _, token := range tokens {
			mockChainClient.EXPECT().TokenBalance(gomock.Any(), token.Wallet, token.TokenID).Return(big.NewInt(500), nil)
		}

		// Expect UpdateTokenAmounts to be called with the correct parameters
		expectedAmounts := make(map[domain.ChainAddress][]*domain.TokenAmount)
		for _, token := range tokens {
			key := domain.ChainAddress{Chain: chain, Address: token.Wallet}
			metadata := tokenMap[domain.ChainToken{Chain: chain, TokenID: token.TokenID}]
			expectedAmounts[key] = []*domain.TokenAmount{
				{
					Token:  domain.NewToken(chain, token.TokenID, metadata.Name, metadata.Symbol, metadata.LogoUrl, metadata.Decimals, metadata.IsVerified),
					Amount: decimal.NewFromBigInt(big.NewInt(500), -int32(metadata.Decimals)),
				},
			}
		}
		mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasks []domain.RealtimeTokenPriceTask) error {
			assert.Equal(t, 3, len(tasks), "Should add tasks for all tokens regardless of balance fetch success")
			expectedTokens := map[string]bool{
				"token1": true,
				"token2": true,
				"token3": true,
			}
			for _, task := range tasks {
				assert.True(t, expectedTokens[task.ChainToken.TokenID], "Unexpected token ID")
			}
			return nil
		}).Times(1)

		mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
			// Return hot prices for all requested tokens dynamically
			var hotPrices []domain.RealtimeTokenPriceResponse
			for _, token := range chainTokens {
				var price domain.Price
				// Use expected prices that match SetAssetPrices expectations
				switch token.TokenID {
				case "token1":
					price = 2.14514
				case "token2":
					price = 3.14514
				case "token3":
					price = 4.14514
				default:
					price = 100.0
				}
				hotPrices = append(hotPrices, domain.RealtimeTokenPriceResponse{
					ChainToken:     token,
					PriceUSD:       price,
					LastUpdateTime: time.Now(),
				})
			}
			return hotPrices, []domain.RealtimeTokenPriceResponse{}, nil
		}).Times(1)

		mockRepo.EXPECT().UpdateTokenAmounts(gomock.Any(), expectedAmounts).Return(nil)

		mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, prices []*domain.TokenPrice) error {
			assert.Equal(t, 3, len(prices), "Should set prices for all tokens regardless of balance fetch success")
			expectedPrices := map[string]float64{
				"token1": 2.14514,
				"token2": 3.14514,
				"token3": 4.14514,
			}
			for _, price := range prices {
				assert.Equal(t, chain, price.Chain)
				expectedPrice, exists := expectedPrices[price.ID]
				assert.True(t, exists, "Unexpected token ID: %s", price.ID)
				assert.Equal(t, expectedPrice, float64(price.Price))
			}
			return nil
		}).Times(1)

		wsRepoMock.EXPECT().GetUIDsByAddresses(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, addresses []string) ([]string, error) {
			assert.ElementsMatch(t, addresses, []string{
				"wallet1",
				"wallet2",
				"wallet3",
			})
			return []string{""}, nil
		})

		// Call the function
		err := UpdateTokenBalances(context.Background(), chain, tokens)

		// Assertions
		assert.Nil(t, err)
	})

	t.Run("UpdateTokenBalances_Error", func(t *testing.T) {
		chain := domain.Solana

		// Mock BatchGetTokens to return token metadata
		tokenMap := map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: chain, TokenID: "token1"}: {
				Name:        "Token One",
				Symbol:      "TK1",
				Decimals:    18,
				CoingeckoID: "",
				LogoUrl:     "https://token1.logo",
				IsVerified:  true,
			},
			{Chain: chain, TokenID: "token2"}: {
				Name:        "Token Two",
				Symbol:      "TK2",
				Decimals:    18,
				CoingeckoID: "",
				LogoUrl:     "https://token2.logo",
				IsVerified:  true,
			},
			{Chain: chain, TokenID: "token3"}: {
				Name:        "Token Three",
				Symbol:      "TK3",
				Decimals:    18,
				CoingeckoID: "",
				LogoUrl:     "https://token3.logo",
				IsVerified:  true,
			},
		}
		tokenmetaMock.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).Return(tokenMap, nil)

		// Helper function to create expected token amounts
		createExpectedTokenAmount := func(chain domain.Chain, tokenID string, amount *big.Int, tokenMap map[domain.ChainToken]*domain.TokenMetadata) *domain.TokenAmount {
			token := tokenMap[domain.ChainToken{Chain: chain, TokenID: tokenID}]
			return &domain.TokenAmount{
				Token:  domain.NewToken(chain, tokenID, token.Name, token.Symbol, token.LogoUrl, token.Decimals, token.IsVerified),
				Amount: decimal.NewFromBigInt(amount, -int32(token.Decimals)),
			}
		}

		// Mock TokenBalance to return error for one of the tokens
		mockChainClient.EXPECT().TokenBalance(gomock.Any(), tokens[0].Wallet, tokens[0].TokenID).Return(big.NewInt(500), nil)
		mockChainClient.EXPECT().TokenBalance(gomock.Any(), tokens[1].Wallet, tokens[1].TokenID).Return(nil, errors.New("TokenBalance error"))
		mockChainClient.EXPECT().TokenBalance(gomock.Any(), tokens[2].Wallet, tokens[2].TokenID).Return(big.NewInt(300), nil)

		// Expect UpdateTokenAmounts to be called with only successful balances
		expectedAmounts := make(map[domain.ChainAddress][]*domain.TokenAmount)
		expectedAmounts[domain.ChainAddress{Chain: chain, Address: tokens[0].Wallet}] = []*domain.TokenAmount{
			createExpectedTokenAmount(chain, tokens[0].TokenID, big.NewInt(500), tokenMap),
		}
		expectedAmounts[domain.ChainAddress{Chain: chain, Address: tokens[2].Wallet}] = []*domain.TokenAmount{
			createExpectedTokenAmount(chain, tokens[2].TokenID, big.NewInt(300), tokenMap),
		}
		mockRepo.EXPECT().UpdateTokenAmounts(gomock.Any(), expectedAmounts).Return(nil)

		// All tokens should be processed for price updates regardless of balance fetch success
		mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasks []domain.RealtimeTokenPriceTask) error {
			assert.Equal(t, 3, len(tasks), "Should add tasks for all tokens regardless of balance fetch success")
			expectedTokens := map[string]bool{
				"token1": true,
				"token2": true,
				"token3": true,
			}
			for _, task := range tasks {
				assert.True(t, expectedTokens[task.ChainToken.TokenID], "Unexpected token ID")
			}
			return nil
		}).Times(1)

		mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
			// Return hot prices for all requested tokens dynamically
			var hotPrices []domain.RealtimeTokenPriceResponse
			for _, token := range chainTokens {
				var price domain.Price
				// Use expected prices that match SetAssetPrices expectations
				switch token.TokenID {
				case "token1":
					price = 2.14514
				case "token2":
					price = 3.14514
				case "token3":
					price = 4.14514
				default:
					price = 200.0
				}
				hotPrices = append(hotPrices, domain.RealtimeTokenPriceResponse{
					ChainToken:     token,
					PriceUSD:       price,
					LastUpdateTime: time.Now(),
				})
			}
			return hotPrices, []domain.RealtimeTokenPriceResponse{}, nil
		}).Times(1)

		mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, prices []*domain.TokenPrice) error {
			assert.Equal(t, 3, len(prices), "Should set prices for all tokens regardless of balance fetch success")
			expectedPrices := map[string]float64{
				"token1": 2.14514,
				"token2": 3.14514,
				"token3": 4.14514,
			}
			for _, price := range prices {
				assert.Equal(t, chain, price.Chain)
				expectedPrice, exists := expectedPrices[price.ID]
				assert.True(t, exists, "Unexpected token ID: %s", price.ID)
				assert.Equal(t, expectedPrice, float64(price.Price))
			}
			return nil
		}).Times(1)

		wsRepoMock.EXPECT().GetUIDsByAddresses(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, addresses []string) ([]string, error) {
			assert.ElementsMatch(t, addresses, []string{
				"wallet1",
				"wallet3",
			})
			return []string{""}, nil
		})

		// Call the function
		err := UpdateTokenBalances(context.Background(), chain, tokens)

		// Assertions
		assert.Nil(t, err)
	})
}

// TestUpdateTokenBalances_WithTokenAmountsFetcher tests the UpdateTokenBalances function when a TokenAmountsFetcher is available.
func TestUpdateTokenBalances_WithTokenAmountsFetcher(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock Repo
	mockRepo := NewMockIRepo(ctrl)

	// Create mock ChainClient
	mockChainClient := domain.NewMockChainClient(ctrl)

	// Create mock TokenAmountsFetcher
	mockAmountsFetcher := domain.NewMockTokenAmountsFetcher(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum, domain.BNBChain}).AnyTimes()

	// Initialize the Repo and ChainClient in the asset service
	Init(mockRepo, map[domain.Chain]domain.ChainClient{
		domain.Solana: mockChainClient,
	}, nil, []domain.TokenAmountsFetcher{mockAmountsFetcher}, mockPriceFetcher)

	// Initialize tokenmeta with mock Repo
	tokenmetaMock := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(tokenmetaMock, nil, nil)

	// Define test tokens
	tokens := []domain.WalletTokenID{
		{Wallet: domain.NewStrAddress("wallet1"), TokenID: "token1"},
		{Wallet: domain.NewStrAddress("wallet2"), TokenID: "token2"},
		{Wallet: domain.NewStrAddress("wallet3"), TokenID: "token3"},
	}

	// Create mock WebsocketRepo
	wsRepoMock := domain.NewMockWebsocketRepo(ctrl)
	ws.Init(wsRepoMock)

	// Mock BatchGetTokenMetadata to return token metadata
	tokenMap := map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: domain.Solana, TokenID: "token1"}: {Name: "Token One", Symbol: "TK1", Decimals: 18, CoingeckoID: "", LogoUrl: "https://token1.logo", IsVerified: true},
		{Chain: domain.Solana, TokenID: "token2"}: {Name: "Token Two", Symbol: "TK2", Decimals: 18, CoingeckoID: "", LogoUrl: "https://token2.logo", IsVerified: true},
		{Chain: domain.Solana, TokenID: "token3"}: {Name: "Token Three", Symbol: "TK3", Decimals: 18, CoingeckoID: "", LogoUrl: "https://token3.logo", IsVerified: true},
	}
	tokenmetaMock.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).Return(tokenMap, nil)

	// Mock TokenAmountsFetcher to return balances
	mockAmountsFetcher.EXPECT().SupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
	mockAmountsFetcher.EXPECT().FetchAmounts(gomock.Any(), domain.Solana, gomock.Any()).DoAndReturn(
		func(_ context.Context, _ domain.Chain, tokens []*domain.WalletToken) (map[domain.Address]map[string]decimal.Decimal, error) {
			// Simulate fetching token amounts
			return map[domain.Address]map[string]decimal.Decimal{
				domain.NewStrAddress("wallet1"): {"token1": decimal.NewFromInt(1000)},
				domain.NewStrAddress("wallet2"): {"token2": decimal.NewFromInt(2000)},
				domain.NewStrAddress("wallet3"): {"token3": decimal.NewFromInt(3000)},
			}, nil
		},
	)

	// Expect UpdateTokenAmounts to be called with the correct parameters
	mockRepo.EXPECT().UpdateTokenAmounts(gomock.Any(), gomock.Any()).DoAndReturn(
		func(_ context.Context, amounts map[domain.ChainAddress][]*domain.TokenAmount) error {
			assert.Equal(t, 3, len(amounts), "Expected 3 ChainAddresses")

			for _, token := range tokens {
				key := domain.ChainAddress{Chain: domain.Solana, Address: token.Wallet}
				tokenAmounts, exists := amounts[key]
				assert.True(t, exists, "Expected ChainAddress to exist: %v", key)
				assert.Equal(t, 1, len(tokenAmounts), "Expected 1 TokenAmount for ChainAddress: %v", key)

				tokenAmount := tokenAmounts[0]
				metadata := tokenMap[domain.ChainToken{Chain: domain.Solana, TokenID: token.TokenID}]

				assert.Equal(t, domain.Solana, tokenAmount.Chain(), "Unexpected Chain")
				assert.Equal(t, token.TokenID, tokenAmount.ID(), "UnexpectedID")
				assert.Equal(t, metadata.Name, tokenAmount.Name(), "Unexpected Name")
				assert.Equal(t, metadata.Symbol, tokenAmount.Symbol(), "Unexpected Symbol")
				assert.Equal(t, metadata.LogoUrl, tokenAmount.LogoUrl(), "Unexpected LogoUrl")
				assert.Equal(t, metadata.Decimals, tokenAmount.Decimals(), "Unexpected Decimals")
				assert.Equal(t, metadata.IsVerified, tokenAmount.IsVerified(), "Unexpected IsVerified")

				amountMap := map[string]decimal.Decimal{
					"token1": decimal.NewFromInt(1000),
					"token2": decimal.NewFromInt(2000),
					"token3": decimal.NewFromInt(3000),
				}
				expectedAmount := amountMap[token.TokenID]
				assert.True(t, expectedAmount.Equal(tokenAmount.Amount), "Unexpected Amount for token %s: expected %s, got %s", token.TokenID, expectedAmount, tokenAmount.Amount)
			}

			return nil
		},
	)

	// Mock AddRealtimeTokenPriceTask to be called for all tokens
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasks []domain.RealtimeTokenPriceTask) error {
		assert.Equal(t, 3, len(tasks), "Should add tasks for all tokens")
		expectedTokens := map[string]bool{
			"token1": true,
			"token2": true,
			"token3": true,
		}
		for _, task := range tasks {
			assert.True(t, expectedTokens[task.ChainToken.TokenID], "Unexpected token ID")
		}
		return nil
	}).Times(1)

	// Mock GetRealtimeTokenPrice to return prices for all tokens
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		// Return hot prices for all requested tokens dynamically
		var hotPrices []domain.RealtimeTokenPriceResponse
		for _, token := range chainTokens {
			var price domain.Price
			// Use expected prices that match SetAssetPrices expectations
			switch token.TokenID {
			case "token1":
				price = 2.14514
			case "token2":
				price = 3.14514
			case "token3":
				price = 4.14514
			case "":
				price = 100.0 // SOL native token
			default:
				price = 50.0 // Other tokens
			}
			hotPrices = append(hotPrices, domain.RealtimeTokenPriceResponse{
				ChainToken:     token,
				PriceUSD:       price,
				LastUpdateTime: time.Now(),
			})
		}
		return hotPrices, []domain.RealtimeTokenPriceResponse{}, nil
	}).Times(1)

	// Mock SetAssetPrices to be called with the fetched prices
	mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, prices []*domain.TokenPrice) error {
		assert.Equal(t, 3, len(prices), "Should set prices for all tokens")
		expectedPrices := map[string]float64{
			"token1": 2.14514,
			"token2": 3.14514,
			"token3": 4.14514,
		}
		for _, price := range prices {
			assert.Equal(t, domain.Solana, price.Chain)
			expectedPrice, exists := expectedPrices[price.ID]
			assert.True(t, exists, "Unexpected token ID: %s", price.ID)
			assert.Equal(t, expectedPrice, float64(price.Price))
		}
		return nil
	}).Times(1)

	// Expect GetUIDsByAddresses to be called and return corresponding UIDs
	wsRepoMock.EXPECT().GetUIDsByAddresses(gomock.Any(), gomock.Any()).DoAndReturn(
		func(_ context.Context, addresses []string) ([]string, error) {
			assert.ElementsMatch(t, addresses, []string{
				"wallet1",
				"wallet2",
				"wallet3",
			})
			return []string{""}, nil // Assuming a single UID for simplicity
		},
	)

	// Call the function under test
	err := UpdateTokenBalances(context.Background(), domain.Solana, tokens)

	// Assertions
	assert.Nil(t, err, "Expected UpdateTokenBalances to succeed without error")
}

// TestUpdateTokenBalances_Solana tests the UpdateTokenBalances function specifically for Solana chain
// with both main token and regular token balances
func TestUpdateTokenBalances_Solana(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock Repo
	mockRepo := NewMockIRepo(ctrl)

	// Create mock ChainClient
	mockChainClient := domain.NewMockChainClient(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana, domain.Ethereum, domain.BNBChain}).AnyTimes()

	// Initialize the Repo and ChainClient in the asset service
	Init(mockRepo, map[domain.Chain]domain.ChainClient{
		domain.Solana: mockChainClient,
	}, nil, nil, mockPriceFetcher)

	// Initialize tokenmeta with mock Repo
	tokenmetaMock := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(tokenmetaMock, nil, nil)

	// Define test tokens - include both main token and regular token
	tokens := []domain.WalletTokenID{
		{Wallet: domain.NewStrAddress("solana_wallet1"), TokenID: ""},       // Main token
		{Wallet: domain.NewStrAddress("solana_wallet1"), TokenID: "token1"}, // Regular token
		{Wallet: domain.NewStrAddress("solana_wallet2"), TokenID: ""},       // Main token
		{Wallet: domain.NewStrAddress("solana_wallet2"), TokenID: "token2"}, // Regular token
	}

	wsRepoMock := domain.NewMockWebsocketRepo(ctrl)
	ws.Init(wsRepoMock)

	t.Run("UpdateTokenBalances_Solana_Success", func(t *testing.T) {
		chain := domain.Solana

		// Mock BatchGetTokens to return token metadata
		tokenMap := map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: chain, TokenID: ""}: {
				Name:        "Solana",
				Symbol:      "SOL",
				Decimals:    9,
				CoingeckoID: "",
				LogoUrl:     "https://solana.logo",
				IsVerified:  true,
			},
			{Chain: chain, TokenID: "token1"}: {
				Name:        "Token One",
				Symbol:      "TK1",
				Decimals:    6,
				CoingeckoID: "",
				LogoUrl:     "https://token1.logo",
				IsVerified:  true,
			},
			{Chain: chain, TokenID: "token2"}: {
				Name:        "Token Two",
				Symbol:      "TK2",
				Decimals:    8,
				CoingeckoID: "",
				LogoUrl:     "https://token2.logo",
				IsVerified:  true,
			},
		}
		tokenmetaMock.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).Return(tokenMap, nil)

		// Mock NativeBalance for main token (SOL)
		mockChainClient.EXPECT().NativeBalance(gomock.Any(), domain.NewStrAddress("solana_wallet1")).Return(big.NewInt(1000000000), nil)
		mockChainClient.EXPECT().NativeBalance(gomock.Any(), domain.NewStrAddress("solana_wallet2")).Return(big.NewInt(2000000000), nil)

		// Mock TokenBalance for regular tokens
		mockChainClient.EXPECT().TokenBalance(gomock.Any(), domain.NewStrAddress("solana_wallet1"), "token1").Return(big.NewInt(500000), nil)
		mockChainClient.EXPECT().TokenBalance(gomock.Any(), domain.NewStrAddress("solana_wallet2"), "token2").Return(big.NewInt(1000000), nil)

		// Expect UpdateTokenAmounts to be called with the correct parameters
		expectedAmounts := make(map[domain.ChainAddress][]*domain.TokenAmount)

		// Wallet 1 with both SOL and token1
		wallet1Key := domain.ChainAddress{Chain: chain, Address: domain.NewStrAddress("solana_wallet1")}
		expectedAmounts[wallet1Key] = []*domain.TokenAmount{
			{
				Token:  domain.NewToken(chain, "", "Solana", "SOL", "https://solana.logo", 9, true),
				Amount: decimal.NewFromBigInt(big.NewInt(1000000000), -9),
			},
			{
				Token:  domain.NewToken(chain, "token1", "Token One", "TK1", "https://token1.logo", 6, true),
				Amount: decimal.NewFromBigInt(big.NewInt(500000), -6),
			},
		}

		// Wallet 2 with both SOL and token2
		wallet2Key := domain.ChainAddress{Chain: chain, Address: domain.NewStrAddress("solana_wallet2")}
		expectedAmounts[wallet2Key] = []*domain.TokenAmount{
			{
				Token:  domain.NewToken(chain, "", "Solana", "SOL", "https://solana.logo", 9, true),
				Amount: decimal.NewFromBigInt(big.NewInt(2000000000), -9),
			},
			{
				Token:  domain.NewToken(chain, "token2", "Token Two", "TK2", "https://token2.logo", 8, true),
				Amount: decimal.NewFromBigInt(big.NewInt(1000000), -8),
			},
		}

		mockRepo.EXPECT().UpdateTokenAmounts(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, amounts map[domain.ChainAddress][]*domain.TokenAmount) error {
			// Verify the amounts map has the expected keys
			assert.Equal(t, 2, len(amounts), "Expected 2 ChainAddresses")

			// Verify wallet1 amounts
			wallet1Amounts, exists := amounts[wallet1Key]
			assert.True(t, exists, "Expected ChainAddress to exist: %v", wallet1Key)
			assert.Equal(t, 2, len(wallet1Amounts), "Expected 2 TokenAmounts for wallet1")

			// Verify wallet2 amounts
			wallet2Amounts, exists := amounts[wallet2Key]
			assert.True(t, exists, "Expected ChainAddress to exist: %v", wallet2Key)
			assert.Equal(t, 2, len(wallet2Amounts), "Expected 2 TokenAmounts for wallet2")

			return nil
		})

		// Mock AddRealtimeTokenPriceTask for all tokens (including main token)
		mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasks []domain.RealtimeTokenPriceTask) error {
			assert.Equal(t, 3, len(tasks), "Should add tasks for all unique tokens")
			expectedTokens := map[string]bool{
				"":       true, // Main token (SOL)
				"token1": true,
				"token2": true,
			}
			for _, task := range tasks {
				assert.True(t, expectedTokens[task.ChainToken.TokenID], "Unexpected token ID: %s", task.ChainToken.TokenID)
			}
			return nil
		}).Times(1)

		// Mock GetRealtimeTokenPrice to return prices
		mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
			// Return hot prices for all requested tokens dynamically
			var hotPrices []domain.RealtimeTokenPriceResponse
			for _, token := range chainTokens {
				var price domain.Price
				// Use expected prices that match SetAssetPrices expectations
				switch token.TokenID {
				case "":
					price = 100.0 // SOL native token
				case "token1":
					price = 2.14514
				case "token2":
					price = 3.14514
				default:
					price = 50.0 // Other tokens
				}
				hotPrices = append(hotPrices, domain.RealtimeTokenPriceResponse{
					ChainToken:     token,
					PriceUSD:       price,
					LastUpdateTime: time.Now(),
				})
			}
			return hotPrices, []domain.RealtimeTokenPriceResponse{}, nil
		}).Times(1)

		// Mock SetAssetPrices
		mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, prices []*domain.TokenPrice) error {
			assert.Equal(t, 3, len(prices), "Should set prices for all tokens")
			expectedPrices := map[string]float64{
				"":       100.0, // SOL price
				"token1": 2.14514,
				"token2": 3.14514,
			}
			for _, price := range prices {
				assert.Equal(t, chain, price.Chain)
				expectedPrice, exists := expectedPrices[price.ID]
				assert.True(t, exists, "Unexpected token ID: %s", price.ID)
				assert.Equal(t, expectedPrice, float64(price.Price))
			}
			return nil
		}).Times(1)

		wsRepoMock.EXPECT().GetUIDsByAddresses(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, addresses []string) ([]string, error) {
			assert.ElementsMatch(t, addresses, []string{
				"solana_wallet1",
				"solana_wallet2",
			})
			return []string{""}, nil
		})

		// Call the function
		err := UpdateTokenBalances(context.Background(), chain, tokens)

		// Assertions
		assert.Nil(t, err)
	})

	t.Run("UpdateTokenBalances_Solana_PartialError", func(t *testing.T) {
		chain := domain.Solana

		// Mock BatchGetTokens to return token metadata
		tokenMap := map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: chain, TokenID: ""}: {
				Name:        "Solana",
				Symbol:      "SOL",
				Decimals:    9,
				CoingeckoID: "",
				LogoUrl:     "https://solana.logo",
				IsVerified:  true,
			},
			{Chain: chain, TokenID: "token1"}: {
				Name:        "Token One",
				Symbol:      "TK1",
				Decimals:    6,
				CoingeckoID: "",
				LogoUrl:     "https://token1.logo",
				IsVerified:  true,
			},
			{Chain: chain, TokenID: "token2"}: {
				Name:        "Token Two",
				Symbol:      "TK2",
				Decimals:    8,
				CoingeckoID: "",
				LogoUrl:     "https://token2.logo",
				IsVerified:  true,
			},
		}
		tokenmetaMock.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).Return(tokenMap, nil)

		// Mock NativeBalance for main token (SOL) - success for both wallets
		mockChainClient.EXPECT().NativeBalance(gomock.Any(), domain.NewStrAddress("solana_wallet1")).Return(big.NewInt(1000000000), nil)
		mockChainClient.EXPECT().NativeBalance(gomock.Any(), domain.NewStrAddress("solana_wallet2")).Return(big.NewInt(2000000000), nil)

		// Mock TokenBalance for regular tokens - error for one token
		mockChainClient.EXPECT().TokenBalance(gomock.Any(), domain.NewStrAddress("solana_wallet1"), "token1").Return(big.NewInt(500000), nil)
		mockChainClient.EXPECT().TokenBalance(gomock.Any(), domain.NewStrAddress("solana_wallet2"), "token2").Return(nil, errors.New("TokenBalance error"))

		// Expect UpdateTokenAmounts to be called with only successful balances
		expectedAmounts := make(map[domain.ChainAddress][]*domain.TokenAmount)

		// Wallet 1 with both SOL and token1 (both successful)
		wallet1Key := domain.ChainAddress{Chain: chain, Address: domain.NewStrAddress("solana_wallet1")}
		expectedAmounts[wallet1Key] = []*domain.TokenAmount{
			{
				Token:  domain.NewToken(chain, "", "Solana", "SOL", "https://solana.logo", 9, true),
				Amount: decimal.NewFromBigInt(big.NewInt(1000000000), -9),
			},
			{
				Token:  domain.NewToken(chain, "token1", "Token One", "TK1", "https://token1.logo", 6, true),
				Amount: decimal.NewFromBigInt(big.NewInt(500000), -6),
			},
		}

		// Wallet 2 with only SOL (token2 failed)
		wallet2Key := domain.ChainAddress{Chain: chain, Address: domain.NewStrAddress("solana_wallet2")}
		expectedAmounts[wallet2Key] = []*domain.TokenAmount{
			{
				Token:  domain.NewToken(chain, "", "Solana", "SOL", "https://solana.logo", 9, true),
				Amount: decimal.NewFromBigInt(big.NewInt(2000000000), -9),
			},
		}

		mockRepo.EXPECT().UpdateTokenAmounts(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, amounts map[domain.ChainAddress][]*domain.TokenAmount) error {
			// Verify the amounts map has the expected keys
			assert.Equal(t, 2, len(amounts), "Expected 2 ChainAddresses")

			// Verify wallet1 amounts
			wallet1Amounts, exists := amounts[wallet1Key]
			assert.True(t, exists, "Expected ChainAddress to exist: %v", wallet1Key)
			assert.Equal(t, 2, len(wallet1Amounts), "Expected 2 TokenAmounts for wallet1")

			// Verify wallet2 amounts
			wallet2Amounts, exists := amounts[wallet2Key]
			assert.True(t, exists, "Expected ChainAddress to exist: %v", wallet2Key)
			assert.Equal(t, 1, len(wallet2Amounts), "Expected 1 TokenAmount for wallet2")

			return nil
		})

		// Mock AddRealtimeTokenPriceTask for all tokens (even if balance fetch fails for some)
		mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasks []domain.RealtimeTokenPriceTask) error {
			assert.Equal(t, 3, len(tasks), "Should add tasks for all unique tokens regardless of balance fetch success")
			expectedTokens := map[string]bool{
				"":       true, // Main token (SOL)
				"token1": true,
				"token2": true,
			}
			for _, task := range tasks {
				assert.True(t, expectedTokens[task.ChainToken.TokenID], "Unexpected token ID: %s", task.ChainToken.TokenID)
			}
			return nil
		}).Times(1)

		// Mock GetRealtimeTokenPrice to return prices for all tokens
		mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
			// Return hot prices for all requested tokens dynamically
			var hotPrices []domain.RealtimeTokenPriceResponse
			for _, token := range chainTokens {
				var price domain.Price
				// Use expected prices that match SetAssetPrices expectations
				switch token.TokenID {
				case "":
					price = 100.0 // SOL native token
				case "token1":
					price = 2.14514
				case "token2":
					price = 3.14514
				default:
					price = 75.0 // Other tokens
				}
				hotPrices = append(hotPrices, domain.RealtimeTokenPriceResponse{
					ChainToken:     token,
					PriceUSD:       price,
					LastUpdateTime: time.Now(),
				})
			}
			return hotPrices, []domain.RealtimeTokenPriceResponse{}, nil
		}).Times(1)

		// Mock SetAssetPrices for all tokens
		mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, prices []*domain.TokenPrice) error {
			assert.Equal(t, 3, len(prices), "Should set prices for all tokens regardless of balance fetch success")
			expectedPrices := map[string]float64{
				"":       100.0, // SOL price
				"token1": 2.14514,
				"token2": 3.14514,
			}
			for _, price := range prices {
				assert.Equal(t, chain, price.Chain)
				expectedPrice, exists := expectedPrices[price.ID]
				assert.True(t, exists, "Unexpected token ID: %s", price.ID)
				assert.Equal(t, expectedPrice, float64(price.Price))
			}
			return nil
		}).Times(1)

		wsRepoMock.EXPECT().GetUIDsByAddresses(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, addresses []string) ([]string, error) {
			assert.ElementsMatch(t, addresses, []string{
				"solana_wallet1",
				"solana_wallet2",
			})
			return []string{""}, nil
		})

		// Call the function
		err := UpdateTokenBalances(context.Background(), chain, tokens)

		// Assertions
		assert.Nil(t, err)
	})
}
