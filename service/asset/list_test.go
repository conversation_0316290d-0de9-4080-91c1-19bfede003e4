package asset

import (
	context "context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAppRepo := domain.NewMockApplicationRepo(ctrl)
	mockAppRepo.EXPECT().GetOAuthApplication(gomock.Any(), "client456").Return(&domain.OAuthApplication{
		Application: domain.Application{
			ClientID: "client456",
			Name:     "KG",
		},
	}, nil)
	application.Init(mockAppRepo)

	mockMetadataRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockMetadataRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
		assert.ElementsMatch(t, []domain.ChainToken{
			{Chain: domain.Ethereum, TokenID: "link"},
		}, tokens)

		return map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: domain.Ethereum, TokenID: "link"}: {
				Name:          "Chainlink",
				Symbol:        "LINK",
				CoingeckoID:   "c-link",
				LogoUrl:       "https://link.logo",
				BinanceTicker: "LINK",
			},
			{Chain: domain.Polygon, TokenID: "matic"}: {
				Name:          "Polygon",
				Symbol:        "POL",
				CoingeckoID:   "c-matic",
				LogoUrl:       "https://matic.logo",
				BinanceTicker: "POL",
			},
			{Chain: domain.Ethereum, TokenID: "eth"}: {
				Name:          "Ethereum",
				Symbol:        "ETH",
				CoingeckoID:   "c-eth",
				LogoUrl:       "https://eth.logo",
				BinanceTicker: "ETH",
			},
		}, nil
	})
	tokenmeta.Init(mockMetadataRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	mockRepo := NewMockIRepo(ctrl)

	param := &ListParam{
		ClientID: "client456",
		Addresses: map[domain.Chain][]domain.Address{
			domain.Ethereum: {domain.NewEvmAddress("0xDEF")},
			domain.Polygon:  {domain.NewEvmAddress("0x123")},
			domain.Arbitrum: {domain.NewEvmAddress("0xABC")},
		},
		Types:                 []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
		IncludeUnverified:     true,
		IncludePriceHistories: true,
		Query:                 "example",
		IncludeTokens:         map[domain.Chain][]string{domain.Arbitrum: {"0x1234"}},
		ExcludeTokens:         map[domain.Chain][]string{domain.Ethereum: {"eth"}, domain.Polygon: {"usdc"}},
		PageNumber:            2,
		PageSize:              20,
	}

	mockRepo.EXPECT().ListAssets(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, p *domain.ListAssetsParam) ([]domain.Asset, int, error) {
		// Assert that the correct parameters are passed
		assert.Equal(t, param.Addresses, p.Addresses)
		assert.Equal(t, param.Types, p.Types)
		assert.Equal(t, param.IncludeUnverified, p.IncludeUnverified)
		assert.Equal(t, param.Query, p.Query)
		assert.Equal(t, param.IncludeTokens, p.IncludeTokens)
		assert.Equal(t, param.ExcludeTokens, p.ExcludeTokens)
		assert.Equal(t, param.PageNumber, p.PageNumber)
		assert.Equal(t, param.PageSize, p.PageSize)
		return []domain.Asset{
			&domain.TokenAsset{
				Token: domain.NewToken(domain.Ethereum, "link", "Chainlink", "LINK", "https://link.logo", 18, true),
				Price: 30.0,
				WalletAmounts: []*domain.WalletTokenAmount{
					{Address: domain.NewEvmAddress("0xDEF"), Amount: decimal.NewFromFloat(500)},
					{Address: domain.NewEvmAddress("0x123"), Amount: decimal.NewFromFloat(1000)},
				},
			},
			func() *domain.NftAsset {
				nft := domain.NewNftAsset(domain.Polygon, "nft123", "CryptoKitty", "https://cryptokitty.logo", true)
				nft.NftType = domain.NftTypeErc721
				nft.ContractAddress = domain.NewEvmAddress("0x9999")
				nft.FloorPriceETH = 0.1
				nft.WalletAmounts = []*domain.WalletNftAmount{
					{Address: domain.NewEvmAddress("0xabc"), Amount: 4},
					{Address: domain.NewEvmAddress("0xdef"), Amount: 1},
				}
				return nft
			}(),
			func() *domain.DefiAsset {
				defi := domain.NewDefiAsset(domain.Arbitrum, "defi456", "ArbiDefi", "https://arbidefi.com")
				defi.SupplyTokens = append(defi.SupplyTokens, &domain.DefiToken{
					ID: "arb-supply", Name: "ARB Supply", Symbol: "ARBS", LogoUrl: util.Ptr("https://arb-supply.logo"),
					Amount: decimal.NewFromFloat(1000), Price: 1.5,
				})
				defi.BorrowTokens = append(defi.BorrowTokens, &domain.DefiToken{
					ID: "arb-borrow", Name: "ARB Borrow", Symbol: "ARBB", LogoUrl: util.Ptr("https://arb-borrow.logo"),
					Amount: decimal.NewFromFloat(300), Price: 1.4,
				})
				defi.RewardTokens = append(defi.RewardTokens, &domain.DefiToken{
					ID: "arb-reward", Name: "ARB Reward", Symbol: "ARBR", LogoUrl: util.Ptr("https://arb-reward.logo"),
					Amount: decimal.NewFromFloat(50), Price: 1.7,
				})
				defi.WalletValues = append(defi.WalletValues, &domain.WalletValue{
					Address: domain.NewEvmAddress("0xABC"), UsdValue: decimal.NewFromFloat(1165),
				})
				return defi
			}(),
		}, 3, nil
	})

	mockRepo.EXPECT().ListMainTokens(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, p map[domain.Chain][]domain.Address) ([]domain.Asset, int, error) {
		assert.Equal(t, param.Addresses, p)
		return []domain.Asset{
			&domain.TokenAsset{
				Token: domain.NewToken(domain.Ethereum, "eth", "Ethereum", "ETH", "https://eth.logo", 18, true),
				Price: 100.0,
				WalletAmounts: []*domain.WalletTokenAmount{
					{Address: domain.NewEvmAddress("0xDEF"), Amount: decimal.NewFromFloat(1.0)},
					{Address: domain.NewEvmAddress("0x123"), Amount: decimal.NewFromFloat(2.0)},
				},
			},
			&domain.TokenAsset{
				Token: domain.NewToken(domain.Polygon, "matic", "Polygon", "POL", "https://matic.logo", 18, true),
				Price: 12.0,
				WalletAmounts: []*domain.WalletTokenAmount{
					{Address: domain.NewEvmAddress("0xabc"), Amount: decimal.NewFromFloat(1.0)},
					{Address: domain.NewEvmAddress("0x123"), Amount: decimal.NewFromFloat(2.0)},
				},
			},
		}, 0, nil
	})

	mockRepo.EXPECT().GetNativeAssetPrice(gomock.Any(), "eth").Return(100.0, nil)

	mockRepo.EXPECT().BatchGetTokenPricesIn24H(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken][]*domain.PricePoint, error) {
		assert.ElementsMatch(t, []domain.ChainToken{
			{Chain: domain.Ethereum, TokenID: "link"},
			{Chain: domain.Polygon, TokenID: "matic"},
			{Chain: domain.Ethereum, TokenID: "eth"},
		}, tokens)
		return map[domain.ChainToken][]*domain.PricePoint{
			{Chain: domain.Ethereum, TokenID: "link"}: {
				{Price: 30.0, Time: time.Now().Add(-24 * time.Hour)},
				{Price: 32.0, Time: time.Now().Add(-23 * time.Hour)},
			},
			{Chain: domain.Polygon, TokenID: "matic"}: {
				{Price: 12.0, Time: time.Now().Add(-24 * time.Hour)},
				{Price: 13.0, Time: time.Now().Add(-23 * time.Hour)},
			},
			{Chain: domain.Ethereum, TokenID: "eth"}: {
				{Price: 100.0, Time: time.Now().Add(-24 * time.Hour)},
				{Price: 102.0, Time: time.Now().Add(-23 * time.Hour)},
			},
		}, nil
	})

	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	Init(mockRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, mockPriceFetcher)
	ctx := context.Background()
	resp, kgErr := List(ctx, param)

	// Assertions
	assert.Nil(t, kgErr)
	assert.NotNil(t, resp)
	assert.Len(t, resp.Assets, 3)
	assert.Len(t, resp.MainTokens, 2)
	assert.Equal(t, 3, resp.TotalCount)

	// Assert Token Asset Fields
	token := resp.Assets[0]
	assert.Equal(t, domain.Ethereum, token.Chain)
	assert.Equal(t, "link", token.ID)
	assert.Equal(t, domain.AssetTypeToken, token.AssetType)
	assert.Equal(t, "LINK", *token.Symbol)
	assert.Equal(t, "Chainlink", token.Name)
	assert.True(t, token.IsVerified)
	assert.Nil(t, token.FloorPriceEth)
	assert.Equal(t, "1500", token.AmountDec)
	assert.Equal(t, float64(1500), token.Amount)
	assert.Equal(t, float64(1500*30), token.UsdValue)
	assert.Nil(t, token.TrcTokenType)
	assert.Equal(t, uint(18), *token.Decimals)
	assert.Equal(t, float64(30), *token.Price)
	assert.Len(t, token.Wallets, 2)
	assert.Equal(t, domain.NewEvmAddress("0xDEF").String(), token.Wallets[0].Address)
	assert.Equal(t, float64(500), token.Wallets[0].Amount)
	assert.Equal(t, "500", token.Wallets[0].AmountStr)
	assert.Equal(t, float64(500*30), token.Wallets[0].UsdValue)
	assert.Equal(t, domain.NewEvmAddress("0x123").String(), token.Wallets[1].Address)
	assert.Equal(t, float64(1000), token.Wallets[1].Amount)
	assert.Equal(t, "1000", token.Wallets[1].AmountStr)
	assert.Equal(t, float64(1000*30), token.Wallets[1].UsdValue)
	assert.Len(t, token.TokenAccounts, 0)
	assert.Len(t, token.PricesIn24H, 2)
	assert.Equal(t, 30.0, token.PricesIn24H[0][1])
	assert.Equal(t, 32.0, token.PricesIn24H[1][1])
	assert.Nil(t, token.DefiMetadata)
	assert.Equal(t, []string{"https://link.logo"}, token.LogoUrls)
	assert.Equal(t, "c-link", token.CoingeckoID)
	assert.Equal(t, "LINK", *token.BinanceTicker)

	// Assert NFT Asset Fields
	nft := resp.Assets[1]
	assert.Equal(t, domain.Polygon, nft.Chain)
	assert.Equal(t, "nft123", nft.ID)
	assert.Equal(t, domain.AssetTypeNft, nft.AssetType)
	assert.Nil(t, nft.Symbol)
	assert.Equal(t, "CryptoKitty", nft.Name)
	assert.True(t, nft.IsVerified)
	assert.NotNil(t, nft.FloorPriceEth)
	assert.Equal(t, 0.1, *nft.FloorPriceEth)
	assert.Equal(t, "5", nft.AmountDec)
	assert.Equal(t, float64(5), nft.Amount)
	assert.Equal(t, float64(5*0.1*100), nft.UsdValue)
	assert.Nil(t, nft.TrcTokenType)
	assert.Nil(t, nft.Decimals)
	assert.Equal(t, float64(0.1*100), *nft.Price)
	assert.Len(t, nft.Wallets, 2)
	assert.Equal(t, domain.NewEvmAddress("0xabc").String(), nft.Wallets[0].Address)
	assert.Equal(t, float64(4), nft.Wallets[0].Amount)
	assert.Equal(t, "4", nft.Wallets[0].AmountStr)
	assert.Equal(t, float64(4*0.1*100), nft.Wallets[0].UsdValue)
	assert.Equal(t, domain.NewEvmAddress("0xdef").String(), nft.Wallets[1].Address)
	assert.Equal(t, float64(1), nft.Wallets[1].Amount)
	assert.Equal(t, "1", nft.Wallets[1].AmountStr)
	assert.Equal(t, float64(1*0.1*100), nft.Wallets[1].UsdValue)
	assert.Len(t, nft.TokenAccounts, 0)
	assert.Len(t, nft.PricesIn24H, 0)
	assert.Nil(t, nft.DefiMetadata)
	assert.Equal(t, []string{"https://cryptokitty.logo"}, nft.LogoUrls)
	assert.Equal(t, "", nft.CoingeckoID)

	// Assert Defi Asset Fields
	defi := resp.Assets[2]
	assert.Equal(t, domain.Arbitrum, defi.Chain)
	assert.Equal(t, "defi456", defi.ID)
	assert.Equal(t, domain.AssetTypeDefi, defi.AssetType)
	assert.Nil(t, defi.Symbol)
	assert.Equal(t, "ArbiDefi", defi.Name)
	assert.True(t, defi.IsVerified)
	assert.Nil(t, defi.FloorPriceEth)
	assert.Equal(t, "1165.000000", defi.AmountDec) // USD Value as string formatted
	assert.Equal(t, float64(1165), defi.Amount)
	assert.Equal(t, float64(1165), defi.UsdValue)
	assert.Nil(t, defi.TrcTokenType)
	assert.Nil(t, defi.Decimals)
	assert.Nil(t, defi.Price)
	assert.Len(t, defi.Wallets, 1)
	assert.Equal(t, domain.NewEvmAddress("0xABC").String(), defi.Wallets[0].Address)
	assert.Equal(t, float64(1165), defi.Wallets[0].Amount)
	assert.Equal(t, "1165", defi.Wallets[0].AmountStr)
	assert.Equal(t, float64(1165), defi.Wallets[0].UsdValue)
	assert.Len(t, defi.TokenAccounts, 0)
	assert.Len(t, defi.PricesIn24H, 0)
	assert.NotNil(t, defi.DefiMetadata)
	assert.Equal(t, "https://arbidefi.com", defi.DefiMetadata.SiteURL)
	// Assert DefiStats
	assert.NotNil(t, defi.DefiMetadata.Stats)
	assert.Equal(t, float64(1585), defi.DefiMetadata.Stats.AssetUsdValue)
	assert.Equal(t, float64(420), defi.DefiMetadata.Stats.DebtUsdValue)
	assert.Equal(t, float64(1165), defi.DefiMetadata.Stats.NetUsdValue)

	// Assert DefiDetail
	assert.NotNil(t, defi.DefiMetadata.Detail)
	assert.Len(t, defi.DefiMetadata.Detail.SupplyTokenList, 1)
	assert.Len(t, defi.DefiMetadata.Detail.RewardTokenList, 1)
	assert.Len(t, defi.DefiMetadata.Detail.BorrowTokenList, 1)

	supplyToken := defi.DefiMetadata.Detail.SupplyTokenList[0]
	assert.Equal(t, "ARB Supply", supplyToken.Name)
	assert.Equal(t, "ARBS", supplyToken.Symbol)
	assert.Equal(t, "https://arb-supply.logo", *supplyToken.LogoURL)
	assert.Equal(t, 1000.0, supplyToken.Amount)
	assert.Equal(t, 1.5, supplyToken.Price)

	rewardToken := defi.DefiMetadata.Detail.RewardTokenList[0]
	assert.Equal(t, "ARB Reward", rewardToken.Name)
	assert.Equal(t, "ARBR", rewardToken.Symbol)
	assert.Equal(t, "https://arb-reward.logo", *rewardToken.LogoURL)
	assert.Equal(t, 50.0, rewardToken.Amount)
	assert.Equal(t, 1.7, rewardToken.Price)

	borrowToken := defi.DefiMetadata.Detail.BorrowTokenList[0]
	assert.Equal(t, "ARB Borrow", borrowToken.Name)
	assert.Equal(t, "ARBB", borrowToken.Symbol)
	assert.Equal(t, "https://arb-borrow.logo", *borrowToken.LogoURL)
	assert.Equal(t, 300.0, borrowToken.Amount)
	assert.Equal(t, 1.4, borrowToken.Price)

	// Assert LogoUrls and CoingeckoID for Defi Asset
	assert.Equal(t, []string{"https://arb-supply.logo"}, defi.LogoUrls)
	assert.Equal(t, "", defi.CoingeckoID)

	// Assert Main Tokens
	assert.Len(t, resp.MainTokens, 2)

	// Assert ETH Main Token Fields
	ethMainToken := resp.MainTokens[0]
	assert.Equal(t, domain.Ethereum, ethMainToken.Chain)
	assert.Equal(t, "eth", ethMainToken.ID)
	assert.Equal(t, domain.AssetTypeToken, ethMainToken.AssetType)
	assert.Equal(t, "ETH", *ethMainToken.Symbol)
	assert.Equal(t, "Ethereum", ethMainToken.Name)
	assert.True(t, ethMainToken.IsVerified)
	assert.Nil(t, ethMainToken.FloorPriceEth)
	assert.Equal(t, "3", ethMainToken.AmountDec)
	assert.Equal(t, float64(3), ethMainToken.Amount)
	assert.Equal(t, float64(3*100), ethMainToken.UsdValue)
	assert.Nil(t, ethMainToken.TrcTokenType)
	assert.Equal(t, uint(18), *ethMainToken.Decimals)
	assert.Equal(t, float64(100), *ethMainToken.Price)
	assert.Len(t, ethMainToken.Wallets, 2)
	assert.Equal(t, domain.NewEvmAddress("0xDEF").String(), ethMainToken.Wallets[0].Address)
	assert.Equal(t, float64(1.0), ethMainToken.Wallets[0].Amount)
	assert.Equal(t, "1", ethMainToken.Wallets[0].AmountStr)
	assert.Equal(t, float64(1.0*100), ethMainToken.Wallets[0].UsdValue)
	assert.Equal(t, domain.NewEvmAddress("0x123").String(), ethMainToken.Wallets[1].Address)
	assert.Equal(t, float64(2.0), ethMainToken.Wallets[1].Amount)
	assert.Equal(t, "2", ethMainToken.Wallets[1].AmountStr)
	assert.Equal(t, float64(2.0*100), ethMainToken.Wallets[1].UsdValue)
	assert.Len(t, ethMainToken.TokenAccounts, 0)
	assert.Len(t, ethMainToken.PricesIn24H, 2)
	assert.Equal(t, 100.0, ethMainToken.PricesIn24H[0][1])
	assert.Equal(t, 102.0, ethMainToken.PricesIn24H[1][1])
	assert.Nil(t, ethMainToken.DefiMetadata)
	assert.Equal(t, []string{"https://eth.logo"}, ethMainToken.LogoUrls)
	assert.Equal(t, "ethereum", ethMainToken.CoingeckoID)
	assert.Equal(t, "ETH", *ethMainToken.BinanceTicker)

	// Assert MATIC Main Token Fields
	maticMainToken := resp.MainTokens[1]
	assert.Equal(t, domain.Polygon, maticMainToken.Chain)
	assert.Equal(t, "matic", maticMainToken.ID)
	assert.Equal(t, domain.AssetTypeToken, maticMainToken.AssetType)
	assert.Equal(t, "POL", *maticMainToken.Symbol)
	assert.Equal(t, "Polygon", maticMainToken.Name)
	assert.True(t, maticMainToken.IsVerified)
	assert.Nil(t, maticMainToken.FloorPriceEth)
	assert.Equal(t, "3", maticMainToken.AmountDec)
	assert.Equal(t, float64(3), maticMainToken.Amount)
	assert.Equal(t, float64(3*12), maticMainToken.UsdValue)
	assert.Nil(t, maticMainToken.TrcTokenType)
	assert.Equal(t, uint(18), *maticMainToken.Decimals)
	assert.Equal(t, float64(12), *maticMainToken.Price)
	assert.Len(t, maticMainToken.Wallets, 2)
	assert.Equal(t, domain.NewEvmAddress("0xabc").String(), maticMainToken.Wallets[0].Address)
	assert.Equal(t, float64(1.0), maticMainToken.Wallets[0].Amount)
	assert.Equal(t, "1", maticMainToken.Wallets[0].AmountStr)
	assert.Equal(t, float64(1.0*12), maticMainToken.Wallets[0].UsdValue)
	assert.Equal(t, domain.NewEvmAddress("0x123").String(), maticMainToken.Wallets[1].Address)
	assert.Equal(t, float64(2.0), maticMainToken.Wallets[1].Amount)
	assert.Equal(t, "2", maticMainToken.Wallets[1].AmountStr)
	assert.Equal(t, float64(2.0*12), maticMainToken.Wallets[1].UsdValue)
	assert.Len(t, maticMainToken.TokenAccounts, 0)
	assert.Len(t, maticMainToken.PricesIn24H, 2)
	assert.Equal(t, 12.0, maticMainToken.PricesIn24H[0][1])
	assert.Equal(t, 13.0, maticMainToken.PricesIn24H[1][1])
	assert.Nil(t, maticMainToken.DefiMetadata)
	assert.Equal(t, []string{"https://matic.logo"}, maticMainToken.LogoUrls)
	assert.Equal(t, "matic-network", maticMainToken.CoingeckoID)
	assert.Equal(t, "POL", *maticMainToken.BinanceTicker)
}

func TestGetSingle(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockTokenMetadataRepo := domain.NewMockTokenMetadataRepo(ctrl)

	// Test GetSingle for Solana Token
	t.Run("GetSingle_SolanaToken", func(t *testing.T) {
		chain := domain.Solana
		assetType := domain.AssetTypeToken
		assetID := "sol-token-id"

		// Mock GetTokenMetadata for Solana Token
		mockTokenMetadataRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			assert.ElementsMatch(t, []domain.ChainToken{{Chain: domain.Solana, TokenID: "sol-token-id"}}, tokens)
			return map[domain.ChainToken]*domain.TokenMetadata{
				{Chain: domain.Solana, TokenID: "sol-token-id"}: {
					Name:          "Solana Token",
					Symbol:        "SOL",
					CoingeckoID:   "c-sol",
					LogoUrl:       "https://sol.token.logo",
					BinanceTicker: "SOL",
				},
			}, nil
		})
		tokenmeta.Init(mockTokenMetadataRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

		// Mock GetSingleAsset for Solana Token
		mockRepo.EXPECT().GetSingleAsset(gomock.Any(), chain, assetType, assetID, []domain.Address{domain.NewStrAddress("sol-wallet1"), domain.NewStrAddress("sol-wallet2")}).DoAndReturn(func(ctx context.Context, c domain.Chain, at domain.AssetType, id string, _ []domain.Address) (domain.Asset, error) {
			t.Logf("chain %v, assetType %v, id %v", c, at, id)

			assert.Equal(t, domain.Solana, c)
			assert.Equal(t, domain.AssetTypeToken, at)
			assert.Equal(t, "sol-token-id", id)

			solanaToken := &domain.SolanaTokenAsset{
				TokenAsset: domain.TokenAsset{
					Token: domain.NewToken(domain.Solana, "sol-token-id", "Solana Token", "SOL", "https://sol.token.logo", 9, true),
					Price: 50.0,
					WalletAmounts: []*domain.WalletTokenAmount{
						{Address: domain.NewStrAddress("sol-wallet1"), Amount: decimal.NewFromFloat(100)},
						{Address: domain.NewStrAddress("sol-wallet2"), Amount: decimal.NewFromFloat(200)},
					},
				},
				Accounts: []*domain.SolanaTokenAccount{
					{Address: "sol-wallet1", Account: "sol-account1"},
					{Address: "sol-wallet2", Account: "sol-account2"},
				},
			}
			t.Logf("solanaToken %v", solanaToken)
			return solanaToken, nil
		})

		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		Init(mockRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, mockPriceFetcher)
		ctx := context.Background()
		asset, kgErr := GetSingle(ctx, chain, assetType, assetID, []domain.Address{domain.NewStrAddress("sol-wallet1"), domain.NewStrAddress("sol-wallet2")})

		// Assertions
		assert.Nil(t, kgErr)
		assert.NotNil(t, asset)
		assert.Equal(t, domain.Solana, asset.Chain)
		assert.Equal(t, "sol-token-id", asset.ID)
		assert.Equal(t, domain.AssetTypeToken, asset.AssetType)
		assert.Equal(t, "SOL", *asset.Symbol)
		assert.Equal(t, "Solana Token", asset.Name)
		assert.True(t, asset.IsVerified)
		assert.Nil(t, asset.FloorPriceEth)
		assert.Equal(t, "300", asset.AmountDec)
		assert.Equal(t, float64(300), asset.Amount)
		assert.Equal(t, float64(300*50), asset.UsdValue)
		assert.Nil(t, asset.TrcTokenType)
		assert.Equal(t, uint(9), *asset.Decimals)
		assert.Equal(t, float64(50), *asset.Price)
		assert.Len(t, asset.Wallets, 2)
		assert.Equal(t, "sol-wallet1", asset.Wallets[0].Address)
		assert.Equal(t, float64(100), asset.Wallets[0].Amount)
		assert.Equal(t, "100", asset.Wallets[0].AmountStr)
		assert.Equal(t, float64(100*50), asset.Wallets[0].UsdValue)
		assert.Equal(t, "sol-wallet2", asset.Wallets[1].Address)
		assert.Equal(t, float64(200), asset.Wallets[1].Amount)
		assert.Equal(t, "200", asset.Wallets[1].AmountStr)
		assert.Equal(t, float64(200*50), asset.Wallets[1].UsdValue)
		assert.Len(t, asset.TokenAccounts, 2)
		assert.Equal(t, "sol-wallet1", asset.TokenAccounts[0].Address)
		assert.Equal(t, "sol-account1", asset.TokenAccounts[0].TokenAccount)
		assert.Equal(t, "sol-wallet2", asset.TokenAccounts[1].Address)
		assert.Equal(t, "sol-account2", asset.TokenAccounts[1].TokenAccount)
		assert.Len(t, asset.PricesIn24H, 0)
		assert.Nil(t, asset.DefiMetadata)
		assert.Equal(t, []string{"https://sol.token.logo"}, asset.LogoUrls)
		assert.Equal(t, "c-sol", asset.CoingeckoID)
		assert.Equal(t, "SOL", *asset.BinanceTicker)
	})

	// Test GetSingle for Tron Token
	t.Run("GetSingle_TronToken", func(t *testing.T) {
		chain := domain.Tron
		assetType := domain.AssetTypeToken
		assetID := "trc-token-id"

		// Mock GetTokenMetadata for Tron Token
		mockTokenMetadataRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			assert.ElementsMatch(t, []domain.ChainToken{{Chain: domain.Tron, TokenID: "trc-token-id"}}, tokens)
			return map[domain.ChainToken]*domain.TokenMetadata{
				{Chain: domain.Tron, TokenID: "trc-token-id"}: {
					Name:          "Tron Token",
					Symbol:        "TRC",
					CoingeckoID:   "c-trc",
					LogoUrl:       "https://trc.token.logo",
					BinanceTicker: "TRC",
				},
			}, nil
		})
		tokenmeta.Init(mockTokenMetadataRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

		// Mock GetSingleAsset for Tron Token
		mockRepo.EXPECT().GetSingleAsset(gomock.Any(), chain, assetType, assetID, []domain.Address{domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"), domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7")}).DoAndReturn(func(ctx context.Context, c domain.Chain, at domain.AssetType, id string, _ []domain.Address) (domain.Asset, error) {
			assert.Equal(t, domain.Tron, c)
			assert.Equal(t, domain.AssetTypeToken, at)
			assert.Equal(t, "trc-token-id", id)

			tronToken := &domain.TokenAsset{
				Token: domain.NewTronToken(domain.Tron, "trc-token-id", "Tron Token", "TRC", "https://trc.token.logo", 6, true, domain.TrcTypeTrc20),
				Price: 10.0,
				WalletAmounts: []*domain.WalletTokenAmount{
					{Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"), Amount: decimal.NewFromFloat(1000)},
					{Address: domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"), Amount: decimal.NewFromFloat(2000)},
				},
			}
			return tronToken, nil
		})

		mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
		Init(mockRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, mockPriceFetcher)
		ctx := context.Background()

		asset, kgErr := GetSingle(ctx, chain, assetType, assetID, []domain.Address{domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"), domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7")})

		// Assertions
		assert.Nil(t, kgErr)
		assert.NotNil(t, asset)
		assert.Equal(t, domain.Tron, asset.Chain)
		assert.Equal(t, "trc-token-id", asset.ID)
		assert.Equal(t, domain.AssetTypeToken, asset.AssetType)
		assert.Equal(t, "TRC", *asset.Symbol)
		assert.Equal(t, "Tron Token", asset.Name)
		assert.True(t, asset.IsVerified)
		assert.Nil(t, asset.FloorPriceEth)
		assert.Equal(t, "3000", asset.AmountDec)
		assert.Equal(t, float64(3000), asset.Amount)
		assert.Equal(t, float64(3000*10), asset.UsdValue)
		assert.NotNil(t, asset.TrcTokenType)
		assert.Equal(t, uint(6), *asset.Decimals)
		assert.Equal(t, float64(10), *asset.Price)
		assert.Len(t, asset.Wallets, 2)
		assert.Equal(t, domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn").String(), asset.Wallets[0].Address)
		assert.Equal(t, float64(1000), asset.Wallets[0].Amount)
		assert.Equal(t, "1000", asset.Wallets[0].AmountStr)
		assert.Equal(t, float64(1000*10), asset.Wallets[0].UsdValue)
		assert.Equal(t, domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7").String(), asset.Wallets[1].Address)
		assert.Equal(t, float64(2000), asset.Wallets[1].Amount)
		assert.Equal(t, "2000", asset.Wallets[1].AmountStr)
		assert.Equal(t, float64(2000*10), asset.Wallets[1].UsdValue)
		assert.Len(t, asset.TokenAccounts, 0)
		assert.Len(t, asset.PricesIn24H, 0)
		assert.Nil(t, asset.DefiMetadata)
		assert.Equal(t, []string{"https://trc.token.logo"}, asset.LogoUrls)
		assert.Equal(t, "c-trc", asset.CoingeckoID)
		assert.Equal(t, domain.TrcTypeTrc20, *asset.TrcTokenType)
		assert.Equal(t, "TRC", *asset.BinanceTicker)
	})
}
