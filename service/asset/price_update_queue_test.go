package asset

import (
	"context"
	"testing"
	time "time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCheckAndUpdatePrices_NoUpdate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)

	mockRepo.EXPECT().GetAssetPriceQueueInfo(gomock.Any()).Return(&domain.AssetPriceQueueInfo{
		LastAdded:  nil,
		LastPopped: nil,
	}, nil)
	mockRepo.EXPECT().SetAssetPriceQueueInfo(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Initialize the repo
	Init(mockRepo, nil, nil, nil, nil)

	ctx := context.Background()
	err := CheckAndUpdatePrices(ctx)

	assert.Nil(t, err)
}

func TestCheckAndUpdatePrices_DoUpdate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Ethereum, domain.Polygon}).AnyTimes()
	mockTokenMeta := domain.NewMockTokenMetadataRepo(ctrl)

	now := time.Now()
	mockRepo.EXPECT().GetAssetPriceQueueInfo(gomock.Any()).Return(&domain.AssetPriceQueueInfo{
		LastAdded:  util.Ptr(now.Add(-10 * time.Second)),
		LastPopped: nil,
	}, nil)
	mockRepo.EXPECT().SetAssetPriceQueueInfo(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, info domain.AssetPriceQueueInfo) error {
		if info.LastAdded != nil && info.LastPopped != nil {
			assert.Equal(t, info.LastAdded, info.LastPopped)
		} else if info.LastAdded == nil {
			assert.NotNil(t, info.LastPopped)
		} else {
			t.Fatalf("LastAdded and LastPopped are both nil")
		}
		return nil
	})
	mockRepo.EXPECT().PopAllAssetPriceUpdateJobs(gomock.Any()).Return([]domain.ChainAddress{
		{
			Chain:   domain.Ethereum,
			Address: domain.NewEvmAddress("0xadd1111"),
		},
		{
			Chain:   domain.Polygon,
			Address: domain.NewEvmAddress("0xadd2222"),
		},
	}, nil)

	// Mock GetTokensByAddress
	mockRepo.EXPECT().GetTokensByAddress(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, addresses map[domain.Chain][]domain.Address) ([]domain.Token, error) {
		for chain, addresses := range addresses {
			switch chain {
			case domain.Ethereum:
				assert.ElementsMatch(t, addresses, []domain.Address{domain.NewEvmAddress("0xadd1111")})
			case domain.Polygon:
				assert.ElementsMatch(t, addresses, []domain.Address{domain.NewAddressByChain(domain.Polygon, "0xadd2222")})
			}
		}
		return nil, nil
	})
	mockRepo.EXPECT().ListAssets(gomock.Any(), gomock.Any()).AnyTimes().Return([]domain.Asset{}, 0, nil)
	mockTokenMeta.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).AnyTimes().Return(nil, nil)
	mockPriceFetcher.EXPECT().GetPrices(gomock.Any(), gomock.Any()).Return(nil, nil)
	// Mock AddRealtimeTokenPriceTask for the new implementation
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	// Mock GetRealtimeTokenPrice to return empty prices (no tokens to price)
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		// Return 0 price for all tokens since no real tokens in this test
		var hotResponses []domain.RealtimeTokenPriceResponse
		for _, chainToken := range chainTokens {
			hotResponses = append(hotResponses, domain.RealtimeTokenPriceResponse{
				ChainToken: chainToken,
				PriceUSD:   0, // Return 0 price for all tokens since no real tokens in this test
			})
		}
		return hotResponses, []domain.RealtimeTokenPriceResponse{}, nil
	}).AnyTimes()
	mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().UpdateDefiAssets(gomock.Any(), gomock.Any()).AnyTimes().Return(nil)
	mockRepo.EXPECT().BatchCreateAssetPriceHistories(gomock.Any(), gomock.Any()).Return(nil)
	mockRepo.EXPECT().DeleteAssetPriceHistories(gomock.Any(), gomock.Any()).Return(nil)

	// Initialize the repo
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)
	tokenmeta.Init(mockTokenMeta, nil, nil)

	ctx := context.Background()
	err := CheckAndUpdatePrices(ctx)

	assert.Nil(t, err)
}
