package asset

import (
	"context"
	"testing"
	time "time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestBalanceSummary(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)

	// Setup mock expectations for BalanceSummary
	mockRepo.EXPECT().
		GetAssetsTotalUsdValue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType) (float64, error) {
			// Assert input parameters
			assert.Equal(t, []domain.Chain{domain.Ethereum}, chains)
			assert.Equal(t, []domain.Address{domain.NewEvmAddress("0xABC123")}, addresses)
			assert.Equal(t, []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft}, types)
			return 1500.0, nil
		})

	mockRepo.EXPECT().
		GetPastAssetsTotalUsdValue(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType, from time.Time) (float64, error) {
			// Assert input parameters
			assert.Equal(t, []domain.Chain{domain.Ethereum}, chains)
			assert.Equal(t, []domain.Address{domain.NewEvmAddress("0xABC123")}, addresses)
			assert.Equal(t, []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft}, types)
			return 1400.0, nil
		})

	// Initialize the asset service with the mocked repository
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	Init(mockRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, mockPriceFetcher)
	ctx := context.Background()
	chains := []domain.Chain{domain.Ethereum}
	addresses := []domain.Address{domain.NewEvmAddress("0xABC123")}
	types := []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft}

	// Call the BalanceSummary function
	resp, kgErr := BalanceSummary(ctx, chains, addresses, types)

	// Assertions
	assert.Nil(t, kgErr)
	assert.NotNil(t, resp)
	assert.Equal(t, 1500.0, resp.UsdValue)
	assert.Equal(t, 1400.0, resp.UsdValue24hAgo)
}

func TestGetWalletBalances(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)

	// Setup mock expectations for GetWalletBalances
	mockRepo.EXPECT().
		GetAssetBalances(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType) (map[domain.ChainAddress]map[domain.AssetType]float64, error) {
			// Assert input parameters
			assert.Equal(t, []domain.Chain{domain.Ethereum, domain.Polygon}, chains)
			assert.Equal(t, []domain.Address{domain.NewEvmAddress("0xDEF456"), domain.NewEvmAddress("0x123ABC")}, addresses)
			assert.Equal(t, []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi}, types)
			return map[domain.ChainAddress]map[domain.AssetType]float64{
				{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0xDEF456")}: {
					domain.AssetTypeToken: 750.0,
					domain.AssetTypeNft:   5.0,
					domain.AssetTypeDefi:  100.0,
				},
			}, nil
		})

	mockRepo.EXPECT().
		GetPastAssetBalances(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType, from time.Time) (map[domain.ChainAddress]map[domain.AssetType]float64, error) {
			// Assert input parameters
			assert.Equal(t, []domain.Chain{domain.Ethereum, domain.Polygon}, chains)
			assert.Equal(t, []domain.Address{domain.NewEvmAddress("0xDEF456"), domain.NewEvmAddress("0x123ABC")}, addresses)
			assert.Equal(t, []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi}, types)
			return map[domain.ChainAddress]map[domain.AssetType]float64{
				{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0xDEF456")}: {
					domain.AssetTypeToken: 700.0,
					domain.AssetTypeNft:   4.5,
				},
			}, nil
		})

	// Initialize the asset service with the mocked repository
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	Init(mockRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, mockPriceFetcher)
	ctx := context.Background()
	chains := []domain.Chain{domain.Ethereum, domain.Polygon}
	addresses := []domain.Address{domain.NewEvmAddress("0xDEF456"), domain.NewEvmAddress("0x123ABC")}
	types := []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi}

	// Call the GetWalletBalances function
	balances, kgErr := GetWalletBalances(ctx, chains, addresses, types)

	// Assertions
	assert.Nil(t, kgErr)
	assert.NotNil(t, balances)
	expectedBalances := map[domain.ChainAddress][]SingleAssetBalance{
		{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0xDEF456")}: {
			{
				AssetType:      domain.AssetTypeToken,
				UsdValue:       750.0,
				UsdValue24hAgo: 700.0,
			},
			{
				AssetType:      domain.AssetTypeNft,
				UsdValue:       5.0,
				UsdValue24hAgo: 4.5,
			},
			{
				AssetType:      domain.AssetTypeDefi,
				UsdValue:       100.0,
				UsdValue24hAgo: 0.0,
			},
		},
	}
	assert.Equal(t, expectedBalances, balances)
}
