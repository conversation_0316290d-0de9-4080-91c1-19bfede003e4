package asset

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func TestGetPricesByContractAddRealtimeTokenPriceTask(t *testing.T) {
	// Create gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)

	// Pre-populate the cache with expected prices
	expectedPrices := map[domain.ChainToken]domain.Price{
		{
			Chain:   domain.Solana,
			TokenID: domain.Solana.MainToken().ID(), // Native SOL token
		}: domain.Price(123.45),
		{
			Chain:   domain.Solana,
			TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump", // PYTH token
		}: domain.Price(6.789),
	}

	// Test tokens
	tokens := []domain.ChainToken{
		{
			Chain:   domain.Solana,
			TokenID: domain.Solana.MainToken().ID(), // Native SOL token
		},
		{
			Chain:   domain.Solana,
			TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump", // PYTH token
		},
	}

	// Set expectations for the mock repository
	// GetRealtimeTokenPrice will be called multiple times in polling loop
	// When prices are available immediately, it should be called once per GetPricesByContract call
	// When cache expires, it might be called multiple times until timeout
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		// Return the expected prices for the tokens as hot prices
		var hotResponses []domain.RealtimeTokenPriceResponse
		for _, token := range tokens {
			if price, exists := expectedPrices[token]; exists {
				hotResponses = append(hotResponses, domain.RealtimeTokenPriceResponse{
					ChainToken: token,
					PriceUSD:   price,
				})
			}
		}
		// Return empty warm prices since these are fresh hot prices
		return hotResponses, []domain.RealtimeTokenPriceResponse{}, nil
	}).MinTimes(2).MaxTimes(10) // Allow for multiple calls due to polling behavior

	// AddRealtimeTokenPriceTask will be called once for each non-empty GetPricesByContract call
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasks []domain.RealtimeTokenPriceTask) error {
		// Verify that the tasks are for the expected tokens
		for _, task := range tasks {
			if _, exists := expectedPrices[task.ChainToken]; !exists {
				return fmt.Errorf("unexpected token in task: %v", task.ChainToken)
			}
		}
		return nil
	}).Times(3) // Called for first, second, and fourth GetPricesByContract calls (third call has empty tokens)

	// First call - should use cache
	ctx := context.Background()
	result1, kgErr := GetPricesByContract(ctx, tokens)
	require.Nil(t, kgErr, "KGError should be nil")
	require.NotNil(t, result1)

	// Verify results
	assert.Equal(t, expectedPrices[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(), // Native SOL token
	}], result1[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(), // Native SOL token
	}], "SOL price should match expected")
	assert.Equal(t, expectedPrices[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump", // PYTH token
	}], result1[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump", // PYTH token
	}], "Specific token price should match expected")

	// Second call - should use cache only
	result2, kgErr := GetPricesByContract(ctx, tokens)
	require.Nil(t, kgErr, "KGError should be nil")
	require.NotNil(t, result2)

	// Verify results
	assert.Equal(t, expectedPrices[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(), // Native SOL token
	}], result2[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(), // Native SOL token
	}], "Cached SOL price should match expected")
	assert.Equal(t, expectedPrices[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump", // PYTH token
	}], result2[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump", // PYTH token
	}], "Cached specific token price should match expected")

	// Test empty tokens
	result3, kgErr := GetPricesByContract(ctx, []domain.ChainToken{})
	require.Nil(t, kgErr, "KGError should be nil")
	assert.Empty(t, result3, "Empty tokens should return empty map")

	// Sleep to ensure cache expiration
	time.Sleep(60 * time.Millisecond)

	// This should call the fetcher again due to expired cache
	result4, kgErr := GetPricesByContract(ctx, tokens)
	require.Nil(t, kgErr, "KGError should be nil")
	require.NotNil(t, result4)

	// Verify results
	assert.Equal(t, expectedPrices[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(), // Native SOL token
	}], result4[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(), // Native SOL token
	}], "SOL price after expiration should match expected")
	assert.Equal(t, expectedPrices[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump", // PYTH token
	}], result4[domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump", // PYTH token
	}], "Specific token price after expiration should match expected")
}

func TestGetPricesByContract_Error(t *testing.T) {
	// Create gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock repo and price fetcher
	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)

	// AddRealtimeTokenPriceTask will be called once per GetPricesByContract call
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).Times(1)

	// Mock GetRealtimeTokenPrice to return an error
	solToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(),
	}
	// GetRealtimeTokenPrice will be called multiple times in the polling loop due to error/timeout
	// The polling continues until 3-second timeout when no valid prices are returned
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).Return([]domain.RealtimeTokenPriceResponse{}, []domain.RealtimeTokenPriceResponse{}, fmt.Errorf("failed to get realtime price")).MinTimes(1).MaxTimes(30)

	// priceFetcher.GetPricesByContract may be called if async processing happens
	mockPriceFetcher.EXPECT().
		GetPricesByContract(gomock.Any(), gomock.Any()).
		Return(nil, fmt.Errorf("timeout while waiting for prices")).
		MaxTimes(1) // May be called at most once during async processing

	// Call should return a KGError wrapping the standard error
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	result, kgErr := GetPricesByContract(ctx, []domain.ChainToken{solToken})
	require.NotNil(t, kgErr, "KGError should not be nil")
	assert.Contains(t, kgErr.String(), "timeout while waiting for prices", "Error message should contain timeout message")
	assert.Contains(t, kgErr.String(), "1 tokens missing out of 1 total tokens", "Error message should contain token count")
	assert.Contains(t, kgErr.String(), solToken.TokenID, "Error message should contain missing token ID")

	// Result should be empty but not nil
	assert.NotNil(t, result, "Result should be an empty map, not nil")
	assert.Empty(t, result, "Result should be empty on error")
}

func TestGetPricesByContract_CacheMissOnFirstCall(t *testing.T) {
	// NOTE: No background processing is happening in this test
	// Create gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)
	// Note: No need to mock priceFetcher since AcquireAndProcessTasks is not called in this test

	// Test tokens
	solToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(),
	}
	pythToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump",
	}
	tokens := []domain.ChainToken{solToken, pythToken}

	// Expected prices after cache is populated
	expectedPrices := map[domain.ChainToken]domain.Price{
		solToken:  domain.Price(150.25),
		pythToken: domain.Price(8.95),
	}

	// AddRealtimeTokenPriceTask will be called once for each GetPricesByContract call
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).Times(2)

	// Track whether we're in first or second GetPricesByContract call
	firstAPICall := true

	// Mock GetRealtimeTokenPrice with different behavior for different API calls
	// First call: returns empty (cache miss), will be called multiple times until timeout
	// Second call: returns prices immediately, called once
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		if firstAPICall {
			// During first API call: always return empty to simulate cache miss
			// This will cause GetRealtimeTokenPrice to timeout and return empty results
			return []domain.RealtimeTokenPriceResponse{}, []domain.RealtimeTokenPriceResponse{}, nil
		}
		// During second API call: return cached prices as hot prices
		hotResponses := make([]domain.RealtimeTokenPriceResponse, 0, len(tokens))
		for _, token := range tokens {
			if price, exists := expectedPrices[token]; exists {
				hotResponses = append(hotResponses, domain.RealtimeTokenPriceResponse{
					ChainToken: token,
					PriceUSD:   price,
				})
			}
		}
		return hotResponses, []domain.RealtimeTokenPriceResponse{}, nil
	}).MinTimes(1).MaxTimes(31) // First call may be called many times (up to 30 for timeout) + 1 for second call

	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	// First call - should return empty results due to cache miss
	// This call will timeout after 3 seconds of polling
	result1, kgErr := GetPricesByContract(ctx, tokens)

	// Note: The first call might return a timeout error due to the 3-second polling mechanism
	// This is actually expected behavior when cache is empty
	if kgErr != nil {
		// Verify it's a timeout error
		assert.Contains(t, kgErr.String(), "timeout while waiting for prices", "Should be a timeout error")
		assert.NotNil(t, result1, "Result should not be nil even on timeout")
		assert.Empty(t, result1, "Result should be empty on cache miss timeout")
	} else {
		// If no error, result should be empty
		require.NotNil(t, result1)
		assert.Empty(t, result1, "First call should return empty map due to cache miss")
	}

	// Set flag for second API call
	firstAPICall = false

	// Simulate a short delay for price processing (in real scenario,
	// the background process would populate the cache)
	time.Sleep(10 * time.Millisecond)

	// Second call - should return cached prices immediately
	ctx2, cancel2 := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel2()
	result2, kgErr := GetPricesByContract(ctx2, tokens)
	require.Nil(t, kgErr, "Second call should succeed")
	require.NotNil(t, result2)
	assert.Len(t, result2, 2, "Second call should return prices for both tokens")

	// Verify that the prices match expectations
	assert.Equal(t, expectedPrices[solToken], result2[solToken], "SOL price should match expected")
	assert.Equal(t, expectedPrices[pythToken], result2[pythToken], "PYTH price should match expected")

	// Test that the tasks were added for price updating
	// This ensures the price update mechanism was triggered
	// The actual expectations are verified through the mock calls above
}

func TestAddTokenPriceTask_ExceedMaxRetries(t *testing.T) {
	// Create gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)

	ctx := context.Background()

	// Create test tokens
	solToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(),
	}
	pythToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump",
	}

	// Create tasks with different retry counts
	tasks := []domain.RealtimeTokenPriceTask{
		{
			ChainToken: solToken,
			RetryCount: MaxRealtimePriceRetries + 1, // This should be filtered out
		},
		{
			ChainToken: pythToken,
			RetryCount: MaxRealtimePriceRetries - 1, // This should be processed
		},
		{
			ChainToken: solToken,
			RetryCount: MaxRealtimePriceRetries + 5, // This should also be filtered out
		},
	}

	// Expect AddRealtimeTokenPriceTask to be called with only the valid task (pythToken)
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasksToAdd []domain.RealtimeTokenPriceTask) error {
		// Should only contain the pythToken task (the one with retry count < max)
		assert.Len(t, tasksToAdd, 1, "Only one task should be added to queue")
		assert.Equal(t, pythToken, tasksToAdd[0].ChainToken, "Only pythToken task should be added")
		assert.Equal(t, MaxRealtimePriceRetries-1, tasksToAdd[0].RetryCount, "Retry count should match expected")
		return nil
	}).Times(1)

	// Call AddTokenPriceTask
	err := AddTokenPriceTask(ctx, tasks)

	// Should not return error even when some tasks are filtered out
	assert.NoError(t, err, "AddTokenPriceTask should not return error")

	// Note: In a real test environment, we might want to verify the error logs
	// were written correctly, but that would require setting up a test logger
	// and capturing the log output, which is beyond the scope of this unit test
}

func TestAddTokenPriceTask_AllTasksExceedMaxRetries(t *testing.T) {
	// Create gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)

	ctx := context.Background()

	// Create test token
	solToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(),
	}

	// Create tasks where all exceed max retries
	tasks := []domain.RealtimeTokenPriceTask{
		{
			ChainToken: solToken,
			RetryCount: MaxRealtimePriceRetries + 1,
		},
		{
			ChainToken: solToken,
			RetryCount: MaxRealtimePriceRetries + 10,
		},
	}

	// Expect AddRealtimeTokenPriceTask to be called with empty slice
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasksToAdd []domain.RealtimeTokenPriceTask) error {
		// Should be empty since all tasks exceed max retries
		assert.Len(t, tasksToAdd, 0, "No tasks should be added to queue when all exceed max retries")
		return nil
	}).Times(1)

	// Call AddTokenPriceTask
	err := AddTokenPriceTask(ctx, tasks)

	// Should not return error even when all tasks are filtered out
	assert.NoError(t, err, "AddTokenPriceTask should not return error even when all tasks are filtered out")
}

func TestAddTokenPriceTask_ValidTasks(t *testing.T) {
	// Create gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)

	ctx := context.Background()

	// Create test tokens
	solToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(),
	}
	pythToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump",
	}

	// Create tasks with valid retry counts
	tasks := []domain.RealtimeTokenPriceTask{
		{
			ChainToken: solToken,
			RetryCount: 0,
		},
		{
			ChainToken: pythToken,
			RetryCount: MaxRealtimePriceRetries,
		},
	}

	// Expect AddRealtimeTokenPriceTask to be called with all tasks
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasksToAdd []domain.RealtimeTokenPriceTask) error {
		// Should contain all tasks since none exceed max retries
		assert.Len(t, tasksToAdd, 2, "All valid tasks should be added to queue")
		assert.Equal(t, solToken, tasksToAdd[0].ChainToken, "First task should be solToken")
		assert.Equal(t, 0, tasksToAdd[0].RetryCount, "First task retry count should be 0")
		assert.Equal(t, pythToken, tasksToAdd[1].ChainToken, "Second task should be pythToken")
		assert.Equal(t, MaxRealtimePriceRetries, tasksToAdd[1].RetryCount, "Second task retry count should equal max retries")
		return nil
	}).Times(1)

	// Call AddTokenPriceTask
	err := AddTokenPriceTask(ctx, tasks)

	// Should not return error
	assert.NoError(t, err, "AddTokenPriceTask should not return error for valid tasks")
}

func TestGetPricesByContract_RealtimePriceTimeout(t *testing.T) {
	// Create gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)

	// Test tokens that will timeout
	solToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(),
	}
	pythToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump",
	}
	tokens := []domain.ChainToken{solToken, pythToken}

	// AddRealtimeTokenPriceTask will be called once
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).Times(1)

	// Mock GetRealtimeTokenPrice to always return empty results (simulating cache miss/no prices available)
	// This will cause the polling to continue until the 3-second timeout
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		// Always return empty slice (no error, but no prices either)
		// This simulates the case where the price fetcher is working but just hasn't found the prices yet
		return []domain.RealtimeTokenPriceResponse{}, []domain.RealtimeTokenPriceResponse{}, nil
	}).MinTimes(25).MaxTimes(35) // Should be called about 30 times over 3 seconds (every 100ms)

	// priceFetcher.GetPricesByContract should not be called in this test since it's for async processing
	// But if it is called, we'll make it return empty as well
	mockPriceFetcher.EXPECT().
		GetPricesByContract(gomock.Any(), gomock.Any()).
		Return(map[domain.ChainToken]domain.Price{}, nil).
		MaxTimes(1)

	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()
	start := time.Now()

	// This should timeout after approximately 3 seconds
	result, kgErr := GetPricesByContract(ctx, tokens)

	duration := time.Since(start)

	// Verify that it actually took close to 3 seconds (with some tolerance for execution time)
	assert.True(t, duration >= 2500*time.Millisecond, "Should take at least 2.5 seconds due to timeout")
	assert.True(t, duration <= 4000*time.Millisecond, "Should not take more than 4 seconds")

	// Should return a timeout error
	require.NotNil(t, kgErr, "Should return KGError due to timeout")
	assert.Contains(t, kgErr.String(), "timeout while waiting for prices", "Error should mention timeout")
	assert.Contains(t, kgErr.String(), "2 tokens missing out of 2 total tokens", "Error should mention missing token count")

	// Result should be an empty map (not nil) since no prices were found
	require.NotNil(t, result, "Result should be an empty map, not nil")
	assert.Empty(t, result, "Result should be empty since no prices were found")
}

func TestGetPricesByContract_PartialTimeout(t *testing.T) {
	// Create gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)

	// Test tokens - we'll get price for one but not the other
	solToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(),
	}
	pythToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump",
	}
	tokens := []domain.ChainToken{solToken, pythToken}

	// Expected price for SOL only
	expectedSOLPrice := domain.Price(150.25)

	// AddRealtimeTokenPriceTask will be called once
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).Times(1)

	// Track call count to simulate getting SOL price after a few calls, but never getting PYTH price
	callCount := 0
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, requestedTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		callCount++

		// After 5 calls (500ms), return SOL price but not PYTH
		if callCount >= 5 {
			// Check if SOL is still in the requested tokens
			for _, token := range requestedTokens {
				if token == solToken {
					// Return only SOL price as hot price, PYTH will remain missing
					return []domain.RealtimeTokenPriceResponse{
						{
							ChainToken:     solToken,
							PriceUSD:       expectedSOLPrice,
							LastUpdateTime: time.Now(),
						},
					}, []domain.RealtimeTokenPriceResponse{}, nil
				}
			}
		}

		// Initially return empty (no prices available yet)
		return []domain.RealtimeTokenPriceResponse{}, []domain.RealtimeTokenPriceResponse{}, nil
	}).MinTimes(25).MaxTimes(35) // Should be called about 30 times over 3 seconds

	// priceFetcher may be called but shouldn't be needed for this test
	mockPriceFetcher.EXPECT().
		GetPricesByContract(gomock.Any(), gomock.Any()).
		Return(map[domain.ChainToken]domain.Price{}, nil).
		MaxTimes(1)

	ctx := context.Background()
	ctx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()
	start := time.Now()

	// This should timeout after approximately 3 seconds because PYTH price is never found
	result, kgErr := GetPricesByContract(ctx, tokens)

	duration := time.Since(start)

	// Verify that it actually took close to 3 seconds
	assert.True(t, duration >= 2500*time.Millisecond, "Should take at least 2.5 seconds due to timeout")
	assert.True(t, duration <= 4000*time.Millisecond, "Should not take more than 4 seconds")

	// Should return a timeout error because not all tokens were found
	require.NotNil(t, kgErr, "Should return KGError due to partial timeout")
	assert.Contains(t, kgErr.String(), "timeout while waiting for prices", "Error should mention timeout")
	assert.Contains(t, kgErr.String(), "1 tokens missing out of 2 total tokens", "Error should mention 1 missing token")

	// Result should contain the SOL price that was found
	require.NotNil(t, result, "Result should not be nil")
	assert.Len(t, result, 1, "Result should contain 1 price (SOL)")
	assert.Equal(t, expectedSOLPrice, result[solToken], "SOL price should be found")
	assert.NotContains(t, result, pythToken, "PYTH price should not be found")
}

func TestGetPricesByContract_WithBackgroundProcessing(t *testing.T) {
	// Create gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Solana}).AnyTimes()
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)

	// Test tokens
	solToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: domain.Solana.MainToken().ID(),
	}
	pythToken := domain.ChainToken{
		Chain:   domain.Solana,
		TokenID: "BcZ23crZndSyvzayD4ySCEtwyhFw39AnaB6ZeJmSpump",
	}
	tokens := []domain.ChainToken{solToken, pythToken}

	// Expected prices that will be fetched by priceFetcher
	expectedPrices := map[domain.ChainToken]domain.Price{
		solToken:  domain.Price(150.25),
		pythToken: domain.Price(8.95),
	}

	// Phase 1: GetPricesByContract call (cache miss)

	// AddRealtimeTokenPriceTask will be called when GetPricesByContract is called
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasks []domain.RealtimeTokenPriceTask) error {
		// Verify the tasks were created correctly
		assert.Len(t, tasks, 2, "Should add 2 tasks for 2 tokens")
		tokenSet := make(map[domain.ChainToken]bool)
		for _, task := range tasks {
			tokenSet[task.ChainToken] = true
			assert.Equal(t, 0, task.RetryCount, "Initial retry count should be 0")
		}
		assert.True(t, tokenSet[solToken], "Should include SOL token task")
		assert.True(t, tokenSet[pythToken], "Should include PYTH token task")
		return nil
	}).Times(1)

	// GetRealtimeTokenPrice will be called multiple times (polling until timeout)
	// Initially return empty (cache miss)
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		// Always return empty during this phase (simulating cache miss)
		return []domain.RealtimeTokenPriceResponse{}, []domain.RealtimeTokenPriceResponse{}, nil
	}).MinTimes(25).MaxTimes(35) // Should be called about 30 times over 3 seconds

	// Phase 2: AcquireAndProcessTasks call (background processing)

	// AcquireRealtimeTokenPriceTask will be called by AcquireAndProcessTasks
	// First call returns tasks, second call returns empty (causes function to return)
	callCount := 0
	mockRepo.EXPECT().AcquireRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, batchSize int) ([]domain.RealtimeTokenPriceTask, error) {
		callCount++
		if callCount == 1 {
			// First call: return the tasks that were added in phase 1
			tasks := []domain.RealtimeTokenPriceTask{
				{ChainToken: solToken, RetryCount: 0},
				{ChainToken: pythToken, RetryCount: 0},
			}
			return tasks, nil
		}
		// Second call: return empty to cause AcquireAndProcessTasks to return
		return []domain.RealtimeTokenPriceTask{}, nil
	}).Times(2)

	// priceFetcher.GetPricesByContract will be called by AcquireAndProcessTasks
	mockPriceFetcher.EXPECT().GetPricesByContract(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, requestedTokens []domain.ChainToken) (map[domain.ChainToken]domain.Price, error) {
		// Verify the correct tokens are requested
		assert.Len(t, requestedTokens, 2, "Should request prices for 2 tokens")
		tokenSet := make(map[domain.ChainToken]bool)
		for _, token := range requestedTokens {
			tokenSet[token] = true
		}
		assert.True(t, tokenSet[solToken], "Should request SOL token price")
		assert.True(t, tokenSet[pythToken], "Should request PYTH token price")

		// Return the expected prices
		return expectedPrices, nil
	}).Times(1)

	// AddRealtimeTokenPriceResponse will be called by AcquireAndProcessTasks
	mockRepo.EXPECT().AddRealtimeTokenPriceResponse(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, prices []domain.RealtimeTokenPriceResponse) error {
		// Verify the correct prices are stored
		assert.Len(t, prices, 2, "Should store 2 price responses")
		priceMap := make(map[domain.ChainToken]domain.Price)
		for _, price := range prices {
			priceMap[price.ChainToken] = price.PriceUSD
			assert.True(t, price.LastUpdateTime.After(time.Now().Add(-1*time.Second)), "LastUpdateTime should be recent")
		}
		assert.Equal(t, expectedPrices[solToken], priceMap[solToken], "SOL price should match expected")
		assert.Equal(t, expectedPrices[pythToken], priceMap[pythToken], "PYTH price should match expected")
		return nil
	}).Times(1)

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	// Execute Phase 1: Call GetPricesByContract (should timeout due to cache miss)
	result1, kgErr := GetPricesByContract(ctx, tokens)

	// Should return timeout error since no prices are in cache
	require.NotNil(t, kgErr, "Should return timeout error")
	assert.Contains(t, kgErr.String(), "timeout while waiting for prices", "Should be a timeout error")
	assert.NotNil(t, result1, "Result should be an empty map, not nil")
	assert.Empty(t, result1, "Result should be empty on cache miss")

	ctx2, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// Execute Phase 2: Simulate background processing with AcquireAndProcessTasks
	processingCtx, cancel := context.WithTimeout(ctx2, 2*time.Second)
	defer cancel()

	// This should process the tasks and populate the cache
	AcquireAndProcessTasks(processingCtx)

	// Phase 3: Verify that cached prices can now be retrieved successfully

	// AddRealtimeTokenPriceTask will be called again for the third GetPricesByContract call
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).Times(1)

	// GetRealtimeTokenPrice should now return the cached prices immediately
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, requestedTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		// Return cached prices immediately as hot prices (simulating successful cache hit)
		hotResponses := make([]domain.RealtimeTokenPriceResponse, 0, len(requestedTokens))
		for _, token := range requestedTokens {
			if price, exists := expectedPrices[token]; exists {
				hotResponses = append(hotResponses, domain.RealtimeTokenPriceResponse{
					ChainToken:     token,
					PriceUSD:       price,
					LastUpdateTime: time.Now(),
				})
			}
		}
		return hotResponses, []domain.RealtimeTokenPriceResponse{}, nil
	}).Times(1) // Should be called only once since prices are found immediately

	// Execute Phase 3: Call GetPricesByContract again (should succeed with cached prices)
	ctx3, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	result2, kgErr := GetPricesByContract(ctx3, tokens)

	// Should succeed this time
	require.Nil(t, kgErr, "Should not return error when prices are cached")
	require.NotNil(t, result2, "Result should not be nil")
	assert.Len(t, result2, 2, "Should return prices for both tokens")

	// Verify the cached prices match expectations
	assert.Equal(t, expectedPrices[solToken], result2[solToken], "Cached SOL price should match expected")
	assert.Equal(t, expectedPrices[pythToken], result2[pythToken], "Cached PYTH price should match expected")

	// Verify that the background processing completed successfully
	// Note: In a real scenario, this would be verified by checking if subsequent
	// GetPricesByContract calls return the cached prices, but since we're in a
	// unit test environment, the verification is done through the mock expectations above
}
