// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/service/asset (interfaces: IRepo)
//
// Generated by this command:
//
//	mockgen -package=asset -self_package=github.com/kryptogo/kg-wallet-backend/service/asset -destination=common_mock.go . IRepo
//

// Package asset is a generated GoMock package.
package asset

import (
	context "context"
	reflect "reflect"
	time "time"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockIRepo is a mock of IRepo interface.
type MockIRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIRepoMockRecorder
	isgomock struct{}
}

// MockIRepoMockRecorder is the mock recorder for MockIRepo.
type MockIRepoMockRecorder struct {
	mock *MockIRepo
}

// NewMockIRepo creates a new mock instance.
func NewMockIRepo(ctrl *gomock.Controller) *MockIRepo {
	mock := &MockIRepo{ctrl: ctrl}
	mock.recorder = &MockIRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRepo) EXPECT() *MockIRepoMockRecorder {
	return m.recorder
}

// AcquireRealtimeTokenPriceTask mocks base method.
func (m *MockIRepo) AcquireRealtimeTokenPriceTask(ctx context.Context, limit int) ([]domain.RealtimeTokenPriceTask, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireRealtimeTokenPriceTask", ctx, limit)
	ret0, _ := ret[0].([]domain.RealtimeTokenPriceTask)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AcquireRealtimeTokenPriceTask indicates an expected call of AcquireRealtimeTokenPriceTask.
func (mr *MockIRepoMockRecorder) AcquireRealtimeTokenPriceTask(ctx, limit any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireRealtimeTokenPriceTask", reflect.TypeOf((*MockIRepo)(nil).AcquireRealtimeTokenPriceTask), ctx, limit)
}

// AddAssetPricesUpdateJob mocks base method.
func (m *MockIRepo) AddAssetPricesUpdateJob(ctx context.Context, walletAddresses []domain.ChainAddress) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAssetPricesUpdateJob", ctx, walletAddresses)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddAssetPricesUpdateJob indicates an expected call of AddAssetPricesUpdateJob.
func (mr *MockIRepoMockRecorder) AddAssetPricesUpdateJob(ctx, walletAddresses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAssetPricesUpdateJob", reflect.TypeOf((*MockIRepo)(nil).AddAssetPricesUpdateJob), ctx, walletAddresses)
}

// AddRealtimeTokenPriceResponse mocks base method.
func (m *MockIRepo) AddRealtimeTokenPriceResponse(ctx context.Context, info []domain.RealtimeTokenPriceResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRealtimeTokenPriceResponse", ctx, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRealtimeTokenPriceResponse indicates an expected call of AddRealtimeTokenPriceResponse.
func (mr *MockIRepoMockRecorder) AddRealtimeTokenPriceResponse(ctx, info any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRealtimeTokenPriceResponse", reflect.TypeOf((*MockIRepo)(nil).AddRealtimeTokenPriceResponse), ctx, info)
}

// AddRealtimeTokenPriceTask mocks base method.
func (m *MockIRepo) AddRealtimeTokenPriceTask(ctx context.Context, info []domain.RealtimeTokenPriceTask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRealtimeTokenPriceTask", ctx, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRealtimeTokenPriceTask indicates an expected call of AddRealtimeTokenPriceTask.
func (mr *MockIRepoMockRecorder) AddRealtimeTokenPriceTask(ctx, info any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRealtimeTokenPriceTask", reflect.TypeOf((*MockIRepo)(nil).AddRealtimeTokenPriceTask), ctx, info)
}

// BatchCreateAssetPriceHistories mocks base method.
func (m *MockIRepo) BatchCreateAssetPriceHistories(ctx context.Context, assetPrices []*domain.TokenPrice) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateAssetPriceHistories", ctx, assetPrices)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreateAssetPriceHistories indicates an expected call of BatchCreateAssetPriceHistories.
func (mr *MockIRepoMockRecorder) BatchCreateAssetPriceHistories(ctx, assetPrices any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateAssetPriceHistories", reflect.TypeOf((*MockIRepo)(nil).BatchCreateAssetPriceHistories), ctx, assetPrices)
}

// BatchGetTokenPrices mocks base method.
func (m *MockIRepo) BatchGetTokenPrices(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPrices", ctx, tokens)
	ret0, _ := ret[0].(map[domain.ChainToken]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPrices indicates an expected call of BatchGetTokenPrices.
func (mr *MockIRepoMockRecorder) BatchGetTokenPrices(ctx, tokens any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPrices", reflect.TypeOf((*MockIRepo)(nil).BatchGetTokenPrices), ctx, tokens)
}

// BatchGetTokenPricesIn24H mocks base method.
func (m *MockIRepo) BatchGetTokenPricesIn24H(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken][]*domain.PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPricesIn24H", ctx, tokens)
	ret0, _ := ret[0].(map[domain.ChainToken][]*domain.PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPricesIn24H indicates an expected call of BatchGetTokenPricesIn24H.
func (mr *MockIRepoMockRecorder) BatchGetTokenPricesIn24H(ctx, tokens any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPricesIn24H", reflect.TypeOf((*MockIRepo)(nil).BatchGetTokenPricesIn24H), ctx, tokens)
}

// DeleteAssetPriceHistories mocks base method.
func (m *MockIRepo) DeleteAssetPriceHistories(ctx context.Context, createdAt time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAssetPriceHistories", ctx, createdAt)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAssetPriceHistories indicates an expected call of DeleteAssetPriceHistories.
func (mr *MockIRepoMockRecorder) DeleteAssetPriceHistories(ctx, createdAt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAssetPriceHistories", reflect.TypeOf((*MockIRepo)(nil).DeleteAssetPriceHistories), ctx, createdAt)
}

// DeleteTokenAmounts mocks base method.
func (m *MockIRepo) DeleteTokenAmounts(ctx context.Context, amounts map[domain.ChainAddress][]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTokenAmounts", ctx, amounts)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTokenAmounts indicates an expected call of DeleteTokenAmounts.
func (mr *MockIRepoMockRecorder) DeleteTokenAmounts(ctx, amounts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTokenAmounts", reflect.TypeOf((*MockIRepo)(nil).DeleteTokenAmounts), ctx, amounts)
}

// GetAllTokens mocks base method.
func (m *MockIRepo) GetAllTokens(ctx context.Context) ([]domain.ChainToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTokens", ctx)
	ret0, _ := ret[0].([]domain.ChainToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTokens indicates an expected call of GetAllTokens.
func (mr *MockIRepoMockRecorder) GetAllTokens(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTokens", reflect.TypeOf((*MockIRepo)(nil).GetAllTokens), ctx)
}

// GetAssetBalances mocks base method.
func (m *MockIRepo) GetAssetBalances(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType) (map[domain.ChainAddress]map[domain.AssetType]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetBalances", ctx, chains, addresses, types)
	ret0, _ := ret[0].(map[domain.ChainAddress]map[domain.AssetType]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetBalances indicates an expected call of GetAssetBalances.
func (mr *MockIRepoMockRecorder) GetAssetBalances(ctx, chains, addresses, types any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetBalances", reflect.TypeOf((*MockIRepo)(nil).GetAssetBalances), ctx, chains, addresses, types)
}

// GetAssetPrice mocks base method.
func (m *MockIRepo) GetAssetPrice(ctx context.Context, chainID, contractAddress string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", ctx, chainID, contractAddress)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockIRepoMockRecorder) GetAssetPrice(ctx, chainID, contractAddress any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockIRepo)(nil).GetAssetPrice), ctx, chainID, contractAddress)
}

// GetAssetPriceQueueInfo mocks base method.
func (m *MockIRepo) GetAssetPriceQueueInfo(ctx context.Context) (*domain.AssetPriceQueueInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPriceQueueInfo", ctx)
	ret0, _ := ret[0].(*domain.AssetPriceQueueInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPriceQueueInfo indicates an expected call of GetAssetPriceQueueInfo.
func (mr *MockIRepoMockRecorder) GetAssetPriceQueueInfo(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPriceQueueInfo", reflect.TypeOf((*MockIRepo)(nil).GetAssetPriceQueueInfo), ctx)
}

// GetAssetsTotalUsdValue mocks base method.
func (m *MockIRepo) GetAssetsTotalUsdValue(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetsTotalUsdValue", ctx, chains, addresses, types)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetsTotalUsdValue indicates an expected call of GetAssetsTotalUsdValue.
func (mr *MockIRepoMockRecorder) GetAssetsTotalUsdValue(ctx, chains, addresses, types any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetsTotalUsdValue", reflect.TypeOf((*MockIRepo)(nil).GetAssetsTotalUsdValue), ctx, chains, addresses, types)
}

// GetNativeAssetPrice mocks base method.
func (m *MockIRepo) GetNativeAssetPrice(ctx context.Context, chainID string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", ctx, chainID)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockIRepoMockRecorder) GetNativeAssetPrice(ctx, chainID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockIRepo)(nil).GetNativeAssetPrice), ctx, chainID)
}

// GetPastAssetBalances mocks base method.
func (m *MockIRepo) GetPastAssetBalances(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType, from time.Time) (map[domain.ChainAddress]map[domain.AssetType]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPastAssetBalances", ctx, chains, addresses, types, from)
	ret0, _ := ret[0].(map[domain.ChainAddress]map[domain.AssetType]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPastAssetBalances indicates an expected call of GetPastAssetBalances.
func (mr *MockIRepoMockRecorder) GetPastAssetBalances(ctx, chains, addresses, types, from any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPastAssetBalances", reflect.TypeOf((*MockIRepo)(nil).GetPastAssetBalances), ctx, chains, addresses, types, from)
}

// GetPastAssetsTotalUsdValue mocks base method.
func (m *MockIRepo) GetPastAssetsTotalUsdValue(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType, from time.Time) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPastAssetsTotalUsdValue", ctx, chains, addresses, types, from)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPastAssetsTotalUsdValue indicates an expected call of GetPastAssetsTotalUsdValue.
func (mr *MockIRepoMockRecorder) GetPastAssetsTotalUsdValue(ctx, chains, addresses, types, from any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPastAssetsTotalUsdValue", reflect.TypeOf((*MockIRepo)(nil).GetPastAssetsTotalUsdValue), ctx, chains, addresses, types, from)
}

// GetRealtimeTokenPrice mocks base method.
func (m *MockIRepo) GetRealtimeTokenPrice(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRealtimeTokenPrice", ctx, chainTokens)
	ret0, _ := ret[0].([]domain.RealtimeTokenPriceResponse)
	ret1, _ := ret[1].([]domain.RealtimeTokenPriceResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRealtimeTokenPrice indicates an expected call of GetRealtimeTokenPrice.
func (mr *MockIRepoMockRecorder) GetRealtimeTokenPrice(ctx, chainTokens any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRealtimeTokenPrice", reflect.TypeOf((*MockIRepo)(nil).GetRealtimeTokenPrice), ctx, chainTokens)
}

// GetSingleAsset mocks base method.
func (m *MockIRepo) GetSingleAsset(ctx context.Context, chain domain.Chain, assetType domain.AssetType, assetID string, addresses []domain.Address) (domain.Asset, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSingleAsset", ctx, chain, assetType, assetID, addresses)
	ret0, _ := ret[0].(domain.Asset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSingleAsset indicates an expected call of GetSingleAsset.
func (mr *MockIRepoMockRecorder) GetSingleAsset(ctx, chain, assetType, assetID, addresses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSingleAsset", reflect.TypeOf((*MockIRepo)(nil).GetSingleAsset), ctx, chain, assetType, assetID, addresses)
}

// GetTokenPrice mocks base method.
func (m *MockIRepo) GetTokenPrice(ctx context.Context, chain domain.Chain, tokenID string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPrice", ctx, chain, tokenID)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPrice indicates an expected call of GetTokenPrice.
func (mr *MockIRepoMockRecorder) GetTokenPrice(ctx, chain, tokenID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPrice", reflect.TypeOf((*MockIRepo)(nil).GetTokenPrice), ctx, chain, tokenID)
}

// GetTokenPricesIn24H mocks base method.
func (m *MockIRepo) GetTokenPricesIn24H(ctx context.Context, chain domain.Chain, tokenID string) ([]*domain.PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPricesIn24H", ctx, chain, tokenID)
	ret0, _ := ret[0].([]*domain.PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPricesIn24H indicates an expected call of GetTokenPricesIn24H.
func (mr *MockIRepoMockRecorder) GetTokenPricesIn24H(ctx, chain, tokenID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPricesIn24H", reflect.TypeOf((*MockIRepo)(nil).GetTokenPricesIn24H), ctx, chain, tokenID)
}

// GetTokensByAddress mocks base method.
func (m *MockIRepo) GetTokensByAddress(ctx context.Context, addresses map[domain.Chain][]domain.Address) ([]domain.ChainToken, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokensByAddress", ctx, addresses)
	ret0, _ := ret[0].([]domain.ChainToken)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokensByAddress indicates an expected call of GetTokensByAddress.
func (mr *MockIRepoMockRecorder) GetTokensByAddress(ctx, addresses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokensByAddress", reflect.TypeOf((*MockIRepo)(nil).GetTokensByAddress), ctx, addresses)
}

// ListAssets mocks base method.
func (m *MockIRepo) ListAssets(ctx context.Context, params *domain.ListAssetsParam) ([]domain.Asset, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAssets", ctx, params)
	ret0, _ := ret[0].([]domain.Asset)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListAssets indicates an expected call of ListAssets.
func (mr *MockIRepoMockRecorder) ListAssets(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAssets", reflect.TypeOf((*MockIRepo)(nil).ListAssets), ctx, params)
}

// ListMainTokens mocks base method.
func (m *MockIRepo) ListMainTokens(ctx context.Context, addresses map[domain.Chain][]domain.Address) ([]domain.Asset, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMainTokens", ctx, addresses)
	ret0, _ := ret[0].([]domain.Asset)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMainTokens indicates an expected call of ListMainTokens.
func (mr *MockIRepoMockRecorder) ListMainTokens(ctx, addresses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMainTokens", reflect.TypeOf((*MockIRepo)(nil).ListMainTokens), ctx, addresses)
}

// PopAllAssetPriceUpdateJobs mocks base method.
func (m *MockIRepo) PopAllAssetPriceUpdateJobs(ctx context.Context) ([]domain.ChainAddress, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PopAllAssetPriceUpdateJobs", ctx)
	ret0, _ := ret[0].([]domain.ChainAddress)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PopAllAssetPriceUpdateJobs indicates an expected call of PopAllAssetPriceUpdateJobs.
func (mr *MockIRepoMockRecorder) PopAllAssetPriceUpdateJobs(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PopAllAssetPriceUpdateJobs", reflect.TypeOf((*MockIRepo)(nil).PopAllAssetPriceUpdateJobs), ctx)
}

// SetAssetPriceQueueInfo mocks base method.
func (m *MockIRepo) SetAssetPriceQueueInfo(ctx context.Context, info domain.AssetPriceQueueInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAssetPriceQueueInfo", ctx, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAssetPriceQueueInfo indicates an expected call of SetAssetPriceQueueInfo.
func (mr *MockIRepoMockRecorder) SetAssetPriceQueueInfo(ctx, info any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAssetPriceQueueInfo", reflect.TypeOf((*MockIRepo)(nil).SetAssetPriceQueueInfo), ctx, info)
}

// SetAssetPrices mocks base method.
func (m *MockIRepo) SetAssetPrices(ctx context.Context, prices []*domain.TokenPrice) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAssetPrices", ctx, prices)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAssetPrices indicates an expected call of SetAssetPrices.
func (mr *MockIRepoMockRecorder) SetAssetPrices(ctx, prices any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAssetPrices", reflect.TypeOf((*MockIRepo)(nil).SetAssetPrices), ctx, prices)
}

// SetDefiAssets mocks base method.
func (m *MockIRepo) SetDefiAssets(ctx context.Context, amounts map[domain.ChainAddress][]*domain.DefiAsset) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDefiAssets", ctx, amounts)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDefiAssets indicates an expected call of SetDefiAssets.
func (mr *MockIRepoMockRecorder) SetDefiAssets(ctx, amounts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDefiAssets", reflect.TypeOf((*MockIRepo)(nil).SetDefiAssets), ctx, amounts)
}

// SetNftAmounts mocks base method.
func (m *MockIRepo) SetNftAmounts(ctx context.Context, amounts map[domain.ChainAddress][]*domain.NftAmount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNftAmounts", ctx, amounts)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetNftAmounts indicates an expected call of SetNftAmounts.
func (mr *MockIRepoMockRecorder) SetNftAmounts(ctx, amounts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNftAmounts", reflect.TypeOf((*MockIRepo)(nil).SetNftAmounts), ctx, amounts)
}

// SetTokenAmounts mocks base method.
func (m *MockIRepo) SetTokenAmounts(ctx context.Context, amounts map[domain.ChainAddress][]*domain.TokenAmount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTokenAmounts", ctx, amounts)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTokenAmounts indicates an expected call of SetTokenAmounts.
func (mr *MockIRepoMockRecorder) SetTokenAmounts(ctx, amounts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTokenAmounts", reflect.TypeOf((*MockIRepo)(nil).SetTokenAmounts), ctx, amounts)
}

// UpdateDefiAssets mocks base method.
func (m *MockIRepo) UpdateDefiAssets(ctx context.Context, defiAssetMap map[domain.ChainAddress][]*domain.DefiAsset) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDefiAssets", ctx, defiAssetMap)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDefiAssets indicates an expected call of UpdateDefiAssets.
func (mr *MockIRepoMockRecorder) UpdateDefiAssets(ctx, defiAssetMap any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDefiAssets", reflect.TypeOf((*MockIRepo)(nil).UpdateDefiAssets), ctx, defiAssetMap)
}

// UpdateTokenAmounts mocks base method.
func (m *MockIRepo) UpdateTokenAmounts(ctx context.Context, amounts map[domain.ChainAddress][]*domain.TokenAmount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTokenAmounts", ctx, amounts)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTokenAmounts indicates an expected call of UpdateTokenAmounts.
func (mr *MockIRepoMockRecorder) UpdateTokenAmounts(ctx, amounts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTokenAmounts", reflect.TypeOf((*MockIRepo)(nil).UpdateTokenAmounts), ctx, amounts)
}
