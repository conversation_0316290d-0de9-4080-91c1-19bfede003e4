package asset

import (
	"context"
	"fmt"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestUpdatePrices_Token(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Ethereum, domain.Polygon}).AnyTimes()
	mockTokenMeta := domain.NewMockTokenMetadataRepo(ctrl)

	// Data used for testing
	metadata := make(map[domain.ChainToken]*domain.TokenMetadata)
	prices := make(map[domain.CoingeckoID]domain.Price)
	testAddr := domain.NewEvmAddress("0xadd1111")

	// Mocking the necessary methods
	mockRepo.EXPECT().GetTokensByAddress(gomock.Any(), gomock.Any()).Return([]domain.ChainToken{}, nil)
	mockRepo.EXPECT().ListAssets(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, param *domain.ListAssetsParam) ([]domain.Asset, int, error) {
		if param.Types[0] == domain.AssetTypeNft {
			return []domain.Asset{
				// To test duplicated assets
				domain.NewNftAsset(domain.Ethereum, "nft-123", "a-nft-asset", "logo-url", false),
				domain.NewNftAsset(domain.Ethereum, "nft-123", "a-nft-asset", "logo-url", false),
			}, 0, nil
		}
		return []domain.Asset{}, 0, nil
	})
	mockTokenMeta.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
		count := 0
		for _, chainToken := range chainTokens {
			// t.Logf("chainToken: %+v", chainToken)
			metadata[chainToken] = &domain.TokenMetadata{
				CoingeckoID: domain.CoingeckoID(fmt.Sprintf("token-%d", count)),
			}
			count = (count + 1) % 3
		}
		metadata[domain.ChainToken{
			Chain:   domain.Ethereum,
			TokenID: "nft-123",
		}] = &domain.TokenMetadata{
			CoingeckoID: "a-nft-cid",
		}
		return metadata, nil
	})
	mockPriceFetcher.EXPECT().GetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ids []domain.CoingeckoID) (map[domain.CoingeckoID]domain.Price, error) {
		for i, id := range ids {
			prices[id] = domain.Price(1.14514 + float64(i))
		}
		prices[domain.Ethereum.MainCoingeckoID()] = 5.114514
		return prices, nil
	})
	// Mock AddRealtimeTokenPriceTask for the new implementation - called once per UpdatePrices call
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// Mock GetRealtimeTokenPrice to return prices for all requested tokens (even if 0)
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		var responses []domain.RealtimeTokenPriceResponse
		for _, chainToken := range chainTokens {
			// Return 0 price for all tokens to avoid timeout
			responses = append(responses, domain.RealtimeTokenPriceResponse{
				ChainToken: chainToken,
				PriceUSD:   0, // 0 price for NFT test since we don't need contract prices
			})
		}
		return responses, []domain.RealtimeTokenPriceResponse{}, nil
	}).Times(1)
	mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, assetPrices []*domain.TokenPrice) error {
		for _, asset := range assetPrices {
			t.Logf("asset: %+v", asset)
			// Check if this asset has a coingecko price or contract price
			chainToken := domain.ChainToken{
				Chain:   asset.Chain,
				TokenID: asset.ID,
			}
			if meta, ok := metadata[chainToken]; ok && meta.CoingeckoID != "" {
				// This asset has coingecko metadata, should use coingecko price
				coingeckoID := metadata[chainToken].CoingeckoID
				assert.Equal(t, asset.Price, float64(prices[coingeckoID]))
			} else {
				// This asset doesn't have coingecko metadata, should use contract price
				// Contract prices start from 2.14514
				assert.GreaterOrEqual(t, asset.Price, 2.14514)
			}
		}
		return nil
	})
	mockRepo.EXPECT().UpdateDefiAssets(gomock.Any(), gomock.Any()).AnyTimes().Return(nil)
	mockRepo.EXPECT().BatchCreateAssetPriceHistories(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, assetPrices []*domain.TokenPrice) error {
		// Uniqueness check
		uniqueAssets := lo.UniqBy(assetPrices, func(tp *domain.TokenPrice) domain.ChainToken {
			return domain.ChainToken{
				Chain:   tp.Chain,
				TokenID: tp.ID,
			}
		})
		assert.Equal(t, uniqueAssets, assetPrices)

		for _, asset := range assetPrices {
			t.Logf("asset: %+v", asset)
			chainToken := domain.ChainToken{
				Chain:   asset.Chain,
				TokenID: asset.ID,
			}
			coingeckoID := metadata[chainToken].CoingeckoID
			assert.Equal(t, asset.Price, float64(prices[coingeckoID]))
		}
		return nil
	})
	mockRepo.EXPECT().DeleteAssetPriceHistories(gomock.Any(), gomock.Any()).Times(1).Return(nil)

	// Initialize the repo and other dependencies
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)
	tokenmeta.Init(mockTokenMeta, nil, nil)

	ctx := context.Background()
	addresses := map[domain.Chain][]domain.Address{
		domain.Ethereum: {testAddr},
	}

	err := UpdatePrices(ctx, addresses, false)

	assert.Nil(t, err)
}

func TestUpdatePrices_Nft(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Ethereum, domain.Polygon}).AnyTimes()
	mockTokenMeta := domain.NewMockTokenMetadataRepo(ctrl)

	// Data used for testing
	metadata := make(map[domain.ChainToken]*domain.TokenMetadata)
	prices := make(map[domain.CoingeckoID]domain.Price)
	testAddr := domain.NewEvmAddress("0xadd1111")
	nftID := "0x333222nft"
	// eth price
	metadata[domain.ChainToken{
		Chain:   domain.Ethereum,
		TokenID: domain.Ethereum.ID(),
	}] = &domain.TokenMetadata{
		CoingeckoID: domain.Ethereum.MainCoingeckoID(),
	}
	prices[domain.Ethereum.MainCoingeckoID()] = 71.22

	// nft asset to test
	nftAsset := domain.NewNftAsset(domain.Polygon, nftID, "a-nft-asset", "logo-url", false)
	nftAsset.FloorPriceETH = 2.5
	nftAsset.WalletAmounts = append(nftAsset.WalletAmounts, &domain.WalletNftAmount{
		Address: testAddr,
		Amount:  1,
	})

	metadata[domain.ChainToken{
		Chain:   domain.Polygon,
		TokenID: nftID,
	}] = &domain.TokenMetadata{
		CoingeckoID: "a-nft-cid",
	}
	prices["a-nft-cid"] = 1.42857

	// Mocking the necessary methods
	mockRepo.EXPECT().GetTokensByAddress(gomock.Any(), gomock.Any()).Return([]domain.ChainToken{}, nil)
	mockRepo.EXPECT().ListAssets(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, param *domain.ListAssetsParam) ([]domain.Asset, int, error) {
		assert.Equal(t, len(param.Types), 1)
		switch param.Types[0] {
		case domain.AssetTypeNft:
			return []domain.Asset{
				nftAsset,
			}, 0, nil
		case domain.AssetTypeDefi:
			return []domain.Asset{}, 0, nil
		default:
			t.Fatalf("Unknown asset type: %v", param.Types[0])
			return nil, 0, nil
		}
	})
	mockTokenMeta.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).AnyTimes().Return(metadata, nil)
	mockPriceFetcher.EXPECT().GetPrices(gomock.Any(), gomock.Any()).Return(prices, nil)
	// Mock AddRealtimeTokenPriceTask for the new implementation - called once per UpdatePrices call
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// Mock GetRealtimeTokenPrice to return prices for all requested tokens (even if 0)
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		var responses []domain.RealtimeTokenPriceResponse
		for _, chainToken := range chainTokens {
			// Return 0 price for all tokens to avoid timeout
			responses = append(responses, domain.RealtimeTokenPriceResponse{
				ChainToken: chainToken,
				PriceUSD:   0, // 0 price for NFT test since we don't need contract prices
			})
		}
		return responses, []domain.RealtimeTokenPriceResponse{}, nil
	}).Times(1)
	mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, assetPrices []*domain.TokenPrice) error {
		for _, asset := range assetPrices {
			t.Logf("asset: %+v", asset)
			// Check if this is the NFT asset
			if asset.Chain.ID() == "matic" && asset.ID == nftID {
				// This should be NFT price calculated from ETH price
				expectedPrice := nftAsset.FloorPriceETH * float64(prices[domain.Ethereum.MainCoingeckoID()])
				assert.InEpsilon(t, asset.Price, expectedPrice, 1e-9)
			} else {
				// Other assets (necessary tokens) may have 0 price, just check they exist
				assert.GreaterOrEqual(t, asset.Price, 0.0)
			}
		}
		return nil
	})
	mockRepo.EXPECT().UpdateDefiAssets(gomock.Any(), gomock.Any()).AnyTimes().Return(nil)
	mockRepo.EXPECT().BatchCreateAssetPriceHistories(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, assetPrices []*domain.TokenPrice) error {
		for _, asset := range assetPrices {
			t.Logf("asset: %+v", asset)
			// Check if this is the NFT asset
			if asset.Chain.ID() == "matic" && asset.ID == nftID {
				// This should be NFT price calculated from ETH price
				expectedPrice := nftAsset.FloorPriceETH * float64(prices[domain.Ethereum.MainCoingeckoID()])
				assert.InEpsilon(t, asset.Price, expectedPrice, 1e-9)
			} else {
				// Other assets (necessary tokens) may have 0 price, just check they exist
				assert.GreaterOrEqual(t, asset.Price, 0.0)
			}
		}
		return nil
	})
	mockRepo.EXPECT().DeleteAssetPriceHistories(gomock.Any(), gomock.Any()).Times(1).Return(nil)

	// Initialize the repo and other dependencies
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)
	tokenmeta.Init(mockTokenMeta, nil, nil)

	ctx := context.Background()
	addresses := map[domain.Chain][]domain.Address{
		domain.Ethereum: {testAddr},
	}

	err := UpdatePrices(ctx, addresses, false)

	assert.Nil(t, err)
}

func TestUpdatePrices_Defi(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Ethereum, domain.Polygon}).AnyTimes()
	mockTokenMeta := domain.NewMockTokenMetadataRepo(ctrl)

	// Data used for testing
	metadata := make(map[domain.ChainToken]*domain.TokenMetadata)
	prices := make(map[domain.CoingeckoID]domain.Price)
	testAddr := domain.NewEvmAddress("0xadd1111")

	// defiAsset to test
	defiAsset := domain.NewDefiAsset(domain.Polygon, "0x111111", "a-defi-asset", "site-url")
	defiAsset.WalletValues = append(defiAsset.WalletValues, &domain.WalletValue{
		Address:  testAddr,
		UsdValue: decimal.NewFromInt(12345678),
	})
	// Update SupplyTokens with distinct IDs
	defiAsset.SupplyTokens = append(defiAsset.SupplyTokens, &domain.DefiToken{
		Amount: decimal.NewFromInt(2),
		ID:     "supply-token-123",
	})
	// Update BorrowTokens with distinct IDs
	defiAsset.BorrowTokens = append(defiAsset.BorrowTokens, &domain.DefiToken{
		Amount: decimal.NewFromInt(1),
		ID:     "borrow-token-456",
	})
	// Add RewardTokens with distinct IDs
	defiAsset.RewardTokens = append(defiAsset.RewardTokens, &domain.DefiToken{
		Amount: decimal.NewFromInt(3),
		ID:     "reward-token-789",
	})
	// Calculate expected USD value based on mocked prices
	// supply-token-123 price: 1.414
	// borrow-token-456 price: 2.515
	// reward-token-789 price: 3.616
	// expectedDefiAssetUsdValue = (2 * 1.414) - (1 * 2.515) + (3 * 3.616) = 2.828 - 2.515 + 10.848 = 11.161
	expectedDefiAssetUsdValue := 11.161

	// Mocking the necessary methods
	mockRepo.EXPECT().GetTokensByAddress(gomock.Any(), gomock.Any()).Return([]domain.ChainToken{}, nil)
	mockRepo.EXPECT().ListAssets(gomock.Any(), gomock.Any()).Times(2).DoAndReturn(func(ctx context.Context, param *domain.ListAssetsParam) ([]domain.Asset, int, error) {
		assert.Equal(t, len(param.Types), 1)
		switch param.Types[0] {
		case domain.AssetTypeNft:
			return []domain.Asset{}, 0, nil
		case domain.AssetTypeDefi:
			return []domain.Asset{defiAsset}, 0, nil
		default:
			t.Fatalf("Unknown asset type: %v", param.Types[0])
			return nil, 0, nil
		}
	})
	mockTokenMeta.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
		for _, chainToken := range chainTokens {
			// t.Logf("chainToken: %+v", chainToken)
			switch chainToken.TokenID {
			case "supply-token-123":
				metadata[chainToken] = &domain.TokenMetadata{
					CoingeckoID: "supply-token-123-cid",
				}
			case "borrow-token-456":
				metadata[chainToken] = &domain.TokenMetadata{
					CoingeckoID: "borrow-token-456-cid",
				}
			case "reward-token-789":
				metadata[chainToken] = &domain.TokenMetadata{
					CoingeckoID: "reward-token-789-cid",
				}
			default:
				metadata[chainToken] = &domain.TokenMetadata{
					CoingeckoID: "does-not-matter",
				}
			}
		}
		return metadata, nil
	})
	mockPriceFetcher.EXPECT().GetPrices(gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, ids []domain.CoingeckoID) (map[domain.CoingeckoID]domain.Price, error) {
		prices["supply-token-123-cid"] = 1.414
		prices["borrow-token-456-cid"] = 2.515
		prices["reward-token-789-cid"] = 3.616

		return prices, nil
	})
	// Mock AddRealtimeTokenPriceTask for the new implementation - called once per UpdatePrices call
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// Mock GetRealtimeTokenPrice to return contract prices
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		var responses []domain.RealtimeTokenPriceResponse
		for _, chainToken := range chainTokens {
			// Return 0 price for all tokens to avoid timeout
			responses = append(responses, domain.RealtimeTokenPriceResponse{
				ChainToken: chainToken,
				PriceUSD:   0, // 0 price for NFT test since we don't need contract prices
			})
		}
		return responses, []domain.RealtimeTokenPriceResponse{}, nil
	}).MinTimes(1).MaxTimes(30) // May be called multiple times due to internal polling
	mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, assetPrices []*domain.TokenPrice) error {
		for _, asset := range assetPrices {
			t.Logf("asset: %+v", asset)
			chainToken := domain.ChainToken{
				Chain:   asset.Chain,
				TokenID: asset.ID,
			}
			coingeckoID := metadata[chainToken].CoingeckoID
			assert.Equal(t, asset.Price, float64(prices[coingeckoID]))
		}
		return nil
	})
	mockRepo.EXPECT().UpdateDefiAssets(gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, defiAssets map[domain.ChainAddress][]*domain.DefiAsset) error {
		t.Logf("defiAssets: %+v", defiAssets)
		for _, assets := range defiAssets {
			assert.Equal(t, len(assets), 1)
			for _, asset := range assets {
				assert.InEpsilon(t, asset.UsdValue(), expectedDefiAssetUsdValue, 1e-5, "Defi Asset USD value should be correctly calculated")
			}
		}
		return nil
	})
	mockRepo.EXPECT().BatchCreateAssetPriceHistories(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, assetPrices []*domain.TokenPrice) error {
		for _, asset := range assetPrices {
			t.Logf("asset: %+v", asset)
			chainToken := domain.ChainToken{
				Chain:   asset.Chain,
				TokenID: asset.ID,
			}
			coingeckoID := metadata[chainToken].CoingeckoID
			assert.Equal(t, asset.Price, float64(prices[coingeckoID]))
		}
		return nil
	})
	mockRepo.EXPECT().DeleteAssetPriceHistories(gomock.Any(), gomock.Any()).Times(1).Return(nil)

	// Initialize the repo and other dependencies
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)
	tokenmeta.Init(mockTokenMeta, nil, nil)

	ctx := context.Background()
	addresses := map[domain.Chain][]domain.Address{
		domain.Ethereum: {testAddr},
	}

	err := UpdatePrices(ctx, addresses, false)

	assert.Nil(t, err)
}

func TestUpdatePrices_All(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Ethereum, domain.Polygon}).AnyTimes()
	mockTokenMeta := domain.NewMockTokenMetadataRepo(ctrl)

	// Data used for testing
	metadata := make(map[domain.ChainToken]*domain.TokenMetadata)
	prices := make(map[domain.CoingeckoID]domain.Price)

	// Setup test tokens
	testTokens := []domain.ChainToken{
		{Chain: domain.Ethereum, TokenID: "token-1"},
		{Chain: domain.Polygon, TokenID: "token-2"},
	}

	// Setup NFT asset
	nftAsset := domain.NewNftAsset(domain.Polygon, "nft-123", "test-nft", "nft-logo-url", false)
	nftAsset.FloorPriceETH = 1.5
	metadata[domain.ChainToken{
		Chain:   domain.Polygon,
		TokenID: "nft-123",
	}] = &domain.TokenMetadata{
		CoingeckoID: "cid-nft-123",
	}
	prices["cid-nft-123"] = 1.5 * 2345.0

	// Setup DeFi asset
	defiAsset := domain.NewDefiAsset(domain.Polygon, "defi-123", "test-defi", "defi-url")
	defiAsset.SupplyTokens = append(defiAsset.SupplyTokens, &domain.DefiToken{
		Amount: decimal.NewFromInt(100),
		ID:     "supply-token-1",
	})
	expectedDefiAssetUsdValue := 100.0 // Expected value based on mocked prices

	// Mock GetTokensByAddress - should return all tokens since updateAllAssets is true
	mockRepo.EXPECT().GetAllTokens(gomock.Any()).Return(testTokens, nil)

	// Mock ListAssets - should return all assets
	mockRepo.EXPECT().ListAssets(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, param *domain.ListAssetsParam) ([]domain.Asset, int, error) {
		assert.Equal(t, param.WithAllAddresses, true)
		assert.Equal(t, len(param.Types), 1)
		switch param.Types[0] {
		case domain.AssetTypeNft:
			return []domain.Asset{nftAsset}, 0, nil
		case domain.AssetTypeDefi:
			return []domain.Asset{defiAsset}, 0, nil
		default:
			return []domain.Asset{}, 0, nil
		}
	})

	// Mock BatchGetTokenMetadata
	mockTokenMeta.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
		for _, chainToken := range chainTokens {
			metadata[chainToken] = &domain.TokenMetadata{
				CoingeckoID: domain.CoingeckoID(fmt.Sprintf("cid-%s", chainToken.TokenID)),
			}
		}
		metadata[domain.ChainToken{
			Chain:   domain.Ethereum,
			TokenID: domain.Ethereum.ID(),
		}] = &domain.TokenMetadata{
			CoingeckoID: domain.Ethereum.MainCoingeckoID(),
		}
		return metadata, nil
	})

	// Mock GetPrices
	mockPriceFetcher.EXPECT().GetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ids []domain.CoingeckoID) (map[domain.CoingeckoID]domain.Price, error) {
		for _, id := range ids {
			prices[id] = 123.45
		}
		prices[domain.Ethereum.MainCoingeckoID()] = 2345.0 // ETH price for NFT calculations
		return prices, nil
	})

	// Mock AddRealtimeTokenPriceTask for the new implementation - called once per UpdatePrices call
	mockRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).Times(1)
	// Mock GetRealtimeTokenPrice to return contract prices
	mockRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, chainTokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		var responses []domain.RealtimeTokenPriceResponse
		for _, chainToken := range chainTokens {
			// Return 0 price for all tokens to avoid timeout
			responses = append(responses, domain.RealtimeTokenPriceResponse{
				ChainToken: chainToken,
				PriceUSD:   0, // 0 price for NFT test since we don't need contract prices
			})
		}
		return responses, []domain.RealtimeTokenPriceResponse{}, nil
	}).MinTimes(1).MaxTimes(30) // May be called multiple times due to internal polling

	// Mock SetAssetPrices
	mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, assetPrices []*domain.TokenPrice) error {
		assert.Equal(t, 15, len(assetPrices))
		for _, asset := range assetPrices {
			t.Logf("asset: %+v", asset)
			chainToken := domain.ChainToken{
				Chain:   asset.Chain,
				TokenID: asset.ID,
			}
			meta, ok := metadata[chainToken]
			assert.True(t, ok, "Token metadata should be found. Chain token: %v", chainToken)
			if ok {
				coingeckoID := domain.CoingeckoID(meta.CoingeckoID)
				// Check if this is a coingecko price or contract price
				if price, exists := prices[coingeckoID]; exists && price > 0 {
					assert.Equal(t, asset.Price, float64(price))
				} else {
					// Should be contract price (234.56)
					assert.Equal(t, asset.Price, 234.56)
				}
			}
		}
		return nil
	})

	// Mock UpdateDefiAssets
	mockRepo.EXPECT().UpdateDefiAssets(gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, defiAssets map[domain.ChainAddress][]*domain.DefiAsset) error {
		t.Logf("defiAssets: %+v", defiAssets)
		for _, assets := range defiAssets {
			for _, asset := range assets {
				assert.InEpsilon(t, asset.UsdValue(), expectedDefiAssetUsdValue, 1e-5, "Defi Asset USD value should be correctly calculated")
			}
		}
		return nil
	})

	// Mock BatchCreateAssetPriceHistories
	mockRepo.EXPECT().BatchCreateAssetPriceHistories(gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, assetPrices []*domain.TokenPrice) error {
		for _, asset := range assetPrices {
			t.Logf("asset: %+v", asset)
			chainToken := domain.ChainToken{
				Chain:   asset.Chain,
				TokenID: asset.ID,
			}
			meta, ok := metadata[chainToken]
			assert.True(t, ok, "Token metadata should be found. Chain token: %v", chainToken)
			if ok {
				coingeckoID := domain.CoingeckoID(meta.CoingeckoID)
				assert.Equal(t, asset.Price, float64(prices[coingeckoID]))
			}
		}
		return nil
	})

	mockRepo.EXPECT().DeleteAssetPriceHistories(gomock.Any(), gomock.Any()).Times(1).Return(nil)

	// Initialize the repo and other dependencies
	Init(mockRepo, nil, nil, nil, mockPriceFetcher)
	tokenmeta.Init(mockTokenMeta, nil, nil)

	// Test with nil addresses and updateAllAssets = true
	err := UpdatePrices(context.Background(), nil, true)

	assert.Nil(t, err)
}
