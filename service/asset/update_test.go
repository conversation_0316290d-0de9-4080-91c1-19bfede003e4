package asset

import (
	"context"
	"errors"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	ws "github.com/kryptogo/kg-wallet-backend/pkg/websocket"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestUpdateAssets(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	fetcher1 := domain.NewMockAssetFetcher(ctrl)
	fetcher2 := domain.NewMockAssetFetcher(ctrl)
	fetcher3 := domain.NewMockAssetFetcher(ctrl)
	fetcher4 := domain.NewMockAssetFetcher(ctrl)
	amountFetcher := domain.NewMockTokenAmountsFetcher(ctrl)

	addr := domain.NewEvmAddress("0x1234")

	// 		     ETH     ARB    POLY
	// 		 +-------+-------+-------+
	// TOKEN |   F1  |   F1  |   F3  |
	// 	     |       |       |       |
	// 	     +-------+-------+-------+
	// DEFI  |   F1  |   F1  |   F3  |
	// 	     |       |       |       |
	// 	     +-------+-------+-------+
	// NFT   |   F2  |   F2  |   F4  |
	// 	     |       |       |       |
	// 	     +-------+-------+-------+
	// F1: Fetcher 1 (eth+arb) (defi+token)
	// F2: Fetcher 2 (eth+arb) (nft)
	// F3: Fetcher 3 (polygon) (defi+token)
	// F4: Fetcher 4 (polygon) (nft)

	// Fetcher 1: (eth+arb) (defi+token)
	fetcher1.EXPECT().SupportedChains().Return([]domain.Chain{domain.Ethereum, domain.Arbitrum}).AnyTimes()
	fetcher1.EXPECT().SupportedTypes().Return([]domain.AssetType{domain.AssetTypeDefi, domain.AssetTypeToken}).AnyTimes()
	fetcher1.EXPECT().GetAssets(gomock.Any(), addr, []domain.Chain{domain.Ethereum, domain.Arbitrum}, []domain.AssetType{domain.AssetTypeDefi, domain.AssetTypeToken}).Return(&domain.AggregatedAssets{
		Tokens: []*domain.TokenAmount{
			{Token: domain.NewToken(domain.Ethereum, "usdc", "USD Coin", "USDC", "https://usdc.logo", 6, true), Amount: decimal.NewFromFloat(1.5), Price: util.Ptr(2000.0)},
			{Token: domain.NewToken(domain.Arbitrum, "arb", "ARB", "ARB", "https://arb.logo", 18, true), Amount: decimal.NewFromFloat(100), Price: util.Ptr(1.5)},
		},
		Defi: []*domain.DefiAsset{
			func() *domain.DefiAsset {
				defi := domain.NewDefiAsset(domain.Ethereum, "defi1", "Ethereum Defi 1", "https://defi1.eth")
				defi.SupplyTokens = append(defi.SupplyTokens, &domain.DefiToken{
					ID: "eth-supply", Name: "ETH Supply", LogoUrl: util.Ptr("https://eth-supply.logo"),
					Amount: decimal.NewFromFloat(5), Price: 2010.0,
				})
				defi.BorrowTokens = append(defi.BorrowTokens, &domain.DefiToken{
					ID: "eth-borrow", Name: "ETH Borrow", LogoUrl: util.Ptr("https://eth-borrow.logo"),
					Amount: decimal.NewFromFloat(2), Price: 1990.0,
				})
				defi.RewardTokens = append(defi.RewardTokens, &domain.DefiToken{
					ID: "eth-reward", Name: "ETH Reward", LogoUrl: util.Ptr("https://eth-reward.logo"),
					Amount: decimal.NewFromFloat(0.1), Price: 2020.0,
				})
				return defi
			}(),
			func() *domain.DefiAsset {
				defi := domain.NewDefiAsset(domain.Arbitrum, "defi2", "Arbitrum Defi 2", "https://defi2.arb")
				defi.SupplyTokens = append(defi.SupplyTokens, &domain.DefiToken{
					ID: "arb-supply", Name: "ARB Supply", LogoUrl: util.Ptr("https://arb-supply.logo"),
					Amount: decimal.NewFromFloat(200), Price: 1.6,
				})
				defi.BorrowTokens = append(defi.BorrowTokens, &domain.DefiToken{
					ID: "arb-borrow", Name: "ARB Borrow", LogoUrl: util.Ptr("https://arb-borrow.logo"),
					Amount: decimal.NewFromFloat(50), Price: 1.4,
				})
				defi.RewardTokens = append(defi.RewardTokens, &domain.DefiToken{
					ID: "arb-reward", Name: "ARB Reward", LogoUrl: util.Ptr("https://arb-reward.logo"),
					Amount: decimal.NewFromFloat(5), Price: 1.7,
				})
				return defi
			}(),
		},
	}, nil)

	// Fetcher 2: (eth+arb) (nft)
	fetcher2.EXPECT().SupportedChains().Return([]domain.Chain{domain.Ethereum, domain.Arbitrum}).AnyTimes()
	fetcher2.EXPECT().SupportedTypes().Return([]domain.AssetType{domain.AssetTypeNft}).AnyTimes()
	fetcher2.EXPECT().GetAssets(gomock.Any(), addr, []domain.Chain{domain.Ethereum, domain.Arbitrum}, []domain.AssetType{domain.AssetTypeNft}).Return(&domain.AggregatedAssets{
		Nfts: []*domain.NftAmount{
			{Nft: domain.Nft{Chain: domain.Ethereum, ContractAddress: domain.NewEvmAddress("aaaa")}, Amount: 1},
			{Nft: domain.Nft{Chain: domain.Arbitrum, ContractAddress: domain.NewEvmAddress("bbbb")}, Amount: 2},
		},
	}, nil)

	// Fetcher 3: (polygon) (defi+token)
	fetcher3.EXPECT().SupportedChains().Return([]domain.Chain{domain.Polygon}).AnyTimes()
	fetcher3.EXPECT().SupportedTypes().Return([]domain.AssetType{domain.AssetTypeDefi, domain.AssetTypeToken}).AnyTimes()
	fetcher3.EXPECT().GetAssets(gomock.Any(), addr, []domain.Chain{domain.Polygon}, []domain.AssetType{domain.AssetTypeDefi, domain.AssetTypeToken}).Return(&domain.AggregatedAssets{
		Tokens: []*domain.TokenAmount{
			{Token: domain.NewToken(domain.Polygon, "matic", "POL", "POL", "https://matic.logo", 18, true), Amount: decimal.NewFromFloat(1000), Price: util.Ptr(0.8)},
		},
		Defi: []*domain.DefiAsset{
			func() *domain.DefiAsset {
				defi := domain.NewDefiAsset(domain.Polygon, "defi3", "Polygon Defi 3", "https://defi3.polygon")
				defi.SupplyTokens = append(defi.SupplyTokens, &domain.DefiToken{
					ID: "matic-supply", Name: "MATIC Supply", LogoUrl: util.Ptr("https://matic-supply.logo"),
					Amount: decimal.NewFromFloat(1000), Price: 0.85,
				})
				defi.BorrowTokens = append(defi.BorrowTokens, &domain.DefiToken{
					ID: "matic-borrow", Name: "MATIC Borrow", LogoUrl: util.Ptr("https://matic-borrow.logo"),
					Amount: decimal.NewFromFloat(500), Price: 0.75,
				})
				defi.RewardTokens = append(defi.RewardTokens, &domain.DefiToken{
					ID: "matic-reward", Name: "MATIC Reward", LogoUrl: util.Ptr("https://matic-reward.logo"),
					Amount: decimal.NewFromFloat(50), Price: 0.9,
				})
				return defi
			}(),
		},
	}, nil)

	// Fetcher 4: (polygon) (nft)
	fetcher4.EXPECT().SupportedChains().Return([]domain.Chain{domain.Polygon}).AnyTimes()
	fetcher4.EXPECT().SupportedTypes().Return([]domain.AssetType{domain.AssetTypeNft}).AnyTimes()
	fetcher4.EXPECT().GetAssets(gomock.Any(), addr, []domain.Chain{domain.Polygon}, []domain.AssetType{domain.AssetTypeNft}).Return(&domain.AggregatedAssets{
		Nfts: []*domain.NftAmount{
			{Nft: domain.Nft{Chain: domain.Polygon, ContractAddress: domain.NewEvmAddress("cccc")}, Amount: 3},
		},
	}, nil)

	// amount fetcher: eth, arb, polygon, but eth fails. The rest should still be processed
	amountFetcher.EXPECT().SupportedChains().Return([]domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Polygon}).AnyTimes()
	amountFetcher.EXPECT().FetchAmounts(gomock.Any(), domain.Ethereum, gomock.Any()).Return(nil, errors.New("eth fetcher failed"))
	amountFetcher.EXPECT().FetchAmounts(gomock.Any(), domain.Arbitrum, gomock.Any()).DoAndReturn(func(_ context.Context, _ domain.Chain, tokens []*domain.WalletToken) (map[domain.Address]map[string]decimal.Decimal, error) {
		assert.Len(t, tokens, 7) // 2 tokens + 5 default tokens
		assert.Equal(t, addr, tokens[0].Wallet)
		assert.Equal(t, "arb", tokens[0].ID())
		assert.Equal(t, addr, tokens[1].Wallet)
		assert.Equal(t, "eth", tokens[1].ID())
		return map[domain.Address]map[string]decimal.Decimal{
			addr: {
				"arb": decimal.NewFromFloat(9999.0),
				"eth": decimal.NewFromFloat(8888.0),
				"******************************************": decimal.NewFromFloat(5487.0),
			},
		}, nil
	})
	amountFetcher.EXPECT().FetchAmounts(gomock.Any(), domain.Polygon, gomock.Any()).DoAndReturn(func(_ context.Context, _ domain.Chain, tokens []*domain.WalletToken) (map[domain.Address]map[string]decimal.Decimal, error) {
		assert.Len(t, tokens, 4) // 1 token + 3 default tokens
		assert.Equal(t, addr, tokens[0].Wallet)
		assert.Equal(t, "matic", tokens[0].ID())
		return map[domain.Address]map[string]decimal.Decimal{
			addr: {
				"matic": decimal.NewFromFloat(7777.0),
				"******************************************": decimal.NewFromFloat(7788.0),
			},
		}, nil
	})

	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	Init(mockRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{fetcher1, fetcher2, fetcher3, fetcher4}, []domain.TokenAmountsFetcher{amountFetcher}, mockPriceFetcher)

	ctx := context.Background()
	addresses := map[domain.Address][]domain.Chain{
		addr: {domain.Ethereum, domain.Arbitrum, domain.Polygon},
	}
	types := map[domain.Chain][]domain.AssetType{
		domain.Ethereum: {domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
		domain.Arbitrum: {domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
		domain.Polygon:  {domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
	}

	mockRepo.EXPECT().SetTokenAmounts(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, amounts map[domain.ChainAddress][]*domain.TokenAmount) error {
		assert.Len(t, amounts, 3)
		assert.Len(t, amounts[domain.ChainAddress{Chain: domain.Ethereum, Address: addr}], 5) // 2 tokens + 3 default
		assert.Len(t, amounts[domain.ChainAddress{Chain: domain.Arbitrum, Address: addr}], 7) // 2 tokens + 5 default
		assert.Len(t, amounts[domain.ChainAddress{Chain: domain.Polygon, Address: addr}], 4)  // 1 token + 3 default

		// eth token
		c := domain.ChainAddress{Chain: domain.Ethereum, Address: addr}
		token := amounts[c][0]
		assert.Equal(t, "usdc", token.ID())
		assert.Equal(t, decimal.NewFromFloat(1.5), token.Amount)
		assert.Equal(t, 2000.0, *token.Price)

		// eth main token is added correctly
		token = amounts[c][1]
		assert.Equal(t, "eth", token.ID())
		assert.Equal(t, decimal.Zero, token.Amount)
		assert.Nil(t, token.Price)

		// eth default token is added correctly
		token = amounts[c][2]
		assert.Equal(t, "******************************************", token.ID())
		assert.Equal(t, decimal.Zero, token.Amount)
		assert.Nil(t, token.Price)

		// arb token with updated amount
		c = domain.ChainAddress{Chain: domain.Arbitrum, Address: addr}
		token = amounts[c][0]
		assert.Equal(t, "arb", token.ID())
		assert.Equal(t, decimal.NewFromFloat(9999.0), token.Amount)
		assert.Equal(t, 1.5, *token.Price)

		// arb main token is added correctly with updated amount
		token = amounts[c][1]
		assert.Equal(t, "eth", token.ID())
		assert.Equal(t, decimal.NewFromFloat(8888.0), token.Amount)
		assert.Nil(t, token.Price)

		// arb default token is added correctly with updated amount
		token = amounts[c][6]
		assert.Equal(t, "******************************************", token.ID())
		assert.Equal(t, decimal.NewFromFloat(5487.0), token.Amount)
		assert.Nil(t, token.Price)

		// polygon main token is not added if it already exists. With updated amount
		c = domain.ChainAddress{Chain: domain.Polygon, Address: addr}
		token = amounts[c][0]
		assert.Equal(t, "matic", token.ID())
		assert.Equal(t, decimal.NewFromFloat(7777.0), token.Amount)
		assert.Equal(t, 0.8, *token.Price)

		// polygon default token is added correctly with updated amount
		token = amounts[c][2]
		assert.Equal(t, "******************************************", token.ID())
		assert.Equal(t, decimal.NewFromFloat(7788.0), token.Amount)
		assert.Nil(t, token.Price)
		return nil
	})

	mockRepo.EXPECT().SetNftAmounts(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, amounts map[domain.ChainAddress][]*domain.NftAmount) error {
		assert.Len(t, amounts, 3)
		assert.Len(t, amounts[domain.ChainAddress{Chain: domain.Ethereum, Address: addr}], 1)
		assert.Len(t, amounts[domain.ChainAddress{Chain: domain.Arbitrum, Address: addr}], 1)
		assert.Len(t, amounts[domain.ChainAddress{Chain: domain.Polygon, Address: addr}], 1)

		c := domain.ChainAddress{Chain: domain.Ethereum, Address: addr}
		nft := amounts[c][0]
		assert.Equal(t, domain.NewEvmAddress("aaaa"), nft.ContractAddress)
		assert.Equal(t, uint(1), nft.Amount)

		c = domain.ChainAddress{Chain: domain.Arbitrum, Address: addr}
		nft = amounts[c][0]
		assert.Equal(t, domain.NewEvmAddress("bbbb"), nft.ContractAddress)
		assert.Equal(t, uint(2), nft.Amount)

		c = domain.ChainAddress{Chain: domain.Polygon, Address: addr}
		nft = amounts[c][0]
		assert.Equal(t, domain.NewEvmAddress("cccc"), nft.ContractAddress)
		assert.Equal(t, uint(3), nft.Amount)

		return nil
	})

	mockRepo.EXPECT().SetDefiAssets(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, assets map[domain.ChainAddress][]*domain.DefiAsset) error {
		assert.Len(t, assets, 3)
		assert.Len(t, assets[domain.ChainAddress{Chain: domain.Ethereum, Address: addr}], 1)
		assert.Len(t, assets[domain.ChainAddress{Chain: domain.Arbitrum, Address: addr}], 1)
		assert.Len(t, assets[domain.ChainAddress{Chain: domain.Polygon, Address: addr}], 1)

		c := domain.ChainAddress{Chain: domain.Ethereum, Address: addr}
		ethDefi := assets[c][0]
		assert.Equal(t, "defi1", ethDefi.ID())
		assert.Equal(t, "Ethereum Defi 1", ethDefi.Name())
		assert.Equal(t, "https://defi1.eth", ethDefi.SiteUrl)
		assert.Len(t, ethDefi.SupplyTokens, 1)
		assert.Len(t, ethDefi.BorrowTokens, 1)
		assert.Len(t, ethDefi.RewardTokens, 1)

		c = domain.ChainAddress{Chain: domain.Arbitrum, Address: addr}
		arbDefi := assets[c][0]
		assert.Equal(t, "defi2", arbDefi.ID())
		assert.Equal(t, "Arbitrum Defi 2", arbDefi.Name())
		assert.Equal(t, "https://defi2.arb", arbDefi.SiteUrl)
		assert.Len(t, arbDefi.SupplyTokens, 1)
		assert.Len(t, arbDefi.BorrowTokens, 1)
		assert.Len(t, arbDefi.RewardTokens, 1)

		c = domain.ChainAddress{Chain: domain.Polygon, Address: addr}
		polyDefi := assets[c][0]
		assert.Equal(t, "defi3", polyDefi.ID())
		assert.Equal(t, "Polygon Defi 3", polyDefi.Name())
		assert.Equal(t, "https://defi3.polygon", polyDefi.SiteUrl)
		assert.Len(t, polyDefi.SupplyTokens, 1)
		assert.Len(t, polyDefi.BorrowTokens, 1)
		assert.Len(t, polyDefi.RewardTokens, 1)

		return nil
	})

	mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, prices []*domain.TokenPrice) error {
		assert.Len(t, prices, 12) // 3 regular tokens + 9 defi tokens
		expectedPrices := map[string]float64{
			"usdc":         2000.0,
			"arb":          1.5,
			"matic":        0.8,
			"eth-supply":   2010.0,
			"eth-borrow":   1990.0,
			"eth-reward":   2020.0,
			"arb-supply":   1.6,
			"arb-borrow":   1.4,
			"arb-reward":   1.7,
			"matic-supply": 0.85,
			"matic-borrow": 0.75,
			"matic-reward": 0.9,
		}
		for _, price := range prices {
			assert.Equal(t, expectedPrices[price.ID], price.Price)
		}
		return nil
	})

	wsRepo := domain.NewMockWebsocketRepo(ctrl)
	wsRepo.EXPECT().GetUIDsByAddresses(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, addresses []string) ([]string, error) {
		assert.ElementsMatch(t, addresses, []string{addr.String()})
		return []string{}, nil
	})
	ws.Init(wsRepo)

	err := Update(ctx, addresses, types)
	assert.Nil(t, err)
}

func TestUpdateAssetsUnsupportedChain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	addr := domain.NewEvmAddress("0x1234")
	mockRepo := NewMockIRepo(ctrl)
	fetcherEthereum := domain.NewMockAssetFetcher(ctrl)

	// Only set up a fetcher for Ethereum
	fetcherEthereum.EXPECT().SupportedChains().Return([]domain.Chain{domain.Ethereum}).AnyTimes()
	fetcherEthereum.EXPECT().SupportedTypes().Return([]domain.AssetType{domain.AssetTypeToken}).AnyTimes()
	// We don't expect GetAssets to be called for Ethereum in this test

	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	Init(mockRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{fetcherEthereum}, []domain.TokenAmountsFetcher{}, mockPriceFetcher)

	ctx := context.Background()
	addresses := map[domain.Address][]domain.Chain{
		addr: {domain.Solana}, // Only request Solana, which is unsupported
	}
	types := map[domain.Chain][]domain.AssetType{
		domain.Ethereum: {domain.AssetTypeToken},
		domain.Solana:   {domain.AssetTypeToken},
	}

	err := Update(ctx, addresses, types)
	assert.NotNil(t, err)
	assert.Contains(t, err.String(), "unsupported chain")
}

func TestUpdateAssetsUnsupportedType(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	addr := domain.NewEvmAddress("0x1234")
	mockRepo := NewMockIRepo(ctrl)
	fetcherEthereum := domain.NewMockAssetFetcher(ctrl)

	// Set up a fetcher for Ethereum that only supports Token type
	fetcherEthereum.EXPECT().SupportedChains().Return([]domain.Chain{domain.Ethereum}).AnyTimes()
	fetcherEthereum.EXPECT().SupportedTypes().Return([]domain.AssetType{domain.AssetTypeToken}).AnyTimes()
	// We don't expect GetAssets to be called in this test

	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	Init(mockRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{fetcherEthereum}, []domain.TokenAmountsFetcher{}, mockPriceFetcher)

	ctx := context.Background()
	addresses := map[domain.Address][]domain.Chain{
		addr: {domain.Ethereum},
	}
	types := map[domain.Chain][]domain.AssetType{
		domain.Ethereum: {domain.AssetTypeNft},
	} // Request NFT type, which is unsupported

	err := Update(ctx, addresses, types)
	assert.NotNil(t, err)
	assert.Contains(t, err.String(), "unsupported asset type")
}

// TestUpdateAssetsFetcherError tests that when a fetcher returns an error,
// empty values are set into the repository methods SetTokenAmounts and SetNftAmounts.
func TestUpdateAssetsFetcherError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	fetcher := domain.NewMockAssetFetcher(ctrl)
	amountFetcher := domain.NewMockTokenAmountsFetcher(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)

	addr := domain.NewEvmAddress("0x5678")

	// Set up the fetcher to return an error
	fetcher.EXPECT().SupportedChains().Return([]domain.Chain{domain.Ethereum}).AnyTimes()
	fetcher.EXPECT().SupportedTypes().Return([]domain.AssetType{domain.AssetTypeToken}).AnyTimes()
	fetcher.EXPECT().GetAssets(gomock.Any(), addr, []domain.Chain{domain.Ethereum}, []domain.AssetType{domain.AssetTypeToken}).
		Return(nil, errors.New("fetcher error"))

	// Set up the amountFetcher to return an error as well
	amountFetcher.EXPECT().SupportedChains().Return([]domain.Chain{domain.Ethereum}).AnyTimes()
	amountFetcher.EXPECT().FetchAmounts(gomock.Any(), domain.Ethereum, gomock.Any()).
		Return(nil, errors.New("amount fetcher error"))

	Init(mockRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{fetcher}, []domain.TokenAmountsFetcher{amountFetcher}, mockPriceFetcher)

	ctx := context.Background()
	addresses := map[domain.Address][]domain.Chain{
		addr: {domain.Ethereum},
	}
	types := map[domain.Chain][]domain.AssetType{
		domain.Ethereum: {domain.AssetTypeToken},
	}

	// Expect repository methods to be called with empty maps
	mockRepo.EXPECT().SetTokenAmounts(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, amounts map[domain.ChainAddress][]*domain.TokenAmount) error {
		assert.Empty(t, amounts, "TokenAmounts should be empty due to fetcher error")
		return nil
	})
	mockRepo.EXPECT().SetNftAmounts(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, amounts map[domain.ChainAddress][]*domain.NftAmount) error {
		assert.Empty(t, amounts, "NftAmounts should be empty due to fetcher error")
		return nil
	})
	mockRepo.EXPECT().SetDefiAssets(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, assets map[domain.ChainAddress][]*domain.DefiAsset) error {
		assert.Empty(t, assets, "DefiAssets should be empty due to fetcher error")
		return nil
	})
	mockRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, prices []*domain.TokenPrice) error {
		assert.Empty(t, prices, "TokenPrices should be empty due to fetcher error")
		return nil
	})

	err := Update(ctx, addresses, types)
	assert.Nil(t, err)
	// assert.NotNil(t, err, "Expected error due to fetcher failure")
	// assert.Contains(t, err.String(), "fetcher error", "Error message should contain fetcher error")
}
