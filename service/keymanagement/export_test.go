package keymanagement_test

import (
	"context"
	"crypto/ecdh"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/gcp"
	"github.com/kryptogo/kg-wallet-backend/service/keymanagement"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"golang.org/x/crypto/hkdf"
)

// mockRepo combines multiple domain repository mocks to satisfy the repository interface
type mockRepo struct {
	*domain.MockStudioOrgRepo
}

func setupMockRepo(ctrl *gomock.Controller) *mockRepo {
	return &mockRepo{
		MockStudioOrgRepo: domain.NewMockStudioOrgRepo(ctrl),
	}
}

func TestRetrieveOrganizationMnemonic_HappyPath(t *testing.T) {
	// Setup
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mr := setupMockRepo(ctrl)
	keymanagement.Init(mr)

	// Generate DHKE key pair for the test client using crypto/ecdh
	curve := ecdh.P256()
	clientPrivateKey, err := curve.GenerateKey(rand.Reader)
	require.NoError(t, err)

	clientPublicKey := clientPrivateKey.PublicKey()
	clientPubKeyBytes := clientPublicKey.Bytes()
	clientPubKeyHex := hex.EncodeToString(clientPubKeyBytes)

	// Mock server's DHKE key pair
	serverPrivateKey, err := curve.GenerateKey(rand.Reader)
	require.NoError(t, err)
	serverPublicKey := serverPrivateKey.PublicKey()

	// Calculate shared secret (client side)
	// In real code, this would be derived by the client
	clientSharedSecret, err := clientPrivateKey.ECDH(serverPublicKey)
	require.NoError(t, err)

	// Derive encryption key using HKDF (client side)
	clientKdf := hkdf.New(sha256.New, clientSharedSecret, nil, []byte("DHKE-Mnemonic-Encryption"))
	clientDerivedKey := make([]byte, 32) // 256-bit key
	_, err = clientKdf.Read(clientDerivedKey)
	require.NoError(t, err)

	// The mock mnemonic we expect to retrieve
	expectedMnemonic := "test mnemonic phrase for wallet encryption decryption test"

	// In a real implementation, the KMS service would:
	// 1. Calculate the same shared secret using client's public key
	// 2. Derive the same encryption key
	// 3. Encrypt the mnemonic with that key

	// Setup a mock HTTP server to simulate the KMS service
	kmsServer := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Parse the incoming request
		var kmsReq keymanagement.KMSRetrieveMnemonicRequest
		err := json.NewDecoder(r.Body).Decode(&kmsReq)
		require.NoError(t, err)

		// Check public key from the request
		assert.Equal(t, clientPubKeyHex, kmsReq.PublicKey)

		// On the server side, we would:
		// 1. Parse the client's public key
		parsedClientPubKey, err := hex.DecodeString(kmsReq.PublicKey)
		require.NoError(t, err)

		// 2. Unmarshal the public key
		unmarshaledClientPubKey, err := curve.NewPublicKey(parsedClientPubKey)
		require.NoError(t, err)

		// 3. Calculate the shared secret
		serverSharedSecret, err := serverPrivateKey.ECDH(unmarshaledClientPubKey)
		require.NoError(t, err)

		// 4. Derive the same encryption key
		serverKdf := hkdf.New(sha256.New, serverSharedSecret, nil, []byte("DHKE-Mnemonic-Encryption"))
		serverDerivedKey := make([]byte, 32)
		_, err = serverKdf.Read(serverDerivedKey)
		require.NoError(t, err)

		// 5. Verify client and server derived the same key
		assert.Equal(t, clientDerivedKey, serverDerivedKey)

		// Prepare the response
		// In a real implementation, this would be the encrypted mnemonic
		// For the test, we'll just return the expected mnemonic directly
		response := keymanagement.RetrieveMnemonicResponse{
			Code: code.OK,
			Data: map[string]interface{}{
				"mnemonic": expectedMnemonic,
			},
		}

		// Send response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(response)
	}))
	defer kmsServer.Close()

	// Mock HTTPClient for testing
	originalClient := gcp.Client
	gcp.Client = &MockHTTPClient{
		kmsURL: kmsServer.URL,
	}
	defer func() { gcp.Client = originalClient }()

	// Setup mock wallet
	ctx := context.Background()
	orgID := 1
	walletType := "ethereum"

	mr.MockStudioOrgRepo.EXPECT().
		GetOrgWallet(ctx, orgID, walletType).
		Return(&domain.StudioOrganizationWallet{
			ID:                  1,
			OrganizationID:      orgID,
			WalletType:          walletType,
			WalletAddress:       "0xabc123",
			EncryptedPrivateKey: "encrypted-private-key",
		}, nil)

	// Execute the function
	req := keymanagement.RetrieveMnemonicRequest{
		WalletType: walletType,
		PublicKey:  clientPubKeyHex,
	}

	resp, err := keymanagement.RetrieveOrganizationMnemonic(ctx, orgID, req)

	// Assert
	require.NoError(t, err)
	require.NotNil(t, resp)
	assert.Equal(t, code.OK, resp.Code)
	assert.Empty(t, resp.Error)

	// Extract mnemonic from response
	data, ok := resp.Data.(map[string]interface{})
	require.True(t, ok)

	mnemonicFromResponse, ok := data["mnemonic"].(string)
	require.True(t, ok)

	// Verify mnemonic
	assert.Equal(t, expectedMnemonic, mnemonicFromResponse)

	// In a real scenario:
	// 1. The client would use the derived key to decrypt the mnemonic
	// 2. We'd verify decryption works as expected

	// For this test, since we're not actually encrypting/decrypting:
	// - We've verified the client can generate the correct key using DHKE
	// - We've verified the mock KMS returns a response with the expected structure
	// - We've verified the service layer correctly processes the response
}

// MockHTTPClient mocks the GCP HTTP client for testing
type MockHTTPClient struct {
	kmsURL string
}

// MakeRequestWithIDToken mocks the HTTP request to KMS
func (m *MockHTTPClient) MakeRequestWithIDToken(ctx context.Context, baseURL, targetURL, method string, body io.Reader, headers map[string]string) (*http.Response, []byte, error) {
	// Since our mock HTTP server is already handling requests, we just need to forward
	// the request to our test server instead of the real KMS service
	// The actual implementation would validate request structure, but for testing
	// we just need to ensure the KMS server's response is returned

	client := &http.Client{}

	// Create request for our mock server
	req, err := http.NewRequestWithContext(ctx, method, m.kmsURL, body)
	if err != nil {
		return nil, nil, err
	}

	// Add headers
	req.Header.Set("Content-Type", "application/json")
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	// Send request to mock server
	resp, err := client.Do(req)
	if err != nil {
		return nil, nil, err
	}
	defer resp.Body.Close()

	// Read response body
	var respData keymanagement.RetrieveMnemonicResponse
	err = json.NewDecoder(resp.Body).Decode(&respData)
	if err != nil {
		return nil, nil, err
	}

	// Convert response back to bytes for the expected interface
	respBytes, err := json.Marshal(respData)
	if err != nil {
		return nil, nil, err
	}

	return resp, respBytes, nil
}
