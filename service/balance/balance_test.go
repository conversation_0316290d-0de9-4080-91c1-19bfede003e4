package balance

import (
	"context"
	"sort"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// TestSummary tests the Summary function
func TestSummary(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Initialize mock repositories
	mockUserRepo := user.NewMockIRepo(ctrl)
	mockAssetRepo := asset.NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)

	// Initialize the user and asset packages with mocks
	user.Init(mockUserRepo)
	asset.Init(mockAssetRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, mockPriceFetcher)

	ctx := context.Background()
	uid := "user123"
	assetTypes := []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft}
	chains := []domain.Chain{domain.Ethereum, domain.Polygon}
	excludeObserver := false

	// Prepare UserData to be returned by GetUser
	userData := &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         uid,
			DisplayName: "Test User",
		},
		Wallets: &domain.Wallets{
			DefaultReceiveWallets: domain.ChainAddressMap{
				domain.Ethereum: domain.NewEvmAddress("0xABC123"),
				domain.Polygon:  domain.NewEvmAddress("0xDEF456"),
			},
			WalletGroups: []*domain.WalletGroup{
				{
					EvmWallets: []*domain.UserWallet{
						{Address: "0xABC123", WalletType: nil},
					},
				},
			},
			EvmWallets: []*domain.UserWallet{
				{Address: "0xDEF456", WalletType: nil},
			},
		},
	}

	// Set expectation for GetUser
	mockUserRepo.EXPECT().
		GetUser(ctx, uid, "", true, &domain.UserPreloads{
			WithWallets: true,
		}).
		DoAndReturn(func(ctx context.Context, uid string, clientID string, withUserData bool, preloads *domain.UserPreloads) (*domain.UserData, *code.KGError) {
			// Assert input parameters
			assert.Equal(t, uid, uid)
			assert.Equal(t, "", clientID)
			assert.True(t, withUserData)
			assert.NotNil(t, preloads)
			assert.True(t, preloads.WithWallets)
			return userData, nil
		})

	// Set expectation for GetAssetsTotalUsdValue
	mockAssetRepo.EXPECT().
		GetAssetsTotalUsdValue(ctx, chains, []domain.Address{domain.NewEvmAddress("0xABC123"), domain.NewEvmAddress("0xDEF456")}, assetTypes).
		DoAndReturn(func(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType) (float64, error) {
			// Assert input parameters
			assert.ElementsMatch(t, chains, chains)
			assert.ElementsMatch(t, addresses, []domain.Address{domain.NewEvmAddress("0xABC123"), domain.NewEvmAddress("0xDEF456")})
			assert.ElementsMatch(t, types, assetTypes)
			return 5000.0, nil
		})

	// Set expectation for GetPastAssetsTotalUsdValue
	mockAssetRepo.EXPECT().
		GetPastAssetsTotalUsdValue(gomock.Any(), chains, []domain.Address{domain.NewEvmAddress("0xABC123"), domain.NewEvmAddress("0xDEF456")}, assetTypes, gomock.Any()).
		DoAndReturn(func(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType, from time.Time) (float64, error) {
			// Assert input parameters
			assert.ElementsMatch(t, chains, chains)
			assert.ElementsMatch(t, addresses, []domain.Address{domain.NewEvmAddress("0xABC123"), domain.NewEvmAddress("0xDEF456")})
			assert.ElementsMatch(t, types, assetTypes)
			return 4500.0, nil
		})

	// Call the Summary function
	resp, kgErr := Summary(ctx, uid, assetTypes, chains, excludeObserver)

	// Assertions
	assert.Nil(t, kgErr)
	assert.NotNil(t, resp)
	assert.Equal(t, 5000.0, resp.UsdValue)
	assert.Equal(t, 4500.0, resp.UsdValue24hAgo)
}

// TestByPath tests the ByPath function with multiple scenarios
func TestByPath(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Initialize mock repositories
	mockUserRepo := user.NewMockIRepo(ctrl)
	mockAssetRepo := asset.NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)

	// Initialize the user and asset packages with mocks
	user.Init(mockUserRepo)
	asset.Init(mockAssetRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{}, []domain.TokenAmountsFetcher{}, mockPriceFetcher)

	ctx := context.Background()
	uid := "user123"

	// Prepare UserData to be returned by GetUser
	userData := &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         uid,
			DisplayName: "Test User",
		},
		Wallets: &domain.Wallets{
			DefaultReceiveWallets: domain.ChainAddressMap{
				domain.Ethereum: domain.NewEvmAddress("0xEVMDefault"),
				domain.Bitcoin:  domain.NewStrAddress("btc-default"),
			},
			WalletGroups: []*domain.WalletGroup{
				{
					EvmWallets:    []*domain.UserWallet{{Address: "0x1111"}},
					BtcWallets:    []*domain.UserWallet{{Chain: "btc", Address: "btc-group-0-0"}},
					SolanaWallets: []*domain.UserWallet{{Chain: "sol", Address: "sol-group-0-0"}},
					TronWallets:   []*domain.UserWallet{{Chain: "tron", Address: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"}},
				},
			},
			EvmWallets: []*domain.UserWallet{{Address: "0x2222"}},
			Wallets: []*domain.UserWallet{
				{Chain: "btc", Address: "single-btc-wallet-0"},
				{Chain: "sol", Address: "single-sol-wallet-0"},
			},
		},
	}

	// Set expectation for GetUser
	mockUserRepo.EXPECT().
		GetUser(ctx, uid, "", true, &domain.UserPreloads{
			WithWallets: true,
		}).
		DoAndReturn(func(ctx context.Context, uid string, clientID string, withUserData bool, preloads *domain.UserPreloads) (*domain.UserData, *code.KGError) {
			// Assert input parameters
			assert.Equal(t, uid, uid)
			assert.Equal(t, "", clientID)
			assert.True(t, withUserData)
			assert.NotNil(t, preloads)
			assert.True(t, preloads.WithWallets)
			return userData, nil
		}).
		AnyTimes()

	// Define test cases
	chains := []domain.Chain{domain.Ethereum, domain.Polygon, domain.Arbitrum, domain.Bitcoin, domain.Solana, domain.Tron}
	assetTypes := []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi}
	testCases := []struct {
		name          string
		path          string
		expectedAddrs []domain.Address
		expectedResp  []*PathAssetBalance
	}{
		{
			name: "Root Path",
			path: "",
			expectedAddrs: []domain.Address{
				domain.NewEvmAddress("0x1111"),
				domain.NewEvmAddress("0x2222"),
				domain.NewStrAddress("btc-group-0-0"),
				domain.NewStrAddress("sol-group-0-0"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
				domain.NewStrAddress("single-btc-wallet-0"),
				domain.NewStrAddress("single-sol-wallet-0"),
			},
			expectedResp: []*PathAssetBalance{
				{ChainID: "eth", AssetType: "token", USDValue: 249.0, USDValue24hAgo: 250.8},
				{ChainID: "eth", AssetType: "nft", USDValue: 449.0, USDValue24hAgo: 450.8},
				{ChainID: "eth", AssetType: "defi", USDValue: 649.0, USDValue24hAgo: 650.8},
				{ChainID: "matic", AssetType: "token", USDValue: 251.0, USDValue24hAgo: 252.8},
				{ChainID: "matic", AssetType: "nft", USDValue: 451.0, USDValue24hAgo: 452.8},
				{ChainID: "matic", AssetType: "defi", USDValue: 651.0, USDValue24hAgo: 652.8},
				{ChainID: "arb", AssetType: "token", USDValue: 252.0, USDValue24hAgo: 253.8},
				{ChainID: "arb", AssetType: "nft", USDValue: 452.0, USDValue24hAgo: 453.8},
				{ChainID: "arb", AssetType: "defi", USDValue: 652.0, USDValue24hAgo: 653.8},
				{ChainID: "btc", AssetType: "token", USDValue: 257.0, USDValue24hAgo: 258.8},
				{ChainID: "btc", AssetType: "nft", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "btc", AssetType: "defi", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "sol", AssetType: "token", USDValue: 260.0, USDValue24hAgo: 261.8},
				{ChainID: "sol", AssetType: "nft", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "sol", AssetType: "defi", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "tron", AssetType: "token", USDValue: 131.0, USDValue24hAgo: 131.9},
				{ChainID: "tron", AssetType: "nft", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "tron", AssetType: "defi", USDValue: 0, USDValue24hAgo: 0},
			},
		},
		{
			name: "Group-0 Path",
			path: "group-0",
			expectedAddrs: []domain.Address{
				domain.NewEvmAddress("0x1111"),
				domain.NewStrAddress("btc-group-0-0"),
				domain.NewStrAddress("sol-group-0-0"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			expectedResp: []*PathAssetBalance{
				{ChainID: "eth", AssetType: "token", USDValue: 123, USDValue24hAgo: 123.9},
				{ChainID: "eth", AssetType: "nft", USDValue: 223, USDValue24hAgo: 223.9},
				{ChainID: "eth", AssetType: "defi", USDValue: 323, USDValue24hAgo: 323.9},
				{ChainID: "matic", AssetType: "token", USDValue: 124, USDValue24hAgo: 124.9},
				{ChainID: "matic", AssetType: "nft", USDValue: 224, USDValue24hAgo: 224.9},
				{ChainID: "matic", AssetType: "defi", USDValue: 324, USDValue24hAgo: 324.9},
				{ChainID: "arb", AssetType: "token", USDValue: 125, USDValue24hAgo: 125.9},
				{ChainID: "arb", AssetType: "nft", USDValue: 225, USDValue24hAgo: 225.9},
				{ChainID: "arb", AssetType: "defi", USDValue: 325, USDValue24hAgo: 325.9},
				{ChainID: "btc", AssetType: "token", USDValue: 128, USDValue24hAgo: 128.9},
				{ChainID: "btc", AssetType: "nft", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "btc", AssetType: "defi", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "sol", AssetType: "token", USDValue: 130, USDValue24hAgo: 130.9},
				{ChainID: "sol", AssetType: "nft", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "sol", AssetType: "defi", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "tron", AssetType: "token", USDValue: 131, USDValue24hAgo: 131.9},
				{ChainID: "tron", AssetType: "nft", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "tron", AssetType: "defi", USDValue: 0, USDValue24hAgo: 0},
			},
		},
		{
			name: "Group-0-EVM-0 Path",
			path: "group-0-evm-0",
			expectedAddrs: []domain.Address{
				domain.NewEvmAddress("0x1111"),
			},
			expectedResp: []*PathAssetBalance{
				{ChainID: "eth", AssetType: "token", USDValue: 123, USDValue24hAgo: 123.9},
				{ChainID: "eth", AssetType: "nft", USDValue: 223, USDValue24hAgo: 223.9},
				{ChainID: "eth", AssetType: "defi", USDValue: 323, USDValue24hAgo: 323.9},
				{ChainID: "matic", AssetType: "token", USDValue: 124, USDValue24hAgo: 124.9},
				{ChainID: "matic", AssetType: "nft", USDValue: 224, USDValue24hAgo: 224.9},
				{ChainID: "matic", AssetType: "defi", USDValue: 324, USDValue24hAgo: 324.9},
				{ChainID: "arb", AssetType: "token", USDValue: 125, USDValue24hAgo: 125.9},
				{ChainID: "arb", AssetType: "nft", USDValue: 225, USDValue24hAgo: 225.9},
				{ChainID: "arb", AssetType: "defi", USDValue: 325, USDValue24hAgo: 325.9},
			},
		},
		{
			name: "Group-0-btc-0 Path",
			path: "group-0-btc-0",
			expectedAddrs: []domain.Address{
				domain.NewStrAddress("btc-group-0-0"),
			},
			expectedResp: []*PathAssetBalance{
				{ChainID: "btc", AssetType: "token", USDValue: 128, USDValue24hAgo: 128.9},
				{ChainID: "btc", AssetType: "nft", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "btc", AssetType: "defi", USDValue: 0, USDValue24hAgo: 0},
			},
		},
		{
			name: "EVM-0 Path",
			path: "evm-0",
			expectedAddrs: []domain.Address{
				domain.NewEvmAddress("0x2222"),
			},
			expectedResp: []*PathAssetBalance{
				{ChainID: "eth", AssetType: "token", USDValue: 126, USDValue24hAgo: 126.9},
				{ChainID: "eth", AssetType: "nft", USDValue: 226, USDValue24hAgo: 226.9},
				{ChainID: "eth", AssetType: "defi", USDValue: 326, USDValue24hAgo: 326.9},
				{ChainID: "matic", AssetType: "token", USDValue: 127, USDValue24hAgo: 127.9},
				{ChainID: "matic", AssetType: "nft", USDValue: 227, USDValue24hAgo: 227.9},
				{ChainID: "matic", AssetType: "defi", USDValue: 327, USDValue24hAgo: 327.9},
				{ChainID: "arb", AssetType: "token", USDValue: 127, USDValue24hAgo: 127.9},
				{ChainID: "arb", AssetType: "nft", USDValue: 227, USDValue24hAgo: 227.9},
				{ChainID: "arb", AssetType: "defi", USDValue: 327, USDValue24hAgo: 327.9},
			},
		},
		{
			name: "Single-1 Path",
			path: "single-1",
			expectedAddrs: []domain.Address{
				domain.NewStrAddress("single-sol-wallet-0"),
			},
			expectedResp: []*PathAssetBalance{
				{ChainID: "sol", AssetType: "token", USDValue: 130, USDValue24hAgo: 130.9},
				{ChainID: "sol", AssetType: "nft", USDValue: 0, USDValue24hAgo: 0},
				{ChainID: "sol", AssetType: "defi", USDValue: 0, USDValue24hAgo: 0},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Mock GetAssetsTotalUsdValue based on the expected addresses
			mockAssetRepo.EXPECT().
				GetAssetBalances(ctx, chains, gomock.Any(), assetTypes).
				DoAndReturn(func(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType) (map[domain.ChainAddress]map[domain.AssetType]float64, error) {
					assert.ElementsMatch(t, chains, chains)
					assert.ElementsMatch(t, addresses, tc.expectedAddrs)
					assert.ElementsMatch(t, types, assetTypes)
					addrs := lo.Map(tc.expectedAddrs, func(item domain.Address, _ int) string { return item.String() })
					ret := map[domain.ChainAddress]map[domain.AssetType]float64{}
					if lo.Contains(addrs, domain.NewEvmAddress("0x1111").String()) {
						ret[domain.ChainAddress{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x1111")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 123.0,
							domain.AssetTypeNft:   223.0,
							domain.AssetTypeDefi:  323.0,
						}
						ret[domain.ChainAddress{Chain: domain.Polygon, Address: domain.NewEvmAddress("0x1111")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 124.0,
							domain.AssetTypeNft:   224.0,
							domain.AssetTypeDefi:  324.0,
						}
						ret[domain.ChainAddress{Chain: domain.Arbitrum, Address: domain.NewEvmAddress("0x1111")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 125.0,
							domain.AssetTypeNft:   225.0,
							domain.AssetTypeDefi:  325.0,
						}
					}
					if lo.Contains(addrs, domain.NewEvmAddress("0x2222").String()) {
						ret[domain.ChainAddress{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x2222")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 126.0,
							domain.AssetTypeNft:   226.0,
							domain.AssetTypeDefi:  326.0,
						}
						ret[domain.ChainAddress{Chain: domain.Polygon, Address: domain.NewEvmAddress("0x2222")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 127.0,
							domain.AssetTypeNft:   227.0,
							domain.AssetTypeDefi:  327.0,
						}
						ret[domain.ChainAddress{Chain: domain.Arbitrum, Address: domain.NewEvmAddress("0x2222")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 127.0,
							domain.AssetTypeNft:   227.0,
							domain.AssetTypeDefi:  327.0,
						}
					}
					if lo.Contains(addrs, domain.NewStrAddress("btc-group-0-0").String()) {
						ret[domain.ChainAddress{Chain: domain.Bitcoin, Address: domain.NewStrAddress("btc-group-0-0")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 128.0,
						}
					}
					if lo.Contains(addrs, domain.NewStrAddress("single-btc-wallet-0").String()) {
						ret[domain.ChainAddress{Chain: domain.Bitcoin, Address: domain.NewStrAddress("single-btc-wallet-0")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 129.0,
						}
					}
					if lo.Contains(addrs, domain.NewStrAddress("sol-group-0-0").String()) {
						ret[domain.ChainAddress{Chain: domain.Solana, Address: domain.NewStrAddress("sol-group-0-0")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 130.0,
						}
					}
					if lo.Contains(addrs, domain.NewStrAddress("single-sol-wallet-0").String()) {
						ret[domain.ChainAddress{Chain: domain.Solana, Address: domain.NewStrAddress("single-sol-wallet-0")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 130.0,
						}
					}
					if lo.Contains(addrs, domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn").String()) {
						ret[domain.ChainAddress{Chain: domain.Tron, Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 131.0,
						}
					}

					return ret, nil
				})

			// Mock GetAssetsTotalUsdValue24hAgo based on the expected addresses
			mockAssetRepo.EXPECT().
				GetPastAssetBalances(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, actualChains []domain.Chain, actualAddresses []domain.Address, types []domain.AssetType, from time.Time) (map[domain.ChainAddress]map[domain.AssetType]float64, error) {
					assert.ElementsMatch(t, actualChains, chains)
					assert.ElementsMatch(t, actualAddresses, tc.expectedAddrs)
					assert.ElementsMatch(t, types, assetTypes)
					addrs := lo.Map(tc.expectedAddrs, func(item domain.Address, _ int) string { return item.String() })
					ret := map[domain.ChainAddress]map[domain.AssetType]float64{}
					if lo.Contains(addrs, domain.NewEvmAddress("0x1111").String()) {
						ret[domain.ChainAddress{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x1111")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 123.9,
							domain.AssetTypeNft:   223.9,
							domain.AssetTypeDefi:  323.9,
						}
						ret[domain.ChainAddress{Chain: domain.Polygon, Address: domain.NewEvmAddress("0x1111")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 124.9,
							domain.AssetTypeNft:   224.9,
							domain.AssetTypeDefi:  324.9,
						}
						ret[domain.ChainAddress{Chain: domain.Arbitrum, Address: domain.NewEvmAddress("0x1111")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 125.9,
							domain.AssetTypeNft:   225.9,
							domain.AssetTypeDefi:  325.9,
						}
					}
					if lo.Contains(addrs, domain.NewEvmAddress("0x2222").String()) {
						ret[domain.ChainAddress{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x2222")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 126.9,
							domain.AssetTypeNft:   226.9,
							domain.AssetTypeDefi:  326.9,
						}
						ret[domain.ChainAddress{Chain: domain.Polygon, Address: domain.NewEvmAddress("0x2222")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 127.9,
							domain.AssetTypeNft:   227.9,
							domain.AssetTypeDefi:  327.9,
						}
						ret[domain.ChainAddress{Chain: domain.Arbitrum, Address: domain.NewEvmAddress("0x2222")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 127.9,
							domain.AssetTypeNft:   227.9,
							domain.AssetTypeDefi:  327.9,
						}
					}
					if lo.Contains(addrs, domain.NewStrAddress("btc-group-0-0").String()) {
						ret[domain.ChainAddress{Chain: domain.Bitcoin, Address: domain.NewStrAddress("btc-group-0-0")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 128.9,
						}
					}
					if lo.Contains(addrs, domain.NewStrAddress("single-btc-wallet-0").String()) {
						ret[domain.ChainAddress{Chain: domain.Bitcoin, Address: domain.NewStrAddress("single-btc-wallet-0")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 129.9,
						}
					}
					if lo.Contains(addrs, domain.NewStrAddress("sol-group-0-0").String()) {
						ret[domain.ChainAddress{Chain: domain.Solana, Address: domain.NewStrAddress("sol-group-0-0")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 130.9,
						}
					}
					if lo.Contains(addrs, domain.NewStrAddress("single-sol-wallet-0").String()) {
						ret[domain.ChainAddress{Chain: domain.Solana, Address: domain.NewStrAddress("single-sol-wallet-0")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 130.9,
						}
					}
					if lo.Contains(addrs, domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn").String()) {
						ret[domain.ChainAddress{Chain: domain.Tron, Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")}] = map[domain.AssetType]float64{
							domain.AssetTypeToken: 131.9,
						}
					}
					return ret, nil
				})

			// Call the ByPath function
			resp, kgErr := ByPath(ctx, uid, tc.path, chains, assetTypes)

			// Assertions
			assert.Nil(t, kgErr)
			assert.NotNil(t, resp)
			sort.SliceStable(tc.expectedResp, func(i, j int) bool {
				if tc.expectedResp[i].USDValue != tc.expectedResp[j].USDValue {
					return tc.expectedResp[i].USDValue > tc.expectedResp[j].USDValue
				}
				if tc.expectedResp[i].ChainID != tc.expectedResp[j].ChainID {
					return tc.expectedResp[i].ChainID < tc.expectedResp[j].ChainID
				}
				return tc.expectedResp[i].AssetType < tc.expectedResp[j].AssetType
			})
			// for i, expected := range tc.expectedResp {
			// 	t.Logf("expectedResp[%d]: %v", i, expected)
			// 	t.Logf("resp[%d]: %v", i, resp[i])
			// }
			assert.Equal(t, tc.expectedResp, resp)
		})
	}
}

// stubFetcher is a stub implementation of AssetFetcher for testing
type stubFetcher struct{}

func (m *stubFetcher) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Polygon, domain.Bitcoin, domain.Solana, domain.Tron}
}

func (m *stubFetcher) SupportedTypes() []domain.AssetType {
	return []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi}
}

func (m *stubFetcher) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	// This is a stub implementation, so we'll return an empty AggregatedAssets
	return &domain.AggregatedAssets{}, nil
}

// TestAllPaths tests the AllPaths function
func TestAllPaths(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Initialize mock repositories
	mockUserRepo := user.NewMockIRepo(ctrl)
	mockAssetRepo := asset.NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)

	// Initialize the user and asset packages with mocks
	user.Init(mockUserRepo)
	asset.Init(mockAssetRepo, map[domain.Chain]domain.ChainClient{}, []domain.AssetFetcher{&stubFetcher{}}, []domain.TokenAmountsFetcher{}, mockPriceFetcher)

	ctx := context.Background()
	uid := "user123"

	// Prepare UserData to be returned by GetUser
	userData := &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         uid,
			DisplayName: "Test User",
		},
		Wallets: &domain.Wallets{
			DefaultReceiveWallets: domain.ChainAddressMap{
				domain.Ethereum: domain.NewEvmAddress("0xEVMDefault"),
				domain.Bitcoin:  domain.NewStrAddress("btc-default"),
			},
			WalletGroups: []*domain.WalletGroup{
				{
					EvmWallets:    []*domain.UserWallet{{Address: "0x1111"}},
					BtcWallets:    []*domain.UserWallet{{Chain: "btc", Address: "btc-group-0-0"}},
					SolanaWallets: []*domain.UserWallet{{Chain: "sol", Address: "sol-group-0-0"}},
					TronWallets:   []*domain.UserWallet{{Chain: "tron", Address: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"}},
				},
			},
			EvmWallets: []*domain.UserWallet{{Address: "0x2222"}},
			Wallets: []*domain.UserWallet{
				{Chain: "btc", Address: "single-btc-wallet-0"},
				{Chain: "sol", Address: "single-sol-wallet-0"},
			},
		},
	}

	// Set expectation for GetUser
	mockUserRepo.EXPECT().
		GetUser(ctx, uid, "", true, &domain.UserPreloads{WithWallets: true}).
		DoAndReturn(func(ctx context.Context, uid string, clientID string, withUserData bool, preloads *domain.UserPreloads) (*domain.UserData, *code.KGError) {
			// Assert input parameters
			assert.Equal(t, uid, uid)
			assert.Equal(t, "", clientID)
			assert.True(t, withUserData)
			assert.NotNil(t, preloads)
			assert.True(t, preloads.WithWallets)
			return userData, nil
		}).
		AnyTimes()

	// Mock GetAssetsTotalUsdValue based on the expected addresses
	expectedChains := []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Polygon, domain.Bitcoin, domain.Solana, domain.Tron}
	assetTypes := []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi}
	expectedAddrs := []domain.Address{
		domain.NewEvmAddress("0x1111"),
		domain.NewEvmAddress("0x2222"),
		domain.NewStrAddress("btc-group-0-0"),
		domain.NewStrAddress("sol-group-0-0"),
		domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
		domain.NewStrAddress("single-btc-wallet-0"),
		domain.NewStrAddress("single-sol-wallet-0"),
	}
	mockAssetRepo.EXPECT().
		GetAssetBalances(ctx, gomock.Any(), gomock.Any(), assetTypes).
		DoAndReturn(func(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType) (map[domain.ChainAddress]map[domain.AssetType]float64, error) {
			assert.ElementsMatch(t, chains, expectedChains)
			assert.ElementsMatch(t, addresses, expectedAddrs)
			ret := map[domain.ChainAddress]map[domain.AssetType]float64{
				{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x1111")}: {
					domain.AssetTypeToken: 123.0,
					domain.AssetTypeNft:   223.0,
					domain.AssetTypeDefi:  323.0,
				},
				{Chain: domain.Polygon, Address: domain.NewEvmAddress("0x1111")}: {
					domain.AssetTypeToken: 124.0,
					domain.AssetTypeNft:   224.0,
					domain.AssetTypeDefi:  324.0,
				},
				{Chain: domain.Arbitrum, Address: domain.NewEvmAddress("0x1111")}: {
					domain.AssetTypeToken: 125.0,
					domain.AssetTypeNft:   225.0,
					domain.AssetTypeDefi:  325.0,
				},
				{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x2222")}: {
					domain.AssetTypeToken: 126.0,
					domain.AssetTypeNft:   226.0,
					domain.AssetTypeDefi:  326.0,
				},
				{Chain: domain.Polygon, Address: domain.NewEvmAddress("0x2222")}: {
					domain.AssetTypeToken: 127.0,
					domain.AssetTypeNft:   227.0,
					domain.AssetTypeDefi:  327.0,
				},
				{Chain: domain.Arbitrum, Address: domain.NewEvmAddress("0x2222")}: {
					domain.AssetTypeToken: 127.0,
					domain.AssetTypeNft:   227.0,
					domain.AssetTypeDefi:  327.0,
				},
				{Chain: domain.Bitcoin, Address: domain.NewStrAddress("btc-group-0-0")}: {
					domain.AssetTypeToken: 128.0,
				},
				{Chain: domain.Bitcoin, Address: domain.NewStrAddress("single-btc-wallet-0")}: {
					domain.AssetTypeToken: 129.0,
				},
				{Chain: domain.Solana, Address: domain.NewStrAddress("sol-group-0-0")}: {
					domain.AssetTypeToken: 130.0,
				},
				{Chain: domain.Solana, Address: domain.NewStrAddress("single-sol-wallet-0")}: {
					domain.AssetTypeToken: 130.0,
				},
				{Chain: domain.Tron, Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")}: {
					domain.AssetTypeToken: 131.0,
				},
			}
			return ret, nil
		})

	// Mock GetAssetsTotalUsdValue24hAgo based on the expected addresses
	mockAssetRepo.EXPECT().
		GetPastAssetBalances(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chains []domain.Chain, addresses []domain.Address, types []domain.AssetType, from time.Time) (map[domain.ChainAddress]map[domain.AssetType]float64, error) {
			assert.ElementsMatch(t, chains, expectedChains)
			assert.ElementsMatch(t, addresses, expectedAddrs)
			ret := map[domain.ChainAddress]map[domain.AssetType]float64{
				{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x1111")}: {
					domain.AssetTypeToken: 123.9,
					domain.AssetTypeNft:   223.9,
					domain.AssetTypeDefi:  323.9,
				},
				{Chain: domain.Polygon, Address: domain.NewEvmAddress("0x1111")}: {
					domain.AssetTypeToken: 124.9,
					domain.AssetTypeNft:   224.9,
					domain.AssetTypeDefi:  324.9,
				},
				{Chain: domain.Arbitrum, Address: domain.NewEvmAddress("0x1111")}: {
					domain.AssetTypeToken: 125.9,
					domain.AssetTypeNft:   225.9,
					domain.AssetTypeDefi:  325.9,
				},
				{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x2222")}: {
					domain.AssetTypeToken: 126.9,
					domain.AssetTypeNft:   226.9,
					domain.AssetTypeDefi:  326.9,
				},
				{Chain: domain.Polygon, Address: domain.NewEvmAddress("0x2222")}: {
					domain.AssetTypeToken: 127.9,
					domain.AssetTypeNft:   227.9,
					domain.AssetTypeDefi:  327.9,
				},
				{Chain: domain.Arbitrum, Address: domain.NewEvmAddress("0x2222")}: {
					domain.AssetTypeToken: 127.9,
					domain.AssetTypeNft:   227.9,
					domain.AssetTypeDefi:  327.9,
				},
				{Chain: domain.Bitcoin, Address: domain.NewStrAddress("btc-group-0-0")}: {
					domain.AssetTypeToken: 128.9,
				},
				{Chain: domain.Bitcoin, Address: domain.NewStrAddress("single-btc-wallet-0")}: {
					domain.AssetTypeToken: 129.9,
				},
				{Chain: domain.Solana, Address: domain.NewStrAddress("sol-group-0-0")}: {
					domain.AssetTypeToken: 130.9,
				},
				{Chain: domain.Solana, Address: domain.NewStrAddress("single-sol-wallet-0")}: {
					domain.AssetTypeToken: 130.9,
				},
				{Chain: domain.Tron, Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")}: {
					domain.AssetTypeToken: 131.9,
				},
			}
			return ret, nil
		})

	// Expected response
	expectedResp := map[string][]*WalletAssetBalance{
		"group-0": {
			{ChainID: "eth", AssetType: "token", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 123.0, USDValue24hAgo: 123.9},
			{ChainID: "eth", AssetType: "nft", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 223.0, USDValue24hAgo: 223.9},
			{ChainID: "eth", AssetType: "defi", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 323.0, USDValue24hAgo: 323.9},
			{ChainID: "matic", AssetType: "token", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 124.0, USDValue24hAgo: 124.9},
			{ChainID: "matic", AssetType: "nft", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 224.0, USDValue24hAgo: 224.9},
			{ChainID: "matic", AssetType: "defi", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 324.0, USDValue24hAgo: 324.9},
			{ChainID: "arb", AssetType: "token", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 125.0, USDValue24hAgo: 125.9},
			{ChainID: "arb", AssetType: "nft", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 225.0, USDValue24hAgo: 225.9},
			{ChainID: "arb", AssetType: "defi", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 325.0, USDValue24hAgo: 325.9},
			{ChainID: "btc", AssetType: "token", WalletAddress: "btc-group-0-0", USDValue: 128.0, USDValue24hAgo: 128.9},
			{ChainID: "btc", AssetType: "nft", WalletAddress: "btc-group-0-0", USDValue: 0, USDValue24hAgo: 0},
			{ChainID: "btc", AssetType: "defi", WalletAddress: "btc-group-0-0", USDValue: 0, USDValue24hAgo: 0},
			{ChainID: "sol", AssetType: "token", WalletAddress: "sol-group-0-0", USDValue: 130.0, USDValue24hAgo: 130.9},
			{ChainID: "sol", AssetType: "nft", WalletAddress: "sol-group-0-0", USDValue: 0, USDValue24hAgo: 0},
			{ChainID: "sol", AssetType: "defi", WalletAddress: "sol-group-0-0", USDValue: 0, USDValue24hAgo: 0},
			{ChainID: "tron", AssetType: "token", WalletAddress: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", USDValue: 131.0, USDValue24hAgo: 131.9},
			{ChainID: "tron", AssetType: "nft", WalletAddress: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", USDValue: 0, USDValue24hAgo: 0},
			{ChainID: "tron", AssetType: "defi", WalletAddress: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", USDValue: 0, USDValue24hAgo: 0},
		},
		"group-0-evm-0": {
			{ChainID: "eth", AssetType: "token", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 123.0, USDValue24hAgo: 123.9},
			{ChainID: "eth", AssetType: "nft", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 223.0, USDValue24hAgo: 223.9},
			{ChainID: "eth", AssetType: "defi", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 323.0, USDValue24hAgo: 323.9},
			{ChainID: "matic", AssetType: "token", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 124.0, USDValue24hAgo: 124.9},
			{ChainID: "matic", AssetType: "nft", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 224.0, USDValue24hAgo: 224.9},
			{ChainID: "matic", AssetType: "defi", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 324.0, USDValue24hAgo: 324.9},
			{ChainID: "arb", AssetType: "token", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 125.0, USDValue24hAgo: 125.9},
			{ChainID: "arb", AssetType: "nft", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 225.0, USDValue24hAgo: 225.9},
			{ChainID: "arb", AssetType: "defi", WalletAddress: domain.NewEvmAddress("0x1111").String(), USDValue: 325.0, USDValue24hAgo: 325.9},
		},
		"group-0-btc-0": {
			{ChainID: "btc", AssetType: "token", WalletAddress: "btc-group-0-0", USDValue: 128.0, USDValue24hAgo: 128.9},
			{ChainID: "btc", AssetType: "nft", WalletAddress: "btc-group-0-0", USDValue: 0, USDValue24hAgo: 0},
			{ChainID: "btc", AssetType: "defi", WalletAddress: "btc-group-0-0", USDValue: 0, USDValue24hAgo: 0},
		},
		"group-0-sol-0": {
			{ChainID: "sol", AssetType: "token", WalletAddress: "sol-group-0-0", USDValue: 130.0, USDValue24hAgo: 130.9},
			{ChainID: "sol", AssetType: "nft", WalletAddress: "sol-group-0-0", USDValue: 0, USDValue24hAgo: 0},
			{ChainID: "sol", AssetType: "defi", WalletAddress: "sol-group-0-0", USDValue: 0, USDValue24hAgo: 0},
		},
		"group-0-tron-0": {
			{ChainID: "tron", AssetType: "token", WalletAddress: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", USDValue: 131.0, USDValue24hAgo: 131.9},
			{ChainID: "tron", AssetType: "nft", WalletAddress: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", USDValue: 0, USDValue24hAgo: 0},
			{ChainID: "tron", AssetType: "defi", WalletAddress: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", USDValue: 0, USDValue24hAgo: 0},
		},
		"evm-0": {
			{ChainID: "eth", AssetType: "token", WalletAddress: domain.NewEvmAddress("0x2222").String(), USDValue: 126.0, USDValue24hAgo: 126.9},
			{ChainID: "eth", AssetType: "nft", WalletAddress: domain.NewEvmAddress("0x2222").String(), USDValue: 226.0, USDValue24hAgo: 226.9},
			{ChainID: "eth", AssetType: "defi", WalletAddress: domain.NewEvmAddress("0x2222").String(), USDValue: 326.0, USDValue24hAgo: 326.9},
			{ChainID: "matic", AssetType: "token", WalletAddress: domain.NewEvmAddress("0x2222").String(), USDValue: 127.0, USDValue24hAgo: 127.9},
			{ChainID: "matic", AssetType: "nft", WalletAddress: domain.NewEvmAddress("0x2222").String(), USDValue: 227.0, USDValue24hAgo: 227.9},
			{ChainID: "matic", AssetType: "defi", WalletAddress: domain.NewEvmAddress("0x2222").String(), USDValue: 327.0, USDValue24hAgo: 327.9},
			{ChainID: "arb", AssetType: "token", WalletAddress: domain.NewEvmAddress("0x2222").String(), USDValue: 127.0, USDValue24hAgo: 127.9},
			{ChainID: "arb", AssetType: "nft", WalletAddress: domain.NewEvmAddress("0x2222").String(), USDValue: 227.0, USDValue24hAgo: 227.9},
			{ChainID: "arb", AssetType: "defi", WalletAddress: domain.NewEvmAddress("0x2222").String(), USDValue: 327.0, USDValue24hAgo: 327.9},
		},
		"single-0": {
			{ChainID: "btc", AssetType: "token", WalletAddress: "single-btc-wallet-0", USDValue: 129.0, USDValue24hAgo: 129.9},
			{ChainID: "btc", AssetType: "nft", WalletAddress: "single-btc-wallet-0", USDValue: 0, USDValue24hAgo: 0},
			{ChainID: "btc", AssetType: "defi", WalletAddress: "single-btc-wallet-0", USDValue: 0, USDValue24hAgo: 0},
		},
		"single-1": {
			{ChainID: "sol", AssetType: "token", WalletAddress: "single-sol-wallet-0", USDValue: 130.0, USDValue24hAgo: 130.9},
			{ChainID: "sol", AssetType: "nft", WalletAddress: "single-sol-wallet-0", USDValue: 0, USDValue24hAgo: 0},
			{ChainID: "sol", AssetType: "defi", WalletAddress: "single-sol-wallet-0", USDValue: 0, USDValue24hAgo: 0},
		},
	}

	// Call the AllPaths function
	resp, kgErr := AllPaths(ctx, uid, []domain.Chain{
		domain.Ethereum,
		domain.Polygon,
		domain.Arbitrum,
		domain.Bitcoin,
		domain.Solana,
		domain.Tron,
	})

	// Assertions
	assert.Nil(t, kgErr)
	assert.NotNil(t, resp)
	assert.Len(t, resp, 8)
	for k, v := range expectedResp {
		t.Logf("checking key %s\n", k)
		assert.Contains(t, resp, k)
		sort.SliceStable(v, func(i, j int) bool {
			if v[i].USDValue != v[j].USDValue {
				return v[i].USDValue > v[j].USDValue
			}
			if v[i].ChainID != v[j].ChainID {
				return v[i].ChainID < v[j].ChainID
			}
			if v[i].AssetType != v[j].AssetType {
				return v[i].AssetType < v[j].AssetType
			}
			return v[i].WalletAddress < v[j].WalletAddress
		})
		// t.Logf("expected %+v\n", lo.Map(v, func(item *WalletAssetBalance, _ int) WalletAssetBalance { return *item }))
		// t.Logf("actual %+v\n", lo.Map(resp[k], func(item *WalletAssetBalance, _ int) WalletAssetBalance { return *item }))
		assert.Equal(t, v, resp[k])
	}
}
