package collect

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	lifiapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/lifi-api"
	okxapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/okx-api"
	signingclient "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/service/erc4337"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestProcessEVM(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})

	ctx := context.Background()
	uid := "test-uid-1"

	signingservertest.Setup(t)
	okxapi.InitDefault(domain.NewAllPassRateLimiter())
	lifiapi.InitDefault(domain.NewAllPassRateLimiter())

	ctrl := gomock.NewController(t)
	mockRepo := domain.NewMockOrganizationWalletRepo(ctrl)
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
		EvmAddress:    "******************************************",
		TronAddress:   "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7",
		SolanaAddress: "7ET7q8BBmpef4MDqE47tffFfHeE2y1ccT5MEDdA9pzeH",
	}, nil).AnyTimes()
	Init(mockRepo)
	erc4337.Init(mockRepo)

	depositAddress, err := signingclient.GetDepositAddresses(ctx, uid)
	if err != nil {
		t.Fatalf("failed to get deposit address: %v", err)
	}
	t.Logf("deposit address: %v", *depositAddress)
	assert.Equal(t, "******************************************", depositAddress.EvmAddress)
	assert.Equal(t, "5bhJnZJscqcZgoPRSwn9RYLmVCHZETPYygoRpzG7k67V", depositAddress.SolanaAddress)

	// salt := server.GetSaltByUID(ctx, uid)

	// target := domain.ChainAddress{
	// 	Chain:   domain.Solana,
	// 	Address: domain.NewStrAddress("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"),
	// }
	// kgErr := ProcessEVM(ctx, domain.BaseChain, salt, target, domain.Solana.MainToken().ID(), 0)
	// target := domain.ChainAddress{
	// 	Chain:   domain.BaseChain,
	// 	Address: domain.NewEvmAddress("******************************************"),
	// }
	// kgErr := ProcessEVM(ctx, domain.Arbitrum, salt, target, "eth", 0)
	// assert.Nil(t, kgErr)
	// if kgErr != nil {
	// 	t.Fatalf("failed to process EVM: %s", kgErr.String())
	// }

	// test harry's uid
	salt := server.GetSaltByUID(ctx, "ycZFiYxcelZvJFSo9wR2fQV81MC3")
	target := domain.ChainAddress{
		Chain:   domain.Arbitrum,
		Address: domain.NewEvmAddress("******************************************"),
	}
	if kgErr := ProcessEVM(ctx, domain.BaseChain, salt, target, domain.Arbitrum.MainToken().ID(), 0); kgErr != nil {
		t.Fatalf("failed to process EVM: %s", kgErr.String())
	}
}
