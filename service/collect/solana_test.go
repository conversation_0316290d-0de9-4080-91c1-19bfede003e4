package collect

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	lifiapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/lifi-api"
	okxapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/okx-api"
	signingclient "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/service/erc4337"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestProcessSolana(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API"})

	ctx := context.Background()
	uid := "test-uid-1"

	signingservertest.Setup(t)
	okxapi.InitDefault(domain.NewAllPassRateLimiter())
	lifiapi.InitDefault(domain.NewAllPassRateLimiter())

	ctrl := gomock.NewController(t)
	mockRepo := domain.NewMockOrganizationWalletRepo(ctrl)
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
		EvmAddress:    "******************************************",
		TronAddress:   "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7",
		SolanaAddress: "7ET7q8BBmpef4MDqE47tffFfHeE2y1ccT5MEDdA9pzeH",
	}, nil).AnyTimes()
	Init(mockRepo)
	erc4337.Init(mockRepo)

	depositAddress, err := signingclient.GetDepositAddresses(ctx, uid)
	if err != nil {
		t.Fatalf("failed to get deposit address: %v", err)
	}
	t.Logf("deposit address: %v", *depositAddress)
	assert.Equal(t, "******************************************", depositAddress.EvmAddress)
	assert.Equal(t, "5bhJnZJscqcZgoPRSwn9RYLmVCHZETPYygoRpzG7k67V", depositAddress.SolanaAddress)

	salt := server.GetSaltByUID(ctx, uid)

	// target := domain.ChainAddress{
	// 	Chain:   domain.Solana,
	// 	Address: domain.NewStrAddress("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"),
	// }
	// kgErr := ProcessSolana(ctx, salt, target, domain.Solana.MainToken().ID())
	target := domain.ChainAddress{
		Chain:   domain.Arbitrum,
		Address: domain.NewEvmAddress("0x0901549Bc297BCFf4221d0ECfc0f718932205e33"),
	}
	kgErr := ProcessSolana(ctx, salt, target, "0xaf88d065e77c8cC2239327C5EDb3A432268e5831") // USDC
	// kgErr := ProcessSolana(ctx, salt, target, domain.Arbitrum.MainToken().ID())
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Fatalf("failed to process solana: %v", kgErr.Error)
	}
}
