package tron

import (
	"context"
	"math/big"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	tronscanapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/tronscan-api"
	trongrid "github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/stretchr/testify/assert"
)

func TestGetTrc20EstimateGas(t *testing.T) {
	from := domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	recipient := domain.NewTronAddress("TL4GB7cvtr9eVXR4a5GVpokfGa6WKywXFw")
	token := domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t")

	gas, err := GetTrc20EstimateGasFee(context.Background(), domain.Tron, from, recipient, token, big.NewInt(1), 0)
	if err != nil {
		t.Fatalf("GetTrc20EstimateGas failed: %v", err)
	}
	t.Logf("Gas: %d", gas)
	assert.Greater(t, gas, int64(0), "gas should be greater than 0")
}

func TestGetFeeAndSentGas(t *testing.T) {
	t.Run("shasta", func(t *testing.T) {
		trongrid.Init(trongrid.InitParams{
			ShastaClient: trongrid.NewGrpcClient(context.Background(), domain.Shasta.ID()),
		})
		txHash := "cf77fa64cc19e7fadea82d3379b637d16891b85e7bca0781304a97650c7b29ea"
		fee, gas, err := GetFeeAndSentGas(context.Background(), domain.Shasta, txHash)
		if err != nil {
			t.Fatalf("GetFeeAndSentGas failed: %v", err)
		}
		t.Logf("Fee: %f, Gas: %f", fee, gas)
		assert.Equal(t, fee, float64(4.15714), "fee should be 4.15714")
		assert.Equal(t, gas, float64(0.5), "gas should be 0.5")
	})

	t.Run("tron", func(t *testing.T) {
		tronscanapi.InitDefault()
		txHash := "e53bb5fa3746d79ce10059934a0c746abafc3f94d884ca3dcf45cc24b0807c69"
		fee, gas, err := GetFeeAndSentGas(context.Background(), domain.Tron, txHash)
		if err != nil {
			t.Fatalf("GetFeeAndSentGas failed: %v", err)
		}
		t.Logf("Fee: %f, Gas: %f", fee, gas)
		assert.Equal(t, fee, float64(0.349), "fee should be 0.349")
		assert.Equal(t, gas, float64(0.5), "gas should be 0.5")
	})
}
