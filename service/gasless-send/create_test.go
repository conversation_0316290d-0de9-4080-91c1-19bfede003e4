package gaslesssend

import (
	context "context"
	"encoding/hex"
	"fmt"
	"net/http"
	"testing"
	time "time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/gotron-sdk/pkg/address"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/chain/tron"
	"github.com/kryptogo/kg-wallet-backend/domain"
	feeeapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/feee-api"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/alert"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	gomock "go.uber.org/mock/gomock"
)

const orgWallet = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
const from = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
const recipient = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
const privateKey = "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3"

func TestCreateGaslessSendV2Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	const (
		amount int64 = 10_000_000
		fee    int64 = 3_000_000
	)

	// Initialize mock repositories and executors
	mockRepo := NewMockIRepo(ctrl)
	mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	mockFeee := feeeapi.NewMockIFeee(ctrl)
	down := setupSigningServer(t, mockRepo)
	defer down()

	// Initialize the gasless-send service with mocks
	Init(mockRepo, mockExecutor, "http://handler.url")
	feeeapi.Init(mockFeee)

	tokenTransferToReceiverTx := createSignedTokenTransferTx(t, privateKey, from, recipient, usdt, amount)
	tokenTransferToOrgTx := createSignedTokenTransferTx(t, privateKey, from, orgWallet, usdt, fee)
	signedTxs := []*tron.Transaction{tokenTransferToReceiverTx, tokenTransferToOrgTx}

	orgID := 1
	chain := domain.Shasta
	{ // signing server
		mockRepo.EXPECT().
			GetSignerByOrganizationId(gomock.Any(), orgID, gomock.Any()).
			DoAndReturn(func(ctx context.Context, orgID int, encryptor domain.PrivateKeyEncryptor) (*domain.OrganizationSigner, *code.KGError) {
				privKey, err := encryptor.Decrypt(ctx, "cmJaQzN2VHg4WUFSSUVsYnNHbFRHb1ptaDNrdmFTMGk5aE1yWGp4KzcwdFc0V1hWYTVKemFzeWNaVlNpcUQ5WTNxRGpXUEpjVjZDZ0RCTWh0bThQTzVpUVMyQzhoS0x2UGluNm9MU2Urc1k9")
				assert.NoError(t, err)
				return &domain.OrganizationSigner{
					TronWallet:      domain.NewTronWallet(orgWallet, privKey),
					TronAlertPolicy: alert.NewTronAlertPolicy(100),
				}, nil
			})

		mockRepo.EXPECT().
			SaveAuditLog(gomock.Any(), gomock.Any()).Return(nil)
	}

	// Mock AcquireLockWithRetry
	mockRepo.EXPECT().
		AcquireLockWithRetry(gomock.Any(), "gasless-send-1-shasta", gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, key string, duration, retryInterval time.Duration) error {
			return nil
		})

	// Mock ReleaseLock
	mockRepo.EXPECT().
		ReleaseLock(gomock.Any(), "gasless-send-1-shasta").
		DoAndReturn(func(ctx context.Context, key string) error {
			return nil
		})

	// Mock GetWalletsByOrganizationId
	mockRepo.EXPECT().
		GetWalletsByOrganizationId(gomock.Any(), orgID).
		DoAndReturn(func(ctx context.Context, id int) (*domain.OrganizationWallets, *code.KGError) {
			return &domain.OrganizationWallets{TronAddress: orgWallet}, nil
		})

	// Mock GetProfitRate
	mockRepo.EXPECT().
		GetProfitRate(gomock.Any(), orgID, domain.ProfitRateServiceTypeSendGasless).
		DoAndReturn(func(ctx context.Context, orgID int, serviceType domain.ProfitRateServiceType) (*domain.AssetProProfitRate, *code.KGError) {
			return &domain.AssetProProfitRate{
				Service:          domain.ProfitRateServiceTypeSendGasless,
				ProfitRate:       decimal.NewFromFloat(0.05),
				ProfitShareRatio: decimal.NewFromFloat(0.1),
			}, nil
		})

	// Mock GetNativeAssetPrice
	mockRepo.EXPECT().
		GetNativeAssetPrice(gomock.Any(), chain.ID()).
		DoAndReturn(func(ctx context.Context, chainID string) (float64, error) {
			return 0.125, nil
		}).AnyTimes()

	// Mock GetTokenPrice
	mockRepo.EXPECT().
		GetTokenPrice(gomock.Any(), chain, usdt).Return(1.01, nil).AnyTimes()

	// Mock GetEnergyRentUnitCost
	mockRepo.EXPECT().
		GetEnergyRentUnitCost(gomock.Any()).
		DoAndReturn(func(ctx context.Context) (float64, error) {
			return 0.0001, nil
		})

	// Mock CreateGaslessSendV2
	mockRepo.EXPECT().
		CreateGaslessSendV2(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, gaslessSend *domain.GaslessSendV2) (int, error) {
			// Assert gaslessSend fields
			assert.Equal(t, orgID, gaslessSend.OrgID)
			assert.Equal(t, chain, gaslessSend.Chain)
			assert.Equal(t, domain.NewTronAddress(from), gaslessSend.From)
			assert.Equal(t, domain.NewTronAddress(usdt), gaslessSend.TokenAddress)
			assert.Equal(t, domain.NewTronAddress(recipient), gaslessSend.Recipient)
			assert.True(t, decimal.NewFromInt(10).Equal(gaslessSend.Amount), "Expected amount %s, got %s", decimal.NewFromInt(10).String(), gaslessSend.Amount.String())
			assert.Equal(t, 3.0, gaslessSend.Fee.InexactFloat64())
			assert.Equal(t, domain.GaslessSendStatusProcessing, gaslessSend.Status)
			return 10, nil
		})

	// Mock feee API AccountInfo
	mockFeee.EXPECT().
		AccountInfo(gomock.Any()).
		DoAndReturn(func(ctx context.Context) (*feeeapi.AccountInfo, error) {
			return &feeeapi.AccountInfo{
				TrxAddress: orgWallet,
				TrxMoney:   50.0,
			}, nil
		})

	// Mock feee API CreateOrder
	mockFeee.EXPECT().
		CreateOrder(gomock.Any(), from, gomock.Any()).
		DoAndReturn(func(ctx context.Context, address string, energy int) (float64, error) {
			t.Logf("energy: %d\n", energy)
			return 4.3, nil
		})

	// Mock UpdateGaslessSendV2
	mockRepo.EXPECT().
		UpdateGaslessSendV2(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, update *domain.UpdateGaslessSendV2Request) error {
			assert.Equal(t, 10, update.ID)
			assert.NotNil(t, update.EnergyRentCost)
			assert.Equal(t, 0.125, *update.NativeTokenPrice)
			assert.Equal(t, 1.01, *update.TokenPrice)
			assert.Equal(t, 4.3, *update.EnergyRentCost)
			assert.Equal(t, 0.625, *update.ActualCostUsd)
			assert.Equal(t, 0.05, update.ProfitMarginRate.InexactFloat64())
			assert.Equal(t, 0.1, update.ProfitShareRatio.InexactFloat64())
			// (3.0 / (1 + 0.1 + 0.05)) * 0.05 * 0.9 * 1.01
			assert.InDelta(t, 0.********739, update.ProfitMargin.InexactFloat64(), 0.000001)
			return nil
		})

	// Mock UpdateGaslessSendV2
	mockRepo.EXPECT().
		UpdateGaslessSendV2(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, update *domain.UpdateGaslessSendV2Request) error {
			assert.Equal(t, 10, update.ID)
			assert.NotNil(t, update.GasFaucetTxHash)
			return nil
		})

	// Mock Execute on AsyncTaskExecutor
	mockExecutor.EXPECT().
		Execute(gomock.Any(), "api-async-tasks-queue", gomock.Any(), "handle-gasless-send-v2-10").
		DoAndReturn(func(ctx context.Context, queueID string, task *domain.HttpTask, taskName string) error {
			assert.Equal(t, "POST", task.Method)
			assert.Equal(t, "http://handler.url/10", task.URL)
			return nil
		})

	// Call Create
	params := &CreateParams{
		OrgID:     orgID,
		UID:       "user_uid",
		Chain:     chain,
		From:      domain.NewTronAddress(from),
		SignedTxs: signedTxs,
	}

	id, kgErr := Create(context.Background(), params)

	// Final assertions
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Fatalf("kgErr: %v", kgErr.Error.Error())
	}
	assert.Equal(t, 10, id)
}

func TestCreateGaslessSendV2Failed(t *testing.T) {
	t.Run("too low fee amount", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// Initialize mock repositories and executors
		mockRepo := NewMockIRepo(ctrl)
		mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
		mockFeee := feeeapi.NewMockIFeee(ctrl)

		// Initialize the gasless-send service with mocks
		Init(mockRepo, mockExecutor, "http://handler.url")
		feeeapi.Init(mockFeee)

		const (
			transferAmount = 1_000_000
			feeAmount      = 2
		)

		tokenTransferToReceiverTx := createSignedTokenTransferTx(t, privateKey, from, recipient, usdt, transferAmount)
		tokenTransferToOrgTx := createSignedTokenTransferTx(t, privateKey, from, orgWallet, usdt, feeAmount)
		signedTxs := []*tron.Transaction{tokenTransferToReceiverTx, tokenTransferToOrgTx}

		orgID := 1
		chain := domain.Shasta

		// Mock AcquireLockWithRetry
		mockRepo.EXPECT().
			AcquireLockWithRetry(gomock.Any(), "gasless-send-1-shasta", gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, key string, duration, retryInterval time.Duration) error {
				return nil
			})

		// Mock ReleaseLock
		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), "gasless-send-1-shasta").
			DoAndReturn(func(ctx context.Context, key string) error {
				return nil
			})

		// Mock GetWalletsByOrganizationId
		mockRepo.EXPECT().
			GetWalletsByOrganizationId(gomock.Any(), orgID).
			DoAndReturn(func(ctx context.Context, id int) (*domain.OrganizationWallets, *code.KGError) {
				return &domain.OrganizationWallets{TronAddress: orgWallet}, nil
			})

		// Mock GetNativeAssetPrice
		mockRepo.EXPECT().
			GetNativeAssetPrice(gomock.Any(), chain.ID()).Return(0.125, nil).AnyTimes()

		// Mock GetTokenPrice
		mockRepo.EXPECT().
			GetTokenPrice(gomock.Any(), chain, usdt).Return(1.01, nil).AnyTimes()

		// Mock GetEnergyRentUnitCost
		mockRepo.EXPECT().
			GetEnergyRentUnitCost(gomock.Any()).
			DoAndReturn(func(ctx context.Context) (float64, error) {
				return 0.0001, nil
			})

		// Call Create
		params := &CreateParams{
			OrgID:     orgID,
			UID:       "user_uid",
			Chain:     chain,
			From:      domain.NewTronAddress(from),
			SignedTxs: signedTxs,
		}

		_, kgErr := Create(context.Background(), params)

		// Final assertions
		assert.NotNil(t, kgErr)
		assert.Contains(t, kgErr.Error.Error(), "fee amount is lower than minimal fee amount")
	})
	t.Run("insufficient token balance", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// Initialize mock repositories and executors
		mockRepo := NewMockIRepo(ctrl)
		mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
		mockFeee := feeeapi.NewMockIFeee(ctrl)

		// Initialize the gasless-send service with mocks
		Init(mockRepo, mockExecutor, "http://handler.url")
		feeeapi.Init(mockFeee)

		const (
			transferAmount = 10_000_000_000_000
			feeAmount      = 2_000_000
		)

		tokenTransferToReceiverTx := createSignedTokenTransferTx(t, privateKey, from, recipient, usdt, transferAmount)
		tokenTransferToOrgTx := createSignedTokenTransferTx(t, privateKey, from, orgWallet, usdt, feeAmount)
		signedTxs := []*tron.Transaction{tokenTransferToReceiverTx, tokenTransferToOrgTx}

		orgID := 1
		chain := domain.Shasta

		// Mock AcquireLockWithRetry
		mockRepo.EXPECT().
			AcquireLockWithRetry(gomock.Any(), "gasless-send-1-shasta", gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, key string, duration, retryInterval time.Duration) error {
				return nil
			})

		// Mock ReleaseLock
		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), "gasless-send-1-shasta").
			DoAndReturn(func(ctx context.Context, key string) error {
				return nil
			})

		// Mock GetWalletsByOrganizationId
		mockRepo.EXPECT().
			GetWalletsByOrganizationId(gomock.Any(), orgID).
			DoAndReturn(func(ctx context.Context, id int) (*domain.OrganizationWallets, *code.KGError) {
				return &domain.OrganizationWallets{TronAddress: orgWallet}, nil
			})

		// Mock GetNativeAssetPrice
		mockRepo.EXPECT().
			GetNativeAssetPrice(gomock.Any(), chain.ID()).Return(0.125, nil).AnyTimes()

		// Mock GetTokenPrice
		mockRepo.EXPECT().
			GetTokenPrice(gomock.Any(), chain, usdt).Return(1.01, nil).AnyTimes()

		// Call Create
		params := &CreateParams{
			OrgID:     orgID,
			UID:       "user_uid",
			Chain:     chain,
			From:      domain.NewTronAddress(from),
			SignedTxs: signedTxs,
		}

		_, kgErr := Create(context.Background(), params)

		// Final assertions
		assert.NotNil(t, kgErr)
		assert.Contains(t, kgErr.Error.Error(), "contract call reverted")
	})

	t.Run("contract call reverted", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		// Initialize mock repositories and executors
		mockRepo := NewMockIRepo(ctrl)
		mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
		mockFeee := feeeapi.NewMockIFeee(ctrl)

		// Initialize the gasless-send service with mocks
		Init(mockRepo, mockExecutor, "http://handler.url")
		feeeapi.Init(mockFeee)

		const (
			amount int64 = 100000000_000_000
			fee    int64 = 3_000_000
		)

		// too large amount of USDT
		tokenTransferToReceiverTx := createSignedTokenTransferTx(t, privateKey, from, recipient, usdt, amount)
		tokenTransferToOrgTx := createSignedTokenTransferTx(t, privateKey, from, orgWallet, usdt, fee)
		signedTxs := []*tron.Transaction{tokenTransferToReceiverTx, tokenTransferToOrgTx}

		orgID := 1
		chain := domain.Shasta

		// Mock AcquireLockWithRetry
		mockRepo.EXPECT().
			AcquireLockWithRetry(gomock.Any(), "gasless-send-1-shasta", gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, key string, duration, retryInterval time.Duration) error {
				return nil
			})

		// Mock ReleaseLock
		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), "gasless-send-1-shasta").
			DoAndReturn(func(ctx context.Context, key string) error {
				return nil
			})

		// Mock GetWalletsByOrganizationId
		mockRepo.EXPECT().
			GetWalletsByOrganizationId(gomock.Any(), orgID).
			DoAndReturn(func(ctx context.Context, id int) (*domain.OrganizationWallets, *code.KGError) {
				return &domain.OrganizationWallets{TronAddress: orgWallet}, nil
			})

		// Mock GetNativeAssetPrice
		mockRepo.EXPECT().
			GetNativeAssetPrice(gomock.Any(), chain.ID()).
			DoAndReturn(func(ctx context.Context, chainID string) (float64, error) {
				return 0.125, nil
			}).AnyTimes()

		// Call Create
		params := &CreateParams{
			OrgID:     orgID,
			UID:       "user_uid",
			Chain:     chain,
			From:      domain.NewTronAddress(from),
			SignedTxs: signedTxs,
		}

		_, kgErr := Create(context.Background(), params)

		// Final assertions
		assert.NotNil(t, kgErr)
		// It should fail because the token transfer contract call reverts
		assert.Contains(t, kgErr.String(), "contract call reverted")
	})
}

// Helper function to create a signed USDT transfer transaction
func createSignedTokenTransferTx(t *testing.T, privateKey, from, to, token string, amount int64) *tron.Transaction {
	priKey, err := util.ParsePrivateKey(privateKey)
	assert.NoError(t, err)

	wallet := domain.NewTronWallet("", priKey)

	client := tron.GetClient(domain.Shasta)

	method := "transfer(address,uint256)"
	params := []map[string]interface{}{
		{"address": to},
		{"uint256": fmt.Sprintf("%d", amount)},
	}
	tx, err := client.TriggerContract(context.Background(), domain.NewTronAddress(from), domain.NewTronAddress(token), method, params, int64(30_000_000), 0, "", 0)
	assert.NoError(t, err)

	tx.Transaction.RawData.Expiration = tx.Transaction.RawData.Timestamp + 10*60*60*1000 // 10 hours
	signedTx, err := wallet.SignTransaction(tx.Transaction.ToProto())
	assert.NoError(t, err)

	receiverAddress, err := address.Base58ToAddress(to)
	assert.NoError(t, err)
	senderAddress, err := address.Base58ToAddress(from)
	assert.NoError(t, err)
	tokenAddress, err := address.Base58ToAddress(token)
	assert.NoError(t, err)

	txHash, err := tron.GetTxHash(signedTx)
	assert.NoError(t, err)
	return &tron.Transaction{
		Visible: false,
		TxID:    txHash,
		RawData: tron.RawData{
			Contract: []tron.Contract{
				{
					Parameter: tron.Parameter{
						Value: tron.Value{
							Data:            fmt.Sprintf("a9059cbb000000000000000000000000%s%064x", receiverAddress.Hex()[4:], amount),
							OwnerAddress:    senderAddress.Hex()[2:],
							ContractAddress: tokenAddress.Hex()[2:],
						},
						TypeURL: "type.googleapis.com/protocol.TriggerSmartContract",
					},
					Type: "TriggerSmartContract",
				},
			},
			RefBlockBytes: hex.EncodeToString(signedTx.RawData.RefBlockBytes),
			RefBlockHash:  hex.EncodeToString(signedTx.RawData.RefBlockHash),
			Expiration:    signedTx.RawData.Expiration,
			FeeLimit:      tx.Transaction.RawData.FeeLimit,
			Timestamp:     signedTx.RawData.Timestamp,
		},
		Signature: []string{hex.EncodeToString(signedTx.Signature[0])},
	}
}

// Helper function to create a signed TRX transfer transaction
func createSignedTRXTransferTx(t *testing.T, privateKey, from, orgWallet string, fee float64) *tron.Transaction {
	priKey, err := util.ParsePrivateKey(privateKey)
	assert.NoError(t, err)

	wallet := domain.NewTronWallet("", priKey)
	feeDec := decimal.NewFromFloat(fee)
	rawFee := feeDec.Mul(decimal.NewFromInt(1_000_000)).BigInt().Int64()

	client := tron.GetClient(domain.Shasta)

	tx, err := client.TransferTRX(context.Background(), domain.NewTronAddress(from), domain.NewTronAddress(orgWallet), rawFee)
	assert.NoError(t, err)

	tx.Transaction.RawData.Expiration = tx.Transaction.RawData.Timestamp + 10*60*60*1000 // 10 hours
	signedTx, err := wallet.SignTransaction(tx.Transaction.ToProto())
	assert.NoError(t, err)

	receiverAddress, err := address.Base58ToAddress(orgWallet)
	assert.NoError(t, err)
	senderAddress, err := address.Base58ToAddress(from)
	assert.NoError(t, err)

	txHash, err := tron.GetTxHash(signedTx)
	assert.NoError(t, err)
	return &tron.Transaction{
		Visible: false,
		TxID:    txHash,
		RawData: tron.RawData{
			Contract: []tron.Contract{
				{
					Parameter: tron.Parameter{
						Value: tron.Value{
							Amount:       rawFee,
							OwnerAddress: senderAddress.Hex()[2:],
							ToAddress:    receiverAddress.Hex()[2:],
						},
						TypeURL: "type.googleapis.com/protocol.TransferContract",
					},
					Type: "TransferContract",
				},
			},
			RefBlockBytes: hex.EncodeToString(signedTx.RawData.RefBlockBytes),
			RefBlockHash:  hex.EncodeToString(signedTx.RawData.RefBlockHash),
			Expiration:    signedTx.RawData.Expiration,
			Timestamp:     signedTx.RawData.Timestamp,
		},
		Signature: []string{hex.EncodeToString(signedTx.Signature[0])},
	}
}

func setupSigningServer(t *testing.T, mockRepo IRepo) func() {
	server.Init(mockRepo, &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	// signing server
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/tron", signing.SignTronTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
	return func() {
		assert.Nil(t, srv.Shutdown(context.Background()))
	}
}
