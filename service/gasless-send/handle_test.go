package gaslesssend

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	sign_client "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// TestHandle tests the Handle function of gasless-send service.
func TestHandle(t *testing.T) {
	const orgWallet = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const from = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	const recipient = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
	const privateKey = "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3"

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Initialize mocks
	mockRepo := NewMockIRepo(ctrl)

	// Initialize the service with mockRepo
	Init(mockRepo, nil, "http://handler.url")
	initSignServer(t)
	// Define test cases
	tests := []struct {
		name          string
		setupMocks    func()
		handleFunc    func() *code.KGError
		expectedError *code.KGError
	}{
		{
			name: "Already Success",
			setupMocks: func() {
				mockRepo.EXPECT().
					AcquireLockWithRetry(gomock.Any(), "gasless-send-handle-1", gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					GetGaslessSendV2ByID(gomock.Any(), 1).
					Return(&domain.GaslessSendV2{
						ID:         1,
						Status:     domain.GaslessSendStatusSuccess,
						RetryCount: 0,
					}, nil)
				mockRepo.EXPECT().
					ReleaseLock(gomock.Any(), "gasless-send-handle-1")
			},
			handleFunc: func() *code.KGError {
				return Handle(context.Background(), 1)
			},
			expectedError: nil,
		},
		{
			name: "Already Failed",
			setupMocks: func() {
				mockRepo.EXPECT().
					AcquireLockWithRetry(gomock.Any(), "gasless-send-handle-2", gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					GetGaslessSendV2ByID(gomock.Any(), 2).
					Return(&domain.GaslessSendV2{
						ID:         2,
						Status:     domain.GaslessSendStatusFailed,
						RetryCount: 0,
					}, nil)
				mockRepo.EXPECT().
					ReleaseLock(gomock.Any(), "gasless-send-handle-2")
			},
			handleFunc: func() *code.KGError {
				return Handle(context.Background(), 2)
			},
			expectedError: code.NewKGError(code.GaslessSendExecutionFailed, http.StatusInternalServerError, errors.New("gasless send has already failed"), nil),
		},
		{
			name: "Success Processing",
			setupMocks: func() {
				const (
					amount int64 = 10_000_000
					fee    int64 = 1_000_000
				)

				gasFaucetTx := createSignedTRXTransferTx(t, privateKey, orgWallet, from, 0.7)
				tokenTransferToReceiverTx := createSignedTokenTransferTx(t, privateKey, from, recipient, usdt, amount)
				tokenTransferToOrgTx := createSignedTokenTransferTx(t, privateKey, from, orgWallet, usdt, fee)
				tokenTransferStr, err := json.Marshal(tokenTransferToReceiverTx)
				assert.Nil(t, err)
				trxTransferStr, err := json.Marshal(tokenTransferToOrgTx)
				assert.Nil(t, err)

				mockRepo.EXPECT().
					AcquireLockWithRetry(gomock.Any(), "gasless-send-handle-3", gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					GetGaslessSendV2ByID(gomock.Any(), 3).
					Return(&domain.GaslessSendV2{
						ID:              3,
						Status:          domain.GaslessSendStatusProcessing,
						RetryCount:      1,
						Chain:           domain.Shasta,
						From:            domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"),
						SignedTxs:       []string{string(tokenTransferStr), string(trxTransferStr)},
						GasFaucetTxHash: util.Ptr(gasFaucetTx.TxID),
					}, nil)

				// Update repository with transaction hashes and actuals
				mockRepo.EXPECT().
					UpdateGaslessSendV2(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, update *domain.UpdateGaslessSendV2Request) error {
						log.Println(update.RetryCount)
						assert.Equal(t, 3, update.ID)
						assert.Equal(t, tokenTransferToReceiverTx.TxID, *update.TokenTransferTxHash)
						assert.Equal(t, tokenTransferToOrgTx.TxID, *update.FeeTransferTxHash)
						return nil
					}).
					Return(nil)
				mockRepo.EXPECT().
					UpdateGaslessSendV2(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, update *domain.UpdateGaslessSendV2Request) error {
						assert.Equal(t, 3, update.ID)
						assert.Equal(t, domain.GaslessSendStatusSuccess, *update.Status)
						return nil
					}).
					Return(nil)

				mockRepo.EXPECT().
					UpdateGaslessSendV2(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, update *domain.UpdateGaslessSendV2Request) error {
						assert.Equal(t, 3, update.ID)
						assert.Equal(t, 2, *update.RetryCount)
						return nil
					}).
					Return(nil)

				// Release lock
				mockRepo.EXPECT().
					ReleaseLock(gomock.Any(), "gasless-send-handle-3")
			},
			handleFunc: func() *code.KGError {
				return Handle(context.Background(), 3)
			},
			expectedError: nil,
		},
		{
			name: "Retry Limit Reached",
			setupMocks: func() {
				mockRepo.EXPECT().
					AcquireLockWithRetry(gomock.Any(), "gasless-send-handle-4", gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					GetGaslessSendV2ByID(gomock.Any(), 4).
					Return(&domain.GaslessSendV2{
						ID:         4,
						Status:     domain.GaslessSendStatusProcessing,
						RetryCount: 3,
					}, nil)
				mockRepo.EXPECT().
					UpdateGaslessSendV2(gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					ReleaseLock(gomock.Any(), "gasless-send-handle-4")
			},
			handleFunc: func() *code.KGError {
				return Handle(context.Background(), 4)
			},
			expectedError: code.NewKGError(code.GaslessSendExecutionFailed, http.StatusInternalServerError, errors.New("maximum retry attempts reached"), nil),
		},
		{
			name: "GasFaucetTxHash Not Found",
			setupMocks: func() {
				const (
					amount int64 = 10_000_000
					fee    int64 = 1_000_000
				)

				tokenTransferToReceiverTx := createSignedTokenTransferTx(t, privateKey, from, recipient, usdt, amount)
				tokenTransferToOrgTx := createSignedTokenTransferTx(t, privateKey, from, orgWallet, usdt, fee)
				tokenTransferStr, err := json.Marshal(tokenTransferToReceiverTx)
				assert.Nil(t, err)
				trxTransferStr, err := json.Marshal(tokenTransferToOrgTx)
				assert.Nil(t, err)

				mockRepo.EXPECT().
					AcquireLockWithRetry(gomock.Any(), "gasless-send-handle-5", gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					GetGaslessSendV2ByID(gomock.Any(), 5).
					Return(&domain.GaslessSendV2{
						ID:         5,
						Status:     domain.GaslessSendStatusProcessing,
						RetryCount: 1,
						Chain:      domain.Shasta,
						From:       domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"),
						SignedTxs:  []string{string(tokenTransferStr), string(trxTransferStr)},
					}, nil)

				// Update repository with transaction hashes and actuals
				mockRepo.EXPECT().
					UpdateGaslessSendV2(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, update *domain.UpdateGaslessSendV2Request) error {
						log.Println(update.RetryCount)
						assert.Equal(t, 5, update.ID)
						assert.Equal(t, 2, *update.RetryCount)
						return nil
					}).
					Return(nil)

				// Release lock
				mockRepo.EXPECT().
					ReleaseLock(gomock.Any(), "gasless-send-handle-5")
			},
			handleFunc: func() *code.KGError {
				return Handle(context.Background(), 5)
			},
			expectedError: code.NewKGError(code.GaslessSendExecutionFailed, http.StatusInternalServerError, fmt.Errorf("gasless send has no gas faucet tx"), nil),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			err := tt.handleFunc()
			if tt.expectedError == nil {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
				assert.Equal(t, tt.expectedError.Code, err.Code)
				assert.Equal(t, tt.expectedError.HttpStatus, err.HttpStatus)
				assert.Contains(t, err.String(), tt.expectedError.String())
			}
		})
	}
}

func initSignServer(t *testing.T) {
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())

	// signing server
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	sign_client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/tron", signing.SignTronTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
}
