package gasswap

import (
	"context"
	"strconv"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestGetQuotesV2(t *testing.T) {
	usdtContract := domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t") // USDT contract on Tron

	ctrl := gomock.NewController(t)
	ctx := context.Background()
	mockEnergyRepo := domain.NewMockEnergyRentCostRepo(ctrl)
	mockGasSwapRepo := domain.NewMockGasSwapCommonRepo(ctrl)

	mockGasSwapRepo.EXPECT().IsGasSwapTokenSupported(gomock.Any(), 1, domain.Tron.ID(), usdtContract.String()).AnyTimes().Return(true, nil)
	mockGasSwapRepo.EXPECT().GetGasSwapOptions(gomock.Any(), domain.Tron.ID(), usdtContract.String()).AnyTimes().Return([]*domain.GasSwapOption{
		{Amount: "20.0", Symbol: "USDT", ImageURL: "https://example.com/usdt.png"},
		{Amount: "40.0", Symbol: "USDT", ImageURL: "https://example.com/usdt.png"},
		{Amount: "60.0", Symbol: "USDT", ImageURL: "https://example.com/usdt.png"},
	}, nil)
	mockGasSwapRepo.EXPECT().GetAllGasSwapSupportedTokens(gomock.Any()).AnyTimes().Return([]*domain.GasSwapToken{
		{
			ChainID:         domain.Tron.ID(),
			ContractAddress: usdtContract.String(),
		},
	}, nil)
	mockGasSwapRepo.EXPECT().GetNativeAssetPrice(gomock.Any(), domain.Tron.ID()).AnyTimes().Return(float64(0.21), nil)
	mockGasSwapRepo.EXPECT().GetAssetPrice(gomock.Any(), domain.Tron.ID(), usdtContract.String()).AnyTimes().Return(float64(1.0), nil)
	mockGasSwapRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).AnyTimes().Return(&domain.OrganizationWallets{
		TronAddress: "TDqSquXBgUCLYvYC4XZgrprLK589dkhSCf",
	}, nil)

	mockEnergyRepo.EXPECT().GetEnergyRentUnitCost(gomock.Any()).AnyTimes().Return(float64(0.000095), nil) // 95 SUN

	t.Run("No amount specified", func(t *testing.T) {
		mockFinanceRepo := domain.NewMockAssetProFinanceRepo(ctrl)
		mockFinanceRepo.EXPECT().GetProfitRate(gomock.Any(), 1, domain.ProfitRateServiceTypeSwapGas).AnyTimes().Return(&domain.AssetProProfitRate{
			Service:    domain.ProfitRateServiceTypeSwapGas,
			ProfitRate: decimal.NewFromFloat(0.1),
		}, nil)

		uRepo := &gasSwapV2Repo{
			GasSwapCommonRepo:   mockGasSwapRepo,
			AssetProFinanceRepo: mockFinanceRepo,
			EnergyRentCostRepo:  mockEnergyRepo,
		}
		Init(uRepo, nil)

		quotes, err := GetQuotesV2(ctx, 1, domain.Tron, domain.NewTronAddress("TRSXRWudzfzY4jH7AaMowdMNUXDkHisbcd"), usdtContract, nil)
		assert.Nil(t, err)
		assert.Len(t, quotes, 3)
		for _, quote := range quotes {
			t.Logf("quote: %+v", quote)
		}
		assert.Equal(t, "USDT", quotes[0].Symbol)
		assert.Equal(t, "https://example.com/usdt.png", quotes[0].ImageURL)
		assert.Equal(t, "20.0", quotes[0].Amount)
		assert.InDelta(t, 79.6069467857143, quotes[0].EstimatedReceive, 0.01)
		assert.InDelta(t, 16.717458825, quotes[0].EstimatedReceiveUsd, 0.01)

		assert.Equal(t, "60.0", quotes[2].Amount)
		assert.InDelta(t, 250.7783753571429, quotes[2].EstimatedReceive, 0.01)
		assert.InDelta(t, 52.663458825000006, quotes[2].EstimatedReceiveUsd, 0.01)
	})

	t.Run("2.5% profit rate, no share ratio", func(t *testing.T) {
		mockFinanceRepo := domain.NewMockAssetProFinanceRepo(ctrl)
		mockFinanceRepo.EXPECT().GetProfitRate(gomock.Any(), 1, domain.ProfitRateServiceTypeSwapGas).AnyTimes().Return(&domain.AssetProProfitRate{
			Service:    domain.ProfitRateServiceTypeSwapGas,
			ProfitRate: decimal.NewFromFloat(0.025),
		}, nil)

		uRepo := &gasSwapV2Repo{
			GasSwapCommonRepo:   mockGasSwapRepo,
			AssetProFinanceRepo: mockFinanceRepo,
			EnergyRentCostRepo:  mockEnergyRepo,
		}
		Init(uRepo, nil)

		amount := decimal.NewFromFloat(20)
		quotes, err := GetQuotesV2(ctx, 1, domain.Tron, domain.NewTronAddress("TRSXRWudzfzY4jH7AaMowdMNUXDkHisbcd"), usdtContract, &amount)
		assert.Nil(t, err)
		t.Logf("err: %v", err)
		assert.Len(t, quotes, 1)
		for _, quote := range quotes {
			t.Logf("quote: %+v", quote)
			assert.Equal(t, "USDT", quote.Symbol)
			assert.Equal(t, "https://example.com/usdt.png", quote.ImageURL)
			assert.Equal(t, "20", quote.Amount)
			assert.InDelta(t, 86.24085901785715, quote.EstimatedReceive, 0.01)
			assert.InDelta(t, 18.11058039375, quote.EstimatedReceiveUsd, 0.01)
		}
	})

	t.Run("7% profit rate, 80% share ratio (does not matter)", func(t *testing.T) {
		mockFinanceRepo := domain.NewMockAssetProFinanceRepo(ctrl)
		mockFinanceRepo.EXPECT().GetProfitRate(gomock.Any(), 1, domain.ProfitRateServiceTypeSwapGas).AnyTimes().Return(&domain.AssetProProfitRate{
			Service:          domain.ProfitRateServiceTypeSwapGas,
			ProfitRate:       decimal.NewFromFloat(0.07),
			ProfitShareRatio: decimal.NewFromFloat(0.8),
		}, nil)

		uRepo := &gasSwapV2Repo{
			GasSwapCommonRepo:   mockGasSwapRepo,
			AssetProFinanceRepo: mockFinanceRepo,
			EnergyRentCostRepo:  mockEnergyRepo,
		}
		Init(uRepo, nil)

		amount := decimal.NewFromFloat(100)
		quotes, err := GetQuotesV2(ctx, 1, domain.Tron, domain.NewTronAddress("TRSXRWudzfzY4jH7AaMowdMNUXDkHisbcd"), usdtContract, &amount)
		assert.Nil(t, err)
		t.Logf("err: %v", err)
		assert.Len(t, quotes, 1)
		for _, quote := range quotes {
			t.Logf("quote: %+v", quote)
			assert.Equal(t, "USDT", quote.Symbol)
			assert.Equal(t, "https://example.com/usdt.png", quote.ImageURL)
			assert.Equal(t, "100", quote.Amount)
			assert.InDelta(t, 436.0147973928572, quote.EstimatedReceive, 0.01)
			assert.InDelta(t, 91.5631074525, quote.EstimatedReceiveUsd, 0.01)
		}
	})

	t.Run("0% profit rate", func(t *testing.T) {
		mockFinanceRepo := domain.NewMockAssetProFinanceRepo(ctrl)
		mockFinanceRepo.EXPECT().GetProfitRate(gomock.Any(), 1, domain.ProfitRateServiceTypeSwapGas).AnyTimes().Return(&domain.AssetProProfitRate{
			Service:    domain.ProfitRateServiceTypeSwapGas,
			ProfitRate: decimal.NewFromFloat(0.0),
		}, nil)

		uRepo := &gasSwapV2Repo{
			GasSwapCommonRepo:   mockGasSwapRepo,
			AssetProFinanceRepo: mockFinanceRepo,
			EnergyRentCostRepo:  mockEnergyRepo,
		}
		Init(uRepo, nil)

		amount := decimal.NewFromFloat(50)
		quotes, err := GetQuotesV2(ctx, 1, domain.Tron, domain.NewTronAddress("TRSXRWudzfzY4jH7AaMowdMNUXDkHisbcd"), usdtContract, &amount)
		assert.Nil(t, err)
		t.Logf("err: %v", err)
		assert.Len(t, quotes, 1)
		for _, quote := range quotes {
			t.Logf("quote: %+v", quote)
			assert.Equal(t, "USDT", quote.Symbol)
			assert.Equal(t, "https://example.com/usdt.png", quote.ImageURL)
			assert.Equal(t, "50", quote.Amount)
			assert.InDelta(t, 231.09502023809523, quote.EstimatedReceive, 0.01)
			assert.InDelta(t, 48.529954249999996, quote.EstimatedReceiveUsd, 0.01)
		}
	})
}

func TestCalculateTxAmounts(t *testing.T) {
	usdtContract := domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t")   // USDT contract on Tron
	binanceHotAddr := domain.NewTronAddress("TDqSquXBgUCLYvYC4XZgrprLK589dkhSCf") // binance-hot-7
	addrWithFreeBandwidth := domain.NewTronAddress("TRSXRWudzfzY4jH7AaMowdMNUXDkHisbcd")

	ctrl := gomock.NewController(t)
	ctx := context.Background()
	mockEnergyRepo := domain.NewMockEnergyRentCostRepo(ctrl)
	mockGasSwapRepo := domain.NewMockGasSwapCommonRepo(ctrl)
	mockGasSwapRepo.EXPECT().GetAllGasSwapSupportedTokens(ctx).AnyTimes().Return([]*domain.GasSwapToken{
		{
			ChainID:         domain.Tron.ID(),
			ContractAddress: usdtContract.String(),
		},
	}, nil)
	mockEnergyRepo.EXPECT().GetEnergyRentUnitCost(gomock.Any()).AnyTimes().Return(float64(0.000095), nil) // 95 SUN

	uRepo := &gasSwapV2Repo{
		GasSwapCommonRepo:  mockGasSwapRepo,
		EnergyRentCostRepo: mockEnergyRepo,
	}
	Init(uRepo, nil)

	t.Run("Recipient swaps 20 USDT to TRX with free bandwidth", func(t *testing.T) {
		param := CalculateTxAmountsParam{
			Chain:                domain.Tron,
			OrgWallet:            binanceHotAddr,
			Recipient:            addrWithFreeBandwidth,
			TokenContractAddress: usdtContract,
			Amount:               "20.0",
			NativeTokenPrice:     0.21,  // 1 TRX = $0.21
			PaidTokenPrice:       1.0,   // 1 USDT = $1.0
			FeeRatio:             0.025, // 2.5% fee
			KGMinimumIncome:      0.5,   // 0.5 USD
		}

		result, kgErr := calculateTxAmounts(ctx, param)
		assert.Nil(t, kgErr)
		assert.NotNil(t, result)

		t.Logf("result: %+v", result)
		assert.Greater(t, result.EstimatedReceive, 0.0)
		assert.Greater(t, result.EstimatedReceiveUsdValue, 0.0)
		assert.InDelta(t, 0.4068, result.GasFaucetTxAmount, 0.01)
		assert.InDelta(t, 83.99091616071429, result.GasSwapTxAmount, 0.01)
		assert.InDelta(t, 17.652330393750002, result.EstimatedReceiveUsdValue, 0.01)

		paidTokenAmount, _ := strconv.ParseFloat(param.Amount, 64)
		profitUsdValue := paidTokenAmount*param.PaidTokenPrice - result.EstimatedCostForOrg*param.NativeTokenPrice
		assert.InDelta(t, 0.881434, profitUsdValue, 0.01)
	})

	t.Run("Org does not need to rent energy for recipient", func(t *testing.T) {
		param := CalculateTxAmountsParam{
			Chain:                domain.Tron,
			OrgWallet:            addrWithFreeBandwidth,
			Recipient:            binanceHotAddr,
			TokenContractAddress: usdtContract,
			Amount:               "20.0",
			NativeTokenPrice:     0.21,  // 1 TRX = $0.21
			PaidTokenPrice:       1.0,   // 1 USDT = $1.0
			FeeRatio:             0.025, // 2.5% fee
			KGMinimumIncome:      0.5,   // 0.5 USD
		}

		result, kgErr := calculateTxAmounts(ctx, param)
		assert.Nil(t, kgErr)
		assert.NotNil(t, result)

		t.Logf("result: %+v", result)
		assert.Greater(t, result.EstimatedReceive, 0.0)
		assert.Greater(t, result.EstimatedReceiveUsdValue, 0.0)
		assert.InDelta(t, 0.4068, result.GasFaucetTxAmount, 0.01)
		assert.InDelta(t, 90.13738928571428, result.GasSwapTxAmount, 0.01)
		assert.InDelta(t, 18.94308975, result.EstimatedReceiveUsdValue, 0.01)

		paidTokenAmount, _ := strconv.ParseFloat(param.Amount, 64)
		profitUsdValue := paidTokenAmount*param.PaidTokenPrice - result.EstimatedCostForOrg*param.NativeTokenPrice
		assert.InDelta(t, 0.985720, profitUsdValue, 0.01)
	})

	t.Run("Swap large amount (2000 USDT) to TRX with free bandwidth", func(t *testing.T) {
		param := CalculateTxAmountsParam{
			Chain:                domain.Tron,
			OrgWallet:            binanceHotAddr,
			Recipient:            addrWithFreeBandwidth,
			TokenContractAddress: usdtContract,
			Amount:               "2000.0",
			NativeTokenPrice:     0.4,   // 1 TRX = $0.4
			PaidTokenPrice:       0.99,  // 1 USDT = $1.0
			FeeRatio:             0.025, // 2.5% fee
			KGMinimumIncome:      10,    // 10 USD
		}

		result, kgErr := calculateTxAmounts(ctx, param)
		assert.Nil(t, kgErr)
		assert.NotNil(t, result)

		t.Logf("result: %+v", result)
		assert.Greater(t, result.EstimatedReceive, 0.0)
		assert.Greater(t, result.EstimatedReceiveUsdValue, 0.0)
		assert.InDelta(t, 0.4068, result.GasFaucetTxAmount, 0.01)
		assert.InDelta(t, 4795.330201875, result.GasSwapTxAmount, 0.01)
		assert.InDelta(t, 1918.15920075, result.EstimatedReceiveUsdValue, 0.01)

		paidTokenAmount, _ := strconv.ParseFloat(param.Amount, 64)
		profitUsdValue := paidTokenAmount*param.PaidTokenPrice - result.EstimatedCostForOrg*param.NativeTokenPrice
		assert.InDelta(t, 59.047969, profitUsdValue, 0.01)
	})
}
