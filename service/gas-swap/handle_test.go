package gasswap

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestHandleV2(t *testing.T) {
	signingservertest.Setup(t)
	const uid = "abc"
	gasSwapID := 3
	usdt := domain.NewTronAddress("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs")
	orgWallet := domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7")
	receiver := domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd")
	privateKey := "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3"
	signedTx := signingservertest.SignTransferTx(t, privateKey, receiver, orgWallet, "20", 10*365*86400*1000)
	signedTxBytes, err := json.Marshal(signedTx)
	assert.NoError(t, err)
	signedTxStr := string(signedTxBytes)

	ctx := context.Background()
	ctrl := gomock.NewController(t)
	repo := domain.NewMockGasSwapCommonRepo(ctrl)
	financeRepo := domain.NewMockAssetProFinanceRepo(ctrl)
	financeRepo.EXPECT().GetProfitRate(gomock.Any(), 2, domain.ProfitRateServiceTypeSwapGas).AnyTimes().Return(&domain.AssetProProfitRate{
		Service:          domain.ProfitRateServiceTypeSwapGas,
		ProfitShareRatio: decimal.NewFromFloat(0.5),
	}, nil)

	repo.EXPECT().GetAllGasSwapSupportedTokens(ctx).AnyTimes().Return([]*domain.GasSwapToken{
		{
			ChainID:         domain.Shasta.ID(),
			ContractAddress: usdt.String(),
		},
	}, nil)
	repo.EXPECT().AcquireLockWithRetry(gomock.Any(), "gas-swap-handle-v2-3", gomock.Any(), gomock.Any()).Return(nil)
	repo.EXPECT().GetGasSwapByID(gomock.Any(), gasSwapID).Return(&domain.GasSwap{
		ID:                  gasSwapID,
		OrgID:               2,
		UID:                 util.Ptr(uid),
		ChainID:             domain.Shasta.ID(),
		From:                orgWallet.String(),
		TokenAddress:        usdt.String(),
		Amount:              "20",
		EstimatedReceive:    188.82268875,
		EstimatedReceiveUsd: 18.882268875,
		EstimatedCost:       190.26196375,
		GasFaucetTxAmount:   0.24,
		GasSwapTxAmount:     188.78268875,
		ProfitMarginRate:    util.Ptr(decimal.NewFromFloat(0.025)),
		NativeTokenPrice:    util.Ptr(0.1),
		PaidTokenPrice:      util.Ptr(1.0),
		ReceiveWallet:       receiver.String(),
		SignedTxs:           []string{signedTxStr},
		Status:              domain.GasSwapStatusProcessing,
		RetryCount:          0,
		Version:             2,
	}, nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, gasSwapID, update.ID)
		assert.NotNil(t, update.GasFaucetTxHash)
		if update.GasFaucetTxHash != nil {
			t.Logf("GasFaucetTxHash: %s\n", *update.GasFaucetTxHash)
		}
		return true
	})).Return(nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, gasSwapID, update.ID)
		assert.NotNil(t, update.UserTransferTxHash)
		if update.UserTransferTxHash != nil {
			t.Logf("UserTransferTxHash: %s\n", *update.UserTransferTxHash)
		}
		return true
	})).Return(nil)
	repo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).AnyTimes().Return(&domain.OrganizationWallets{TronAddress: orgWallet.String()}, nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, gasSwapID, update.ID)
		assert.NotNil(t, update.GasSwapTxHash)
		if update.GasSwapTxHash != nil {
			t.Logf("GasSwapTxHash: %s\n", *update.GasSwapTxHash)
		}
		return true
	})).Return(nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, gasSwapID, update.ID)
		assert.NotNil(t, update.Status)
		assert.Equal(t, domain.GasSwapStatusSuccess, *update.Status)
		return true
	})).Return(nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		// EstimatedReceive: 188.82268875
		// EstimatedReceiveUsd: 18.882268875
		// EstimatedCost: 190.26196375
		// GasFaucetTxAmount: 0.24
		// GasSwapTxAmount: 188.78268875
		assert.Equal(t, gasSwapID, update.ID)
		assert.NotNil(t, update.ActualReceive)
		assert.NotNil(t, update.ActualCost)
		assert.NotNil(t, update.ProfitMargin)
		if update.ActualReceive != nil && update.ActualCost != nil && update.ProfitMargin != nil {
			t.Logf("ActualReceive: %f, ActualCost: %f, ProfitMargin: %f\n",
				*update.ActualReceive, *update.ActualCost, update.ProfitMargin.InexactFloat64())
			assert.InEpsilon(t, 188, *update.ActualReceive, 0.05)
			assert.InEpsilon(t, 190, *update.ActualCost, 0.05)
			assert.InEpsilon(t, 0.4, update.ProfitMargin.InexactFloat64(), 0.5)
		}
		return true
	})).Return(nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, gasSwapID, update.ID)
		assert.Equal(t, 1, *update.RetryCount)
		return true
	})).Return(nil)
	repo.EXPECT().ReleaseLock(gomock.Any(), "gas-swap-handle-v2-3")
	uRepo := &gasSwapV2Repo{
		GasSwapCommonRepo:   repo,
		AssetProFinanceRepo: financeRepo,
	}
	Init(uRepo, nil)

	kgErr := HandleV2(ctx, 3)
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Logf("kgErr: %v", kgErr.String())
	}
}
