package gasswap

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/chain/tron"
	"github.com/kryptogo/kg-wallet-backend/domain"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestValidateAmountSufficient(t *testing.T) {
	ctx := context.Background()

	ctrl := gomock.NewController(t)
	repo := domain.NewMockGasSwapCommonRepo(ctrl)

	repo.EXPECT().GetAllGasSwapSupportedTokens(ctx).AnyTimes().Return([]*domain.GasSwapToken{}, nil)

	chain := domain.Shasta
	tokenAddress := domain.NewTronAddress("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs") // USDT
	uRepo := &gasSwapV2Repo{
		GasSwapCommonRepo: repo,
	}
	Init(uRepo, nil)

	err := isAmountSupportedAndEnough(ctx, chain, tokenAddress, domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"), "100")
	assert.Nil(t, err)
	err = isAmountSupportedAndEnough(ctx, chain, tokenAddress, domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"), "10000000")
	assert.NotNil(t, err)
	assert.Equal(t, "insufficient token balance", err.String())
	err = isAmountSupportedAndEnough(ctx, chain, tokenAddress, domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"), "1")
	assert.NotNil(t, err)
	assert.Equal(t, "amount not supported", err.String())
}

func TestValidateNativeTokenEnough(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	repo := domain.NewMockGasSwapCommonRepo(ctrl)

	repo.EXPECT().GetAllGasSwapSupportedTokens(ctx).AnyTimes().Return([]*domain.GasSwapToken{}, nil)
	repo.EXPECT().PendingGasSwapCostSum(ctx, 2, domain.Shasta.ID()).Return(10.0, nil)
	repo.EXPECT().PendingGasSwapCostSum(ctx, 2, domain.Shasta.ID()).Return(1000000.0, nil)
	uRepo := &gasSwapV2Repo{
		GasSwapCommonRepo: repo,
	}
	Init(uRepo, nil)

	orgID := 2
	chain := domain.Shasta
	wallet := domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7")
	cost := 0.123
	err := validateNativeTokenEnough(ctx, orgID, chain, wallet, cost)
	assert.Nil(t, err)

	cost = 1000000.0
	err = validateNativeTokenEnough(ctx, orgID, chain, wallet, cost)
	assert.NotNil(t, err)
	assert.Equal(t, "insufficient native token", err.String())
}

func TestValidateSignedTransferTxs(t *testing.T) {
	ctx := context.Background()

	recipient := domain.NewTronAddress("TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft")
	txStr := `{"raw_data":{"contract":[{"parameter":{"value":{"data":"a9059cbb0000000000000000000000004d8733bd335d86d06dd3a28aebe4f822ca7a92300000000000000000000000000000000000000000000000000000000001312d00","owner_address":"419b0e4215f6ffb52680076108a318d66c11224e2a","contract_address":"4142a1e39aefa49290f2b3f9ed688d7cecf86cd6e0"},"type_url":"type.googleapis.com/protocol.TriggerSmartContract"},"type":"TriggerSmartContract"}],"ref_block_bytes":"7602","ref_block_hash":"c228282ff4b88020","expiration":1732613626485,"fee_limit":30000000,"timestamp":1732613625485},"signature":["ba7a6f6600a0a867b3f5a062502dcf75dc5def6f1c745dc56a3d8f693df8c37a7014510575d0c9b9add79cfc96c2b3472469decaef2f37ea64e7ce618a1bb6ce1c"],"visible":false,"txid":"c29ce0c053afaf15a916821e0082cdbf03803692e4267048536485317b1fc063"}`

	token := domain.NewTronAddress("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs") // USDT
	badSender := domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	kgErr := validateSignedTransferTxs(ctx, []string{txStr}, domain.Shasta, token, badSender, "20.0", recipient)
	assert.NotNil(t, kgErr)
	assert.Equal(t, "incorrect signer", kgErr.String())

	sender := domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd")
	kgErr = validateSignedTransferTxs(ctx, []string{txStr}, domain.Shasta, token, sender, "20.0", recipient)
	assert.NotNil(t, kgErr)
	assert.Equal(t, "expiration should be at least 3 minutes later", kgErr.String())

	txStr = `{"raw_data":{"contract":[{"parameter":{"value":{"data":"a9059cbb0000000000000000000000004d8733bd335d86d06dd3a28aebe4f822ca7a92300000000000000000000000000000000000000000000000000000000001312d00","owner_address":"419b0e4215f6ffb52680076108a318d66c11224e2a","contract_address":"4142a1e39aefa49290f2b3f9ed688d7cecf86cd6e0"},"type_url":"type.googleapis.com/protocol.TriggerSmartContract"},"type":"TriggerSmartContract"}],"ref_block_bytes":"75de","ref_block_hash":"85c723e1f1f1a96c","expiration":2047973518285,"fee_limit":30000000,"timestamp":1732613518285},"signature":["a79344b885c6317838fcc5dfd69061ae6623d449e893118a32cb10b6c7d2184134364871ff900b80667dee99d2596c41b961eedd30b29426ae6e96f0ef167aca1b"],"visible":false,"txid":"12ea147ce0063f691e2206a859dad9a27125b57c0e6c82e2505b3159bc19b4b9"}`

	badRecipient := domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd")
	kgErr = validateSignedTransferTxs(ctx, []string{txStr}, domain.Shasta, token, sender, "20.0", badRecipient)
	assert.NotNil(t, kgErr)
	assert.Equal(t, fmt.Sprintf("receiver address(%s) not equal to %s", recipient, badRecipient), kgErr.String())

	kgErr = validateSignedTransferTxs(ctx, []string{txStr}, domain.Shasta, token, sender, "12345.123", recipient)
	assert.NotNil(t, kgErr)
	assert.Equal(t, "incorrect transfer amount", kgErr.String())

	kgErr = validateSignedTransferTxs(ctx, []string{txStr}, domain.Shasta, token, sender, "20.0", recipient)
	t.Logf("kgErr: %v", kgErr)
	assert.Nil(t, kgErr)

	privateKey := "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3"
	tx := signingservertest.SignTransferTx(t, privateKey, sender, recipient, "20.0", 1*time.Hour)
	txToSend := tx.ToProto()

	tronClient := tron.GetClient(domain.Shasta)
	txHash, err := tronClient.BroadcastTransaction(ctx, txToSend)
	assert.Nil(t, err)
	t.Logf("txHash: %s", txHash)

	ctxWithTimeout, cancel := context.WithTimeout(ctx, 20*time.Second)
	defer cancel()
	status, err := tronClient.WaitUntilTransactionConfirmed(ctxWithTimeout, txHash)
	assert.Nil(t, err)
	assert.Equal(t, domain.TransactionStatusSuccess, status)

	signedTxBytes, err := json.Marshal(tx)
	assert.NoError(t, err)
	signedTxStr := string(signedTxBytes)
	t.Logf("signedTxStr: %s", signedTxStr)
	kgErr = validateSignedTransferTxs(ctx, []string{signedTxStr}, domain.Shasta, token, sender, "20", domain.NewTronAddress("TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft"))
	assert.NotNil(t, kgErr)
	assert.Equal(t, "tx already used", kgErr.String())
}
