package gasswap

import (
	"context"
	"math/big"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/chain/tron"
	"github.com/kryptogo/kg-wallet-backend/domain"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestSendTrxTx(t *testing.T) {
	signingservertest.Setup(t)
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	repo := domain.NewMockGasSwapCommonRepo(ctrl)
	repo.EXPECT().GetAllGasSwapSupportedTokens(ctx).AnyTimes().Return([]*domain.GasSwapToken{}, nil)

	repo.EXPECT().GetWalletsByOrganizationId(ctx, 1).Return(&domain.OrganizationWallets{TronAddress: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"}, nil)
	uRepo := &gasSwapV2Repo{
		GasSwapCommonRepo: repo,
	}
	Init(uRepo, nil)

	chain := domain.Shasta
	amount := big.NewInt(12_340_000) // 12.34 TRX
	txHash, kgErr := sendTrx(ctx, chain, domain.NewTronAddress("T9yD1CAWBKg8LizsZqZgSjMXR5XckavaEL"), amount)
	assert.Nil(t, kgErr)
	assert.NotEmpty(t, txHash)
	t.Logf("txHash: %s", txHash)

	client := tron.GetClient(chain)
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	status, err := client.WaitUntilTransactionConfirmed(ctxWithTimeout, txHash)
	assert.Nil(t, err)
	assert.Equal(t, domain.TransactionStatusSuccess, status)

	txDetail, err := client.TransactionDetail(ctx, txHash)
	assert.Nil(t, err)
	assert.Equal(t, amount, txDetail.Value)
}
