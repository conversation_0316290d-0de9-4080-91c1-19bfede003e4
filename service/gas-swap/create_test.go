package gasswap

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

type gasSwapV2Repo struct {
	domain.GasSwapCommonRepo
	domain.AssetProFinanceRepo
	domain.EnergyRentCostRepo
}

func TestCreateV2(t *testing.T) {
	const uid = "abc"
	usdt := domain.NewTronAddress("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs")
	orgWallet := domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7")
	receiver := domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd")
	privateKey := "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3"
	signedTx := signingservertest.SignTransferTx(t, privateKey, receiver, orgWallet, "150", 10*365*86400*1000)
	signedTxBytes, err := json.Marshal(signedTx)
	assert.NoError(t, err)
	signedTxStr := string(signedTxBytes)

	ctx := context.Background()
	ctrl := gomock.NewController(t)
	repo := domain.NewMockGasSwapCommonRepo(ctrl)
	financeRepo := domain.NewMockAssetProFinanceRepo(ctrl)
	energyRepo := domain.NewMockEnergyRentCostRepo(ctrl)
	executor := domain.NewMockAsyncTaskExecutor(ctrl)
	repo.EXPECT().GetAllGasSwapSupportedTokens(ctx).AnyTimes().Return([]*domain.GasSwapToken{
		{
			ChainID:         domain.Shasta.ID(),
			ContractAddress: usdt.String(),
		},
	}, nil)
	repo.EXPECT().AcquireLockWithRetry(gomock.Any(), "gas-swap-2-shasta", gomock.Any(), gomock.Any()).Return(nil)
	repo.EXPECT().HasProcessingGasSwap(gomock.Any(), uid).Return(false, nil)
	repo.EXPECT().IsGasSwapTokenSupported(gomock.Any(), 2, domain.Shasta.ID(), usdt.String()).Return(true, nil)
	repo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).AnyTimes().Return(&domain.OrganizationWallets{TronAddress: orgWallet.String()}, nil)
	repo.EXPECT().GetNativeAssetPrice(gomock.Any(), domain.Shasta.ID()).Return(0.1, nil)
	repo.EXPECT().GetAssetPrice(gomock.Any(), domain.Shasta.ID(), usdt.String()).Return(1.0, nil)
	financeRepo.EXPECT().GetProfitRate(gomock.Any(), 2, domain.ProfitRateServiceTypeSwapGas).AnyTimes().Return(&domain.AssetProProfitRate{
		Service:          domain.ProfitRateServiceTypeSwapGas,
		ProfitRate:       decimal.NewFromFloat(0.025),
		ProfitShareRatio: decimal.NewFromFloat(0.5),
	}, nil)
	energyRepo.EXPECT().GetEnergyRentUnitCost(gomock.Any()).AnyTimes().Return(float64(0.000095), nil)
	repo.EXPECT().PendingGasSwapCostSum(gomock.Any(), 2, domain.Shasta.ID()).Return(10.0, nil)
	repo.EXPECT().CreateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		gasSwap, ok := v.(*domain.GasSwap)
		if !ok {
			return false
		}
		t.Logf("gasSwap EstimatedReceive: %v", gasSwap.EstimatedReceive)
		t.Logf("gasSwap EstimatedReceiveUsd: %v", gasSwap.EstimatedReceiveUsd)
		t.Logf("gasSwap EstimatedCost: %v", gasSwap.EstimatedCost)
		t.Logf("gasSwap GasFaucetTxAmount: %v", gasSwap.GasFaucetTxAmount)
		t.Logf("gasSwap GasSwapTxAmount: %v", gasSwap.GasSwapTxAmount)
		t.Logf("gasSwap ProfitMarginRate: %v", gasSwap.ProfitMarginRate)
		t.Logf("gasSwap ProfitMargin: %v", gasSwap.ProfitMargin)
		assert.Equal(t, 2, gasSwap.OrgID)
		assert.Equal(t, uid, *gasSwap.UID)
		assert.Equal(t, domain.Shasta.ID(), gasSwap.ChainID)
		assert.Equal(t, orgWallet.String(), gasSwap.From)
		assert.Equal(t, usdt.String(), gasSwap.TokenAddress)
		assert.Equal(t, receiver.String(), gasSwap.ReceiveWallet)
		assert.Equal(t, "150", gasSwap.Amount)
		// native token full amount: 150 * 1.0 / 0.1 = 1500
		assert.InDelta(t, 1459.0979568750001, gasSwap.EstimatedReceive, 0.01)
		assert.InDelta(t, 145.9097956875, gasSwap.EstimatedReceiveUsd, 0.01)
		assert.InDelta(t, 1460.676231875, gasSwap.EstimatedCost, 0.01)
		assert.InDelta(t, 0.4068, gasSwap.GasFaucetTxAmount, 0.01)
		assert.InDelta(t, 1459.030156875, gasSwap.GasSwapTxAmount, 0.01)
		assert.True(t, decimal.NewFromFloat(0.025).Equal(*gasSwap.ProfitMarginRate))
		assert.Equal(t, signedTxStr, gasSwap.SignedTxs[0])
		assert.Equal(t, domain.GasSwapStatusProcessing, gasSwap.Status)
		assert.Equal(t, 0.1, *gasSwap.NativeTokenPrice)
		assert.Equal(t, 1.0, *gasSwap.PaidTokenPrice)
		assert.Equal(t, 2, gasSwap.Version)
		return true
	},
	)).Return(3, nil)
	executor.EXPECT().Execute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, queueID string, task *domain.HttpTask, taskName string) error {
		assert.Equal(t, "POST", task.Method)
		assert.Contains(t, task.URL, "/_v/handle_gas_swap/v2/3")
		assert.Equal(t, "handle-gas-swap-v2-3", taskName)
		return nil
	})
	repo.EXPECT().ReleaseLock(gomock.Any(), "gas-swap-2-shasta")

	uRepo := &gasSwapV2Repo{
		GasSwapCommonRepo:   repo,
		AssetProFinanceRepo: financeRepo,
		EnergyRentCostRepo:  energyRepo,
	}
	Init(uRepo, executor)

	params := &CreateParamsV2{
		OrgID:         2,
		UID:           uid,
		Chain:         domain.Shasta,
		TokenAddress:  usdt,
		Amount:        "150",
		ReceiveWallet: receiver,
		SignedTxs:     []string{signedTxStr},
	}
	gasSwapID, kgErr := CreateV2(ctx, params)
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Logf("kgErr: %v", kgErr)
	}
	assert.Equal(t, 3, gasSwapID)
}
