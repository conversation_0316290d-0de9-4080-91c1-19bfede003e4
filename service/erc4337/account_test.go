package erc4337

import (
	"math/big"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestGetAccountAddress(t *testing.T) {
	t.<PERSON>llel()
	chain := domain.Sepolia

	ownerAddress := domain.NewEvmAddress("******************************************")
	address, err := GetAccountAddress(chain, ownerAddress, big.NewInt(1))
	assert.NoError(t, err)
	assert.Equal(t, "******************************************", address.String())

	address, err = GetAccountAddress(chain, ownerAddress, big.NewInt(2))
	assert.NoError(t, err)
	assert.Equal(t, "******************************************", address.String())

	ownerAddress = domain.NewEvmAddress("******************************************")
	address, err = GetAccountAddress(chain, ownerAddress, big.NewInt(1))
	assert.NoError(t, err)
	assert.Equal(t, "******************************************", address.String())
}
