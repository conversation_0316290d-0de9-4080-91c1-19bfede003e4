package erc4337

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/chain/evm"
	"github.com/kryptogo/kg-wallet-backend/domain"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestDeposit(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	signingservertest.Setup(t)

	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
		EvmAddress: "******************************************",
	}, nil)
	Init(mockRepo)

	amount, err := decimal.NewFromString("0.001")
	if err != nil {
		t.Fatalf("failed to create decimal: %v", err)
	}
	amount = amount.Shift(int32(domain.Holesky.MainToken().Decimals()))
	txHash, err := Deposit(ctx, domain.Holesky, amount.BigInt())
	if err != nil {
		t.Fatalf("failed to deposit: %v", err)
	}
	t.Logf("deposited to paymaster: %s", txHash)
	client := evm.GetClient(domain.Holesky)
	waitCtx, cancel := context.WithTimeout(ctx, 180*time.Second)
	defer cancel()
	txStatus, err := client.WaitUntilTransactionConfirmed(waitCtx, txHash)
	if err != nil {
		t.Fatalf("failed to wait for transaction confirmed: %v", err)
	}
	assert.Equal(t, domain.TransactionStatusSuccess, txStatus)
}

func TestDepositIfNotEnoughBalanceInEntrypoint(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	signingservertest.Setup(t)

	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).AnyTimes().Return(&domain.OrganizationWallets{
		EvmAddress: "******************************************",
	}, nil)
	Init(mockRepo)

	t.Run("Threshold is less than entrypoint balance", func(t *testing.T) {
		t.Parallel()
		threshold, err := decimal.NewFromString("1") // 10^(-18) ETH
		if err != nil {
			t.Fatalf("failed to create decimal: %v", err)
		}
		amount, err := decimal.NewFromString("1") // 10^(-18) ETH
		if err != nil {
			t.Fatalf("failed to create decimal: %v", err)
		}
		txHash, err := DepositIfNotEnoughBalanceInEntrypoint(ctx, domain.Holesky, threshold.BigInt(), amount.BigInt())
		if err != nil {
			t.Fatalf("failed to deposit: %v", err)
		}
		assert.Empty(t, txHash)
	})

	t.Run("Threshold is greater than entrypoint balance", func(t *testing.T) {
		t.Parallel()
		threshold, err := decimal.NewFromString("100000000000000000000") // 100 ETH
		if err != nil {
			t.Fatalf("failed to create decimal: %v", err)
		}
		amount, err := decimal.NewFromString("1") // 10^(-18) ETH
		if err != nil {
			t.Fatalf("failed to create decimal: %v", err)
		}
		txHash, err := DepositIfNotEnoughBalanceInEntrypoint(ctx, domain.Holesky, threshold.BigInt(), amount.BigInt())
		if err != nil {
			t.Fatalf("failed to deposit: %v", err)
		}
		assert.NotEmpty(t, txHash)
		t.Logf("txHash: %s", txHash)
		client := evm.GetClient(domain.Holesky)
		txStatus, err := client.WaitUntilTransactionConfirmed(ctx, txHash)
		t.Logf("txStatus: %s", txStatus)
		if err != nil {
			t.Fatalf("failed to wait for transaction confirmed: %v", err)
		}
		assert.Equal(t, domain.TransactionStatusSuccess, txStatus)
	})
}
