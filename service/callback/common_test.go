package callback

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func setupCallbackServiceTest(t *testing.T) (*gomock.Controller, *domain.MockCallbackLogRepo) {
	ctrl := gomock.NewController(t)
	mockRepo := domain.NewMockCallbackLogRepo(ctrl)

	// Initialize the service with the mock repo
	Init(mockRepo)

	return ctrl, mockRepo
}

func TestGetCallbackLogs(t *testing.T) {
	ctrl, mockRepo := setupCallbackServiceTest(t)
	defer ctrl.Finish()

	ctx := context.Background()
	now := time.Now().UTC()

	t.Run("GetCallbackLogs_Success", func(t *testing.T) {
		filter := domain.CallbackLogFilter{
			PaymentIntentID: util.Ptr("payment-intent-1"),
			Type:            []domain.CallbackType{domain.CallbackTypePayment},
			Status:          []domain.CallbackStatus{domain.CallbackStatusSuccess},
			OrgID:           util.Ptr(1),
			ClientID:        util.Ptr("test-client"),
			Page:            1,
			PageSize:        10,
		}

		expectedLogs := []*domain.CallbackLog{
			{
				ID:              "log-1",
				PaymentIntentID: util.Ptr("payment-intent-1"),
				URL:             "https://example.com/webhook",
				Type:            domain.CallbackTypePayment,
				Status:          domain.CallbackStatusSuccess,
				StatusCode:      util.Ptr(200),
				CallbackPayload: `{"event":"payment_completed"}`,
				Duration:        250 * time.Millisecond,
				OrgID:           util.Ptr(1),
				ClientID:        util.Ptr("test-client"),
				CreatedAt:       now,
			},
		}

		mockRepo.EXPECT().
			GetCallbackLogs(ctx, filter).
			Return(expectedLogs, 1, nil)

		logs, total, err := GetCallbackLogs(ctx, filter)
		require.NoError(t, err)
		assert.Equal(t, expectedLogs, logs)
		assert.Equal(t, 1, total)

		// Verify new columns in the returned logs
		assert.Len(t, logs, 1)
		log := logs[0]
		assert.NotNil(t, log.OrgID)
		assert.Equal(t, 1, *log.OrgID)
		assert.NotNil(t, log.ClientID)
		assert.Equal(t, "test-client", *log.ClientID)
	})

	t.Run("GetCallbackLogs_WithOrgFilter", func(t *testing.T) {
		filter := domain.CallbackLogFilter{
			OrgID:    util.Ptr(2),
			Page:     1,
			PageSize: 10,
		}

		expectedLogs := []*domain.CallbackLog{
			{
				ID:       "log-2",
				URL:      "https://example2.com/webhook",
				Type:     domain.CallbackTypeTest,
				Status:   domain.CallbackStatusSuccess,
				OrgID:    util.Ptr(2),
				ClientID: util.Ptr("client-2"),
			},
		}

		mockRepo.EXPECT().
			GetCallbackLogs(ctx, filter).
			Return(expectedLogs, 1, nil)

		logs, total, err := GetCallbackLogs(ctx, filter)
		require.NoError(t, err)
		assert.Equal(t, expectedLogs, logs)
		assert.Equal(t, 1, total)

		// Verify org filtering worked
		assert.Len(t, logs, 1)
		log := logs[0]
		assert.NotNil(t, log.OrgID)
		assert.Equal(t, 2, *log.OrgID)
		assert.NotNil(t, log.ClientID)
		assert.Equal(t, "client-2", *log.ClientID)
	})

	t.Run("GetCallbackLogs_Error", func(t *testing.T) {
		filter := domain.CallbackLogFilter{
			Page:     1,
			PageSize: 10,
		}

		mockRepo.EXPECT().
			GetCallbackLogs(ctx, filter).
			Return(nil, 0, errors.New("database error"))

		logs, total, err := GetCallbackLogs(ctx, filter)
		require.Error(t, err)
		assert.Nil(t, logs)
		assert.Equal(t, 0, total)
		assert.Contains(t, err.Error(), "database error")
	})

	t.Run("GetCallbackLogs_NoRepoSet", func(t *testing.T) {
		// Test when repo is not initialized
		executorInstance.callbackLogRepo = nil

		filter := domain.CallbackLogFilter{
			Page:     1,
			PageSize: 10,
		}

		logs, total, err := GetCallbackLogs(ctx, filter)
		require.Error(t, err)
		assert.Nil(t, logs)
		assert.Equal(t, 0, total)
		assert.Contains(t, err.Error(), "callback log repository not set")
	})
}

func TestGetCallbackLogByID(t *testing.T) {
	ctrl, mockRepo := setupCallbackServiceTest(t)
	defer ctrl.Finish()

	ctx := context.Background()
	now := time.Now().UTC()

	t.Run("GetCallbackLogByID_Success", func(t *testing.T) {
		logID := "test-log-id"
		expectedLog := &domain.CallbackLog{
			ID:              logID,
			PaymentIntentID: util.Ptr("payment-intent-1"),
			URL:             "https://example.com/webhook",
			Type:            domain.CallbackTypePayment,
			Status:          domain.CallbackStatusSuccess,
			StatusCode:      util.Ptr(200),
			CallbackPayload: `{"event":"payment_completed"}`,
			Duration:        250 * time.Millisecond,
			OrgID:           util.Ptr(1),
			ClientID:        util.Ptr("test-client"),
			CreatedAt:       now,
		}

		mockRepo.EXPECT().
			GetCallbackLogByID(ctx, logID).
			Return(expectedLog, nil)

		log, err := GetCallbackLogByID(ctx, logID)
		require.NoError(t, err)
		assert.Equal(t, expectedLog, log)

		// Verify new columns in the returned log
		assert.NotNil(t, log.OrgID)
		assert.Equal(t, 1, *log.OrgID)
		assert.NotNil(t, log.ClientID)
		assert.Equal(t, "test-client", *log.ClientID)
	})

	t.Run("GetCallbackLogByID_NotFound", func(t *testing.T) {
		logID := "non-existent-id"

		mockRepo.EXPECT().
			GetCallbackLogByID(ctx, logID).
			Return(nil, domain.ErrRecordNotFound)

		log, err := GetCallbackLogByID(ctx, logID)
		require.Error(t, err)
		assert.Nil(t, log)
		assert.Equal(t, domain.ErrRecordNotFound, err)
	})

	t.Run("GetCallbackLogByID_Error", func(t *testing.T) {
		logID := "test-log-id"

		mockRepo.EXPECT().
			GetCallbackLogByID(ctx, logID).
			Return(nil, errors.New("database error"))

		log, err := GetCallbackLogByID(ctx, logID)
		require.Error(t, err)
		assert.Nil(t, log)
		assert.Contains(t, err.Error(), "database error")
	})

	t.Run("GetCallbackLogByID_NoRepoSet", func(t *testing.T) {
		// Test when repo is not initialized
		executorInstance.callbackLogRepo = nil

		logID := "test-log-id"

		log, err := GetCallbackLogByID(ctx, logID)
		require.Error(t, err)
		assert.Nil(t, log)
		assert.Contains(t, err.Error(), "callback log repository not set")
	})
}

func TestLogCallback(t *testing.T) {
	ctrl, mockRepo := setupCallbackServiceTest(t)
	defer ctrl.Finish()

	ctx := context.Background()
	now := time.Now().UTC()

	t.Run("LogCallback_Success", func(t *testing.T) {
		inputLog := &domain.CallbackLog{
			PaymentIntentID: util.Ptr("payment-intent-1"),
			URL:             "https://example.com/webhook",
			Type:            domain.CallbackTypePayment,
			Status:          domain.CallbackStatusSuccess,
			StatusCode:      util.Ptr(200),
			CallbackPayload: `{"event":"payment_completed"}`,
			Duration:        250 * time.Millisecond,
			OrgID:           util.Ptr(1),
			ClientID:        util.Ptr("test-client"),
			CreatedAt:       now,
		}

		expectedLog := &domain.CallbackLog{
			ID:              "generated-id",
			PaymentIntentID: inputLog.PaymentIntentID,
			URL:             inputLog.URL,
			Type:            inputLog.Type,
			Status:          inputLog.Status,
			StatusCode:      inputLog.StatusCode,
			CallbackPayload: inputLog.CallbackPayload,
			Duration:        inputLog.Duration,
			OrgID:           inputLog.OrgID,
			ClientID:        inputLog.ClientID,
			CreatedAt:       inputLog.CreatedAt,
		}

		mockRepo.EXPECT().
			CreateCallbackLog(ctx, inputLog).
			Return(expectedLog, nil)

		result, err := LogCallback(ctx, inputLog)
		require.NoError(t, err)
		assert.Equal(t, expectedLog, result)

		// Verify new columns are preserved
		assert.NotNil(t, result.OrgID)
		assert.Equal(t, 1, *result.OrgID)
		assert.NotNil(t, result.ClientID)
		assert.Equal(t, "test-client", *result.ClientID)
	})

	t.Run("LogCallback_Error", func(t *testing.T) {
		inputLog := &domain.CallbackLog{
			URL:             "https://example.com/webhook",
			Type:            domain.CallbackTypePayment,
			Status:          domain.CallbackStatusSuccess,
			StatusCode:      util.Ptr(200),
			CallbackPayload: `{"event":"test"}`,
			Duration:        100 * time.Millisecond,
			OrgID:           util.Ptr(2),
			ClientID:        util.Ptr("test-client-2"),
			CreatedAt:       now,
		}

		mockRepo.EXPECT().
			CreateCallbackLog(ctx, inputLog).
			Return(nil, errors.New("database error"))

		result, err := LogCallback(ctx, inputLog)
		require.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "database error")
	})

	t.Run("LogCallback_NoRepoSet", func(t *testing.T) {
		// Test when repo is not initialized
		executorInstance.callbackLogRepo = nil

		inputLog := &domain.CallbackLog{
			URL:             "https://example.com/webhook",
			Type:            domain.CallbackTypePayment,
			Status:          domain.CallbackStatusSuccess,
			StatusCode:      util.Ptr(200),
			CallbackPayload: `{"event":"test"}`,
			Duration:        100 * time.Millisecond,
			OrgID:           util.Ptr(3),
			ClientID:        util.Ptr("test-client-3"),
			CreatedAt:       now,
		}

		result, err := LogCallback(ctx, inputLog)
		// Should not error when repo is not set, just log warning and return original log
		require.NoError(t, err)
		assert.Equal(t, inputLog, result)
	})
}

func TestSendTestCallback(t *testing.T) {
	ctrl, mockRepo := setupCallbackServiceTest(t)
	defer ctrl.Finish()

	ctx := context.Background()

	t.Run("SendTestCallback_Success", func(t *testing.T) {
		request := &domain.TestCallbackRequest{
			URL: "https://example.com/webhook",
			Payload: map[string]any{
				"test": "payload",
			},
			ClientID:    "test-client",
			SignPayload: false,
		}

		execCtx := domain.CallbackExecutionContext{
			ClientID: "test-client",
			OrgID:    1,
		}

		expectedLog := &domain.CallbackLog{
			ID:              "generated-id",
			URL:             request.URL,
			Type:            domain.CallbackTypeTest,
			Status:          domain.CallbackStatusSuccess,
			StatusCode:      util.Ptr(200),
			CallbackPayload: `{"test":"payload"}`,
			Duration:        100 * time.Millisecond,
			OrgID:           util.Ptr(1),
			ClientID:        util.Ptr("test-client"),
			CreatedAt:       time.Now(),
		}

		// Mock the executor's SendTestCallback method
		mockRepo.EXPECT().
			CreateCallbackLog(ctx, gomock.Any()).
			Return(expectedLog, nil)

		result, err := SendTestCallback(ctx, request, execCtx)
		require.NoError(t, err)
		assert.Equal(t, expectedLog, result)

		// Verify new columns in result
		assert.NotNil(t, result.OrgID)
		assert.Equal(t, 1, *result.OrgID)
		assert.NotNil(t, result.ClientID)
		assert.Equal(t, "test-client", *result.ClientID)
	})

	t.Run("SendTestCallback_WithDifferentOrgClient", func(t *testing.T) {
		request := &domain.TestCallbackRequest{
			URL: "https://example.com/webhook",
			Payload: map[string]any{
				"test": "payload",
			},
			ClientID:    "other-client",
			SignPayload: false,
		}

		execCtx := domain.CallbackExecutionContext{
			ClientID: "other-client",
			OrgID:    2,
		}

		expectedLog := &domain.CallbackLog{
			ID:              "generated-id-2",
			URL:             request.URL,
			Type:            domain.CallbackTypeTest,
			Status:          domain.CallbackStatusSuccess,
			StatusCode:      util.Ptr(200),
			CallbackPayload: `{"test":"payload"}`,
			Duration:        150 * time.Millisecond,
			OrgID:           util.Ptr(2),
			ClientID:        util.Ptr("other-client"),
			CreatedAt:       time.Now(),
		}

		// Mock the executor's SendTestCallback method
		mockRepo.EXPECT().
			CreateCallbackLog(ctx, gomock.Any()).
			Return(expectedLog, nil)

		result, err := SendTestCallback(ctx, request, execCtx)
		require.NoError(t, err)
		assert.Equal(t, expectedLog, result)

		// Verify different org and client values
		assert.NotNil(t, result.OrgID)
		assert.Equal(t, 2, *result.OrgID)
		assert.NotNil(t, result.ClientID)
		assert.Equal(t, "other-client", *result.ClientID)
	})

	t.Run("SendTestCallback_NoRepoSet", func(t *testing.T) {
		// Test when repo is not initialized
		executorInstance.callbackLogRepo = nil

		request := &domain.TestCallbackRequest{
			URL: "https://example.com/webhook",
			Payload: map[string]any{
				"test": "payload",
			},
			ClientID:    "test-client",
			SignPayload: false,
		}

		execCtx := domain.CallbackExecutionContext{
			ClientID: "test-client",
			OrgID:    1,
		}

		result, err := SendTestCallback(ctx, request, execCtx)
		require.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "callback log repository not set")
	})
}
