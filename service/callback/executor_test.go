package callback

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func TestCallbackExecutor_SendTestCallback(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockCallbackLogRepo(ctrl)
	executor := &callbackExecutor{
		callbackLogRepo: mockRepo,
	}

	ctx := context.Background()

	t.Run("SendTestCallback_Success", func(t *testing.T) {
		// Create a test server that returns success
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			assert.Equal(t, "POST", r.Method)
			assert.Equal(t, "application/json", r.Header.Get("Content-Type"))
			assert.Equal(t, "KG-Wallet-Test-Callback/1.0", r.<PERSON><PERSON>.Get("User-Agent"))

			// Verify request body
			var payload map[string]interface{}
			err := json.NewDecoder(r.Body).Decode(&payload)
			require.NoError(t, err)
			assert.Equal(t, true, payload["test"]) // test field is always set to true for test callbacks

			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"success":true}`))
		}))
		defer server.Close()

		request := &domain.TestCallbackRequest{
			URL: server.URL,
			Payload: map[string]any{
				"test": "test-value",
			},
			ClientID:    "test-client",
			SignPayload: false,
		}

		execCtx := domain.CallbackExecutionContext{
			ClientID: "test-client",
			OrgID:    1,
		}

		// Mock the repository call
		mockRepo.EXPECT().
			CreateCallbackLog(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, log *domain.CallbackLog) (*domain.CallbackLog, error) {
				// Verify the log structure
				assert.Equal(t, server.URL, log.URL)
				assert.Equal(t, domain.CallbackTypeTest, log.Type)
				assert.Equal(t, domain.CallbackStatusSuccess, log.Status)
				assert.NotNil(t, log.StatusCode)
				assert.Equal(t, 200, *log.StatusCode)
				assert.Contains(t, log.CallbackPayload, "\"test\":true") // test field is always true for test callbacks
				assert.Greater(t, log.Duration, time.Duration(0))

				// Verify new columns
				assert.NotNil(t, log.OrgID)
				assert.Equal(t, 1, *log.OrgID)
				assert.NotNil(t, log.ClientID)
				assert.Equal(t, "test-client", *log.ClientID)

				// Return the log with an ID
				log.ID = "generated-id"
				return log, nil
			})

		result, err := executor.SendTestCallback(ctx, request, execCtx)
		require.NoError(t, err)
		assert.Equal(t, "generated-id", result.ID)
		assert.Equal(t, domain.CallbackStatusSuccess, result.Status)
		// Verify new columns in result
		assert.NotNil(t, result.OrgID)
		assert.Equal(t, 1, *result.OrgID)
		assert.NotNil(t, result.ClientID)
		assert.Equal(t, "test-client", *result.ClientID)
	})

	t.Run("SendTestCallback_HTTPError", func(t *testing.T) {
		// Create a test server that returns an error
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
			w.Write([]byte(`{"error":"internal server error"}`))
		}))
		defer server.Close()

		request := &domain.TestCallbackRequest{
			URL: server.URL,
			Payload: map[string]any{
				"test": "test-value",
			},
			ClientID:    "test-client",
			SignPayload: false,
		}

		execCtx := domain.CallbackExecutionContext{
			ClientID: "test-client",
			OrgID:    2,
		}

		// Mock the repository call
		mockRepo.EXPECT().
			CreateCallbackLog(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, log *domain.CallbackLog) (*domain.CallbackLog, error) {
				// Verify the log structure for failed callback
				assert.Equal(t, server.URL, log.URL)
				assert.Equal(t, domain.CallbackTypeTest, log.Type)
				assert.Equal(t, domain.CallbackStatusFailed, log.Status)
				assert.NotNil(t, log.StatusCode)
				assert.Equal(t, 500, *log.StatusCode)
				assert.NotNil(t, log.Error)
				assert.Contains(t, *log.Error, "HTTP 500")
				assert.Greater(t, log.Duration, time.Duration(0))

				// Verify new columns
				assert.NotNil(t, log.OrgID)
				assert.Equal(t, 2, *log.OrgID)
				assert.NotNil(t, log.ClientID)
				assert.Equal(t, "test-client", *log.ClientID)

				// Return the log with an ID
				log.ID = "generated-id"
				return log, nil
			})

		result, err := executor.SendTestCallback(ctx, request, execCtx)
		require.NoError(t, err)
		assert.Equal(t, "generated-id", result.ID)
		assert.Equal(t, domain.CallbackStatusFailed, result.Status)
		// Verify new columns in result
		assert.NotNil(t, result.OrgID)
		assert.Equal(t, 2, *result.OrgID)
		assert.NotNil(t, result.ClientID)
		assert.Equal(t, "test-client", *result.ClientID)
	})

	t.Run("SendTestCallback_InvalidURL", func(t *testing.T) {
		request := &domain.TestCallbackRequest{
			URL: "invalid-url",
			Payload: map[string]any{
				"test": "test-value",
			},
			ClientID:    "test-client",
			SignPayload: false,
		}

		execCtx := domain.CallbackExecutionContext{
			ClientID: "test-client",
			OrgID:    3,
		}

		// Mock the repository call
		mockRepo.EXPECT().
			CreateCallbackLog(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, log *domain.CallbackLog) (*domain.CallbackLog, error) {
				// Verify the log structure for failed callback
				assert.Equal(t, "invalid-url", log.URL)
				assert.Equal(t, domain.CallbackTypeTest, log.Type)
				assert.Equal(t, domain.CallbackStatusFailed, log.Status)
				assert.NotNil(t, log.Error)
				assert.Contains(t, *log.Error, "Request failed")
				assert.Greater(t, log.Duration, time.Duration(0))

				// Verify new columns
				assert.NotNil(t, log.OrgID)
				assert.Equal(t, 3, *log.OrgID)
				assert.NotNil(t, log.ClientID)
				assert.Equal(t, "test-client", *log.ClientID)

				// Return the log with an ID
				log.ID = "generated-id"
				return log, nil
			})

		result, err := executor.SendTestCallback(ctx, request, execCtx)
		require.NoError(t, err)
		assert.Equal(t, "generated-id", result.ID)
		assert.Equal(t, domain.CallbackStatusFailed, result.Status)
		// Verify new columns in result
		assert.NotNil(t, result.OrgID)
		assert.Equal(t, 3, *result.OrgID)
		assert.NotNil(t, result.ClientID)
		assert.Equal(t, "test-client", *result.ClientID)
	})

	t.Run("SendTestCallback_DefaultPayload", func(t *testing.T) {
		// Create a test server that returns success
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify default payload is used when none provided
			var payload map[string]interface{}
			err := json.NewDecoder(r.Body).Decode(&payload)
			require.NoError(t, err)
			assert.Equal(t, "completed", payload["status"])
			assert.Equal(t, "100.00", payload["amount"])
			assert.Equal(t, "USD", payload["currency"])
			assert.Equal(t, true, payload["test"])
			assert.NotNil(t, payload["timestamp"])

			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"success":true}`))
		}))
		defer server.Close()

		request := &domain.TestCallbackRequest{
			URL:         server.URL,
			Payload:     nil, // No payload provided
			ClientID:    "test-client-default",
			SignPayload: false,
		}

		execCtx := domain.CallbackExecutionContext{
			ClientID: "test-client-default",
			OrgID:    4,
		}

		// Mock the repository call
		mockRepo.EXPECT().
			CreateCallbackLog(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, log *domain.CallbackLog) (*domain.CallbackLog, error) {
				// Verify default payload was used
				assert.Contains(t, log.CallbackPayload, "completed")
				assert.Contains(t, log.CallbackPayload, "100.00")
				assert.Contains(t, log.CallbackPayload, "USD")

				// Verify new columns
				assert.NotNil(t, log.OrgID)
				assert.Equal(t, 4, *log.OrgID)
				assert.NotNil(t, log.ClientID)
				assert.Equal(t, "test-client-default", *log.ClientID)

				log.ID = "generated-id"
				return log, nil
			})

		result, err := executor.SendTestCallback(ctx, request, execCtx)
		require.NoError(t, err)
		assert.Equal(t, "generated-id", result.ID)
		// Verify new columns in result
		assert.NotNil(t, result.OrgID)
		assert.Equal(t, 4, *result.OrgID)
		assert.NotNil(t, result.ClientID)
		assert.Equal(t, "test-client-default", *result.ClientID)
	})

	t.Run("SendTestCallback_RepositoryError", func(t *testing.T) {
		// Create a test server that returns success
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"success":true}`))
		}))
		defer server.Close()

		request := &domain.TestCallbackRequest{
			URL: server.URL,
			Payload: map[string]any{
				"test": "test-value",
			},
			ClientID:    "test-client",
			SignPayload: false,
		}

		execCtx := domain.CallbackExecutionContext{
			ClientID: "test-client",
			OrgID:    5,
		}

		// Mock the repository call to return an error
		mockRepo.EXPECT().
			CreateCallbackLog(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, log *domain.CallbackLog) (*domain.CallbackLog, error) {
				// Verify new columns are set even when repo fails
				assert.NotNil(t, log.OrgID)
				assert.Equal(t, 5, *log.OrgID)
				assert.NotNil(t, log.ClientID)
				assert.Equal(t, "test-client", *log.ClientID)

				return nil, assert.AnError
			})

		result, err := executor.SendTestCallback(ctx, request, execCtx)
		require.Error(t, err)
		assert.Nil(t, result)
		assert.Equal(t, assert.AnError, err)
	})

	t.Run("SendTestCallback_EmptyPayload", func(t *testing.T) {
		// Create a test server that returns success
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify default payload is used when empty payload provided
			var payload map[string]interface{}
			err := json.NewDecoder(r.Body).Decode(&payload)
			require.NoError(t, err)
			assert.Equal(t, "completed", payload["status"])

			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"success":true}`))
		}))
		defer server.Close()

		request := &domain.TestCallbackRequest{
			URL:         server.URL,
			Payload:     map[string]any{}, // Empty payload
			ClientID:    "test-client-empty",
			SignPayload: false,
		}

		execCtx := domain.CallbackExecutionContext{
			ClientID: "test-client-empty",
			OrgID:    6,
		}

		// Mock the repository call
		mockRepo.EXPECT().
			CreateCallbackLog(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, log *domain.CallbackLog) (*domain.CallbackLog, error) {
				// Verify new columns
				assert.NotNil(t, log.OrgID)
				assert.Equal(t, 6, *log.OrgID)
				assert.NotNil(t, log.ClientID)
				assert.Equal(t, "test-client-empty", *log.ClientID)

				log.ID = "generated-id"
				return log, nil
			})

		result, err := executor.SendTestCallback(ctx, request, execCtx)
		require.NoError(t, err)
		assert.Equal(t, "generated-id", result.ID)
		// Verify new columns in result
		assert.NotNil(t, result.OrgID)
		assert.Equal(t, 6, *result.OrgID)
		assert.NotNil(t, result.ClientID)
		assert.Equal(t, "test-client-empty", *result.ClientID)
	})

	t.Run("SendTestCallback_NullOrgID", func(t *testing.T) {
		// Test with null org_id (should be allowed)
		server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"success":true}`))
		}))
		defer server.Close()

		request := &domain.TestCallbackRequest{
			URL: server.URL,
			Payload: map[string]any{
				"test": "test-value",
			},
			ClientID:    "test-client-null-org",
			SignPayload: false,
		}

		execCtx := domain.CallbackExecutionContext{
			ClientID: "test-client-null-org",
			OrgID:    0, // Zero org_id to test edge case
		}

		// Mock the repository call
		mockRepo.EXPECT().
			CreateCallbackLog(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, log *domain.CallbackLog) (*domain.CallbackLog, error) {
				// Verify org_id is zero but client_id is set
				assert.NotNil(t, log.OrgID)
				assert.Equal(t, 0, *log.OrgID)
				assert.NotNil(t, log.ClientID)
				assert.Equal(t, "test-client-null-org", *log.ClientID)

				log.ID = "generated-id"
				return log, nil
			})

		result, err := executor.SendTestCallback(ctx, request, execCtx)
		require.NoError(t, err)
		assert.Equal(t, "generated-id", result.ID)
		// Verify new columns in result
		assert.NotNil(t, result.OrgID)
		assert.Equal(t, 0, *result.OrgID)
		assert.NotNil(t, result.ClientID)
		assert.Equal(t, "test-client-null-org", *result.ClientID)
	})
}
