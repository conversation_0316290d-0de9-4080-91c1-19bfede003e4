package tokensignal

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func TestUpsertBuySignal(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	repo = mockRepo

	ctx := context.Background()

	// Test successful upsert
	t.Run("Success", func(t *testing.T) {
		// Setup websocket mock
		mockWS := websocket.NewMockIService(ctrl)
		websocket.Set(mockWS)

		params := &UpsertBuySignalParams{
			TokenAddress:     "token123",
			SmartWalletCount: 10,
			BuyEntryPrice:    0.001,
			HighestPrice:     0.002,
			EmitTime:         time.Now(),
			TelegramLink:     "https://t.me/example",
			WinRate:          0.75,
			AverageHolding:   48.5,
			AverageWinRate:   0.65,
		}

		// Mock GetBuySignalByTokenAddress to return not found (so it's a new signal)
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, params.TokenAddress).Return(nil, domain.ErrRecordNotFound)

		mockRepo.EXPECT().UpsertBuySignal(ctx, gomock.Any()).DoAndReturn(
			func(_ context.Context, signal *domain.TokenBuySignal) error {
				assert.Equal(t, params.TokenAddress, signal.TokenAddress)
				assert.Equal(t, params.SmartWalletCount, signal.SmartWalletCount)
				assert.Equal(t, params.BuyEntryPrice, signal.BuyEntryPrice)
				assert.Equal(t, params.HighestPrice, signal.HighestPrice)
				assert.Equal(t, params.TelegramLink, signal.TelegramLink)
				assert.Equal(t, params.WinRate, signal.WinRate)
				assert.Equal(t, params.AverageHolding, signal.AverageHolding)
				assert.Equal(t, params.AverageWinRate, signal.AverageWinRate)
				return nil
			})

		mockRepo.EXPECT().DeleteSellSignalByTokenAddress(ctx, params.TokenAddress).Return(nil)

		// Mock websocket notification
		mockWS.EXPECT().SendEventByUIDs(
			gomock.Any(),
			gomock.Eq([]string{}),
			gomock.Eq(websocket.EventTokenBuySignal),
			gomock.Any(),
		).Return(nil)

		err := UpsertBuySignal(ctx, params)
		assert.Nil(t, err)
	})

	// Test upsert failure
	t.Run("UpsertFailure", func(t *testing.T) {
		// Setup websocket mock
		mockWS := websocket.NewMockIService(ctrl)
		websocket.Set(mockWS)

		params := &UpsertBuySignalParams{
			TokenAddress:     "token456",
			SmartWalletCount: 15,
			BuyEntryPrice:    0.002,
			HighestPrice:     0.003,
			EmitTime:         time.Now(),
			TelegramLink:     "https://t.me/example2",
			WinRate:          0.85,
			AverageHolding:   72.3,
			AverageWinRate:   0.78,
		}

		// Mock GetBuySignalByTokenAddress to return not found
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, params.TokenAddress).Return(nil, domain.ErrRecordNotFound)

		dbError := errors.New("database error")
		mockRepo.EXPECT().UpsertBuySignal(ctx, gomock.Any()).Return(dbError)

		err := UpsertBuySignal(ctx, params)
		assert.NotNil(t, err)
		assert.Equal(t, dbError, err.Error)
	})

	// Test delete sell signal failure (should not affect overall result)
	t.Run("DeleteSellSignalFailure", func(t *testing.T) {
		// Setup websocket mock
		mockWS := websocket.NewMockIService(ctrl)
		websocket.Set(mockWS)

		params := &UpsertBuySignalParams{
			TokenAddress:     "token789",
			SmartWalletCount: 20,
			BuyEntryPrice:    0.003,
			HighestPrice:     0.004,
			EmitTime:         time.Now(),
			TelegramLink:     "https://t.me/example3",
			WinRate:          0.95,
			AverageHolding:   36.7,
			AverageWinRate:   0.55,
		}

		// Mock GetBuySignalByTokenAddress to return not found
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, params.TokenAddress).Return(nil, domain.ErrRecordNotFound)

		mockRepo.EXPECT().UpsertBuySignal(ctx, gomock.Any()).Return(nil)
		mockRepo.EXPECT().DeleteSellSignalByTokenAddress(ctx, params.TokenAddress).Return(errors.New("delete error"))

		// Mock websocket notification
		mockWS.EXPECT().SendEventByUIDs(
			gomock.Any(),
			gomock.Eq([]string{}),
			gomock.Eq(websocket.EventTokenBuySignal),
			gomock.Any(),
		).Return(nil)

		err := UpsertBuySignal(ctx, params)
		assert.Nil(t, err) // Should still succeed even if delete fails
	})
}

func TestAddSellSignal(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	repo = mockRepo

	// Setup websocket mock
	mockWS := websocket.NewMockIService(ctrl)
	websocket.Set(mockWS)

	ctx := context.Background()

	// Test successful add
	t.Run("Success", func(t *testing.T) {
		params := &AddSellSignalParams{
			TokenAddress: "token123",
			TelegramLink: "https://t.me/example",
		}

		// Mock the buy signal that will be used for BuyEntryTime
		buySignalEmitTime := time.Now().Add(-24 * time.Hour) // 1 day ago
		mockBuySignal := &domain.TokenBuySignal{
			TokenAddress: "token123",
			EmitTime:     buySignalEmitTime,
		}

		// First expect a call to get the buy signal
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, params.TokenAddress).Return(mockBuySignal, nil)

		mockRepo.EXPECT().AddSellSignal(ctx, gomock.Any()).DoAndReturn(
			func(_ context.Context, signal *domain.TokenSellSignal) error {
				assert.Equal(t, params.TokenAddress, signal.TokenAddress)
				assert.Equal(t, params.TelegramLink, signal.TelegramLink)
				// Verify BuyEntryTime was set correctly from the buy signal
				assert.NotNil(t, signal.BuyEntryTime)
				assert.Equal(t, buySignalEmitTime, *signal.BuyEntryTime)
				return nil
			})

		mockRepo.EXPECT().DeleteBuySignalByTokenAddress(ctx, params.TokenAddress).Return(nil)

		// Mock websocket notification
		mockWS.EXPECT().SendEventByUIDs(
			gomock.Any(),
			gomock.Eq([]string{}),
			gomock.Eq(websocket.EventTokenSellSignal),
			gomock.Any(),
		).Return(nil)

		err := AddSellSignal(ctx, params)
		assert.Nil(t, err)
	})

	// Test buy signal not found
	t.Run("BuySignalNotFound", func(t *testing.T) {
		params := &AddSellSignalParams{
			TokenAddress: "nonexistent-token",
			TelegramLink: "https://t.me/example-nonexistent",
		}

		// Mock GetBuySignalByTokenAddress to return not found
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, params.TokenAddress).Return(nil, domain.ErrRecordNotFound)

		mockRepo.EXPECT().AddSellSignal(ctx, gomock.Any()).DoAndReturn(
			func(_ context.Context, signal *domain.TokenSellSignal) error {
				assert.Equal(t, params.TokenAddress, signal.TokenAddress)
				assert.Equal(t, params.TelegramLink, signal.TelegramLink)
				assert.Nil(t, signal.BuyEntryTime)
				return nil
			})

		err := AddSellSignal(ctx, params)
		assert.Nil(t, err)
		// assert.Equal(t, code.RecordNotFound, err.Code)
	})

	// Test add failure
	t.Run("AddFailure", func(t *testing.T) {
		params := &AddSellSignalParams{
			TokenAddress: "token456",
			TelegramLink: "https://t.me/example2",
		}

		// Mock the buy signal
		mockBuySignal := &domain.TokenBuySignal{
			TokenAddress: "token456",
			EmitTime:     time.Now(),
		}

		// First expect a call to get the buy signal
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, params.TokenAddress).Return(mockBuySignal, nil)

		dbError := errors.New("database error")
		mockRepo.EXPECT().AddSellSignal(ctx, gomock.Any()).Return(dbError)

		err := AddSellSignal(ctx, params)
		assert.NotNil(t, err)
		assert.Equal(t, dbError, err.Error)
	})

	// Test delete buy signal failure (should not affect overall result)
	t.Run("DeleteBuySignalFailure", func(t *testing.T) {
		params := &AddSellSignalParams{
			TokenAddress: "token789",
			TelegramLink: "https://t.me/example3",
		}

		// Mock the buy signal
		mockBuySignal := &domain.TokenBuySignal{
			TokenAddress: "token789",
			EmitTime:     time.Now(),
		}

		// First expect a call to get the buy signal
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, params.TokenAddress).Return(mockBuySignal, nil)

		mockRepo.EXPECT().AddSellSignal(ctx, gomock.Any()).Return(nil)
		mockRepo.EXPECT().DeleteBuySignalByTokenAddress(ctx, params.TokenAddress).Return(errors.New("delete error"))

		// Mock websocket notification
		mockWS.EXPECT().SendEventByUIDs(
			gomock.Any(),
			gomock.Eq([]string{}),
			gomock.Eq(websocket.EventTokenSellSignal),
			gomock.Any(),
		).Return(nil)

		err := AddSellSignal(ctx, params)
		assert.Nil(t, err) // Should still succeed even if delete fails
	})
}

func TestListBuySignals(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	repo = mockRepo

	// Setup websocket mock
	mockWS := websocket.NewMockIService(ctrl)
	websocket.Set(mockWS)

	ctx := context.Background()

	// Test successful list
	t.Run("Success", func(t *testing.T) {
		signals := []*domain.TokenBuySignal{
			{
				ID:               1,
				TokenAddress:     "token123",
				SmartWalletCount: 10,
				BuyEntryPrice:    0.001,
				HighestPrice:     0.002,
				EmitTime:         time.Now(),
				TelegramLink:     "https://t.me/example",
				WinRate:          0.75,
				AverageHolding:   48.5,
				AverageWinRate:   0.65,
			},
			{
				ID:               2,
				TokenAddress:     "token456",
				SmartWalletCount: 15,
				BuyEntryPrice:    0.002,
				HighestPrice:     0.003,
				EmitTime:         time.Now().Add(-time.Hour), // Older signal
				TelegramLink:     "https://t.me/example2",
				WinRate:          0.85,
				AverageHolding:   72.3,
				AverageWinRate:   0.78,
			},
		}

		mockRepo.EXPECT().ListBuySignals(ctx).Return(signals, nil)

		result, err := ListBuySignals(ctx)
		assert.Nil(t, err)
		assert.Equal(t, 2, len(result))
		assert.Equal(t, "token123", result[0].TokenAddress)
		assert.Equal(t, "token456", result[1].TokenAddress)
	})

	// Test list failure
	t.Run("Failure", func(t *testing.T) {
		dbError := errors.New("database error")
		mockRepo.EXPECT().ListBuySignals(ctx).Return(nil, dbError)

		result, err := ListBuySignals(ctx)
		assert.NotNil(t, err)
		assert.Nil(t, result)
		assert.Equal(t, dbError, err.Error)
	})
}

func TestListSellSignals(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	repo = mockRepo

	// Setup websocket mock
	mockWS := websocket.NewMockIService(ctrl)
	websocket.Set(mockWS)

	ctx := context.Background()

	// Test successful list
	t.Run("Success", func(t *testing.T) {
		now := time.Now()
		buyEntryTime := now.Add(-48 * time.Hour)

		signals := []*domain.TokenSellSignal{
			{
				ID:           1,
				TokenAddress: "token123",
				EmitTime:     now,
				TelegramLink: "https://t.me/example",
				BuyEntryTime: &buyEntryTime,
			},
			{
				ID:           2,
				TokenAddress: "token456",
				EmitTime:     now.Add(-time.Hour), // Older signal
				TelegramLink: "https://t.me/example2",
				BuyEntryTime: nil, // Test null BuyEntryTime
			},
		}

		mockRepo.EXPECT().ListSellSignals(ctx, 100).Return(signals, nil)

		result, err := ListSellSignals(ctx, 100)
		assert.Nil(t, err)
		assert.Equal(t, 2, len(result))

		// Check first signal with BuyEntryTime
		assert.Equal(t, "token123", result[0].TokenAddress)
		assert.NotNil(t, result[0].BuyEntryTime)
		assert.Equal(t, buyEntryTime, *result[0].BuyEntryTime)

		// Check second signal with nil BuyEntryTime
		assert.Equal(t, "token456", result[1].TokenAddress)
		assert.Nil(t, result[1].BuyEntryTime)
	})

	// Test list failure
	t.Run("Failure", func(t *testing.T) {
		dbError := errors.New("database error")
		mockRepo.EXPECT().ListSellSignals(ctx, 100).Return(nil, dbError)

		result, err := ListSellSignals(ctx, 100)
		assert.NotNil(t, err)
		assert.Nil(t, result)
		assert.Equal(t, dbError, err.Error)
	})
}

func TestGetSignalStats(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	repo = mockRepo

	tests := []struct {
		name           string
		pastTopSignals []*domain.TokenSellSignal
		pastTopErr     error
		totalCount     int
		twoXCount      int
		avgGain        float64
		statsErr       error
		topSellSignal  *domain.TokenSellSignal
		topBuySignal   *domain.TokenBuySignal
		topErr         error
		wantErr        bool
		wantStats      *SignalStats
	}{
		{
			name: "normal case",
			pastTopSignals: []*domain.TokenSellSignal{
				{
					TokenAddress: "0x123",
					HighestGain:  2.5,
				},
				{
					TokenAddress: "0x456",
					HighestGain:  3.0,
				},
			},
			totalCount: 100,
			twoXCount:  50,
			avgGain:    2.5,
			topSellSignal: &domain.TokenSellSignal{
				TokenAddress: "0x789",
				HighestGain:  4.0,
			},
			wantErr: false,
			wantStats: &SignalStats{
				PastTopSignals: []PastTopSignal{
					{
						TokenAddress: "0x123",
						HighestGain:  2.5,
					},
					{
						TokenAddress: "0x456",
						HighestGain:  3.0,
					},
				},
				Last7d2xRatio:   0.5,
				Last7d2xAvgGain: 2.5,
				TodayTopSignal: &TodayTopSignal{
					TokenAddress: "0x789",
					HighestGain:  4.0,
				},
			},
		},
		{
			name:           "error getting past top signals",
			pastTopSignals: nil,
			pastTopErr:     errors.New("database error"),
			wantErr:        true,
		},
		{
			name:           "error getting last 7d stats",
			pastTopSignals: []*domain.TokenSellSignal{},
			statsErr:       errors.New("database error"),
			wantErr:        true,
		},
		{
			name:           "error getting today's top signal",
			pastTopSignals: []*domain.TokenSellSignal{},
			topErr:         errors.New("database error"),
			wantErr:        true,
		},
		{
			name:           "empty data",
			pastTopSignals: []*domain.TokenSellSignal{},
			totalCount:     0,
			twoXCount:      0,
			avgGain:        0,
			wantErr:        false,
			wantStats: &SignalStats{
				PastTopSignals:  []PastTopSignal{},
				Last7d2xRatio:   0,
				Last7d2xAvgGain: 0,
				TodayTopSignal:  nil,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()

			mockRepo.EXPECT().GetPastTopSignals(ctx).Return(tt.pastTopSignals, tt.pastTopErr)
			if tt.pastTopErr == nil {
				mockRepo.EXPECT().GetLast7d2xStats(ctx).Return(tt.totalCount, tt.twoXCount, tt.avgGain, tt.statsErr)
				if tt.statsErr == nil {
					mockRepo.EXPECT().GetTodayTopSignal(ctx).Return(tt.topSellSignal, tt.topBuySignal, tt.topErr)
				}
			}

			stats, kgErr := GetSignalStats(ctx)
			if tt.wantErr {
				require.NotNil(t, kgErr)
				require.Nil(t, stats)
			} else {
				require.Nil(t, kgErr)
				require.Equal(t, tt.wantStats, stats)
			}
		})
	}
}

func TestUpsertBuySignalBatch(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	repo = mockRepo

	ctx := context.Background()

	// Test successful batch upsert with new and existing signals
	t.Run("Success_MixedSignals", func(t *testing.T) {
		// Setup websocket mock
		mockWS := websocket.NewMockIService(ctrl)
		websocket.Set(mockWS)

		// Create batch params with 3 signals (2 new, 1 existing)
		now := time.Now()
		batchParams := &UpsertBuySignalBatchParams{
			Signals: []UpsertBuySignalParams{
				{
					TokenAddress:     "batch1",
					SmartWalletCount: 10,
					BuyEntryPrice:    0.001,
					HighestPrice:     0.002,
					EmitTime:         now,
					TelegramLink:     "https://t.me/batch1",
					WinRate:          0.75,
					AverageHolding:   48.5,
					AverageWinRate:   0.65,
					Symbol:           "BATCH1",
				},
				{
					TokenAddress:     "batch2",
					SmartWalletCount: 15,
					BuyEntryPrice:    0.003,
					HighestPrice:     0.005,
					EmitTime:         now,
					TelegramLink:     "https://t.me/batch2",
					WinRate:          0.80,
					AverageHolding:   52.0,
					AverageWinRate:   0.70,
					Symbol:           "BATCH2",
				},
				{
					TokenAddress:     "batch3", // This one exists already
					SmartWalletCount: 20,
					BuyEntryPrice:    0.005,
					HighestPrice:     0.008,
					EmitTime:         now,
					TelegramLink:     "https://t.me/batch3",
					WinRate:          0.85,
					AverageHolding:   60.0,
					AverageWinRate:   0.75,
					Symbol:           "BATCH3",
				},
			},
		}

		// Mock GetBuySignalByTokenAddress for each signal
		// First two should return not found (new signals)
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, "batch1").Return(nil, domain.ErrRecordNotFound)
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, "batch2").Return(nil, domain.ErrRecordNotFound)
		// Third one should return success (existing signal)
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, "batch3").Return(&domain.TokenBuySignal{
			TokenAddress: "batch3",
			EmitTime:     now.Add(-time.Hour), // It exists from before
		}, nil)

		// Mock the batch upsert
		mockRepo.EXPECT().UpsertBuySignalBatch(ctx, gomock.Any()).DoAndReturn(
			func(_ context.Context, signals []*domain.TokenBuySignal) error {
				assert.Equal(t, 3, len(signals))

				// Check the values of all signals
				for i, signal := range signals {
					assert.Equal(t, batchParams.Signals[i].TokenAddress, signal.TokenAddress)
					assert.Equal(t, batchParams.Signals[i].SmartWalletCount, signal.SmartWalletCount)
					assert.Equal(t, batchParams.Signals[i].BuyEntryPrice, signal.BuyEntryPrice)
					assert.Equal(t, batchParams.Signals[i].HighestPrice, signal.HighestPrice)
					assert.Equal(t, batchParams.Signals[i].TelegramLink, signal.TelegramLink)
					assert.Equal(t, batchParams.Signals[i].WinRate, signal.WinRate)
					assert.Equal(t, batchParams.Signals[i].AverageHolding, signal.AverageHolding)
					assert.Equal(t, batchParams.Signals[i].AverageWinRate, signal.AverageWinRate)
				}
				return nil
			})

		// Mock all sell signal deletions
		mockRepo.EXPECT().DeleteSellSignalByTokenAddress(ctx, "batch1").Return(nil)
		mockRepo.EXPECT().DeleteSellSignalByTokenAddress(ctx, "batch2").Return(nil)
		mockRepo.EXPECT().DeleteSellSignalByTokenAddress(ctx, "batch3").Return(nil)

		// Mock websocket notifications - should only happen for the 2 new signals
		mockWS.EXPECT().SendEventByUIDs(
			gomock.Any(),
			gomock.Eq([]string{}),
			gomock.Eq(websocket.EventTokenBuySignal),
			gomock.Any(),
		).Times(2).Return(nil)

		err := UpsertBuySignalBatch(ctx, batchParams)
		assert.Nil(t, err)
	})

	// Test empty batch
	t.Run("EmptyBatch", func(t *testing.T) {
		batchParams := &UpsertBuySignalBatchParams{
			Signals: []UpsertBuySignalParams{},
		}

		err := UpsertBuySignalBatch(ctx, batchParams)
		assert.Nil(t, err)
	})

	// Test batch upsert failure
	t.Run("UpsertFailure", func(t *testing.T) {
		mockWS := websocket.NewMockIService(ctrl)
		websocket.Set(mockWS)

		batchParams := &UpsertBuySignalBatchParams{
			Signals: []UpsertBuySignalParams{
				{
					TokenAddress:     "batchError1",
					SmartWalletCount: 10,
					BuyEntryPrice:    0.001,
					HighestPrice:     0.002,
					EmitTime:         time.Now(),
					TelegramLink:     "https://t.me/batchError1",
					WinRate:          0.75,
					AverageHolding:   48.5,
					AverageWinRate:   0.65,
					Symbol:           "ERROR1",
				},
				{
					TokenAddress:     "batchError2",
					SmartWalletCount: 15,
					BuyEntryPrice:    0.003,
					HighestPrice:     0.005,
					EmitTime:         time.Now(),
					TelegramLink:     "https://t.me/batchError2",
					WinRate:          0.80,
					AverageHolding:   52.0,
					AverageWinRate:   0.70,
					Symbol:           "ERROR2",
				},
			},
		}

		// Check for new signals
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, "batchError1").Return(nil, domain.ErrRecordNotFound)
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, "batchError2").Return(nil, domain.ErrRecordNotFound)

		// Mock database error
		dbError := errors.New("database error")
		mockRepo.EXPECT().UpsertBuySignalBatch(ctx, gomock.Any()).Return(dbError)

		err := UpsertBuySignalBatch(ctx, batchParams)
		assert.NotNil(t, err)
		assert.Equal(t, dbError, err.Error)
	})

	// Test with delete sell signal failure (should not affect overall result)
	t.Run("DeleteSellSignalFailure", func(t *testing.T) {
		mockWS := websocket.NewMockIService(ctrl)
		websocket.Set(mockWS)

		batchParams := &UpsertBuySignalBatchParams{
			Signals: []UpsertBuySignalParams{
				{
					TokenAddress:     "batchDeleteError",
					SmartWalletCount: 10,
					BuyEntryPrice:    0.001,
					HighestPrice:     0.002,
					EmitTime:         time.Now(),
					TelegramLink:     "https://t.me/batchDeleteError",
					WinRate:          0.75,
					AverageHolding:   48.5,
					AverageWinRate:   0.65,
					Symbol:           "DELETE",
				},
			},
		}

		// Check if it's a new signal
		mockRepo.EXPECT().GetBuySignalByTokenAddress(ctx, "batchDeleteError").Return(nil, domain.ErrRecordNotFound)

		// Mock upsert success but delete failure
		mockRepo.EXPECT().UpsertBuySignalBatch(ctx, gomock.Any()).Return(nil)
		mockRepo.EXPECT().DeleteSellSignalByTokenAddress(ctx, "batchDeleteError").Return(errors.New("delete error"))

		// Mock websocket notification
		mockWS.EXPECT().SendEventByUIDs(
			gomock.Any(),
			gomock.Eq([]string{}),
			gomock.Eq(websocket.EventTokenBuySignal),
			gomock.Any(),
		).Return(nil)

		err := UpsertBuySignalBatch(ctx, batchParams)
		assert.Nil(t, err) // Should still succeed even if sell signal delete fails
	})
}
