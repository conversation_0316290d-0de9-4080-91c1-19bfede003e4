// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/service/token-signal (interfaces: IRepo)
//
// Generated by this command:
//
//	mockgen -package=tokensignal -self_package=github.com/kryptogo/kg-wallet-backend/service/token-signal -destination=common_mock.go . IRepo
//

// Package tokensignal is a generated GoMock package.
package tokensignal

import (
	context "context"
	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
	reflect "reflect"
)

// MockIRepo is a mock of IRepo interface.
type MockIRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIRepoMockRecorder
}

// MockIRepoMockRecorder is the mock recorder for MockIRepo.
type MockIRepoMockRecorder struct {
	mock *MockIRepo
}

// NewMockIRepo creates a new mock instance.
func NewMockIRepo(ctrl *gomock.Controller) *MockIRepo {
	mock := &MockIRepo{ctrl: ctrl}
	mock.recorder = &MockIRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRepo) EXPECT() *MockIRepoMockRecorder {
	return m.recorder
}

// AddSellSignal mocks base method.
func (m *MockIRepo) AddSellSignal(arg0 context.Context, arg1 *domain.TokenSellSignal) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSellSignal", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddSellSignal indicates an expected call of AddSellSignal.
func (mr *MockIRepoMockRecorder) AddSellSignal(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSellSignal", reflect.TypeOf((*MockIRepo)(nil).AddSellSignal), arg0, arg1)
}

// DeleteBuySignalByTokenAddress mocks base method.
func (m *MockIRepo) DeleteBuySignalByTokenAddress(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBuySignalByTokenAddress", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBuySignalByTokenAddress indicates an expected call of DeleteBuySignalByTokenAddress.
func (mr *MockIRepoMockRecorder) DeleteBuySignalByTokenAddress(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBuySignalByTokenAddress", reflect.TypeOf((*MockIRepo)(nil).DeleteBuySignalByTokenAddress), arg0, arg1)
}

// DeleteSellSignalByTokenAddress mocks base method.
func (m *MockIRepo) DeleteSellSignalByTokenAddress(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSellSignalByTokenAddress", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSellSignalByTokenAddress indicates an expected call of DeleteSellSignalByTokenAddress.
func (mr *MockIRepoMockRecorder) DeleteSellSignalByTokenAddress(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSellSignalByTokenAddress", reflect.TypeOf((*MockIRepo)(nil).DeleteSellSignalByTokenAddress), arg0, arg1)
}

// GetBuySignalByTokenAddress mocks base method.
func (m *MockIRepo) GetBuySignalByTokenAddress(arg0 context.Context, arg1 string) (*domain.TokenBuySignal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBuySignalByTokenAddress", arg0, arg1)
	ret0, _ := ret[0].(*domain.TokenBuySignal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBuySignalByTokenAddress indicates an expected call of GetBuySignalByTokenAddress.
func (mr *MockIRepoMockRecorder) GetBuySignalByTokenAddress(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBuySignalByTokenAddress", reflect.TypeOf((*MockIRepo)(nil).GetBuySignalByTokenAddress), arg0, arg1)
}

// ListBuySignals mocks base method.
func (m *MockIRepo) ListBuySignals(arg0 context.Context) ([]*domain.TokenBuySignal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBuySignals", arg0)
	ret0, _ := ret[0].([]*domain.TokenBuySignal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBuySignals indicates an expected call of ListBuySignals.
func (mr *MockIRepoMockRecorder) ListBuySignals(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBuySignals", reflect.TypeOf((*MockIRepo)(nil).ListBuySignals), arg0)
}

// ListSellSignals mocks base method.
func (m *MockIRepo) ListSellSignals(arg0 context.Context, arg1 int) ([]*domain.TokenSellSignal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSellSignals", arg0, arg1)
	ret0, _ := ret[0].([]*domain.TokenSellSignal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSellSignals indicates an expected call of ListSellSignals.
func (mr *MockIRepoMockRecorder) ListSellSignals(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSellSignals", reflect.TypeOf((*MockIRepo)(nil).ListSellSignals), arg0, arg1)
}

// UpsertBuySignal mocks base method.
func (m *MockIRepo) UpsertBuySignal(arg0 context.Context, arg1 *domain.TokenBuySignal) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertBuySignal", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertBuySignal indicates an expected call of UpsertBuySignal.
func (mr *MockIRepoMockRecorder) UpsertBuySignal(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertBuySignal", reflect.TypeOf((*MockIRepo)(nil).UpsertBuySignal), arg0, arg1)
}

// UpsertBuySignalBatch mocks base method.
func (m *MockIRepo) UpsertBuySignalBatch(arg0 context.Context, arg1 []*domain.TokenBuySignal) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertBuySignalBatch", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertBuySignalBatch indicates an expected call of UpsertBuySignalBatch.
func (mr *MockIRepoMockRecorder) UpsertBuySignalBatch(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertBuySignalBatch", reflect.TypeOf((*MockIRepo)(nil).UpsertBuySignalBatch), arg0, arg1)
}

// GetLast7d2xStats mocks base method.
func (m *MockIRepo) GetLast7d2xStats(arg0 context.Context) (int, int, float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLast7d2xStats", arg0)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(float64)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// GetLast7d2xStats indicates an expected call of GetLast7d2xStats.
func (mr *MockIRepoMockRecorder) GetLast7d2xStats(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLast7d2xStats", reflect.TypeOf((*MockIRepo)(nil).GetLast7d2xStats), arg0)
}

// GetPastTopSignals mocks base method.
func (m *MockIRepo) GetPastTopSignals(arg0 context.Context) ([]*domain.TokenSellSignal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPastTopSignals", arg0)
	ret0, _ := ret[0].([]*domain.TokenSellSignal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPastTopSignals indicates an expected call of GetPastTopSignals.
func (mr *MockIRepoMockRecorder) GetPastTopSignals(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPastTopSignals", reflect.TypeOf((*MockIRepo)(nil).GetPastTopSignals), arg0)
}

// GetTodayTopSignal mocks base method.
func (m *MockIRepo) GetTodayTopSignal(arg0 context.Context) (*domain.TokenSellSignal, *domain.TokenBuySignal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTodayTopSignal", arg0)
	ret0, _ := ret[0].(*domain.TokenSellSignal)
	ret1, _ := ret[1].(*domain.TokenBuySignal)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetTodayTopSignal indicates an expected call of GetTodayTopSignal.
func (mr *MockIRepoMockRecorder) GetTodayTopSignal(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTodayTopSignal", reflect.TypeOf((*MockIRepo)(nil).GetTodayTopSignal), arg0)
}
