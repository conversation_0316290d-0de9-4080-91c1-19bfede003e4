// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/service/universal-swap (interfaces: IRepo)
//
// Generated by this command:
//
//	mockgen -package=universalswap -self_package=github.com/kryptogo/kg-wallet-backend/service/universal-swap -destination=common_mock.go . IRepo
//

// Package universalswap is a generated GoMock package.
package universalswap

import (
	context "context"
	reflect "reflect"
	time "time"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockIRepo is a mock of IRepo interface.
type MockIRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIRepoMockRecorder
}

// MockIRepoMockRecorder is the mock recorder for MockIRepo.
type MockIRepoMockRecorder struct {
	mock *MockIRepo
}

// NewMockIRepo creates a new mock instance.
func NewMockIRepo(ctrl *gomock.Controller) *MockIRepo {
	mock := &MockIRepo{ctrl: ctrl}
	mock.recorder = &MockIRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRepo) EXPECT() *MockIRepoMockRecorder {
	return m.recorder
}

// AcquireLock mocks base method.
func (m *MockIRepo) AcquireLock(arg0 context.Context, arg1 string, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLock indicates an expected call of AcquireLock.
func (mr *MockIRepoMockRecorder) AcquireLock(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLock", reflect.TypeOf((*MockIRepo)(nil).AcquireLock), arg0, arg1, arg2)
}

// AcquireLockWithRetry mocks base method.
func (m *MockIRepo) AcquireLockWithRetry(arg0 context.Context, arg1 string, arg2, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLockWithRetry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLockWithRetry indicates an expected call of AcquireLockWithRetry.
func (mr *MockIRepoMockRecorder) AcquireLockWithRetry(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLockWithRetry", reflect.TypeOf((*MockIRepo)(nil).AcquireLockWithRetry), arg0, arg1, arg2, arg3)
}

// BatchGetTokenPrices mocks base method.
func (m *MockIRepo) BatchGetTokenPrices(arg0 context.Context, arg1 []domain.ChainToken) (map[domain.ChainToken]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPrices", arg0, arg1)
	ret0, _ := ret[0].(map[domain.ChainToken]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPrices indicates an expected call of BatchGetTokenPrices.
func (mr *MockIRepoMockRecorder) BatchGetTokenPrices(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPrices", reflect.TypeOf((*MockIRepo)(nil).BatchGetTokenPrices), arg0, arg1)
}

// BatchGetTokenPricesIn24H mocks base method.
func (m *MockIRepo) BatchGetTokenPricesIn24H(arg0 context.Context, arg1 []domain.ChainToken) (map[domain.ChainToken][]*domain.PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPricesIn24H", arg0, arg1)
	ret0, _ := ret[0].(map[domain.ChainToken][]*domain.PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPricesIn24H indicates an expected call of BatchGetTokenPricesIn24H.
func (mr *MockIRepoMockRecorder) BatchGetTokenPricesIn24H(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPricesIn24H", reflect.TypeOf((*MockIRepo)(nil).BatchGetTokenPricesIn24H), arg0, arg1)
}

// CreateUniversalSwap mocks base method.
func (m *MockIRepo) CreateUniversalSwap(arg0 context.Context, arg1 *domain.UniversalSwap) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUniversalSwap", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUniversalSwap indicates an expected call of CreateUniversalSwap.
func (mr *MockIRepoMockRecorder) CreateUniversalSwap(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUniversalSwap", reflect.TypeOf((*MockIRepo)(nil).CreateUniversalSwap), arg0, arg1)
}

// GetAssetPrice mocks base method.
func (m *MockIRepo) GetAssetPrice(arg0 context.Context, arg1, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockIRepoMockRecorder) GetAssetPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockIRepo)(nil).GetAssetPrice), arg0, arg1, arg2)
}

// GetNativeAssetPrice mocks base method.
func (m *MockIRepo) GetNativeAssetPrice(arg0 context.Context, arg1 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockIRepoMockRecorder) GetNativeAssetPrice(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockIRepo)(nil).GetNativeAssetPrice), arg0, arg1)
}

// GetTokenPrice mocks base method.
func (m *MockIRepo) GetTokenPrice(arg0 context.Context, arg1 domain.Chain, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPrice indicates an expected call of GetTokenPrice.
func (mr *MockIRepoMockRecorder) GetTokenPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPrice", reflect.TypeOf((*MockIRepo)(nil).GetTokenPrice), arg0, arg1, arg2)
}

// GetTokenPricesIn24H mocks base method.
func (m *MockIRepo) GetTokenPricesIn24H(arg0 context.Context, arg1 domain.Chain, arg2 string) ([]*domain.PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPricesIn24H", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*domain.PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPricesIn24H indicates an expected call of GetTokenPricesIn24H.
func (mr *MockIRepoMockRecorder) GetTokenPricesIn24H(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPricesIn24H", reflect.TypeOf((*MockIRepo)(nil).GetTokenPricesIn24H), arg0, arg1, arg2)
}

// GetUniversalSwapByDestinationWallet mocks base method.
func (m *MockIRepo) GetUniversalSwapByDestinationWallet(arg0 context.Context, arg1, arg2 string) (*domain.UniversalSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUniversalSwapByDestinationWallet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*domain.UniversalSwap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUniversalSwapByDestinationWallet indicates an expected call of GetUniversalSwapByDestinationWallet.
func (mr *MockIRepoMockRecorder) GetUniversalSwapByDestinationWallet(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUniversalSwapByDestinationWallet", reflect.TypeOf((*MockIRepo)(nil).GetUniversalSwapByDestinationWallet), arg0, arg1, arg2)
}

// GetUniversalSwapByID mocks base method.
func (m *MockIRepo) GetUniversalSwapByID(arg0 context.Context, arg1 int) (*domain.UniversalSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUniversalSwapByID", arg0, arg1)
	ret0, _ := ret[0].(*domain.UniversalSwap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUniversalSwapByID indicates an expected call of GetUniversalSwapByID.
func (mr *MockIRepoMockRecorder) GetUniversalSwapByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUniversalSwapByID", reflect.TypeOf((*MockIRepo)(nil).GetUniversalSwapByID), arg0, arg1)
}

// GetUniversalSwapsByUID mocks base method.
func (m *MockIRepo) GetUniversalSwapsByUID(arg0 context.Context, arg1 string) ([]*domain.UniversalSwap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUniversalSwapsByUID", arg0, arg1)
	ret0, _ := ret[0].([]*domain.UniversalSwap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUniversalSwapsByUID indicates an expected call of GetUniversalSwapsByUID.
func (mr *MockIRepoMockRecorder) GetUniversalSwapsByUID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUniversalSwapsByUID", reflect.TypeOf((*MockIRepo)(nil).GetUniversalSwapsByUID), arg0, arg1)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockIRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*domain.OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*domain.OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockIRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockIRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// ReleaseLock mocks base method.
func (m *MockIRepo) ReleaseLock(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseLock", arg0, arg1)
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockIRepoMockRecorder) ReleaseLock(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockIRepo)(nil).ReleaseLock), arg0, arg1)
}

// UpdateUniversalSwap mocks base method.
func (m *MockIRepo) UpdateUniversalSwap(arg0 context.Context, arg1 *domain.UpdateUniversalSwapRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUniversalSwap", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUniversalSwap indicates an expected call of UpdateUniversalSwap.
func (mr *MockIRepoMockRecorder) UpdateUniversalSwap(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUniversalSwap", reflect.TypeOf((*MockIRepo)(nil).UpdateUniversalSwap), arg0, arg1)
}
