package universalswap

import (
	"context"
	"fmt"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestGetSourceAddressForDestination(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Setup mock repository
	mockRepo := NewMockIRepo(ctrl)

	// Initialize the service with mock
	Init(mockRepo, nil, "")

	// Test data
	ctx := context.Background()
	chainID := "eth"
	walletAddress := domain.NewEvmAddress("******************************************")
	sourceAddress := domain.NewEvmAddress("******************************************")

	// Test cases
	t.Run("successful retrieval of source address", func(t *testing.T) {
		// Create a swap with source transactions
		swap := &domain.UniversalSwap{
			ID: 1,
			SourceTransactions: []*domain.SourceTransaction{
				{
					From: sourceAddress,
					To:   domain.NewEvmAddress("******************************************"),
				},
				{
					From: domain.NewEvmAddress("******************************************"),
					To:   domain.NewEvmAddress("******************************************"),
				},
			},
			Destination: &domain.Destination{
				Chain:         domain.Ethereum,
				WalletAddress: walletAddress,
			},
		}

		// Setup mock expectations
		mockRepo.EXPECT().
			GetUniversalSwapByDestinationWallet(gomock.Any(), chainID, walletAddress.String()).
			Return(swap, nil)

		// Call the function
		address, err := GetSourceAddressForDestination(ctx, chainID, walletAddress.String())

		// Assertions
		assert.NoError(t, err)
		assert.Equal(t, sourceAddress.String(), address.String())
	})

	t.Run("no swap found for destination", func(t *testing.T) {
		// Setup mock expectations for not found case
		mockRepo.EXPECT().
			GetUniversalSwapByDestinationWallet(gomock.Any(), chainID, walletAddress.String()).
			Return(nil, domain.ErrRecordNotFound)

		// Call the function
		address, err := GetSourceAddressForDestination(ctx, chainID, walletAddress.String())

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, domain.ErrRecordNotFound, err)
		assert.Nil(t, address)
	})

	t.Run("swap found but no source transactions", func(t *testing.T) {
		// Create a swap with no source transactions
		swap := &domain.UniversalSwap{
			ID:                 1,
			SourceTransactions: []*domain.SourceTransaction{},
			Destination: &domain.Destination{
				Chain:         domain.Ethereum,
				WalletAddress: walletAddress,
			},
		}

		// Setup mock expectations
		mockRepo.EXPECT().
			GetUniversalSwapByDestinationWallet(gomock.Any(), chainID, walletAddress.String()).
			Return(swap, nil)

		// Call the function
		address, err := GetSourceAddressForDestination(ctx, chainID, walletAddress.String())

		// Assertions
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no source transactions found")
		assert.Nil(t, address)
	})

	t.Run("database error", func(t *testing.T) {
		// Setup mock expectations for database error
		dbError := fmt.Errorf("database connection error")
		mockRepo.EXPECT().
			GetUniversalSwapByDestinationWallet(gomock.Any(), chainID, walletAddress.String()).
			Return(nil, dbError)

		// Call the function
		address, err := GetSourceAddressForDestination(ctx, chainID, walletAddress.String())

		// Assertions
		assert.Error(t, err)
		assert.Equal(t, dbError, err)
		assert.Nil(t, address)
	})
}
