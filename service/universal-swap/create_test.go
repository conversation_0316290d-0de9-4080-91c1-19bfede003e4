package universalswap

import (
	"context"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/kg-wallet-backend/chain/evm"
	"github.com/kryptogo/kg-wallet-backend/domain"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCreate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// setup signing server
	signingservertest.Setup(t)

	// Setup mock repo
	mockRepo := NewMockIRepo(ctrl)
	mockRepo.EXPECT().GetNativeAssetPrice(gomock.Any(), "arb").Return(3000.0, nil).AnyTimes()
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
		EvmAddress:    "******************************************",
		TronAddress:   "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7",
		SolanaAddress: "7ET7q8BBmpef4MDqE47tffFfHeE2y1ccT5MEDdA9pzeH",
	}, nil).AnyTimes()
	mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	Init(mockRepo, mockExecutor, "http://localhost:8080")
	tokenmetaMock := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(tokenmetaMock, nil, nil)
	tokenmetaMock.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).Return(map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: domain.Arbitrum, TokenID: "******************************************"}: {
			CoingeckoID: "usdc",
			IsVerified:  true,
			Decimals:    6,
		},
		{Chain: domain.Arbitrum, TokenID: domain.Arbitrum.MainToken().ID()}: {
			CoingeckoID: "eth",
			IsVerified:  true,
			Decimals:    18,
		},
	}, nil).AnyTimes()

	// Generate random private key and address
	privateKey, err := crypto.HexToECDSA("652729b996d63504a1a47a14fff210340c1a7059c9ae44a80953dea19d01e040")
	assert.NoError(t, err)
	userAddress := crypto.PubkeyToAddress(privateKey.PublicKey) // ******************************************
	uid := "test-uid-1"

	// Setup test data
	ctx := context.Background()
	chain := domain.Arbitrum
	tokenID := "******************************************"
	toAddress := common.HexToAddress("******************************************") // Example destination address
	amount := big.NewInt(1)

	// Create and sign ERC20 transfer tx
	client := evm.GetClient(chain)
	nonce, err := client.GetNonce(ctx, domain.EvmAddress{Address: userAddress})
	assert.NoError(t, err)
	gasPrice, err := client.GetRawClient().SuggestGasPrice(ctx)
	assert.NoError(t, err)
	nativeTx, err := client.CreateNativeTransfer(ctx, domain.NewEvmAddress("0x1"), domain.NewEvmAddress("0x2"), big.NewInt(1))
	assert.NoError(t, err)
	gasLimit := nativeTx.Gas() * 2

	// tx1: transfer ERC20 token
	transferFnSig := []byte("transfer(address,uint256)")
	methodID := crypto.Keccak256(transferFnSig)[:4]
	paddedAddress := common.LeftPadBytes(toAddress.Bytes(), 32)
	paddedAmount := common.LeftPadBytes(amount.Bytes(), 32)
	data := append(methodID, append(paddedAddress, paddedAmount...)...)

	tx1 := types.NewTransaction(nonce, common.HexToAddress(tokenID), big.NewInt(0), gasLimit, gasPrice, data)
	signer := types.LatestSignerForChainID(big.NewInt(chain.Number()))
	signedTx, err := types.SignTx(tx1, signer, privateKey)
	assert.NoError(t, err)

	signedTxBytes, err := signedTx.MarshalBinary()
	assert.NoError(t, err)

	tx2 := types.NewTransaction(nonce+1, toAddress, big.NewInt(10_000_000_000_000), gasLimit, gasPrice, []byte{})
	signer = types.LatestSignerForChainID(big.NewInt(chain.Number()))
	signedTx2, err := types.SignTx(tx2, signer, privateKey)
	assert.NoError(t, err)

	signedTxBytes2, err := signedTx2.MarshalBinary()
	assert.NoError(t, err)

	// Setup mock expectations
	// Mock token balance check
	mockRepo.EXPECT().
		BatchGetTokenPrices(gomock.Any(), gomock.Any()).
		Return(map[domain.ChainToken]float64{
			{Chain: chain, TokenID: tokenID}:                          1_000_000,
			{Chain: chain, TokenID: domain.Arbitrum.MainToken().ID()}: 3_000_000_000,
		}, nil)

	// Mock create universal swap
	mockRepo.EXPECT().
		CreateUniversalSwap(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, swap *domain.UniversalSwap) (int, error) {
			t.Logf("Creating universal swap, ID: %d, UID: %s, SourceTransactions: %v, Destination: %v, Status: %v, SponsorTransactions: %v, FeeRate: %v, EstimatedFinishAt: %v, RetryCount: %v, CreatedAt: %v", swap.ID, swap.UID, swap.SourceTransactions, swap.Destination, swap.Status, swap.SponsorTransactions, swap.FeeRate, swap.EstimatedFinishAt, swap.RetryCount, swap.CreatedAt)
			assert.Equal(t, domain.UniversalSwapStatusPending, swap.Status)
			assert.Equal(t, 2, len(swap.SourceTransactions))
			assert.Equal(t, chain, swap.SourceTransactions[0].Chain)
			assert.Equal(t, userAddress.Hex(), swap.SourceTransactions[0].From.String())
			assert.Equal(t, domain.SourceTxStatusInit, swap.SourceTransactions[0].Status)
			assert.Equal(t, "1", swap.SourceTransactions[0].RawAmount)
			if len(swap.SponsorTransactions) > 0 {

				assert.Equal(t, 1, len(swap.SponsorTransactions))
				assert.Equal(t, chain, swap.SponsorTransactions[0].Chain)
				assert.Equal(t, "******************************************", swap.SponsorTransactions[0].From.String())
			}
			assert.Equal(t, chain, swap.SourceTransactions[1].Chain)
			assert.Equal(t, userAddress.Hex(), swap.SourceTransactions[1].From.String())
			assert.Equal(t, domain.SourceTxStatusInit, swap.SourceTransactions[1].Status)
			assert.Equal(t, "10000000000000", swap.SourceTransactions[1].RawAmount)
			return 1, nil
		})

	// Mock task creation
	mockExecutor.EXPECT().
		Execute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil)

	// Create universal swap request
	swap := &domain.UniversalSwap{
		UID: uid,
		SourceTransactions: []*domain.SourceTransaction{
			{
				Chain:     chain,
				From:      domain.NewEvmAddress(userAddress.Hex()),
				To:        domain.NewEvmAddress(toAddress.Hex()),
				TokenID:   tokenID,
				RawAmount: amount.String(),
				SignedTx:  common.Bytes2Hex(signedTxBytes),
				Status:    domain.SourceTxStatusInit,
			},
			{
				Chain:     chain,
				From:      domain.NewEvmAddress(userAddress.Hex()),
				To:        domain.NewEvmAddress(toAddress.Hex()),
				TokenID:   domain.Arbitrum.MainToken().ID(),
				RawAmount: "10000000000000",
				SignedTx:  common.Bytes2Hex(signedTxBytes2),
				Status:    domain.SourceTxStatusInit,
			},
		},
		Destination: &domain.Destination{
			Chain:         chain,
			WalletAddress: domain.NewEvmAddress(toAddress.Hex()),
			TokenID:       tokenID,
		},
	}

	// Call Create
	id, kgErr := Create(ctx, swap)
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Fatalf("failed to create universal swap: %v", kgErr.Error)
	}
	assert.Equal(t, 1, id)
}
