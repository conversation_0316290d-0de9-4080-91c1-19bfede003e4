package universalswap

import (
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestValidateSourceSolanaTx(t *testing.T) {
	t.Run("valid SOL transfer", func(t *testing.T) {
		// This is a real Solana SOL transfer tx
		tx := &domain.SourceTransaction{
			Chain:     domain.Solana,
			From:      domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"),
			To:        domain.NewAddressByChain(domain.Solana, "5bhJnZJscqcZgoPRSwn9RYLmVCHZETPYygoRpzG7k67V"),
			TokenID:   domain.Solana.MainToken().ID(), // SOL
			RawAmount: "11000000",                     // 0.011 SOL
			SignedTx:  "2eTA6JCz7bYy8AAcHio8vfnfPyrYTt1SsvDanhKGCTwJ7iBEAAx36Q45rqNGeLGFYSy6Vn9XHaNYzc15DNrebxvsrn6xvyss4QZdXy8Dhdk8i5XyHUkiXYrYHU4bfMmP4TZE8Zq7uX75XgQXiRjy2i721Qw1qbSXjBcexfgZ8UfCLXXF1XY2WgzRtHFow4Qbr3T88f6jTDXBYUbEqWfCYiEdKq9wVyeq1Te9skRjskS1j9jco7fBZkA9dGTcVxF41DxcgocbR5CvbPcsTwn8ixorPkXTD4smVuySV4H5",
		}

		neededNative, txHash, err := validateSourceSolanaTx(tx)
		assert.NoError(t, err)
		if err != nil {
			t.Fatalf("failed to validate source solana tx: %v", err)
		}
		assert.Equal(t, "11005000", neededNative.String()) // 0.011 SOL + 5000 lamports per signature
		assert.Equal(t, "4wM2WeuwqgKrFqPM8WKuTqCp5q9awu9d6oXpUZa62Rh1fn48dt2V2W5WfzDgQdzoAZLkVz99nmSf2s4HWz8JxQcH", txHash)
	})

	t.Run("valid SPL token transfer with creating ATA", func(t *testing.T) {
		// This is a real Solana SPL token transfer tx
		tx := &domain.SourceTransaction{
			Chain:     domain.Solana,
			From:      domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"),
			To:        domain.NewAddressByChain(domain.Solana, "5bhJnZJscqcZgoPRSwn9RYLmVCHZETPYygoRpzG7k67V"),
			TokenID:   "4hGYiv6w7SyNXjASvVFoG9XTiAtjiTWmf4krcp2apump", // Some SPL token
			RawAmount: "111111000000",
			SignedTx:  "48UWBTag47ART1KejyXa9zbonYp8JPBo24M9DU2MMSpCxGpNGgV1J1apsFZqiSrEoewbSz4kkvgUoganxgiFFgdYFtWJq6DMzg8R4fsAKAtE42BEg3hxSuzBDCxygnr1Xmqb6CchqetzdaMctpkh5NjNj92m7hvTEormtu1suK52pKp4zVDkzGj7qNwsTwxSnfjEeSdqpe2kSwdAdZxjRkjYD8PQzaeSdVuU8Sg8HjhwTVzJSQs7cZVMZyMXaDf3452ugzBSdLKf9WLAXmwNqoaib963TPft1RBMucFGxZU37UcK4DvuPooR73Mnta8tDJgVz6hKKPej3Y42mjsviNt6buAZ4hMcKVK8wPw7Kw12ehpLsmp8XVxUDUpLL4WQN4fud9wXSQA9ukpTFst88e8t27YNrVdU2sSVH43WyDU2Y7ESrDvtMBAeHDWMf6XhdvCRDc6BCUjuCG9bufUynj38vCkmxK2JE2E6NfLoS3pBEeHiUopKdyCP4bjWwiCHUEERdGQPfg3wVjnf",
		}

		neededNative, txHash, err := validateSourceSolanaTx(tx)
		assert.NoError(t, err)
		if err != nil {
			t.Fatalf("failed to validate source solana tx: %v", err)
		}
		assert.Equal(t, "5000", neededNative.String()) // Just 5000 lamports per signature
		assert.Equal(t, "4oav61x631DpMUB2knjMmH5DuaT2xwmEqCeLXR5YMzdcAvfhD26cJger6g2Eo4PFuYEZ8TYakeEueT2doAw9Gn4s", txHash)
	})

	t.Run("valid SPL token transfer without creating ATA", func(t *testing.T) {
		// This is a real Solana SPL token transfer tx
		tx := &domain.SourceTransaction{
			Chain:     domain.Solana,
			From:      domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"),
			To:        domain.NewAddressByChain(domain.Solana, "5bhJnZJscqcZgoPRSwn9RYLmVCHZETPYygoRpzG7k67V"),
			TokenID:   "4hGYiv6w7SyNXjASvVFoG9XTiAtjiTWmf4krcp2apump", // Some SPL token
			RawAmount: "111111000000",
			SignedTx:  "7RXN1gN8h1X5LkM7v9wCVHy7ofydrpU6jx5ceqGbd5NsPnjDCgyouBhadSbK2S5or3maKkBZ8zKwsN8v1VoWcWbgFHMmX4E67J24ALVxnSE3wEwcHeS3o1eKPP4Q9vy5QDwJw4f5qxybSarhfSthGhGwzjcrkBjrfh61GKLWLdKBQNjmAXa6MVxNymQkRAUk4FzH9nmVtmYkkT7dtTynrmrQBNStUwZ2686EC8zdiVbew4UdkuosSUG13BvqtccVvMukJLB92dPwbde7iMNhqKusQqJ9gZqKPDa16TrB5YkLQD7iJYr2k3ZxDGdeuf6gWRHe8A7DpVeUNVekHLtc73nZZFqrCSmJ6EhvHtnFQTuwtKUiuydW8HGfo7W9T5Z",
		}

		neededNative, txHash, err := validateSourceSolanaTx(tx)
		assert.NoError(t, err)
		if err != nil {
			t.Fatalf("failed to validate source solana tx: %v", err)
		}
		assert.Equal(t, "5000", neededNative.String())
		assert.Equal(t, "2ncvtR79QVMUcibjzzx5CVvd6bWP6HeqySk5i4GiH3CP3C7PUMPwrBD4RjXUV9rwrySJRzR2Vx6mDEkakTk2AjyP", txHash)
	})

	t.Run("invalid token mint", func(t *testing.T) {
		tx := &domain.SourceTransaction{
			Chain:     domain.Solana,
			From:      domain.NewAddressByChain(domain.Solana, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"),
			To:        domain.NewAddressByChain(domain.Solana, "5bhJnZJscqcZgoPRSwn9RYLmVCHZETPYygoRpzG7k67V"),
			TokenID:   "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			RawAmount: "111111000000",
			SignedTx:  "7RXN1gN8h1X5LkM7v9wCVHy7ofydrpU6jx5ceqGbd5NsPnjDCgyouBhadSbK2S5or3maKkBZ8zKwsN8v1VoWcWbgFHMmX4E67J24ALVxnSE3wEwcHeS3o1eKPP4Q9vy5QDwJw4f5qxybSarhfSthGhGwzjcrkBjrfh61GKLWLdKBQNjmAXa6MVxNymQkRAUk4FzH9nmVtmYkkT7dtTynrmrQBNStUwZ2686EC8zdiVbew4UdkuosSUG13BvqtccVvMukJLB92dPwbde7iMNhqKusQqJ9gZqKPDa16TrB5YkLQD7iJYr2k3ZxDGdeuf6gWRHe8A7DpVeUNVekHLtc73nZZFqrCSmJ6EhvHtnFQTuwtKUiuydW8HGfo7W9T5Z",
		}

		_, _, err := validateSourceSolanaTx(tx)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid source token account")
	})
}
