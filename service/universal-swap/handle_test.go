package universalswap

import (
	"context"
	"math/big"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/kg-wallet-backend/chain/evm"
	"github.com/kryptogo/kg-wallet-backend/domain"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/service/collect"
	"github.com/kryptogo/kg-wallet-backend/service/erc4337"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestHandle(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY"})

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// setup signing server
	signingservertest.Setup(t)

	// Setup mock repo
	mockRepo := NewMockIRepo(ctrl)
	mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	Init(mockRepo, mockExecutor, "http://localhost:8080")

	// Setup token metadata mock
	tokenmetaMock := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(tokenmetaMock, nil, nil)

	// Setup collect service
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
		EvmAddress:    "******************************************",
		TronAddress:   "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7",
		SolanaAddress: "7ET7q8BBmpef4MDqE47tffFfHeE2y1ccT5MEDdA9pzeH",
	}, nil).AnyTimes()
	collect.Init(mockRepo)
	erc4337.Init(mockRepo)

	// Setup test data
	ctx := context.Background()
	chain := domain.Arbitrum
	tokenID := "******************************************" // USDC on Arbitrum
	privateKey, err := crypto.HexToECDSA("652729b996d63504a1a47a14fff210340c1a7059c9ae44a80953dea19d01e040")
	assert.NoError(t, err)
	userAddress := crypto.PubkeyToAddress(privateKey.PublicKey) // ******************************************
	uid := "test-uid-1"
	depositAddr := "******************************************"
	amount := big.NewInt(1) // 0.000001 USDC (6 decimals)

	// Create and sign USDC transfer tx
	client := evm.GetClient(chain)
	nonce, err := client.GetNonce(ctx, domain.EvmAddress{Address: userAddress})
	assert.NoError(t, err)
	gasPrice, err := client.GetRawClient().SuggestGasPrice(ctx)
	assert.NoError(t, err)
	nativeTx, err := client.CreateNativeTransfer(ctx, domain.NewEvmAddress("0x1"), domain.NewEvmAddress("0x2"), big.NewInt(1))
	assert.NoError(t, err)
	gasLimit := nativeTx.Gas() * 2

	// Create ERC20 transfer data
	transferFnSig := []byte("transfer(address,uint256)")
	methodID := crypto.Keccak256(transferFnSig)[:4]
	paddedAddress := common.LeftPadBytes(common.HexToAddress(depositAddr).Bytes(), 32)
	paddedAmount := common.LeftPadBytes(amount.Bytes(), 32)
	data := append(methodID, append(paddedAddress, paddedAmount...)...)

	tx := types.NewTransaction(nonce, common.HexToAddress(tokenID), big.NewInt(0), gasLimit, gasPrice, data)
	signer := types.LatestSignerForChainID(big.NewInt(chain.Number()))
	signedTx, err := types.SignTx(tx, signer, privateKey)
	assert.NoError(t, err)

	signedTxBytes, err := signedTx.MarshalBinary()
	assert.NoError(t, err)

	// Create universal swap object
	swap := &domain.UniversalSwap{
		ID:  1,
		UID: uid,
		SourceTransactions: []*domain.SourceTransaction{
			{
				Chain:     chain,
				From:      domain.NewEvmAddress(userAddress.Hex()),
				To:        domain.NewEvmAddress(depositAddr),
				TokenID:   tokenID,
				RawAmount: amount.String(),
				SignedTx:  common.Bytes2Hex(signedTxBytes),
				TxHash:    signedTx.Hash().Hex(),
				Status:    domain.SourceTxStatusInit,
			},
		},
		Destination: &domain.Destination{
			Chain:         chain,
			WalletAddress: domain.NewEvmAddress("******************************************"),
			TokenID:       tokenID,
		},
		Status:            domain.UniversalSwapStatusPending,
		FeeRate:           0.006,
		EstimatedFinishAt: time.Now().Add(2 * time.Minute),
		CreatedAt:         time.Now(),
	}

	// Mock repo responses
	// 1. Lock acquisition
	mockRepo.EXPECT().
		AcquireLockWithRetry(gomock.Any(), "universal-swap-handle-1", gomock.Any(), gomock.Any()).
		Return(nil)
	mockRepo.EXPECT().
		ReleaseLock(gomock.Any(), "universal-swap-handle-1")

	// Mock GetUniversalSwapByID to return different states in sequence
	callCount := 0
	mockRepo.EXPECT().
		GetUniversalSwapByID(gomock.Any(), 1).
		DoAndReturn(func(_ context.Context, _ int) (*domain.UniversalSwap, error) {
			// First call returns initial state
			if callCount == 0 {
				callCount++
				return swap, nil
			}
			// Second call returns broadcasted state
			confirmedSwap := *swap
			confirmedSwap.SourceTransactions[0].Status = domain.SourceTxStatusConfirmed
			return &confirmedSwap, nil
		}).Times(2)

	// Mock all UpdateUniversalSwap calls
	mockRepo.EXPECT().
		UpdateUniversalSwap(gomock.Any(), gomock.Any()).
		DoAndReturn(func(_ context.Context, req *domain.UpdateUniversalSwapRequest) error {
			switch {
			// Update after broadcasting
			case req.Source != nil && req.Source[signedTx.Hash().Hex()].Status != nil && *req.Source[signedTx.Hash().Hex()].Status == domain.SourceTxStatusBroadcasted:
				return nil

			// Update after confirmation
			case req.Source != nil && req.Source[signedTx.Hash().Hex()].Status != nil && *req.Source[signedTx.Hash().Hex()].Status == domain.SourceTxStatusConfirmed:
				return nil

			// Final update to success
			case req.Status != nil && *req.Status == domain.UniversalSwapStatusSuccess:
				return nil

			// Retry count
			case req.RetryCount != nil && *req.RetryCount == 1:
				return nil

			default:
				t.Errorf("unexpected UpdateUniversalSwap call with request: %+v", req)
				return nil
			}
		}).AnyTimes()

	// Call Handle
	kgErr := Handle(ctx, 1)
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Fatalf("Handle returned error: %v", kgErr.Error)
	}
}
