package free_transfer

import (
	"context"
	"net/http"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCheckWalletGasStatus(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	t.Run("Sufficient gas", func(t *testing.T) {
		chain := domain.Arbitrum
		walletAddress := "******************************************"
		expectedResult := true

		hasEnoughGas, kgErr := CheckWalletGasStatus(ctx, chain, walletAddress)

		assert.Nil(t, kgErr)
		assert.Equal(t, expectedResult, hasEnoughGas)
	})

	t.Run("Insufficient gas", func(t *testing.T) {
		chain := domain.Arbitrum
		walletAddress := "******************************************"
		expectedResult := false

		hasEnoughGas, kgErr := CheckWalletGasStatus(ctx, chain, walletAddress)

		assert.Nil(t, kgErr)
		assert.Equal(t, expectedResult, hasEnoughGas)
	})

	t.Run("Non-EVM chain", func(t *testing.T) {
		chain := domain.Tron
		walletAddress := "******************************************" // does not matter
		expectedResult := true

		hasEnoughGas, kgErr := CheckWalletGasStatus(ctx, chain, walletAddress)

		assert.Nil(t, kgErr)
		assert.Equal(t, expectedResult, hasEnoughGas)
	})
}

func TestHandleFreeTransfer(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	orgID := 1

	t.Run("Non-supported chain", func(t *testing.T) {
		chain := domain.Tron
		autoGasExchange := false
		expectedResult := false
		expectedError := code.NewKGError(code.FreeTransferExecutionFailed, http.StatusBadRequest, ErrChainNotSupported, nil)

		result, kgErr := HandleFreeTransfer(ctx, orgID, chain, autoGasExchange)

		assert.NotNil(t, kgErr)
		assert.Equal(t, expectedError.Code, kgErr.Code)
		assert.Equal(t, expectedError.HttpStatus, kgErr.HttpStatus)
		assert.Equal(t, expectedResult, result)
	})

	t.Run("Free sends exhausted", func(t *testing.T) {
		chain := domain.Arbitrum
		autoGasExchange := false
		freeSendCount := &domain.OrgFreeSendCount{
			OrganizationID: orgID,
			UsedCount:      MaxFreeSendCount,
		}
		orgWallet := &domain.StudioOrganizationWallet{
			WalletType:    "evm",
			WalletAddress: "******************************************",
		}
		expectedResult := false
		expectedError := code.NewKGError(code.FreeTransferExecutionFailed, http.StatusBadRequest, ErrFreeGasExhausted, nil)

		mockTransferRepo := domain.NewMockStudioFreeTransferRepo(ctrl)
		mockTransferRepo.EXPECT().
			GetOrgFreeSendCount(ctx, orgID).
			Return(freeSendCount, nil)

		Init(mockTransferRepo)
		mockOrgRepo := domain.NewMockStudioOrgRepo(ctrl)
		organization.Init(organization.InitParam{
			StudioOrgRepo: mockOrgRepo,
		})
		mockOrgRepo.EXPECT().
			GetOrgWallet(ctx, orgID, "evm").
			Return(orgWallet, nil)

		result, kgErr := HandleFreeTransfer(ctx, orgID, chain, autoGasExchange)

		assert.NotNil(t, kgErr)
		assert.Equal(t, expectedError.Code, kgErr.Code)
		assert.Equal(t, expectedError.HttpStatus, kgErr.HttpStatus)
		assert.Equal(t, expectedResult, result)
	})

	t.Run("Successful free transfer", func(t *testing.T) {
		signingservertest.Setup(t)
		chain := domain.Arbitrum
		autoGasExchange := false
		freeSendCount := &domain.OrgFreeSendCount{
			OrganizationID: orgID,
			UsedCount:      0,
		}
		orgWallet := &domain.StudioOrganizationWallet{
			WalletType:    "evm",
			WalletAddress: "******************************************",
		}
		expectedResult := true

		mockTransferRepo := domain.NewMockStudioFreeTransferRepo(ctrl)
		mockTransferRepo.EXPECT().
			GetOrgFreeSendCount(ctx, orgID).
			Return(freeSendCount, nil)

		mockTransferRepo.EXPECT().
			UpdateOrgFreeSendCount(ctx, orgID, freeSendCount.UsedCount+1).
			Return(nil)

		Init(mockTransferRepo)

		mockOrgRepo := domain.NewMockStudioOrgRepo(ctrl)
		organization.Init(organization.InitParam{
			StudioOrgRepo: mockOrgRepo,
		})
		mockOrgRepo.EXPECT().
			GetOrgWallet(ctx, 1, "evm").
			Return(orgWallet, nil)
		mockOrgRepo.EXPECT().
			GetOrgWallet(ctx, orgID, "evm").
			Return(orgWallet, nil)

		result, kgErr := HandleFreeTransfer(ctx, orgID, chain, autoGasExchange)

		assert.Nil(t, kgErr)
		assert.Equal(t, expectedResult, result)
	})
}
