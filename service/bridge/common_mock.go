// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/service/bridge (interfaces: IRepo)

// Package bridge is a generated GoMock package.
package bridge

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	domain "github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// MockIRepo is a mock of IRepo interface.
type MockIRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIRepoMockRecorder
}

// MockIRepoMockRecorder is the mock recorder for MockIRepo.
type MockIRepoMockRecorder struct {
	mock *MockIRepo
}

// NewMockIRepo creates a new mock instance.
func NewMockIRepo(ctrl *gomock.Controller) *MockIRepo {
	mock := &MockIRepo{ctrl: ctrl}
	mock.recorder = &MockIRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRepo) EXPECT() *MockIRepoMockRecorder {
	return m.recorder
}

// BatchGetTokenPrices mocks base method.
func (m *MockIRepo) BatchGetTokenPrices(arg0 context.Context, arg1 []domain.ChainToken) (map[domain.ChainToken]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPrices", arg0, arg1)
	ret0, _ := ret[0].(map[domain.ChainToken]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPrices indicates an expected call of BatchGetTokenPrices.
func (mr *MockIRepoMockRecorder) BatchGetTokenPrices(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPrices", reflect.TypeOf((*MockIRepo)(nil).BatchGetTokenPrices), arg0, arg1)
}

// BatchGetTokenPricesIn24H mocks base method.
func (m *MockIRepo) BatchGetTokenPricesIn24H(arg0 context.Context, arg1 []domain.ChainToken) (map[domain.ChainToken][]*domain.PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPricesIn24H", arg0, arg1)
	ret0, _ := ret[0].(map[domain.ChainToken][]*domain.PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPricesIn24H indicates an expected call of BatchGetTokenPricesIn24H.
func (mr *MockIRepoMockRecorder) BatchGetTokenPricesIn24H(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPricesIn24H", reflect.TypeOf((*MockIRepo)(nil).BatchGetTokenPricesIn24H), arg0, arg1)
}

// CreateBridgeExternalAccount mocks base method.
func (m *MockIRepo) CreateBridgeExternalAccount(arg0 context.Context, arg1 *domain.CreateBridgeExternalAccountData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBridgeExternalAccount", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBridgeExternalAccount indicates an expected call of CreateBridgeExternalAccount.
func (mr *MockIRepoMockRecorder) CreateBridgeExternalAccount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBridgeExternalAccount", reflect.TypeOf((*MockIRepo)(nil).CreateBridgeExternalAccount), arg0, arg1)
}

// CreateBridgeOrganization mocks base method.
func (m *MockIRepo) CreateBridgeOrganization(arg0 context.Context, arg1 *domain.CreateBridgeOrganizationData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBridgeOrganization", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBridgeOrganization indicates an expected call of CreateBridgeOrganization.
func (mr *MockIRepoMockRecorder) CreateBridgeOrganization(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBridgeOrganization", reflect.TypeOf((*MockIRepo)(nil).CreateBridgeOrganization), arg0, arg1)
}

// CreateBridgeRecord mocks base method.
func (m *MockIRepo) CreateBridgeRecord(arg0 context.Context, arg1 *domain.BridgeRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBridgeRecord", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBridgeRecord indicates an expected call of CreateBridgeRecord.
func (mr *MockIRepoMockRecorder) CreateBridgeRecord(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBridgeRecord", reflect.TypeOf((*MockIRepo)(nil).CreateBridgeRecord), arg0, arg1)
}

// CreateBridgeTransfer mocks base method.
func (m *MockIRepo) CreateBridgeTransfer(arg0 context.Context, arg1 *domain.CreateBridgeTransferData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBridgeTransfer", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateBridgeTransfer indicates an expected call of CreateBridgeTransfer.
func (mr *MockIRepoMockRecorder) CreateBridgeTransfer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBridgeTransfer", reflect.TypeOf((*MockIRepo)(nil).CreateBridgeTransfer), arg0, arg1)
}

// GetAssetPrice mocks base method.
func (m *MockIRepo) GetAssetPrice(arg0 context.Context, arg1, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockIRepoMockRecorder) GetAssetPrice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockIRepo)(nil).GetAssetPrice), arg0, arg1, arg2)
}

// GetBridgeExternalAccountByID mocks base method.
func (m *MockIRepo) GetBridgeExternalAccountByID(arg0 context.Context, arg1 string) (*domain.BridgeExternalAccountData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBridgeExternalAccountByID", arg0, arg1)
	ret0, _ := ret[0].(*domain.BridgeExternalAccountData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBridgeExternalAccountByID indicates an expected call of GetBridgeExternalAccountByID.
func (mr *MockIRepoMockRecorder) GetBridgeExternalAccountByID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBridgeExternalAccountByID", reflect.TypeOf((*MockIRepo)(nil).GetBridgeExternalAccountByID), arg0, arg1)
}

// GetBridgeExternalAccountsByOrgID mocks base method.
func (m *MockIRepo) GetBridgeExternalAccountsByOrgID(arg0 context.Context, arg1 int) ([]*domain.BridgeExternalAccountData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBridgeExternalAccountsByOrgID", arg0, arg1)
	ret0, _ := ret[0].([]*domain.BridgeExternalAccountData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBridgeExternalAccountsByOrgID indicates an expected call of GetBridgeExternalAccountsByOrgID.
func (mr *MockIRepoMockRecorder) GetBridgeExternalAccountsByOrgID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBridgeExternalAccountsByOrgID", reflect.TypeOf((*MockIRepo)(nil).GetBridgeExternalAccountsByOrgID), arg0, arg1)
}

// GetBridgeOrganizationByOrgID mocks base method.
func (m *MockIRepo) GetBridgeOrganizationByOrgID(arg0 context.Context, arg1 int) (*domain.BridgeOrganizationData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBridgeOrganizationByOrgID", arg0, arg1)
	ret0, _ := ret[0].(*domain.BridgeOrganizationData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBridgeOrganizationByOrgID indicates an expected call of GetBridgeOrganizationByOrgID.
func (mr *MockIRepoMockRecorder) GetBridgeOrganizationByOrgID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBridgeOrganizationByOrgID", reflect.TypeOf((*MockIRepo)(nil).GetBridgeOrganizationByOrgID), arg0, arg1)
}

// GetBridgeTransfersByOrgID mocks base method.
func (m *MockIRepo) GetBridgeTransfersByOrgID(arg0 context.Context, arg1 int) ([]*domain.BridgeTransferData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBridgeTransfersByOrgID", arg0, arg1)
	ret0, _ := ret[0].([]*domain.BridgeTransferData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBridgeTransfersByOrgID indicates an expected call of GetBridgeTransfersByOrgID.
func (mr *MockIRepoMockRecorder) GetBridgeTransfersByOrgID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBridgeTransfersByOrgID", reflect.TypeOf((*MockIRepo)(nil).GetBridgeTransfersByOrgID), arg0, arg1)
}

// GetNativeAssetPrice mocks base method.
func (m *MockIRepo) GetNativeAssetPrice(arg0 context.Context, arg1 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockIRepoMockRecorder) GetNativeAssetPrice(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockIRepo)(nil).GetNativeAssetPrice), arg0, arg1)
}

// GetProfitRate mocks base method.
func (m *MockIRepo) GetProfitRate(arg0 context.Context, arg1 int, arg2 domain.ProfitRateServiceType) (*domain.AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*domain.AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRate indicates an expected call of GetProfitRate.
func (mr *MockIRepoMockRecorder) GetProfitRate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRate", reflect.TypeOf((*MockIRepo)(nil).GetProfitRate), arg0, arg1, arg2)
}

// GetProfitRates mocks base method.
func (m *MockIRepo) GetProfitRates(arg0 context.Context, arg1 int) ([]*domain.AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRates", arg0, arg1)
	ret0, _ := ret[0].([]*domain.AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRates indicates an expected call of GetProfitRates.
func (mr *MockIRepoMockRecorder) GetProfitRates(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRates", reflect.TypeOf((*MockIRepo)(nil).GetProfitRates), arg0, arg1)
}

// GetTokenPrice mocks base method.
func (m *MockIRepo) GetTokenPrice(arg0 context.Context, arg1 domain.Chain, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPrice indicates an expected call of GetTokenPrice.
func (mr *MockIRepoMockRecorder) GetTokenPrice(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPrice", reflect.TypeOf((*MockIRepo)(nil).GetTokenPrice), arg0, arg1, arg2)
}

// GetTokenPricesIn24H mocks base method.
func (m *MockIRepo) GetTokenPricesIn24H(arg0 context.Context, arg1 domain.Chain, arg2 string) ([]*domain.PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPricesIn24H", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*domain.PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPricesIn24H indicates an expected call of GetTokenPricesIn24H.
func (mr *MockIRepoMockRecorder) GetTokenPricesIn24H(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPricesIn24H", reflect.TypeOf((*MockIRepo)(nil).GetTokenPricesIn24H), arg0, arg1, arg2)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockIRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*domain.OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*domain.OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockIRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockIRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// UpdateBridgeRecord mocks base method.
func (m *MockIRepo) UpdateBridgeRecord(arg0 context.Context, arg1 string, arg2 *domain.BridgeRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBridgeRecord", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBridgeRecord indicates an expected call of UpdateBridgeRecord.
func (mr *MockIRepoMockRecorder) UpdateBridgeRecord(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBridgeRecord", reflect.TypeOf((*MockIRepo)(nil).UpdateBridgeRecord), arg0, arg1, arg2)
}

// UpsertProfitRate mocks base method.
func (m *MockIRepo) UpsertProfitRate(arg0 context.Context, arg1 domain.UpsertProfitRateParams) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertProfitRate", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// UpsertProfitRate indicates an expected call of UpsertProfitRate.
func (mr *MockIRepoMockRecorder) UpsertProfitRate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertProfitRate", reflect.TypeOf((*MockIRepo)(nil).UpsertProfitRate), arg0, arg1)
}
