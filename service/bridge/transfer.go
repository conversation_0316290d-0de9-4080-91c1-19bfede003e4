package bridge

import (
	"context"
	"fmt"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// transferService implements domain.BridgeTransferService
type transferService struct {
	repo             domain.BridgeTransferRepo
	bridgeOrgRepo    domain.BridgeOrganizationRepo
	apiClient        domain.BridgeAPIClient
}

// CreateBridgeTransfer creates a bridge transfer by calling Bridge API and saving the result
func  CreateBridgeTransfer(ctx context.Context, req *domain.CreateBridgeTransferRequest) (*domain.CreateBridgeTransferResponse, *code.KGError) {
	kglog.InfoWithDataCtx(ctx, "Creating bridge transfer", map[string]interface{}{
		"organization_id": req.OrganizationID,
		"amount": req.Amount,
		"source_payment_rail": req.Source.PaymentRail,
		"source_currency": req.Source.Currency,
		"source_from_address": req.Source.FromAddress,
		"dest_payment_rail": req.Destination.PaymentRail,
		"dest_currency": req.Destination.Currency,
		"dest_external_account_id": req.Destination.ExternalAccountID,
	})

	// Get customer_id from bridge organization
	bridgeOrg, err := r.GetBridgeOrganizationByOrgID(ctx, req.OrganizationID)
	if err != nil {
		if err == domain.ErrRecordNotFound {
			kglog.ErrorWithDataCtx(ctx, "Bridge organization not found", map[string]interface{}{
				"organization_id": req.OrganizationID,
			})
			return nil, code.NewKGError(code.ParamIncorrect, 404, fmt.Errorf("bridge organization not found for organization ID: %d", req.OrganizationID), map[string]interface{}{
				"organization_id": req.OrganizationID,
			})
		}
		kglog.ErrorWithDataCtx(ctx, "Failed to get bridge organization", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"error":          err.Error(),
		})
		return nil, code.NewKGError(code.DBError, 500, err, nil)
	}

	customerID := bridgeOrg.CustomerID

	// Call Bridge API to create transfer
	bridgeReq := &domain.BridgeCreateTransferRequest{
		Amount:      req.Amount,
		OnBehalfOf:  customerID,
		Source:      req.Source,
		Destination: req.Destination,
	}

	bridgeResp, err := apiClient.CreateTransfer(ctx, bridgeReq)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to create transfer via Bridge API", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"customer_id":     customerID,
			"amount":          req.Amount,
			"error":          err.Error(),
		})
		return nil, code.NewKGError(code.ExternalAPIError, 500, err, nil)
	}

	// Parse created_at timestamp
	createdAt, err := time.Parse(time.RFC3339, bridgeResp.CreatedAt)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Failed to parse created_at timestamp", map[string]interface{}{
			"created_at": bridgeResp.CreatedAt,
			"error":      err.Error(),
		})
		createdAt = time.Now()
	}

	// Parse updated_at timestamp
	updatedAt, err := time.Parse(time.RFC3339, bridgeResp.UpdatedAt)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Failed to parse updated_at timestamp", map[string]interface{}{
			"updated_at": bridgeResp.UpdatedAt,
			"error":      err.Error(),
		})
		updatedAt = time.Now()
	}

	// Map chain from payment rail
	chain := mapPaymentRailToChain(req.Source.PaymentRail)
	
	// Get deposit address from source deposit instructions
	depositToAddress := ""
	if bridgeResp.SourceDepositInstructions != nil {
		depositToAddress = bridgeResp.SourceDepositInstructions.ToAddress
	}

	// Save to database
	transferData := &domain.CreateBridgeTransferData{
		BridgeTransferID:        bridgeResp.ID,
		OrganizationID:          req.OrganizationID,
		BridgeExternalAccountID: &req.Destination.ExternalAccountID,
		Chain:                   chain,
		FromAddress:             req.Source.FromAddress,
		Amount:                  bridgeResp.Amount,
		Currency:                bridgeResp.Currency,
		Status:                  bridgeResp.State,
		DepositToAddress:        depositToAddress,
	}

	if err := r.CreateBridgeTransfer(ctx, transferData); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to save bridge transfer to database", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"customer_id":     customerID,
			"transfer_id":     bridgeResp.ID,
			"error":          err.Error(),
		})
		return nil, code.NewKGError(code.DBError, 500, err, nil)
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created bridge transfer", map[string]interface{}{
		"organization_id": req.OrganizationID,
		"customer_id":     customerID,
		"transfer_id":     bridgeResp.ID,
		"state":           bridgeResp.State,
		"amount":          bridgeResp.Amount,
		"currency":        bridgeResp.Currency,
	})

	// Return response
	return &domain.CreateBridgeTransferResponse{
		ID:                        bridgeResp.ID,
		ClientReferenceID:         bridgeResp.ClientReferenceID,
		State:                     bridgeResp.State,
		OnBehalfOf:                bridgeResp.OnBehalfOf,
		Currency:                  bridgeResp.Currency,
		Amount:                    bridgeResp.Amount,
		DeveloperFee:              bridgeResp.DeveloperFee,
		Source:                    bridgeResp.Source,
		Destination:               bridgeResp.Destination,
		CreatedAt:                 createdAt,
		UpdatedAt:                 updatedAt,
		Receipt:                   bridgeResp.Receipt,
		SourceDepositInstructions: bridgeResp.SourceDepositInstructions,
	}, nil
}

// GetBridgeTransfersByOrgID retrieves all bridge transfers for an organization
func  GetBridgeTransfersByOrgID(ctx context.Context, organizationID int) ([]*domain.BridgeTransferData, *code.KGError) {
	kglog.InfoWithDataCtx(ctx, "Getting bridge transfers by organization ID", map[string]interface{}{
		"organization_id": organizationID,
	})

	transfers, err := r.GetBridgeTransfersByOrgID(ctx, organizationID)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get bridge transfers", map[string]interface{}{
			"organization_id": organizationID,
			"error":          err.Error(),
		})
		return nil, code.NewKGError(code.DBError, 500, err, nil)
	}

	kglog.InfoWithDataCtx(ctx, "Successfully retrieved bridge transfers", map[string]interface{}{
		"organization_id": organizationID,
		"count":          len(transfers),
	})

	return transfers, nil
}

// mapPaymentRailToChain maps payment rail to chain name
func mapPaymentRailToChain(paymentRail string) string {
	switch paymentRail {
	case "arbitrum":
		return "arbitrum"
	case "optimism":
		return "optimism"
	case "base":
		return "base"
	default:
		return paymentRail
	}
}
