package bridge

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// CreateBridgeOrganization creates a bridge organization by calling Bridge API and saving the result
func CreateBridgeOrganization(ctx context.Context, req *domain.CreateBridgeOrganizationRequest) (*domain.CreateBridgeOrganizationResponse, *code.KGError) {
	kglog.InfoWithDataCtx(ctx, "Creating bridge organization", map[string]interface{}{
		"organization_id": req.OrganizationID,
		"email":          req.Email,
		"full_name":      req.FullName,
	})

	// Check if bridge organization already exists
	existingOrg, err := r.GetBridgeOrganizationByOrgID(ctx, req.OrganizationID)
	if err != nil && err != domain.ErrRecordNotFound {
		kglog.ErrorWithDataCtx(ctx, "Failed to check existing bridge organization", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"error":          err.Error(),
		})
		return nil, code.NewKGError(code.DBError, 500, err, nil)
	}

	if existingOrg != nil {
		kglog.WarningWithDataCtx(ctx, "Bridge organization already exists", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"customer_id":     existingOrg.CustomerID,
		})
		return nil, code.NewKGError(code.BridgeOrganizationAlreadyExists, 400, fmt.Errorf("bridge organization already exists"), map[string]interface{}{
			"organization_id": req.OrganizationID,
		})
	}

	// Call Bridge API to create KYC link
	bridgeReq := &domain.BridgeCreateKYCLinkRequest{
		FullName: req.FullName,
		Email:    req.Email,
		Type:     "business", // Default to business as specified
	}

	bridgeResp, err := apiClient.CreateKYCLink(ctx, bridgeReq)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to create KYC link via Bridge API", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"error":          err.Error(),
		})
		
		// Check if this is a parameter validation error from Bridge API
		if strings.Contains(err.Error(), "Bridge API returned parameter validation error") {
			return nil, code.NewKGError(code.ParamIncorrect, 400, err, map[string]interface{}{
				"organization_id": req.OrganizationID,
			})
		}
		
		return nil, code.NewKGError(code.ExternalAPIError, 500, err, nil)
	}

	// Parse created_at timestamp
	createdAt, err := time.Parse(time.RFC3339, bridgeResp.CreatedAt)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Failed to parse created_at timestamp", map[string]interface{}{
			"created_at": bridgeResp.CreatedAt,
			"error":      err.Error(),
		})
		createdAt = time.Now()
	}

	// Save to database
	bridgeOrgData := &domain.CreateBridgeOrganizationData{
		OrganizationID:   req.OrganizationID,
		CustomerID:       bridgeResp.CustomerID,
		FullName:         bridgeResp.FullName,
		Email:            bridgeResp.Email,
		Type:             bridgeResp.Type,
		KYCLink:          &bridgeResp.KYCLink,
		TOSLink:          &bridgeResp.TOSLink,
		KYCStatus:        bridgeResp.KYCStatus,
		TOSStatus:        bridgeResp.TOSStatus,
		RejectionReasons: bridgeResp.RejectionReasons,
	}

	if err := r.CreateBridgeOrganization(ctx, bridgeOrgData); err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to save bridge organization to database", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"customer_id":     bridgeResp.CustomerID,
			"error":          err.Error(),
		})
		return nil, code.NewKGError(code.DBError, 500, err, nil)
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created bridge organization", map[string]interface{}{
		"organization_id": req.OrganizationID,
		"customer_id":     bridgeResp.CustomerID,
		"kyc_status":      bridgeResp.KYCStatus,
		"tos_status":      bridgeResp.TOSStatus,
	})

	// Return response
	return &domain.CreateBridgeOrganizationResponse{
		OrganizationID:   req.OrganizationID,
		CustomerID:       bridgeResp.CustomerID,
		FullName:         bridgeResp.FullName,
		Email:            bridgeResp.Email,
		Type:             bridgeResp.Type,
		KYCLink:          &bridgeResp.KYCLink,
		TOSLink:          &bridgeResp.TOSLink,
		KYCStatus:        bridgeResp.KYCStatus,
		TOSStatus:        bridgeResp.TOSStatus,
		RejectionReasons: bridgeResp.RejectionReasons,
		CreatedAt:        createdAt,
	}, nil
}

// GetBridgeOrganization retrieves bridge organization information by organization ID
func GetBridgeOrganization(ctx context.Context, organizationID int) (*domain.BridgeOrganizationData, *code.KGError) {
	kglog.InfoWithDataCtx(ctx, "Getting bridge organization", map[string]interface{}{
		"organization_id": organizationID,
	})

	bridgeOrg, err := r.GetBridgeOrganizationByOrgID(ctx, organizationID)
	if err != nil {
		if err == domain.ErrRecordNotFound {
			kglog.WarningWithDataCtx(ctx, "Bridge organization not found", map[string]interface{}{
				"organization_id": organizationID,
			})
			return nil, code.NewKGError(code.RecordNotFound, 404, err, map[string]interface{}{
				"organization_id": organizationID,
			})
		}
		
		kglog.ErrorWithDataCtx(ctx, "Failed to get bridge organization", map[string]interface{}{
			"organization_id": organizationID,
			"error":          err.Error(),
		})
		return nil, code.NewKGError(code.DBError, 500, err, nil)
	}

	kglog.InfoWithDataCtx(ctx, "Successfully retrieved bridge organization", map[string]interface{}{
		"organization_id": organizationID,
		"customer_id":     bridgeOrg.CustomerID,
	})

	return bridgeOrg, nil
}
