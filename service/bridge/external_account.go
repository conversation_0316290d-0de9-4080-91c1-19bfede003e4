package bridge

import (
	"context"
	"fmt"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
)

// CreateBridgeExternalAccount creates a bridge external account by calling Bridge API and saving the result
//
// Duplicate Handling:
// If Bridge API returns a "duplicate_external_account" error, the function will:
// 1. Parse the error response to extract the existing account ID
// 2. Fetch the full account details from Bridge API using the existing ID
// 3. Check if the account exists in our database
// 4. Save the account to our database if it doesn't exist locally
// 5. Return the existing account details as if it was successfully created
//
// This ensures idempotent behavior - calling this function multiple times with the same
// account details will always return the same result without error.
func CreateBridgeExternalAccount(ctx context.Context, req *domain.CreateBridgeExternalAccountRequest) (*domain.CreateBridgeExternalAccountResponse, *code.KGError) {
	kglog.InfoWithDataCtx(ctx, "Creating bridge external account", map[string]interface{}{
		"organization_id":    req.OrganizationID,
		"currency":           req.Currency,
		"bank_name":          req.BankName,
		"account_owner_name": req.AccountOwnerName,
		"account_owner_type": req.AccountOwnerType,
		"account_type":       req.AccountType,
	})

	// Get customer_id from bridge organization
	bridgeOrg, err := r.GetBridgeOrganizationByOrgID(ctx, req.OrganizationID)
	if err != nil {
		if err == domain.ErrRecordNotFound {
			kglog.ErrorWithDataCtx(ctx, "Bridge organization not found", map[string]interface{}{
				"organization_id": req.OrganizationID,
			})
			return nil, code.NewKGError(code.ParamIncorrect, 404, fmt.Errorf("bridge organization not found for organization ID: %d", req.OrganizationID), map[string]interface{}{
				"organization_id": req.OrganizationID,
			})
		}
		kglog.ErrorWithDataCtx(ctx, "Failed to get bridge organization", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"error":           err.Error(),
		})
		return nil, code.NewKGError(code.DBError, 500, err, nil)
	}

	customerID := bridgeOrg.CustomerID

	// Call Bridge API to create external account
	bridgeReq := &domain.BridgeCreateExternalAccountRequest{
		Currency:         req.Currency,
		BankName:         req.BankName,
		AccountOwnerName: req.AccountOwnerName,
		AccountOwnerType: req.AccountOwnerType,
		AccountType:      req.AccountType,
		BusinessName:     req.BusinessName,
		Swift:            req.Swift,
		US:               req.US,
		Address:          req.Address,
	}

	// For unknown account type, set root level account_number from swift details
	if req.AccountType == "unknown" && req.Swift != nil && req.Swift.Account != nil {
		bridgeReq.AccountNumber = &req.Swift.Account.AccountNumber
	}

	bridgeResp, err := apiClient.CreateExternalAccount(ctx, customerID, bridgeReq)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to create external account via Bridge API", map[string]interface{}{
			"organization_id": req.OrganizationID,
			"customer_id":     customerID,
			"error":           err.Error(),
		})
		return nil, code.NewKGError(code.ExternalAPIError, 500, err, nil)
	}

	// Parse created_at timestamp
	createdAt, err := time.Parse(time.RFC3339, bridgeResp.CreatedAt)
	if err != nil {
		kglog.WarningWithDataCtx(ctx, "Failed to parse created_at timestamp", map[string]interface{}{
			"created_at": bridgeResp.CreatedAt,
			"error":      err.Error(),
		})
		createdAt = time.Now()
	}

	// Extract account information
	var accountLast4, accountBIC *string
	if bridgeResp.Account != nil {
		if bridgeResp.Account.Last4 != "" {
			accountLast4 = &bridgeResp.Account.Last4
		}
		if bridgeResp.Account.BIC != "" {
			accountBIC = &bridgeResp.Account.BIC
		}
	}

	// Save to database (check if it already exists first)
	externalAccountData := &domain.CreateBridgeExternalAccountData{
		BridgeExternalAccountID: bridgeResp.ID, // Map Bridge API ID to BridgeExternalAccountID
		CustomerID:              bridgeResp.CustomerID,
		OrganizationID:          req.OrganizationID,
		BankName:                bridgeResp.BankName,
		AccountOwnerName:        bridgeResp.AccountOwnerName,
		Active:                  bridgeResp.Active,
		Currency:                bridgeResp.Currency,
		AccountOwnerType:        bridgeResp.AccountOwnerType,
		AccountType:             bridgeResp.AccountType,
		BusinessName:            bridgeResp.BusinessName,
		AccountLast4:            accountLast4,
		AccountBIC:              accountBIC,
	}

	// Check if external account already exists in our database
	existingAccount, err := r.GetBridgeExternalAccountByID(ctx, bridgeResp.ID)
	if err != nil && err != domain.ErrRecordNotFound {
		kglog.ErrorWithDataCtx(ctx, "Failed to check existing bridge external account", map[string]interface{}{
			"organization_id":     req.OrganizationID,
			"customer_id":         customerID,
			"external_account_id": bridgeResp.ID,
			"error":               err.Error(),
		})
		return nil, code.NewKGError(code.DBError, 500, err, nil)
	}

	if existingAccount != nil {
		kglog.InfoWithDataCtx(ctx, "Bridge external account already exists in database", map[string]interface{}{
			"organization_id":     req.OrganizationID,
			"customer_id":         customerID,
			"external_account_id": bridgeResp.ID,
		})
	} else {
		// Create new record only if it doesn't exist
		if err := r.CreateBridgeExternalAccount(ctx, externalAccountData); err != nil {
			kglog.ErrorWithDataCtx(ctx, "Failed to save bridge external account to database", map[string]interface{}{
				"organization_id":     req.OrganizationID,
				"customer_id":         customerID,
				"external_account_id": bridgeResp.ID,
				"error":               err.Error(),
			})
			return nil, code.NewKGError(code.DBError, 500, err, nil)
		}

		kglog.InfoWithDataCtx(ctx, "Successfully saved bridge external account to database", map[string]interface{}{
			"organization_id":     req.OrganizationID,
			"customer_id":         customerID,
			"external_account_id": bridgeResp.ID,
		})
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created bridge external account", map[string]interface{}{
		"organization_id":     req.OrganizationID,
		"customer_id":         customerID,
		"external_account_id": bridgeResp.ID,
		"account_owner_name":  bridgeResp.AccountOwnerName,
		"currency":            bridgeResp.Currency,
		"active":              bridgeResp.Active,
	})

	// Return response
	return &domain.CreateBridgeExternalAccountResponse{
		ID:               bridgeResp.ID,
		CustomerID:       bridgeResp.CustomerID,
		OrganizationID:   req.OrganizationID,
		BankName:         bridgeResp.BankName,
		AccountOwnerName: bridgeResp.AccountOwnerName,
		Active:           bridgeResp.Active,
		Currency:         bridgeResp.Currency,
		AccountOwnerType: bridgeResp.AccountOwnerType,
		AccountType:      bridgeResp.AccountType,
		BusinessName:     bridgeResp.BusinessName,
		Account:          bridgeResp.Account,
		CreatedAt:        createdAt,
	}, nil
}

// GetBridgeExternalAccountsByOrgID retrieves all bridge external accounts for an organization
func GetBridgeExternalAccountsByOrgID(ctx context.Context, organizationID int) ([]*domain.BridgeExternalAccountData, *code.KGError) {
	kglog.InfoWithDataCtx(ctx, "Getting bridge external accounts by organization ID", map[string]interface{}{
		"organization_id": organizationID,
	})

	accounts, err := r.GetBridgeExternalAccountsByOrgID(ctx, organizationID)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get bridge external accounts", map[string]interface{}{
			"organization_id": organizationID,
			"error":           err.Error(),
		})
		return nil, code.NewKGError(code.DBError, 500, err, nil)
	}

	kglog.InfoWithDataCtx(ctx, "Successfully retrieved bridge external accounts", map[string]interface{}{
		"organization_id": organizationID,
		"count":           len(accounts),
	})

	return accounts, nil
}

// GetBridgeExternalAccountByID retrieves a specific bridge external account by ID
func GetBridgeExternalAccountByID(ctx context.Context, externalAccountID string) (*domain.BridgeExternalAccountData, *code.KGError) {
	kglog.InfoWithDataCtx(ctx, "Getting bridge external account by ID", map[string]interface{}{
		"external_account_id": externalAccountID,
	})

	account, err := r.GetBridgeExternalAccountByID(ctx, externalAccountID)
	if err != nil {
		if err == domain.ErrRecordNotFound {
			kglog.WarningWithDataCtx(ctx, "Bridge external account not found", map[string]interface{}{
				"external_account_id": externalAccountID,
			})
			return nil, code.NewKGError(code.ParamIncorrect, 404, fmt.Errorf("bridge external account not found"), map[string]interface{}{
				"external_account_id": externalAccountID,
			})
		}
		kglog.ErrorWithDataCtx(ctx, "Failed to get bridge external account", map[string]interface{}{
			"external_account_id": externalAccountID,
			"error":               err.Error(),
		})
		return nil, code.NewKGError(code.DBError, 500, err, nil)
	}

	kglog.InfoWithDataCtx(ctx, "Successfully retrieved bridge external account", map[string]interface{}{
		"external_account_id": externalAccountID,
		"organization_id":     account.OrganizationID,
		"customer_id":         account.CustomerID,
	})

	return account, nil
}
