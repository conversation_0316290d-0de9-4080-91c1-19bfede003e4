package bridge

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	bridgeapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/bridge-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// setupBridgeOrganizationTest sets up test environment for bridge organization tests
func setupBridgeOrganizationTest(t *testing.T) domain.BridgeOrganizationRepo {
	// Reset database
	rdb.Reset()
	
	// Initialize dependencies
	bridgeapi.InitDefault()
	
	// Create test studio organizations
	err := rdb.Get().Create([]model.StudioOrganization{
		{ID: 1, Name: "Test Organization 1"},
		{ID: 2, Name: "Test Organization 2"},
	}).Error
	require.NoError(t, err)
	
	// Get repository
	repo := rdb.GormRepo()
	
	// Initialize bridge service with dependencies
	apiClient := bridgeapi.Get()
	Init(repo, apiClient)
	
	return repo
}

func TestBridgeOrganizationService_CreateBridgeOrganization_Success(t *testing.T) {
	repo := setupBridgeOrganizationTest(t)
	ctx := context.Background()
	
	// Test case: successful creation
	req := &domain.CreateBridgeOrganizationRequest{
		OrganizationID: 1,
		Email:          fmt.Sprintf("<EMAIL>", time.Now().Unix()),
		FullName:       "Test User",
	}
	
	t.Run("create_new_bridge_organization", func(t *testing.T) {
		// Execute
		resp, kgErr := CreateBridgeOrganization(ctx, req)
		
		// Assert
		require.Nil(t, kgErr, "Should not return error")
		require.NotNil(t, resp, "Response should not be nil")
		
		// Verify response fields
		assert.NotEmpty(t, resp.CustomerID, "CustomerID should not be empty")
		assert.Equal(t, req.Email, resp.Email, "Email should match")
		assert.Equal(t, req.FullName, resp.FullName, "FullName should match")
		assert.Equal(t, "business", resp.Type, "Type should be business")
		assert.NotEmpty(t, resp.KYCLink, "KYCLink should not be empty")
		assert.NotEmpty(t, resp.TOSLink, "TOSLink should not be empty")
		assert.NotEmpty(t, resp.CreatedAt, "CreatedAt should not be empty")
		
		// Verify data was saved to database
		savedOrg, err := repo.GetBridgeOrganizationByOrgID(ctx, req.OrganizationID)
		require.NoError(t, err, "Should be able to retrieve saved organization")
		assert.Equal(t, resp.CustomerID, savedOrg.CustomerID, "Saved CustomerID should match")
		assert.Equal(t, req.Email, savedOrg.Email, "Saved Email should match")
		assert.Equal(t, req.FullName, savedOrg.FullName, "Saved FullName should match")
		
		t.Logf("Successfully created bridge organization with CustomerID: %s", resp.CustomerID)
		if resp.KYCLink != nil {
			t.Logf("KYC Link: %s", *resp.KYCLink)
		}
		if resp.TOSLink != nil {
			t.Logf("TOS Link: %s", *resp.TOSLink)
		}
	})
}

func TestBridgeOrganizationService_CreateBridgeOrganization_AlreadyExists(t *testing.T) {
	repo := setupBridgeOrganizationTest(t)
	ctx := context.Background()
	
	// Create existing bridge organization
	kycLink := "https://example.com/kyc"
	tosLink := "https://example.com/tos"
	existingOrg := &domain.CreateBridgeOrganizationData{
		OrganizationID:   1,
		CustomerID:       "existing-customer-id",
		FullName:         "Existing User",
		Email:            "<EMAIL>",
		Type:             "business",
		KYCLink:          &kycLink,
		TOSLink:          &tosLink,
		KYCStatus:        "not_started",
		TOSStatus:        "pending",
		RejectionReasons: []string{},
	}
	err := repo.CreateBridgeOrganization(ctx, existingOrg)
	require.NoError(t, err)
	
	// Test case: attempt to create duplicate
	req := &domain.CreateBridgeOrganizationRequest{
		OrganizationID: 1,
		Email:          "<EMAIL>",
		FullName:       "New User",
	}
	
	t.Run("create_duplicate_bridge_organization", func(t *testing.T) {
		// Execute
		resp, kgErr := CreateBridgeOrganization(ctx, req)
		
		// Assert
		require.NotNil(t, kgErr, "Should return error for duplicate organization")
		assert.Equal(t, code.BridgeOrganizationAlreadyExists, kgErr.Code, "Should return correct error code")
		assert.Nil(t, resp, "Response should be nil for error case")
	})
}

func TestBridgeOrganizationService_CreateBridgeOrganization_InvalidOrganization(t *testing.T) {
	setupBridgeOrganizationTest(t)
	ctx := context.Background()
	
	// Test case: invalid organization ID
	req := &domain.CreateBridgeOrganizationRequest{
		OrganizationID: 999, // Non-existent organization
		Email:          "<EMAIL>",
		FullName:       "Test User",
	}
	
	t.Run("create_with_invalid_organization", func(t *testing.T) {
		// Execute
		resp, kgErr := CreateBridgeOrganization(ctx, req)
		
		// Assert
		require.NotNil(t, kgErr, "Should return error for invalid organization")
		assert.Nil(t, resp, "Response should be nil for error case")
	})
}

func TestBridgeOrganizationService_CreateBridgeOrganization_ExternalAPIError(t *testing.T) {
	// This test will verify error handling when external API fails
	// We can test this by using invalid API credentials or malformed data
	
	// Reset and setup with potentially invalid config
	rdb.Reset()
	
	// Create test organization
	err := rdb.Get().Create([]model.StudioOrganization{
		{ID: 1, Name: "Test Organization"},
	}).Error
	require.NoError(t, err)
	
	// Get repository and initialize
	repo := rdb.GormRepo()
	
	// Use real API client that might fail with bad data
	bridgeapi.InitDefault()
	apiClient := bridgeapi.Get()
	Init(repo, apiClient)
	
	ctx := context.Background()
	req := &domain.CreateBridgeOrganizationRequest{
		OrganizationID: 1,
		Email:          "invalid-email", // Invalid email format
		FullName:       "",              // Empty full name
	}
	
	t.Run("external_api_error", func(t *testing.T) {
		// Execute
		resp, kgErr := CreateBridgeOrganization(ctx, req)
		
		// Assert
		require.NotNil(t, kgErr, "Should return error for invalid input")
		assert.Nil(t, resp, "Response should be nil for error case")
		
		t.Logf("Received expected error: %s", kgErr.String())
	})
}

func TestBridgeOrganizationService_CreateBridgeOrganization_RealAPI(t *testing.T) {
	// This test makes a real call to the Bridge API
	// Skip if we don't want to make external calls
	if testing.Short() {
		t.Skip("Skipping external API test in short mode")
	}
	
	// Verify that API credentials are configured
	apiKey := config.GetString("BRIDGE_API_KEY")
	baseURL := config.GetString("BRIDGE_API_URL")
	
	if apiKey == "" || baseURL == "" {
		t.Skip("Bridge API credentials not configured")
	}
	
	setupBridgeOrganizationTest(t)
	ctx := context.Background()
	
	// Use a unique email to avoid conflicts
	uniqueEmail := fmt.Sprintf("<EMAIL>", time.Now().Unix())
	
	req := &domain.CreateBridgeOrganizationRequest{
		OrganizationID: 1,
		Email:          uniqueEmail,
		FullName:       "API Test User",
	}
	
	t.Run("real_api_call", func(t *testing.T) {
		// Execute real API call
		resp, kgErr := CreateBridgeOrganization(ctx, req)
		
		// If this is a real test environment, we expect success
		// If it's a sandbox with restrictions, we might get specific errors
		if kgErr != nil {
			// Log the error for debugging
			t.Logf("API call failed (this might be expected in test environment): %s", kgErr.String())
			
			// For some test environments, certain errors are expected
			// We can still verify that the error is handled properly
			assert.NotNil(t, kgErr, "Error should be properly structured")
			return
		}
		
		// If successful, verify the response
		require.NotNil(t, resp, "Response should not be nil for successful call")
		assert.NotEmpty(t, resp.CustomerID, "CustomerID should be returned")
		assert.Equal(t, uniqueEmail, resp.Email, "Email should match request")
		assert.Equal(t, "API Test User", resp.FullName, "FullName should match request")
		assert.Equal(t, "business", resp.Type, "Type should be business")
		
		// Verify that Bridge API returned proper links
		assert.NotEmpty(t, resp.KYCLink, "KYC link should be provided")
		assert.NotEmpty(t, resp.TOSLink, "TOS link should be provided")
		
		// Log success details
		t.Logf("✅ Real API call successful!")
		t.Logf("CustomerID: %s", resp.CustomerID)
		if resp.KYCLink != nil {
			t.Logf("KYC Link: %s", *resp.KYCLink)
		}
		if resp.TOSLink != nil {
			t.Logf("TOS Link: %s", *resp.TOSLink)
		}
		
		// Verify response can be marshaled to JSON (API contract test)
		jsonData, err := json.Marshal(resp)
		assert.NoError(t, err, "Response should be JSON serializable")
		assert.NotEmpty(t, jsonData, "JSON data should not be empty")
		
		t.Logf("Response JSON: %s", string(jsonData))
	})
}

func TestBridgeOrganizationService_GetBridgeOrganization_Success(t *testing.T) {
	repo := setupBridgeOrganizationTest(t)
	ctx := context.Background()
	
	// Create a bridge organization first
	kycLink := "https://example.com/kyc"
	tosLink := "https://example.com/tos"
	existingOrg := &domain.CreateBridgeOrganizationData{
		OrganizationID:   1,
		CustomerID:       "test-customer-id",
		FullName:         "Test User",
		Email:            "<EMAIL>",
		Type:             "business",
		KYCLink:          &kycLink,
		TOSLink:          &tosLink,
		KYCStatus:        "approved",
		TOSStatus:        "approved",
		RejectionReasons: []string{},
	}
	err := repo.CreateBridgeOrganization(ctx, existingOrg)
	require.NoError(t, err)
	
	t.Run("get_existing_bridge_organization", func(t *testing.T) {
		// Execute
		result, kgErr := GetBridgeOrganization(ctx, 1)
		
		// Assert
		require.Nil(t, kgErr, "Should not return error")
		require.NotNil(t, result, "Result should not be nil")
		
		// Verify returned data
		assert.Equal(t, existingOrg.CustomerID, result.CustomerID, "CustomerID should match")
		assert.Equal(t, existingOrg.FullName, result.FullName, "FullName should match")
		assert.Equal(t, existingOrg.Email, result.Email, "Email should match")
		assert.Equal(t, existingOrg.Type, result.Type, "Type should match")
		assert.Equal(t, existingOrg.KYCStatus, result.KYCStatus, "KYCStatus should match")
		assert.Equal(t, existingOrg.TOSStatus, result.TOSStatus, "TOSStatus should match")
	})
}

func TestBridgeOrganizationService_GetBridgeOrganization_NotFound(t *testing.T) {
	setupBridgeOrganizationTest(t)
	ctx := context.Background()
	
	t.Run("get_non_existent_bridge_organization", func(t *testing.T) {
		// Execute
		result, kgErr := GetBridgeOrganization(ctx, 999)
		
		// Assert
		require.NotNil(t, kgErr, "Should return error for non-existent organization")
		assert.Equal(t, code.RecordNotFound, kgErr.Code, "Should return correct error code")
		assert.Equal(t, 404, kgErr.HttpStatus, "Should return 404 status")
		assert.Nil(t, result, "Result should be nil for error case")
	})
}