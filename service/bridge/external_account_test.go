package bridge

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockBridgeAPIClient is a mock implementation of BridgeAPIClient
type MockBridgeAPIClient struct {
	ctrl     *gomock.Controller
	recorder *MockBridgeAPIClientMockRecorder
}

type MockBridgeAPIClientMockRecorder struct {
	mock *MockBridgeAPIClient
}

func NewMockBridgeAPIClient(ctrl *gomock.Controller) *MockBridgeAPIClient {
	mock := &MockBridgeAPIClient{ctrl: ctrl}
	mock.recorder = &MockBridgeAPIClientMockRecorder{mock}
	return mock
}

func (m *MockBridgeAPIClient) EXPECT() *MockBridgeAPIClientMockRecorder {
	return m.recorder
}

func (m *MockBridgeAPIClient) CreateKYCLink(ctx context.Context, req *domain.BridgeCreateKYCLinkRequest) (*domain.BridgeCreateKYCLinkResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateKYCLink", ctx, req)
	ret0, _ := ret[0].(*domain.BridgeCreateKYCLinkResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockBridgeAPIClientMockRecorder) CreateKYCLink(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateKYCLink", reflect.TypeOf((*MockBridgeAPIClient)(nil).CreateKYCLink), ctx, req)
}

func (m *MockBridgeAPIClient) CreateExternalAccount(ctx context.Context, customerID string, req *domain.BridgeCreateExternalAccountRequest) (*domain.BridgeCreateExternalAccountResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateExternalAccount", ctx, customerID, req)
	ret0, _ := ret[0].(*domain.BridgeCreateExternalAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockBridgeAPIClientMockRecorder) CreateExternalAccount(ctx, customerID, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateExternalAccount", reflect.TypeOf((*MockBridgeAPIClient)(nil).CreateExternalAccount), ctx, customerID, req)
}

func (m *MockBridgeAPIClient) CreateTransfer(ctx context.Context, req *domain.BridgeCreateTransferRequest) (*domain.BridgeCreateTransferResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTransfer", ctx, req)
	ret0, _ := ret[0].(*domain.BridgeCreateTransferResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockBridgeAPIClientMockRecorder) CreateTransfer(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTransfer", reflect.TypeOf((*MockBridgeAPIClient)(nil).CreateTransfer), ctx, req)
}

// setupMockBridgeExternalAccountTest sets up mock dependencies for bridge external account tests
func setupMockBridgeExternalAccountTest(t *testing.T) (*gomock.Controller, *MockIRepo, *MockBridgeAPIClient) {
	ctrl := gomock.NewController(t)
	
	mockRepo := NewMockIRepo(ctrl)
	mockAPIClient := NewMockBridgeAPIClient(ctrl)
	
	// Initialize bridge service with mock dependencies
	Init(mockRepo, mockAPIClient)
	
	return ctrl, mockRepo, mockAPIClient
}

func TestBridgeExternalAccountService_CreateBridgeExternalAccount_Success(t *testing.T) {
	ctrl, mockRepo, mockAPIClient := setupMockBridgeExternalAccountTest(t)
	defer ctrl.Finish()
	
	ctx := context.Background()
	
	req := &domain.CreateBridgeExternalAccountRequest{
		OrganizationID:   1,
		Currency:         "usd",
		BankName:         "Mega International Commercial Bank",
		AccountOwnerName: "KryptoGO Co., Ltd.",
		AccountOwnerType: "business",
		AccountType:      "unknown",
		BusinessName:     &[]string{"KryptoGO Co., Ltd."}[0],
		Swift: &domain.SwiftDetails{
			Account: &domain.SwiftAccount{
				AccountNumber: "***********",
				BIC:           "ICBCTWTP216",
			},
			Address: &domain.AddressDetails{
				Country:     "TWN",
				City:        "Taipei City",
				StreetLine1: "No. 333, Section 1",
				StreetLine2: "Keelung Rd, Xinyi District",
				PostalCode:  "110",
			},
			PurposeOfFunds:           []string{"invoice_for_goods_and_services"},
			Category:                 "supplier",
			ShortBusinessDescription: "operation",
		},
		Address: &domain.AddressDetails{
			Country:     "TWN",
			PostalCode:  "110",
			City:        "Taipei City",
			StreetLine1: "12F., No. 161, Songde Rd.",
			StreetLine2: "Xinyi Dist.",
		},
	}
	
	t.Run("create_new_external_account", func(t *testing.T) {
		// Mock bridge organization exists
		bridgeOrg := &domain.BridgeOrganizationData{
			OrganizationID: 1,
			CustomerID:     "test-customer-id",
			FullName:       "Test Organization",
			Email:          "<EMAIL>",
		}
		
		mockRepo.EXPECT().
			GetBridgeOrganizationByOrgID(ctx, req.OrganizationID).
			Return(bridgeOrg, nil)
		
		// Mock API response
		bridgeAPIResp := &domain.BridgeCreateExternalAccountResponse{
			ID:               "test-external-account-id",
			CustomerID:       "test-customer-id",
			BankName:         req.BankName,
			AccountOwnerName: req.AccountOwnerName,
			Active:           true,
			Currency:         req.Currency,
			AccountOwnerType: req.AccountOwnerType,
			AccountType:      req.AccountType,
			BusinessName:     req.BusinessName,
			Account: &domain.AccountInfo{
				Last4: "2345",
				BIC:   "ICBCTWTP216",
			},
			CreatedAt: time.Now().Format(time.RFC3339),
			UpdatedAt: time.Now().Format(time.RFC3339),
		}
		
		mockAPIClient.EXPECT().
			CreateExternalAccount(ctx, bridgeOrg.CustomerID, gomock.Any()).
			Return(bridgeAPIResp, nil)
		
		// Mock checking if external account already exists (should not exist)
		mockRepo.EXPECT().
			GetBridgeExternalAccountByID(ctx, bridgeAPIResp.ID).
			Return(nil, domain.ErrRecordNotFound)
		
		// Mock saving to database
		mockRepo.EXPECT().
			CreateBridgeExternalAccount(ctx, gomock.Any()).
			Return(nil)
		
		// Execute
		resp, kgErr := CreateBridgeExternalAccount(ctx, req)
		
		// Assert
		require.Nil(t, kgErr, "Should not return error")
		require.NotNil(t, resp, "Response should not be nil")
		
		// Verify response fields
		assert.Equal(t, bridgeAPIResp.ID, resp.ID, "ID should match")
		assert.Equal(t, bridgeAPIResp.CustomerID, resp.CustomerID, "CustomerID should match")
		assert.Equal(t, req.OrganizationID, resp.OrganizationID, "OrganizationID should match")
		assert.Equal(t, req.BankName, resp.BankName, "BankName should match")
		assert.Equal(t, req.AccountOwnerName, resp.AccountOwnerName, "AccountOwnerName should match")
		assert.Equal(t, req.Currency, resp.Currency, "Currency should match")
		assert.Equal(t, req.AccountOwnerType, resp.AccountOwnerType, "AccountOwnerType should match")
		assert.Equal(t, req.AccountType, resp.AccountType, "AccountType should match")
		assert.True(t, resp.Active, "Account should be active")
		
		t.Logf("Successfully created bridge external account with ID: %s", resp.ID)
	})
}

func TestBridgeExternalAccountService_CreateBridgeExternalAccount_BridgeOrgNotFound(t *testing.T) {
	ctrl, mockRepo, _ := setupMockBridgeExternalAccountTest(t)
	defer ctrl.Finish()
	
	ctx := context.Background()
	
	req := &domain.CreateBridgeExternalAccountRequest{
		OrganizationID: 999, // Non-existent organization
		Currency:       "usd",
		BankName:       "Test Bank",
	}
	
	t.Run("bridge_organization_not_found", func(t *testing.T) {
		// Mock bridge organization not found
		mockRepo.EXPECT().
			GetBridgeOrganizationByOrgID(ctx, req.OrganizationID).
			Return(nil, domain.ErrRecordNotFound)
		
		// Execute
		resp, kgErr := CreateBridgeExternalAccount(ctx, req)
		
		// Assert
		require.NotNil(t, kgErr, "Should return error for non-existent bridge organization")
		assert.Equal(t, code.ParamIncorrect, kgErr.Code, "Should return correct error code")
		assert.Equal(t, 404, kgErr.HttpStatus, "Should return 404 status")
		assert.Nil(t, resp, "Response should be nil for error case")
	})
}

func TestBridgeExternalAccountService_CreateBridgeExternalAccount_ExternalAPIError(t *testing.T) {
	ctrl, mockRepo, mockAPIClient := setupMockBridgeExternalAccountTest(t)
	defer ctrl.Finish()
	
	ctx := context.Background()
	
	req := &domain.CreateBridgeExternalAccountRequest{
		OrganizationID: 1,
		Currency:       "usd",
		BankName:       "Test Bank",
	}
	
	t.Run("external_api_error", func(t *testing.T) {
		// Mock bridge organization exists
		bridgeOrg := &domain.BridgeOrganizationData{
			OrganizationID: 1,
			CustomerID:     "test-customer-id",
		}
		
		mockRepo.EXPECT().
			GetBridgeOrganizationByOrgID(ctx, req.OrganizationID).
			Return(bridgeOrg, nil)
		
		// Mock API error
		mockAPIClient.EXPECT().
			CreateExternalAccount(ctx, bridgeOrg.CustomerID, gomock.Any()).
			Return(nil, fmt.Errorf("bridge API error"))
		
		// Execute
		resp, kgErr := CreateBridgeExternalAccount(ctx, req)
		
		// Assert
		require.NotNil(t, kgErr, "Should return error for API error")
		assert.Equal(t, code.ExternalAPIError, kgErr.Code, "Should return correct error code")
		assert.Nil(t, resp, "Response should be nil for error case")
		
		t.Logf("Received expected error: %s", kgErr.String())
	})
}

func TestBridgeExternalAccountService_CreateBridgeExternalAccount_AlreadyExists(t *testing.T) {
	ctrl, mockRepo, mockAPIClient := setupMockBridgeExternalAccountTest(t)
	defer ctrl.Finish()
	
	ctx := context.Background()
	
	req := &domain.CreateBridgeExternalAccountRequest{
		OrganizationID: 1,
		Currency:       "usd",
		BankName:       "Test Bank",
	}
	
	t.Run("external_account_already_exists", func(t *testing.T) {
		// Mock bridge organization exists
		bridgeOrg := &domain.BridgeOrganizationData{
			OrganizationID: 1,
			CustomerID:     "test-customer-id",
		}
		
		mockRepo.EXPECT().
			GetBridgeOrganizationByOrgID(ctx, req.OrganizationID).
			Return(bridgeOrg, nil)
		
		// Mock API response
		bridgeAPIResp := &domain.BridgeCreateExternalAccountResponse{
			ID:               "existing-external-account-id",
			CustomerID:       "test-customer-id",
			BankName:         req.BankName,
			AccountOwnerName: "Test User",
			Active:           true,
			Currency:         req.Currency,
			CreatedAt:        time.Now().Format(time.RFC3339),
			UpdatedAt:        time.Now().Format(time.RFC3339),
		}
		
		mockAPIClient.EXPECT().
			CreateExternalAccount(ctx, bridgeOrg.CustomerID, gomock.Any()).
			Return(bridgeAPIResp, nil)
		
		// Mock external account already exists in database
		existingAccount := &domain.BridgeExternalAccountData{
			BridgeExternalAccountID: bridgeAPIResp.ID,
			CustomerID:              bridgeAPIResp.CustomerID,
			OrganizationID:          req.OrganizationID,
		}
		
		mockRepo.EXPECT().
			GetBridgeExternalAccountByID(ctx, bridgeAPIResp.ID).
			Return(existingAccount, nil)
		
		// Execute
		resp, kgErr := CreateBridgeExternalAccount(ctx, req)
		
		// Assert - should still succeed since account exists
		require.Nil(t, kgErr, "Should not return error for existing account")
		require.NotNil(t, resp, "Response should not be nil")
		assert.Equal(t, bridgeAPIResp.ID, resp.ID, "ID should match")
		
		t.Logf("Successfully handled existing external account: %s", resp.ID)
	})
}

func TestBridgeExternalAccountService_CreateBridgeExternalAccount_SaveError(t *testing.T) {
	ctrl, mockRepo, mockAPIClient := setupMockBridgeExternalAccountTest(t)
	defer ctrl.Finish()
	
	ctx := context.Background()
	
	req := &domain.CreateBridgeExternalAccountRequest{
		OrganizationID: 1,
		Currency:       "usd",
		BankName:       "Test Bank",
	}
	
	t.Run("save_to_database_error", func(t *testing.T) {
		// Mock bridge organization exists
		bridgeOrg := &domain.BridgeOrganizationData{
			OrganizationID: 1,
			CustomerID:     "test-customer-id",
		}
		
		mockRepo.EXPECT().
			GetBridgeOrganizationByOrgID(ctx, req.OrganizationID).
			Return(bridgeOrg, nil)
		
		// Mock API response
		bridgeAPIResp := &domain.BridgeCreateExternalAccountResponse{
			ID:               "test-external-account-id",
			CustomerID:       "test-customer-id",
			BankName:         req.BankName,
			Active:           true,
			Currency:         req.Currency,
			CreatedAt:        time.Now().Format(time.RFC3339),
			UpdatedAt:        time.Now().Format(time.RFC3339),
		}
		
		mockAPIClient.EXPECT().
			CreateExternalAccount(ctx, bridgeOrg.CustomerID, gomock.Any()).
			Return(bridgeAPIResp, nil)
		
		// Mock checking if external account already exists (should not exist)
		mockRepo.EXPECT().
			GetBridgeExternalAccountByID(ctx, bridgeAPIResp.ID).
			Return(nil, domain.ErrRecordNotFound)
		
		// Mock save error
		mockRepo.EXPECT().
			CreateBridgeExternalAccount(ctx, gomock.Any()).
			Return(fmt.Errorf("database save error"))
		
		// Execute
		resp, kgErr := CreateBridgeExternalAccount(ctx, req)
		
		// Assert
		require.NotNil(t, kgErr, "Should return error for save error")
		assert.Equal(t, code.DBError, kgErr.Code, "Should return correct error code")
		assert.Nil(t, resp, "Response should be nil for error case")
	})
}

func TestBridgeExternalAccountService_GetBridgeExternalAccountsByOrgID_Success(t *testing.T) {
	ctrl, mockRepo, _ := setupMockBridgeExternalAccountTest(t)
	defer ctrl.Finish()
	
	ctx := context.Background()
	testOrgID := 1
	
	t.Run("get_external_accounts_by_org_id", func(t *testing.T) {
		// Mock external accounts
		expectedAccounts := []*domain.BridgeExternalAccountData{
			{
				BridgeExternalAccountID: "test-account-1",
				CustomerID:              "test-customer-id",
				OrganizationID:          testOrgID,
				BankName:                "Test Bank 1",
				AccountOwnerName:        "Test Owner 1",
				Active:                  true,
				Currency:                "usd",
				AccountOwnerType:        "business",
				AccountType:             "iban",
			},
			{
				BridgeExternalAccountID: "test-account-2",
				CustomerID:              "test-customer-id",
				OrganizationID:          testOrgID,
				BankName:                "Test Bank 2",
				AccountOwnerName:        "Test Owner 2",
				Active:                  true,
				Currency:                "usd",
				AccountOwnerType:        "individual",
				AccountType:             "us",
			},
		}
		
		mockRepo.EXPECT().
			GetBridgeExternalAccountsByOrgID(ctx, testOrgID).
			Return(expectedAccounts, nil)
		
		// Execute
		accounts, kgErr := GetBridgeExternalAccountsByOrgID(ctx, testOrgID)
		
		// Assert
		require.Nil(t, kgErr, "Should not return error")
		require.NotNil(t, accounts, "Accounts should not be nil")
		assert.Len(t, accounts, 2, "Should return 2 accounts")
		
		// Verify account details
		for i, account := range accounts {
			assert.Equal(t, expectedAccounts[i].BridgeExternalAccountID, account.BridgeExternalAccountID, "Account ID should match")
			assert.Equal(t, testOrgID, account.OrganizationID, "Organization ID should match")
			assert.NotEmpty(t, account.BankName, "Bank name should not be empty")
			assert.NotEmpty(t, account.AccountOwnerName, "Account owner name should not be empty")
		}
		
		t.Logf("✅ Successfully retrieved %d accounts for organization %d", len(accounts), testOrgID)
	})
}

func TestBridgeExternalAccountService_GetBridgeExternalAccountsByOrgID_Empty(t *testing.T) {
	ctrl, mockRepo, _ := setupMockBridgeExternalAccountTest(t)
	defer ctrl.Finish()
	
	ctx := context.Background()
	testOrgID := 777
	
	t.Run("get_external_accounts_empty_org", func(t *testing.T) {
		// Mock empty result
		mockRepo.EXPECT().
			GetBridgeExternalAccountsByOrgID(ctx, testOrgID).
			Return([]*domain.BridgeExternalAccountData{}, nil)
		
		// Execute
		accounts, kgErr := GetBridgeExternalAccountsByOrgID(ctx, testOrgID)
		
		// Assert
		require.Nil(t, kgErr, "Should not return error")
		require.NotNil(t, accounts, "Accounts should not be nil")
		assert.Len(t, accounts, 0, "Should return empty array for organization with no accounts")
		
		t.Logf("✅ Correctly returned empty array for organization with no accounts")
	})
}

func TestBridgeExternalAccountService_GetBridgeExternalAccountByID_Success(t *testing.T) {
	ctrl, mockRepo, _ := setupMockBridgeExternalAccountTest(t)
	defer ctrl.Finish()
	
	ctx := context.Background()
	accountID := "test-account-1"
	
	t.Run("get_external_account_by_id", func(t *testing.T) {
		// Mock external account
		expectedAccount := &domain.BridgeExternalAccountData{
			BridgeExternalAccountID: accountID,
			CustomerID:              "test-customer-id",
			OrganizationID:          1,
			BankName:                "Test Bank 1",
			AccountOwnerName:        "Test Owner 1",
			Active:                  true,
			Currency:                "usd",
			AccountOwnerType:        "business",
			AccountType:             "iban",
		}
		
		mockRepo.EXPECT().
			GetBridgeExternalAccountByID(ctx, accountID).
			Return(expectedAccount, nil)
		
		// Execute
		account, kgErr := GetBridgeExternalAccountByID(ctx, accountID)
		
		// Assert
		require.Nil(t, kgErr, "Should not return error")
		require.NotNil(t, account, "Account should not be nil")
		
		// Verify account details
		assert.Equal(t, accountID, account.BridgeExternalAccountID, "Account ID should match")
		assert.Equal(t, expectedAccount.OrganizationID, account.OrganizationID, "Organization ID should match")
		assert.Equal(t, expectedAccount.CustomerID, account.CustomerID, "Customer ID should match")
		assert.Equal(t, expectedAccount.BankName, account.BankName, "Bank name should match")
		assert.Equal(t, expectedAccount.AccountOwnerName, account.AccountOwnerName, "Account owner name should match")
		assert.Equal(t, expectedAccount.AccountOwnerType, account.AccountOwnerType, "Account owner type should match")
		assert.Equal(t, expectedAccount.AccountType, account.AccountType, "Account type should match")
		assert.True(t, account.Active, "Account should be active")
		
		t.Logf("✅ Successfully retrieved account: %s for organization %d", account.BridgeExternalAccountID, account.OrganizationID)
	})
}

func TestBridgeExternalAccountService_GetBridgeExternalAccountByID_NotFound(t *testing.T) {
	ctrl, mockRepo, _ := setupMockBridgeExternalAccountTest(t)
	defer ctrl.Finish()
	
	ctx := context.Background()
	accountID := "non-existent-account"
	
	t.Run("get_external_account_by_id_not_found", func(t *testing.T) {
		// Mock account not found
		mockRepo.EXPECT().
			GetBridgeExternalAccountByID(ctx, accountID).
			Return(nil, domain.ErrRecordNotFound)
		
		// Execute
		account, kgErr := GetBridgeExternalAccountByID(ctx, accountID)
		
		// Assert
		require.NotNil(t, kgErr, "Should return error for non-existent account")
		assert.Equal(t, code.ParamIncorrect, kgErr.Code, "Should return correct error code")
		assert.Equal(t, 404, kgErr.HttpStatus, "Should return 404 status")
		assert.Nil(t, account, "Account should be nil for error case")
		
		t.Logf("✅ Correctly returned 404 for non-existent account")
	})
}

func TestBridgeExternalAccountService_GetBridgeExternalAccountByID_DatabaseError(t *testing.T) {
	ctrl, mockRepo, _ := setupMockBridgeExternalAccountTest(t)
	defer ctrl.Finish()
	
	ctx := context.Background()
	accountID := "test-account-1"
	
	t.Run("database_error", func(t *testing.T) {
		// Mock database error
		mockRepo.EXPECT().
			GetBridgeExternalAccountByID(ctx, accountID).
			Return(nil, fmt.Errorf("database connection error"))
		
		// Execute
		account, kgErr := GetBridgeExternalAccountByID(ctx, accountID)
		
		// Assert
		require.NotNil(t, kgErr, "Should return error for database error")
		assert.Equal(t, code.DBError, kgErr.Code, "Should return correct error code")
		assert.Nil(t, account, "Account should be nil for error case")
	})
}

// TestBridgeExternalAccountService_ValidateUSAccount tests US account validation
func TestBridgeExternalAccountService_ValidateUSAccount(t *testing.T) {
	testCases := []struct {
		name        string
		req         *domain.CreateBridgeExternalAccountRequest
		shouldError bool
		errorMsg    string
	}{
		{
			name: "valid_us_account",
			req: &domain.CreateBridgeExternalAccountRequest{
				OrganizationID:   1,
				Currency:         "usd",
				BankName:         "Chase Bank",
				AccountOwnerName: "John Doe",
				AccountOwnerType: "individual",
				AccountType:      "us",
				US: &domain.USDetails{
					Account: &domain.USAccount{
						AccountNumber: "************",
						RoutingNumber: "*********",
					},
				},
				Address: &domain.AddressDetails{
					Country:     "USA",
					City:        "New York",
					StreetLine1: "270 Park Ave",
					StreetLine2: "Floor 10",
					PostalCode:  "10001",
					State:       &[]string{"NY"}[0],
				},
			},
			shouldError: false,
		},
		{
			name: "missing_us_details",
			req: &domain.CreateBridgeExternalAccountRequest{
				OrganizationID:   1,
				Currency:         "usd",
				BankName:         "Chase Bank",
				AccountOwnerName: "John Doe",
				AccountOwnerType: "individual",
				AccountType:      "us",
				// US field is nil
			},
			shouldError: true,
			errorMsg:    "us account details are required when account_type is 'us'",
		},
		{
			name: "missing_account_number",
			req: &domain.CreateBridgeExternalAccountRequest{
				OrganizationID:   1,
				Currency:         "usd",
				BankName:         "Chase Bank",
				AccountOwnerName: "John Doe",
				AccountOwnerType: "individual",
				AccountType:      "us",
				US: &domain.USDetails{
					Account: &domain.USAccount{
						// AccountNumber is missing
						RoutingNumber: "*********",
					},
				},
				Address: &domain.AddressDetails{
					Country:     "USA",
					City:        "New York",
					StreetLine1: "270 Park Ave",
					PostalCode:  "10001",
				},
			},
			shouldError: true,
			errorMsg:    "account_number is required for US accounts",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.req.ValidateAccountTypeSpecificFields()
			
			if tc.shouldError {
				assert.Error(t, err, "Expected validation to fail for %s", tc.name)
				if tc.errorMsg != "" {
					assert.Contains(t, err.Error(), tc.errorMsg, "Error message should contain expected text")
				}
			} else {
				assert.NoError(t, err, "Expected validation to pass for %s", tc.name)
			}
		})
	}
}

// TestBridgeExternalAccountService_ValidateIBANAccount tests IBAN account validation
func TestBridgeExternalAccountService_ValidateIBANAccount(t *testing.T) {
	testCases := []struct {
		name        string
		req         *domain.CreateBridgeExternalAccountRequest
		shouldError bool
		errorMsg    string
	}{
		{
			name: "valid_iban_account",
			req: &domain.CreateBridgeExternalAccountRequest{
				OrganizationID:   1,
				Currency:         "usd",
				BankName:         "Test Bank",
				AccountOwnerName: "Test User",
				AccountOwnerType: "individual",
				AccountType:      "iban",
				Swift: &domain.SwiftDetails{
					Account: &domain.SwiftAccount{
						AccountNumber: "12345",
						BIC:           "TESTBIC123",
					},
				},
			},
			shouldError: false,
		},
		{
			name: "missing_swift_details",
			req: &domain.CreateBridgeExternalAccountRequest{
				OrganizationID:   1,
				Currency:         "usd",
				BankName:         "Test Bank",
				AccountOwnerName: "Test User",
				AccountOwnerType: "individual",
				AccountType:      "iban",
				// Swift field is nil
			},
			shouldError: true,
			errorMsg:    "swift details are required when account_type is 'iban'",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.req.ValidateAccountTypeSpecificFields()
			
			if tc.shouldError {
				assert.Error(t, err, "Expected validation to fail for %s", tc.name)
				if tc.errorMsg != "" {
					assert.Contains(t, err.Error(), tc.errorMsg, "Error message should contain expected text")
				}
			} else {
				assert.NoError(t, err, "Expected validation to pass for %s", tc.name)
			}
		})
	}
} 