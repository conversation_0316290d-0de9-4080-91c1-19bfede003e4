package tokenanalysis

import (
	"context"
	"crypto/ed25519"
	"crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"net/http"
	"testing"
	"time"

	solanago "github.com/gagliardetto/solana-go"
	"github.com/kryptogo/kg-wallet-backend/domain"
	solanaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api"
	tokenanalysisapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/token-analysis"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	gomock "go.uber.org/mock/gomock"
)

func TestAnalyzeToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mocks
	mockRepo := NewMockIRepo(ctrl)
	mockAnalysisAPI := tokenanalysisapi.NewMockITokenAnalysis(ctrl)

	// Setup token analysis API mock
	tokenanalysisapi.Set(mockAnalysisAPI)

	// Initialize the service with the mock repo
	Init(mockRepo)

	// Test data
	ctx := context.Background()

	// Generate a new Ed25519 keypair using crypto/ed25519
	seed := make([]byte, ed25519.SeedSize)
	_, err := rand.Read(seed)
	require.NoError(t, err, "Failed to generate random seed")

	// Create a private key from the seed
	privateKey := ed25519.NewKeyFromSeed(seed)

	// Convert to Solana private key format
	solPrivateKey := solanago.PrivateKey(privateKey)

	// Get the public key in Solana format
	publicKey := solPrivateKey.PublicKey()
	walletAddress := publicKey.String()

	t.Logf("Using wallet address: %s", walletAddress)

	// Create message with current timestamp
	timestamp := time.Now().Unix()
	message := fmt.Sprintf(messageFormat, timestamp)

	// Sign the message with the Solana private key
	signature, err := solPrivateKey.Sign([]byte(message))
	require.NoError(t, err, "Failed to sign message")

	// Convert to string as expected by the API
	signatureStr := signature.String()

	tokenAddress := "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" // USDC on Solana

	// Mock analysis result
	mockAnalysis := &tokenanalysisapi.Analysis{
		CEX:               0.25,
		IndividualTraders: 0.4,
		KOLTraders:        0.15,
		LiquidityPools:    0.1,
		Others:            0.05,
		SuspectInsiders:   0.05,
	}

	// Test cases
	tests := []struct {
		name           string
		setupMocks     func()
		expectedResult *AnalysisResponse
		expectedError  *code.KGError
	}{
		{
			name: "Successful analysis",
			setupMocks: func() {
				// Mock UseCredit
				mockRepo.EXPECT().UseCredit(gomock.Any(), walletAddress).Return(nil)

				// Mock AnalyzeToken
				mockAnalysisAPI.EXPECT().AnalyzeToken(gomock.Any(), tokenAddress).Return(mockAnalysis, nil)

				// Mock CreateAnalysis
				mockRepo.EXPECT().CreateAnalysis(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, analysis *domain.TokenAnalysis) (int, error) {
						assert.Equal(t, walletAddress, analysis.WalletAddress)
						assert.Equal(t, tokenAddress, analysis.TokenAddress)
						return 1, nil
					})
			},
			expectedResult: &AnalysisResponse{
				ID:        1,
				Analysis:  mockAnalysis,
				Timestamp: timestamp,
			},
			expectedError: nil,
		},
		{
			name: "Insufficient credits",
			setupMocks: func() {
				// Mock UseCredit with insufficient credits error
				mockRepo.EXPECT().UseCredit(gomock.Any(), walletAddress).Return(domain.ErrInsufficientBalance)
			},
			expectedResult: nil,
			expectedError:  code.NewKGError(code.InsufficientBalance, http.StatusBadRequest, fmt.Errorf("insufficient credits"), nil),
		},
		{
			name: "API error with credit refund",
			setupMocks: func() {
				// Mock UseCredit
				mockRepo.EXPECT().UseCredit(gomock.Any(), walletAddress).Return(nil)

				// Mock AnalyzeToken with error
				mockAnalysisAPI.EXPECT().AnalyzeToken(gomock.Any(), tokenAddress).Return(nil, fmt.Errorf("api error"))

				// Mock AddCredits for refund
				mockRepo.EXPECT().AddCredits(gomock.Any(), walletAddress, 1).Return(nil)
			},
			expectedResult: nil,
			expectedError:  code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, fmt.Errorf("api error"), nil),
		},
		{
			name: "Database error",
			setupMocks: func() {
				// Mock UseCredit
				mockRepo.EXPECT().UseCredit(gomock.Any(), walletAddress).Return(nil)

				// Mock AnalyzeToken
				mockAnalysisAPI.EXPECT().AnalyzeToken(gomock.Any(), tokenAddress).Return(mockAnalysis, nil)

				// Mock CreateAnalysis with error
				mockRepo.EXPECT().CreateAnalysis(gomock.Any(), gomock.Any()).Return(0, fmt.Errorf("db error"))
			},
			expectedResult: nil,
			expectedError:  code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil),
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mocks
			tc.setupMocks()

			// Call the function
			result, err := AnalyzeToken(ctx, walletAddress, tokenAddress, message, signatureStr)

			// Check results
			if tc.expectedError != nil {
				assert.Equal(t, tc.expectedError.Code, err.Code)
				assert.Equal(t, tc.expectedError.HttpStatus, err.HttpStatus)
			} else {
				require.Nil(t, err, "Expected no error but got: %v", err)
				require.NotNil(t, result, "Expected non-nil result")
				assert.Equal(t, tc.expectedResult.ID, result.ID)
				assert.Equal(t, tc.expectedResult.Analysis, result.Analysis)
				assert.Equal(t, tc.expectedResult.Timestamp, result.Timestamp)
			}
		})
	}
}

func TestAddCredits(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock repo
	mockRepo := NewMockIRepo(ctrl)

	// Initialize the service with the mock repo
	Init(mockRepo)

	// Test data
	ctx := context.Background()
	walletAddress := "********************************************"
	credits := 5

	// Test cases
	tests := []struct {
		name          string
		setupMocks    func()
		expectedError *code.KGError
	}{
		{
			name: "Successful addition",
			setupMocks: func() {
				mockRepo.EXPECT().AddCredits(gomock.Any(), walletAddress, credits).Return(nil)
			},
			expectedError: nil,
		},
		{
			name: "Database error",
			setupMocks: func() {
				mockRepo.EXPECT().AddCredits(gomock.Any(), walletAddress, credits).Return(fmt.Errorf("db error"))
			},
			expectedError: code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil),
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mocks
			tc.setupMocks()

			// Call the function
			err := AddCredits(ctx, walletAddress, credits)

			// Check results
			if tc.expectedError != nil {
				assert.Equal(t, tc.expectedError.Code, err.Code)
				assert.Equal(t, tc.expectedError.HttpStatus, err.HttpStatus)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

func TestGetCredits(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock repo
	mockRepo := NewMockIRepo(ctrl)

	// Initialize the service with the mock repo
	Init(mockRepo)

	// Test data
	ctx := context.Background()
	walletAddress := "********************************************"
	credits := 10
	volume := 500.0

	// Test cases
	tests := []struct {
		name           string
		setupMocks     func()
		expectedResult *CreditsResponse
		expectedError  *code.KGError
	}{
		{
			name: "Successful retrieval",
			setupMocks: func() {
				mockRepo.EXPECT().GetCredits(gomock.Any(), walletAddress).Return(&domain.TokenAnalysisCredit{
					WalletAddress: walletAddress,
					Credits:       credits,
				}, nil)
				mockRepo.EXPECT().GetTradingVolume(gomock.Any(), walletAddress).Return(volume, nil)
			},
			expectedResult: &CreditsResponse{
				Credits:       credits,
				TradingVolume: volume,
			},
			expectedError: nil,
		},
		{
			name: "GetCredits error",
			setupMocks: func() {
				mockRepo.EXPECT().GetCredits(gomock.Any(), walletAddress).Return(nil, fmt.Errorf("db error"))
			},
			expectedResult: nil,
			expectedError:  code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil),
		},
		{
			name: "GetTradingVolume error",
			setupMocks: func() {
				mockRepo.EXPECT().GetCredits(gomock.Any(), walletAddress).Return(&domain.TokenAnalysisCredit{
					WalletAddress: walletAddress,
					Credits:       credits,
				}, nil)
				mockRepo.EXPECT().GetTradingVolume(gomock.Any(), walletAddress).Return(0.0, fmt.Errorf("db error"))
			},
			expectedResult: nil,
			expectedError:  code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil),
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mocks
			tc.setupMocks()

			// Call the function
			result, err := GetCredits(ctx, walletAddress)

			// Check results
			if tc.expectedError != nil {
				assert.Equal(t, tc.expectedError.Code, err.Code)
				assert.Equal(t, tc.expectedError.HttpStatus, err.HttpStatus)
			} else {
				assert.Nil(t, err)
				assert.Equal(t, tc.expectedResult.Credits, result.Credits)
				assert.Equal(t, tc.expectedResult.TradingVolume, result.TradingVolume)
			}
		})
	}
}

func TestProcessTradingVolume(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock repo
	mockRepo := NewMockIRepo(ctrl)

	// Initialize the service with the mock repo
	Init(mockRepo)
	solanaapi.InitDefault()

	// For this test we're assuming XYZ_TRADING_FEE = 0.01 (1%) and XYZ_TRADING_VOLUME_PER_CREDIT = 100.0
	// We will not modify the config values but assume these values are used in the service implementation

	// Test data
	ctx := context.Background()
	txHash := "5uqVPGTB6qCBpGrTVBw9uNV7K6v3hC6duwC3BgfwpXm5zBBRyXvZR4WRHqGTzx5nwfPMhiiRWneV5ExQwGq6ygtj"
	fromAddress := "********************************************" // Sender wallet
	orgAddress := "GsTx64KUeHpAsZcTtLSyAuZTrtFHqWtJbVc36XZtiZpr"  // Organization wallet
	solPrice := 200.0                                             // Example SOL price in USD
	oldVolume := 499.0
	newVolume := 601.0

	// Test cases
	tests := []struct {
		name          string
		setupMocks    func()
		expectedError *code.KGError
	}{
		{
			name: "Duplicate transaction",
			setupMocks: func() {
				// Mock TradingVolumeExists with true return value
				mockRepo.EXPECT().TradingVolumeExists(gomock.Any(), txHash).Return(true, nil)
			},
			expectedError: code.NewKGError(code.TransactionAlreadyExists, http.StatusConflict, fmt.Errorf("transaction already processed"), nil),
		},
		{
			name: "TradingVolumeExists error",
			setupMocks: func() {
				// Mock TradingVolumeExists with error
				mockRepo.EXPECT().TradingVolumeExists(gomock.Any(), txHash).Return(false, fmt.Errorf("db error"))
			},
			expectedError: code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil),
		},
		{
			name: "Successful trading volume processing with credit award",
			setupMocks: func() {
				// Mock TradingVolumeExists with false return value
				mockRepo.EXPECT().TradingVolumeExists(gomock.Any(), txHash).Return(false, nil)

				// Mock GetWalletsByOrganizationId
				mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
					SolanaAddress: orgAddress,
				}, nil)

				// Mock GetNativeAssetPrice
				mockRepo.EXPECT().GetNativeAssetPrice(gomock.Any(), "sol").Return(solPrice, nil)

				// Mock GetTradingVolume for old volume
				mockRepo.EXPECT().GetTradingVolume(gomock.Any(), fromAddress).Return(oldVolume, nil)

				// Mock RecordTradingVolume
				mockRepo.EXPECT().RecordTradingVolume(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, volume *domain.TokenAnalysisTradingVolume) error {
						assert.Equal(t, fromAddress, volume.WalletAddress)
						assert.Equal(t, txHash, volume.TxHash)
						// WSOL amount * SOL price / fee (0.01) = 1 * 100 / 0.01 = 10000
						// But we're actually expecting the volumeUSD to be calculated in the service
						assert.InDelta(t, 215.9916, volume.VolumeUSD, 0.1)
						return nil
					})

				// Mock GetTradingVolume for new volume
				mockRepo.EXPECT().GetTradingVolume(gomock.Any(), fromAddress).Return(newVolume, nil)

				// Mock AddCredits for the newly earned credit
				mockRepo.EXPECT().AddCredits(gomock.Any(), fromAddress, 2).Return(nil)
			},
			expectedError: nil,
		},
		{
			name: "GetWalletsByOrganizationId error",
			setupMocks: func() {
				// Mock TradingVolumeExists with false return value
				mockRepo.EXPECT().TradingVolumeExists(gomock.Any(), txHash).Return(false, nil)

				// Mock GetWalletsByOrganizationId with error
				mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(nil,
					code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil))
			},
			expectedError: code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil),
		},
		{
			name: "GetNativeAssetPrice error",
			setupMocks: func() {
				// Mock TradingVolumeExists with false return value
				mockRepo.EXPECT().TradingVolumeExists(gomock.Any(), txHash).Return(false, nil)

				// Mock GetWalletsByOrganizationId
				mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
					SolanaAddress: orgAddress,
				}, nil)

				// Mock GetNativeAssetPrice with error
				mockRepo.EXPECT().GetNativeAssetPrice(gomock.Any(), "sol").Return(0.0, fmt.Errorf("api error"))
			},
			expectedError: code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, fmt.Errorf("failed to get SOL price: api error"), nil),
		},
		{
			name: "GetTradingVolume error (old volume)",
			setupMocks: func() {
				// Mock TradingVolumeExists with false return value
				mockRepo.EXPECT().TradingVolumeExists(gomock.Any(), txHash).Return(false, nil)

				// Mock GetWalletsByOrganizationId
				mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
					SolanaAddress: orgAddress,
				}, nil)

				// Mock GetNativeAssetPrice
				mockRepo.EXPECT().GetNativeAssetPrice(gomock.Any(), "sol").Return(solPrice, nil)

				// Mock GetTradingVolume with error
				mockRepo.EXPECT().GetTradingVolume(gomock.Any(), fromAddress).Return(0.0, fmt.Errorf("db error"))
			},
			expectedError: code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil),
		},
		{
			name: "RecordTradingVolume error",
			setupMocks: func() {
				// Mock TradingVolumeExists with false return value
				mockRepo.EXPECT().TradingVolumeExists(gomock.Any(), txHash).Return(false, nil)

				// Mock GetWalletsByOrganizationId
				mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
					SolanaAddress: orgAddress,
				}, nil)

				// Mock GetNativeAssetPrice
				mockRepo.EXPECT().GetNativeAssetPrice(gomock.Any(), "sol").Return(solPrice, nil)

				// Mock GetTradingVolume for old volume
				mockRepo.EXPECT().GetTradingVolume(gomock.Any(), fromAddress).Return(oldVolume, nil)

				// Mock RecordTradingVolume with error
				mockRepo.EXPECT().RecordTradingVolume(gomock.Any(), gomock.Any()).Return(fmt.Errorf("db error"))
			},
			expectedError: code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil),
		},
		{
			name: "GetTradingVolume error (new volume)",
			setupMocks: func() {
				// Mock TradingVolumeExists with false return value
				mockRepo.EXPECT().TradingVolumeExists(gomock.Any(), txHash).Return(false, nil)

				// Mock GetWalletsByOrganizationId
				mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
					SolanaAddress: orgAddress,
				}, nil)

				// Mock GetNativeAssetPrice
				mockRepo.EXPECT().GetNativeAssetPrice(gomock.Any(), "sol").Return(solPrice, nil)

				// Mock GetTradingVolume for old volume
				mockRepo.EXPECT().GetTradingVolume(gomock.Any(), fromAddress).Return(oldVolume, nil)

				// Mock RecordTradingVolume
				mockRepo.EXPECT().RecordTradingVolume(gomock.Any(), gomock.Any()).Return(nil)

				// Mock GetTradingVolume for new volume with error
				mockRepo.EXPECT().GetTradingVolume(gomock.Any(), fromAddress).Return(0.0, fmt.Errorf("db error"))
			},
			expectedError: code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil),
		},
		{
			name: "AddCredits error",
			setupMocks: func() {
				// Mock TradingVolumeExists with false return value
				mockRepo.EXPECT().TradingVolumeExists(gomock.Any(), txHash).Return(false, nil)

				// Mock GetWalletsByOrganizationId
				mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
					SolanaAddress: orgAddress,
				}, nil)

				// Mock GetNativeAssetPrice
				mockRepo.EXPECT().GetNativeAssetPrice(gomock.Any(), "sol").Return(solPrice, nil)

				// Mock GetTradingVolume for old volume
				mockRepo.EXPECT().GetTradingVolume(gomock.Any(), fromAddress).Return(oldVolume, nil)

				// Mock RecordTradingVolume
				mockRepo.EXPECT().RecordTradingVolume(gomock.Any(), gomock.Any()).Return(nil)

				// Mock GetTradingVolume for new volume
				mockRepo.EXPECT().GetTradingVolume(gomock.Any(), fromAddress).Return(newVolume, nil)

				// Mock AddCredits with error
				mockRepo.EXPECT().AddCredits(gomock.Any(), fromAddress, 2).Return(fmt.Errorf("db error"))
			},
			expectedError: code.NewKGError(code.DBError, http.StatusInternalServerError, fmt.Errorf("db error"), nil),
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Setup mocks
			tc.setupMocks()

			// Call the function
			err := ProcessTradingVolume(ctx, txHash)

			// Check results
			if tc.expectedError != nil {
				assert.Equal(t, tc.expectedError.Code, err.Code)
				assert.Equal(t, tc.expectedError.HttpStatus, err.HttpStatus)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

func TestPurchaseCredits(t *testing.T) {
	ctx := context.Background()

	// Test: Duplicate transaction
	t.Run("Duplicate_transaction", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_duplicate"
		walletAddress := "test_wallet"

		// Mock duplicate transaction check - returns true
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(true, nil)

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, result)
		assert.NotNil(t, err)
		assert.Equal(t, code.TransactionAlreadyExists, err.Code)
	})

	// Test: CreditPurchaseExists error
	t.Run("CreditPurchaseExists_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_db_error"
		walletAddress := "test_wallet"

		// Mock database error
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, errors.New("database error"))

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, result)
		assert.NotNil(t, err)
		assert.Equal(t, code.DBError, err.Code)
	})

	// Test: WaitUntilTransactionConfirmed error
	t.Run("WaitUntilTransactionConfirmed_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_wait_error"
		walletAddress := "test_wallet"

		// Mock no duplicate transaction
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, nil)

		// Mock transaction confirmation error
		mockChainClient.EXPECT().
			WaitUntilTransactionConfirmed(gomock.Any(), txHash).
			Return(domain.TransactionStatusUnknown, errors.New("timeout error"))

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, result)
		assert.NotNil(t, err)
		assert.Equal(t, code.TransactionInvalid, err.Code)
	})

	// Test: Transaction failed
	t.Run("Transaction_failed", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_failed"
		walletAddress := "test_wallet"

		// Mock no duplicate transaction
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, nil)

		// Mock transaction status failed
		mockChainClient.EXPECT().
			WaitUntilTransactionConfirmed(gomock.Any(), txHash).
			Return(domain.TransactionStatusFailed, nil)

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, result)
		assert.NotNil(t, err)
		assert.Equal(t, code.TransactionFailed, err.Code)
	})

	// Test: TransactionDetail error
	t.Run("TransactionDetail_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_detail_error"
		walletAddress := "test_wallet"

		// Mock no duplicate transaction
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, nil)

		// Mock successful transaction confirmation
		mockChainClient.EXPECT().
			WaitUntilTransactionConfirmed(gomock.Any(), txHash).
			Return(domain.TransactionStatusSuccess, nil)

		// Mock transaction detail error
		mockChainClient.EXPECT().
			TransactionDetail(ctx, txHash).
			Return(nil, errors.New("failed to get transaction detail"))

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, result)
		assert.NotNil(t, err)
		assert.Equal(t, code.ExternalAPIError, err.Code)
	})

	// Test: Wrong sender wallet
	t.Run("Wrong_sender_wallet", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_wrong_sender"
		walletAddress := "correct_wallet"
		wrongWallet := "wrong_wallet"

		// Mock no duplicate transaction
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, nil)

		// Mock successful transaction confirmation
		mockChainClient.EXPECT().
			WaitUntilTransactionConfirmed(gomock.Any(), txHash).
			Return(domain.TransactionStatusSuccess, nil)

		// Create mock transaction detail with wrong sender
		txDetail := &domain.TransactionDetail{
			From: domain.NewAddressByChain(domain.Solana, wrongWallet),
		}

		// Mock transaction detail with wrong sender
		mockChainClient.EXPECT().
			TransactionDetail(ctx, txHash).
			Return(txDetail, nil)

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, result)
		assert.NotNil(t, err)
		assert.Equal(t, code.ParamIncorrect, err.Code)
		assert.Contains(t, err.Error.Error(), "transaction not from specified wallet")
	})

	// Test: GetWalletsByOrganizationId error
	t.Run("GetWalletsByOrganizationId_error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_org_error"
		walletAddress := "test_wallet"

		// Mock no duplicate transaction
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, nil)

		// Mock successful transaction confirmation
		mockChainClient.EXPECT().
			WaitUntilTransactionConfirmed(gomock.Any(), txHash).
			Return(domain.TransactionStatusSuccess, nil)

		// Create mock transaction detail
		txDetail := &domain.TransactionDetail{
			From: domain.NewAddressByChain(domain.Solana, walletAddress),
		}

		// Mock transaction detail
		mockChainClient.EXPECT().
			TransactionDetail(ctx, txHash).
			Return(txDetail, nil)

		// Mock organization wallet error
		mockRepo.EXPECT().
			GetWalletsByOrganizationId(ctx, 1).
			Return(nil, code.NewKGError(code.DBError, http.StatusInternalServerError, errors.New("db error"), nil))

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, result)
		assert.NotNil(t, err)
		assert.Equal(t, code.DBError, err.Code)
	})

	// Test: No SOL transfer to organization wallet
	t.Run("No_SOL_transfer_to_org_wallet", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_no_transfer"
		walletAddress := "test_wallet"
		orgWallet := "org_wallet"

		// Mock no duplicate transaction
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, nil)

		// Mock successful transaction confirmation
		mockChainClient.EXPECT().
			WaitUntilTransactionConfirmed(gomock.Any(), txHash).
			Return(domain.TransactionStatusSuccess, nil)

		// Create mock transaction detail with no transfers to org
		txDetail := &domain.TransactionDetail{
			From: domain.NewAddressByChain(domain.Solana, walletAddress),
			TransactionTransfers: domain.TransactionTransfers{
				InternalTransfers: []*domain.NativeTokenTransfer{
					{
						From: domain.NewAddressByChain(domain.Solana, walletAddress),
						To:   domain.NewAddressByChain(domain.Solana, "some_other_wallet"),
					},
				},
			},
		}

		// Mock transaction detail
		mockChainClient.EXPECT().
			TransactionDetail(ctx, txHash).
			Return(txDetail, nil)

		// Mock organization wallet
		orgWallets := &domain.OrganizationWallets{
			SolanaAddress: orgWallet,
		}
		mockRepo.EXPECT().
			GetWalletsByOrganizationId(ctx, 1).
			Return(orgWallets, nil)

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, result)
		assert.NotNil(t, err)
		assert.Equal(t, code.ParamIncorrect, err.Code)
		assert.Contains(t, err.Error.Error(), "no SOL transfer to organization wallet found")
	})

	// Test: Successful single credit purchase
	t.Run("Successful_single_credit_purchase", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_single"
		walletAddress := "test_wallet"
		orgWallet := "org_wallet"

		// Mock no duplicate transaction
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, nil)

		// Mock successful transaction confirmation
		mockChainClient.EXPECT().
			WaitUntilTransactionConfirmed(gomock.Any(), txHash).
			Return(domain.TransactionStatusSuccess, nil)

		// Create mock transaction detail with SOL transfer to org (0.001 SOL = 1000000 lamports)
		solAmountLamports := big.NewInt(1000000) // 0.001 SOL in lamports
		txDetail := &domain.TransactionDetail{
			From: domain.NewAddressByChain(domain.Solana, walletAddress),
			TransactionTransfers: domain.TransactionTransfers{
				InternalTransfers: []*domain.NativeTokenTransfer{
					{
						From:   domain.NewAddressByChain(domain.Solana, walletAddress),
						To:     domain.NewAddressByChain(domain.Solana, orgWallet),
						Amount: solAmountLamports,
					},
				},
			},
		}

		// Mock transaction detail
		mockChainClient.EXPECT().
			TransactionDetail(ctx, txHash).
			Return(txDetail, nil)

		// Mock organization wallet
		orgWallets := &domain.OrganizationWallets{
			SolanaAddress: orgWallet,
		}
		mockRepo.EXPECT().
			GetWalletsByOrganizationId(ctx, 1).
			Return(orgWallets, nil)

		// Mock credit purchase recording
		mockRepo.EXPECT().
			RecordCreditPurchase(ctx, gomock.Any()).
			Return(nil)

		// Mock adding credits
		mockRepo.EXPECT().
			AddCredits(ctx, walletAddress, 1). // 0.001 SOL = 1 credit
			Return(nil)

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, walletAddress, result.WalletAddress)
		assert.Equal(t, txHash, result.TxHash)
		assert.Equal(t, 0.001, result.SolAmount)
		assert.Equal(t, 1, result.CreditsPurchased) // 0.001 SOL = 1 credit
	})

	// Test: Successful 10 credits package purchase
	t.Run("Successful_10_credits_package", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_10_package"
		walletAddress := "test_wallet"
		orgWallet := "org_wallet"

		// Mock no duplicate transaction
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, nil)

		// Mock successful transaction confirmation
		mockChainClient.EXPECT().
			WaitUntilTransactionConfirmed(gomock.Any(), txHash).
			Return(domain.TransactionStatusSuccess, nil)

		// Create mock transaction detail with SOL transfer to org (0.08 SOL = 80000000 lamports)
		solAmountLamports := big.NewInt(80000000) // 0.08 SOL in lamports
		txDetail := &domain.TransactionDetail{
			From: domain.NewAddressByChain(domain.Solana, walletAddress),
			TransactionTransfers: domain.TransactionTransfers{
				InternalTransfers: []*domain.NativeTokenTransfer{
					{
						From:   domain.NewAddressByChain(domain.Solana, walletAddress),
						To:     domain.NewAddressByChain(domain.Solana, orgWallet),
						Amount: solAmountLamports,
					},
				},
			},
		}

		// Mock transaction detail
		mockChainClient.EXPECT().
			TransactionDetail(ctx, txHash).
			Return(txDetail, nil)

		// Mock organization wallet
		orgWallets := &domain.OrganizationWallets{
			SolanaAddress: orgWallet,
		}
		mockRepo.EXPECT().
			GetWalletsByOrganizationId(ctx, 1).
			Return(orgWallets, nil)

		// Mock credit purchase recording
		mockRepo.EXPECT().
			RecordCreditPurchase(ctx, gomock.Any()).
			Return(nil)

		// Mock adding credits
		mockRepo.EXPECT().
			AddCredits(ctx, walletAddress, 117). // 0.08 SOL = 117 credits (greedy optimized)
			Return(nil)

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, walletAddress, result.WalletAddress)
		assert.Equal(t, txHash, result.TxHash)
		assert.Equal(t, 0.08, result.SolAmount)
		assert.Equal(t, 117, result.CreditsPurchased) // 0.08 SOL = 117 credits (greedy optimized)
	})

	// Test: Successful 50 credits package purchase
	t.Run("Successful_50_credits_package", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_50_package"
		walletAddress := "test_wallet"
		orgWallet := "org_wallet"

		// Mock no duplicate transaction
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, nil)

		// Mock successful transaction confirmation
		mockChainClient.EXPECT().
			WaitUntilTransactionConfirmed(gomock.Any(), txHash).
			Return(domain.TransactionStatusSuccess, nil)

		// Create mock transaction detail with SOL transfer to org (0.0325 SOL = 32500000 lamports)
		solAmountLamports := big.NewInt(32500000) // 0.0325 SOL in lamports
		txDetail := &domain.TransactionDetail{
			From: domain.NewAddressByChain(domain.Solana, walletAddress),
			TransactionTransfers: domain.TransactionTransfers{
				InternalTransfers: []*domain.NativeTokenTransfer{
					{
						From:   domain.NewAddressByChain(domain.Solana, walletAddress),
						To:     domain.NewAddressByChain(domain.Solana, orgWallet),
						Amount: solAmountLamports,
					},
				},
			},
		}

		// Mock transaction detail
		mockChainClient.EXPECT().
			TransactionDetail(ctx, txHash).
			Return(txDetail, nil)

		// Mock organization wallet
		orgWallets := &domain.OrganizationWallets{
			SolanaAddress: orgWallet,
		}
		mockRepo.EXPECT().
			GetWalletsByOrganizationId(ctx, 1).
			Return(orgWallets, nil)

		// Mock credit purchase recording
		mockRepo.EXPECT().
			RecordCreditPurchase(ctx, gomock.Any()).
			Return(nil)

		// Mock adding credits
		mockRepo.EXPECT().
			AddCredits(ctx, walletAddress, 50). // 0.0325 SOL = 50 credits package
			Return(nil)

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, walletAddress, result.WalletAddress)
		assert.Equal(t, txHash, result.TxHash)
		assert.Equal(t, 0.0325, result.SolAmount)
		assert.Equal(t, 50, result.CreditsPurchased) // 0.0325 SOL = 50 credits package
	})

	// Test: Invalid SOL amount
	t.Run("Invalid_SOL_amount", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := NewMockIRepo(ctrl)
		mockChainClient := domain.NewMockChainClient(ctrl)

		Init(mockRepo)

		txHash := "test_tx_hash_invalid"
		walletAddress := "test_wallet"
		orgWallet := "org_wallet"

		// Mock no duplicate transaction
		mockRepo.EXPECT().
			CreditPurchaseExists(ctx, txHash).
			Return(false, nil)

		// Mock successful transaction confirmation
		mockChainClient.EXPECT().
			WaitUntilTransactionConfirmed(gomock.Any(), txHash).
			Return(domain.TransactionStatusSuccess, nil)

		// Create mock transaction detail with invalid SOL amount (0.0005 SOL = 500000 lamports)
		solAmountLamports := big.NewInt(500000) // 0.0005 SOL in lamports - not valid for any package
		txDetail := &domain.TransactionDetail{
			From: domain.NewAddressByChain(domain.Solana, walletAddress),
			TransactionTransfers: domain.TransactionTransfers{
				InternalTransfers: []*domain.NativeTokenTransfer{
					{
						From:   domain.NewAddressByChain(domain.Solana, walletAddress),
						To:     domain.NewAddressByChain(domain.Solana, orgWallet),
						Amount: solAmountLamports,
					},
				},
			},
		}

		// Mock transaction detail
		mockChainClient.EXPECT().
			TransactionDetail(ctx, txHash).
			Return(txDetail, nil)

		// Mock organization wallet
		orgWallets := &domain.OrganizationWallets{
			SolanaAddress: orgWallet,
		}
		mockRepo.EXPECT().
			GetWalletsByOrganizationId(ctx, 1).
			Return(orgWallets, nil)

		// Call function
		result, err := purchaseCreditsWithClient(ctx, walletAddress, txHash, mockChainClient)

		// Assertions
		assert.Nil(t, result)
		assert.NotNil(t, err)
		assert.Equal(t, code.ParamIncorrect, err.Code)
		assert.Contains(t, err.Error.Error(), "invalid SOL amount for credit purchase")
	})
}

func TestCalculateCreditsFromSOL(t *testing.T) {
	// Test with current local/dev pricing configuration
	tests := []struct {
		name        string
		solAmount   string
		expected    int
		description string
	}{
		{
			name:        "single_50_credits_package",
			solAmount:   "0.0325",
			expected:    50,
			description: "50 credits package for 0.0325 SOL",
		},
		{
			name:        "single_10_credits_package",
			solAmount:   "0.008",
			expected:    10,
			description: "10 credits package for 0.008 SOL",
		},
		{
			name:        "single_credit",
			solAmount:   "0.001",
			expected:    1,
			description: "Single credit for 0.001 SOL",
		},
		{
			name:        "multiple_single_credits",
			solAmount:   "0.005",
			expected:    5,
			description: "5 credits for 0.005 SOL (5 * 0.001)",
		},
		{
			name:        "combination_60_credits",
			solAmount:   "0.0405", // 0.00065 * 50 + 0.0008 * 10 = 0.0405
			expected:    60,
			description: "60 credits: 50-package + 10-package",
		},
		{
			name:        "combination_70_credits",
			solAmount:   "0.0485",
			expected:    70,
			description: "70 credits: 50-package + 10-package + 10-package",
		},
		{
			name:        "combination_multiple_50s",
			solAmount:   "0.065", // 0.0325 * 2 = 0.065 SOL
			expected:    100,
			description: "100 credits: 2×50-package (0.065 SOL)",
		},
		{
			name:        "combination_multiple_10s",
			solAmount:   "0.16", // 0.08 * 2 = 0.16 SOL
			expected:    236,    // Greedy optimized result (updated after config fix)
			description: "0.16 SOL optimized with greedy algorithm",
		},
		{
			name:        "combination_500_credits",
			solAmount:   "0.325", // 0.0325 * 10 = 0.325 SOL
			expected:    500,
			description: "500 credits: 10×50-package (0.325 SOL)",
		},
		{
			name:        "combination_mixed_large",
			solAmount:   "0.3425", // 0.0325*3 (150) + 0.08*3 (30) + 0.005 (5) = 0.0975 + 0.24 + 0.005 = 0.3425
			expected:    521,      // Greedy optimized result (updated after config fix)
			description: "0.3425 SOL optimized with greedy algorithm",
		},
		{
			name:        "invalid_amount_too_small",
			solAmount:   "0.0005",
			expected:    0,
			description: "Invalid amount (less than minimum)",
		},
		{
			name:        "zero_amount",
			solAmount:   "0",
			expected:    0,
			description: "Zero amount",
		},
		{
			name:        "large_single_amount",
			solAmount:   "0.1",
			expected:    152, // Greedy optimized result
			description: "0.1 SOL optimized with greedy algorithm",
		},
		{
			name:        "partial_package_amount",
			solAmount:   "0.0335", // 0.0325 (50) + 0.001 (1) = 0.0335 SOL
			expected:    51,
			description: "51 credits: 50-package + 1 single (0.0335 SOL)",
		},
		{
			name:        "mixed_optimal_combination",
			solAmount:   "0.2025", // 0.0325 (50) + 0.08 (10) + 0.08 (10) + 0.01 (10 singles) = 0.2025 SOL
			expected:    307,      // Greedy optimized result
			description: "0.2025 SOL optimized with greedy algorithm",
		},
		{
			name:        "greedy_algorithm_test",
			solAmount:   "0.2425", // Should be 7×50 packages (0.2275) + 0.015 remainder = 7×50 + 15 singles
			expected:    367,      // Greedy optimized result (updated after config fix)
			description: "367 credits using greedy algorithm (0.2425 SOL)",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			solAmount, err := decimal.NewFromString(tc.solAmount)
			require.NoError(t, err, "Failed to parse test SOL amount")

			result := calculateCreditsFromSOL(solAmount)
			assert.Equal(t, tc.expected, result, tc.description)
		})
	}
}
