// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/service/token-analysis (interfaces: IRepo)
//
// Generated by this command:
//
//	mockgen -package=tokenanalysis -self_package=github.com/kryptogo/kg-wallet-backend/service/token-analysis -destination=common_mock.go . IRepo
//

// Package tokenanalysis is a generated GoMock package.
package tokenanalysis

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockIRepo is a mock of IRepo interface.
type MockIRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIRepoMockRecorder
	isgomock struct{}
}

// MockIRepoMockRecorder is the mock recorder for MockIRepo.
type MockIRepoMockRecorder struct {
	mock *MockIRepo
}

// NewMockIRepo creates a new mock instance.
func NewMockIRepo(ctrl *gomock.Controller) *MockIRepo {
	mock := &MockIRepo{ctrl: ctrl}
	mock.recorder = &MockIRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRepo) EXPECT() *MockIRepoMockRecorder {
	return m.recorder
}

// AddCredits mocks base method.
func (m *MockIRepo) AddCredits(ctx context.Context, walletAddress string, credits int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCredits", ctx, walletAddress, credits)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCredits indicates an expected call of AddCredits.
func (mr *MockIRepoMockRecorder) AddCredits(ctx, walletAddress, credits any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCredits", reflect.TypeOf((*MockIRepo)(nil).AddCredits), ctx, walletAddress, credits)
}

// BatchGetTokenPrices mocks base method.
func (m *MockIRepo) BatchGetTokenPrices(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPrices", ctx, tokens)
	ret0, _ := ret[0].(map[domain.ChainToken]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPrices indicates an expected call of BatchGetTokenPrices.
func (mr *MockIRepoMockRecorder) BatchGetTokenPrices(ctx, tokens any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPrices", reflect.TypeOf((*MockIRepo)(nil).BatchGetTokenPrices), ctx, tokens)
}

// BatchGetTokenPricesIn24H mocks base method.
func (m *MockIRepo) BatchGetTokenPricesIn24H(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken][]*domain.PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPricesIn24H", ctx, tokens)
	ret0, _ := ret[0].(map[domain.ChainToken][]*domain.PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPricesIn24H indicates an expected call of BatchGetTokenPricesIn24H.
func (mr *MockIRepoMockRecorder) BatchGetTokenPricesIn24H(ctx, tokens any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPricesIn24H", reflect.TypeOf((*MockIRepo)(nil).BatchGetTokenPricesIn24H), ctx, tokens)
}

// CreateAnalysis mocks base method.
func (m *MockIRepo) CreateAnalysis(ctx context.Context, analysis *domain.TokenAnalysis) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAnalysis", ctx, analysis)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAnalysis indicates an expected call of CreateAnalysis.
func (mr *MockIRepoMockRecorder) CreateAnalysis(ctx, analysis any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAnalysis", reflect.TypeOf((*MockIRepo)(nil).CreateAnalysis), ctx, analysis)
}

// CreditPurchaseExists mocks base method.
func (m *MockIRepo) CreditPurchaseExists(ctx context.Context, txHash string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreditPurchaseExists", ctx, txHash)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreditPurchaseExists indicates an expected call of CreditPurchaseExists.
func (mr *MockIRepoMockRecorder) CreditPurchaseExists(ctx, txHash any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreditPurchaseExists", reflect.TypeOf((*MockIRepo)(nil).CreditPurchaseExists), ctx, txHash)
}

// GetAnalysis mocks base method.
func (m *MockIRepo) GetAnalysis(ctx context.Context, id int) (*domain.TokenAnalysis, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAnalysis", ctx, id)
	ret0, _ := ret[0].(*domain.TokenAnalysis)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAnalysis indicates an expected call of GetAnalysis.
func (mr *MockIRepoMockRecorder) GetAnalysis(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAnalysis", reflect.TypeOf((*MockIRepo)(nil).GetAnalysis), ctx, id)
}

// GetAssetPrice mocks base method.
func (m *MockIRepo) GetAssetPrice(ctx context.Context, chainID, contractAddress string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", ctx, chainID, contractAddress)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockIRepoMockRecorder) GetAssetPrice(ctx, chainID, contractAddress any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockIRepo)(nil).GetAssetPrice), ctx, chainID, contractAddress)
}

// GetCredits mocks base method.
func (m *MockIRepo) GetCredits(ctx context.Context, walletAddress string) (*domain.TokenAnalysisCredit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCredits", ctx, walletAddress)
	ret0, _ := ret[0].(*domain.TokenAnalysisCredit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCredits indicates an expected call of GetCredits.
func (mr *MockIRepoMockRecorder) GetCredits(ctx, walletAddress any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCredits", reflect.TypeOf((*MockIRepo)(nil).GetCredits), ctx, walletAddress)
}

// GetNativeAssetPrice mocks base method.
func (m *MockIRepo) GetNativeAssetPrice(ctx context.Context, chainID string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", ctx, chainID)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockIRepoMockRecorder) GetNativeAssetPrice(ctx, chainID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockIRepo)(nil).GetNativeAssetPrice), ctx, chainID)
}

// GetTokenPrice mocks base method.
func (m *MockIRepo) GetTokenPrice(ctx context.Context, chain domain.Chain, tokenID string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPrice", ctx, chain, tokenID)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPrice indicates an expected call of GetTokenPrice.
func (mr *MockIRepoMockRecorder) GetTokenPrice(ctx, chain, tokenID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPrice", reflect.TypeOf((*MockIRepo)(nil).GetTokenPrice), ctx, chain, tokenID)
}

// GetTokenPricesIn24H mocks base method.
func (m *MockIRepo) GetTokenPricesIn24H(ctx context.Context, chain domain.Chain, tokenID string) ([]*domain.PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPricesIn24H", ctx, chain, tokenID)
	ret0, _ := ret[0].([]*domain.PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPricesIn24H indicates an expected call of GetTokenPricesIn24H.
func (mr *MockIRepoMockRecorder) GetTokenPricesIn24H(ctx, chain, tokenID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPricesIn24H", reflect.TypeOf((*MockIRepo)(nil).GetTokenPricesIn24H), ctx, chain, tokenID)
}

// GetTradingVolume mocks base method.
func (m *MockIRepo) GetTradingVolume(ctx context.Context, walletAddress string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTradingVolume", ctx, walletAddress)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTradingVolume indicates an expected call of GetTradingVolume.
func (mr *MockIRepoMockRecorder) GetTradingVolume(ctx, walletAddress any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTradingVolume", reflect.TypeOf((*MockIRepo)(nil).GetTradingVolume), ctx, walletAddress)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockIRepo) GetWalletsByOrganizationId(ctx context.Context, orgID int) (*domain.OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", ctx, orgID)
	ret0, _ := ret[0].(*domain.OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockIRepoMockRecorder) GetWalletsByOrganizationId(ctx, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockIRepo)(nil).GetWalletsByOrganizationId), ctx, orgID)
}

// RecordCreditPurchase mocks base method.
func (m *MockIRepo) RecordCreditPurchase(ctx context.Context, purchase *domain.TokenAnalysisCreditPurchase) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordCreditPurchase", ctx, purchase)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordCreditPurchase indicates an expected call of RecordCreditPurchase.
func (mr *MockIRepoMockRecorder) RecordCreditPurchase(ctx, purchase any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordCreditPurchase", reflect.TypeOf((*MockIRepo)(nil).RecordCreditPurchase), ctx, purchase)
}

// RecordTradingVolume mocks base method.
func (m *MockIRepo) RecordTradingVolume(ctx context.Context, volume *domain.TokenAnalysisTradingVolume) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordTradingVolume", ctx, volume)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordTradingVolume indicates an expected call of RecordTradingVolume.
func (mr *MockIRepoMockRecorder) RecordTradingVolume(ctx, volume any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordTradingVolume", reflect.TypeOf((*MockIRepo)(nil).RecordTradingVolume), ctx, volume)
}

// TradingVolumeExists mocks base method.
func (m *MockIRepo) TradingVolumeExists(ctx context.Context, txHash string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TradingVolumeExists", ctx, txHash)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TradingVolumeExists indicates an expected call of TradingVolumeExists.
func (mr *MockIRepoMockRecorder) TradingVolumeExists(ctx, txHash any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TradingVolumeExists", reflect.TypeOf((*MockIRepo)(nil).TradingVolumeExists), ctx, txHash)
}

// UseCredit mocks base method.
func (m *MockIRepo) UseCredit(ctx context.Context, walletAddress string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UseCredit", ctx, walletAddress)
	ret0, _ := ret[0].(error)
	return ret0
}

// UseCredit indicates an expected call of UseCredit.
func (mr *MockIRepoMockRecorder) UseCredit(ctx, walletAddress any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UseCredit", reflect.TypeOf((*MockIRepo)(nil).UseCredit), ctx, walletAddress)
}
// HasReceivedFreeCreditToday mocks base method.
func (m *MockIRepo) HasReceivedFreeCreditToday(ctx context.Context, walletAddress string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasReceivedFreeCreditToday", ctx, walletAddress)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasReceivedFreeCreditToday indicates an expected call of HasReceivedFreeCreditToday.
func (mr *MockIRepoMockRecorder) HasReceivedFreeCreditToday(ctx, walletAddress any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasReceivedFreeCreditToday", reflect.TypeOf((*MockIRepo)(nil).HasReceivedFreeCreditToday), ctx, walletAddress)
}

// RecordFreeCreditToday mocks base method.
func (m *MockIRepo) RecordFreeCreditToday(ctx context.Context, walletAddress string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RecordFreeCreditToday", ctx, walletAddress)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecordFreeCreditToday indicates an expected call of RecordFreeCreditToday.
func (mr *MockIRepoMockRecorder) RecordFreeCreditToday(ctx, walletAddress any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecordFreeCreditToday", reflect.TypeOf((*MockIRepo)(nil).RecordFreeCreditToday), ctx, walletAddress)
}
