package chainsync

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
	gomock "go.uber.org/mock/gomock"
)

func TestSync(t *testing.T) {
	ctrl := gomock.NewController(t)

	ethAddr := domain.NewEvmAddress("******************************************")
	ethAddr2 := domain.NewEvmAddress("******************************************")
	tronAddr := domain.NewTronAddress("TMBeViiBxxSGmPiMgbvWBwF4ngSbmr8888")
	tronAddr2 := domain.NewTronAddress("TL1234aiqsJacvpji9QWMBazeEbTCKHpTN")
	tronAddr3 := domain.NewTronAddress("THoa8888hHCSNt2eWPY9M2qAgeqLxvhd5N")

	mockChainSyncRepo := NewMockIRepo(ctrl)
	mockChainClientEthereum := domain.NewMockChainClient(ctrl)
	mockChainClientTron := domain.NewMockChainClient(ctrl)
	mockChainFetcherEthereum := domain.NewMockChainSyncFetcher(ctrl)
	mockChainFetcherTron := domain.NewMockChainSyncFetcher(ctrl)
	mockHttpTaskExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	handlerURL := "/random/handle/url"

	// First loop
	mockChainSyncRepo.EXPECT().AcquireLock(gomock.Any(), gomock.Any(), gomock.Any()).Times(2).Return(nil)
	mockChainSyncRepo.EXPECT().ReleaseLock(gomock.Any(), gomock.Any()).Times(2).Return()
	mockChainSyncRepo.EXPECT().GetLastSyncedBlockNo(gomock.Any(), gomock.Any()).Times(2).DoAndReturn(func(ctx context.Context, chain domain.Chain) (uint64, error) {
		switch chain {
		case domain.Ethereum:
			return 5, nil
		case domain.Tron:
			return 8, nil
		}
		t.Fatalf("unexpected chain: %v", chain)
		return 0, nil
	})

	mockChainFetcherEthereum.EXPECT().FetchTxsInRange(gomock.Any(), domain.Ethereum, uint64(6), uint64(21)).Times(1).Return(uint64(12), []*domain.TransactionWithAddresses{
		{
			Chain:     domain.Ethereum,
			Hash:      "0x123",
			Addresses: []domain.Address{ethAddr, ethAddr2},
		},
	}, nil)
	mockChainFetcherTron.EXPECT().FetchTxsInRange(gomock.Any(), domain.Tron, uint64(9), uint64(24)).Times(1).Return(uint64(10), []*domain.TransactionWithAddresses{
		{
			Chain:     domain.Tron,
			Hash:      "0x456",
			Addresses: []domain.Address{tronAddr, tronAddr2},
		},
		{
			Chain:     domain.Tron,
			Hash:      "0x789",
			Addresses: []domain.Address{tronAddr, tronAddr3},
		},
	}, nil)

	mockChainSyncRepo.EXPECT().BatchCheckAddressOwnedByUser(gomock.Any(), gomock.Any(), gomock.Any()).Times(2).DoAndReturn(func(ctx context.Context, chain domain.Chain, addresses []domain.Address) (map[domain.Address]bool, error) {
		switch chain {
		case domain.Ethereum:
			assert.ElementsMatch(t, []domain.Address{ethAddr, ethAddr2}, addresses)
			return map[domain.Address]bool{ethAddr: true, ethAddr2: false}, nil
		case domain.Tron:
			assert.ElementsMatch(t, []domain.Address{tronAddr, tronAddr2, tronAddr3}, addresses)
			return map[domain.Address]bool{tronAddr: false, tronAddr2: true, tronAddr3: false}, nil
		}
		t.Fatalf("unexpected chain: %v", chain)
		return nil, nil
	})
	mockChainSyncRepo.EXPECT().BatchCheckAddressIsActivePaymentAddress(gomock.Any(), gomock.Any(), gomock.Any()).Times(2).DoAndReturn(func(ctx context.Context, chain domain.Chain, addresses []domain.Address) (map[domain.Address]bool, error) {
		switch chain {
		case domain.Ethereum:
			assert.ElementsMatch(t, []domain.Address{ethAddr, ethAddr2}, addresses)
			return map[domain.Address]bool{ethAddr: false, ethAddr2: false}, nil
		case domain.Tron:
			assert.ElementsMatch(t, []domain.Address{tronAddr, tronAddr2, tronAddr3}, addresses)
			return map[domain.Address]bool{tronAddr: false, tronAddr2: false, tronAddr3: false}, nil
		}
		t.Fatalf("unexpected chain: %v", chain)
		return nil, nil
	})

	ethTaskEnqueued := false
	tronTaskEnqueued := false
	mockHttpTaskExecutor.EXPECT().Execute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(2).DoAndReturn(func(ctx context.Context, queueID string, task *domain.HttpTask, taskName string) error {
		assert.Contains(t, task.URL, handlerURL)
		assert.Equal(t, "POST", task.Method)
		var res struct {
			ChainID string `json:"chain_id"`
			TxHash  string `json:"tx_hash"`
		}
		err := json.Unmarshal(task.Body, &res)
		assert.NoError(t, err)

		ethTaskName := fmt.Sprintf("%s-%s", domain.Ethereum.ID(), "0x123")
		tronTaskName := fmt.Sprintf("%s-%s", domain.Tron.ID(), "456")
		switch taskName {
		case ethTaskName:
			ethTaskEnqueued = true
			assert.Equal(t, domain.Ethereum.ID(), res.ChainID)
			assert.Equal(t, "0x123", res.TxHash)
		case tronTaskName:
			tronTaskEnqueued = true
			assert.Equal(t, domain.Tron.ID(), res.ChainID)
			assert.Equal(t, "456", res.TxHash)
		default:
			t.Fatalf("unexpected task name: %s", taskName)
		}
		return nil
	})

	mockChainSyncRepo.EXPECT().SetLastSyncedBlockNo(gomock.Any(), gomock.Any(), gomock.Any()).Times(2).DoAndReturn(func(ctx context.Context, chain domain.Chain, blockNo uint64) error {
		switch chain {
		case domain.Ethereum:
			assert.Equal(t, uint64(12), blockNo)
			return nil
		case domain.Tron:
			assert.Equal(t, uint64(10), blockNo)
			return nil
		}
		t.Fatalf("unexpected chain: %v", chain)
		return nil
	})

	Init(mockChainSyncRepo,
		map[domain.Chain]domain.ChainClient{domain.Ethereum: mockChainClientEthereum, domain.Tron: mockChainClientTron},
		map[domain.Chain]domain.ChainSyncFetcher{domain.Ethereum: mockChainFetcherEthereum, domain.Tron: mockChainFetcherTron},
		mockHttpTaskExecutor,
		handlerURL)

	// Create a context with timeout to simulate the API timeout
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	timeStart := time.Now()
	Sync(ctx)

	assert.True(t, ethTaskEnqueued)
	assert.True(t, tronTaskEnqueued)
	assert.InEpsilon(t, 1*time.Second, time.Since(timeStart), 0.5)
}
