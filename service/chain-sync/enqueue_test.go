package chainsync

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	time "time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/stretchr/testify/assert"
	gomock "go.uber.org/mock/gomock"
)

func TestEnqueue(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)

	handlerURL := "http://example.com/handler"
	queueID := config.GetString("CLOUD_TASK_CHAIN_SYNC_QUEUE")

	// Initialize the service with mocks
	Init(nil, nil, nil, mockExecutor, handlerURL)

	chain := domain.Arbitrum
	txHash := "0x1234567890abcdef"

	t.Run("success case", func(t *testing.T) {
		taskName := fmt.Sprintf("%s-%s", chain.ID(), txHash)
		bodyMap := map[string]string{
			"chain_id": chain.ID(),
			"tx_hash":  txHash,
		}
		body, _ := json.Marshal(bodyMap)

		// Expect Execute to be called with correct parameters
		mockExecutor.EXPECT().
			Execute(gomock.Any(), queueID, gomock.Any(), taskName).
			DoAndReturn(func(_ context.Context, _ string, task *domain.HttpTask, _ string) error {
				expectedTask := &domain.HttpTask{
					Method:  "POST",
					URL:     handlerURL,
					Body:    body,
					Timeout: 10 * time.Minute,
				}
				assert.Equal(t, expectedTask, task)
				return nil
			})

		err := Enqueue(context.Background(), chain, txHash)
		assert.Nil(t, err)
	})

	t.Run("success tron case", func(t *testing.T) {
		chain := domain.Tron
		txHash := "0x1234567890abcdef"
		taskName := fmt.Sprintf("%s-%s", chain.ID(), txHash[2:])
		bodyMap := map[string]string{
			"chain_id": chain.ID(),
			"tx_hash":  txHash[2:],
		}
		body, _ := json.Marshal(bodyMap)

		// Expect Execute to be called with correct parameters
		mockExecutor.EXPECT().
			Execute(gomock.Any(), queueID, gomock.Any(), taskName).
			DoAndReturn(func(_ context.Context, _ string, task *domain.HttpTask, _ string) error {
				expectedTask := &domain.HttpTask{
					Method:  "POST",
					URL:     handlerURL,
					Body:    body,
					Timeout: 10 * time.Minute,
				}
				assert.Equal(t, expectedTask, task)
				return nil
			})

		err := Enqueue(context.Background(), chain, txHash)
		assert.Nil(t, err)
	})

	t.Run("duplicate transaction", func(t *testing.T) {
		taskName := fmt.Sprintf("%s-%s", chain.ID(), txHash)
		bodyMap := map[string]string{
			"chain_id": chain.ID(),
			"tx_hash":  txHash,
		}
		body, _ := json.Marshal(bodyMap)

		expectedTask := &domain.HttpTask{
			Method:  "POST",
			URL:     handlerURL,
			Body:    body,
			Timeout: 10 * time.Minute,
		}

		// Simulate duplicate task error
		mockExecutor.EXPECT().
			Execute(gomock.Any(), queueID, expectedTask, taskName).
			Return(code.ErrCloudTaskDuplicated)

		err := Enqueue(context.Background(), chain, txHash)
		assert.Nil(t, err) // Expect no error on duplicate
	})

	t.Run("execute error", func(t *testing.T) {
		taskName := fmt.Sprintf("%s-%s", chain.ID(), txHash)
		bodyMap := map[string]string{
			"chain_id": chain.ID(),
			"tx_hash":  txHash,
		}
		body, _ := json.Marshal(bodyMap)

		expectedTask := &domain.HttpTask{
			Method:  "POST",
			URL:     handlerURL,
			Body:    body,
			Timeout: 10 * time.Minute,
		}

		execErr := fmt.Errorf("execution failed")

		// Expect Execute to return an error
		mockExecutor.EXPECT().
			Execute(gomock.Any(), queueID, expectedTask, taskName).
			Return(execErr)

		err := Enqueue(context.Background(), chain, txHash)
		assert.NotNil(t, err)
		assert.Equal(t, code.CloudTaskExecutionFailed, err.Code)
	})
}
