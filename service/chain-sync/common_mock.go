// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/service/chain-sync (interfaces: IRepo)
//
// Generated by this command:
//
//	mockgen -package=chainsync -self_package=github.com/kryptogo/kg-wallet-backend/service/chain-sync -destination=common_mock.go . IRepo
//

// Package chainsync is a generated GoMock package.
package chainsync

import (
	context "context"
	reflect "reflect"
	time "time"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockIRepo is a mock of IRepo interface.
type MockIRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIRepoMockRecorder
}

// MockIRepoMockRecorder is the mock recorder for MockIRepo.
type MockIRepoMockRecorder struct {
	mock *MockIRepo
}

// NewMockIRepo creates a new mock instance.
func NewMockIRepo(ctrl *gomock.Controller) *MockIRepo {
	mock := &MockIRepo{ctrl: ctrl}
	mock.recorder = &MockIRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRepo) EXPECT() *MockIRepoMockRecorder {
	return m.recorder
}

// AcquireLock mocks base method.
func (m *MockIRepo) AcquireLock(arg0 context.Context, arg1 string, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLock indicates an expected call of AcquireLock.
func (mr *MockIRepoMockRecorder) AcquireLock(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLock", reflect.TypeOf((*MockIRepo)(nil).AcquireLock), arg0, arg1, arg2)
}

// AcquireLockWithRetry mocks base method.
func (m *MockIRepo) AcquireLockWithRetry(arg0 context.Context, arg1 string, arg2, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLockWithRetry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLockWithRetry indicates an expected call of AcquireLockWithRetry.
func (mr *MockIRepoMockRecorder) AcquireLockWithRetry(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLockWithRetry", reflect.TypeOf((*MockIRepo)(nil).AcquireLockWithRetry), arg0, arg1, arg2, arg3)
}

// BatchCheckAddressIsActivePaymentAddress mocks base method.
func (m *MockIRepo) BatchCheckAddressIsActivePaymentAddress(arg0 context.Context, arg1 domain.Chain, arg2 []domain.Address) (map[domain.Address]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckAddressIsActivePaymentAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[domain.Address]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckAddressIsActivePaymentAddress indicates an expected call of BatchCheckAddressIsActivePaymentAddress.
func (mr *MockIRepoMockRecorder) BatchCheckAddressIsActivePaymentAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckAddressIsActivePaymentAddress", reflect.TypeOf((*MockIRepo)(nil).BatchCheckAddressIsActivePaymentAddress), arg0, arg1, arg2)
}

// BatchCheckAddressOwnedByUser mocks base method.
func (m *MockIRepo) BatchCheckAddressOwnedByUser(arg0 context.Context, arg1 domain.Chain, arg2 []domain.Address) (map[domain.Address]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckAddressOwnedByUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[domain.Address]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckAddressOwnedByUser indicates an expected call of BatchCheckAddressOwnedByUser.
func (mr *MockIRepoMockRecorder) BatchCheckAddressOwnedByUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckAddressOwnedByUser", reflect.TypeOf((*MockIRepo)(nil).BatchCheckAddressOwnedByUser), arg0, arg1, arg2)
}

// CheckAddressOwnedByUser mocks base method.
func (m *MockIRepo) CheckAddressOwnedByUser(arg0 context.Context, arg1 domain.Chain, arg2 domain.Address) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAddressOwnedByUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAddressOwnedByUser indicates an expected call of CheckAddressOwnedByUser.
func (mr *MockIRepoMockRecorder) CheckAddressOwnedByUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAddressOwnedByUser", reflect.TypeOf((*MockIRepo)(nil).CheckAddressOwnedByUser), arg0, arg1, arg2)
}

// CreatePaymentIntent mocks base method.
func (m *MockIRepo) CreatePaymentIntent(arg0 context.Context, arg1 *domain.PaymentIntent) (*domain.PaymentIntent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePaymentIntent", arg0, arg1)
	ret0, _ := ret[0].(*domain.PaymentIntent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePaymentIntent indicates an expected call of CreatePaymentIntent.
func (mr *MockIRepoMockRecorder) CreatePaymentIntent(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePaymentIntent", reflect.TypeOf((*MockIRepo)(nil).CreatePaymentIntent), arg0, arg1)
}

// GetLastSyncedBlockNo mocks base method.
func (m *MockIRepo) GetLastSyncedBlockNo(arg0 context.Context, arg1 domain.Chain) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastSyncedBlockNo", arg0, arg1)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastSyncedBlockNo indicates an expected call of GetLastSyncedBlockNo.
func (mr *MockIRepoMockRecorder) GetLastSyncedBlockNo(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastSyncedBlockNo", reflect.TypeOf((*MockIRepo)(nil).GetLastSyncedBlockNo), arg0, arg1)
}

// GetPaymentIntentByChainAddress mocks base method.
func (m *MockIRepo) GetPaymentIntentByChainAddress(arg0 context.Context, arg1 domain.ChainAddress) (*domain.PaymentIntent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentIntentByChainAddress", arg0, arg1)
	ret0, _ := ret[0].(*domain.PaymentIntent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentIntentByChainAddress indicates an expected call of GetPaymentIntentByChainAddress.
func (mr *MockIRepoMockRecorder) GetPaymentIntentByChainAddress(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentIntentByChainAddress", reflect.TypeOf((*MockIRepo)(nil).GetPaymentIntentByChainAddress), arg0, arg1)
}

// GetPaymentIntentByID mocks base method.
func (m *MockIRepo) GetPaymentIntentByID(arg0 context.Context, arg1 string) (*domain.PaymentIntent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentIntentByID", arg0, arg1)
	ret0, _ := ret[0].(*domain.PaymentIntent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentIntentByID indicates an expected call of GetPaymentIntentByID.
func (mr *MockIRepoMockRecorder) GetPaymentIntentByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentIntentByID", reflect.TypeOf((*MockIRepo)(nil).GetPaymentIntentByID), arg0, arg1)
}

// GetPaymentIntentStats mocks base method.
func (m *MockIRepo) GetPaymentIntentStats(arg0 context.Context, arg1 int) (*domain.PaymentIntentStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentIntentStats", arg0, arg1)
	ret0, _ := ret[0].(*domain.PaymentIntentStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentIntentStats indicates an expected call of GetPaymentIntentStats.
func (mr *MockIRepoMockRecorder) GetPaymentIntentStats(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentIntentStats", reflect.TypeOf((*MockIRepo)(nil).GetPaymentIntentStats), arg0, arg1)
}

// GetPaymentIntentStatsWithTimeRange mocks base method.
func (m *MockIRepo) GetPaymentIntentStatsWithTimeRange(arg0 context.Context, arg1 domain.GetPaymentIntentStatsParams) (*domain.PaymentIntentStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentIntentStatsWithTimeRange", arg0, arg1)
	ret0, _ := ret[0].(*domain.PaymentIntentStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPaymentIntentStatsWithTimeRange indicates an expected call of GetPaymentIntentStatsWithTimeRange.
func (mr *MockIRepoMockRecorder) GetPaymentIntentStatsWithTimeRange(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentIntentStatsWithTimeRange", reflect.TypeOf((*MockIRepo)(nil).GetPaymentIntentStatsWithTimeRange), arg0, arg1)
}

// GetPaymentIntents mocks base method.
func (m *MockIRepo) GetPaymentIntents(arg0 context.Context, arg1 domain.GetPaymentIntentsParams) ([]*domain.PaymentIntent, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPaymentIntents", arg0, arg1)
	ret0, _ := ret[0].([]*domain.PaymentIntent)
	ret1, _ := ret[1].(int)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPaymentIntents indicates an expected call of GetPaymentIntents.
func (mr *MockIRepoMockRecorder) GetPaymentIntents(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPaymentIntents", reflect.TypeOf((*MockIRepo)(nil).GetPaymentIntents), arg0, arg1)
}

// GetPendingIntents mocks base method.
func (m *MockIRepo) GetPendingIntents(arg0 context.Context) ([]*domain.PaymentIntent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPendingIntents", arg0)
	ret0, _ := ret[0].([]*domain.PaymentIntent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingIntents indicates an expected call of GetPendingIntents.
func (mr *MockIRepoMockRecorder) GetPendingIntents(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingIntents", reflect.TypeOf((*MockIRepo)(nil).GetPendingIntents), arg0)
}

// GetPendingIntentsByPaymentAddresses mocks base method.
func (m *MockIRepo) GetPendingIntentsByPaymentAddresses(arg0 context.Context, arg1 domain.Chain, arg2 []domain.Address) (map[domain.Address]*domain.PaymentIntent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPendingIntentsByPaymentAddresses", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[domain.Address]*domain.PaymentIntent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingIntentsByPaymentAddresses indicates an expected call of GetPendingIntentsByPaymentAddresses.
func (mr *MockIRepoMockRecorder) GetPendingIntentsByPaymentAddresses(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingIntentsByPaymentAddresses", reflect.TypeOf((*MockIRepo)(nil).GetPendingIntentsByPaymentAddresses), arg0, arg1, arg2)
}

// GetUsersForAddressNotification mocks base method.
func (m *MockIRepo) GetUsersForAddressNotification(arg0 context.Context, arg1, arg2, arg3 string) ([]*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersForAddressNotification", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersForAddressNotification indicates an expected call of GetUsersForAddressNotification.
func (mr *MockIRepoMockRecorder) GetUsersForAddressNotification(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersForAddressNotification", reflect.TypeOf((*MockIRepo)(nil).GetUsersForAddressNotification), arg0, arg1, arg2, arg3)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockIRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*domain.OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*domain.OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockIRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockIRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// ReleaseLock mocks base method.
func (m *MockIRepo) ReleaseLock(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseLock", arg0, arg1)
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockIRepoMockRecorder) ReleaseLock(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockIRepo)(nil).ReleaseLock), arg0, arg1)
}

// SetLastSyncedBlockNo mocks base method.
func (m *MockIRepo) SetLastSyncedBlockNo(arg0 context.Context, arg1 domain.Chain, arg2 uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLastSyncedBlockNo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLastSyncedBlockNo indicates an expected call of SetLastSyncedBlockNo.
func (mr *MockIRepoMockRecorder) SetLastSyncedBlockNo(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLastSyncedBlockNo", reflect.TypeOf((*MockIRepo)(nil).SetLastSyncedBlockNo), arg0, arg1, arg2)
}

// UpdatePaymentIntent mocks base method.
func (m *MockIRepo) UpdatePaymentIntent(arg0 context.Context, arg1 string, arg2 *domain.PaymentIntentUpdate) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePaymentIntent", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePaymentIntent indicates an expected call of UpdatePaymentIntent.
func (mr *MockIRepoMockRecorder) UpdatePaymentIntent(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePaymentIntent", reflect.TypeOf((*MockIRepo)(nil).UpdatePaymentIntent), arg0, arg1, arg2)
}
