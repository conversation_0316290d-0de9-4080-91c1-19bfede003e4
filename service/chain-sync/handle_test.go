package chainsync

import (
	"context"
	"math/big"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/chain/tron"
	"github.com/kryptogo/kg-wallet-backend/domain"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/fcm"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	ws "github.com/kryptogo/kg-wallet-backend/pkg/websocket"
	"github.com/kryptogo/kg-wallet-backend/service/asset"
	"github.com/kryptogo/kg-wallet-backend/service/notification"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	gomock "go.uber.org/mock/gomock"
)

func TestHandle(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// reset rdb. It should be removed after tx service is decoupled with repo
	rdb.Reset()

	// Mock dependencies
	mockRepo := NewMockIRepo(ctrl)
	mockChainClient := domain.NewMockChainClient(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Tron, domain.Arbitrum}).AnyTimes()
	mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockAssetRepo := asset.NewMockIRepo(ctrl)

	// Initialize services with mocks
	Init(mockRepo, map[domain.Chain]domain.ChainClient{
		domain.Arbitrum: mockChainClient,
	}, nil, mockExecutor, "http://example.com/handler")

	tokenmeta.Init(mockTokenMetaRepo, nil, nil)
	asset.Init(mockAssetRepo, map[domain.Chain]domain.ChainClient{
		domain.Arbitrum: mockChainClient,
	}, nil, nil, mockPriceFetcher)
	mockNotificationRepo := domain.NewMockNotificationRepo(ctrl)
	notification.Init(mockNotificationRepo)

	mockUserRepo := domain.NewMockUserRepo(ctrl)
	tx.Init(mockUserRepo)
	mockWebSocketService := ws.NewMockIService(ctrl)
	ws.Set(mockWebSocketService)

	// Define test data
	chain := domain.Arbitrum
	txHash := "test-tx-hash"

	txDetail := &domain.TransactionDetail{
		Chain:     chain,
		Hash:      txHash,
		BlockNum:  123456,
		IsError:   false,
		From:      domain.NewStrAddress("0xSender1"),
		To:        domain.NewStrAddress("0xReceiver1"),
		Value:     big.NewInt(1000000000000000000), // 1 ETH
		Data:      "0xData",
		GasPrice:  big.NewInt(20000000000),
		GasUsed:   big.NewInt(21000),
		MethodID:  "0xMethodID",
		Timestamp: time.Now(),
		TransactionTransfers: domain.TransactionTransfers{
			InternalTransfers: []*domain.NativeTokenTransfer{
				{
					From:   domain.NewStrAddress("0xReceiver1"),
					To:     domain.NewStrAddress("0xReceiver2"),
					Amount: big.NewInt(1000000000000000000),
				},
				{
					From:   domain.NewStrAddress("0xReceiver2"),
					To:     domain.NewStrAddress("0xReceiver3"),
					Amount: big.NewInt(1000000000000000000),
				},
			},
			TokenTransfers: []*domain.TokenTransfer{
				{
					Contract: domain.NewStrAddress("0xContract2"),
					From:     domain.NewStrAddress("0xSender2"),
					To:       domain.NewStrAddress("0xReceiver2"),
					Amount:   big.NewInt(200000000000000000),
				},
				{
					Contract: domain.NewStrAddress("0xContract3"),
					From:     domain.NewStrAddress("0xSender3"),
					To:       domain.NewStrAddress("0xReceiver3"),
					Amount:   big.NewInt(300000000000000000),
				},
			},
			NftTransfers: []*domain.NftTransfer{},
		},
	}

	// Mock validateAndGetTransactionDetails
	mockChainClient.EXPECT().
		WaitUntilTransactionConfirmed(gomock.Any(), txHash).
		Return(domain.TransactionStatusSuccess, nil)

	mockChainClient.EXPECT().
		TransactionDetail(gomock.Any(), txHash).
		Return(txDetail, nil)

	mockChainClient.EXPECT().
		NativeBalance(gomock.Any(), domain.NewStrAddress("0xReceiver1")).
		Return(big.NewInt(1100000000000000000), nil)

	mockChainClient.EXPECT().
		NativeBalance(gomock.Any(), domain.NewStrAddress("0xReceiver3")).
		Return(big.NewInt(1200000000000000000), nil)

	// Mock TokenBalance for Contract2
	mockChainClient.EXPECT().
		TokenBalance(gomock.Any(), domain.NewStrAddress("0xSender2"), "0xContract2").
		Return(big.NewInt(800000000000000000), nil)

	// Mock TokenBalance for Contract3
	mockChainClient.EXPECT().
		TokenBalance(gomock.Any(), domain.NewStrAddress("0xSender3"), "0xContract3").
		Return(big.NewInt(700000000000000000), nil)

	mockChainClient.EXPECT().
		TokenBalance(gomock.Any(), domain.NewStrAddress("0xReceiver3"), "0xContract3").
		Return(big.NewInt(300000000000000000), nil)

	// Mock identifyChangedBalances
	mockRepo.EXPECT().
		BatchCheckAddressOwnedByUser(gomock.Any(), chain, gomock.Any()).
		DoAndReturn(func(ctx context.Context, chain domain.Chain, addresses []domain.Address) (map[domain.Address]bool, error) {
			assert.ElementsMatch(t, addresses, []domain.Address{
				domain.NewStrAddress("0xSender1"),
				domain.NewStrAddress("0xReceiver1"),
				domain.NewStrAddress("0xSender2"),
				domain.NewStrAddress("0xReceiver2"),
				domain.NewStrAddress("0xSender3"),
				domain.NewStrAddress("0xReceiver3"),
			})
			return map[domain.Address]bool{
				domain.NewStrAddress("0xReceiver1"): true,
				domain.NewStrAddress("0xSender2"):   true,
				domain.NewStrAddress("0xSender3"):   true,
				domain.NewStrAddress("0xReceiver3"): true,
			}, nil
		})
	mockRepo.EXPECT().
		BatchCheckAddressIsActivePaymentAddress(gomock.Any(), chain, gomock.Any()).
		DoAndReturn(func(ctx context.Context, chain domain.Chain, addresses []domain.Address) (map[domain.Address]bool, error) {
			assert.ElementsMatch(t, addresses, []domain.Address{
				domain.NewStrAddress("0xSender1"),
				domain.NewStrAddress("0xReceiver1"),
				domain.NewStrAddress("0xSender2"),
				domain.NewStrAddress("0xReceiver2"),
				domain.NewStrAddress("0xSender3"),
				domain.NewStrAddress("0xReceiver3"),
			})
			return map[domain.Address]bool{
				domain.NewStrAddress("0xReceiver1"): false,
				domain.NewStrAddress("0xSender2"):   false,
				domain.NewStrAddress("0xSender3"):   false,
				domain.NewStrAddress("0xReceiver3"): false,
			}, nil
		})

	// Mock getTokenMetadata
	mockTokenMetaRepo.EXPECT().
		BatchGetTokenMetadata(gomock.Any(), []domain.ChainToken{
			{Chain: chain, TokenID: "0xContract2"},
			{Chain: chain, TokenID: "0xContract3"},
		}).
		Return(map[domain.ChainToken]*domain.TokenMetadata{
			{
				Chain:   chain,
				TokenID: "0xContract2",
			}: {
				Name:        "Contract2",
				Symbol:      "C2",
				Decimals:    16,
				CoingeckoID: "contract2",
				LogoUrl:     "https://etherscan.io/token/images/ethereum_28.png",
				IsVerified:  true,
			},
			{
				Chain:   chain,
				TokenID: "0xContract3",
			}: {
				Name:        "Contract3",
				Symbol:      "C3",
				Decimals:    18,
				CoingeckoID: "contract3",
				LogoUrl:     "https://etherscan.io/token/images/ethereum_28.png",
				IsVerified:  true,
			},
		}, nil).AnyTimes()

	// Mock updateAssetBalances
	mockAssetRepo.EXPECT().
		UpdateTokenAmounts(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, amounts map[domain.ChainAddress][]*domain.TokenAmount) error {
			expectedNativeAmounts := map[domain.ChainAddress][]*domain.TokenAmount{
				{Chain: chain, Address: domain.NewStrAddress("0xReceiver1")}: {
					{
						Token:  chain.MainToken(),
						Amount: decimal.NewFromBigInt(big.NewInt(1100000000000000000), -18), // 1.1 ETH
					},
				},
				{Chain: chain, Address: domain.NewStrAddress("0xReceiver3")}: {
					{
						Token:  chain.MainToken(),
						Amount: decimal.NewFromBigInt(big.NewInt(1200000000000000000), -18), // 1.1 ETH
					},
				},
			}
			expectedTokenAmounts := map[domain.ChainAddress][]*domain.TokenAmount{
				{Chain: chain, Address: domain.NewStrAddress("0xSender2")}: {
					{
						Token:  domain.NewToken(chain, "0xContract2", "Contract2", "C2", "https://etherscan.io/token/images/ethereum_28.png", 16, true),
						Amount: decimal.NewFromBigInt(big.NewInt(800000000000000000), -16), // 1.1 ETH
					},
				},
				{Chain: chain, Address: domain.NewStrAddress("0xSender3")}: {
					{
						Token:  domain.NewToken(chain, "0xContract3", "Contract3", "C3", "https://etherscan.io/token/images/ethereum_28.png", 18, true),
						Amount: decimal.NewFromBigInt(big.NewInt(700000000000000000), -18), // 1.1 ETH
					},
				},
				{Chain: chain, Address: domain.NewStrAddress("0xReceiver3")}: {
					{
						Token:  domain.NewToken(chain, "0xContract3", "Contract3", "C3", "https://etherscan.io/token/images/ethereum_28.png", 18, true),
						Amount: decimal.NewFromBigInt(big.NewInt(300000000000000000), -18), // 0.3 ETH
					},
				},
			}
			if len(expectedNativeAmounts) == len(amounts) {
				for k, v := range expectedNativeAmounts {
					assert.Equal(t, v, amounts[k])
				}
			}
			if len(expectedTokenAmounts) == len(amounts) {
				for k, v := range expectedTokenAmounts {
					assert.Equal(t, v, amounts[k])
				}
			}
			return nil
		}).Times(2)

	mockRepo.EXPECT().
		GetUsersForAddressNotification(gomock.Any(), chain.ID(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chainID, address, clientID string) ([]*domain.UserData, error) {
			assert.Contains(t, []string{
				"0xReceiver1",
				"0xSender2",
				"0xSender3",
				"0xReceiver3",
			}, address)
			return []*domain.UserData{}, nil
		}).
		AnyTimes()

	mockWebSocketService.EXPECT().
		SendEventByAddresses(gomock.Any(), gomock.Any(), ws.EventWalletAssetsUpdated, nil).
		DoAndReturn(func(ctx context.Context, addresses []string, eventName ws.WsEventName, data interface{}) error {
			if len(addresses) == 2 {
				assert.ElementsMatch(t, []string{"0xReceiver1", "0xReceiver3"}, addresses)
			} else if len(addresses) == 3 {
				assert.ElementsMatch(t, []string{"0xSender2", "0xSender3", "0xReceiver3"}, addresses)
			}
			return nil
		}).
		Times(2)

	mockTokenMetaRepo.EXPECT().
		IsTokenSpam(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chain domain.Chain, tokenID string) (bool, error) {
			assert.Contains(t, []string{
				"0xContract2",
				"0xContract3",
			}, tokenID)
			return false, nil
		}).
		AnyTimes()

	// Note: GetPricesByContract is called during async price processing, not during Handle
	// We don't expect it to be called in this test since we're returning valid cached prices

	// Mock SetAssetPrices - this will be called when UpdateTokenBalances saves the fetched prices
	mockAssetRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, prices []*domain.TokenPrice) error {
		assert.Equal(t, 2, len(prices))
		assert.Equal(t, chain, prices[0].Chain)
		assert.ElementsMatch(t, []string{"0xContract2", "0xContract3"}, []string{prices[0].ID, prices[1].ID})
		return nil
	}).Times(1)

	// Mock AddRealtimeTokenPriceTask for the price fetching
	mockAssetRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasks []domain.RealtimeTokenPriceTask) error {
		assert.Equal(t, 2, len(tasks))
		assert.ElementsMatch(t, []domain.ChainToken{
			{Chain: chain, TokenID: "0xContract2"},
			{Chain: chain, TokenID: "0xContract3"},
		}, []domain.ChainToken{tasks[0].ChainToken, tasks[1].ChainToken})
		return nil
	})

	// Mock GetRealtimeTokenPrice for the realtime price polling
	mockAssetRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		// Return price responses for the requested tokens as hot prices to simulate successful cache hits
		hotResponses := make([]domain.RealtimeTokenPriceResponse, len(tokens))
		for i, token := range tokens {
			var price domain.Price = 1.0
			if token.TokenID == "0xContract3" {
				price = 2.0
			}
			hotResponses[i] = domain.RealtimeTokenPriceResponse{
				ChainToken:     token,
				PriceUSD:       price,
				LastUpdateTime: time.Now(),
			}
		}
		return hotResponses, []domain.RealtimeTokenPriceResponse{}, nil
	}).Times(1)

	// Execute Handle
	err := Handle(context.Background(), chain, txHash)
	assert.Nil(t, err)
}

func TestHandleTron(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	// reset rdb. It should be removed after tx service is decoupled with repo
	rdb.Reset()

	// Mock dependencies
	mockRepo := NewMockIRepo(ctrl)
	mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockAssetRepo := asset.NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Tron, domain.Arbitrum}).AnyTimes()

	// Initialize services with mocks
	client := tron.GetClient(domain.Tron)
	Init(mockRepo, map[domain.Chain]domain.ChainClient{
		domain.Tron: client,
	}, nil, mockExecutor, "http://example.com/handler")

	tokenmeta.Init(mockTokenMetaRepo, nil, nil)
	asset.Init(mockAssetRepo, map[domain.Chain]domain.ChainClient{
		domain.Tron: client,
	}, nil, nil, mockPriceFetcher)
	mockNotificationRepo := domain.NewMockNotificationRepo(ctrl)
	notification.Init(mockNotificationRepo)

	mockUserRepo := domain.NewMockUserRepo(ctrl)
	tx.Init(mockUserRepo)
	mockWebSocketService := ws.NewMockIService(ctrl)
	ws.Set(mockWebSocketService)
	mockFcmService := fcm.NewMockIService(ctrl)
	fcm.Set(mockFcmService)

	// Define test data
	chain := domain.Tron
	txHash := "a3eba918ba3be2e71147b669790bdaea5f07eea648fe4b3baaab4edb96c6508c"
	from := domain.NewTronAddress("TXKLyrnT1ujFo93rdfr2bJTEVkEo5jjrfx")
	to := domain.NewTronAddress("TBjXaF4w3ca1NKnSSHzTch79HbLzzjQA3o")
	usdt := domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t")

	// Setup mock for AddNotification
	mockNotificationRepo.EXPECT().
		AddNotification(gomock.Any(), gomock.Any(), true).
		DoAndReturn(func(ctx context.Context, notification *domain.Notification, sentToAllClient bool) (int, error) {
			assert.Equal(t, domain.NotificationMessageTypeTransaction, notification.MessageType)
			assert.Equal(t, "user1", notification.Receiver)
			assert.Equal(t, "TXKLyr...jrfx・送出 1 USDT", notification.Title.GetLocaleString("zh_TW"))
			assert.Equal(t, "💸您已成功送出 1 USDT！\n👾發送給：TBjXaF...QA3o", notification.Summary.GetLocaleString("zh_TW"))
			return 5, nil
		})

	// Setup mock for GetUserClientIDs
	mockNotificationRepo.EXPECT().
		GetUserClientIDs(gomock.Any(), "user1").
		Return([]string{"client1"}, nil)

	// Setup mock for GetUserLocale
	mockNotificationRepo.EXPECT().
		GetUserLocale(gomock.Any(), "user1", "client1").
		Return("zh_TW", nil)

	// Setup mock for GetFcmTokens
	mockNotificationRepo.EXPECT().
		GetFcmTokens(gomock.Any(), "user1", "client1").
		Return([]string{"fcm_token_1"}, nil)

	// Setup mock for SetUserFcmTokens
	mockNotificationRepo.EXPECT().
		SetUserFcmTokens(gomock.Any(), "user1", "client1", []string{"fcm_token_1"}).
		Return(nil)

	// Setup mock for SendMulticastMessage
	mockFcmService.EXPECT().
		SendMulticastMessage(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, title, summary string, messageType domain.NotificationMessageType, tokens []string, notificationID int) ([]string, error) {
			assert.Equal(t, "TXKLyr...jrfx・送出 1 USDT", title)
			assert.Equal(t, "💸您已成功送出 1 USDT！\n👾發送給：TBjXaF...QA3o", summary)
			return tokens, nil
		})

	// Mock identifyChangedBalances
	mockRepo.EXPECT().
		BatchCheckAddressOwnedByUser(gomock.Any(), chain, gomock.Any()).
		DoAndReturn(func(ctx context.Context, chain domain.Chain, addresses []domain.Address) (map[domain.Address]bool, error) {
			assert.ElementsMatch(t, addresses, []domain.Address{from, to, usdt})
			return map[domain.Address]bool{
				from: true,
				to:   true,
			}, nil
		})
	mockRepo.EXPECT().
		BatchCheckAddressIsActivePaymentAddress(gomock.Any(), chain, gomock.Any()).
		DoAndReturn(func(ctx context.Context, chain domain.Chain, addresses []domain.Address) (map[domain.Address]bool, error) {
			assert.ElementsMatch(t, addresses, []domain.Address{from, to, usdt})
			return map[domain.Address]bool{
				from: false,
				to:   false,
			}, nil
		})

	mockRepo.EXPECT().
		GetUsersForAddressNotification(gomock.Any(), chain.ID(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chainID, address, clientID string) ([]*domain.UserData, error) {
			assert.Contains(t, []string{from.String(), to.String()}, address)
			if address == from.String() {
				return []*domain.UserData{
					{UserInfo: domain.UserInfo{UID: "user1"}},
				}, nil
			}
			return []*domain.UserData{}, nil
		}).
		Times(2)

	// Mock updateAssetBalances
	mockAssetRepo.EXPECT().
		UpdateTokenAmounts(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, amounts map[domain.ChainAddress][]*domain.TokenAmount) error {
			if amount, ok := amounts[domain.ChainAddress{Chain: chain, Address: from}]; ok {
				assert.Equal(t, 1, len(amount))
				assert.Equal(t, "_", amount[0].Token.ID())
				assert.True(t, amount[0].Amount.GreaterThan(decimal.Zero))
			}
			if amount, ok := amounts[domain.ChainAddress{Chain: chain, Address: to}]; ok {
				assert.Equal(t, 1, len(amount))
				assert.Equal(t, usdt.String(), amount[0].Token.ID())
				assert.True(t, amount[0].Amount.GreaterThan(decimal.Zero))
			}
			return nil
		}).Times(2)
	mockAssetRepo.EXPECT().
		DeleteTokenAmounts(gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	mockWebSocketService.EXPECT().
		SendEventByAddresses(gomock.Any(), gomock.Any(), ws.EventWalletAssetsUpdated, nil).
		DoAndReturn(func(ctx context.Context, addresses []string, _ ws.WsEventName, data interface{}) error {
			if len(addresses) == 1 {
				assert.Contains(t, []string{from.String(), to.String()}, addresses[0])
			}
			return nil
		}).Times(2)

	mockWebSocketService.EXPECT().
		SendEventByUIDs(gomock.Any(), []string{"user1"}, ws.EventWalletAssetsUpdated, nil).
		Return(nil)

	mockWebSocketService.EXPECT().
		SendEventByUIDs(gomock.Any(), []string{"user1"}, ws.EventWalletTxListUpdated, nil).
		Return(nil)

	// Mock token metadata check
	mockTokenMetaRepo.EXPECT().
		IsTokenSpam(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chain domain.Chain, tokenID string) (bool, error) {
			return false, nil
		}).
		AnyTimes()

	// Note: GetPricesByContract is called during async price processing, not during Handle
	// We don't expect it to be called in this test since we're returning valid cached prices

	// Mock SetAssetPrices - this will be called when UpdateTokenBalances saves the fetched prices
	mockAssetRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, prices []*domain.TokenPrice) error {
		assert.Equal(t, 1, len(prices))
		assert.Equal(t, chain, prices[0].Chain)
		assert.Equal(t, usdt.String(), prices[0].ID)
		assert.Equal(t, float64(1.0), prices[0].Price)
		return nil
	}).Times(1)

	// Mock AddRealtimeTokenPriceTask for the price fetching
	mockAssetRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tasks []domain.RealtimeTokenPriceTask) error {
		assert.Equal(t, 1, len(tasks))
		assert.Equal(t, domain.ChainToken{Chain: chain, TokenID: usdt.String()}, tasks[0].ChainToken)
		return nil
	})

	// Mock GetRealtimeTokenPrice for the realtime price polling
	mockAssetRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		// Return price responses for the requested tokens as hot prices to simulate successful cache hits
		hotResponses := make([]domain.RealtimeTokenPriceResponse, len(tokens))
		for i, token := range tokens {
			hotResponses[i] = domain.RealtimeTokenPriceResponse{
				ChainToken:     token,
				PriceUSD:       domain.Price(1.0), // USDT price
				LastUpdateTime: time.Now(),
			}
		}
		return hotResponses, []domain.RealtimeTokenPriceResponse{}, nil
	}).Times(1)

	// Execute Handle
	kgErr := Handle(ctx, chain, txHash)
	assert.Nil(t, kgErr)

	tx, err := rdb.TxList(ctx, "tron", from.String(), txHash)
	assert.Nil(t, err)
	assert.NotNil(t, tx)
	assert.Equal(t, "tron", tx.ChainID)
	assert.Equal(t, from.String(), tx.Address)
	assert.Equal(t, "a3eba918ba3be2e71147b669790bdaea5f07eea648fe4b3baaab4edb96c6508c", tx.TxHash)
	assert.Equal(t, uint32(66565632), tx.BlockNum)
	assert.Equal(t, int32(7), tx.TxType)
	assert.NotNil(t, tx.Send)
	assert.Nil(t, tx.Receive)
	assert.Equal(t, "1 USDT", *tx.Send)
	assert.Equal(t, float64(0.345), tx.FeeDecimal)
	assert.Equal(t, "345000", tx.Fee)
	assert.Equal(t, to.String(), tx.TargetAddress)

	tx, err = rdb.TxList(ctx, "tron", to.String(), txHash)
	assert.Nil(t, err)
	assert.NotNil(t, tx)
	assert.Equal(t, "tron", tx.ChainID)
	assert.Equal(t, to.String(), tx.Address)
	assert.Equal(t, "a3eba918ba3be2e71147b669790bdaea5f07eea648fe4b3baaab4edb96c6508c", tx.TxHash)
	assert.Equal(t, uint32(66565632), tx.BlockNum)
	assert.Equal(t, int32(6), tx.TxType)
	assert.Nil(t, tx.Send)
	assert.NotNil(t, tx.Receive)
	assert.Equal(t, "1 USDT", *tx.Receive)
	assert.Equal(t, float64(0), tx.FeeDecimal)
	assert.Equal(t, "0", tx.Fee)
	assert.Equal(t, from.String(), tx.TargetAddress)
}

func TestHandleTronWithExistingTxDetail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	// reset rdb
	rdb.Reset()

	// Mock dependencies
	mockRepo := NewMockIRepo(ctrl)
	mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockAssetRepo := asset.NewMockIRepo(ctrl)
	mockPriceFetcher := domain.NewMockPriceFetcher(ctrl)
	mockPriceFetcher.EXPECT().PricesByContractSupportedChains().Return([]domain.Chain{domain.Tron, domain.Arbitrum}).AnyTimes()

	// Initialize services with mocks
	client := tron.GetClient(domain.Tron)
	Init(mockRepo, map[domain.Chain]domain.ChainClient{
		domain.Tron: client,
	}, nil, mockExecutor, "http://example.com/handler")

	tokenmeta.Init(mockTokenMetaRepo, nil, nil)
	asset.Init(mockAssetRepo, map[domain.Chain]domain.ChainClient{
		domain.Tron: client,
	}, nil, nil, mockPriceFetcher)
	mockNotificationRepo := domain.NewMockNotificationRepo(ctrl)
	notification.Init(mockNotificationRepo)

	mockUserRepo := domain.NewMockUserRepo(ctrl)
	tx.Init(mockUserRepo)
	mockWebSocketService := ws.NewMockIService(ctrl)
	ws.Set(mockWebSocketService)

	// Define test data
	chain := domain.Tron
	txHash := "a3eba918ba3be2e71147b669790bdaea5f07eea648fe4b3baaab4edb96c6508c"
	from := domain.NewTronAddress("TXKLyrnT1ujFo93rdfr2bJTEVkEo5jjrfx")
	to := domain.NewTronAddress("TBjXaF4w3ca1NKnSSHzTch79HbLzzjQA3o")

	// Mock with any params
	mockRepo.EXPECT().
		BatchCheckAddressOwnedByUser(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(map[domain.Address]bool{
			from: true,
			to:   true,
		}, nil).
		AnyTimes()
	mockRepo.EXPECT().
		BatchCheckAddressIsActivePaymentAddress(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(map[domain.Address]bool{
			from: false,
			to:   false,
		}, nil).
		AnyTimes()

	mockRepo.EXPECT().
		GetUsersForAddressNotification(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return([]*domain.UserData{}, nil).
		AnyTimes()

	mockAssetRepo.EXPECT().
		UpdateTokenAmounts(gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	mockAssetRepo.EXPECT().
		DeleteTokenAmounts(gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	mockWebSocketService.EXPECT().
		SendEventByAddresses(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	// Mock token metadata check
	mockTokenMetaRepo.EXPECT().
		IsTokenSpam(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chain domain.Chain, tokenID string) (bool, error) {
			return false, nil
		}).
		AnyTimes()

	// Note: GetPricesByContract is called during async price processing, not during Handle
	// We don't expect it to be called in this test since we're returning valid cached prices

	// Mock SetAssetPrices - this will be called when UpdateTokenBalances saves the fetched prices
	mockAssetRepo.EXPECT().SetAssetPrices(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	// Mock AddRealtimeTokenPriceTask for the price fetching
	mockAssetRepo.EXPECT().AddRealtimeTokenPriceTask(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	// Mock GetRealtimeTokenPrice for the realtime price polling
	mockAssetRepo.EXPECT().GetRealtimeTokenPrice(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) ([]domain.RealtimeTokenPriceResponse, []domain.RealtimeTokenPriceResponse, error) {
		// Return price responses for the requested tokens as hot prices to simulate successful cache hits
		hotResponses := make([]domain.RealtimeTokenPriceResponse, len(tokens))
		for i, token := range tokens {
			hotResponses[i] = domain.RealtimeTokenPriceResponse{
				ChainToken:     token,
				PriceUSD:       domain.Price(1.0), // USDT price
				LastUpdateTime: time.Now(),
			}
		}
		return hotResponses, []domain.RealtimeTokenPriceResponse{}, nil
	}).Times(1)

	// Insert a wrong TxDetail before Handle
	err := rdb.SetTxDetails(ctx, "tron", from.String(), txHash, []*dbmodel.TxDetail{
		{
			ChainID:         "tron",
			Address:         from.String(),
			TxHash:          txHash,
			BlockNum:        66565632,
			Category:        "txlist",
			FromAddress:     from.String(),
			ToAddress:       to.String(),
			ContractAddress: "",
			Asset:           "TRX",
			Value:           "10000000",
			ValueDecimals:   6,
			ValueDecimal:    10.0,
			GasPrice:        "0",
			GasUsed:         "13499850",
			TxTimestamp:     time.Now(),
			ModifiedAt:      time.Now(),
		},
	})
	assert.Nil(t, err)

	// Execute Handle
	kgErr := Handle(ctx, chain, txHash)
	assert.Nil(t, kgErr)

	// the new tx details should overwrite the old ones
	tx, err := rdb.TxList(ctx, "tron", from.String(), txHash)
	assert.Nil(t, err)
	assert.NotNil(t, tx)
	assert.Equal(t, "tron", tx.ChainID)
	assert.Equal(t, from.String(), tx.Address)
	assert.Equal(t, "a3eba918ba3be2e71147b669790bdaea5f07eea648fe4b3baaab4edb96c6508c", tx.TxHash)
	assert.Equal(t, uint32(66565632), tx.BlockNum)
	assert.Equal(t, int32(7), tx.TxType)
	assert.NotNil(t, tx.Send)
	assert.Nil(t, tx.Receive)
	assert.Equal(t, "1 USDT", *tx.Send)
	assert.Equal(t, float64(0.345), tx.FeeDecimal)
	assert.Equal(t, "345000", tx.Fee)
	assert.Equal(t, to.String(), tx.TargetAddress)
}
