package payment

import (
	"context"
	"encoding/json"
	"sync"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestExpireIntent(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	chain := domain.Sepolia
	payToken := "USDC"
	clientID := "client123"
	fiatAmount := "150"
	fiatCurrency := "USD"
	callbackURL := "http://test.com"
	paymentAddress := domain.NewEvmAddress("0x123456")
	scenarioID := "test-scenario"
	intent := &domain.PaymentIntent{
		ID:             "test-intent-id",
		PaymentChain:   chain,
		ClientID:       clientID,
		PaymentAddress: paymentAddress,
		FiatAmount:     util.Ptr(lo.Must(decimal.NewFromString(fiatAmount))),
		FiatCurrency:   util.Ptr(fiatCurrency),
		Symbol:         payToken,
		Status:         domain.PaymentIntentStatusPending,
		CallbackURL:    &callbackURL,
		GroupKey:       &scenarioID,
	}

	// Create a WaitGroup to wait for async callback to complete
	var wg sync.WaitGroup
	wg.Add(1)

	// Mock application repo for the callback signature generation
	mockAppRepo := domain.NewMockApplicationRepo(ctrl)
	mockAppRepo.EXPECT().GetApplication(gomock.Any(), clientID).Return(&domain.Application{
		ClientID:     clientID,
		ClientSecret: "test-secret",
		Name:         "Test App",
		Domain:       "test.com",
	}, nil)
	application.Init(mockAppRepo)

	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockRepo.EXPECT().AcquireLock(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, intentID string, duration time.Duration) (bool, error) {
		return true, nil
	})
	mockRepo.EXPECT().UpdatePaymentIntent(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, intentID string, update *domain.PaymentIntentUpdate) error {
		assert.Equal(t, intentID, "test-intent-id")
		assert.NotNil(t, update)
		assert.NotNil(t, update.Status)
		assert.Equal(t, *update.Status, domain.PaymentIntentStatusExpired)
		return nil
	})
	mockRepo.EXPECT().ReleaseLock(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, intentID string) error {
		return nil
	})

	mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
	mockCallbackExecutor.EXPECT().SendRequest(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, url string, body []byte, execCtx domain.CallbackExecutionContext) (*resty.Response, error) {
		t.Logf("callback request: %s", string(body))
		assert.Equal(t, url, callbackURL)
		bodyStruct := &CallbackPayload{}
		err := json.Unmarshal(body, bodyStruct)
		assert.NoError(t, err)
		assert.Equal(t, bodyStruct.PaymentIntentID, "test-intent-id")
		assert.Equal(t, bodyStruct.ClientID, clientID)
		assert.Equal(t, bodyStruct.Status, string(domain.PaymentIntentStatusExpired))
		assert.Equal(t, bodyStruct.FiatAmount, fiatAmount)
		assert.Equal(t, bodyStruct.FiatCurrency, fiatCurrency)
		assert.Equal(t, bodyStruct.PaymentChainID, chain.ID())
		assert.Equal(t, bodyStruct.Symbol, payToken)
		assert.Equal(t, bodyStruct.GroupKey, &scenarioID)

		assert.Nil(t, bodyStruct.PaymentTxHash, "PaymentTxHash should be nil for an expired intent")
		assert.Nil(t, bodyStruct.RefundTxHash, "RefundTxHash should be nil for an expired intent")
		assert.Nil(t, bodyStruct.ReceivedAmount, "ReceivedAmount should be nil for an expired intent")
		assert.Nil(t, bodyStruct.AggregatedAmount, "AggregatedAmount should be nil for an expired intent")
		assert.Nil(t, bodyStruct.RefundAmount, "RefundAmount should be nil for an expired intent")

		// Signal that the callback has been processed
		wg.Done()
		return &resty.Response{}, nil
	})
	Init(mockRepo, mockCallbackExecutor, nil)

	mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
	mockAlchemy.EXPECT().RemoveSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
		assert.Equal(t, addresses, []string{paymentAddress.String()})
		return nil
	})
	alchemyapi.Set(mockAlchemy)

	err := expireIntent(ctx, intent)
	assert.NoError(t, err)

	// Wait for the async callback to complete before finishing the test
	wg.Wait()
}
