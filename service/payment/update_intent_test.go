package payment

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/service/erc4337"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestGetWalletNonce(t *testing.T) {
	chain := domain.Sepolia

	from := domain.NewEvmAddress("******************************************")
	nonce, err := erc4337.GetEntrypointNonce(chain, from)
	if err != nil {
		t.Fatalf("failed to get wallet nonce: %v", err)
	}
	t.Logf("wallet nonce: %d", nonce)
}

func TestCheckAndUpdatePendingIntent(t *testing.T) {
	ctrl := gomock.NewController(t)
	ctx := context.Background()

	chain := domain.Arbitrum
	clientID := "client123"
	t.Run("Expire intent", func(t *testing.T) {
		paymentAddress := domain.NewEvmAddress("******************************************")
		mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
		mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
		Init(mockRepo, mockCallbackExecutor, nil)

		mockRepo.EXPECT().GetPendingIntents(gomock.Any()).Return([]*domain.PaymentIntent{
			{
				ID:             "intent-test-id-1",
				ClientID:       clientID,
				PaymentChain:   chain,
				Decimals:       6,
				PaymentAddress: paymentAddress,
				TokenAddress:   domain.NewEvmAddress("******************************************").String(),
				CryptoAmount:   decimal.NewFromFloat32(0.1000292085288904),
			},
		}, nil)
		mockRepo.EXPECT().AcquireLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		wg := sync.WaitGroup{}
		wg.Add(1)
		mockRepo.EXPECT().ReleaseLock(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, intentID string) error {
			wg.Done()
			return nil
		})
		mockRepo.EXPECT().UpdatePaymentIntent(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, intentID string, updateFields *domain.PaymentIntentUpdate) error {
			assert.Equal(t, intentID, "intent-test-id-1")
			assert.Equal(t, *updateFields.Status, domain.PaymentIntentStatusExpired)
			return nil
		})
		mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
		mockAlchemy.EXPECT().RemoveSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
			assert.Equal(t, addresses, []string{paymentAddress.String()})
			return nil
		})
		alchemyapi.Set(mockAlchemy)

		err := checkAndUpdatePendingIntent(ctx)
		assert.NoError(t, err)
	})

	t.Run("Do nothing if deadline is not reached", func(t *testing.T) {
		mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
		mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
		Init(mockRepo, mockCallbackExecutor, nil)
		paymentAddress := domain.NewEvmAddress("******************************************")

		mockRepo.EXPECT().GetPendingIntents(gomock.Any()).Return([]*domain.PaymentIntent{
			{
				ID:              "intent-test-id-1",
				ClientID:        clientID,
				PaymentChain:    chain,
				Decimals:        6,
				PaymentDeadline: time.Now().Add(time.Hour),
				PaymentAddress:  paymentAddress,
				TokenAddress:    domain.NewEvmAddress("******************************************").String(),
				CryptoAmount:    decimal.NewFromFloat32(0.1000292085288904),
			},
		}, nil)

		err := checkAndUpdatePendingIntent(ctx)
		assert.NoError(t, err)
	})

	t.Run("Avoid race condition", func(t *testing.T) {
		mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
		mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
		Init(mockRepo, mockCallbackExecutor, nil)

		// Use a WaitGroup to synchronize lock acquisition attempts
		var wg sync.WaitGroup
		wg.Add(2) // Expecting two lock acquisition attempts

		mockRepo.EXPECT().GetPendingIntents(gomock.Any()).Return([]*domain.PaymentIntent{
			{
				ID:             "intent-test-id-1",
				ClientID:       clientID,
				PaymentChain:   chain,
				Decimals:       6,
				PaymentAddress: domain.NewEvmAddress("******************************************"),
				TokenAddress:   domain.NewEvmAddress("******************************************").String(),
				CryptoAmount:   decimal.NewFromFloat32(0.1000292085288904),
			},
			{
				ID:             "intent-test-id-2",
				ClientID:       clientID,
				PaymentChain:   chain,
				Decimals:       6,
				PaymentAddress: domain.NewEvmAddress("******************************************"),
				TokenAddress:   domain.NewEvmAddress("******************************************").String(),
				CryptoAmount:   decimal.NewFromFloat32(0),
			},
		}, nil)

		mockRepo.EXPECT().AcquireLock(gomock.Any(), gomock.Any(), gomock.Any()).Times(2).DoAndReturn(func(ctx context.Context, intentID string, duration time.Duration) error {
			// Decrement the WaitGroup counter when lock acquisition is attempted
			defer wg.Done()
			return domain.ErrLockNotAcquired
		})

		// Run the function under test
		err := checkAndUpdatePendingIntent(ctx)
		assert.NoError(t, err)

		// Wait for all lock acquisition attempts
		wg.Wait()
	})
}
