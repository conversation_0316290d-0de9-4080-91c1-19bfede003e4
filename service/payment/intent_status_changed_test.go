package payment

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestExtractFirstProductName(t *testing.T) {
	t.Run("No products field", func(t *testing.T) {
		orderData := map[string]any{"email": "<EMAIL>"}
		want := ""
		if got := extractFirstProductName(orderData); got != want {
			t.Errorf("extractFirstProductName() = %v, want %v", got, want)
		}
	})

	t.Run("Products field is not a slice", func(t *testing.T) {
		orderData := map[string]any{"products": "not-a-slice"}
		want := ""
		if got := extractFirstProductName(orderData); got != want {
			t.<PERSON><PERSON>("extractFirstProductName() = %v, want %v", got, want)
		}
	})

	t.Run("Products slice is empty", func(t *testing.T) {
		orderData := map[string]any{"products": []any{}}
		want := ""
		if got := extractFirstProductName(orderData); got != want {
			t.Errorf("extractFirstProductName() = %v, want %v", got, want)
		}
	})

	t.Run("First product is not a map", func(t *testing.T) {
		orderData := map[string]any{"products": []any{"not-a-map"}}
		want := ""
		if got := extractFirstProductName(orderData); got != want {
			t.Errorf("extractFirstProductName() = %v, want %v", got, want)
		}
	})

	t.Run("First product map has no name field", func(t *testing.T) {
		orderData := map[string]any{"products": []any{map[string]any{"id": "123"}}}
		want := ""
		if got := extractFirstProductName(orderData); got != want {
			t.Errorf("extractFirstProductName() = %v, want %v", got, want)
		}
	})

	t.Run("First product name is not a string", func(t *testing.T) {
		orderData := map[string]any{"products": []any{map[string]any{"name": 123}}}
		want := ""
		if got := extractFirstProductName(orderData); got != want {
			t.Errorf("extractFirstProductName() = %v, want %v", got, want)
		}
	})

	t.Run("First product name is empty string", func(t *testing.T) {
		orderData := map[string]any{"products": []any{map[string]any{"name": ""}}}
		want := ""
		if got := extractFirstProductName(orderData); got != want {
			t.Errorf("extractFirstProductName() = %v, want %v", got, want)
		}
	})

	t.Run("Valid product name", func(t *testing.T) {
		orderData := map[string]any{"products": []any{map[string]any{"name": "Cool T-Shirt"}}}
		want := "Cool T-Shirt"
		if got := extractFirstProductName(orderData); got != want {
			t.Errorf("extractFirstProductName() = %v, want %v", got, want)
		}
	})

	t.Run("Valid product name with other products", func(t *testing.T) {
		orderData := map[string]any{
			"products": []any{
				map[string]any{"name": "First Product"},
				map[string]any{"name": "Second Product"},
			},
		}
		want := "First Product"
		if got := extractFirstProductName(orderData); got != want {
			t.Errorf("extractFirstProductName() = %v, want %v", got, want)
		}
	})

	t.Run("OrderData is nil", func(t *testing.T) {
		var orderData map[string]any // nil map
		want := ""
		if got := extractFirstProductName(orderData); got != want {
			t.Errorf("extractFirstProductName() = %v, want %v", got, want)
		}
	})

	t.Run("OrderData is empty map", func(t *testing.T) {
		orderData := map[string]any{}
		want := ""
		if got := extractFirstProductName(orderData); got != want {
			t.Errorf("extractFirstProductName() = %v, want %v", got, want)
		}
	})
}

func TestDetermineEmailSubjectAndTitle(t *testing.T) {
	t.Run("Order with product name, Success status, Customer", func(t *testing.T) {
		isOrder := true
		firstProductName := "Shiny Widget"
		status := domain.PaymentIntentStatusSuccess
		appName := "My Awesome App"
		isOwner := false
		wantSubject := "My Awesome App - Order: 'Shiny Widget' Status: success"
		wantBodyTitle := "Status of Your Order for 'Shiny Widget': success"

		gotSubject, gotBodyTitle := determineEmailSubjectAndTitle(isOrder, firstProductName, status, appName, isOwner)
		if gotSubject != wantSubject {
			t.Errorf("determineEmailSubjectAndTitle() gotSubject = %v, want %v", gotSubject, wantSubject)
		}
		if gotBodyTitle != wantBodyTitle {
			t.Errorf("determineEmailSubjectAndTitle() gotBodyTitle = %v, want %v", gotBodyTitle, wantBodyTitle)
		}
	})

	t.Run("Order with product name, Success status, Owner", func(t *testing.T) {
		isOrder := true
		firstProductName := "Shiny Widget"
		status := domain.PaymentIntentStatusSuccess
		appName := "My Awesome App"
		isOwner := true
		wantSubject := "My Awesome App - New Order for 'Shiny Widget': success"
		wantBodyTitle := "Product Order Notification: 'Shiny Widget' - success"

		gotSubject, gotBodyTitle := determineEmailSubjectAndTitle(isOrder, firstProductName, status, appName, isOwner)
		if gotSubject != wantSubject {
			t.Errorf("determineEmailSubjectAndTitle() gotSubject = %v, want %v", gotSubject, wantSubject)
		}
		if gotBodyTitle != wantBodyTitle {
			t.Errorf("determineEmailSubjectAndTitle() gotBodyTitle = %v, want %v", gotBodyTitle, wantBodyTitle)
		}
	})

	t.Run("Order no product name, Failed status, Customer", func(t *testing.T) {
		isOrder := true
		firstProductName := ""
		status := domain.PaymentIntentStatusInsufficientRefunded
		appName := "My Store"
		isOwner := false
		wantSubject := "My Store - Order Status: insufficient_refunded"
		wantBodyTitle := "Your Order Status: insufficient_refunded"

		gotSubject, gotBodyTitle := determineEmailSubjectAndTitle(isOrder, firstProductName, status, appName, isOwner)
		if gotSubject != wantSubject {
			t.Errorf("determineEmailSubjectAndTitle() gotSubject = %v, want %v", gotSubject, wantSubject)
		}
		if gotBodyTitle != wantBodyTitle {
			t.Errorf("determineEmailSubjectAndTitle() gotBodyTitle = %v, want %v", gotBodyTitle, wantBodyTitle)
		}
	})

	t.Run("Non-order payment, Pending status, Customer", func(t *testing.T) {
		isOrder := false
		firstProductName := "" // Should be ignored
		status := domain.PaymentIntentStatusPending
		appName := "Generic Payments"
		isOwner := false
		wantSubject := "Generic Payments - Payment Status: pending"
		wantBodyTitle := "Your Payment Status Update"

		gotSubject, gotBodyTitle := determineEmailSubjectAndTitle(isOrder, firstProductName, status, appName, isOwner)
		if gotSubject != wantSubject {
			t.Errorf("determineEmailSubjectAndTitle() gotSubject = %v, want %v", gotSubject, wantSubject)
		}
		if gotBodyTitle != wantBodyTitle {
			t.Errorf("determineEmailSubjectAndTitle() gotBodyTitle = %v, want %v", gotBodyTitle, wantBodyTitle)
		}
	})

	t.Run("Order with product name, Custom status, Customer", func(t *testing.T) {
		isOrder := true
		firstProductName := "Custom Item"
		status := domain.PaymentIntentStatus("custom_processing") // Test with a string that converts to PaymentIntentStatus
		appName := "Custom Shop"
		isOwner := false
		wantSubject := "Custom Shop - Order: 'Custom Item' Status: custom_processing"
		wantBodyTitle := "Status of Your Order for 'Custom Item': custom_processing"

		gotSubject, gotBodyTitle := determineEmailSubjectAndTitle(isOrder, firstProductName, status, appName, isOwner)
		if gotSubject != wantSubject {
			t.Errorf("determineEmailSubjectAndTitle() gotSubject = %v, want %v", gotSubject, wantSubject)
		}
		if gotBodyTitle != wantBodyTitle {
			t.Errorf("determineEmailSubjectAndTitle() gotBodyTitle = %v, want %v", gotBodyTitle, wantBodyTitle)
		}
	})
}

func TestPrepareEmailParams(t *testing.T) {
	ctx := context.Background()
	testAppName := "Test App"
	testRecipientName := "John Doe"
	baseIntent := &domain.PaymentIntent{
		ID:           "intent_123",
		Status:       domain.PaymentIntentStatusSuccess,
		CryptoAmount: decimal.NewFromInt(10),
		Symbol:       "ETH",
		OrderData:    map[string]any{},
	}

	fiatAmt := decimal.NewFromFloat(100.50)
	fiatCurrency := "USD"
	receivedAmt := decimal.NewFromInt(9)
	txHash := "0x123abc"
	txTimestamp := time.Date(2023, 1, 1, 10, 0, 0, 0, time.UTC)

	oauthAppWithLogo := &domain.OAuthApplication{
		Application: domain.Application{
			Name: testAppName,
		},
		MainLogo:       "http://example.com/logo.png",
		SupportAddress: "<EMAIL>",
	}
	oauthAppWithoutLogo := &domain.OAuthApplication{
		Application: domain.Application{
			Name: testAppName,
		},
	}

	t.Run("Simple non-order payment, Customer", func(t *testing.T) {
		intent := baseIntent
		oauthApp := oauthAppWithoutLogo
		appName := testAppName
		isGenericAppName := false
		recipientName := testRecipientName
		isOwner := false
		wantSubject := fmt.Sprintf("%s - Payment Status: %s", testAppName, baseIntent.Status)
		wantBodyTitle := "Your Payment Status Update"
		wantParams := map[string]any{
			"RecipientName":    testRecipientName,
			"PaymentIntentID":  baseIntent.ID,
			"Status":           string(baseIntent.Status),
			"StatusClass":      string(baseIntent.Status),
			"BodyTitle":        wantBodyTitle,
			"IsOrder":          false,
			"IsOwner":          false,
			"FirstProductName": "",
			"IsGenericAppName": false,
			"FiatAmount":       "",
			"FiatCurrency":     "",
			"CryptoAmount":     baseIntent.CryptoAmount.String(),
			"Symbol":           baseIntent.Symbol,
			"AppName":          testAppName,
			"MainLogo":         "",
			"SupportAddress":   "",
			"CurrentYear":      time.Now().Year(),
			"OrderDataItems":   []OrderDataItem(nil), // No order data for non-order payment
			"HasOrderData":     false,
			"SuccessMessage":   "",
			"IsFirstSale":      false,
		}
		wantErr := false

		got, err := prepareEmailParams(ctx, intent, oauthApp, appName, isGenericAppName, recipientName, isOwner, "")
		if (err != nil) != wantErr {
			t.Errorf("prepareEmailParams() error = %v, wantErr %v", err, wantErr)
			return
		}
		assert.Equal(t, wantSubject, got.Subject, "Subject mismatch")
		assert.Equal(t, wantBodyTitle, got.Parameters["BodyTitle"], "BodyTitle mismatch")
		assert.Equal(t, len(wantParams), len(got.Parameters), "Params map length mismatch")
		for k, vWant := range wantParams {
			vGot, ok := got.Parameters[k]
			assert.True(t, ok, fmt.Sprintf("Expected key %s not found in params", k))
			// Special handling for CurrentYear as it's dynamic
			if k == "CurrentYear" {
				assert.IsType(t, vWant, vGot, fmt.Sprintf("Param type mismatch for key %s", k))
			} else {
				assert.Equal(t, vWant, vGot, fmt.Sprintf("Param mismatch for key %s", k))
			}
		}
	})

	t.Run("Order payment with product and full details", func(t *testing.T) {
		intent := &domain.PaymentIntent{
			ID:                   "intent_order_123",
			Status:               domain.PaymentIntentStatusInsufficientNotRefunded,
			CryptoAmount:         decimal.NewFromInt(50),
			Symbol:               "BTC",
			FiatAmount:           util.Ptr(fiatAmt),
			FiatCurrency:         &fiatCurrency,
			ReceivedCryptoAmount: util.Ptr(receivedAmt),
			PaymentChain:         domain.Arbitrum,
			PaymentTxHash:        &txHash,
			PaymentTxTimestamp:   &txTimestamp,
			OrderData: map[string]any{
				"email":    "<EMAIL>",
				"products": []any{map[string]any{"name": "Mega Deal", "price": 99.99}},
			},
		}
		oauthApp := oauthAppWithLogo
		appName := "Super Store"
		isGenericAppName := false
		recipientName := "Valued Shopper"
		isOwner := false
		wantSubject := "Super Store - Order: 'Mega Deal' Status: insufficient_not_refunded"
		wantBodyTitle := "Status of Your Order for 'Mega Deal': insufficient_not_refunded"

		wantParams := map[string]any{
			"RecipientName":      "Valued Shopper",
			"PaymentIntentID":    "intent_order_123",
			"Status":             "insufficient_not_refunded",
			"StatusClass":        "insufficient_not_refunded",
			"BodyTitle":          wantBodyTitle,
			"IsOrder":            true,
			"IsOwner":            false,
			"FirstProductName":   "Mega Deal",
			"IsGenericAppName":   false,
			"FiatAmount":         fiatAmt.String(),
			"FiatCurrency":       fiatCurrency,
			"CryptoAmount":       decimal.NewFromInt(50).String(),
			"Symbol":             "BTC",
			"AppName":            "Super Store",
			"MainLogo":           oauthAppWithLogo.MainLogo,
			"SupportAddress":     oauthAppWithLogo.SupportAddress,
			"CurrentYear":        time.Now().Year(),
			"ReceivedAmount":     receivedAmt.String(),
			"PaymentTxHash":      txHash,
			"PaymentTxTimestamp": txTimestamp.Format(time.RFC1123),
			"HasOrderData":       true,
			"SuccessMessage":     "Test success message",
		}
		wantErr := false

		got, err := prepareEmailParams(ctx, intent, oauthApp, appName, isGenericAppName, recipientName, isOwner, "Test success message")
		if (err != nil) != wantErr {
			t.Errorf("prepareEmailParams() error = %v, wantErr %v", err, wantErr)
			return
		}
		assert.Equal(t, wantSubject, got.Subject, "Subject mismatch")
		assert.Equal(t, wantBodyTitle, got.Parameters["BodyTitle"], "BodyTitle mismatch")

		// Check that we have the expected basic params
		for k, vWant := range wantParams {
			vGot, ok := got.Parameters[k]
			assert.True(t, ok, fmt.Sprintf("Expected key %s not found in params", k))
			if k == "CurrentYear" {
				assert.IsType(t, vWant, vGot, fmt.Sprintf("Param type mismatch for key %s", k))
			} else {
				assert.Equal(t, vWant, vGot, fmt.Sprintf("Param mismatch for key %s", k))
			}
		}

		// Check OrderDataItems specifically
		gotOrderDataItems, ok := got.Parameters["OrderDataItems"]
		assert.True(t, ok, "OrderDataItems should be present")
		gotItems, gotOk := gotOrderDataItems.([]OrderDataItem)
		assert.True(t, gotOk, "OrderDataItems should be of type []OrderDataItem")
		assert.Greater(t, len(gotItems), 0, "Should have order data items")

		// Check that email and product data are properly extracted
		foundItems := make(map[string]string)
		for _, item := range gotItems {
			foundItems[item.Key] = item.Value
		}

		assert.Equal(t, "<EMAIL>", foundItems["email"], "Email should be correct")

		// Products should be formatted as a regular array, not special individual fields
		products := foundItems["products"]
		assert.NotEmpty(t, products, "Products should not be empty")
		assert.Contains(t, products, "Mega Deal", "Products should contain product name")
		assert.Contains(t, products, "99.99", "Products should contain product price")
	})

	t.Run("Generic app name usage", func(t *testing.T) {
		intent := baseIntent
		oauthApp := (*domain.OAuthApplication)(nil) // No OAuth app, or one with no name
		appName := "Payment Service"                // Default app name passed in
		isGenericAppName := true
		recipientName := testRecipientName
		isOwner := false
		wantSubject := fmt.Sprintf("Payment Service - Payment Status: %s", baseIntent.Status)
		wantBodyTitle := "Your Payment Status Update"
		wantParams := map[string]any{
			"RecipientName":    testRecipientName,
			"PaymentIntentID":  baseIntent.ID,
			"Status":           string(baseIntent.Status),
			"StatusClass":      string(baseIntent.Status),
			"BodyTitle":        wantBodyTitle,
			"IsOrder":          false,
			"IsOwner":          false,
			"FirstProductName": "",
			"IsGenericAppName": true,
			"FiatAmount":       "",
			"FiatCurrency":     "",
			"CryptoAmount":     baseIntent.CryptoAmount.String(),
			"Symbol":           baseIntent.Symbol,
			"AppName":          "Payment Service",
			"MainLogo":         "",
			"SupportAddress":   "",
			"CurrentYear":      time.Now().Year(),
			"OrderDataItems":   []OrderDataItem(nil), // No order data for non-order payment
			"HasOrderData":     false,
			"SuccessMessage":   "",
			"IsFirstSale":      false,
		}
		wantErr := false

		got, err := prepareEmailParams(ctx, intent, oauthApp, appName, isGenericAppName, recipientName, isOwner, "")
		if (err != nil) != wantErr {
			t.Errorf("prepareEmailParams() error = %v, wantErr %v", err, wantErr)
			return
		}
		assert.Equal(t, wantSubject, got.Subject, "Subject mismatch")
		assert.Equal(t, wantBodyTitle, got.Parameters["BodyTitle"], "BodyTitle mismatch")
		assert.Equal(t, len(wantParams), len(got.Parameters), "Params map length mismatch")
		for k, vWant := range wantParams {
			vGot, ok := got.Parameters[k]
			assert.True(t, ok, fmt.Sprintf("Expected key %s not found in params", k))
			if k == "CurrentYear" {
				assert.IsType(t, vWant, vGot, fmt.Sprintf("Param type mismatch for key %s", k))
			} else {
				assert.Equal(t, vWant, vGot, fmt.Sprintf("Param mismatch for key %s", k))
			}
		}
	})
}

// Test extractSuccessMessageFromOrderData function
func TestExtractSuccessMessageFromOrderData(t *testing.T) {
	ctx := context.Background()

	t.Run("Nil OrderData", func(t *testing.T) {
		intent := &domain.PaymentIntent{
			OrderData: nil,
		}
		result := extractSuccessMessageFromOrderData(ctx, intent)
		assert.Equal(t, "", result, "Should return empty string for nil OrderData")
	})

	t.Run("No products field", func(t *testing.T) {
		intent := &domain.PaymentIntent{
			OrderData: map[string]any{"email": "<EMAIL>"},
		}
		result := extractSuccessMessageFromOrderData(ctx, intent)
		assert.Equal(t, "", result, "Should return empty string when no products field")
	})

	t.Run("Products field is not a slice", func(t *testing.T) {
		intent := &domain.PaymentIntent{
			OrderData: map[string]any{"products": "not-a-slice"},
		}
		result := extractSuccessMessageFromOrderData(ctx, intent)
		assert.Equal(t, "", result, "Should return empty string when products is not a slice")
	})

	t.Run("Empty products slice", func(t *testing.T) {
		intent := &domain.PaymentIntent{
			OrderData: map[string]any{"products": []any{}},
		}
		result := extractSuccessMessageFromOrderData(ctx, intent)
		assert.Equal(t, "", result, "Should return empty string when products slice is empty")
	})

	t.Run("First product is not a map", func(t *testing.T) {
		intent := &domain.PaymentIntent{
			OrderData: map[string]any{"products": []any{"not-a-map"}},
		}
		result := extractSuccessMessageFromOrderData(ctx, intent)
		assert.Equal(t, "", result, "Should return empty string when first product is not a map")
	})

	t.Run("First product has no id field", func(t *testing.T) {
		intent := &domain.PaymentIntent{
			OrderData: map[string]any{"products": []any{map[string]any{"name": "Test Product"}}},
		}
		result := extractSuccessMessageFromOrderData(ctx, intent)
		assert.Equal(t, "", result, "Should return empty string when first product has no id field")
	})

	t.Run("Product id is not a string", func(t *testing.T) {
		intent := &domain.PaymentIntent{
			OrderData: map[string]any{"products": []any{map[string]any{"id": 123}}},
		}
		result := extractSuccessMessageFromOrderData(ctx, intent)
		assert.Equal(t, "", result, "Should return empty string when product id is not a string")
	})

	t.Run("Product id is empty string", func(t *testing.T) {
		intent := &domain.PaymentIntent{
			OrderData: map[string]any{"products": []any{map[string]any{"id": ""}}},
		}
		result := extractSuccessMessageFromOrderData(ctx, intent)
		assert.Equal(t, "", result, "Should return empty string when product id is empty")
	})

	// Note: We cannot easily test the successful case with actual payment_item.GetPaymentItemByID
	// without mocking, as it requires database access. The function structure ensures that
	// if GetPaymentItemByID fails or returns nil/nil SuccessMessage, empty string is returned.
}

// Test the new order data extraction functionality
func TestExtractOrderDataForEmail(t *testing.T) {
	t.Run("Empty order data", func(t *testing.T) {
		orderData := map[string]any{}
		result := extractOrderDataForEmail(orderData)
		assert.Nil(t, result, "Should return nil for empty order data")
	})

	t.Run("Nil order data", func(t *testing.T) {
		var orderData map[string]any
		result := extractOrderDataForEmail(orderData)
		assert.Nil(t, result, "Should return nil for nil order data")
	})

	t.Run("Email gets priority, other fields alphabetical", func(t *testing.T) {
		orderData := map[string]any{
			"lastName":  "Doe",
			"email":     "<EMAIL>",
			"firstName": "John",
			"phone":     "+1234567890",
		}
		result := extractOrderDataForEmail(orderData)

		// Should have 4 items
		assert.Equal(t, 4, len(result), "Should have 4 items")

		// Email should be first (preferred), then alphabetical: firstName, lastName, phone
		assert.Equal(t, "email", result[0].Key)
		assert.Equal(t, "<EMAIL>", result[0].Value)

		assert.Equal(t, "firstName", result[1].Key)
		assert.Equal(t, "John", result[1].Value)

		assert.Equal(t, "lastName", result[2].Key)
		assert.Equal(t, "Doe", result[2].Value)

		assert.Equal(t, "phone", result[3].Key)
		assert.Equal(t, "+1234567890", result[3].Value)
	})

	t.Run("Mixed data types", func(t *testing.T) {
		orderData := map[string]any{
			"email":    "<EMAIL>",
			"age":      25,
			"price":    99.99,
			"active":   true,
			"inactive": false,
			"empty":    "",
			"null":     nil,
		}
		result := extractOrderDataForEmail(orderData)

		// Should skip empty and null values
		assert.Greater(t, len(result), 0, "Should have some items")

		// Check different data types are formatted correctly
		foundItems := make(map[string]string)
		for _, item := range result {
			foundItems[item.Key] = item.Value
		}

		assert.Equal(t, "<EMAIL>", foundItems["email"], "Email should be correct")
		assert.Equal(t, "25", foundItems["age"], "Age should be formatted as string")
		assert.Equal(t, "99.99", foundItems["price"], "Price should be formatted correctly")
		assert.Equal(t, "Yes", foundItems["active"], "Boolean true should be Yes")
		assert.Equal(t, "No", foundItems["inactive"], "Boolean false should be No")

		// Should not contain empty or null values
		_, hasEmpty := foundItems["empty"]
		_, hasNull := foundItems["null"]
		assert.False(t, hasEmpty, "Should not include empty values")
		assert.False(t, hasNull, "Should not include null values")
	})

	t.Run("Array handling", func(t *testing.T) {
		orderData := map[string]any{
			"email":      "<EMAIL>",
			"tags":       []any{"vip", "premium", "gold"},
			"categories": []any{"electronics", "gadgets"},
			"emptyArray": []any{},
		}
		result := extractOrderDataForEmail(orderData)

		foundItems := make(map[string]string)
		for _, item := range result {
			foundItems[item.Key] = item.Value
		}

		assert.Equal(t, "<EMAIL>", foundItems["email"], "Email should be correct")
		assert.Equal(t, "vip, premium, gold", foundItems["tags"], "Array should be comma-separated")
		assert.Equal(t, "electronics, gadgets", foundItems["categories"], "Array should be comma-separated")

		// Empty array should not be included
		_, hasEmptyArray := foundItems["emptyArray"]
		assert.False(t, hasEmptyArray, "Should not include empty arrays")
	})

	t.Run("Object handling", func(t *testing.T) {
		orderData := map[string]any{
			"email": "<EMAIL>",
			"address": map[string]any{
				"street": "123 Main St",
				"city":   "New York",
				"zip":    "10001",
			},
			"preferences": map[string]any{
				"name":    "Newsletter",
				"value":   "weekly",
				"enabled": true,
			},
		}
		result := extractOrderDataForEmail(orderData)

		foundItems := make(map[string]string)
		for _, item := range result {
			foundItems[item.Key] = item.Value
		}

		assert.Equal(t, "<EMAIL>", foundItems["email"], "Email should be correct")

		// Address should be formatted nicely
		address := foundItems["address"]
		assert.Contains(t, address, "street: 123 Main St", "Address should contain street")
		assert.Contains(t, address, "city: New York", "Address should contain city")
		assert.Contains(t, address, "zip: 10001", "Address should contain zip")

		// Preferences should show meaningful fields
		preferences := foundItems["preferences"]
		assert.Contains(t, preferences, "name: Newsletter", "Preferences should contain name")
		assert.Contains(t, preferences, "value: weekly", "Preferences should contain value")
	})

	t.Run("Products as regular array - no special handling", func(t *testing.T) {
		orderData := map[string]any{
			"email": "<EMAIL>",
			"products": []any{
				map[string]any{
					"name":  "Premium Widget",
					"price": 99.99,
				},
				map[string]any{
					"name":  "Basic Widget",
					"price": 49.99,
				},
			},
		}
		result := extractOrderDataForEmail(orderData)

		foundItems := make(map[string]string)
		for _, item := range result {
			foundItems[item.Key] = item.Value
		}

		assert.Equal(t, "<EMAIL>", foundItems["email"], "Email should be correct")

		// Products should be formatted as a normal array of objects
		products := foundItems["products"]
		assert.NotEmpty(t, products, "Products should not be empty")
		assert.Contains(t, products, "Premium Widget", "Products should contain first product name")
		assert.Contains(t, products, "Basic Widget", "Products should contain second product name")
	})

	t.Run("Alphabetical ordering for non-preferred keys", func(t *testing.T) {
		orderData := map[string]any{
			"email":  "<EMAIL>", // preferred key
			"zebra":  "last",
			"apple":  "first",
			"banana": "middle",
		}
		result := extractOrderDataForEmail(orderData)

		// Email should be first (preferred), then alphabetical: apple, banana, zebra
		assert.Equal(t, "email", result[0].Key, "Email should be first (preferred)")
		assert.Equal(t, "apple", result[1].Key, "Apple should be first alphabetically")
		assert.Equal(t, "banana", result[2].Key, "Banana should be second alphabetically")
		assert.Equal(t, "zebra", result[3].Key, "Zebra should be last alphabetically")
	})

	t.Run("Complex nested data", func(t *testing.T) {
		orderData := map[string]any{
			"email": "<EMAIL>",
			"shipping": map[string]any{
				"address": map[string]any{
					"street": "456 Oak Ave",
					"city":   "Boston",
				},
				"method": "express",
			},
			"items": []any{
				map[string]any{
					"name":     "Book",
					"metadata": map[string]any{"author": "John Smith", "pages": 200},
				},
			},
		}
		result := extractOrderDataForEmail(orderData)

		assert.Greater(t, len(result), 0, "Should have some items")

		// Check that nested structures are handled reasonably
		foundItems := make(map[string]string)
		for _, item := range result {
			foundItems[item.Key] = item.Value
		}

		assert.Equal(t, "<EMAIL>", foundItems["email"], "Email should be correct")

		// Shipping should be formatted as object
		shipping := foundItems["shipping"]
		assert.NotEmpty(t, shipping, "Shipping should not be empty")
		assert.Contains(t, shipping, "method", "Shipping should contain method info")

		// Items should be formatted as array
		items := foundItems["items"]
		assert.NotEmpty(t, items, "Items should not be empty")
		assert.Contains(t, items, "Book", "Items should contain book name")
	})
}

func TestFormatOrderDataValue(t *testing.T) {
	testCases := []struct {
		name     string
		input    any
		expected string
	}{
		{"nil value", nil, ""},
		{"empty string", "", ""},
		{"string with spaces", "  hello world  ", "hello world"},
		{"regular string", "test", "test"},
		{"integer", 42, "42"},
		{"int64", int64(123), "123"},
		{"int32", int32(456), "456"},
		{"float64", 99.99, "99.99"},
		{"float32", float32(12.34), "12.34"},
		{"true boolean", true, "Yes"},
		{"false boolean", false, "No"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := formatOrderDataValue(tc.input)
			assert.Equal(t, tc.expected, result, fmt.Sprintf("formatOrderDataValue(%v)", tc.input))
		})
	}

	t.Run("simple array", func(t *testing.T) {
		input := []any{"apple", "banana", "cherry"}
		result := formatOrderDataValue(input)
		assert.Equal(t, "apple, banana, cherry", result, "Array should be comma-separated")
	})

	t.Run("empty array", func(t *testing.T) {
		input := []any{}
		result := formatOrderDataValue(input)
		assert.Equal(t, "", result, "Empty array should return empty string")
	})

	t.Run("simple object", func(t *testing.T) {
		input := map[string]any{
			"name":  "Test Item",
			"value": "123",
		}
		result := formatOrderDataValue(input)
		assert.Contains(t, result, "name: Test Item", "Object should contain name")
		assert.Contains(t, result, "value: 123", "Object should contain value")
	})

	t.Run("object with only non-meaningful fields", func(t *testing.T) {
		input := map[string]any{
			"field1": "value1",
			"field2": "value2",
			"field3": "value3",
			"field4": "value4", // Should be limited to 3 fields
		}
		result := formatOrderDataValue(input)
		assert.NotEmpty(t, result, "Object should not be empty")
		// Should contain at most 3 field pairs
		semicolonCount := strings.Count(result, ";")
		assert.LessOrEqual(t, semicolonCount, 2, "Should have at most 2 semicolons (3 fields)")
	})

	t.Run("long string truncation", func(t *testing.T) {
		longString := strings.Repeat("a", 250) // Create a string longer than 200 characters

		result := formatOrderDataValue(longString)
		assert.Equal(t, 200, len(result), "Should truncate to 200 characters")
		assert.True(t, strings.HasSuffix(result, "..."), "Should end with ...")
		assert.Equal(t, 197, len(strings.TrimSuffix(result, "...")), "Should have 197 characters plus ...")
	})
}

func TestExtractProductIdentifier(t *testing.T) {
	tests := []struct {
		name      string
		orderData map[string]any
		want      string
	}{
		{
			name:      "no products data",
			orderData: map[string]any{},
			want:      "",
		},
		{
			name:      "products not a slice",
			orderData: map[string]any{"products": "not-a-slice"},
			want:      "",
		},
		{
			name:      "empty products slice",
			orderData: map[string]any{"products": []any{}},
			want:      "",
		},
		{
			name:      "first product not a map",
			orderData: map[string]any{"products": []any{"not-a-map"}},
			want:      "",
		},
		{
			name:      "product with id",
			orderData: map[string]any{"products": []any{map[string]any{"id": "prod-123", "name": "Cool T-Shirt"}}},
			want:      "prod-123",
		},
		{
			name:      "product with sku (no id)",
			orderData: map[string]any{"products": []any{map[string]any{"sku": "SKU-456", "name": "Cool T-Shirt"}}},
			want:      "SKU-456",
		},
		{
			name:      "product with name only",
			orderData: map[string]any{"products": []any{map[string]any{"name": "Cool T-Shirt"}}},
			want:      "Cool T-Shirt",
		},
		{
			name:      "product with empty values",
			orderData: map[string]any{"products": []any{map[string]any{"id": "", "sku": "", "name": ""}}},
			want:      "",
		},
		{
			name:      "product with priority order (id takes precedence)",
			orderData: map[string]any{"products": []any{map[string]any{"id": "prod-123", "sku": "SKU-456", "name": "Cool T-Shirt"}}},
			want:      "prod-123",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := extractProductIdentifier(tt.orderData); got != tt.want {
				t.Errorf("extractProductIdentifier() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDetermineIfFirstSale(t *testing.T) {
	// Note: This test is mainly for the logic flow since we can't easily test the database query part
	// without setting up a complete mock repository
	tests := []struct {
		name   string
		intent *domain.PaymentIntent
		want   bool
	}{
		{
			name: "non-successful status",
			intent: &domain.PaymentIntent{
				ID:        "test-1",
				Status:    domain.PaymentIntentStatusPending,
				OrderData: map[string]any{"products": []any{map[string]any{"id": "prod-123"}}},
			},
			want: false,
		},
		{
			name: "no order data",
			intent: &domain.PaymentIntent{
				ID:        "test-2",
				Status:    domain.PaymentIntentStatusSuccess,
				OrderData: map[string]any{},
			},
			want: false,
		},
		{
			name: "no product identifier",
			intent: &domain.PaymentIntent{
				ID:        "test-3",
				Status:    domain.PaymentIntentStatusSuccess,
				OrderData: map[string]any{"other": "data"},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := determineIfFirstSale(context.Background(), tt.intent)
			if got != tt.want {
				t.Errorf("determineIfFirstSale() = %v, want %v", got, tt.want)
			}
		})
	}
}
