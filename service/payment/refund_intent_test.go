package payment

import (
	"context"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/assetpro"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestRefundIntent(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	Init(mockRepo, nil, nil)

	t.Run("intent not found", func(t *testing.T) {
		req := &RefundIntentRequest{
			IntentID:           "non-existent",
			RefundCryptoAmount: decimal.NewFromFloat(1.5),
			To:                 "******************************************",
		}

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), "non-existent").
			Return(nil, domain.ErrRecordNotFound)

		err := RefundIntent(ctx, "test-uid", req)
		assert.Equal(t, code.PaymentIntentNotFound, err.Code)
		assert.Equal(t, domain.ErrRecordNotFound.Error(), err.Error.Error())
	})

	t.Run("invalid intent status", func(t *testing.T) {
		req := &RefundIntentRequest{
			IntentID:           "wrong-status",
			RefundCryptoAmount: decimal.NewFromFloat(1.5),
			To:                 "******************************************",
		}

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), "wrong-status").
			Return(&domain.PaymentIntent{
				ID:           "wrong-status",
				Status:       domain.PaymentIntentStatusSuccess,
				PaymentChain: domain.Holesky,
			}, nil)

		mockRepo.EXPECT().
			AcquireLock(gomock.Any(), "payment_intent:wrong-status", gomock.Any()).
			Return(nil)
		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), "payment_intent:wrong-status")

		err := RefundIntent(ctx, "test-uid", req)
		assert.Equal(t, code.ParamIncorrect, err.Code)
		assert.Equal(t, "invalid intent status: success, expected: insufficient_not_refunded", err.Error.Error())
	})

	t.Run("invalid refund amount", func(t *testing.T) {
		req := &RefundIntentRequest{
			IntentID:           "invalid-refund-amount",
			RefundCryptoAmount: decimal.NewFromFloat(-1.5),
			To:                 "******************************************",
		}

		mockRepo.EXPECT().
			AcquireLock(gomock.Any(), "payment_intent:invalid-refund-amount", gomock.Any()).
			Return(nil)
		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), "payment_intent:invalid-refund-amount")

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), "invalid-refund-amount").
			Return(&domain.PaymentIntent{
				ID:                     "invalid-refund-amount",
				Status:                 domain.PaymentIntentStatusInsufficientNotRefunded,
				AggregatedCryptoAmount: decimalPtr(decimal.NewFromFloat(2.0)),
				PaymentChain:           domain.Holesky,
			}, nil)

		err := RefundIntent(ctx, "test-uid", req)
		assert.Equal(t, code.ParamIncorrect, err.Code)
		assert.Equal(t, "invalid refund amount", err.Error.Error())
	})

	t.Run("refund amount exceeds aggregated amount", func(t *testing.T) {
		req := &RefundIntentRequest{
			IntentID:           "refund-too-much",
			RefundCryptoAmount: decimal.NewFromFloat(2.5),
			To:                 "******************************************",
		}

		mockRepo.EXPECT().
			AcquireLock(gomock.Any(), "payment_intent:refund-too-much", gomock.Any()).
			Return(nil)
		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), "payment_intent:refund-too-much")

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), "refund-too-much").
			Return(&domain.PaymentIntent{
				ID:                     "refund-too-much",
				Status:                 domain.PaymentIntentStatusInsufficientNotRefunded,
				AggregatedCryptoAmount: decimalPtr(decimal.NewFromFloat(2.0)),
				RefundCryptoAmount:     decimalPtr(decimal.NewFromFloat(0.0)),
				PaymentChain:           domain.Holesky,
			}, nil)

		err := RefundIntent(ctx, "test-uid", req)
		assert.Equal(t, code.ParamIncorrect, err.Code)
		assert.Equal(t, "refund amount 2.5 exceeds aggregated amount 2", err.Error.Error())
	})

	t.Run("successful refund", func(t *testing.T) {
		// Reset database and set up test data
		setupSigningServer(t)

		// Initialize assetpro with real database repo
		assetpro.InitTransfer(rdb.GormRepo())
		organization.Init(organization.InitParam{
			StudioOrgRepo: rdb.GormRepo(),
		})
		tx.Init(rdb.GormRepo())

		refundReceiver := domain.NewEvmAddress("******************************************")
		payerAddress := domain.NewEvmAddress("******************************************")
		req := &RefundIntentRequest{
			IntentID:           "successful-refund",
			RefundCryptoAmount: decimal.NewFromFloat(1.5),
			To:                 refundReceiver.String(),
		}

		mockRepo.EXPECT().
			GetPaymentIntentByID(gomock.Any(), "successful-refund").
			Return(&domain.PaymentIntent{
				ID:                     "successful-refund",
				Status:                 domain.PaymentIntentStatusInsufficientNotRefunded,
				AggregatedCryptoAmount: decimalPtr(decimal.NewFromFloat(2.0)),
				OrgID:                  1,
				PaymentChain:           domain.Holesky,
				TokenAddress:           "******************************************", // USDC on Holesky
				PaymentAddress:         domain.NewEvmAddress("******************************************"),
				PayerAddress:           &payerAddress,
			}, nil)

		mockRepo.EXPECT().
			AcquireLock(gomock.Any(), "payment_intent:successful-refund", gomock.Any()).
			Return(nil)
		mockRepo.EXPECT().
			ReleaseLock(gomock.Any(), "payment_intent:successful-refund")

		mockRepo.EXPECT().
			UpdatePaymentIntent(gomock.Any(), "successful-refund", gomock.Any()).
			DoAndReturn(func(ctx context.Context, id string, update *domain.PaymentIntentUpdate) error {
				assert.Equal(t, domain.PaymentIntentStatusInsufficientRefunded, *update.Status)
				assert.NotNil(t, update.RefundCryptoAmount)
				assert.Equal(t, decimal.NewFromFloat(1.5), *update.RefundCryptoAmount)
				assert.NotNil(t, update.RefundTxHash)
				return nil
			})

		Init(mockRepo, nil, nil)

		kgErr := RefundIntent(ctx, "uid1", req)
		assert.Nil(t, kgErr)
	})
}

func decimalPtr(d decimal.Decimal) *decimal.Decimal {
	return &d
}

func setupSigningServer(t *testing.T) {
	// setup rdb
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())

	// init services
	application.Init(rdb.GormRepo())

	// signing server
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", signing.SignEvmTransaction)
	rSigning.POST("/v1/sign/tron", signing.SignTronTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
}
