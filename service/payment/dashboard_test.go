package payment

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/coingecko"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestGetDashboardMetrics(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	orgID := 1

	// Create mock chain tokens and revenues
	chainToken1 := domain.ChainToken{Chain: domain.Ethereum, TokenID: domain.Ethereum.MainToken().ID()}
	chainToken2 := domain.ChainToken{Chain: domain.Polygon, TokenID: domain.Polygon.MainToken().ID()}

	revenue1, _ := decimal.NewFromString("10.5")
	revenue2, _ := decimal.NewFromString("20.75")

	// Predefine expected metadata for clarity
	ethMetadata := &domain.TokenMetadata{
		Name:        "Ethereum",
		Symbol:      "ETH",
		Decimals:    18,
		CoingeckoID: "ethereum",
		LogoUrl:     "https://ethereum.org/logo.png",
		IsVerified:  true,
	}
	maticMetadata := &domain.TokenMetadata{
		Name:        "Polygon",
		Symbol:      "MATIC",
		Decimals:    18,
		CoingeckoID: "matic-network",
		LogoUrl:     "https://polygon.technology/logo.png",
		IsVerified:  true,
	}

	// Mock return values for repository
	mockStats := &domain.PaymentIntentStats{
		TotalRevenue: map[domain.ChainToken]decimal.Decimal{
			chainToken1: revenue1,
			chainToken2: revenue2,
		},
		ValidOrderCount:     150,
		UniqueCustomerCount: 100,
	}

	// Mock repository
	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
		OrgID: orgID,
		Days:  nil, // All-time stats
	}).Return(mockStats, nil)

	// Mock token metadata fetcher (using the existing mock from tokenmeta package)
	mockTokenMetadataRepo := domain.NewMockTokenMetadataRepo(ctrl)

	// Configure mock token metadata repository to handle individual token requests
	mockTokenMetadataRepo.EXPECT().
		BatchGetTokenMetadata(gomock.Any(), gomock.Any()).
		DoAndReturn(func(_ context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			if len(tokens) != 1 {
				return nil, fmt.Errorf("unexpected number of tokens in BatchGetTokenMetadata call: %d", len(tokens))
			}
			requestedToken := tokens[0]
			resultMap := make(map[domain.ChainToken]*domain.TokenMetadata)
			if requestedToken == chainToken1 {
				resultMap[chainToken1] = ethMetadata
			} else if requestedToken == chainToken2 {
				resultMap[chainToken2] = maticMetadata
			} else {
				return nil, fmt.Errorf("unexpected token requested: %+v", requestedToken)
			}
			return resultMap, nil
		}).AnyTimes()

	// Initialize token metadata service with our mock repository
	tokenmeta.Init(mockTokenMetadataRepo, nil, nil)

	// Mock Coingecko service
	mockCoingecko := coingecko.NewMockIService(ctrl)
	mockCoingecko.EXPECT().QuoteInUSD(gomock.Any(), "ethereum").Return(float64(2000), nil).AnyTimes()
	mockCoingecko.EXPECT().QuoteInUSD(gomock.Any(), "matic-network").Return(float64(1.5), nil).AnyTimes()
	coingecko.Set(mockCoingecko)

	// Initialize service with mocked repository
	Init(mockRepo, nil, nil)

	// Call the function being tested
	metrics, kgErr := GetDashboardMetrics(ctx, orgID)

	// Verify no error occurred
	assert.Nil(t, kgErr)
	assert.NotNil(t, metrics)

	// Verify metrics content
	assert.Equal(t, 150, metrics.ValidOrderCount)
	assert.Equal(t, 100, metrics.UniqueCustomerCount)

	// Verify revenue data
	ethTokenID := domain.Ethereum.MainToken().ID()
	maticTokenID := domain.Polygon.MainToken().ID()

	assert.Len(t, metrics.TotalRevenue, 2) // Ensure both chains are present
	assert.Contains(t, metrics.TotalRevenue, domain.Ethereum.ID())
	assert.Contains(t, metrics.TotalRevenue[domain.Ethereum.ID()], ethTokenID)
	assert.Contains(t, metrics.TotalRevenue, domain.Polygon.ID())
	assert.Contains(t, metrics.TotalRevenue[domain.Polygon.ID()], maticTokenID)

	// Verify ETH data
	ethInfo := metrics.TotalRevenue[domain.Ethereum.ID()][ethTokenID]
	assert.Equal(t, revenue1.String(), ethInfo.Amount.String())
	assert.Equal(t, decimal.NewFromFloat(2000).String(), ethInfo.Rate.String())
	assert.Equal(t, revenue1.Mul(decimal.NewFromFloat(2000)).String(), ethInfo.TotalUSD.String())

	// Verify MATIC data
	maticInfo := metrics.TotalRevenue[domain.Polygon.ID()][maticTokenID]
	assert.Equal(t, revenue2.String(), maticInfo.Amount.String())
	assert.Equal(t, decimal.NewFromFloat(1.5).String(), maticInfo.Rate.String())
	assert.Equal(t, revenue2.Mul(decimal.NewFromFloat(1.5)).String(), maticInfo.TotalUSD.String())
}

func TestGetDashboardMetricsExternalErrorHandling(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	orgID := 1

	// Create mock chain tokens and revenues
	// Use a custom token ID that won't be in knownMetadata to test error handling
	chainToken1 := domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"}

	revenue1, _ := decimal.NewFromString("10.5")

	// Mock return values for repository
	mockStats := &domain.PaymentIntentStats{
		TotalRevenue: map[domain.ChainToken]decimal.Decimal{
			chainToken1: revenue1,
		},
		ValidOrderCount:     150,
		UniqueCustomerCount: 100,
	}

	// Test case 1: Token metadata service failure
	t.Run("TokenMetadataError", func(t *testing.T) {
		// Mock repository for this subtest
		mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
		mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  nil,
		}).Return(mockStats, nil)

		metadataError := errors.New("failed to fetch token metadata")

		// Mock token metadata repository to return error
		mockTokenMetadataRepo := domain.NewMockTokenMetadataRepo(ctrl)
		mockTokenMetadataRepo.EXPECT().
			BatchGetTokenMetadata(gomock.Any(), gomock.Any()). // Use gomock.Any() for the slice
			DoAndReturn(func(_ context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
				// Assert that the expected token is present
				assert.True(t, len(tokens) == 1 && tokens[0] == chainToken1, "Expected tokens to contain only chainToken1")
				return nil, metadataError
			}).AnyTimes()

		// Initialize token metadata service with our mock repository
		tokenmeta.Init(mockTokenMetadataRepo, nil, nil)

		// Initialize service with mocked repository
		Init(mockRepo, nil, nil)

		// Call the function being tested
		metrics, kgErr := GetDashboardMetrics(ctx, orgID)

		// Verify error response
		assert.Nil(t, metrics)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ExternalAPIError, kgErr.Code)
		assert.Equal(t, http.StatusInternalServerError, kgErr.HttpStatus)
		assert.Contains(t, kgErr.Error.Error(), "failed to get token metadata")
	})

	// Test case 2: Coingecko service failure
	t.Run("CoingeckoError", func(t *testing.T) {
		// Mock repository for this subtest
		mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
		mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  nil,
		}).Return(mockStats, nil)

		coingeckoError := errors.New("failed to fetch token price")

		// Mock token metadata repository to return success
		mockTokenMetadataRepo := domain.NewMockTokenMetadataRepo(ctrl)
		mockTokenMetadataRepo.EXPECT().
			BatchGetTokenMetadata(gomock.Any(), gomock.Any()). // Use gomock.Any() for the slice
			DoAndReturn(func(_ context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
				// Assert that the expected token is present
				assert.True(t, len(tokens) == 1 && tokens[0] == chainToken1, "Expected tokens to contain only chainToken1")
				return map[domain.ChainToken]*domain.TokenMetadata{
					chainToken1: { // Return metadata only for the requested token
						Name:        "Test Token",
						Symbol:      "TEST",
						Decimals:    18,
						CoingeckoID: "ethereum",
						LogoUrl:     "https://test.com/logo.png",
						IsVerified:  true,
					},
				}, nil
			}).AnyTimes()

		// Initialize token metadata service with our mock repository
		tokenmeta.Init(mockTokenMetadataRepo, nil, nil)

		// Mock Coingecko service to return error
		mockCoingecko := coingecko.NewMockIService(ctrl)
		mockCoingecko.EXPECT().QuoteInUSD(gomock.Any(), "ethereum").Return(float64(0), coingeckoError).AnyTimes()
		coingecko.Set(mockCoingecko)

		// Initialize service with mocked repository
		Init(mockRepo, nil, nil)

		// Call the function being tested
		metrics, kgErr := GetDashboardMetrics(ctx, orgID)

		// Verify error response
		assert.Nil(t, metrics)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ExternalAPIError, kgErr.Code)
		assert.Equal(t, http.StatusInternalServerError, kgErr.HttpStatus)
		assert.Contains(t, kgErr.Error.Error(), "failed to get rate")
	})

	// Test case 3: Missing Coingecko ID
	t.Run("MissingCoingeckoID", func(t *testing.T) {
		// Mock repository for this subtest
		mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
		mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  nil,
		}).Return(mockStats, nil)

		// Mock token metadata repository to return metadata without Coingecko ID
		mockTokenMetadataRepo := domain.NewMockTokenMetadataRepo(ctrl)
		mockTokenMetadataRepo.EXPECT().
			BatchGetTokenMetadata(gomock.Any(), gomock.Any()). // Use gomock.Any() for the slice
			DoAndReturn(func(_ context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
				// Assert that the expected token is present
				assert.True(t, len(tokens) == 1 && tokens[0] == chainToken1, "Expected tokens to contain only chainToken1")
				return map[domain.ChainToken]*domain.TokenMetadata{
					chainToken1: { // Return metadata only for the requested token
						Name:       "Test Token",
						Symbol:     "TEST",
						Decimals:   18,
						LogoUrl:    "https://test.com/logo.png",
						IsVerified: true,
						// No CoingeckoID
					},
				}, nil
			}).AnyTimes()

		// Initialize token metadata service with our mock repository
		tokenmeta.Init(mockTokenMetadataRepo, nil, nil)

		// Initialize service with mocked repository
		Init(mockRepo, nil, nil)

		// Call the function being tested
		metrics, kgErr := GetDashboardMetrics(ctx, orgID)

		// Verify error response
		assert.Nil(t, metrics)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ExternalAPIError, kgErr.Code)
		assert.Equal(t, http.StatusInternalServerError, kgErr.HttpStatus)
		assert.Contains(t, kgErr.Error.Error(), "has no Coingecko ID")
	})
}

func TestGetDashboardMetricsInvalidOrg(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	// Test with invalid organization ID (0)
	metrics, kgErr := GetDashboardMetrics(ctx, 0)

	// Verify error response
	assert.Nil(t, metrics)
	assert.NotNil(t, kgErr)
	assert.Equal(t, code.ParamIncorrect, kgErr.Code)
	assert.Equal(t, http.StatusBadRequest, kgErr.HttpStatus)

	// Test with invalid organization ID (-1)
	metrics, kgErr = GetDashboardMetrics(ctx, -1)

	// Verify error response
	assert.Nil(t, metrics)
	assert.NotNil(t, kgErr)
	assert.Equal(t, code.ParamIncorrect, kgErr.Code)
	assert.Equal(t, http.StatusBadRequest, kgErr.HttpStatus)
}

func TestGetDashboardMetricsRepositoryError(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	orgID := 1
	repoErr := errors.New("database error")

	// Mock repository to return an error
	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
		OrgID: orgID,
		Days:  nil,
	}).Return(nil, repoErr)

	// Initialize service with mocked repository
	Init(mockRepo, nil, nil)

	// Call the function being tested
	metrics, kgErr := GetDashboardMetrics(ctx, orgID)

	// Verify error response
	assert.Nil(t, metrics)
	assert.NotNil(t, kgErr)
	assert.Equal(t, code.DBError, kgErr.Code)
	assert.Equal(t, http.StatusInternalServerError, kgErr.HttpStatus)
	assert.Equal(t, repoErr, kgErr.Error)
}

func TestGetDashboardMetricsWithTimeRange(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	orgID := 1

	// Create mock chain tokens and revenues
	chainToken1 := domain.ChainToken{Chain: domain.Ethereum, TokenID: domain.Ethereum.MainToken().ID()}
	chainToken2 := domain.ChainToken{Chain: domain.Polygon, TokenID: domain.Polygon.MainToken().ID()}

	revenue1, _ := decimal.NewFromString("5.25")
	revenue2, _ := decimal.NewFromString("10.5")

	// Mock metadata
	ethMetadata := &domain.TokenMetadata{
		Name:        "Ethereum",
		Symbol:      "ETH",
		Decimals:    18,
		CoingeckoID: "ethereum",
		LogoUrl:     "https://ethereum.org/logo.png",
		IsVerified:  true,
	}
	maticMetadata := &domain.TokenMetadata{
		Name:        "Polygon",
		Symbol:      "MATIC",
		Decimals:    18,
		CoingeckoID: "matic-network",
		LogoUrl:     "https://polygon.technology/logo.png",
		IsVerified:  true,
	}

	t.Run("Last7Days", func(t *testing.T) {
		days := 7
		params := domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  &days,
		}

		// Mock return values for repository
		mockStats := &domain.PaymentIntentStats{
			TotalRevenue: map[domain.ChainToken]decimal.Decimal{
				chainToken1: revenue1,
				chainToken2: revenue2,
			},
			ValidOrderCount:     25,
			UniqueCustomerCount: 15,
		}

		// Mock repository
		mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
		mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), params).Return(mockStats, nil)

		// Mock token metadata
		mockTokenMetadataRepo := domain.NewMockTokenMetadataRepo(ctrl)
		mockTokenMetadataRepo.EXPECT().
			BatchGetTokenMetadata(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
				resultMap := make(map[domain.ChainToken]*domain.TokenMetadata)
				for _, token := range tokens {
					if token == chainToken1 {
						resultMap[chainToken1] = ethMetadata
					} else if token == chainToken2 {
						resultMap[chainToken2] = maticMetadata
					}
				}
				return resultMap, nil
			}).AnyTimes()

		tokenmeta.Init(mockTokenMetadataRepo, nil, nil)

		// Mock Coingecko service
		mockCoingecko := coingecko.NewMockIService(ctrl)
		mockCoingecko.EXPECT().QuoteInUSD(gomock.Any(), "ethereum").Return(float64(2000), nil).AnyTimes()
		mockCoingecko.EXPECT().QuoteInUSD(gomock.Any(), "matic-network").Return(float64(1.5), nil).AnyTimes()
		coingecko.Set(mockCoingecko)

		// Initialize service
		Init(mockRepo, nil, nil)

		// Call the function being tested
		serviceParams := GetDashboardMetricsParams{
			OrgID: orgID,
			Days:  &days,
		}
		metrics, kgErr := GetDashboardMetricsWithTimeRange(ctx, serviceParams)

		// Verify no error occurred
		assert.Nil(t, kgErr)
		assert.NotNil(t, metrics)

		// Verify metrics content
		assert.Equal(t, 25, metrics.ValidOrderCount)
		assert.Equal(t, 15, metrics.UniqueCustomerCount)

		// Verify revenue data
		assert.Len(t, metrics.TotalRevenue, 2)

		// Verify ETH data
		ethTokenID := domain.Ethereum.MainToken().ID()
		ethInfo := metrics.TotalRevenue[domain.Ethereum.ID()][ethTokenID]
		assert.Equal(t, revenue1.String(), ethInfo.Amount.String())
		assert.Equal(t, decimal.NewFromFloat(2000).String(), ethInfo.Rate.String())
		assert.Equal(t, revenue1.Mul(decimal.NewFromFloat(2000)).String(), ethInfo.TotalUSD.String())

		// Verify MATIC data
		maticTokenID := domain.Polygon.MainToken().ID()
		maticInfo := metrics.TotalRevenue[domain.Polygon.ID()][maticTokenID]
		assert.Equal(t, revenue2.String(), maticInfo.Amount.String())
		assert.Equal(t, decimal.NewFromFloat(1.5).String(), maticInfo.Rate.String())
		assert.Equal(t, revenue2.Mul(decimal.NewFromFloat(1.5)).String(), maticInfo.TotalUSD.String())
	})

	t.Run("Last30Days", func(t *testing.T) {
		days := 30
		params := domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  &days,
		}

		// Mock return values for repository
		mockStats := &domain.PaymentIntentStats{
			TotalRevenue: map[domain.ChainToken]decimal.Decimal{
				chainToken1: decimal.NewFromInt(100),
				chainToken2: decimal.NewFromInt(50),
			},
			ValidOrderCount:     75,
			UniqueCustomerCount: 45,
		}

		// Mock repository
		mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
		mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), params).Return(mockStats, nil)

		// Mock token metadata
		mockTokenMetadataRepo := domain.NewMockTokenMetadataRepo(ctrl)
		mockTokenMetadataRepo.EXPECT().
			BatchGetTokenMetadata(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
				resultMap := make(map[domain.ChainToken]*domain.TokenMetadata)
				for _, token := range tokens {
					if token == chainToken1 {
						resultMap[chainToken1] = ethMetadata
					} else if token == chainToken2 {
						resultMap[chainToken2] = maticMetadata
					}
				}
				return resultMap, nil
			}).AnyTimes()

		tokenmeta.Init(mockTokenMetadataRepo, nil, nil)

		// Mock Coingecko service
		mockCoingecko := coingecko.NewMockIService(ctrl)
		mockCoingecko.EXPECT().QuoteInUSD(gomock.Any(), "ethereum").Return(float64(2000), nil).AnyTimes()
		mockCoingecko.EXPECT().QuoteInUSD(gomock.Any(), "matic-network").Return(float64(1.5), nil).AnyTimes()
		coingecko.Set(mockCoingecko)

		// Initialize service
		Init(mockRepo, nil, nil)

		// Call the function being tested
		serviceParams := GetDashboardMetricsParams{
			OrgID: orgID,
			Days:  &days,
		}
		metrics, kgErr := GetDashboardMetricsWithTimeRange(ctx, serviceParams)

		// Verify no error occurred
		assert.Nil(t, kgErr)
		assert.NotNil(t, metrics)

		// Verify metrics content
		assert.Equal(t, 75, metrics.ValidOrderCount)
		assert.Equal(t, 45, metrics.UniqueCustomerCount)
	})

	t.Run("InvalidDaysParameter", func(t *testing.T) {
		invalidDays := 91
		serviceParams := GetDashboardMetricsParams{
			OrgID: orgID,
			Days:  &invalidDays,
		}

		// Mock repository - shouldn't be called due to validation
		mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
		Init(mockRepo, nil, nil)

		// Call the function being tested
		metrics, kgErr := GetDashboardMetricsWithTimeRange(ctx, serviceParams)

		// Verify error response
		assert.Nil(t, metrics)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Equal(t, http.StatusBadRequest, kgErr.HttpStatus)
		assert.Contains(t, kgErr.Data["error"], "days parameter must be between 1 and 90")
	})

	t.Run("RepositoryError", func(t *testing.T) {
		days := 7
		params := domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  &days,
		}

		// Mock repository to return error
		mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
		mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), params).Return(nil, errors.New("database error"))

		// Initialize service
		Init(mockRepo, nil, nil)

		// Call the function being tested
		serviceParams := GetDashboardMetricsParams{
			OrgID: orgID,
			Days:  &days,
		}
		metrics, kgErr := GetDashboardMetricsWithTimeRange(ctx, serviceParams)

		// Verify error response
		assert.Nil(t, metrics)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.DBError, kgErr.Code)
		assert.Equal(t, http.StatusInternalServerError, kgErr.HttpStatus)
	})
}

// TestGetDashboardMetricsMultipleTokensSameChain tests the critical scenario where
// multiple tokens exist on the same blockchain, ensuring they are all correctly aggregated
func TestGetDashboardMetricsMultipleTokensSameChain(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	orgID := 1

	// Create multiple tokens on the same chain (Ethereum)
	usdtToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"} // USDT
	usdcToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"} // USDC
	daiToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"}  // DAI

	// Different revenues for each token
	usdtRevenue, _ := decimal.NewFromString("1000.50")
	usdcRevenue, _ := decimal.NewFromString("750.25")
	daiRevenue, _ := decimal.NewFromString("500.75")

	// Mock metadata for all tokens
	usdtMetadata := &domain.TokenMetadata{
		Name:        "Tether USD",
		Symbol:      "USDT",
		Decimals:    6,
		CoingeckoID: "tether",
		LogoUrl:     "https://tether.to/logo.png",
		IsVerified:  true,
	}
	usdcMetadata := &domain.TokenMetadata{
		Name:        "USD Coin",
		Symbol:      "USDC",
		Decimals:    6,
		CoingeckoID: "usd-coin",
		LogoUrl:     "https://centre.io/logo.png",
		IsVerified:  true,
	}
	daiMetadata := &domain.TokenMetadata{
		Name:        "Dai Stablecoin",
		Symbol:      "DAI",
		Decimals:    18,
		CoingeckoID: "dai",
		LogoUrl:     "https://makerdao.com/logo.png",
		IsVerified:  true,
	}

	// Mock return values for repository
	mockStats := &domain.PaymentIntentStats{
		TotalRevenue: map[domain.ChainToken]decimal.Decimal{
			usdtToken: usdtRevenue,
			usdcToken: usdcRevenue,
			daiToken:  daiRevenue,
		},
		ValidOrderCount:     300,
		UniqueCustomerCount: 150,
	}

	// Mock repository
	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockRepo.EXPECT().GetPaymentIntentStatsWithTimeRange(gomock.Any(), domain.GetPaymentIntentStatsParams{
		OrgID: orgID,
		Days:  nil, // All-time stats
	}).Return(mockStats, nil)

	// Mock token metadata fetcher
	mockTokenMetadataRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockTokenMetadataRepo.EXPECT().
		BatchGetTokenMetadata(gomock.Any(), gomock.Any()).
		DoAndReturn(func(_ context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			resultMap := make(map[domain.ChainToken]*domain.TokenMetadata)
			for _, token := range tokens {
				switch token {
				case usdtToken:
					resultMap[usdtToken] = usdtMetadata
				case usdcToken:
					resultMap[usdcToken] = usdcMetadata
				case daiToken:
					resultMap[daiToken] = daiMetadata
				}
			}
			return resultMap, nil
		}).AnyTimes()

	// Initialize token metadata service with our mock repository
	tokenmeta.Init(mockTokenMetadataRepo, nil, nil)

	// Mock Coingecko service - all stablecoins should be close to $1
	mockCoingecko := coingecko.NewMockIService(ctrl)
	mockCoingecko.EXPECT().QuoteInUSD(gomock.Any(), "tether").Return(float64(1.0), nil).AnyTimes()
	mockCoingecko.EXPECT().QuoteInUSD(gomock.Any(), "usd-coin").Return(float64(1.0), nil).AnyTimes()
	mockCoingecko.EXPECT().QuoteInUSD(gomock.Any(), "dai").Return(float64(0.999), nil).AnyTimes()
	coingecko.Set(mockCoingecko)

	// Initialize service with mocked repository
	Init(mockRepo, nil, nil)

	// Call the function being tested
	metrics, kgErr := GetDashboardMetrics(ctx, orgID)

	// Verify no error occurred
	assert.Nil(t, kgErr)
	assert.NotNil(t, metrics)

	// Verify metrics content
	assert.Equal(t, 300, metrics.ValidOrderCount)
	assert.Equal(t, 150, metrics.UniqueCustomerCount)

	// CRITICAL VERIFICATION: All three tokens should be present on the same chain
	ethChainID := domain.Ethereum.ID()
	assert.Contains(t, metrics.TotalRevenue, ethChainID, "Ethereum chain should be present in revenue")

	ethTokens := metrics.TotalRevenue[ethChainID]
	assert.Len(t, ethTokens, 3, "All three tokens should be present on Ethereum chain")

	// Verify USDT data
	assert.Contains(t, ethTokens, usdtToken.TokenID, "USDT should be present")
	usdtInfo := ethTokens[usdtToken.TokenID]
	assert.Equal(t, usdtRevenue.String(), usdtInfo.Amount.String())
	assert.Equal(t, decimal.NewFromFloat(1.0).String(), usdtInfo.Rate.String())
	assert.Equal(t, usdtRevenue.Mul(decimal.NewFromFloat(1.0)).String(), usdtInfo.TotalUSD.String())

	// Verify USDC data
	assert.Contains(t, ethTokens, usdcToken.TokenID, "USDC should be present")
	usdcInfo := ethTokens[usdcToken.TokenID]
	assert.Equal(t, usdcRevenue.String(), usdcInfo.Amount.String())
	assert.Equal(t, decimal.NewFromFloat(1.0).String(), usdcInfo.Rate.String())
	assert.Equal(t, usdcRevenue.Mul(decimal.NewFromFloat(1.0)).String(), usdcInfo.TotalUSD.String())

	// Verify DAI data
	assert.Contains(t, ethTokens, daiToken.TokenID, "DAI should be present")
	daiInfo := ethTokens[daiToken.TokenID]
	assert.Equal(t, daiRevenue.String(), daiInfo.Amount.String())
	assert.Equal(t, decimal.NewFromFloat(0.999).String(), daiInfo.Rate.String())
	assert.Equal(t, daiRevenue.Mul(decimal.NewFromFloat(0.999)).String(), daiInfo.TotalUSD.String())

	// Calculate total USD across all tokens
	expectedTotalUSD := usdtRevenue.Mul(decimal.NewFromFloat(1.0)).
		Add(usdcRevenue.Mul(decimal.NewFromFloat(1.0))).
		Add(daiRevenue.Mul(decimal.NewFromFloat(0.999)))

	actualTotalUSD := usdtInfo.TotalUSD.Add(usdcInfo.TotalUSD).Add(daiInfo.TotalUSD)
	assert.Equal(t, expectedTotalUSD.String(), actualTotalUSD.String(), "Total USD calculation should be correct")

	// Log success message
	t.Logf("SUCCESS: All three tokens correctly aggregated on the same chain")
	t.Logf("USDT: %s USD, USDC: %s USD, DAI: %s USD",
		usdtInfo.TotalUSD.String(), usdcInfo.TotalUSD.String(), daiInfo.TotalUSD.String())
}
