package payment

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	coingeckoapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/coingecko-api"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestGetQuote(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()

	// Mock CoinGecko API
	mockCoingecko := coingeckoapi.NewMockICoingecko(ctrl)
	coingeckoapi.Set(mockCoingecko)

	// Mock token metadata repo
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(mockTokenMetaRepo, nil, nil)

	// Add known metadata for the tokens we'll be testing on all supported chains
	usdcMetadata := &domain.TokenMetadata{
		Name:        "USD Coin",
		Symbol:      "USDC",
		Decimals:    6,
		CoingeckoID: "usd-coin",
		LogoUrl:     "https://usdc.logo",
		IsVerified:  true,
	}
	usdtMetadata := &domain.TokenMetadata{
		Name:        "Tether USD",
		Symbol:      "USDT",
		Decimals:    6,
		CoingeckoID: "tether",
		LogoUrl:     "https://usdt.logo",
		IsVerified:  true,
	}

	tokenmeta.AddKnown(map[domain.ChainToken]*domain.TokenMetadata{
		// Arbitrum
		{Chain: domain.Arbitrum, TokenID: "******************************************"}: usdcMetadata,
		{Chain: domain.Arbitrum, TokenID: "******************************************"}: usdtMetadata,
		// Optimism
		{Chain: domain.Optimism, TokenID: "******************************************"}: usdcMetadata,
		{Chain: domain.Optimism, TokenID: "******************************************"}: usdtMetadata,
		// Base
		{Chain: domain.BaseChain, TokenID: "******************************************"}: usdcMetadata,
	})

	t.Run("GetQuote_Fiat_To_DefaultTokens", func(t *testing.T) {
		// Test fiat to default supported tokens (USDC, USDT on Arbitrum, Optimism and Base)
		params := QuoteParams{
			SourceType:     QuoteSourceTypeFiat,
			SourceCurrency: stringPtr("USD"),
			Amount:         "100",
			TargetTokens:   []TargetToken{}, // Should default to supported tokens
		}

		// Mock currency support check
		mockCoingecko.EXPECT().IsCurrencySupported(gomock.Any(), "usd").Return(true, nil)

		// Mock exchange rate calls - need more calls now for all chains
		mockCoingecko.EXPECT().SimplePrice(gomock.Any(), []domain.CoingeckoID{"usd-coin"}, "usd").Return(
			map[domain.CoingeckoID]float64{"usd-coin": 1.0}, nil).Times(3) // USDC on 3 chains
		mockCoingecko.EXPECT().SimplePrice(gomock.Any(), []domain.CoingeckoID{"tether"}, "usd").Return(
			map[domain.CoingeckoID]float64{"tether": 1.0}, nil).Times(2) // USDT on 2 chains

		quote, kgErr := GetQuote(ctx, params)

		// Check error first
		if kgErr != nil {
			t.Fatalf("GetQuote failed unexpectedly: %v", kgErr)
		}

		assert.NotNil(t, quote)
		assert.Equal(t, QuoteSourceTypeFiat, quote.SourceType)
		assert.Equal(t, "USD", *quote.SourceCurrency)
		assert.True(t, decimal.NewFromInt(100).Equal(quote.SourceAmount))
		assert.Len(t, quote.Results, 5) // Now expecting 5 results: arb(USDC,USDT), optimism(USDC,USDT), base(USDC)

		// Count results by chain and verify expected combinations
		chainResults := make(map[string][]string)
		for _, result := range quote.Results {
			chainResults[result.ChainID] = append(chainResults[result.ChainID], result.Symbol)
			// Use decimal comparison with tolerance for precision issues
			assert.True(t, decimal.NewFromInt(100).Sub(result.Amount).Abs().LessThan(decimal.NewFromFloat(0.001)))
			assert.True(t, decimal.NewFromInt(1).Sub(result.ExchangeRate).Abs().LessThan(decimal.NewFromFloat(0.001)))
		}

		// Verify expected chain/token combinations
		assert.Len(t, chainResults["arb"], 2)      // Arbitrum: USDC, USDT
		assert.Len(t, chainResults["optimism"], 2) // Optimism: USDC, USDT
		assert.Len(t, chainResults["base"], 1)     // Base: USDC only
	})

	t.Run("GetQuote_Crypto_To_DefaultTokens", func(t *testing.T) {
		// Test crypto to default supported tokens
		sourceChainID := "eth"
		sourceContractAddress := "******************************************"
		params := QuoteParams{
			SourceType:            QuoteSourceTypeCrypto,
			SourceChainID:         &sourceChainID,
			SourceContractAddress: &sourceContractAddress,
			Amount:                "1000",
			TargetTokens:          []TargetToken{}, // Should default to supported tokens
		}

		// Add known metadata for the source token (ETH)
		ethMetadata := &domain.TokenMetadata{
			Name:        "Ethereum",
			Symbol:      "ETH",
			Decimals:    18,
			CoingeckoID: "ethereum",
			LogoUrl:     "https://eth.logo",
			IsVerified:  true,
		}
		tokenmeta.AddKnown(map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: domain.Ethereum, TokenID: sourceContractAddress}: ethMetadata,
		})

		// Mock BatchGetTokenMetadata for the source token lookup
		mockTokenMetaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).Return(
			map[domain.ChainToken]*domain.TokenMetadata{
				{Chain: domain.Ethereum, TokenID: sourceContractAddress}: ethMetadata,
			}, nil).AnyTimes()

		// Mock price calls - need more calls now for all chains
		mockCoingecko.EXPECT().SimplePrice(gomock.Any(), []domain.CoingeckoID{"ethereum"}, "usd").Return(
			map[domain.CoingeckoID]float64{"ethereum": 2000.0}, nil) // 1 ETH = $2000
		mockCoingecko.EXPECT().SimplePrice(gomock.Any(), []domain.CoingeckoID{"usd-coin"}, "usd").Return(
			map[domain.CoingeckoID]float64{"usd-coin": 1.0}, nil).Times(3) // USDC on 3 chains
		mockCoingecko.EXPECT().SimplePrice(gomock.Any(), []domain.CoingeckoID{"tether"}, "usd").Return(
			map[domain.CoingeckoID]float64{"tether": 1.0}, nil).Times(2) // USDT on 2 chains

		quote, kgErr := GetQuote(ctx, params)

		if kgErr != nil {
			t.Fatalf("GetQuote failed unexpectedly: %v", kgErr)
		}

		assert.NotNil(t, quote)
		assert.Equal(t, QuoteSourceTypeCrypto, quote.SourceType)
		assert.Equal(t, sourceChainID, *quote.SourceChainID)
		assert.Equal(t, "ETH", *quote.SourceSymbol)
		assert.True(t, decimal.NewFromInt(1000).Equal(quote.SourceAmount))
		assert.Len(t, quote.Results, 5) // Now expecting 5 results: arb(USDC,USDT), optimism(USDC,USDT), base(USDC)

		// Count results by chain and verify expected combinations
		chainResults := make(map[string][]string)
		for _, result := range quote.Results {
			chainResults[result.ChainID] = append(chainResults[result.ChainID], result.Symbol)
			// Check results - 1000 ETH * $2000 = $2,000,000 worth
			assert.True(t, decimal.NewFromInt(2000000).Sub(result.Amount).Abs().LessThan(decimal.NewFromFloat(0.001)))
			assert.True(t, decimal.NewFromInt(2000).Sub(result.ExchangeRate).Abs().LessThan(decimal.NewFromFloat(0.001)))
		}

		// Verify expected chain/token combinations
		assert.Len(t, chainResults["arb"], 2)      // Arbitrum: USDC, USDT
		assert.Len(t, chainResults["optimism"], 2) // Optimism: USDC, USDT
		assert.Len(t, chainResults["base"], 1)     // Base: USDC only
	})

	t.Run("GetQuote_Fiat_To_SpecificToken", func(t *testing.T) {
		// Test fiat to specific target token - can use any of the supported chains
		params := QuoteParams{
			SourceType:     QuoteSourceTypeFiat,
			SourceCurrency: stringPtr("TWD"),
			Amount:         "500",
			TargetTokens: []TargetToken{
				{ChainID: "optimism", Symbol: "USDC"}, // Testing with Optimism instead of hardcoded arb
			},
		}

		// Mock currency support check
		mockCoingecko.EXPECT().IsCurrencySupported(gomock.Any(), "twd").Return(true, nil)

		// Mock exchange rate call - 1 USDC = 30 TWD
		mockCoingecko.EXPECT().SimplePrice(gomock.Any(), []domain.CoingeckoID{"usd-coin"}, "twd").Return(
			map[domain.CoingeckoID]float64{"usd-coin": 30.0}, nil)

		quote, kgErr := GetQuote(ctx, params)

		if kgErr != nil {
			t.Fatalf("GetQuote failed unexpectedly: %v", kgErr)
		}

		assert.NotNil(t, quote)
		assert.Equal(t, QuoteSourceTypeFiat, quote.SourceType)
		assert.Equal(t, "TWD", *quote.SourceCurrency)
		assert.True(t, decimal.NewFromInt(500).Equal(quote.SourceAmount))
		assert.Len(t, quote.Results, 1)

		result := quote.Results[0]
		assert.Equal(t, "optimism", result.ChainID)
		assert.Equal(t, "USDC", result.Symbol)
		assert.Equal(t, "******************************************", result.TokenAddress) // Optimism USDC address
		// 500 TWD / 30 TWD per USDC = 16.666... USDC
		expectedAmount := decimal.NewFromInt(500).Div(decimal.NewFromInt(30))
		assert.True(t, expectedAmount.Sub(result.Amount).Abs().LessThan(decimal.NewFromFloat(0.000001)))
		assert.True(t, decimal.NewFromInt(30).Sub(result.ExchangeRate).Abs().LessThan(decimal.NewFromFloat(0.000001)))
	})

	t.Run("GetQuote_InvalidSourceType", func(t *testing.T) {
		params := QuoteParams{
			SourceType: "invalid",
			Amount:     "100",
		}

		quote, kgErr := GetQuote(ctx, params)

		assert.NotNil(t, kgErr)
		assert.Nil(t, quote)
		assert.Contains(t, kgErr.Error.Error(), "invalid source_type")
	})

	t.Run("GetQuote_InvalidAmount", func(t *testing.T) {
		params := QuoteParams{
			SourceType:     QuoteSourceTypeFiat,
			SourceCurrency: stringPtr("USD"),
			Amount:         "invalid",
		}

		quote, kgErr := GetQuote(ctx, params)

		assert.NotNil(t, kgErr)
		assert.Nil(t, quote)
		assert.Contains(t, kgErr.Error.Error(), "invalid amount")
	})

	t.Run("GetQuote_UnsupportedFiatCurrency", func(t *testing.T) {
		params := QuoteParams{
			SourceType:     QuoteSourceTypeFiat,
			SourceCurrency: stringPtr("INVALID"),
			Amount:         "100",
		}

		// Mock currency support check
		mockCoingecko.EXPECT().IsCurrencySupported(gomock.Any(), "invalid").Return(false, nil)

		quote, kgErr := GetQuote(ctx, params)

		assert.NotNil(t, kgErr)
		assert.Nil(t, quote)
		assert.Contains(t, kgErr.Error.Error(), "unsupported fiat currency")
	})
}

func stringPtr(s string) *string {
	return &s
}
