package payment

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"fmt"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCallbackSignatureGeneration(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Mock dependencies
	mockAppRepo := domain.NewMockApplicationRepo(ctrl)
	application.Init(mockAppRepo)

	// Test data
	ctx := context.Background()
	clientID := "test-client-id"
	clientSecret := "test-client-secret"

	// Mock the application repository to return our test client
	mockAppRepo.EXPECT().GetApplication(gomock.Any(), clientID).Return(&domain.Application{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		Name:         "Test App",
		Domain:       "example.com",
	}, nil)

	// Generate a signed payload
	payload, err := signJSONPayload(ctx, clientID, []byte(`{"test":"value"}`))
	assert.NoError(t, err)

	// Parse the payload to verify signature was added
	var payloadMap map[string]interface{}
	err = json.Unmarshal(payload, &payloadMap)
	assert.NoError(t, err)

	// Verify the signature field exists
	signature, ok := payloadMap["signature"].(string)
	assert.True(t, ok, "Signature should be a string")
	assert.NotEmpty(t, signature, "Signature should not be empty")

	// Verify timestamp was added
	timestamp, ok := payloadMap["timestamp"].(float64)
	assert.True(t, ok, "Timestamp should be a number")
	assert.Greater(t, timestamp, float64(0), "Timestamp should be positive")
}

func TestCallbackPayloadValidation(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Mock dependencies
	mockAppRepo := domain.NewMockApplicationRepo(ctrl)
	mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
	application.Init(mockAppRepo)
	Init(nil, mockCallbackExecutor, nil)

	// Test data
	ctx := context.Background()
	clientID := "test-client-id"
	clientSecret := "test-client-secret"
	callbackURL := "https://example.com/callback"
	cryptoAmount, _ := decimal.NewFromString("10.5")
	fiatAmount, _ := decimal.NewFromString("100")
	deadline := time.Now().Add(30 * time.Minute)
	paymentTxTimestamp := time.Now()
	// Create a payment intent
	intent := &domain.PaymentIntent{
		ID:                 "test-intent-id",
		ClientID:           clientID,
		FiatAmount:         util.Ptr(fiatAmount),
		FiatCurrency:       util.Ptr("USD"),
		CryptoAmount:       cryptoAmount,
		PaymentDeadline:    deadline,
		Status:             domain.PaymentIntentStatusPending,
		PaymentChain:       domain.Ethereum,
		Symbol:             "ETH",
		OrderData:          map[string]any{"order_id": "12345"},
		CallbackURL:        &callbackURL,
		PaymentAddress:     domain.NewEvmAddress("******************************************"),
		TokenAddress:       "0xTokenAddress",
		PayerAddress:       domain.NewEvmAddress("0xPayerAddress"),
		PaymentTxTimestamp: &paymentTxTimestamp,
		PricingMode:        "fiat",
	}

	// Mock the application repository to return our test client
	mockAppRepo.EXPECT().GetApplication(gomock.Any(), clientID).AnyTimes().Return(&domain.Application{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		Name:         "Test App",
		Domain:       "example.com",
	}, nil)

	// Mock the callback executor to capture the payload
	var capturedPayload []byte
	done := make(chan bool)
	mockCallbackExecutor.EXPECT().SendRequest(gomock.Any(), gomock.Eq(callbackURL), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, url string, payload []byte, execCtx domain.CallbackExecutionContext) (*resty.Response, error) {
			capturedPayload = payload
			done <- true // Signal that the callback has been captured
			return &resty.Response{}, nil
		})

	// Send the callback
	IntentStatusChanged(ctx, intent)

	// Wait for the callback to complete (since it's now asynchronous)
	select {
	case <-done:
		// Continue with assertions
	case <-time.After(2 * time.Second):
		t.Fatal("Timed out waiting for callback to complete")
	}

	// Now simulate client-side validation
	var payloadMap map[string]interface{}
	err := json.Unmarshal(capturedPayload, &payloadMap)
	assert.NoError(t, err)

	// Assert KGDeepLink in captured payload
	kgDeepLink, ok := payloadMap["kg_deep_link"].(string)
	assert.True(t, ok, "KGDeepLink should be a string in payload")
	expectedDeepLink := fmt.Sprintf(kgDeepLinkFormat,
		intent.PaymentAddress.String(),
		intent.PaymentChain.ID(),
		intent.TokenAddress,
		intent.CryptoAmount.String(),
	)
	assert.Equal(t, expectedDeepLink, kgDeepLink, "KGDeepLink value mismatch")

	// Extract signature and timestamp
	signature, ok := payloadMap["signature"].(string)
	assert.True(t, ok, "Signature should be a string")
	timestamp, ok := payloadMap["timestamp"].(float64)
	assert.True(t, ok, "Timestamp should be a number")

	// Validate timestamp is recent (within 5 minutes)
	timestampTime := time.Unix(int64(timestamp), 0)
	assert.True(t, time.Since(timestampTime) < 5*time.Minute, "Timestamp should be recent")

	// Remove signature to create payload for validation
	delete(payloadMap, "signature")

	// Marshal without signature
	unsignedPayload, err := json.Marshal(payloadMap)
	assert.NoError(t, err)

	// Generate expected signature
	expectedSignature := generateHMAC(unsignedPayload, clientSecret)

	// Compare signatures
	assert.Equal(t, expectedSignature, signature, "Signatures should match")
}

// This function shows how a client would validate a callback payload
func validateCallbackPayload(payload []byte, clientSecret string) (bool, error) {
	// Parse the payload
	var payloadMap map[string]interface{}
	if err := json.Unmarshal(payload, &payloadMap); err != nil {
		return false, err
	}

	// Extract and remove signature
	signature, ok := payloadMap["signature"].(string)
	if !ok {
		return false, nil // Missing signature
	}
	delete(payloadMap, "signature")

	// Extract timestamp
	timestamp, ok := payloadMap["timestamp"].(float64)
	if !ok {
		return false, nil // Missing timestamp
	}

	// Check if timestamp is within validity period
	timestampTime := time.Unix(int64(timestamp), 0)
	if time.Since(timestampTime) > signatureValidityDuration {
		kglog.Warning("Signature timestamp is too old")
		return false, nil
	}

	// Marshal without signature
	unsignedPayload, err := json.Marshal(payloadMap)
	if err != nil {
		return false, err
	}

	// Generate expected signature
	expectedSignature := generateHMAC(unsignedPayload, clientSecret)

	// Compare signatures
	return expectedSignature == signature, nil
}

func TestClientValidationFunction(t *testing.T) {
	// Create a sample payload
	clientSecret := "test-secret-key"
	payload := map[string]interface{}{
		"payment_intent_id": "test-123",
		"client_id":         "client-456",
		"amount":            "100.00",
		"timestamp":         time.Now().Unix(),
	}

	// Marshal the payload
	payloadBytes, err := json.Marshal(payload)
	assert.NoError(t, err)

	// Sign the payload
	signature := generateHMAC(payloadBytes, clientSecret)

	// Add signature
	payload["signature"] = signature

	// Marshal full payload with signature
	signedPayloadBytes, err := json.Marshal(payload)
	assert.NoError(t, err)

	// Validate using client function
	valid, err := validateCallbackPayload(signedPayloadBytes, clientSecret)
	assert.NoError(t, err)
	assert.True(t, valid, "Signature should be valid")

	// Test with wrong secret
	valid, err = validateCallbackPayload(signedPayloadBytes, "wrong-secret")
	assert.NoError(t, err)
	assert.False(t, valid, "Signature should be invalid with wrong secret")

	// Test with expired timestamp
	oldPayload := map[string]interface{}{
		"payment_intent_id": "test-123",
		"client_id":         "client-456",
		"amount":            "100.00",
		"timestamp":         time.Now().Add(-10 * time.Minute).Unix(),
	}
	oldPayloadBytes, err := json.Marshal(oldPayload)
	assert.NoError(t, err)
	oldSignature := generateHMAC(oldPayloadBytes, clientSecret)
	oldPayload["signature"] = oldSignature
	oldSignedPayloadBytes, err := json.Marshal(oldPayload)
	assert.NoError(t, err)

	valid, err = validateCallbackPayload(oldSignedPayloadBytes, clientSecret)
	assert.NoError(t, err)
	assert.False(t, valid, "Signature should be invalid due to expired timestamp")
}
