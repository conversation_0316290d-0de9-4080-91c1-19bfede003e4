package payment_test

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/kg-wallet-backend/chain/evm"
	evmtest "github.com/kryptogo/kg-wallet-backend/chain/evm/test"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	coingeckoapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/coingecko-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/erc4337"
	"github.com/kryptogo/kg-wallet-backend/service/payment"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	universalswap "github.com/kryptogo/kg-wallet-backend/service/universal-swap"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestIntentE2E(t *testing.T) {
	rdb.Reset()
	assert.NoError(t, rdb.CreateSeedData())
	application.Init(rdb.GormRepo())
	oauth.Init(rdb.GormRepo())
	erc4337.Init(rdb.GormRepo())
	uRepo := repo.Unified()
	orgInitParam := organization.InitParam{
		StudioOrgRepo:       uRepo,
		StudioRoleRepo:      uRepo,
		StudioRoleCacheRepo: cache.NewRedisStudioRoleCacheRepo(cache.Client),
		SendgridClient:      nil,
	}
	organization.Init(orgInitParam)
	universalswap.Init(uRepo, nil, "")

	ctx := context.Background()
	ctrl := gomock.NewController(t)
	payToken := "USDC"
	tokenAddress := "******************************************"
	clientID := "41902cd3a636c7eb0af0fe9b"
	origin := "http://localhost:8040"
	fiatAmount := "500"
	fiatCurrency := "USD"

	t.Run("Bad amount", func(t *testing.T) {
		chain := domain.Sepolia
		paymentIntent, kgErr := payment.CreateIntent(ctx, payment.CreateIntentParams{
			Chain:       chain,
			PayToken:    payToken,
			ClientID:    clientID,
			Origin:      origin,
			Amount:      "0.ab1ccd3=",
			Currency:    util.Ptr(fiatCurrency),
			PricingMode: payment.PricingModeFiat,
			CallbackURL: util.Ptr(""),
		})
		t.Logf("kgErr: %+v", kgErr)
		assert.Error(t, kgErr.Error)
		assert.Contains(t, kgErr.Error.Error(), "invalid amount")
		assert.Nil(t, paymentIntent)

		paymentIntent, kgErr = payment.CreateIntent(ctx, payment.CreateIntentParams{
			Chain:       chain,
			PayToken:    payToken,
			ClientID:    clientID,
			Origin:      "https://wallet-dev.kryptogo.com",
			Amount:      "0.ab1ccd3=",
			Currency:    util.Ptr(fiatCurrency),
			PricingMode: payment.PricingModeFiat,
			CallbackURL: util.Ptr(""),
		})
		t.Logf("kgErr: %+v", kgErr)
		assert.Error(t, kgErr.Error)
		assert.Contains(t, kgErr.Error.Error(), "invalid amount")
		assert.Nil(t, paymentIntent)

		paymentIntent, kgErr = payment.CreateIntent(ctx, payment.CreateIntentParams{
			Chain:       chain,
			PayToken:    payToken,
			ClientID:    clientID,
			Origin:      "http://localhost:1234",
			Amount:      "0.ab1ccd3=",
			Currency:    util.Ptr("USD"),
			PricingMode: payment.PricingModeFiat,
			CallbackURL: util.Ptr(""),
		})
		t.Logf("kgErr: %+v", kgErr)
		assert.Error(t, kgErr.Error)
		assert.Contains(t, kgErr.Error.Error(), "invalid amount")
		assert.Nil(t, paymentIntent)
	})

	t.Run("Bad callback url", func(t *testing.T) {
		chain := domain.Sepolia

		signingservertest.Setup(t)
		coingeckoapi.InitDefault(domain.NewAllPassRateLimiter())

		paymentIntent, kgErr := payment.CreateIntent(ctx, payment.CreateIntentParams{
			Chain:       chain,
			PayToken:    payToken,
			ClientID:    clientID,
			Origin:      origin,
			Amount:      "0.1",
			Currency:    util.Ptr(fiatCurrency),
			PricingMode: payment.PricingModeFiat,
			CallbackURL: util.Ptr("123"),
		})
		t.Logf("kgErr: %+v", kgErr)
		assert.Error(t, kgErr.Error)
		assert.Contains(t, kgErr.Error.Error(), "invalid callback url")
		assert.Nil(t, paymentIntent)
	})

	t.Run("Payment intent success - Holesky", func(t *testing.T) {
		chain := domain.Holesky
		signingservertest.Setup(t)
		coingeckoapi.InitDefault(domain.NewAllPassRateLimiter())

		mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
		var paymentAddress string
		mockAlchemy.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
			paymentAddress = addresses[0]
			return nil
		})
		mockAlchemy.EXPECT().RemoveSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
			assert.Equal(t, addresses, []string{paymentAddress})
			return nil
		})
		alchemyapi.Set(mockAlchemy)

		user.Init(repo.Unified())
		mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
		mockTokenMetaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			assert.Equal(t, tokens, []domain.ChainToken{
				{
					Chain:   chain,
					TokenID: tokenAddress,
				},
			})
			return map[domain.ChainToken]*domain.TokenMetadata{
				{
					Chain:   chain,
					TokenID: tokenAddress,
				}: {
					Decimals:    6,
					Symbol:      "USDC",
					CoingeckoID: domain.CoingeckoID("ethereum"), // For testing exchange rate
				},
			}, nil
		})
		tokenmeta.Init(mockTokenMetaRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

		mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
		createParams := payment.CreateIntentParams{
			Chain:       chain,
			PayToken:    payToken,
			ClientID:    clientID,
			Origin:      origin,
			Amount:      fiatAmount,
			Currency:    util.Ptr(fiatCurrency),
			PricingMode: payment.PricingModeFiat,
			OrderData:   map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(100)},
			CallbackURL: util.Ptr("http://localhost:12345"),
		}
		var createdPaymentIntent *payment.Intent
		var aggregationTxHash string
		mockCallbackExecutor.EXPECT().SendRequest(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, url string, body []byte, execCtx domain.CallbackExecutionContext) (*resty.Response, error) {
			assert.Equal(t, url, "http://localhost:12345")

			var callbackContent payment.CallbackPayload
			err := json.Unmarshal(body, &callbackContent)
			if err != nil {
				t.Fatalf("failed to unmarshal callback content: %v", err)
			}
			assert.Equal(t, payToken, callbackContent.Symbol)
			assert.Equal(t, clientID, callbackContent.ClientID)
			assert.Equal(t, fiatAmount, callbackContent.FiatAmount)
			assert.Equal(t, fiatCurrency, callbackContent.FiatCurrency)
			assert.Equal(t, chain.String(), callbackContent.PaymentChainID)
			assert.Equal(t, createParams.OrderData, callbackContent.OrderData)
			assert.Equal(t, "http://localhost:12345", callbackContent.CallbackURL)
			assert.Contains(t, []string{string(domain.PaymentIntentStatusPending), string(domain.PaymentIntentStatusSuccess)}, callbackContent.Status)
			if callbackContent.Status == string(domain.PaymentIntentStatusPending) {
				t.Logf("pending callbackContent: %+v", callbackContent)
				assert.Nil(t, callbackContent.PaymentTxHash, "PaymentTxHash should be nil for pending intent")
				assert.Nil(t, callbackContent.RefundTxHash, "RefundTxHash should be nil for pending intent")
				assert.Nil(t, callbackContent.ReceivedAmount, "ReceivedAmount should be nil for pending intent")
				assert.Nil(t, callbackContent.AggregatedAmount, "AggregatedAmount should be nil for pending intent")
				assert.Nil(t, callbackContent.RefundAmount, "RefundAmount should be nil for pending intent")
			} else if callbackContent.Status == string(domain.PaymentIntentStatusSuccess) {
				assert.Equal(t, createdPaymentIntent.ID, callbackContent.PaymentIntentID)
				assert.InDelta(t, createdPaymentIntent.PaymentDeadline, callbackContent.PaymentDeadline, 1)
				assert.Equal(t, createdPaymentIntent.CryptoAmount, callbackContent.CryptoAmount)
				assert.Equal(t, createdPaymentIntent.OrderData, callbackContent.OrderData)

				assert.NotNil(t, callbackContent.PaymentTxHash, "PaymentTxHash should not be nil for successful intent")

				assert.NotNil(t, callbackContent.ReceivedAmount, "ReceivedAmount should not be nil for successful intent")
				assert.NotNil(t, callbackContent.AggregatedAmount, "AggregatedAmount should not be nil for successful intent")

				assert.Nil(t, callbackContent.RefundTxHash, "RefundTxHash should be nil for successful intent")
				assert.Nil(t, callbackContent.RefundAmount, "RefundAmount should be nil for successful intent")
			}
			return &resty.Response{}, nil
		})
		uRepo := repo.Unified()
		payment.Init(uRepo, mockCallbackExecutor, nil)

		createdPaymentIntent, kgErr := payment.CreateIntent(ctx, createParams)
		if kgErr != nil {
			t.Fatalf("kgErr: %+v", kgErr)
		}
		assert.NotNil(t, createdPaymentIntent)
		assert.Equal(t, paymentAddress, createdPaymentIntent.PaymentAddress)
		assert.Equal(t, domain.PaymentIntentStatusPending.String(), createdPaymentIntent.Status)
		assert.Equal(t, payToken, createdPaymentIntent.Symbol)
		assert.Equal(t, uint(6), createdPaymentIntent.Decimals)

		privateKey := "afb24289e9e06060d6a291698c783404f3d92dafcc69de63297bd3a6ad35832b"
		privateKeyECDSA, err := crypto.HexToECDSA(privateKey)
		assert.NoError(t, err)
		payAmount, err := decimal.NewFromString(createdPaymentIntent.CryptoAmount)
		assert.NoError(t, err)

		payAmount = payAmount.Mul(decimal.NewFromInt(10)).Ceil().Div(decimal.NewFromInt(10))
		payAmount = payAmount.Mul(decimal.NewFromInt(1000000))

		paymentTxHash2 := evmtest.TransferToken(t, chain, privateKeyECDSA, domain.NewEvmAddress(paymentAddress), domain.NewEvmAddress(tokenAddress), payAmount)
		t.Logf("payment txHash: %s", paymentTxHash2)

		mockAlchemy.EXPECT().GetAssetTransfers(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, chainID string, params *alchemyapi.GetAssetTransfersParams) (*alchemyapi.GetAssetTransfersResp, *resty.Response, error) {
			if params.ToAddress == paymentAddress {
				value := payAmount.Div(decimal.NewFromInt(1000000)).InexactFloat64()
				return &alchemyapi.GetAssetTransfersResp{
					Result: struct {
						Transfers []alchemyapi.AssetTransfer `json:"transfers"`
						PageKey   string                     `json:"pageKey"`
					}{
						Transfers: []alchemyapi.AssetTransfer{
							{
								Hash:          paymentTxHash2,
								BlockNum:      "0x123456",
								From:          "******************************************",
								To:            paymentAddress,
								Value:         &value,
								Asset:         "USDC",
								Category:      "erc20",
								Erc721TokenID: nil,
								RawContract: struct {
									Value   *string `json:"value"`
									Address string  `json:"address"`
									Decimal *string `json:"decimal"`
								}{
									Address: tokenAddress,
								},
							},
						},
					},
				}, nil, nil
			}
			return &alchemyapi.GetAssetTransfersResp{
				Result: struct {
					Transfers []alchemyapi.AssetTransfer `json:"transfers"`
					PageKey   string                     `json:"pageKey"`
				}{
					Transfers: []alchemyapi.AssetTransfer{},
				},
			}, nil, nil
		})

		err = payment.UpdateIntentByTxHash(ctx, chain, paymentTxHash2)
		if err != nil {
			t.Fatalf("failed to update intent by txHash: %v", err)
		}

		intent2, err := rdb.GormRepo().GetPaymentIntentByID(ctx, createdPaymentIntent.ID)
		if err != nil {
			t.Fatalf("failed to get intent by id: %v", err)
		}
		t.Logf("intent2: %+v", intent2)
		assert.Equal(t, domain.PaymentIntentStatusSuccess, intent2.Status)
		assert.NotNil(t, intent2.PaymentTxHash)
		assert.Equal(t, paymentTxHash2, *intent2.PaymentTxHash)
		assert.NotNil(t, intent2.AggregationTxHash)
		aggregationTxHash = *intent2.AggregationTxHash

		assert.NotNil(t, intent2.ReceivedCryptoAmount, "ReceivedCryptoAmount should not be nil")
		assert.NotNil(t, intent2.AggregatedCryptoAmount, "AggregatedCryptoAmount should not be nil")
		assert.Nil(t, intent2.RefundCryptoAmount, "RefundCryptoAmount should be nil for successful intent")
		assert.NotNil(t, intent2.KGFeeAmount, "KGFeeAmount should not be nil")

		assert.NotNil(t, intent2.PayerAddress, "PayerAddress should not be nil")
		assert.Equal(t, "******************************************", intent2.PayerAddress.String())
		assert.NotNil(t, intent2.PaymentTxTimestamp, "PaymentTxTimestamp should not be nil")

		receivedAmount, err := decimal.NewFromString(createdPaymentIntent.CryptoAmount)
		assert.NoError(t, err)
		receivedAmount = receivedAmount.Mul(decimal.NewFromInt(10)).Ceil().Div(decimal.NewFromInt(10))

		expectedFeeAmount := receivedAmount.Div(decimal.NewFromInt(100))
		expectedAggregatedAmount := receivedAmount.Sub(expectedFeeAmount)

		assert.True(t, intent2.ReceivedCryptoAmount.Equal(receivedAmount) ||
			intent2.ReceivedCryptoAmount.Sub(receivedAmount).Abs().LessThan(decimal.NewFromFloat(0.0001)),
			"Received amount should match expected: %s vs %s", intent2.ReceivedCryptoAmount, receivedAmount)

		assert.True(t, intent2.AggregatedCryptoAmount.Equal(expectedAggregatedAmount) ||
			intent2.AggregatedCryptoAmount.Sub(expectedAggregatedAmount).Abs().LessThan(decimal.NewFromFloat(0.0001)),
			"Aggregated amount should be ~99% of received: %s vs %s", intent2.AggregatedCryptoAmount, expectedAggregatedAmount)

		assert.True(t, intent2.KGFeeAmount.Equal(expectedFeeAmount) ||
			intent2.KGFeeAmount.Sub(expectedFeeAmount).Abs().LessThan(decimal.NewFromFloat(0.0001)),
			"Fee amount should be ~1% of received: %s vs %s", intent2.KGFeeAmount, expectedFeeAmount)

		t.Logf("aggregationTxHash: %s", aggregationTxHash)
		client := evm.GetClient(chain)
		status, err := client.WaitUntilTransactionConfirmed(ctx, aggregationTxHash)
		assert.NoError(t, err)
		assert.Equal(t, domain.TransactionStatusSuccess, status)

		receipt, err := client.GetRawClient().TransactionReceipt(ctx, common.HexToHash(aggregationTxHash))
		assert.NoError(t, err, "Failed to get transaction receipt")

		assert.Equal(t, uint64(1), receipt.Status, "Transaction should be successful")

		assert.Greater(t, len(receipt.Logs), 0, "Transaction should have logs (transfers)")

		foundKGTransfer := false
		foundOrgTransfer := false

		kgAddr, kgErr := repo.Unified().GetWalletsByOrganizationId(ctx, 1)
		assert.Nil(t, kgErr, "Failed to get KG wallets")
		kgAddress := domain.NewEvmAddress(kgAddr.EvmAddress)

		orgID, kgErr := application.GetApplicationOrgId(ctx, clientID)
		assert.Nil(t, kgErr, "Failed to get application org ID")
		orgAddr, kgErr := repo.Unified().GetWalletsByOrganizationId(ctx, orgID)
		assert.Nil(t, kgErr, "Failed to get organization wallets")
		orgAddress := domain.NewEvmAddress(orgAddr.EvmAddress)

		for _, log := range receipt.Logs {
			if len(log.Topics) != 3 {
				continue
			}

			if log.Topics[0] == common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef") {
				to := common.BytesToAddress(log.Topics[2].Bytes())

				switch to {
				case kgAddress.Address:
					foundKGTransfer = true
					t.Logf("Found transfer to KG address: %s", to.Hex())
				case orgAddress.Address:
					foundOrgTransfer = true
					t.Logf("Found transfer to organization address: %s", to.Hex())
				}
			}
		}

		assert.True(t, foundKGTransfer, "Should find a transfer to KG address %s", kgAddress.String())
		assert.True(t, foundOrgTransfer, "Should find a transfer to organization address %s", orgAddress.String())
	})

	t.Run("Payment intent insufficient (without refund yet) - Holesky", func(t *testing.T) {
		chain := domain.Holesky
		signingservertest.Setup(t)
		coingeckoapi.InitDefault(domain.NewAllPassRateLimiter())

		mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
		var paymentAddress string
		mockAlchemy.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
			paymentAddress = addresses[0]
			return nil
		})
		mockAlchemy.EXPECT().RemoveSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
			assert.Equal(t, addresses, []string{paymentAddress})
			return nil
		})
		alchemyapi.Set(mockAlchemy)

		user.Init(repo.Unified())
		mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
		mockTokenMetaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			assert.Equal(t, tokens, []domain.ChainToken{
				{
					Chain:   chain,
					TokenID: tokenAddress,
				},
			})
			return map[domain.ChainToken]*domain.TokenMetadata{
				{
					Chain:   chain,
					TokenID: tokenAddress,
				}: {
					Decimals:    6,
					Symbol:      "USDC",
					CoingeckoID: domain.CoingeckoID("ethereum"), // For testing exchange rate
				},
			}, nil
		})
		tokenmeta.Init(mockTokenMetaRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

		mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
		createParams := payment.CreateIntentParams{
			Chain:       chain,
			PayToken:    payToken,
			ClientID:    clientID,
			Origin:      origin,
			Amount:      fiatAmount,
			Currency:    util.Ptr(fiatCurrency),
			PricingMode: payment.PricingModeFiat,
			OrderData:   map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(100)},
			CallbackURL: util.Ptr("http://localhost:12345"),
		}
		var createdPaymentIntent *payment.Intent
		var aggregationTxHash string
		mockCallbackExecutor.EXPECT().SendRequest(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, url string, body []byte, execCtx domain.CallbackExecutionContext) (*resty.Response, error) {
			assert.Equal(t, url, "http://localhost:12345")

			var callbackContent payment.CallbackPayload
			err := json.Unmarshal(body, &callbackContent)
			if err != nil {
				t.Fatalf("failed to unmarshal callback content: %v", err)
			}
			assert.Equal(t, payToken, callbackContent.Symbol)
			assert.Equal(t, clientID, callbackContent.ClientID)
			assert.Equal(t, fiatAmount, callbackContent.FiatAmount)
			assert.Equal(t, fiatCurrency, callbackContent.FiatCurrency)
			assert.Equal(t, chain.String(), callbackContent.PaymentChainID)
			assert.Equal(t, createParams.OrderData, callbackContent.OrderData)
			assert.Equal(t, "http://localhost:12345", callbackContent.CallbackURL)
			assert.Contains(t, []string{
				string(domain.PaymentIntentStatusPending),
				string(domain.PaymentIntentStatusInsufficientNotRefunded),
			}, callbackContent.Status)
			if callbackContent.Status == string(domain.PaymentIntentStatusPending) {
				assert.Nil(t, callbackContent.PaymentTxHash, "PaymentTxHash should be nil for pending intent")
				assert.Nil(t, callbackContent.RefundTxHash, "RefundTxHash should be nil for pending intent")
				assert.Nil(t, callbackContent.ReceivedAmount, "ReceivedAmount should be nil for pending intent")
				assert.Nil(t, callbackContent.AggregatedAmount, "AggregatedAmount should be nil for pending intent")
				assert.Nil(t, callbackContent.RefundAmount, "RefundAmount should be nil for pending intent")
			} else if callbackContent.Status == string(domain.PaymentIntentStatusInsufficientNotRefunded) {
				assert.Equal(t, createdPaymentIntent.ID, callbackContent.PaymentIntentID)
				assert.InDelta(t, createdPaymentIntent.PaymentDeadline, callbackContent.PaymentDeadline, 1)
				assert.Equal(t, createdPaymentIntent.CryptoAmount, callbackContent.CryptoAmount)
				assert.Equal(t, createdPaymentIntent.OrderData, callbackContent.OrderData)

				assert.NotNil(t, callbackContent.PaymentTxHash, "PaymentTxHash should not be nil for insufficient intent")

				assert.NotNil(t, callbackContent.ReceivedAmount, "ReceivedAmount should not be nil for insufficient intent")
				assert.NotNil(t, callbackContent.AggregatedAmount, "AggregatedAmount should not be nil for insufficient intent")

				assert.Nil(t, callbackContent.RefundTxHash, "RefundTxHash should be nil for insufficient_not_refunded intent")
				assert.Nil(t, callbackContent.RefundAmount, "RefundAmount should be nil for insufficient_not_refunded intent")
			}

			return &resty.Response{}, nil
		})
		uRepo := repo.Unified()
		payment.Init(uRepo, mockCallbackExecutor, nil)

		createdPaymentIntent, kgErr := payment.CreateIntent(ctx, createParams)
		if kgErr != nil {
			t.Fatalf("kgErr: %+v", kgErr)
		}
		assert.NotNil(t, createdPaymentIntent)
		assert.Equal(t, paymentAddress, createdPaymentIntent.PaymentAddress)
		assert.Equal(t, domain.PaymentIntentStatusPending.String(), createdPaymentIntent.Status)
		assert.Equal(t, payToken, createdPaymentIntent.Symbol)
		assert.Equal(t, uint(6), createdPaymentIntent.Decimals)

		privateKey := "afb24289e9e06060d6a291698c783404f3d92dafcc69de63297bd3a6ad35832b"
		privateKeyECDSA, err := crypto.HexToECDSA(privateKey)
		assert.NoError(t, err)
		payAmount, err := decimal.NewFromString(createdPaymentIntent.CryptoAmount)
		assert.NoError(t, err)
		payAmount = payAmount.Div(decimal.NewFromInt(2))
		payAmount = payAmount.Mul(decimal.NewFromInt(1000000))

		paymentTxHash := evmtest.TransferToken(t, chain, privateKeyECDSA, domain.NewEvmAddress(paymentAddress), domain.NewEvmAddress(tokenAddress), payAmount)
		t.Logf("payment txHash: %s", paymentTxHash)

		mockAlchemy.EXPECT().GetAssetTransfers(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, chainID string, params *alchemyapi.GetAssetTransfersParams) (*alchemyapi.GetAssetTransfersResp, *resty.Response, error) {
			if params.ToAddress == paymentAddress {
				value := payAmount.Div(decimal.NewFromInt(1000000)).InexactFloat64()
				return &alchemyapi.GetAssetTransfersResp{
					Result: struct {
						Transfers []alchemyapi.AssetTransfer `json:"transfers"`
						PageKey   string                     `json:"pageKey"`
					}{
						Transfers: []alchemyapi.AssetTransfer{
							{
								Hash:          paymentTxHash,
								BlockNum:      "0x123456",
								From:          "******************************************",
								To:            paymentAddress,
								Value:         &value,
								Asset:         "USDC",
								Category:      "erc20",
								Erc721TokenID: nil,
								RawContract: struct {
									Value   *string `json:"value"`
									Address string  `json:"address"`
									Decimal *string `json:"decimal"`
								}{
									Address: tokenAddress,
								},
							},
						},
					},
				}, nil, nil
			}
			return &alchemyapi.GetAssetTransfersResp{
				Result: struct {
					Transfers []alchemyapi.AssetTransfer `json:"transfers"`
					PageKey   string                     `json:"pageKey"`
				}{
					Transfers: []alchemyapi.AssetTransfer{},
				},
			}, nil, nil
		})

		err = payment.UpdateIntentByTxHash(ctx, chain, paymentTxHash)
		if err != nil {
			t.Fatalf("failed to update intent by txHash: %v", err)
		}

		intent2, err := rdb.GormRepo().GetPaymentIntentByID(ctx, createdPaymentIntent.ID)
		if err != nil {
			t.Fatalf("failed to get intent by id: %v", err)
		}
		t.Logf("intent2: %+v", intent2)
		assert.Equal(t, domain.PaymentIntentStatusInsufficientNotRefunded, intent2.Status)
		assert.NotNil(t, intent2.PaymentTxHash)
		assert.Equal(t, paymentTxHash, *intent2.PaymentTxHash)
		assert.NotNil(t, intent2.AggregationTxHash)
		aggregationTxHash = *intent2.AggregationTxHash
		assert.NotNil(t, intent2.ReceivedCryptoAmount)
		assert.NotNil(t, intent2.AggregatedCryptoAmount)
		assert.NotNil(t, intent2.KGFeeAmount)
		assert.Nil(t, intent2.RefundCryptoAmount)
		assert.Nil(t, intent2.RefundTxHash)

		assert.NotNil(t, intent2.PayerAddress, "PayerAddress should not be nil")
		assert.Equal(t, "******************************************", intent2.PayerAddress.String())
		assert.NotNil(t, intent2.PaymentTxTimestamp, "PaymentTxTimestamp should not be nil")

		receivedAmount := payAmount.Div(decimal.NewFromInt(1000000))

		expectedFeeAmount := receivedAmount.Div(decimal.NewFromInt(100))
		expectedAggregatedAmount := receivedAmount.Sub(expectedFeeAmount)

		assert.True(t, intent2.ReceivedCryptoAmount.Equal(receivedAmount) ||
			intent2.ReceivedCryptoAmount.Sub(receivedAmount).Abs().LessThan(decimal.NewFromFloat(0.0001)),
			"Received amount should match expected: %s vs %s", intent2.ReceivedCryptoAmount, receivedAmount)

		assert.True(t, intent2.KGFeeAmount.Equal(expectedFeeAmount) ||
			intent2.KGFeeAmount.Sub(expectedFeeAmount).Abs().LessThan(decimal.NewFromFloat(0.0001)),
			"Fee amount should be ~1% of received: %s vs %s", intent2.KGFeeAmount, expectedFeeAmount)

		assert.True(t, intent2.AggregatedCryptoAmount.Equal(expectedAggregatedAmount) ||
			intent2.AggregatedCryptoAmount.Sub(expectedAggregatedAmount).Abs().LessThan(decimal.NewFromFloat(0.0001)),
			"Aggregated amount should be ~99% of received: %s vs %s", intent2.AggregatedCryptoAmount, expectedAggregatedAmount)

		t.Logf("aggregationTxHash: %s", aggregationTxHash)
		client := evm.GetClient(chain)
		status, err := client.WaitUntilTransactionConfirmed(ctx, aggregationTxHash)
		assert.NoError(t, err)
		assert.Equal(t, domain.TransactionStatusSuccess, status)

		receipt, err := client.GetRawClient().TransactionReceipt(ctx, common.HexToHash(aggregationTxHash))
		assert.NoError(t, err, "Failed to get transaction receipt")

		assert.Equal(t, uint64(1), receipt.Status, "Transaction should be successful")

		assert.Greater(t, len(receipt.Logs), 0, "Transaction should have logs (transfers)")

		foundKGTransfer := false
		foundOrgTransfer := false

		kgAddr, kgErr := repo.Unified().GetWalletsByOrganizationId(ctx, 1)
		assert.Nil(t, kgErr, "Failed to get KG wallets")
		kgAddress := domain.NewEvmAddress(kgAddr.EvmAddress)

		orgID, kgErr := application.GetApplicationOrgId(ctx, clientID)
		assert.Nil(t, kgErr, "Failed to get application org ID")
		orgAddr, kgErr := repo.Unified().GetWalletsByOrganizationId(ctx, orgID)
		assert.Nil(t, kgErr, "Failed to get organization wallets")
		orgAddress := domain.NewEvmAddress(orgAddr.EvmAddress)

		for _, log := range receipt.Logs {
			if len(log.Topics) != 3 {
				continue
			}

			if log.Topics[0] == common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef") {
				to := common.BytesToAddress(log.Topics[2].Bytes())

				switch to {
				case kgAddress.Address:
					foundKGTransfer = true
					t.Logf("Found transfer to KG address: %s", to.Hex())
				case orgAddress.Address:
					foundOrgTransfer = true
					t.Logf("Found transfer to organization address: %s", to.Hex())
				}
			}
		}

		assert.True(t, foundKGTransfer, "Should find a transfer to KG address %s", kgAddress.String())
		assert.True(t, foundOrgTransfer, "Should find a transfer to organization address %s", orgAddress.String())

		secondPayAmount, err := decimal.NewFromString(createdPaymentIntent.CryptoAmount)
		assert.NoError(t, err)
		secondPayAmount = secondPayAmount.Mul(decimal.NewFromInt(2)).Mul(decimal.NewFromInt(1000000))
		secondPaymentTxHash := evmtest.TransferToken(t, chain, privateKeyECDSA, domain.NewEvmAddress(paymentAddress), domain.NewEvmAddress(tokenAddress), secondPayAmount)
		t.Logf("second payment txHash: %s", secondPaymentTxHash)

		err = payment.UpdateIntentByTxHash(ctx, chain, secondPaymentTxHash)
		if err != nil {
			t.Fatalf("failed to update intent by second txHash: %v", err)
		}

		intent3, err := rdb.GormRepo().GetPaymentIntentByID(ctx, createdPaymentIntent.ID)
		if err != nil {
			t.Fatalf("failed to get intent by id: %v", err)
		}

		assert.Equal(t, domain.PaymentIntentStatusInsufficientNotRefunded, intent3.Status)
		assert.Equal(t, paymentTxHash, *intent3.PaymentTxHash)
	})

	t.Run("Payment intent success with default imported address - Holesky", func(t *testing.T) {
		chain := domain.Holesky
		signingservertest.Setup(t)
		coingeckoapi.InitDefault(domain.NewAllPassRateLimiter())

		orgID, kgErrApp := application.GetApplicationOrgId(ctx, clientID)
		assert.Nil(t, kgErrApp, "Failed to get application org ID")
		assert.NotEqual(t, 1, orgID, "Test should not use KG org (ID 1) to properly test default address logic")

		defaultImportedAddressStr := "0x1111111111111111111111111111111111111111"
		defaultImportedAddr := domain.NewEvmAddress(defaultImportedAddressStr)
		db := rdb.Get()
		importedAddrRecord := model.StudioOrganizationImportedAddress{
			OrganizationID:        orgID,
			Chain:                 chain.ID(),
			Address:               defaultImportedAddressStr,
			DefaultReceiveAddress: true,
			AddedByUserID:         "e2e-test",
			AddedAt:               time.Now(),
		}
		err := db.Create(&importedAddrRecord).Error
		assert.NoError(t, err, "Failed to insert default imported address")

		mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
		var paymentAddress string
		mockAlchemy.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
			paymentAddress = addresses[0]
			return nil
		})
		mockAlchemy.EXPECT().RemoveSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
			assert.Equal(t, addresses, []string{paymentAddress})
			return nil
		})
		alchemyapi.Set(mockAlchemy)

		user.Init(repo.Unified())
		mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
		mockTokenMetaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			assert.Equal(t, tokens, []domain.ChainToken{
				{
					Chain:   chain,
					TokenID: tokenAddress,
				},
			})
			return map[domain.ChainToken]*domain.TokenMetadata{
				{
					Chain:   chain,
					TokenID: tokenAddress,
				}: {
					Decimals:    6,
					Symbol:      "USDC",
					CoingeckoID: domain.CoingeckoID("ethereum"),
				},
			}, nil
		})
		tokenmeta.Init(mockTokenMetaRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

		mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
		createParams := payment.CreateIntentParams{
			Chain:       chain,
			PayToken:    payToken,
			ClientID:    clientID,
			Origin:      origin,
			Amount:      fiatAmount,
			Currency:    util.Ptr(fiatCurrency),
			PricingMode: payment.PricingModeFiat,
			OrderData:   map[string]any{"order_id": "e2e_default_addr", "customer_id": "cust_default_addr"},
			CallbackURL: util.Ptr("http://localhost:12345"),
		}
		var createdPaymentIntent *payment.Intent
		mockCallbackExecutor.EXPECT().SendRequest(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, url string, body []byte, execCtx domain.CallbackExecutionContext) (*resty.Response, error) {
			var callbackContent payment.CallbackPayload
			errJson := json.Unmarshal(body, &callbackContent)
			assert.NoError(t, errJson)
			assert.Equal(t, clientID, callbackContent.ClientID)
			return &resty.Response{}, nil
		})
		uRepo := repo.Unified()
		payment.Init(uRepo, mockCallbackExecutor, nil)

		createdPaymentIntent, kgErr := payment.CreateIntent(ctx, createParams)
		if kgErr != nil {
			t.Fatalf("kgErr: %+v", kgErr)
		}
		assert.NotNil(t, createdPaymentIntent)
		assert.Equal(t, paymentAddress, createdPaymentIntent.PaymentAddress)

		privateKey := "afb24289e9e06060d6a291698c783404f3d92dafcc69de63297bd3a6ad35832b"
		privateKeyECDSA, err := crypto.HexToECDSA(privateKey)
		assert.NoError(t, err)
		payAmountDec, err := decimal.NewFromString(createdPaymentIntent.CryptoAmount)
		assert.NoError(t, err)
		payAmountDec = payAmountDec.Mul(decimal.NewFromInt(10)).Ceil().Div(decimal.NewFromInt(10))
		payAmountDec = payAmountDec.Mul(decimal.NewFromInt(1000000))

		paymentTxHash := evmtest.TransferToken(t, chain, privateKeyECDSA, domain.NewEvmAddress(paymentAddress), domain.NewEvmAddress(tokenAddress), payAmountDec)
		t.Logf("payment txHash for default addr test: %s", paymentTxHash)

		mockAlchemy.EXPECT().GetAssetTransfers(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, chainID string, params *alchemyapi.GetAssetTransfersParams) (*alchemyapi.GetAssetTransfersResp, *resty.Response, error) {
			if params.ToAddress == paymentAddress {
				value := payAmountDec.Div(decimal.NewFromInt(1000000)).InexactFloat64()
				return &alchemyapi.GetAssetTransfersResp{
					Result: struct {
						Transfers []alchemyapi.AssetTransfer `json:"transfers"`
						PageKey   string                     `json:"pageKey"`
					}{
						Transfers: []alchemyapi.AssetTransfer{
							{
								Hash:     paymentTxHash,
								BlockNum: "0xabcdef",
								From:     "******************************************",
								To:       paymentAddress,
								Value:    &value,
								Asset:    "USDC",
								Category: "erc20",
								RawContract: struct {
									Value   *string `json:"value"`
									Address string  `json:"address"`
									Decimal *string `json:"decimal"`
								}{
									Address: tokenAddress,
								},
							},
						},
					},
				}, nil, nil
			}
			return &alchemyapi.GetAssetTransfersResp{Result: struct {
				Transfers []alchemyapi.AssetTransfer `json:"transfers"`
				PageKey   string                     `json:"pageKey"`
			}{}}, nil, nil
		})

		err = payment.UpdateIntentByTxHash(ctx, chain, paymentTxHash)
		assert.NoError(t, err, "Failed to update intent by txHash")

		intentAfterUpdate, err := rdb.GormRepo().GetPaymentIntentByID(ctx, createdPaymentIntent.ID)
		assert.NoError(t, err, "Failed to get intent by ID after update")
		assert.Equal(t, domain.PaymentIntentStatusSuccess, intentAfterUpdate.Status)
		assert.NotNil(t, intentAfterUpdate.AggregationTxHash, "AggregationTxHash should be set")
		aggregationTxHash := *intentAfterUpdate.AggregationTxHash
		t.Logf("aggregationTxHash for default addr test: %s", aggregationTxHash)

		client := evm.GetClient(chain)
		status, err := client.WaitUntilTransactionConfirmed(ctx, aggregationTxHash)
		assert.NoError(t, err)
		assert.Equal(t, domain.TransactionStatusSuccess, status)

		receipt, err := client.GetRawClient().TransactionReceipt(ctx, common.HexToHash(aggregationTxHash))
		assert.NoError(t, err, "Failed to get transaction receipt")
		assert.Equal(t, uint64(1), receipt.Status, "Aggregation transaction should be successful")

		foundKGTransfer := false
		foundDefaultAddrTransfer := false

		kgWallets, kgErrKg := repo.Unified().GetWalletsByOrganizationId(ctx, 1)
		assert.Nil(t, kgErrKg)
		kgAddress := domain.NewEvmAddress(kgWallets.EvmAddress)

		for _, logEntry := range receipt.Logs {
			if len(logEntry.Topics) == 3 && logEntry.Topics[0] == common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef") {
				toAddress := common.BytesToAddress(logEntry.Topics[2].Bytes())
				if toAddress == kgAddress.Address {
					foundKGTransfer = true
					t.Logf("Found transfer to KG address: %s", toAddress.Hex())
				} else if toAddress == defaultImportedAddr.Address {
					foundDefaultAddrTransfer = true
					t.Logf("Found transfer to default imported address: %s", toAddress.Hex())
				}
			}
		}

		assert.True(t, foundKGTransfer, "Should find a transfer to KG address %s", kgAddress.String())
		assert.True(t, foundDefaultAddrTransfer, "Should find a transfer to the default imported address %s", defaultImportedAddr.String())

		orgWallets, kgErrOrg := repo.Unified().GetWalletsByOrganizationId(ctx, orgID)
		assert.Nil(t, kgErrOrg)
		orgMainAddress := domain.NewEvmAddress(orgWallets.EvmAddress)
		if orgMainAddress.String() != defaultImportedAddr.String() {
			foundOrgMainWalletTransfer := false
			for _, logEntry := range receipt.Logs {
				if len(logEntry.Topics) == 3 && logEntry.Topics[0] == common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef") {
					toAddress := common.BytesToAddress(logEntry.Topics[2].Bytes())
					if toAddress == orgMainAddress.Address {
						foundOrgMainWalletTransfer = true
						break
					}
				}
			}
			assert.False(t, foundOrgMainWalletTransfer, "Should NOT find a transfer to the organization's main wallet %s when a default is set", orgMainAddress.String())
		}
	})

	t.Run("Payment intent success with custom payout target address - Holesky", func(t *testing.T) {
		chain := domain.Holesky
		signingservertest.Setup(t)
		coingeckoapi.InitDefault(domain.NewAllPassRateLimiter())

		customPayoutAddress := "******************************************"

		mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
		var paymentAddress string
		mockAlchemy.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
			paymentAddress = addresses[0]
			return nil
		})
		mockAlchemy.EXPECT().RemoveSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
			assert.Equal(t, addresses, []string{paymentAddress})
			return nil
		})
		alchemyapi.Set(mockAlchemy)

		user.Init(repo.Unified())
		mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
		mockTokenMetaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			assert.Equal(t, tokens, []domain.ChainToken{
				{
					Chain:   chain,
					TokenID: tokenAddress,
				},
			})
			return map[domain.ChainToken]*domain.TokenMetadata{
				{
					Chain:   chain,
					TokenID: tokenAddress,
				}: {
					Decimals:    6,
					Symbol:      "USDC",
					CoingeckoID: domain.CoingeckoID("ethereum"),
				},
			}, nil
		})
		tokenmeta.Init(mockTokenMetaRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

		mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
		createParams := payment.CreateIntentParams{
			Chain:               chain,
			PayToken:            payToken,
			ClientID:            clientID,
			Origin:              origin,
			Amount:              fiatAmount,
			Currency:            util.Ptr(fiatCurrency),
			PricingMode:         payment.PricingModeFiat,
			OrderData:           map[string]any{"order_id": "e2e_custom_payout", "customer_id": "cust_custom_payout"},
			CallbackURL:         util.Ptr("http://localhost:12345"),
			PayoutTargetAddress: util.Ptr(customPayoutAddress),
		}
		var createdPaymentIntent *payment.Intent
		mockCallbackExecutor.EXPECT().SendRequest(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, url string, body []byte, execCtx domain.CallbackExecutionContext) (*resty.Response, error) {
			var callbackContent payment.CallbackPayload
			errJson := json.Unmarshal(body, &callbackContent)
			assert.NoError(t, errJson)
			assert.Equal(t, clientID, callbackContent.ClientID)
			return &resty.Response{}, nil
		})
		uRepo := repo.Unified()
		payment.Init(uRepo, mockCallbackExecutor, nil)

		createdPaymentIntent, kgErr := payment.CreateIntent(ctx, createParams)
		if kgErr != nil {
			t.Fatalf("kgErr: %+v", kgErr)
		}
		assert.NotNil(t, createdPaymentIntent)
		assert.Equal(t, paymentAddress, createdPaymentIntent.PaymentAddress)

		privateKey := "afb24289e9e06060d6a291698c783404f3d92dafcc69de63297bd3a6ad35832b"
		privateKeyECDSA, err := crypto.HexToECDSA(privateKey)
		assert.NoError(t, err)
		payAmountDec, err := decimal.NewFromString(createdPaymentIntent.CryptoAmount)
		assert.NoError(t, err)
		payAmountDec = payAmountDec.Mul(decimal.NewFromInt(10)).Ceil().Div(decimal.NewFromInt(10))
		payAmountDec = payAmountDec.Mul(decimal.NewFromInt(1000000))

		paymentTxHash := evmtest.TransferToken(t, chain, privateKeyECDSA, domain.NewEvmAddress(paymentAddress), domain.NewEvmAddress(tokenAddress), payAmountDec)
		t.Logf("payment txHash for custom payout target test: %s", paymentTxHash)

		mockAlchemy.EXPECT().GetAssetTransfers(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, chainID string, params *alchemyapi.GetAssetTransfersParams) (*alchemyapi.GetAssetTransfersResp, *resty.Response, error) {
			if params.ToAddress == paymentAddress {
				value := payAmountDec.Div(decimal.NewFromInt(1000000)).InexactFloat64()
				return &alchemyapi.GetAssetTransfersResp{
					Result: struct {
						Transfers []alchemyapi.AssetTransfer `json:"transfers"`
						PageKey   string                     `json:"pageKey"`
					}{
						Transfers: []alchemyapi.AssetTransfer{
							{
								Hash:     paymentTxHash,
								BlockNum: "0xabcdef",
								From:     "******************************************",
								To:       paymentAddress,
								Value:    &value,
								Asset:    "USDC",
								Category: "erc20",
								RawContract: struct {
									Value   *string `json:"value"`
									Address string  `json:"address"`
									Decimal *string `json:"decimal"`
								}{
									Address: tokenAddress,
								},
							},
						},
					},
				}, nil, nil
			}
			return &alchemyapi.GetAssetTransfersResp{Result: struct {
				Transfers []alchemyapi.AssetTransfer `json:"transfers"`
				PageKey   string                     `json:"pageKey"`
			}{}}, nil, nil
		})

		err = payment.UpdateIntentByTxHash(ctx, chain, paymentTxHash)
		assert.NoError(t, err, "Failed to update intent by txHash")

		intentAfterUpdate, err := rdb.GormRepo().GetPaymentIntentByID(ctx, createdPaymentIntent.ID)
		assert.NoError(t, err, "Failed to get intent by ID after update")
		assert.Equal(t, domain.PaymentIntentStatusSuccess, intentAfterUpdate.Status)
		assert.NotNil(t, intentAfterUpdate.AggregationTxHash, "AggregationTxHash should be set")
		aggregationTxHash := *intentAfterUpdate.AggregationTxHash
		t.Logf("aggregationTxHash for custom payout target test: %s", aggregationTxHash)

		client := evm.GetClient(chain)
		status, err := client.WaitUntilTransactionConfirmed(ctx, aggregationTxHash)
		assert.NoError(t, err)
		assert.Equal(t, domain.TransactionStatusSuccess, status)

		receipt, err := client.GetRawClient().TransactionReceipt(ctx, common.HexToHash(aggregationTxHash))
		assert.NoError(t, err, "Failed to get transaction receipt")
		assert.Equal(t, uint64(1), receipt.Status, "Aggregation transaction should be successful")

		foundKGTransfer := false
		foundCustomPayoutTransfer := false

		kgWallets, kgErrKg := repo.Unified().GetWalletsByOrganizationId(ctx, 1)
		assert.Nil(t, kgErrKg)
		kgAddress := domain.NewEvmAddress(kgWallets.EvmAddress)
		customPayoutAddr := domain.NewEvmAddress(customPayoutAddress)

		for _, logEntry := range receipt.Logs {
			if len(logEntry.Topics) == 3 && logEntry.Topics[0] == common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef") {
				toAddress := common.BytesToAddress(logEntry.Topics[2].Bytes())
				if toAddress == kgAddress.Address {
					foundKGTransfer = true
					t.Logf("Found transfer to KG address: %s", toAddress.Hex())
				} else if toAddress == customPayoutAddr.Address {
					foundCustomPayoutTransfer = true
					t.Logf("Found transfer to custom payout target address: %s", toAddress.Hex())
				}
			}
		}

		assert.True(t, foundKGTransfer, "Should find a transfer to KG address %s", kgAddress.String())
		assert.True(t, foundCustomPayoutTransfer, "Should find a transfer to the custom payout target address %s", customPayoutAddr.String())

		// Verify that org main wallet and default imported addresses do NOT receive transfers when custom payout target is set
		orgID, kgErrApp := application.GetApplicationOrgId(ctx, clientID)
		assert.Nil(t, kgErrApp, "Failed to get application org ID")
		orgWallets, kgErrOrg := repo.Unified().GetWalletsByOrganizationId(ctx, orgID)
		assert.Nil(t, kgErrOrg)
		orgMainAddress := domain.NewEvmAddress(orgWallets.EvmAddress)

		foundOrgMainWalletTransfer := false
		for _, logEntry := range receipt.Logs {
			if len(logEntry.Topics) == 3 && logEntry.Topics[0] == common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef") {
				toAddress := common.BytesToAddress(logEntry.Topics[2].Bytes())
				if toAddress == orgMainAddress.Address {
					foundOrgMainWalletTransfer = true
					break
				}
			}
		}
		assert.False(t, foundOrgMainWalletTransfer, "Should NOT find a transfer to the organization's main wallet %s when custom payout target is set", orgMainAddress.String())
	})
}
