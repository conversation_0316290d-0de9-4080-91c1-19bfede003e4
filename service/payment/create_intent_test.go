package payment

import (
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/big"
	"strings"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	coingeckoapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/coingecko-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/erc4337"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCreateIntent(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	chain := domain.Sepolia
	payToken := "USDC"
	clientID := "client123"
	origin := "https://www.google.com"
	fiatAmount := "150"
	fiatCurrency := "USD"
	fiatCurrencyLower := strings.ToLower(fiatCurrency)
	tokenAddress, err := getTokenAddress(chain, payToken)
	assert.NoError(t, err)
	scenarioID := "test-scenario"

	mockAppRepo := domain.NewMockApplicationRepo(ctrl)
	mockAppRepo.EXPECT().GetApplicationOrgId(gomock.Any(), clientID).Return(1, nil)
	mockAppRepo.EXPECT().GetApplication(gomock.Any(), clientID).Return(&domain.Application{
		ClientID:     clientID,
		ClientSecret: "test-secret",
		Name:         "Test App",
		Domain:       "test.com",
	}, nil)
	application.Init(mockAppRepo)

	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockTokenMetaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).Return(map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: chain, TokenID: tokenAddress}: {CoingeckoID: domain.CoingeckoID("usdt-cid"), Decimals: 6},
	}, nil)
	tokenmeta.Init(mockTokenMetaRepo, nil, nil)

	mockCoingecko := coingeckoapi.NewMockICoingecko(ctrl)
	mockCoingecko.EXPECT().SimplePrice(gomock.Any(), []domain.CoingeckoID{"usdt-cid"}, fiatCurrencyLower).Return(map[domain.CoingeckoID]float64{
		"usdt-cid": 0.99,
	}, nil)
	mockCoingecko.EXPECT().IsCurrencySupported(gomock.Any(), fiatCurrencyLower).AnyTimes().Return(true, nil)
	coingeckoapi.Set(mockCoingecko)

	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).AnyTimes().Return(&domain.OrganizationWallets{
		EvmAddress: "******************************************",
	}, nil)
	var paymentAddress domain.Address
	mockRepo.EXPECT().CreatePaymentIntent(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, intent *domain.PaymentIntent) (*domain.PaymentIntent, error) {
		paymentAddress = intent.PaymentAddress
		t.Logf("intent: %+v", intent)
		assert.Equal(t, chain, intent.PaymentChain)
		assert.Equal(t, "******************************************", intent.TokenAddress)
		assert.Equal(t, payToken, intent.Symbol)
		assert.Equal(t, uint(6), intent.Decimals)
		assert.Equal(t, "151.5151515151515152", intent.CryptoAmount.String())
		assert.Equal(t, fiatAmount, intent.FiatAmount.String())
		assert.NotNil(t, intent.CryptoPrice)
		assert.Equal(t, "0.99", intent.CryptoPrice.String(), "CryptoPrice should be the direct rate from Coingecko")
		if fiatCurrency != "" {
			assert.NotNil(t, intent.FiatCurrency)
			assert.Equal(t, fiatCurrency, *intent.FiatCurrency)
		}
		assert.Equal(t, PricingModeFiat.String(), intent.PricingMode)
		assert.Equal(t, domain.PaymentIntentStatusPending, intent.Status)
		assert.Equal(t, "1234567890", intent.OrderData["order_id"])
		assert.Equal(t, "0123456789", intent.OrderData["customer_id"])
		assert.Equal(t, float64(200), intent.OrderData["amount_in_game"])
		assert.Equal(t, "http://test.com", *intent.CallbackURL)
		assert.Equal(t, "test-scenario", *intent.GroupKey)
		assert.WithinDuration(t, intent.PaymentDeadline, time.Now().Add(30*time.Minute), 1*time.Minute)
		intent.ID = "123-id"
		checkPaymentAddressReproducibility(t, chain, intent)
		return intent, nil
	})

	mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
	// Add a channel to synchronize the async callback
	done := make(chan bool)
	mockCallbackExecutor.EXPECT().SendRequest(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, url string, body []byte, execCtx domain.CallbackExecutionContext) (*resty.Response, error) {
		defer func() {
			done <- true // Signal that the callback has been processed
		}()
		t.Logf("callback request: %s", string(body))
		assert.Equal(t, url, "http://test.com")
		bodyStruct := &CallbackPayload{}
		err := json.Unmarshal(body, bodyStruct)
		assert.NoError(t, err)
		assert.Equal(t, bodyStruct.PaymentIntentID, "123-id")
		assert.Equal(t, bodyStruct.ClientID, clientID)
		assert.Equal(t, bodyStruct.FiatAmount, fiatAmount)
		assert.Equal(t, bodyStruct.FiatCurrency, fiatCurrency)
		assert.Equal(t, "0.99", *bodyStruct.CryptoPrice)
		assert.InDelta(t, bodyStruct.PaymentDeadline, int64(time.Now().Add(30*time.Minute).Unix()), 5)
		assert.Equal(t, bodyStruct.Status, string(domain.PaymentIntentStatusPending))
		assert.Equal(t, bodyStruct.PaymentChainID, chain.ID())
		assert.Equal(t, bodyStruct.Symbol, payToken)
		assert.Equal(t, bodyStruct.CryptoAmount, "151.5151515151515152")
		assert.Equal(t, bodyStruct.OrderData, map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(200)})
		assert.Equal(t, bodyStruct.CallbackURL, "http://test.com")
		assert.Equal(t, bodyStruct.GroupKey, &scenarioID)

		assert.Nil(t, bodyStruct.PaymentTxHash, "PaymentTxHash should be nil for a new intent")
		assert.Nil(t, bodyStruct.RefundTxHash, "RefundTxHash should be nil for a new intent")
		assert.Nil(t, bodyStruct.ReceivedAmount, "ReceivedAmount should be nil for a new intent")
		assert.Nil(t, bodyStruct.AggregatedAmount, "AggregatedAmount should be nil for a new intent")
		assert.Nil(t, bodyStruct.RefundAmount, "RefundAmount should be nil for a new intent")

		// Assert KGDeepLink for callback
		assert.NotNil(t, bodyStruct.KGDeepLink)
		expectedDeepLink := fmt.Sprintf(kgDeepLinkFormat, paymentAddress.String(), chain.ID(), tokenAddress, "151.5151515151515152")
		assert.Equal(t, expectedDeepLink, *bodyStruct.KGDeepLink)

		return &resty.Response{}, nil
	})
	Init(mockRepo, mockCallbackExecutor, nil)

	mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
	mockAlchemy.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
		assert.Equal(t, addresses, []string{paymentAddress.String()})
		return nil
	})
	alchemyapi.Set(mockAlchemy)

	intent, kgErr := CreateIntent(ctx, CreateIntentParams{
		Chain:       chain,
		PayToken:    payToken,
		ClientID:    clientID,
		Origin:      origin,
		Amount:      fiatAmount,
		Currency:    util.Ptr(fiatCurrency),
		PricingMode: PricingModeFiat,
		OrderData:   map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(200)},
		CallbackURL: util.Ptr("http://test.com"),
		GroupKey:    &scenarioID,
	})
	if kgErr != nil {
		t.Fatalf("failed to create intent: %v", kgErr.Error)
	}

	// Wait for the callback to complete before continuing
	select {
	case <-done:
		// Continue with assertions
	case <-time.After(2 * time.Second):
		t.Fatal("Timed out waiting for callback to complete")
	}

	assert.Equal(t, intent.ID, "123-id")

	// Assert KGDeepLink for CreateIntent response
	assert.NotNil(t, intent.KGDeepLink)
	assert.NotNil(t, intent.CryptoPrice)
	assert.Equal(t, "0.99", *intent.CryptoPrice)
	expectedDeepLink := fmt.Sprintf(kgDeepLinkFormat, paymentAddress.String(), chain.ID(), tokenAddress, "151.5151515151515152")
	assert.Equal(t, expectedDeepLink, *intent.KGDeepLink)
}

func checkPaymentAddressReproducibility(t *testing.T, chain domain.Chain, intent *domain.PaymentIntent) {
	saltBytes, err := hex.DecodeString(intent.PaymentAddressSalt)
	if err != nil {
		t.Fatalf("failed to decode payment address salt: %v", err)
	}
	salt := new(big.Int).SetBytes(saltBytes)
	t.Logf("intent.PaymentAddressSalt: %s", intent.PaymentAddressSalt)
	t.Logf("salt: %s", salt.String())

	ownerAddress, kgErr := getOrganizationAddress(context.Background(), chain, 1)
	assert.Nil(t, kgErr)

	addr, err := erc4337.GetAccountAddress(chain, ownerAddress, salt)
	assert.NoError(t, err)
	assert.Equal(t, addr.String(), intent.PaymentAddress.String())
}

func TestGetIntents(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	orgID := 1
	clientID := "client123"
	status := domain.PaymentIntentStatusPending

	// Create mock intents to be returned - one fiat and one crypto
	fiatAmount1, _ := decimal.NewFromString("100")
	cryptoAmount1, _ := decimal.NewFromString("0.05")
	cryptoAmount2, _ := decimal.NewFromString("0.1")
	mockIntents := []*domain.PaymentIntent{
		{
			ID:              "intent1",
			ClientID:        clientID,
			OrgID:           orgID,
			Status:          status,
			FiatAmount:      util.Ptr(fiatAmount1),
			FiatCurrency:    util.Ptr("USD"),
			PaymentChain:    domain.Ethereum,
			PaymentAddress:  domain.NewEvmAddress("******************************************"),
			TokenAddress:    "******************************************",
			Symbol:          "ETH",
			Decimals:        18,
			CryptoAmount:    cryptoAmount1,
			PricingMode:     string(PricingModeFiat),
			PaymentDeadline: time.Now().Add(30 * time.Minute),
			OrderData:       map[string]any{"order_id": "123"},
		},
		{
			ID:              "intent2",
			ClientID:        clientID,
			OrgID:           orgID,
			Status:          status,
			PaymentChain:    domain.Ethereum,
			PaymentAddress:  domain.NewEvmAddress("******************************************"),
			TokenAddress:    "******************************************",
			Symbol:          "USDC",
			Decimals:        6,
			CryptoAmount:    cryptoAmount2,
			PricingMode:     string(PricingModeCrypto),
			PaymentDeadline: time.Now().Add(30 * time.Minute),
			OrderData:       map[string]any{"order_id": "456"},
		},
	}

	// Mock repository response
	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockRepo.EXPECT().GetPaymentIntents(gomock.Any(), gomock.Any()).DoAndReturn(
		func(ctx context.Context, params domain.GetPaymentIntentsParams) ([]*domain.PaymentIntent, int, error) {
			// Verify the parameters are passed correctly
			assert.Equal(t, orgID, params.OrgID)
			assert.NotNil(t, params.ClientID)
			assert.Equal(t, clientID, *params.ClientID)
			assert.NotNil(t, params.Status)
			assert.Equal(t, []domain.PaymentIntentStatus{status}, params.Status)
			assert.Equal(t, 1, params.Page)
			assert.Equal(t, 10, params.PageSize)

			return mockIntents, len(mockIntents), nil
		},
	)

	// Initialize the service with mocks
	mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
	Init(mockRepo, mockCallbackExecutor, nil)

	// Test the GetIntents function
	clientIDPtr := clientID
	params := domain.GetPaymentIntentsParams{
		OrgID:    orgID,
		ClientID: &clientIDPtr,
		Status:   []domain.PaymentIntentStatus{status},
		Page:     1,
		PageSize: 10,
	}

	intents, totalCount, kgErr := GetIntents(ctx, params)
	assert.Nil(t, kgErr)
	assert.Equal(t, 2, len(intents))
	assert.Equal(t, 2, totalCount)

	// Verify the returned intents are correctly converted
	for i, intent := range intents {
		assert.Equal(t, mockIntents[i].ID, intent.ID)
		assert.Equal(t, mockIntents[i].ClientID, intent.ClientID)
		assert.Equal(t, mockIntents[i].OrgID, intent.OrgID)
		assert.Equal(t, string(mockIntents[i].Status), intent.Status)

		// Check FiatAmount and FiatCurrency with proper nil handling
		if mockIntents[i].FiatAmount != nil {
			assert.NotNil(t, intent.FiatAmount)
			assert.Equal(t, mockIntents[i].FiatAmount.String(), *intent.FiatAmount)
		} else {
			assert.Nil(t, intent.FiatAmount)
		}

		if mockIntents[i].FiatCurrency != nil {
			assert.NotNil(t, intent.FiatCurrency)
			assert.Equal(t, *mockIntents[i].FiatCurrency, *intent.FiatCurrency)
		} else {
			assert.Nil(t, intent.FiatCurrency)
		}

		assert.Equal(t, mockIntents[i].PaymentChain.ID(), intent.PaymentChain)
		assert.Equal(t, mockIntents[i].PaymentAddress.String(), intent.PaymentAddress)
		assert.Equal(t, mockIntents[i].TokenAddress, intent.TokenAddress)
		assert.Equal(t, mockIntents[i].Symbol, intent.Symbol)
		assert.Equal(t, mockIntents[i].Decimals, intent.Decimals)
		assert.Equal(t, mockIntents[i].CryptoAmount.String(), intent.CryptoAmount)
		assert.InDelta(t, mockIntents[i].PaymentDeadline.Unix(), intent.PaymentDeadline, 1)
		assert.Equal(t, mockIntents[i].OrderData, intent.OrderData)

		// Check pricing mode (verify it's preserved in the response)
		if i == 0 {
			assert.Equal(t, string(PricingModeFiat), intent.PricingMode)
		} else {
			assert.Equal(t, string(PricingModeCrypto), intent.PricingMode)
		}

		// Assert KGDeepLink for GetIntents response
		assert.NotNil(t, intent.KGDeepLink)
		expectedDeepLink := fmt.Sprintf(kgDeepLinkFormat,
			mockIntents[i].PaymentAddress.String(),
			mockIntents[i].PaymentChain.ID(),
			mockIntents[i].TokenAddress,
			mockIntents[i].CryptoAmount.String(),
		)
		assert.Equal(t, expectedDeepLink, *intent.KGDeepLink)
	}
}

func TestCreateIntentWithCryptoPricing(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	chain := domain.Sepolia
	payToken := "USDC"
	clientID := "client123"
	origin := "https://www.google.com"
	cryptoAmount := "3.5" // Direct crypto amount
	tokenAddress, err := getTokenAddress(chain, payToken)
	assert.NoError(t, err)
	scenarioID := "test-scenario"
	expectedUSDPrice := "0.98" // Example: USDC price in USD

	mockAppRepo := domain.NewMockApplicationRepo(ctrl)
	mockAppRepo.EXPECT().GetApplicationOrgId(gomock.Any(), clientID).Return(1, nil)
	mockAppRepo.EXPECT().GetApplication(gomock.Any(), clientID).Return(&domain.Application{
		ClientID:     clientID,
		ClientSecret: "test-secret",
		Name:         "Test App",
		Domain:       "test.com",
	}, nil)
	application.Init(mockAppRepo)

	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockTokenMetaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).Return(map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: chain, TokenID: tokenAddress}: {CoingeckoID: domain.CoingeckoID("usdc-cid"), Decimals: 6}, // Changed to usdc-cid for clarity
	}, nil)
	tokenmeta.Init(mockTokenMetaRepo, nil, nil)

	mockCoingecko := coingeckoapi.NewMockICoingecko(ctrl)
	// Expect SimplePrice to be called with "usd" as currency because params.Currency is nil in this test
	mockCoingecko.EXPECT().SimplePrice(gomock.Any(), []domain.CoingeckoID{"usdc-cid"}, "usd").Return(map[domain.CoingeckoID]float64{
		"usdc-cid": 0.98, // USDC to USD rate
	}, nil)
	mockCoingecko.EXPECT().IsCurrencySupported(gomock.Any(), "usd").AnyTimes().Return(true, nil) // For the CryptoPrice fetch
	// If an explicit currency were provided in params, IsCurrencySupported would be called for that too for validation.
	// Since params.Currency is nil here, only the default "usd" for CryptoPrice is checked.
	coingeckoapi.Set(mockCoingecko)

	mockRepo := domain.NewMockPaymentIntentRepo(ctrl)
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).AnyTimes().Return(&domain.OrganizationWallets{
		EvmAddress: "******************************************",
	}, nil)
	var paymentAddress domain.Address
	mockRepo.EXPECT().CreatePaymentIntent(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, intent *domain.PaymentIntent) (*domain.PaymentIntent, error) {
		paymentAddress = intent.PaymentAddress
		t.Logf("intent: %+v", intent)
		assert.Equal(t, chain, intent.PaymentChain)
		assert.Equal(t, "******************************************", intent.TokenAddress)
		assert.Equal(t, payToken, intent.Symbol)
		assert.Equal(t, uint(6), intent.Decimals)
		assert.Equal(t, cryptoAmount, intent.CryptoAmount.String())
		assert.Nil(t, intent.FiatAmount, "FiatAmount should be nil in crypto mode without explicit currency")
		assert.Nil(t, intent.FiatCurrency, "FiatCurrency should be nil in crypto mode without explicit currency")
		assert.NotNil(t, intent.CryptoPrice)
		assert.Equal(t, expectedUSDPrice, intent.CryptoPrice.String(), "CryptoPrice should be the USD rate")
		assert.Equal(t, PricingModeCrypto.String(), intent.PricingMode)
		assert.Equal(t, domain.PaymentIntentStatusPending, intent.Status)
		assert.Equal(t, "1234567890", intent.OrderData["order_id"])
		assert.Equal(t, "0123456789", intent.OrderData["customer_id"])
		assert.Equal(t, float64(200), intent.OrderData["amount_in_game"])
		assert.Equal(t, "http://test.com", *intent.CallbackURL)
		assert.Equal(t, "test-scenario", *intent.GroupKey)
		assert.WithinDuration(t, intent.PaymentDeadline, time.Now().Add(30*time.Minute), 1*time.Minute)
		intent.ID = "123-id"
		checkPaymentAddressReproducibility(t, chain, intent)
		return intent, nil
	})

	mockCallbackExecutor := domain.NewMockCallbackExecutor(ctrl)
	// Add a channel to synchronize the async callback
	done := make(chan bool)
	mockCallbackExecutor.EXPECT().SendRequest(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, url string, body []byte, execCtx domain.CallbackExecutionContext) (*resty.Response, error) {
		defer func() {
			done <- true // Signal that the callback has been processed
		}()
		t.Logf("callback request: %s", string(body))
		assert.Equal(t, url, "http://test.com")
		bodyStruct := &CallbackPayload{}
		err := json.Unmarshal(body, bodyStruct)
		assert.NoError(t, err)
		assert.Equal(t, bodyStruct.PaymentIntentID, "123-id")
		assert.Equal(t, bodyStruct.ClientID, clientID)
		assert.Equal(t, bodyStruct.FiatAmount, "", "FiatAmount in callback should be empty for crypto mode without explicit currency")
		assert.Equal(t, bodyStruct.FiatCurrency, "", "FiatCurrency in callback should be empty for crypto mode without explicit currency")
		assert.NotNil(t, bodyStruct.CryptoPrice)                   // CryptoPrice should be present in callback
		assert.Equal(t, expectedUSDPrice, *bodyStruct.CryptoPrice) // Assert CryptoPrice value in callback
		assert.InDelta(t, bodyStruct.PaymentDeadline, int64(time.Now().Add(30*time.Minute).Unix()), 5)
		assert.Equal(t, bodyStruct.Status, string(domain.PaymentIntentStatusPending))
		assert.Equal(t, bodyStruct.PaymentChainID, chain.ID())
		assert.Equal(t, bodyStruct.Symbol, payToken)
		assert.Equal(t, bodyStruct.CryptoAmount, cryptoAmount)
		// Verify pricing mode in the callback would be reflected in the data
		// Though not directly part of the payload, the structure of the payload
		// should reflect the pricing mode (crypto amount is exact, fiat is calculated)
		assert.Equal(t, bodyStruct.OrderData, map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(200)})
		assert.Equal(t, bodyStruct.CallbackURL, "http://test.com")
		assert.Equal(t, bodyStruct.GroupKey, &scenarioID)

		assert.Nil(t, bodyStruct.PaymentTxHash, "PaymentTxHash should be nil for a new intent")
		assert.Nil(t, bodyStruct.RefundTxHash, "RefundTxHash should be nil for a new intent")
		assert.Nil(t, bodyStruct.ReceivedAmount, "ReceivedAmount should be nil for a new intent")
		assert.Nil(t, bodyStruct.AggregatedAmount, "AggregatedAmount should be nil for a new intent")
		assert.Nil(t, bodyStruct.RefundAmount, "RefundAmount should be nil for a new intent")

		// Assert KGDeepLink for callback
		assert.NotNil(t, bodyStruct.KGDeepLink)
		expectedDeepLink := fmt.Sprintf(kgDeepLinkFormat, paymentAddress.String(), chain.ID(), tokenAddress, cryptoAmount)
		assert.Equal(t, expectedDeepLink, *bodyStruct.KGDeepLink)

		return &resty.Response{}, nil
	})
	Init(mockRepo, mockCallbackExecutor, nil)

	mockAlchemy := alchemyapi.NewMockIAlchemy(ctrl)
	mockAlchemy.EXPECT().AddSingleWebhookAddresses(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).DoAndReturn(func(ctx context.Context, webhookID string, addresses []string) error {
		assert.Equal(t, addresses, []string{paymentAddress.String()})
		return nil
	})
	alchemyapi.Set(mockAlchemy)

	// Create intent with crypto pricing mode
	intent, kgErr := CreateIntent(ctx, CreateIntentParams{
		Chain:       chain,
		PayToken:    payToken,
		ClientID:    clientID,
		Origin:      origin,
		Amount:      cryptoAmount,
		Currency:    nil, // Explicitly nil for this crypto mode test scenario
		PricingMode: PricingModeCrypto,
		OrderData:   map[string]any{"order_id": "1234567890", "customer_id": "0123456789", "amount_in_game": float64(200)},
		CallbackURL: util.Ptr("http://test.com"),
		GroupKey:    &scenarioID,
	})
	if kgErr != nil {
		t.Fatalf("failed to create intent: %v", kgErr.Error)
	}

	// Wait for the callback to complete before continuing
	select {
	case <-done:
		// Continue with assertions
	case <-time.After(2 * time.Second):
		t.Fatal("Timed out waiting for callback to complete")
	}

	assert.Equal(t, intent.ID, "123-id")

	// Assert KGDeepLink for CreateIntent response
	assert.NotNil(t, intent.KGDeepLink)
	assert.NotNil(t, intent.CryptoPrice)                   // Ensure CryptoPrice is in the response
	assert.Equal(t, expectedUSDPrice, *intent.CryptoPrice) // Check value of CryptoPrice in response
	expectedDeepLink := fmt.Sprintf(kgDeepLinkFormat, paymentAddress.String(), chain.ID(), tokenAddress, cryptoAmount)
	assert.Equal(t, expectedDeepLink, *intent.KGDeepLink)
}
