package sendwithrent

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// TestHandle tests the Handle function of send-with-rent service.
func TestHandle(t *testing.T) {
	const orgWallet = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const from = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	const recipient = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
	const privateKey = "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3"

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Initialize mocks
	mockRepo := NewMockIRepo(ctrl)

	// Initialize the service with mockRepo
	Init(mockRepo, nil, "http://handler.url")

	// Define test cases
	tests := []struct {
		name          string
		setupMocks    func()
		handleFunc    func() *code.KGError
		expectedError *code.KGError
	}{
		{
			name: "Already Success",
			setupMocks: func() {
				mockRepo.EXPECT().
					AcquireLockWithRetry(gomock.Any(), "send-with-rent-handle-1", gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					GetSendWithRentByID(gomock.Any(), 1).
					Return(&domain.SendWithRent{
						ID:         1,
						Status:     domain.SendWithRentStatusSuccess,
						RetryCount: 0,
					}, nil)
				mockRepo.EXPECT().
					ReleaseLock(gomock.Any(), "send-with-rent-handle-1")
			},
			handleFunc: func() *code.KGError {
				return Handle(context.Background(), 1)
			},
			expectedError: nil,
		},
		{
			name: "Already Failed",
			setupMocks: func() {
				mockRepo.EXPECT().
					AcquireLockWithRetry(gomock.Any(), "send-with-rent-handle-2", gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					GetSendWithRentByID(gomock.Any(), 2).
					Return(&domain.SendWithRent{
						ID:         2,
						Status:     domain.SendWithRentStatusFailed,
						RetryCount: 0,
					}, nil)
				mockRepo.EXPECT().
					ReleaseLock(gomock.Any(), "send-with-rent-handle-2")
			},
			handleFunc: func() *code.KGError {
				return Handle(context.Background(), 2)
			},
			expectedError: code.NewKGError(code.SendWithRentExecutionFailed, http.StatusInternalServerError, errors.New("send with rent has already failed"), nil),
		},
		{
			name: "Success Processing",
			setupMocks: func() {
				tokenTransferTx := createSignedTokenTransferTx(t, privateKey, from, recipient, usdt, 10)
				trxTransferTx := createSignedTRXTransferTx(t, privateKey, from, orgWallet, 1.0)
				tokenTransferStr, err := json.Marshal(tokenTransferTx)
				assert.Nil(t, err)
				trxTransferStr, err := json.Marshal(trxTransferTx)
				assert.Nil(t, err)

				mockRepo.EXPECT().
					AcquireLockWithRetry(gomock.Any(), "send-with-rent-handle-3", gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					GetSendWithRentByID(gomock.Any(), 3).
					Return(&domain.SendWithRent{
						ID:         3,
						Status:     domain.SendWithRentStatusProcessing,
						RetryCount: 1,
						Chain:      domain.Shasta,
						From:       domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"),
						SignedTxs:  []string{string(tokenTransferStr), string(trxTransferStr)},
					}, nil)

				// Update repository with transaction hashes and actuals
				mockRepo.EXPECT().
					UpdateSendWithRent(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, update *domain.UpdateSendWithRentRequest) error {
						assert.Equal(t, 3, update.ID)
						assert.Equal(t, tokenTransferTx.TxID, *update.TokenTransferTxHash)
						assert.Equal(t, trxTransferTx.TxID, *update.FeeTransferTxHash)
						return nil
					}).
					Return(nil)
				mockRepo.EXPECT().
					UpdateSendWithRent(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, update *domain.UpdateSendWithRentRequest) error {
						assert.Equal(t, 3, update.ID)
						assert.Equal(t, domain.SendWithRentStatusSuccess, *update.Status)
						return nil
					}).
					Return(nil)

				mockRepo.EXPECT().
					UpdateSendWithRent(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, update *domain.UpdateSendWithRentRequest) error {
						assert.Equal(t, 3, update.ID)
						assert.Equal(t, 2, *update.RetryCount)
						return nil
					}).
					Return(nil)

				// Release lock
				mockRepo.EXPECT().
					ReleaseLock(gomock.Any(), "send-with-rent-handle-3")
			},
			handleFunc: func() *code.KGError {
				return Handle(context.Background(), 3)
			},
			expectedError: nil,
		},
		{
			name: "Retry Limit Reached",
			setupMocks: func() {
				mockRepo.EXPECT().
					AcquireLockWithRetry(gomock.Any(), "send-with-rent-handle-4", gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					GetSendWithRentByID(gomock.Any(), 4).
					Return(&domain.SendWithRent{
						ID:         4,
						Status:     domain.SendWithRentStatusProcessing,
						RetryCount: 3,
					}, nil)
				mockRepo.EXPECT().
					UpdateSendWithRent(gomock.Any(), gomock.Any()).
					Return(nil)
				mockRepo.EXPECT().
					ReleaseLock(gomock.Any(), "send-with-rent-handle-4")
			},
			handleFunc: func() *code.KGError {
				return Handle(context.Background(), 4)
			},
			expectedError: code.NewKGError(code.SendWithRentExecutionFailed, http.StatusInternalServerError, errors.New("maximum retry attempts reached"), nil),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			err := tt.handleFunc()
			if tt.expectedError == nil {
				assert.Nil(t, err)
			} else {
				assert.NotNil(t, err)
				assert.Equal(t, tt.expectedError.Code, err.Code)
				assert.Equal(t, tt.expectedError.HttpStatus, err.HttpStatus)
				assert.Contains(t, err.String(), tt.expectedError.String())
			}
		})
	}
}
