// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/service/send-with-rent (interfaces: IRepo)
//
// Generated by this command:
//
//	mockgen -package=sendwithrent -self_package=github.com/kryptogo/kg-wallet-backend/service/send-with-rent -destination=common_mock.go . IRepo
//

// Package sendwithrent is a generated GoMock package.
package sendwithrent

import (
	context "context"
	reflect "reflect"
	time "time"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockIRepo is a mock of IRepo interface.
type MockIRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIRepoMockRecorder
}

// MockIRepoMockRecorder is the mock recorder for MockIRepo.
type MockIRepoMockRecorder struct {
	mock *MockIRepo
}

// NewMockIRepo creates a new mock instance.
func NewMockIRepo(ctrl *gomock.Controller) *MockIRepo {
	mock := &MockIRepo{ctrl: ctrl}
	mock.recorder = &MockIRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRepo) EXPECT() *MockIRepoMockRecorder {
	return m.recorder
}

// AcquireLock mocks base method.
func (m *MockIRepo) AcquireLock(arg0 context.Context, arg1 string, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLock indicates an expected call of AcquireLock.
func (mr *MockIRepoMockRecorder) AcquireLock(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLock", reflect.TypeOf((*MockIRepo)(nil).AcquireLock), arg0, arg1, arg2)
}

// AcquireLockWithRetry mocks base method.
func (m *MockIRepo) AcquireLockWithRetry(arg0 context.Context, arg1 string, arg2, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLockWithRetry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLockWithRetry indicates an expected call of AcquireLockWithRetry.
func (mr *MockIRepoMockRecorder) AcquireLockWithRetry(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLockWithRetry", reflect.TypeOf((*MockIRepo)(nil).AcquireLockWithRetry), arg0, arg1, arg2, arg3)
}

// BatchGetTokenPrices mocks base method.
func (m *MockIRepo) BatchGetTokenPrices(arg0 context.Context, arg1 []domain.ChainToken) (map[domain.ChainToken]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPrices", arg0, arg1)
	ret0, _ := ret[0].(map[domain.ChainToken]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPrices indicates an expected call of BatchGetTokenPrices.
func (mr *MockIRepoMockRecorder) BatchGetTokenPrices(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPrices", reflect.TypeOf((*MockIRepo)(nil).BatchGetTokenPrices), arg0, arg1)
}

// BatchGetTokenPricesIn24H mocks base method.
func (m *MockIRepo) BatchGetTokenPricesIn24H(arg0 context.Context, arg1 []domain.ChainToken) (map[domain.ChainToken][]*domain.PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetTokenPricesIn24H", arg0, arg1)
	ret0, _ := ret[0].(map[domain.ChainToken][]*domain.PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetTokenPricesIn24H indicates an expected call of BatchGetTokenPricesIn24H.
func (mr *MockIRepoMockRecorder) BatchGetTokenPricesIn24H(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetTokenPricesIn24H", reflect.TypeOf((*MockIRepo)(nil).BatchGetTokenPricesIn24H), arg0, arg1)
}

// CreateSendWithRent mocks base method.
func (m *MockIRepo) CreateSendWithRent(arg0 context.Context, arg1 *domain.SendWithRent) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSendWithRent", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSendWithRent indicates an expected call of CreateSendWithRent.
func (mr *MockIRepoMockRecorder) CreateSendWithRent(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSendWithRent", reflect.TypeOf((*MockIRepo)(nil).CreateSendWithRent), arg0, arg1)
}

// GetAllLiquidities mocks base method.
func (m *MockIRepo) GetAllLiquidities(arg0 context.Context) ([]*domain.AssetProLiquidity, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllLiquidities", arg0)
	ret0, _ := ret[0].([]*domain.AssetProLiquidity)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetAllLiquidities indicates an expected call of GetAllLiquidities.
func (mr *MockIRepoMockRecorder) GetAllLiquidities(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllLiquidities", reflect.TypeOf((*MockIRepo)(nil).GetAllLiquidities), arg0)
}

// GetAssetPrice mocks base method.
func (m *MockIRepo) GetAssetPrice(arg0 context.Context, arg1, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssetPrice indicates an expected call of GetAssetPrice.
func (mr *MockIRepoMockRecorder) GetAssetPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetPrice", reflect.TypeOf((*MockIRepo)(nil).GetAssetPrice), arg0, arg1, arg2)
}

// GetEnergyRentUnitCost mocks base method.
func (m *MockIRepo) GetEnergyRentUnitCost(arg0 context.Context) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnergyRentUnitCost", arg0)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnergyRentUnitCost indicates an expected call of GetEnergyRentUnitCost.
func (mr *MockIRepoMockRecorder) GetEnergyRentUnitCost(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnergyRentUnitCost", reflect.TypeOf((*MockIRepo)(nil).GetEnergyRentUnitCost), arg0)
}

// GetLiquidities mocks base method.
func (m *MockIRepo) GetLiquidities(arg0 context.Context, arg1 int) ([]*domain.AssetProLiquidity, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiquidities", arg0, arg1)
	ret0, _ := ret[0].([]*domain.AssetProLiquidity)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetLiquidities indicates an expected call of GetLiquidities.
func (mr *MockIRepoMockRecorder) GetLiquidities(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiquidities", reflect.TypeOf((*MockIRepo)(nil).GetLiquidities), arg0, arg1)
}

// GetLiquidity mocks base method.
func (m *MockIRepo) GetLiquidity(arg0 context.Context, arg1 int, arg2 domain.LiquidityType, arg3 string, arg4 *string) (*domain.AssetProLiquidity, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLiquidity", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*domain.AssetProLiquidity)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetLiquidity indicates an expected call of GetLiquidity.
func (mr *MockIRepoMockRecorder) GetLiquidity(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLiquidity", reflect.TypeOf((*MockIRepo)(nil).GetLiquidity), arg0, arg1, arg2, arg3, arg4)
}

// GetNativeAssetPrice mocks base method.
func (m *MockIRepo) GetNativeAssetPrice(arg0 context.Context, arg1 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNativeAssetPrice", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNativeAssetPrice indicates an expected call of GetNativeAssetPrice.
func (mr *MockIRepoMockRecorder) GetNativeAssetPrice(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNativeAssetPrice", reflect.TypeOf((*MockIRepo)(nil).GetNativeAssetPrice), arg0, arg1)
}

// GetProfitRate mocks base method.
func (m *MockIRepo) GetProfitRate(arg0 context.Context, arg1 int, arg2 domain.ProfitRateServiceType) (*domain.AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*domain.AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRate indicates an expected call of GetProfitRate.
func (mr *MockIRepoMockRecorder) GetProfitRate(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRate", reflect.TypeOf((*MockIRepo)(nil).GetProfitRate), arg0, arg1, arg2)
}

// GetProfitRates mocks base method.
func (m *MockIRepo) GetProfitRates(arg0 context.Context, arg1 int) ([]*domain.AssetProProfitRate, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfitRates", arg0, arg1)
	ret0, _ := ret[0].([]*domain.AssetProProfitRate)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetProfitRates indicates an expected call of GetProfitRates.
func (mr *MockIRepoMockRecorder) GetProfitRates(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfitRates", reflect.TypeOf((*MockIRepo)(nil).GetProfitRates), arg0, arg1)
}

// GetSendWithRentByID mocks base method.
func (m *MockIRepo) GetSendWithRentByID(arg0 context.Context, arg1 int) (*domain.SendWithRent, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSendWithRentByID", arg0, arg1)
	ret0, _ := ret[0].(*domain.SendWithRent)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSendWithRentByID indicates an expected call of GetSendWithRentByID.
func (mr *MockIRepoMockRecorder) GetSendWithRentByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSendWithRentByID", reflect.TypeOf((*MockIRepo)(nil).GetSendWithRentByID), arg0, arg1)
}

// GetTokenPrice mocks base method.
func (m *MockIRepo) GetTokenPrice(arg0 context.Context, arg1 domain.Chain, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPrice", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPrice indicates an expected call of GetTokenPrice.
func (mr *MockIRepoMockRecorder) GetTokenPrice(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPrice", reflect.TypeOf((*MockIRepo)(nil).GetTokenPrice), arg0, arg1, arg2)
}

// GetTokenPricesIn24H mocks base method.
func (m *MockIRepo) GetTokenPricesIn24H(arg0 context.Context, arg1 domain.Chain, arg2 string) ([]*domain.PricePoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPricesIn24H", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*domain.PricePoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPricesIn24H indicates an expected call of GetTokenPricesIn24H.
func (mr *MockIRepoMockRecorder) GetTokenPricesIn24H(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPricesIn24H", reflect.TypeOf((*MockIRepo)(nil).GetTokenPricesIn24H), arg0, arg1, arg2)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockIRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*domain.OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*domain.OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockIRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockIRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// ReleaseLock mocks base method.
func (m *MockIRepo) ReleaseLock(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseLock", arg0, arg1)
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockIRepoMockRecorder) ReleaseLock(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockIRepo)(nil).ReleaseLock), arg0, arg1)
}

// SetEnergyRentUnitCost mocks base method.
func (m *MockIRepo) SetEnergyRentUnitCost(arg0 context.Context, arg1 float64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEnergyRentUnitCost", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetEnergyRentUnitCost indicates an expected call of SetEnergyRentUnitCost.
func (mr *MockIRepoMockRecorder) SetEnergyRentUnitCost(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEnergyRentUnitCost", reflect.TypeOf((*MockIRepo)(nil).SetEnergyRentUnitCost), arg0, arg1)
}

// UpdateSendWithRent mocks base method.
func (m *MockIRepo) UpdateSendWithRent(arg0 context.Context, arg1 *domain.UpdateSendWithRentRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSendWithRent", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSendWithRent indicates an expected call of UpdateSendWithRent.
func (mr *MockIRepoMockRecorder) UpdateSendWithRent(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSendWithRent", reflect.TypeOf((*MockIRepo)(nil).UpdateSendWithRent), arg0, arg1)
}

// UpsertProfitRate mocks base method.
func (m *MockIRepo) UpsertProfitRate(arg0 context.Context, arg1 domain.UpsertProfitRateParams) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertProfitRate", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// UpsertProfitRate indicates an expected call of UpsertProfitRate.
func (mr *MockIRepoMockRecorder) UpsertProfitRate(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertProfitRate", reflect.TypeOf((*MockIRepo)(nil).UpsertProfitRate), arg0, arg1)
}
