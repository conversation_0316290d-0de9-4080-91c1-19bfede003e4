package tokenmeta

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/goplus"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

type mockGoPlus struct{}

func (m *mockGoPlus) IsSpamToken(ctx context.Context, chainNo int64, contractAddress string) (bool, error) {
	return false, nil
}

func TestIsSpam(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock repo
	mockRepo := domain.NewMockTokenMetadataRepo(ctrl)

	// Setup test data
	ctx := context.Background()
	chain := domain.Polygon
	tokenID := "******************************************"

	// Setup mock expectations
	mockRepo.EXPECT().
		IsTokenSpam(ctx, chain, tokenID).
		Return(false, domain.ErrRecordNotFound)

	mockRepo.EXPECT().
		IsAirdropEventByAddress(ctx, chain, tokenID).
		Return(false)

	mockRepo.EXPECT().
		SetTokenSpam(ctx, chain, tokenID, true).
		Return(nil)

	// Initialize with mocks
	Init(mockRepo, nil, nil)
	goplus.Set(&mockGoPlus{})

	// Run test
	result := IsSpam(ctx, chain, tokenID)

	// Verify result
	assert.True(t, result)
} 