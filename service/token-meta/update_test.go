package tokenmeta

import (
	"context"
	"errors"
	"net/http"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// MockTokenMetadataUpdater is a stub updater for testing.
type MockTokenMetadataUpdater struct {
	domain.TokenMetadataUpdater
	mockFetch func(ctx context.Context) (map[domain.ChainToken]*domain.TokenMetadata, error)
}

func (m *MockTokenMetadataUpdater) FetchMetadatas(ctx context.Context) (map[domain.ChainToken]*domain.TokenMetadata, error) {
	return m.mockFetch(ctx)
}

func TestUpdate(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockUpdater := &MockTokenMetadataUpdater{}
	defaultMockFetch := func(ctx context.Context) (map[domain.ChainToken]*domain.TokenMetadata, error) {
		return map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: domain.Ethereum, TokenID: "newtoken"}: {
				Name:        "NewToken",
				Symbol:      "NTK",
				Decimals:    8,
				CoingeckoID: "c-ntk",
				LogoUrl:     "https://ntk.logo",
				IsVerified:  true,
			},
		}, nil
	}

	Init(mockRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{mockUpdater})

	t.Run("Update_Success", func(t *testing.T) {
		ctx := context.Background()
		mockUpdater.mockFetch = defaultMockFetch

		mockRepo.EXPECT().
			BatchUpdateTokenMetadata(ctx, gomock.Any()).
			Return(nil)

		err := Update(ctx)
		assert.Nil(t, err)
	})

	t.Run("Update_FetchError", func(t *testing.T) {
		mockUpdater.mockFetch = func(ctx context.Context) (map[domain.ChainToken]*domain.TokenMetadata, error) {
			return nil, errors.New("fetch error")
		}

		Init(mockRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{mockUpdater})

		ctx := context.Background()

		err := Update(ctx)
		assert.NotNil(t, err)
		assert.Equal(t, code.ExternalAPIError, err.Code)
		assert.Equal(t, http.StatusInternalServerError, err.HttpStatus)
	})

	t.Run("Update_SaveError", func(t *testing.T) {
		ctx := context.Background()
		mockUpdater.mockFetch = defaultMockFetch

		mockRepo.EXPECT().
			BatchUpdateTokenMetadata(ctx, gomock.Any()).
			Return(errors.New("save error"))

		err := Update(ctx)
		assert.NotNil(t, err)
		t.Logf("got err: %v", err)
		assert.Equal(t, code.DBError, err.Code)
		assert.Equal(t, http.StatusInternalServerError, err.HttpStatus)
	})

	t.Run("Update_MergeMetadata", func(t *testing.T) {
		// Setup two updaters to provide overlapping metadata
		mockUpdater1 := &MockTokenMetadataUpdater{
			mockFetch: func(ctx context.Context) (map[domain.ChainToken]*domain.TokenMetadata, error) {
				return map[domain.ChainToken]*domain.TokenMetadata{
					{Chain: domain.Ethereum, TokenID: "mergeToken"}: {
						Name:          "MergeToken",
						Symbol:        "",
						Decimals:      0,
						BinanceTicker: "",
					},
				}, nil
			},
		}

		mockUpdater2 := &MockTokenMetadataUpdater{
			mockFetch: func(ctx context.Context) (map[domain.ChainToken]*domain.TokenMetadata, error) {
				return map[domain.ChainToken]*domain.TokenMetadata{
					{Chain: domain.Ethereum, TokenID: "mergeToken"}: {
						Name:          "",
						Symbol:        "MTK",
						Decimals:      10,
						CoingeckoID:   "c-mtk",
						LogoUrl:       "https://mtk.logo",
						IsVerified:    true,
						BinanceTicker: "NEWTICKER",
					},
				}, nil
			},
		}

		Init(mockRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{mockUpdater1, mockUpdater2})

		mockRepo.EXPECT().
			BatchUpdateTokenMetadata(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, metadata map[domain.ChainToken]*domain.TokenMetadata) error {
				assert.Len(t, metadata, 1)
				mergeTokenKey := domain.ChainToken{Chain: domain.Ethereum, TokenID: "mergeToken"}
				assert.Contains(t, metadata, mergeTokenKey)
				assert.Equal(t, domain.TokenMetadata{
					Name:          "MergeToken",
					Symbol:        "MTK",
					Decimals:      10,
					CoingeckoID:   "c-mtk",
					LogoUrl:       "https://mtk.logo",
					IsVerified:    true,
					BinanceTicker: "NEWTICKER",
				}, *metadata[mergeTokenKey])
				return nil
			})

		ctx := context.Background()
		err := Update(ctx)
		assert.Nil(t, err)
	})
}
