package tokenmeta

import (
	"context"
	"errors"
	"sync"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/github"
	moralisapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/moralis-api"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// MockTokenMetadataFetcher is a stub fetcher for testing.
type MockTokenMetadataFetcher struct {
	domain.TokenMetadataFetcher
	mockFetch func(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error)
}

func (m *MockTokenMetadataFetcher) SupportedChains() []domain.Chain {
	return []domain.Chain{domain.Ethereum, domain.Polygon}
}

func (m *MockTokenMetadataFetcher) FetchMetadata(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
	return m.mockFetch(ctx, chain, tokenID)
}

func TestGet(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockTokenMetadataRepo(ctrl)

	// Initialize known metadata
	initKnownMetadata := func() {
		Init(mockRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})
	}

	t.Run("Get_MetadataFoundInRepo", func(t *testing.T) {
		initKnownMetadata()

		ctx := context.Background()
		chain := domain.Ethereum
		tokenID := "link"

		expectedMetadata := &domain.TokenMetadata{
			Name:        "Chainlink",
			Symbol:      "LINK",
			Decimals:    18,
			CoingeckoID: "c-link",
			LogoUrl:     "https://link.logo",
			IsVerified:  true,
		}

		mockRepo.EXPECT().
			BatchGetTokenMetadata(gomock.Any(), []domain.ChainToken{{Chain: chain, TokenID: tokenID}}).
			Return(map[domain.ChainToken]*domain.TokenMetadata{
				{Chain: chain, TokenID: tokenID}: expectedMetadata,
			}, nil)

		metadata, err := Get(ctx, chain, tokenID)
		assert.NoError(t, err)
		assert.Equal(t, expectedMetadata, metadata)
	})

	t.Run("Get_MetadataFetchedAndSaved", func(t *testing.T) {
		// Create a mock fetcher
		mockFetcher := &MockTokenMetadataFetcher{
			mockFetch: func(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
				return &domain.TokenMetadata{
					Name:        "NewToken",
					Symbol:      "NTK",
					Decimals:    8,
					CoingeckoID: "c-ntk",
					LogoUrl:     "https://ntk.logo",
					IsVerified:  true,
				}, nil
			},
		}

		Init(mockRepo, []domain.TokenMetadataFetcher{mockFetcher}, []domain.TokenMetadataUpdater{})

		ctx := context.Background()
		chain := domain.Polygon
		tokenID := "newtoken"

		// Expect BatchGetTokenMetadata to return empty
		mockRepo.EXPECT().
			BatchGetTokenMetadata(gomock.Any(), []domain.ChainToken{{Chain: chain, TokenID: tokenID}}).
			Return(map[domain.ChainToken]*domain.TokenMetadata{}, nil)

		// Expect UpsertTokenMetadata to be called
		mockRepo.EXPECT().
			UpsertTokenMetadata(gomock.Any(), chain, tokenID, gomock.Any()).
			Return(nil)

		metadata, err := Get(ctx, chain, tokenID)
		assert.NoError(t, err)
		assert.Equal(t, "NewToken", metadata.Name)
	})

	t.Run("Get_MetadataFetchError", func(t *testing.T) {
		// Create a mock fetcher that returns error
		mockFetcher := &MockTokenMetadataFetcher{
			mockFetch: func(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
				return nil, errors.New("fetch error")
			},
		}

		Init(mockRepo, []domain.TokenMetadataFetcher{mockFetcher}, []domain.TokenMetadataUpdater{})

		ctx := context.Background()
		chain := domain.Polygon
		tokenID := "errorToken"

		// Expect BatchGetTokenMetadata to return empty
		mockRepo.EXPECT().
			BatchGetTokenMetadata(gomock.Any(), []domain.ChainToken{{Chain: chain, TokenID: tokenID}}).
			Return(map[domain.ChainToken]*domain.TokenMetadata{}, nil)

		// Expect UpsertTokenMetadata not to be called since fetch failed

		metadata, err := Get(ctx, chain, tokenID)
		assert.Error(t, err)
		assert.Nil(t, metadata)
	})

	t.Run("Get_MetadataFetchNoFetcher", func(t *testing.T) {
		Init(mockRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

		ctx := context.Background()
		chain := domain.Bitcoin
		tokenID := ""

		// Main token should not attempt to fetch
		metadata, err := Get(ctx, chain, tokenID)
		assert.NoError(t, err)
		assert.Equal(t, domain.MainTokenMetadata(chain), metadata)
	})

	t.Run("Get_BatchGetError", func(t *testing.T) {
		initKnownMetadata()

		ctx := context.Background()
		chain := domain.Ethereum
		tokenID := "link"

		mockRepo.EXPECT().
			BatchGetTokenMetadata(gomock.Any(), []domain.ChainToken{{Chain: chain, TokenID: tokenID}}).
			Return(nil, errors.New("db error"))

		metadata, err := Get(ctx, chain, tokenID)
		assert.Error(t, err)
		assert.Nil(t, metadata)
	})
}

func TestBatchGet(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockTokenMetadataRepo(ctrl)

	// Create a mock fetcher
	mockFetcher := &MockTokenMetadataFetcher{
		mockFetch: func(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
			assert.Equal(t, domain.Polygon, chain)
			if tokenID == "newtoken" {
				return &domain.TokenMetadata{
					Name:        "FetchedToken",
					Symbol:      "FTK",
					Decimals:    10,
					CoingeckoID: "c-ftk",
					LogoUrl:     "https://ftk.logo",
					IsVerified:  true,
				}, nil
			}
			return nil, domain.ErrRecordNotFound
		},
	}

	Init(mockRepo, []domain.TokenMetadataFetcher{mockFetcher}, []domain.TokenMetadataUpdater{})

	ctx := context.Background()
	tokens := []domain.ChainToken{
		{Chain: domain.Ethereum, TokenID: "link"},
		{Chain: domain.Polygon, TokenID: "newtoken"},
		{Chain: domain.Polygon, TokenID: "emptytoken"},
		{Chain: domain.Ethereum, TokenID: "eth"},
	}

	mockRepo.EXPECT().
		BatchGetTokenMetadata(gomock.Any(), []domain.ChainToken{
			{Chain: domain.Ethereum, TokenID: "link"},
			{Chain: domain.Polygon, TokenID: "newtoken"},
			{Chain: domain.Polygon, TokenID: "emptytoken"},
		}).
		Return(map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: domain.Ethereum, TokenID: "link"}: {
				Name:        "Chainlink",
				Symbol:      "LINK",
				Decimals:    18,
				CoingeckoID: "c-link",
				LogoUrl:     "https://link.logo",
				IsVerified:  true,
			},
		}, nil)

	syncGroup := sync.WaitGroup{}
	syncGroup.Add(1)
	mockRepo.EXPECT().
		BatchUpsertTokenMetadata(gomock.Any(), map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: domain.Polygon, TokenID: "newtoken"}: {
				Name:        "FetchedToken",
				Symbol:      "FTK",
				Decimals:    10,
				CoingeckoID: "c-ftk",
				LogoUrl:     "https://ftk.logo",
				IsVerified:  true,
			},
			{Chain: domain.Polygon, TokenID: "emptytoken"}: {},
		}).
		DoAndReturn(func(ctx context.Context, metadatas map[domain.ChainToken]*domain.TokenMetadata) error {
			syncGroup.Done()
			return nil
		})

	metadatas, err := BatchGet(ctx, tokens, false)
	syncGroup.Wait()
	assert.NoError(t, err)
	assert.Len(t, metadatas, 3)
	assert.Equal(t, "Chainlink", metadatas[domain.ChainToken{Chain: domain.Ethereum, TokenID: "link"}].Name)
	assert.Equal(t, "FetchedToken", metadatas[domain.ChainToken{Chain: domain.Polygon, TokenID: "newtoken"}].Name)
	assert.Equal(t, "Ether", metadatas[domain.ChainToken{Chain: domain.Ethereum, TokenID: "eth"}].Name)
	assert.Nil(t, metadatas[domain.ChainToken{Chain: domain.Polygon, TokenID: "emptytoken"}])
}

func TestGetToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockFetcher := &MockTokenMetadataFetcher{
		mockFetch: func(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
			return &domain.TokenMetadata{
				Name:        "FetchedToken",
				Symbol:      "FTK",
				Decimals:    10,
				CoingeckoID: "c-ftk",
				LogoUrl:     "https://ftk.logo",
				IsVerified:  true,
			}, nil
		},
	}

	Init(mockRepo, []domain.TokenMetadataFetcher{mockFetcher}, []domain.TokenMetadataUpdater{})

	ctx := context.Background()
	chain := domain.Polygon
	tokenID := "newtoken"

	// Expect BatchGetTokenMetadata to return empty
	mockRepo.EXPECT().
		BatchGetTokenMetadata(gomock.Any(), []domain.ChainToken{{Chain: chain, TokenID: tokenID}}).
		Return(map[domain.ChainToken]*domain.TokenMetadata{}, nil)

	// Expect UpsertTokenMetadata to be called
	mockRepo.EXPECT().
		UpsertTokenMetadata(gomock.Any(), chain, tokenID, gomock.Any()).
		Return(nil)

	token, err := GetToken(ctx, chain, tokenID)
	assert.NoError(t, err)
	assert.Equal(t, "FetchedToken", token.Name())
	assert.Equal(t, "FTK", token.Symbol())
}

func TestGetNativeToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockTokenMetadataRepo(ctrl)

	Init(mockRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	ctx := context.Background()
	token, err := GetToken(ctx, domain.Polygon, "******************************************")
	assert.NoError(t, err)
	assert.Equal(t, "Polygon", token.Name())
	assert.Equal(t, "POL", token.Symbol())

	token, err = GetToken(ctx, domain.Arbitrum, "******************************************")
	assert.NoError(t, err)
	assert.Equal(t, "Ether", token.Name())
	assert.Equal(t, "ETH", token.Symbol())

	token, err = GetToken(ctx, domain.Tron, "TXka46PPwttNPWfFDPtt3GUodbPThyufaV")
	assert.NoError(t, err)
	assert.Equal(t, "Tron", token.Name())
	assert.Equal(t, "TRX", token.Symbol())
}

func TestGetKnownToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockTokenMetadataRepo(ctrl)

	Init(mockRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	ctx := context.Background()
	metadata, err := Get(ctx, domain.Polygon, "******************************************")
	assert.NoError(t, err)
	assert.Equal(t, "USD Coin", metadata.Name)
	assert.Equal(t, "USDC", metadata.Symbol)
	assert.Equal(t, uint(6), metadata.Decimals)
	assert.Equal(t, "usd-coin", string(metadata.CoingeckoID))
	assert.True(t, metadata.IsVerified)

	// Test with lower case token ID
	metadata, err = Get(ctx, domain.Polygon, "******************************************")
	assert.NoError(t, err)
	assert.Equal(t, "USD Coin", metadata.Name)
	assert.Equal(t, "USDC", metadata.Symbol)
	assert.Equal(t, uint(6), metadata.Decimals)
	assert.Equal(t, "usd-coin", string(metadata.CoingeckoID))
	assert.True(t, metadata.IsVerified)

	// Test binance ticker
	metadata, err = Get(ctx, domain.Arbitrum, "0xFF970A61A04b1cA14834A43f5dE4533eBDDB5CC8")
	assert.NoError(t, err)
	assert.Equal(t, "Bridged USDC", metadata.Name)
	assert.Equal(t, "USDC.e", metadata.Symbol)
	assert.Equal(t, uint(6), metadata.Decimals)
	assert.Equal(t, "usd-coin", string(metadata.CoingeckoID))
	assert.Equal(t, "USDC", metadata.BinanceTicker)
	assert.True(t, metadata.IsVerified)
}

// TestBatchGetTokens tests the BatchGetTokens function
func TestBatchGetTokens(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockFetcher := &MockTokenMetadataFetcher{
		mockFetch: func(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
			return &domain.TokenMetadata{
				Name:        "FetchedToken",
				Symbol:      "FTK",
				Decimals:    10,
				CoingeckoID: "c-ftk",
				LogoUrl:     "https://ftk.logo",
				IsVerified:  true,
			}, nil
		},
	}

	Init(mockRepo, []domain.TokenMetadataFetcher{mockFetcher}, []domain.TokenMetadataUpdater{})

	ctx := context.Background()
	tokens := []domain.ChainToken{
		{Chain: domain.Ethereum, TokenID: "link"},
		{Chain: domain.Polygon, TokenID: "newtoken"},
		{Chain: domain.Ethereum, TokenID: "eth"},
	}

	// Expect BatchGetTokenMetadata to return metadata for "link" and miss "newtoken"
	mockRepo.EXPECT().
		BatchGetTokenMetadata(gomock.Any(), []domain.ChainToken{
			{Chain: domain.Ethereum, TokenID: "link"},
			{Chain: domain.Polygon, TokenID: "newtoken"},
		}).
		Return(map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: domain.Ethereum, TokenID: "link"}: {
				Name:        "Chainlink",
				Symbol:      "LINK",
				Decimals:    18,
				CoingeckoID: "c-link",
				LogoUrl:     "https://link.logo",
				IsVerified:  true,
			},
		}, nil)

	syncGroup := sync.WaitGroup{}
	syncGroup.Add(1)
	mockRepo.EXPECT().
		BatchUpsertTokenMetadata(gomock.Any(), map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: domain.Polygon, TokenID: "newtoken"}: {
				Name:        "FetchedToken",
				Symbol:      "FTK",
				Decimals:    10,
				CoingeckoID: "c-ftk",
				LogoUrl:     "https://ftk.logo",
				IsVerified:  true,
			},
		}).
		DoAndReturn(func(ctx context.Context, metadatas map[domain.ChainToken]*domain.TokenMetadata) error {
			syncGroup.Done()
			return nil
		})

	// Call BatchGetTokens
	metadatas, err := BatchGetTokens(ctx, tokens)
	syncGroup.Wait()
	assert.NoError(t, err)
	assert.Len(t, metadatas, 3)

	// Verify metadata for "link"
	linkMetadata, exists := metadatas[domain.ChainToken{Chain: domain.Ethereum, TokenID: "link"}]
	assert.True(t, exists)
	assert.Equal(t, "Chainlink", linkMetadata.Name())

	// Verify metadata for "newtoken"
	newTokenMetadata, exists := metadatas[domain.ChainToken{Chain: domain.Polygon, TokenID: "newtoken"}]
	assert.True(t, exists)
	assert.Equal(t, "FetchedToken", newTokenMetadata.Name())

	// Verify main token for "eth"
	ethToken, exists := metadatas[domain.ChainToken{Chain: domain.Ethereum, TokenID: "eth"}]
	assert.True(t, exists)
	assert.Equal(t, domain.Ethereum.MainToken(), ethToken)

	t.Run("BatchGet_MetadataFetcherFails", func(t *testing.T) {
		// Create a mock fetcher that always fails
		mockFetcher := &MockTokenMetadataFetcher{
			mockFetch: func(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
				return nil, errors.New("fetcher failure")
			},
		}

		Init(mockRepo, []domain.TokenMetadataFetcher{mockFetcher}, []domain.TokenMetadataUpdater{})

		ctx := context.Background()
		tokens := []domain.ChainToken{
			{Chain: domain.Ethereum, TokenID: "failedToken"},
		}

		// Expect BatchGetTokenMetadata to return empty
		mockRepo.EXPECT().
			BatchGetTokenMetadata(gomock.Any(), tokens).
			Return(map[domain.ChainToken]*domain.TokenMetadata{}, nil)

		// Expect UpsertTokenMetadata not to be called due to fetch failure

		// Call BatchGetTokens
		metadatas, err := BatchGetTokens(ctx, tokens)
		assert.NoError(t, err)
		assert.Len(t, metadatas, 0)
	})
}

// TestBatchGetTokens_FirstFetcherFails_SecondSucceeds tests the BatchGetTokens function when the first fetcher fails and the second fetcher succeeds
func TestBatchGetTokens_FirstFetcherFails_SecondSucceeds(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockTokenMetadataRepo(ctrl)

	// First fetcher that fails
	mockFetcher1 := &MockTokenMetadataFetcher{
		mockFetch: func(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
			return nil, errors.New("first fetcher failed")
		},
	}

	// Second fetcher that succeeds
	mockFetcher2 := &MockTokenMetadataFetcher{
		mockFetch: func(ctx context.Context, chain domain.Chain, tokenID string) (*domain.TokenMetadata, error) {
			return &domain.TokenMetadata{
				Name:        "SecondFetcherToken",
				Symbol:      "SFTK",
				Decimals:    10,
				CoingeckoID: "c-sftk",
				LogoUrl:     "https://sftk.logo",
				IsVerified:  true,
			}, nil
		},
	}

	Init(mockRepo, []domain.TokenMetadataFetcher{mockFetcher1, mockFetcher2}, []domain.TokenMetadataUpdater{})

	ctx := context.Background()
	tokens := []domain.ChainToken{
		{Chain: domain.Ethereum, TokenID: "multiFetcherToken"},
	}

	// Expect BatchGetTokenMetadata to return empty, indicating the token is not in the repo
	mockRepo.EXPECT().
		BatchGetTokenMetadata(gomock.Any(), []domain.ChainToken{
			{Chain: domain.Ethereum, TokenID: "multiFetcherToken"},
		}).
		Return(map[domain.ChainToken]*domain.TokenMetadata{}, nil)

	syncGroup := sync.WaitGroup{}
	syncGroup.Add(1)
	// Expect BatchUpsertTokenMetadata to be called with the second fetcher's output
	mockRepo.EXPECT().
		BatchUpsertTokenMetadata(gomock.Any(), map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: domain.Ethereum, TokenID: "multiFetcherToken"}: {
				Name:        "SecondFetcherToken",
				Symbol:      "SFTK",
				Decimals:    10,
				CoingeckoID: "c-sftk",
				LogoUrl:     "https://sftk.logo",
				IsVerified:  true,
			},
		}).
		DoAndReturn(func(ctx context.Context, metadatas map[domain.ChainToken]*domain.TokenMetadata) error {
			syncGroup.Done()
			return nil
		})

	// Call BatchGetTokens
	metadatas, err := BatchGetTokens(ctx, tokens)
	syncGroup.Wait()
	assert.NoError(t, err)
	assert.Len(t, metadatas, 1)

	// Verify the fetched metadata
	fetchedMetadata, exists := metadatas[domain.ChainToken{Chain: domain.Ethereum, TokenID: "multiFetcherToken"}]
	assert.True(t, exists)
	assert.Equal(t, "SecondFetcherToken", fetchedMetadata.Name())
	assert.Equal(t, "SFTK", fetchedMetadata.Symbol())
	assert.Equal(t, uint(10), fetchedMetadata.Decimals())
	assert.Equal(t, "https://sftk.logo", fetchedMetadata.LogoUrl())
	assert.True(t, fetchedMetadata.IsVerified())
}

func TestBatchGetMetadataRealFetcher(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY", "MORALIS_API_KEY"})
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockTokenMetadataRepo(ctrl)

	alchemyapi.InitDefault()

	metadataFetchers := []domain.TokenMetadataFetcher{
		alchemyapi.Fetcher(),
		moralisapi.Fetcher(),
		github.Fetcher(),
	}
	Init(mockRepo, metadataFetchers, []domain.TokenMetadataUpdater{})

	ctx := context.Background()
	mockRepo.EXPECT().
		BatchGetTokenMetadata(gomock.Any(), gomock.Any()).
		Return(map[domain.ChainToken]*domain.TokenMetadata{}, nil)

	syncGroup := sync.WaitGroup{}
	syncGroup.Add(1)
	mockRepo.EXPECT().
		BatchUpsertTokenMetadata(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, metadatas map[domain.ChainToken]*domain.TokenMetadata) error {
			for _, metadata := range metadatas {
				t.Logf("metadata to save: %v", metadata)
			}
			syncGroup.Done()
			return nil
		})

	metadata, err := BatchGet(ctx, []domain.ChainToken{{Chain: domain.Ethereum, TokenID: "******************************************"}}, false)
	assert.NoError(t, err)
	data := metadata[domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"}]
	assert.NotNil(t, data)
	assert.Equal(t, "BS9000", data.Symbol)
	assert.Equal(t, uint(9), data.Decimals)
}
