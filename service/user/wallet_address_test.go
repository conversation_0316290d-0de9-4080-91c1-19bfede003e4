package user

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/stretchr/testify/assert"
)

func TestFilterUserWalletAddresses(t *testing.T) {
	rdb.Reset()
	users, userIDs := dbtest.Users()
	userID := userIDs[0]

	Init(repo.Unified())

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	data, _ := rdb.GormRepo().GetUser(context.Background(), userID, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	assert.NotNil(t, data)

	res := data.AddressesByChains("group-0-evm-0", []string{"ronin"}, false)
	assert.Equal(t, map[string][]string{
		"ronin": {"******************************************"},
	}, res)

	res = data.AddressesByChains("group-0-evm-0", []string{"oasys"}, false)
	assert.Equal(t, map[string][]string{
		"oasys": {"******************************************"},
	}, res)

	res = data.AddressesByChains("group-0-evm-0", []string{"eth", "matic"}, false)
	assert.Equal(t, map[string][]string{
		"eth":   {"******************************************"},
		"matic": {"******************************************"},
	}, res)

	res = data.AddressesByChains("group-0", []string{"bsc", "arb"}, false)
	assert.Equal(t, map[string][]string{
		"arb": {
			"******************************************",
			"******************************************",
			"******************************************",
		},
		"bsc": {
			"******************************************",
			"******************************************",
			"******************************************",
		},
	}, res)

	res = data.AddressesByChains("group-0-btc-0", []string{"btc"}, false)
	assert.Equal(t, map[string][]string{
		"btc": {"******************************************"},
	}, res)

	res = data.AddressesByChains("group-0-sol-0", []string{"sol"}, false)
	assert.Equal(t, map[string][]string{
		"sol": {"D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"},
	}, res)

	res = data.AddressesByChains("group-0-tron-0", []string{"tron"}, false)
	assert.Equal(t, map[string][]string{
		"tron": {"TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"},
	}, res)

	res = data.AddressesByChains("evm-0", []string{"eth"}, false)
	assert.Equal(t, map[string][]string{
		"eth": {"******************************************"},
	}, res)

	res = data.AddressesByChains("", []string{"eth", "btc", "tron"}, false)
	assert.Equal(t, map[string][]string{
		"btc": {
			"******************************************",
			"******************************************",
		},
		"eth": {
			"******************************************",
			"******************************************",
			"******************************************",
			"******************************************",
			"******************************************",
		},
		"tron": {
			"TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn",
			"TL4GB7cvtr9eVXR4a5GVpokfGa6WKywXFw",
			"TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7",
			"TAFD6ZsENuFX5MW6ZPqkkpKNPknArq3iii",
			"TVZG1rmshiLcwYSczDxGB4mr2eLLjPJiAt",
			"TW1FWKdR2eRdMGEEDU29nVPKurAmmJvuoR",
		},
	}, res)
}

func TestGetObserverWalletAddresses(t *testing.T) {
	rdb.Reset()
	users, userIDs := dbtest.Users()
	userID := userIDs[0]

	Init(repo.Unified())

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	data, _ := rdb.GormRepo().GetUser(context.Background(), userID, "", true, &domain.UserPreloads{
		WithWallets: true,
	})
	assert.NotNil(t, data)

	res := data.GetObserverWalletAddresses([]string{"eth"})
	assert.Equal(t, map[string][]string{
		"eth": {"******************************************"},
	}, res)
}
