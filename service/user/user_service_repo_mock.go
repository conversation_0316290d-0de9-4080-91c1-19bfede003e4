// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/service/user (interfaces: IRepo)
//
// Generated by this command:
//
//	mockgen -package=user -self_package=github.com/kryptogo/kg-wallet-backend/service/user -destination=user_service_repo_mock.go . IRepo
//

// Package user is a generated GoMock package.
package user

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockIRepo is a mock of IRepo interface.
type MockIRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIRepoMockRecorder
}

// MockIRepoMockRecorder is the mock recorder for MockIRepo.
type MockIRepoMockRecorder struct {
	mock *MockIRepo
}

// NewMockIRepo creates a new mock instance.
func NewMockIRepo(ctrl *gomock.Controller) *MockIRepo {
	mock := &MockIRepo{ctrl: ctrl}
	mock.recorder = &MockIRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRepo) EXPECT() *MockIRepoMockRecorder {
	return m.recorder
}

// BatchCheckAddressIsActivePaymentAddress mocks base method.
func (m *MockIRepo) BatchCheckAddressIsActivePaymentAddress(arg0 context.Context, arg1 domain.Chain, arg2 []domain.Address) (map[domain.Address]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckAddressIsActivePaymentAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[domain.Address]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckAddressIsActivePaymentAddress indicates an expected call of BatchCheckAddressIsActivePaymentAddress.
func (mr *MockIRepoMockRecorder) BatchCheckAddressIsActivePaymentAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckAddressIsActivePaymentAddress", reflect.TypeOf((*MockIRepo)(nil).BatchCheckAddressIsActivePaymentAddress), arg0, arg1, arg2)
}

// BatchCheckAddressOwnedByUser mocks base method.
func (m *MockIRepo) BatchCheckAddressOwnedByUser(arg0 context.Context, arg1 domain.Chain, arg2 []domain.Address) (map[domain.Address]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCheckAddressOwnedByUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[domain.Address]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCheckAddressOwnedByUser indicates an expected call of BatchCheckAddressOwnedByUser.
func (mr *MockIRepoMockRecorder) BatchCheckAddressOwnedByUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCheckAddressOwnedByUser", reflect.TypeOf((*MockIRepo)(nil).BatchCheckAddressOwnedByUser), arg0, arg1, arg2)
}

// BatchSetUsers mocks base method.
func (m *MockIRepo) BatchSetUsers(arg0 context.Context, arg1 map[string]domain.UserData) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSetUsers", arg0, arg1)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// BatchSetUsers indicates an expected call of BatchSetUsers.
func (mr *MockIRepoMockRecorder) BatchSetUsers(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSetUsers", reflect.TypeOf((*MockIRepo)(nil).BatchSetUsers), arg0, arg1)
}

// CheckAddressOwnedByUser mocks base method.
func (m *MockIRepo) CheckAddressOwnedByUser(arg0 context.Context, arg1 domain.Chain, arg2 domain.Address) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAddressOwnedByUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckAddressOwnedByUser indicates an expected call of CheckAddressOwnedByUser.
func (mr *MockIRepoMockRecorder) CheckAddressOwnedByUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAddressOwnedByUser", reflect.TypeOf((*MockIRepo)(nil).CheckAddressOwnedByUser), arg0, arg1, arg2)
}

// CountByUidWithRegisterWallet mocks base method.
func (m *MockIRepo) CountByUidWithRegisterWallet(arg0 context.Context, arg1 []string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByUidWithRegisterWallet", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByUidWithRegisterWallet indicates an expected call of CountByUidWithRegisterWallet.
func (mr *MockIRepoMockRecorder) CountByUidWithRegisterWallet(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByUidWithRegisterWallet", reflect.TypeOf((*MockIRepo)(nil).CountByUidWithRegisterWallet), arg0, arg1)
}

// CreateUser mocks base method.
func (m *MockIRepo) CreateUser(arg0 context.Context, arg1 string, arg2 *domain.UserData) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// CreateUser indicates an expected call of CreateUser.
func (mr *MockIRepoMockRecorder) CreateUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUser", reflect.TypeOf((*MockIRepo)(nil).CreateUser), arg0, arg1, arg2)
}

// DeleteUser mocks base method.
func (m *MockIRepo) DeleteUser(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUser", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUser indicates an expected call of DeleteUser.
func (mr *MockIRepoMockRecorder) DeleteUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUser", reflect.TypeOf((*MockIRepo)(nil).DeleteUser), arg0, arg1)
}

// DeleteUserFields mocks base method.
func (m *MockIRepo) DeleteUserFields(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserFields", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserFields indicates an expected call of DeleteUserFields.
func (mr *MockIRepoMockRecorder) DeleteUserFields(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserFields", reflect.TypeOf((*MockIRepo)(nil).DeleteUserFields), arg0, arg1, arg2)
}

// DeleteUserMnemonic mocks base method.
func (m *MockIRepo) DeleteUserMnemonic(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserMnemonic", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserMnemonic indicates an expected call of DeleteUserMnemonic.
func (mr *MockIRepoMockRecorder) DeleteUserMnemonic(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserMnemonic", reflect.TypeOf((*MockIRepo)(nil).DeleteUserMnemonic), arg0, arg1)
}

// DeleteUserShareKey mocks base method.
func (m *MockIRepo) DeleteUserShareKey(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserShareKey", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserShareKey indicates an expected call of DeleteUserShareKey.
func (mr *MockIRepoMockRecorder) DeleteUserShareKey(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserShareKey", reflect.TypeOf((*MockIRepo)(nil).DeleteUserShareKey), arg0, arg1)
}

// DeleteUserVaultData mocks base method.
func (m *MockIRepo) DeleteUserVaultData(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserVaultData", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserVaultData indicates an expected call of DeleteUserVaultData.
func (mr *MockIRepoMockRecorder) DeleteUserVaultData(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserVaultData", reflect.TypeOf((*MockIRepo)(nil).DeleteUserVaultData), arg0, arg1)
}

// DeleteUserWallets mocks base method.
func (m *MockIRepo) DeleteUserWallets(arg0 context.Context, arg1 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserWallets", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserWallets indicates an expected call of DeleteUserWallets.
func (mr *MockIRepoMockRecorder) DeleteUserWallets(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserWallets", reflect.TypeOf((*MockIRepo)(nil).DeleteUserWallets), arg0, arg1)
}

// GetAllEvmAddresses mocks base method.
func (m *MockIRepo) GetAllEvmAddresses(arg0 context.Context, arg1 bool) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllEvmAddresses", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllEvmAddresses indicates an expected call of GetAllEvmAddresses.
func (mr *MockIRepoMockRecorder) GetAllEvmAddresses(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllEvmAddresses", reflect.TypeOf((*MockIRepo)(nil).GetAllEvmAddresses), arg0, arg1)
}

// GetDefaultReceiveAddress mocks base method.
func (m *MockIRepo) GetDefaultReceiveAddress(arg0 context.Context, arg1, arg2 string) (string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDefaultReceiveAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetDefaultReceiveAddress indicates an expected call of GetDefaultReceiveAddress.
func (mr *MockIRepoMockRecorder) GetDefaultReceiveAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDefaultReceiveAddress", reflect.TypeOf((*MockIRepo)(nil).GetDefaultReceiveAddress), arg0, arg1, arg2)
}

// GetFcmTokens mocks base method.
func (m *MockIRepo) GetFcmTokens(arg0 context.Context, arg1, arg2 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFcmTokens", arg0, arg1, arg2)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetFcmTokens indicates an expected call of GetFcmTokens.
func (mr *MockIRepoMockRecorder) GetFcmTokens(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFcmTokens", reflect.TypeOf((*MockIRepo)(nil).GetFcmTokens), arg0, arg1, arg2)
}

// GetUser mocks base method.
func (m *MockIRepo) GetUser(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 *domain.UserPreloads) (*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUser", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUser indicates an expected call of GetUser.
func (mr *MockIRepoMockRecorder) GetUser(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUser", reflect.TypeOf((*MockIRepo)(nil).GetUser), arg0, arg1, arg2, arg3, arg4)
}

// GetUserByEmail mocks base method.
func (m *MockIRepo) GetUserByEmail(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 *domain.UserPreloads) (*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByEmail", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserByEmail indicates an expected call of GetUserByEmail.
func (mr *MockIRepoMockRecorder) GetUserByEmail(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByEmail", reflect.TypeOf((*MockIRepo)(nil).GetUserByEmail), arg0, arg1, arg2, arg3, arg4)
}

// GetUserByHandle mocks base method.
func (m *MockIRepo) GetUserByHandle(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 *domain.UserPreloads) (*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByHandle", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserByHandle indicates an expected call of GetUserByHandle.
func (mr *MockIRepoMockRecorder) GetUserByHandle(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByHandle", reflect.TypeOf((*MockIRepo)(nil).GetUserByHandle), arg0, arg1, arg2, arg3, arg4)
}

// GetUserByPhoneNumber mocks base method.
func (m *MockIRepo) GetUserByPhoneNumber(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 *domain.UserPreloads) (*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByPhoneNumber", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserByPhoneNumber indicates an expected call of GetUserByPhoneNumber.
func (mr *MockIRepoMockRecorder) GetUserByPhoneNumber(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByPhoneNumber", reflect.TypeOf((*MockIRepo)(nil).GetUserByPhoneNumber), arg0, arg1, arg2, arg3, arg4)
}

// GetUserClientIDs mocks base method.
func (m *MockIRepo) GetUserClientIDs(arg0 context.Context, arg1 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserClientIDs", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserClientIDs indicates an expected call of GetUserClientIDs.
func (mr *MockIRepoMockRecorder) GetUserClientIDs(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserClientIDs", reflect.TypeOf((*MockIRepo)(nil).GetUserClientIDs), arg0, arg1)
}

// GetUserDefaultAddresses mocks base method.
func (m *MockIRepo) GetUserDefaultAddresses(arg0 context.Context, arg1 string) (map[domain.Chain]domain.Address, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserDefaultAddresses", arg0, arg1)
	ret0, _ := ret[0].(map[domain.Chain]domain.Address)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserDefaultAddresses indicates an expected call of GetUserDefaultAddresses.
func (mr *MockIRepoMockRecorder) GetUserDefaultAddresses(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserDefaultAddresses", reflect.TypeOf((*MockIRepo)(nil).GetUserDefaultAddresses), arg0, arg1)
}

// GetUserLocale mocks base method.
func (m *MockIRepo) GetUserLocale(arg0 context.Context, arg1, arg2 string) (string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLocale", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserLocale indicates an expected call of GetUserLocale.
func (mr *MockIRepoMockRecorder) GetUserLocale(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLocale", reflect.TypeOf((*MockIRepo)(nil).GetUserLocale), arg0, arg1, arg2)
}

// GetUserWalletAddresses mocks base method.
func (m *MockIRepo) GetUserWalletAddresses(arg0 context.Context, arg1 string) ([]string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserWalletAddresses", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUserWalletAddresses indicates an expected call of GetUserWalletAddresses.
func (mr *MockIRepoMockRecorder) GetUserWalletAddresses(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserWalletAddresses", reflect.TypeOf((*MockIRepo)(nil).GetUserWalletAddresses), arg0, arg1)
}

// GetUsers mocks base method.
func (m *MockIRepo) GetUsers(arg0 context.Context, arg1 []string, arg2 string, arg3 bool, arg4 *domain.UserPreloads) ([]*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsers", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsers indicates an expected call of GetUsers.
func (mr *MockIRepoMockRecorder) GetUsers(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsers", reflect.TypeOf((*MockIRepo)(nil).GetUsers), arg0, arg1, arg2, arg3, arg4)
}

// GetUsersByEmails mocks base method.
func (m *MockIRepo) GetUsersByEmails(arg0 context.Context, arg1 []string, arg2 string, arg3 bool, arg4 *domain.UserPreloads) ([]*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersByEmails", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersByEmails indicates an expected call of GetUsersByEmails.
func (mr *MockIRepoMockRecorder) GetUsersByEmails(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersByEmails", reflect.TypeOf((*MockIRepo)(nil).GetUsersByEmails), arg0, arg1, arg2, arg3, arg4)
}

// GetUsersByEthAddressWithValidFcmToken mocks base method.
func (m *MockIRepo) GetUsersByEthAddressWithValidFcmToken(arg0 context.Context, arg1, arg2 string, arg3 bool, arg4 *domain.UserPreloads) ([]*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersByEthAddressWithValidFcmToken", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersByEthAddressWithValidFcmToken indicates an expected call of GetUsersByEthAddressWithValidFcmToken.
func (mr *MockIRepoMockRecorder) GetUsersByEthAddressWithValidFcmToken(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersByEthAddressWithValidFcmToken", reflect.TypeOf((*MockIRepo)(nil).GetUsersByEthAddressWithValidFcmToken), arg0, arg1, arg2, arg3, arg4)
}

// GetUsersByPhoneNumbers mocks base method.
func (m *MockIRepo) GetUsersByPhoneNumbers(arg0 context.Context, arg1 []string, arg2 string, arg3 bool, arg4 *domain.UserPreloads) ([]*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersByPhoneNumbers", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersByPhoneNumbers indicates an expected call of GetUsersByPhoneNumbers.
func (mr *MockIRepoMockRecorder) GetUsersByPhoneNumbers(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersByPhoneNumbers", reflect.TypeOf((*MockIRepo)(nil).GetUsersByPhoneNumbers), arg0, arg1, arg2, arg3, arg4)
}

// GetUsersForAddressNotification mocks base method.
func (m *MockIRepo) GetUsersForAddressNotification(arg0 context.Context, arg1, arg2, arg3 string) ([]*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersForAddressNotification", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersForAddressNotification indicates an expected call of GetUsersForAddressNotification.
func (mr *MockIRepoMockRecorder) GetUsersForAddressNotification(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersForAddressNotification", reflect.TypeOf((*MockIRepo)(nil).GetUsersForAddressNotification), arg0, arg1, arg2, arg3)
}

// GetUsersWithValidFcmToken mocks base method.
func (m *MockIRepo) GetUsersWithValidFcmToken(arg0 context.Context, arg1 []string, arg2 string, arg3 bool, arg4 *domain.UserPreloads) ([]*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUsersWithValidFcmToken", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetUsersWithValidFcmToken indicates an expected call of GetUsersWithValidFcmToken.
func (mr *MockIRepoMockRecorder) GetUsersWithValidFcmToken(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUsersWithValidFcmToken", reflect.TypeOf((*MockIRepo)(nil).GetUsersWithValidFcmToken), arg0, arg1, arg2, arg3, arg4)
}

// RemoveAvatar mocks base method.
func (m *MockIRepo) RemoveAvatar(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveAvatar", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveAvatar indicates an expected call of RemoveAvatar.
func (mr *MockIRepoMockRecorder) RemoveAvatar(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveAvatar", reflect.TypeOf((*MockIRepo)(nil).RemoveAvatar), arg0, arg1, arg2, arg3)
}

// SaveGoogleInfo mocks base method.
func (m *MockIRepo) SaveGoogleInfo(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveGoogleInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveGoogleInfo indicates an expected call of SaveGoogleInfo.
func (mr *MockIRepoMockRecorder) SaveGoogleInfo(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveGoogleInfo", reflect.TypeOf((*MockIRepo)(nil).SaveGoogleInfo), arg0, arg1, arg2, arg3)
}

// SavePassword mocks base method.
func (m *MockIRepo) SavePassword(arg0 context.Context, arg1 string, arg2 *domain.SavePasswordParams) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SavePassword", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SavePassword indicates an expected call of SavePassword.
func (mr *MockIRepoMockRecorder) SavePassword(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SavePassword", reflect.TypeOf((*MockIRepo)(nil).SavePassword), arg0, arg1, arg2)
}

// SaveUserAvatar mocks base method.
func (m *MockIRepo) SaveUserAvatar(arg0 context.Context, arg1, arg2, arg3, arg4, arg5 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserAvatar", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserAvatar indicates an expected call of SaveUserAvatar.
func (mr *MockIRepoMockRecorder) SaveUserAvatar(arg0, arg1, arg2, arg3, arg4, arg5 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserAvatar", reflect.TypeOf((*MockIRepo)(nil).SaveUserAvatar), arg0, arg1, arg2, arg3, arg4, arg5)
}

// SaveUserHideSpamNft mocks base method.
func (m *MockIRepo) SaveUserHideSpamNft(arg0 context.Context, arg1 string, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserHideSpamNft", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserHideSpamNft indicates an expected call of SaveUserHideSpamNft.
func (mr *MockIRepoMockRecorder) SaveUserHideSpamNft(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserHideSpamNft", reflect.TypeOf((*MockIRepo)(nil).SaveUserHideSpamNft), arg0, arg1, arg2)
}

// SaveUserShareKey mocks base method.
func (m *MockIRepo) SaveUserShareKey(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserShareKey", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserShareKey indicates an expected call of SaveUserShareKey.
func (mr *MockIRepoMockRecorder) SaveUserShareKey(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserShareKey", reflect.TypeOf((*MockIRepo)(nil).SaveUserShareKey), arg0, arg1, arg2)
}

// SaveUserVaultData mocks base method.
func (m *MockIRepo) SaveUserVaultData(arg0 context.Context, arg1 string, arg2 *domain.VaultData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserVaultData", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserVaultData indicates an expected call of SaveUserVaultData.
func (mr *MockIRepoMockRecorder) SaveUserVaultData(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserVaultData", reflect.TypeOf((*MockIRepo)(nil).SaveUserVaultData), arg0, arg1, arg2)
}

// SaveUserWallets mocks base method.
func (m *MockIRepo) SaveUserWallets(arg0 context.Context, arg1 string, arg2 *domain.Wallets) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveUserWallets", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveUserWallets indicates an expected call of SaveUserWallets.
func (mr *MockIRepoMockRecorder) SaveUserWallets(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserWallets", reflect.TypeOf((*MockIRepo)(nil).SaveUserWallets), arg0, arg1, arg2)
}

// SetUser mocks base method.
func (m *MockIRepo) SetUser(arg0 context.Context, arg1 *domain.UserData) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUser", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUser indicates an expected call of SetUser.
func (mr *MockIRepoMockRecorder) SetUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUser", reflect.TypeOf((*MockIRepo)(nil).SetUser), arg0, arg1)
}

// SetUserFcmTokens mocks base method.
func (m *MockIRepo) SetUserFcmTokens(arg0 context.Context, arg1, arg2 string, arg3 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserFcmTokens", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserFcmTokens indicates an expected call of SetUserFcmTokens.
func (mr *MockIRepoMockRecorder) SetUserFcmTokens(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserFcmTokens", reflect.TypeOf((*MockIRepo)(nil).SetUserFcmTokens), arg0, arg1, arg2, arg3)
}

// UpdateUserEmail mocks base method.
func (m *MockIRepo) UpdateUserEmail(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserEmail", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserEmail indicates an expected call of UpdateUserEmail.
func (mr *MockIRepoMockRecorder) UpdateUserEmail(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserEmail", reflect.TypeOf((*MockIRepo)(nil).UpdateUserEmail), arg0, arg1, arg2)
}

// UpdateUserFcmTokenTimestamp mocks base method.
func (m *MockIRepo) UpdateUserFcmTokenTimestamp(arg0 context.Context, arg1, arg2, arg3 string, arg4 float64) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserFcmTokenTimestamp", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserFcmTokenTimestamp indicates an expected call of UpdateUserFcmTokenTimestamp.
func (mr *MockIRepoMockRecorder) UpdateUserFcmTokenTimestamp(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserFcmTokenTimestamp", reflect.TypeOf((*MockIRepo)(nil).UpdateUserFcmTokenTimestamp), arg0, arg1, arg2, arg3, arg4)
}

// UpdateUserInfo mocks base method.
func (m *MockIRepo) UpdateUserInfo(arg0 context.Context, arg1, arg2 string, arg3 map[string]any) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserInfo", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserInfo indicates an expected call of UpdateUserInfo.
func (mr *MockIRepoMockRecorder) UpdateUserInfo(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserInfo", reflect.TypeOf((*MockIRepo)(nil).UpdateUserInfo), arg0, arg1, arg2, arg3)
}

// UpdateUserPhoneNumber mocks base method.
func (m *MockIRepo) UpdateUserPhoneNumber(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserPhoneNumber", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserPhoneNumber indicates an expected call of UpdateUserPhoneNumber.
func (mr *MockIRepoMockRecorder) UpdateUserPhoneNumber(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserPhoneNumber", reflect.TypeOf((*MockIRepo)(nil).UpdateUserPhoneNumber), arg0, arg1, arg2)
}

// UserAll mocks base method.
func (m *MockIRepo) UserAll(arg0 context.Context, arg1 string, arg2 bool, arg3 *domain.UserPreloads) ([]*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserAll", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// UserAll indicates an expected call of UserAll.
func (mr *MockIRepoMockRecorder) UserAll(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserAll", reflect.TypeOf((*MockIRepo)(nil).UserAll), arg0, arg1, arg2, arg3)
}

// UserListAfterWithLimit mocks base method.
func (m *MockIRepo) UserListAfterWithLimit(arg0 context.Context, arg1 *string, arg2 int, arg3 string, arg4 bool, arg5 *domain.UserPreloads) ([]*domain.UserData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserListAfterWithLimit", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*domain.UserData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// UserListAfterWithLimit indicates an expected call of UserListAfterWithLimit.
func (mr *MockIRepoMockRecorder) UserListAfterWithLimit(arg0, arg1, arg2, arg3, arg4, arg5 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserListAfterWithLimit", reflect.TypeOf((*MockIRepo)(nil).UserListAfterWithLimit), arg0, arg1, arg2, arg3, arg4, arg5)
}

// UserNotificationReadAll mocks base method.
func (m *MockIRepo) UserNotificationReadAll(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserNotificationReadAll", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UserNotificationReadAll indicates an expected call of UserNotificationReadAll.
func (mr *MockIRepoMockRecorder) UserNotificationReadAll(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserNotificationReadAll", reflect.TypeOf((*MockIRepo)(nil).UserNotificationReadAll), arg0, arg1, arg2)
}
