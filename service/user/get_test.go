package user

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestGetAddressesByIdentifier(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := NewMockIRepo(ctrl)
	originalRepo := r
	r = mockRepo
	defer func() { r = originalRepo }()

	ctx := context.Background()
	testUID := "test-uid-123"
	testPhone := "+************"
	testEmail := "<EMAIL>"
	testClientID := ""
	withUserData := true

	// Setup default preloads for tests
	defaultPreloads := &domain.UserPreloads{
		WithWallets: true,
	}

	// Setup test user data with wallets
	evmAddress := domain.NewEvmAddress("******************************************")
	btcAddress := domain.NewStrAddress("******************************************")
	solAddress := domain.NewStrAddress("D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7")
	tronAddress := domain.NewTronAddress("******************************************")

	// Create limited set of chains for testing
	testUserData := &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         testUID,
			PhoneNumber: testPhone,
			Email:       nil,
		},
		Wallets: &domain.Wallets{
			DefaultReceiveWallets: map[domain.Chain]domain.Address{
				domain.Ethereum: evmAddress,
				domain.Bitcoin:  btcAddress,
				domain.Solana:   solAddress,
				domain.Tron:     tronAddress,
			},
		},
	}

	t.Run("Success - Get by Phone Number", func(t *testing.T) {
		// Mock GetByPhoneNumber call
		mockRepo.EXPECT().
			GetUserByPhoneNumber(ctx, testPhone, testClientID, withUserData, defaultPreloads).
			Return(testUserData, nil)

		// Call the function
		addresses, err := GetAddressesByIdentifier(ctx, testPhone, "")

		// Assert
		assert.Nil(t, err)
		// Check only the chains we're specifically testing for
		assert.Equal(t, evmAddress, addresses[domain.Ethereum])
		assert.Equal(t, btcAddress, addresses[domain.Bitcoin])
		assert.Equal(t, solAddress, addresses[domain.Solana])
		assert.Equal(t, tronAddress, addresses[domain.Tron])
	})

	t.Run("Success - Get by Email", func(t *testing.T) {
		// Create a new user with email
		emailPtr := testEmail
		testUserWithEmail := &domain.UserData{
			UserInfo: domain.UserInfo{
				UID:   testUID,
				Email: &emailPtr,
			},
			Wallets: &domain.Wallets{
				DefaultReceiveWallets: map[domain.Chain]domain.Address{
					domain.Ethereum: evmAddress,
					domain.Bitcoin:  btcAddress,
					domain.Solana:   solAddress,
					domain.Tron:     tronAddress,
				},
			},
		}

		// Mock GetByEmail call
		mockRepo.EXPECT().
			GetUserByEmail(ctx, testEmail, testClientID, withUserData, defaultPreloads).
			Return(testUserWithEmail, nil)

		// Call the function
		addresses, err := GetAddressesByIdentifier(ctx, "", testEmail)

		// Assert
		assert.Nil(t, err)
		// Check only the chains we're specifically testing for
		assert.Equal(t, evmAddress, addresses[domain.Ethereum])
		assert.Equal(t, btcAddress, addresses[domain.Bitcoin])
		assert.Equal(t, solAddress, addresses[domain.Solana])
		assert.Equal(t, tronAddress, addresses[domain.Tron])
	})

	t.Run("Error - Missing Identifiers", func(t *testing.T) {
		// Call the function with empty phone and email
		addresses, err := GetAddressesByIdentifier(ctx, "", "")

		// Assert
		assert.NotNil(t, err)
		assert.Nil(t, addresses)
		assert.Equal(t, code.ParamIncorrect, err.Code)
		assert.Equal(t, http.StatusBadRequest, err.HttpStatus)
	})

	t.Run("Error - User Not Found by Phone", func(t *testing.T) {
		// Mock error response from GetByPhoneNumber
		notFoundErr := code.NewKGError(code.UserNotFound, http.StatusNotFound,
			fmt.Errorf("user not found"), nil)

		mockRepo.EXPECT().
			GetUserByPhoneNumber(ctx, testPhone, testClientID, withUserData, defaultPreloads).
			Return(nil, notFoundErr)

		// Call the function
		addresses, err := GetAddressesByIdentifier(ctx, testPhone, "")

		// Assert
		assert.NotNil(t, err)
		assert.Nil(t, addresses)
		assert.Equal(t, code.UserNotFound, err.Code)
		assert.Equal(t, http.StatusNotFound, err.HttpStatus)
	})

	t.Run("Error - User Not Found by Email", func(t *testing.T) {
		// Mock error response from GetByEmail
		notFoundErr := code.NewKGError(code.UserNotFound, http.StatusNotFound,
			fmt.Errorf("user not found"), nil)

		mockRepo.EXPECT().
			GetUserByEmail(ctx, testEmail, testClientID, withUserData, defaultPreloads).
			Return(nil, notFoundErr)

		// Call the function
		addresses, err := GetAddressesByIdentifier(ctx, "", testEmail)

		// Assert
		assert.NotNil(t, err)
		assert.Nil(t, addresses)
		assert.Equal(t, code.UserNotFound, err.Code)
		assert.Equal(t, http.StatusNotFound, err.HttpStatus)
	})

	t.Run("Error - Database Error", func(t *testing.T) {
		// Mock error response from GetByPhoneNumber
		dbErr := code.NewKGError(code.DBError, http.StatusInternalServerError,
			fmt.Errorf("database error"), nil)

		mockRepo.EXPECT().
			GetUserByPhoneNumber(ctx, testPhone, testClientID, withUserData, defaultPreloads).
			Return(nil, dbErr)

		// Call the function
		addresses, err := GetAddressesByIdentifier(ctx, testPhone, "")

		// Assert
		assert.NotNil(t, err)
		assert.Nil(t, addresses)
		assert.Equal(t, code.DBError, err.Code)
		assert.Equal(t, http.StatusInternalServerError, err.HttpStatus)
	})
}
