package referral

import (
	"context"
	"math/big"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	solanaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api"
	signingservertest "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/test"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestWithdrawReferralRewards_HappyPath(t *testing.T) {
	// Setup
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	signingservertest.Setup(t)

	mockRepo := NewMockIRepo(ctrl)
	Init(mockRepo)
	solanaapi.InitDefault()

	ctx := context.Background()
	uid := "test-user-id"
	recipientAddress := "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"
	amount := decimal.NewFromFloat(0.000001)

	mockRepo.EXPECT().AcquireLockWithRetry(
		gomock.Any(),
		"referral-reward-withdraw-1",
		gomock.Any(),
		gomock.Any(),
	).Return(nil)
	mockRepo.EXPECT().ReleaseLock(ctx, "referral-reward-withdraw-1")

	// Mock GetReferralBalance
	mockRepo.EXPECT().
		GetReferralBalance(ctx, uid).
		Return(&domain.ReferralBalance{
			UID:              uid,
			TotalRewards:     big.NewInt(1000000000),
			AvailableRewards: big.NewInt(1000000000),
			WithdrawnRewards: big.NewInt(0),
		}, nil)

	// Mock GetWalletsByOrganizationId
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
		SolanaAddress: "7ET7q8BBmpef4MDqE47tffFfHeE2y1ccT5MEDdA9pzeH",
	}, nil)

	// Mock CreateReferralWithdrawal
	mockRepo.EXPECT().
		CreateReferralWithdrawal(ctx, gomock.Any()).
		DoAndReturn(func(ctx context.Context, param *domain.ReferralWithdrawal) (int, error) {
			assert.Equal(t, uid, param.UID)
			amountBig := amount.Mul(decimal.NewFromFloat(1_000_000_000)).BigInt()
			assert.Equal(t, amountBig.String(), param.Amount.String())
			assert.Equal(t, recipientAddress, param.Recipient.String())
			return 1, nil
		})

	// Mock UpdateReferralWithdrawalTx
	mockRepo.EXPECT().
		UpdateReferralWithdrawalTx(ctx, 1, gomock.Any()).
		DoAndReturn(func(ctx context.Context, id int, txHash string) error {
			t.Logf("UpdateReferralWithdrawalTx: id=%d, txHash=%s", id, txHash)
			return nil
		})

	// Create service with mocked dependencies
	result, err := WithdrawReferralRewards(ctx, uid, amount, recipientAddress)

	// Assert
	assert.Nil(t, err)
	if err != nil {
		t.Fatalf("WithdrawReferralRewards failed: %+v", err)
	}
	assert.NotNil(t, result)
	assert.Equal(t, amount.String(), result.Amount)
	assert.NotEmpty(t, result.TxHash)
}
