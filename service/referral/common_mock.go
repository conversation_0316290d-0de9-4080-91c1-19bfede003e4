// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/service/referral (interfaces: IRepo)
//
// Generated by this command:
//
//	mockgen -package=referral -self_package=github.com/kryptogo/kg-wallet-backend/service/referral -destination=common_mock.go . IRepo
//

// Package referral is a generated GoMock package.
package referral

import (
	context "context"
	reflect "reflect"
	time "time"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockIRepo is a mock of IRepo interface.
type MockIRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIRepoMockRecorder
}

// MockIRepoMockRecorder is the mock recorder for MockIRepo.
type MockIRepoMockRecorder struct {
	mock *MockIRepo
}

// NewMockIRepo creates a new mock instance.
func NewMockIRepo(ctrl *gomock.Controller) *MockIRepo {
	mock := &MockIRepo{ctrl: ctrl}
	mock.recorder = &MockIRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIRepo) EXPECT() *MockIRepoMockRecorder {
	return m.recorder
}

// AcquireLock mocks base method.
func (m *MockIRepo) AcquireLock(arg0 context.Context, arg1 string, arg2 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLock", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLock indicates an expected call of AcquireLock.
func (mr *MockIRepoMockRecorder) AcquireLock(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLock", reflect.TypeOf((*MockIRepo)(nil).AcquireLock), arg0, arg1, arg2)
}

// AcquireLockWithRetry mocks base method.
func (m *MockIRepo) AcquireLockWithRetry(arg0 context.Context, arg1 string, arg2, arg3 time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AcquireLockWithRetry", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// AcquireLockWithRetry indicates an expected call of AcquireLockWithRetry.
func (mr *MockIRepoMockRecorder) AcquireLockWithRetry(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcquireLockWithRetry", reflect.TypeOf((*MockIRepo)(nil).AcquireLockWithRetry), arg0, arg1, arg2, arg3)
}

// CreateReferralReward mocks base method.
func (m *MockIRepo) CreateReferralReward(arg0 context.Context, arg1 *domain.ReferralReward) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateReferralReward", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateReferralReward indicates an expected call of CreateReferralReward.
func (mr *MockIRepoMockRecorder) CreateReferralReward(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateReferralReward", reflect.TypeOf((*MockIRepo)(nil).CreateReferralReward), arg0, arg1)
}

// CreateReferralWithdrawal mocks base method.
func (m *MockIRepo) CreateReferralWithdrawal(arg0 context.Context, arg1 *domain.ReferralWithdrawal) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateReferralWithdrawal", arg0, arg1)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateReferralWithdrawal indicates an expected call of CreateReferralWithdrawal.
func (mr *MockIRepoMockRecorder) CreateReferralWithdrawal(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateReferralWithdrawal", reflect.TypeOf((*MockIRepo)(nil).CreateReferralWithdrawal), arg0, arg1)
}

// GetReferralBalance mocks base method.
func (m *MockIRepo) GetReferralBalance(arg0 context.Context, arg1 string) (*domain.ReferralBalance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetReferralBalance", arg0, arg1)
	ret0, _ := ret[0].(*domain.ReferralBalance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReferralBalance indicates an expected call of GetReferralBalance.
func (mr *MockIRepoMockRecorder) GetReferralBalance(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReferralBalance", reflect.TypeOf((*MockIRepo)(nil).GetReferralBalance), arg0, arg1)
}

// GetUserByReferralCode mocks base method.
func (m *MockIRepo) GetUserByReferralCode(arg0 context.Context, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserByReferralCode", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserByReferralCode indicates an expected call of GetUserByReferralCode.
func (mr *MockIRepoMockRecorder) GetUserByReferralCode(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserByReferralCode", reflect.TypeOf((*MockIRepo)(nil).GetUserByReferralCode), arg0, arg1)
}

// GetWalletsByOrganizationId mocks base method.
func (m *MockIRepo) GetWalletsByOrganizationId(arg0 context.Context, arg1 int) (*domain.OrganizationWallets, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWalletsByOrganizationId", arg0, arg1)
	ret0, _ := ret[0].(*domain.OrganizationWallets)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetWalletsByOrganizationId indicates an expected call of GetWalletsByOrganizationId.
func (mr *MockIRepoMockRecorder) GetWalletsByOrganizationId(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWalletsByOrganizationId", reflect.TypeOf((*MockIRepo)(nil).GetWalletsByOrganizationId), arg0, arg1)
}

// ReleaseLock mocks base method.
func (m *MockIRepo) ReleaseLock(arg0 context.Context, arg1 string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ReleaseLock", arg0, arg1)
}

// ReleaseLock indicates an expected call of ReleaseLock.
func (mr *MockIRepoMockRecorder) ReleaseLock(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReleaseLock", reflect.TypeOf((*MockIRepo)(nil).ReleaseLock), arg0, arg1)
}

// UpdateReferralWithdrawalTx mocks base method.
func (m *MockIRepo) UpdateReferralWithdrawalTx(arg0 context.Context, arg1 int, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateReferralWithdrawalTx", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateReferralWithdrawalTx indicates an expected call of UpdateReferralWithdrawalTx.
func (mr *MockIRepoMockRecorder) UpdateReferralWithdrawalTx(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateReferralWithdrawalTx", reflect.TypeOf((*MockIRepo)(nil).UpdateReferralWithdrawalTx), arg0, arg1, arg2)
}
