package referral

import (
	"context"
	"fmt"
	"math/big"
	"net/http"

	solanago "github.com/gagliardetto/solana-go"
	associatedtokenaccount "github.com/gagliardetto/solana-go/programs/associated-token-account"
	computebudget "github.com/gagliardetto/solana-go/programs/compute-budget"
	"github.com/gagliardetto/solana-go/programs/system"
	"github.com/gagliardetto/solana-go/programs/token"
	"github.com/gagliardetto/solana-go/rpc"
	"github.com/kryptogo/kg-wallet-backend/chain/solana"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	signingclient "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
)

// SendOrgSolana sends the given amount of SOL from the org wallet to the recipient address. Returns txHash or error.
func SendOrgSolana(ctx context.Context, orgID int, amountLamports *big.Int, recipient string) (string, *code.KGError) {
	client := solana.GetClient(domain.Solana)
	orgWallets, kgErr := r.GetWalletsByOrganizationId(ctx, orgID)
	if kgErr != nil {
		return "", kgErr
	}
	solBalance, err := client.NativeBalance(ctx, domain.NewStrAddress(orgWallets.SolanaAddress))
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get SOL balance", map[string]interface{}{"error": err.Error()})
		return "", code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, fmt.Errorf("failed to get SOL balance: %w", err), nil)
	}
	wsolBalance, err := client.TokenBalance(ctx, domain.NewStrAddress(orgWallets.SolanaAddress), wsolTokenMint)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get WSOL balance", map[string]interface{}{"error": err.Error()})
		return "", code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, fmt.Errorf("failed to get WSOL balance: %w", err), nil)
	}
	if new(big.Int).Add(solBalance, wsolBalance).Cmp(new(big.Int).Add(amountLamports, big.NewInt(1000000))) < 0 {
		// 0.001 SOL as buffer for tx fee
		return "", code.NewKGError(code.InsufficientOrgBalance, http.StatusBadRequest, fmt.Errorf("insufficient WSOL balance in organization wallet"), nil)
	}

	rawClient := solana.GetRawClient()
	recentBlockHash, err := rawClient.GetLatestBlockhash(ctx, rpc.CommitmentConfirmed)
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to get recent blockhash", map[string]interface{}{"error": err.Error()})
		return "", code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, fmt.Errorf("failed to get recent blockhash: %w", err), nil)
	}

	var instructions []solanago.Instruction
	computePriceIx := computebudget.NewSetComputeUnitPriceInstruction(10_000)
	instructions = append(instructions, computePriceIx.Build())
	computeLimitIx := computebudget.NewSetComputeUnitLimitInstruction(50_000)
	instructions = append(instructions, computeLimitIx.Build())

	orgAddress := solanago.MustPublicKeyFromBase58(orgWallets.SolanaAddress)
	if solBalance.Cmp(new(big.Int).Add(amountLamports, big.NewInt(1000000))) < 0 {
		tokenAccountsResult, err := rawClient.GetTokenAccountsByOwner(ctx, orgAddress, &rpc.GetTokenAccountsConfig{
			Mint: solanago.WrappedSol.ToPointer(),
		}, &rpc.GetTokenAccountsOpts{
			Encoding: solanago.EncodingBase64Zstd,
		})
		if err != nil {
			kglog.ErrorWithDataCtx(ctx, "Failed to get org WSOL associated token account", map[string]interface{}{"error": err.Error()})
			return "", code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, fmt.Errorf("failed to get org WSOL associated token account: %w", err), nil)
		}
		if len(tokenAccountsResult.Value) == 0 {
			return "", code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, fmt.Errorf("failed to get org WSOL associated token account: %w", err), nil)
		}
		orgTokenAccount := tokenAccountsResult.Value[0].Pubkey

		// Close existing WSOL account
		closeWsolIx := token.NewCloseAccountInstruction(
			orgTokenAccount,
			orgAddress,
			orgAddress,
			[]solanago.PublicKey{},
		).Build()
		instructions = append(instructions, closeWsolIx)

		// Create WSOL account again using associated token program
		createWsolIx := associatedtokenaccount.NewCreateInstruction(orgAddress, orgAddress, solanago.WrappedSol).Build()
		instructions = append(instructions, createWsolIx)
	}

	// Transfer SOL to recipient
	transferIx := system.NewTransferInstruction(
		amountLamports.Uint64(),
		orgAddress,
		solanago.MustPublicKeyFromBase58(recipient),
	).Build()
	instructions = append(instructions, transferIx)

	tx, err := solanago.NewTransaction(instructions, recentBlockHash.Value.Blockhash, solanago.TransactionPayer(orgAddress))
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to create transaction", map[string]interface{}{"error": err.Error()})
		return "", code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, fmt.Errorf("failed to create transaction: %w", err), nil)
	}

	txHash, err := signingclient.SignAndBroadcastSolanaTransaction(ctx, orgID, tx, [32]byte{})
	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to broadcast transaction", map[string]interface{}{"error": err.Error()})
		return "", code.NewKGError(code.ExternalAPIError, http.StatusInternalServerError, fmt.Errorf("failed to broadcast transaction: %w", err), nil)
	}

	return txHash, nil
}
