// Code generated by MockGen. DO NOT EDIT.
// Source: common.go
//
// Generated by this command:
//
//	mockgen -source=common.go -destination=common_mock.go -package=notification IService
//

// Package notification is a generated GoMock package.
package notification

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockIService is a mock of IService interface.
type MockIService struct {
	ctrl     *gomock.Controller
	recorder *MockIServiceMockRecorder
}

// MockIServiceMockRecorder is the mock recorder for MockIService.
type MockIServiceMockRecorder struct {
	mock *MockIService
}

// NewMockIService creates a new mock instance.
func NewMockIService(ctrl *gomock.Controller) *MockIService {
	mock := &MockIService{ctrl: ctrl}
	mock.recorder = &MockIServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIService) EXPECT() *MockIServiceMockRecorder {
	return m.recorder
}

// Send mocks base method.
func (m *MockIService) Send(ctx context.Context, notification *domain.Notification) (int, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", ctx, notification)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// Send indicates an expected call of Send.
func (mr *MockIServiceMockRecorder) Send(ctx, notification any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockIService)(nil).Send), ctx, notification)
}

// SendAnnouncement mocks base method.
func (m *MockIService) SendAnnouncement(ctx context.Context, notification *domain.Notification, toLocales []string) *code.KGError {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendAnnouncement", ctx, notification, toLocales)
	ret0, _ := ret[0].(*code.KGError)
	return ret0
}

// SendAnnouncement indicates an expected call of SendAnnouncement.
func (mr *MockIServiceMockRecorder) SendAnnouncement(ctx, notification, toLocales any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendAnnouncement", reflect.TypeOf((*MockIService)(nil).SendAnnouncement), ctx, notification, toLocales)
}
