//go:generate mockgen -package=bridge -self_package=github.com/kryptogo/kg-wallet-backend/service/bridge-fee -destination=common_mock.go . IRepo

package bridge

import (
	"context"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

// IRepo defines the interface for bridge-fee service repository
type IRepo interface {
	// Profit rate management
	domain.ProfitRateRepo
	
	// Asset price management
	domain.AssetPriceRepo
	
	// Organization wallet management
	GetWalletsByOrganizationId(ctx context.Context, orgID int) (wallets *domain.OrganizationWallets, kgErr *code.KGError)
	
	// Bridge record management
	CreateBridgeRecord(ctx context.Context, params *domain.BridgeRecord) error
	UpdateBridgeRecord(ctx context.Context, fromTxHash string, params *domain.BridgeRecord) error
}

var r IRepo

func Init(repo IRepo) {
	r = repo
}
