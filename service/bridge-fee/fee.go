package bridge

import (
	"context"
	"fmt"
	"net/http"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
)

func GetFeeInfo(ctx context.Context, orgID int, chain domain.Chain, serviceType domain.ProfitRateServiceType) (float64, string, *code.KGError) {
	if serviceType != domain.ProfitRateServiceTypeBridge && serviceType != domain.ProfitRateServiceTypeSwapDefi {
		return 0, "", code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("invalid service type: %s", serviceType), nil)
	}

	profitRate, err := r.GetProfitRate(ctx, orgID, serviceType)
	if err != nil {
		return 0, "", err
	}
	kgMinimumRevenueRate := profitRate.GetKgMinimumRevenueRate()
	rate := profitRate.ProfitRate.Add(kgMinimumRevenueRate)
	wallets, err := r.GetWalletsByOrganizationId(ctx, orgID)
	if err != nil {
		return 0, "", err
	}
	var wallet string
	if chain.IsTVM() {
		wallet = wallets.TronAddress
	} else if chain.IsEVM() {
		wallet = wallets.EvmAddress
	} else {
		return 0, "", code.NewKGError(code.ChainIDNotSupported, http.StatusBadRequest, fmt.Errorf("unsupported chain id %s for org wallets", chain.ID()), nil)
	}
	return rate.InexactFloat64(), wallet, nil
}
