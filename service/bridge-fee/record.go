package bridge

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/kryptogo/kg-wallet-backend/chain"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/tracing"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/shopspring/decimal"
)

func CreateBridgeRecord(ctx context.Context, params *domain.CreateBridgeRecordParams) *code.KGError {
	ctx, span := tracing.Start(ctx, "service.bridge.CreateBridgeRecord")
	defer span.End()

	bridge := domain.BridgeRecord{
		OrgID:            params.OrgID,
		UID:              params.UID,
		FromChain:        params.FromChain,
		FromAddress:      params.FromAddress,
		FromTokenAddress: params.FromTokenAddress,
		FromAmount:       params.FromAmount,
		FromTxHash:       params.FromTxHash,

		ToChain:        params.ToChain,
		ToAddress:      params.ToAddress,
		ToTokenAddress: params.ToTokenAddress,
		ToAmount:       params.ToAmount,
		ToTxHash:       params.ToTxHash,

		FeeChain:          params.FeeChain,
		FeeReceiveAddress: params.FeeReceiveAddress,
		FeeTokenAddress:   params.FeeTokenAddress,
		FeeTxHash:         params.FeeTxHash,
	}

	// Validate fee tx
	{
		feeAmount, feeTokenPrice, kgErr := parseFeeTx(ctx, bridge)
		if kgErr != nil {
			return kgErr
		}

		if feeAmount.LessThan(params.EstimatedFeeAmount) {
			kglog.WarningWithDataCtx(ctx, "Bridge, fee amount is less than estimated fee amount", map[string]interface{}{
				"feeAmount":          feeAmount,
				"estimatedFeeAmount": params.EstimatedFeeAmount,
			})
		}

		bridge.FeeAmount = *feeAmount
		bridge.FeeTokenPrice = decimal.NewFromFloat(*feeTokenPrice)
		bridge.FeeUSD = bridge.FeeAmount.Mul(bridge.FeeTokenPrice)
	}

	// Calculate profit margin
	{
		serviceType := domain.ProfitRateServiceTypeBridge
		if params.FromChain == params.ToChain {
			serviceType = domain.ProfitRateServiceTypeSwapDefi
		}

		fromTokenPrice, err := r.GetTokenPrice(ctx, bridge.FromChain, bridge.FromTokenAddress)
		if err != nil {
			return code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
		}

		profitRate, kgErr := r.GetProfitRate(ctx, bridge.OrgID, serviceType)
		if kgErr != nil {
			return kgErr
		}
		kgMinimumRate := profitRate.GetKgMinimumRevenueRate()
		kgMinimumRevenue, err := profitRate.GetKgMinimumRevenue(&bridge.FromAmount, nil)
		if err != nil {
			return code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
		}
		kgMinimumRevenueUSD := kgMinimumRevenue.Mul(decimal.NewFromFloat(fromTokenPrice))
		profitMargin := bridge.FeeUSD.Sub(kgMinimumRevenueUSD).Mul(decimal.NewFromInt(1).Sub(profitRate.ProfitShareRatio))
		if profitMargin.IsNegative() {
			kglog.ErrorWithDataCtx(ctx, "Bridge, profit margin is negative", map[string]interface{}{
				"profitMargin":        profitMargin,
				"feeUSD":              bridge.FeeUSD,
				"kgMinimumRevenueUSD": kgMinimumRevenueUSD,
				"profitRate":          profitRate,
			})
			return code.NewKGError(code.InternalError, http.StatusInternalServerError, errors.New("profit margin is negative"), nil)
		}

		bridge.ProfitKgMinimumRate = kgMinimumRate
		bridge.ProfitRate = profitRate.ProfitRate
		bridge.ProfitShareRatio = profitRate.ProfitShareRatio
		bridge.ProfitMargin = profitMargin
	}

	err := r.CreateBridgeRecord(ctx, &bridge)
	if err != nil {
		return code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}
	return nil
}

func parseFeeTx(ctx context.Context, bridge domain.BridgeRecord) (*decimal.Decimal, *float64, *code.KGError) {
	client, err := chain.GetChainClient(bridge.FromChain)
	if err != nil {
		return nil, nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}

	ctxWithTimeout, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()
	status, err := client.WaitUntilTransactionConfirmed(ctxWithTimeout, bridge.FeeTxHash)
	if err != nil {
		return nil, nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}
	if status != domain.TransactionStatusSuccess {
		return nil, nil, code.NewKGError(code.InternalError, http.StatusBadRequest, fmt.Errorf("fee tx not success: %v", status), nil)
	}

	price, err := r.GetTokenPrice(ctx, bridge.FeeChain, bridge.FeeTokenAddress)
	if err != nil {
		return nil, nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}

	tx, err := client.TransactionDetail(ctx, bridge.FeeTxHash)
	if err != nil {
		return nil, nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}

	// check all receivers and find the one that is the fee receiver
	if tx.To == bridge.FeeReceiveAddress {
		// fee is native token
		decimals := bridge.FeeChain.MainToken().Decimals()
		feeAmount := decimal.NewFromBigInt(tx.Value, 0).Div(decimal.New(1, int32(decimals)))
		return &feeAmount, &price, nil
	}

	for _, transfer := range tx.InternalTransfers {
		if transfer.To == bridge.FeeReceiveAddress {
			// fee is native token
			decimals := bridge.FeeChain.MainToken().Decimals()
			feeAmount := decimal.NewFromBigInt(transfer.Amount, 0).Div(decimal.New(1, int32(decimals)))
			return &feeAmount, &price, nil
		}
	}

	// fee is not native token, should be other fungible token
	meta, err := tokenmeta.Get(ctx, bridge.FeeChain, bridge.FeeTokenAddress)
	if err != nil {
		return nil, nil, code.NewKGError(code.ParamIncorrect, http.StatusBadRequest, fmt.Errorf("get token metadata failed: %w", err), nil)
	}

	decimals := meta.Decimals
	for _, transfer := range tx.TokenTransfers {
		if transfer.To == bridge.FeeReceiveAddress {
			feeAmount := decimal.NewFromBigInt(transfer.Amount, 0).Div(decimal.New(1, int32(decimals)))
			return &feeAmount, &price, nil
		}
	}

	return nil, nil, code.NewKGError(code.InternalError, http.StatusInternalServerError, fmt.Errorf("fee token not found"), nil)
}

func UpdateBridgeRecord(ctx context.Context, fromTxHash string, bridge *domain.BridgeRecord) *code.KGError {
	err := r.UpdateBridgeRecord(ctx, fromTxHash, bridge)
	if err != nil {
		if err == code.ErrBridgeRecordNotFound {
			return code.NewKGError(code.ParamIncorrect, http.StatusNotFound, err, nil)
		}
		return code.NewKGError(code.InternalError, http.StatusInternalServerError, err, nil)
	}
	return nil
}
