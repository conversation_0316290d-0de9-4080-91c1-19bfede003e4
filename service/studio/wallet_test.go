package studio_test

import (
	"context"
	"errors"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/service/studio"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// mockRepo combines multiple domain repository mocks to satisfy the IRepo interface
type mockRepo struct {
	*domain.MockStudioOrgRepo
	*domain.MockStudioUserAPIKeyRepo
}

func setupMockRepo(ctrl *gomock.Controller) *mockRepo {
	return &mockRepo{
		MockStudioOrgRepo:        domain.NewMockStudioOrgRepo(ctrl),
		MockStudioUserAPIKeyRepo: domain.NewMockStudioUserAPIKeyRepo(ctrl),
	}
}

func TestGetOrganizationWallet(t *testing.T) {
	// Setup
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mr := setupMockRepo(ctrl)
	studio.Init(mr)

	ctx := context.Background()
	orgID := 1
	walletType := "ethereum"

	// Test case: Success
	t.Run("success", func(t *testing.T) {
		expectedWallet := &domain.StudioOrganizationWallet{
			ID:                  1,
			OrganizationID:      orgID,
			WalletType:          walletType,
			WalletAddress:       "0xabc123",
			EncryptedPrivateKey: "encrypted-private-key",
		}

		mr.MockStudioOrgRepo.EXPECT().
			GetOrgWallet(ctx, orgID, walletType).
			Return(expectedWallet, nil)

		wallet, err := studio.GetOrganizationWallet(ctx, orgID, walletType)

		assert.Nil(t, err)
		assert.Equal(t, expectedWallet, wallet)
	})

	// Test case: Error
	t.Run("error", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			GetOrgWallet(ctx, orgID, walletType).
			Return(nil, errors.New("database error"))

		wallet, err := studio.GetOrganizationWallet(ctx, orgID, walletType)

		assert.NotNil(t, err)
		assert.Nil(t, wallet)
		assert.Contains(t, err.Error(), "database error")
	})
}

func TestGetImportedAddresses(t *testing.T) {
	// Setup
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mr := setupMockRepo(ctrl)
	studio.Init(mr)

	ctx := context.Background()
	orgID := 1

	// Test case: Success
	t.Run("success", func(t *testing.T) {
		expectedAddresses := []*domain.ImportedAddress{
			{
				ID:                    1,
				OrganizationID:        orgID,
				Chain:                 "ethereum",
				Address:               "0xabc123",
				AddedByUserID:         "user1",
				DefaultReceiveAddress: false,
			},
			{
				ID:                    2,
				OrganizationID:        orgID,
				Chain:                 "solana",
				Address:               "sol123456",
				AddedByUserID:         "user2",
				DefaultReceiveAddress: false,
			},
		}

		mr.MockStudioOrgRepo.EXPECT().
			GetImportedAddressesByOrgID(ctx, orgID).
			Return(expectedAddresses, nil)

		addresses, err := studio.GetImportedAddresses(ctx, orgID)

		assert.Nil(t, err)
		assert.Equal(t, expectedAddresses, addresses)
	})

	// Test case: Error
	t.Run("error", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			GetImportedAddressesByOrgID(ctx, orgID).
			Return(nil, errors.New("database error"))

		addresses, err := studio.GetImportedAddresses(ctx, orgID)

		assert.NotNil(t, err)
		assert.Nil(t, addresses)
		assert.Contains(t, err.Error(), "database error")
	})
}

func TestCountImportedAddresses(t *testing.T) {
	// Setup
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mr := setupMockRepo(ctrl)
	studio.Init(mr)

	ctx := context.Background()
	orgID := 1
	chain := "ethereum"

	// Test case: Success
	t.Run("success", func(t *testing.T) {
		expectedCount := int64(5)

		mr.MockStudioOrgRepo.EXPECT().
			CountImportedAddresses(ctx, orgID, chain).
			Return(expectedCount, nil)

		count, err := studio.CountImportedAddresses(ctx, orgID, chain)

		assert.Nil(t, err)
		assert.Equal(t, expectedCount, count)
	})

	// Test case: Error
	t.Run("error", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			CountImportedAddresses(ctx, orgID, chain).
			Return(int64(0), errors.New("database error"))

		count, err := studio.CountImportedAddresses(ctx, orgID, chain)

		assert.NotNil(t, err)
		assert.Equal(t, int64(0), count)
		assert.Contains(t, err.Error(), "database error")
	})
}

func TestImportAddress(t *testing.T) {
	// Setup
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mr := setupMockRepo(ctrl)
	studio.Init(mr)

	ctx := context.Background()
	orgID := 1
	chain := "ethereum"
	address := "0xabc123"
	userID := "user1"

	// Test case: Success
	t.Run("success", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			InsertImportedAddress(ctx, orgID, chain, address, userID).
			Return(nil)

		err := studio.ImportAddress(ctx, orgID, chain, address, userID)

		assert.Nil(t, err)
	})

	// Test case: Error
	t.Run("error", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			InsertImportedAddress(ctx, orgID, chain, address, userID).
			Return(errors.New("database error"))

		err := studio.ImportAddress(ctx, orgID, chain, address, userID)

		assert.NotNil(t, err)
		assert.Contains(t, err.Error(), "database error")
	})
}

func TestDeleteImportedAddress(t *testing.T) {
	// Setup
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mr := setupMockRepo(ctrl)
	studio.Init(mr)

	ctx := context.Background()
	orgID := 1
	addressID := 1

	// Test case: Success
	t.Run("success", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			DeleteImportedAddress(ctx, orgID, addressID).
			Return(nil)

		err := studio.DeleteImportedAddress(ctx, orgID, addressID)

		assert.Nil(t, err)
	})

	// Test case: Error
	t.Run("error", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			DeleteImportedAddress(ctx, orgID, addressID).
			Return(errors.New("database error"))

		err := studio.DeleteImportedAddress(ctx, orgID, addressID)

		assert.NotNil(t, err)
		assert.Contains(t, err.Error(), "database error")
	})
}

func TestSetDefaultImportedAddress(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mr := setupMockRepo(ctrl)
	studio.Init(mr)

	ctx := context.Background()
	orgID := 1
	addressID := 101
	isDefaultTrue := true
	isDefaultFalse := false
	dbErr := errors.New("database error")
	notFoundErr := errors.New("imported address not found")

	t.Run("success_set_true", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			SetDefaultImportedAddress(ctx, orgID, addressID, isDefaultTrue).
			Return(nil)

		kgErr := studio.SetDefaultImportedAddress(ctx, orgID, addressID, isDefaultTrue)
		assert.Nil(t, kgErr)
	})

	t.Run("success_set_false", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			SetDefaultImportedAddress(ctx, orgID, addressID, isDefaultFalse).
			Return(nil)

		kgErr := studio.SetDefaultImportedAddress(ctx, orgID, addressID, isDefaultFalse)
		assert.Nil(t, kgErr)
	})

	t.Run("error_db_error", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			SetDefaultImportedAddress(ctx, orgID, addressID, isDefaultTrue).
			Return(dbErr)

		kgErr := studio.SetDefaultImportedAddress(ctx, orgID, addressID, isDefaultTrue)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.DBError, kgErr.Code)
		assert.ErrorContains(t, kgErr.Error, "database error")
	})

	t.Run("error_address_not_found", func(t *testing.T) {
		mr.MockStudioOrgRepo.EXPECT().
			SetDefaultImportedAddress(ctx, orgID, addressID, isDefaultTrue).
			Return(notFoundErr) // Mocking the specific error string our RDB layer returns

		kgErr := studio.SetDefaultImportedAddress(ctx, orgID, addressID, isDefaultTrue)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.RecordNotFound, kgErr.Code)
		assert.ErrorContains(t, kgErr.Error, "imported address not found")
	})
}
