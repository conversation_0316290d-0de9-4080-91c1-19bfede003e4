package studio

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCreateStudioUserAPIKey(t *testing.T) {
	// Set up mock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a mock repo
	// Create a mock repo
	mockAPIKeyRepo := domain.NewMockStudioUserAPIKeyRepo(ctrl)
	mockStudioOrgRepo := domain.NewMockStudioOrgRepo(ctrl)

	// Create a struct that satisfies the studio.IRepo interface by embedding the mocks
	type mockStudioFullRepo struct {
		*domain.MockStudioUserAPIKeyRepo
		*domain.MockStudioOrgRepo
	}

	fullRepo := &mockStudioFullRepo{
		MockStudioUserAPIKeyRepo: mockAPIKeyRepo,
		MockStudioOrgRepo:        mockStudioOrgRepo,
	}

	// Initialize the service with the mock repo
	Init(fullRepo)

	// Test data
	orgID := 1
	uid := "test-user-123"
	keyName := "Test API Key"
	description := "This is a test API key"
	var storedPrefix string

	// Create params
	params := &CreateStudioUserAPIKeyParams{
		OrgID:       orgID,
		UID:         uid,
		Name:        keyName,
		Description: &description,
	}

	// Set up mock expectations
	mockAPIKeyRepo.EXPECT().
		CreateStudioUserAPIKey(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, apiKey *domain.StudioUserAPIKey) *code.KGError {
			// Verify apiKey fields
			assert.Equal(t, orgID, apiKey.OrgID)
			assert.Equal(t, uid, apiKey.UID)
			assert.Equal(t, keyName, apiKey.Name)
			assert.Equal(t, description, *apiKey.Description)
			assert.NotEmpty(t, apiKey.KeyPrefix)
			assert.NotEmpty(t, apiKey.KeyHash)
			assert.Greater(t, apiKey.CreatedAt, int64(0))
			assert.Len(t, apiKey.KeyPrefix, 8) // Verify prefix length
			assert.Nil(t, apiKey.DeletedAt)    // Shouldn't be deleted

			// Capture the prefix to verify later
			storedPrefix = apiKey.KeyPrefix

			// Update the ID field as the real repo would
			apiKey.ID = 123
			return nil
		})

	// Call the function
	apiKey, kgErr := CreateStudioUserAPIKey(context.Background(), params)

	// Assertions
	assert.Nil(t, kgErr)
	assert.NotEmpty(t, apiKey)
	assert.Contains(t, apiKey, ".")
	assert.True(t, len(apiKey) > 15, "API key should be of sufficient length")

	// Verify the generated API key starts with the prefix stored in the repository
	assert.True(t, apiKey[:8] == storedPrefix, "API key should start with the stored prefix")
}

func TestGetStudioUserAPIKey(t *testing.T) {
	// Set up mock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a mock repo
	mockAPIKeyRepo := domain.NewMockStudioUserAPIKeyRepo(ctrl)
	mockStudioOrgRepo := domain.NewMockStudioOrgRepo(ctrl)

	// Create a struct that satisfies the studio.IRepo interface by embedding the mocks
	type mockStudioFullRepo struct {
		*domain.MockStudioUserAPIKeyRepo
		*domain.MockStudioOrgRepo
	}

	fullRepo := &mockStudioFullRepo{
		MockStudioUserAPIKeyRepo: mockAPIKeyRepo,
		MockStudioOrgRepo:        mockStudioOrgRepo,
	}

	// Initialize the service with the mock repo
	Init(fullRepo)

	// Test data
	apiKey := "prefix.secret"
	now := time.Now().Unix()
	description := "API key description"
	mockAPIKeyModel := &domain.StudioUserAPIKey{
		ID:          123,
		OrgID:       1,
		UID:         "test-user-123",
		Name:        "Test API Key",
		KeyPrefix:   "prefix",
		KeyHash:     "hashed_value",
		Description: &description,
		CreatedAt:   now,
	}

	// Set up mock expectations
	mockAPIKeyRepo.EXPECT().
		GetStudioUserAPIKeyByHash(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, hash string) (*domain.StudioUserAPIKey, *code.KGError) {
			// Verify hash is properly passed
			assert.NotEmpty(t, hash)
			return mockAPIKeyModel, nil
		})

	// Call the function
	result, kgErr := GetStudioUserAPIKey(context.Background(), apiKey)

	// Assertions
	assert.Nil(t, kgErr)
	assert.NotNil(t, result)

	// Verify individual fields
	assert.Equal(t, 123, result.ID)
	assert.Equal(t, 1, result.OrgID)
	assert.Equal(t, "test-user-123", result.UID)
	assert.Equal(t, "Test API Key", result.Name)
	assert.Equal(t, "prefix", result.KeyPrefix)
	assert.Equal(t, "hashed_value", result.KeyHash)
	assert.Equal(t, description, *result.Description)
	assert.Equal(t, now, result.CreatedAt)
	assert.Nil(t, result.DeletedAt)
}

func TestListStudioUserAPIKeys(t *testing.T) {
	// Set up mock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a mock repo
	// Create a mock repo
	mockAPIKeyRepo := domain.NewMockStudioUserAPIKeyRepo(ctrl)
	mockStudioOrgRepo := domain.NewMockStudioOrgRepo(ctrl)

	// Create a struct that satisfies the studio.IRepo interface by embedding the mocks
	type mockStudioFullRepo struct {
		*domain.MockStudioUserAPIKeyRepo
		*domain.MockStudioOrgRepo
	}

	fullRepo := &mockStudioFullRepo{
		MockStudioUserAPIKeyRepo: mockAPIKeyRepo,
		MockStudioOrgRepo:        mockStudioOrgRepo,
	}

	// Initialize the service with the mock repo
	Init(fullRepo)

	// Test data
	orgID := 1
	uid := "test-user-123"
	page := 1
	pageSize := 10
	totalCount := 2
	now := time.Now().Unix()
	description1 := "Description 1"
	description2 := "Description 2"
	mockAPIKeys := []*domain.StudioUserAPIKey{
		{
			ID:          123,
			OrgID:       orgID,
			UID:         uid,
			Name:        "Test API Key 1",
			KeyPrefix:   "prefix1",
			KeyHash:     "hash1",
			Description: &description1,
			CreatedAt:   now,
		},
		{
			ID:          124,
			OrgID:       orgID,
			UID:         uid,
			Name:        "Test API Key 2",
			KeyPrefix:   "prefix2",
			KeyHash:     "hash2",
			Description: &description2,
			CreatedAt:   now - 100,
		},
	}

	// Create the expected params struct
	expectedParams := domain.GetStudioUserAPIKeysParams{
		OrgID:    orgID,
		UID:      uid,
		Page:     page,
		PageSize: pageSize,
	}

	// Set up mock expectations
	mockAPIKeyRepo.EXPECT().
		ListStudioUserAPIKeys(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, params domain.GetStudioUserAPIKeysParams) ([]*domain.StudioUserAPIKey, int, *code.KGError) {
			// Verify params are properly passed
			assert.Equal(t, expectedParams, params)
			return mockAPIKeys, totalCount, nil
		})

	// Create the params for the call
	params := domain.GetStudioUserAPIKeysParams{
		OrgID:    orgID,
		UID:      uid,
		Page:     page,
		PageSize: pageSize,
	}

	// Call the function
	results, count, kgErr := ListStudioUserAPIKeys(context.Background(), params)

	// Assertions
	assert.Nil(t, kgErr)
	assert.Equal(t, mockAPIKeys, results)
	assert.Equal(t, totalCount, count)
	assert.Len(t, results, 2)

	// Verify individual fields of each key
	firstKey := results[0]
	assert.Equal(t, 123, firstKey.ID)
	assert.Equal(t, orgID, firstKey.OrgID)
	assert.Equal(t, uid, firstKey.UID)
	assert.Equal(t, "Test API Key 1", firstKey.Name)
	assert.Equal(t, "prefix1", firstKey.KeyPrefix)
	assert.Equal(t, "hash1", firstKey.KeyHash)
	assert.Equal(t, description1, *firstKey.Description)
	assert.Equal(t, now, firstKey.CreatedAt)

	secondKey := results[1]
	assert.Equal(t, 124, secondKey.ID)
	assert.Equal(t, "Test API Key 2", secondKey.Name)
	assert.Equal(t, "prefix2", secondKey.KeyPrefix)
	assert.Equal(t, now-100, secondKey.CreatedAt)
}

func TestUpdateAPIKeyLastUsed(t *testing.T) {
	// Set up mock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a mock repo
	// Create a mock repo
	mockAPIKeyRepo := domain.NewMockStudioUserAPIKeyRepo(ctrl)
	mockStudioOrgRepo := domain.NewMockStudioOrgRepo(ctrl)

	// Create a struct that satisfies the studio.IRepo interface by embedding the mocks
	type mockStudioFullRepo struct {
		*domain.MockStudioUserAPIKeyRepo
		*domain.MockStudioOrgRepo
	}

	fullRepo := &mockStudioFullRepo{
		MockStudioUserAPIKeyRepo: mockAPIKeyRepo,
		MockStudioOrgRepo:        mockStudioOrgRepo,
	}

	// Initialize the service with the mock repo
	Init(fullRepo)

	// Test data
	apiKeyID := 123
	mockAPIKey := &domain.StudioUserAPIKey{
		ID:        apiKeyID,
		OrgID:     1,
		UID:       "test-user-123",
		Name:      "Test API Key",
		KeyPrefix: "prefix",
		CreatedAt: time.Now().Unix() - 3600, // Created an hour ago
	}

	var updatedLastUsed int64

	// Set up mock expectations
	mockAPIKeyRepo.EXPECT().
		UpdateAPIKeyLastUsed(gomock.Any(), apiKeyID).
		DoAndReturn(func(ctx context.Context, id int) *code.KGError {
			// Verify ID is properly passed
			assert.Equal(t, 123, id)
			// Simulate updating the LastUsedAt field
			updatedLastUsed = time.Now().Unix()
			return nil
		})

	// Set up a mock to verify the updated value
	mockAPIKeyRepo.EXPECT().
		GetStudioUserAPIKeyByHash(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, hash string) (*domain.StudioUserAPIKey, *code.KGError) {
			// Return API key with updated LastUsedAt
			mockAPIKey.LastUsedAt = &updatedLastUsed
			return mockAPIKey, nil
		}).AnyTimes()

	// Call the function
	kgErr := UpdateAPIKeyLastUsed(context.Background(), apiKeyID)

	// Assertions
	assert.Nil(t, kgErr)

	// Verify the LastUsedAt was updated by retrieving the key
	// This is a bit artificial since we're using the same mock, but demonstrates the verification approach
	result, kgErr := GetStudioUserAPIKey(context.Background(), "some.key")
	assert.Nil(t, kgErr)
	assert.NotNil(t, result.LastUsedAt)
	assert.Equal(t, updatedLastUsed, *result.LastUsedAt)
	// The LastUsedAt should be recent
	assert.True(t, time.Now().Unix()-*result.LastUsedAt < 10, "LastUsedAt should be recent")
}

func TestDeleteStudioUserAPIKey(t *testing.T) {
	// Set up mock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a mock repo
	// Create a mock repo
	mockAPIKeyRepo := domain.NewMockStudioUserAPIKeyRepo(ctrl)
	mockStudioOrgRepo := domain.NewMockStudioOrgRepo(ctrl)

	// Create a struct that satisfies the studio.IRepo interface by embedding the mocks
	type mockStudioFullRepo struct {
		*domain.MockStudioUserAPIKeyRepo
		*domain.MockStudioOrgRepo
	}

	fullRepo := &mockStudioFullRepo{
		MockStudioUserAPIKeyRepo: mockAPIKeyRepo,
		MockStudioOrgRepo:        mockStudioOrgRepo,
	}

	// Initialize the service with the mock repo
	Init(fullRepo)

	// Test data
	orgID := 1
	apiKeyID := 123
	uid := "test-user-123"

	// Simulate deleted/not found API key
	notFoundError := code.NewKGError(code.APIKeyNotValid, 404, nil, nil)

	// Set up mock expectations for delete
	mockAPIKeyRepo.EXPECT().
		DeleteStudioUserAPIKey(gomock.Any(), orgID, apiKeyID, uid).
		DoAndReturn(func(ctx context.Context, orgID, apiKeyID int, uid string) *code.KGError {
			// Verify parameters
			assert.Equal(t, 1, orgID)
			assert.Equal(t, 123, apiKeyID)
			assert.Equal(t, "test-user-123", uid)
			return nil
		})

	// Set up mock for verification attempt after deletion
	mockAPIKeyRepo.EXPECT().
		GetStudioUserAPIKeyByHash(gomock.Any(), gomock.Any()).
		Return(nil, notFoundError).AnyTimes()

	// Call the function
	kgErr := DeleteStudioUserAPIKey(context.Background(), orgID, apiKeyID, uid)

	// Assertions
	assert.Nil(t, kgErr)

	// Verify the key can't be found after deletion
	// This simulates trying to get a deleted key
	result, kgErr := GetStudioUserAPIKey(context.Background(), "some.key")
	assert.Nil(t, result)
	assert.NotNil(t, kgErr)
	assert.Equal(t, code.APIKeyNotValid, kgErr.Code)
	assert.Equal(t, 404, kgErr.HttpStatus)
}
