package payment_item

import (
	"context"
	"net/http"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCreatePaymentItem(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	mockRepo := domain.NewMockPaymentItemRepo(ctrl)
	mockAppRepo := domain.NewMockApplicationRepo(ctrl)

	// Initialize both repositories
	Init(mockRepo)
	application.Init(mockAppRepo)

	// Mock the client service
	clientID := "test-client-id"
	orgID := 1

	// Mock application repository calls for client validation
	mockOAuthApp := &domain.OAuthApplication{
		Application: domain.Application{
			ClientID: clientID,
			Name:     "Test Client",
		},
	}

	mockAppRepo.EXPECT().GetOAuthApplication(gomock.Any(), gomock.Eq(clientID)).Return(mockOAuthApp, nil).AnyTimes()
	mockAppRepo.EXPECT().GetApplicationOrgId(gomock.Any(), gomock.Eq(clientID)).Return(orgID, nil).AnyTimes()

	// Prepare common not found error
	notFoundErr := code.NewKGError(code.RecordNotFound, http.StatusNotFound, nil, nil)

	t.Run("ValidInput", func(t *testing.T) {
		price, _ := decimal.NewFromString("100.50")
		validInput := &domain.PaymentItemCreate{
			Name:            "Test Item",
			Price:           price,
			Currency:        "USD",
			OrganizationID:  1,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
			CallbackURL:     util.Ptr("https://example.com/callback"),
			PayToken:        util.Ptr("service-test-token-123"),
		}

		expectedItem := &domain.PaymentItem{
			ID:              "test-item-id",
			Name:            validInput.Name,
			Price:           validInput.Price,
			Currency:        validInput.Currency,
			OrganizationID:  validInput.OrganizationID,
			ClientID:        validInput.ClientID,
			OrderDataFields: validInput.OrderDataFields,
			CallbackURL:     util.Ptr("https://example.com/callback"),
			PayToken:        util.Ptr("service-test-token-123"),
		}

		// Mock check for existing item by name - return not found
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(validInput.OrganizationID),
			gomock.Eq(validInput.ClientID),
			gomock.Eq(validInput.Name),
		).Return(nil, notFoundErr)

		mockRepo.EXPECT().CreatePaymentItem(gomock.Any(), gomock.Eq(validInput)).Return(expectedItem, nil)

		item, kgErr := CreatePaymentItem(ctx, validInput)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItem, item)
		assert.Equal(t, clientID, item.ClientID)
		assert.Equal(t, "service-test-token-123", *item.PayToken)
	})

	t.Run("EmptyName", func(t *testing.T) {
		price, _ := decimal.NewFromString("100.50")
		invalidInput := &domain.PaymentItemCreate{
			Name:           "",
			Price:          price,
			Currency:       "USD",
			OrganizationID: 1,
			ClientID:       clientID,
		}

		item, kgErr := CreatePaymentItem(ctx, invalidInput)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Nil(t, item)
	})

	t.Run("InvalidPrice", func(t *testing.T) {
		invalidPrice, _ := decimal.NewFromString("-10.50")
		invalidPriceInput := &domain.PaymentItemCreate{
			Name:           "Test Item with Invalid Price",
			Price:          invalidPrice,
			Currency:       "USD",
			OrganizationID: 1,
			ClientID:       clientID,
		}

		// Mock check for existing item with invalid price
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(invalidPriceInput.OrganizationID),
			gomock.Eq(invalidPriceInput.ClientID),
			gomock.Eq(invalidPriceInput.Name),
		).Return(nil, notFoundErr)

		item, kgErr := CreatePaymentItem(ctx, invalidPriceInput)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Nil(t, item)
	})

	t.Run("InvalidCurrency", func(t *testing.T) {
		price, _ := decimal.NewFromString("100.50")
		invalidCurrencyInput := &domain.PaymentItemCreate{
			Name:           "Test Item with Invalid Currency",
			Price:          price,
			Currency:       "EUR", // Not supported
			OrganizationID: 1,
			ClientID:       clientID,
		}

		// Mock check for existing item with invalid currency
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(invalidCurrencyInput.OrganizationID),
			gomock.Eq(invalidCurrencyInput.ClientID),
			gomock.Eq(invalidCurrencyInput.Name),
		).Return(nil, notFoundErr)

		item, kgErr := CreatePaymentItem(ctx, invalidCurrencyInput)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Nil(t, item)
	})

	t.Run("ValidInputWithOrderDataFields", func(t *testing.T) {
		price, _ := decimal.NewFromString("100.50")
		validOrderFieldsInput := &domain.PaymentItemCreate{
			Name:           "Test Item with Fields",
			Price:          price,
			Currency:       "USD",
			OrganizationID: 1,
			ClientID:       clientID,
			OrderDataFields: []domain.OrderDataField{
				{
					FieldName:  "customer_name",
					FieldLabel: "Customer Name",
					Required:   true,
					FieldType:  "text",
				},
			},
		}

		expectedItemWithFields := &domain.PaymentItem{
			ID:             "test-item-id-2",
			Name:           validOrderFieldsInput.Name,
			Price:          validOrderFieldsInput.Price,
			Currency:       validOrderFieldsInput.Currency,
			OrganizationID: validOrderFieldsInput.OrganizationID,
			ClientID:       validOrderFieldsInput.ClientID,
			OrderDataFields: []domain.OrderDataField{
				{
					FieldName:  "customer_name",
					FieldLabel: "Customer Name",
					Required:   true,
					FieldType:  "text",
				},
			},
		}

		// Mock check for existing item with order fields - return not found
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(validOrderFieldsInput.OrganizationID),
			gomock.Eq(validOrderFieldsInput.ClientID),
			gomock.Eq(validOrderFieldsInput.Name),
		).Return(nil, notFoundErr)

		mockRepo.EXPECT().CreatePaymentItem(gomock.Any(), gomock.Eq(validOrderFieldsInput)).Return(expectedItemWithFields, nil)

		item, kgErr := CreatePaymentItem(ctx, validOrderFieldsInput)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItemWithFields, item)
	})

	t.Run("InvalidOrderDataFields", func(t *testing.T) {
		price, _ := decimal.NewFromString("100.50")
		invalidOrderFieldsInput := &domain.PaymentItemCreate{
			Name:           "Test Item with Invalid Fields",
			Price:          price,
			Currency:       "USD",
			OrganizationID: 1,
			ClientID:       clientID,
			OrderDataFields: []domain.OrderDataField{
				{
					FieldName:  "",
					FieldLabel: "Empty Field Name",
					Required:   true,
					FieldType:  "text",
				},
			},
		}

		// Mock check for existing item with invalid fields
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(invalidOrderFieldsInput.OrganizationID),
			gomock.Eq(invalidOrderFieldsInput.ClientID),
			gomock.Eq(invalidOrderFieldsInput.Name),
		).Return(nil, notFoundErr)

		item, kgErr := CreatePaymentItem(ctx, invalidOrderFieldsInput)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Nil(t, item)
	})

	t.Run("ValidInputWithConfig", func(t *testing.T) {
		price, _ := decimal.NewFromString("100.50")
		validConfigInput := &domain.PaymentItemCreate{
			Name:            "Test Item with Config",
			Price:           price,
			Currency:        "USD",
			OrganizationID:  1,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
			Config: map[string]any{
				"paymentMethod": "credit_card",
				"allowPartial":  true,
				"settings": map[string]any{
					"showLogo":     true,
					"theme":        "dark",
					"redirectTime": 5,
				},
				"supportedCurrencies": []string{"USD", "TWD"},
			},
		}

		expectedItemWithConfig := &domain.PaymentItem{
			ID:              "test-item-id-3",
			Name:            validConfigInput.Name,
			Price:           validConfigInput.Price,
			Currency:        validConfigInput.Currency,
			OrganizationID:  validConfigInput.OrganizationID,
			ClientID:        validConfigInput.ClientID,
			OrderDataFields: validConfigInput.OrderDataFields,
			Config: map[string]any{
				"paymentMethod": "credit_card",
				"allowPartial":  true,
				"settings": map[string]any{
					"showLogo":     true,
					"theme":        "dark",
					"redirectTime": 5,
				},
				"supportedCurrencies": []string{"USD", "TWD"},
			},
		}

		// Mock check for existing item with config - return not found
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(validConfigInput.OrganizationID),
			gomock.Eq(validConfigInput.ClientID),
			gomock.Eq(validConfigInput.Name),
		).Return(nil, notFoundErr)

		mockRepo.EXPECT().CreatePaymentItem(gomock.Any(), gomock.Eq(validConfigInput)).Return(expectedItemWithConfig, nil)

		item, kgErr := CreatePaymentItem(ctx, validConfigInput)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItemWithConfig, item)
		assert.NotNil(t, item.Config)
		assert.Equal(t, "credit_card", item.Config["paymentMethod"])
		assert.Equal(t, true, item.Config["allowPartial"])
		assert.NotNil(t, item.Config["settings"])
	})

	t.Run("ValidInputWithPayToken", func(t *testing.T) {
		price, _ := decimal.NewFromString("100.50")
		validPayTokenInput := &domain.PaymentItemCreate{
			Name:            "Test Item with PayToken",
			Price:           price,
			Currency:        "USD",
			OrganizationID:  1,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
			PayToken:        util.Ptr("custom-pay-token-abc123"),
		}

		expectedItemWithPayToken := &domain.PaymentItem{
			ID:              "test-item-id-paytoken",
			Name:            validPayTokenInput.Name,
			Price:           validPayTokenInput.Price,
			Currency:        validPayTokenInput.Currency,
			OrganizationID:  validPayTokenInput.OrganizationID,
			ClientID:        validPayTokenInput.ClientID,
			OrderDataFields: validPayTokenInput.OrderDataFields,
			PayToken:        util.Ptr("custom-pay-token-abc123"),
		}

		// Mock check for existing item with pay_token - return not found
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(validPayTokenInput.OrganizationID),
			gomock.Eq(validPayTokenInput.ClientID),
			gomock.Eq(validPayTokenInput.Name),
		).Return(nil, notFoundErr)

		mockRepo.EXPECT().CreatePaymentItem(gomock.Any(), gomock.Eq(validPayTokenInput)).Return(expectedItemWithPayToken, nil)

		item, kgErr := CreatePaymentItem(ctx, validPayTokenInput)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItemWithPayToken, item)
		assert.NotNil(t, item.PayToken)
		assert.Equal(t, "custom-pay-token-abc123", *item.PayToken)
	})

	t.Run("ValidInputWithoutPayToken", func(t *testing.T) {
		price, _ := decimal.NewFromString("100.50")
		validInputNoPayToken := &domain.PaymentItemCreate{
			Name:            "Test Item without PayToken",
			Price:           price,
			Currency:        "TWD",
			OrganizationID:  1,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
			PayToken:        nil,
		}

		expectedItemNoPayToken := &domain.PaymentItem{
			ID:              "test-item-id-no-paytoken",
			Name:            validInputNoPayToken.Name,
			Price:           validInputNoPayToken.Price,
			Currency:        validInputNoPayToken.Currency,
			OrganizationID:  validInputNoPayToken.OrganizationID,
			ClientID:        validInputNoPayToken.ClientID,
			OrderDataFields: validInputNoPayToken.OrderDataFields,
			PayToken:        nil,
		}

		// Mock check for existing item without pay_token - return not found
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(validInputNoPayToken.OrganizationID),
			gomock.Eq(validInputNoPayToken.ClientID),
			gomock.Eq(validInputNoPayToken.Name),
		).Return(nil, notFoundErr)

		mockRepo.EXPECT().CreatePaymentItem(gomock.Any(), gomock.Eq(validInputNoPayToken)).Return(expectedItemNoPayToken, nil)

		item, kgErr := CreatePaymentItem(ctx, validInputNoPayToken)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItemNoPayToken, item)
		assert.Nil(t, item.PayToken)
	})
}

func TestGetPaymentItemByID(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	mockRepo := domain.NewMockPaymentItemRepo(ctrl)
	mockAppRepo := domain.NewMockApplicationRepo(ctrl)

	Init(mockRepo)
	application.Init(mockAppRepo)

	t.Run("ValidID", func(t *testing.T) {
		itemID := "valid-item-id"
		clientID := "test-client-id"
		price, _ := decimal.NewFromString("100.50")
		expectedItem := &domain.PaymentItem{
			ID:              itemID,
			Name:            "Test Item",
			Price:           price,
			Currency:        "USD",
			OrganizationID:  1,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
		}

		mockRepo.EXPECT().GetPaymentItemByID(gomock.Any(), gomock.Eq(itemID)).Return(expectedItem, nil)

		item, kgErr := GetPaymentItemByID(ctx, itemID)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItem, item)
		assert.Equal(t, clientID, item.ClientID)
	})

	t.Run("EmptyID", func(t *testing.T) {
		emptyID := ""

		item, kgErr := GetPaymentItemByID(ctx, emptyID)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Nil(t, item)
	})
}

func TestGetPaymentItems(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	mockRepo := domain.NewMockPaymentItemRepo(ctrl)
	mockAppRepo := domain.NewMockApplicationRepo(ctrl)

	Init(mockRepo)
	application.Init(mockAppRepo)

	t.Run("ValidParametersWithClientID", func(t *testing.T) {
		orgID := 1
		clientID := "test-client-id"
		page := 1
		pageSize := 10

		price1, _ := decimal.NewFromString("100.50")
		price2, _ := decimal.NewFromString("200.75")

		expectedItems := []*domain.PaymentItem{
			{
				ID:              "item-1",
				Name:            "Item 1",
				Price:           price1,
				Currency:        "USD",
				OrganizationID:  orgID,
				ClientID:        clientID,
				OrderDataFields: []domain.OrderDataField{},
			},
			{
				ID:              "item-2",
				Name:            "Item 2",
				Price:           price2,
				Currency:        "USD",
				OrganizationID:  orgID,
				ClientID:        clientID,
				OrderDataFields: []domain.OrderDataField{},
			},
		}
		expectedTotal := 2

		mockRepo.EXPECT().GetPaymentItems(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, params domain.GetPaymentItemsParams) ([]*domain.PaymentItem, int, *code.KGError) {
				assert.Equal(t, orgID, params.OrganizationID)
				assert.Equal(t, clientID, params.ClientID)
				assert.Equal(t, page, params.Page)
				assert.Equal(t, pageSize, params.PageSize)
				return expectedItems, expectedTotal, nil
			},
		)

		items, total, kgErr := GetPaymentItems(ctx, orgID, clientID, page, pageSize)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItems, items)
		assert.Equal(t, expectedTotal, total)
	})

	t.Run("ValidParametersWithoutClientID", func(t *testing.T) {
		orgID := 1
		emptyClientID := ""
		page := 1
		pageSize := 10

		price1, _ := decimal.NewFromString("100.50")
		price2, _ := decimal.NewFromString("200.75")

		expectedItems := []*domain.PaymentItem{
			{
				ID:              "item-1",
				Name:            "Item 1",
				Price:           price1,
				Currency:        "USD",
				OrganizationID:  orgID,
				ClientID:        "test-client-id",
				OrderDataFields: []domain.OrderDataField{},
			},
			{
				ID:              "item-2",
				Name:            "Item 2",
				Price:           price2,
				Currency:        "USD",
				OrganizationID:  orgID,
				ClientID:        "test-client-id",
				OrderDataFields: []domain.OrderDataField{},
			},
		}
		expectedTotal := 2

		mockRepo.EXPECT().GetPaymentItems(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, params domain.GetPaymentItemsParams) ([]*domain.PaymentItem, int, *code.KGError) {
				assert.Equal(t, orgID, params.OrganizationID)
				assert.Equal(t, emptyClientID, params.ClientID) // Should be empty string
				assert.Equal(t, page, params.Page)
				assert.Equal(t, pageSize, params.PageSize)
				return expectedItems, expectedTotal, nil
			},
		)

		items, total, kgErr := GetPaymentItems(ctx, orgID, emptyClientID, page, pageSize)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItems, items)
		assert.Equal(t, expectedTotal, total)
	})

	t.Run("InvalidOrganizationID", func(t *testing.T) {
		invalidOrgID := 0
		clientID := "test-client-id"
		page := 1
		pageSize := 10

		items, total, kgErr := GetPaymentItems(ctx, invalidOrgID, clientID, page, pageSize)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Nil(t, items)
		assert.Equal(t, 0, total)
	})

	t.Run("NegativePageAndPageSize", func(t *testing.T) {
		orgID := 1
		clientID := "test-client-id"

		price1, _ := decimal.NewFromString("100.50")
		price2, _ := decimal.NewFromString("200.75")

		expectedItems := []*domain.PaymentItem{
			{
				ID:              "item-1",
				Name:            "Item 1",
				Price:           price1,
				Currency:        "USD",
				OrganizationID:  orgID,
				ClientID:        clientID,
				OrderDataFields: []domain.OrderDataField{},
			},
			{
				ID:              "item-2",
				Name:            "Item 2",
				Price:           price2,
				Currency:        "USD",
				OrganizationID:  orgID,
				ClientID:        clientID,
				OrderDataFields: []domain.OrderDataField{},
			},
		}
		expectedTotal := 2

		mockRepo.EXPECT().GetPaymentItems(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, params domain.GetPaymentItemsParams) ([]*domain.PaymentItem, int, *code.KGError) {
				assert.Equal(t, orgID, params.OrganizationID)
				assert.Equal(t, clientID, params.ClientID)
				assert.Equal(t, 1, params.Page)      // Should be corrected to 1
				assert.Equal(t, 10, params.PageSize) // Should be corrected to 10
				return expectedItems, expectedTotal, nil
			},
		)

		items, total, kgErr := GetPaymentItems(ctx, orgID, clientID, -1, -5)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItems, items)
		assert.Equal(t, expectedTotal, total)
	})
}

func TestUpdatePaymentItem(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	mockRepo := domain.NewMockPaymentItemRepo(ctrl)
	mockAppRepo := domain.NewMockApplicationRepo(ctrl)

	Init(mockRepo)
	application.Init(mockAppRepo)

	// Common setup
	itemID := "valid-item-id"
	clientID := "test-client-id"
	orgID := 1

	// Set up mock for application repository
	mockOAuthApp := &domain.OAuthApplication{
		Application: domain.Application{
			ClientID: clientID,
			Name:     "Test Client",
		},
	}
	mockAppRepo.EXPECT().GetOAuthApplication(gomock.Any(), gomock.Eq(clientID)).Return(mockOAuthApp, nil).AnyTimes()
	mockAppRepo.EXPECT().GetApplicationOrgId(gomock.Any(), gomock.Eq(clientID)).Return(orgID, nil).AnyTimes()

	// For updating to new client ID
	newClientID := "new-client-id"
	mockOAuthAppNew := &domain.OAuthApplication{
		Application: domain.Application{
			ClientID: newClientID,
			Name:     "New Test Client",
		},
	}
	mockAppRepo.EXPECT().GetOAuthApplication(gomock.Any(), gomock.Eq(newClientID)).Return(mockOAuthAppNew, nil).AnyTimes()
	mockAppRepo.EXPECT().GetApplicationOrgId(gomock.Any(), gomock.Eq(newClientID)).Return(orgID, nil).AnyTimes()

	// Common existing item
	existingItem := &domain.PaymentItem{
		ID:              itemID,
		Name:            "Original Item",
		Price:           decimal.NewFromInt(100),
		Currency:        "USD",
		OrganizationID:  1,
		ClientID:        clientID,
		OrderDataFields: []domain.OrderDataField{},
	}

	// Common not found error
	notFoundErr := code.NewKGError(code.RecordNotFound, http.StatusNotFound, nil, nil)

	t.Run("ValidUpdate", func(t *testing.T) {
		newName := "Updated Item"
		newPrice, _ := decimal.NewFromString("150.75")

		validUpdate := &domain.PaymentItemUpdate{
			Name:  &newName,
			Price: &newPrice,
		}

		expectedUpdatedItem := &domain.PaymentItem{
			ID:              itemID,
			Name:            newName,
			Price:           newPrice,
			Currency:        "USD",
			OrganizationID:  1,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
		}

		// Mock GetPaymentItemByID for validation
		mockRepo.EXPECT().GetPaymentItemByID(gomock.Any(), gomock.Eq(itemID)).Return(existingItem, nil)

		// Mock check if name already exists - return not found if name is being changed
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(existingItem.OrganizationID),
			gomock.Eq(existingItem.ClientID),
			gomock.Eq(newName),
		).Return(nil, notFoundErr)

		mockRepo.EXPECT().UpdatePaymentItem(gomock.Any(), gomock.Eq(itemID), gomock.Eq(validUpdate)).
			Return(expectedUpdatedItem, nil)

		item, kgErr := UpdatePaymentItem(ctx, itemID, validUpdate)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedUpdatedItem, item)
		assert.Equal(t, clientID, item.ClientID)
	})

	t.Run("EmptyID", func(t *testing.T) {
		emptyID := ""
		newName := "Updated Item"
		validUpdate := &domain.PaymentItemUpdate{
			Name: &newName,
		}

		item, kgErr := UpdatePaymentItem(ctx, emptyID, validUpdate)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Nil(t, item)
	})

	t.Run("InvalidPrice", func(t *testing.T) {
		invalidPrice, _ := decimal.NewFromString("-10.50")
		invalidPriceUpdate := &domain.PaymentItemUpdate{
			Price: &invalidPrice,
		}

		// Mock GetPaymentItemByID for validation of invalid price update
		mockRepo.EXPECT().GetPaymentItemByID(gomock.Any(), gomock.Eq(itemID)).Return(existingItem, nil)

		item, kgErr := UpdatePaymentItem(ctx, itemID, invalidPriceUpdate)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Nil(t, item)
	})

	t.Run("InvalidCurrency", func(t *testing.T) {
		invalidCurrency := "EUR" // Not supported
		invalidCurrencyUpdate := &domain.PaymentItemUpdate{
			Currency: &invalidCurrency,
		}

		// Mock GetPaymentItemByID for validation of invalid currency update
		mockRepo.EXPECT().GetPaymentItemByID(gomock.Any(), gomock.Eq(itemID)).Return(existingItem, nil)

		item, kgErr := UpdatePaymentItem(ctx, itemID, invalidCurrencyUpdate)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Nil(t, item)
	})

	t.Run("ValidUpdateWithOrderDataFields", func(t *testing.T) {
		newPrice, _ := decimal.NewFromString("150.75")
		validFields := []domain.OrderDataField{
			{
				FieldName:  "customer_email",
				FieldLabel: "Customer Email",
				Required:   true,
				FieldType:  "email",
			},
		}

		validFieldsUpdate := &domain.PaymentItemUpdate{
			OrderDataFields: &validFields,
		}

		expectedItemWithFields := &domain.PaymentItem{
			ID:             itemID,
			Name:           "Test Item",
			Price:          newPrice,
			Currency:       "USD",
			OrganizationID: 1,
			ClientID:       clientID,
			OrderDataFields: []domain.OrderDataField{
				{
					FieldName:  "customer_email",
					FieldLabel: "Customer Email",
					Required:   true,
					FieldType:  "email",
				},
			},
		}

		// Mock GetPaymentItemByID for validation of valid fields update
		mockRepo.EXPECT().GetPaymentItemByID(gomock.Any(), gomock.Eq(itemID)).Return(existingItem, nil)

		mockRepo.EXPECT().UpdatePaymentItem(gomock.Any(), gomock.Eq(itemID), gomock.Eq(validFieldsUpdate)).
			Return(expectedItemWithFields, nil)

		item, kgErr := UpdatePaymentItem(ctx, itemID, validFieldsUpdate)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItemWithFields, item)
	})

	t.Run("InvalidOrderDataFields", func(t *testing.T) {
		invalidFields := []domain.OrderDataField{
			{
				FieldName:  "customer_email",
				FieldLabel: "", // Empty field label
				Required:   true,
				FieldType:  "email",
			},
		}

		invalidFieldsUpdate := &domain.PaymentItemUpdate{
			OrderDataFields: &invalidFields,
		}

		// Mock GetPaymentItemByID for validation of invalid fields update
		mockRepo.EXPECT().GetPaymentItemByID(gomock.Any(), gomock.Eq(itemID)).Return(existingItem, nil)

		item, kgErr := UpdatePaymentItem(ctx, itemID, invalidFieldsUpdate)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
		assert.Nil(t, item)
	})

	t.Run("ValidUpdateWithConfig", func(t *testing.T) {
		newPrice, _ := decimal.NewFromString("150.75")
		configMap := map[string]any{
			"paymentMethod": "crypto",
			"allowPartial":  false,
			"settings": map[string]any{
				"showLogo": false,
				"theme":    "light",
				"timeout":  30,
			},
			"supportedChains": []string{"ETH", "BSC", "Polygon"},
		}

		validConfigUpdate := &domain.PaymentItemUpdate{
			Config: &configMap,
		}

		expectedItemWithConfig := &domain.PaymentItem{
			ID:              itemID,
			Name:            "Test Item",
			Price:           newPrice,
			Currency:        "USD",
			OrganizationID:  1,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
			Config:          configMap,
		}

		// Mock GetPaymentItemByID for validation of valid config update
		mockRepo.EXPECT().GetPaymentItemByID(gomock.Any(), gomock.Eq(itemID)).Return(existingItem, nil)

		mockRepo.EXPECT().UpdatePaymentItem(gomock.Any(), gomock.Eq(itemID), gomock.Eq(validConfigUpdate)).
			Return(expectedItemWithConfig, nil)

		item, kgErr := UpdatePaymentItem(ctx, itemID, validConfigUpdate)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItemWithConfig, item)
		assert.NotNil(t, item.Config)
		assert.Equal(t, "crypto", item.Config["paymentMethod"])
		assert.Equal(t, false, item.Config["allowPartial"])
		assert.NotNil(t, item.Config["settings"])
		assert.Equal(t, "light", item.Config["settings"].(map[string]any)["theme"])
		assert.Equal(t, 3, len(item.Config["supportedChains"].([]string)))
	})

	t.Run("UpdateWithBothFieldsAndConfig", func(t *testing.T) {
		newPrice, _ := decimal.NewFromString("150.75")
		combinedUpdateFields := []domain.OrderDataField{
			{
				FieldName:  "wallet_address",
				FieldLabel: "Wallet Address",
				Required:   true,
				FieldType:  "text",
			},
		}

		combinedUpdateConfig := map[string]any{
			"paymentMethod": "crypto",
			"networkFee":    0.05,
		}

		combinedUpdate := &domain.PaymentItemUpdate{
			OrderDataFields: &combinedUpdateFields,
			Config:          &combinedUpdateConfig,
		}

		expectedCombinedItem := &domain.PaymentItem{
			ID:              itemID,
			Name:            "Test Item",
			Price:           newPrice,
			Currency:        "USD",
			OrganizationID:  1,
			ClientID:        clientID,
			OrderDataFields: combinedUpdateFields,
			Config:          combinedUpdateConfig,
		}

		// Mock GetPaymentItemByID for validation of combined update
		mockRepo.EXPECT().GetPaymentItemByID(gomock.Any(), gomock.Eq(itemID)).Return(existingItem, nil)

		mockRepo.EXPECT().UpdatePaymentItem(gomock.Any(), gomock.Eq(itemID), gomock.Eq(combinedUpdate)).
			Return(expectedCombinedItem, nil)

		item, kgErr := UpdatePaymentItem(ctx, itemID, combinedUpdate)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedCombinedItem, item)
		assert.NotNil(t, item.Config)
		assert.Equal(t, "crypto", item.Config["paymentMethod"])
		assert.Equal(t, 0.05, item.Config["networkFee"])
		assert.Equal(t, 1, len(item.OrderDataFields))
		assert.Equal(t, "wallet_address", item.OrderDataFields[0].FieldName)
	})

	t.Run("UpdatePayTokenOnly", func(t *testing.T) {
		newPayToken := "updated-service-pay-token-xyz"
		payTokenUpdate := &domain.PaymentItemUpdate{
			PayToken: &newPayToken,
		}

		expectedItemWithPayToken := &domain.PaymentItem{
			ID:              itemID,
			Name:            "Test Item",
			Price:           decimal.NewFromInt(100),
			Currency:        "USD",
			OrganizationID:  1,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
			PayToken:        &newPayToken,
		}

		// Mock GetPaymentItemByID for validation of pay_token update
		mockRepo.EXPECT().GetPaymentItemByID(gomock.Any(), gomock.Eq(itemID)).Return(existingItem, nil)

		mockRepo.EXPECT().UpdatePaymentItem(gomock.Any(), gomock.Eq(itemID), gomock.Eq(payTokenUpdate)).
			Return(expectedItemWithPayToken, nil)

		item, kgErr := UpdatePaymentItem(ctx, itemID, payTokenUpdate)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItemWithPayToken, item)
		assert.NotNil(t, item.PayToken)
		assert.Equal(t, "updated-service-pay-token-xyz", *item.PayToken)
	})

	t.Run("UpdatePayTokenToEmpty", func(t *testing.T) {
		emptyPayToken := ""
		emptyPayTokenUpdate := &domain.PaymentItemUpdate{
			PayToken: &emptyPayToken,
		}

		expectedItemWithEmptyPayToken := &domain.PaymentItem{
			ID:              itemID,
			Name:            "Test Item",
			Price:           decimal.NewFromInt(100),
			Currency:        "USD",
			OrganizationID:  1,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
			PayToken:        &emptyPayToken,
		}

		// Mock GetPaymentItemByID for validation of empty pay_token update
		mockRepo.EXPECT().GetPaymentItemByID(gomock.Any(), gomock.Eq(itemID)).Return(existingItem, nil)

		mockRepo.EXPECT().UpdatePaymentItem(gomock.Any(), gomock.Eq(itemID), gomock.Eq(emptyPayTokenUpdate)).
			Return(expectedItemWithEmptyPayToken, nil)

		item, kgErr := UpdatePaymentItem(ctx, itemID, emptyPayTokenUpdate)
		assert.Nil(t, kgErr)
		assert.Equal(t, expectedItemWithEmptyPayToken, item)
		assert.NotNil(t, item.PayToken)
		assert.Equal(t, "", *item.PayToken)
	})
}

func TestDeletePaymentItem(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	mockRepo := domain.NewMockPaymentItemRepo(ctrl)
	mockAppRepo := domain.NewMockApplicationRepo(ctrl)

	Init(mockRepo)
	application.Init(mockAppRepo)

	t.Run("ValidID", func(t *testing.T) {
		itemID := "valid-item-id"

		mockRepo.EXPECT().DeletePaymentItem(gomock.Any(), gomock.Eq(itemID)).Return(nil)

		kgErr := DeletePaymentItem(ctx, itemID)
		assert.Nil(t, kgErr)
	})

	t.Run("EmptyID", func(t *testing.T) {
		emptyID := ""

		kgErr := DeletePaymentItem(ctx, emptyID)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.ParamIncorrect, kgErr.Code)
	})
}

func TestGetPaymentItemByOrgClientName(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	mockRepo := domain.NewMockPaymentItemRepo(ctrl)
	mockAppRepo := domain.NewMockApplicationRepo(ctrl)

	Init(mockRepo)
	application.Init(mockAppRepo)

	// Common setup
	orgID := 100
	clientID := "test-client-id"
	name := "Test Item"
	price, _ := decimal.NewFromString("99.99")

	// Mock the client service for validation
	mockOAuthApp := &domain.OAuthApplication{
		Application: domain.Application{
			ClientID: clientID,
			Name:     "Test Client",
		},
	}
	mockAppRepo.EXPECT().GetOAuthApplication(gomock.Any(), gomock.Eq(clientID)).Return(mockOAuthApp, nil).AnyTimes()
	mockAppRepo.EXPECT().GetApplicationOrgId(gomock.Any(), gomock.Eq(clientID)).Return(orgID, nil).AnyTimes()

	// Common not found error
	notFoundErr := code.NewKGError(code.RecordNotFound, http.StatusNotFound, nil, nil)

	t.Run("FoundItemCreateDuplicateNameError", func(t *testing.T) {
		expectedItem := &domain.PaymentItem{
			ID:              "test-item-id",
			Name:            name,
			Price:           price,
			Currency:        "USD",
			OrganizationID:  orgID,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
		}

		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(orgID),
			gomock.Eq(clientID),
			gomock.Eq(name),
		).Return(expectedItem, nil)

		// We'll use CreatePaymentItem since it calls GetPaymentItemByOrgClientName to check for duplicates
		createInput := &domain.PaymentItemCreate{
			Name:            name,
			Price:           price,
			Currency:        "USD",
			OrganizationID:  orgID,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
		}

		// We expect this to fail with a duplicate name error since our mock returns an existing item
		_, kgErr := CreatePaymentItem(ctx, createInput)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.PaymentItemNameAlreadyExists, kgErr.Code)
	})

	t.Run("NotFoundItemCreateSuccess", func(t *testing.T) {
		anotherName := "Non-Existent Item"

		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(orgID),
			gomock.Eq(clientID),
			gomock.Eq(anotherName),
		).Return(nil, notFoundErr)

		// Now let's create a new item with a different name
		createInput := &domain.PaymentItemCreate{
			Name:            anotherName,
			Price:           price,
			Currency:        "USD",
			OrganizationID:  orgID,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
		}

		// Mock the repository to return the created item
		expectedCreatedItem := &domain.PaymentItem{
			ID:              "new-item-id",
			Name:            anotherName,
			Price:           price,
			Currency:        "USD",
			OrganizationID:  orgID,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
		}
		mockRepo.EXPECT().CreatePaymentItem(gomock.Any(), gomock.Eq(createInput)).Return(expectedCreatedItem, nil)

		// This should succeed since the item doesn't exist
		createdItem, kgErr := CreatePaymentItem(ctx, createInput)
		assert.Nil(t, kgErr)
		assert.NotNil(t, createdItem)
		assert.Equal(t, anotherName, createdItem.Name)
	})

	t.Run("DatabaseError", func(t *testing.T) {
		errorName := "Error Item"
		dbError := code.NewKGError(code.DBError, http.StatusInternalServerError, nil, nil)

		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(orgID),
			gomock.Eq(clientID),
			gomock.Eq(errorName),
		).Return(nil, dbError)

		// Try to create an item that causes a DB error during existence check
		createInput := &domain.PaymentItemCreate{
			Name:            errorName,
			Price:           price,
			Currency:        "USD",
			OrganizationID:  orgID,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
		}

		_, kgErr := CreatePaymentItem(ctx, createInput)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.DBError, kgErr.Code)
	})

	t.Run("UpdateWithNewNameSuccess", func(t *testing.T) {
		existingID := "existing-item-id"
		existingItem := &domain.PaymentItem{
			ID:              existingID,
			Name:            "Original Name",
			Price:           price,
			Currency:        "USD",
			OrganizationID:  orgID,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
		}

		newName := "New Name"
		update := &domain.PaymentItemUpdate{
			Name: &newName,
		}

		// Mock getting the existing item
		mockRepo.EXPECT().GetPaymentItemByID(
			gomock.Any(),
			gomock.Eq(existingID),
		).Return(existingItem, nil)

		// Mock checking for collision with the new name
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(orgID),
			gomock.Eq(clientID),
			gomock.Eq(newName),
		).Return(nil, notFoundErr)

		// Mock the update operation
		updatedItem := &domain.PaymentItem{
			ID:              existingID,
			Name:            newName,
			Price:           price,
			Currency:        "USD",
			OrganizationID:  orgID,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
		}
		mockRepo.EXPECT().UpdatePaymentItem(
			gomock.Any(),
			gomock.Eq(existingID),
			gomock.Eq(update),
		).Return(updatedItem, nil)

		// Update should succeed
		result, kgErr := UpdatePaymentItem(ctx, existingID, update)
		assert.Nil(t, kgErr)
		assert.NotNil(t, result)
		assert.Equal(t, newName, result.Name)
	})

	t.Run("UpdateWithNameCollisionFailure", func(t *testing.T) {
		existingID := "existing-item-id"
		existingItem := &domain.PaymentItem{
			ID:              existingID,
			Name:            "Original Name",
			Price:           price,
			Currency:        "USD",
			OrganizationID:  orgID,
			ClientID:        clientID,
			OrderDataFields: []domain.OrderDataField{},
		}

		collidingName := "Colliding Name"
		collidingUpdate := &domain.PaymentItemUpdate{
			Name: &collidingName,
		}

		// Mock getting the existing item again
		mockRepo.EXPECT().GetPaymentItemByID(
			gomock.Any(),
			gomock.Eq(existingID),
		).Return(existingItem, nil)

		// Mock finding an existing item with the same name
		collidingItem := &domain.PaymentItem{
			ID:             "other-item-id", // Different ID
			Name:           collidingName,
			OrganizationID: orgID,
			ClientID:       clientID,
		}
		mockRepo.EXPECT().GetPaymentItemByOrgClientName(
			gomock.Any(),
			gomock.Eq(orgID),
			gomock.Eq(clientID),
			gomock.Eq(collidingName),
		).Return(collidingItem, nil)

		// Update should fail with name collision error
		_, kgErr := UpdatePaymentItem(ctx, existingID, collidingUpdate)
		assert.NotNil(t, kgErr)
		assert.Equal(t, code.PaymentItemNameAlreadyExists, kgErr.Code)
	})
}
