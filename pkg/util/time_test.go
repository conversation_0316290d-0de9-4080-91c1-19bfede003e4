package util

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestPrevSunday(t *testing.T) {
	prevSunday := time.Date(2022, 12, 4, 0, 0, 0, 0, time.UTC)
	testcases := []struct {
		desc     string
		t        time.Time
		expected time.Time
	}{
		{"test - 1", prevSunday, prevSunday},
		{"test - 2", prevSunday.AddDate(0, 0, 1), prevSunday},
		{"test - 3", prevSunday.AddDate(0, 0, 2), prevSunday},
		{"test - 4", prevSunday.AddDate(0, 0, 3), prevSunday},
		{"test - 5", prevSunday.AddDate(0, 0, 4), prevSunday},
		{"test - 6", prevSunday.AddDate(0, 0, 5), prevSunday},
		{"test - 7", prevSunday.AddDate(0, 0, 6), prevSunday},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.<PERSON>()
			assert.Equal(t, tc.expected, PrevSunday(tc.t))
		})
	}
}

func TestUnixMilliToTime(t *testing.T) {
	testcases := []struct {
		desc     string
		milli    int64
		expected time.Time
	}{
		{"test - 1", 1638496800000, time.Date(2021, 12, 3, 2, 0, 0, 0, time.UTC)},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected.UnixMilli(), UnixMilliToTime(tc.milli).UnixMilli())
		})
	}
}

func TestUnixSecondToTime(t *testing.T) {
	testcases := []struct {
		desc     string
		milli    int64
		expected time.Time
	}{
		{"test - 1", 1638496800, time.Date(2021, 12, 3, 2, 0, 0, 0, time.UTC)},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected.Unix(), UnixSecondToTime(tc.milli).Unix())
		})
	}
}

func TestUnixSecondToCSTTime(t *testing.T) {
	testcases := []struct {
		desc     string
		milli    int64
		expected time.Time
	}{
		{"test - 1", 1638496800, time.Date(2021, 12, 3, 2, 0, 0, 0, time.UTC)},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected.Unix(), UnixSecondToCSTTime(tc.milli).Unix())
		})
	}
}
