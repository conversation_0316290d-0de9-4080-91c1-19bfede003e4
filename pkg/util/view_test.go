package util

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestShortenNumber(t *testing.T) {
	testcases := []struct {
		desc     string
		original string
		expected string
	}{
		// case1 - < 0.0000005
		{"#1", "0.00000049", "< 0.000001"},

		// case2 - >= 0.0000005
		{"#2", "0.0000005", "≈ 0.000001"},
		{"#3", "0.99999949", "≈ 0.999999"},
		{"#4", "0.9999995", "≈ 1"},
		{"#5", "0.000005", "0.000005"},
		{"#6", "0.00005", "0.00005"},

		// case3 - large numbers
		{"#7", "912345678", "912,345,678"},
		{"#8", "1912345678", "191234567..."},
		{"#9", "1912345678.9", "191234567..."},

		// case 4 - mixed
		{"#10", "123456789.00000049", "≈ 123456789"},
		{"#11", "123456789.0000005", "123456789..."},
		{"#12", "912345678.99999949", "912345678..."},
		{"#13", "912345678.9999995", "≈ 912345679"},
		{"#14", "912345678.900005", "912345678..."},
		{"#15", "91234567.890005", "91234567.8..."},
		{"#16", "9123456.789005", "9123456.78..."},
		{"#17", "912345.678905", "912345.678..."},
		{"#18", "91234.567895", "91234.5678..."},
		{"#19", "9123.456789", "9123.45678..."},
		{"#20", "912.345678", "912.345678"},
		{"#21", "91.234567", "91.234567"},
		{"#22", "9.123456", "9.123456"},

		// case 5 - mixed
		{"#23", "999999999.99999949", "999999999..."},
		{"#24", "1999999999.9999995", "200000000..."},

		// case 6 - normal numbers
		{"#25", "1234567", "1,234,567"},
		{"#26", "1234567.12", "1,234,567.12"},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run("TestShortenNumber"+tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected, ShortenNumber(tc.original))
		})
	}
}
