package util_test

import (
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestDomainToURL(t *testing.T) {
	s := assert.New(t)

	s.Equal("https://kryptogo.com", util.DomainToURL("kryptogo.com"))
	s.Equal("https://kryptogo.com", util.DomainToURL("http://kryptogo.com"))
	s.Equal("https://kryptogo.com", util.DomainToURL("https://kryptogo.com"))
}
