package util

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestToSupportLocale(t *testing.T) {
	testcases := []struct {
		desc     string
		locale   string
		expected string
	}{
		{"test - 1", "en_TW", "en_US"},
		{"test - 2", "zh_Hant_TW", "zh_TW"},
		{"test - 3", "zh_Hans_CN", "zh_CN"},
		{"test - 4", "ja_<PERSON>", "en_US"},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.<PERSON>()
			assert.Equal(t, tc.expected, ToSupportedLocale(tc.locale))
		})
	}
}

func TestGetRegionLocaleFromPhoneNumber(t *testing.T) {
	testcases := []struct {
		desc     string
		phone    string
		expected string
	}{
		{"test - 1", "+886912345678", "zh_TW"},
		{"test - 2", "+8612345678901", "CN"},
		{"test - 3", "+14155552671", "US"},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected, GetRegionLocaleFromPhoneNumber(tc.phone))
		})
	}
}

func TestLocaleByPhoneNumber(t *testing.T) {
	testcases := []struct {
		desc     string
		phone    string
		expected string
	}{
		{"Taiwan - correct", "+886912345678", "zh_TW"},
		{"China - correct", "+8612345678901", "zh_CN"},
		{"US - correct", "+14155552671", "en_US"},
		{"Japan - correct", "+819069649296", "en_US"},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected, LocaleByPhoneNumber(tc.phone))
		})
	}
}
