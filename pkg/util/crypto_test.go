package util

import (
	"fmt"
	"strconv"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHmacSha256(t *testing.T) {
	type args struct {
		data   string
		secret string
	}
	timestamp := int64(1620000000000)
	tStr := strconv.FormatInt(timestamp, 10)
	fmt.Println(tStr)
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test",
			args: args{
				data:   "recvWindow=20000&type=SPOT&timestamp=" + tStr,
				secret: "testingsecret",
			},
			want: "01ec17d3e28939f199a1edeb58698aecea6766762543d4a18a9191c4f01cabe7",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := HmacSha256(tt.args.data, tt.args.secret); got != tt.want {
				t.Errorf("HmacSha256() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBcryptPassword(t *testing.T) {
	password := "testingpassword"
	wrongPassword := "wrongpassword"
	hash, err := GenerateBcryptPassword(password)
	assert.Nil(t, err)
	err = CompareBcryptPassword(string(hash), password)
	assert.Nil(t, err)
	err = CompareBcryptPassword(string(hash), wrongPassword)
	assert.NotNil(t, err)
}

func TestArgonPassword(t *testing.T) {
	password := "testingpassword"
	wrongPassword := "wrongpassword"
	hash, salt, err := GenerateArgonPassword(password)
	assert.Nil(t, err)
	matched, err := CompareArgonPassword(password, hash, salt)
	assert.Nil(t, err)
	assert.True(t, matched)
	matched, err = CompareArgonPassword(wrongPassword, hash, salt)
	assert.Nil(t, err)
	assert.False(t, matched)
}
