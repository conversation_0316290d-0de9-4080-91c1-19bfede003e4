package util

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsEmailValid(t *testing.T) {
	testcases := []struct {
		desc     string
		email    string
		expected bool
	}{
		{"test - valid address", "<EMAIL>", true},
		{"test - invalid address", "123", false},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected, IsEmailValid(tc.email))
		})
	}
}

func TestIsEmailMatch(t *testing.T) {
	expectedEmail := "<EMAIL>"
	testcases := []struct {
		desc        string
		actualEmail string
		expected    bool
	}{
		{"test - exactly match", "<EMAIL>", true},
		{"test - match with prefix", "<EMAIL>", true},
		{"test - match with prefix only", "<EMAIL>", false},
		{"test - wrong host", "<EMAIL>", false},
		{"test - wrong username", "<EMAIL>", false},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected, IsEmailMatch(tc.actualEmail, expectedEmail))
		})
	}
}
