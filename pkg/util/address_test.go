package util

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsZeroAddress(t *testing.T) {
	testcases := []struct {
		desc     string
		address  string
		expected bool
	}{
		{"test - 1", "0x0", true},
		{"test - 2", "0x0000000000000000000000000000000000000000", true},
		{"test - 3", "0x0000000000000000000000000000000000000001", false},
		{"test - 4", "1x0", false},
		{"test - 5", "0x1", false},
		{"test - 6", "", true},
		{"test - 7", "0x", true},
		{"test - 8", "00", true},
		{"test - 9", "01", false},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected, IsZeroAddress(tc.address))
		})
	}
}

// TestToChecksumAddress tests ToChecksumAddress
func TestToChecksumAddress(t *testing.T) {
	address := "0x0b3f868e0be5597d5db7feb59e1cadbb0fdda50a"
	expected := "0x0b3F868E0BE5597D5DB7fEB59E1CADBb0fdDa50a"
	actual, err := ToChecksumAddress(address)
	assert.NoError(t, err)
	assert.Equal(t, expected, actual)
}
