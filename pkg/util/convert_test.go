package util

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDecToHexWithPrefix(t *testing.T) {
	assert.Equal(t, "0x1a9d493", DecToHexWithPrefix(27907219))
}

func TestFloat64ToString(t *testing.T) {
	testcases := []struct {
		desc       string
		floatValue float64
		expected   string
	}{
		{"test - 100", 100, "100"},
		{"test - 10", 10, "10"},
		{"test - 1", 1, "1"},
		{"test - 0.1", 0.1, "0.1"},
		{"test - 0.01", 0.01, "0.01"},
		{"test - 0.001", 0.001, "0.001"},
		{"test - 0.0001", 0.0001, "0.0001"},
		{"test - 0.00001", 0.00001, "0.00001"},
		{"test - 0.000001", 0.000001, "0.000001"},
		{"test - 0.0000001", 0.0000001, "0.0000001"},
		{"test - 0.00000001", 0.00000001, "0.00000001"},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected, Float64ToString(tc.floatValue))
		})
	}
}

func TestTrimTrailingZero(t *testing.T) {
	testcases := []struct {
		desc     string
		original string
		expected string
	}{
		{"test - 1", "1000", "1000"},
		{"test - 2", "1000.", "1000"},
		{"test - 3", "1000.13", "1000.13"},
		{"test - 4", "1000.1300", "1000.13"},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected, TrimTrailingZero(tc.original))
		})
	}
}

func TestStringFloat64DecToBalance(t *testing.T) {
	testcases := []struct {
		desc     string
		value    string
		decimals int
		expected string
	}{
		{"test - 1", "277329997283527909671", 18, "277.329997283527909671"},
		{"test - 2", "123456", 6, "0.123456"},
		{"test - 3", "123456", 18, "0.000000000000123456"},
		{"test - 4", "123456000", 6, "123.456"},
		{"test - 5", "12345600000000", 6, "12345600"},
		{"test - 6", "123456000000", 6, "123456"},
		{"test - 7", "123456000000", 18, "0.000000123456"},
		{"test - 8", "500000000000000000", 18, "0.5"},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected, StringFloat64DecToBalance(tc.value, tc.decimals))
		})
	}
}

func TestHexIncreaseBy(t *testing.T) {
	testcases := []struct {
		desc      string
		hex       string
		increment int
		expected  string
	}{
		{"test - 1", "0x2222933", 1, "0x2222934"},
		{"test - 2", "0x222293e", -1, "0x222293d"},
	}

	for _, tc := range testcases {
		tc := tc
		t.Run(tc.desc, func(t *testing.T) {
			t.Parallel()
			assert.Equal(t, tc.expected, HexIncreaseBy(tc.hex, int64(tc.increment)))
		})
	}
}
