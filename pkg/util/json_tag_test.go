package util

import (
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// TestIsEmptyValue tests isEmptyValue
func TestIsEmptyValue(t *testing.T) {
	emptyS := ""
	emptyI := 0
	emptyF := 0.0
	emptyB := false
	emptyT := time.Time{}

	assert.True(t, isEmptyValue(reflect.ValueOf(emptyS)))
	assert.True(t, isEmptyValue(reflect.ValueOf(emptyI)))
	assert.True(t, isEmptyValue(reflect.ValueOf(emptyF)))
	assert.True(t, isEmptyValue(reflect.ValueOf(emptyB)))
	assert.True(t, isEmptyValue(reflect.ValueOf(emptyT)))
}

// TestGetGormTagsIfNotEmpty tests GetGormTagsIfNotEmpty
func TestGetGormTagsIfNotEmpty(t *testing.T) {
	type project struct {
		ProjectID             int           `gorm:"column:project_id;type:integer auto_increment;primaryKey" json:"project_id"`
		Status                ProjectStatus `gorm:"column:status;type:varchar(10);not null" json:"status"`
		CollectionImageURL    string        `gorm:"column:collection_image_url;type:text" json:"collection_image_url"`
		CollectionName        string        `gorm:"column:collection_name;type:text" json:"collection_name"`
		SymbolName            string        `gorm:"column:symbol_name;type:text" json:"symbol_name"`
		CollectionDescription string        `gorm:"column:collection_description;type:text" json:"collection_description"`
		BannerImageURL        string        `gorm:"column:banner_image_url;type:text" json:"banner_image_url"`
		ContractSchemaName    string        `gorm:"column:contract_schema_name;type:text" json:"contract_schema_name"`
		MaxSupply             int           `gorm:"column:max_supply;type:integer" json:"max_supply"`
		StartTime             time.Time     `gorm:"column:start_time;type:datetime" json:"start_time"`
		EndTime               *time.Time    `gorm:"column:end_time;type:datetime" json:"end_time"`
		Title                 string        `gorm:"column:title;type:text" json:"title"`
		Subtitle              string        `gorm:"column:subtitle;type:text" json:"subtitle"`
		FaviconImageURL       string        `gorm:"column:favicon_image_url;type:text" json:"favicon_image_url"`
		MsgContent            string        `gorm:"column:msg_content;type:text" json:"msg_content"`
	}

	p := project{
		ProjectID:      1,
		Status:         ProjectStatusDraft,
		CollectionName: "test",
		StartTime:      time.Now(),
	}
	tags := GetGormTagsIfNotEmpty(&p)
	fmt.Println("tags", tags)
	assert.ElementsMatch(t, []string{"project_id", "status", "collection_name", "start_time"}, tags)

}

type ProjectStatus string

const (
	ProjectStatusDraft   ProjectStatus = "draft"
	ProjectStatusPublish ProjectStatus = "publish"
)
