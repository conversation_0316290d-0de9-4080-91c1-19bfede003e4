package util

import (
	"fmt"
	"math/big"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestToWei(t *testing.T) {
	tests := []struct {
		input    decimal.Decimal
		decimals int
		expected *big.Int
	}{
		{decimal.NewFromFloat(1.23), 18, big.NewInt(1230000000000000000)},
		{decimal.NewFromFloat(0), 18, big.NewInt(0)},
		{decimal.NewFromFloat(1), 18, big.NewInt(1000000000000000000)},
		{decimal.NewFromFloat(1.23), 6, big.NewInt(1230000)},
	}

	for _, test := range tests {
		result := ToWei(test.input, test.decimals)
		if result.Cmp(test.expected) != 0 {
			t.Errorf("ToWei(%s, %d) = %s; want %s", test.input, test.decimals, result, test.expected)
		}
	}
}

func TestIsValidContract(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY"})

	// polygon
	{
		apiKey := config.GetString("ALCHEMY_API_KEY")
		rpcUrl := fmt.Sprintf("https://polygon-mainnet.g.alchemyapi.io/v2/%s", apiKey)
		contractAddress := "******************************************"
		assert.True(t, IsValidContract(contractAddress, rpcUrl))

		WalletAddress := "******************************************"
		assert.False(t, IsValidContract(WalletAddress, rpcUrl))
	}

	// sepolia
	{
		rpcUrl := "https://ethereum-sepolia.blockpi.network/v1/rpc/public"

		contractAddress := "******************************************"
		assert.True(t, IsValidContract(contractAddress, rpcUrl))

		WalletAddress := "******************************************"
		assert.False(t, IsValidContract(WalletAddress, rpcUrl))

	}
}
