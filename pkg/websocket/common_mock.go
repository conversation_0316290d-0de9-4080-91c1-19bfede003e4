// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/websocket (interfaces: IService)
//
// Generated by this command:
//
//	mockgen -package=websocket -self_package=github.com/kryptogo/kg-wallet-backend/pkg/websocket -destination=common_mock.go . IService
//

// Package websocket is a generated GoMock package.
package websocket

import (
	context "context"
	http "net/http"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIService is a mock of IService interface.
type MockIService struct {
	ctrl     *gomock.Controller
	recorder *MockIServiceMockRecorder
}

// MockIServiceMockRecorder is the mock recorder for MockIService.
type MockIServiceMockRecorder struct {
	mock *MockIService
}

// NewMockIService creates a new mock instance.
func NewMockIService(ctrl *gomock.Controller) *MockIService {
	mock := &MockIService{ctrl: ctrl}
	mock.recorder = &MockIServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIService) EXPECT() *MockIServiceMockRecorder {
	return m.recorder
}

// DisconnectWsOnTermination mocks base method.
func (m *MockIService) DisconnectWsOnTermination() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "DisconnectWsOnTermination")
}

// DisconnectWsOnTermination indicates an expected call of DisconnectWsOnTermination.
func (mr *MockIServiceMockRecorder) DisconnectWsOnTermination() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DisconnectWsOnTermination", reflect.TypeOf((*MockIService)(nil).DisconnectWsOnTermination))
}

// GetWsEventNotificationChannel mocks base method.
func (m *MockIService) GetWsEventNotificationChannel() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWsEventNotificationChannel")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetWsEventNotificationChannel indicates an expected call of GetWsEventNotificationChannel.
func (mr *MockIServiceMockRecorder) GetWsEventNotificationChannel() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWsEventNotificationChannel", reflect.TypeOf((*MockIService)(nil).GetWsEventNotificationChannel))
}

// HandleRequest mocks base method.
func (m *MockIService) HandleRequest(arg0 http.ResponseWriter, arg1 *http.Request) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HandleRequest", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// HandleRequest indicates an expected call of HandleRequest.
func (mr *MockIServiceMockRecorder) HandleRequest(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleRequest", reflect.TypeOf((*MockIService)(nil).HandleRequest), arg0, arg1)
}

// SendEventByAddresses mocks base method.
func (m *MockIService) SendEventByAddresses(arg0 context.Context, arg1 []string, arg2 WsEventName, arg3 any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEventByAddresses", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendEventByAddresses indicates an expected call of SendEventByAddresses.
func (mr *MockIServiceMockRecorder) SendEventByAddresses(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEventByAddresses", reflect.TypeOf((*MockIService)(nil).SendEventByAddresses), arg0, arg1, arg2, arg3)
}

// SendEventByUIDs mocks base method.
func (m *MockIService) SendEventByUIDs(arg0 context.Context, arg1 []string, arg2 WsEventName, arg3 any) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEventByUIDs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendEventByUIDs indicates an expected call of SendEventByUIDs.
func (mr *MockIServiceMockRecorder) SendEventByUIDs(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEventByUIDs", reflect.TypeOf((*MockIService)(nil).SendEventByUIDs), arg0, arg1, arg2, arg3)
}
