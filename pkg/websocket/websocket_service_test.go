package websocket_test

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	ws "github.com/kryptogo/kg-wallet-backend/pkg/websocket"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestSendEventByAddresses(t *testing.T) {
	ctx := context.Background()

	address1 := "TAFD6ZsENuFX5MW6ZPqkkpKNPknArq3iii" // Owned by uid1

	ctrl := gomock.NewController(t)
	mockRepo := domain.NewMockWebsocketRepo(ctrl)
	mockRepo.EXPECT().GetUIDsByAddresses(gomock.Any(), gomock.Any()).AnyTimes().Return([]string{"uid1"}, nil)
	ws.Init(mockRepo)

	// Should receive the event after 3 seconds
	{
		startTime := time.Now()

		// Call SendEventByAddresses
		err := ws.Get().SendEventByAddresses(ctx, []string{address1}, "TestSendEventByAddresses_test_event", nil)
		assert.Nil(t, err)

		wsEventSubscription := cache.Subscribe(ws.Get().GetWsEventNotificationChannel())
		defer wsEventSubscription.Close()

		ch := wsEventSubscription.Channel()
		eventReceived := false
		// Wait for at most 10 seconds for the event to be received
		timeout := time.After(10 * time.Second)
		for !eventReceived {
			select {
			case msg := <-ch:
				var event ws.EventNotification
				err := json.Unmarshal([]byte(msg.Payload), &event)
				assert.Nil(t, err)
				t.Logf("event: %+v", event)
				if event.Event == "TestSendEventByAddresses_test_event" {
					assert.True(t, time.Since(startTime) > 3*time.Second)
					eventReceived = true
				}
			case <-timeout:
				assert.Fail(t, "timeout waiting for event")
				return
			}
		}
		assert.True(t, eventReceived)
	}

	// If same event arrives 2 seconds later than first event, we should only receive the event once after 5 seconds
	{
		startTime := time.Now()

		// Call SendEventByAddresses and after 2 seconds, call it again
		err := ws.Get().SendEventByAddresses(ctx, []string{address1}, "TestSendEventByAddresses_test_event", nil)
		assert.Nil(t, err)
		time.Sleep(2 * time.Second)
		err = ws.Get().SendEventByAddresses(ctx, []string{address1}, "TestSendEventByAddresses_test_event", nil)
		assert.Nil(t, err)

		wsEventSubscription := cache.Subscribe(ws.Get().GetWsEventNotificationChannel())
		defer wsEventSubscription.Close()

		ch := wsEventSubscription.Channel()
		// Wait for at most 10 seconds for the event to be received
		eventReceivedCount := 0
		timeReached := false
		timeout := time.After(10 * time.Second)
		for !timeReached {
			select {
			case msg := <-ch:
				var event ws.EventNotification
				err := json.Unmarshal([]byte(msg.Payload), &event)
				assert.Nil(t, err)
				t.Logf("event: %+v", event)
				if event.Event == "TestSendEventByAddresses_test_event" {
					assert.True(t, time.Since(startTime) >= 5*time.Second)
					eventReceivedCount++
				}
			case <-timeout:
				timeReached = true
			}
		}
		assert.Equal(t, 1, eventReceivedCount)
	}

	// Different events should not interfere with each other
	{
		startTime := time.Now()

		// Call SendEventByAddresses
		err := ws.Get().SendEventByAddresses(ctx, []string{address1}, "TestSendEventByAddresses_test_event", nil)
		assert.Nil(t, err)
		time.Sleep(2 * time.Second)
		err = ws.Get().SendEventByAddresses(ctx, []string{address1}, "TestSendEventByAddresses_test_event_2", nil)
		assert.Nil(t, err)

		wsEventSubscription := cache.Subscribe(ws.Get().GetWsEventNotificationChannel())
		defer wsEventSubscription.Close()

		ch := wsEventSubscription.Channel()
		eventReceived := []bool{false, false}
		// Wait for at most 10 seconds for the event to be received
		timeout := time.After(10 * time.Second)
		for !eventReceived[0] || !eventReceived[1] {
			select {
			case msg := <-ch:
				var event ws.EventNotification
				err := json.Unmarshal([]byte(msg.Payload), &event)
				assert.Nil(t, err)
				t.Logf("event: %+v", event)
				switch event.Event {
				case "TestSendEventByAddresses_test_event":
					assert.True(t, time.Since(startTime) >= 3*time.Second)
					assert.True(t, time.Since(startTime) <= 5*time.Second)
					eventReceived[0] = true
				case "TestSendEventByAddresses_test_event_2":
					assert.True(t, time.Since(startTime) >= 5*time.Second)
					assert.True(t, time.Since(startTime) <= 7*time.Second)
					eventReceived[1] = true
				}
			case <-timeout:
				assert.Fail(t, "timeout waiting for event")
				return
			}
		}
		assert.True(t, eventReceived[0])
		assert.True(t, eventReceived[1])
	}
}

func TestSendEventByAddresses_MultipleUIDs(t *testing.T) {
	ctx := context.Background()

	address := "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn" // Shared by uid1 and uid2

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockWebsocketRepo(ctrl)
	mockRepo.EXPECT().GetUIDsByAddresses(gomock.Any(), gomock.Any()).Return([]string{"uid1", "uid2"}, nil).AnyTimes()
	ws.Init(mockRepo)

	// Call SendEventByAddresses
	err := ws.Get().SendEventByAddresses(ctx, []string{address}, "TestEvent_MultipleUIDs", nil)
	assert.Nil(t, err)

	wsEventSubscription := cache.Subscribe(ws.Get().GetWsEventNotificationChannel())
	defer wsEventSubscription.Close()

	ch := wsEventSubscription.Channel()
	receivedUIDs := make(map[string]bool)
	expectedUIDs := map[string]bool{"uid1": true, "uid2": true}

	// Wait for at most 10 seconds for the events to be received
	timeout := time.After(10 * time.Second)
	for len(receivedUIDs) < len(expectedUIDs) {
		select {
		case msg := <-ch:
			var event ws.EventNotification
			err := json.Unmarshal([]byte(msg.Payload), &event)
			assert.Nil(t, err)
			t.Logf("event: %+v", event)
			receivedUIDs[event.UID] = true
		case <-timeout:
			t.Fatal("Test timed out waiting for notifications")
		}
	}

	assert.Equal(t, expectedUIDs, receivedUIDs, "Received UIDs do not match expected UIDs")
}
