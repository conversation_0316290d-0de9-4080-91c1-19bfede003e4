package websocket

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

// Test for token signal broadcast functionality
func TestTokenSignalBroadcast(t *testing.T) {
	// Set up the test server with a websocket service
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockWebsocketRepo(ctrl)
	Init(mockRepo)

	wsServer := Get().(*serviceImpl)
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		_ = wsServer.HandleRequest(w, r)
	}))
	defer server.Close()

	// Create multiple websocket clients to act as subscribers
	subscriber1, err := connectClient(server.URL, "subscriber1")
	assert.Nil(t, err)
	defer subscriber1.Close()

	subscriber2, err := connectClient(server.URL, "subscriber2")
	assert.Nil(t, err)
	defer subscriber2.Close()

	// Subscribe to token signals
	subscribeToTokenSignals(t, subscriber1)
	subscribeToTokenSignals(t, subscriber2)

	// Allow time for subscriptions to be processed
	time.Sleep(500 * time.Millisecond)

	// Test broadcasting a buy signal
	testBuySignalBroadcast(t, wsServer, "token123")

	// Verify both subscribers received the buy signal
	verifySignalReceived(t, subscriber1, EventTokenBuySignal, "token123")
	verifySignalReceived(t, subscriber2, EventTokenBuySignal, "token123")

	// Test broadcasting a sell signal
	testSellSignalBroadcast(t, wsServer, "token456")

	// Verify both subscribers received the sell signal
	verifySignalReceived(t, subscriber1, EventTokenSellSignal, "token456")
	verifySignalReceived(t, subscriber2, EventTokenSellSignal, "token456")
}

// Helper function to connect a websocket client
func connectClient(serverURL, clientID string) (*websocket.Conn, error) {
	headers := http.Header{
		"User-Agent": []string{"Test Client " + clientID},
	}
	conn, _, err := websocket.DefaultDialer.Dial(strings.Replace(serverURL, "http", "ws", 1), headers)
	return conn, err
}

// Helper function to subscribe to token signals
func subscribeToTokenSignals(t *testing.T, conn *websocket.Conn) {
	subscribeMsg := map[string]interface{}{
		"event": EventTokenSignalSubscribe,
		"data":  map[string]interface{}{},
	}

	jsonMsg, err := json.Marshal(subscribeMsg)
	assert.Nil(t, err)

	err = conn.WriteMessage(websocket.TextMessage, jsonMsg)
	assert.Nil(t, err)

	// Read response to subscription
	_, response, err := conn.ReadMessage()
	assert.Nil(t, err)

	var respData responseData
	err = json.Unmarshal(response, &respData)
	assert.Nil(t, err)
	assert.Equal(t, EventTokenSignalSubscribe, respData.Event)
	assert.Equal(t, 0, respData.Code) // Success
}

// Test broadcasting a buy signal
func testBuySignalBroadcast(t *testing.T, wsServer *serviceImpl, tokenAddress string) {
	ctx := context.Background()
	err := wsServer.SendEventByUIDs(
		ctx,
		[]string{},
		EventTokenBuySignal,
		map[string]string{"token_address": tokenAddress},
	)
	assert.Nil(t, err)

	// Allow time for the message to be processed
	time.Sleep(100 * time.Millisecond)
}

// Test broadcasting a sell signal
func testSellSignalBroadcast(t *testing.T, wsServer *serviceImpl, tokenAddress string) {
	ctx := context.Background()
	err := wsServer.SendEventByUIDs(
		ctx,
		[]string{},
		EventTokenSellSignal,
		map[string]string{"token_address": tokenAddress},
	)
	assert.Nil(t, err)

	// Allow time for the message to be processed
	time.Sleep(100 * time.Millisecond)
}

// Verify that a client received a signal event
func verifySignalReceived(t *testing.T, conn *websocket.Conn, expectedEvent WsEventName, expectedTokenAddress string) {
	// Set a timeout for receiving the message
	_ = conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	_, msg, err := conn.ReadMessage()
	assert.Nil(t, err)

	var eventData eventData
	err = json.Unmarshal(msg, &eventData)
	assert.Nil(t, err)

	assert.Equal(t, expectedEvent, eventData.Event)

	// Check that the data contains the expected token address
	data, ok := eventData.Data.(map[string]interface{})
	assert.True(t, ok)

	tokenAddress, ok := data["token_address"]
	assert.True(t, ok)
	assert.Equal(t, expectedTokenAddress, tokenAddress)
}

// TestTokenSignalRedisDistribution tests that token signal events are distributed via Redis
func TestTokenSignalRedisDistribution(t *testing.T) {
	// Skip test if in CI environment as Redis might not be available
	if testing.Short() {
		t.Skip("Skipping Redis test in short mode")
	}

	// Set up two mock websocket services to simulate multiple server instances
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockRepo := domain.NewMockWebsocketRepo(ctrl)
	wsService1 := NewWebSocketServer(mockRepo)

	// Ensure both services are using the same Redis channel
	// In CI or test environment, the channel might have a random suffix
	channel := wsService1.GetWsEventNotificationChannel()
	t.Logf("Using Redis channel: %s", channel)

	// Subscribe to the Redis channel
	subscription := cache.Subscribe(channel)
	defer subscription.Close()

	// Send a token signal broadcast from the first service
	ctx := context.Background()
	tokenAddress := "token789"

	// Log before sending event
	t.Logf("Sending event for token: %s", tokenAddress)

	err := wsService1.SendEventByUIDs(
		ctx,
		[]string{},
		EventTokenBuySignal,
		map[string]string{"token_address": tokenAddress},
	)
	assert.Nil(t, err)

	// The debouncing mechanism in sendEventToUIDs adds a 3-second delay
	// We need to wait longer than that to receive the message
	t.Log("Waiting for Redis message (this may take up to 10 seconds)...")

	// Check that the event was published to Redis and can be received by the second service
	select {
	case msg := <-subscription.Channel():
		t.Logf("Received Redis message: %s", msg.Payload)

		var event EventNotification
		err := json.Unmarshal([]byte(msg.Payload), &event)
		assert.Nil(t, err)

		t.Logf("Event details: UID=%s, DstClientType=%s, Event=%s",
			event.UID, event.DstClientType, event.Event)

		assert.Equal(t, "", event.UID)
		assert.Equal(t, DstTypeAll, event.DstClientType)
		assert.Equal(t, EventTokenBuySignal, event.Event)

		data, ok := event.Data.(map[string]interface{})
		assert.True(t, ok)
		assert.Equal(t, tokenAddress, data["token_address"])

	case <-time.After(10 * time.Second): // Increase timeout to 10 seconds
		t.Fatal("Timed out waiting for Redis message")
	}
}
