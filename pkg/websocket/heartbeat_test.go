package websocket_test

import (
	"log"
	"net"
	"net/url"
	"os"
	"os/signal"
	"testing"
	"time"

	"github.com/gorilla/websocket"

	"github.com/stretchr/testify/assert"
)

// var addr = flag.String("addr", "localhost:8030", "http service address")
const (
	addr = "localhost:8030"

	// Time allowed to write a message to the peer.
	// writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer.
	// pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait.
	// pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer.
	maxMessageSize = 512
)

func init() {
	// flag.Parse()
	// log.SetFlags(0)
}

func TestPingPong_Success(t *testing.T) {
	if !isPortOpen(addr) {
		t.Skip("port is closed")
	}
	WSClient(t, true, "websocket: close 1000 (normal)")
}

func TestPingPong_Fail(t *testing.T) {
	if !isPortOpen(addr) {
		t.Skip("port is closed")
	}
	WSClient(t, false, "websocket: close 1006 (abnormal closure): unexpected EOF")
}

func isPortOpen(addr string) bool {
	_, err := net.Dial("tcp", addr)
	return err == nil
}

func WSClient(t *testing.T, ping bool, errMsg string) {
	// func main() {
	interrupt := make(chan os.Signal, 1)
	signal.Notify(interrupt, os.Interrupt)
	u := url.URL{Scheme: "ws", Host: addr, Path: "/v1/ws"}
	log.Printf("connecting to %s", u.String())

	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		log.Fatal("dial:", err)
	}
	defer c.Close()

	done := make(chan struct{})
	go readPump(t, done, c, ping, errMsg)

	ticker := time.NewTicker(time.Second)
	Donetimeout := 70 * time.Second
	Doneticker := time.NewTicker(Donetimeout)

	defer func() {
		ticker.Stop()
		Doneticker.Stop()
	}()
	err = c.WriteMessage(websocket.TextMessage, []byte("{\"event\":\"auth\",\"data\":{\"token\":\"KG-DEV:123456\",\"uid\":\"testuser1\"}}"))
	if err != nil {
		log.Println("write:", err)
		return
	}
	for {
		select {
		case <-done:
			return
		case <-ticker.C:
		case <-Doneticker.C:
			err := c.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
			if err != nil {
				log.Println("write close:", err)
				return
			}
			select {
			case <-done:
			case <-time.After(time.Second):
			}
			return
		case <-interrupt:
			log.Println("interrupt")

			// Cleanly close the connection by sending a close message and then
			// waiting (with timeout) for the server to close the connection.
			err := c.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
			if err != nil {
				log.Println("write close:", err)
				return
			}
			select {
			case <-done:
			case <-time.After(time.Second):
			}
			return
		}
	}
}

func readPump(t *testing.T, done chan struct{}, c *websocket.Conn, ping bool, errMsg string) {
	defer close(done)

	c.SetReadLimit(maxMessageSize)
	c.SetPingHandler(func(s string) error {
		if ping {
			err := c.WriteMessage(websocket.PongMessage, []byte("client side: send pong"))
			if err != nil {
				log.Println("PingHandler:", err)
				return err
			}
		}
		return nil
	})
	for {
		_, message, err := c.ReadMessage()
		if err != nil {
			log.Println("read err:", err.Error())
			assert.True(t, err.Error() == errMsg)
			return
		}
		log.Printf("recv: %s", message)
	}

}
