package websocket

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/service/notification"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"gopkg.in/olahol/melody.v1"
)

func adapterHandleRequest(wsServer *serviceImpl) func(w http.ResponseWriter, r *http.Request) {
	return func(w http.ResponseWriter, r *http.Request) {
		err := wsServer.server.HandleRequest(w, r)
		fmt.Printf("adapterHandleRequest, err:%v\n", err)
	}
}

func TestEventCancelByExt(t *testing.T) {
	rdb.Reset()
	// Create test server
	wsServer := NewWebSocketServer(rdb.GormRepo())
	s := httptest.NewServer(http.HandlerFunc(adapterHandleRequest(wsServer)))
	defer s.Close()

	uid := "user-" + uuid.New().String()
	reqUuid := uuid.New().String()
	// mock firebase
	ctrl := gomock.NewController(t)
	n := notification.NewMockIService(ctrl)
	notification.Set(n)
	n.EXPECT().Send(gomock.Any(), gomock.Any()).Times(1).Return(0, nil)

	// create connection
	extWs, appWs, err := createBothWayConns(s)
	assert.Nil(t, err)
	defer extWs.Close()
	defer appWs.Close()

	// auth
	testAuth(t, extWs, uid)
	testAuth(t, appWs, uid)
	time.Sleep(1 * time.Second) // wait for server handle message

	// extension client-side send request to app client-side
	testRpcRequestForExt(t, extWs, reqUuid)
	testRpcRequestForApp(t, appWs, []string{reqUuid})

	// extension client-side send cancel to app client-side
	testRpcCancelByExt(t, extWs, appWs, reqUuid)

	// verify aliveSessions
	testAliveSession(t, uid)
}

func TestEventCancelByApp(t *testing.T) {
	rdb.Reset()
	// Create test server
	wsServer := NewWebSocketServer(rdb.GormRepo())
	s := httptest.NewServer(http.HandlerFunc(adapterHandleRequest(wsServer)))
	defer s.Close()

	uid := "user-" + uuid.New().String()
	reqUuid := uuid.New().String()
	// mock firebase
	ctrl := gomock.NewController(t)
	n := notification.NewMockIService(ctrl)
	notification.Set(n)
	n.EXPECT().Send(gomock.Any(), gomock.Any()).Times(1).Return(0, nil)

	// create connection
	extWs, appWs, err := createBothWayConns(s)
	assert.Nil(t, err)
	defer extWs.Close()
	defer appWs.Close()

	// auth
	testAuth(t, extWs, uid)
	testAuth(t, appWs, uid)
	time.Sleep(1 * time.Second) // wait for server handle message

	// send request again
	testRpcRequestForExt(t, extWs, reqUuid)
	testRpcRequestForApp(t, appWs, []string{reqUuid})

	// app client-side send cancel to extension client-side
	// testRpcCancelByApp(t, extWs, appWs, reqUuid)
	testRpcCancelByApp(t, extWs, appWs, reqUuid)

	// verify aliveSessions
	testAliveSession(t, uid)
}

func TestSendPendingRequests(t *testing.T) {
	rdb.Reset()
	// Create test server
	wsServer := NewWebSocketServer(rdb.GormRepo())
	s := httptest.NewServer(http.HandlerFunc(adapterHandleRequest(wsServer)))
	defer s.Close()

	uid := "user-" + uuid.New().String()
	reqUuids := []string{
		uuid.New().String(),
		uuid.New().String(),
		uuid.New().String(),
	}
	// mock firebase
	ctrl := gomock.NewController(t)
	n := notification.NewMockIService(ctrl)
	notification.Set(n)
	n.EXPECT().Send(gomock.Any(), gomock.Any()).Times(3).Return(0, nil)

	// create connection
	extWs, appWs, err := createBothWayConns(s)
	assert.Nil(t, err)
	defer extWs.Close()
	defer appWs.Close()

	// send request to app client-side
	testAuth(t, extWs, uid)
	for _, reqUuid := range reqUuids {
		testRpcRequestForExt(t, extWs, reqUuid)
	}

	// receive request from extension client-side
	testAuth(t, appWs, uid)
	time.Sleep(5 * time.Second)
	// the order is random
	testRpcRequestForApp(t, appWs, reqUuids)

	// verify aliveSessions
	testAliveSession(t, uid)
}

func createBothWayConns(s *httptest.Server) (*websocket.Conn, *websocket.Conn, error) {
	extWsHeader := http.Header{
		"User-Agent": []string{"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36"},
	}
	appWsHeader := http.Header{
		"User-Agent": []string{"Dart/2.18 (dart:io)"},
	}
	extWs, _, err := websocket.DefaultDialer.Dial(strings.Replace(s.URL, "http", "ws", 1), extWsHeader)
	if err != nil {
		return nil, nil, err
	}
	appWs, _, err := websocket.DefaultDialer.Dial(strings.Replace(s.URL, "http", "ws", 1), appWsHeader)
	if err != nil {
		return nil, nil, err
	}
	return extWs, appWs, nil
}

func testAuth(t *testing.T, ws *websocket.Conn, uid string) {
	authBody := map[string]interface{}{
		"event": "auth",
		"data": map[string]interface{}{
			"token": "KG-DEV:123456",
			"uid":   uid,
		},
	}
	jsonStr, err := json.Marshal(authBody)
	assert.Nil(t, err)
	err = ws.WriteMessage(websocket.TextMessage, jsonStr)
	assert.Nil(t, err)

	// verify auth response
	_, ret, err := ws.ReadMessage()
	assert.Nil(t, err)
	respData := &responseData{}
	err = json.Unmarshal(ret, respData)
	assert.Nil(t, err)
	assert.Equal(t, EventAuth, respData.Event)
	assert.Equal(t, 0, respData.Code)
}

func testAliveSession(t *testing.T, uid string) {
	sessionKeys := composeSessionKey("extension", uid)
	sessionList, ok := aliveSessions.Load(sessionKeys[0])
	assert.True(t, ok)
	assert.Equal(t, 1, len(sessionList.([]*melody.Session)))

	sessionKeys = composeSessionKey("app", uid)
	sessionList, ok = aliveSessions.Load(sessionKeys[0])
	assert.True(t, ok)
	assert.Equal(t, 1, len(sessionList.([]*melody.Session)))
}

func testRpcRequestForExt(t *testing.T, extWs *websocket.Conn, reqUuid string) {
	rpcRequestBody := &responseData{
		Event: EventJsonRpcRequest,
		Data: map[string]interface{}{
			"request_uuid": reqUuid,
			"chain_id":     "matic",
			"id":           0,
			"jsonrpc":      "2.0",
			"method":       "eth_sendTransaction",
			"params": []map[string]interface{}{
				{
					"from":  "0x01",
					"to":    "0x23",
					"value": "0x45",
				},
			},
		},
	}
	jsonStr, err := json.Marshal(rpcRequestBody)
	assert.Nil(t, err)
	err = extWs.WriteMessage(websocket.TextMessage, []byte(jsonStr))
	assert.Nil(t, err)

	// verify response when sending rpc request
	_, ret, err := extWs.ReadMessage()
	assert.Nil(t, err)
	respData := &responseData{}
	err = json.Unmarshal(ret, respData)
	assert.Nil(t, err)
	assert.Equal(t, EventJsonRpcRequest, respData.Event)
	assert.Equal(t, 0, respData.Code)
	assert.Equal(t, reqUuid, respData.RequestUUID)
}

func testRpcRequestForApp(t *testing.T, appWs *websocket.Conn, reqUuids []string) {
	// verify received rpc request
	matchingUuid := map[string]util.Void{}
	timeout := time.After(15 * time.Second)
loop:
	for {
		select {
		case <-timeout:
			t.Error("testRpcRequestForApp timeout")
			break loop
		default:
			broadcastData := &eventData{}
			_, ret, err := appWs.ReadMessage()
			assert.Nil(t, err)
			err = json.Unmarshal(ret, broadcastData)
			assert.Nil(t, err)
			assert.Equal(t, EventJsonRpcRequest, broadcastData.Event)
			uuid := broadcastData.Data.(map[string]interface{})["request_uuid"].(string)
			fmt.Printf("testRpcRequestForApp, uuid:%s, data:%+v\n", uuid, broadcastData.Data)

			assert.Contains(t, reqUuids, uuid)
			matchingUuid[uuid] = util.Void{}
			assert.True(t, broadcastData.CreatedAt.Before(time.Now().Add(1*time.Minute)))
			assert.True(t, broadcastData.CreatedAt.After(time.Now().Add(-1*time.Minute)))
			if len(matchingUuid) == len(reqUuids) {
				break loop
			}
		}
	}

	assert.Equal(t, len(reqUuids), len(matchingUuid))
}

func testRpcCancelByExt(t *testing.T, extWs, appWs *websocket.Conn, reqUuid string) {
	cancelBody := &responseData{
		Event: EventJsonRpcCancel,
		Data: map[string]interface{}{
			"request_uuid": reqUuid,
			"jsonrpc":      "2.0",
		},
	}
	jsonStr, err := json.Marshal(cancelBody)
	assert.Nil(t, err)

	// cancel from extension
	err = extWs.WriteMessage(websocket.TextMessage, []byte(jsonStr))
	assert.Nil(t, err)
	_, ret, err := extWs.ReadMessage()
	assert.Nil(t, err)
	respData := &responseData{}
	err = json.Unmarshal(ret, respData)
	assert.Nil(t, err)
	assert.Equal(t, EventJsonRpcCancel, respData.Event)
	assert.Equal(t, 0, respData.Code)
	_, ret, err = appWs.ReadMessage()
	assert.Nil(t, err)

	broadcastData := &eventData{}
	err = json.Unmarshal(ret, broadcastData)
	assert.Nil(t, err)

	// receive event json_rpc_request from app, when cancel from extension
	assert.Equal(t, EventJsonRpcRequest, broadcastData.Event)
	assert.Equal(t, reqUuid, broadcastData.Data.(map[string]interface{})["request_uuid"].(string))
	assert.Contains(t, broadcastData.Data.(map[string]interface{}), "error")
}

func testRpcCancelByApp(t *testing.T, extWs, appWs *websocket.Conn, reqUuid string) {
	cancelBody := &responseData{
		Event: EventJsonRpcCancel,
		Data: map[string]interface{}{
			"request_uuid": reqUuid,
			"jsonrpc":      "2.0",
		},
	}
	jsonStr, err := json.Marshal(cancelBody)
	assert.Nil(t, err)

	// cancel from app
	err = appWs.WriteMessage(websocket.TextMessage, []byte(jsonStr))
	assert.Nil(t, err)
	_, ret, err := appWs.ReadMessage()
	assert.Nil(t, err)
	respData := &responseData{}
	err = json.Unmarshal(ret, respData)
	fmt.Printf("testRpcCancelByApp, respData:%+v\n", respData)
	assert.Nil(t, err)
	assert.Equal(t, EventJsonRpcCancel, respData.Event)
	assert.Equal(t, 0, respData.Code)

	// verify cancel response from extension
	_, ret, err = extWs.ReadMessage()
	assert.Nil(t, err)

	broadcastData := &eventData{}
	err = json.Unmarshal(ret, broadcastData)
	assert.Nil(t, err)

	// receive event json_rpc_response from extension, when cancel from app
	assert.Equal(t, EventJsonRpcResponse, broadcastData.Event)
	assert.Equal(t, reqUuid, broadcastData.Data.(map[string]interface{})["request_uuid"].(string))
	assert.Contains(t, broadcastData.Data.(map[string]interface{}), "error")
}
