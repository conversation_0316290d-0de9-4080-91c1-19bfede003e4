package l10n

import (
	"testing"
)

func TestStringWithParams(t *testing.T) {
	type args struct {
		key    string
		locale string
		params interface{}
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test_sms_login_content",
			args: args{
				key:    "sms-login-content",
				locale: "en_US",
				params: map[string]interface{}{
					"code":        "123456",
					"expire":      120,
					"wallet_name": "KryptoGO",
				},
			},
			want: "KryptoGO: 123456 is your code for phone enrollment. Expires in 120 seconds. Never share this code.",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := StringWithParams(tt.args.key, tt.args.locale, tt.args.params); got != tt.want {
				t.Errorf("StringWithParams() = %v, want %v", got, tt.want)
			}
		})
	}
}
