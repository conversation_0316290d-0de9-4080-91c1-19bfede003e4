package verifier

import (
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"github.com/lestrrat-go/jwx/jwk"
	"github.com/stretchr/testify/assert"
)

func setupJWKServer() (*httptest.Server, *rsa.PrivateKey, jwk.Key, error) {
	// Generate a new RSA private key in PEM format
	_, privateKey, err := GenerateRSAPrivateKeyPEM()
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to generate RSA private key: %v", err)
	}

	// Convert the RSA key to JWK
	jwkKey, err := jwk.New(privateKey.Public())
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to create JWK from private key: %v", err)
	}

	if err := jwkKey.Set(jwk.<PERSON><PERSON><PERSON><PERSON>, "test-kid"); err != nil {
		return nil, nil, nil, fmt.Errorf("failed to set kid: %v", err)
	}
	if err := jwkKey.Set(jwk.AlgorithmKey, "RS256"); err != nil {
		return nil, nil, nil, fmt.Errorf("failed to set alg: %v", err)
	}

	set := jwk.NewSet()
	set.Add(jwkKey)

	// Setup Gin router
	router := gin.Default()
	router.GET("/.well-known/jwks.json", func(c *gin.Context) {
		err = json.NewEncoder(c.Writer).Encode(set)
		if err != nil {
			c.Status(500)
			return
		}
	})

	// Start a test server
	ts := httptest.NewServer(router)
	return ts, privateKey, jwkKey, nil
}

func TestVerifySuccess(t *testing.T) {
	// Setup the JWK server
	ts, privateKey, _, err := setupJWKServer()
	assert.Nil(t, err)
	defer ts.Close()

	// Create the verifier
	v := NewJwtVerifier(ts.URL+"/.well-known/jwks.json", "test-aud", "test-iss", "uid")

	// Sign a token
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, jwt.MapClaims{
		"iss": "test-iss",
		"aud": "test-aud",
		"uid": "test-user",
		"exp": time.Now().Add(time.Hour).Unix(),
	})

	// Set the key ID
	token.Header["kid"] = "test-kid"

	// Sign the token with the private key
	tokenString, err := token.SignedString(privateKey)
	assert.Nil(t, err)
	t.Logf("tokenString: %s\n", tokenString)

	// Verify the token and user ID
	userID, err := v.Verify(tokenString)
	assert.Nil(t, err)
	assert.Equal(t, "test-user", userID)
}
