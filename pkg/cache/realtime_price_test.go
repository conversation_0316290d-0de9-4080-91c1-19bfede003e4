package cache

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAcquireRealtimeTokenPriceTask(t *testing.T) {
	// Setup
	ctx := context.Background()
	repo := GetRepo()

	tasks := []domain.RealtimeTokenPriceTask{
		{ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "token1"}, RetryCount: 0},
		{ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "token2"}, RetryCount: 1},
	}

	// Add test tasks to Redis
	err := repo.AddRealtimeTokenPriceTask(ctx, tasks)
	require.NoError(t, err)

	t.Run("success", func(t *testing.T) {
		got, err := repo.AcquireRealtimeTokenPriceTask(ctx, 2)
		assert.NoError(t, err)
		assert.Len(t, got, 2)
		// Check that the tasks were acquired
		for _, task := range got {
			assert.Contains(t, []string{"token1", "token2"}, task.ChainToken.TokenID)
			assert.Equal(t, domain.Ethereum, task.ChainToken.Chain)
		}
	})

	t.Run("not found", func(t *testing.T) {
		// Clear the tasks
		Client.Del(ctx, RealtimePriceTaskKey)

		got, err := repo.AcquireRealtimeTokenPriceTask(ctx, 2)
		assert.ErrorIs(t, err, domain.ErrRecordNotFound)
		assert.Nil(t, got)
	})

	t.Run("redis error", func(t *testing.T) {
		// Create a temporary invalid client to trigger an error
		originalClient := Client
		Client = nil

		got, err := repo.AcquireRealtimeTokenPriceTask(ctx, 2)
		assert.Error(t, err)
		assert.Nil(t, got)
		assert.Contains(t, err.Error(), "redis client is nil")

		// Restore the client
		Client = originalClient
	})

	// Clean up
	Client.Del(ctx, RealtimePriceTaskKey)
}

func TestAddRealtimeTokenPriceTask(t *testing.T) {
	// Setup
	ctx := context.Background()
	repo := GetRepo()

	task := domain.RealtimeTokenPriceTask{ChainToken: domain.ChainToken{Chain: domain.Ethereum, TokenID: "token1"}, RetryCount: 0}
	tasks := []domain.RealtimeTokenPriceTask{task}

	t.Run("success", func(t *testing.T) {
		err := repo.AddRealtimeTokenPriceTask(ctx, tasks)
		assert.NoError(t, err)

		// Verify tasks were added
		result, err := Client.Exists(ctx, RealtimePriceTaskKey).Result()
		assert.NoError(t, err)
		assert.Equal(t, int64(1), result)
	})

	t.Run("error", func(t *testing.T) {
		// Create a temporary invalid client to trigger an error
		originalClient := Client
		Client = nil

		err := repo.AddRealtimeTokenPriceTask(ctx, tasks)
		assert.Error(t, err)

		// Restore the client
		Client = originalClient
	})

	// Clean up
	Client.Del(ctx, RealtimePriceTaskKey)
}

func TestAddRealtimeTokenPriceResponse(t *testing.T) {
	// Setup
	ctx := context.Background()
	repo := GetRepo()

	serving := domain.RealtimeTokenPriceResponse{
		ChainToken:     domain.ChainToken{Chain: domain.Ethereum, TokenID: "token1"},
		PriceUSD:       123.45,
		LastUpdateTime: time.Now(),
	}
	servings := []domain.RealtimeTokenPriceResponse{serving}

	t.Run("success", func(t *testing.T) {
		err := repo.AddRealtimeTokenPriceResponse(ctx, servings)
		assert.NoError(t, err)

		// Verify price was added
		field := fmt.Sprintf("%s:%s", serving.ChainToken.Chain, serving.ChainToken.TokenID)
		exists, err := Client.HExists(ctx, RealtimePriceResponseKey, field).Result()
		assert.NoError(t, err)
		assert.True(t, exists)
	})

	t.Run("error", func(t *testing.T) {
		// Create a temporary invalid client to trigger an error
		originalClient := Client
		Client = nil

		err := repo.AddRealtimeTokenPriceResponse(ctx, servings)
		assert.Error(t, err)

		// Restore the client
		Client = originalClient
	})

	// Clean up
	Client.HDel(ctx, RealtimePriceResponseKey, fmt.Sprintf("%s:%s", serving.ChainToken.Chain, serving.ChainToken.TokenID))
}

// Note: In the implementation, we need to adjust our test to match the actual function signature
// The implementation expects chainTokens []domain.ChainToken but we'll put our test token in a slice
func TestGetRealtimeTokenPrice(t *testing.T) {
	// Setup
	ctx := context.Background()
	repo := GetRepo()

	chainToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "token1"}
	chainTokens := []domain.ChainToken{chainToken}
	serving := domain.RealtimeTokenPriceResponse{
		ChainToken:     chainToken,
		PriceUSD:       123.45,
		LastUpdateTime: time.Now(),
	}

	// Add test data
	err := repo.AddRealtimeTokenPriceResponse(ctx, []domain.RealtimeTokenPriceResponse{serving})
	require.NoError(t, err)

	t.Run("success_hot_price", func(t *testing.T) {
		hotPrices, warmPrices, err := repo.GetRealtimeTokenPrice(ctx, chainTokens)
		assert.NoError(t, err)
		assert.Len(t, hotPrices, 1)
		assert.Equal(t, chainToken, hotPrices[0].ChainToken)
		assert.Equal(t, domain.Price(123.45), hotPrices[0].PriceUSD)
		assert.Empty(t, warmPrices) // Should be empty for fresh prices
	})

	t.Run("success_warm_price", func(t *testing.T) {
		// Create a price that's older than hot TTL but within warm TTL
		oldServing := domain.RealtimeTokenPriceResponse{
			ChainToken:     chainToken,
			PriceUSD:       456.78,
			LastUpdateTime: time.Now().Add(-5 * time.Second), // 5 seconds old (warm)
		}
		err := repo.AddRealtimeTokenPriceResponse(ctx, []domain.RealtimeTokenPriceResponse{oldServing})
		require.NoError(t, err)

		hotPrices, warmPrices, err := repo.GetRealtimeTokenPrice(ctx, chainTokens)
		assert.NoError(t, err)
		assert.Empty(t, hotPrices)   // Should be empty for old prices
		assert.Len(t, warmPrices, 1) // Should have warm price
		assert.Equal(t, chainToken, warmPrices[0].ChainToken)
		assert.Equal(t, domain.Price(456.78), warmPrices[0].PriceUSD)
	})

	t.Run("not_found", func(t *testing.T) {
		// Try to get a non-existent price
		nonExistentToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "nonexistent"}
		hotPrices, warmPrices, err := repo.GetRealtimeTokenPrice(ctx, []domain.ChainToken{nonExistentToken})
		assert.ErrorIs(t, err, domain.ErrRecordNotFound)
		assert.Nil(t, hotPrices)
		assert.Nil(t, warmPrices)
	})

	// Clean up
	key := fmt.Sprintf("%s:%s:%s", RealtimePriceResponseKey, chainToken.Chain, chainToken.TokenID)
	Client.Del(ctx, key)
}
