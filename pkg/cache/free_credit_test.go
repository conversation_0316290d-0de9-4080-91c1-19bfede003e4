package cache

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFreeCredit(t *testing.T) {
	ctx := context.Background()
	repo := &Repo{}

	// Test data
	walletAddress := "test_wallet_address"

	t.Run("RecordFreeCreditToday", func(t *testing.T) {
		// Test recording free credit
		err := repo.RecordFreeCreditToday(ctx, walletAddress)
		require.NoError(t, err)

		// Verify the key exists
		key := composeFreeCreditKey(walletAddress)
		exists, err := Exists(ctx, key)
		require.NoError(t, err)
		assert.True(t, exists)

		// Verify TTL is set correctly (should be until end of day)
		loc, _ := time.LoadLocation("Asia/Taipei")
		now := time.Now().In(loc)
		endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, loc)
		expectedTTL := endOfDay.Sub(now)

		ttl := TTL(key)
		// Allow 1 second difference due to execution time
		assert.InDelta(t, expectedTTL.Seconds(), ttl.Seconds(), 1)
	})

	t.Run("HasReceivedFreeCreditToday", func(t *testing.T) {
		// Test when credit exists
		exists, err := repo.HasReceivedFreeCreditToday(ctx, walletAddress)
		require.NoError(t, err)
		assert.True(t, exists)

		// Test when credit doesn't exist
		nonExistentWallet := "non_existent_wallet"
		exists, err = repo.HasReceivedFreeCreditToday(ctx, nonExistentWallet)
		require.NoError(t, err)
		assert.False(t, exists)
	})

	t.Run("KeyFormat", func(t *testing.T) {
		// Test key format
		key := composeFreeCreditKey(walletAddress)
		loc, _ := time.LoadLocation("Asia/Taipei")
		today := time.Now().In(loc).Format("2006-01-02")
		expectedKey := "free_credit:" + today + ":" + walletAddress
		assert.Equal(t, expectedKey, key)
	})

	t.Run("Expiration", func(t *testing.T) {
		// Test that key expires at end of day
		key := composeFreeCreditKey(walletAddress)
		ttl := TTL(key)

		loc, _ := time.LoadLocation("Asia/Taipei")
		now := time.Now().In(loc)
		endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, loc)
		expectedTTL := endOfDay.Sub(now)

		// Allow 1 second difference due to execution time
		assert.InDelta(t, expectedTTL.Seconds(), ttl.Seconds(), 1)
	})

	t.Run("MultipleWallets", func(t *testing.T) {
		// Test multiple wallets
		wallet1 := "wallet1"
		wallet2 := "wallet2"

		// Record credits for both wallets
		err := repo.RecordFreeCreditToday(ctx, wallet1)
		require.NoError(t, err)
		err = repo.RecordFreeCreditToday(ctx, wallet2)
		require.NoError(t, err)

		// Verify both exist
		exists1, err := repo.HasReceivedFreeCreditToday(ctx, wallet1)
		require.NoError(t, err)
		assert.True(t, exists1)

		exists2, err := repo.HasReceivedFreeCreditToday(ctx, wallet2)
		require.NoError(t, err)
		assert.True(t, exists2)

		// Verify non-existent wallet still returns false
		exists3, err := repo.HasReceivedFreeCreditToday(ctx, "wallet3")
		require.NoError(t, err)
		assert.False(t, exists3)
	})
}
