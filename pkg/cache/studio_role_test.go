package cache_test

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/stretchr/testify/assert"
)

func TestStudioRoleCacheRepo(t *testing.T) {
	s := assert.New(t)
	s.NotNil(cache.Client)
	ctx := context.Background()

	expectedUser1StudioRoles := []domain.StudioRole{
		{Module: "module1", Name: "name1"},
		{Module: "module1", Name: "name2"},
		{Module: "module2", Name: "name1"},
	}
	expectedUser2StudioRoles := []domain.StudioRole{
		{
			Module: "module1",
			Name:   "name1",
		},
	}

	repo := cache.NewRedisStudioRoleCacheRepo(cache.Client)
	{ // try to set roles to cache
		s.Nil(repo.SetRoles(ctx, []domain.StudioRoleBinding{
			{
				OrganizationID: 1,
				UID:            "uid1",
				StudioRole:     expectedUser1StudioRoles[0],
			},
			{
				OrganizationID: 1,
				UID:            "uid1",
				StudioRole:     expectedUser1StudioRoles[1],
			},
			{
				OrganizationID: 1,
				UID:            "uid1",
				StudioRole:     expectedUser1StudioRoles[2],
			},
			{
				OrganizationID: 2,
				UID:            "uid2",
				StudioRole:     expectedUser2StudioRoles[0],
			},
		}))
	}
	{ // get user1's roles from cache
		actualBindings, kgError := repo.GetRolesByUser(ctx, 1, "uid1")
		s.Nil(kgError)

		for _, expectedUser1StudioRole := range expectedUser1StudioRoles {
			s.Contains(actualBindings, expectedUser1StudioRole)
		}
	}
	{ // get user2's roles from cache
		actualBindings, kgError := repo.GetRolesByUser(ctx, 2, "uid2")
		s.Nil(kgError)

		for _, expectedUser2StudioRole := range expectedUser2StudioRoles {
			s.Contains(actualBindings, expectedUser2StudioRole)
		}
	}
	{ // get empty user roles from cache
		actualBindings, kgError := repo.GetRolesByUser(ctx, 0, "")
		s.Nil(kgError)
		s.Len(actualBindings, 0)
	}
	{
		s.Nil(repo.SetUserRoles(ctx, 1, "uid1", []domain.StudioRole{
			expectedUser1StudioRoles[1],
		}))

		actualBinding, kgError := repo.GetRolesByUser(ctx, 1, "uid1")
		s.Nil(kgError)

		s.Len(actualBinding, 1)
		s.Contains(actualBinding, expectedUser1StudioRoles[1])
	}
	{ // set than clear
		s.Nil(repo.SetUserRoles(ctx, 1, "uid1", []domain.StudioRole{
			expectedUser1StudioRoles[1],
		}))

		actualBinding, kgError := repo.GetRolesByUser(ctx, 1, "uid1")
		s.Nil(kgError)

		s.Len(actualBinding, 1)
		s.Contains(actualBinding, expectedUser1StudioRoles[1])

		s.Nil(repo.SetUserRoles(ctx, 1, "uid1", []domain.StudioRole{}))

		actualBinding, kgError = repo.GetRolesByUser(ctx, 1, "uid1")
		s.Nil(kgError)

		s.Len(actualBinding, 0)
	}
}
