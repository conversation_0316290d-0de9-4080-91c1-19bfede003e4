package cache

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAcquireLock(t *testing.T) {
	// Setup
	ctx := context.Background()
	repo := GetRepo()

	// Test case 1: Successfully acquire a lock
	t.Run("Successful lock acquisition", func(t *testing.T) {
		key := "test_lock_1"
		duration := time.Second * 5

		err := repo.AcquireLock(ctx, key, duration)
		require.NoError(t, err)

		// Verify the lock was acquired
		exists, err := Client.Exists(ctx, key).Result()
		require.NoError(t, err)
		assert.Equal(t, int64(1), exists)

		// Clean up
		Client.Del(ctx, key)
	})

	// Test case 2: Fail to acquire an already held lock
	t.Run("Fail to acquire held lock", func(t *testing.T) {
		key := "test_lock_2"
		duration := time.Second * 5

		// Acquire the lock first
		err := repo.AcquireLock(ctx, key, duration)
		require.NoError(t, err)

		// Try to acquire the same lock again
		err = repo.AcquireLock(ctx, key, duration)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "lock not acquired")

		// Clean up
		Client.Del(ctx, key)
	})
}
