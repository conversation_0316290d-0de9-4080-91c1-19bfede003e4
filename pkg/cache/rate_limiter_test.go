package cache

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/stretchr/testify/assert"
)

func TestAllow(t *testing.T) {
	// Use a lower burst and rate for easier testing
	limiter := NewCommonRateLimiter("test", domain.RateLimitParams{
		Period: 2 * time.Second,
		Burst:  2,
		Rate:   1,
	})

	ctx := context.Background()
	key := "test_key"

	// Test allowing requests within burst limit
	for i := 0; i < 2; i++ {
		result, err := limiter.allow(ctx, key)
		assert.NoError(t, err)
		assert.Equal(t, 1, result.Allowed)
	}

	// Test hitting the rate limit
	result, err := limiter.allow(ctx, key)
	assert.NoError(t, err)
	assert.Equal(t, 0, result.Allowed)
}

func TestWait(t *testing.T) {
	// Use a lower burst and rate for easier testing
	limiter := NewCommonRateLimiter("test", domain.RateLimitParams{
		Period: 2 * time.Second,
		Burst:  1,
		Rate:   1,
	})

	ctx := context.Background()
	key := "test_key"

	t.Run("wait_for_request_to_be_allowed", func(t *testing.T) {
		// Allow one request to consume the burst
		_, _ = limiter.allow(ctx, key)

		// Test waiting for a request to be allowed after hitting the rate limit
		start := time.Now()
		err := limiter.Wait(ctx, key, 10*time.Second)
		duration := time.Since(start)

		assert.NoError(t, err)
		assert.GreaterOrEqual(t, duration, 2*time.Second, "Wait should have waited for at least 2 seconds")
	})

	t.Run("timeout_when_waiting_for_request_to_be_allowed", func(t *testing.T) {
		// Allow another request to reset the rate limit
		_, _ = limiter.allow(ctx, key)

		// Test timeout when waiting for a request to be allowed
		start := time.Now()
		err := limiter.Wait(ctx, key, 1*time.Second)
		duration := time.Since(start)

		assert.ErrorIs(t, err, code.ErrRateLimitWaitTimeout)
		assert.InDelta(t, 1*time.Second, duration, float64(500*time.Millisecond), "Wait should have timed out around 1 second")
	})

	t.Run("wait_for_request_to_be_allowed_with_context_timeout", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(ctx, 300*time.Millisecond)
		defer cancel()

		// Allow one request to consume the burst
		_, _ = limiter.allow(ctx, key)

		// Test timeout when waiting for a request to be allowed
		start := time.Now()
		err := limiter.Wait(ctx, key, 10*time.Second)
		duration := time.Since(start)

		assert.ErrorIs(t, err, code.ErrRateLimitWaitTimeout)
		assert.InDelta(t, 300*time.Millisecond, duration, float64(100*time.Millisecond), "Wait should have timed out around 300 milliseconds")
	})
}
