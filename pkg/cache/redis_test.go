package cache

import (
	"context"
	"fmt"
	"testing"

	"github.com/go-redis/redis/v8"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestPushArray(t *testing.T) {
	key := util.GenerateRandomCode(10)
	err := RPush(key, "1", "2", "3")
	assert.Nil(t, err)

	values, err := GetArray(key)
	assert.<PERSON>l(t, err)
	assert.Equal(t, []string{"1", "2", "3"}, values)

	fmt.Println(values)
	length, err := <PERSON><PERSON>(key)
	assert.Nil(t, err)
	assert.Equal(t, int64(3), length)

	// clear
	Del(key)
}

func TestInsertArray(t *testing.T) {
	key := util.GenerateRandomCode(10)
	err := RPush(key, "1", "2", "3")
	assert.Nil(t, err)
	err = LInsert(key, "AFTER", "1", "2")
	assert.Nil(t, err)

	values, err := GetArray(key)
	assert.Nil(t, err)
	assert.Equal(t, []string{"1", "2", "2", "3"}, values)

	fmt.Println(values)
	length, err := LLen(key)
	assert.Nil(t, err)
	assert.Equal(t, int64(4), length)

	// clear
	Del(key)
}

func TestZset(t *testing.T) {
	key := util.GenerateRandomCode(10)
	// insert
	_, err := ZAdd(key, &redis.Z{Score: 5, Member: 5})
	assert.Nil(t, err)
	_, err = ZAdd(key, &redis.Z{Score: 2, Member: 2})
	assert.Nil(t, err)
	_, err = ZAdd(key, &redis.Z{Score: 3, Member: 3})
	assert.Nil(t, err)

	length, err := ZLen(key)
	assert.Nil(t, err)
	assert.Equal(t, int64(3), length)

	// pop min
	value, err := ZPopMin(key)
	assert.Nil(t, err)
	assert.Equal(t, "2", value)
	value, err = ZPopMin(key)
	assert.Nil(t, err)
	assert.Equal(t, "3", value)
	value, err = ZPopMin(key)
	assert.Nil(t, err)
	assert.Equal(t, "5", value)
}

func TestZAddNX(t *testing.T) {
	key := util.GenerateRandomCode(10)
	defer Del(key)

	// First add should succeed
	added, err := ZAddNX(key, &redis.Z{Score: 1, Member: "a"})
	assert.Nil(t, err)
	assert.Equal(t, int64(1), added)

	// Second add with same member but different score should fail
	added, err = ZAddNX(key, &redis.Z{Score: 2, Member: "a"})
	assert.Nil(t, err)
	assert.Equal(t, int64(0), added)

	// Add a new member should succeed
	added, err = ZAddNX(key, &redis.Z{Score: 3, Member: "b"})
	assert.Nil(t, err)
	assert.Equal(t, int64(1), added)

	// Verify the length
	length, err := ZLen(key)
	assert.Nil(t, err)
	assert.Equal(t, int64(2), length)

	// Verify the first member still has the original score
	min, err := ZMin(key)
	assert.Nil(t, err)
	assert.Equal(t, "a", min)
}

func TestZAddNXCtx(t *testing.T) {
	key := util.GenerateRandomCode(10)
	defer Del(key)

	// First add should succeed
	added, err := ZAddNXCtx(context.Background(), key, &redis.Z{Score: 1, Member: "a"})
	assert.Nil(t, err)
	assert.Equal(t, int64(1), added)
}

func TestZRemCtx(t *testing.T) {
	key := util.GenerateRandomCode(10)
	defer Del(key)

	// First add should succeed
	added, err := ZAddNXCtx(context.Background(), key, &redis.Z{Score: 1, Member: "a"})
	assert.Nil(t, err)
	assert.Equal(t, int64(1), added)

	// Remove should succeed
	removed, err := ZRemCtx(context.Background(), key, "a")
	assert.Nil(t, err)
	assert.Equal(t, int64(1), removed)
}

func TestSetInToday(t *testing.T) {
	key := util.GenerateRandomCode(10)
	s := SetInToday(key, "1")
	assert.Equal(t, "OK", s)

	ttl := TTL(key)
	assert.Less(t, ttl.Seconds(), float64(86400))
	assert.Greater(t, ttl.Seconds(), float64(0))
}

func TestResetMinPool(t *testing.T) {
	key := util.GenerateRandomCode(10)

	// insert
	_, err := ZAdd(key, &redis.Z{Score: 5, Member: 5})
	assert.Nil(t, err)
	_, err = ZAdd(key, &redis.Z{Score: 2, Member: 2})
	assert.Nil(t, err)
	_, err = ZAdd(key, &redis.Z{Score: 3, Member: 3})
	assert.Nil(t, err)

	length, err := ZLen(key)
	assert.Nil(t, err)
	assert.Equal(t, int64(3), length)

	// reset
	err = ResetMinPool(key, &redis.Z{Score: 4, Member: 4})
	assert.Nil(t, err)
	min, err := ZMin(key)
	assert.Nil(t, err)
	assert.Equal(t, "4", min)
}
