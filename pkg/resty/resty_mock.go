// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/resty (interfaces: Client,Request)
//
// Generated by this command:
//
//	mockgen -package=resty -destination=resty_mock.go . Client,Request
//

// Package resty is a generated GoMock package.
package resty

import (
	context "context"
	http "net/http"
	url "net/url"
	reflect "reflect"
	time "time"

	resty "github.com/go-resty/resty/v2"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// BaseURL mocks base method.
func (m *MockClient) BaseURL() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BaseURL")
	ret0, _ := ret[0].(string)
	return ret0
}

// BaseURL indicates an expected call of BaseURL.
func (mr *MockClientMockRecorder) BaseURL() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BaseURL", reflect.TypeOf((*MockClient)(nil).BaseURL))
}

// Clone mocks base method.
func (m *MockClient) Clone() Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Clone")
	ret0, _ := ret[0].(Client)
	return ret0
}

// Clone indicates an expected call of Clone.
func (mr *MockClientMockRecorder) Clone() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Clone", reflect.TypeOf((*MockClient)(nil).Clone))
}

// OnAfterResponse mocks base method.
func (m *MockClient) OnAfterResponse(arg0 resty.ResponseMiddleware) Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnAfterResponse", arg0)
	ret0, _ := ret[0].(Client)
	return ret0
}

// OnAfterResponse indicates an expected call of OnAfterResponse.
func (mr *MockClientMockRecorder) OnAfterResponse(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnAfterResponse", reflect.TypeOf((*MockClient)(nil).OnAfterResponse), arg0)
}

// OnBeforeRequest mocks base method.
func (m *MockClient) OnBeforeRequest(arg0 resty.RequestMiddleware) Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnBeforeRequest", arg0)
	ret0, _ := ret[0].(Client)
	return ret0
}

// OnBeforeRequest indicates an expected call of OnBeforeRequest.
func (mr *MockClientMockRecorder) OnBeforeRequest(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnBeforeRequest", reflect.TypeOf((*MockClient)(nil).OnBeforeRequest), arg0)
}

// R mocks base method.
func (m *MockClient) R() Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "R")
	ret0, _ := ret[0].(Request)
	return ret0
}

// R indicates an expected call of R.
func (mr *MockClientMockRecorder) R() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "R", reflect.TypeOf((*MockClient)(nil).R))
}

// SetBaseURL mocks base method.
func (m *MockClient) SetBaseURL(arg0 string) Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBaseURL", arg0)
	ret0, _ := ret[0].(Client)
	return ret0
}

// SetBaseURL indicates an expected call of SetBaseURL.
func (mr *MockClientMockRecorder) SetBaseURL(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBaseURL", reflect.TypeOf((*MockClient)(nil).SetBaseURL), arg0)
}

// SetBasicAuth mocks base method.
func (m *MockClient) SetBasicAuth(arg0, arg1 string) Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBasicAuth", arg0, arg1)
	ret0, _ := ret[0].(Client)
	return ret0
}

// SetBasicAuth indicates an expected call of SetBasicAuth.
func (mr *MockClientMockRecorder) SetBasicAuth(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBasicAuth", reflect.TypeOf((*MockClient)(nil).SetBasicAuth), arg0, arg1)
}

// SetHeader mocks base method.
func (m *MockClient) SetHeader(arg0, arg1 string) Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0, arg1)
	ret0, _ := ret[0].(Client)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockClientMockRecorder) SetHeader(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockClient)(nil).SetHeader), arg0, arg1)
}

// SetTimeout mocks base method.
func (m *MockClient) SetTimeout(arg0 time.Duration) Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTimeout", arg0)
	ret0, _ := ret[0].(Client)
	return ret0
}

// SetTimeout indicates an expected call of SetTimeout.
func (mr *MockClientMockRecorder) SetTimeout(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTimeout", reflect.TypeOf((*MockClient)(nil).SetTimeout), arg0)
}

// SetTransport mocks base method.
func (m *MockClient) SetTransport(arg0 http.RoundTripper) Client {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTransport", arg0)
	ret0, _ := ret[0].(Client)
	return ret0
}

// SetTransport indicates an expected call of SetTransport.
func (mr *MockClientMockRecorder) SetTransport(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTransport", reflect.TypeOf((*MockClient)(nil).SetTransport), arg0)
}

// MockRequest is a mock of Request interface.
type MockRequest struct {
	ctrl     *gomock.Controller
	recorder *MockRequestMockRecorder
}

// MockRequestMockRecorder is the mock recorder for MockRequest.
type MockRequestMockRecorder struct {
	mock *MockRequest
}

// NewMockRequest creates a new mock instance.
func NewMockRequest(ctrl *gomock.Controller) *MockRequest {
	mock := &MockRequest{ctrl: ctrl}
	mock.recorder = &MockRequestMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRequest) EXPECT() *MockRequestMockRecorder {
	return m.recorder
}

// Delete mocks base method.
func (m *MockRequest) Delete(arg0 string) (*resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", arg0)
	ret0, _ := ret[0].(*resty.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockRequestMockRecorder) Delete(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRequest)(nil).Delete), arg0)
}

// Execute mocks base method.
func (m *MockRequest) Execute(arg0, arg1 string) (*resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", arg0, arg1)
	ret0, _ := ret[0].(*resty.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Execute indicates an expected call of Execute.
func (mr *MockRequestMockRecorder) Execute(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*MockRequest)(nil).Execute), arg0, arg1)
}

// Get mocks base method.
func (m *MockRequest) Get(arg0 string) (*resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0)
	ret0, _ := ret[0].(*resty.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockRequestMockRecorder) Get(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockRequest)(nil).Get), arg0)
}

// Patch mocks base method.
func (m *MockRequest) Patch(arg0 string) (*resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Patch", arg0)
	ret0, _ := ret[0].(*resty.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Patch indicates an expected call of Patch.
func (mr *MockRequestMockRecorder) Patch(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Patch", reflect.TypeOf((*MockRequest)(nil).Patch), arg0)
}

// Post mocks base method.
func (m *MockRequest) Post(arg0 string) (*resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Post", arg0)
	ret0, _ := ret[0].(*resty.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Post indicates an expected call of Post.
func (mr *MockRequestMockRecorder) Post(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Post", reflect.TypeOf((*MockRequest)(nil).Post), arg0)
}

// Put mocks base method.
func (m *MockRequest) Put(arg0 string) (*resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Put", arg0)
	ret0, _ := ret[0].(*resty.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Put indicates an expected call of Put.
func (mr *MockRequestMockRecorder) Put(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Put", reflect.TypeOf((*MockRequest)(nil).Put), arg0)
}

// SetAuthToken mocks base method.
func (m *MockRequest) SetAuthToken(arg0 string) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAuthToken", arg0)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetAuthToken indicates an expected call of SetAuthToken.
func (mr *MockRequestMockRecorder) SetAuthToken(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAuthToken", reflect.TypeOf((*MockRequest)(nil).SetAuthToken), arg0)
}

// SetBasicAuth mocks base method.
func (m *MockRequest) SetBasicAuth(arg0, arg1 string) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBasicAuth", arg0, arg1)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetBasicAuth indicates an expected call of SetBasicAuth.
func (mr *MockRequestMockRecorder) SetBasicAuth(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBasicAuth", reflect.TypeOf((*MockRequest)(nil).SetBasicAuth), arg0, arg1)
}

// SetBody mocks base method.
func (m *MockRequest) SetBody(arg0 any) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBody", arg0)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetBody indicates an expected call of SetBody.
func (mr *MockRequestMockRecorder) SetBody(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBody", reflect.TypeOf((*MockRequest)(nil).SetBody), arg0)
}

// SetContext mocks base method.
func (m *MockRequest) SetContext(arg0 context.Context) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetContext", arg0)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetContext indicates an expected call of SetContext.
func (mr *MockRequestMockRecorder) SetContext(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetContext", reflect.TypeOf((*MockRequest)(nil).SetContext), arg0)
}

// SetFormData mocks base method.
func (m *MockRequest) SetFormData(arg0 map[string]string) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFormData", arg0)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetFormData indicates an expected call of SetFormData.
func (mr *MockRequestMockRecorder) SetFormData(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFormData", reflect.TypeOf((*MockRequest)(nil).SetFormData), arg0)
}

// SetHeader mocks base method.
func (m *MockRequest) SetHeader(arg0, arg1 string) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeader", arg0, arg1)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetHeader indicates an expected call of SetHeader.
func (mr *MockRequestMockRecorder) SetHeader(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeader", reflect.TypeOf((*MockRequest)(nil).SetHeader), arg0, arg1)
}

// SetHeaders mocks base method.
func (m *MockRequest) SetHeaders(arg0 map[string]string) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetHeaders", arg0)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetHeaders indicates an expected call of SetHeaders.
func (mr *MockRequestMockRecorder) SetHeaders(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHeaders", reflect.TypeOf((*MockRequest)(nil).SetHeaders), arg0)
}

// SetPathParam mocks base method.
func (m *MockRequest) SetPathParam(arg0, arg1 string) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPathParam", arg0, arg1)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetPathParam indicates an expected call of SetPathParam.
func (mr *MockRequestMockRecorder) SetPathParam(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPathParam", reflect.TypeOf((*MockRequest)(nil).SetPathParam), arg0, arg1)
}

// SetPathParams mocks base method.
func (m *MockRequest) SetPathParams(arg0 map[string]string) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPathParams", arg0)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetPathParams indicates an expected call of SetPathParams.
func (mr *MockRequestMockRecorder) SetPathParams(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPathParams", reflect.TypeOf((*MockRequest)(nil).SetPathParams), arg0)
}

// SetQueryParam mocks base method.
func (m *MockRequest) SetQueryParam(arg0, arg1 string) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetQueryParam", arg0, arg1)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetQueryParam indicates an expected call of SetQueryParam.
func (mr *MockRequestMockRecorder) SetQueryParam(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetQueryParam", reflect.TypeOf((*MockRequest)(nil).SetQueryParam), arg0, arg1)
}

// SetQueryParams mocks base method.
func (m *MockRequest) SetQueryParams(arg0 map[string]string) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetQueryParams", arg0)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetQueryParams indicates an expected call of SetQueryParams.
func (mr *MockRequestMockRecorder) SetQueryParams(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetQueryParams", reflect.TypeOf((*MockRequest)(nil).SetQueryParams), arg0)
}

// SetQueryParamsFromValues mocks base method.
func (m *MockRequest) SetQueryParamsFromValues(arg0 url.Values) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetQueryParamsFromValues", arg0)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetQueryParamsFromValues indicates an expected call of SetQueryParamsFromValues.
func (mr *MockRequestMockRecorder) SetQueryParamsFromValues(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetQueryParamsFromValues", reflect.TypeOf((*MockRequest)(nil).SetQueryParamsFromValues), arg0)
}

// SetResult mocks base method.
func (m *MockRequest) SetResult(arg0 any) Request {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetResult", arg0)
	ret0, _ := ret[0].(Request)
	return ret0
}

// SetResult indicates an expected call of SetResult.
func (mr *MockRequestMockRecorder) SetResult(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetResult", reflect.TypeOf((*MockRequest)(nil).SetResult), arg0)
}
