package fcm

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestSendMulticast(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	tokens := []string{
		"e39_ECurNUu6kZP9pqFwIi:APA91bGkLyuhN9nTXsxcBFLD0J6lFjoKTdECHhJoi7D0SpEkTvStf0bWhnGJXMkT-SViFapj8tnunQMOo2OXD74p-freM5WzKf-ksadl6eNxqYVS8H4izcyAPk2N-t8L3zoAXK6bN30_",
		"dRc-A6Ozt0bmhympIOCUXu:APA91bHcV-KlOfNXgmtHJxR3GqjsbP-Jwcn5-jtpwEzkmsOaf5LcADKBmCTKMvWLYvU2LUpMYzzsfpA9JMGjeMApnHspsIVvR-d7xKuGlPkUpUl24lRPvuyWKclAN_gcC002tkAdcmiC",
		"fIYK7bQ40kz8vwj_wxMp65:APA91bH-BxdrMZVMrItOBTty4qhV2yCLj85rd9vonf_E55NEfBT0GGbLed8lwmBdzt3NuziAK_YnnRRdsvnm61GPMENi-55EKPvzNEVr6bqa9K9cLDEZNYbbp3j6SZzbj2dRxQLAGUzU",
	}

	response, err := instance.SendMulticastMessage(context.Background(), "Testing", "12345", "transaction", tokens, 1000)
	assert.Nil(t, err)
	assert.NotNil(t, response)
	t.Log(response)

	response, err = instance.SendMulticastMessageWithImage(context.Background(), "Testing", "12345", "transaction", tokens, "https://wallet-static.kryptogo.com/public/logo/KryptoGO-all_yellow.png", 1000)
	assert.Nil(t, err)
	assert.NotNil(t, response)
	t.Log(response)
}

func TestSendToTopic(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	topic := GetTopic("9c5a79fc1117310f976b53752659b61d", "en_US")
	err := instance.SendToTopic(context.Background(), topic, "Testing", "This is an announcement", 100)
	assert.Nil(t, err)
}
