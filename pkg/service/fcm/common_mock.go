// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/service/fcm (interfaces: IService)
//
// Generated by this command:
//
//	mockgen -package=fcm -self_package=github.com/kryptogo/kg-wallet-backend/pkg/service/fcm -destination=common_mock.go . IService
//

// Package fcm is a generated GoMock package.
package fcm

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockIService is a mock of IService interface.
type MockIService struct {
	ctrl     *gomock.Controller
	recorder *MockIServiceMockRecorder
}

// MockIServiceMockRecorder is the mock recorder for MockIService.
type MockIServiceMockRecorder struct {
	mock *MockIService
}

// NewMockIService creates a new mock instance.
func NewMockIService(ctrl *gomock.Controller) *MockIService {
	mock := &MockIService{ctrl: ctrl}
	mock.recorder = &MockIServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIService) EXPECT() *MockIServiceMockRecorder {
	return m.recorder
}

// SendMulticastMessage mocks base method.
func (m *MockIService) SendMulticastMessage(arg0 context.Context, arg1, arg2 string, arg3 domain.NotificationMessageType, arg4 []string, arg5 int) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMulticastMessage", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendMulticastMessage indicates an expected call of SendMulticastMessage.
func (mr *MockIServiceMockRecorder) SendMulticastMessage(arg0, arg1, arg2, arg3, arg4, arg5 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMulticastMessage", reflect.TypeOf((*MockIService)(nil).SendMulticastMessage), arg0, arg1, arg2, arg3, arg4, arg5)
}

// SendMulticastMessageWithImage mocks base method.
func (m *MockIService) SendMulticastMessageWithImage(arg0 context.Context, arg1, arg2 string, arg3 domain.NotificationMessageType, arg4 []string, arg5 string, arg6 int) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMulticastMessageWithImage", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendMulticastMessageWithImage indicates an expected call of SendMulticastMessageWithImage.
func (mr *MockIServiceMockRecorder) SendMulticastMessageWithImage(arg0, arg1, arg2, arg3, arg4, arg5, arg6 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMulticastMessageWithImage", reflect.TypeOf((*MockIService)(nil).SendMulticastMessageWithImage), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// SendToTopic mocks base method.
func (m *MockIService) SendToTopic(arg0 context.Context, arg1, arg2, arg3 string, arg4 int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendToTopic", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendToTopic indicates an expected call of SendToTopic.
func (mr *MockIServiceMockRecorder) SendToTopic(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendToTopic", reflect.TypeOf((*MockIService)(nil).SendToTopic), arg0, arg1, arg2, arg3, arg4)
}
