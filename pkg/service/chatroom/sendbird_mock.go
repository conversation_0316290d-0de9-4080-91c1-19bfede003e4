// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom (interfaces: Service)
//
// Generated by this command:
//
//	mockgen -package=chatroom -self_package=github.com/kryptogo/kg-wallet-backend/pkg/service/chatroom -destination=sendbird_mock.go . Service
//

// Package chatroom is a generated GoMock package.
package chatroom

import (
	context "context"
	reflect "reflect"

	resty "github.com/go-resty/resty/v2"
	domain "github.com/kryptogo/kg-wallet-backend/domain"
	sendbirdapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// CreateAUser mocks base method.
func (m *MockService) CreateAUser(arg0 context.Context, arg1 *domain.UserData) (*sendbirdapi.User, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAUser", arg0, arg1)
	ret0, _ := ret[0].(*sendbirdapi.User)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CreateAUser indicates an expected call of CreateAUser.
func (mr *MockServiceMockRecorder) CreateAUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAUser", reflect.TypeOf((*MockService)(nil).CreateAUser), arg0, arg1)
}

// IssueASessionToken mocks base method.
func (m *MockService) IssueASessionToken(arg0 context.Context, arg1 string, arg2 int64) (*sendbirdapi.IssueASessionTokenResponse, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IssueASessionToken", arg0, arg1, arg2)
	ret0, _ := ret[0].(*sendbirdapi.IssueASessionTokenResponse)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// IssueASessionToken indicates an expected call of IssueASessionToken.
func (mr *MockServiceMockRecorder) IssueASessionToken(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IssueASessionToken", reflect.TypeOf((*MockService)(nil).IssueASessionToken), arg0, arg1, arg2)
}

// ListChatrooms mocks base method.
func (m *MockService) ListChatrooms(arg0 context.Context, arg1 string) ([]*Chatroom, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListChatrooms", arg0, arg1)
	ret0, _ := ret[0].([]*Chatroom)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListChatrooms indicates an expected call of ListChatrooms.
func (mr *MockServiceMockRecorder) ListChatrooms(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListChatrooms", reflect.TypeOf((*MockService)(nil).ListChatrooms), arg0, arg1)
}

// UpdateAUser mocks base method.
func (m *MockService) UpdateAUser(arg0 context.Context, arg1 string, arg2, arg3 *string) (*sendbirdapi.User, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAUser", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*sendbirdapi.User)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// UpdateAUser indicates an expected call of UpdateAUser.
func (mr *MockServiceMockRecorder) UpdateAUser(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAUser", reflect.TypeOf((*MockService)(nil).UpdateAUser), arg0, arg1, arg2, arg3)
}
