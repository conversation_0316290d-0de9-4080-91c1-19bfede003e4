package chatroom

import (
	"context"
	"testing"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/stretchr/testify/assert"
	gomock "go.uber.org/mock/gomock"
)

func TestIssueASessionToken(t *testing.T) {
	ctrl := gomock.NewController(t)
	m := sendbirdapi.NewMockSendbirdClientI(ctrl)
	ctx := context.Background()
	resp := &sendbirdapi.IssueASessionTokenResponse{
		Token:     "token1",
		ExpiresAt: 123456789,
	}
	m.EXPECT().IssueASessionToken(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).Return(resp, &resty.Response{}, nil)

	s := GetService(m)
	tokenResp, restyResp, err := s.IssueASessionToken(ctx, "user1", 0)
	assert.Nil(t, err)
	assert.NotNil(t, restyResp)
	assert.Equal(t, "token1", tokenResp.Token)
	assert.Equal(t, int64(123456789), tokenResp.ExpiresAt)
}
