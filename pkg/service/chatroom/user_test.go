package chatroom

import (
	"context"
	"testing"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	"github.com/stretchr/testify/assert"
	gomock "go.uber.org/mock/gomock"
)

func TestCreateAUser(t *testing.T) {
	ctrl := gomock.NewController(t)
	m := sendbirdapi.NewMockSendbirdClientI(ctrl)
	ctx := context.Background()
	resp := &sendbirdapi.User{
		UserID:     "user1",
		Nickname:   "nickname1",
		ProfileURL: "profileURL1",
	}
	m.EXPECT().CreateAUser(gomock.Any(), gomock.Any()).Times(1).Return(resp, &resty.Response{}, nil)

	s := GetService(m)
	user, restyResp, err := s.CreateAUser(ctx, &domain.UserData{
		UserInfo: domain.UserInfo{
			UID:         "user1",
			DisplayName: "nickname1",
			Avatar: &domain.Avatar{
				AvatarURL: "profileURL1",
			},
		},
	})
	assert.Nil(t, err)
	assert.NotNil(t, restyResp)
	assert.Equal(t, "user1", user.UserID)
	assert.Equal(t, "nickname1", user.Nickname)
	assert.Equal(t, "profileURL1", user.ProfileURL)
}

func TestUpdateAUser(t *testing.T) {
	ctrl := gomock.NewController(t)
	m := sendbirdapi.NewMockSendbirdClientI(ctrl)
	ctx := context.Background()
	resp := &sendbirdapi.User{
		UserID:     "user1",
		Nickname:   "nickname1",
		ProfileURL: "profileURL1",
	}
	m.EXPECT().UpdateAUser(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).Return(resp, &resty.Response{}, nil)

	s := GetService(m)
	nickname := "nickname1"
	profileImageURL := "profileURL1"
	user, restyResp, err := s.UpdateAUser(ctx, "user1", &nickname, &profileImageURL)
	assert.Nil(t, err)
	assert.NotNil(t, restyResp)
	assert.Equal(t, "user1", user.UserID)
	assert.Equal(t, "nickname1", user.Nickname)
	assert.Equal(t, "profileURL1", user.ProfileURL)
}
