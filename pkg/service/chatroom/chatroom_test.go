package chatroom

import (
	"context"
	"testing"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/assert"
	gomock "go.uber.org/mock/gomock"
)

func TestListChatrooms(t *testing.T) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateNftAssets(rdb.Get()))
	assert.Nil(t, rdbtest.CreateNftUserAmounts(rdb.Get()))

	users, userIDs := dbtest.Users()
	uid := userIDs[0]

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	ctrl := gomock.NewController(t)
	m := sendbirdapi.NewMockSendbirdClientI(ctrl)
	ctx := context.Background()
	openChannelResp := &sendbirdapi.ListOpenChannelsResponse{
		Channels: []*sendbirdapi.OpenChannel{
			{
				Channel: &sendbirdapi.Channel{
					Name:       "name1",
					ChannelURL: "channel1",
					CoverURL:   "cover1",
					CustomType: "custom1",
					Data: `
					{
						"background_image":"background1",
						"description":"description1"
					}`,
					Operators: []*sendbirdapi.User{
						{
							UserID: uid,
						},
					},
				},
			},
			{
				Channel: &sendbirdapi.Channel{
					Name:       "name2",
					ChannelURL: "channel2",
					CoverURL:   "cover2",
					CustomType: "custom2",
				},
			},
		},
		Next: "",
	}
	m.EXPECT().ListOpenChannels(gomock.Any(), gomock.Any()).Times(1).Return(openChannelResp, &resty.Response{}, nil)

	groupChannelResp := &sendbirdapi.ListGroupChannelsResponse{
		Channels: []*sendbirdapi.GroupChannel{
			{
				Channel: &sendbirdapi.Channel{
					Name:       "name1",
					ChannelURL: "channel1",
					CoverURL:   "cover1",
					CustomType: "custom1",
					Data: `
					{
						"background_image":"background1","description":"description1",
						"qualification": {
							"conditions": [
								{
									"chain_id": "ronin","contract_address":"******************************************"
								},
								{
									"chain_id": "eth","contract_address":"******************************************"
								}
							],
							"logic_operator": "or"
						}
					}`,
					Operators: []*sendbirdapi.User{
						{
							UserID: uid,
						},
					},
				},
			},
			{
				Channel: &sendbirdapi.Channel{
					Name:       "name2",
					ChannelURL: "channel2",
					CoverURL:   "cover2",
					CustomType: "custom2",
					Data: `
					{
						"background_image":"background2",
						"description":"description2",
						"qualification": {
							"conditions": [
								{
									"chain_id": "ronin",
									"contract_address": "******************************************"
								},
								{
									"chain_id": "eth",
									"contract_address": "******************************************"
								}
							],
							"logic_operator": "and"
						}
					}`,
				},
			},
		},
		Next: "",
	}
	m.EXPECT().ListGroupChannels(gomock.Any(), gomock.Any()).Times(1).Return(groupChannelResp, &resty.Response{}, nil)

	s := GetService(m)
	chatrooms, restyResp, err := s.ListChatrooms(ctx, uid)
	assert.Nil(t, err)
	assert.NotNil(t, restyResp)
	assert.Equal(t, 4, len(chatrooms))

	chatroom1 := chatrooms[0]
	chatroom2 := chatrooms[1]
	chatroom3 := chatrooms[2]
	chatroom4 := chatrooms[3]

	// open channel 1
	assert.Equal(t, "name1", chatroom1.Name)
	assert.Equal(t, "channel1", chatroom1.ChannelURL)
	assert.Equal(t, "cover1", chatroom1.CoverURL)
	assert.Equal(t, "custom1", chatroom1.CustomType)
	assert.Equal(t, "open", chatroom1.ChannelType.String())
	assert.Equal(t, "background1", chatroom1.Data.BackgroundImage)
	assert.Equal(t, "description1", chatroom1.Data.Description)
	assert.Equal(t, RoleMod, chatroom1.Role)
	assert.Equal(t, true, chatroom1.IsQualified)

	// open channel 2
	assert.Equal(t, "name2", chatroom2.Name)
	assert.Equal(t, "channel2", chatroom2.ChannelURL)
	assert.Equal(t, "cover2", chatroom2.CoverURL)
	assert.Equal(t, "custom2", chatroom2.CustomType)
	assert.Equal(t, "open", chatroom2.ChannelType.String())
	assert.Equal(t, "", chatroom2.Data.BackgroundImage)
	assert.Equal(t, "", chatroom2.Data.Description)
	assert.Equal(t, RoleMember, chatroom2.Role)
	assert.Equal(t, true, chatroom2.IsQualified)

	// group channel 1
	assert.Equal(t, "name1", chatroom3.Name)
	assert.Equal(t, "channel1", chatroom3.ChannelURL)
	assert.Equal(t, "cover1", chatroom3.CoverURL)
	assert.Equal(t, "custom1", chatroom3.CustomType)
	assert.Equal(t, "group", chatroom3.ChannelType.String())
	assert.Equal(t, "background1", chatroom3.Data.BackgroundImage)
	assert.Equal(t, "description1", chatroom3.Data.Description)
	assert.Equal(t, "ronin", chatroom3.Data.Qualification.Conditions[0].ChainID)
	assert.Equal(t, "******************************************", chatroom3.Data.Qualification.Conditions[0].ContractAddress)
	assert.Equal(t, "Axie Infinity Axies", chatroom3.Data.Qualification.Conditions[0].CollectionName)
	assert.Equal(t, "https://assets.axieinfinity.com/axies/3724537/axie/axie-full-transparent.png", chatroom3.Data.Qualification.Conditions[0].CollectionImageURL)
	assert.Equal(t, true, chatroom3.Data.Qualification.Conditions[0].Matched)
	assert.Equal(t, "eth", chatroom3.Data.Qualification.Conditions[1].ChainID)
	assert.Equal(t, "******************************************", chatroom3.Data.Qualification.Conditions[1].ContractAddress)
	assert.Equal(t, "Bored Ape Yacht Club", chatroom3.Data.Qualification.Conditions[1].CollectionName)
	assert.Equal(t, "https://i.seadn.io/gae/Ju9CkWtV-1Okvf45wo8UctR-M9He2PjILP0oOvxE89AyiPPGtrR3gysu1Zgy0hjd2xKIgjJJtWIc0ybj4Vd7wv8t3pxDGHoJBzDB?auto=format&dpr=1&w=3840", chatroom3.Data.Qualification.Conditions[1].CollectionImageURL)
	assert.Equal(t, false, chatroom3.Data.Qualification.Conditions[1].Matched)
	assert.Equal(t, "or", chatroom3.Data.Qualification.LogicOperator.String())
	assert.Equal(t, RoleMod, chatroom3.Role)
	assert.Equal(t, true, chatroom3.IsQualified)

	// group channel 2
	assert.Equal(t, "name2", chatroom4.Name)
	assert.Equal(t, "channel2", chatroom4.ChannelURL)
	assert.Equal(t, "cover2", chatroom4.CoverURL)
	assert.Equal(t, "custom2", chatroom4.CustomType)
	assert.Equal(t, "group", chatroom4.ChannelType.String())
	assert.Equal(t, "background2", chatroom4.Data.BackgroundImage)
	assert.Equal(t, "description2", chatroom4.Data.Description)
	assert.Equal(t, "ronin", chatroom4.Data.Qualification.Conditions[0].ChainID)
	assert.Equal(t, "******************************************", chatroom4.Data.Qualification.Conditions[0].ContractAddress)
	assert.Equal(t, "Axie Infinity Axies", chatroom4.Data.Qualification.Conditions[0].CollectionName)
	assert.Equal(t, "https://assets.axieinfinity.com/axies/3724537/axie/axie-full-transparent.png", chatroom4.Data.Qualification.Conditions[0].CollectionImageURL)
	assert.Equal(t, true, chatroom4.Data.Qualification.Conditions[0].Matched)
	assert.Equal(t, "eth", chatroom4.Data.Qualification.Conditions[1].ChainID)
	assert.Equal(t, "******************************************", chatroom4.Data.Qualification.Conditions[1].ContractAddress)
	assert.Equal(t, "Bored Ape Yacht Club", chatroom4.Data.Qualification.Conditions[1].CollectionName)
	assert.Equal(t, "https://i.seadn.io/gae/Ju9CkWtV-1Okvf45wo8UctR-M9He2PjILP0oOvxE89AyiPPGtrR3gysu1Zgy0hjd2xKIgjJJtWIc0ybj4Vd7wv8t3pxDGHoJBzDB?auto=format&dpr=1&w=3840", chatroom4.Data.Qualification.Conditions[1].CollectionImageURL)
	assert.Equal(t, false, chatroom4.Data.Qualification.Conditions[1].Matched)
	assert.Equal(t, "and", chatroom4.Data.Qualification.LogicOperator.String())
	assert.Equal(t, RoleMember, chatroom4.Role)
	assert.Equal(t, false, chatroom4.IsQualified)
}
