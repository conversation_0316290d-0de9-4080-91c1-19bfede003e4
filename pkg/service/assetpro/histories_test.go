package assetpro

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestGetRemainLimit(t *testing.T) {

	tx.Init(rdb.GormRepo())
	rdb.Reset()

	// org
	orgID := 1
	assert.Nil(t, dbtest.CreateStudioOrganizations(rdb.Get()))

	// studio user
	uid := util.RandString(10)
	email := util.RandEmail()
	assert.Nil(t, dbtest.CreateStudioUsers(rdb.Get(), uid, &email))

	// asset pro tx logs
	assert.Nil(t, dbtest.CreateAssetProTxLogs(rdb.Get(), uid))

	// clear cache
	now := util.NowInCST()
	cache.Del(cache.ComposeDailyUsedLimitCacheKey(orgID, uid, now))

	// repos
	InitTransfer(rdb.GormRepo())
	organization.Init(organization.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})

	// get remain limit
	remainLimit, kgErr := GetRemainLimit(context.Background(), orgID, uid)
	assert.Nil(t, kgErr)
	val, ok := remainLimit.Float64()
	assert.True(t, ok)
	assert.Equal(t, 0.0, val)
}
