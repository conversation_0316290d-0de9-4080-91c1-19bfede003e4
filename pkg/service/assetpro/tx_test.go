package assetpro

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/customer"
	customerrepo "github.com/kryptogo/kg-wallet-backend/pkg/service/customer/repo"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type TransferSuite struct {
	suite.Suite
	OrgID          int
	LocalhostOrgID int
	UID            string
	Email          string
	Srv            *http.Server
}

func (suite *TransferSuite) SetupSuite() {
	rdb.Reset()

	tx.Init(rdb.GormRepo())
	alchemyapi.InitDefault()
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})
	alchemyapi.InitDefault()
	tx.Init(rdb.GormRepo())

	// org
	orgID := 1
	localhostOrgID := 2
	suite.OrgID = orgID
	suite.LocalhostOrgID = localhostOrgID
	err := dbtest.CreateStudioOrganizations(rdb.Get())
	assert.Nil(suite.T(), err)
	err = dbtest.CreateStudioOrganizationWallets(rdb.Get())
	assert.Nil(suite.T(), err)
	err = dbtest.CreateAssetPrices(rdb.Get())
	assert.Nil(suite.T(), err)

	// firebase user
	users, uid, _, email := test.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err = firebase.BatchCreateUsersBySeed(users)
	assert.Nil(suite.T(), err)
	suite.UID = uid
	suite.Email = email

	// studio user
	err = dbtest.CreateStudioUsers(rdb.Get(), uid, &email)
	assert.Nil(suite.T(), err)

	// clear cache
	cache.Del(cache.ComposeDailyUsedLimitCacheKey(orgID, uid, util.NowInCST()))
	cache.Del(cache.ComposeDailyUsedLimitCacheKey(localhostOrgID, uid, util.NowInCST()))

	// customer
	err = dbtest.CreateCustomers(rdb.Get(), []*model.Customer{
		{OrganizationID: orgID, UID: uid, KYCStatus: domain.KycStatusVerified},
		{OrganizationID: localhostOrgID, UID: uid, KYCStatus: domain.KycStatusVerified},
	})
	assert.Nil(suite.T(), err)

	// signing server
	TEST_SIGNING_PORT := testutil.UnusedPort(suite.T())
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", signing.SignEvmTransaction)
	rSigning.POST("/v1/sign/tron", signing.SignTronTransaction)

	// create HTTP server for graceful shutdown
	suite.Srv = &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := suite.Srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			suite.T().Logf("Signing server terminated with error: %v\n", err)
		}
	}()

	// wait for server to start
	time.Sleep(1 * time.Second)

	// repos
	InitTransfer(rdb.GormRepo())
	organization.Init(organization.InitParam{
		StudioOrgRepo: rdb.GormRepo(),
	})
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
}

func (suite *TransferSuite) TearDownSuite() {
	err := suite.Srv.Shutdown(context.Background())
	assert.Nil(suite.T(), err)
	fmt.Println("Finish Shutdown")
}

func (suite *TransferSuite) SetupTest() {
	customer.Init(customerrepo.NewCustomerRepo())
}

func (suite *TransferSuite) TearDownTest() {
}

func (suite *TransferSuite) TestValidateAmount() {
	{
		// amount is 0
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(),
			TransferFungibleTokenWithoutApprovalRequest{
				OrganizationID:  suite.OrgID,
				OperatorUID:     suite.UID,
				ChainID:         "holesky",
				To:              "******************************************",
				ContractAddress: "******************************************", // usdt on localhost
				Amount:          decimal.Zero,
				Recipient:       Recipient{},
			})
		assert.NotNil(suite.T(), kgError)
		assert.Empty(suite.T(), serialID)
		assert.Equal(suite.T(), "amount must be greater than zero", kgError.String())
		assert.Equal(suite.T(), 400, kgError.HttpStatus)
		assert.Equal(suite.T(), 1004, kgError.Code)
	}
	{
		// amount is negative
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(),
			TransferFungibleTokenWithoutApprovalRequest{
				OrganizationID:  suite.OrgID,
				OperatorUID:     suite.UID,
				ChainID:         "holesky",
				To:              "******************************************",
				ContractAddress: "******************************************", // usdt on localhost
				Amount:          decimal.NewFromInt(-1),
				Recipient:       Recipient{},
			})
		assert.NotNil(suite.T(), kgError)
		assert.Empty(suite.T(), serialID)
		assert.Equal(suite.T(), "amount must be greater than zero", kgError.String())
		assert.Equal(suite.T(), 400, kgError.HttpStatus)
		assert.Equal(suite.T(), 1004, kgError.Code)
	}
}

func (suite *TransferSuite) TestValidateCustomer() {
	{
		// user not found
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(),
			TransferFungibleTokenWithoutApprovalRequest{
				OrganizationID:  suite.OrgID,
				OperatorUID:     suite.UID,
				ChainID:         "holesky",
				To:              "******************************************",
				ContractAddress: "******************************************", // usdt on localhost
				Amount:          decimal.NewFromInt(1),
				Recipient: Recipient{
					Email: util.Ptr("<EMAIL>"),
				},
			})
		assert.NotNil(suite.T(), kgError)
		assert.Empty(suite.T(), serialID)
		assert.Equal(suite.T(), "user not found", kgError.String())
		assert.Equal(suite.T(), 404, kgError.HttpStatus)
		assert.Equal(suite.T(), 2100, kgError.Code)
	}
	{
		// customer wallet address not matched
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(),
			TransferFungibleTokenWithoutApprovalRequest{
				OrganizationID:  suite.OrgID,
				OperatorUID:     suite.UID,
				ChainID:         "holesky",
				To:              "******************************************",
				ContractAddress: "******************************************", // usdt on localhost
				Amount:          decimal.NewFromInt(1),
				Recipient: Recipient{
					Email: util.Ptr(suite.Email),
				},
			})
		assert.NotNil(suite.T(), kgError)
		assert.Empty(suite.T(), serialID)
		assert.Equal(suite.T(), "wallet address not matched", kgError.String())
		assert.Equal(suite.T(), 400, kgError.HttpStatus)
		assert.Equal(suite.T(), 6008, kgError.Code)
	}
}

func (suite *TransferSuite) TestTransferLimit() {
	{
		// transfer usdt on localhost, exceed daily limit
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(),
			TransferFungibleTokenWithoutApprovalRequest{
				OrganizationID:  suite.OrgID,
				OperatorUID:     suite.UID,
				ChainID:         "holesky",
				To:              "******************************************",
				ContractAddress: "******************************************", // usdt on localhost
				Amount:          decimal.NewFromInt(10_000),
				Recipient: Recipient{
					Email: util.Ptr(suite.Email),
				},
			})
		assert.NotNil(suite.T(), kgError)
		assert.Empty(suite.T(), serialID)
		assert.Equal(suite.T(), "exceed daily limit", kgError.String())
		assert.Equal(suite.T(), 400, kgError.HttpStatus)
		assert.Equal(suite.T(), 6003, kgError.Code)
	}
}

func (suite *TransferSuite) TestUnsupportedChain() {
	{
		// transfer solana, not supported
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(),
			TransferFungibleTokenWithoutApprovalRequest{
				OrganizationID:  suite.OrgID,
				OperatorUID:     suite.UID,
				ChainID:         "sol",
				To:              "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
				ContractAddress: "******************************************", // usdt on localhost
				Amount:          decimal.NewFromInt(1),
				Recipient: Recipient{
					Email: util.Ptr(suite.Email),
				},
			})
		assert.NotNil(suite.T(), kgError)
		assert.Empty(suite.T(), serialID)
		assert.Equal(suite.T(), "chainID not supported", kgError.String())
		assert.Equal(suite.T(), 400, kgError.HttpStatus)
		assert.Equal(suite.T(), 1004, kgError.Code)
	}
}

func (suite *TransferSuite) TestTransferInLocalhost() {
	{
		// transfer unsupported token
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(), TransferFungibleTokenWithoutApprovalRequest{
			OrganizationID:  suite.OrgID,
			OperatorUID:     suite.UID,
			ChainID:         "holesky",
			To:              "******************************************",
			ContractAddress: "******************************************",
			Amount:          decimal.NewFromInt(1),
			Recipient: Recipient{
				Email: util.Ptr(suite.Email),
			},
		})
		assert.NotNil(suite.T(), kgError)
		assert.Empty(suite.T(), serialID)
		assert.Equal(suite.T(), "token not supported", kgError.String())
		assert.Equal(suite.T(), 400, kgError.HttpStatus)
		assert.Equal(suite.T(), 6005, kgError.Code)
	}
	{
		// transfer usdt on localhost, using localhost org
		// FIXME: should transfer all tokens to org wallet (******************************************)
		// then use org to transfer, and remove localhost org
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(), TransferFungibleTokenWithoutApprovalRequest{
			OrganizationID:  suite.LocalhostOrgID,
			OperatorUID:     suite.UID,
			ChainID:         "holesky",
			To:              "******************************************",
			ContractAddress: "******************************************", // usdt on localhost
			Amount:          decimal.NewFromFloat(0.0001),
			Recipient: Recipient{
				Email: util.Ptr(suite.Email),
			},
		})
		assert.Nil(suite.T(), kgError)
		assert.NotEmpty(suite.T(), serialID)

		// wait for tx to be confirmed, check results
		// check tx log, check transfer limit
		txLog, kgErr := repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
		assert.Nil(suite.T(), kgErr)
		assert.Equal(suite.T(), domain.AssetProTxLogStatusSending, txLog.Status)
		assert.Equal(suite.T(), serialID, txLog.SerialID)
		assert.NotEmpty(suite.T(), txLog.TxHash)
		assert.Equal(suite.T(), "0.0001", txLog.Amount.String())
		assert.Equal(suite.T(), "0.0001", txLog.UsdAmount.String())
		assert.Equal(suite.T(), "******************************************", txLog.ToAddress)
		assert.Equal(suite.T(), "******************************************", txLog.FromAddress)
		assert.Equal(suite.T(), "******************************************", txLog.ContractAddress)
		assert.Equal(suite.T(), "holesky", txLog.ChainID)
		assert.Equal(suite.T(), suite.LocalhostOrgID, txLog.OrganizationID)
		assert.Equal(suite.T(), suite.UID, txLog.OperatorUID)
		assert.Equal(suite.T(), "USDT", txLog.Token)
		assert.Greater(suite.T(), txLog.SubmitTime, 0)

		// check transfer limit
		dailyUsedLimit, err := cache.GetDailyUsedLimit(context.Background(), suite.LocalhostOrgID, suite.UID, util.NowInCST())
		assert.Nil(suite.T(), err)
		assert.NotNil(suite.T(), dailyUsedLimit)
		if dailyUsedLimit != nil {
			assert.Equal(suite.T(), "0.0001", dailyUsedLimit.String())
		}

		tx.Get().WaitTxConfirmed(context.Background(), txLog.ChainID, txLog.TxHash, 10)

		// Poll every second for up to 30 seconds until TransferTime is set
		timeout := time.After(60 * time.Second)
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()
		done := false
		for !done {
			select {
			case <-timeout:
				suite.T().Fatal("Timed out waiting for TransferTime to be set")
				done = true
			case <-ticker.C:
				txLog, kgErr = repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
				assert.Nil(suite.T(), kgErr)
				if txLog.Status == domain.AssetProTxLogStatusSendSuccess {
					assert.Greater(suite.T(), *txLog.TransferTime, txLog.SubmitTime)
					done = true
				}
			}
		}
	}
	{
		// transfer eth on localhost
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(), TransferFungibleTokenWithoutApprovalRequest{
			OrganizationID:  suite.LocalhostOrgID,
			OperatorUID:     suite.UID,
			ChainID:         "holesky",
			To:              "******************************************",
			ContractAddress: "", // eth on localhost
			Amount:          decimal.NewFromFloat(0.0001),
			Recipient: Recipient{
				Email: util.Ptr(suite.Email),
			},
		})
		assert.Nil(suite.T(), kgError)
		assert.NotEmpty(suite.T(), serialID)

		// wait for tx to be confirmed, check results
		// check tx log, check transfer limit
		txlog, kgErr := repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
		assert.Nil(suite.T(), kgErr)
		assert.Equal(suite.T(), domain.AssetProTxLogStatusSending, txlog.Status)
		assert.NotEmpty(suite.T(), txlog.TxHash)
		assert.Equal(suite.T(), "0.0001", txlog.Amount.String())
		assert.Equal(suite.T(), "0.26", txlog.UsdAmount.String())
		assert.Equal(suite.T(), "******************************************", txlog.ToAddress)
		assert.Equal(suite.T(), "******************************************", txlog.FromAddress)
		assert.Equal(suite.T(), "", txlog.ContractAddress)
		assert.Equal(suite.T(), "holesky", txlog.ChainID)
		assert.Equal(suite.T(), suite.LocalhostOrgID, txlog.OrganizationID)
		assert.Equal(suite.T(), suite.UID, txlog.OperatorUID)
		assert.Equal(suite.T(), "ETH", txlog.Token)
		assert.Greater(suite.T(), txlog.SubmitTime, 0)

		// check transfer limit
		dailyUsedLimit, err := cache.GetDailyUsedLimit(context.Background(), suite.LocalhostOrgID, suite.UID, util.NowInCST())
		assert.Nil(suite.T(), err)
		assert.NotNil(suite.T(), dailyUsedLimit)
		assert.Equal(suite.T(), "0.2601", dailyUsedLimit.String())
	}
}

func (suite *TransferSuite) TestTransferInSepolia() {
	testutil.RequireConfigOrSkip(suite.T(), "TEST_3RD_PARTY_API")

	{
		// transfer usdt on sepolia
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(), TransferFungibleTokenWithoutApprovalRequest{
			OrganizationID:  suite.OrgID,
			OperatorUID:     suite.UID,
			ChainID:         "sepolia",
			To:              "******************************************",
			ContractAddress: "******************************************", // usdt on sepolia
			Amount:          decimal.NewFromFloat(0.0001),
			Recipient: Recipient{
				Email: util.Ptr(suite.Email),
			},
		})
		assert.Nil(suite.T(), kgError)
		assert.NotEmpty(suite.T(), serialID)

		// wait for tx to be confirmed, check results
		// check tx log, check transfer limit
		txlog, kgErr := repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
		assert.Nil(suite.T(), kgErr)
		assert.Equal(suite.T(), domain.AssetProTxLogStatusSending, txlog.Status)
		assert.NotEmpty(suite.T(), txlog.TxHash)
		assert.Equal(suite.T(), "0.0001", txlog.Amount.String())
		assert.Equal(suite.T(), "0.0001", txlog.UsdAmount.String())
		assert.Equal(suite.T(), "******************************************", txlog.ToAddress)
		assert.Equal(suite.T(), "******************************************", txlog.FromAddress)
		assert.Equal(suite.T(), "******************************************", txlog.ContractAddress)
		assert.Equal(suite.T(), "sepolia", txlog.ChainID)
		assert.Equal(suite.T(), suite.OrgID, txlog.OrganizationID)
		assert.Equal(suite.T(), suite.UID, txlog.OperatorUID)
		assert.Equal(suite.T(), "USDT", txlog.Token)
		assert.Greater(suite.T(), txlog.SubmitTime, 0)

		// check transfer limit
		dailyUsedLimit, err := cache.GetDailyUsedLimit(context.Background(), suite.OrgID, suite.UID, util.NowInCST())
		assert.Nil(suite.T(), err)
		assert.NotNil(suite.T(), dailyUsedLimit)
		assert.Equal(suite.T(), "0.0001", dailyUsedLimit.String())

		if config.GetBool("TEST_3RD_PARTY_API") {
			time.Sleep(15 * time.Second) // wait for tx to be confirmed
			txlog, kgErr = repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
			assert.Nil(suite.T(), kgErr)
			assert.Equal(suite.T(), domain.AssetProTxLogStatusSendSuccess, txlog.Status)
			assert.Greater(suite.T(), *txlog.TransferTime, txlog.SubmitTime)
		}
	}
	{
		// transfer eth on sepolia
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(), TransferFungibleTokenWithoutApprovalRequest{
			OrganizationID:  suite.OrgID,
			OperatorUID:     suite.UID,
			ChainID:         "sepolia",
			To:              "******************************************",
			ContractAddress: "", // eth on sepolia
			Amount:          decimal.NewFromFloat(0.0001),
			Recipient: Recipient{
				Email: util.Ptr(suite.Email),
			},
		})
		assert.Nil(suite.T(), kgError)
		assert.NotEmpty(suite.T(), serialID)

		// wait for tx to be confirmed, check results
		// check tx log, check transfer limit
		txlog, kgErr := repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
		assert.Nil(suite.T(), kgErr)
		assert.Equal(suite.T(), domain.AssetProTxLogStatusSending, txlog.Status)
		assert.NotEmpty(suite.T(), txlog.TxHash)
		assert.Equal(suite.T(), "0.0001", txlog.Amount.String())
		assert.Equal(suite.T(), "0.26", txlog.UsdAmount.String())
		assert.Equal(suite.T(), "******************************************", txlog.ToAddress)
		assert.Equal(suite.T(), "******************************************", txlog.FromAddress)
		assert.Equal(suite.T(), "", txlog.ContractAddress)
		assert.Equal(suite.T(), "sepolia", txlog.ChainID)
		assert.Equal(suite.T(), suite.OrgID, txlog.OrganizationID)
		assert.Equal(suite.T(), suite.UID, txlog.OperatorUID)
		assert.Equal(suite.T(), "ETH", txlog.Token)
		assert.Greater(suite.T(), txlog.SubmitTime, 0)

		// check transfer limit
		dailyUsedLimit, err := cache.GetDailyUsedLimit(context.Background(), suite.OrgID, suite.UID, util.NowInCST())
		assert.Nil(suite.T(), err)
		assert.NotNil(suite.T(), dailyUsedLimit)
		assert.Equal(suite.T(), "0.2601", dailyUsedLimit.String())

		if config.GetBool("TEST_3RD_PARTY_API") {
			time.Sleep(15 * time.Second) // wait for tx to be confirmed
			txlog, kgErr = repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
			assert.Nil(suite.T(), kgErr)
			assert.Equal(suite.T(), domain.AssetProTxLogStatusSendSuccess, txlog.Status)
			assert.Greater(suite.T(), *txlog.TransferTime, txlog.SubmitTime)
		}
	}
}

func (suite *TransferSuite) TestTransferInShasta() {
	{
		// transfer usdt on shasta
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(), TransferFungibleTokenWithoutApprovalRequest{
			OrganizationID:  suite.OrgID,
			OperatorUID:     suite.UID,
			ChainID:         "shasta",
			To:              "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn",
			ContractAddress: "TSdZwNqpHofzP6BsBKGQUWdBeJphLmF6id", // usdt on localhost
			Amount:          decimal.NewFromFloat(0.0001),
			Recipient: Recipient{
				Email: util.Ptr(suite.Email),
			},
		})
		assert.Nil(suite.T(), kgError)
		assert.NotEmpty(suite.T(), serialID)

		// wait for tx to be confirmed, check results
		// check tx log, check transfer limit
		txlog, kgErr := repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
		assert.Nil(suite.T(), kgErr)
		assert.Equal(suite.T(), domain.AssetProTxLogStatusSending, txlog.Status)
		assert.NotEmpty(suite.T(), txlog.TxHash)
		assert.Equal(suite.T(), "0.0001", txlog.Amount.String())
		assert.Equal(suite.T(), "0.0001", txlog.UsdAmount.String())
		assert.Equal(suite.T(), "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", txlog.ToAddress)
		assert.Equal(suite.T(), "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7", txlog.FromAddress)
		assert.Equal(suite.T(), "TSdZwNqpHofzP6BsBKGQUWdBeJphLmF6id", txlog.ContractAddress)
		assert.Equal(suite.T(), "shasta", txlog.ChainID)
		assert.Equal(suite.T(), suite.OrgID, txlog.OrganizationID)
		assert.Equal(suite.T(), suite.UID, txlog.OperatorUID)
		assert.Equal(suite.T(), "USDC", txlog.Token)
		assert.Greater(suite.T(), txlog.SubmitTime, 0)

		// help us to check flaky test
		log.Println("cache.ComposeDailyUsedLimitCacheKey:",
			cache.ComposeDailyUsedLimitCacheKey(suite.OrgID, suite.UID, util.NowInCST()))

		// check transfer limit
		dailyUsedLimit, err := cache.GetDailyUsedLimit(context.Background(), suite.OrgID, suite.UID, util.NowInCST())
		assert.Nil(suite.T(), err)
		assert.NotNil(suite.T(), dailyUsedLimit)
		assert.Equal(suite.T(), "0.0001", dailyUsedLimit.String())

		// Poll every second for up to 30 seconds until TransferTime is set
		timeout := time.After(60 * time.Second)
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()
		done := false
		for !done {
			select {
			case <-timeout:
				suite.T().Fatal("Timed out waiting for TransferTime to be set")
				done = true
			case <-ticker.C:
				txlog, kgErr = repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
				assert.Nil(suite.T(), kgErr)
				if txlog.Status == domain.AssetProTxLogStatusSendSuccess {
					assert.Greater(suite.T(), *txlog.TransferTime, txlog.SubmitTime)
					done = true
				}
			}
		}
	}
	{
		// transfer trx on shasta
		serialID, kgError := TransferFungibleTokenWithoutApproval(context.Background(), TransferFungibleTokenWithoutApprovalRequest{
			OrganizationID:  suite.OrgID,
			OperatorUID:     suite.UID,
			ChainID:         "shasta",
			To:              "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn",
			ContractAddress: "", // trx on shasta
			Amount:          decimal.NewFromFloat(0.01),
			Recipient: Recipient{
				Email: util.Ptr(suite.Email),
			},
		})
		assert.Nil(suite.T(), kgError)
		if kgError != nil {
			suite.T().Fatal(kgError.Error)
		}
		assert.NotEmpty(suite.T(), serialID)

		// wait for tx to be confirmed, check results
		// check tx log, check transfer limit
		txlog, kgErr := repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
		assert.Nil(suite.T(), kgErr)
		assert.Equal(suite.T(), domain.AssetProTxLogStatusSending, txlog.Status)
		assert.NotEmpty(suite.T(), txlog.TxHash)
		assert.Equal(suite.T(), "0.01", txlog.Amount.String())
		assert.Equal(suite.T(), "0.0012", txlog.UsdAmount.String())
		assert.Equal(suite.T(), "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", txlog.ToAddress)
		assert.Equal(suite.T(), "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7", txlog.FromAddress)
		assert.Equal(suite.T(), "", txlog.ContractAddress)
		assert.Equal(suite.T(), "shasta", txlog.ChainID)
		assert.Equal(suite.T(), suite.OrgID, txlog.OrganizationID)
		assert.Equal(suite.T(), suite.UID, txlog.OperatorUID)
		assert.Equal(suite.T(), "TRX", txlog.Token)
		assert.Greater(suite.T(), txlog.SubmitTime, 0)

		// help us to check flaky test
		log.Println("cache.ComposeDailyUsedLimitCacheKey:",
			cache.ComposeDailyUsedLimitCacheKey(suite.OrgID, suite.UID, util.NowInCST()))

		// check transfer limit
		dailyUsedLimit, err := cache.GetDailyUsedLimit(context.Background(), suite.OrgID, suite.UID, util.NowInCST())
		assert.Nil(suite.T(), err)
		assert.NotNil(suite.T(), dailyUsedLimit)
		assert.Equal(suite.T(), "0.0013", dailyUsedLimit.String())

		// Poll every second for up to 30 seconds until TransferTime is set
		timeout := time.After(60 * time.Second)
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()
		done := false
		for !done {
			select {
			case <-timeout:
				suite.T().Fatal("Timed out waiting for TransferTime to be set")
				done = true
			case <-ticker.C:
				txlog, kgErr = repo.GetAssetProTxLogBySerialID(context.Background(), serialID)
				assert.Nil(suite.T(), kgErr)
				if txlog.Status == domain.AssetProTxLogStatusSendSuccess {
					assert.Greater(suite.T(), *txlog.TransferTime, txlog.SubmitTime)
					done = true
				}
			}
		}
	}
}

func TestTransferSuite(t *testing.T) {
	suite.Run(t, new(TransferSuite))
}
