package jwt

import (
	"os"
	"testing"
	"time"

	"github.com/golang-jwt/jwt"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/stretchr/testify/assert"
)

func TestKgTokenJwtStrategy_CreateToken(t *testing.T) {
	// Mock config value using environment variable
	os.Setenv("ACCESS_TOKEN_EXP_MINUTES", "60")

	// Initialize strategy
	strategy := &KgTokenJwtStrategy{
		secretKey: "test-secret-key",
	}

	t.Run("with string input (backward compatibility)", func(t *testing.T) {
		// Create a token with just userID (string)
		userID := "test-user-id"
		token, _, err := strategy.CreateToken(userID)

		// Assert
		assert.NoError(t, err)
		assert.NotEmpty(t, token)

		// Parse the token to verify claims
		claims := &KGTokenClaims{}
		parsedToken, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
			return []byte("test-secret-key"), nil
		})

		assert.NoError(t, err)
		assert.True(t, parsedToken.Valid)
		assert.Equal(t, userID, claims.Subject)
		assert.Empty(t, claims.Email, "Email should be empty when using string input")
	})

	t.Run("with TokenData input", func(t *testing.T) {
		// Create a token with TokenData including email
		data := TokenData{
			UserID: "test-user-id",
			Email:  "<EMAIL>",
		}
		token, _, err := strategy.CreateToken(data)

		// Assert
		assert.NoError(t, err)
		assert.NotEmpty(t, token)

		// Parse the token to verify claims
		claims := &KGTokenClaims{}
		parsedToken, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
			return []byte("test-secret-key"), nil
		})

		assert.NoError(t, err)
		assert.True(t, parsedToken.Valid)
		assert.Equal(t, data.UserID, claims.Subject)
		assert.Equal(t, data.Email, claims.Email, "Email should match the input")
	})

	t.Run("with invalid data type", func(t *testing.T) {
		// Try to create a token with invalid data type
		token, _, err := strategy.CreateToken(123) // Invalid type

		// Assert
		assert.Error(t, err)
		assert.Empty(t, token)
		assert.Contains(t, err.Error(), "invalid data type")
	})
}

func TestKgTokenJwtStrategy_Parse(t *testing.T) {
	// Initialize strategy
	strategy := &KgTokenJwtStrategy{
		secretKey: "test-secret-key",
	}

	t.Run("valid token with email", func(t *testing.T) {
		// Create a valid token with email
		now := time.Now()
		expiresAt := now.Add(time.Hour)

		tokenClaims := KGTokenClaims{
			Email: "<EMAIL>",
			StandardClaims: jwt.StandardClaims{
				Subject:   "test-user-id",
				ExpiresAt: expiresAt.Unix(),
				IssuedAt:  now.Unix(),
			},
		}

		jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, tokenClaims)
		tokenString, _ := jwtToken.SignedString([]byte("test-secret-key"))

		// Parse the token
		claimsI, code, err := strategy.Parse(tokenString)

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, 0, code)

		claims, ok := claimsI.(*KGTokenClaims)
		assert.True(t, ok)
		assert.Equal(t, "<EMAIL>", claims.Email)
		assert.Equal(t, "test-user-id", claims.Subject)
	})

	t.Run("expired token", func(t *testing.T) {
		// Create an expired token
		now := time.Now()
		expiresAt := now.Add(-time.Hour) // Expired 1 hour ago

		tokenClaims := KGTokenClaims{
			Email: "<EMAIL>",
			StandardClaims: jwt.StandardClaims{
				Subject:   "test-user-id",
				ExpiresAt: expiresAt.Unix(),
				IssuedAt:  now.Unix(),
			},
		}

		jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, tokenClaims)
		tokenString, _ := jwtToken.SignedString([]byte("test-secret-key"))

		// Parse the token
		_, errCode, err := strategy.Parse(tokenString)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, code.OAuthTokenInvalid, errCode)
		assert.Equal(t, code.ErrJwtExpired, err)
	})

	t.Run("invalid signature", func(t *testing.T) {
		// Create a token signed with a different key
		now := time.Now()
		expiresAt := now.Add(time.Hour)

		tokenClaims := KGTokenClaims{
			Email: "<EMAIL>",
			StandardClaims: jwt.StandardClaims{
				Subject:   "test-user-id",
				ExpiresAt: expiresAt.Unix(),
				IssuedAt:  now.Unix(),
			},
		}

		jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, tokenClaims)
		tokenString, _ := jwtToken.SignedString([]byte("wrong-secret-key"))

		// Parse the token
		_, errCode, err := strategy.Parse(tokenString)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, code.OAuthTokenInvalid, errCode)
	})
}
