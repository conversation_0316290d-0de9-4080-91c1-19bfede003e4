package firebase

import (
	"context"
	"testing"

	"firebase.google.com/go/v4/auth"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/suite"
)

type AuthTestSuite struct {
	suite.Suite
	Phone  string
	Email  string
	UID    string
	Handle string
}

func (suite *AuthTestSuite) SetupTest() {
	suite.Phone = util.RandPhone()
	suite.Email = util.RandEmail()
	suite.UID = util.RandString(20)
	suite.Handle = util.RandString(15)
}

func (suite *AuthTestSuite) TestCreateAndDeleteUser() {
	user, err := CreateUserByPhoneNumber(context.Background(), suite.Phone, "")
	if err != nil {
		suite.FailNow(err.Error())
	}
	suite.Equal(suite.Phone, user.PhoneNumber)
	uid := user.UID
	suite.NotEmpty(uid)

	users := Users()
	suite.Greater(len(users), 0)

	err = DeleteUser(uid)
	if err != nil {
		suite.FailNow(err.Error())
	}
	_, err = GetUserByPhoneNumber(context.Background(), suite.Phone)
	if err == nil {
		suite.FailNow("User record is not deleted")
	}
}

func (suite *AuthTestSuite) TestCreateAndGetUserByEmail() {
	user, err := CreateUserByEmail(context.Background(), suite.Email, suite.UID)
	if err != nil {
		suite.FailNow(err.Error())
	}
	suite.Equal(suite.Email, user.Email)
	uid := user.UID
	suite.NotEmpty(uid)

	users := Users()
	suite.Greater(len(users), 0)

	u, err := GetUser(context.Background(), &UserParams{
		Email: suite.Email,
	})
	if err != nil {
		suite.FailNow(err.Error())
	}
	suite.Equal(suite.Email, u.Email)

	err = DeleteUser(uid)
	if err != nil {
		suite.FailNow(err.Error())
	}
	_, err = GetUserByPhoneNumber(context.Background(), suite.Phone)
	if err == nil {
		suite.FailNow("User record is not deleted")
	}
}

func (suite *AuthTestSuite) TestCreateUserAndCheckHasLinkedGoogle() {
	user, err := CreateUserByEmail(context.Background(), suite.Email, suite.UID)
	if err != nil {
		suite.FailNow(err.Error())
	}
	suite.Equal(suite.Email, user.Email)
	uid := user.UID
	suite.NotEmpty(uid)

	users := Users()
	suite.Greater(len(users), 0)

	googleEmail, hasLinkedGoogle := HasLinkedGoogle(user)
	suite.False(hasLinkedGoogle)
	suite.Empty(googleEmail)

	// firebase emulator does not support link google account
	// so we have to mock it
	user.ProviderUserInfo = []*auth.UserInfo{
		{
			ProviderID: "google.com",
			Email:      user.Email,
		},
	}

	googleEmail, hasLinkedGoogle = HasLinkedGoogle(user)
	suite.True(hasLinkedGoogle)
	suite.Equal(user.Email, googleEmail)
}

func TestFirebaseAuthSuite(t *testing.T) {
	suite.Run(t, new(AuthTestSuite))
}
