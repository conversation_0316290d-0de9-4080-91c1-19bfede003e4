package cron

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestUpdateUserBalance(t *testing.T) {
	ctx := context.Background()
	rdb.Reset()
	users, _ := dbtest.Users()

	err := rdb.GormRepo().BatchSetUsers(ctx, users)
	if err != nil {
		t.Fatalf("failed to set users: %v", err)
	}
	assert.Nil(t, rdbtest.CreateAssets(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAssetPrices(rdb.Get()))

	assets := []*model.Asset{
		{
			ChainID:       "arb",
			AssetGroup:    "******************************************",
			Amount:        util.Ptr("123"),
			WalletAddress: "******************************************",
			AssetType:     "token",
			CreatedAt:     time.Now(),
			Name:          "Tether USD",
			LogoUrls:      "[]",
		},
		{
			ChainID:       "arb",
			AssetGroup:    "Aave V3 Lending",
			Amount:        util.Ptr("35366.389148"),
			WalletAddress: "******************************************",
			AssetType:     "defi",
			Metadata:      util.Ptr(`{"name": "deposit", "pool": {"id": "", "chain": "", "index": "", "time_at": 0, "adapter_id": "", "controller": "", "project_id": ""}, "stats": {"net_usd_value": 35378.80806851162, "debt_usd_value": 0, "asset_usd_value": 35378.80806851162}, "detail": {"borrow_token_list": null, "reward_token_list": null, "supply_token_list": [{"id": "******************************************", "name": "Tether USD", "chain": "arb", "price": 1.0004158992, "amount": 29763.020686, "symbol": "USDT", "is_core": false, "time_at": 0, "decimals": 6, "logo_url": "https://cdn.zerion.io/******************************************.png", "is_wallet": false, "is_verified": false, "protocol_id": "", "is_collateral": false, "display_symbol": "", "optimized_symbol": ""}, {"id": "******************************************", "name": "USD Coin", "chain": "arb", "price": 1.000608621, "amount": 5600.000688, "symbol": "USDC", "is_core": false, "time_at": 0, "decimals": 6, "logo_url": "https://cdn.zerion.io/******************************************.png", "is_wallet": false, "is_verified": false, "protocol_id": "", "is_collateral": false, "display_symbol": "", "optimized_symbol": ""}]}, "site_url": "", "asset_dict": null, "updated_at": 0, "detail_types": null, "proxy_detail": {"project": {"id": "", "name": "", "logo_url": "", "site_url": ""}, "proxy_contract_id": ""}}`),
			CreatedAt:     time.Now(),
			Name:          "Aave V3 Lending Tether USD/USD Coin",
			LogoUrls:      "[]",
		},
	}
	prices := []model.AssetPrice{
		{
			ChainID:    "arb",
			AssetGroup: "******************************************",
			Price:      "1",
		},
	}
	assert.Nil(t, rdb.Get().Create(&assets).Error)
	assert.Nil(t, rdb.Get().Create(&prices).Error)

	UpdateUserBalance(ctx)

	// get results and assert
	balances := []*model.HistoricalUserBalance{}
	assert.Nil(t, rdb.Get().Find(&balances).Error)
	assert.Len(t, balances, 18)
	validateUserBalance(t, balances, "arb", "******************************************", "normal", decimal.NewFromFloat(35366.389148000), decimal.NewFromFloat(35363.021374))
	validateUserBalance(t, balances, "arb", "******************************************", "observer", decimal.NewFromFloat(123.000000000), decimal.NewFromFloat(123.000000000))
	validateUserBalance(t, balances, "bsc", "******************************************", "normal", decimal.NewFromFloat(100000000.000000000), decimal.NewFromFloat(100.000000000))
	validateUserBalance(t, balances, "tron", "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", "normal", decimal.NewFromFloat(500000000.000000000), decimal.NewFromFloat(500.000000000))
	validateUserBalance(t, balances, "arb", "******************************************", "normal", decimal.NewFromFloat(123.000000000), decimal.NewFromFloat(123.000000000))
	validateUserBalance(t, balances, "eth", "******************************************", "normal", decimal.NewFromFloat(200002426.940000), decimal.NewFromFloat(0.000000000))
}

func validateUserBalance(t *testing.T, userBalances []*model.HistoricalUserBalance, chainID string, walletAddress string, walletType string, totalBalance, stableCoinBalance decimal.Decimal) {
	for _, userBalance := range userBalances {
		if userBalance.ChainID == chainID && userBalance.WalletAddress == walletAddress && userBalance.WalletType == walletType {
			assert.InDeltaf(t, totalBalance.InexactFloat64(), userBalance.Balance.InexactFloat64(), 0.001, "expected %f, actual %f", totalBalance.InexactFloat64(), userBalance.Balance.InexactFloat64())
			assert.InDeltaf(t, stableCoinBalance.InexactFloat64(), userBalance.StableCoinBalance.InexactFloat64(), 0.001, "expected %f, actual %f", stableCoinBalance.InexactFloat64(), userBalance.StableCoinBalance.InexactFloat64())
			return
		}
	}
	t.Fatalf("balance not found for chainID=%s walletAddress=%s walletType=%s", chainID, walletAddress, walletType)
}
