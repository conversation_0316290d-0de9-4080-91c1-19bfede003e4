package cron

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/alert"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

// usage: turn gotest timeout to 30 minutes, and setup firebase in local docker. set ETHERSCAN_API_KEY
// TestUpdateStudioNftProjectsErc721
func TestUpdateStudioNftProjectsErc721(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	srv := signing.SetupDeploySmartContract(t)
	defer func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}()

	now := time.Now()
	halfAnHourAgo := now.Add(-30 * time.Minute)

	ctx := context.Background()
	rdb.Reset()
	err := dbtest.CreateNftProjectsToDeploy(rdb.Get())
	assert.Nil(t, err)
	err = dbtest.CreateStudioDefault(rdb.Get())
	assert.Nil(t, err)
	server.Init(rdb.GormRepo(), &alert.SlackAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	initProjects(ctx, halfAnHourAgo)
	projects, err := rdb.GetStudioDeployedNftProjects(ctx)
	assert.NoError(t, err)
	assert.Len(t, projects, 1)

	// skip gcs load, because googleapi is not implemented by mock firebase
	uploadNftItems(ctx, halfAnHourAgo)
	uploadNftCollection(ctx, halfAnHourAgo)

	_, err = rdb.UpsertStudioNftProject(ctx, &model.StudioNftProject{
		ProjectID:     721,
		PublishStatus: util.Ptr(model.PublishStatusUploadedNftCollection),
	})
	assert.Nil(t, err)

	uploadNftData(ctx, halfAnHourAgo)
	time.Sleep(1 * time.Minute) // wait for release of redis lock
	projects, err = rdb.GetStudioNftProjectsByPublishStatus(ctx, []model.PublishStatus{model.PublishStatusUploadedNftData}, nil)
	assert.NoError(t, err)
	assert.Len(t, projects, 1)

	timeout := time.After(5 * time.Minute)
loop:
	for {
		select {
		case <-timeout:
			t.Error("verifyContract timeout")
			break loop
		default:
			verifyContract(ctx, halfAnHourAgo)
			projects, err = rdb.GetStudioNftProjectsByPublishStatus(ctx, []model.PublishStatus{model.PublishStatusSuccess}, nil)
			assert.NoError(t, err)
			assert.Len(t, projects, 1)
			if projects[0].Verified {
				break loop
			}
			time.Sleep(1 * time.Minute)
		}
	}

}

// usage: turn gotest timeout to 30 minutes, and setup firebase in local docker. set ETHERSCAN_API_KEY
// TestUpdateStudioNftProjectsErc1155
func TestUpdateStudioNftProjectsErc1155(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	srv := signing.SetupDeploySmartContract(t)
	defer func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}()

	now := time.Now()
	halfAnHourAgo := now.Add(-30 * time.Minute)

	ctx := context.Background()
	rdb.Reset()
	err := dbtest.CreateNftProjectsToDeploy1155(rdb.Get())
	assert.Nil(t, err)
	err = dbtest.CreateStudioDefault(rdb.Get())
	assert.Nil(t, err)
	server.Init(rdb.GormRepo(), &alert.SlackAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	initProjects(ctx, halfAnHourAgo)
	projects, err := rdb.GetStudioDeployedNftProjects(ctx)
	assert.NoError(t, err)
	assert.Len(t, projects, 1)

	// skip gcs load, because googleapi is not implemented by mock firebase
	uploadNftItems(ctx, halfAnHourAgo)
	uploadNftCollection(ctx, halfAnHourAgo)

	_, err = rdb.UpsertStudioNftProject(ctx, &model.StudioNftProject{
		ProjectID:     1155,
		PublishStatus: util.Ptr(model.PublishStatusUploadedNftCollection),
	})
	assert.Nil(t, err)

	uploadNftData(ctx, halfAnHourAgo)
	time.Sleep(1 * time.Minute) // wait for release of redis lock
	projects, err = rdb.GetStudioNftProjectsByPublishStatus(ctx, []model.PublishStatus{model.PublishStatusSuccess}, nil)
	assert.NoError(t, err)
	assert.Len(t, projects, 1)

	timeout := time.After(15 * time.Minute)
loop:
	for {
		select {
		case <-timeout:
			t.Error("verifyContract timeout")
			break loop
		default:
			verifyContract(ctx, halfAnHourAgo)
			projects, err = rdb.GetStudioNftProjectsByPublishStatus(ctx, []model.PublishStatus{model.PublishStatusSuccess}, nil)
			assert.NoError(t, err)
			assert.Len(t, projects, 1)
			if projects[0].Verified {
				break loop
			}
			time.Sleep(1 * time.Minute)
		}
	}

}

func TestUpdateStudioNftProjectsFailed(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	now := time.Now()
	halfAnHourAgo := now.Add(1 * time.Hour)

	ctx := context.Background()
	rdb.Reset()
	err := dbtest.CreateNftProjects(rdb.Get())
	assert.Nil(t, err)

	initProjects(ctx, halfAnHourAgo)
	projects, err := rdb.GetStudioDeployedNftProjects(ctx)
	assert.NoError(t, err)
	assert.Len(t, projects, 1)
	projects, err = rdb.GetStudioNftProjectsByPublishStatus(ctx, []model.PublishStatus{model.PublishStatusFailed}, nil)
	assert.NoError(t, err)
	assert.Len(t, projects, 1)

}
