package cron

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	signingclient "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"

	firestoretest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

type SheetInfo struct {
	spreadsheetID    string
	sheetName        string
	columnPhone      int
	columnHash       int
	columnDisableSMS int
}

func TestLoadingTest(t *testing.T) {
	if config.GetString("TEST_SHEET_CONTENT") != "" {
		t.Skip("skip test")
	}
	rdb.Reset()

	// test data
	sheetInfo := SheetInfo{
		spreadsheetID:    "1K_ML2ZP6OnIBgcG7BIIC0UTUTmQnYcj72SdzL27I-0A",
		sheetName:        "Sheet1",
		columnPhone:      1,
		columnHash:       2,
		columnDisableSMS: 4,
	}
	chainID := "matic"
	testingNumber := 10

	rand.New(rand.NewSource(time.Now().UnixNano()))

	// random phone number
	phoneNumbers := make([]string, testingNumber)
	for i := 0; i < testingNumber; i++ {
		phoneNumbers[i] = "886966" + fmt.Sprintf("%06d", rand.Intn(999999))
	}

	writeSheet(t, chainID, sheetInfo, phoneNumbers)
}

func TestPhoneFormat(t *testing.T) {
	if config.GetString("TEST_SHEET_CONTENT") != "" {
		t.Skip("skip test")
	}
	// test data
	sheetInfo := SheetInfo{
		spreadsheetID:    "1K_ML2ZP6OnIBgcG7BIIC0UTUTmQnYcj72SdzL27I-0A",
		sheetName:        "Sheet1",
		columnPhone:      1,
		columnHash:       2,
		columnDisableSMS: 4,
	}
	chainID := "matic"
	phoneNumbers := []string{
		"+886966",
		"886966",
		"966",
		"0966",
	}

	rand.New(rand.NewSource(time.Now().UnixNano()))

	for i := 0; i < len(phoneNumbers); i++ {
		phoneNumbers[i] = phoneNumbers[i] + fmt.Sprintf("%06d", rand.Intn(999999))
	}
	writeSheet(t, chainID, sheetInfo, phoneNumbers)
}

func writeSheet(t *testing.T, chainID string, sheetInfo SheetInfo, phoneNumbers []string) {
	ctx := context.Background()
	sheetRange := fmt.Sprintf("%s!A:Z", sheetInfo.sheetName)
	rows, err := util.SheetClientImpl.ReadSheet(sheetInfo.spreadsheetID, sheetRange)
	if err != nil {
		kglog.ErrorWithData("crawler_meet_tapiei, ReadSheet failed", err)
		return
	}
	rowStart := len(rows) + 1
	rowEnd := rowStart + len(phoneNumbers) - 1
	phoneColumn := string(rune('A' + sheetInfo.columnPhone))
	hashColumn := string(rune('A' + sheetInfo.columnHash))
	disableSMSColumn := string(rune('A' + sheetInfo.columnDisableSMS))

	// write data
	for rowID := rowStart; rowID <= rowEnd; rowID++ {
		values := [][]interface{}{{phoneNumbers[rowID-rowStart], "", "", "true"}}
		err = util.SheetClientImpl.WriteSheetRow(sheetInfo.spreadsheetID, sheetInfo.sheetName, fmt.Sprintf("%s%d:%s%d", phoneColumn, rowID, disableSMSColumn, rowID), values)
		if err != nil {
			kglog.ErrorWithData("crawler_meet_tapiei, WriteSheetRow failed", err)
			return
		}
	}
	kglog.Info("wait to run crawler")
	time.Sleep(10 * time.Second)

	// if data isn't increaing, it means the data is not written
	sheetRange = fmt.Sprintf("%s!%s%d:%s%d", sheetInfo.sheetName, hashColumn, rowStart, hashColumn, rowEnd)
	rowNumber := 0
	for {
		rows, err = util.SheetClientImpl.ReadSheet(sheetInfo.spreadsheetID, sheetRange)
		if err != nil {
			kglog.ErrorWithData("crawler_meet_tapiei, ReadSheet failed", err)
			return
		}
		kglog.DebugWithData("crawler_meet_tapiei, ReadSheet", map[string]interface{}{
			"rowNumber": rowNumber,
			"rows":      rows,
		})
		if len(rows) > rowNumber {
			rowNumber = len(rows)
		} else {
			break
		}
		time.Sleep(8 * time.Second)
	}

	// all hash values
	hashList := make([]string, 0)
	for _, row := range rows {
		txHash := row[sheetInfo.columnHash].(string)
		fmt.Println("txHash", txHash)
		_, err := hexutil.Decode(txHash)
		if err != nil {
			kglog.ErrorWithData("crawler_meet_tapiei, hexutil.Decode failed", err)
			continue
		}

		hashList = append(hashList, txHash)
	}
	// check number
	directRetryLimit := 5
	confirmed := 0
	assert.Equal(t, len(phoneNumbers), len(hashList))

	for _, txHash := range hashList {
		status := tx.Get().WaitTxConfirmed(ctx, chainID, txHash, directRetryLimit)
		if status != model.TxStatusSuccess {
			kglog.ErrorWithData("crawler_meet_tapiei, WaitTxConfirmed failed", err)
			continue
		} else {
			confirmed++
		}
	}

	assert.Equal(t, len(phoneNumbers), confirmed)
}

func TestAirdropBySheets(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY"})

	// test data
	eventID := "kryptogo-yacht-club"

	rdb.Reset()
	users, _, phone, _ := firestoretest.User()
	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)

	rdb.Init()
	setupStudio(t)
	assert.Nil(t, rdbtest.CreateAirdropEvents(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	// init google sheet client
	sheetInfos := make(map[string][]airdropEvent, 0)
	sheetInfosStr := config.GetString("AIRDROP_SHEET_INFO")
	assert.NotEmpty(t, sheetInfosStr)
	err = json.Unmarshal([]byte(sheetInfosStr), &sheetInfos)
	assert.Nil(t, err)

	// write sheet data
	timestamp := time.Now().Unix()
	values := [][]interface{}{{timestamp, phone, "true"}}
	rowIdxStr := getRowFromCache(sheetInfos[eventID][0].SpreadsheetID, sheetInfos[eventID][0].SheetName)
	rowIdx, err := strconv.Atoi(rowIdxStr)
	assert.Nil(t, err)
	fmt.Println("write row: ", rowIdx)
	sheetRange := fmt.Sprintf("%s%d:%s%d", "A", rowIdx, "C", rowIdx)
	err = util.SheetClientImpl.WriteSheetRow(sheetInfos[eventID][0].SpreadsheetID, sheetInfos[eventID][0].SheetName, sheetRange, values)
	assert.Nil(t, err)

	// setup signing server
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", signing.SignEvmTransaction)
	// create HTTP server for graceful shutdown
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	signingclient.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)

	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
	defer func() {
		err = srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}()
	time.Sleep(1 * time.Second) // wait for server to start

	// run crawler to airdrop
	CrawlerAirdropSheet(context.Background(), eventID)

	// read sheet data to check hash
	timeout := time.After(3 * time.Second) // wait for signing
loop:
	for {
		select {
		case <-timeout:
			t.Error("hash not found")
			break loop
		default:
			hashColumn := 3
			sheetRange = fmt.Sprintf("%s%d:%s%d", "A", rowIdx, "D", rowIdx)
			rows, err := util.SheetClientImpl.ReadSheet(sheetInfos[eventID][0].SpreadsheetID, sheetRange)
			assert.Nil(t, err)
			if len(rows[0]) >= hashColumn && rows[0][hashColumn] != nil && rows[0][hashColumn] != "" {
				assert.Contains(t, rows[0][hashColumn], "0x")
				fmt.Println("hash found: ", rows[0][hashColumn])
				break loop
			}
		}
	}

}
func setupStudio(t *testing.T) {
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationWallets(rdb.Get()))
	organization.Init(organization.InitParam{
		StudioOrgRepo:  rdb.GormRepo(),
		StudioRoleRepo: rdb.GormRepo(),
	})
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
}
