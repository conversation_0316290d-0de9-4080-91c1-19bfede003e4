package db

import (
	"context"
	"fmt"
	"net/url"

	"cloud.google.com/go/storage"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

const (
	gcsUploadPath = "/v1/gcs/signed_url"
	gcsGetPath    = "/v1/gcs/signed_url"
)

// MockGSC google storage
type MockGSC struct {
	client     *storage.Client
	bucket     *storage.BucketHandle
	projectID  string
	bucketName string
	Host       string
}

// StorageImageToGoogleStorage store a base64 encoded image to Google storage. Returns the path
func (gss *MockGSC) StorageImageToGoogleStorage(staticHost, folder, fileName, image, mimeType string) (string, error) {
	fileExt := "jpeg"
	if mimeType == "image/png" {
		fileExt = "png"
	}
	fullName := fmt.Sprintf("%s.%s", fileName, fileExt)
	storedFilename, err := gss.storeImage(image, folder, fullName)
	if err != nil {
		return "", err
	}
	fullURL := fmt.Sprintf("%s%s", staticHost, storedFilename)
	return fullURL, nil
}

// Store a base64 encoded image to Google storage. Returns the path
func (gss *MockGSC) storeImage(image string, folder string, fileName string) (string, error) {
	imageBytes, err := util.Base64Decode([]byte(image))
	if err != nil {
		return "", fmt.Errorf("[StoreImage] base64 decode error: %v", err)
	}

	objectPath := fmt.Sprintf("%s/%s", folder, fileName)
	obj := gss.bucket.Object(objectPath)
	writer := obj.NewWriter(context.Background())
	_, err = writer.Write(imageBytes)
	if err != nil {
		return "", fmt.Errorf("[StoreImage] writer error: %v", err)
	}
	if err = writer.Close(); err != nil {
		return "", fmt.Errorf("[StoreImage] writer close error: %v", err)
	}
	return objectPath, nil
}

// SetObjectPublic Set a object to public
func (gss *MockGSC) SetObjectPublic(objectPath string) error {
	return nil
}

// UploadObjectAsPublic Store a object to Google storage
func (gss *MockGSC) UploadObjectAsPublic(data []byte, objectPath string) error {

	// upload
	obj := gss.bucket.Object(objectPath)
	writer := obj.NewWriter(context.Background())
	_, err := writer.Write(data)
	if err != nil {
		return err
	}
	if err = writer.Close(); err != nil {
		return err
	}

	return nil
}

// GenerateV4PutObjectSignedURL generates object signed URL with PUT method.
func (gss *MockGSC) GenerateV4PutObjectSignedURL(object string) (string, error) {
	return url.JoinPath(gss.Host, gcsUploadPath, object)
}

// GenerateV4GetObjectSignedURL generates object signed URL with GET method.
func (gss *MockGSC) GenerateV4GetObjectSignedURL(object string) (string, error) {
	return url.JoinPath(gss.Host, gcsGetPath, object)

}

// GenerateV4PutObjectSignedURLasPublic generates object signed URL with PUT method. Acl is public.
func (gss *MockGSC) GenerateV4PutObjectSignedURLasPublic(object string) (string, error) {
	return url.JoinPath(gss.Host, gcsUploadPath, object)
}

// GenerateV4GetObjectSignedURLasPublic generates object signed URL with GET method. , Acl is public.
func (gss *MockGSC) GenerateV4GetObjectSignedURLasPublic(object string) (string, error) {
	return url.JoinPath(gss.Host, gcsGetPath, object)
}
