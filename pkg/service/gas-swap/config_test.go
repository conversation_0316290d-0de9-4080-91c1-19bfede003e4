package gasswap

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestEnable(t *testing.T) {
	ctx := context.Background()
	repo, _ := setup(t)
	repo.EXPECT().GetWalletsByOrganizationId(ctx, 1).AnyTimes().Return(&domain.OrganizationWallets{TronAddress: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"}, nil)

	result, kgErr := Enable(ctx, 1, "shasta")
	// gas swap already enabled
	assert.Nil(t, kgErr)
	assert.Empty(t, result)
}
