package gasswap

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	gasswaptest "github.com/kryptogo/kg-wallet-backend/pkg/service/gas-swap/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestValidateAmountSufficient(t *testing.T) {
	ctx := context.Background()

	repo, _ := setup(t)
	chainID := "shasta"
	tokenAddress := "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs" // USDT
	repo.EXPECT().GetNativeAssetPrice(gomock.Any(), chainID).Times(3).Return(0.13, nil)
	repo.EXPECT().GetAssetPrice(gomock.Any(), chainID, tokenAddress).Times(3).Return(1.0, nil)

	err := isAmountSupportedAndEnough(ctx, chainID, tokenAddress, "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7", "100")
	assert.Nil(t, err)
	err = isAmountSupportedAndEnough(ctx, chainID, tokenAddress, "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7", "10000000")
	assert.NotNil(t, err)
	assert.Equal(t, "insufficient token balance", err.String())
	err = isAmountSupportedAndEnough(ctx, chainID, tokenAddress, "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7", "1")
	assert.NotNil(t, err)
	assert.Equal(t, "amount not supported", err.String())
}

func TestValidateNativeTokenEnough(t *testing.T) {
	ctx := context.Background()
	repo, _ := setup(t)
	repo.EXPECT().PendingGasSwapCostSum(ctx, 2, "shasta").Return(10.0, nil)
	repo.EXPECT().PendingGasSwapCostSum(ctx, 2, "shasta").Return(1000000.0, nil)

	orgID := 2
	chainID := "shasta"
	wallet := "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	cost := 0.123
	err := validateNativeTokenEnough(ctx, orgID, chainID, wallet, cost)
	assert.Nil(t, err)

	cost = 1000000.0
	err = validateNativeTokenEnough(ctx, orgID, chainID, wallet, cost)
	assert.NotNil(t, err)
	assert.Equal(t, "insufficient native token", err.String())
}

func TestValidateSignedApproveTxs(t *testing.T) {
	ctx := context.Background()
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(ctx, model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(ctx, model.ChainIDShasta),
	})

	txStr := `{"visible":false,"txID":"facaa2e07c36836b8bd3f97f57994009686a3dc80ad0441be9eb5bcbb984ca25","raw_data":{"contract":[{"parameter":{"value":{"data":"095ea7b3000000000000000000000000bd8af8309031cdf04c9c8df6e2d8ecd91813a4fe0000000000000000000000000000000000000000000000000000000000000064","owner_address":"410ef10eb33885368978c1f74b96d3490e43e83363","contract_address":"4142a1e39aefa49290f2b3f9ed688d7cecf86cd6e0"},"type_url":"type.googleapis.com/protocol.TriggerSmartContract"},"type":"TriggerSmartContract"}],"ref_block_bytes":"8812","ref_block_hash":"c27130b0b02e8e3a","expiration":1713363786000,"fee_limit":100000000,"timestamp":1713363726570},"raw_data_hex":"0a0288122208c27130b0b02e8e3a4090b2c2e3ee315aae01081f12a9010a31747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e54726967676572536d617274436f6e747261637412740a15410ef10eb33885368978c1f74b96d3490e43e8336312154142a1e39aefa49290f2b3f9ed688d7cecf86cd6e02244095ea7b3000000000000000000000000bd8af8309031cdf04c9c8df6e2d8ecd91813a4fe000000000000000000000000000000000000000000000000000000000000006470eae1bee3ee31900180c2d72f","signature":["ba805626d6c650ff9c15016d42f1ad8dbb6d1ed0633a343e6d20bd61c39f5dfb01141ccee06ab2eb788560ce0e3c4936b60147b33cd29c111744a3efd282b59f1B"]}`

	token := "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs" // USDT
	wallet := "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"
	spender := "TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft"
	kgErr := validateSignedTronApproveTxs(ctx, []string{txStr}, "shasta", token, "0.0001", spender, wallet)
	assert.NotNil(t, kgErr)
	assert.Equal(t, "expiration should be at least 3 minutes later", kgErr.String())

	wallet = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	txStr = `{"visible":false,"txID":"1e77d1a938f5a0f9f54d13db473f2574ee1403ea0dda8b36ff5b21fbf72381a0","raw_data":{"contract":[{"parameter":{"value":{"data":"095ea7b30000000000000000000000004d8733bd335d86d06dd3a28aebe4f822ca7a92300000000000000000000000000000000000000000000000000000000001312d00","owner_address":"419b0e4215f6ffb52680076108a318d66c11224e2a","contract_address":"4142a1e39aefa49290f2b3f9ed688d7cecf86cd6e0"},"type_url":"type.googleapis.com/protocol.TriggerSmartContract"},"type":"TriggerSmartContract"}],"ref_block_bytes":"5e37","ref_block_hash":"592eb521ea8e18bb","expiration":2029287042304,"fee_limit":30000000,"timestamp":1713927042200},"raw_data_hex":"0a025e372208592eb521ea8e18bb4080dabbd7873b5aae01081f12a9010a31747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e54726967676572536d617274436f6e747261637412740a15419b0e4215f6ffb52680076108a318d66c11224e2a12154142a1e39aefa49290f2b3f9ed688d7cecf86cd6e02244095ea7b30000000000000000000000004d8733bd335d86d06dd3a28aebe4f822ca7a92300000000000000000000000000000000000000000000000000000000001312d007098e98cf0f03190018087a70e","signature":["e0dc2b3bff6b7d123cac15d5e4401d1d596dd354872bb3d95ddcf170277b3a1850828ce2a8fa02f10bb44f30ffaf2455f8048b0cdc7e6bde8dfb00093df364991C"]}`
	kgErr = validateSignedTronApproveTxs(ctx, []string{txStr}, "shasta", token, "20", spender, wallet)
	assert.Nil(t, kgErr)

	privateKey := "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3"
	wallet = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	tx := gasswaptest.SignApproveTx(t, privateKey, wallet, "TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft", "20.0")
	txToSend := tx.ToProto()
	tronClient, err := tron.GetClient("shasta")
	assert.Nil(t, err)
	txHash, err := tronClient.BroadcastTransaction(txToSend)
	assert.Nil(t, err)
	tron.WaitTxConfirmed(ctx, "shasta", txHash, 10)
	signedTxBytes, err := json.Marshal(tx)
	assert.NoError(t, err)
	signedTxStr := string(signedTxBytes)
	kgErr = validateSignedTronApproveTxs(ctx, []string{signedTxStr}, "shasta", token, "20", spender, wallet)
	assert.NotNil(t, kgErr)
	assert.Equal(t, "tx already used", kgErr.String())
}
