package gasswap

import (
	"context"
	"math/big"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestCalculateTxAmounts(t *testing.T) {
	tests := []struct {
		name             string
		amount           string
		nativeTokenPrice float64
		paidTokenPrice   float64
		feeRatio         float64
		wantReceive      float64
		wantReceiveUsd   float64
		wantCost         float64
		wantGasFaucetTx  float64
		wantGasSwapTx    float64
		wantGasFaucetRaw *big.Int
		wantErr          bool
	}{
		{
			name:             "normal case",
			amount:           "100",
			nativeTokenPrice: 0.15, // 0.15 USD per native token
			paidTokenPrice:   1.0,  // 1 USD per paid token
			feeRatio:         0.01, // 1% fee
			wantReceive:      636.591,
			wantReceiveUsd:   95.48871,
			wantCost:         660.236,
			wantGasFaucetTx:  30.0,
			wantGasSwapTx:    606.936,
			wantGasFaucetRaw: decimal.NewFromFloat(30).Mul(decimal.NewFromFloat(1e6)).BigInt(),
			wantErr:          false,
		},
		{
			name:             "minimum amount",
			amount:           "20",
			nativeTokenPrice: 0.15,        // 0.15 USD per native token
			paidTokenPrice:   1.0,         // 1 USD per paid token
			feeRatio:         0.01,        // 1% fee
			wantReceive:      108.59145,   // (20 * 1.0 / 0.15 - 1.9 - 0.345 - 0.5 - 20.9) * 0.99
			wantReceiveUsd:   16.28871749, // 108.59145 * 0.15
			wantCost:         132.23645,   // 30 + 1.9 + 78.93645 + 0.5 + 20.9
			wantGasFaucetTx:  30.0,
			wantGasSwapTx:    78.93645, // 108.59145 - 30 + 0.345
			wantGasFaucetRaw: decimal.NewFromFloat(30).Mul(decimal.NewFromFloat(1e6)).BigInt(),
			wantErr:          false,
		},
		{
			name:             "minimum amount with high fee ratio",
			amount:           "20",
			nativeTokenPrice: 0.15,      // 0.15 USD per native token
			paidTokenPrice:   1.0,       // 1 USD per paid token
			feeRatio:         0.2,       // 20% fee
			wantReceive:      87.75067,  // (20 * 1.0 / 0.15 - 1.9 - 0.345 - 0.5 - 20.9) * 0.8
			wantReceiveUsd:   13.1626,   // 87.75067 * 0.15
			wantCost:         111.39567, // 30 + 1.9 + 58.09567 + 0.5 + 20.9
			wantGasFaucetTx:  30.0,
			wantGasSwapTx:    58.09567, // 87.75067 - 30 + 0.345
			wantGasFaucetRaw: decimal.NewFromFloat(30).Mul(decimal.NewFromFloat(1e6)).BigInt(),
			wantErr:          false,
		},
		{
			name:             "invalid amount",
			amount:           "invalid",
			nativeTokenPrice: 0.15,
			paidTokenPrice:   1.0,
			feeRatio:         0.01,
			wantErr:          true,
		},
		{
			name:             "high fee ratio",
			amount:           "100",
			nativeTokenPrice: 0.15,
			paidTokenPrice:   1.0,
			feeRatio:         0.1,       // 10% fee
			wantReceive:      578.7195,  // (100 * 1.0 / 0.15 - 1.9 - 0.345 - 0.5 - 20.9) * 0.9
			wantReceiveUsd:   86.807925, // 578.7195 * 0.15
			wantCost:         602.3645,  // 30 + 1.9 + 549.0645 + 0.5 + 20.9
			wantGasFaucetTx:  30.0,
			wantGasSwapTx:    549.0645, // 578.7195 - 30 + 0.345
			wantGasFaucetRaw: decimal.NewFromFloat(30).Mul(decimal.NewFromFloat(1e6)).BigInt(),
			wantErr:          false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			gotReceive, gotReceiveUsd, gotCost, gotGasFaucetTx, gotGasSwapTx, gotGasFaucetRaw, gotErr := calculateTxAmounts(
				ctx,
				tt.amount,
				tt.nativeTokenPrice,
				tt.paidTokenPrice,
				tt.feeRatio,
			)

			if tt.wantErr {
				assert.NotNil(t, gotErr)
				return
			}

			assert.Nil(t, gotErr)
			assert.InDelta(t, tt.wantReceive, gotReceive, 0.001)
			assert.InDelta(t, tt.wantReceiveUsd, gotReceiveUsd, 0.001)
			assert.InDelta(t, tt.wantCost, gotCost, 0.001)
			assert.InDelta(t, tt.wantGasFaucetTx, gotGasFaucetTx, 0.001)
			assert.InDelta(t, tt.wantGasSwapTx, gotGasSwapTx, 0.001)
			assert.Equal(t, tt.wantGasFaucetRaw.String(), gotGasFaucetRaw.String())
		})
	}
}
