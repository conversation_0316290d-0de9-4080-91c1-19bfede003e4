package gasswap

import (
	"context"
	"math"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCreate(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
	const chainID = "shasta"
	const receiver = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	const uid = "abc"
	const orgWallet = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const swapContract = "TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft"
	// signed using tronbox console. private key of receiver: df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3
	// enter following command in console to generate signed tx: tronWeb.transactionBuilder.triggerSmartContract("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs", "approve(address,uint256)",{feeLimit: 30000000,callValue: 0,shouldPollResponse: false},[{ type: 'address', value: "TH38zqVhncEVo2wkrcCcsZFrjmp82f84ft" }, { type: 'uint256', value: 20000000 }], "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd").then((tx) => {tx.transaction.raw_data.expiration = Date.now()+10*365*86400*1000; tx.transaction.raw_data_hex = tronWeb.utils.transaction.txPbToRawDataHex(tronWeb.utils.transaction.txJsonToPb(tx.transaction)).toLowerCase(); tx.transaction.txID = tronWeb.utils.transaction.txPbToTxID(tronWeb.utils.transaction.txJsonToPb(tx.transaction)).replace(/^0x/, ''); return tronWeb.trx.sign(tx.transaction)}).then((signedTx)=>console.log(JSON.stringify(signedTx))).catch(console.error);
	const signedTx = `{"visible":false,"txID":"1e77d1a938f5a0f9f54d13db473f2574ee1403ea0dda8b36ff5b21fbf72381a0","raw_data":{"contract":[{"parameter":{"value":{"data":"095ea7b30000000000000000000000004d8733bd335d86d06dd3a28aebe4f822ca7a92300000000000000000000000000000000000000000000000000000000001312d00","owner_address":"419b0e4215f6ffb52680076108a318d66c11224e2a","contract_address":"4142a1e39aefa49290f2b3f9ed688d7cecf86cd6e0"},"type_url":"type.googleapis.com/protocol.TriggerSmartContract"},"type":"TriggerSmartContract"}],"ref_block_bytes":"5e37","ref_block_hash":"592eb521ea8e18bb","expiration":2029287042304,"fee_limit":30000000,"timestamp":1713927042200},"raw_data_hex":"0a025e372208592eb521ea8e18bb4080dabbd7873b5aae01081f12a9010a31747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e54726967676572536d617274436f6e747261637412740a15419b0e4215f6ffb52680076108a318d66c11224e2a12154142a1e39aefa49290f2b3f9ed688d7cecf86cd6e02244095ea7b30000000000000000000000004d8733bd335d86d06dd3a28aebe4f822ca7a92300000000000000000000000000000000000000000000000000000000001312d007098e98cf0f03190018087a70e","signature":["e0dc2b3bff6b7d123cac15d5e4401d1d596dd354872bb3d95ddcf170277b3a1850828ce2a8fa02f10bb44f30ffaf2455f8048b0cdc7e6bde8dfb00093df364991C"]}`

	ctx := context.Background()
	repo, executor := setup(t)
	repo.EXPECT().AcquireLockWithRetry(gomock.Any(), "gas-swap-2-shasta", gomock.Any(), gomock.Any()).Return(nil)
	repo.EXPECT().HasProcessingGasSwap(gomock.Any(), uid).Return(false, nil)
	repo.EXPECT().IsGasSwapTokenSupported(gomock.Any(), 2, chainID, usdt).Return(true, nil)
	repo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 2).AnyTimes().Return(&domain.OrganizationWallets{TronAddress: orgWallet}, nil)
	repo.EXPECT().GetGasSwapOptions(gomock.Any(), chainID, usdt).Return(
		[]*domain.GasSwapOption{
			{Amount: "20", ApproveSpender: swapContract},
		}, nil,
	)
	repo.EXPECT().GetNativeAssetPrice(gomock.Any(), chainID).Return(0.1, nil)
	repo.EXPECT().GetAssetPrice(gomock.Any(), chainID, usdt).Return(1.0, nil)
	repo.EXPECT().PendingGasSwapCostSum(gomock.Any(), 2, chainID).Return(10.0, nil)
	repo.EXPECT().CreateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		gasSwap, ok := v.(*domain.GasSwap)
		if !ok {
			return false
		}
		assert.Equal(t, 2, gasSwap.OrgID)
		assert.Equal(t, uid, gasSwap.UID)
		assert.Equal(t, chainID, gasSwap.ChainID)
		assert.Equal(t, orgWallet, gasSwap.From)
		assert.Equal(t, usdt, gasSwap.TokenAddress)
		assert.Equal(t, "20", gasSwap.Amount)
		assert.True(t, math.Abs(gasSwap.EstimatedReceive-143.82) < 0.1)
		assert.True(t, math.Abs(gasSwap.EstimatedReceiveUsd-14.382) < 0.1)
		assert.True(t, math.Abs(gasSwap.EstimatedCost-184.02) < 0.1)
		assert.True(t, math.Abs(gasSwap.GasFaucetTxAmount-30) < 0.1)
		assert.True(t, math.Abs(gasSwap.GasSwapTxAmount-128.82) < 0.1)
		assert.Equal(t, receiver, gasSwap.ReceiveWallet)
		assert.Equal(t, signedTx, gasSwap.SignedTxs[0])
		assert.Equal(t, domain.GasSwapStatusProcessing, gasSwap.Status)
		t.Logf("gasSwap: %v", gasSwap)
		return true
	},
	)).Return(3, nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(
		func(v any) bool {
			update, ok := v.(*domain.GasSwapUpdate)
			if !ok {
				return false
			}
			assert.Equal(t, 3, update.ID)
			assert.NotNil(t, update.GasFaucetTxHash)
			t.Logf("GasFaucetTxHash: %s\n", *update.GasFaucetTxHash)
			return true
		},
	)).Return(nil)
	executor.EXPECT().Execute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	repo.EXPECT().ReleaseLock(gomock.Any(), "gas-swap-2-shasta")

	params := &CreateParams{
		OrgID:         2,
		UID:           uid,
		ChainID:       chainID,
		TokenAddress:  usdt,
		Amount:        "20",
		ReceiveWallet: receiver,
		SignedTxs:     []string{signedTx},
	}
	gasSwapID, err := Create(ctx, params)
	assert.Nil(t, err)
	if err != nil {
		t.Logf("err: %v", err)
	}
	assert.Equal(t, 3, gasSwapID)
}

func TestHandle(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
	const chainID = "shasta"
	const orgID = 2
	const gasSwapID = 3
	const receiver = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	const uid = "abc"
	const orgWallet = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"

	ctx := context.Background()
	repo, _ := setup(t)
	repo.EXPECT().AcquireLockWithRetry(gomock.Any(), "gas-swap-handle-3", gomock.Any(), gomock.Any()).Return(nil)
	repo.EXPECT().GetGasSwapByID(gomock.Any(), gasSwapID).Return(&domain.GasSwap{
		ID:                  gasSwapID,
		OrgID:               orgID,
		UID:                 util.Ptr(uid),
		ChainID:             chainID,
		From:                orgWallet,
		TokenAddress:        usdt,
		Amount:              "20",
		EstimatedReceive:    143.82,
		EstimatedReceiveUsd: 14.382,
		EstimatedCost:       184.02,
		GasFaucetTxAmount:   30,
		GasSwapTxAmount:     128.82,
		ReceiveWallet:       receiver,
		SignedTxs:           []string{`{"visible":false,"txID":"2556bcdb6d0e560147a40378fed6d0944adba2d46c1e7bbdebef43b1b7d3358e","raw_data":{"contract":[{"parameter":{"value":{"data":"095ea7b30000000000000000000000004d8733bd335d86d06dd3a28aebe4f822ca7a92300000000000000000000000000000000000000000000000000000000001312d00","owner_address":"419b0e4215f6ffb52680076108a318d66c11224e2a","contract_address":"4142a1e39aefa49290f2b3f9ed688d7cecf86cd6e0"},"type_url":"type.googleapis.com/protocol.TriggerSmartContract"},"type":"TriggerSmartContract"}],"ref_block_bytes":"7561","ref_block_hash":"3673312c652ccb86","expiration":1713948609776,"fee_limit":30000000,"timestamp":1713945009676},"raw_data_hex":"0a02756122083673312c652ccb8640f099b1faf0315aae01081f12a9010a31747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e54726967676572536d617274436f6e747261637412740a15419b0e4215f6ffb52680076108a318d66c11224e2a12154142a1e39aefa49290f2b3f9ed688d7cecf86cd6e02244095ea7b30000000000000000000000004d8733bd335d86d06dd3a28aebe4f822ca7a92300000000000000000000000000000000000000000000000000000000001312d00708cbcd5f8f03190018087a70e","signature":["3f8754bcbfff21900053e88dfd469a948a3a97856a711368e8698519c9334150650b5dbde53138b7d8ef7f3501cda7f45dbda5b442afdaee3f7aa0cdf520bc711C"]}`},
		Status:              domain.GasSwapStatusProcessing,
		GasFaucetTxHash:     util.Ptr("00db641151cfba99946ade7adb156d21d8a443a8a6f6f4c3071aba08d4399a8c"),
		RetryCount:          0,
	}, nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, gasSwapID, update.ID)
		assert.NotNil(t, update.UserApproveTxHash)
		t.Logf("UserApproveTxHash: %s\n", *update.UserApproveTxHash)
		return true
	})).Return(nil)
	repo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 2).AnyTimes().Return(&domain.OrganizationWallets{TronAddress: orgWallet}, nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, gasSwapID, update.ID)
		assert.NotNil(t, update.GasSwapTxHash)
		t.Logf("GasSwapTxHash: %s\n", *update.GasSwapTxHash)
		return true
	})).Return(nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, gasSwapID, update.ID)
		assert.NotNil(t, update.Status)
		assert.Equal(t, domain.GasSwapStatusSuccess, *update.Status)
		return true
	})).Return(nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, gasSwapID, update.ID)
		assert.NotNil(t, update.ActualReceive)
		assert.NotNil(t, update.ActualCost)
		assert.NotNil(t, update.ProfitMargin)
		t.Logf("ActualReceive: %f, ActualCost: %f, ProfitMargin: %f\n",
			*update.ActualReceive, *update.ActualCost, update.ProfitMargin.InexactFloat64())
		return true
	})).Return(nil)
	repo.EXPECT().UpdateGasSwap(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.GasSwapUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, gasSwapID, update.ID)
		assert.Equal(t, 1, *update.RetryCount)
		return true
	})).Return(nil)
	repo.EXPECT().ReleaseLock(gomock.Any(), "gas-swap-handle-3")

	err := Handle(ctx, 3)
	assert.Nil(t, err)
	if err != nil {
		t.Logf("err: %v", err.String())
	}
}

func TestGetFeeAndSentGas(t *testing.T) {
	ctx := context.Background()
	fee, value, err := tron.GetFeeAndSentGas(ctx, "shasta", "1b0b99d3ff6502b147bca92f79706ee7eeae389e191989bfed4a97074fbe4817")
	assert.Nil(t, err)
	assert.Equal(t, 13.9715, fee)
	assert.Equal(t, 128.82, value)

	fee, value, err = tron.GetFeeAndSentGas(ctx, "shasta", "efceff7ef7c5119a857a6bda78f7682190951cc3145c6041804a6b6d49078704")
	assert.Nil(t, err)
	assert.Equal(t, 3.45888, fee)
	assert.Equal(t, 0.0, value)

	fee, value, err = tron.GetFeeAndSentGas(ctx, "shasta", "00db641151cfba99946ade7adb156d21d8a443a8a6f6f4c3071aba08d4399a8c")
	assert.Nil(t, err)
	assert.Equal(t, 4.15814, fee)
	assert.Equal(t, 30.0, value)
}
