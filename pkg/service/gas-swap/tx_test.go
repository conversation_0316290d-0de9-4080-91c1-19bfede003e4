package gasswap

import (
	"context"
	"math/big"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func setup(t *testing.T) (*domain.MockGasSwapCommonRepo, *domain.MockAsyncTaskExecutor) {
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())
	ctrl := gomock.NewController(t)
	repo := domain.NewMockGasSwapCommonRepo(ctrl)
	e := domain.NewMockAsyncTaskExecutor(ctrl)
	repo.EXPECT().GetAllGasSwapSupportedTokens(gomock.Any()).AnyTimes().Return(nil, nil)
	Init(repo, rdb.GormRepo(), e)

	// signing server
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/tron", signing.SignTronTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
	return repo, e
}

func TestSendAddOperatorTx(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	ctx := context.Background()
	repo, _ := setup(t)
	repo.EXPECT().GetWalletsByOrganizationId(ctx, 1).Return(&domain.OrganizationWallets{TronAddress: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"}, nil)
	repo.EXPECT().GetWalletsByOrganizationId(ctx, 2).Return(&domain.OrganizationWallets{TronAddress: "T9yD1CAWBKg8LizsZqZgSjMXR5XckavaEL"}, nil)

	orgID := 2
	chainID := "shasta"
	txHash, kgErr := sendAddOperatorTx(ctx, orgID, chainID)
	assert.Nil(t, kgErr)
	assert.NotEmpty(t, txHash)
	t.Logf("txHash: %s", txHash)
}

func TestSendGasFaucetTx(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	ctx := context.Background()
	repo, _ := setup(t)
	repo.EXPECT().GetWalletsByOrganizationId(ctx, 1).Return(&domain.OrganizationWallets{TronAddress: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"}, nil)

	swapID := 1
	orgID := 1
	chainID := "shasta"
	amount := big.NewInt(12_340_000) // 12.34 TRX
	txHash, kgErr := sendGasFaucetTx(ctx, swapID, orgID, chainID, "T9yD1CAWBKg8LizsZqZgSjMXR5XckavaEL", amount)
	assert.Nil(t, kgErr)
	assert.NotEmpty(t, txHash)
	t.Logf("txHash: %s", txHash)
}

func TestSendGasSwapTx(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	ctx := context.Background()
	repo, _ := setup(t)
	repo.EXPECT().GetWalletsByOrganizationId(ctx, 1).Return(&domain.OrganizationWallets{TronAddress: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"}, nil)

	swapID := 1
	orgID := 1
	chainID := "shasta"
	token := "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs" // USDT
	gasAmount := big.NewInt(11_220_000)           // 11.22 TRX
	tokenAmount := big.NewInt(100)                // 0.0001 USDT
	txHash, kgErr := sendGasSwapTx(ctx, swapID, orgID, chainID, token, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", gasAmount, tokenAmount)
	assert.Nil(t, kgErr)
	assert.NotEmpty(t, txHash)
	t.Logf("txHash: %s", txHash)
}

func TestHasOperator(t *testing.T) {
	ctx := context.Background()
	repo, _ := setup(t)
	repo.EXPECT().GetWalletsByOrganizationId(ctx, 1).Return(&domain.OrganizationWallets{TronAddress: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"}, nil)

	result, kgErr := hasOperator(ctx, 1, "shasta")
	assert.Nil(t, kgErr)
	assert.True(t, result)
}
