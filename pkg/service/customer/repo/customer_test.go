package repo

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"
	"github.com/stretchr/testify/assert"
)

func TestCreateAndGetCustomer(t *testing.T) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	user.Init(repo.Unified())

	customerRepo := NewCustomerRepo()

	kgOrgID := 1
	kgClientID := "20991a3ae83233d6de85d62906d71fd3"

	t.Run("Create customer with email", func(t *testing.T) {
		users, uid, email := dbtest.UserEmailOnly()
		kgErr := rdb.GormRepo().BatchSetUsers(context.Background(), users)
		if kgErr != nil {
			t.Fatalf("failed to set users: %v", kgErr.Error)
		}

		kgErr = customerRepo.CreateCustomer(context.Background(), uid, kgClientID)
		if kgErr != nil {
			t.Fatalf("failed to create customer: %v", kgErr.Error)
		}
		customer, kgErr := customerRepo.GetCustomer(context.Background(), "", "", email, kgOrgID)
		if kgErr != nil {
			t.Fatalf("failed to get customer: %v", kgErr.Error)
		}
		checkCustomer(t, customer)
	})

	t.Run("Create customer with phone", func(t *testing.T) {
		users, uid, phone := dbtest.UserPhoneOnly()
		kgErr := rdb.GormRepo().BatchSetUsers(context.Background(), users)
		if kgErr != nil {
			t.Fatalf("failed to set users: %v", kgErr.Error)
		}

		kgErr = customerRepo.CreateCustomer(context.Background(), uid, kgClientID)
		if kgErr != nil {
			t.Fatalf("failed to create customer: %v", kgErr.Error)
		}
		customer, kgErr := customerRepo.GetCustomer(context.Background(), "", phone, "", kgOrgID)
		if kgErr != nil {
			t.Fatalf("failed to get customer: %v", kgErr.Error)
		}
		checkCustomer(t, customer)
	})

	t.Run("Create customer with phone and email", func(t *testing.T) {
		users, uid, phone, email := dbtest.User()
		kgErr := rdb.GormRepo().BatchSetUsers(context.Background(), users)
		if kgErr != nil {
			t.Fatalf("failed to set users: %v", kgErr.Error)
		}

		kgErr = customerRepo.CreateCustomer(context.Background(), uid, kgClientID)
		if kgErr != nil {
			t.Fatalf("failed to create customer: %v", kgErr.Error)
		}
		customer, kgErr := customerRepo.GetCustomer(context.Background(), "", phone, email, kgOrgID)
		if kgErr != nil {
			t.Fatalf("failed to get customer: %v", kgErr.Error)
		}
		checkCustomer(t, customer)
	})

	t.Run("Get non-existing user", func(t *testing.T) {
		customer, kgErr := customerRepo.GetCustomer(context.Background(), "", "non-existing-phone", "", kgOrgID)
		assert.Nil(t, customer)
		assert.NotNil(t, kgErr)
		assert.Equal(t, kgErr.Code, 2100)

		customer, kgErr = customerRepo.GetCustomer(context.Background(), "", "", "non-existing-email", kgOrgID)
		assert.Nil(t, customer)
		assert.NotNil(t, kgErr)
		assert.Equal(t, kgErr.HttpStatus, 404)
		assert.Equal(t, kgErr.Code, 2100)
	})

	t.Run("Get user with non-existing customer", func(t *testing.T) {
		users, _, phone, _ := dbtest.User()
		kgErr := rdb.GormRepo().BatchSetUsers(context.Background(), users)
		if kgErr != nil {
			t.Fatalf("failed to set users: %v", kgErr.Error)
		}

		customer, kgErr := customerRepo.GetCustomer(context.Background(), "", phone, "", kgOrgID)
		assert.Nil(t, customer)
		assert.NotNil(t, kgErr)
		assert.Equal(t, kgErr.HttpStatus, 404)
		assert.Equal(t, kgErr.Code, 7016)
	})
}

func checkCustomer(t *testing.T, customer *domain.Customer) {
	assert.NotNil(t, customer)
	assert.Equal(t, customer.DisplayName, "哈里")
	// assert.Equal(t, len(customer.Wallets), 10)
	for _, wallet := range customer.DefaultReceivingWallets {
		switch wallet.ChainID {
		case "arb":
			assert.Equal(t, wallet.Address, "******************************************")
		case "bsc":
			assert.Equal(t, wallet.Address, "******************************************")
		case "btc":
			assert.Equal(t, wallet.Address, "******************************************")
		case "eth":
			assert.Equal(t, wallet.Address, "******************************************")
		case "kcc":
			assert.Equal(t, wallet.Address, "******************************************")
		case "matic":
			assert.Equal(t, wallet.Address, "******************************************")
		case "sol":
			assert.Equal(t, wallet.Address, "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7")
		case "tron":
			assert.Equal(t, wallet.Address, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
		case "ronin":
			assert.Equal(t, wallet.Address, "******************************************")
		case "oasys":
			assert.Equal(t, wallet.Address, "******************************************")
		case "shasta":
			assert.Equal(t, wallet.Address, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
		case "sepolia":
			assert.Equal(t, wallet.Address, "******************************************")
		case "holesky":
			assert.Equal(t, wallet.Address, "******************************************")
		default:
		}
	}
	assert.Equal(t, customer.AvatarURL, "https://lh3.googleusercontent.com/H4nD73fI_qNP_C4mn6d2pImSpHeQ9VRKZ5YTlpyYvYfGw00f_6NVhfvvXxLQt147_yjj-2XTODzI5B_MLFSBdGUbRX_di3Oezq59uQ")
	assert.Equal(t, string(customer.KycStatus), "unverified")
}
