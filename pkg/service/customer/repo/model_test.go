package repo

import (
	"fmt"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestGetAudienceName(t *testing.T) {
	s := assert.New(t)
	s.Nil(getAudienceName(nil, nil, nil))
	{
		legalName := util.Ptr(util.RandString(6))
		s.Equal(getAudienceName(legalName, nil, nil), legalName)
	}
	{
		legalName := util.Ptr("")
		walletDisplayName := util.Ptr("")
		userName := util.RandString(6)
		email := util.Ptr(userName)
		s.Nil(getAudienceName(legalName, walletDisplayName, email))
	}
	{
		legalName := util.Ptr(util.RandString(6))
		walletDisplayName := util.Ptr(util.RandString(7))
		s.Equal(getAudienceName(legalName, walletDisplayName, nil), legalName)
	}
	{
		legalName := util.Ptr(util.RandString(6))
		walletDisplayName := util.Ptr(util.RandString(7))
		userName := util.RandString(8)
		email := util.Ptr(fmt.Sprintf("%s@%s.%s", userName, util.RandString(6), util.RandString(3)))
		s.Equal(*getAudienceName(legalName, walletDisplayName, email), *legalName)
	}
	{
		legalName := util.Ptr(util.RandString(6))
		userName := util.RandString(7)
		email := util.Ptr(fmt.Sprintf("%s@%s.%s", userName, util.RandString(6), util.RandString(3)))
		s.Equal(*getAudienceName(legalName, nil, email), *legalName)
	}
	{
		userName := util.RandString(6)
		email := util.Ptr(fmt.Sprintf("%s@%s.%s", userName, util.RandString(6), util.RandString(3)))
		s.Equal(*getAudienceName(nil, nil, email), userName)
	}
	{
		walletDisplayName := util.Ptr(util.RandString(7))
		userName := util.RandString(8)
		email := util.Ptr(fmt.Sprintf("%s@%s.%s", userName, util.RandString(6), util.RandString(3)))
		s.Equal(*getAudienceName(nil, walletDisplayName, email), *walletDisplayName)
	}
	{
		legalName := util.Ptr("")
		walletDisplayName := util.Ptr(util.RandString(7))
		userName := util.RandString(8)
		email := util.Ptr(fmt.Sprintf("%s@%s.%s", userName, util.RandString(6), util.RandString(3)))
		s.Equal(*getAudienceName(legalName, walletDisplayName, email), *walletDisplayName)
	}
	{
		legalName := util.Ptr("")
		walletDisplayName := util.Ptr("")
		userName := util.RandString(8)
		email := util.Ptr(fmt.Sprintf("%s@%s.%s", userName, util.RandString(6), util.RandString(3)))
		s.Equal(*getAudienceName(legalName, walletDisplayName, email), userName)
	}
}
