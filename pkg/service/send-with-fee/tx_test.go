package sendwithfee

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/seed"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/stretchr/testify/assert"
)

func TestQuerySendWithFeeEvent(t *testing.T) {
	t.Skip("skip because tron rpc is very unstable now")

	rdb.Reset()
	assert.NoError(t, rdb.Get().AutoMigrate(&model.AssetPrice{}))
	assert.NoError(t, rdb.Get().Create(seed.AssetPrices()).Error)
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})
	Init(rdb.GormRepo())
	contractAddresses := config.GetStringMap("SEND_WITH_FEE_CONTRACTS")

	t.Run("QuerySendWithFeeEvent_SendNativeToken", func(t *testing.T) {
		testQuerySendWithFeeEvent_SendNativeToken(t, "shasta", contractAddresses["shasta"],
			"f1e629df450007dfa90a7c2ac9dff40de8216f89fb40cc6651edc39a1d30315e")
	})

	t.Run("QuerySendWithFeeEvent_TokenSentWithFee_NotApprovedYet", func(t *testing.T) {
		testQuerySendWithFeeEvent_TokenSentWithFee_NotApprovedYet(t, "shasta", contractAddresses["shasta"],
			"9367630f1f399295bfe9efb241cfc31d9d012cc9fed3411a7839ba2054bfee5b")
	})

	t.Run("QuerySendWithFeeEvent_TokenSentWithFee_Approved", func(t *testing.T) {
		testQuerySendWithFeeEvent_TokenSentWithFee_Approved(t, "tron", contractAddresses["tron"],
			"e3e9835363dbc0e9fabb9258bd19ac10b02515c4ba979ad8062f0efe994c2e06")
	})

}

func testQuerySendWithFeeEvent_SendNativeToken(t *testing.T, chainID, contractAddress, txHash string) {
	event, err := querySendWithFeeEvent(context.Background(), chainID, contractAddress, txHash)
	assert.Nil(t, err)
	assert.NotNil(t, event)

	assert.Equal(t, "2", event.Amount.String())
	assert.Equal(t, "TT3d4CjhtCrTuK18PQTjNfLrjM2PTcy9oA", event.Sender)
	assert.Equal(t, "TLZRN257VTa9uEU4zrNUZCEzzGigtwGE5U", event.Recipient)
	assert.Equal(t, "TULerCNSn1T4YXoYvJmGKJKgWZs5hFxve4", event.FeeAddress)
	assert.Equal(t, "", event.TokenAddress)
	assert.Equal(t, "1", event.FeeAmount.String())
	assert.Equal(t, "0.12", event.TokenPrice.String())
}

func testQuerySendWithFeeEvent_TokenSentWithFee_NotApprovedYet(t *testing.T, chainID, contractAddress, txHash string) {
	event, err := querySendWithFeeEvent(context.Background(), chainID, contractAddress, txHash)
	assert.Nil(t, err)
	assert.NotNil(t, event)

	assert.Equal(t, "1", event.Amount.String())
	assert.Equal(t, "TT3d4CjhtCrTuK18PQTjNfLrjM2PTcy9oA", event.Sender)
	assert.Equal(t, "TLZRN257VTa9uEU4zrNUZCEzzGigtwGE5U", event.Recipient)
	assert.Equal(t, "TULerCNSn1T4YXoYvJmGKJKgWZs5hFxve4", event.FeeAddress)
	assert.Equal(t, "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs", event.TokenAddress)
	assert.Equal(t, "1", event.FeeAmount.String())
	assert.Equal(t, "0.12", event.TokenPrice.String())
}

func testQuerySendWithFeeEvent_TokenSentWithFee_Approved(t *testing.T, chainID, contractAddress, txHash string) {
	event, err := querySendWithFeeEvent(context.Background(), chainID, contractAddress, txHash)
	assert.Nil(t, err)
	assert.NotNil(t, event)

	assert.Equal(t, "10000", event.Amount.String())
	assert.Equal(t, "TFScuXg8sn15Vh2wFZBBe7V23GfxaNTyKn", event.Sender)
	assert.Equal(t, "TFKKCQMsctgi9reccviEcoPCowXK1DHkLD", event.Recipient)
	assert.Equal(t, "TSSH8dtg57tjJPaXQgpncbsmWHwfAjArUR", event.FeeAddress)
	assert.Equal(t, "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", event.TokenAddress)
	assert.Equal(t, "2.836176", event.FeeAmount.String())
	assert.Equal(t, "0.12", event.TokenPrice.String())
}
