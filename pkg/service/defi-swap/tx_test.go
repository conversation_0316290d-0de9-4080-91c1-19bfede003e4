package defiswap

import (
	"context"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/coingecko"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
)

func TestGetDefiSwapTxDetail(t *testing.T) {
	// You need to set POLYGONSCAN_API_KEY
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	s := assert.New(t)
	rdb.Reset()
	s.NoError(rdb.Get().Create(&model.AssetPrice{
		ChainID:    "matic",
		AssetGroup: "matic",
		Price:      "0.4",
	}).Error)

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.Background()
	mockTokenPriceFetcher := coingecko.NewMockIService(ctrl)

	alchemyapi.InitDefault() // needs env vars both ALCHEMY_API_KEY and ALCHEMY_TOKEN
	Init(rdb.GormRepo(), mockTokenPriceFetcher)

	feeAddress := common.HexToAddress("******************************************")

	t.Run("Main token to USDT", func(t *testing.T) {
		txHash := "0x985d3c02a3eeeeda6510f2d60f2b1a33ac6655acae6733307a7fa2c954db776f"
		chainID := "matic"

		txDetail, err := getDefiSwapTxDetail(ctx, chainID, txHash, feeAddress)

		s.NoError(err)
		s.NotNil(txDetail)
		s.Equal(decimal.NewFromInt(1).String(), txDetail.amount.String())
		s.Equal(decimal.NewFromFloat(0.01).String(), txDetail.feeAmount.String())
		s.Nil(txDetail.tokenAddress)
		s.Equal(decimal.NewFromFloat(0.4).String(), txDetail.tokenPrice.String())
	})

	t.Run("USDT to USDC", func(t *testing.T) {
		txHash := "0xd20d2b2bcc44789944fa920cc97809a551631dd7a928d2976ea91133f85f03d7"
		chainID := "matic"
		expectedTokenAddress := common.HexToAddress("0xc2132D05D31c914a87C6611C10748AEb04B58e8F")

		mockTokenPriceFetcher.EXPECT().
			GetTokenPriceByAddress(gomock.Any(), domain.Polygon, expectedTokenAddress.String()).
			Return(1.0, nil)

		txDetail, err := getDefiSwapTxDetail(ctx, chainID, txHash, feeAddress)

		s.NoError(err)
		s.NotNil(txDetail)
		s.Equal(decimal.NewFromFloat(0.1).String(), txDetail.amount.String())
		s.Equal(decimal.NewFromFloat(0.001).String(), txDetail.feeAmount.String())
		s.Equal(expectedTokenAddress.String(), txDetail.tokenAddress.String())
		s.Equal(decimal.NewFromFloat(1.0).String(), txDetail.tokenPrice.String())
	})

	t.Run("USDT to main token", func(t *testing.T) {
		txHash := "0xf41e9445d7a1f4a98ffcb676d69bffa63de269bbe67f003e420363f704771345"
		chainID := "matic"
		expectedTokenAddress := common.HexToAddress("0xc2132D05D31c914a87C6611C10748AEb04B58e8F")

		mockTokenPriceFetcher.EXPECT().
			GetTokenPriceByAddress(gomock.Any(), domain.Polygon, expectedTokenAddress.String()).
			Return(1.1, nil)

		txDetail, err := getDefiSwapTxDetail(ctx, chainID, txHash, feeAddress)

		s.NoError(err)
		s.NotNil(txDetail)
		s.Equal(decimal.NewFromFloat(0.1).String(), txDetail.amount.String())
		s.Equal(decimal.NewFromFloat(0.001).String(), txDetail.feeAmount.String())
		s.Equal(expectedTokenAddress.String(), txDetail.tokenAddress.String())
		s.Equal(decimal.NewFromFloat(1.1).String(), txDetail.tokenPrice.String())
	})
}
