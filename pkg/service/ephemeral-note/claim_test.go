package ephemeralnote

import (
	"context"
	"encoding/json"
	"fmt"
	"math/big"
	"net/http"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/chain/evm"
	abibinding "github.com/kryptogo/kg-wallet-backend/chain/evm/abi-binding"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/eth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func initTokenMeta(ctrl *gomock.Controller) {
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(mockTokenMetaRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})
	tokenmeta.AddKnown(map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: domain.IDToChain("holesky"), TokenID: "******************************************"}: {
			Name:    "USD Coin",
			Symbol:  "USDC",
			LogoUrl: "https://example.com/usdc.png",
		},
		{Chain: domain.IDToChain("shasta"), TokenID: "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"}: {
			Name:    "USD Tether",
			Symbol:  "USDT",
			LogoUrl: "https://example.com/usdt.png",
		},
	})
}

func TestClaimNote(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	setupSigningServer(t)

	mockRepo := domain.NewMockEphemeralNoteRepo(ctrl)
	mockAsyncTaskExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	Init(mockRepo, mockAsyncTaskExecutor)
	alchemyapi.InitDefault()

	initTokenMeta(ctrl)

	// Hard code sender and ephemeral private keys
	senderPrivKey, _ := crypto.HexToECDSA("4f912942c735b8a7b66d0fd116aae44c909c0339a2f16713c5aa3b502ed1a26d")
	ephPrivKey, _ := crypto.GenerateKey()

	senderAddress := crypto.PubkeyToAddress(senderPrivKey.PublicKey) // ******************************************
	ephAddress := crypto.PubkeyToAddress(ephPrivKey.PublicKey)
	orgWallet := "******************************************"

	// Set up test data
	ctx := context.Background()
	chainID := "holesky"
	noteID := "test-note-id"
	usdcAddress := common.HexToAddress("******************************************")
	amount := big.NewInt(10) // 0.00001 USDC

	// Connect to Sepolia
	client, err := ethclient.Dial(eth.RpcURL(chainID))
	require.NoError(t, err)

	// Get the real contract address from config
	contractAddress := common.HexToAddress(config.GetStringMap("EPHEMERAL_NOTES_CONTRACTS")[chainID])
	contract, err := abibinding.NewEphemeralNotes(contractAddress, client)
	require.NoError(t, err)

	// Create note on the real contract
	auth, err := bind.NewKeyedTransactorWithChainID(senderPrivKey, big.NewInt(17000)) // holesky chain ID
	require.NoError(t, err)
	auth.GasLimit = uint64(500000) // Increase the gas limit

	tx, err := contract.CreateNote(auth, ephAddress, usdcAddress, amount)
	require.NoError(t, err)

	// Wait for the transaction to be mined
	receipt, err := bind.WaitMined(ctx, client, tx)
	require.NoError(t, err)
	require.Equal(t, uint64(1), receipt.Status) // Check if transaction was successful
	txHash := tx.Hash()
	t.Logf("Create Note tx hash: %s\n", txHash.Hex())
	c := evm.GetClient(domain.Holesky)
	_, err = c.WaitUntilTransactionConfirmed(ctx, txHash.Hex())
	require.NoError(t, err)

	// Set up mock expectations
	note := &domain.EphemeralNote{
		ID:             noteID,
		ChainID:        chainID,
		From:           senderAddress,
		TokenAddress:   usdcAddress,
		Amount:         amount.String(),
		DepositTxHash:  txHash,
		EphemeralOwner: ephAddress,
		Status:         domain.EphemeralNoteStatusActive,
		Symbol:         "USDC",
		TokenDecimals:  6,
		SenderUID:      "test-uid",
		CreatedAt:      time.Now(),
	}

	mockRepo.EXPECT().GetEphemeralNoteByID(gomock.Any(), noteID).Return(note, nil).Times(2)
	mockRepo.EXPECT().GetEphemeralOwner(gomock.Any(), ephAddress).Return(&domain.EphemeralOwner{
		Address:    ephAddress,
		PrivateKey: crypto.FromECDSA(ephPrivKey),
		IsUsing:    true,
		Type:       domain.EphemeralOwnerTypeEvm,
	}, nil).Times(2)
	mockRepo.EXPECT().GetAssetPrice(gomock.Any(), chainID, util.AddressToString(chainID, usdcAddress)).Return(100000.0, nil).AnyTimes()
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
		EvmAddress: orgWallet,
	}, nil)
	mockRepo.EXPECT().UpdateEphemeralNoteClaimed(gomock.Any(), noteID, gomock.Any()).Return(nil)
	mockRepo.EXPECT().SetEphemeralOwnerStatus(gomock.Any(), ephAddress, false).Return(nil)
	// Mock the getEnrichedNote function
	mockRepo.EXPECT().GetUsers(gomock.Any(), []string{"test-uid"}, "", true, &domain.UserPreloads{WithAvatar: true}).Return([]*domain.UserData{
		{
			UserInfo: domain.UserInfo{
				UID:         "test-uid",
				DisplayName: "Test User",
				Avatar:      &domain.Avatar{AvatarURL: "https://example.com/avatar.png"},
			},
		},
	}, nil)
	mockRepo.EXPECT().GetSendLinkCampaignUser(gomock.Any(), "test-uid").Return(nil, domain.ErrRecordNotFound)

	// Call the function being tested
	time.Sleep(2 * time.Second)
	claimedNote, kgErr := ClaimNote(ctx, noteID, "******************************************", "test-uid")

	// Assert results
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Fatal(kgErr.Error)
	}
	assert.NotNil(t, claimedNote)
	assert.Equal(t, domain.EphemeralNoteStatusClaimed, claimedNote.Status)
	assert.NotNil(t, claimedNote.ClaimTxHash)
	txHash = (*claimedNote.ClaimTxHash)
	t.Logf("Claim Note tx hash: %s\n", txHash.Hex())
	assert.Equal(t, "Test User", claimedNote.SenderDisplayName)
	assert.Equal(t, "https://example.com/avatar.png", claimedNote.SenderAvatar)
	assert.Equal(t, 100000.0, claimedNote.TokenPrice)
	assert.Equal(t, "https://example.com/usdc.png", claimedNote.TokenImage)
	_, err = c.WaitUntilTransactionConfirmed(ctx, txHash.Hex())
	require.NoError(t, err)
	time.Sleep(1 * time.Second) // avoid flaky

	// Verify the note was actually claimed on the contract
	contractNote, err := contract.Notes(&bind.CallOpts{}, ephAddress)
	require.NoError(t, err)
	assert.Equal(t, common.Address{}, contractNote.EphemeralOwner) // Should be zero address after claiming
}

func TestClaimNoteTron(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	setupSigningServer(t)
	tron.Init(tron.InitParams{
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})

	mockRepo := domain.NewMockEphemeralNoteRepo(ctrl)
	mockAsyncTaskExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	Init(mockRepo, mockAsyncTaskExecutor)
	initTokenMeta(ctrl)

	// Hard code sender and ephemeral private keys
	senderPrivKey, _ := crypto.HexToECDSA("df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3")
	ephPrivKey, _ := crypto.GenerateKey()

	senderAddress := crypto.PubkeyToAddress(senderPrivKey.PublicKey)
	ephAddress := crypto.PubkeyToAddress(ephPrivKey.PublicKey)
	orgWallet := "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7" // Tron address for organization wallet

	// Set up test data
	ctx := context.Background()
	chainID := model.ChainIDShasta
	noteID := "test-note-id"
	usdtAddress := "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs" // USDT contract address on Shasta
	amount := big.NewInt(10)                            // 0.00001 USDT

	// Connect to Shasta
	client := tron.NewGrpcClient(ctx, chainID)
	contractAddress := config.GetStringMap("EPHEMERAL_NOTES_CONTRACTS")[chainID]

	// Create note on the real contract
	createNoteParams := []map[string]interface{}{
		{"address": util.EthAddressToTronAddress(ephAddress)},
		{"address": usdtAddress},
		{"uint256": amount.String()},
	}
	tx, err := client.TriggerContract(ctx, util.EthAddressToTronAddress(senderAddress), contractAddress, "createNote(address,address,uint256)", createNoteParams, 60_000_000, 0, "", 0)
	require.NoError(t, err)

	// Broadcast the transaction
	wallet := domain.NewTronWallet("", senderPrivKey)
	createNoteTx, err := wallet.SignTransaction(tx.Transaction)
	require.NoError(t, err)
	txHash, err := client.BroadcastTransaction(createNoteTx)
	require.NoError(t, err)
	t.Logf("Create Note tx hash: %s\n", txHash)
	_, err = tron.WaitUntilTransactionConfirmed(ctx, chainID, txHash)
	require.NoError(t, err)

	// Set up mock expectations
	note := &domain.EphemeralNote{
		ID:             noteID,
		ChainID:        chainID,
		From:           senderAddress,
		TokenAddress:   util.TronAddressToEthAddress(usdtAddress),
		Amount:         amount.String(),
		DepositTxHash:  common.HexToHash(txHash),
		EphemeralOwner: ephAddress,
		Status:         domain.EphemeralNoteStatusActive,
		Symbol:         "USDT",
		TokenDecimals:  6,
		SenderUID:      "test-uid",
		CreatedAt:      time.Now(),
	}

	mockRepo.EXPECT().GetEphemeralNoteByID(gomock.Any(), noteID).Return(note, nil).Times(2)
	mockRepo.EXPECT().GetEphemeralOwner(gomock.Any(), ephAddress).Return(&domain.EphemeralOwner{
		Address:    ephAddress,
		PrivateKey: crypto.FromECDSA(ephPrivKey),
		IsUsing:    true,
		Type:       domain.EphemeralOwnerTypeTron,
	}, nil).Times(2)
	mockRepo.EXPECT().GetAssetPrice(gomock.Any(), chainID, usdtAddress).Return(100000.0, nil).AnyTimes()
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
		TronAddress: orgWallet,
	}, nil)
	mockRepo.EXPECT().UpdateEphemeralNoteClaimed(gomock.Any(), noteID, gomock.Any()).Return(nil)
	mockRepo.EXPECT().SetEphemeralOwnerStatus(gomock.Any(), ephAddress, false).Return(nil)
	mockRepo.EXPECT().GetUsers(gomock.Any(), []string{"test-uid"}, "", true, &domain.UserPreloads{WithAvatar: true}).Return([]*domain.UserData{
		{
			UserInfo: domain.UserInfo{
				UID:         "test-uid",
				DisplayName: "Test User",
				Avatar:      &domain.Avatar{AvatarURL: "https://example.com/avatar.png"},
			},
		},
	}, nil)
	mockRepo.EXPECT().GetSendLinkCampaignUser(gomock.Any(), "test-uid").Return(nil, domain.ErrRecordNotFound)

	// Call the function being tested
	claimedNote, kgErr := ClaimNote(ctx, noteID, orgWallet, "test-uid")

	// Assert results
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Fatal(kgErr.Error)
	}
	assert.NotNil(t, claimedNote)
	assert.Equal(t, domain.EphemeralNoteStatusClaimed, claimedNote.Status)
	assert.NotNil(t, claimedNote.ClaimTxHash)
	t.Logf("Claim Note tx hash: %s\n", (*claimedNote.ClaimTxHash).Hex())
	assert.Equal(t, "Test User", claimedNote.SenderDisplayName)
	assert.Equal(t, "https://example.com/avatar.png", claimedNote.SenderAvatar)
	assert.Equal(t, 100000.0, claimedNote.TokenPrice)
	assert.Equal(t, "https://example.com/usdt.png", claimedNote.TokenImage)

	// Verify the note was actually claimed on the contract
	contractNote, err := getContractNoteTron(chainID, contractAddress, ephAddress)
	require.NoError(t, err)
	assert.Equal(t, common.Address{}, contractNote.EphemeralOwner) // Should be zero address after claiming
}

func TestClaimNoteWithCampaign(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	setupSigningServer(t)

	mockRepo := domain.NewMockEphemeralNoteRepo(ctrl)
	mockAsyncTaskExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	Init(mockRepo, mockAsyncTaskExecutor)
	initTokenMeta(ctrl)
	alchemyapi.InitDefault()

	// Hard code sender and ephemeral private keys
	senderPrivKey, _ := crypto.HexToECDSA("4f912942c735b8a7b66d0fd116aae44c909c0339a2f16713c5aa3b502ed1a26d")
	ephPrivKey, _ := crypto.GenerateKey()

	senderAddress := crypto.PubkeyToAddress(senderPrivKey.PublicKey)
	ephAddress := crypto.PubkeyToAddress(ephPrivKey.PublicKey)
	orgWallet := "******************************************"

	// Set up test data
	ctx := context.Background()
	chainID := "holesky"
	noteID := "test-note-id"
	usdcAddress := common.HexToAddress("******************************************")
	amount := big.NewInt(10) // 0.00001 USDC
	recipientUID := "recipient-uid"

	// Connect to holesky
	client, err := ethclient.Dial(eth.RpcURL(chainID))
	require.NoError(t, err)

	// Get the real contract address from config
	contractAddress := common.HexToAddress(config.GetStringMap("EPHEMERAL_NOTES_CONTRACTS")[chainID])
	contract, err := abibinding.NewEphemeralNotes(contractAddress, client)
	require.NoError(t, err)

	// Create note on the real contract
	auth, err := bind.NewKeyedTransactorWithChainID(senderPrivKey, big.NewInt(17000)) // holesky chain ID
	require.NoError(t, err)

	tx, err := contract.CreateNote(auth, ephAddress, usdcAddress, amount)
	require.NoError(t, err)

	// Wait for the transaction to be mined
	receipt, err := bind.WaitMined(ctx, client, tx)
	require.NoError(t, err)
	require.Equal(t, uint64(1), receipt.Status) // Check if transaction was successful
	txHash := tx.Hash()
	t.Logf("Create Note tx hash: %s\n", txHash.Hex())

	c := evm.GetClient(domain.Holesky)
	_, err = c.WaitUntilTransactionConfirmed(ctx, txHash.Hex())
	require.NoError(t, err)

	// Set up mock expectations
	note := &domain.EphemeralNote{
		ID:             noteID,
		ChainID:        chainID,
		From:           senderAddress,
		TokenAddress:   usdcAddress,
		Amount:         amount.String(),
		DepositTxHash:  txHash,
		EphemeralOwner: ephAddress,
		Status:         domain.EphemeralNoteStatusActive,
		Symbol:         "USDC",
		TokenDecimals:  6,
		SenderUID:      "test-uid",
		CreatedAt:      time.Now(),
	}

	mockRepo.EXPECT().GetEphemeralNoteByID(gomock.Any(), noteID).Return(note, nil).Times(2)
	mockRepo.EXPECT().GetEphemeralOwner(gomock.Any(), ephAddress).Return(&domain.EphemeralOwner{
		Address:    ephAddress,
		PrivateKey: crypto.FromECDSA(ephPrivKey),
		IsUsing:    true,
		Type:       domain.EphemeralOwnerTypeEvm,
	}, nil).Times(2)
	mockRepo.EXPECT().GetAssetPrice(gomock.Any(), chainID, util.AddressToString(chainID, usdcAddress)).Return(100000.0, nil).AnyTimes()
	mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).Return(&domain.OrganizationWallets{
		EvmAddress: orgWallet,
	}, nil)
	mockRepo.EXPECT().UpdateEphemeralNoteClaimed(gomock.Any(), noteID, gomock.Any()).Return(nil)
	mockRepo.EXPECT().SetEphemeralOwnerStatus(gomock.Any(), ephAddress, false).Return(nil)
	mockRepo.EXPECT().GetUsers(gomock.Any(), []string{"test-uid"}, "", true, &domain.UserPreloads{WithAvatar: true}).Return([]*domain.UserData{
		{
			UserInfo: domain.UserInfo{
				UID:         "test-uid",
				DisplayName: "Test User",
				Avatar:      &domain.Avatar{AvatarURL: "https://example.com/avatar.png"},
			},
		},
	}, nil)

	// Campaign-related expectations
	mockRepo.EXPECT().GetSendLinkCampaignUser(gomock.Any(), "test-uid").Return(&domain.SendLinkCampaignUser{
		UID:    "test-uid",
		Status: domain.SendLinkCampaignUserStatusLinkShared,
	}, nil)
	mockRepo.EXPECT().UpdateSendLinkCampaignUser(gomock.Any(), gomock.Any()).DoAndReturn(
		func(_ context.Context, update *domain.SendLinkCampaignUserUpdate) error {
			assert.Equal(t, "test-uid", update.UID)
			assert.NotNil(t, update.Status)
			assert.Equal(t, domain.SendLinkCampaignUserStatusLinkClaimed, *update.Status)
			return nil
		},
	).Return(nil)

	// Expect the async task executor to be called
	mockAsyncTaskExecutor.EXPECT().Execute(
		gomock.Any(),
		gomock.Eq(config.GetString("CLOUD_TASK_API_QUEUE")),
		gomock.Any(),
		gomock.Eq(fmt.Sprintf("send-link-campaign-reward-%s", "test-uid")),
	).DoAndReturn(func(ctx context.Context, queueID string, task *domain.HttpTask, taskName string) error {
		assert.Equal(t, "POST", task.Method)
		assert.Contains(t, task.URL, "/_v/send_link_campaign/send_reward")

		var body map[string]string
		err := json.Unmarshal(task.Body, &body)
		assert.NoError(t, err)
		assert.Equal(t, "test-uid", body["sender_uid"])
		assert.Equal(t, recipientUID, body["recipient_uid"])

		return nil
	})

	time.Sleep(2 * time.Second) // avoid flaky
	// Call the function being tested
	claimedNote, kgErr := ClaimNote(ctx, noteID, orgWallet, recipientUID)

	// Assert results
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Fatal(kgErr.Error)
	}
	assert.NotNil(t, claimedNote)
	assert.Equal(t, domain.EphemeralNoteStatusClaimed, claimedNote.Status)
	assert.NotNil(t, claimedNote.ClaimTxHash)
	t.Logf("Claim Note tx hash: %s\n", (*claimedNote.ClaimTxHash).Hex())
	assert.Equal(t, "Test User", claimedNote.SenderDisplayName)
	assert.Equal(t, "https://example.com/avatar.png", claimedNote.SenderAvatar)
	assert.Equal(t, 100000.0, claimedNote.TokenPrice)
	assert.Equal(t, "https://example.com/usdc.png", claimedNote.TokenImage)

	// Verify the note was actually claimed on the contract
	_, err = c.WaitUntilTransactionConfirmed(ctx, txHash.Hex())
	require.NoError(t, err)
	time.Sleep(3 * time.Second) // avoid flaky
	contractNote, err := contract.Notes(&bind.CallOpts{}, ephAddress)
	require.NoError(t, err)
	assert.Equal(t, common.Address{}, contractNote.EphemeralOwner) // Should be zero address after claiming
}

func setupSigningServer(t *testing.T) {
	// setup rdb
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())

	// init services
	application.Init(rdb.GormRepo())

	// signing server
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", signing.SignEvmTransaction)
	rSigning.POST("/v1/sign/tron", signing.SignTronTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
}
