package ephemeralnote

import (
	"context"
	"math/big"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCreateNote(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockRepo := domain.NewMockEphemeralNoteRepo(ctrl)
	mockAsyncTaskExecutor := domain.NewMockAsyncTaskExecutor(ctrl)

	// Initialize the service with mock repo
	Init(mockRepo, mockAsyncTaskExecutor)
	alchemyapi.InitDefault()

	// Set up test data
	ctx := context.Background()
	chainID := model.ChainIDSepolia
	txHash := common.HexToHash("0x0ff3d33c808f973a805f33c6ff0f1affcfd203abe8655583c0f7b2586a0b4ff5")
	ephemeralOwner := common.HexToAddress("******************************************")
	from := common.HexToAddress("******************************************")
	token := common.HexToAddress("******************************************")
	amount := big.NewInt(1000) // 0.001 USDC

	// Set up expectations
	mockRepo.EXPECT().GetEphemeralNoteByDepositTx(gomock.Any(), txHash).Return(nil, domain.ErrRecordNotFound)
	mockRepo.EXPECT().GetEphemeralOwner(gomock.Any(), ephemeralOwner).Return(&domain.EphemeralOwner{
		Address:    ephemeralOwner,
		PrivateKey: []byte("private_key"),
		IsUsing:    true,
		Type:       domain.EphemeralOwnerTypeEvm,
	}, nil)
	mockRepo.EXPECT().CreateEphemeralNote(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, note *domain.EphemeralNote) error {
		t.Logf("Create note: %+v", *note)
		assert.Equal(t, chainID, note.ChainID)
		assert.Equal(t, from, note.From)
		assert.Equal(t, token, note.TokenAddress)
		assert.Equal(t, amount.String(), note.Amount)
		assert.Equal(t, txHash, note.DepositTxHash)
		assert.Equal(t, ephemeralOwner, note.EphemeralOwner)
		assert.Equal(t, domain.EphemeralNoteStatusActive, note.Status)
		assert.Equal(t, "USDC", note.Symbol)
		assert.Equal(t, uint8(6), note.TokenDecimals)
		assert.Equal(t, "test-uid", note.SenderUID)
		assert.Len(t, note.ID, 12)
		assert.WithinDuration(t, time.Now(), note.CreatedAt, time.Second)
		return nil
	})
	mockRepo.EXPECT().GetUserWalletAddresses(gomock.Any(), "test-uid").Return([]string{util.AddressToString(chainID, from)}, nil)
	mockRepo.EXPECT().GetAssetPrice(gomock.Any(), chainID, token.Hex()).Return(1.0, nil)
	mockRepo.EXPECT().GetSendLinkCampaignUser(gomock.Any(), "test-uid").Return(nil, domain.ErrRecordNotFound)

	// Call the function being tested
	noteID, err := CreateNote(ctx, "test-uid", chainID, txHash)
	if err != nil {
		t.Errorf("CreateNote() error = %v", err.Error)
	}

	// Assert results
	assert.Nil(t, err)
	assert.Len(t, noteID, 12)
}

func TestCreateNoteTron(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockRepo := domain.NewMockEphemeralNoteRepo(ctrl)

	tron.Init(tron.InitParams{
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})
	mockAsyncTaskExecutor := domain.NewMockAsyncTaskExecutor(ctrl)
	Init(mockRepo, mockAsyncTaskExecutor)

	// Set up test data
	ctx := context.Background()
	chainID := model.ChainIDShasta
	txHash := common.HexToHash("55992e42a056e1184f65d4885bc99612b948d0387845e0cd2cde318084be7400")
	ephemeralOwner := util.TronAddressToEthAddress("T9yD14Nj9j7xAB4dbGeiX9h8upfCg3PBbY")
	from := util.TronAddressToEthAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	token := util.TronAddressToEthAddress("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs")
	amount := big.NewInt(1000) // 0.001 USDT

	// Set up expectations
	mockRepo.EXPECT().GetEphemeralNoteByDepositTx(gomock.Any(), txHash).Return(nil, domain.ErrRecordNotFound)
	mockRepo.EXPECT().GetEphemeralOwner(gomock.Any(), ephemeralOwner).Return(&domain.EphemeralOwner{
		Address:    ephemeralOwner,
		PrivateKey: []byte("private_key"),
		IsUsing:    true,
		Type:       domain.EphemeralOwnerTypeEvm,
	}, nil)
	mockRepo.EXPECT().CreateEphemeralNote(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, note *domain.EphemeralNote) error {
		t.Logf("Create note: %+v", *note)
		assert.Equal(t, chainID, note.ChainID)
		assert.Equal(t, from, note.From)
		assert.Equal(t, token, note.TokenAddress)
		assert.Equal(t, amount.String(), note.Amount)
		assert.Equal(t, txHash, note.DepositTxHash)
		assert.Equal(t, ephemeralOwner, note.EphemeralOwner)
		assert.Equal(t, domain.EphemeralNoteStatusActive, note.Status)
		assert.Equal(t, "USDT", note.Symbol)
		assert.Equal(t, uint8(6), note.TokenDecimals)
		assert.Equal(t, "test-uid", note.SenderUID)
		assert.Len(t, note.ID, 12)
		assert.WithinDuration(t, time.Now(), note.CreatedAt, time.Second)
		return nil
	})
	mockRepo.EXPECT().GetUserWalletAddresses(gomock.Any(), "test-uid").Return([]string{util.AddressToString(chainID, from)}, nil)
	mockRepo.EXPECT().GetAssetPrice(gomock.Any(), chainID, util.EthAddressToTronAddress(token)).Return(1.0, nil)
	mockRepo.EXPECT().GetSendLinkCampaignUser(gomock.Any(), "test-uid").Return(nil, domain.ErrRecordNotFound)

	// Call the function being tested
	noteID, err := CreateNote(ctx, "test-uid", chainID, txHash)
	if err != nil {
		t.Errorf("CreateNote() error = %v", err.Error)
	}

	// Assert results
	assert.Nil(t, err)
	assert.Len(t, noteID, 12)
}

func TestCreateNoteWithCampaign(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockRepo := domain.NewMockEphemeralNoteRepo(ctrl)
	mockAsyncTaskExecutor := domain.NewMockAsyncTaskExecutor(ctrl)

	// Initialize the service with mock repo
	Init(mockRepo, mockAsyncTaskExecutor)

	// Set up test data
	ctx := context.Background()
	chainID := model.ChainIDSepolia
	txHash := common.HexToHash("0x0ff3d33c808f973a805f33c6ff0f1affcfd203abe8655583c0f7b2586a0b4ff5")
	ephemeralOwner := common.HexToAddress("******************************************")
	from := common.HexToAddress("******************************************")
	token := common.HexToAddress("******************************************")
	amount := big.NewInt(1000) // 0.001 USDC (assuming 6 decimals)

	// Set up expectations
	mockRepo.EXPECT().GetEphemeralNoteByDepositTx(gomock.Any(), txHash).Return(nil, domain.ErrRecordNotFound)
	mockRepo.EXPECT().GetEphemeralOwner(gomock.Any(), ephemeralOwner).Return(&domain.EphemeralOwner{
		Address:    ephemeralOwner,
		PrivateKey: []byte("private_key"),
		IsUsing:    true,
		Type:       domain.EphemeralOwnerTypeEvm,
	}, nil)
	mockRepo.EXPECT().CreateEphemeralNote(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, note *domain.EphemeralNote) error {
		assert.Equal(t, chainID, note.ChainID)
		assert.Equal(t, from, note.From)
		assert.Equal(t, token, note.TokenAddress)
		assert.Equal(t, amount.String(), note.Amount)
		assert.Equal(t, txHash, note.DepositTxHash)
		assert.Equal(t, ephemeralOwner, note.EphemeralOwner)
		assert.Equal(t, domain.EphemeralNoteStatusActive, note.Status)
		assert.Equal(t, "USDC", note.Symbol)
		assert.Equal(t, uint8(6), note.TokenDecimals)
		assert.Equal(t, "test-uid", note.SenderUID)
		assert.Len(t, note.ID, 12)
		assert.WithinDuration(t, time.Now(), note.CreatedAt, time.Second)
		return nil
	})
	mockRepo.EXPECT().GetUserWalletAddresses(gomock.Any(), "test-uid").Return([]string{util.AddressToString(chainID, from)}, nil)
	mockRepo.EXPECT().GetAssetPrice(gomock.Any(), chainID, token.Hex()).Return(10000.0, nil)
	mockRepo.EXPECT().GetSendLinkCampaignUser(gomock.Any(), "test-uid").Return(&domain.SendLinkCampaignUser{
		UID:    "test-uid",
		Status: domain.SendLinkCampaignUserStatusNotStarted,
	}, nil)
	mockRepo.EXPECT().UpdateSendLinkCampaignUser(gomock.Any(), &domain.SendLinkCampaignUserUpdate{
		UID:    "test-uid",
		Status: util.Ptr(domain.SendLinkCampaignUserStatusLinkShared),
	}).Return(nil)

	// Call the function being tested
	noteID, err := CreateNote(ctx, "test-uid", chainID, txHash)
	if err != nil {
		t.Errorf("CreateNote() error = %v", err.Error)
	}

	// Assert results
	assert.Nil(t, err)
	assert.Len(t, noteID, 12)
}
