package ephemeralnote

import (
	"context"
	"fmt"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestRequestEphemeralOwner(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockRepo := domain.NewMockEphemeralNoteRepo(ctrl)
	mockAsyncTaskExecutor := domain.NewMockAsyncTaskExecutor(ctrl)

	// Initialize the service with mock repo
	Init(mockRepo, mockAsyncTaskExecutor)

	t.Run("With unused owner available", func(t *testing.T) {
		ctx := context.Background()
		unusedAddress := common.HexToAddress("******************************************")

		// Set up expectations
		mockRepo.EXPECT().AcquireLockWithRetry(
			gomock.Any(),
			ephemeralOwnerLockKey,
			lockDuration,
			retryInterval,
		).Return(nil)
		mockRepo.EXPECT().ReleaseLock(ctx, ephemeralOwnerLockKey)
		mockRepo.EXPECT().GetUnusedEphemeralOwner(ctx, domain.EphemeralOwnerTypeEvm).Return(unusedAddress, nil)
		mockRepo.EXPECT().SetEphemeralOwnerStatus(ctx, unusedAddress, true).Return(nil)

		// Call the function being tested
		address, err := RequestEphemeralOwner(ctx, "arb")

		// Assert results
		assert.Nil(t, err)
		assert.Equal(t, unusedAddress, address)
	})

	t.Run("Without unused owner available", func(t *testing.T) {
		ctx := context.Background()

		// Set up expectations
		mockRepo.EXPECT().AcquireLockWithRetry(
			gomock.Any(),
			ephemeralOwnerLockKey,
			lockDuration,
			retryInterval,
		).Return(nil)
		mockRepo.EXPECT().ReleaseLock(ctx, ephemeralOwnerLockKey)
		mockRepo.EXPECT().GetUnusedEphemeralOwner(ctx, domain.EphemeralOwnerTypeEvm).Return(common.Address{}, nil)
		mockRepo.EXPECT().SaveEphemeralOwner(ctx, gomock.Any()).DoAndReturn(func(_ context.Context, owner *domain.EphemeralOwner) error {
			t.Logf("Save ephemeral owner: %+v", *owner)
			assert.NotEqual(t, common.Address{}, owner.Address)
			assert.NotEmpty(t, owner.PrivateKey)
			assert.Equal(t, domain.EphemeralOwnerTypeEvm, owner.Type)
			assert.True(t, owner.IsUsing)
			return nil
		})

		// Call the function being tested
		address, err := RequestEphemeralOwner(ctx, "arb")

		// Assert results
		assert.Nil(t, err)
		assert.NotEqual(t, common.Address{}, address)
	})
}

func TestReleaseEphemeralOwner(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockRepo := domain.NewMockEphemeralNoteRepo(ctrl)
	mockAsyncTaskExecutor := domain.NewMockAsyncTaskExecutor(ctrl)

	tron.Init(tron.InitParams{
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})
	Init(mockRepo, mockAsyncTaskExecutor)

	// Test setup
	ctx := context.Background()
	chainID := "sepolia"
	ephemeralOwner := common.HexToAddress("******************************************")
	mockRepo.EXPECT().SetEphemeralOwnerStatus(ctx, ephemeralOwner, false).Return(nil)
	mockRepo.EXPECT().GetActiveNotesByEphemeralOwner(ctx, ephemeralOwner).Return([]*domain.EphemeralNote{}, nil)

	// Call the function being tested
	err := ReleaseEphemeralOwner(ctx, chainID, ephemeralOwner)
	assert.Nil(t, err)
	if err != nil {
		t.Fatal(err.Error)
	}

	chainID = "shasta"
	mockRepo.EXPECT().SetEphemeralOwnerStatus(ctx, ephemeralOwner, false).Return(nil)
	mockRepo.EXPECT().GetActiveNotesByEphemeralOwner(ctx, ephemeralOwner).Return([]*domain.EphemeralNote{}, nil)

	// Call the function being tested
	err = ReleaseEphemeralOwner(ctx, chainID, ephemeralOwner)
	assert.Nil(t, err)
}

func TestGenerateClaimSignature(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockRepo := domain.NewMockEphemeralNoteRepo(ctrl)
	mockAsyncTaskExecutor := domain.NewMockAsyncTaskExecutor(ctrl)

	// Initialize the service with mock repo
	Init(mockRepo, mockAsyncTaskExecutor)

	ctx := context.Background()
	noteID := "test-note-id"
	recipient := common.HexToAddress("******************************************")

	// Generate a test private key
	privateKey, err := crypto.GenerateKey()
	assert.NoError(t, err)
	privateKeyBytes := crypto.FromECDSA(privateKey)
	ephemeralOwner := crypto.PubkeyToAddress(privateKey.PublicKey)

	// Create a test note
	testNote := &domain.EphemeralNote{
		ID:             noteID,
		EphemeralOwner: ephemeralOwner,
		Status:         domain.EphemeralNoteStatusActive,
	}

	// Set up expectations
	mockRepo.EXPECT().GetEphemeralNoteByID(ctx, noteID).Return(testNote, nil)
	mockRepo.EXPECT().GetEphemeralOwner(ctx, ephemeralOwner).Return(&domain.EphemeralOwner{
		Address:    ephemeralOwner,
		PrivateKey: privateKeyBytes,
		IsUsing:    true,
	}, nil)

	// Call the function being tested
	signature, kgErr := GenerateClaimSignature(ctx, noteID, recipient)

	// Assert results
	assert.Nil(t, kgErr)
	assert.NotNil(t, signature)
	assert.Len(t, signature, 65) // v (1 byte) + r (32 bytes) + s (32 bytes)

	// Extract v, r, s from the signature
	r := signature[:32]
	s := signature[32:64]
	v := signature[64]

	assert.True(t, v == 27 || v == 28) // v should be either 27 or 28

	// Verify the signature
	msg := crypto.Keccak256(recipient.Bytes())
	prefixedMsg := []byte(fmt.Sprintf("\x19Ethereum Signed Message:\n%d", len(msg)))
	prefixedMsg = append(prefixedMsg, msg...)
	prefixedHash := crypto.Keccak256Hash(prefixedMsg)

	// Combine r, s, v for ecrecover (note the order change)
	rsv := append(r, s...)
	rsv = append(rsv, v-27) // ecrecover expects v to be 0 or 1

	pubKeyBytes, err := crypto.Ecrecover(prefixedHash.Bytes(), rsv)
	assert.NoError(t, err)

	// Convert pubKey to address
	pubKey, err := crypto.UnmarshalPubkey(pubKeyBytes)
	assert.NoError(t, err)
	recoveredAddress := crypto.PubkeyToAddress(*pubKey)

	assert.Equal(t, ephemeralOwner, recoveredAddress)

	// Test with non-existent note
	mockRepo.EXPECT().GetEphemeralNoteByID(ctx, "non-existent-id").Return(nil, nil)
	_, kgErr = GenerateClaimSignature(ctx, "non-existent-id", recipient)
	assert.NotNil(t, kgErr)
	assert.Contains(t, kgErr.String(), "note not found")

	// Test with inactive note
	inactiveNote := &domain.EphemeralNote{
		ID:             "inactive-note-id",
		EphemeralOwner: ephemeralOwner,
		Status:         domain.EphemeralNoteStatusClaimed,
	}
	mockRepo.EXPECT().GetEphemeralNoteByID(ctx, "inactive-note-id").Return(inactiveNote, nil)
	_, kgErr = GenerateClaimSignature(ctx, "inactive-note-id", recipient)
	assert.NotNil(t, kgErr)
	assert.Contains(t, kgErr.String(), "note is not active")
}
