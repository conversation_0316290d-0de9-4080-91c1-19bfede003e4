package wallet

import (
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/kms"
	"github.com/stretchr/testify/assert"
)

func TestGenerateAddress(t *testing.T) {
	mnemonic := "guard glare faith ethics mammal shadow equip bag beauty olive short input"
	testcases := map[string]string{
		"bitcoin":  "******************************************",
		"ethereum": "******************************************",
		"solana":   "7ohkNvsmDHyMpaf52PELxyw6VR3nj4oV9U5NC8hDu5wK",
		"tron":     "TRzzojjEHtRkozmJktRScmY9iHMkFFLpSR",
	}

	for chainName, derivationPath := range kms.WalletDerivationPath {
		address, _, err := kms.GenerateAddress(mnemonic, chainName, derivationPath)
		assert.Nil(t, err)
		expected := testcases[chainName]
		assert.Equal(t, expected, address, "The generated addresses are not equal")
	}
}
