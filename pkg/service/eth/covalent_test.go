package eth

import (
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/apis/covalenthq"
	"github.com/stretchr/testify/assert"
)

func TestComposeRoninAssets(t *testing.T) {
	tokens := []*covalenthq.TokenItem{
		{
			ContractDecimals:     18,
			ContractName:         "Wrapped Ether",
			ContractTickerSymbol: "WETH",
			ContractAddress:      "******************************************",
			Type:                 covalenthq.TokenTypeCryptocurrency,
			NativeToken:          false,
			LogoURL:              "https://assets.coingecko.com/coins/images/2518/thumb/weth.png?1628852295",
			Balance:              "1000000000000000000",
		},
		{
			ContractDecimals:     6,
			ContractName:         "Smooth Love Potion",
			ContractTickerSymbol: "SLP",
			ContractAddress:      "******************************************",
			Type:                 covalenthq.TokenTypeCryptocurrency,
			NativeToken:          false,
			LogoURL:              "https://assets.coingecko.com/coins/images/10366/thumb/SLP.png?1578640057",
			Balance:              "10000000000000000",
		},
	}
	assets := composeAssets("roninAddress", tokens, "ronin")
	assert.Equal(t, len(assets), 2)
	assert.Equal(t, assets[0].ChainID, "ronin")
	assert.Equal(t, assets[0].AssetGroup, "******************************************")
	assert.Equal(t, assets[0].WalletAddress, "roninAddress")
	assert.Equal(t, *assets[0].IsVerified, true)
	assert.Equal(t, *assets[0].Symbol, "WETH")
	assert.Equal(t, *assets[0].Amount, "1")
	assert.Equal(t, assets[0].AssetType, "token")
	assert.Equal(t, assets[0].Name, "Wrapped Ether")
	assert.Equal(t, assets[0].LogoUrls, "[\"https://assets.coingecko.com/coins/images/2518/thumb/weth.png?1628852295\"]")
	assert.Equal(t, *assets[0].Decimals, int32(18))

	assert.Equal(t, assets[1].ChainID, "ronin")
	assert.Equal(t, assets[1].AssetGroup, "******************************************")
	assert.Equal(t, assets[1].WalletAddress, "roninAddress")
	assert.Equal(t, *assets[1].IsVerified, true)
	assert.Equal(t, *assets[1].Symbol, "SLP")
	assert.Equal(t, *assets[1].Amount, "10000000000")
	assert.Equal(t, assets[1].AssetType, "token")
	assert.Equal(t, assets[1].Name, "Smooth Love Potion")
	assert.Equal(t, assets[1].LogoUrls, "[\"https://assets.coingecko.com/coins/images/10366/thumb/SLP.png?1578640057\"]")
	assert.Equal(t, *assets[1].Decimals, int32(6))
}

func TestComposeOasysAssets(t *testing.T) {
	tokens := []*covalenthq.TokenItem{
		{
			ContractDecimals:     18,
			ContractName:         "Oasys",
			ContractTickerSymbol: "OAS",
			ContractAddress:      "******************************************",
			Type:                 covalenthq.TokenTypeCryptocurrency,
			NativeToken:          true,
			LogoURL:              "https://www.datocms-assets.com/86369/1688487917-oasys-colour.png",
			Balance:              "45240021464790054876522",
		},
		{
			ContractDecimals:     18,
			ContractName:         "Tether USD",
			ContractTickerSymbol: "USDT",
			ContractAddress:      "******************************************",
			SupportsErc:          []string{"erc20"},
			Type:                 covalenthq.TokenTypeCryptocurrency,
			NativeToken:          false,
			LogoURL:              "https://logos.covalenthq.com/tokens/248/******************************************.png",
			Balance:              "142755789315822882938",
		},
	}
	assets := composeAssets("oasysAddress", tokens, "oasys")
	assert.Equal(t, len(assets), 2)
	assert.Equal(t, assets[0].ChainID, "oasys")
	assert.Equal(t, assets[0].AssetGroup, "oasys")
	assert.Equal(t, assets[0].WalletAddress, "oasysAddress")
	assert.Equal(t, *assets[0].IsVerified, true)
	assert.Equal(t, *assets[0].Symbol, "OAS")
	assert.Equal(t, *assets[0].Amount, "45240.021464790054876522")
	assert.Equal(t, assets[0].AssetType, "token")
	assert.Equal(t, assets[0].Name, "Oasys Token")
	assert.Equal(t, assets[0].LogoUrls, "[\"https://www.datocms-assets.com/86369/1688487917-oasys-colour.png\"]")
	assert.Equal(t, *assets[0].Decimals, int32(18))

	assert.Equal(t, assets[1].ChainID, "oasys")
	assert.Equal(t, assets[1].AssetGroup, "******************************************")
	assert.Equal(t, assets[1].WalletAddress, "oasysAddress")
	assert.Equal(t, *assets[1].IsVerified, true)
	assert.Equal(t, *assets[1].Symbol, "USDT")
	assert.Equal(t, *assets[1].Amount, "142.755789315822882938")
	assert.Equal(t, assets[1].AssetType, "token")
	assert.Equal(t, assets[1].Name, "Tether USD")
	assert.Equal(t, assets[1].LogoUrls, "[\"https://logos.covalenthq.com/tokens/248/******************************************.png\"]")
	assert.Equal(t, *assets[1].Decimals, int32(18))
}
