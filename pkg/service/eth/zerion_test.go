package eth

import (
	"context"
	"testing"
	"time"

	zerionapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/zerion-api"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
)

func TestTokenListByZerionRESTful(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ZERION_API_KEY_V2"})
	zerionapi.InitDefault()

	for range 20 {
		assets, _, _ := TokenListByZerionRESTful(context.Background(), []string{"******************************************"})
		for _, asset := range assets {
			if asset.ChainID == "arb" && asset.AssetGroup == "******************************************" {
				t.Logf("arb usdt asset amount: %+v\n", *asset.Amount)
			}
		}
		time.Sleep(3 * time.Second)
	}

}
