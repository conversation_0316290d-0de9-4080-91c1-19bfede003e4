package eth

import (
	"context"
	"encoding/json"
	"testing"

	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	zerionapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/zerion-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestUpdateAssets(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY", "ZERION_API_KEY_V2"})

	rdb.Reset()
	zerionapi.InitDefault()
	alchemyapi.InitDefault()

	ctx := context.Background()
	syncAssetTypes := []string{model.AssetTypeToken}
	syncChainIds := []string{model.ChainIDEthereum, model.ChainIDPolygon, model.ChainIDBNBChain, model.ChainIDArb}
	wallets := []string{"******************************************", "******************************************", "******************************************"}
	UpdateAssets(ctx, wallets, true, syncAssetTypes, syncChainIds, true)
}

func TestUpdateAssetsWithMockZerion(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rdb.Reset()
	mockZerion := zerionapi.NewMockIZerion(ctrl)
	zerionapi.SetJustToken(mockZerion)

	ctx := context.Background()
	walletAddress := "******************************************"
	syncAssetTypes := []string{model.AssetTypeToken}
	syncChainIds := []string{model.ChainIDEthereum, model.ChainIDPolygon, model.ChainIDBNBChain, model.ChainIDArb}

	// Mock the WalletPositions response
	mockResponse := &zerionapi.WalletPositionsResponse{}
	data := testutil.ReadJsonFile("pkg/apis/zerion-api/test/positions-balance-test.json")
	assert.Nil(t, json.Unmarshal(data, mockResponse))
	mockZerion.EXPECT().
		WalletPositions(gomock.Any(), walletAddress, gomock.Any()).
		Return(mockResponse, nil).
		AnyTimes()

	UpdateAssets(ctx, []string{walletAddress}, true, syncAssetTypes, syncChainIds, true)

	// Get single asset for USDC on Arbitrum
	singleAssetParams := &rdb.SingleAssetParams{
		ChainID:         "arb",
		AssetGroup:      "******************************************",
		AssetType:       model.AssetTypeToken,
		WalletAddresses: []string{walletAddress},
	}
	asset, _, _, err := rdb.SingleAsset(ctx, singleAssetParams)
	assert.Nil(t, err)
	// assert it's not using zerion's response but balance from chain
	assert.NotEqual(t, (*asset.AmountDec).InexactFloat64(), 159978.0)
}
