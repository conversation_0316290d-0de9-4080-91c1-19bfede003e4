package airdrop

import (
	"context"
	"testing"

	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestIsUserReceivedErc721(t *testing.T) {
	ctx := context.Background()

	rdb.Reset()
	assert.Nil(t, dbtest.CreateAirdropEvents(rdb.Get()))
	assert.Nil(t, dbtest.CreateAirdropLogs(rdb.Get()))
	events := dbtest.GetAirdropEvents()
	event := (*events)[0]
	// request parameter from mint page
	{
		// received again
		_, errCode, err := IsUserReceived(ctx, &event, "******************************************", "+886908590579", nil, 1)
		assert.Error(t, err)
		assert.Equal(t, 4006, errCode)
	}

}

func TestIsUserReceivedErc1155(t *testing.T) {
	ctx := context.Background()

	rdb.Reset()
	assert.Nil(t, dbtest.CreateAirdropEvents(rdb.Get()))
	assert.Nil(t, dbtest.CreateAirdropLogs(rdb.Get()))
	events := dbtest.GetAirdropEvents()
	event := (*events)[1]
	// request parameter from mint page
	{
		// received again
		_, errCode, err := IsUserReceived(ctx, &event, "******************************************", "+886908590579", util.Ptr(int32(0)), 1)
		assert.Error(t, err)
		assert.Equal(t, 4006, errCode)
	}

	{
		// received again
		_, errCode, err := IsUserReceived(ctx, &event, "******************************************", "+886908590579", util.Ptr(int32(1)), 1)
		assert.NoError(t, err)
		assert.Equal(t, 0, errCode)
	}
}
