package rdb

import (
	"context"
	"math/big"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetUserByReferralCode(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()

	t.Run("success", func(t *testing.T) {
		// Setup test data
		referralCode := "TEST123"
		user := &model.User{
			UID:            "test_user",
			TxReferralCode: &referralCode,
		}
		err := GetWith(ctx).Create(user).Error
		require.NoError(t, err)

		// Test
		uid, err := repo.GetUserByReferralCode(ctx, "TEST123")
		require.NoError(t, err)
		assert.Equal(t, "test_user", uid)
	})

	t.Run("not found", func(t *testing.T) {
		uid, err := repo.GetUserByReferralCode(ctx, "NONEXISTENT")
		assert.Equal(t, domain.ErrRecordNotFound, err)
		assert.Empty(t, uid)
	})
}

func TestCreateReferralReward(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()

	t.Run("success", func(t *testing.T) {
		// Setup test data
		amount := big.NewInt(1000000000) // 1 SOL
		reward := &domain.ReferralReward{
			ReferrerUID: "test_user",
			From:        domain.NewStrAddress("7mhctkPyPNGTKKZZmZxqM8NghapZcWVVBpn2SgyM9KBT"),
			TxHash:      "test_tx_hash",
			Amount:      amount,
		}

		// Test
		id, err := repo.CreateReferralReward(ctx, reward)
		require.NoError(t, err)
		assert.NotEmpty(t, id)

		// Verify reward record
		var rewardModel model.ReferralReward
		err = GetWith(ctx).Where("tx_hash = ?", "test_tx_hash").First(&rewardModel).Error
		require.NoError(t, err)
		assert.Equal(t, amount.String(), rewardModel.Amount)

		// Verify balance update
		var balance model.ReferralBalance
		err = GetWith(ctx).Where("user_id = ?", "test_user").First(&balance).Error
		require.NoError(t, err)
		assert.Equal(t, amount.String(), balance.TotalRewards)
		assert.Equal(t, amount.String(), balance.AvailableRewards)
		assert.Equal(t, "0", balance.WithdrawnRewards)
	})

	t.Run("duplicate tx hash", func(t *testing.T) {
		amount := big.NewInt(1000000000)
		reward := &domain.ReferralReward{
			ReferrerUID: "test_user",
			From:        domain.NewStrAddress("7mhctkPyPNGTKKZZmZxqM8NghapZcWVVBpn2SgyM9KBT"),
			TxHash:      "test_tx_hash",
			Amount:      amount,
		}

		_, err := repo.CreateReferralReward(ctx, reward)
		assert.Error(t, err) // Should fail due to unique constraint
	})
}

func TestGetReferralBalance(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()

	t.Run("existing balance", func(t *testing.T) {
		// Setup test data
		balance := &model.ReferralBalance{
			UserID:           "test_user",
			TotalRewards:     "2000000000",
			AvailableRewards: "1500000000",
			WithdrawnRewards: "500000000",
		}
		err := GetWith(ctx).Create(balance).Error
		require.NoError(t, err)

		// Test
		result, err := repo.GetReferralBalance(ctx, "test_user")
		require.NoError(t, err)
		assert.Equal(t, "test_user", result.UID)
		assert.Equal(t, "2000000000", result.TotalRewards.String())
		assert.Equal(t, "1500000000", result.AvailableRewards.String())
		assert.Equal(t, "500000000", result.WithdrawnRewards.String())
	})

	t.Run("new user", func(t *testing.T) {
		result, err := repo.GetReferralBalance(ctx, "new_user")
		require.NoError(t, err)
		assert.Equal(t, "new_user", result.UID)
		assert.Equal(t, "0", result.TotalRewards.String())
		assert.Equal(t, "0", result.AvailableRewards.String())
		assert.Equal(t, "0", result.WithdrawnRewards.String())
	})
}

func TestCreateReferralWithdrawal(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()

	t.Run("success", func(t *testing.T) {
		// Setup test data
		userID := "test_user"
		user := &model.User{
			UID: userID,
		}
		err := GetWith(ctx).Create(user).Error
		require.NoError(t, err)

		// Create initial balance
		balance := &model.ReferralBalance{
			UserID:           userID,
			TotalRewards:     "2000000000", // 2 SOL
			AvailableRewards: "1500000000", // 1.5 SOL
			WithdrawnRewards: "500000000",  // 0.5 SOL
		}
		err = GetWith(ctx).Create(balance).Error
		require.NoError(t, err)

		// Test withdrawal creation
		amount := big.NewInt(1000000000) // 1 SOL
		recipientAddr := "recipient_address"
		withdrawal := &domain.ReferralWithdrawal{
			UID:       userID,
			Amount:    amount,
			Recipient: domain.NewStrAddress(recipientAddr),
		}
		id, err := repo.CreateReferralWithdrawal(ctx, withdrawal)
		require.NoError(t, err)
		assert.NotEmpty(t, id)

		// Verify withdrawal record
		var withdrawalModel model.ReferralWithdrawal
		err = GetWith(ctx).Where("id = ?", id).First(&withdrawalModel).Error
		require.NoError(t, err)
		assert.Equal(t, userID, withdrawalModel.UserID)
		assert.Equal(t, amount.String(), withdrawalModel.Amount)
		assert.Equal(t, recipientAddr, withdrawalModel.RecipientAddr)
		assert.Equal(t, "pending", withdrawalModel.Status)

		// Verify balance was updated
		var updatedBalance model.ReferralBalance
		err = GetWith(ctx).Where("user_id = ?", userID).First(&updatedBalance).Error
		require.NoError(t, err)
		assert.Equal(t, "2000000000", updatedBalance.TotalRewards)
		assert.Equal(t, "500000000", updatedBalance.AvailableRewards)  // 1.5 - 1.0 = 0.5 SOL
		assert.Equal(t, "1500000000", updatedBalance.WithdrawnRewards) // 0.5 + 1.0 = 1.5 SOL
	})

	t.Run("insufficient balance", func(t *testing.T) {
		// Setup test data
		userID := "test_user_2"
		user := &model.User{
			UID: userID,
		}
		err := GetWith(ctx).Create(user).Error
		require.NoError(t, err)

		balance := &model.ReferralBalance{
			UserID:           userID,
			TotalRewards:     "1000000000", // 1 SOL
			AvailableRewards: "500000000",  // 0.5 SOL
			WithdrawnRewards: "500000000",  // 0.5 SOL
		}
		err = GetWith(ctx).Create(balance).Error
		require.NoError(t, err)

		// Try to withdraw more than available
		amount := big.NewInt(1000000000) // 1 SOL
		recipientAddr := "recipient_address"
		withdrawal := &domain.ReferralWithdrawal{
			UID:       userID,
			Amount:    amount,
			Recipient: domain.NewStrAddress(recipientAddr),
		}
		id, err := repo.CreateReferralWithdrawal(ctx, withdrawal)
		assert.Equal(t, domain.ErrInsufficientBalance, err)
		assert.Empty(t, id)

		// Verify balance remains unchanged
		var updatedBalance model.ReferralBalance
		err = GetWith(ctx).Where("user_id = ?", userID).First(&updatedBalance).Error
		require.NoError(t, err)
		assert.Equal(t, "1000000000", updatedBalance.TotalRewards)
		assert.Equal(t, "500000000", updatedBalance.AvailableRewards)
		assert.Equal(t, "500000000", updatedBalance.WithdrawnRewards)
	})
}

func TestUpdateReferralWithdrawalTx(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()
	withdrawal := &model.ReferralWithdrawal{
		Amount:        "1000000000",
		RecipientAddr: "7mhctkPyPNGTKKZZmZxqM8NghapZcWVVBpn2SgyM9KBT",
		Status:        "pending",
	}
	err := GetWith(ctx).Create(withdrawal).Error
	require.NoError(t, err)

	// Test
	err = repo.UpdateReferralWithdrawalTx(ctx, withdrawal.ID, "test_tx_hash")
	require.NoError(t, err)

	// Verify update
	var updated model.ReferralWithdrawal
	err = GetWith(ctx).Where("id = ?", withdrawal.ID).First(&updated).Error
	require.NoError(t, err)
	assert.Equal(t, "test_tx_hash", *updated.TxHash)
	assert.Equal(t, "success", updated.Status)
}
