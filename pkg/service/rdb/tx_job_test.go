package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/stretchr/testify/assert"
)

func TestUpdateTxLists(t *testing.T) {
	Reset()
	ctx := context.Background()
	timestamp, err := time.Parse("2006-01-02 15:04:05", "2024-04-19 08:22:25")
	assert.Nil(t, err)
	txlists := []*model.TxList{
		{
			ChainID:     "matic",
			Address:     "******************************************",
			TxHash:      "0xa97941045f895ab11bb40c1237d413df59aede9841863439f29c629e2738f076",
			TxTimestamp: timestamp,
			BlockNum:    1,
		},
	}
	assert.Nil(t, UpdateTxLists(ctx, txlists))
	actualTxList := model.TxList{}
	Get().Model(&model.TxList{}).Where("tx_hash = ?", "0xa97941045f895ab11bb40c1237d413df59aede9841863439f29c629e2738f076").First(&actualTxList)
	assert.Equal(t, uint32(1), actualTxList.BlockNum)

	// update again
	txlists[0].TxTimestamp = timestamp.Add(time.Hour)
	txlists[0].BlockNum = 2
	assert.Nil(t, UpdateTxLists(ctx, txlists))
	actualTxList = model.TxList{}
	Get().Model(&model.TxList{}).Where("tx_hash = ?", "0xa97941045f895ab11bb40c1237d413df59aede9841863439f29c629e2738f076").First(&actualTxList)
	assert.Equal(t, uint32(2), actualTxList.BlockNum)
}
