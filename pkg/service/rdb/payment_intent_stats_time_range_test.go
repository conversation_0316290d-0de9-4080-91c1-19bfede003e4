package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetPaymentIntentStatsWithTimeRange(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()
	orgID := 1
	now := time.Now().UTC()

	// Create test payment intents with different timestamps
	payments := []*model.PaymentIntent{
		// 45 days ago - should be included in 90-day range but not 30-day
		{
			ID:                     "payment-45d",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(100)),
			PayerAddress:           "0xPayer1",
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			CreatedAt:              now.Add(-45 * 24 * time.Hour),
			UpdatedAt:              now.Add(-45 * 24 * time.Hour),
			PaymentTxTimestamp:     &[]time.Time{now.Add(-45 * 24 * time.Hour)}[0],
		},
		// 15 days ago - should be included in both 30-day and 90-day ranges
		{
			ID:                     "payment-15d",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(200)),
			PayerAddress:           "0xPayer2",
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			CreatedAt:              now.Add(-15 * 24 * time.Hour),
			UpdatedAt:              now.Add(-15 * 24 * time.Hour),
			PaymentTxTimestamp:     &[]time.Time{now.Add(-15 * 24 * time.Hour)}[0],
		},
		// 5 days ago - should be included in 7-day, 30-day, and 90-day ranges
		{
			ID:                     "payment-5d",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Polygon.ID(),
			TokenAddress:           "0xToken2",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(50)),
			PayerAddress:           "0xPayer3",
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			CreatedAt:              now.Add(-5 * 24 * time.Hour),
			UpdatedAt:              now.Add(-5 * 24 * time.Hour),
			PaymentTxTimestamp:     &[]time.Time{now.Add(-5 * 24 * time.Hour)}[0],
		},
		// 1 day ago - should be included in all ranges
		{
			ID:                     "payment-1d",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(75)),
			PayerAddress:           "0xPayer1", // Same payer as first payment
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			CreatedAt:              now.Add(-1 * 24 * time.Hour),
			UpdatedAt:              now.Add(-1 * 24 * time.Hour),
			PaymentTxTimestamp:     &[]time.Time{now.Add(-1 * 24 * time.Hour)}[0],
		},
		// 100 days ago - should not be included in any range (outside 90-day limit)
		{
			ID:                     "payment-100d",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(300)),
			PayerAddress:           "0xPayer4",
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			CreatedAt:              now.Add(-100 * 24 * time.Hour),
			UpdatedAt:              now.Add(-100 * 24 * time.Hour),
			PaymentTxTimestamp:     &[]time.Time{now.Add(-100 * 24 * time.Hour)}[0],
		},
		// Different org - should not be included
		{
			ID:                     "payment-other-org",
			OrgID:                  2,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(500)),
			PayerAddress:           "0xPayer5",
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			CreatedAt:              now.Add(-5 * 24 * time.Hour),
			UpdatedAt:              now.Add(-5 * 24 * time.Hour),
			PaymentTxTimestamp:     &[]time.Time{now.Add(-5 * 24 * time.Hour)}[0],
		},
		// Pending status - should be counted for orders but not revenue
		{
			ID:              "payment-pending",
			OrgID:           orgID,
			Status:          domain.PaymentIntentStatusPending,
			PaymentChain:    domain.Ethereum.ID(),
			TokenAddress:    "0xToken1",
			PayerAddress:    "0xPayer6",
			PaymentDeadline: now.Add(time.Hour),
			OrderData:       "{}",
			CreatedAt:       now.Add(-3 * 24 * time.Hour),
			UpdatedAt:       now.Add(-3 * 24 * time.Hour),
		},
	}

	// Insert test data
	for _, p := range payments {
		err := Get().Create(p).Error
		require.NoError(t, err)
	}

	t.Run("AllTimeStats", func(t *testing.T) {
		params := domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  nil, // Should use all-time method
		}

		stats, err := repo.GetPaymentIntentStatsWithTimeRange(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, orgID, stats.OrganizationID)
		assert.NotNil(t, stats.TotalRevenue)

		// Should include all successful payments for this org (including 100-day old one)
		ethToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "0xToken1"}
		polyToken := domain.ChainToken{Chain: domain.Polygon, TokenID: "0xToken2"}

		ethAmount, exists := stats.TotalRevenue[ethToken]
		assert.True(t, exists)
		// 100 + 200 + 75 + 300 = 675 (all ETH payments including 100-day old)
		assert.Equal(t, "675", ethAmount.String())

		polyAmount, exists := stats.TotalRevenue[polyToken]
		assert.True(t, exists)
		assert.Equal(t, "50", polyAmount.String())

		// Should count 4 unique customers (0xPayer1, 0xPayer2, 0xPayer3, 0xPayer4)
		// Note: All-time method includes all successful payments regardless of age
		assert.Equal(t, 4, stats.UniqueCustomerCount)

		// Should count all non-expired orders (5 successful + 1 pending)
		assert.Equal(t, 6, stats.ValidOrderCount)
	})

	t.Run("Last7Days", func(t *testing.T) {
		days := 7
		params := domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  &days,
		}

		stats, err := repo.GetPaymentIntentStatsWithTimeRange(ctx, params)
		require.NoError(t, err)

		// Should include payments from 5d and 1d ago
		ethToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "0xToken1"}
		polyToken := domain.ChainToken{Chain: domain.Polygon, TokenID: "0xToken2"}

		ethAmount, exists := stats.TotalRevenue[ethToken]
		assert.True(t, exists)
		assert.Equal(t, "75", ethAmount.String()) // Only 1-day payment

		polyAmount, exists := stats.TotalRevenue[polyToken]
		assert.True(t, exists)
		assert.Equal(t, "50", polyAmount.String()) // 5-day payment

		// Should count 2 unique customers (0xPayer1, 0xPayer3)
		assert.Equal(t, 2, stats.UniqueCustomerCount)

		// Should count orders created in last 7 days (5d, 1d, 3d pending)
		assert.Equal(t, 3, stats.ValidOrderCount)
	})

	t.Run("Last30Days", func(t *testing.T) {
		days := 30
		params := domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  &days,
		}

		stats, err := repo.GetPaymentIntentStatsWithTimeRange(ctx, params)
		require.NoError(t, err)

		// Should include payments from 15d, 5d, and 1d ago
		ethToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "0xToken1"}
		polyToken := domain.ChainToken{Chain: domain.Polygon, TokenID: "0xToken2"}

		ethAmount, exists := stats.TotalRevenue[ethToken]
		assert.True(t, exists)
		assert.Equal(t, "275", ethAmount.String()) // 200 + 75

		polyAmount, exists := stats.TotalRevenue[polyToken]
		assert.True(t, exists)
		assert.Equal(t, "50", polyAmount.String())

		// Should count 3 unique customers (0xPayer1, 0xPayer2, 0xPayer3)
		assert.Equal(t, 3, stats.UniqueCustomerCount)

		// Should count orders created in last 30 days (15d, 5d, 1d, 3d pending)
		assert.Equal(t, 4, stats.ValidOrderCount)
	})

	t.Run("Last90Days", func(t *testing.T) {
		days := 90
		params := domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  &days,
		}

		stats, err := repo.GetPaymentIntentStatsWithTimeRange(ctx, params)
		require.NoError(t, err)

		// Should include payments from 45d, 15d, 5d, and 1d ago (but not 100d)
		ethToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "0xToken1"}
		polyToken := domain.ChainToken{Chain: domain.Polygon, TokenID: "0xToken2"}

		ethAmount, exists := stats.TotalRevenue[ethToken]
		assert.True(t, exists)
		assert.Equal(t, "375", ethAmount.String()) // 100 + 200 + 75

		polyAmount, exists := stats.TotalRevenue[polyToken]
		assert.True(t, exists)
		assert.Equal(t, "50", polyAmount.String())

		// Should count 3 unique customers (0xPayer1, 0xPayer2, 0xPayer3)
		assert.Equal(t, 3, stats.UniqueCustomerCount)

		// Should count orders created in last 90 days (45d, 15d, 5d, 1d, 3d pending)
		assert.Equal(t, 5, stats.ValidOrderCount)
	})

	t.Run("ValidationErrors", func(t *testing.T) {
		// Test invalid days parameter
		invalidDays := 0
		params := domain.GetPaymentIntentStatsParams{
			OrgID: orgID,
			Days:  &invalidDays,
		}

		_, err := repo.GetPaymentIntentStatsWithTimeRange(ctx, params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "days parameter must be between 1 and 90")

		// Test days > 90
		tooManyDays := 91
		params.Days = &tooManyDays

		_, err = repo.GetPaymentIntentStatsWithTimeRange(ctx, params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "days parameter must be between 1 and 90")

		// Test negative days
		negativeDays := -1
		params.Days = &negativeDays

		_, err = repo.GetPaymentIntentStatsWithTimeRange(ctx, params)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "days parameter must be between 1 and 90")
	})

	t.Run("EmptyResults", func(t *testing.T) {
		// Test with org that has no data
		emptyOrgID := 999
		days := 30
		params := domain.GetPaymentIntentStatsParams{
			OrgID: emptyOrgID,
			Days:  &days,
		}

		stats, err := repo.GetPaymentIntentStatsWithTimeRange(ctx, params)
		require.NoError(t, err)
		assert.Equal(t, emptyOrgID, stats.OrganizationID)
		assert.Empty(t, stats.TotalRevenue)
		assert.Equal(t, 0, stats.ValidOrderCount)
		assert.Equal(t, 0, stats.UniqueCustomerCount)
	})
}

func TestCalculateRevenueForTimeRange(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()
	orgID := 1
	now := time.Now().UTC()

	// Create test payment intents
	payments := []*model.PaymentIntent{
		{
			ID:                     "revenue-test-1",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(100)),
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			PaymentTxTimestamp:     &[]time.Time{now.Add(-5 * 24 * time.Hour)}[0],
		},
		{
			ID:                     "revenue-test-2",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusPending, // Should be excluded
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(50)),
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			PaymentTxTimestamp:     &[]time.Time{now.Add(-3 * 24 * time.Hour)}[0],
		},
		{
			ID:                     "revenue-test-3",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(25)),
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			PaymentTxTimestamp:     &[]time.Time{now.Add(-15 * 24 * time.Hour)}[0], // Outside 7-day range
		},
	}

	for _, p := range payments {
		err := Get().Create(p).Error
		require.NoError(t, err)
	}

	startTime := now.AddDate(0, 0, -7).Truncate(24 * time.Hour)
	endTime := now

	revenue, err := repo.calculateRevenueForTimeRange(ctx, orgID, startTime, endTime)
	require.NoError(t, err)

	// Should only include the first payment (successful and within time range)
	ethToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "0xToken1"}
	amount, exists := revenue[ethToken]
	assert.True(t, exists)
	assert.Equal(t, "100", amount.String())
}

func TestCalculateValidOrderCountForTimeRange(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()
	orgID := 1
	now := time.Now().UTC()

	// Create test payment intents
	payments := []*model.PaymentIntent{
		{
			ID:              "order-test-1",
			OrgID:           orgID,
			Status:          domain.PaymentIntentStatusSuccess,
			PaymentDeadline: now.Add(time.Hour),
			OrderData:       "{}",
			CreatedAt:       now.Add(-5 * 24 * time.Hour),
		},
		{
			ID:              "order-test-2",
			OrgID:           orgID,
			Status:          domain.PaymentIntentStatusPending,
			PaymentDeadline: now.Add(time.Hour),
			OrderData:       "{}",
			CreatedAt:       now.Add(-3 * 24 * time.Hour),
		},
		{
			ID:              "order-test-3",
			OrgID:           orgID,
			Status:          domain.PaymentIntentStatusExpired, // Should be excluded
			PaymentDeadline: now.Add(time.Hour),
			OrderData:       "{}",
			CreatedAt:       now.Add(-2 * 24 * time.Hour),
		},
		{
			ID:              "order-test-4",
			OrgID:           orgID,
			Status:          domain.PaymentIntentStatusSuccess,
			PaymentDeadline: now.Add(time.Hour),
			OrderData:       "{}",
			CreatedAt:       now.Add(-15 * 24 * time.Hour), // Outside 7-day range
		},
	}

	for _, p := range payments {
		err := Get().Create(p).Error
		require.NoError(t, err)
	}

	startTime := now.AddDate(0, 0, -7).Truncate(24 * time.Hour)
	endTime := now

	count, err := repo.calculateValidOrderCountForTimeRange(ctx, orgID, startTime, endTime)
	require.NoError(t, err)

	// Should count 2 orders (success and pending, but not expired)
	assert.Equal(t, 2, count)
}

func TestCalculateUniqueCustomerCountForTimeRange(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()
	orgID := 1
	now := time.Now().UTC()

	// Create test payment intents
	payments := []*model.PaymentIntent{
		{
			ID:                 "customer-test-1",
			OrgID:              orgID,
			Status:             domain.PaymentIntentStatusSuccess,
			PayerAddress:       "0xPayer1",
			PaymentDeadline:    now.Add(time.Hour),
			OrderData:          "{}",
			PaymentTxTimestamp: &[]time.Time{now.Add(-5 * 24 * time.Hour)}[0],
		},
		{
			ID:                 "customer-test-2",
			OrgID:              orgID,
			Status:             domain.PaymentIntentStatusSuccess,
			PayerAddress:       "0xPayer2",
			PaymentDeadline:    now.Add(time.Hour),
			OrderData:          "{}",
			PaymentTxTimestamp: &[]time.Time{now.Add(-3 * 24 * time.Hour)}[0],
		},
		{
			ID:                 "customer-test-3",
			OrgID:              orgID,
			Status:             domain.PaymentIntentStatusSuccess,
			PayerAddress:       "0xPayer1", // Duplicate customer
			PaymentDeadline:    now.Add(time.Hour),
			OrderData:          "{}",
			PaymentTxTimestamp: &[]time.Time{now.Add(-2 * 24 * time.Hour)}[0],
		},
		{
			ID:                 "customer-test-4",
			OrgID:              orgID,
			Status:             domain.PaymentIntentStatusPending, // Should be excluded
			PayerAddress:       "0xPayer3",
			PaymentDeadline:    now.Add(time.Hour),
			OrderData:          "{}",
			PaymentTxTimestamp: &[]time.Time{now.Add(-1 * 24 * time.Hour)}[0],
		},
		{
			ID:                 "customer-test-5",
			OrgID:              orgID,
			Status:             domain.PaymentIntentStatusSuccess,
			PayerAddress:       "0xPayer4",
			PaymentDeadline:    now.Add(time.Hour),
			OrderData:          "{}",
			PaymentTxTimestamp: &[]time.Time{now.Add(-15 * 24 * time.Hour)}[0], // Outside 7-day range
		},
	}

	for _, p := range payments {
		err := Get().Create(p).Error
		require.NoError(t, err)
	}

	startTime := now.AddDate(0, 0, -7).Truncate(24 * time.Hour)
	endTime := now

	count, err := repo.calculateUniqueCustomerCountForTimeRange(ctx, orgID, startTime, endTime)
	require.NoError(t, err)

	// Should count 2 unique customers (0xPayer1 and 0xPayer2)
	// 0xPayer3 is excluded (pending status), 0xPayer4 is outside time range
	assert.Equal(t, 2, count)
}
