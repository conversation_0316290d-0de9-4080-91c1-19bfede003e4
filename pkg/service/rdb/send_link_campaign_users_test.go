package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/stretchr/testify/assert"
)

func TestGetUserFirstTransactionTime(t *testing.T) {
	ctx := context.Background()
	Reset()

	r := GormRepo()
	users, uids := dbtest.Users()
	assert.Nil(t, r.BatchSetUsers(ctx, users))

	firstTxTime, err := r.GetUserFirstTransactionTime(ctx, uids[0])
	assert.Nil(t, err)
	assert.Nil(t, firstTxTime)

	oneDayAgo := time.Now().AddDate(0, 0, -1)
	txList := []*model.TxList{
		{
			Address:     "******************************************",
			TxTimestamp: oneDayAgo,
		},
	}
	assert.Nil(t, Get().Create(txList).Error)

	firstTxTime, err = r.GetUserFirstTransactionTime(ctx, uids[0])
	assert.Nil(t, err)
	assert.NotNil(t, firstTxTime)
	// Allow 1 second difference
	assert.WithinDuration(t, oneDayAgo, *firstTxTime, 1*time.Second)
}
