package rdb

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestCreateContactsAddedByPhoneNumber_FilterExisting(t *testing.T) {
	Reset()

	ctx := context.Background()
	existingContact := &domain.UpsertContactItem{
		OwnerID:     "owner123",
		UID:         util.Ptr("uid-existing"),
		PhoneNumber: util.Ptr("************"),
		Nickname:    util.Ptr("Existing Contact"),
	}

	// Insert existing contact
	err := UpsertContacts(ctx, nil, nil, []*domain.UpsertContactItem{existingContact}, nil)
	assert.Nil(t, err)

	// Attempt to add contacts, including one that already exists
	newContacts := []*domain.UpsertContactItem{
		{
			OwnerID:     "owner123",
			UID:         util.Ptr("uid-new-1"),
			PhoneNumber: util.Ptr("************"),
			Nickname:    util.Ptr("New Contact 1"),
		},
		{
			OwnerID:     "owner123",
			UID:         util.Ptr("uid-existing"), // Duplicate UID and PhoneNumber
			PhoneNumber: util.Ptr("************"),
			Nickname:    util.Ptr("Existing Contact Duplicate"),
		},
	}

	err = UpsertContacts(ctx, nil, nil, newContacts, nil)
	assert.Nil(t, err)

	// Retrieve all contacts for owner123
	contacts, kgErr := ListContacts(ctx, &domain.ListContactsParams{OwnerID: "owner123"})
	assert.Nil(t, kgErr)
	assert.Len(t, contacts, 2)

	// Verify that the duplicate was not added again
	for _, contact := range contacts {
		if contact.UID == "uid-existing" {
			assert.Equal(t, "Existing Contact", *contact.Nickname)
		} else {
			assert.Equal(t, "New Contact 1", *contact.Nickname)
		}
	}
}
