package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestUniversalSwap(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()

	t.Run("CreateUniversalSwap", func(t *testing.T) {
		// Prepare test data
		now := time.Now().UTC()
		swap := &domain.UniversalSwap{
			UID:     "test-uid",
			Status:  domain.UniversalSwapStatusPending,
			FeeRate: 0.1,
			Destination: &domain.Destination{
				Chain:         domain.Ethereum,
				WalletAddress: domain.NewEvmAddress("0x1234"),
				TokenID:       "0xtoken",
			},
			SourceTransactions: []*domain.SourceTransaction{
				{
					Chain:     domain.Ethereum,
					From:      domain.NewEvmAddress("0x5555"),
					To:        domain.NewEvmAddress("0x6666"),
					TokenID:   "0xsrctoken",
					RawAmount: "1000000",
					SignedTx:  "0xsigned",
					TxHash:    "0xhash1",
					Status:    domain.SourceTxStatusInit,
				},
			},
			SponsorTransactions: []*domain.SponsorTransaction{
				{
					Chain:     domain.Ethereum,
					From:      domain.NewEvmAddress("0x8888"),
					To:        domain.NewEvmAddress("0x5555"),
					RawAmount: "1000000",
					TxHash:    "0xsponsorhash",
					Status:    domain.SourceTxStatusInit,
				},
			},
			EstimatedFinishAt: now,
			CreatedAt:         now,
		}

		// Create record
		id, err := repo.CreateUniversalSwap(ctx, swap)
		require.NoError(t, err)
		assert.Greater(t, id, 0)

		// Verify record in database
		var record model.UniversalSwap
		err = Get().
			Preload("SourceTransactions").
			Preload("SponsorTransactions").
			Where("id = ?", id).First(&record).Error
		require.NoError(t, err)

		// Assert main record fields
		assert.Equal(t, swap.UID, record.UID)
		assert.Equal(t, swap.Status, record.Status)
		assert.Equal(t, swap.FeeRate, record.FeeRate)
		assert.Equal(t, swap.Destination.Chain.ID(), record.DestChain)
		assert.Equal(t, swap.Destination.WalletAddress.String(), record.DestWalletAddress)
		assert.Equal(t, swap.Destination.TokenID, record.DestTokenID)
		assert.WithinDuration(t, swap.EstimatedFinishAt, record.EstimatedFinishAt, time.Second)
		assert.WithinDuration(t, swap.CreatedAt, record.CreatedAt, time.Second)

		// Assert source transactions
		require.Len(t, record.SourceTransactions, 1)
		assert.Equal(t, swap.SourceTransactions[0].Chain.ID(), record.SourceTransactions[0].Chain)
		assert.Equal(t, swap.SourceTransactions[0].From.String(), record.SourceTransactions[0].From)
		assert.Equal(t, swap.SourceTransactions[0].To.String(), record.SourceTransactions[0].To)
		assert.Equal(t, swap.SourceTransactions[0].TokenID, record.SourceTransactions[0].TokenID)
		assert.Equal(t, swap.SourceTransactions[0].RawAmount, record.SourceTransactions[0].RawAmount)
		assert.Equal(t, swap.SourceTransactions[0].SignedTx, record.SourceTransactions[0].SignedTx)
		assert.Equal(t, swap.SourceTransactions[0].Status, record.SourceTransactions[0].Status)

		// Assert sponsor transactions
		require.Len(t, record.SponsorTransactions, 1)
		assert.Equal(t, swap.SponsorTransactions[0].Chain.ID(), record.SponsorTransactions[0].Chain)
		assert.Equal(t, swap.SponsorTransactions[0].From.String(), record.SponsorTransactions[0].From)
		assert.Equal(t, swap.SponsorTransactions[0].To.String(), record.SponsorTransactions[0].To)
		assert.Equal(t, swap.SponsorTransactions[0].RawAmount, record.SponsorTransactions[0].RawAmount)
		assert.Equal(t, swap.SponsorTransactions[0].TxHash, record.SponsorTransactions[0].TxHash)
		assert.Equal(t, swap.SponsorTransactions[0].Status, record.SponsorTransactions[0].Status)

		t.Run("UpdateUniversalSwap", func(t *testing.T) {
			// Test update status
			newStatus := domain.UniversalSwapStatusSuccess
			err := repo.UpdateUniversalSwap(ctx, &domain.UpdateUniversalSwapRequest{
				ID:     id,
				Status: &newStatus,
			})
			require.NoError(t, err)

			// Verify status update
			var updatedRecord model.UniversalSwap
			err = Get().First(&updatedRecord, id).Error
			require.NoError(t, err)
			assert.Equal(t, newStatus, updatedRecord.Status)

			// Test update source transaction
			newSrcStatus := domain.SourceTxStatusConfirmed
			fundsSent := true
			err = repo.UpdateUniversalSwap(ctx, &domain.UpdateUniversalSwapRequest{
				ID: id,
				Source: map[string]*domain.UpdateUniversalSwapSourceTx{
					"0xhash1": {
						Status:    &newSrcStatus,
						FundsSent: &fundsSent,
					},
				},
			})
			require.NoError(t, err)

			// Verify source transaction update
			var srcTx model.UniversalSwapSourceTx
			err = Get().Where("swap_id = ? AND tx_hash = ?", id, "0xhash1").First(&srcTx).Error
			require.NoError(t, err)
			assert.Equal(t, newSrcStatus, srcTx.Status)
			assert.Equal(t, fundsSent, srcTx.FundsSent)

			// Test update sponsor transaction
			newSponsorStatus := domain.SourceTxStatusConfirmed
			err = repo.UpdateUniversalSwap(ctx, &domain.UpdateUniversalSwapRequest{
				ID: id,
				Sponsor: map[string]*domain.SourceTxStatus{
					"0xsponsorhash": &newSponsorStatus,
				},
			})
			require.NoError(t, err)

			// Verify sponsor transaction update
			var sponsorTx model.UniversalSwapSponsorTx
			err = Get().Where("swap_id = ? AND tx_hash = ?", id, "0xsponsorhash").First(&sponsorTx).Error
			require.NoError(t, err)
			assert.Equal(t, newSponsorStatus, sponsorTx.Status)

			// Test update received amount
			receivedAmount := "2000000"
			err = repo.UpdateUniversalSwap(ctx, &domain.UpdateUniversalSwapRequest{
				ID:                id,
				ReceivedRawAmount: &receivedAmount,
			})
			require.NoError(t, err)

			// Verify received amount update
			err = Get().First(&updatedRecord, id).Error
			require.NoError(t, err)
			assert.Equal(t, receivedAmount, *updatedRecord.DestReceivedRawAmount)
		})

		t.Run("GetUniversalSwapByID", func(t *testing.T) {
			// Test get existing record
			found, err := repo.GetUniversalSwapByID(ctx, id)
			require.NoError(t, err)
			require.NotNil(t, found)

			assert.Equal(t, id, found.ID)
			assert.Equal(t, swap.UID, found.UID)
			assert.Equal(t, domain.UniversalSwapStatusSuccess, found.Status) // Updated status
			assert.Equal(t, swap.FeeRate, found.FeeRate)
			assert.Equal(t, swap.Destination.Chain, found.Destination.Chain)
			assert.Equal(t, swap.Destination.WalletAddress, found.Destination.WalletAddress)
			assert.Equal(t, swap.Destination.TokenID, found.Destination.TokenID)
			assert.Equal(t, "2000000", *found.Destination.ReceivedRawAmount) // Updated amount
			assert.WithinDuration(t, swap.EstimatedFinishAt, found.EstimatedFinishAt, time.Second)

			// Test get non-existent record
			notFound, err := repo.GetUniversalSwapByID(ctx, 99999)
			assert.ErrorIs(t, err, domain.ErrRecordNotFound)
			assert.Nil(t, notFound)
		})

		t.Run("GetUniversalSwapsByUID", func(t *testing.T) {
			// Create another swap for same UID
			swap2 := *swap
			_, err := repo.CreateUniversalSwap(ctx, &swap2)
			require.NoError(t, err)

			// Test get by UID
			found, err := repo.GetUniversalSwapsByUID(ctx, swap.UID)
			require.NoError(t, err)
			require.Len(t, found, 2)

			// Test get by non-existent UID
			empty, err := repo.GetUniversalSwapsByUID(ctx, "non-existent")
			require.NoError(t, err)
			assert.Empty(t, empty)
		})
	})
}
