package rdb_test

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/suite"
)

type organizationSuite struct {
	suite.Suite

	ctx  context.Context
	repo domain.StudioOrgRepo
}

func (s *organizationSuite) SetupTest() {
	rdb.Init()
	rdb.Reset()

	s.ctx = context.Background()
	s.repo = rdb.GormRepo()
}

func (s *organizationSuite) TestGetOrganizationsByActiveUser() {
	org1ID := 1
	org2ID := 2
	uid := util.RandString(20)

	{
		orgs, err := s.repo.GetOrganizationsByActiveUser(s.ctx, uid)
		s.NoError(err)
		s.Len(orgs, 0)
	}

	s.NoError(rdb.Get().Create(&model.StudioOrganization{
		ID:   org1ID,
		Name: "org1",
	}).Error)
	s.NoError(rdb.Get().Create(&model.StudioUser{
		OrganizationID: org1ID,
		UID:            uid,
		Status:         model.StudioUserStatusActive,
	}).Error)
	{
		orgs, err := s.repo.GetOrganizationsByActiveUser(s.ctx, uid)
		s.NoError(err)
		s.Len(orgs, 1)
		s.Equal(orgs[0].ID, org1ID)
	}

	s.NoError(rdb.Get().Create(&model.StudioOrganization{
		ID:   org2ID,
		Name: "org2",
	}).Error)
	s.NoError(rdb.Get().Create(&model.StudioUser{
		OrganizationID: org2ID,
		UID:            uid,
		Status:         model.StudioUserStatusPending,
	}).Error)
	{
		orgs, err := s.repo.GetOrganizationsByActiveUser(s.ctx, uid)
		s.NoError(err)
		s.Len(orgs, 1)
		s.Equal(orgs[0].ID, org1ID)
	}

	s.NoError(rdb.Get().Model(&model.StudioUser{}).Where(&model.StudioUser{
		OrganizationID: org2ID,
		UID:            uid,
	}).Updates(map[string]any{
		"status": model.StudioUserStatusActive,
	}).Error)
	{
		orgs, err := s.repo.GetOrganizationsByActiveUser(s.ctx, uid)
		s.NoError(err)
		s.Len(orgs, 2)
		s.Equal(orgs[0].ID, org1ID)
		s.Equal(orgs[1].ID, org2ID)
	}
}

func TestOrganizationSuite(t *testing.T) {
	suite.Run(t, new(organizationSuite))
}
