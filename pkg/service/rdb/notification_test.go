package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/suite"
)

type NotificationTestSuite struct {
	suite.Suite
	ctx       context.Context
	r         *Repo
	uid       string
	clientID  string
	now       time.Time
	yesterday time.Time
}

func (s *NotificationTestSuite) SetupTest() {
	s.ctx = context.Background()
	Reset()

	s.r = GormRepo()
	users, uids := dbtest.Users()
	s.Nil(s.r.BatchSetUsers(s.ctx, users))

	s.uid = uids[0]
	s.clientID = "test_client"

	s.now = time.Now()
	s.yesterday = s.now.AddDate(0, 0, -1)

	// Create test notifications
	notifications := []*model.Notification{
		{
			ID:          1,
			Receiver:    s.uid,
			ContentType: domain.NotificationContentTypeText,
			MessageType: domain.NotificationMessageTypeSystem,
			Title:       "Test 1",
			Summary:     "Summary 1",
			Message:     "Message 1",
			CreatedAt:   s.now,
			ClientID:    &s.clientID,
		},
		{
			ID:          2,
			Receiver:    s.uid,
			ContentType: domain.NotificationContentTypeText,
			MessageType: domain.NotificationMessageTypeSystem,
			Title:       "Test 2",
			Summary:     "Summary 2",
			Message:     "Message 2",
			CreatedAt:   s.yesterday,
			ClientID:    &s.clientID,
		},
		{
			ID:          3,
			Receiver:    ReceiverAll,
			ContentType: domain.NotificationContentTypeText,
			MessageType: domain.NotificationMessageTypeAnnouncement,
			Title:       "Test 3",
			Summary:     "Summary 3",
			Message:     "Message 3",
			CreatedAt:   s.now,
			ClientID:    &s.clientID,
		},
		{
			ID:          4,
			Receiver:    ReceiverAll,
			ContentType: domain.NotificationContentTypeText,
			MessageType: domain.NotificationMessageTypeAnnouncement,
			Title:       "Test 4",
			Summary:     "Summary 4",
			Message:     "Message 4",
			CreatedAt:   s.yesterday,
			ClientID:    nil, // Global announcement
		},
		{
			ID:          5,
			Receiver:    ReceiverAll,
			ContentType: domain.NotificationContentTypeText,
			MessageType: domain.NotificationMessageTypeAnnouncement,
			Title:       "Test 5",
			Summary:     "Summary 5",
			Message:     "Message 5",
			CreatedAt:   time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC), // old announcement
			ClientID:    &s.clientID,
		},
	}

	s.Nil(Get().Create(&notifications).Error)
}

func TestNotificationSuite(t *testing.T) {
	suite.Run(t, new(NotificationTestSuite))
}

func (s *NotificationTestSuite) TestAllUnreadNotifications() {
	count := NotificationUnreadCount(s.ctx, s.uid, s.clientID)
	s.Equal(3, count) // Should count notifications 1, 2, and 3 (same clientID). Not include notification before ss_create_time
}

func (s *NotificationTestSuite) TestWithSomeReadNotifications() {
	// Mark notification 1 as read
	notificationRead := &model.NotificationsRead{
		UID:       s.uid,
		Nid:       1,
		CreatedAt: s.now,
	}
	s.Nil(Get().Create(notificationRead).Error)

	count := NotificationUnreadCount(s.ctx, s.uid, s.clientID)
	s.Equal(2, count) // Should count notifications 2 and 3
}

func (s *NotificationTestSuite) TestWithReadAllTimestamp() {
	// Update user's read_all_timestamp to yesterday
	readAllTime := s.yesterday.Add(time.Second)
	s.Nil(s.r.SetUser(s.ctx, &domain.UserData{
		UserInfo: domain.UserInfo{
			UID: s.uid,
			ReadAllTimeStampMap: map[string]*util.CustomTime{
				s.clientID: {Time: &readAllTime},
			},
		},
	}))

	count := NotificationUnreadCount(s.ctx, s.uid, s.clientID)
	s.Equal(2, count) // Should only count notification created after yesterday
}

func (s *NotificationTestSuite) TestNonExistentUser() {
	count := NotificationUnreadCount(s.ctx, "non-existent-uid", s.clientID)
	s.Equal(0, count)
}

func (s *NotificationTestSuite) TestWithReadAllTimestampBeforeAllNotifications() {
	// Update user's read_all_timestamp to two days ago
	readAllTime := s.yesterday.AddDate(0, 0, -2).Add(time.Second)
	s.Nil(s.r.SetUser(s.ctx, &domain.UserData{
		UserInfo: domain.UserInfo{
			UID: s.uid,
			ReadAllTimeStampMap: map[string]*util.CustomTime{
				s.clientID: {Time: &readAllTime},
			},
		},
	}))

	count := NotificationUnreadCount(s.ctx, s.uid, s.clientID)
	s.Equal(3, count) // Should count all unread notifications after two days ago
}

func (s *NotificationTestSuite) TestWithNoSSCreateTime() {
	s.Nil(Get().Model(&model.User{}).Where("uid = ?", s.uid).Update("ss_create_time", nil).Error)

	count := NotificationUnreadCount(s.ctx, s.uid, s.clientID)
	s.Equal(4, count) // Should count all unread notifications including old announcement
}
