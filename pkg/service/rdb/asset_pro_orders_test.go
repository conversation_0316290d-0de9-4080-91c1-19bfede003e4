package rdb

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type validateAndCreateOrderSuite struct {
	suite.Suite
	repo           domain.AssetProOrderRepo
	OrganizationID int
}

func TestValidateAndCreateOrderSuite(t *testing.T) {
	suite.Run(t, new(validateAndCreateOrderSuite))
}

func (s *validateAndCreateOrderSuite) SetupTest() {
	Init()
	Reset()
	s.OrganizationID = 1
	s.repo = GormRepo()

	s.NoError(rdbtest.CreateStudioOrganizations(Get()))
	s.NoError(rdbtest.CreateCustomers(Get(), []*model.Customer{{OrganizationID: 1, UID: "user1"}, {OrganizationID: 1, UID: "user2"}, {OrganizationID: 1, UID: "user3"}}))
	s.NoError(rdbtest.CreateDefaultStudioUsers(Get()))
	s.NoError(rdbtest.CreateAssetProProducts(Get(), s.OrganizationID))
	s.NoError(rdbtest.CreateStudioMarkets(Get()))

}

func (s *validateAndCreateOrderSuite) TestNormal() {
	ctx := context.Background()
	uid := "user1"
	orderID := fmt.Sprintf("%d-%d-%d", s.OrganizationID, time.Now().Unix(), 1)
	s.Nil(s.repo.ValidateAndCreateOrder(ctx, uid, &domain.CreateAssetProOrderParams{
		ID:            orderID,
		ProductID:     1,
		Name:          "test",
		WalletAddress: "0x123",
		Price:         decimal.NewFromFloat(28.5),
		Amount:        decimal.NewFromFloat(10),
		// 200.5 to 1000.5
		TotalCost:    decimal.NewFromFloat(28.5 * 10 * (1 + 0.005)),
		USDAmount:    decimal.NewFromFloat(28.5 * 10.1),
		USDTotalCost: decimal.NewFromFloat(28.5 * 10.1),
		ExchangeRate: decimal.NewFromFloat(30),
		TransferTo: domain.AssetProOrderBankAccount{
			BankName:          util.Ptr("bank"),
			BranchName:        util.Ptr("branch"),
			AccountNumber:     util.Ptr("123"),
			AccountHolderName: util.Ptr("name"),
		},
		FeeType:                   domain.AssetProProductFeeTypeFeeIncluded,
		ProportionalFeePercentage: decimal.NewFromFloat(1),
		ProportionalMinimumFee:    decimal.NewFromFloat(50.12),
		PaymentStatus:             domain.AssetProPaymentStatusUnpaid,
		OrderStatus:               domain.AssetProOrderStatusUnpaid,
	}, &domain.CreateAssetProOrderAuditLogParams{
		OrderID:          orderID,
		OrganizationID:   s.OrganizationID,
		CustomerUID:      &uid,
		PaymentStatusOld: nil,
		PaymentStatusNew: util.Ptr(domain.AssetProPaymentStatusUnpaid),
		OrderStatusOld:   nil,
		OrderStatusNew:   util.Ptr(domain.AssetProOrderStatusUnpaid),
	}))
	order := model.AssetProOrder{}
	s.NoError(Get().Where("id = ?", orderID).First(&order).Error)
	s.NoError(Get().Where("order_id = ?", orderID).First(&model.AssetProOrderAuditLog{}).Error)
	s.Equal(orderID, order.ID)
	s.Equal(s.OrganizationID, order.OrganizationID)
	s.Equal(uid, order.CustomerUID)
	s.Equal("0x123", order.WalletAddress)
	s.Equal(1, order.ProductID)
	s.True(decimal.NewFromFloat(10).Equal(order.Amount))
	s.True(decimal.NewFromFloat(28.5).Equal(order.Price))
	s.True(decimal.NewFromFloat(28.5 * 10 * (1 + 0.005)).Equal(order.TotalCost))
	s.True(decimal.NewFromFloat(28.5 * 10.1).Equal(order.USDAmount))
	s.True(decimal.NewFromFloat(28.5 * 10.1).Equal(order.USDTotalCost))
	s.True(decimal.NewFromFloat(30).Equal(order.ExchangeRate))
	s.Equal(domain.AssetProProductFeeTypeFeeIncluded, order.FeeType)
	s.True(decimal.NewFromFloat(1).Equal(*order.ProportionalFeePercentage))
	s.True(decimal.NewFromFloat(50.12).Equal(*order.ProportionalMinimumFee))
	s.Equal(domain.AssetProPaymentStatusUnpaid, order.PaymentStatus)
	s.Equal(domain.AssetProOrderStatusUnpaid, order.OrderStatus)
	s.Equal("bank", *order.TransferToBankName)
	s.Equal("branch", *order.TransferToBranchName)
	s.Equal("123", *order.TransferToAccountNumber)
	s.Equal("name", *order.TransferToAccountHolderName)
}

func (s *validateAndCreateOrderSuite) TestNoStock() {
	ctx := context.Background()
	wg := sync.WaitGroup{}
	times := 3
	uids := []string{"user1", "user2", "user3"}
	kgErrs := make([]*code.KGError, times)

	for i := 0; i < times; i++ {
		wg.Add(1)
		go func(i int, uid string) {
			defer wg.Done()
			kgErr := s.repo.ValidateAndCreateOrder(ctx, uid, &domain.CreateAssetProOrderParams{
				ID:            fmt.Sprintf("%d-%d-%d", s.OrganizationID, time.Now().Unix(), i+1),
				ProductID:     3,
				Name:          "test",
				WalletAddress: "0x123",
				Price:         decimal.NewFromFloat(20.5),
				Amount:        decimal.NewFromFloat(500), // total stock is 1230.5
				TotalCost:     decimal.NewFromFloat(20.5 * 500),
				USDAmount:     decimal.NewFromFloat(20.5 * 500),
				USDTotalCost:  decimal.NewFromFloat(20.5 * 500),
				ExchangeRate:  decimal.NewFromFloat(30),
				TransferTo: domain.AssetProOrderBankAccount{
					BankName:          util.Ptr("bank"),
					BranchName:        util.Ptr("branch"),
					AccountNumber:     util.Ptr("123"),
					AccountHolderName: util.Ptr("name"),
				},
				FeeType:                   domain.AssetProProductFeeTypeNoFee,
				ProportionalFeePercentage: decimal.NewFromFloat(0.0),
				ProportionalMinimumFee:    decimal.NewFromFloat(0.0),
				PaymentStatus:             domain.AssetProPaymentStatusUnpaid,
				OrderStatus:               domain.AssetProOrderStatusUnpaid,
			}, &domain.CreateAssetProOrderAuditLogParams{
				OrderID:          fmt.Sprintf("%d-%d-%d", s.OrganizationID, time.Now().Unix(), i+1),
				OrganizationID:   s.OrganizationID,
				CustomerUID:      &uid,
				PaymentStatusOld: nil,
				PaymentStatusNew: util.Ptr(domain.AssetProPaymentStatusUnpaid),
				OrderStatusOld:   nil,
				OrderStatusNew:   util.Ptr(domain.AssetProOrderStatusUnpaid),
			})
			kgErrs[i] = kgErr
		}(i, uids[i])
	}

	wg.Wait()
	errCount := 0
	for _, kgErr := range kgErrs {
		if kgErr != nil {
			errCount++
			s.Equal("stock is not enough", kgErr.String())
		}
	}
	s.Equal(1, errCount)
	actualOrders := []*model.AssetProOrder{}
	s.NoError(Get().Model(&model.AssetProOrder{}).Where("product_id = 3").Find(&actualOrders).Error)
	s.Len(actualOrders, 2)
}

type cancelOrderForMerchantSuite struct {
	suite.Suite
	repo           domain.AssetProOrderRepo
	OrganizationID int
}

func TestCancelOrderForMerchant(t *testing.T) {
	suite.Run(t, new(cancelOrderForMerchantSuite))
}

func (s *cancelOrderForMerchantSuite) SetupTest() {
	Init()
	Reset()
	s.OrganizationID = 1
	s.repo = GormRepo()

	s.NoError(rdbtest.CreateStudioOrganizations(Get()))
	s.NoError(rdbtest.CreateCustomers(Get(), []*model.Customer{{OrganizationID: 1, UID: "user1"}, {OrganizationID: 1, UID: "user2"}, {OrganizationID: 1, UID: "user3"}}))
	s.NoError(rdbtest.CreateDefaultStudioUsers(Get()))
	s.NoError(rdbtest.CreateAssetProProducts(Get(), s.OrganizationID))
	s.NoError(rdbtest.CreateStudioMarkets(Get()))

}

func (s *cancelOrderForMerchantSuite) TestNormal() {
	ctx := context.Background()
	tests := []struct {
		name          string
		paymentStatus domain.AssetProPaymentStatus
		orderStatus   domain.AssetProOrderStatus
		wants         struct {
			orderStatusNew   domain.AssetProOrderStatus
			paymentStatusNew domain.AssetProPaymentStatus
		}
	}{
		{
			name:          "unpaid",
			paymentStatus: domain.AssetProPaymentStatusUnpaid,
			orderStatus:   domain.AssetProOrderStatusUnpaid,
			wants: struct {
				orderStatusNew   domain.AssetProOrderStatus
				paymentStatusNew domain.AssetProPaymentStatus
			}{
				orderStatusNew:   domain.AssetProOrderStatusCancelled,
				paymentStatusNew: domain.AssetProPaymentStatusAwaitingRefund,
			},
		},
		{
			name:          "paid, awaiting confirmation",
			paymentStatus: domain.AssetProPaymentStatusUnpaid,
			orderStatus:   domain.AssetProOrderStatusAwaitingConfirmation,
			wants: struct {
				orderStatusNew   domain.AssetProOrderStatus
				paymentStatusNew domain.AssetProPaymentStatus
			}{
				orderStatusNew:   domain.AssetProOrderStatusCancelled,
				paymentStatusNew: domain.AssetProPaymentStatusUnpaid,
			},
		},
	}
	for i, tt := range tests {

		// create order
		uid := "user1"
		operatorUID := "testuser2"
		orderID := fmt.Sprintf("%d-%d-%d", s.OrganizationID, time.Now().Unix()+int64(i*10), 1)
		s.Nil(s.repo.ValidateAndCreateOrder(ctx, uid, &domain.CreateAssetProOrderParams{
			ID:            orderID,
			ProductID:     1,
			Name:          "test",
			WalletAddress: "0x123",
			Price:         decimal.NewFromFloat(28.5),
			Amount:        decimal.NewFromFloat(10),
			TotalCost:     decimal.NewFromFloat(28.5 * 10 * (1 + 0.005)),
			USDAmount:     decimal.NewFromFloat(28.5 * 10.1),
			USDTotalCost:  decimal.NewFromFloat(28.5 * 10.1),
			ExchangeRate:  decimal.NewFromFloat(30),
			TransferTo: domain.AssetProOrderBankAccount{
				BankName:          util.Ptr("bank"),
				BranchName:        util.Ptr("branch"),
				AccountNumber:     util.Ptr("123"),
				AccountHolderName: util.Ptr("name"),
			},
			FeeType:                   domain.AssetProProductFeeTypeFeeIncluded,
			ProportionalFeePercentage: decimal.NewFromFloat(1),
			ProportionalMinimumFee:    decimal.NewFromFloat(50.12),
			PaymentStatus:             tt.paymentStatus,
			OrderStatus:               tt.orderStatus,
		}, &domain.CreateAssetProOrderAuditLogParams{
			OrderID:          orderID,
			OrganizationID:   s.OrganizationID,
			CustomerUID:      &uid,
			PaymentStatusOld: nil,
			PaymentStatusNew: util.Ptr(domain.AssetProPaymentStatusUnpaid),
			OrderStatusOld:   nil,
			OrderStatusNew:   util.Ptr(domain.AssetProOrderStatusUnpaid),
		}))
		s.NoError(Get().Where("id = ?", orderID).First(&model.AssetProOrder{}).Error)
		s.NoError(Get().Where("order_id = ?", orderID).First(&model.AssetProOrderAuditLog{}).Error)

		// check product's stock
		product := &model.AssetProProduct{}
		s.NoError(Get().Where("id = 1").First(product).Error)
		s.Equal(0, decimal.NewFromFloat(1220.5).Cmp(*product.Stock))

		// cancel order
		s.Nil(s.repo.CancelOrderForMerchant(ctx, s.OrganizationID, orderID, &domain.CancelOrderForMerchantParams{
			OperatorUID: operatorUID,
		}))
		auditLog := &model.AssetProOrderAuditLog{}
		s.NoError(Get().Where("order_id = ?", orderID).Order("id desc").First(auditLog).Error)
		s.Equal(tt.orderStatus, *auditLog.OrderStatusOld)
		s.Equal(tt.wants.orderStatusNew, *auditLog.OrderStatusNew)
		if auditLog.PaymentStatusOld != nil {
			s.Equal(tt.paymentStatus, *auditLog.PaymentStatusOld)
		}
		if auditLog.PaymentStatusNew != nil {
			s.Equal(tt.wants.paymentStatusNew, *auditLog.PaymentStatusNew)
		}
		// check product's stock
		s.NoError(Get().Where("id = 1").First(product).Error)
		s.Equal(0, decimal.NewFromFloat(1230.5).Cmp(*product.Stock))
	}
}
