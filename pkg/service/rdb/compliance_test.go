package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/paging"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type complyFlowTestSuite struct {
	suite.Suite
	Repo           domain.ComplianceRepository
	OrganizationID int
}

func (suite *complyFlowTestSuite) SetupTest() {
	Init()
	suite.Repo = GormRepo()
	suite.OrganizationID = 1
	Reset()

	assert.Nil(suite.T(), dbtest.CreateCustomersWithDefault(Get()))
	assert.Nil(suite.T(), dbtest.CreateCaseSubmissions(Get()))
	assert.Nil(suite.T(), dbtest.CreateKycAuditLog(Get()))
	auditorUID := "testuser2"
	assert.Nil(suite.T(), dbtest.CreateStudioUsers(Get(), auditorUID, nil))
}

func (suite *complyFlowTestSuite) TestNoQueryParams() {
	ctx := context.Background()
	cases, paging, err := suite.Repo.GetLatestCaseByUsers(ctx, suite.OrganizationID, nil, &paging.Query{Paging: &paging.Paging{PageNumber: 1, PageSize: 10}, Sorting: paging.SortingList{{Column: "submitted_at", Direction: paging.DirectionDESC}}})

	assert.Nil(suite.T(), err)
	assert.Equal(suite.T(), 3, paging.TotalCount)
	for _, c := range cases {
		switch c.UID {
		case "user1":
			assert.Equal(suite.T(), 2, c.CaseID)
			assert.Equal(suite.T(), 2, util.Val(c.FormID))
			assert.Equal(suite.T(), 2, util.Val(c.IdvID))
			assert.Equal(suite.T(), 2, util.Val(c.CddID))
			assert.Equal(suite.T(), domain.KycStatusVerified, util.Val(c.KycStatus))
			assert.Equal(suite.T(), domain.IdvStatusAccept, util.Val(c.IdvStatus))
			assert.Equal(suite.T(), 32, util.Val(c.RiskScore))
			assert.Equal(suite.T(), false, util.Val(c.SanctionMatched))
			assert.Equal(suite.T(), "internal notes 3", *c.InternalNotes)

		case "user2":
			assert.Equal(suite.T(), 7, c.CaseID)
			assert.Equal(suite.T(), 7, util.Val(c.FormID))
			assert.Nil(suite.T(), c.IdvID)
			assert.Nil(suite.T(), c.CddID)
			assert.Equal(suite.T(), domain.KycStatusPending, util.Val(c.KycStatus))
			assert.Nil(suite.T(), c.IdvStatus)
			assert.Nil(suite.T(), c.RiskScore)
			assert.Nil(suite.T(), c.SanctionMatched)
		case "user3":
			assert.Equal(suite.T(), 6, c.CaseID)
			assert.Equal(suite.T(), 4, util.Val(c.FormID))
			assert.Equal(suite.T(), 5, util.Val(c.IdvID))
			assert.Equal(suite.T(), 6, util.Val(c.CddID))
			assert.Equal(suite.T(), domain.KycStatusRejected, util.Val(c.KycStatus))
			assert.Equal(suite.T(), domain.IdvStatusReject, util.Val(c.IdvStatus))
			assert.Equal(suite.T(), 67, util.Val(c.RiskScore))
			assert.Equal(suite.T(), true, util.Val(c.SanctionMatched))
			assert.Equal(suite.T(), domain.RejectReasons{"sanctioned", "high_risk_money_laundering"}, *c.RejectReasons)
			assert.Equal(suite.T(), "internal notes 4", *c.InternalNotes)
			assert.True(suite.T(), *c.ReviewedAt > 0)
			assert.Equal(suite.T(), "testuser2", *c.ReviewerName)

		}
	}
}

// the submit time of user3 is after 6 days from now, so it should not be in the result
func (suite *complyFlowTestSuite) TestSubmitTime() {
	ctx := context.Background()
	cases, paging, err := suite.Repo.GetLatestCaseByUsers(ctx, suite.OrganizationID, &domain.GetLatestCaseByUsersParams{
		SubmittedAtFrom: util.Ptr(int(time.Now().AddDate(0, 0, 2).Add(-10 * time.Minute).Unix())),
		SubmittedAtTo:   util.Ptr(int(time.Now().AddDate(0, 0, 4).Unix())),
	}, &paging.Query{Paging: &paging.Paging{PageNumber: 1, PageSize: 10}, Sorting: paging.SortingList{{Column: "submitted_at", Direction: paging.DirectionDESC}}})

	assert.Nil(suite.T(), err)
	assert.Equal(suite.T(), 2, paging.TotalCount)
	for _, c := range cases {
		suite.T().Log(c)
		if lo.Contains([]string{"user3"}, c.UID) {
			assert.Fail(suite.T(), "these user_ids should not be in the result")
		}
	}

}

func (suite *complyFlowTestSuite) TestRiskLabel() {
	ctx := context.Background()
	_, paging, err := suite.Repo.GetLatestCaseByUsers(ctx, suite.OrganizationID, &domain.GetLatestCaseByUsersParams{
		// label: middle
		RiskScoreFrom: util.Ptr(33),
		RiskScoreTo:   util.Ptr(66),
	}, &paging.Query{Paging: &paging.Paging{PageNumber: 1, PageSize: 10}, Sorting: paging.SortingList{{Column: "submitted_at", Direction: paging.DirectionDESC}}})

	assert.Nil(suite.T(), err)
	assert.Equal(suite.T(), 0, paging.TotalCount)
}

func (suite *complyFlowTestSuite) TestSanctioned() {
	ctx := context.Background()
	cases, paging, err := suite.Repo.GetLatestCaseByUsers(ctx, suite.OrganizationID, &domain.GetLatestCaseByUsersParams{
		Sanctioned: util.Ptr(false),
	}, &paging.Query{Paging: &paging.Paging{PageNumber: 1, PageSize: 10}, Sorting: paging.SortingList{{Column: "submitted_at", Direction: paging.DirectionDESC}}})
	assert.Nil(suite.T(), err)
	assert.Equal(suite.T(), 1, paging.TotalCount)

	for _, c := range cases {
		if lo.Contains([]string{"user2", "user3"}, c.UID) {
			assert.Fail(suite.T(), "these user_ids should not be in the result")
		}
	}
}

func (suite *complyFlowTestSuite) TestIdvStatus() {
	ctx := context.Background()
	cases, paging, err := suite.Repo.GetLatestCaseByUsers(ctx, suite.OrganizationID, &domain.GetLatestCaseByUsersParams{
		IdvStatus: []*domain.IdvStatus{util.Ptr(domain.IdvStatusAccept), util.Ptr(domain.IdvStatusReview)},
	}, &paging.Query{Paging: &paging.Paging{PageNumber: 1, PageSize: 10}, Sorting: paging.SortingList{{Column: "submitted_at", Direction: paging.DirectionDESC}}})

	assert.Nil(suite.T(), err)
	assert.Equal(suite.T(), 1, paging.TotalCount)

	for _, c := range cases {
		if lo.Contains([]string{"user2", "user3"}, c.UID) {
			assert.Fail(suite.T(), "these user_ids should not be in the result")
		}
	}
}

func (suite *complyFlowTestSuite) TestWithFuzzySearch() {
	ctx := context.Background()

	fuzzySearchCases := []string{"侯", "333333333", "<EMAIL>", "C333333333", "line-3"}

	for _, fuzzySearch := range fuzzySearchCases {
		cases, paging, err := suite.Repo.GetLatestCaseByUsers(ctx, suite.OrganizationID, &domain.GetLatestCaseByUsersParams{}, &paging.Query{
			FuzzySearch: fuzzySearch,
			Paging:      &paging.Paging{PageNumber: 1, PageSize: 10}, Sorting: paging.SortingList{{Column: "submitted_at", Direction: paging.DirectionDESC}}})

		assert.Nil(suite.T(), err)
		assert.Equal(suite.T(), 1, paging.TotalCount)

		for _, c := range cases {
			if lo.Contains([]string{"user1", "user2"}, c.UID) {
				assert.Fail(suite.T(), "these user_ids should not be in the result")
			}
		}
	}
}

func (suite *complyFlowTestSuite) TestNoResult() {
	ctx := context.Background()

	fuzzySearchCases := []string{"沒有這個人"}

	for _, fuzzySearch := range fuzzySearchCases {
		_, paging, err := suite.Repo.GetLatestCaseByUsers(ctx, suite.OrganizationID, &domain.GetLatestCaseByUsersParams{}, &paging.Query{
			FuzzySearch: fuzzySearch,
			Paging:      &paging.Paging{PageNumber: 1, PageSize: 10}, Sorting: paging.SortingList{{Column: "submitted_at", Direction: paging.DirectionDESC}}})

		assert.Nil(suite.T(), err)
		assert.Equal(suite.T(), 0, paging.TotalCount)

	}
}

func (suite *complyFlowTestSuite) TestGetPendingCaseCount() {
	ctx := context.Background()

	count, kgErr := suite.Repo.GetPendingCaseCount(ctx, suite.OrganizationID)
	assert.Nil(suite.T(), kgErr)
	assert.Equal(suite.T(), 1, count)

}

func TestComplyFlowTestSuite(t *testing.T) {
	suite.Run(t, new(complyFlowTestSuite))
}
