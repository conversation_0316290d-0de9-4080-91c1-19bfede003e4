package rdb

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/stretchr/testify/assert"
)

func TestGetTokenPrice(t *testing.T) {
	// Reset the database to a fresh state
	Reset()

	// Create seed data
	assert.Nil(t, dbtest.CreateAssets(Get()))
	assert.Nil(t, dbtest.CreateAssetPrices(Get()))

	// Define test cases
	testCases := []struct {
		name          string
		chain         domain.Chain
		tokenID       string
		expectedPrice float64
		expectError   bool
	}{
		{
			name:          "Valid Ethereum Token",
			chain:         domain.Ethereum,
			tokenID:       "******************************************", // FavoriteAsset
			expectedPrice: 0.01,
			expectError:   false,
		},
		{
			name:        "Non-existent Token",
			chain:       domain.Ethereum,
			tokenID:     "0xNonExistentToken",
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			price, err := GormRepo().GetTokenPrice(context.Background(), tc.chain, tc.tokenID)

			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedPrice, price)
			}
		})
	}
}

func TestBatchGetTokenPrices(t *testing.T) {
	// Reset the database to a fresh state
	Reset()

	// Create seed data
	assert.Nil(t, dbtest.CreateAssets(Get()))
	assert.Nil(t, dbtest.CreateAssetPrices(Get()))

	// Define test cases
	testCases := []struct {
		name           string
		tokens         []domain.ChainToken
		expectedPrices map[domain.ChainToken]float64
		expectError    bool
	}{
		{
			name: "Multiple Tokens",
			tokens: []domain.ChainToken{
				{Chain: domain.Ethereum, TokenID: "******************************************"}, // FavoriteAsset
				{Chain: domain.Ronin, TokenID: "ronin"},                                         // NotFavoriteAsset
				{Chain: domain.Ethereum, TokenID: "0xNonExistentToken"},                         // Not Exist
			},
			expectedPrices: map[domain.ChainToken]float64{
				{Chain: domain.Ethereum, TokenID: "******************************************"}: 0.01,
				{Chain: domain.Ronin, TokenID: "ronin"}:                                         0.02,
			},
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			prices, err := GormRepo().BatchGetTokenPrices(context.Background(), tc.tokens)

			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedPrices, prices)
			}
		})
	}
}

func TestGetTokenPricesIn24H(t *testing.T) {
	// Reset the database to a fresh state
	Reset()

	// Create seed data
	assert.Nil(t, dbtest.CreateAssets(Get()))
	assert.Nil(t, dbtest.CreateAssetPriceHistories(Get()))

	// Define test cases
	testCases := []struct {
		name                string
		chain               domain.Chain
		tokenID             string
		expectedPricePoints []*domain.PricePoint
		expectError         bool
	}{
		{
			name:    "Valid Ethereum Token",
			chain:   domain.Ethereum,
			tokenID: "******************************************", // FavoriteAsset
			expectedPricePoints: []*domain.PricePoint{
				{
					Price: 0.012,
				},
				{
					Price: 0.01,
				},
			},
			expectError: false,
		},
		{
			name:        "Non-existent Token",
			chain:       domain.Ethereum,
			tokenID:     "0xNonExistentToken",
			expectError: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			prices, err := GormRepo().GetTokenPricesIn24H(context.Background(), tc.chain, tc.tokenID)

			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				for i := range tc.expectedPricePoints {
					assert.Equal(t, tc.expectedPricePoints[i].Price, prices[i].Price)
				}
			}
		})
	}
}

func TestBatchGetTokenPricesIn24H(t *testing.T) {
	// Reset the database to a fresh state
	Reset()

	// Create seed data
	assert.Nil(t, dbtest.CreateAssets(Get()))
	assert.Nil(t, dbtest.CreateAssetPriceHistories(Get()))

	// Define test cases
	testCases := []struct {
		name                string
		tokens              []domain.ChainToken
		expectedPricePoints map[domain.ChainToken][]*domain.PricePoint
		expectError         bool
	}{
		{
			name: "Multiple Tokens",
			tokens: []domain.ChainToken{
				{Chain: domain.Ethereum, TokenID: "******************************************"}, // FavoriteAsset
				{Chain: domain.Ronin, TokenID: "ronin"},                                         // NotFavoriteAsset
			},
			expectedPricePoints: map[domain.ChainToken][]*domain.PricePoint{
				{Chain: domain.Ethereum, TokenID: "******************************************"}: {
					{
						Price: 0.012,
					},
					{
						Price: 0.01,
					},
				},
				{Chain: domain.Ronin, TokenID: "ronin"}: {
					{
						Price: 0.017,
					},
					{
						Price: 0.02,
					},
				},
			},
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			priceMap, err := GormRepo().BatchGetTokenPricesIn24H(context.Background(), tc.tokens)

			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				for _, token := range tc.tokens {
					for i := range tc.expectedPricePoints[token] {
						assert.Equal(t, tc.expectedPricePoints[token][i].Price, priceMap[token][i].Price)
					}
				}
			}
		})
	}
}
