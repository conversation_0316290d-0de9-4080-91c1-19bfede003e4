package rdb

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"

	"github.com/stretchr/testify/assert"
)

func TestGetAllUserWallets(t *testing.T) {
	ctx := context.Background()
	Reset()
	users, _ := dbtest.Users()
	assert.Nil(t, GormRepo().BatchSetUsers(ctx, users))

	wallets, err := GetAllUserWallets(ctx)
	assert.Nil(t, err)
	assert.Len(t, wallets, 25)
	foundObserver := false
	for _, wallet := range wallets {
		if wallet.WalletType == domain.WalletTypeObserver {
			foundObserver = true
			break
		}
	}
	assert.True(t, foundObserver)
}

func TestGetUIDsByAddresses(t *testing.T) {
	Reset()
	ctx := context.Background()
	users, uids := dbtest.Users()
	r := GormRepo()
	r.Batch<PERSON>etUsers(ctx, users)

	address1 := "TAFD6ZsENuFX5MW6ZPqkkpKNPknArq3iii"         // Owned by uid1
	address2 := "******************************************" // Owned by uid1 and uid2
	uid1 := uids[0]
	uid2 := uids[1]

	// Call to see if it gets the right UIDs of given addresses
	currentUIDs, err := r.GetUIDsByAddresses(ctx, []string{address1})
	assert.Nil(t, err)
	assert.ElementsMatch(t, []string{uid1}, currentUIDs)

	currentUIDs, err = r.GetUIDsByAddresses(ctx, []string{address2})
	assert.Nil(t, err)
	assert.ElementsMatch(t, []string{uid1, uid2}, currentUIDs)

	currentUIDs, err = r.GetUIDsByAddresses(ctx, []string{address1, address2})
	assert.Nil(t, err)
	assert.ElementsMatch(t, []string{uid1, uid2}, currentUIDs)
}

func TestBatchCheckAddressOwnedByUser(t *testing.T) {
	Reset()
	ctx := context.Background()
	users, _ := dbtest.Users()
	r := GormRepo()
	r.BatchSetUsers(ctx, users)

	addrs := []domain.Address{
		domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
		domain.NewTronAddress("TL4GB7cvtr9eVXR4a5GVpokfGa6WKywXFw"),
		domain.NewTronAddress("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"),
	}
	ownedAddressesMap, err := r.BatchCheckAddressOwnedByUser(ctx, domain.Tron, addrs)
	assert.Nil(t, err)
	for _, addr := range addrs {
		assert.Equal(t, ownedAddressesMap[addr], true)
	}
}
