package rdb

import (
	"context"
	"sort"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
)

type AssetRepoTestSuite struct {
	suite.Suite
	repo domain.AssetRepo
	ctx  context.Context
}

func (s *AssetRepoTestSuite) SetupSuite() {
	Init()
	s.repo = GormRepo()
	s.ctx = context.Background()
}

func (s *AssetRepoTestSuite) SetupTest() {
	Reset()
}

func TestAssetRepoSuite(t *testing.T) {
	suite.Run(t, new(AssetRepoTestSuite))
}

func (s *AssetRepoTestSuite) TestSetTokenAmounts() {
	// Pre-insert three token assets
	preInsertAssets := []model.Asset{
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xabc",
			Amount:        util.Ptr(decimal.NewFromFloat(100).String()),
			WalletAddress: "0x123",
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Test Token",
			Symbol:        util.Ptr("TST"),
			LogoUrls:      `["http://logo.url"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xdef",
			Amount:        util.Ptr(decimal.NewFromFloat(50).String()),
			WalletAddress: "0x123",
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Another Token",
			Symbol:        util.Ptr("ANT"),
			LogoUrls:      `["http://another.logo"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xghi",
			Amount:        util.Ptr(decimal.NewFromFloat(75).String()),
			WalletAddress: "0x456",
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Third Token",
			Symbol:        util.Ptr("THAT"),
			LogoUrls:      `["http://third.logo"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
	}

	s.NoError(Get().Create(&preInsertAssets).Error)

	// Set the token amounts (this should delete existing assets and set new ones)
	amounts := map[domain.ChainAddress][]*domain.TokenAmount{
		{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x123")}: {
			{
				Token:  domain.NewToken(domain.Ethereum, "0xabc", "Test Token", "TST", "http://logo.url", 18, true),
				Amount: decimal.NewFromFloat(100.000000000000000001),
			},
		},
	}

	s.NoError(s.repo.SetTokenAmounts(s.ctx, amounts))

	// Verify the data was set correctly
	assets, totalCount, err := s.repo.ListAssets(s.ctx, &domain.ListAssetsParam{
		Addresses: map[domain.Chain][]domain.Address{
			domain.Ethereum: {domain.NewEvmAddress("0x123")},
		},
		Types:      []domain.AssetType{domain.AssetTypeToken},
		PageSize:   10,
		PageNumber: 1,
	})
	s.NoError(err)
	s.Len(assets, 1)
	s.Equal(1, totalCount)

	asset := assets[0].(*domain.TokenAsset)
	s.Equal(domain.Ethereum, asset.Chain())
	s.Equal("0xabc", asset.ID())
	s.Equal(domain.AssetTypeToken, asset.Type())
	s.Equal("Test Token", asset.Name())
	s.Equal([]string{"http://logo.url"}, asset.LogoUrls())
	s.InDelta(0, asset.UsdValue(), 0.001) // UsdValue might be 0 if price is not set
	s.True(asset.IsVerified())
	s.Equal("TST", asset.Symbol())
	s.Equal(uint(18), asset.Decimals())
	s.Len(asset.WalletAmounts, 1)
	s.Equal(domain.NewEvmAddress("0x123"), asset.WalletAmounts[0].Address)
	s.Equal(decimal.NewFromFloat(100.000000000000000001).String(), asset.WalletAmounts[0].Amount.String())
}

func (s *AssetRepoTestSuite) TestUpdateTokenAmounts() {
	// Pre-insert three token assets
	preInsertAssets := []model.Asset{
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xabc",
			Amount:        util.Ptr(decimal.NewFromFloat(100).String()),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Test Token",
			Symbol:        util.Ptr("TST"),
			LogoUrls:      `["http://logo.url"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xabc",
			Amount:        util.Ptr(decimal.NewFromFloat(200).String()),
			WalletAddress: domain.NewEvmAddress("0x456").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Test Token",
			Symbol:        util.Ptr("TST"),
			LogoUrls:      `["http://logo.url"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xdef",
			Amount:        util.Ptr(decimal.NewFromFloat(50).String()),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Another Token",
			Symbol:        util.Ptr("ANT"),
			LogoUrls:      `["http://another.logo"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},

		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xghi",
			Amount:        util.Ptr(decimal.NewFromFloat(75).String()),
			WalletAddress: domain.NewEvmAddress("0x456").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Third Token",
			Symbol:        util.Ptr("THRD"),
			LogoUrls:      `["http://third.logo"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
	}

	s.NoError(Get().Create(&preInsertAssets).Error)

	{
		var count int64
		s.NoError(Get().Model(&model.Asset{}).Count(&count).Error)
		s.Equal(int64(4), count)
	}

	// Update the token amounts
	amounts := map[domain.ChainAddress][]*domain.TokenAmount{
		{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x123")}: {
			{
				Token:  domain.NewToken(domain.Ethereum, "0xabc", "Updated Token", "UPD", "http://updated.logo", 18, true),
				Amount: decimal.NewFromFloat(150),
			},
			{
				Token:  domain.NewToken(domain.Ethereum, "0xdef", "Another Updated Token", "AUT", "http://another.updated.logo", 18, true),
				Amount: decimal.NewFromFloat(100),
			},
		},
	}

	s.NoError(s.repo.UpdateTokenAmounts(s.ctx, amounts))

	{
		var count int64
		s.NoError(Get().Model(&model.Asset{}).Count(&count).Error)
		s.Equal(int64(4), count)
	}

	// Verify the data was updated correctly
	assets, totalCount, err := s.repo.ListAssets(s.ctx, &domain.ListAssetsParam{
		Addresses: map[domain.Chain][]domain.Address{
			domain.Ethereum: {domain.NewEvmAddress("0x123"), domain.NewEvmAddress("0x456")},
		},
		Types:      []domain.AssetType{domain.AssetTypeToken},
		PageSize:   10,
		PageNumber: 1,
	})
	s.NoError(err)
	s.Len(assets, 3)
	s.Equal(3, totalCount)

	// Check updated assets
	for _, asset := range assets {
		tokenAsset := asset.(*domain.TokenAsset)
		switch tokenAsset.ID() {
		case "0xabc":
			s.Equal("Updated Token", tokenAsset.Name())
			s.Equal("UPD", tokenAsset.Symbol())
			s.Equal([]string{"http://updated.logo"}, tokenAsset.LogoUrls())
			s.Equal(decimal.NewFromFloat(200).String(), tokenAsset.WalletAmounts[0].Amount.String())
			s.Equal(domain.Ethereum, tokenAsset.Chain())
			s.Equal(domain.AssetTypeToken, tokenAsset.Type())
			s.InDelta(0, tokenAsset.UsdValue(), 0.001)
			s.True(tokenAsset.IsVerified())
			s.Equal(uint(18), tokenAsset.Decimals())
			s.Len(tokenAsset.WalletAmounts, 2)
			s.Equal(domain.NewEvmAddress("0x456"), tokenAsset.WalletAmounts[0].Address)
			s.Equal(decimal.NewFromFloat(200).String(), tokenAsset.WalletAmounts[0].Amount.String())
			s.Equal(domain.NewEvmAddress("0x123"), tokenAsset.WalletAmounts[1].Address)
			s.Equal(decimal.NewFromFloat(150).String(), tokenAsset.WalletAmounts[1].Amount.String())
		case "0xdef":
			s.Equal("Another Updated Token", tokenAsset.Name())
			s.Equal("AUT", tokenAsset.Symbol())
			s.Equal([]string{"http://another.updated.logo"}, tokenAsset.LogoUrls())
			s.Equal(decimal.NewFromFloat(100).String(), tokenAsset.WalletAmounts[0].Amount.String())
			s.Equal(domain.Ethereum, tokenAsset.Chain())
			s.Equal(domain.AssetTypeToken, tokenAsset.Type())
			s.InDelta(0, tokenAsset.UsdValue(), 0.001)
			s.True(tokenAsset.IsVerified())
			s.Equal(uint(18), tokenAsset.Decimals())
			s.Len(tokenAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x123"), tokenAsset.WalletAmounts[0].Address)
		case "0xghi":
			s.Equal("Third Token", tokenAsset.Name())
			s.Equal("THRD", tokenAsset.Symbol())
			s.Equal([]string{"http://third.logo"}, tokenAsset.LogoUrls())
			s.Equal(decimal.NewFromFloat(75).String(), tokenAsset.WalletAmounts[0].Amount.String())
			s.Equal(domain.Ethereum, tokenAsset.Chain())
			s.Equal(domain.AssetTypeToken, tokenAsset.Type())
			s.InDelta(0, tokenAsset.UsdValue(), 0.001)
			s.True(tokenAsset.IsVerified())
			s.Equal(uint(18), tokenAsset.Decimals())
			s.Len(tokenAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x456"), tokenAsset.WalletAmounts[0].Address)
		default:
			s.Fail("Unexpected asset ID")
		}
	}
}

func (s *AssetRepoTestSuite) TestSetNftAmounts() {
	// Pre-insert three NFT assets
	preInsertAssets := []model.Asset{
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "test-collection",
			Amount:        util.Ptr("1"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeNft),
			Name:          "Test NFT",
			LogoUrls:      `["http://nft.logo"]`,
			IsVerified:    util.Ptr(true),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "test-collection-2",
			Amount:        util.Ptr("2"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeNft),
			Name:          "Another NFT",
			LogoUrls:      `["http://another.nft.logo"]`,
			IsVerified:    util.Ptr(true),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "test-collection-3",
			Amount:        util.Ptr("1"),
			WalletAddress: domain.NewEvmAddress("0x456").String(),
			AssetType:     string(domain.AssetTypeNft),
			Name:          "Third NFT",
			LogoUrls:      `["http://third.nft.logo"]`,
			IsVerified:    util.Ptr(true),
		},
	}

	s.NoError(Get().Create(&preInsertAssets).Error)

	// Set the NFT amounts (this should delete existing assets and set new ones)
	amounts := map[domain.ChainAddress][]*domain.NftAmount{
		{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x123")}: {
			{
				Nft: domain.Nft{
					Chain:           domain.Ethereum,
					ContractAddress: domain.NewEvmAddress("0xabc"),
					TokenID:         "1",
					Name:            "Test NFT",
					ImageUrl:        "http://nft.logo",
					CollectionSlug:  util.Ptr("test-collection"),
				},
				Amount: 1,
			},
		},
	}

	s.NoError(s.repo.SetNftAmounts(s.ctx, amounts))

	// Verify the data was set correctly
	assets, totalCount, err := s.repo.ListAssets(s.ctx, &domain.ListAssetsParam{
		Addresses: map[domain.Chain][]domain.Address{
			domain.Ethereum: {domain.NewEvmAddress("0x123")},
		},
		Types:      []domain.AssetType{domain.AssetTypeNft},
		PageSize:   10,
		PageNumber: 1,
	})
	s.NoError(err)
	s.Len(assets, 1)
	s.Equal(1, totalCount)

	asset := assets[0].(*domain.NftAsset)
	s.Equal(domain.Ethereum, asset.Chain())
	s.Equal("test-collection", asset.ID())
	s.Equal(domain.AssetTypeNft, asset.Type())
	s.Equal("Test NFT", asset.Name())
	s.Equal([]string{"http://nft.logo"}, asset.LogoUrls())
	s.InDelta(0, asset.UsdValue(), 0.001) // UsdValue might be 0 if price is not set
	s.True(asset.IsVerified())
	s.Len(asset.WalletAmounts, 1)
	s.Equal(domain.NewEvmAddress("0x123"), asset.WalletAmounts[0].Address)
	s.Equal(uint(1), asset.WalletAmounts[0].Amount)
}

func (s *AssetRepoTestSuite) TestSetDefiAssets() {
	// Pre-insert three DeFi assets
	preInsertAssets := []model.Asset{
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "defi-1",
			Amount:        util.Ptr("100"),
			WalletAddress: "0x123",
			AssetType:     string(domain.AssetTypeDefi),
			Name:          "Test DeFi",
			LogoUrls:      `["http://defi.logo"]`,
			Metadata:      util.Ptr(`{"site_url": "http://defi.site"}`),
			IsVerified:    util.Ptr(true),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "defi-2",
			Amount:        util.Ptr("50"),
			WalletAddress: "0x123",
			AssetType:     string(domain.AssetTypeDefi),
			Name:          "Another DeFi",
			LogoUrls:      `["http://another.defi.logo"]`,
			Metadata:      util.Ptr(`{"site_url": "http://another.defi.site"}`),
			IsVerified:    util.Ptr(true),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "defi-3",
			Amount:        util.Ptr("75"),
			WalletAddress: "0x456",
			AssetType:     string(domain.AssetTypeDefi),
			Name:          "Third DeFi",
			LogoUrls:      `["http://third.defi.logo"]`,
			Metadata:      util.Ptr(`{"site_url": "http://third.defi.site"}`),
			IsVerified:    util.Ptr(true),
		},
	}

	s.NoError(Get().Create(&preInsertAssets).Error)

	// Set the DeFi assets (this should delete existing assets and set new ones)
	amounts := map[domain.ChainAddress][]*domain.DefiAsset{
		{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x123")}: {
			func() *domain.DefiAsset {
				asset := domain.NewDefiAsset(domain.Ethereum, "defi-1", "Test DeFi", "http://defi.site")
				asset.SupplyTokens = []*domain.DefiToken{
					{
						ID:      "0xabc",
						Name:    "Supply Token",
						Symbol:  "SUP",
						LogoUrl: util.Ptr("http://supply.logo"),
						Amount:  decimal.NewFromFloat(50),
						Price:   2,
					},
				}
				asset.RewardTokens = []*domain.DefiToken{
					{
						ID:      "0xdef",
						Name:    "Reward Token",
						Symbol:  "REW",
						LogoUrl: util.Ptr("http://reward.logo"),
						Amount:  decimal.NewFromFloat(10),
						Price:   1,
					},
				}
				return asset
			}(),
		},
	}

	s.NoError(s.repo.SetDefiAssets(s.ctx, amounts))

	// Verify the data was set correctly
	assets, totalCount, err := s.repo.ListAssets(s.ctx, &domain.ListAssetsParam{
		Addresses: map[domain.Chain][]domain.Address{
			domain.Ethereum: {domain.NewEvmAddress("0x123")},
		},
		Types:      []domain.AssetType{domain.AssetTypeDefi},
		PageSize:   10,
		PageNumber: 1,
	})
	s.NoError(err)
	s.Len(assets, 1)
	s.Equal(1, totalCount)

	asset := assets[0].(*domain.DefiAsset)
	s.Equal(domain.Ethereum, asset.Chain())
	s.Equal("defi-1", asset.ID())
	s.Equal(domain.AssetTypeDefi, asset.Type())
	s.Equal("Test DeFi", asset.Name())
	s.Equal("http://defi.site", asset.SiteUrl)
	s.InDelta(110, asset.UsdValue(), 0.001) // 50 * 2 + 10 * 1
	s.True(asset.IsVerified())
	s.Len(asset.SupplyTokens, 1)
	s.Equal("Supply Token", asset.SupplyTokens[0].Name)
	s.Equal(decimal.NewFromFloat(50).String(), asset.SupplyTokens[0].Amount.String())
	s.Len(asset.RewardTokens, 1)
	s.Equal("Reward Token", asset.RewardTokens[0].Name)
	s.Equal(decimal.NewFromFloat(10).String(), asset.RewardTokens[0].Amount.String())
}

func (s *AssetRepoTestSuite) TestUpdateDefiAssets() {
	// Pre-insert three DeFi assets
	preInsertAssets := []model.Asset{
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "defi-1",
			Amount:        util.Ptr("100"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeDefi),
			Name:          "Test DeFi",
			LogoUrls:      `["http://defi.logo"]`,
			Metadata:      util.Ptr(`{"detail":{"supply_token_list":[{"id":"0xabc","chain":"eth","name":"Supply Token","symbol":"SUP","display_symbol":"SUP","optimized_symbol":"SUP","decimals":18,"logo_url":"http://supply.logo","protocol_id":"","price":2,"is_verified":true,"is_core":false,"is_wallet":false,"time_at":0,"amount":50,"is_collateral":false}],"reward_token_list":[{"id":"0xdef","chain":"eth","name":"Reward Token","symbol":"REW","display_symbol":"REW","optimized_symbol":"REW","decimals":18,"logo_url":"http://reward.logo","protocol_id":"","price":1,"is_verified":true,"is_core":false,"is_wallet":false,"time_at":0,"amount":10,"is_collateral":false}],"borrow_token_list":[]},"site_url":"http://defi.site"}`),
			IsVerified:    util.Ptr(true),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "defi-2",
			Amount:        util.Ptr("50"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeDefi),
			Name:          "Another DeFi",
			LogoUrls:      `["http://another.defi.logo"]`,
			Metadata:      util.Ptr(`{"detail":{"supply_token_list":[{"id":"0xdef","chain":"eth","name":"Supply Token","symbol":"SUP","display_symbol":"SUP","optimized_symbol":"SUP","decimals":18,"logo_url":"http://supply.logo","protocol_id":"","price":1,"is_verified":true,"is_core":false,"is_wallet":false,"time_at":0,"amount":50,"is_collateral":false}],"reward_token_list":[],"borrow_token_list":[]},"site_url":"http://another.defi.site"}`),
			IsVerified:    util.Ptr(true),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "defi-3",
			Amount:        util.Ptr("75"),
			WalletAddress: domain.NewEvmAddress("0x456").String(),
			AssetType:     string(domain.AssetTypeDefi),
			Name:          "Third DeFi",
			LogoUrls:      `["http://third.defi.logo"]`,
			Metadata:      util.Ptr(`{"detail":{"supply_token_list":[{"id":"0xghi","chain":"eth","name":"Supply Token","symbol":"SUP","display_symbol":"SUP","optimized_symbol":"SUP","decimals":18,"logo_url":"http://supply.logo","protocol_id":"","price":1,"is_verified":true,"is_core":false,"is_wallet":false,"time_at":0,"amount":75,"is_collateral":false}],"reward_token_list":[],"borrow_token_list":[]},"site_url":"http://third.defi.site"}`),
			IsVerified:    util.Ptr(true),
		},
	}

	s.NoError(Get().Create(&preInsertAssets).Error)

	{
		var count int64
		s.NoError(Get().Model(&model.Asset{}).Count(&count).Error)
		s.Equal(int64(3), count)
	}

	// Update the DeFi assets
	amounts := map[domain.ChainAddress][]*domain.DefiAsset{
		{Chain: domain.Ethereum, Address: domain.NewEvmAddress("0x123")}: {
			func() *domain.DefiAsset {
				asset := domain.NewDefiAsset(domain.Ethereum, "defi-1", "Updated DeFi", "http://updated.defi.site")
				asset.SupplyTokens = []*domain.DefiToken{
					{
						ID:      "0xabc",
						Name:    "Updated Supply Token",
						Symbol:  "USUP",
						LogoUrl: util.Ptr("http://updated.supply.logo"),
						Amount:  decimal.NewFromFloat(75),
						Price:   3,
					},
				}
				asset.RewardTokens = []*domain.DefiToken{
					{
						ID:      "0xdef",
						Name:    "Updated Reward Token",
						Symbol:  "UREW",
						LogoUrl: util.Ptr("http://updated.reward.logo"),
						Amount:  decimal.NewFromFloat(15),
						Price:   2,
					},
				}
				return asset
			}(),
			func() *domain.DefiAsset {
				asset := domain.NewDefiAsset(domain.Ethereum, "defi-2", "Another Updated DeFi", "http://another.updated.defi.site")
				asset.SupplyTokens = []*domain.DefiToken{
					{
						ID:      "0xghi",
						Name:    "Another Updated Supply Token",
						Symbol:  "AUSUP",
						LogoUrl: util.Ptr("http://another.updated.supply.logo"),
						Amount:  decimal.NewFromFloat(100),
						Price:   1,
					},
				}
				return asset
			}(),
		},
	}

	s.NoError(s.repo.UpdateDefiAssets(s.ctx, amounts))

	{
		var count int64
		s.NoError(Get().Model(&model.Asset{}).Count(&count).Error)
		s.Equal(int64(3), count)
	}

	// Verify the data was updated correctly
	assets, totalCount, err := s.repo.ListAssets(s.ctx, &domain.ListAssetsParam{
		Addresses: map[domain.Chain][]domain.Address{
			domain.Ethereum: {domain.NewEvmAddress("0x123"), domain.NewEvmAddress("0x456")},
		},
		Types:      []domain.AssetType{domain.AssetTypeDefi},
		PageSize:   10,
		PageNumber: 1,
	})
	s.NoError(err)
	s.Len(assets, 3)
	s.Equal(3, totalCount)

	// Check updated assets
	for _, asset := range assets {
		defiAsset := asset.(*domain.DefiAsset)
		switch defiAsset.ID() {
		case "defi-1":
			s.Equal("Updated DeFi", defiAsset.Name())
			s.Equal("http://updated.defi.site", defiAsset.SiteUrl)
			s.InDelta(255, defiAsset.UsdValue(), 0.001) // 75 * 3 + 15 * 2
			s.True(defiAsset.IsVerified())
			s.Len(defiAsset.SupplyTokens, 1)
			s.Equal("Updated Supply Token", defiAsset.SupplyTokens[0].Name)
			s.Equal(decimal.NewFromFloat(75).String(), defiAsset.SupplyTokens[0].Amount.String())
			s.Len(defiAsset.RewardTokens, 1)
			s.Equal("Updated Reward Token", defiAsset.RewardTokens[0].Name)
			s.Equal(decimal.NewFromFloat(15).String(), defiAsset.RewardTokens[0].Amount.String())
		case "defi-2":
			s.Equal("Another Updated DeFi", defiAsset.Name())
			s.Equal("http://another.updated.defi.site", defiAsset.SiteUrl)
			s.InDelta(100, defiAsset.UsdValue(), 0.001) // 100 * 1
			s.True(defiAsset.IsVerified())
			s.Len(defiAsset.SupplyTokens, 1)
			s.Equal("Another Updated Supply Token", defiAsset.SupplyTokens[0].Name)
			s.Equal(decimal.NewFromFloat(100).String(), defiAsset.SupplyTokens[0].Amount.String())
			s.Len(defiAsset.RewardTokens, 0)
		case "defi-3":
			s.Equal("Third DeFi", defiAsset.Name())
			s.Equal("http://third.defi.site", defiAsset.SiteUrl)
			s.InDelta(75, defiAsset.UsdValue(), 0.001)
			s.True(defiAsset.IsVerified())
		default:
			s.Fail("Unexpected asset ID")
		}
	}
}

func (s *AssetRepoTestSuite) TestListMainTokens() {
	// Pre-insert assets
	preInsertAssets := []model.Asset{
		// Address A, Chain 1 (Ethereum)
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    domain.Ethereum.MainToken().ID(),
			Amount:        util.Ptr("100"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Ethereum",
			Symbol:        util.Ptr("ETH"),
			IsVerified:    util.Ptr(true),
			LogoUrls:      `["https://example.com/ethereum_logo.png"]`,
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xToken1",
			Amount:        util.Ptr("50"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token1",
			Symbol:        util.Ptr("TKN1"),
			IsVerified:    util.Ptr(true),
			LogoUrls:      `["https://example.com/token1_logo.png"]`,
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xToken2",
			Amount:        util.Ptr("75"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token2",
			Symbol:        util.Ptr("TKN2"),
			IsVerified:    util.Ptr(true),
			LogoUrls:      `["https://example.com/token2_logo.png"]`,
		},

		// Address A, Chain 2 (BNB Chain)
		{
			ChainID:       domain.BNBChain.ID(),
			AssetGroup:    domain.BNBChain.MainToken().ID(),
			Amount:        util.Ptr("200"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "BNB",
			Symbol:        util.Ptr("BNB"),
			IsVerified:    util.Ptr(true),
			LogoUrls:      `["https://example.com/bnb_logo.png"]`,
		},
		{
			ChainID:       domain.BNBChain.ID(),
			AssetGroup:    "0xToken3",
			Amount:        util.Ptr("150"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token3",
			Symbol:        util.Ptr("TKN3"),
			IsVerified:    util.Ptr(true),
			LogoUrls:      `["https://example.com/token3_logo.png"]`,
		},
		{
			ChainID:       domain.BNBChain.ID(),
			AssetGroup:    "0xToken4",
			Amount:        util.Ptr("175"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token4",
			Symbol:        util.Ptr("TKN4"),
			IsVerified:    util.Ptr(true),
			LogoUrls:      `["https://example.com/token4_logo.png"]`,
		},

		// Address A, Chain 3 (Polygon) - only non-main tokens
		{
			ChainID:       domain.Polygon.ID(),
			AssetGroup:    "0xToken5",
			Amount:        util.Ptr("300"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token5",
			Symbol:        util.Ptr("TKN5"),
			IsVerified:    util.Ptr(true),
			LogoUrls:      `["https://example.com/token5_logo.png"]`,
		},

		// Address B, Chain 1 (Ethereum) - only main token
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    domain.Ethereum.MainToken().ID(),
			Amount:        util.Ptr("500"),
			WalletAddress: domain.NewEvmAddress("0x456").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Ethereum",
			Symbol:        util.Ptr("ETH"),
			IsVerified:    util.Ptr(true),
			LogoUrls:      `["https://example.com/ethereum_logo.png"]`,
		},
	}

	s.NoError(Get().Create(&preInsertAssets).Error)

	// Test ListMainTokens
	addresses := map[domain.Chain][]domain.Address{
		domain.Ethereum: {domain.NewEvmAddress("0x123"), domain.NewEvmAddress("0x456")},
		domain.BNBChain: {domain.NewEvmAddress("0x123"), domain.NewEvmAddress("0x456")},
		domain.Polygon:  {domain.NewEvmAddress("0x123"), domain.NewEvmAddress("0x456")},
	}

	mainTokens, err := s.repo.ListMainTokens(s.ctx, addresses)
	s.NoError(err)

	// Verify the results
	s.Len(mainTokens, 2) // Only Ethereum and BNB Chain should have main tokens

	for _, token := range mainTokens {
		switch token.Chain() {
		case domain.Ethereum:
			s.Equal(domain.Ethereum.MainToken().ID(), token.ID())
			s.Equal("Ethereum", token.Name())
			s.Equal("ETH", token.(*domain.TokenAsset).Symbol())
		case domain.BNBChain:
			s.Equal(domain.BNBChain.MainToken().ID(), token.ID())
			s.Equal("BNB", token.Name())
			s.Equal("BNB", token.(*domain.TokenAsset).Symbol())
		default:
			s.Fail("Unexpected chain in main tokens")
		}
	}

	// Verify Polygon's main token is not included
	for _, token := range mainTokens {
		s.NotEqual(domain.Polygon.ID(), token.Chain().ID())
	}
}

func (s *AssetRepoTestSuite) TestListAssets() {
	// Prepare test data
	assets := []model.Asset{
		// Address 1, Ethereum
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xtoken1",
			Amount:        util.Ptr("100"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token 1",
			Symbol:        util.Ptr("TKN1"),
			LogoUrls:      `["https://example.com/token1.png"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xnft1",
			Amount:        util.Ptr("1"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeNft),
			Name:          "NFT 1",
			LogoUrls:      `["https://example.com/nft1.png"]`,
			IsVerified:    util.Ptr(true),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "",
			Amount:        util.Ptr("1"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeNft),
			Name:          "NFT Empty",
			LogoUrls:      `["https://example.com/nft1.png"]`,
			IsVerified:    util.Ptr(true),
		},
		// Address 1, BNB Chain
		{
			ChainID:       domain.BNBChain.ID(),
			AssetGroup:    "0xtoken2",
			Amount:        util.Ptr("200"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token 2",
			Symbol:        util.Ptr("TKN2"),
			LogoUrls:      `["https://example.com/token2.png"]`,
			IsVerified:    util.Ptr(false),
			Decimals:      util.Ptr(int32(8)),
		},
		{
			ChainID:       domain.BNBChain.ID(),
			AssetGroup:    "defi1",
			Amount:        util.Ptr("300"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeDefi),
			Name:          "DeFi 1",
			LogoUrls:      `["https://example.com/defi1.png"]`,
			IsVerified:    util.Ptr(true),
			Metadata:      util.Ptr(`{"detail":{"supply_token_list":[{"id":"0xabc","chain":"bsc","name":"Supply Token","symbol":"SUP","display_symbol":"SUP","optimized_symbol":"SUP","decimals":18,"logo_url":"http://supply.logo","protocol_id":"","price":1,"is_verified":true,"is_core":false,"is_wallet":false,"time_at":0,"amount":300,"is_collateral":false}],"reward_token_list":[],"borrow_token_list":[]},"site_url":"https://defi1.com"}`),
		},
		// Address 2, Solana
		{
			ChainID:       domain.Solana.ID(),
			AssetGroup:    "soltoken1",
			Amount:        util.Ptr("400"),
			WalletAddress: "solana_address_456",
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Solana Token 1",
			Symbol:        util.Ptr("STKN1"),
			LogoUrls:      `["https://example.com/soltoken1.png"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(9)),
			TokenAccount:  util.Ptr("solana_token_account_789"),
		},
		// Address 3, Tron
		{
			ChainID:       domain.Tron.ID(),
			AssetGroup:    "trontoken1",
			Amount:        util.Ptr("500"),
			WalletAddress: domain.NewTronAddress("0xTron").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Tron Token 1",
			Symbol:        util.Ptr("TRKN1"),
			LogoUrls:      `["https://example.com/trontoken1.png"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(6)),
			TokenType:     util.Ptr("trc20"),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xtoken1",
			Amount:        util.Ptr("150"),
			WalletAddress: domain.NewEvmAddress("0x456").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token 1",
			Symbol:        util.Ptr("TKN1"),
			LogoUrls:      `["https://example.com/token1.png"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
	}

	s.NoError(Get().Create(&assets).Error)

	assetPrices := []model.AssetPrice{
		{
			ChainID:    domain.Ethereum.ID(),
			AssetGroup: "0xtoken1",
			Price:      "10",
		},
		{
			ChainID:    domain.Ethereum.ID(),
			AssetGroup: "0xnft1",
			Price:      "100",
		},
		{
			ChainID:    domain.BNBChain.ID(),
			AssetGroup: "0xtoken2",
			Price:      "5",
		},
		{
			ChainID:    domain.Solana.ID(),
			AssetGroup: "soltoken1",
			Price:      "2",
		},
		{
			ChainID:    domain.Tron.ID(),
			AssetGroup: "trontoken1",
			Price:      "1",
		},
	}

	s.NoError(Get().Create(&assetPrices).Error)

	s.Run("Get All assets", func() {
		params := &domain.ListAssetsParam{
			WithAllAddresses: true,
			Types:            []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			PageSize:         10,
			PageNumber:       1,
		}
		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		for _, asset := range assets {
			s.T().Logf("Asset ID: %s, Chain: %s, Type: %s, IsVerified: %t", asset.ID(), asset.Chain().ID(), asset.Type(), asset.IsVerified())
		}
		s.Equal(5, total)
		s.Len(assets, 5)
	})

	// Test cases
	s.Run("Filter by single address and single chain", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.Ethereum: {domain.NewEvmAddress("0x123")},
			},
			Types:      []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(2, total)
		s.Len(assets, 2)

		{
			s.Equal(domain.Ethereum, assets[0].Chain())
			s.Equal(domain.AssetTypeToken, assets[0].Type())
			s.True(assets[0].IsVerified())

			tokenAsset := assets[0].(*domain.TokenAsset)
			s.Equal("0xtoken1", tokenAsset.ID())
			s.Equal("Token 1", tokenAsset.Name())
			s.Equal("TKN1", tokenAsset.Symbol())
			s.Equal([]string{"https://example.com/token1.png"}, tokenAsset.LogoUrls())
			s.Equal(uint(18), tokenAsset.Decimals())
			s.Len(tokenAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x123"), tokenAsset.WalletAmounts[0].Address)
			s.Equal("100", tokenAsset.WalletAmounts[0].Amount.String())
		}

		{
			s.Equal(domain.Ethereum, assets[1].Chain())
			s.Equal(domain.AssetTypeNft, assets[1].Type())
			s.True(assets[1].IsVerified())

			nftAsset := assets[1].(*domain.NftAsset)
			s.Equal("0xnft1", nftAsset.ID())
			s.Equal("NFT 1", nftAsset.Name())
			s.Equal([]string{"https://example.com/nft1.png"}, nftAsset.LogoUrls())
			s.Len(nftAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x123"), nftAsset.WalletAmounts[0].Address)
			s.Equal(uint(1), nftAsset.WalletAmounts[0].Amount)
		}
	})

	s.Run("Filter by multiple addresses and multiple chains", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.Ethereum: {domain.NewEvmAddress("0x123"), domain.NewEvmAddress("0x456")},
				domain.BNBChain: {domain.NewEvmAddress("0x123")},
			},
			Types:      []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(3, total)
		s.Len(assets, 3)

		{
			s.Equal(domain.Ethereum, assets[0].Chain())
			s.Equal(domain.AssetTypeToken, assets[0].Type())
			s.True(assets[0].IsVerified())

			tokenAsset := assets[0].(*domain.TokenAsset)
			s.Equal("0xtoken1", tokenAsset.ID())
			s.Equal("Token 1", tokenAsset.Name())
			s.Equal("TKN1", tokenAsset.Symbol())
			s.Equal([]string{"https://example.com/token1.png"}, tokenAsset.LogoUrls())
			s.Equal(uint(18), tokenAsset.Decimals())
			s.Len(tokenAsset.WalletAmounts, 2)
			s.Equal(domain.NewEvmAddress("0x456"), tokenAsset.WalletAmounts[0].Address)
			s.Equal("150", tokenAsset.WalletAmounts[0].Amount.String())
			s.Equal(domain.NewEvmAddress("0x123"), tokenAsset.WalletAmounts[1].Address)
			s.Equal("100", tokenAsset.WalletAmounts[1].Amount.String())
		}
		{
			s.Equal(domain.BNBChain, assets[1].Chain())
			s.Equal(domain.AssetTypeDefi, assets[1].Type())
			s.True(assets[1].IsVerified())

			defiAsset := assets[1].(*domain.DefiAsset)
			s.Equal("defi1", defiAsset.ID())
			s.Equal("DeFi 1", defiAsset.Name())
			s.Equal([]string{"http://supply.logo"}, defiAsset.LogoUrls())
			s.Equal("https://defi1.com", defiAsset.SiteUrl)
			s.Len(defiAsset.SupplyTokens, 1)
			s.Equal("0xabc", defiAsset.SupplyTokens[0].ID)
			s.Equal("Supply Token", defiAsset.SupplyTokens[0].Name)
			s.Equal("SUP", defiAsset.SupplyTokens[0].Symbol)
			s.Equal("http://supply.logo", *defiAsset.SupplyTokens[0].LogoUrl)
			s.Equal(decimal.NewFromFloat(300).String(), defiAsset.SupplyTokens[0].Amount.String())
			s.Equal(1.0, defiAsset.SupplyTokens[0].Price)
			s.Len(defiAsset.RewardTokens, 0)
			s.Len(defiAsset.BorrowTokens, 0)
			s.Len(defiAsset.WalletValues, 1)
			s.Equal(domain.NewEvmAddress("0x123"), defiAsset.WalletValues[0].Address)
			s.Equal(decimal.NewFromFloat(300).String(), defiAsset.WalletValues[0].UsdValue.String())
		}
		{
			s.Equal(domain.Ethereum, assets[2].Chain())
			s.Equal(domain.AssetTypeNft, assets[2].Type())
			s.True(assets[2].IsVerified())

			nftAsset := assets[2].(*domain.NftAsset)
			s.Equal("0xnft1", nftAsset.ID())
			s.Equal("NFT 1", nftAsset.Name())
			s.Equal([]string{"https://example.com/nft1.png"}, nftAsset.LogoUrls())
			s.Len(nftAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x123"), nftAsset.WalletAmounts[0].Address)
			s.Equal(uint(1), nftAsset.WalletAmounts[0].Amount)
		}
	})

	s.Run("Filter by types", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.Ethereum: {domain.NewEvmAddress("0x123")},
				domain.BNBChain: {domain.NewEvmAddress("0x123")},
			},
			Types:      []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft},
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(2, total)
		s.Len(assets, 2)

		{
			s.Equal(domain.Ethereum, assets[0].Chain())
			s.Equal(domain.AssetTypeToken, assets[0].Type())
			s.True(assets[0].IsVerified())

			tokenAsset := assets[0].(*domain.TokenAsset)
			s.Equal("0xtoken1", tokenAsset.ID())
			s.Equal("Token 1", tokenAsset.Name())
			s.Equal("TKN1", tokenAsset.Symbol())
			s.Equal([]string{"https://example.com/token1.png"}, tokenAsset.LogoUrls())
			s.Equal(uint(18), tokenAsset.Decimals())
			s.Len(tokenAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x123"), tokenAsset.WalletAmounts[0].Address)
			s.Equal("100", tokenAsset.WalletAmounts[0].Amount.String())
		}

		{
			s.Equal(domain.Ethereum, assets[1].Chain())
			s.Equal(domain.AssetTypeNft, assets[1].Type())
			s.True(assets[1].IsVerified())

			nftAsset := assets[1].(*domain.NftAsset)
			s.Equal("0xnft1", nftAsset.ID())
			s.Equal("NFT 1", nftAsset.Name())
			s.Equal([]string{"https://example.com/nft1.png"}, nftAsset.LogoUrls())
			s.Len(nftAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x123"), nftAsset.WalletAmounts[0].Address)
			s.Equal(uint(1), nftAsset.WalletAmounts[0].Amount)
		}
	})

	s.Run("Include unverified assets", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.BNBChain: {domain.NewEvmAddress("0x123")},
			},
			Types:             []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			IncludeUnverified: true,
			PageSize:          10,
			PageNumber:        1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(2, total)
		s.Len(assets, 2)

		{
			s.Equal(domain.BNBChain, assets[0].Chain())
			s.Equal(domain.AssetTypeDefi, assets[0].Type())
			s.True(assets[0].IsVerified())

			defiAsset := assets[0].(*domain.DefiAsset)
			s.Equal("defi1", defiAsset.ID())
			s.Equal("DeFi 1", defiAsset.Name())
			s.Equal([]string{"http://supply.logo"}, defiAsset.LogoUrls())
			s.Equal("https://defi1.com", defiAsset.SiteUrl)
			s.Len(defiAsset.SupplyTokens, 1)
			s.Equal("0xabc", defiAsset.SupplyTokens[0].ID)
			s.Equal("Supply Token", defiAsset.SupplyTokens[0].Name)
			s.Equal("SUP", defiAsset.SupplyTokens[0].Symbol)
			s.Equal("http://supply.logo", *defiAsset.SupplyTokens[0].LogoUrl)
			s.Equal(decimal.NewFromFloat(300).String(), defiAsset.SupplyTokens[0].Amount.String())
			s.Equal(1.0, defiAsset.SupplyTokens[0].Price)
			s.Len(defiAsset.RewardTokens, 0)
			s.Len(defiAsset.BorrowTokens, 0)
			s.Len(defiAsset.WalletValues, 1)
			s.Equal(domain.NewEvmAddress("0x123"), defiAsset.WalletValues[0].Address)
			s.Equal(decimal.NewFromFloat(300).String(), defiAsset.WalletValues[0].UsdValue.String())
		}
		{
			s.Equal(domain.BNBChain, assets[1].Chain())
			s.Equal(domain.AssetTypeToken, assets[1].Type())
			s.False(assets[1].IsVerified())

			tokenAsset := assets[1].(*domain.TokenAsset)
			s.Equal("0xtoken2", tokenAsset.ID())
			s.Equal("Token 2", tokenAsset.Name())
			s.Equal("TKN2", tokenAsset.Symbol())
			s.Equal([]string{"https://example.com/token2.png"}, tokenAsset.LogoUrls())
			s.Equal(uint(8), tokenAsset.Decimals())
			s.Len(tokenAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x123"), tokenAsset.WalletAmounts[0].Address)
			s.Equal("200", tokenAsset.WalletAmounts[0].Amount.String())
		}
	})

	s.Run("Query by name without IncludeUnverified", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.BNBChain: {domain.NewEvmAddress("0x123")},
			},
			Types:      []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			Query:      "Token 2",
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(0, total)
		s.Len(assets, 0)
	})

	s.Run("Query by name with IncludeUnverified", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.BNBChain: {domain.NewEvmAddress("0x123")},
			},
			Types:             []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			Query:             "Token 2",
			IncludeUnverified: true,
			PageSize:          10,
			PageNumber:        1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(1, total)
		s.Len(assets, 1)

		{
			s.Equal(domain.BNBChain, assets[0].Chain())
			s.Equal(domain.AssetTypeToken, assets[0].Type())
			s.False(assets[0].IsVerified())

			tokenAsset := assets[0].(*domain.TokenAsset)
			s.Equal("0xtoken2", tokenAsset.ID())
			s.Equal("Token 2", tokenAsset.Name())
			s.Equal("TKN2", tokenAsset.Symbol())
			s.Equal([]string{"https://example.com/token2.png"}, tokenAsset.LogoUrls())
			s.Equal(uint(8), tokenAsset.Decimals())
			s.Len(tokenAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x123"), tokenAsset.WalletAmounts[0].Address)
			s.Equal("200", tokenAsset.WalletAmounts[0].Amount.String())
		}
	})

	s.Run("Include specific tokens", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.Ethereum: {domain.NewEvmAddress("0x123"), domain.NewEvmAddress("0x456")},
				domain.BNBChain: {domain.NewEvmAddress("0x123")},
			},
			Types: []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			IncludeTokens: map[domain.Chain][]string{
				domain.Ethereum: {"0xtoken1"},
			},
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(1, total)
		s.Len(assets, 1)

		{
			s.Equal(domain.Ethereum, assets[0].Chain())
			s.Equal(domain.AssetTypeToken, assets[0].Type())
			s.True(assets[0].IsVerified())

			tokenAsset := assets[0].(*domain.TokenAsset)
			s.Equal("0xtoken1", tokenAsset.ID())
			s.Equal("Token 1", tokenAsset.Name())
			s.Equal("TKN1", tokenAsset.Symbol())
			s.Equal([]string{"https://example.com/token1.png"}, tokenAsset.LogoUrls())
			s.Equal(uint(18), tokenAsset.Decimals())
			s.Len(tokenAsset.WalletAmounts, 2)
			s.Equal(domain.NewEvmAddress("0x456"), tokenAsset.WalletAmounts[0].Address)
			s.Equal("150", tokenAsset.WalletAmounts[0].Amount.String())
			s.Equal(domain.NewEvmAddress("0x123"), tokenAsset.WalletAmounts[1].Address)
			s.Equal("100", tokenAsset.WalletAmounts[1].Amount.String())
		}
	})

	s.Run("Exclude specific tokens", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.Ethereum: {domain.NewEvmAddress("0x123"), domain.NewEvmAddress("0x456")},
				domain.BNBChain: {domain.NewEvmAddress("0x123")},
			},
			Types: []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			ExcludeTokens: map[domain.Chain][]string{
				domain.Ethereum: {"0xtoken1"},
			},
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(2, total)
		s.Len(assets, 2)

		{
			s.Equal(domain.BNBChain, assets[0].Chain())
			s.Equal(domain.AssetTypeDefi, assets[0].Type())
			s.True(assets[0].IsVerified())

			defiAsset := assets[0].(*domain.DefiAsset)
			s.Equal("defi1", defiAsset.ID())
			s.Equal("DeFi 1", defiAsset.Name())
			s.Equal([]string{"http://supply.logo"}, defiAsset.LogoUrls())
			s.Equal("https://defi1.com", defiAsset.SiteUrl)
			s.Len(defiAsset.SupplyTokens, 1)
			s.Equal("0xabc", defiAsset.SupplyTokens[0].ID)
			s.Equal("Supply Token", defiAsset.SupplyTokens[0].Name)
			s.Equal("SUP", defiAsset.SupplyTokens[0].Symbol)
			s.Equal("http://supply.logo", *defiAsset.SupplyTokens[0].LogoUrl)
			s.Equal(decimal.NewFromFloat(300).String(), defiAsset.SupplyTokens[0].Amount.String())
			s.Equal(1.0, defiAsset.SupplyTokens[0].Price)
			s.Len(defiAsset.RewardTokens, 0)
			s.Len(defiAsset.BorrowTokens, 0)
			s.Len(defiAsset.WalletValues, 1)
			s.Equal(domain.NewEvmAddress("0x123"), defiAsset.WalletValues[0].Address)
			s.Equal(decimal.NewFromFloat(300).String(), defiAsset.WalletValues[0].UsdValue.String())
		}
		{
			s.Equal(domain.Ethereum, assets[1].Chain())
			s.Equal(domain.AssetTypeNft, assets[1].Type())
			s.True(assets[1].IsVerified())

			nftAsset := assets[1].(*domain.NftAsset)
			s.Equal("0xnft1", nftAsset.ID())
			s.Equal("NFT 1", nftAsset.Name())
			s.Equal([]string{"https://example.com/nft1.png"}, nftAsset.LogoUrls())
			s.Len(nftAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x123"), nftAsset.WalletAmounts[0].Address)
			s.Equal(uint(1), nftAsset.WalletAmounts[0].Amount)
		}
	})

	s.Run("Combination of filters", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.Ethereum: {domain.NewEvmAddress("0x123"), domain.NewEvmAddress("0x456")},
				domain.BNBChain: {domain.NewEvmAddress("0x123")},
			},
			Types:             []domain.AssetType{domain.AssetTypeToken},
			IncludeUnverified: true,
			Query:             "Token",
			ExcludeTokens: map[domain.Chain][]string{
				domain.Ethereum: {"0xtoken1"},
			},
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(1, total)
		s.Len(assets, 1)

		{
			s.Equal(domain.BNBChain, assets[0].Chain())
			s.Equal(domain.AssetTypeToken, assets[0].Type())
			s.False(assets[0].IsVerified())

			tokenAsset := assets[0].(*domain.TokenAsset)
			s.Equal("0xtoken2", tokenAsset.ID())
			s.Equal("Token 2", tokenAsset.Name())
			s.Equal("TKN2", tokenAsset.Symbol())
			s.Equal([]string{"https://example.com/token2.png"}, tokenAsset.LogoUrls())
			s.Equal(uint(8), tokenAsset.Decimals())
			s.Len(tokenAsset.WalletAmounts, 1)
			s.Equal(domain.NewEvmAddress("0x123"), tokenAsset.WalletAmounts[0].Address)
			s.Equal("200", tokenAsset.WalletAmounts[0].Amount.String())
		}
	})

	s.Run("Pagination", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.Ethereum: {domain.NewEvmAddress("0x123")},
				domain.BNBChain: {domain.NewEvmAddress("0x123")},
				domain.Solana:   {domain.NewStrAddress("solana_address_456")},
			},
			Types:      []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			PageSize:   2,
			PageNumber: 2,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(4, total)
		s.Len(assets, 2)
	})

	s.Run("Empty addresses map", func() {
		params := &domain.ListAssetsParam{
			Addresses:  map[domain.Chain][]domain.Address{},
			Types:      []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(0, total)
		s.Len(assets, 0)
	})

	s.Run("No matching assets", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.Ethereum: {domain.NewEvmAddress("0x789")},
			},
			Types:      []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(0, total)
		s.Len(assets, 0)
	})

	s.Run("Solana token with metadata", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.Solana: {domain.NewStrAddress("solana_address_456")},
			},
			Types:      []domain.AssetType{domain.AssetTypeToken},
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(1, total)
		s.Len(assets, 1)

		solanaAsset, ok := assets[0].(*domain.SolanaTokenAsset)
		s.True(ok)
		s.Equal(domain.Solana, solanaAsset.Chain())
		s.Equal("soltoken1", solanaAsset.ID())
		s.Equal("Solana Token 1", solanaAsset.Name())
		s.Equal("STKN1", solanaAsset.Symbol())
		s.Equal([]string{"https://example.com/soltoken1.png"}, solanaAsset.LogoUrls())
		s.Equal(uint(9), solanaAsset.Decimals())
		s.True(solanaAsset.IsVerified())
		s.Len(solanaAsset.Accounts, 1)
		s.Equal("solana_address_456", solanaAsset.Accounts[0].Address)
		s.Equal("solana_token_account_789", solanaAsset.Accounts[0].Account)
	})
	s.Run("Tron token with token type", func() {
		params := &domain.ListAssetsParam{
			Addresses: map[domain.Chain][]domain.Address{
				domain.Tron: {domain.NewTronAddress("0xTron")},
			},
			Types:      []domain.AssetType{domain.AssetTypeToken},
			PageSize:   10,
			PageNumber: 1,
		}

		assets, total, err := s.repo.ListAssets(s.ctx, params)
		s.NoError(err)
		s.Equal(1, total)
		s.Len(assets, 1)

		tronAsset, ok := assets[0].(*domain.TokenAsset)
		s.True(ok)
		s.Equal(domain.Tron, tronAsset.Chain())
		s.Equal("trontoken1", tronAsset.ID())
		s.Equal("Tron Token 1", tronAsset.Name())
		s.Equal("TRKN1", tronAsset.Symbol())
		s.Equal([]string{"https://example.com/trontoken1.png"}, tronAsset.LogoUrls())
		s.Equal(uint(6), tronAsset.Decimals())
		s.True(tronAsset.IsVerified())
		s.Len(tronAsset.WalletAmounts, 1)
		s.Equal(domain.NewTronAddress("0xTron"), tronAsset.WalletAmounts[0].Address)
		s.Equal("500", tronAsset.WalletAmounts[0].Amount.String())
		tronToken, ok := tronAsset.Token.(domain.TronToken)
		s.True(ok)
		s.Equal("trc20", tronToken.TrcType().String())
	})

	// Test case for verified and unverified assets
	s.Run("Price handling", func() {
		s.Run("Should use verified asset price", func() {
			verifiedAsset := model.Asset{
				ChainID:       domain.Ethereum.ID(),
				AssetGroup:    "test-token-price",
				Amount:        util.Ptr(decimal.NewFromFloat(100).String()),
				WalletAddress: domain.NewEvmAddress("0x123123").String(),
				AssetType:     string(domain.AssetTypeToken),
				Name:          "Test Token",
				Symbol:        util.Ptr("TEST"),
				IsVerified:    util.Ptr(true),
				LogoUrls:      `["https://test.com/logo.png"]`,
				Decimals:      util.Ptr(int32(18)),
			}

			// Create asset price
			assetPrice := model.AssetPrice{
				ChainID:    domain.Ethereum.ID(),
				AssetGroup: "test-token-price",
				Price:      "1.5",
			}

			s.NoError(Get().Create(&verifiedAsset).Error)
			s.NoError(Get().Create(&assetPrice).Error)

			// Test listing assets
			assets, totalCount, err := s.repo.ListAssets(s.ctx, &domain.ListAssetsParam{
				Addresses: map[domain.Chain][]domain.Address{
					domain.Ethereum: {domain.NewEvmAddress("0x123123")},
				},
				Types:      []domain.AssetType{domain.AssetTypeToken},
				PageSize:   10,
				PageNumber: 1,
			})
			s.NoError(err)
			s.Len(assets, 1)
			s.Equal(1, totalCount)

			// Verify that the verified asset is used
			tokenAsset := assets[0].(*domain.TokenAsset)
			s.Equal("test-token-price", tokenAsset.ID())
			s.Equal(decimal.NewFromFloat(100).String(), tokenAsset.WalletAmounts[0].Amount.String())
			s.Equal(1.5, tokenAsset.Price)
			s.True(tokenAsset.IsVerified())
		})

		// Test case for unverified assets only
		s.Run("Should use 1e-18 price when only unverified assets exist", func() {
			// Create an unverified asset
			unverifiedAsset := model.Asset{
				ChainID:       domain.Ethereum.ID(),
				AssetGroup:    "unverified-token-price",
				Amount:        util.Ptr(decimal.NewFromFloat(300).String()),
				WalletAddress: domain.NewEvmAddress("0x123456").String(),
				AssetType:     string(domain.AssetTypeToken),
				Name:          "Unverified Token",
				Symbol:        util.Ptr("UNT"),
				IsVerified:    util.Ptr(false),
				LogoUrls:      `["https://test.com/unverified.png"]`,
				Decimals:      util.Ptr(int32(18)),
			}

			// Create asset price
			assetPrice := model.AssetPrice{
				ChainID:    domain.Ethereum.ID(),
				AssetGroup: "unverified-token-price",
				Price:      "2.0",
			}

			s.NoError(Get().Create(&unverifiedAsset).Error)
			s.NoError(Get().Create(&assetPrice).Error)

			// Test listing assets
			assets, totalCount, err := s.repo.ListAssets(s.ctx, &domain.ListAssetsParam{
				Addresses: map[domain.Chain][]domain.Address{
					domain.Ethereum: {domain.NewEvmAddress("0x123456")},
				},
				Types:             []domain.AssetType{domain.AssetTypeToken},
				PageSize:          10,
				PageNumber:        1,
				IncludeUnverified: true,
			})
			s.NoError(err)
			s.Len(assets, 1)
			s.Equal(1, totalCount)

			// Verify that the unverified asset uses 1e-18 price
			tokenAsset := assets[0].(*domain.TokenAsset)
			s.Equal("unverified-token-price", tokenAsset.ID())
			s.Equal(decimal.NewFromFloat(300).String(), tokenAsset.WalletAmounts[0].Amount.String())
			s.Equal(1e-18, tokenAsset.Price)
			s.False(tokenAsset.IsVerified())
		})
	})
}

func (s *AssetRepoTestSuite) TestGetSingleAsset() {
	// Pre-insert test assets
	preInsertAssets := []model.Asset{
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xtoken1",
			Amount:        util.Ptr(decimal.NewFromFloat(100).String()),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token 1",
			Symbol:        util.Ptr("TKN1"),
			LogoUrls:      `["https://example.com/token1.png"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
		{
			ChainID:       domain.BNBChain.ID(),
			AssetGroup:    "0xtoken2",
			Amount:        util.Ptr(decimal.NewFromFloat(150).String()),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token 2",
			Symbol:        util.Ptr("TKN2"),
			LogoUrls:      `["https://example.com/token2.png"]`,
			IsVerified:    util.Ptr(false),
			Decimals:      util.Ptr(int32(8)),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xnft1",
			Amount:        util.Ptr("1"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeNft),
			Name:          "NFT 1",
			LogoUrls:      `["https://example.com/nft1.png"]`,
			IsVerified:    util.Ptr(true),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xtoken1",
			Amount:        util.Ptr(decimal.NewFromFloat(50).String()),
			WalletAddress: domain.NewEvmAddress("0x456").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token 1",
			Symbol:        util.Ptr("TKN1"),
			LogoUrls:      `["https://example.com/token1.png"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
	}

	assetPrices := []model.AssetPrice{
		{
			ChainID:    domain.Ethereum.ID(),
			AssetGroup: "0xtoken1",
			Price:      "10",
		},
		{
			ChainID:    domain.Ethereum.ID(),
			AssetGroup: "0xtoken2",
			Price:      "20",
		},
		{
			ChainID:    domain.Ethereum.ID(),
			AssetGroup: "0xnft1",
			Price:      "500",
		},
	}

	s.NoError(Get().Create(&preInsertAssets).Error)
	s.NoError(Get().Create(&assetPrices).Error)

	// Test cases
	s.Run("Get existing token asset", func() {
		asset, err := s.repo.GetSingleAsset(s.ctx,
			domain.Ethereum,
			domain.AssetTypeToken,
			"0xtoken1",
			[]domain.Address{
				domain.NewEvmAddress("0x123"),
				domain.NewEvmAddress("0x456"),
			},
		)
		s.NoError(err)
		s.NotNil(asset)

		tokenAsset, ok := asset.(*domain.TokenAsset)
		s.True(ok)
		s.Equal(domain.Ethereum, tokenAsset.Chain())
		s.Equal("0xtoken1", tokenAsset.ID())
		s.Equal(domain.AssetTypeToken, tokenAsset.Type())
		s.Equal("Token 1", tokenAsset.Name())
		s.Equal("TKN1", tokenAsset.Symbol())
		s.Equal([]string{"https://example.com/token1.png"}, tokenAsset.LogoUrls())
		s.True(tokenAsset.IsVerified())
		s.Equal(uint(18), tokenAsset.Decimals())
		s.Equal(10.0, tokenAsset.Price)
		s.Len(tokenAsset.WalletAmounts, 2)
		s.Equal(domain.NewEvmAddress("0x123"), tokenAsset.WalletAmounts[0].Address)
		s.Equal(decimal.NewFromFloat(100).String(), tokenAsset.WalletAmounts[0].Amount.String())
		s.Equal(domain.NewEvmAddress("0x456"), tokenAsset.WalletAmounts[1].Address)
		s.Equal(decimal.NewFromFloat(50).String(), tokenAsset.WalletAmounts[1].Amount.String())
	})

	s.Run("Get existing NFT asset", func() {
		asset, err := s.repo.GetSingleAsset(s.ctx, domain.Ethereum, domain.AssetTypeNft, "0xnft1", []domain.Address{domain.NewEvmAddress("0x123")})
		s.NoError(err)
		s.NotNil(asset)

		nftAsset, ok := asset.(*domain.NftAsset)
		s.True(ok)
		s.Equal(domain.Ethereum, nftAsset.Chain())
		s.Equal("0xnft1", nftAsset.ID())
		s.Equal(domain.AssetTypeNft, nftAsset.Type())
		s.Equal("NFT 1", nftAsset.Name())
		s.Equal("https://example.com/nft1.png", nftAsset.LogoUrls()[0])
		s.True(nftAsset.IsVerified())
		s.Len(nftAsset.WalletAmounts, 1)
		s.Equal(domain.NewEvmAddress("0x123"), nftAsset.WalletAmounts[0].Address)
		s.Equal(uint(1), nftAsset.WalletAmounts[0].Amount)
	})

	s.Run("Get non-existing asset", func() {
		_, err := s.repo.GetSingleAsset(s.ctx,
			domain.Ethereum,
			domain.AssetTypeToken,
			"0xnonexistent",
			[]domain.Address{
				domain.NewEvmAddress("0x123"),
			},
		)
		s.Equal(domain.ErrRecordNotFound, err)
	})

	s.Run("Get asset with non-owning addresses", func() {
		// still return the asset metadata even if the address is not owning the asset
		asset, err := s.repo.GetSingleAsset(s.ctx,
			domain.Ethereum,
			domain.AssetTypeToken,
			"0xtoken1",
			[]domain.Address{
				domain.NewEvmAddress("0x789"),
			},
		)
		s.NoError(err)
		s.NotNil(asset)

		tokenAsset, ok := asset.(*domain.TokenAsset)
		s.True(ok)
		s.Equal(domain.Ethereum, tokenAsset.Chain())
		s.Equal("0xtoken1", tokenAsset.ID())
		s.Equal(domain.AssetTypeToken, tokenAsset.Type())
		s.Equal("Token 1", tokenAsset.Name())
		s.Equal("TKN1", tokenAsset.Symbol())
		s.Equal([]string{"https://example.com/token1.png"}, tokenAsset.LogoUrls())
		s.True(tokenAsset.IsVerified())
		s.Equal(uint(18), tokenAsset.Decimals())
		s.Equal(10.0, tokenAsset.Price)
		s.Len(tokenAsset.WalletAmounts, 1)
		s.Equal(domain.NewEvmAddress("0x789"), tokenAsset.WalletAmounts[0].Address)
		s.Equal(decimal.NewFromFloat(0).String(), tokenAsset.WalletAmounts[0].Amount.String())
	})
}

func (s *AssetRepoTestSuite) TestGetTokensByAddress() {
	// Pre-insert test assets
	preInsertAssets := []model.Asset{
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xtoken1",
			Amount:        util.Ptr(decimal.NewFromFloat(100).String()),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token 1",
			Symbol:        util.Ptr("TKN1"),
			LogoUrls:      `["https://example.com/token1.png"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
		{
			ChainID:       domain.BNBChain.ID(),
			AssetGroup:    "0xtoken2",
			Amount:        util.Ptr(decimal.NewFromFloat(200).String()),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token 2",
			Symbol:        util.Ptr("TKN2"),
			LogoUrls:      `["https://example.com/token2.png"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xtoken3",
			Amount:        util.Ptr(decimal.NewFromFloat(300).String()),
			WalletAddress: domain.NewEvmAddress("0x456").String(),
			AssetType:     string(domain.AssetTypeToken),
			Name:          "Token 3",
			Symbol:        util.Ptr("TKN3"),
			LogoUrls:      `["https://example.com/token3.png"]`,
			IsVerified:    util.Ptr(true),
			Decimals:      util.Ptr(int32(18)),
		},
		{
			ChainID:       domain.Ethereum.ID(),
			AssetGroup:    "0xnft1",
			Amount:        util.Ptr("1"),
			WalletAddress: domain.NewEvmAddress("0x123").String(),
			AssetType:     string(domain.AssetTypeNft),
			Name:          "NFT 1",
			LogoUrls:      `["https://example.com/nft1.png"]`,
			IsVerified:    util.Ptr(true),
		},
	}

	assetPrices := []model.AssetPrice{
		{
			ChainID:    domain.Ethereum.ID(),
			AssetGroup: "0xtoken1",
			Price:      "10",
		},
		{
			ChainID:    domain.Ethereum.ID(),
			AssetGroup: "0xtoken2",
			Price:      "20",
		},
		{
			ChainID:    domain.Ethereum.ID(),
			AssetGroup: "0xnft1",
			Price:      "500",
		},
	}

	s.NoError(Get().Create(&preInsertAssets).Error)
	s.NoError(Get().Create(&assetPrices).Error)

	// Test cases
	s.Run("Get tokens for single address", func() {
		mChainAddresses := map[domain.Chain][]domain.Address{
			domain.Ethereum: {domain.NewEvmAddress("0x123")},
		}

		tokens, err := s.repo.GetTokensByAddress(s.ctx, mChainAddresses)
		s.NoError(err)
		s.Len(tokens, 1)
		s.Equal(domain.Ethereum, tokens[0].Chain)
		s.Equal("0xtoken1", tokens[0].TokenID)
	})

	s.Run("Get tokens for multiple addresses and chains", func() {
		mChainAddresses := map[domain.Chain][]domain.Address{
			domain.Ethereum: {domain.NewEvmAddress("0x123"), domain.NewEvmAddress("0x456")},
			domain.BNBChain: {domain.NewEvmAddress("0x123")},
		}

		tokens, err := s.repo.GetTokensByAddress(s.ctx, mChainAddresses)
		s.NoError(err)
		s.Len(tokens, 3)

		// Sort the tokens to ensure consistent order for comparison
		sort.Slice(tokens, func(i, j int) bool {
			if tokens[i].Chain != tokens[j].Chain {
				return tokens[i].Chain.ID() < tokens[j].Chain.ID()
			}
			return tokens[i].TokenID < tokens[j].TokenID
		})

		s.Equal(domain.BNBChain, tokens[0].Chain)
		s.Equal("0xtoken2", tokens[0].TokenID)
		s.Equal(domain.Ethereum, tokens[1].Chain)
		s.Equal("0xtoken1", tokens[1].TokenID)
		s.Equal(domain.Ethereum, tokens[2].Chain)
		s.Equal("0xtoken3", tokens[2].TokenID)
	})

	s.Run("Get tokens for non-existent address", func() {
		mChainAddresses := map[domain.Chain][]domain.Address{
			domain.Ethereum: {domain.NewEvmAddress("0x789")},
		}

		tokens, err := s.repo.GetTokensByAddress(s.ctx, mChainAddresses)
		s.NoError(err)
		s.Len(tokens, 0)
	})

	s.Run("Get tokens with empty address map", func() {
		mChainAddresses := map[domain.Chain][]domain.Address{}

		tokens, err := s.repo.GetTokensByAddress(s.ctx, mChainAddresses)
		s.NoError(err)
		s.Len(tokens, 0)
	})
}
