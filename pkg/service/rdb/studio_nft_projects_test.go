package rdb

import (
	"context"
	"testing"
	"time"

	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

// TestUpsertStudioNftProject test upsert studio nft project
func TestUpsertStudioNftProject(t *testing.T) {
	organization := util.RandString(20)

	project := &dbmodel.StudioNftProject{
		Status:         dbmodel.ProjectStatusDraft,
		CollectionName: util.RandString(20),
		Organization:   organization,
		ChainID:        util.Ptr("sepolia"),
	}
	Reset()
	ctx := context.Background()

	projectID, err := UpsertStudioNftProject(ctx, project)
	assert.Nil(t, err)
	assert.NotEqual(t, 0, projectID)
	actualProject, err := GetStudioNftProjectByID(ctx, organization, projectID)
	assert.Nil(t, err)
	assert.Equal(t, project.Status, actualProject.Status)
	assert.Equal(t, project.CollectionName, *actualProject.CollectionName)

	// get by publish status: no data
	projects, err := GetStudioNftProjectsByPublishStatus(ctx, []dbmodel.PublishStatus{dbmodel.PublishStatusUploadedNftData, dbmodel.PublishStatusInit}, nil)
	assert.Nil(t, err)
	assert.Equal(t, 0, len(projects))

	// update collection name
	project.ProjectID = projectID
	project.CollectionName = util.RandString(20)
	projectID, err = UpsertStudioNftProject(ctx, project)
	assert.Nil(t, err)
	assert.Equal(t, projectID, project.ProjectID)
	actualProject, err = GetStudioNftProjectByID(ctx, organization, projectID)
	assert.Nil(t, err)
	assert.Equal(t, project.Status, actualProject.Status)
	assert.Equal(t, project.CollectionName, *actualProject.CollectionName)

	// update publish status
	_, err = UpsertStudioNftProject(ctx, &dbmodel.StudioNftProject{
		PublishStatus: util.Ptr(dbmodel.PublishStatusInit),
		ProjectID:     projectID,
	})
	assert.Nil(t, err)

	// get by publish status: has data
	projects, err = GetStudioNftProjectsByPublishStatus(ctx, []dbmodel.PublishStatus{dbmodel.PublishStatusInit}, nil)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(projects))

	// get by publish status and updated after time: no data
	projects, err = GetStudioNftProjectsByPublishStatus(ctx, []dbmodel.PublishStatus{dbmodel.PublishStatusInit}, util.Ptr(time.Now().Add(time.Second)))
	assert.Nil(t, err)
	assert.Equal(t, 0, len(projects))
}

// TestGetStudioNftProjectStats test get studio nft project stats
func TestGetStudioNftProjectStats(t *testing.T) {
	Reset()
	ctx := context.Background()
	assert.Nil(t, rdbtest.CreateNftProjects(Get()))
	org := "KryptoGO"
	projects, _, err := GetStudioNftProjectStats(ctx, org, GetStudioNftProjectStatsParams{})
	assert.NoError(t, err)
	assert.Equal(t, 5, len(projects))
	assert.ElementsMatch(t, []int{1, 2, 3, 1155, 60}, getProjectIDs(projects))
	assert.Equal(t, int64(1691462088), (projects)[0].StartTime)
}

func getProjectIDs(projects []*StudioNftProjectStats) []int {
	projectIDs := make([]int, len(projects))
	for i, project := range projects {
		projectIDs[i] = project.ProjectID
	}
	return projectIDs
}
