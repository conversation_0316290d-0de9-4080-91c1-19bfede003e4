package rdb

import (
	"context"
	"math/big"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestSendWithRent(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()

	t.Run("CreateSendWithRent", func(t *testing.T) {
		// Prepare test data
		now := time.Now().UTC()
		amount := big.NewInt(1000000)
		fee := decimal.NewFromFloat(1.5)
		energyRentCost := 0.5
		actualCostUsd := 2.5
		signedTxs := []string{
			`{"visible":false,"txID":"tx1","raw_data":{"contract":[{"parameter":{"value":{}}}]}}`,
			`{"visible":false,"txID":"tx2","raw_data":{"contract":[{"parameter":{"value":{}}}]}}`,
		}

		sendWithRent := &domain.SendWithRent{
			OrgID:               1,
			UID:                 "test-uid",
			Chain:               domain.Shasta,
			From:                domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			Recipient:           domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"),
			TokenAddress:        domain.NewTronAddress("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"),
			Amount:              amount,
			Fee:                 fee,
			SignedTxs:           signedTxs,
			Status:              domain.SendWithRentStatusProcessing,
			EnergyRentCost:      &energyRentCost,
			ActualCostUsd:       &actualCostUsd,
			TokenTransferTxHash: util.Ptr("hash1"),
			FeeTransferTxHash:   util.Ptr("hash2"),
			RetryCount:          0,
			EstimatedFinishAt:   now.Add(5 * time.Minute),
			CreatedAt:           now,
		}

		// Create record
		id, err := repo.CreateSendWithRent(ctx, sendWithRent)
		require.NoError(t, err)
		assert.Greater(t, id, 0)

		// Verify record in database
		var record model.StudioOrganizationSendWithRent
		err = Get().Where("id = ?", id).First(&record).Error
		require.NoError(t, err)

		// Assert all fields
		assert.Equal(t, sendWithRent.OrgID, record.OrganizationID)
		assert.Equal(t, sendWithRent.UID, record.UID)
		assert.Equal(t, sendWithRent.Chain.ID(), record.ChainID)
		assert.Equal(t, sendWithRent.From.String(), record.From)
		assert.Equal(t, sendWithRent.Recipient.String(), record.Recipient)
		assert.Equal(t, sendWithRent.TokenAddress.String(), record.TokenAddress)
		assert.Equal(t, sendWithRent.Amount.String(), record.Amount)
		assert.Equal(t, sendWithRent.Fee.String(), record.Fee.String())
		assert.Contains(t, record.SignedTxs, "tx1")
		assert.Contains(t, record.SignedTxs, "tx2")
		assert.Equal(t, sendWithRent.Status, record.Status)
		assert.Equal(t, decimal.NewFromFloat(energyRentCost).String(), record.EnergyRentCost.String())
		assert.Equal(t, decimal.NewFromFloat(actualCostUsd).String(), record.ActualCostUsd.String())
		assert.Equal(t, *sendWithRent.TokenTransferTxHash, *record.TokenTransferTxHash)
		assert.Equal(t, *sendWithRent.FeeTransferTxHash, *record.FeeTransferTxHash)
		assert.Equal(t, sendWithRent.RetryCount, record.RetryCount)
		assert.WithinDuration(t, sendWithRent.EstimatedFinishAt, record.EstimatedFinishAt, time.Second)
		assert.WithinDuration(t, sendWithRent.CreatedAt, record.CreatedAt, time.Second)

		t.Run("UpdateSendWithRent", func(t *testing.T) {
			// Update specific fields
			newStatus := domain.SendWithRentStatusSuccess
			newEnergyRentCost := 0.8
			newActualCostUsd := 3.0
			newTokenTxHash := "newhash1"
			newFeeTxHash := "newhash2"
			newRetryCount := 1

			update := &domain.UpdateSendWithRentRequest{
				ID:                  id,
				Status:              &newStatus,
				EnergyRentCost:      &newEnergyRentCost,
				ActualCostUsd:       &newActualCostUsd,
				TokenTransferTxHash: &newTokenTxHash,
				FeeTransferTxHash:   &newFeeTxHash,
				RetryCount:          &newRetryCount,
				ProfitMarginRate:    util.Ptr(decimal.NewFromFloat(0.1)),
				ProfitShareRatio:    util.Ptr(decimal.NewFromFloat(0.2)),
				ProfitMargin:        util.Ptr(decimal.NewFromFloat(0.3)),
			}

			err = repo.UpdateSendWithRent(ctx, update)
			require.NoError(t, err)

			// Verify updated record
			var updatedRecord model.StudioOrganizationSendWithRent
			err = Get().Where("id = ?", id).First(&updatedRecord).Error
			require.NoError(t, err)

			// Assert updated fields
			assert.Equal(t, newStatus, updatedRecord.Status)
			assert.Equal(t, decimal.NewFromFloat(newEnergyRentCost).String(), updatedRecord.EnergyRentCost.String())
			assert.Equal(t, decimal.NewFromFloat(newActualCostUsd).String(), updatedRecord.ActualCostUsd.String())
			assert.Equal(t, newTokenTxHash, *updatedRecord.TokenTransferTxHash)
			assert.Equal(t, newFeeTxHash, *updatedRecord.FeeTransferTxHash)
			assert.Equal(t, newRetryCount, updatedRecord.RetryCount)
			assert.Equal(t, 0.1, updatedRecord.ProfitMarginRate.InexactFloat64())
			assert.Equal(t, 0.2, updatedRecord.ProfitShareRatio.InexactFloat64())
			assert.Equal(t, 0.3, updatedRecord.ProfitMargin.InexactFloat64())

			// Verify unchanged fields remain the same
			assert.Equal(t, record.OrganizationID, updatedRecord.OrganizationID)
			assert.Equal(t, record.UID, updatedRecord.UID)
			assert.Equal(t, record.ChainID, updatedRecord.ChainID)
			assert.Equal(t, record.Amount, updatedRecord.Amount)
			assert.Equal(t, record.Fee.String(), updatedRecord.Fee.String())
		})

		t.Run("GetSendWithRentByID", func(t *testing.T) {
			// Get record
			retrieved, err := repo.GetSendWithRentByID(ctx, id)
			require.NoError(t, err)

			// Assert all fields
			assert.Equal(t, id, retrieved.ID)
			assert.Equal(t, sendWithRent.OrgID, retrieved.OrgID)
			assert.Equal(t, sendWithRent.UID, retrieved.UID)
			assert.Equal(t, domain.Shasta.ID(), retrieved.Chain.ID())
			assert.Equal(t, sendWithRent.From.String(), retrieved.From.String())
			assert.Equal(t, sendWithRent.Recipient.String(), retrieved.Recipient.String())
			assert.Equal(t, sendWithRent.TokenAddress.String(), retrieved.TokenAddress.String())
			assert.Equal(t, amount.String(), retrieved.Amount.String())
			assert.Equal(t, fee.String(), retrieved.Fee.String())
			assert.Equal(t, signedTxs, retrieved.SignedTxs)
			assert.Equal(t, domain.SendWithRentStatusSuccess, retrieved.Status) // Updated status
			assert.InDelta(t, 0.8, *retrieved.EnergyRentCost, 0.001)            // Updated value
			assert.InDelta(t, 3.0, *retrieved.ActualCostUsd, 0.001)             // Updated value
			assert.Equal(t, "newhash1", *retrieved.TokenTransferTxHash)
			assert.Equal(t, "newhash2", *retrieved.FeeTransferTxHash)
			assert.Equal(t, 1, retrieved.RetryCount)
			assert.WithinDuration(t, sendWithRent.EstimatedFinishAt, retrieved.EstimatedFinishAt, time.Second)
			assert.WithinDuration(t, sendWithRent.CreatedAt, retrieved.CreatedAt, time.Second)
		})

		t.Run("GetSendWithRentByID_NotFound", func(t *testing.T) {
			// Try to get non-existent record
			_, err := repo.GetSendWithRentByID(ctx, 99999)
			assert.ErrorIs(t, err, domain.ErrRecordNotFound)
		})
	})
}
