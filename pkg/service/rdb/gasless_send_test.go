package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGaslessSend(t *testing.T) {
	Reset()

	ctx := context.Background()
	r := GormRepo()

	// Test data
	data := &domain.GaslessSend{
		OrgID:             1,
		UID:               "unique-id",
		ChainID:           "chain-id",
		From:              "from-address",
		Recipient:         "recipient-address",
		TokenAddress:      "token-address",
		Amount:            "100",
		Fee:               "1",
		SignedTxs:         []string{"signed-tx-1", "signed-tx-2"},
		CreatedAt:         time.Now(),
		FeeUsd:            1.5,
		EstimatedFinishAt: time.Now().Add(time.Hour),
		Status:            domain.GaslessSendStatus("processing"),
		RetryCount:        0,
	}

	// Call get by ID first and assert not found
	_, err := r.GetGaslessSendByID(ctx, data.ID)
	assert.Equal(t, domain.ErrGaslessSendNotFound, err)

	// Call create
	id, err := r.CreateGaslessSend(ctx, data)
	assert.NoError(t, err)
	assert.Greater(t, id, 0)
	data.ID = id

	// Call get by ID and assert correct value
	retrieved, err := r.GetGaslessSendByID(ctx, id)
	assert.NoError(t, err)
	assert.Equal(t, data.OrgID, retrieved.OrgID)
	assert.Equal(t, data.UID, retrieved.UID)
	assert.Equal(t, data.ChainID, retrieved.ChainID)
	assert.Equal(t, data.From, retrieved.From)
	assert.Equal(t, data.Recipient, retrieved.Recipient)
	assert.Equal(t, data.TokenAddress, retrieved.TokenAddress)
	assert.Equal(t, data.Amount, retrieved.Amount)
	assert.Equal(t, data.Fee, retrieved.Fee)
	assert.ElementsMatch(t, data.SignedTxs, retrieved.SignedTxs)
	assert.InDelta(t, data.CreatedAt.Unix(), retrieved.CreatedAt.Unix(), 2)
	assert.Equal(t, data.FeeUsd, retrieved.FeeUsd)
	assert.InDelta(t, data.EstimatedFinishAt.Unix(), retrieved.EstimatedFinishAt.Unix(), 2)
	assert.Equal(t, data.Status, retrieved.Status)
	assert.Equal(t, data.RetryCount, retrieved.RetryCount)

	// Call update with GasFaucetTxHash and EnergyRentCost
	update1 := &domain.UpdateGaslessSendRequest{
		ID:              id,
		GasFaucetTxHash: util.Ptr("new-gas-faucet-tx-hash"),
		EnergyRentCost:  util.Ptr(2.5),
	}
	err = r.UpdateGaslessSend(ctx, update1)
	assert.NoError(t, err)

	// Call get by ID and assert correct value
	retrieved, err = r.GetGaslessSendByID(ctx, id)
	assert.NoError(t, err)
	assert.Equal(t, *update1.GasFaucetTxHash, *retrieved.GasFaucetTxHash)
	assert.Equal(t, *update1.EnergyRentCost, *retrieved.EnergyRentCost)

	// Call update with UserApproveTxHash and ActualCostUsd
	update2 := &domain.UpdateGaslessSendRequest{
		ID:                id,
		UserApproveTxHash: util.Ptr("new-user-approve-tx-hash"),
		ActualCostUsd:     util.Ptr(3.5),
	}
	err = r.UpdateGaslessSend(ctx, update2)
	assert.NoError(t, err)

	// Call get by ID and assert correct value
	retrieved, err = r.GetGaslessSendByID(ctx, id)
	assert.NoError(t, err)
	assert.Equal(t, *update1.GasFaucetTxHash, *retrieved.GasFaucetTxHash)
	assert.Equal(t, *update1.EnergyRentCost, *retrieved.EnergyRentCost)
	assert.Equal(t, *update2.UserApproveTxHash, *retrieved.UserApproveTxHash)
	assert.Equal(t, *update2.ActualCostUsd, *retrieved.ActualCostUsd)
}

func TestGaslessSendV2(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()

	t.Run("CreateGaslessSend", func(t *testing.T) {
		// Prepare test data
		now := time.Now().UTC()
		amount := decimal.NewFromInt(1)
		fee := decimal.NewFromFloat(1.5)
		energyRentCost := 0.5
		actualCostUsd := 2.5
		signedTxs := []string{
			`{"visible":false,"txID":"tx1","raw_data":{"contract":[{"parameter":{"value":{}}}]}}`,
			`{"visible":false,"txID":"tx2","raw_data":{"contract":[{"parameter":{"value":{}}}]}}`,
		}

		gaslessSend := &domain.GaslessSendV2{
			OrgID:               1,
			UID:                 "test-uid",
			Chain:               domain.Shasta,
			From:                domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			Recipient:           domain.NewTronAddress("TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"),
			TokenAddress:        domain.NewTronAddress("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"),
			Amount:              amount,
			Fee:                 fee,
			SignedTxs:           signedTxs,
			Status:              domain.GaslessSendStatusProcessing,
			EnergyRentCost:      &energyRentCost,
			ActualCostUsd:       &actualCostUsd,
			TokenTransferTxHash: util.Ptr("hash1"),
			FeeTransferTxHash:   util.Ptr("hash2"),
			RetryCount:          0,
			EstimatedFinishAt:   now.Add(5 * time.Minute),
			CreatedAt:           now,
		}

		// Create record
		id, err := repo.CreateGaslessSendV2(ctx, gaslessSend)
		require.NoError(t, err)
		assert.Greater(t, id, 0)

		// Verify record in database
		var record model.StudioOrganizationGaslessSend
		err = Get().Where("id = ?", id).First(&record).Error
		require.NoError(t, err)

		// Assert all fields
		assert.Equal(t, gaslessSend.OrgID, record.OrganizationID)
		assert.Equal(t, gaslessSend.UID, record.UID)
		assert.Equal(t, gaslessSend.Chain.ID(), record.ChainID)
		assert.Equal(t, gaslessSend.From.String(), record.From)
		assert.Equal(t, gaslessSend.Recipient.String(), record.Recipient)
		assert.Equal(t, gaslessSend.TokenAddress.String(), record.TokenAddress)
		assert.Equal(t, gaslessSend.Amount.String(), record.Amount)
		assert.Equal(t, gaslessSend.Fee.String(), record.Fee)
		assert.Contains(t, record.SignedTxs, "tx1")
		assert.Contains(t, record.SignedTxs, "tx2")
		assert.Equal(t, gaslessSend.Status, record.Status)
		assert.Equal(t, decimal.NewFromFloat(energyRentCost).String(), record.EnergyRentCost.String())
		assert.Equal(t, decimal.NewFromFloat(actualCostUsd).String(), record.ActualCostUsd.String())
		assert.Equal(t, *gaslessSend.TokenTransferTxHash, *record.TokenTransferTxHash)
		assert.Equal(t, *gaslessSend.FeeTransferTxHash, *record.FeeTransferTxHash)
		assert.Equal(t, gaslessSend.RetryCount, record.RetryCount)
		assert.WithinDuration(t, gaslessSend.EstimatedFinishAt, record.EstimatedFinishAt, time.Second)
		assert.WithinDuration(t, gaslessSend.CreatedAt, record.CreatedAt, time.Second)

		t.Run("UpdateGaslessSend", func(t *testing.T) {
			// Update specific fields
			newStatus := domain.GaslessSendStatusSuccess
			newEnergyRentCost := 0.8
			newActualCostUsd := 3.0
			newTokenTxHash := "newhash1"
			newFeeTxHash := "newhash2"
			newGasFaucetTxHash := "newhash3"
			newRetryCount := 1
			newNativeTokenPrice := 1.0
			newTokenPrice := 2.0

			update := &domain.UpdateGaslessSendV2Request{
				ID:                  id,
				Status:              &newStatus,
				EnergyRentCost:      &newEnergyRentCost,
				NativeTokenPrice:    &newNativeTokenPrice,
				TokenPrice:          &newTokenPrice,
				ActualCostUsd:       &newActualCostUsd,
				GasFaucetTxHash:     &newGasFaucetTxHash,
				TokenTransferTxHash: &newTokenTxHash,
				FeeTransferTxHash:   &newFeeTxHash,
				RetryCount:          &newRetryCount,
				ProfitMarginRate:    util.Ptr(decimal.NewFromFloat(0.1)),
				ProfitShareRatio:    util.Ptr(decimal.NewFromFloat(0.2)),
				ProfitMargin:        util.Ptr(decimal.NewFromFloat(0.3)),
			}

			err = repo.UpdateGaslessSendV2(ctx, update)
			require.NoError(t, err)

			// Verify updated record
			var updatedRecord model.StudioOrganizationGaslessSend
			err = Get().Where("id = ?", id).First(&updatedRecord).Error
			require.NoError(t, err)

			// Assert updated fields
			assert.Equal(t, newStatus, updatedRecord.Status)
			assert.Equal(t, decimal.NewFromFloat(newEnergyRentCost).String(), updatedRecord.EnergyRentCost.String())
			assert.Equal(t, decimal.NewFromFloat(newNativeTokenPrice).String(), updatedRecord.NativeTokenPrice.String())
			assert.Equal(t, decimal.NewFromFloat(newTokenPrice).String(), updatedRecord.TokenPrice.String())
			assert.Equal(t, decimal.NewFromFloat(newActualCostUsd).String(), updatedRecord.ActualCostUsd.String())
			assert.Equal(t, newGasFaucetTxHash, *updatedRecord.GasFaucetTxHash)
			assert.Equal(t, newTokenTxHash, *updatedRecord.TokenTransferTxHash)
			assert.Equal(t, newFeeTxHash, *updatedRecord.FeeTransferTxHash)
			assert.Equal(t, newRetryCount, updatedRecord.RetryCount)
			assert.Equal(t, 0.1, updatedRecord.ProfitMarginRate.InexactFloat64())
			assert.Equal(t, 0.2, updatedRecord.ProfitShareRatio.InexactFloat64())
			assert.Equal(t, 0.3, updatedRecord.ProfitMargin.InexactFloat64())

			// Verify unchanged fields remain the same
			assert.Equal(t, record.OrganizationID, updatedRecord.OrganizationID)
			assert.Equal(t, record.UID, updatedRecord.UID)
			assert.Equal(t, record.ChainID, updatedRecord.ChainID)
			assert.Equal(t, record.Amount, updatedRecord.Amount)
			assert.Equal(t, record.Fee, updatedRecord.Fee)
		})

		t.Run("GetGaslessSendByID", func(t *testing.T) {
			// Get record
			retrieved, err := repo.GetGaslessSendV2ByID(ctx, id)
			require.NoError(t, err)

			// Assert all fields
			assert.Equal(t, id, retrieved.ID)
			assert.Equal(t, gaslessSend.OrgID, retrieved.OrgID)
			assert.Equal(t, gaslessSend.UID, retrieved.UID)
			assert.Equal(t, domain.Shasta.ID(), retrieved.Chain.ID())
			assert.Equal(t, gaslessSend.From.String(), retrieved.From.String())
			assert.Equal(t, gaslessSend.Recipient.String(), retrieved.Recipient.String())
			assert.Equal(t, gaslessSend.TokenAddress.String(), retrieved.TokenAddress.String())
			assert.Equal(t, amount.String(), retrieved.Amount.String())
			assert.Equal(t, fee.String(), retrieved.Fee.String())
			assert.Equal(t, signedTxs, retrieved.SignedTxs)
			assert.Equal(t, domain.GaslessSendStatusSuccess, retrieved.Status) // Updated status
			assert.InDelta(t, 0.8, *retrieved.EnergyRentCost, 0.001)           // Updated value
			assert.InDelta(t, 3.0, *retrieved.ActualCostUsd, 0.001)            // Updated value
			assert.Equal(t, "newhash3", *retrieved.GasFaucetTxHash)
			assert.Equal(t, "newhash1", *retrieved.TokenTransferTxHash)
			assert.Equal(t, "newhash2", *retrieved.FeeTransferTxHash)
			assert.Equal(t, 1, retrieved.RetryCount)
			assert.WithinDuration(t, gaslessSend.EstimatedFinishAt, retrieved.EstimatedFinishAt, time.Second)
			assert.WithinDuration(t, gaslessSend.CreatedAt, retrieved.CreatedAt, time.Second)
		})

		t.Run("GetGaslessSendByID_NotFound", func(t *testing.T) {
			// Try to get non-existent record
			_, err := repo.GetGaslessSendV2ByID(ctx, 99999)
			assert.ErrorIs(t, err, domain.ErrRecordNotFound)
		})
	})
}
