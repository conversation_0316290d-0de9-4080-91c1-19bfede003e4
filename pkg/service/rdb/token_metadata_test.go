package rdb

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestBatchUpsertTokenMetadata(t *testing.T) {
	Reset()

	ctx := context.Background()

	// Use actual Chain instances from chain.go instead of mocks
	initialTokens := []domain.ChainToken{
		{Chain: domain.Ethereum, TokenID: "token1"},
		{Chain: domain.Polygon, TokenID: "token2"},
	}

	initialMetadata := map[domain.ChainToken]*domain.TokenMetadata{
		initialTokens[0]: {
			Name:          "Token One",
			Symbol:        "TKN1",
			Decimals:      18,
			CoingeckoID:   "coingecko1",
			LogoUrl:       "http://example.com/tkn1.png",
			IsVerified:    true,
			BinanceTicker: "TKN1",
		},
		initialTokens[1]: {
			Name:        "",
			Symbol:      "TKN2",
			Decimals:    0,
			CoingeckoID: "",
			LogoUrl:     "",
			IsVerified:  false,
		},
	}

	repo := GormRepo()

	// Upsert initial data
	err := repo.BatchUpsertTokenMetadata(ctx, initialMetadata)
	assert.NoError(t, err)

	tokenMetadatas := model.TokenMetadata{}
	err = repo.db.Find(&tokenMetadatas).Error
	assert.NoError(t, err)
	// t.Logf("tokenMetadatas: %+v", tokenMetadatas)

	// Prepare upsert data using actual Chains
	upsertTokens := map[domain.ChainToken]*domain.TokenMetadata{
		initialTokens[0]: { // Update existing token
			Name:        "Token One Updated",
			Symbol:      "", // Should not overwrite
			Decimals:    0,  // Should not overwrite
			CoingeckoID: "", // Should not overwrite
			LogoUrl:     "http://example.com/tkn1_new.png",
			IsVerified:  true,
		},
		{Chain: domain.BNBChain, TokenID: "token3"}: { // New token
			Name:        "Token Three",
			Symbol:      "TKN3",
			Decimals:    8,
			CoingeckoID: "coingecko3",
			LogoUrl:     "http://example.com/tkn3.png",
			IsVerified:  true,
		},
	}

	// Perform BatchUpsertTokenMetadata
	err = repo.BatchUpsertTokenMetadata(ctx, upsertTokens)
	assert.NoError(t, err)

	// Verify using BatchGetTokenMetadata
	allTokens := append(initialTokens, domain.ChainToken{Chain: domain.BNBChain, TokenID: "token3"})
	result, err := repo.BatchGetTokenMetadata(ctx, allTokens)
	assert.NoError(t, err)

	// t.Logf("result: %+v", result)

	// Assertions for updated token1
	updatedToken1, exists := result[initialTokens[0]]
	assert.True(t, exists)
	assert.Equal(t, "Token One Updated", updatedToken1.Name)
	assert.Equal(t, "TKN1", updatedToken1.Symbol)     // Should remain unchanged
	assert.Equal(t, uint(18), updatedToken1.Decimals) // Should remain unchanged
	assert.Equal(t, "coingecko1", string(updatedToken1.CoingeckoID))
	assert.Equal(t, "http://example.com/tkn1_new.png", updatedToken1.LogoUrl)
	assert.Equal(t, "TKN1", updatedToken1.BinanceTicker)

	// Assertions for token2 (unchanged)
	token2, exists := result[initialTokens[1]]
	assert.True(t, exists)
	assert.Equal(t, "", token2.Name) // Initially empty
	assert.Equal(t, "TKN2", token2.Symbol)
	assert.Equal(t, uint(0), token2.Decimals)
	assert.Equal(t, "", string(token2.CoingeckoID))
	assert.Equal(t, "", token2.LogoUrl)

	// Assertions for new token3
	token3, exists := result[domain.ChainToken{Chain: domain.BNBChain, TokenID: "token3"}]
	assert.True(t, exists)
	assert.Equal(t, "Token Three", token3.Name)
	assert.Equal(t, "TKN3", token3.Symbol)
	assert.Equal(t, uint(8), token3.Decimals)
	assert.Equal(t, "coingecko3", string(token3.CoingeckoID))
	assert.Equal(t, "http://example.com/tkn3.png", token3.LogoUrl)

	// Verify using GetTokenMetadata
	token1Fetched, err := repo.GetTokenMetadata(ctx, domain.Ethereum, "token1")
	assert.NoError(t, err)
	assert.Equal(t, "Token One Updated", token1Fetched.Name)
	assert.Equal(t, "TKN1", token1Fetched.Symbol)
	assert.Equal(t, uint(18), token1Fetched.Decimals)
	assert.Equal(t, "coingecko1", string(token1Fetched.CoingeckoID))
	assert.Equal(t, "http://example.com/tkn1_new.png", token1Fetched.LogoUrl)
	assert.Equal(t, "TKN1", token1Fetched.BinanceTicker)

	token3Fetched, err := repo.GetTokenMetadata(ctx, domain.BNBChain, "token3")
	assert.NoError(t, err)
	assert.Equal(t, "Token Three", token3Fetched.Name)
	assert.Equal(t, "TKN3", token3Fetched.Symbol)
	assert.Equal(t, uint(8), token3Fetched.Decimals)
	assert.Equal(t, "coingecko3", string(token3Fetched.CoingeckoID))
	assert.Equal(t, "http://example.com/tkn3.png", token3Fetched.LogoUrl)
}

// TestBatchUpsertTokenMetadataWithEmptyValues tests the BatchUpsertTokenMetadata function
// with empty values
func TestBatchUpsertTokenMetadataWithEmptyValues(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()

	// Create test data with empty values
	metadatas := map[domain.ChainToken]*domain.TokenMetadata{
		{
			Chain:   domain.Ethereum,
			TokenID: "0x123",
		}: {},
	}

	// Perform the upsert
	err := repo.BatchUpsertTokenMetadata(ctx, metadatas)
	assert.NoError(t, err)

	// Verify the records were created
	tokens := []domain.ChainToken{
		{Chain: domain.Ethereum, TokenID: "0x123"},
	}

	result, err := repo.BatchGetTokenMetadata(ctx, tokens)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(result), "Should have created 1 record")

	// Check first token
	token1 := result[tokens[0]]
	assert.NotNil(t, token1)
	assert.Equal(t, "", token1.Name) // empty name should be preserved
	assert.Equal(t, "", token1.Symbol)
	assert.Equal(t, "", string(token1.CoingeckoID))
	assert.Equal(t, uint(0), token1.Decimals)
	assert.Equal(t, "", token1.LogoUrl)
	assert.False(t, token1.IsVerified)
}

// TestUpsertTokenMetadata tests the UpsertTokenMetadata function
func TestUpsertTokenMetadata(t *testing.T) {
	Reset()

	ctx := context.Background()

	// Initial data using actual Chain instances
	initialTokens := []domain.ChainToken{
		{Chain: domain.Ethereum, TokenID: "token1"},
		{Chain: domain.Polygon, TokenID: "token2"},
	}

	initialMetadata := map[domain.ChainToken]*domain.TokenMetadata{
		initialTokens[0]: {
			Name:          "Token One",
			Symbol:        "TKN1",
			Decimals:      18,
			CoingeckoID:   "coingecko1",
			LogoUrl:       "http://example.com/tkn1.png",
			IsVerified:    true,
			BinanceTicker: "TKN1",
		},
		initialTokens[1]: {
			Name:        "",
			Symbol:      "TKN2",
			Decimals:    0,
			CoingeckoID: "",
			LogoUrl:     "",
			IsVerified:  false,
		},
	}

	repo := GormRepo()

	// Upsert initial data
	err := repo.BatchUpsertTokenMetadata(ctx, initialMetadata)
	assert.NoError(t, err)

	// Verify initial upsert using BatchGetTokenMetadata
	result, err := repo.BatchGetTokenMetadata(ctx, initialTokens)
	assert.NoError(t, err)

	// Assertions for initial tokens
	token1, exists := result[initialTokens[0]]
	assert.True(t, exists)
	assert.Equal(t, "Token One", token1.Name)
	assert.Equal(t, "TKN1", token1.Symbol)
	assert.Equal(t, uint(18), token1.Decimals)
	assert.Equal(t, "coingecko1", string(token1.CoingeckoID))
	assert.Equal(t, "http://example.com/tkn1.png", token1.LogoUrl)
	assert.Equal(t, "TKN1", token1.BinanceTicker)

	token2, exists := result[initialTokens[1]]
	assert.True(t, exists)
	assert.Equal(t, "", token2.Name)
	assert.Equal(t, "TKN2", token2.Symbol)
	assert.Equal(t, uint(0), token2.Decimals)
	assert.Equal(t, "", string(token2.CoingeckoID))
	assert.Equal(t, "", token2.LogoUrl)

	// Upsert a single token using UpsertTokenMetadata
	upsertToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "token1"}
	upsertMetadata := &domain.TokenMetadata{
		Name:        "Token One Upgraded",
		Symbol:      "", // Should not overwrite
		Decimals:    0,  // Should not overwrite
		CoingeckoID: "", // Should not overwrite
		LogoUrl:     "http://example.com/tkn1_upgraded.png",
		IsVerified:  true,
	}

	err = repo.UpsertTokenMetadata(ctx, upsertToken.Chain, upsertToken.TokenID, upsertMetadata)
	assert.NoError(t, err)

	// Insert a new token using UpsertTokenMetadata
	newToken := domain.ChainToken{Chain: domain.BNBChain, TokenID: "token3"}
	newMetadata := &domain.TokenMetadata{
		Name:        "Token Three",
		Symbol:      "TKN3",
		Decimals:    8,
		CoingeckoID: "coingecko3",
		LogoUrl:     "http://example.com/tkn3.png",
		IsVerified:  true,
	}

	err = repo.UpsertTokenMetadata(ctx, newToken.Chain, newToken.TokenID, newMetadata)
	assert.NoError(t, err)

	// Verify updates and insertions using BatchGetTokenMetadata
	allTokens := append(initialTokens, newToken)
	result, err = repo.BatchGetTokenMetadata(ctx, allTokens)
	assert.NoError(t, err)

	// Assertions for updated token1
	updatedToken1, exists := result[initialTokens[0]]
	assert.True(t, exists)
	assert.Equal(t, "Token One Upgraded", updatedToken1.Name)
	assert.Equal(t, "TKN1", updatedToken1.Symbol)     // Should remain unchanged
	assert.Equal(t, uint(18), updatedToken1.Decimals) // Should remain unchanged
	assert.Equal(t, "coingecko1", string(updatedToken1.CoingeckoID))
	assert.Equal(t, "http://example.com/tkn1_upgraded.png", updatedToken1.LogoUrl)
	assert.Equal(t, "TKN1", updatedToken1.BinanceTicker)

	// Assertions for token2 (unchanged)
	token2, exists = result[initialTokens[1]]
	assert.True(t, exists)
	assert.Equal(t, "", token2.Name) // Initially empty
	assert.Equal(t, "TKN2", token2.Symbol)
	assert.Equal(t, uint(0), token2.Decimals)
	assert.Equal(t, "", string(token2.CoingeckoID))
	assert.Equal(t, "", token2.LogoUrl)

	// Assertions for new token3
	token3, exists := result[newToken]
	assert.True(t, exists)
	assert.Equal(t, "Token Three", token3.Name)
	assert.Equal(t, "TKN3", token3.Symbol)
	assert.Equal(t, uint(8), token3.Decimals)
	assert.Equal(t, "coingecko3", string(token3.CoingeckoID))
	assert.Equal(t, "http://example.com/tkn3.png", token3.LogoUrl)

	// Verify using GetTokenMetadata
	token1Fetched, err := repo.GetTokenMetadata(ctx, domain.Ethereum, "token1")
	assert.NoError(t, err)
	assert.Equal(t, "Token One Upgraded", token1Fetched.Name)
	assert.Equal(t, "TKN1", token1Fetched.Symbol)
	assert.Equal(t, uint(18), token1Fetched.Decimals)
	assert.Equal(t, "coingecko1", string(token1Fetched.CoingeckoID))
	assert.Equal(t, "http://example.com/tkn1_upgraded.png", token1Fetched.LogoUrl)
	assert.Equal(t, "TKN1", token1Fetched.BinanceTicker)

	token3Fetched, err := repo.GetTokenMetadata(ctx, domain.BNBChain, "token3")
	assert.NoError(t, err)
	assert.Equal(t, "Token Three", token3Fetched.Name)
	assert.Equal(t, "TKN3", token3Fetched.Symbol)
	assert.Equal(t, uint(8), token3Fetched.Decimals)
	assert.Equal(t, "coingecko3", string(token3Fetched.CoingeckoID))
	assert.Equal(t, "http://example.com/tkn3.png", token3Fetched.LogoUrl)
}

func TestBatchGetTokenMetadata(t *testing.T) {
	Reset()

	assert.NoError(t, Get().Create([]model.TokenMetadata{
		{
			ChainID:         "ethereum",
			ContractAddress: "******************************************",
			Name:            util.Ptr("The Verge"),
			Symbol:          util.Ptr("VERGE"),
		},
		{
			ChainID:         "polygon-pos",
			ContractAddress: "******************************************",
			CoingeckoID:     util.Ptr("bridged-usdc-polygon-pos-bridge"),
			Name:            util.Ptr("USD Coin Bridged"),
			Symbol:          util.Ptr("USDCE"),
		},
	}).Error)
	assert.NoError(t, Get().Create([]model.DataContractMetadata{
		{
			ChainID:         "eth",
			ContractAddress: "******************************************",
			Name:            "The Verge",
			Symbol:          "VERGE",
			Decimals:        9,
		},
		{
			ChainID:            "matic",
			ContractAddress:    "******************************************",
			Name:               "USD Coin Bridged",
			Symbol:             "USDCE",
			ContractSchemaName: "ERC20",
			Decimals:           6,
		},
	}).Error)

	ctx := context.Background()
	repo := GormRepo()

	ethToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"}
	caseChangedToken := domain.ChainToken{Chain: domain.Polygon, TokenID: "******************************************"}
	tokens := []domain.ChainToken{
		ethToken,
		caseChangedToken,
	}

	result, err := repo.BatchGetTokenMetadata(ctx, tokens)
	assert.NoError(t, err)
	assert.Len(t, result, 2)

	assert.NotNil(t, result[ethToken])
	assert.Equal(t, "The Verge", result[ethToken].Name)
	assert.Equal(t, "VERGE", result[ethToken].Symbol)
	assert.Equal(t, uint(9), result[ethToken].Decimals)

	assert.NotNil(t, result[caseChangedToken])
	assert.Equal(t, "USD Coin Bridged", result[caseChangedToken].Name)
	assert.Equal(t, "USDCE", result[caseChangedToken].Symbol)
	assert.Equal(t, uint(6), result[caseChangedToken].Decimals)
}

func TestBatchUpdateTokenMetadata(t *testing.T) {
	Reset()
	assert.NoError(t, Get().Create([]model.TokenMetadata{
		{
			ChainID:         "ethereum",
			ContractAddress: "******************************************",
			Name:            util.Ptr("The Verge"),
			Symbol:          util.Ptr("VERGE"),
		},
		{
			ChainID:         "polygon-pos",
			ContractAddress: "******************************************",
			CoingeckoID:     util.Ptr("bridged-usdc-polygon-pos-bridge"),
			Name:            util.Ptr("USD Coin Bridged"),
			Symbol:          util.Ptr("USDCE"),
		},
	}).Error)
	assert.NoError(t, Get().Create([]model.DataContractMetadata{
		{
			ChainID:         "eth",
			ContractAddress: "******************************************",
			Name:            "The Verge",
			Symbol:          "VERGE",
			Decimals:        9,
		},
		{
			ChainID:            "matic",
			ContractAddress:    "******************************************",
			Name:               "USD Coin Bridged",
			Symbol:             "USDCE",
			ContractSchemaName: "ERC20",
			Decimals:           6,
		},
	}).Error)

	ctx := context.Background()
	repo := GormRepo()

	ethToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "******************************************"}
	polygonToken := domain.ChainToken{Chain: domain.Polygon, TokenID: "******************************************"}
	nonExistentToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: "non-existent-token"}

	metadatas := map[domain.ChainToken]*domain.TokenMetadata{
		ethToken:         {Name: "The Verge Updated", Symbol: "VERGE Updated", Decimals: 10}, // Will be updated
		nonExistentToken: {Name: "Non-existent Token", Symbol: "NT", Decimals: 18},           // Will not be created
		polygonToken:     {Decimals: 0},                                                      // Will not be updated
	}
	err := repo.BatchUpdateTokenMetadata(ctx, metadatas)
	assert.NoError(t, err)

	// Verify the results
	token1, err := repo.GetTokenMetadata(ctx, domain.Ethereum, "******************************************")
	assert.NoError(t, err)
	assert.Equal(t, "The Verge Updated", token1.Name)
	assert.Equal(t, "VERGE Updated", token1.Symbol)
	assert.Equal(t, uint(10), token1.Decimals)

	token2, err := repo.GetTokenMetadata(ctx, domain.Polygon, "******************************************")
	assert.NoError(t, err)
	assert.Equal(t, "USD Coin Bridged", token2.Name)
	assert.Equal(t, "USDCE", token2.Symbol)
	assert.Equal(t, uint(6), token2.Decimals)

	token3, err := repo.GetTokenMetadata(ctx, domain.Ethereum, "non-existent-token")
	assert.Error(t, err)
	assert.Nil(t, token3)
}
