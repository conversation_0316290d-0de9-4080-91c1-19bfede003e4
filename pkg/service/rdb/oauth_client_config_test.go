package rdb

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestApplications(t *testing.T) {
	Reset()
	ctx := context.Background()
	r := GormRepo()

	apps, err := r.GetAllOAuthApplications(ctx)
	assert.Nil(t, err)
	assert.Equal(t, 0, len(apps))

	err = r.UpsertOAuthApplication(ctx, 1, &domain.OAuthApplication{
		Application: domain.Application{
			ClientID:     "client_id",
			Name:         "name",
			ClientSecret: "123",
			Domain:       "domain",
		},
	})
	assert.Nil(t, err)

	err = r.UpsertCustomAuthApplication(ctx, 1, &domain.CustomAuthApplication{
		Application: domain.Application{
			ClientID:     "client_id_2",
			Name:         "name333",
			ClientSecret: "ffff",
			Domain:       "http://localhost:8040",
		},
		VerifierType: domain.CustomAuthVerifierTypeJwt,
		JwtVerifierParams: &domain.JwtVerifierParams{
			JwkURL:   "http://localhost:8080/jwk",
			Audience: "audience1",
			Issuer:   "issuer2",
			UIDField: "sub",
		},
	})
	assert.Nil(t, err)

	apps, err = r.GetAllOAuthApplications(ctx)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(apps))
	assert.Equal(t, "client_id", apps[0].ClientID)

	// get oauth: success and error cases
	app, err := r.GetOAuthApplication(ctx, "client_id")
	assert.Nil(t, err)
	assert.Equal(t, "client_id", app.ClientID)
	assert.Equal(t, "name", app.Name)
	assert.Equal(t, "123", app.ClientSecret)
	assert.Equal(t, "domain", app.Domain)

	app, err = r.GetOAuthApplication(ctx, "client_id_789")
	assert.NotNil(t, err)
	assert.Nil(t, app)

	app, err = r.GetOAuthApplication(ctx, "client_id_2")
	assert.NotNil(t, err)
	assert.Nil(t, app)

	err = r.UpsertOAuthApplication(ctx, 1, &domain.OAuthApplication{
		Application: domain.Application{
			ClientID:     "client_id",
			Name:         "name2",
			ClientSecret: "1234",
			Domain:       "domain2",
		},
	})
	assert.Nil(t, err)

	app, err = r.GetOAuthApplicationByDomain(ctx, "domain2")
	assert.Nil(t, err)
	assert.Equal(t, "client_id", app.ClientID)
	assert.Equal(t, "name2", app.Name)
	assert.Equal(t, "1234", app.ClientSecret)
	assert.Equal(t, "domain2", app.Domain)

	// get custom auth application
	customApp, err := r.GetCustomAuthApplication(ctx, "client_id_2")
	assert.Nil(t, err)
	assert.Equal(t, "client_id_2", customApp.ClientID)
	assert.Equal(t, "name333", customApp.Name)
	assert.Equal(t, "ffff", customApp.ClientSecret)
	assert.Equal(t, "http://localhost:8040", customApp.Domain)
	assert.Equal(t, domain.CustomAuthVerifierTypeJwt, customApp.VerifierType)
	assert.NotNil(t, customApp.JwtVerifierParams)
	assert.Equal(t, "http://localhost:8080/jwk", customApp.JwtVerifierParams.JwkURL)
	assert.Equal(t, "audience1", customApp.JwtVerifierParams.Audience)
	assert.Equal(t, "issuer2", customApp.JwtVerifierParams.Issuer)
	assert.Equal(t, "sub", customApp.JwtVerifierParams.UIDField)

	customApp, err = r.GetCustomAuthApplication(ctx, "client_id_789")
	assert.NotNil(t, err)
	assert.Nil(t, customApp)

	// error if updating custom auth application id with oauth application
	err = r.UpsertOAuthApplication(ctx, 1, &domain.OAuthApplication{
		Application: domain.Application{
			ClientID:     "client_id_2",
			Name:         "name2",
			ClientSecret: "1234",
			Domain:       "domain2",
		},
	})
	assert.Equal(t, domain.ErrNotOAuthApplication, err)

	// error if updating oauth application id with custom auth application
	err = r.UpsertCustomAuthApplication(ctx, 1, &domain.CustomAuthApplication{
		Application: domain.Application{
			ClientID:     "client_id",
			Name:         "name444",
			ClientSecret: "bbb",
			Domain:       "http://localhost:8040",
		},
		VerifierType: domain.CustomAuthVerifierTypeJwt,
		JwtVerifierParams: &domain.JwtVerifierParams{
			JwkURL:   "http://localhost:8080/jwk",
			Audience: "audience1",
			Issuer:   "issuer2",
			UIDField: "sub",
		},
	})
	assert.Equal(t, domain.ErrNotCustomAuthApplication, err)
}
