package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestUpdateAirdropEvent(t *testing.T) {
	Reset()

	ctx := context.Background()
	eventId := util.RandString(10)
	now := time.Now()
	_, err := CreateAirdropEvent(ctx, &model.AirdropEvent{
		EventID:            eventId,
		ChainID:            "matic",
		FromAddress:        "0x123",
		ContractAddress:    "0x456",
		MaxSupply:          100,
		TotalSupply:        0,
		StartTime:          now,
		EndTime:            now.AddDate(0, 0, 1),
		ContractSchemaName: model.SchemaERC721,
		Title:              util.Ptr("title_before"),
		SubTitle:           util.Ptr("subtitle_before"),
		FaviconURL:         util.Ptr("favicon_url_before"),
		MsgContent:         util.Ptr("msg_content_before"),
	})
	assert.Nil(t, err)

	beforeEvent := model.AirdropEvent{}
	Get().Select("title, subtitle, favicon_url, msg_content, start_time, end_time").Where("event_id = ?", eventId).Find(&beforeEvent)
	assert.Equal(t, "title_before", *beforeEvent.Title)
	assert.Equal(t, "subtitle_before", *beforeEvent.SubTitle)
	assert.Equal(t, "favicon_url_before", *beforeEvent.FaviconURL)
	assert.Equal(t, "msg_content_before", *beforeEvent.MsgContent)
	assert.NotEmpty(t, beforeEvent.StartTime)
	assert.NotEmpty(t, beforeEvent.EndTime)

	err = UpdateAirdropEvent(ctx, &UpdateAirdropEventParams{
		EventID:    eventId,
		Title:      util.Ptr("title_after"),
		SubTitle:   util.Ptr("subtitle_after"),
		FaviconURL: util.Ptr("favicon_url_after"),
		MsgContent: util.Ptr("msg_content_after"),
		StartTime:  util.Ptr(now.AddDate(0, 0, 2)),
		EndTime:    util.Ptr(now.AddDate(0, 0, 3)),
	})
	assert.Nil(t, err)
	afterEvent := model.AirdropEvent{}
	Get().Select("title, subtitle, favicon_url, msg_content, start_time, end_time").Where("event_id = ?", eventId).Find(&afterEvent)
	assert.Equal(t, "title_after", *afterEvent.Title)
	assert.Equal(t, "subtitle_after", *afterEvent.SubTitle)
	assert.Equal(t, "favicon_url_after", *afterEvent.FaviconURL)
	assert.Equal(t, "msg_content_after", *afterEvent.MsgContent)
	assert.NotEqual(t, beforeEvent.StartTime, afterEvent.StartTime)
	assert.NotEqual(t, beforeEvent.EndTime, afterEvent.EndTime)
}
