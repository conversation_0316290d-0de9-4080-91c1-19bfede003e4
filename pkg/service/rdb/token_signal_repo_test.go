package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestTokenSignalRepo(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Test UpsertBuySignal
	t.Run("UpsertBuySignal", func(t *testing.T) {
		// Create a new buy signal
		buySignal := &domain.TokenBuySignal{
			TokenAddress:     "token123",
			SmartWalletCount: 10,
			BuyEntryPrice:    0.001,
			HighestPrice:     0.002,
			EmitTime:         time.Now(),
			TelegramLink:     "https://t.me/example",
			WinRate:          0.75,
			AverageHolding:   48.5,
			AverageWinRate:   0.65,
		}

		// Upsert the buy signal
		err := repo.UpsertBuySignal(ctx, buySignal)
		assert.NoError(t, err)

		// Get the buy signal
		result, err := repo.GetBuySignalByTokenAddress(ctx, buySignal.TokenAddress)
		assert.NoError(t, err)
		assert.Equal(t, buySignal.TokenAddress, result.TokenAddress)
		assert.Equal(t, buySignal.SmartWalletCount, result.SmartWalletCount)
		assert.Equal(t, buySignal.BuyEntryPrice, result.BuyEntryPrice)
		assert.Equal(t, buySignal.HighestPrice, result.HighestPrice)
		assert.Equal(t, buySignal.TelegramLink, result.TelegramLink)
		assert.Equal(t, buySignal.WinRate, result.WinRate)
		assert.Equal(t, buySignal.AverageHolding, result.AverageHolding)
		assert.Equal(t, buySignal.AverageWinRate, result.AverageWinRate)

		// Update the buy signal
		updatedBuySignal := &domain.TokenBuySignal{
			TokenAddress:     "token123",
			SmartWalletCount: 20,
			BuyEntryPrice:    0.002,
			HighestPrice:     0.003,
			EmitTime:         time.Now(),
			TelegramLink:     "https://t.me/example2",
			WinRate:          0.85,
			AverageHolding:   72.3,
			AverageWinRate:   0.78,
		}

		// Upsert the updated buy signal
		err = repo.UpsertBuySignal(ctx, updatedBuySignal)
		assert.NoError(t, err)

		// Get the updated buy signal
		result, err = repo.GetBuySignalByTokenAddress(ctx, updatedBuySignal.TokenAddress)
		assert.NoError(t, err)
		assert.Equal(t, updatedBuySignal.TokenAddress, result.TokenAddress)
		assert.Equal(t, updatedBuySignal.SmartWalletCount, result.SmartWalletCount)
		assert.Equal(t, updatedBuySignal.BuyEntryPrice, result.BuyEntryPrice)
		assert.Equal(t, updatedBuySignal.HighestPrice, result.HighestPrice)
		assert.Equal(t, updatedBuySignal.TelegramLink, result.TelegramLink)
		assert.Equal(t, updatedBuySignal.WinRate, result.WinRate)
		assert.Equal(t, updatedBuySignal.AverageHolding, result.AverageHolding)
		assert.Equal(t, updatedBuySignal.AverageWinRate, result.AverageWinRate)
	})

	// Test UpsertBuySignalBatch
	t.Run("UpsertBuySignalBatch", func(t *testing.T) {
		// Create multiple buy signals
		buySignals := []*domain.TokenBuySignal{
			{
				TokenAddress:     "batch-token1",
				SmartWalletCount: 10,
				BuyEntryPrice:    0.001,
				HighestPrice:     0.002,
				EmitTime:         time.Now(),
				TelegramLink:     "https://t.me/example-batch1",
				WinRate:          0.75,
				AverageHolding:   48.5,
				AverageWinRate:   0.65,
			},
			{
				TokenAddress:     "batch-token2",
				SmartWalletCount: 15,
				BuyEntryPrice:    0.003,
				HighestPrice:     0.005,
				EmitTime:         time.Now(),
				TelegramLink:     "https://t.me/example-batch2",
				WinRate:          0.80,
				AverageHolding:   52.0,
				AverageWinRate:   0.70,
			},
			{
				TokenAddress:     "batch-token3",
				SmartWalletCount: 20,
				BuyEntryPrice:    0.005,
				HighestPrice:     0.008,
				EmitTime:         time.Now(),
				TelegramLink:     "https://t.me/example-batch3",
				WinRate:          0.85,
				AverageHolding:   60.0,
				AverageWinRate:   0.75,
			},
		}

		// Upsert batch of buy signals
		err := repo.UpsertBuySignalBatch(ctx, buySignals)
		assert.NoError(t, err)

		// Verify each signal was created correctly
		for _, signal := range buySignals {
			result, err := repo.GetBuySignalByTokenAddress(ctx, signal.TokenAddress)
			assert.NoError(t, err)
			assert.Equal(t, signal.TokenAddress, result.TokenAddress)
			assert.Equal(t, signal.SmartWalletCount, result.SmartWalletCount)
			assert.Equal(t, signal.BuyEntryPrice, result.BuyEntryPrice)
			assert.Equal(t, signal.HighestPrice, result.HighestPrice)
			assert.Equal(t, signal.TelegramLink, result.TelegramLink)
			assert.Equal(t, signal.WinRate, result.WinRate)
			assert.Equal(t, signal.AverageHolding, result.AverageHolding)
			assert.Equal(t, signal.AverageWinRate, result.AverageWinRate)
		}

		// Test updating existing signals in batch
		updatedSignals := []*domain.TokenBuySignal{
			{
				TokenAddress:     "batch-token1", // existing
				SmartWalletCount: 25,             // updated
				BuyEntryPrice:    0.002,          // updated
				HighestPrice:     0.004,          // updated
				EmitTime:         time.Now(),
				TelegramLink:     "https://t.me/example-batch1-updated", // updated
				WinRate:          0.90,                                  // updated
				AverageHolding:   70.0,                                  // updated
				AverageWinRate:   0.85,                                  // updated
			},
			{
				TokenAddress:     "batch-token4", // new
				SmartWalletCount: 30,
				BuyEntryPrice:    0.010,
				HighestPrice:     0.015,
				EmitTime:         time.Now(),
				TelegramLink:     "https://t.me/example-batch4",
				WinRate:          0.95,
				AverageHolding:   80.0,
				AverageWinRate:   0.90,
			},
		}

		// Upsert batch with mixed new and updated signals
		err = repo.UpsertBuySignalBatch(ctx, updatedSignals)
		assert.NoError(t, err)

		// Verify the updated signal
		result, err := repo.GetBuySignalByTokenAddress(ctx, "batch-token1")
		assert.NoError(t, err)
		assert.Equal(t, 25, result.SmartWalletCount) // Verify it was updated
		assert.Equal(t, 0.002, result.BuyEntryPrice)
		assert.Equal(t, "https://t.me/example-batch1-updated", result.TelegramLink)

		// Verify the new signal
		result, err = repo.GetBuySignalByTokenAddress(ctx, "batch-token4")
		assert.NoError(t, err)
		assert.Equal(t, "batch-token4", result.TokenAddress)
		assert.Equal(t, 30, result.SmartWalletCount)

		// Test empty batch (should not error)
		err = repo.UpsertBuySignalBatch(ctx, []*domain.TokenBuySignal{})
		assert.NoError(t, err)
	})

	// Test ListBuySignals
	t.Run("ListBuySignals", func(t *testing.T) {
		// Create another buy signal
		buySignal2 := &domain.TokenBuySignal{
			TokenAddress:     "token456",
			SmartWalletCount: 15,
			BuyEntryPrice:    0.003,
			HighestPrice:     0.005,
			EmitTime:         time.Now(),
			TelegramLink:     "https://t.me/example3",
			WinRate:          0.65,
			AverageHolding:   36.7,
			AverageWinRate:   0.55,
		}

		// Upsert the second buy signal
		err := repo.UpsertBuySignal(ctx, buySignal2)
		assert.NoError(t, err)

		// List buy signals
		signals, err := repo.ListBuySignals(ctx)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(signals), 2)

		// Check if both signals are in the list
		found1 := false
		found2 := false
		for _, signal := range signals {
			if signal.TokenAddress == "token123" {
				found1 = true
			}
			if signal.TokenAddress == "token456" {
				found2 = true
			}
		}
		assert.True(t, found1)
		assert.True(t, found2)

		// Verify signals are ordered by emit_time desc
		if len(signals) >= 2 {
			assert.True(t, signals[0].EmitTime.After(signals[1].EmitTime) ||
				signals[0].EmitTime.Equal(signals[1].EmitTime),
				"Signals should be ordered by emit_time desc")
		}
	})

	// Test AddSellSignal
	t.Run("AddSellSignal", func(t *testing.T) {
		// Create a buy signal first so we have a valid BuyEntryTime reference
		buySignal := &domain.TokenBuySignal{
			TokenAddress:     "token789",
			SmartWalletCount: 10,
			BuyEntryPrice:    0.001,
			HighestPrice:     0.002,
			EmitTime:         time.Now().Add(-24 * time.Hour).Truncate(time.Second), // 1 day ago
			TelegramLink:     "https://t.me/example-buy",
		}

		// Insert the buy signal
		err := repo.UpsertBuySignal(ctx, buySignal)
		assert.NoError(t, err)

		// Create a sell signal with BuyEntryTime
		sellSignalWithBuyTime := &domain.TokenSellSignal{
			TokenAddress: "token789",
			EmitTime:     time.Now(),
			TelegramLink: "https://t.me/example4",
			HighestGain:  0.5,
			BuyEntryTime: &buySignal.EmitTime,
		}

		// Add the sell signal
		err = repo.AddSellSignal(ctx, sellSignalWithBuyTime)
		assert.NoError(t, err)

		// List sell signals
		signals, err := repo.ListSellSignals(ctx, 10)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, len(signals), 1)

		// Check if the sell signal is in the list with correct BuyEntryTime
		found := false
		for _, signal := range signals {
			if signal.TokenAddress == "token789" {
				found = true
				assert.Equal(t, sellSignalWithBuyTime.TelegramLink, signal.TelegramLink)
				assert.Equal(t, sellSignalWithBuyTime.HighestGain, signal.HighestGain)
				assert.NotNil(t, signal.BuyEntryTime)
				assert.Equal(t, buySignal.EmitTime.Unix(), signal.BuyEntryTime.Unix())
			}
		}
		assert.True(t, found)

		// Test a sell signal with nil BuyEntryTime
		sellSignalNilBuyTime := &domain.TokenSellSignal{
			TokenAddress: "token-nil-buy-time",
			EmitTime:     time.Now(),
			TelegramLink: "https://t.me/example-nil",
			HighestGain:  0.8,
			BuyEntryTime: nil,
		}

		// Add the sell signal with nil BuyEntryTime
		err = repo.AddSellSignal(ctx, sellSignalNilBuyTime)
		assert.NoError(t, err)

		// List sell signals again
		signals, err = repo.ListSellSignals(ctx, 10)
		assert.NoError(t, err)

		// Check if the sell signal with nil BuyEntryTime is in the list
		found = false
		for _, signal := range signals {
			if signal.TokenAddress == "token-nil-buy-time" {
				found = true
				assert.Equal(t, sellSignalNilBuyTime.TelegramLink, signal.TelegramLink)
				assert.Equal(t, sellSignalNilBuyTime.HighestGain, signal.HighestGain)
				assert.Nil(t, signal.BuyEntryTime)
			}
		}
		assert.True(t, found)
	})

	// Test AddSellSignal with duplicate token address
	t.Run("AddSellSignal_DuplicateTokenAddress", func(t *testing.T) {
		// Create an initial sell signal
		initialTime := time.Now().Add(-48 * time.Hour).Truncate(time.Second)
		initialSellSignal := &domain.TokenSellSignal{
			TokenAddress: "token-duplicate-test",
			EmitTime:     initialTime,
			TelegramLink: "https://t.me/example-duplicate-1",
			HighestGain:  0.5,
			BuyEntryTime: nil,
		}

		// Add the initial sell signal
		err := repo.AddSellSignal(ctx, initialSellSignal)
		assert.NoError(t, err)

		// Get the initial signal to verify it was added correctly
		signals, err := repo.ListSellSignals(ctx, 10)
		assert.NoError(t, err)
		var initialSignal *domain.TokenSellSignal
		for _, s := range signals {
			if s.TokenAddress == "token-duplicate-test" {
				initialSignal = s
				break
			}
		}
		assert.NotNil(t, initialSignal)
		assert.Equal(t, 0.5, initialSignal.HighestGain)
		assert.Equal(t, initialTime.Unix(), initialSignal.EmitTime.Unix())
		assert.Equal(t, "https://t.me/example-duplicate-1", initialSignal.TelegramLink)

		// Create a second sell signal with the same token address but higher gain
		updatedTime := time.Now().Truncate(time.Second)
		updatedSellSignal := &domain.TokenSellSignal{
			TokenAddress: "token-duplicate-test",
			EmitTime:     updatedTime,
			TelegramLink: "https://t.me/example-duplicate-2", // Different from initial
			HighestGain:  1.5,                                // Higher than initial
			BuyEntryTime: nil,
		}

		// Add the updated sell signal
		err = repo.AddSellSignal(ctx, updatedSellSignal)
		assert.NoError(t, err)

		// Get the signals again
		signals, err = repo.ListSellSignals(ctx, 10)
		assert.NoError(t, err)
		var updatedSignal *domain.TokenSellSignal
		for _, s := range signals {
			if s.TokenAddress == "token-duplicate-test" {
				updatedSignal = s
				break
			}
		}

		// Verify only highest_gain was updated, other fields remained the same
		assert.NotNil(t, updatedSignal)
		assert.Equal(t, 1.5, updatedSignal.HighestGain)                                 // Should be updated
		assert.Equal(t, initialTime.Unix(), updatedSignal.EmitTime.Unix())              // Should NOT be updated
		assert.Equal(t, "https://t.me/example-duplicate-1", updatedSignal.TelegramLink) // Should NOT be updated
	})

	// Test DeleteBuySignalByTokenAddress
	t.Run("DeleteBuySignalByTokenAddress", func(t *testing.T) {
		// Delete a buy signal
		err := repo.DeleteBuySignalByTokenAddress(ctx, "token123")
		assert.NoError(t, err)

		// Try to get the deleted buy signal
		_, err = repo.GetBuySignalByTokenAddress(ctx, "token123")
		assert.Error(t, err)
		assert.Equal(t, domain.ErrRecordNotFound, err)
	})

	// Test DeleteSellSignalByTokenAddress
	t.Run("DeleteSellSignalByTokenAddress", func(t *testing.T) {
		// Delete a sell signal
		err := repo.DeleteSellSignalByTokenAddress(ctx, "token789")
		assert.NoError(t, err)

		// List sell signals
		signals, err := repo.ListSellSignals(ctx, 10)
		assert.NoError(t, err)

		// Check if the sell signal is not in the list
		for _, signal := range signals {
			assert.NotEqual(t, "token789", signal.TokenAddress)
		}
	})
}

func TestGetPastTopSignals(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Create test data
	now := time.Now()
	signals := []*model.TokenSellSignal{
		{
			TokenAddress: "0x1",
			EmitTime:     now.Add(-24 * time.Hour),
			HighestGain:  2.5,
		},
		{
			TokenAddress: "0x2",
			EmitTime:     now.Add(-48 * time.Hour),
			HighestGain:  1.8,
		},
		{
			TokenAddress: "0x3",
			EmitTime:     now.Add(-72 * time.Hour),
			HighestGain:  0.5, // Should not be included as gain < 1.0
		},
	}

	for _, signal := range signals {
		err := repo.AddSellSignal(ctx, signal.ToDomain())
		require.NoError(t, err)
	}

	// Test the function
	topSignals, err := repo.GetPastTopSignals(ctx)
	require.NoError(t, err)
	assert.Len(t, topSignals, 2) // Only 2 signals with gain >= 1.0

	// Verify the signals are ordered by emit time
	assert.Equal(t, "0x1", topSignals[0].TokenAddress)
	assert.Equal(t, "0x2", topSignals[1].TokenAddress)
}

func TestGetLast7d2xStats(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Create test data for sell signals
	now := time.Now()
	sellSignals := []*model.TokenSellSignal{
		{
			TokenAddress: "0x1",
			EmitTime:     now.Add(-24 * time.Hour),
			HighestGain:  2.0,
		},
		{
			TokenAddress: "0x2",
			EmitTime:     now.Add(-48 * time.Hour),
			HighestGain:  1.5,
		},
		{
			TokenAddress: "0x3",
			EmitTime:     now.Add(-8 * 24 * time.Hour), // Outside 7 days
			HighestGain:  3.0,
		},
	}

	for _, signal := range sellSignals {
		err := repo.AddSellSignal(ctx, signal.ToDomain())
		require.NoError(t, err)
	}

	// Create test data for buy signals
	buySignals := []*domain.TokenBuySignal{
		{
			TokenAddress:  "0x4",
			EmitTime:      now.Add(-24 * time.Hour),
			BuyEntryPrice: 1.0,
			HighestPrice:  3.0, // gain = 3.0/1.0 - 1 = 2.0
		},
		{
			TokenAddress:  "0x5",
			EmitTime:      now.Add(-72 * time.Hour),
			BuyEntryPrice: 2.0,
			HighestPrice:  3.0, // gain = 3.0/2.0 - 1 = 0.5 (should not count as 2x)
		},
		{
			TokenAddress:  "0x6",
			EmitTime:      now.Add(-10 * 24 * time.Hour), // Outside 7 days
			BuyEntryPrice: 1.0,
			HighestPrice:  4.0, // gain = 4.0/1.0 - 1 = 3.0 (outside time range)
		},
	}

	for _, signal := range buySignals {
		err := repo.UpsertBuySignal(ctx, signal)
		require.NoError(t, err)
	}

	// Test the function
	totalCount, twoXCount, avgGain, err := repo.GetLast7d2xStats(ctx)
	require.NoError(t, err)

	// Expected results:
	// Sell signals within 7 days: 2 (both with gain >= 1.0) - sumGain = 2.0 + 1.5 = 3.5
	// Buy signals within 7 days: 2 (only 1 with gain >= 1.0) - sumGain = 2.0
	// Total signals: 4, 2x count: 3, avgGain = (3.5 + 2.0) / 3 = 1.83

	assert.Equal(t, 4, totalCount)         // 2 sell signals + 2 buy signals within 7 days
	assert.Equal(t, 3, twoXCount)          // 2 sell signals + 1 buy signal with gain >= 1.0
	assert.InDelta(t, 1.83, avgGain, 0.01) // (2.0 + 1.5 + 2.0) / 3 ≈ 1.83
}

func TestGetTodayTopSignal(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Create test data
	now := time.Now()
	signals := []*domain.TokenSellSignal{
		{
			TokenAddress: "0x1",
			EmitTime:     now.Add(-1 * time.Hour),
			HighestGain:  2.5,
		},
		{
			TokenAddress: "0x2",
			EmitTime:     now.Add(-2 * time.Hour),
			HighestGain:  1.5,
		},
		{
			TokenAddress: "0x3",
			EmitTime:     now.Add(-3 * time.Hour),
			HighestGain:  3.0,
		},
	}

	// Create buy signals with different ratios
	buySignals := []*domain.TokenBuySignal{
		{
			TokenAddress:  "0x4",
			EmitTime:      now.Add(-1 * time.Hour),
			BuyEntryPrice: 1.0,
			HighestPrice:  2.0, // ratio = 2.0
		},
		{
			TokenAddress:  "0x5",
			EmitTime:      now.Add(-2 * time.Hour),
			BuyEntryPrice: 0.5,
			HighestPrice:  2.0, // ratio = 4.0
		},
		{
			TokenAddress:  "0x6",
			EmitTime:      now.Add(-3 * time.Hour),
			BuyEntryPrice: 0.2,
			HighestPrice:  1.0, // ratio = 5.0
		},
	}

	// Insert test data
	for _, signal := range signals {
		err := repo.AddSellSignal(ctx, signal)
		require.NoError(t, err)
	}

	for _, signal := range buySignals {
		err := repo.UpsertBuySignal(ctx, signal)
		require.NoError(t, err)
	}

	// Get today's top signal
	sellSignal, buySignal, err := repo.GetTodayTopSignal(ctx)
	require.NoError(t, err)
	assert.NotNil(t, sellSignal)
	assert.NotNil(t, buySignal)
	assert.Equal(t, "0x3", sellSignal.TokenAddress)
	assert.Equal(t, 3.0, sellSignal.HighestGain)
	assert.Equal(t, "0x6", buySignal.TokenAddress) // Should be the one with highest ratio (5.0)
	assert.Equal(t, 0.2, buySignal.BuyEntryPrice)
	assert.Equal(t, 1.0, buySignal.HighestPrice)
}

func TestGetTodayTopSignal_NoSignals(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Get today's top signal when no signals exist
	sellSignal, buySignal, err := repo.GetTodayTopSignal(ctx)
	require.NoError(t, err)
	assert.Nil(t, sellSignal)
	assert.Nil(t, buySignal)
}

// TestAddSellSignalWithDuplicateTokenAddress tests that AddSellSignal updates
// only the highest_gain field when a record with the same token_address already exists
func TestAddSellSignalWithDuplicateTokenAddress(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Create an initial sell signal
	initialTime := time.Now().Add(-48 * time.Hour).Truncate(time.Second)
	initialSellSignal := &domain.TokenSellSignal{
		TokenAddress: "token-duplicate-test",
		EmitTime:     initialTime,
		TelegramLink: "https://t.me/example-duplicate-1",
		HighestGain:  0.5,
		BuyEntryTime: nil,
	}

	// Add the initial sell signal
	err := repo.AddSellSignal(ctx, initialSellSignal)
	assert.NoError(t, err)

	// Create a second sell signal with the same token address but higher gain
	updatedTime := time.Now().Truncate(time.Second)
	updatedSellSignal := &domain.TokenSellSignal{
		TokenAddress: "token-duplicate-test",             // Same address
		EmitTime:     updatedTime,                        // Different time
		TelegramLink: "https://t.me/example-duplicate-2", // Different link
		HighestGain:  1.5,                                // Higher gain
		BuyEntryTime: nil,
	}

	// Add the updated sell signal, which should update only the highest_gain
	err = repo.AddSellSignal(ctx, updatedSellSignal)
	assert.NoError(t, err)

	// List sell signals to find our test signal
	signals, err := repo.ListSellSignals(ctx, 10)
	assert.NoError(t, err)

	// Find our test signal
	var foundSignal *domain.TokenSellSignal
	for _, s := range signals {
		if s.TokenAddress == "token-duplicate-test" {
			foundSignal = s
			break
		}
	}

	// Verify that only the highest_gain was updated, other fields remained the same
	assert.NotNil(t, foundSignal)
	assert.Equal(t, 1.5, foundSignal.HighestGain)                                 // Should be updated
	assert.Equal(t, initialTime.Unix(), foundSignal.EmitTime.Unix())              // Should NOT be updated
	assert.Equal(t, "https://t.me/example-duplicate-1", foundSignal.TelegramLink) // Should NOT be updated
}
