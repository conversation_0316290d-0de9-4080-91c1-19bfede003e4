package rdb

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"

	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/stretchr/testify/assert"
)

var mockWalletConfig = &model.WalletConfig{
	AllRpcs:        util.Ptr(model.StringArray{"ethereum", "polygon"}),
	ShowNFTSellBtn: util.Ptr(true),
	SupportedLocales: &model.SupportedLocales{
		{
			LanguageCode: util.Ptr("zh"),
			CountryCode:  util.Ptr("TW"),
		},
	},
	CustomTokens: &model.CustomTokens{
		{
			ChainID:         util.Ptr("ethereum"),
			ContractAddress: util.Ptr("******************************************"),
		},
	},
	AppStoreInfo: &model.AppInfoMap{
		IOS: &model.AppInfo{
			Name: util.Ptr("KryptoGO"),
		},
	},
}

var mockWalletTheme = &model.WalletTheme{
	PrimaryColor:   util.Ptr("#FFFFFF"),
	SecondaryColor: util.Ptr("#000000"),
}

var mockWalletProject = &model.StudioWalletProject{
	Organization: "KryptoGO",
	Status:       "publish",
	ProjectName:  "KryptoGO Yacht Club",
	ProjectImage: util.Ptr("https://wallet-static.kryptogo.com/public/assets/images/NFT_KGYC.gif"),
	Version:      util.Ptr("1.0.0"),
	Config:       mockWalletConfig,
	Theme:        mockWalletTheme,
}

func TestCreateNewProject(t *testing.T) {
	ctx := context.Background()
	Reset()

	// Call the UpsertStudioWalletProject method with a new project
	// Make sure to provide a mock database context for testing
	projectID, err := UpsertStudioWalletProject(ctx, mockWalletProject)

	// Assert that the returned projectID is greater than 0 and there's no error
	assert.NoError(t, err)
	assert.NotEqual(t, 0, projectID)

	// Check if the project was inserted correctly into the database
	projects, _, err := GetStudioWalletProjects(ctx, GetWalletProjectsParams{
		Organization: "KryptoGO",
	})
	assert.NoError(t, err)
	assert.NotNil(t, projects)
	assert.Equal(t, 1, len(*projects))
	assert.Equal(t, mockWalletProject.Organization, (*projects)[0].Organization)
	assert.Equal(t, mockWalletProject.Status, (*projects)[0].Status)
	assert.Equal(t, mockWalletProject.ProjectName, (*projects)[0].ProjectName)
	assert.Equal(t, *mockWalletProject.ProjectImage, *(*projects)[0].ProjectImage)
	assert.Equal(t, *mockWalletProject.Version, *(*projects)[0].Version)
	// config
	assert.NotNil(t, (*projects)[0].Config)
	assert.NotNil(t, (*projects)[0].Config.AllRpcs)
	assert.Equal(t, *mockWalletProject.Config.AllRpcs, *(*projects)[0].Config.AllRpcs)
	assert.NotNil(t, (*projects)[0].Config.ShowNFTSellBtn)
	assert.Equal(t, *mockWalletProject.Config.ShowNFTSellBtn, *(*projects)[0].Config.ShowNFTSellBtn)
	assert.NotNil(t, (*projects)[0].Config.SupportedLocales)
	assert.Equal(t, *mockWalletProject.Config.SupportedLocales, *(*projects)[0].Config.SupportedLocales)
	assert.NotNil(t, (*projects)[0].Config.CustomTokens)
	assert.Equal(t, *mockWalletProject.Config.CustomTokens, *(*projects)[0].Config.CustomTokens)
	assert.NotNil(t, (*projects)[0].Config.AppStoreInfo)
	assert.Equal(t, *mockWalletProject.Config.AppStoreInfo, *(*projects)[0].Config.AppStoreInfo)
	// theme
	assert.NotNil(t, (*projects)[0].Theme)
	assert.NotNil(t, (*projects)[0].Theme.PrimaryColor)
	assert.Equal(t, *mockWalletProject.Theme.PrimaryColor, *(*projects)[0].Theme.PrimaryColor)

}

func TestUpdateExistingProject(t *testing.T) {
	ctx := context.Background()

	Reset()
	assert.NoError(t, rdbtest.CreateStudioWalletProjects(Get()))

	// Update some fields in the mockWalletProject to simulate an update
	updatedProject := rdbtest.WalletProjects[0]
	updatedProject.ProjectName = "Updated Project Name"
	updatedProject.Config.ShowNFTSellBtn = util.Ptr(false)

	projectID, err := UpsertStudioWalletProject(ctx, &updatedProject)
	assert.NoError(t, err)
	assert.Equal(t, updatedProject.ProjectID, projectID)

	// Check if the project was inserted correctly into the database
	projects, _, err := GetStudioWalletProjects(ctx, GetWalletProjectsParams{
		Organization: "KryptoGO",
	})
	assert.NoError(t, err)
	assert.NotNil(t, projects)
	assert.Equal(t, 1, len(*projects))
	assert.Equal(t, updatedProject.ProjectName, (*projects)[0].ProjectName)
	assert.Equal(t, *updatedProject.Config.ShowNFTSellBtn, *(*projects)[0].Config.ShowNFTSellBtn)
}
