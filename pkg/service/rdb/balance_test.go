package rdb

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/stretchr/testify/assert"
)

func TestGetAssetBalances(t *testing.T) {
	// Reset the database to a fresh state
	Reset()

	// Create seed data
	assert.Nil(t, dbtest.CreateAssets(Get()))
	assert.Nil(t, dbtest.CreateAssetPrices(Get()))

	// Define test cases
	testCases := []struct {
		name             string
		chains           []domain.Chain
		addresses        []domain.Address
		types            []domain.AssetType
		expectedBalances map[domain.ChainAddress]map[domain.AssetType]float64
	}{
		{
			name:      "Ethereum and Arbitrum",
			chains:    []domain.Chain{domain.Ethereum, domain.Arbitrum},
			addresses: []domain.Address{domain.NewEvmAddress("******************************************")},
			types:     []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			expectedBalances: map[domain.ChainAddress]map[domain.AssetType]float64{
				{
					Chain:   domain.Ethereum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					// eth: 200 * 1000000, ******************************************: 175000 * 0.01
					domain.AssetTypeToken: 200000000 + 1750,
					// a-random-nft: 3 * 54.87
					domain.AssetTypeNft: 164.61,
					// a-seed-defi-asset-0456: 512.33
					domain.AssetTypeDefi: 512.33,
				},
				{
					Chain:   domain.Arbitrum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					// a-seed-defi-asset-0123: 1249.327
					domain.AssetTypeDefi: 1249.327,
				},
			},
		},
		{
			name:   "Tron",
			chains: []domain.Chain{domain.Tron},
			addresses: []domain.Address{
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
				domain.NewEvmAddress("******************************************"), // Should not matter
			},
			types: []domain.AssetType{domain.AssetTypeToken},
			expectedBalances: map[domain.ChainAddress]map[domain.AssetType]float64{
				{
					Chain:   domain.Tron,
					Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
				}: {
					// TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8: 500 * 1000000
					domain.AssetTypeToken: 500000000,
				},
			},
		},
		{
			name:   "Arbitrum and Tron",
			chains: []domain.Chain{domain.Arbitrum, domain.Tron},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			types: []domain.AssetType{domain.AssetTypeDefi, domain.AssetTypeToken},
			expectedBalances: map[domain.ChainAddress]map[domain.AssetType]float64{
				{
					Chain:   domain.Arbitrum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					// a-seed-defi-asset-0123: 1249.327
					domain.AssetTypeDefi: 1249.327,
				},
				{
					Chain:   domain.Tron,
					Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
				}: {
					// TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8: 500 * 1000000
					domain.AssetTypeToken: 500000000,
				},
			},
		},
		{
			name:   "All chains and addresses",
			chains: []domain.Chain{domain.Ethereum, domain.BNBChain, domain.Tron, domain.Arbitrum},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			types: []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			expectedBalances: map[domain.ChainAddress]map[domain.AssetType]float64{
				{
					Chain:   domain.Ethereum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					// eth: 200 * 1000000, ******************************************: 175000 * 0.01
					domain.AssetTypeToken: 200000000 + 1750,
					// a-random-nft: 3 * 54.87
					domain.AssetTypeNft: 164.61,
					// a-seed-defi-asset-0456: 512.33
					domain.AssetTypeDefi: 512.33,
				},
				{
					Chain:   domain.Tron,
					Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
				}: {
					// TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8: 500 * 1000000
					domain.AssetTypeToken: 500000000,
				},
				{
					Chain:   domain.Arbitrum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					// a-seed-defi-asset-0123: 1249.327
					domain.AssetTypeDefi: 1249.327,
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Call the method to test
			balances, err := GormRepo().GetAssetBalances(context.Background(), tc.chains, tc.addresses, tc.types)

			// Assertions
			assert.NoError(t, err)
			assert.Equal(t, tc.expectedBalances, balances)
		})
	}
}

func TestGetAssetsTotalUsdValue(t *testing.T) {
	// Reset the database to a fresh state
	Reset()

	// Create seed data
	assert.Nil(t, dbtest.CreateAssets(Get()))
	assert.Nil(t, dbtest.CreateAssetPrices(Get()))

	// Define test cases
	testCases := []struct {
		name               string
		chains             []domain.Chain
		addresses          []domain.Address
		types              []domain.AssetType
		expectedTotalValue float64
	}{
		{
			name:               "Ethereum and Arbitrum",
			chains:             []domain.Chain{domain.Ethereum, domain.Arbitrum},
			addresses:          []domain.Address{domain.NewEvmAddress("******************************************")},
			types:              []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			expectedTotalValue: 200000000 + 1750 + 164.61 + 512.33 + 1249.327,
		},
		{
			name:   "Tron",
			chains: []domain.Chain{domain.Tron},
			addresses: []domain.Address{
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
				domain.NewEvmAddress("******************************************"), // Should not matter
			},
			types:              []domain.AssetType{domain.AssetTypeToken},
			expectedTotalValue: 500000000,
		},
		{
			name:   "Arbitrum and Tron",
			chains: []domain.Chain{domain.Arbitrum, domain.Tron},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			types:              []domain.AssetType{domain.AssetTypeDefi, domain.AssetTypeToken},
			expectedTotalValue: 1249.327 + 500000000,
		},
		{
			name:   "All chains and addresses",
			chains: []domain.Chain{domain.Ethereum, domain.BNBChain, domain.Tron, domain.Arbitrum},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			types:              []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeNft, domain.AssetTypeDefi},
			expectedTotalValue: 200000000 + 1750 + 164.61 + 512.33 + 500000000 + 1249.327,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Call the method to test
			totalUsdValue, err := GormRepo().GetAssetsTotalUsdValue(context.Background(), tc.chains, tc.addresses, tc.types)

			// Assertions
			assert.NoError(t, err)
			if tc.expectedTotalValue == 0 {
				assert.InDelta(t, tc.expectedTotalValue, totalUsdValue, 0.00001)
			} else {
				assert.InEpsilon(t, tc.expectedTotalValue, totalUsdValue, 0.00001)
			}
			assert.InEpsilon(t, tc.expectedTotalValue, totalUsdValue, 0.0001)
		})
	}
}
