package rdb_test

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/suite"
)

type studioRoleSuite struct {
	suite.Suite
	ctx context.Context
}

func (s *studioRoleSuite) SetupSuite() {
	s.ctx = context.Background()
	rdb.Reset()
	s.Nil(rdbtest.CreateStudioDefault(rdb.Get()))
}

func (s *studioRoleSuite) TestGetRoleBindingsNotExist() {
	{
		studioRoleBindings, err := rdb.GormRepo().GetRoleBindings(
			s.ctx, domain.GetRoleBindingsRequest{
				OrgID: 999,
				UID:   "55688",
			},
		)
		s.Nil(err)

		s.Len(studioRoleBindings, 0)
	}
	{
		studioRoleBindings, err := rdb.GormRepo().GetRoleBindings(
			s.ctx, domain.GetRoleBindingsRequest{
				UID: "55688",
			},
		)
		s.Nil(err)

		s.Len(studioRoleBindings, 0)
	}
	{
		studioRoleBindings, err := rdb.GormRepo().GetRoleBindings(
			s.ctx, domain.GetRoleBindingsRequest{
				OrgID: 999,
			},
		)
		s.Nil(err)

		s.Len(studioRoleBindings, 0)
	}
	{
		studioRoleBindings, err := rdb.GormRepo().GetRoleBindings(
			s.ctx, domain.GetRoleBindingsRequest{
				Roles: []domain.StudioRole{
					{Module: "not_exist", Name: "not_exist"},
				},
			},
		)
		s.Nil(err)

		s.Len(studioRoleBindings, 0)
	}
}

func (s *studioRoleSuite) TestGetRoleBindingsWithoutReq() {
	var count int64
	db := rdb.Get()

	// count all users which are active
	s.NoError(db.Model(&model.StudioRoleBinding{}).
		Where("(uid, organization_id) IN (?)",
			db.Model(&model.StudioUser{}).
				Select("uid, organization_id").
				Where("status = ?", model.StudioUserStatusActive),
		).Count(&count).Error)

	studioRoleBindings, err := rdb.GormRepo().GetRoleBindings(
		s.ctx, domain.GetRoleBindingsRequest{},
	)
	s.Nil(err)

	s.Len(studioRoleBindings, int(count))

	mStudioRoleBinding := make(map[domain.StudioRoleBinding]struct{})
	for _, roleBinding := range studioRoleBindings {
		mStudioRoleBinding[roleBinding] = struct{}{}
	}

	// check if all role bindings are distinct
	s.Len(mStudioRoleBinding, int(count))
}
func (s *studioRoleSuite) TestGetRoleBindingsByUserOrOrg() {
	{
		studioRoleBindings, err := rdb.GormRepo().GetRoleBindings(
			s.ctx, domain.GetRoleBindingsRequest{
				OrgID: 1,
				UID:   "uid1",
			},
		)
		s.Nil(err)

		expectedRoles := []domain.StudioRole{{
			Module: "",
			Name:   "owner",
		}, {
			Module: "asset_pro",
			Name:   "admin",
		}}

		s.Len(studioRoleBindings, len(expectedRoles))

		actualRoles := make([]domain.StudioRole, 0, len(studioRoleBindings))
		for _, role := range studioRoleBindings {
			s.Equal(role.OrganizationID, 1)
			s.Equal(role.UID, "uid1")
			actualRoles = append(actualRoles, role.StudioRole)
		}

		for _, expectedRole := range expectedRoles {
			s.Contains(actualRoles, expectedRole)
		}
	}

	{
		studioRoleBindings, err := rdb.GormRepo().GetRoleBindings(
			s.ctx, domain.GetRoleBindingsRequest{
				OrgID: 1,
			},
		)
		s.Nil(err)

		s.Len(studioRoleBindings, 4)
	}

	{
		studioRoleBindings, err := rdb.GormRepo().GetRoleBindings(
			s.ctx, domain.GetRoleBindingsRequest{
				OrgID: 2,
				UID:   "uid2",
			})
		s.Nil(err)

		expectedRoles := []domain.StudioRole{
			{Module: "user_360", Name: "admin"},
			{Module: "wallet_builder", Name: "admin"},
			{Module: "asset_pro", Name: "admin"},
			{Module: "asset_pro", Name: "approver"},
			{Module: "asset_pro", Name: "trader"},
			{Module: "nft_boost", Name: "admin"},
			{Module: "compliance", Name: "admin"},
		}

		s.Len(studioRoleBindings, len(expectedRoles))

		actualRoles := make([]domain.StudioRole, 0, len(studioRoleBindings))
		for _, role := range studioRoleBindings {
			s.Equal(role.OrganizationID, 2)
			s.Equal(role.UID, "uid2")
			actualRoles = append(actualRoles, role.StudioRole)
		}

		for _, expectedRole := range expectedRoles {
			s.Contains(actualRoles, expectedRole)
		}
	}

	{
		studioRoleBindings, err := rdb.GormRepo().GetRoleBindings(
			s.ctx, domain.GetRoleBindingsRequest{
				OrgID: 2,
			},
		)
		s.Nil(err)

		s.Len(studioRoleBindings, 7)
	}
}

func (s *studioRoleSuite) TestGetRoleBindingsByUserOrOrgAfterDeleteUser() {
	db := rdb.Get()
	uid := util.RandString(20)
	studioUser := model.StudioUser{
		OrganizationID: 1,
		UID:            uid,
		Status:         model.StudioUserStatusActive,
		RoleBinding: []model.StudioRoleBinding{
			{
				RoleID: 1,
			},
		},
	}

	s.NoError(db.Create(&studioUser).Error)

	studioRoleBindings, err := rdb.GormRepo().GetRoleBindings(
		s.ctx, domain.GetRoleBindingsRequest{
			OrgID: 1,
			UID:   uid,
		},
	)
	s.Nil(err)
	s.Len(studioRoleBindings, 1)

	s.NoError(db.Delete(&studioUser).Error)

	studioRoleBindings, err = rdb.GormRepo().GetRoleBindings(
		s.ctx, domain.GetRoleBindingsRequest{
			OrgID: 1,
			UID:   uid,
		},
	)
	s.Nil(err)
	s.Len(studioRoleBindings, 0)
}

func (s *studioRoleSuite) TestSaveUserRolesWithInvalidRole() {
	err := rdb.GormRepo().SaveUserRoles(s.ctx, domain.SaveUserRolesRequest{
		OrgID: 1,
		UID:   "uid1",
		Roles: []domain.StudioRole{
			{Module: "not_exist", Name: "not_exist"},
		},
	})
	s.NotNil(err)
	s.Error(err.Error)
	s.Equal(400, err.HttpStatus)
	s.Equal(code.InvalidRoles, err.Code)
}

func (s *studioRoleSuite) TestSaveUserRolesNormal() {
	// owner will be unbound
	// asset_pro:admin, compliance:admin, user_360:admin will be bound

	var (
		orgID  int = 999
		userID     = "55688"
	)

	s.NoError(rdb.Get().Where("organization_id = ? AND uid = ?", orgID, userID).
		Delete(&model.StudioRoleBinding{}).Error)
	s.NoError(rdb.Get().Create(&model.StudioUser{
		UID:            userID,
		OrganizationID: orgID,
		Status:         model.StudioUserStatusActive,
	}).Error)
	s.NoError(rdb.Get().Create([]model.StudioRoleBinding{
		{
			RoleID:         1, // owner
			OrganizationID: orgID,
			UID:            userID,
		},
		{
			RoleID:         7, // nft_boost:admin
			OrganizationID: orgID,
			UID:            userID,
		},
	}).Error)

	var count int64
	s.NoError(rdb.Get().Model(&model.StudioRoleBinding{}).
		Where("organization_id = ? AND uid = ?", orgID, userID).
		Count(&count).Error)
	s.Equal(count, int64(2)) // owner and nft_boost:admin

	err := rdb.GormRepo().SaveUserRoles(s.ctx, domain.SaveUserRolesRequest{
		OrgID: orgID,
		UID:   userID,
		Roles: []domain.StudioRole{
			{Module: "nft_boost", Name: "admin"},
			{Module: "asset_pro", Name: "admin"},
			{Module: "compliance", Name: "admin"},
			{Module: "user_360", Name: "admin"},
		},
	})
	s.Nil(err)

	var roleBindings []model.StudioRoleBinding
	s.NoError(rdb.Get().Preload("Role").
		Where("organization_id = ? AND uid = ?", orgID, userID).
		Find(&roleBindings).Error)
	s.Len(roleBindings, 4)

	expectedRoles := []domain.StudioRole{
		{Module: "nft_boost", Name: "admin"},  // already existed
		{Module: "asset_pro", Name: "admin"},  // new
		{Module: "compliance", Name: "admin"}, // new
		{Module: "user_360", Name: "admin"},   // new
	}

	actualRoles := make([]string, 0, len(roleBindings))
	for _, role := range roleBindings {
		actualRoles = append(actualRoles, role.Role.String())
	}

	for _, expectedRole := range expectedRoles {
		s.Contains(actualRoles, expectedRole.String())
	}
}

func TestStudioRoleSuite(t *testing.T) {
	suite.Run(t, new(studioRoleSuite))
}
