package rdb

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestCreateBridgeRecord(t *testing.T) {
	Reset()
	repo := &Repo{db: Get()}

	// Create test data
	bridgeRecord := &domain.BridgeRecord{
		OrgID:             1,
		UID:               nil,
		FromChain:         domain.Arbitrum,
		FromAddress:       domain.NewEvmAddress("******************************************"),
		FromTokenAddress:  "******************************************",
		FromAmount:        decimal.NewFromFloat(5),
		FromTxHash:        "0xf98a8eb02ad8da1b176628552347ed2cee72f52ae8ac2ba13ce51a1003c38f36",
		ToChain:           domain.Polygon,
		ToAddress:         domain.NewEvmAddress("******************************************"),
		ToTokenAddress:    "******************************************",
		ToAmount:          decimal.NewFromFloat(4.849878),
		FeeChain:          domain.Arbitrum,
		FeeReceiveAddress: domain.NewEvmAddress("******************************************"),
		FeeTokenAddress:   "******************************************",
		FeeAmount:         decimal.NewFromFloat(0.15),
		FeeTxHash:         "0xf98a8eb02ad8da1b176628552347ed2cee72f52ae8ac2ba13ce51a1003c38f36",
	}

	// Create the record
	err := repo.CreateBridgeRecord(context.Background(), bridgeRecord)
	assert.NoError(t, err)

	// Verify the record was created with null UID
	var record model.StudioOrganizationBridgeRecord
	err = Get().Where("from_tx_hash = ?", bridgeRecord.FromTxHash).First(&record).Error
	assert.NoError(t, err)
	assert.Nil(t, record.UID, "UID should be null when input UID is empty")

	// Verify other fields were stored correctly
	assert.Equal(t, bridgeRecord.OrgID, record.OrganizationID)
	assert.Equal(t, bridgeRecord.FromChain.ID(), record.FromChainID)
	assert.Equal(t, bridgeRecord.FromAddress.String(), record.From)
	assert.Equal(t, bridgeRecord.FromTokenAddress, record.FromTokenAddress)
	assert.Equal(t, bridgeRecord.FromAmount.String(), record.FromAmount.String())
	assert.Equal(t, bridgeRecord.FromTxHash, record.FromTxHash)
}
