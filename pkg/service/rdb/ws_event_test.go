package rdb

import (
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func setupTestRepo() *Repo {
	// Initialize the test database
	Reset()
	return GormRepo()
}

func TestInsertWsEvent(t *testing.T) {
	repo := setupTestRepo()

	event := &domain.WsEvent{
		RequestUUID:    []byte("test-uuid-123"),
		UID:            "user123",
		ChainID:        "chain-abc",
		JsonrpcID:      1,
		JsonrpcVersion: "2.0",
		Method:         "test_method",
		Params:         `{"param":"value"}`,
	}

	err := repo.InsertWsEvent(event)
	assert.NoError(t, err, "InsertWsEvent should not return an error")

	// Cleanup
	err = repo.SetWsEventCanceled(event.RequestUUID)
	assert.NoError(t, err, "SetWsEventCanceled should not return an error")
}

func TestSaveWsEventResult(t *testing.T) {
	repo := setupTestRepo()

	requestUUID := []byte{0xC3, 0x92, 0xC5, 0x40, 0x96, 0xCC, 0x46, 0xF7, 0x80, 0x43, 0x59, 0x48, 0x0D, 0x04, 0x1A, 0xBD}
	event := &domain.WsEvent{
		RequestUUID:    requestUUID,
		UID:            "user456",
		ChainID:        "chain-def",
		JsonrpcID:      2,
		JsonrpcVersion: "2.0",
		Method:         "test_method_update",
		Params:         `{"param":"value2"}`,
	}

	// Insert the event first
	err := repo.InsertWsEvent(event)
	assert.NoError(t, err, "InsertWsEvent should not return an error")

	update := &domain.WsEventUpdate{
		RequestUUID: event.RequestUUID,
		Method:      "test_method_update",
		Result:      util.Ptr(`{"result":"success"}`),
		Error:       nil,
	}

	err = repo.SaveWsEventResult(update)
	assert.NoError(t, err, "SaveWsEventResult should not return an error")

	// Cleanup
	err = repo.SetWsEventCanceled(event.RequestUUID)
	assert.NoError(t, err, "SetWsEventCanceled should not return an error")
}

func TestGetWsEvent(t *testing.T) {
	repo := setupTestRepo()
	requestUUID := []byte{0xC3, 0x92, 0xC5, 0x40, 0x96, 0xCC, 0x46, 0xF7, 0x80, 0x43, 0x59, 0x48, 0x0D, 0x04, 0x1A, 0xBD}
	event := &domain.WsEvent{
		RequestUUID:    requestUUID,
		UID:            "user789",
		ChainID:        "chain-ghi",
		JsonrpcID:      3,
		JsonrpcVersion: "2.0",
		Method:         "test_method_get",
		Params:         `{"param":"value3"}`,
	}

	// Insert the event first
	err := repo.InsertWsEvent(event)
	assert.NoError(t, err, "InsertWsEvent should not return an error")

	// Retrieve the event
	retrievedEvent, err := repo.GetWsEvent(event.RequestUUID)
	assert.NoError(t, err, "GetWsEvent should not return an error")
	assert.Equal(t, event.RequestUUID, retrievedEvent.RequestUUID, "RequestUUID should match")
	assert.Equal(t, event.UID, retrievedEvent.UID, "UID should match")
	assert.Equal(t, event.Method, retrievedEvent.Method, "Method should match")

	// Cleanup
	err = repo.SetWsEventCanceled(event.RequestUUID)
	assert.NoError(t, err, "SetWsEventCanceled should not return an error")
}
