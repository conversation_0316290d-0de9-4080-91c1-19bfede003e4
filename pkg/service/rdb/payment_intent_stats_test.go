package rdb

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Helper function to convert decimal.Decimal to *decimal.Decimal
func decimalPtr(d decimal.Decimal) *decimal.Decimal {
	return &d
}

func TestGetPaymentIntentStats(t *testing.T) {
	Reset() // Reset the database to a clean state

	ctx := context.Background()
	repo := GormRepo()
	orgID := 1

	// Test case 1: No existing stats, should calculate total stats
	t.Run("CalculateTotalStats", func(t *testing.T) {
		// First call should calculate from scratch
		stats, err := repo.GetPaymentIntentStats(ctx, orgID)
		require.NoError(t, err)
		assert.Equal(t, orgID, stats.OrganizationID)
		assert.NotNil(t, stats.TotalRevenue)
		assert.GreaterOrEqual(t, stats.UpdatedAt.Unix(), time.Now().Add(-time.Minute).Unix())

		// Verify stats were saved to database
		var record model.PaymentIntentStats
		err = Get().Where("organization_id = ?", orgID).First(&record).Error
		require.NoError(t, err)
		assert.Equal(t, orgID, record.OrganizationID)
	})

	// Test case 2: Call with existing stats, should perform incremental update
	t.Run("IncrementalUpdate", func(t *testing.T) {
		t.Skip("Skipping for being too flaky.")
		// Add a new payment intent to the database with success status
		chain := domain.Ethereum
		tokenID := "0x123"
		amount := decimal.NewFromInt(100)

		payment := &model.PaymentIntent{
			ID:                     "test-payment-1",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           chain.ID(),
			TokenAddress:           tokenID,
			AggregatedCryptoAmount: &amount,
			PayerAddress:           "0xPayer123",
			OrderData:              "{}",
			PaymentDeadline:        time.Now().Add(time.Hour * 24),
			CreatedAt:              time.Now(),
			UpdatedAt:              time.Now(),
		}
		err := Get().Create(payment).Error
		require.NoError(t, err)

		// Get stats again - should perform incremental update
		statsAfterUpdate, err := repo.GetPaymentIntentStats(ctx, orgID)
		require.NoError(t, err)

		// Verify the revenue includes our new payment intent
		chainToken := domain.ChainToken{Chain: chain, TokenID: tokenID}
		for key, val := range statsAfterUpdate.TotalRevenue {
			t.Logf("key: %s %s, val: %s", key.Chain.ID(), key.TokenID, val.String())
		}
		val, exists := statsAfterUpdate.TotalRevenue[chainToken]
		assert.True(t, exists)
		assert.Equal(t, amount.String(), val.String())
	})
}

func TestCalculateRevenue(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()
	orgID := 1

	// Create multiple payment intents with different statuses, chains, and tokens
	now := time.Now()
	payments := []*model.PaymentIntent{
		{
			ID:                     "payment-1",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(100)),
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			PayerAddress:           "0xPayerTest1",
			Symbol:                 "TKN1",
			CreatedAt:              now.Add(-time.Hour * 24),
			UpdatedAt:              now.Add(-time.Hour * 24),
		},
		{
			ID:                     "payment-2",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1", // Same token as payment-1
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(50)),
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			PayerAddress:           "0xPayerTest2",
			Symbol:                 "TKN1",
			CreatedAt:              now.Add(-time.Hour * 12),
			UpdatedAt:              now.Add(-time.Hour * 12),
		},
		{
			ID:                     "payment-3",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Polygon.ID(),
			TokenAddress:           "0xToken2",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(75)),
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			PayerAddress:           "0xPayerTest3",
			Symbol:                 "TKN2",
			CreatedAt:              now.Add(-time.Hour * 6),
			UpdatedAt:              now.Add(-time.Hour * 6),
		},
		{
			ID:                     "payment-4",
			OrgID:                  orgID,
			Status:                 domain.PaymentIntentStatusPending, // Not successful, should be excluded
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(30)),
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			PayerAddress:           "0xPayerTest4",
			Symbol:                 "TKN1",
			CreatedAt:              now.Add(-time.Hour * 3),
			UpdatedAt:              now.Add(-time.Hour * 3),
		},
		{
			ID:                     "payment-5",
			OrgID:                  2, // Different org, should be excluded
			Status:                 domain.PaymentIntentStatusSuccess,
			PaymentChain:           domain.Ethereum.ID(),
			TokenAddress:           "0xToken1",
			AggregatedCryptoAmount: decimalPtr(decimal.NewFromInt(20)),
			PaymentDeadline:        now.Add(time.Hour),
			OrderData:              "{}",
			PayerAddress:           "0xPayerTest5",
			Symbol:                 "TKN1",
			CreatedAt:              now.Add(-time.Hour * 1),
			UpdatedAt:              now.Add(-time.Hour * 1),
		},
	}

	// Insert test data
	for _, p := range payments {
		err := Get().Create(p).Error
		require.NoError(t, err)
	}

	// Test full calculation
	t.Run("TotalRevenue", func(t *testing.T) {
		revenue, err := repo.calculateRevenue(ctx, orgID, time.Time{})
		require.NoError(t, err)

		// Should find 3 successful payments for org 1
		assert.Len(t, revenue, 2) // 2 unique chain/token combinations

		// Verify aggregated amounts
		eth1 := domain.ChainToken{Chain: domain.Ethereum, TokenID: "0xToken1"}
		poly2 := domain.ChainToken{Chain: domain.Polygon, TokenID: "0xToken2"}

		// Ethereum/Token1 should have 100 + 50 = 150
		ethAmount, exists := revenue[eth1]
		assert.True(t, exists)
		assert.Equal(t, "150", ethAmount.String())

		// Polygon/Token2 should have 75
		polyAmount, exists := revenue[poly2]
		assert.True(t, exists)
		assert.Equal(t, "75", polyAmount.String())
	})

	// Test incremental calculation
	t.Run("IncrementalRevenue", func(t *testing.T) {
		// Calculate revenue after the first payment
		since := now.Add(-time.Hour * 20) // Only include payments after payment-1
		revenue, err := repo.calculateRevenue(ctx, orgID, since)
		require.NoError(t, err)

		// Should only find payment-2 and payment-3
		assert.Len(t, revenue, 2)

		eth1 := domain.ChainToken{Chain: domain.Ethereum, TokenID: "0xToken1"}
		poly2 := domain.ChainToken{Chain: domain.Polygon, TokenID: "0xToken2"}

		// Ethereum/Token1 should only have amount from payment-2 (50)
		ethAmount, exists := revenue[eth1]
		assert.True(t, exists)
		assert.Equal(t, "50", ethAmount.String())

		// Polygon/Token2 should have amount from payment-3 (75)
		polyAmount, exists := revenue[poly2]
		assert.True(t, exists)
		assert.Equal(t, "75", polyAmount.String())
	})
}

func TestMergeRevenueMaps(t *testing.T) {
	// Create two revenue maps with some overlapping keys
	eth := domain.Ethereum
	poly := domain.Polygon

	map1 := map[domain.ChainToken]decimal.Decimal{
		{Chain: eth, TokenID: "0xToken1"}:  decimal.NewFromInt(100),
		{Chain: eth, TokenID: "0xToken2"}:  decimal.NewFromInt(50),
		{Chain: poly, TokenID: "0xToken3"}: decimal.NewFromInt(25),
	}

	map2 := map[domain.ChainToken]decimal.Decimal{
		{Chain: eth, TokenID: "0xToken1"}:  decimal.NewFromInt(75),  // Overlapping
		{Chain: poly, TokenID: "0xToken4"}: decimal.NewFromInt(120), // New key
	}

	merged := mergeRevenueMaps(map1, map2)

	// Verify merged results
	assert.Len(t, merged, 4) // 4 unique chain/token combinations

	// Check individual values
	assert.Equal(t, "175", merged[domain.ChainToken{Chain: eth, TokenID: "0xToken1"}].String()) // 100 + 75
	assert.Equal(t, "50", merged[domain.ChainToken{Chain: eth, TokenID: "0xToken2"}].String())
	assert.Equal(t, "25", merged[domain.ChainToken{Chain: poly, TokenID: "0xToken3"}].String())
	assert.Equal(t, "120", merged[domain.ChainToken{Chain: poly, TokenID: "0xToken4"}].String())
}

func TestCalculateValidOrderCount(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()
	orgID := 1
	now := time.Now()

	// Create payment intents with different statuses
	payments := []*model.PaymentIntent{
		{ID: "order-1", OrgID: orgID, Status: domain.PaymentIntentStatusPending, PaymentDeadline: now.Add(time.Hour), OrderData: "{}"},
		{ID: "order-2", OrgID: orgID, Status: domain.PaymentIntentStatusSuccess, PaymentDeadline: now.Add(time.Hour), OrderData: "{}"},
		{ID: "order-3", OrgID: orgID, Status: domain.PaymentIntentStatusExpired, PaymentDeadline: now.Add(time.Hour), OrderData: "{}"},              // Should be excluded
		{ID: "order-4", OrgID: orgID, Status: domain.PaymentIntentStatusInsufficientRefunded, PaymentDeadline: now.Add(time.Hour), OrderData: "{}"}, // Changed to a valid status
		{ID: "order-5", OrgID: 2, Status: domain.PaymentIntentStatusSuccess, PaymentDeadline: now.Add(time.Hour), OrderData: "{}"},                  // Different org
	}

	for _, p := range payments {
		err := Get().Create(p).Error
		require.NoError(t, err)
	}

	count, err := repo.calculateValidOrderCount(ctx, orgID)
	require.NoError(t, err)
	assert.Equal(t, 3, count) // 3 non-expired orders for orgID 1
}

func TestCalculateUniqueCustomerCount(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()
	orgID := 1

	now := time.Now()

	// Create payment intents with different payer addresses
	payments := []*model.PaymentIntent{
		{ID: "payment-1", OrgID: orgID, PayerAddress: "0xPayer1", Status: domain.PaymentIntentStatusSuccess, PaymentDeadline: now.Add(time.Hour), OrderData: "{}"},
		{ID: "payment-2", OrgID: orgID, PayerAddress: "0xPayer2", Status: domain.PaymentIntentStatusSuccess, PaymentDeadline: now.Add(time.Hour), OrderData: "{}"},
		{ID: "payment-3", OrgID: orgID, PayerAddress: "0xPayer1", Status: domain.PaymentIntentStatusSuccess, PaymentDeadline: now.Add(time.Hour), OrderData: "{}"}, // Duplicate, should also be success
		{ID: "payment-4", OrgID: orgID, PayerAddress: "", Status: domain.PaymentIntentStatusSuccess, PaymentDeadline: now.Add(time.Hour), OrderData: "{}", PaymentTxHash: stringPtr("0xTransaction1")},
		{ID: "payment-5", OrgID: 2, PayerAddress: "0xPayer3", Status: domain.PaymentIntentStatusSuccess, PaymentDeadline: now.Add(time.Hour), OrderData: "{}"}, // Different org, also set to success for consistency if it were org 1
	}

	for _, p := range payments {
		err := Get().Create(p).Error
		require.NoError(t, err)
	}

	count, err := repo.calculateUniqueCustomerCount(ctx, orgID)
	require.NoError(t, err)
	assert.Equal(t, 2, count)
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}

func TestUpdatePaymentIntentStats(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()
	orgID := 1

	// Create test data for payment intent stats
	chain1 := domain.Ethereum
	chain2 := domain.Polygon
	token1 := "0xToken1"
	token2 := "0xToken2"

	statsData := &domain.PaymentIntentStats{
		OrganizationID: orgID,
		TotalRevenue: map[domain.ChainToken]decimal.Decimal{
			{Chain: chain1, TokenID: token1}: decimal.NewFromInt(100),
			{Chain: chain2, TokenID: token2}: decimal.NewFromInt(50),
		},
		ValidOrderCount:     5,
		UniqueCustomerCount: 3,
		UpdatedAt:           time.Now(),
	}

	// Test update
	err := repo.UpdatePaymentIntentStats(ctx, statsData)
	require.NoError(t, err)

	// Verify record was saved
	var record model.PaymentIntentStats
	err = Get().Where("organization_id = ?", orgID).First(&record).Error
	require.NoError(t, err)
	assert.Equal(t, orgID, record.OrganizationID)
	assert.Equal(t, 5, record.ValidOrderCount)
	assert.Equal(t, 3, record.UniqueCustomerCount)

	// Verify the revenue was properly serialized
	var revenueMap map[string]string
	require.NoError(t, json.Unmarshal([]byte(record.TotalRevenue), &revenueMap))

	// Check the revenue values
	ethKey := chain1.ID() + ":" + token1
	polyKey := chain2.ID() + ":" + token2

	assert.Equal(t, "100", revenueMap[ethKey])
	assert.Equal(t, "50", revenueMap[polyKey])

	// Test update with different values
	updatedStats := &domain.PaymentIntentStats{
		OrganizationID: orgID,
		TotalRevenue: map[domain.ChainToken]decimal.Decimal{
			{Chain: chain1, TokenID: token1}: decimal.NewFromInt(150),
			{Chain: chain2, TokenID: token2}: decimal.NewFromInt(75),
		},
		ValidOrderCount:     8,
		UniqueCustomerCount: 4,
		UpdatedAt:           time.Now(),
	}

	// Update again
	err = repo.UpdatePaymentIntentStats(ctx, updatedStats)
	require.NoError(t, err)

	// Verify update
	err = Get().Where("organization_id = ?", orgID).First(&record).Error
	require.NoError(t, err)
	assert.Equal(t, 8, record.ValidOrderCount)
	assert.Equal(t, 4, record.UniqueCustomerCount)

	// Verify updated revenue
	require.NoError(t, json.Unmarshal([]byte(record.TotalRevenue), &revenueMap))
	assert.Equal(t, "150", revenueMap[ethKey])
	assert.Equal(t, "75", revenueMap[polyKey])
}

func TestToDomainPaymentIntentStats(t *testing.T) {
	ctx := context.Background()

	// Create a model record with serialized revenue data
	chain1 := domain.Ethereum.ID()
	chain2 := domain.Polygon.ID()
	token1 := "0xToken1"
	token2 := "0xToken2"

	revenueMap := map[string]string{
		chain1 + ":" + token1: "100",
		chain2 + ":" + token2: "50",
	}

	revenueJSON, err := json.Marshal(revenueMap)
	require.NoError(t, err)

	record := &model.PaymentIntentStats{
		OrganizationID:      1,
		TotalRevenue:        string(revenueJSON),
		ValidOrderCount:     5,
		UniqueCustomerCount: 3,
		UpdatedAt:           time.Now(),
	}

	// Convert to domain object
	stats, err := toDomainPaymentIntentStats(ctx, record)
	require.NoError(t, err)

	// Verify conversion
	assert.Equal(t, 1, stats.OrganizationID)
	assert.Equal(t, 5, stats.ValidOrderCount)
	assert.Equal(t, 3, stats.UniqueCustomerCount)

	// Verify revenue conversion
	assert.Len(t, stats.TotalRevenue, 2)

	ethToken := domain.ChainToken{Chain: domain.Ethereum, TokenID: token1}
	polyToken := domain.ChainToken{Chain: domain.Polygon, TokenID: token2}

	ethAmount, exists := stats.TotalRevenue[ethToken]
	assert.True(t, exists)
	assert.Equal(t, "100", ethAmount.String())

	polyAmount, exists := stats.TotalRevenue[polyToken]
	assert.True(t, exists)
	assert.Equal(t, "50", polyAmount.String())

	// Test invalid chain ID
	revenueMap["invalid-chain:token"] = "10"
	revenueJSON, err = json.Marshal(revenueMap)
	require.NoError(t, err)

	record.TotalRevenue = string(revenueJSON)

	// Should still convert without error but skip the invalid entry
	stats, err = toDomainPaymentIntentStats(ctx, record)
	require.NoError(t, err)
	assert.Len(t, stats.TotalRevenue, 2) // Still 2, invalid one skipped
}

func TestGetPaymentIntentStatsErrorCases(t *testing.T) {
	Reset()

	ctx := context.Background()
	repo := GormRepo()
	orgID := 1

	// Test handling of invalid data within the JSON field
	t.Run("InvalidAmountInJSON", func(t *testing.T) {
		// Create a record with valid JSON structure but an invalid amount string
		validChain := domain.Ethereum.ID()
		validToken := "0xValidToken"
		revenueMap := map[string]string{
			validChain + ":" + validToken: "not-a-number", // Invalid amount value
		}
		revenueJSON, err := json.Marshal(revenueMap)
		require.NoError(t, err)

		record := &model.PaymentIntentStats{
			OrganizationID:      orgID,
			TotalRevenue:        string(revenueJSON),
			ValidOrderCount:     5,
			UniqueCustomerCount: 3,
			UpdatedAt:           time.Now(), // Needs a valid time
			CreatedAt:           time.Now(), // Needs a valid time
		}

		err = Get().Create(record).Error
		require.NoError(t, err, "Failed to insert record with invalid amount string in JSON")

		// Get should fall back to recalculating from scratch due to amount parsing error
		kglog.InfoCtx(ctx, "Expecting GetPaymentIntentStats to handle invalid amount string in JSON by recalculating")
		stats, err := repo.GetPaymentIntentStats(ctx, orgID)
		require.NoError(t, err) // Should not fail the overall function
		assert.NotNil(t, stats)
		assert.Equal(t, orgID, stats.OrganizationID)
		// Because it recalculated after the conversion error, revenue should be empty
		// (assuming no *other* valid payment intents exist for this org in the DB)
		assert.Empty(t, stats.TotalRevenue, "Recalculated revenue should be empty after amount parse error")
	})

	// Test invalid chain token format (no need for Reset here as the previous test recalculates)
	t.Run("InvalidChainToken", func(t *testing.T) {
		// Create a record with valid JSON but invalid chain format
		revenueMap := map[string]string{
			"invalid-format": "100", // Missing colon separator
		}

		revenueJSON, err := json.Marshal(revenueMap)
		require.NoError(t, err)

		// Use Create here, as the JSON itself is valid
		record := &model.PaymentIntentStats{
			OrganizationID:      orgID,
			TotalRevenue:        string(revenueJSON),
			ValidOrderCount:     5,
			UniqueCustomerCount: 3,
			UpdatedAt:           time.Now(),
			CreatedAt:           time.Now(), // Ensure this is set too
		}

		err = Get().Create(record).Error
		require.NoError(t, err)

		// Get should work and skip the invalid entry during conversion, then recalculate
		kglog.InfoCtx(ctx, "Expecting GetPaymentIntentStats to handle invalid chain token by skipping and recalculating")
		stats, err := repo.GetPaymentIntentStats(ctx, orgID)
		require.NoError(t, err)
		// It should have recalculated, finding the record from the previous sub-test (InvalidAmountInJSON)
		// but failed to parse its amount, resulting in empty revenue again.
		assert.Empty(t, stats.TotalRevenue) // No valid entries were parsed across both records
	})
}
