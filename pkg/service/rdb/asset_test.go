package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/stretchr/testify/assert"
)

func TestGetAsset(t *testing.T) {
	Reset()
	assert.Nil(t, dbtest.CreateAssets(Get()))
	assert.Nil(t, dbtest.CreateAssetPrices(Get()))

	time.Sleep(1 * time.Second)

	// all assets except for the favorite
	assetParams := &AssetParams{
		AssetTypes:      []string{dbtest.NotFavoriteAsset.AssetType},
		ChainIDs:        []string{dbtest.NotFavoriteAsset.ChainID},
		WalletAddresses: []string{dbtest.NotFavoriteAsset.WalletAddress},
		Exclude: map[string][]string{
			dbtest.FavoriteAsset.ChainID: {dbtest.FavoriteAsset.AssetGroup},
		},
	}
	allAssets, errCode, err := Assets(context.Background(), assetParams, nil)
	assert.Nil(t, err)
	assert.True(t, errCode == 0)
	assert.Len(t, allAssets, 1)

	assert.True(t, allAssets[0].AssetGroup == dbtest.NotFavoriteAsset.AssetGroup)
	assert.True(t, (*allAssets[0].AmountDec).String() == *dbtest.NotFavoriteAsset.Amount)

	// favorite assets
	favoriteAssetParams := &AssetParams{
		AssetTypes:      []string{dbtest.FavoriteAsset.AssetType},
		ChainIDs:        []string{dbtest.FavoriteAsset.ChainID},
		WalletAddresses: []string{dbtest.FavoriteAsset.WalletAddress},
		// Favorite: map[string][]string{
		// 	dbtest.FavoriteAsset.ChainID: {dbtest.FavoriteAsset.AssetGroup},
		// },
	}
	allAssets, _, _ = Assets(context.Background(), favoriteAssetParams, nil)
	assert.Len(t, allAssets, 2)
	assert.Equal(t, dbtest.FavoriteAsset.AssetGroup, allAssets[1].AssetGroup)
	assert.Equal(t, *dbtest.FavoriteAsset.Amount, (*allAssets[1].AmountDec).String())
}

func TestSyncDefiAsset(t *testing.T) {
	Reset()
	CreateSeedData()

	// all assets except for the favorite
	assetParams := &AssetParams{
		AssetTypes:      []string{dbtest.DefiAssetToUpdate.AssetType},
		ChainIDs:        []string{dbtest.DefiAssetToUpdate.ChainID},
		WalletAddresses: []string{dbtest.DefiAssetToUpdate.WalletAddress},
	}
	allAssets, errCode, err := Assets(context.Background(), assetParams, nil)
	assert.Nil(t, err)
	assert.True(t, errCode == 0)
	assert.Len(t, allAssets, 1)
	assert.Equal(t, "27.233", (*allAssets[0].AmountDec).String(), "Amount should be 27.233")

	newDefiAssets := []*DefiAsset{
		{
			ChainID:    dbtest.DefiAssetToUpdate.ChainID,
			AssetGroup: dbtest.DefiAssetToUpdate.AssetGroup,
			// We comment one of the condition here to test if the condition is working properly
			// AssetType:         dbtest.DefiAssetToUpdate.AssetType,
			WalletAddress:     dbtest.DefiAssetToUpdate.WalletAddress,
			Amount:            *dbtest.DefiAssetToUpdate.Amount,
			LastModifiedStart: time.Now().Add(-1 * time.Hour),
		},
	}

	err = UpdateDefiAssetsInBatch(context.Background(), newDefiAssets)
	assert.Nil(t, err)

	allAssets, errCode, err = Assets(context.Background(), assetParams, nil)
	assert.Nil(t, err)
	assert.True(t, errCode == 0)
	assert.Len(t, allAssets, 1)
	assert.Equal(t, "27.233", (*allAssets[0].AmountDec).String(), "Amount should not be updated yet.")

	newDefiAssets[0].AssetType = dbtest.DefiAssetToUpdate.AssetType
	err = UpdateDefiAssetsInBatch(context.Background(), newDefiAssets)
	assert.Nil(t, err)

	allAssets, errCode, err = Assets(context.Background(), assetParams, nil)
	assert.Nil(t, err)
	assert.True(t, errCode == 0)
	assert.Len(t, allAssets, 1)
	assert.Equal(t, *dbtest.DefiAssetToUpdate.Amount, (*allAssets[0].AmountDec).String(), "Amount should be updated.")
}

func TestGetDefiAsset(t *testing.T) {
	Reset()
	CreateSeedData()

	a := dbtest.DefiAssetToUpdateMatic
	newDefiAssets := []*DefiAsset{
		{
			ChainID:           a.ChainID,
			AssetGroup:        a.AssetGroup,
			AssetType:         a.AssetType,
			WalletAddress:     a.WalletAddress,
			Amount:            *a.Amount,
			LastModifiedStart: time.Now(),
		},
	}
	assert.Nil(t, GetWith(context.Background()).Create(&model.Asset{
		ChainID:       a.ChainID,
		AssetGroup:    a.AssetGroup,
		AssetType:     a.AssetType,
		WalletAddress: a.WalletAddress,
		LogoUrls:      a.LogoUrls,
	}).Error)

	err := UpdateDefiAssetsInBatch(context.Background(), newDefiAssets)
	assert.Nil(t, err)

	assetParams := &AssetParams{
		AssetTypes:      []string{a.AssetType},
		ChainIDs:        []string{a.ChainID},
		WalletAddresses: []string{a.WalletAddress},
	}
	allAssets, errCode, err := Assets(context.Background(), assetParams, nil)
	assert.Nil(t, err)
	assert.True(t, errCode == 0)
	assert.Len(t, allAssets, 1)
	t.Logf("allAssets: %+v", allAssets)
	assert.Equal(t, "257.348", (*allAssets[0].AmountDec).String(), "Amount should be updated.")
}
