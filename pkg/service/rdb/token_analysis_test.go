package rdb

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestTokenAnalysis(t *testing.T) {
	Reset()
	ctx := context.Background()
	repo := GormRepo()

	// Test GetCredits, AddCredits, and UseCredit
	t.Run("Credits", func(t *testing.T) {
		walletAddress := "0x123456789abcdef"

		// Test getting credits for new wallet (should return 0)
		credits, err := repo.GetCredits(ctx, walletAddress)
		require.NoError(t, err)
		assert.Equal(t, walletAddress, credits.WalletAddress)
		assert.Equal(t, 0, credits.Credits)

		// Add credits to the wallet
		err = repo.AddCredits(ctx, walletAddress, 10)
		require.NoError(t, err)

		// Verify credits were added
		credits, err = repo.GetCredits(ctx, walletAddress)
		require.NoError(t, err)
		assert.Equal(t, 10, credits.Credits)

		// Add more credits
		err = repo.AddCredits(ctx, walletAddress, 5)
		require.NoError(t, err)

		// Verify total credits
		credits, err = repo.GetCredits(ctx, walletAddress)
		require.NoError(t, err)
		assert.Equal(t, 15, credits.Credits) // 10 + 5

		// Use a credit
		err = repo.UseCredit(ctx, walletAddress)
		require.NoError(t, err)

		// Verify remaining credits
		credits, err = repo.GetCredits(ctx, walletAddress)
		require.NoError(t, err)
		assert.Equal(t, 14, credits.Credits) // 15 - 1

		// Use all remaining credits
		for i := 0; i < 14; i++ {
			err = repo.UseCredit(ctx, walletAddress)
			require.NoError(t, err)
		}

		// Verify zero credits
		credits, err = repo.GetCredits(ctx, walletAddress)
		require.NoError(t, err)
		assert.Equal(t, 0, credits.Credits)

		// Try to use credit with zero balance
		err = repo.UseCredit(ctx, walletAddress)
		require.Error(t, err)
		assert.Equal(t, domain.ErrInsufficientBalance, err)
	})

	// Test RecordTradingVolume and GetTradingVolume
	t.Run("TradingVolume", func(t *testing.T) {
		walletAddress := "0x987654321abcdef"

		// Verify initial volume is zero
		volume, err := repo.GetTradingVolume(ctx, walletAddress)
		require.NoError(t, err)
		assert.Equal(t, 0.0, volume)

		// Record trading volume
		tradingVolume := &domain.TokenAnalysisTradingVolume{
			WalletAddress: walletAddress,
			TxHash:        "0xabcdef123456",
			VolumeUSD:     100.5,
		}

		err = repo.RecordTradingVolume(ctx, tradingVolume)
		require.NoError(t, err)

		// Verify volume was recorded
		volume, err = repo.GetTradingVolume(ctx, walletAddress)
		require.NoError(t, err)
		assert.Equal(t, 100.5, volume)

		// Add another volume record
		tradingVolume2 := &domain.TokenAnalysisTradingVolume{
			WalletAddress: walletAddress,
			TxHash:        "0xfedcba654321",
			VolumeUSD:     50.75,
		}

		err = repo.RecordTradingVolume(ctx, tradingVolume2)
		require.NoError(t, err)

		// Verify total volume
		volume, err = repo.GetTradingVolume(ctx, walletAddress)
		require.NoError(t, err)
		assert.Equal(t, 151.25, volume) // 100.5 + 50.75

		// Test TradingVolumeExists
		exists, err := repo.TradingVolumeExists(ctx, "0xabcdef123456")
		require.NoError(t, err)
		assert.True(t, exists)

		exists, err = repo.TradingVolumeExists(ctx, "0xnonexistent")
		require.NoError(t, err)
		assert.False(t, exists)

		// Test duplicate transaction hash
		duplicateVolume := &domain.TokenAnalysisTradingVolume{
			WalletAddress: walletAddress,
			TxHash:        "0xabcdef123456", // Same as first record
			VolumeUSD:     75.0,
		}

		err = repo.RecordTradingVolume(ctx, duplicateVolume)
		assert.Error(t, err) // Should fail due to unique constraint
	})

	// Test CreateAnalysis and GetAnalysis
	t.Run("TokenAnalysis", func(t *testing.T) {
		walletAddress := "0x123456789abcdef"
		tokenAddress := "0xfedcba987654"
		analysisJSON := `{"risk_score": 85, "details": "Example analysis"}`

		// Create token analysis
		analysis := &domain.TokenAnalysis{
			WalletAddress: walletAddress,
			TokenAddress:  tokenAddress,
			Analysis:      analysisJSON,
		}

		id, err := repo.CreateAnalysis(ctx, analysis)
		require.NoError(t, err)
		assert.Greater(t, id, 0)

		// Retrieve the analysis
		retrieved, err := repo.GetAnalysis(ctx, id)
		require.NoError(t, err)
		assert.Equal(t, id, retrieved.ID)
		assert.Equal(t, walletAddress, retrieved.WalletAddress)
		assert.Equal(t, tokenAddress, retrieved.TokenAddress)

		// Test getting non-existent analysis
		_, err = repo.GetAnalysis(ctx, 9999)
		require.Error(t, err)
		assert.Equal(t, domain.ErrRecordNotFound, err)
	})

	// Test RecordCreditPurchase and CreditPurchaseExists
	t.Run("CreditPurchase", func(t *testing.T) {
		walletAddress := "0x111222333444555"
		txHash := "0xabcdef123456789"

		// Test CreditPurchaseExists for non-existent transaction
		exists, err := repo.CreditPurchaseExists(ctx, txHash)
		require.NoError(t, err)
		assert.False(t, exists)

		// Record a credit purchase
		purchase := &domain.TokenAnalysisCreditPurchase{
			WalletAddress:    walletAddress,
			SolAmount:        0.1,
			CreditsPurchased: 5,
			TxHash:           txHash,
		}

		err = repo.RecordCreditPurchase(ctx, purchase)
		require.NoError(t, err)

		// Test CreditPurchaseExists for existing transaction
		exists, err = repo.CreditPurchaseExists(ctx, txHash)
		require.NoError(t, err)
		assert.True(t, exists)

		// Test duplicate transaction hash
		duplicatePurchase := &domain.TokenAnalysisCreditPurchase{
			WalletAddress:    "0x999888777666555",
			SolAmount:        0.05,
			CreditsPurchased: 2,
			TxHash:           txHash, // Same transaction hash
		}

		err = repo.RecordCreditPurchase(ctx, duplicatePurchase)
		assert.Error(t, err) // Should fail due to unique constraint on tx_hash

		// Test with different transaction hash
		anotherTxHash := "0xfedcba987654321"
		anotherPurchase := &domain.TokenAnalysisCreditPurchase{
			WalletAddress:    walletAddress,
			SolAmount:        0.2,
			CreditsPurchased: 10,
			TxHash:           anotherTxHash,
		}

		err = repo.RecordCreditPurchase(ctx, anotherPurchase)
		require.NoError(t, err)

		// Verify both transactions exist
		exists, err = repo.CreditPurchaseExists(ctx, txHash)
		require.NoError(t, err)
		assert.True(t, exists)

		exists, err = repo.CreditPurchaseExists(ctx, anotherTxHash)
		require.NoError(t, err)
		assert.True(t, exists)

		// Test non-existent transaction
		exists, err = repo.CreditPurchaseExists(ctx, "0xnonexistent")
		require.NoError(t, err)
		assert.False(t, exists)
	})
}
