package rdb

import (
	"context"
	"fmt"
	"sort"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"

	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestNfts(t *testing.T) {
	ctx := context.Background()
	Reset()
	assert.Nil(t, rdbtest.CreateNftUserAmounts(database))
	assert.Nil(t, rdbtest.CreateNftAssets(database))
	params := &NftParams{
		ChainID: "ronin",
		Tagtype: "ALL",
		PagingParams: PagingParams{
			PageSize:   20,
			PageNumber: 1,
		},
	}

	// normal case: has data in nft_user_amounts and also in nft_assets
	walletAddressesByChainID := map[string][]string{
		"ronin": {"******************************************"},
	}
	nfts, paging, errCode, err := Nfts(ctx, params, walletAddressesByChainID)
	assert.Nil(t, err)
	assert.Equal(t, 0, errCode)
	assert.NotNil(t, nfts)
	assert.Len(t, *nfts, 1)
	assert.NotNil(t, paging)
	assert.Equal(t, int64(1), paging.TotalCount)
	assert.Equal(t, 1, paging.PageNumber)
	assert.Equal(t, 20, paging.PageSize)

	// abnormal case: has data in nft_user_amounts but not in nft_assets
	walletAddressesByChainID = map[string][]string{
		"ronin": {"******************************************"},
	}
	nfts, paging, errCode, err = Nfts(ctx, params, walletAddressesByChainID)
	assert.Nil(t, err)
	assert.Equal(t, 0, errCode)
	assert.NotNil(t, nfts)
	assert.Len(t, *nfts, 0)
	assert.NotNil(t, paging)
	assert.Equal(t, int64(0), paging.TotalCount)
	assert.Equal(t, 1, paging.PageNumber)
	assert.Equal(t, 20, paging.PageSize)
}

func TestBatchUpdateNftFloorPrice(t *testing.T) {
	ctx := context.Background()
	Reset()

	collections := []model.NftCollection{}
	collection := model.NftCollection{
		Slug:                  "big_pp_haver3 Collection",
		FloorPriceSymbol:      "ETH",
		ModifiedAt:            time.Now(),
		TotalVolume:           0,
		ThirtyDayVolume:       0,
		Stats:                 nil,
		AveragePrice:          100,
		OneDayAveragePrice:    101,
		SevenDayAveragePrice:  102,
		ThirtyDayAveragePrice: 103,
	}
	collection.SetFloorPrice(50000000004434)
	collections = append(collections, collection)
	collection = model.NftCollection{
		Slug:             "tanjakan3view",
		FloorPriceSymbol: "ETH",
		ModifiedAt:       time.Now(),
		TotalVolume:      0,
		ThirtyDayVolume:  0,
		Stats:            nil,
	}
	collection.SetFloorPrice(0.0000000001)
	collections = append(collections, collection)
	err := BatchUpdateNftFloorPrice(ctx, &collections)
	assert.Nil(t, err)

	nftCollection, err := NftCollection(ctx, "big_pp_haver3 Collection")
	assert.Nil(t, err)
	assert.NotNil(t, nftCollection)
	assert.Equal(t, 1000000.0, nftCollection.FloorPrice)
	assert.Equal(t, 100.0, nftCollection.AveragePrice)
	assert.Equal(t, 101.0, nftCollection.OneDayAveragePrice)
	assert.Equal(t, 102.0, nftCollection.SevenDayAveragePrice)
	assert.Equal(t, 103.0, nftCollection.ThirtyDayAveragePrice)

	nftCollection, err = NftCollection(ctx, "tanjakan3view")
	assert.Nil(t, err)
	assert.NotNil(t, nftCollection)
	assert.Equal(t, 0.0, nftCollection.FloorPrice)
}

func TestSaveNftAssetSameColumns(t *testing.T) {
	ctx := context.Background()
	Reset()
	contractAddresses := []string{util.RandString(42), util.RandString(42), util.RandString(42)}
	nftDescriptions := []string{"1111111", "2222222", "333333"}
	newNftDescriptions := []string{"new1111111", "new2222222", "new333333"}

	nftAssets := []model.NftAsset{
		{
			ChainID:         model.ChainIDPolygon,
			ContractAddress: contractAddresses[0],
			TokenID:         "1",
			NftDescription:  &nftDescriptions[0],
		},
		{
			ChainID:         model.ChainIDPolygon,
			ContractAddress: contractAddresses[1],
			TokenID:         "1",
			NftDescription:  &nftDescriptions[1],
		},
		{
			ChainID:         model.ChainIDPolygon,
			ContractAddress: contractAddresses[2],
			TokenID:         "1",
			NftDescription:  &nftDescriptions[2],
		},
	}

	// first save
	err := SaveNftAsset(ctx, &nftAssets)
	assert.Nil(t, err)

	// test update
	for i := range nftAssets {
		asset, err := GetNftAsset(ctx, model.ChainIDPolygon, contractAddresses[i], "1")
		assert.Nil(t, err)
		assert.NotNil(t, asset)
		assert.Equal(t, nftDescriptions[i], *asset.NftDescription)
	}

	// overwrite again
	for i := range nftAssets {
		nftAssets[i].NftDescription = &newNftDescriptions[i]
	}

	err = SaveNftAsset(ctx, &nftAssets)
	assert.Nil(t, err)

	// test update
	for i := range nftAssets {
		asset, err := GetNftAsset(ctx, model.ChainIDPolygon, contractAddresses[i], "1")
		assert.Nil(t, err)
		assert.NotNil(t, asset)
		assert.Equal(t, newNftDescriptions[i], *asset.NftDescription)
	}

}

func TestSaveNftAssetNotSameColumns(t *testing.T) {
	s := assert.New(t)
	ctx := context.Background()
	Reset()

	nftAssets := []model.NftAsset{
		{
			ChainID:         model.ChainIDPolygon,
			ContractAddress: util.RandString(42),
			TokenID:         "1",
			NftDescription:  util.Ptr("description"),
		},
		{
			ChainID:         model.ChainIDPolygon,
			ContractAddress: util.RandString(42),
			TokenID:         "1",
			CollectionSlug:  util.Ptr("collection_slug"),
		},
		{
			ChainID:         model.ChainIDPolygon,
			ContractAddress: util.RandString(42),
			TokenID:         "1",
			LastPrice:       1.0,
		},
		{
			ChainID:         model.ChainIDPolygon,
			ContractAddress: util.RandString(42),
			TokenID:         "1",
			LastPrice:       2.0,
		},
	}

	// first save
	err := SaveNftAsset(ctx, &nftAssets)
	s.NoError(err)

	{
		asset, err := GetNftAsset(ctx, model.ChainIDPolygon, nftAssets[0].ContractAddress, "1")
		fmt.Println("asset", asset)
		s.NoError(err)
		s.NotNil(asset)
		s.Equal(*nftAssets[0].NftDescription, *asset.NftDescription)
		s.Nil(asset.CollectionSlug)
		s.Equal(float64(0), asset.LastPrice)
	}

	{
		asset, err := GetNftAsset(ctx, model.ChainIDPolygon, nftAssets[1].ContractAddress, "1")
		fmt.Println("asset", asset)
		s.NoError(err)
		s.NotNil(t, asset)
		s.Nil(asset.NftDescription)
		s.Equal(*nftAssets[1].CollectionSlug, *asset.CollectionSlug)
		s.Equal(float64(0), asset.LastPrice)
	}

	{
		asset, err := GetNftAsset(ctx, model.ChainIDPolygon, nftAssets[2].ContractAddress, "1")
		fmt.Println("asset", asset)
		s.NoError(err)
		s.NotNil(t, asset)
		s.Nil(asset.NftDescription)
		s.Nil(asset.CollectionSlug)
		s.Equal(float64(1), asset.LastPrice)
	}

	{
		asset, err := GetNftAsset(ctx, model.ChainIDPolygon, nftAssets[3].ContractAddress, "1")
		fmt.Println("asset", asset)
		s.NoError(err)
		s.NotNil(t, asset)
		s.Nil(asset.NftDescription)
		s.Nil(asset.CollectionSlug)
		s.Equal(float64(2), asset.LastPrice)
	}
}

// func NftsReceived(ctx context.Context, params *NftsReceivedParams, hideSpamNft *bool) (*[]*VNftReceived, int, error) {

func TestNftsReceived(t *testing.T) {
	clientID := "20991a3ae83233d6de85d62906d71fd3"

	ctx := context.Background()

	Reset()
	users, uids := dbtest.Users()
	uid := uids[0]

	_ = GormRepo().BatchSetUsers(context.Background(), users)

	assert.NoError(t, rdbtest.CreateNftUserAmounts(database))
	assert.NoError(t, rdbtest.CreateNftAssets(database))
	assert.NoError(t, rdbtest.CreateClientUsers(Get(), uid))

	params := &NftsReceivedParams{
		UID:           uid,
		StartedAt:     time.Now().AddDate(0, -1, 0).Unix(),
		StartedAtTime: time.Now().AddDate(0, -1, 0),
		ClientID:      clientID,
	}

	// hideSpamNft := false
	{
		hideSpamNft := util.Ptr(false)
		nfts, _, err := NftsReceived(ctx, params, hideSpamNft)
		assert.NoError(t, err)
		assert.NotNil(t, nfts)
		assert.Len(t, *nfts, 1)
	}

	// hideSpamNft := true
	{
		hideSpamNft := util.Ptr(true)
		nfts, _, err := NftsReceived(ctx, params, hideSpamNft)
		assert.NoError(t, err)
		assert.NotNil(t, nfts)
		assert.Len(t, *nfts, 0)
	}
}

func TestUpdateNftAssetCollectionDetailForNonExistingSlugs(t *testing.T) {
	ctx := context.Background()
	Reset()
	now := time.Now()
	// create nft_collections
	err := database.Create([]*model.NftCollection{
		{
			Slug:             "bored-ape-yacht-club",
			FloorPriceSymbol: "ETH",
			ModifiedAt:       now,
			TotalVolume:      0,
			ThirtyDayVolume:  0,
			Stats:            nil,
		},
	}).Error
	assert.Nil(t, err)
	// create nft_assets
	err = database.Create([]*model.NftAsset{
		{
			ChainID:            "ronin",
			ContractAddress:    "******************************************",
			TokenID:            "3724537",
			CollectionSlug:     util.Ptr("******************************************"),
			ContractSchemaName: util.Ptr("ERC721"),
			ImageURL:           "https://assets.axieinfinity.com/axies/3724537/axie/axie-full-transparent.png",
			ImagePreviewURL:    "https://assets.axieinfinity.com/axies/3724537/axie/axie-full-transparent.png",
			ModifiedAt:         now,
			Name:               "Bulbasaur",
			CollectionName:     util.Ptr("Axie Infinity Axies"),
			UpdateRetryCnt:     0,
			UpdateRetryAfter:   now,
			UpdatePriority:     0,
		},
		{
			// Bored Ape Yacht Club #3740
			ChainID:            "ethereum",
			ContractAddress:    "******************************************",
			TokenID:            "3740",
			CollectionSlug:     util.Ptr("bored-ape-yacht-club"),
			ContractSchemaName: util.Ptr("ERC721"),
			ImageURL:           "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840",
			ImagePreviewURL:    "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840",
			ModifiedAt:         now,
			Name:               "Bored Ape Yacht Club #3740",
			CollectionName:     util.Ptr("Bored Ape Yacht Club"),
			CollectionImageURL: util.Ptr("https://i.seadn.io/gae/Ju9CkWtV-1Okvf45wo8UctR-M9He2PjILP0oOvxE89AyiPPGtrR3gysu1Zgy0hjd2xKIgjJJtWIc0ybj4Vd7wv8t3pxDGHoJBzDB?auto=format&dpr=1&w=3840"),
			UpdateRetryCnt:     0,
			UpdateRetryAfter:   now.Add(time.Hour),
			UpdatePriority:     0,
		},
		{
			// Bored Ape Yacht Club #3741
			ChainID:            "ethereum",
			ContractAddress:    "******************************************",
			TokenID:            "3741",
			CollectionSlug:     util.Ptr("bored-ape-yacht-club"),
			ContractSchemaName: util.Ptr("ERC721"),
			ImageURL:           "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840",
			ImagePreviewURL:    "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840",
			ModifiedAt:         now,
			Name:               "Bored Ape Yacht Club #3741",
			CollectionName:     util.Ptr("Bored Ape Yacht Club"),
			CollectionImageURL: util.Ptr("https://i.seadn.io/gae/Ju9CkWtV-1Okvf45wo8UctR-M9He2PjILP0oOvxE89AyiPPGtrR3gysu1Zgy0hjd2xKIgjJJtWIc0ybj4Vd7wv8t3pxDGHoJBzDB?auto=format&dpr=1&w=3840"),
			UpdateRetryCnt:     0,
			UpdateRetryAfter:   now.Add(2 * time.Hour),
			UpdatePriority:     0,
		},
		{
			// non-existing-slug-1
			ChainID:            "ethereum",
			ContractAddress:    "0x00001",
			TokenID:            "1",
			CollectionSlug:     util.Ptr("non-existing-slug-1"),
			ContractSchemaName: util.Ptr("ERC721"),
			ImageURL:           "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840",
			ImagePreviewURL:    "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840",
			ModifiedAt:         now,
			Name:               "non-existing-slug-1-1",
			UpdateRetryCnt:     0,
			UpdateRetryAfter:   now.Add(3 * time.Hour),
			UpdatePriority:     0,
		},
		{
			// non-existing-slug-1
			ChainID:            "ethereum",
			ContractAddress:    "0x00001",
			TokenID:            "2",
			CollectionSlug:     util.Ptr("non-existing-slug-1"),
			ContractSchemaName: util.Ptr("ERC721"),
			ImageURL:           "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840",
			ImagePreviewURL:    "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840",
			ModifiedAt:         now,
			Name:               "non-existing-slug-1-2",
			UpdateRetryCnt:     0,
			UpdateRetryAfter:   now.Add(4 * time.Hour),
			UpdatePriority:     0,
		},
		{
			// non-existing-slug-2
			ChainID:            "ethereum",
			ContractAddress:    "0x00002",
			TokenID:            "1",
			CollectionSlug:     util.Ptr("non-existing-slug-2"),
			ContractSchemaName: util.Ptr("ERC721"),
			ImageURL:           "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840",
			ImagePreviewURL:    "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840",
			ModifiedAt:         now,
			Name:               "non-existing-slug-2-1",
			UpdateRetryCnt:     0,
			UpdateRetryAfter:   now.Add(5 * time.Hour),
			UpdatePriority:     0,
		},
	}).Error
	assert.Nil(t, err)

	// get non existing slugs
	slugs := []string{"non-existing-slug-1", "non-existing-slug-2", "bored-ape-yacht-club"}
	newSlugs, err := GetNonExistingNftCollectionSlugs(ctx, slugs)
	assert.Nil(t, err)
	sort.Strings(newSlugs)
	assert.Equal(t, []string{"non-existing-slug-1", "non-existing-slug-2"}, newSlugs)

	// update nft_assets
	for i, newSlug := range newSlugs {
		nftAsset := model.NftAsset{
			CollectionName:     util.Ptr(fmt.Sprintf("collection-name-%d", i+1)),
			CollectionImageURL: util.Ptr(fmt.Sprintf("collection-image-url-%d", i+1)),
		}
		assert.NoError(t, UpdateNftAssetCollectionDetailBySlug(ctx, nftAsset, newSlug))
	}

	// check nft_assets
	nftAssets := []model.NftAsset{}
	assert.NoError(t, database.Order("update_retry_after asc").Find(&nftAssets).Error)
	assert.Len(t, nftAssets, 6)
	// assert ronin assets
	assert.Equal(t, "ronin", nftAssets[0].ChainID)
	assert.Equal(t, "******************************************", nftAssets[0].ContractAddress)
	assert.Equal(t, "3724537", nftAssets[0].TokenID)
	assert.Equal(t, "Axie Infinity Axies", *nftAssets[0].CollectionName)
	assert.Equal(t, "https://assets.axieinfinity.com/axies/3724537/axie/axie-full-transparent.png", nftAssets[0].ImageURL)
	assert.Equal(t, "https://assets.axieinfinity.com/axies/3724537/axie/axie-full-transparent.png", nftAssets[0].ImagePreviewURL)
	assert.Equal(t, "Bulbasaur", nftAssets[0].Name)
	assert.Equal(t, "******************************************", *nftAssets[0].CollectionSlug)
	assert.Equal(t, "ERC721", *nftAssets[0].ContractSchemaName)
	assert.Equal(t, int32(0), nftAssets[0].UpdateRetryCnt)
	assert.Equal(t, int32(0), nftAssets[0].UpdatePriority)
	// assert bored-ape-yacht-club assets
	assert.Equal(t, "ethereum", nftAssets[1].ChainID)
	assert.Equal(t, "******************************************", nftAssets[1].ContractAddress)
	assert.Equal(t, "3740", nftAssets[1].TokenID)
	assert.Equal(t, "Bored Ape Yacht Club", *nftAssets[1].CollectionName)
	assert.Equal(t, "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840", nftAssets[1].ImageURL)
	assert.Equal(t, "https://i.seadn.io/gae/VrsplkWxN-lzGbq9jmCgs2cY4_WW4ZNEYXkjR0fJHDg-VM-2SeXm3wGQtAlHAsjfH31F0ilfTng7G4AxXOKz0aabamMifLTXrEefBtQ?auto=format&dpr=1&w=3840", nftAssets[1].ImagePreviewURL)
	assert.Equal(t, "Bored Ape Yacht Club #3740", nftAssets[1].Name)
	assert.Equal(t, "bored-ape-yacht-club", *nftAssets[1].CollectionSlug)
	assert.Equal(t, "ERC721", *nftAssets[1].ContractSchemaName)
	assert.Equal(t, int32(0), nftAssets[1].UpdateRetryCnt)
	assert.Equal(t, int32(0), nftAssets[1].UpdatePriority)
	// assert bored-ape-yacht-club assets
	assert.Equal(t, "ethereum", nftAssets[2].ChainID)
	assert.Equal(t, "******************************************", nftAssets[2].ContractAddress)
	assert.Equal(t, "3741", nftAssets[2].TokenID)
	assert.Equal(t, "Bored Ape Yacht Club", *nftAssets[2].CollectionName)
	assert.Equal(t, "https://i.seadn.io/gae/Ju9CkWtV-1Okvf45wo8UctR-M9He2PjILP0oOvxE89AyiPPGtrR3gysu1Zgy0hjd2xKIgjJJtWIc0ybj4Vd7wv8t3pxDGHoJBzDB?auto=format&dpr=1&w=3840", *nftAssets[2].CollectionImageURL)
	assert.Equal(t, "Bored Ape Yacht Club #3741", nftAssets[2].Name)
	assert.Equal(t, "bored-ape-yacht-club", *nftAssets[2].CollectionSlug)
	assert.Equal(t, "ERC721", *nftAssets[2].ContractSchemaName)
	assert.Equal(t, int32(0), nftAssets[2].UpdateRetryCnt)
	assert.Equal(t, int32(0), nftAssets[2].UpdatePriority)
	// assert non-existing-slug-1 assets
	assert.Equal(t, "ethereum", nftAssets[3].ChainID)
	assert.Equal(t, "0x00001", nftAssets[3].ContractAddress)
	assert.Equal(t, "1", nftAssets[3].TokenID)
	assert.Equal(t, "collection-name-1", *nftAssets[3].CollectionName)
	assert.Equal(t, "collection-image-url-1", *nftAssets[3].CollectionImageURL)
	assert.Equal(t, "non-existing-slug-1-1", nftAssets[3].Name)
	assert.Equal(t, "non-existing-slug-1", *nftAssets[3].CollectionSlug)
	assert.Equal(t, "ERC721", *nftAssets[3].ContractSchemaName)
	assert.Equal(t, int32(0), nftAssets[3].UpdateRetryCnt)
	assert.Equal(t, int32(0), nftAssets[3].UpdatePriority)
	// assert non-existing-slug-1 assets
	assert.Equal(t, "ethereum", nftAssets[4].ChainID)
	assert.Equal(t, "0x00001", nftAssets[4].ContractAddress)
	assert.Equal(t, "2", nftAssets[4].TokenID)
	assert.Equal(t, "collection-name-1", *nftAssets[4].CollectionName)
	assert.Equal(t, "collection-image-url-1", *nftAssets[4].CollectionImageURL)
	assert.Equal(t, "non-existing-slug-1-2", nftAssets[4].Name)
	assert.Equal(t, "non-existing-slug-1", *nftAssets[4].CollectionSlug)
	assert.Equal(t, "ERC721", *nftAssets[4].ContractSchemaName)
	assert.Equal(t, int32(0), nftAssets[4].UpdateRetryCnt)
	assert.Equal(t, int32(0), nftAssets[4].UpdatePriority)
	// assert non-existing-slug-2 assets
	assert.Equal(t, "ethereum", nftAssets[5].ChainID)
	assert.Equal(t, "0x00002", nftAssets[5].ContractAddress)
	assert.Equal(t, "1", nftAssets[5].TokenID)
	assert.Equal(t, "collection-name-2", *nftAssets[5].CollectionName)
	assert.Equal(t, "collection-image-url-2", *nftAssets[5].CollectionImageURL)
	assert.Equal(t, "non-existing-slug-2-1", nftAssets[5].Name)
	assert.Equal(t, "non-existing-slug-2", *nftAssets[5].CollectionSlug)
	assert.Equal(t, "ERC721", *nftAssets[5].ContractSchemaName)
	assert.Equal(t, int32(0), nftAssets[5].UpdateRetryCnt)
	assert.Equal(t, int32(0), nftAssets[5].UpdatePriority)
}
