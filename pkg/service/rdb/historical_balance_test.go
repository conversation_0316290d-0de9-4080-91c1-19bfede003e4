package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/stretchr/testify/assert"
)

func TestGetPastAssetBalances(t *testing.T) {
	Reset()

	// Seed data
	err := dbtest.CreateHistoricalBalances(Get())
	assert.NoError(t, err)

	// Define test cases
	testCases := []struct {
		name             string
		chains           []domain.Chain
		addresses        []domain.Address
		types            []domain.AssetType
		from             time.Time
		expectedBalances map[domain.ChainAddress]map[domain.AssetType]float64
		expectError      bool
	}{
		{
			name:   "Empty Asset Types",
			chains: []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Tron},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
			},
			types:            []domain.AssetType{},
			from:             time.Date(2024, 3, 12, 0, 0, 0, 0, time.UTC),
			expectedBalances: map[domain.ChainAddress]map[domain.AssetType]float64{},
			expectError:      false,
		},
		{
			name:   "No Matching Address",
			chains: []domain.Chain{domain.Tron, domain.Ethereum, domain.Arbitrum},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
			},
			types:            []domain.AssetType{domain.AssetTypeToken},
			from:             time.Date(2024, 3, 12, 0, 0, 0, 0, time.UTC),
			expectedBalances: map[domain.ChainAddress]map[domain.AssetType]float64{},
			expectError:      false,
		},
		{
			name:   "Get balances even long before 2024-03-12",
			chains: []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Tron},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			types: []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeDefi},
			from:  time.Date(2004, 3, 11, 0, 0, 0, 0, time.UTC), // 20 years ago
			expectedBalances: map[domain.ChainAddress]map[domain.AssetType]float64{
				{
					Chain:   domain.Ethereum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					domain.AssetTypeToken: 200.0,
					domain.AssetTypeDefi:  5121.33,
				},
				{
					Chain:   domain.Arbitrum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					domain.AssetTypeDefi: 1249.327,
				},
				{
					Chain:   domain.Tron,
					Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
				}: {
					domain.AssetTypeToken: 500.0,
				},
			},
			expectError: false,
		},
		{
			name:   "Balances after 2024-03-12",
			chains: []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Tron},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			types: []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeDefi, domain.AssetTypeNft},
			from:  time.Date(2024, 3, 12, 0, 0, 0, 0, time.UTC),
			expectedBalances: map[domain.ChainAddress]map[domain.AssetType]float64{
				{
					Chain:   domain.Ethereum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					domain.AssetTypeToken: 200.0,
					domain.AssetTypeDefi:  5121.33,
					domain.AssetTypeNft:   3.0,
				},
				{
					Chain:   domain.Arbitrum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					domain.AssetTypeDefi: 1249.327,
				},
				{
					Chain:   domain.Tron,
					Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
				}: {
					domain.AssetTypeToken: 500.0,
				},
			},
			expectError: false,
		},
		{
			name:   "Balances after 2024-03-13",
			chains: []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Tron},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			types: []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeDefi, domain.AssetTypeNft},
			from:  time.Date(2024, 3, 13, 0, 0, 0, 0, time.UTC),
			expectedBalances: map[domain.ChainAddress]map[domain.AssetType]float64{
				{
					Chain:   domain.Ethereum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					domain.AssetTypeToken: 2001.0,
					domain.AssetTypeNft:   31.0,
				},
				{
					Chain:   domain.Arbitrum,
					Address: domain.NewEvmAddress("******************************************"),
				}: {
					domain.AssetTypeDefi: 12491.327,
				},
				{
					Chain:   domain.Tron,
					Address: domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
				}: {
					domain.AssetTypeToken: 5001.0,
				},
			},
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			balances, err := GormRepo().GetPastAssetBalances(context.Background(), tc.chains, tc.addresses, tc.types, tc.from)
			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tc.expectedBalances, balances)
			}
		})
	}
}

func TestGetPastAssetsTotalUsdValue(t *testing.T) {
	Reset()

	// Seed data
	err := dbtest.CreateHistoricalBalances(Get())
	assert.NoError(t, err)

	// Define test cases
	testCases := []struct {
		name             string
		chains           []domain.Chain
		addresses        []domain.Address
		types            []domain.AssetType
		from             time.Time
		expectedUsdValue float64
		expectError      bool
	}{
		{
			name:   "Empty Asset Types",
			chains: []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Tron},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
			},
			types:            []domain.AssetType{},
			from:             time.Date(2024, 3, 12, 0, 0, 0, 0, time.UTC),
			expectedUsdValue: 0.0,
			expectError:      false,
		},
		{
			name:   "No Matching Address",
			chains: []domain.Chain{domain.Tron, domain.Ethereum, domain.Arbitrum},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
			},
			types:            []domain.AssetType{domain.AssetTypeToken},
			from:             time.Date(2024, 3, 12, 0, 0, 0, 0, time.UTC),
			expectedUsdValue: 0.0,
			expectError:      false,
		},
		{
			name:   "Get balances even long before 2024-03-12",
			chains: []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Tron},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			types:            []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeDefi},
			from:             time.Date(2004, 3, 11, 0, 0, 0, 0, time.UTC), // 20 years ago
			expectedUsdValue: 200.0 + 5121.33 + 1249.327 + 500.0,
			expectError:      false,
		},
		{
			name:   "Balances after 2024-03-12",
			chains: []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Tron},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			types:            []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeDefi, domain.AssetTypeNft},
			from:             time.Date(2024, 3, 12, 0, 0, 0, 0, time.UTC),
			expectedUsdValue: 200.0 + 5121.33 + 3.0 + 1249.327 + 500.0,
			expectError:      false,
		},
		{
			name:   "Balances after 2024-03-13",
			chains: []domain.Chain{domain.Ethereum, domain.Arbitrum, domain.Tron},
			addresses: []domain.Address{
				domain.NewEvmAddress("******************************************"),
				domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
			},
			types:            []domain.AssetType{domain.AssetTypeToken, domain.AssetTypeDefi, domain.AssetTypeNft},
			from:             time.Date(2024, 3, 13, 0, 0, 0, 0, time.UTC),
			expectedUsdValue: 2001.0 + 31.0 + 12491.327 + 5001.0,
			expectError:      false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			balances, err := GormRepo().GetPastAssetsTotalUsdValue(context.Background(), tc.chains, tc.addresses, tc.types, tc.from)
			if tc.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				if tc.expectedUsdValue == 0 {
					assert.InDelta(t, tc.expectedUsdValue, balances, 0.00001)
				} else {
					assert.InEpsilon(t, tc.expectedUsdValue, balances, 0.00001)
				}
			}
		})
	}
}
