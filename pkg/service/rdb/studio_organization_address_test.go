package rdb

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/stretchr/testify/suite"
)

type StudioOrgAddressRepoTestSuite struct {
	suite.Suite
	repo domain.StudioOrgRepo // Using the interface type, GormRepo() will provide the concrete rdb.Repo
	ctx  context.Context
}

func (s *StudioOrgAddressRepoTestSuite) SetupSuite() {
	Init() // Assuming this initializes the test DB connection
	s.repo = GormRepo()
	s.ctx = context.Background()
}

func (s *StudioOrgAddressRepoTestSuite) SetupTest() {
	// Reset data before each test to ensure a clean state
	// This usually involves deleting data from relevant tables
	// For simplicity, we might delete all from studio_organization_imported_addresses
	// Ensure this doesn't conflict with other tests if run in parallel or if Reset() is global.
	// A more robust Reset would be to use transactions and rollback, or specific deletions.
	err := Get().Exec("DELETE FROM " + model.TableNameStudioOrganizationImportedAddress).Error
	s.Require().NoError(err, "Failed to clear imported addresses table")
}

func TestStudioOrgAddressRepoSuite(t *testing.T) {
	suite.Run(t, new(StudioOrgAddressRepoTestSuite))
}

func (s *StudioOrgAddressRepoTestSuite) createImportedAddress(orgID int, chain, address, userID string, isDefault bool) *model.StudioOrganizationImportedAddress {
	addr := model.StudioOrganizationImportedAddress{
		OrganizationID:        orgID,
		Chain:                 chain,
		Address:               address,
		DefaultReceiveAddress: isDefault,
		AddedByUserID:         userID,
		AddedAt:               time.Now(),
		UpdatedAt:             time.Now(),
	}
	err := Get().Create(&addr).Error
	s.Require().NoError(err)
	return &addr
}

func (s *StudioOrgAddressRepoTestSuite) TestSetDefaultImportedAddress_SetNewDefault() {
	orgID := 1
	chain := "ETH"
	userID := "user-test-1"

	// Create initial addresses
	addr1 := s.createImportedAddress(orgID, chain, "0xAddr1", userID, true)   // Initially default
	addr2 := s.createImportedAddress(orgID, chain, "0xAddr2", userID, false)  // Not default
	addr3 := s.createImportedAddress(orgID, "OTHER", "0xAddr3", userID, true) // Different chain, should be unaffected. Shortened "OTHER_CHAIN" to "OTHER"
	addr4 := s.createImportedAddress(orgID+1, chain, "0xAddr4", userID, true) // Different org, should be unaffected

	// Action: Set addr2 as the new default for orgID and chain ETH
	err := s.repo.SetDefaultImportedAddress(s.ctx, orgID, addr2.ID, true)
	s.Require().NoError(err)

	// Assertions
	// Check addr1 (was default, should now be false)
	var updatedAddr1 model.StudioOrganizationImportedAddress
	err = Get().First(&updatedAddr1, addr1.ID).Error
	s.Require().NoError(err)
	s.False(updatedAddr1.DefaultReceiveAddress, "Addr1 should no longer be default")

	// Check addr2 (was false, should now be true)
	var updatedAddr2 model.StudioOrganizationImportedAddress
	err = Get().First(&updatedAddr2, addr2.ID).Error
	s.Require().NoError(err)
	s.True(updatedAddr2.DefaultReceiveAddress, "Addr2 should now be default")

	// Check addr3 (different chain, should be unaffected)
	var updatedAddr3 model.StudioOrganizationImportedAddress
	err = Get().First(&updatedAddr3, addr3.ID).Error
	s.Require().NoError(err)
	s.True(updatedAddr3.DefaultReceiveAddress, "Addr3 (different chain) should remain default")

	// Check addr4 (different org, should be unaffected)
	var updatedAddr4 model.StudioOrganizationImportedAddress
	err = Get().First(&updatedAddr4, addr4.ID).Error
	s.Require().NoError(err)
	s.True(updatedAddr4.DefaultReceiveAddress, "Addr4 (different org) should remain default")
}

func (s *StudioOrgAddressRepoTestSuite) TestSetDefaultImportedAddress_UnsetOnlyDefault() {
	orgID := 2
	chain := "SOL"
	userID := "user-test-2"

	addr1 := s.createImportedAddress(orgID, chain, "SolAddr1", userID, true) // Initially default

	// Action: Set addr1 to non-default
	err := s.repo.SetDefaultImportedAddress(s.ctx, orgID, addr1.ID, false)
	s.Require().NoError(err)

	// Assertions
	var updatedAddr1 model.StudioOrganizationImportedAddress
	err = Get().First(&updatedAddr1, addr1.ID).Error
	s.Require().NoError(err)
	s.False(updatedAddr1.DefaultReceiveAddress, "Addr1 should be non-default")
}

func (s *StudioOrgAddressRepoTestSuite) TestSetDefaultImportedAddress_NoOtherDefaultExists() {
	orgID := 3
	chain := "MATIC"
	userID := "user-test-3"

	addr1 := s.createImportedAddress(orgID, chain, "MaticAddr1", userID, false) // Initially not default

	// Action: Set addr1 as default
	err := s.repo.SetDefaultImportedAddress(s.ctx, orgID, addr1.ID, true)
	s.Require().NoError(err)

	// Assertions
	var updatedAddr1 model.StudioOrganizationImportedAddress
	err = Get().First(&updatedAddr1, addr1.ID).Error
	s.Require().NoError(err)
	s.True(updatedAddr1.DefaultReceiveAddress, "Addr1 should now be default")
}

func (s *StudioOrgAddressRepoTestSuite) TestSetDefaultImportedAddress_AddressNotFound() {
	orgID := 4
	nonExistentAddressID := 99999

	err := s.repo.SetDefaultImportedAddress(s.ctx, orgID, nonExistentAddressID, true)
	s.Require().Error(err)
	// Check if the error is what the RDB layer returns for "not found"
	// The RDB layer currently returns errors.New("imported address not found")
	s.EqualError(errors.New("imported address not found"), err.Error(), "Error should indicate address not found")

	// Or, if the RDB layer is changed to return gorm.ErrRecordNotFound directly from the transaction:
	// s.True(errors.Is(err, gorm.ErrRecordNotFound), "Error should be gorm.ErrRecordNotFound")
}

func (s *StudioOrgAddressRepoTestSuite) TestSetDefaultImportedAddress_SetToFalseWhenNoOtherDefault() {
	orgID := 5
	chain := "AVAX"
	userID := "user-test-5"

	addr1 := s.createImportedAddress(orgID, chain, "AvaxAddr1", userID, false)

	// Action: Explicitly set addr1 to non-default (even though it already is)
	err := s.repo.SetDefaultImportedAddress(s.ctx, orgID, addr1.ID, false)
	s.Require().NoError(err)

	// Assertions
	var updatedAddr1 model.StudioOrganizationImportedAddress
	err = Get().First(&updatedAddr1, addr1.ID).Error
	s.Require().NoError(err)
	s.False(updatedAddr1.DefaultReceiveAddress, "Addr1 should remain non-default")
}

func (s *StudioOrgAddressRepoTestSuite) TestSetDefaultImportedAddress_Idempotency_SetToTrue() {
	orgID := 6
	chain := "FTM"
	userID := "user-test-6"

	addr1 := s.createImportedAddress(orgID, chain, "FtmAddr1", userID, true) // Already default

	// Action: Set addr1 as default again
	err := s.repo.SetDefaultImportedAddress(s.ctx, orgID, addr1.ID, true)
	s.Require().NoError(err)

	// Assertions
	var updatedAddr1 model.StudioOrganizationImportedAddress
	err = Get().First(&updatedAddr1, addr1.ID).Error
	s.Require().NoError(err)
	s.True(updatedAddr1.DefaultReceiveAddress, "Addr1 should remain default")

	// Ensure no other record was accidentally modified if there were any (none in this specific test setup for simplicity)
}

func (s *StudioOrgAddressRepoTestSuite) TestGetImportedAddressesByOrgID() {
	orgID1 := 7
	orgID2 := 8
	userID := "user-test-7"

	// Create addresses for orgID1
	addr1Org1 := s.createImportedAddress(orgID1, "ETH", "0xOrg1Addr1", userID, true)
	s.createImportedAddress(orgID1, "ETH", "0xOrg1Addr2", userID, false)
	// Create address for orgID2 (should not be returned for orgID1)
	s.createImportedAddress(orgID2, "ETH", "0xOrg2Addr1", userID, true)

	retrievedAddresses, err := s.repo.GetImportedAddressesByOrgID(s.ctx, orgID1)
	s.Require().NoError(err)
	s.Require().Len(retrievedAddresses, 2, "Should retrieve 2 addresses for orgID1")

	foundAddr1 := false
	for _, addr := range retrievedAddresses {
		s.Equal(orgID1, addr.OrganizationID)
		if addr.ID == addr1Org1.ID {
			foundAddr1 = true
			s.True(addr.DefaultReceiveAddress, "Addr1Org1 should be default")
		} else {
			s.False(addr.DefaultReceiveAddress, "Other addresses for orgID1 should not be default in this test case")
		}
	}
	s.True(foundAddr1, "Address 1 for OrgID1 not found in results")

	// Test for an org with no addresses
	nonExistentOrgID := 99
	retrievedAddressesNonExistent, err := s.repo.GetImportedAddressesByOrgID(s.ctx, nonExistentOrgID)
	s.Require().NoError(err)
	s.Require().Empty(retrievedAddressesNonExistent, "Should retrieve no addresses for a non-existent org or org with no addresses")
}
