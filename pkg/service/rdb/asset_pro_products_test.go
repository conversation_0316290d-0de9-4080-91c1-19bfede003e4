package rdb

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
)

type testUpdateProductSuite struct {
	suite.Suite
	Repo domain.AssetProProductRepo
}

func (s *testUpdateProductSuite) SetupSuite() {
	s.Repo = GormRepo()
	Reset()
	s.NoError(rdbtest.CreateAssetProOrdersDefault(Get()))
}

func (s *testUpdateProductSuite) TestNormal() {
	orgID := 1
	productID := 1
	operatorUID := "testuser2"
	ctx := context.Background()
	params := &domain.UpdateProductParams{
		IsPublished:               false,
		Name:                      "new_name",
		Price:                     decimal.NewFromFloat(28.5),
		OrderLimitsTo:             util.Ptr(decimal.NewFromFloat(1000.5)),
		FeeType:                   "fee_included",
		ProportionalFeePercentage: util.Ptr(decimal.NewFromFloat(1)),
		ProportionalMinimumFee:    util.Ptr(decimal.NewFromFloat(50.12)),
	}
	s.Nil(s.Repo.UpdateProductByOperator(ctx, orgID, operatorUID, productID, params))
	newProduct, kgErr := s.Repo.GetProductByID(ctx, productID)
	s.Nil(kgErr)
	s.Equal(operatorUID, newProduct.Operator.UID)
	s.Equal(params.IsPublished, newProduct.IsPublished)
	s.Equal("", newProduct.AssetProProductInfo.Image)
	s.Equal(params.Name, newProduct.AssetProProductInfo.Name)
	s.True(params.Price.Equal(*newProduct.AssetProProductInfo.Price))
	s.Nil(newProduct.OrderLimitsFrom)
	s.True(params.OrderLimitsTo.Equal(*newProduct.OrderLimitsTo))
	s.True(decimal.NewFromFloat(1230.5).Equal(*newProduct.Stock))
	s.Equal(params.FeeType, newProduct.FeeType)
	s.True(params.ProportionalFeePercentage.Equal(*newProduct.ProportionalFeePercentage))
	s.True(params.ProportionalMinimumFee.Equal(*newProduct.ProportionalMinimumFee))

}

func TestUpdateProductSuite(t *testing.T) {
	suite.Run(t, new(testUpdateProductSuite))
}
