package rdb

import (
	"context"
	"testing"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestEphemeralNoteRepo(t *testing.T) {
	Reset()

	ctx := context.Background()
	r := GormRepo()

	// Test EphemeralOwner methods
	t.Run("EphemeralOwner", func(t *testing.T) {
		t.Parallel()
		// Test GetUnusedEphemeralOwner when no owners exist
		unusedOwner, err := r.GetUnusedEphemeralOwner(ctx, domain.EphemeralOwnerTypeEvm)
		assert.NoError(t, err)
		assert.Equal(t, common.Address{}, unusedOwner)

		// Test SaveEphemeralOwner
		owner := &domain.EphemeralOwner{
			Address:    common.HexToAddress("******************************************"),
			PrivateKey: []byte("test-private-key"),
			IsUsing:    false,
			Type:       domain.EphemeralOwnerTypeEvm,
		}
		err = r.SaveEphemeralOwner(ctx, owner)
		assert.NoError(t, err)

		// Test GetUnusedEphemeralOwner after saving an unused owner
		unusedOwner, err = r.GetUnusedEphemeralOwner(ctx, domain.EphemeralOwnerTypeEvm)
		assert.NoError(t, err)
		assert.Equal(t, owner.Address, unusedOwner)

		// Test SetEphemeralOwnerStatus
		err = r.SetEphemeralOwnerStatus(ctx, owner.Address, true)
		assert.NoError(t, err)

		// Test GetEphemeralOwner
		retrievedOwner, err := r.GetEphemeralOwner(ctx, owner.Address)
		assert.NoError(t, err)
		assert.Equal(t, owner.Address, retrievedOwner.Address)
		assert.Equal(t, owner.PrivateKey, retrievedOwner.PrivateKey)
		assert.True(t, retrievedOwner.IsUsing)

		// Test GetUnusedEphemeralOwner after setting the owner to used
		unusedOwner, err = r.GetUnusedEphemeralOwner(ctx, domain.EphemeralOwnerTypeEvm)
		assert.NoError(t, err)
		assert.Equal(t, common.Address{}, unusedOwner)
	})

	// Test EphemeralNote methods
	t.Run("EphemeralNote", func(t *testing.T) {
		t.Parallel()
		// Test data
		note := &domain.EphemeralNote{
			ID:             "test-note-id",
			ChainID:        "sepolia",
			From:           common.HexToAddress("******************************************"),
			TokenAddress:   common.HexToAddress("******************************************"),
			Amount:         "1000000000000000000",
			DepositTxHash:  common.HexToHash("0x3333333333333333333333333333333333333333333333333333333333333333"),
			EphemeralOwner: common.HexToAddress("******************************************"),
			Status:         domain.EphemeralNoteStatusActive,
			CreatedAt:      time.Now(),
		}

		// Test CreateEphemeralNote
		err := r.CreateEphemeralNote(ctx, note)
		assert.NoError(t, err)

		// Test GetEphemeralNoteByID
		retrievedNote, err := r.GetEphemeralNoteByID(ctx, note.ID)
		assert.NoError(t, err)
		assertEqualNotes(t, note, retrievedNote)

		// Test GetEphemeralNoteByDepositTx
		retrievedNote, err = r.GetEphemeralNoteByDepositTx(ctx, note.DepositTxHash)
		assert.NoError(t, err)
		assertEqualNotes(t, note, retrievedNote)

		// Test UpdateEphemeralNoteClaimed
		claimTxHash := common.HexToHash("0x5555555555555555555555555555555555555555555555555555555555555555")
		err = r.UpdateEphemeralNoteClaimed(ctx, note.ID, claimTxHash)
		assert.NoError(t, err)

		retrievedNote, err = r.GetEphemeralNoteByID(ctx, note.ID)
		assert.NoError(t, err)
		assert.Equal(t, domain.EphemeralNoteStatusClaimed, retrievedNote.Status)
		require.NotNil(t, retrievedNote.ClaimTxHash)
		assert.Equal(t, claimTxHash, *retrievedNote.ClaimTxHash)

		// Test GetActiveNotesByEphemeralOwner
		activeNotes, err := r.GetActiveNotesByEphemeralOwner(ctx, note.EphemeralOwner)
		assert.NoError(t, err)
		assert.Empty(t, activeNotes)

		// Create a new active note
		activeNote := &domain.EphemeralNote{
			ID:             "active-note",
			ChainID:        "sepolia",
			From:           common.HexToAddress("******************************************"),
			TokenAddress:   common.HexToAddress("******************************************"),
			Amount:         "2000000000000000000",
			DepositTxHash:  common.HexToHash("0x6666666666666666666666666666666666666666666666666666666666666666"),
			EphemeralOwner: note.EphemeralOwner,
			Status:         domain.EphemeralNoteStatusActive,
			CreatedAt:      time.Now(),
		}
		err = r.CreateEphemeralNote(ctx, activeNote)
		assert.NoError(t, err)

		activeNotes, err = r.GetActiveNotesByEphemeralOwner(ctx, note.EphemeralOwner)
		assert.NoError(t, err)
		assert.Len(t, activeNotes, 1)
		assertEqualNotes(t, activeNote, activeNotes[0])

		// Test UpdateEphemeralNoteStatus
		err = r.UpdateEphemeralNoteStatus(ctx, activeNote.ID, domain.EphemeralNoteStatusCancelled)
		assert.NoError(t, err)

		retrievedNote, err = r.GetEphemeralNoteByID(ctx, activeNote.ID)
		assert.NoError(t, err)
		assert.Equal(t, domain.EphemeralNoteStatusCancelled, retrievedNote.Status)

		// Test GetEphemeralNotesByAddress
		userNotes, err := r.GetEphemeralNotesByAddress(ctx, util.AddressToString(note.ChainID, note.From))
		assert.NoError(t, err)
		assert.Len(t, userNotes, 2)

		// Sort userNotes by ID to ensure consistent order
		if userNotes[0].ID < userNotes[1].ID {
			userNotes[0], userNotes[1] = userNotes[1], userNotes[0]
		}
		note.Status = domain.EphemeralNoteStatusClaimed
		note.ClaimTxHash = &claimTxHash
		activeNote.Status = domain.EphemeralNoteStatusCancelled
		assertEqualNotes(t, note, userNotes[0])
		assertEqualNotes(t, activeNote, userNotes[1])
	})
}

func assertEqualNotes(t *testing.T, expected, actual *domain.EphemeralNote) {
	assert.Equal(t, expected.ID, actual.ID)
	assert.Equal(t, expected.ChainID, actual.ChainID)
	assert.Equal(t, expected.From, actual.From)
	assert.Equal(t, expected.TokenAddress, actual.TokenAddress)
	assert.Equal(t, expected.Amount, actual.Amount)
	assert.Equal(t, expected.DepositTxHash, actual.DepositTxHash)
	assert.Equal(t, expected.EphemeralOwner, actual.EphemeralOwner)
	assert.Equal(t, expected.Status, actual.Status)
	assert.WithinDuration(t, expected.CreatedAt, actual.CreatedAt, time.Second)

	if expected.ClaimTxHash == nil {
		assert.Nil(t, actual.ClaimTxHash)
	} else {
		require.NotNil(t, actual.ClaimTxHash)
		assert.Equal(t, *expected.ClaimTxHash, *actual.ClaimTxHash)
	}
}
