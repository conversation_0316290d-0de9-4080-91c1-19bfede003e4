package nft

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/apis"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestUpdateOasyx(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	s := assert.New(t)

	oasyxContractAddress := "******************************************"

	rdb.Reset()
	apis.InitDefault()
	ethAddress := "******************************************"
	updateOasyx(context.TODO(), ethAddress)

	var nftAssets []*model.NftAsset
	s.NoError(rdb.Get().Find(&nftAssets).Error)

	var oasyxAsset *model.NftAsset

	for index, nftAsset := range nftAssets {
		if nftAsset.ContractAddress != oasyxContractAddress {
			continue
		}

		oasyxAsset = nftAssets[index]
		break
	}

	s.NotNil(oasyxAsset)
	s.NotEmpty(oasyxAsset.Name)
	s.NotEmpty(oasyxAsset.CollectionImageURL)
	s.NotEmpty(oasyxAsset.ImageURL)
	s.NotEmpty(oasyxAsset.ChainID)
	s.NotEmpty(oasyxAsset.ContractAddress)
	s.NotEmpty(oasyxAsset.TokenID)
	s.NotNil(oasyxAsset.CollectionSlug)
	s.NotNil(oasyxAsset.ContractSchemaName)
	s.NotEmpty(oasyxAsset.ImagePreviewURL)
	s.NotEmpty(oasyxAsset.NftDescription)
	s.NotNil(oasyxAsset.IsSpam)
}
