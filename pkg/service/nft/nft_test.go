package nft

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/apis"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestFetchAsset(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OPENSEA_API_KEY", "ALCHEMY_API_KEY"})
	rdb.Reset()
	apis.InitDefault()

	asset, err := FetchAsset(context.Background(), "polygon", "******************************************", "78264146013019701962873384485664310048479070499054914840570521498581083357185")
	assert.Nil(t, err)
	assert.NotNil(t, asset)
	t.Logf("asset: %+v", asset)

	asset, err = FetchAsset(context.Background(), "ethereum", "******************************************", "222")
	assert.Nil(t, err)
	assert.NotNil(t, asset)
	assert.NotNil(t, asset.CollectionName)
	assert.NotNil(t, asset.CollectionImageURL)
	assert.NotEmpty(t, *(asset.CollectionName))
	assert.NotEmpty(t, *(asset.CollectionImageURL))
	assert.Equal(t, "ERC721", *(asset.ContractSchemaName))
	assert.Equal(t, "boredapeyachtclub", *(asset.CollectionSlug))
	assetStr, err := json.Marshal(asset)
	assert.Nil(t, err)
	t.Logf("asset: %+v", string(assetStr))

	collection, err := rdb.NftCollection(context.Background(), "boredapeyachtclub")
	assert.Nil(t, err)
	assert.NotNil(t, collection)
	assert.Greater(t, collection.FloorPrice, float64(1))
	assert.Equal(t, int32(1), collection.History)
	t.Logf("collection: %+v", collection)
}

func TestNftIsSpam(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY"})
	rdb.Reset()
	assert.NoError(t, rdbtest.CreateAirdropEvents(rdb.Get()))
	apis.InitDefault()

	// nft in airdrop event
	{
		ctx := context.Background()
		chainID := "polygon"
		contractAddress := "0xA319e5213bfA83Ec0B212d467f022Ff172735a66"
		tokenID := "1"
		asset, err := getAssetFromAlchemy(ctx, chainID, contractAddress, tokenID)
		assert.NoError(t, err)
		assert.NotNil(t, asset)
		assert.NotNil(t, asset.IsSpam)
		assert.False(t, *asset.IsSpam)
	}

	// nft not in airdrop event
	{
		ctx := context.Background()
		chainID := "polygon"
		contractAddress := "0x58dd843d5BFB9fCcc439c299b65Cda0194c5dd40"
		tokenID := "1"
		asset, err := getAssetFromAlchemy(ctx, chainID, contractAddress, tokenID)
		assert.NoError(t, err)
		assert.NotNil(t, asset)
		assert.NotNil(t, asset.IsSpam)
		assert.False(t, *asset.IsSpam)
	}

	// nft spm
	{
		ctx := context.Background()
		chainID := "polygon"
		contractAddress := "0xa52624a36b41465258d72766ac4f8bebb7b8ef9f"
		tokenID := "1"
		asset, err := getAssetFromAlchemy(ctx, chainID, contractAddress, tokenID)
		assert.NoError(t, err)
		assert.NotNil(t, asset)
		assert.NotNil(t, asset.IsSpam)
		assert.True(t, *asset.IsSpam)
	}

	// nft spm
	{
		ctx := context.Background()
		chainID := "polygon"
		contractAddress := "0x426b48eeb10ac7dfcf749b48e09dccbebd608e47"
		tokenID := "1"
		asset, err := getAssetFromAlchemy(ctx, chainID, contractAddress, tokenID)
		assert.NoError(t, err)
		assert.NotNil(t, asset)
		assert.NotNil(t, asset.IsSpam)
		assert.True(t, *asset.IsSpam)
	}

	// nft spam
	{
		ctx := context.Background()
		chainID := "polygon"
		contractAddress := "0x328b6020266f6B2694c46c1696691839aDE8ad61"
		tokenID := "0"
		asset, err := getAssetFromAlchemy(ctx, chainID, contractAddress, tokenID)
		assert.NoError(t, err)
		assert.NotNil(t, asset)
		assert.True(t, *asset.IsSpam)
	}

	// nft spam
	{
		ctx := context.Background()
		chainID := "polygon"
		contractAddress := "0x633db306a3bee00dd99f462bf9cd0231bd964a31"
		tokenID := "0"
		asset, err := getAssetFromAlchemy(ctx, chainID, contractAddress, tokenID)
		assert.NoError(t, err)
		assert.NotNil(t, asset)
		assert.True(t, *asset.IsSpam)
	}

	// nft spam
	{
		ctx := context.Background()
		chainID := "polygon"
		contractAddress := "0x6523aefe202c589e3d0b4e3093150d071e7a8e68"
		tokenID := "2923176777993686863942097856128103811283053744538297861954718482784246038529"
		asset, err := getAssetFromAlchemy(ctx, chainID, contractAddress, tokenID)
		assert.NoError(t, err)
		assert.NotNil(t, asset)
		assert.True(t, *asset.IsSpam)
	}
}
