package nft

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/apis"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestUpdateNftAssetsByAddress(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"ALCHEMY_API_KEY", "OPENSEA_API_KEY"})
	rdb.Reset()
	apis.InitDefault()

	owner := "******************************************"
	UpdateNftAssetsByAddress(context.Background(), owner, time.Second, UpdatePriorityHigh, []string{"eth", "matic"})
	testNftFetching(t, owner, []string{model.ChainIDEthereum, model.ChainIDPolygon})

	time.Sleep(time.Second * 1)

	// Test update specific chain and ensure it does not clear another chain's NFT
	UpdateNftAssetsByAddress(context.Background(), owner, time.Second, UpdatePriorityHigh, []string{model.ChainIDPolygon})
	testNftFetching(t, owner, []string{model.ChainIDEthereum, model.ChainIDPolygon})
}

func testNftFetching(t *testing.T, owner string, chainIDs []string) {
	for _, chainID := range chainIDs {
		nfts, _, errCode, err := rdb.Nfts(context.Background(), &rdb.NftParams{
			Tagtype: "ALL",
			PagingParams: rdb.PagingParams{
				PageNumber: 1,
				PageSize:   100,
			},
		}, map[string][]string{
			chainID: {owner},
		})
		assert.Nil(t, err)
		assert.Equal(t, 0, errCode)
		assert.NotNil(t, nfts)
		t.Logf("%s nfts length: %d\n", chainID, len(*nfts))
		assert.NotEmpty(t, *nfts)
	}
}

func TestComposeNftAssets(t *testing.T) {
	ctx := context.Background()
	ethAddress := "******************************************"
	UpdatePriorityHigh := int32(3)
	nfts := []alchemyapi.Nft{
		{
			Contract: struct {
				Address string "json:\"address\""
			}{
				Address: "******************************************",
			},
			ID: struct {
				TokenID       string "json:\"tokenId\""
				TokenMetadata struct {
					TokenType string "json:\"tokenType\""
				} "json:\"tokenMetadata\""
			}{
				TokenID: "1",
				TokenMetadata: struct {
					TokenType string "json:\"tokenType\""
				}{
					TokenType: "ERC721",
				},
			},
			Description: "111111",
			Metadata: struct {
				Name        string "json:\"name\""
				Image       string "json:\"image\""
				Description string "json:\"description\""
			}{
				Name:        "CryptoKitties #1",
				Image:       "https://storage.opensea.io/files/******************************************/1.png",
				Description: "item 111111",
			},
			ContractMetadata: struct {
				Description string "json:\"description\""
			}{
				Description: "contract 111111",
			},
		},
		{
			Contract: struct {
				Address string "json:\"address\""
			}{
				Address: "******************************************",
			},
			ID: struct {
				TokenID       string "json:\"tokenId\""
				TokenMetadata struct {
					TokenType string "json:\"tokenType\""
				} "json:\"tokenMetadata\""
			}{
				TokenID: "2",
				TokenMetadata: struct {
					TokenType string "json:\"tokenType\""
				}{
					TokenType: "ERC721",
				},
			},
			Description: "222222",
			Metadata: struct {
				Name        string "json:\"name\""
				Image       string "json:\"image\""
				Description string "json:\"description\""
			}{
				Name:        "CryptoKitties #2",
				Image:       "https://storage.opensea.io/files/******************************************/2.png",
				Description: "item 222222",
			},
			ContractMetadata: struct {
				Description string "json:\"description\""
			}{
				Description: "contract 222222",
			},
		},
		{
			Contract: struct {
				Address string "json:\"address\""
			}{
				Address: "******************************************",
			},
			ID: struct {
				TokenID       string "json:\"tokenId\""
				TokenMetadata struct {
					TokenType string "json:\"tokenType\""
				} "json:\"tokenMetadata\""
			}{
				TokenID: "1",
				TokenMetadata: struct {
					TokenType string "json:\"tokenType\""
				}{
					TokenType: "ERC721",
				},
			},
			Description: "333333",
			Metadata: struct {
				Name        string "json:\"name\""
				Image       string "json:\"image\""
				Description string "json:\"description\""
			}{
				Name:        "The Church of the Good Shepherd #3",
				Image:       "https://storage.opensea.io/files/******************************************/3.png",
				Description: "item 333333",
			},
			ContractMetadata: struct {
				Description string "json:\"description\""
			}{
				Description: "contract 333333",
			},
		},
	}

	assets, userAmounts := composeNftAssets(ctx, ethAddress, UpdatePriorityHigh, &nfts)
	assert.Equal(t, 3, len(*assets))
	assert.Equal(t, 3, len(*userAmounts))
}
