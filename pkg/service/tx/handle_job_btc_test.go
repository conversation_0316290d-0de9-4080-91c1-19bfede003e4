package tx

import (
	"context"
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	blockchainapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchain-api"
	blockchairapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchair-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	gomock "go.uber.org/mock/gomock"
	"gorm.io/gorm"

	ws "github.com/kryptogo/kg-wallet-backend/pkg/websocket"
	"github.com/stretchr/testify/assert"
)

func TestHandleJobBtc(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "BLOCKCHAIR_API_KEY"})

	rdb.Reset()
	blockchairapi.InitDefault(domain.NewAllPassRateLimiter())
	blockchainapi.InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()
	t.Run("txlist", func(t *testing.T) {
		address := "******************************************"
		assert.NoError(t, rdb.Get().Create(&model.TxJob{
			ChainID:  model.ChainIDBitcoin,
			Address:  address,
			Category: "txlist",
			FromNum:  0,
			ToNum:    0,
			TryAfter: time.Now().Add(-time.Second),
		}).Error)
		ws.Init(rdb.GormRepo())

		// Correctly populate the TxJob instance
		{
			var txJob model.TxJob
			rdb.Get().First(&txJob)
			assert.Equal(t, uint32(0), txJob.ToNum)
			HandleTxJob(ctx, &txJob)

			var txDetailsCount int64
			assert.NoError(t, rdb.Get().Model(&model.TxDetail{}).Count(&txDetailsCount).Error)
			assert.GreaterOrEqual(t, int64(22), txDetailsCount)

			err := rdb.Get().First(&txJob).Error
			if err != nil {
				assert.ErrorAs(t, err, &gorm.ErrRecordNotFound)
			} else {
				assert.Equal(t, uint32(60), txJob.ToNum)
			}
		}

		// Check tx lists and tx details content
		txHashReceive := "4c2138601a0ab50228042e2f44a153106965e7b901c90966ee694f571bfba97e"
		{
			var txlist model.TxList
			assert.NoError(t, rdb.Get().Where("tx_hash = ? and address = ?", txHashReceive, address).First(&txlist).Error)
			assert.Equal(t, int32(6), txlist.TxType)
			assert.Equal(t, "******************************************", txlist.TargetAddress)
			assert.NotNil(t, txlist.Receive)
			if txlist.Receive != nil {
				assert.Equal(t, "≈ 0.007875 BTC", *txlist.Receive)
			}
			assert.Empty(t, txlist.Send)
		}

		txHashSend := "9c44c83cbe14c0874ee2d274bec0c72d16b2a0d3506ac3c6d74129e28bd7aab4"
		{
			var txlist model.TxList
			assert.NoError(t, rdb.Get().Where("tx_hash = ? and address = ?", txHashSend, address).First(&txlist).Error)
			// 2023-10-07 12:00:57
			date := time.Date(2023, 10, 7, 12, 0, 57, 0, time.UTC)
			assert.Equal(t, date, txlist.TxTimestamp)
			assert.Equal(t, "******************************************", txlist.TargetAddress)
			assert.Equal(t, int32(7), txlist.TxType)
			assert.NotNil(t, txlist.Send)
			if txlist.Send != nil {
				assert.Equal(t, "≈ 0.000103 BTC", *txlist.Send)
			}
			assert.Empty(t, txlist.Receive)
		}

		{
			var txDetail model.TxDetail
			assert.NoError(t, rdb.Get().Where("tx_hash = ? and address = ?", txHashSend, address).First(&txDetail).Error)
			assert.Equal(t, "10282", txDetail.Value)
			assert.Equal(t, "BTC", txDetail.Asset)
			assert.Empty(t, txDetail.ContractAddress)
			valueString := strconv.FormatFloat(txDetail.ValueDecimal, 'f', -1, 64)
			assert.Equal(t, "0.00010282", valueString)
		}
	})
}

func TestHandleJobBtcMocked(t *testing.T) {
	rdb.Reset()
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	t.Run("txlist", func(t *testing.T) {
		blockchairObj := blockchairapi.NewMockIBlockchair(ctrl)
		blockchainObj := blockchainapi.NewMockIBlockchain(ctrl)
		blockchairapi.Set(blockchairObj)
		blockchainapi.Set(blockchainObj)
		now := time.Now()
		nowStr := now.Format("2006-01-02 15:04:05")
		blockchairObj.EXPECT().AddressInfo(gomock.Any(), gomock.Any()).Return(&blockchairapi.AddressInfoResp{
			Data: map[string]*blockchairapi.AddressAndTransactions{
				"******************************************": {
					Transactions: []*blockchairapi.Transaction{
						{
							Hash:          "testhashbtc1",
							Time:          nowStr,
							BalanceChange: 200000,
						},
						{
							Hash:          "testhashbtc2",
							Time:          nowStr,
							BalanceChange: -300000,
						},
					},
				},
			},
		}, time.Second, nil)
		blockchainObj.EXPECT().GetTransaction(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, txHash string) (*domain.BitcoinTransaction, error) {
			switch txHash {
			case "testhashbtc1":
				return &domain.BitcoinTransaction{
					BlockID: 50,
					Time:    time.Date(2009, 1, 12, 3, 30, 25, 0, time.UTC),
					Inputs: []domain.BitcoinTransactionInput{
						{
							Address: "sender",
							Value:   500000,
						},
					},
					Outputs: []domain.BitcoinTransactionOutput{
						{
							Address: "sender",
							Value:   300000,
						},
						{
							Address: "******************************************",
							Value:   200000,
						},
					},
				}, nil
			case "testhashbtc2":
				return &domain.BitcoinTransaction{
					BlockID: 100,
					Time:    now,
					Inputs: []domain.BitcoinTransactionInput{
						{
							Address: "******************************************",
							Value:   100000,
						},
						{
							Address: "******************************************",
							Value:   250000,
						},
					},
					Outputs: []domain.BitcoinTransactionOutput{
						{
							Address: "receiver",
							Value:   300000,
						},
						{
							Address: "******************************************",
							Value:   250000,
						},
					},
				}, nil
			}
			return nil, fmt.Errorf("not found")
		})
		address := "******************************************"
		assert.NoError(t, rdb.Get().Create(&model.TxJob{
			ChainID:  model.ChainIDBitcoin,
			Address:  address,
			Category: "txlist",
			FromNum:  0,
			ToNum:    0,
			TryAfter: time.Now().Add(-time.Second),
		}).Error)
		ws.Init(rdb.GormRepo())

		// Correctly populate the TxJob instance
		{
			var txJob model.TxJob
			rdb.Get().First(&txJob)
			assert.Equal(t, uint32(0), txJob.ToNum)
			HandleTxJob(ctx, &txJob)

			var txDetailsCount int64
			assert.NoError(t, rdb.Get().Model(&model.TxDetail{}).Count(&txDetailsCount).Error)
			assert.Equal(t, int64(2), txDetailsCount)

			err := rdb.Get().First(&txJob).Error
			assert.ErrorAs(t, err, &gorm.ErrRecordNotFound)
		}

		// Check tx lists and tx details content
		txHashReceive := "testhashbtc1"
		{
			var txlist model.TxList
			assert.NoError(t, rdb.Get().Where("tx_hash = ? and address = ?", txHashReceive, address).First(&txlist).Error)
			assert.Equal(t, int32(6), txlist.TxType)
			assert.NotNil(t, txlist.Receive)
			if txlist.Receive != nil {
				assert.Equal(t, "0.002 BTC", *txlist.Receive)
			}
			assert.Empty(t, txlist.Send)
		}

		txHashSend := "testhashbtc2"
		{
			var txlist model.TxList
			assert.NoError(t, rdb.Get().Where("tx_hash = ? and address = ?", txHashSend, address).First(&txlist).Error)
			assert.Equal(t, "receiver", txlist.TargetAddress)
			assert.Equal(t, int32(7), txlist.TxType)
			assert.NotNil(t, txlist.Send)
			if txlist.Send != nil {
				assert.Equal(t, "0.003 BTC", *txlist.Send)
			}
			assert.Empty(t, txlist.Receive)
		}

		{
			var txDetail model.TxDetail
			assert.NoError(t, rdb.Get().Where("tx_hash = ? and address = ?", txHashSend, address).First(&txDetail).Error)
			assert.Equal(t, "300000", txDetail.Value)
			assert.Equal(t, "BTC", txDetail.Asset)
			assert.Empty(t, txDetail.ContractAddress)
			valueString := strconv.FormatFloat(txDetail.ValueDecimal, 'f', -1, 64)
			assert.Equal(t, "0.003", valueString)
		}
	})
}
