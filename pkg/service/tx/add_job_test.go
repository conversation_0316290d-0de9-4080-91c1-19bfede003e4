package tx_test

import (
	"context"
	"strings"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/stretchr/testify/assert"
)

var addrsByChain = map[string][]string{
	"sol": {
		"D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
		"CpBQcnDT29RCAbuG9f4udXrd9eKCiuyRfkFUUWVjKK2Y",
	},
	"arb": {
		"0x0901549Bc297BCFf4221d0ECfc0f718932205e3",
		"******************************************",
		"******************************************",
	},
	"btc": {
		"******************************************",
		"******************************************",
	},
	"eth": {
		"0x0901549Bc297BCFf4221d0ECfc0f718932205e3",
		"******************************************",
		"******************************************",
	},
	"matic": {
		"0x0901549Bc297BCFf4221d0ECfc0f718932205e3",
		"******************************************",
		"******************************************",
	},
	"bsc": {
		"0x0901549Bc297BCFf4221d0ECfc0f718932205e3",
		"******************************************",
		"******************************************",
	},
	// kcc tx jobs won't be added
	"kcc": {
		"0x0901549Bc297BCFf4221d0ECfc0f718932205e3",
		"******************************************",
		"******************************************",
	},
	"ronin": {
		"0x0901549Bc297BCFf4221d0ECfc0f718932205e3",
		"******************************************",
		"******************************************",
	},
	"tron": {
		"TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn",
		"TEWdRAiBawsbN5iSbpiT7G1TmkHuhUz7rU",
	},
}

func TestAddTxJobs(t *testing.T) {
	ctx := context.Background()

	// test the behavior of old and new AddTxJobs are the same
	for i := 0; i < 2; i++ {
		rdb.Reset()

		// add jobs and got correct number of jobs
		addTxJobsByVersion(i, addrsByChain)
		jobs, err := rdb.GetUnfinishedJobs(ctx, addrsByChain)
		assert.Nil(t, err)
		assert.Equal(t, 70, len(jobs))

		// add again and ensure no new jobs added
		addTxJobsByVersion(i, addrsByChain)
		jobs, err = rdb.GetUnfinishedJobs(ctx, addrsByChain)
		assert.Nil(t, err)
		assert.Equal(t, 70, len(jobs))

		// set a eth job as finished
		ethJobs, err := rdb.GetNextBatch(ctx, "eth")
		assert.Nil(t, err)
		assert.Len(t, ethJobs, 10)
		ethJob := ethJobs[0]
		rdb.UpdateTxJobAsDone(ctx, ethJob)
		err = rdb.UpdateTxUpdate(ctx, &model.TxUpdate{
			ChainID:  ethJob.ChainID,
			Address:  ethJob.Address,
			Category: ethJob.Category,
			FromNum:  100,
			ToNum:    200,
		})
		assert.Nil(t, err)

		// set a btc job as finished
		btcJobs, err := rdb.GetNextBatch(ctx, "btc")
		assert.Nil(t, err)
		assert.Len(t, btcJobs, 2)
		btcJob := btcJobs[0]
		rdb.UpdateTxJobAsDone(ctx, btcJob)
		err = rdb.UpdateTxUpdate(ctx, &model.TxUpdate{
			ChainID:  btcJob.ChainID,
			Address:  btcJob.Address,
			Category: btcJob.Category,
			FromNum:  300,
			ToNum:    400,
		})
		assert.Nil(t, err)

		// unfinished jobs should be reduced by 2
		jobs, err = rdb.GetUnfinishedJobs(ctx, addrsByChain)
		assert.Nil(t, err)
		assert.Equal(t, 68, len(jobs))

		// add again and ensure two new job is added
		addTxJobsByVersion(i, addrsByChain)
		jobs, err = rdb.GetUnfinishedJobs(ctx, addrsByChain)
		assert.Nil(t, err)
		assert.Equal(t, 70, len(jobs))

		// assert updated tx jobs exist in database
		assert.Contains(t, jobs, rdb.TxJobKey{
			ChainID:  "eth",
			Address:  strings.ToLower(ethJob.Address),
			Category: ethJob.Category,
			FromNum:  201,
		})
		assert.Contains(t, jobs, rdb.TxJobKey{
			ChainID:  "btc",
			Address:  btcJob.Address,
			Category: btcJob.Category,
			FromNum:  401,
		})
	}
}

func addTxJobsByVersion(version int, addrsByChain map[string][]string) {
	if version == 0 {
		oldAddTxJobs(addrsByChain)
	} else {
		newAddTxJobs(addrsByChain)
	}
}

func oldAddTxJobs(addrsByChain map[string][]string) {
	for chainID, addrs := range addrsByChain {
		for _, addr := range addrs {
			tx.AddTxJob(context.Background(), chainID, addr)
		}
	}
}

func newAddTxJobs(addrsByChain map[string][]string) {
	tx.AddTxJobs(context.Background(), addrsByChain)
}
