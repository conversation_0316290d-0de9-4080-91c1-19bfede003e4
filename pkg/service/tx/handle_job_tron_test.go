package tx

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	tronscanapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/tronscan-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestHandleTronTxlist(t *testing.T) {
	// Setup mock controller
	ctrl := gomock.NewController(t)

	// Create mock tronscan API
	mockTronscan := tronscanapi.NewMockITronscan(ctrl)
	tronscanapi.Set(mockTronscan)

	mockTokenMeta := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(mockTokenMeta, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	// Reset database
	rdb.Reset()

	ctx := context.Background()

	// Create test job
	job := &model.TxJob{
		ID:        1,
		ChainID:   "tron",
		Address:   "TBjXaF4w3ca1NKnSSHzTch79HbLzzjQA3o",
		Category:  "txlist",
		FromNum:   0,
		ToNum:     0,
		Priority:  1,
		TryAfter:  time.Now(),
		CreatedAt: time.Now(),
	}
	rdb.AddTxJob(ctx, job.ChainID, job.Address, job.Category, job.FromNum, job.ToNum)

	// Setup mock response
	expectedResp := &tronscanapi.TransactionResp{
		Data: []tronscanapi.Tx{
			{
				Block:        67149286,
				Hash:         "a68995da66617c42c595fd8c56132c655179c6b05010fe0c6740572d8ea335c6",
				Timestamp:    1732097673000,
				OwnerAddress: "TBjXaF4w3ca1NKnSSHzTch79HbLzzjQA3o",
				ToAddressList: []string{
					"TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
				},
				ToAddress:    "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
				ContractType: 31,
				Confirmed:    true,
				Revert:       false,
				ContractData: struct {
					Amount          int    `json:"amount"`
					AssetName       string `json:"asset_name"`
					OwnerAddress    string `json:"owner_address"`
					ToAddress       string `json:"to_address"`
					Data            string `json:"data"`
					ContractAddress string `json:"contract_address"`
					TokenInfo       struct {
						TokenID      string `json:"tokenId"`
						TokenAbbr    string `json:"tokenAbbr"`
						TokenName    string `json:"tokenName"`
						TokenDecimal int    `json:"tokenDecimal"`
						TokenCanShow int    `json:"tokenCanShow"`
						TokenType    string `json:"tokenType"`
						TokenLogo    string `json:"tokenLogo"`
						TokenLevel   string `json:"tokenLevel"`
						Vip          bool   `json:"vip"`
					} `json:"tokenInfo"`
					NewContract struct {
						OriginAddress string `json:"origin_address"`
					} `json:"new_contract,omitempty"`
				}{
					Data:            "095ea7b3000000000000000000000041515fb626393b1f427427433025a2cbe429af595d00000000000000000000000000000000000000000000000000000002540be400",
					OwnerAddress:    "TBjXaF4w3ca1NKnSSHzTch79HbLzzjQA3o",
					ContractAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
				},
				ContractRet: "SUCCESS",
				Result:      "SUCCESS",
				Amount:      "0",
				Cost: struct {
					NetFee            int `json:"net_fee"`
					EnergyUsage       int `json:"energy_usage"`
					Fee               int `json:"fee"`
					EnergyFee         int `json:"energy_fee"`
					EnergyUsageTotal  int `json:"energy_usage_total"`
					OriginEnergyUsage int `json:"origin_energy_usage"`
					NetUsage          int `json:"net_usage"`
				}{
					NetFee:           345000,
					EnergyUsage:      99764,
					Fee:              345000,
					EnergyUsageTotal: 99764,
				},
				TokenInfo: struct {
					TokenID      string `json:"tokenId"`
					TokenAbbr    string `json:"tokenAbbr"`
					TokenName    string `json:"tokenName"`
					TokenDecimal int    `json:"tokenDecimal"`
					TokenCanShow int    `json:"tokenCanShow"`
					TokenType    string `json:"tokenType"`
					TokenLogo    string `json:"tokenLogo"`
					TokenLevel   string `json:"tokenLevel"`
					Vip          bool   `json:"vip"`
				}{
					TokenAbbr:    "trx",
					TokenName:    "trx",
					TokenDecimal: 6,
					TokenType:    "trc10",
					TokenLogo:    "https://static.tronscan.org/production/logo/trx.png",
					TokenLevel:   "2",
					Vip:          true,
				},
			},
		},
	}

	// Setup mock expectations
	mockTronscan.EXPECT().
		Transaction(gomock.Any(), &tronscanapi.RequestParams{
			Address: "TBjXaF4w3ca1NKnSSHzTch79HbLzzjQA3o",
			Start:   0,
		}).
		Return(expectedResp, time.Duration(0), nil)

	// Call function under test
	handleTronTxlist(ctx, job)

	// Assert tx details
	query := rdb.Get().Model(&model.TxDetail{})
	var txDetails []model.TxDetail
	err := query.Find(&txDetails).Error
	assert.NoError(t, err)
	assert.Len(t, txDetails, 1)

	txDetail := txDetails[0]
	assert.Equal(t, "tron", txDetail.ChainID)
	assert.Equal(t, "TBjXaF4w3ca1NKnSSHzTch79HbLzzjQA3o", txDetail.Address)
	assert.Equal(t, "a68995da66617c42c595fd8c56132c655179c6b05010fe0c6740572d8ea335c6", txDetail.TxHash)
	assert.Equal(t, int64(1732097673), txDetail.TxTimestamp.Unix())
	assert.Equal(t, "txlist", txDetail.Category)
	assert.Equal(t, uint32(67149286), txDetail.BlockNum)
	assert.Equal(t, "TBjXaF4w3ca1NKnSSHzTch79HbLzzjQA3o", txDetail.FromAddress)
	assert.Equal(t, "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", txDetail.ToAddress)
	assert.Equal(t, "", txDetail.ContractAddress)
	assert.Equal(t, "USDT", txDetail.Asset)
	assert.Equal(t, "", txDetail.TokenID)
	assert.Equal(t, "0", txDetail.Value)
	assert.Equal(t, int32(6), txDetail.ValueDecimals)
	assert.Equal(t, float64(0), txDetail.ValueDecimal)
	assert.Equal(t, "1", txDetail.GasPrice)
	assert.Equal(t, "345000", txDetail.GasUsed)
	assert.Equal(t, int32(0), txDetail.IsError)
	assert.Equal(t, "0x095ea7b3", *txDetail.MethodID)
	assert.Equal(t, int32(1), txDetail.Approve)

	// assert tx stat
	txStats := parseTxByTxDetails(ctx, []*model.TxDetail{&txDetail})
	assert.Equal(t, true, txStats.Approve)
	assert.Equal(t, int32(12), txStats.TxType())

	// Assert tx lists
	var txLists []model.TxList
	err = rdb.Get().Model(&model.TxList{}).Find(&txLists).Error
	assert.NoError(t, err)
	assert.Len(t, txLists, 1)

	txList := txLists[0]
	assert.Equal(t, "tron", txList.ChainID)
	assert.Equal(t, "TBjXaF4w3ca1NKnSSHzTch79HbLzzjQA3o", txList.Address)
	assert.Equal(t, "a68995da66617c42c595fd8c56132c655179c6b05010fe0c6740572d8ea335c6", txList.TxHash)
	assert.Equal(t, "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", txList.TargetAddress)
	assert.Equal(t, int64(1732097673), txList.TxTimestamp.Unix())
	assert.Equal(t, uint32(67149286), txList.BlockNum)
	assert.Equal(t, int32(12), txList.TxType)

	// Assert tx job is deleted
	var txJobs []model.TxJob
	err = rdb.Get().Model(&model.TxJob{}).Find(&txJobs).Error
	assert.NoError(t, err)
	assert.Len(t, txJobs, 0)

	// Assert tx job log
	var txJobLogs []model.TxJobLog
	err = rdb.Get().Model(&model.TxJobLog{}).Find(&txJobLogs).Error
	assert.NoError(t, err)
	assert.Len(t, txJobLogs, 1)

	txJobLog := txJobLogs[0]
	assert.Equal(t, "tron", txJobLog.ChainID)
	assert.Equal(t, "TBjXaF4w3ca1NKnSSHzTch79HbLzzjQA3o", txJobLog.Address)
	assert.Equal(t, "txlist", txJobLog.Category)
	assert.Equal(t, int32(1), txJobLog.DataCnt)
	assert.Equal(t, int32(1), txJobLog.Status)
}
