package tx

import (
	"context"
	"testing"

	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/stretchr/testify/assert"
)

func TestTxListTitle(t *testing.T) {
	t.<PERSON>llel()
	tx := &dbmodel.TxList{
		ChainID:       "arb",
		Address:       "******************************************",
		TxHash:        "0x91e2d26d70e894cf4f3b2ec10549d3b191798459306f4279a557971e7e223902",
		TargetAddress: "******************************************",
		BlockNum:      211890776,
		TxType:        model.TxTypeSendOne,
		Send:          util.Ptr("0.002 ETH"),
		Receive:       util.Ptr("0.002 ETH"),
	}
	title := txListTitle(tx, "en_US")
	t.Logf("title: %s", title)
	assert.Contains(t, title, "Sent 0 ETH")
}

func TestGetTxDetailCard(t *testing.T) {
	t.Parallel()
	tx := &dbmodel.TxDetail{
		ChainID:       "matic",
		Address:       "******************************************",
		TxHash:        "0x4b6124955748d48b60b10b353bb528910110c8e0688e8006c27ca01f158a5d51",
		BlockNum:      63396517,
		Category:      "txlistinternal",
		Asset:         "POL",
		Value:         "0.5",
		ValueDecimals: 18,
		ValueDecimal:  0.5,
	}
	card := getTxDetailCard(context.Background(), tx, "en_US")
	assert.Equal(t, DetailCard{
		Type:               "sent",
		TypeDesc:           "Sent",
		ExternalURL:        "",
		Title:              "0.5 POL",
		Subtitle:           "",
		ImageURL:           "https://wallet-static.kryptogo.com/public/assets/images/polygon-matic-logo.webp",
		ContractAddress:    "",
		ContractAddressURL: "",
	}, card)
}
