package tx

import (
	"context"
	"fmt"
	"strconv"
	"testing"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/domain"
	solanaapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api"
	solscanapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/solscan-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	ws "github.com/kryptogo/kg-wallet-backend/pkg/websocket"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"
)

func TestHandleJobSol(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "SOLSCAN_API_KEY_V2"})

	rdb.Reset()
	t.Run("tokentx", func(t *testing.T) {
		address := "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"
		assert.NoError(t, rdb.Get().Create(&model.TxJob{
			ChainID:  model.ChainIDSolana,
			Address:  address,
			Category: "tokentx",
			FromNum:  0,
			ToNum:    0,
			TryAfter: time.Now().Add(-time.Second),
		}).Error)
		solscanapi.InitDefault()
		ws.Init(rdb.GormRepo())

		// Correctly populate the TxJob instance
		{
			var txJob model.TxJob
			rdb.Get().First(&txJob)
			assert.Equal(t, uint32(0), txJob.ToNum)
			HandleTxJob(context.Background(), &txJob)

			var txDetailsCount int64
			assert.NoError(t, rdb.Get().Model(&model.TxDetail{}).Count(&txDetailsCount).Error)
			assert.Equal(t, int64(60), txDetailsCount)

			assert.NoError(t, rdb.Get().First(&txJob).Error)
			assert.Equal(t, uint32(60), txJob.ToNum)
			HandleTxJob(context.Background(), &txJob)

			assert.NoError(t, rdb.Get().Model(&model.TxDetail{}).Count(&txDetailsCount).Error)
			assert.Greater(t, int64(80), txDetailsCount)

			err := rdb.Get().First(&txJob).Error
			if err != nil {
				assert.ErrorAs(t, err, &gorm.ErrRecordNotFound)
			} else {
				assert.Equal(t, uint32(120), txJob.ToNum)
			}
		}

		// Check tx lists and tx details content
		txHashReceive := "352iodHFNsmA2DWwyu7n3RwZxQHDWo1N9eKXzW1xmx2Wqd8QPobGWb5iZ3QfTeofgxszCjYSp3AbVd54xKm4vgE"
		{
			var txlist model.TxList
			assert.NoError(t, rdb.Get().Where("tx_hash = ? and address = ?", txHashReceive, address).First(&txlist).Error)
			assert.NotNil(t, txlist.Receive)
			if txlist.Receive != nil {
				assert.Equal(t, "0.01 SOL", *txlist.Receive)
			}
			assert.Empty(t, txlist.Send)
		}

		txHashSend := "65r1N8Y1WAExaueERodcG3G4wqR1hJi1McvPRGdCWXu41eF3cGux9VpM7r1UZDYPcE4Z7zg92b5GBzkdhJS9T3Uq"
		{
			var txlist model.TxList
			assert.NoError(t, rdb.Get().Where("tx_hash = ? and address = ?", txHashSend, address).First(&txlist).Error)
			date := time.Date(2021, 11, 5, 7, 17, 43, 0, time.UTC)
			assert.Equal(t, date, txlist.TxTimestamp)
			assert.Equal(t, "6hPH8PxjNpDgcTx22HPp6riGi9iocJUjUj1X4CbCdJgW", txlist.TargetAddress)
			assert.Equal(t, int32(7), txlist.TxType)
			assert.NotNil(t, txlist.Send)
			if txlist.Send != nil {
				assert.Equal(t, "0.005 SOL", *txlist.Send)
			}
			assert.Empty(t, txlist.Receive)
		}

		{
			var txDetail model.TxDetail
			assert.NoError(t, rdb.Get().Where("tx_hash = ? and address = ?", txHashSend, address).First(&txDetail).Error)
			assert.Equal(t, "5000000", txDetail.Value)
			assert.Equal(t, "SOL", txDetail.Asset)
			assert.Equal(t, "So11111111111111111111111111111111111111111", txDetail.ContractAddress)
			valueString := strconv.FormatFloat(txDetail.ValueDecimal, 'f', -1, 64)
			assert.Equal(t, "0.005", valueString)
		}
	})
}

func TestHandleSolanaTransfers(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Mock solscanapi
	mockSolscan := solscanapi.NewMockISolscan(ctrl)
	solscanapi.Set(mockSolscan)
	solanaapi.InitDefault()

	// Setup test data
	now := time.Now()
	recentTx := &solscanapi.Transfer{
		BlockID:       100,
		TransID:       "2hZwhKwrgheJQuFcvujujqgBUsWvBRp4TLspfwa3rf71cdzWDCcbzudTSWxHDzaAR9gCT3cD4xNegPTtJrG51RdU",
		BlockTime:     now.Unix(),
		ActivityType:  solscanapi.ActivityTypeSPLTransfer,
		FromAddress:   "sender",
		ToAddress:     "receiver",
		TokenAddress:  "token_addr",
		TokenDecimals: 9,
		Amount:        1000000000,
	}

	// Mock GetTransfers to return one recent transaction
	mockSolscan.EXPECT().
		GetTransfers(gomock.Any(), gomock.Any()).
		Return([]*solscanapi.Transfer{recentTx}, 1*time.Second, nil)

	// Setup test job
	job := &model.TxJob{
		ID:       1,
		ChainID:  model.ChainIDSolana,
		Address:  "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
		Category: "tokentx",
		FromNum:  0,
		ToNum:    0,
	}

	// Initialize DB
	rdb.Reset()
	ws.Init(rdb.GormRepo())
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	mockTokenMetaRepo.EXPECT().BatchGetTokenMetadata(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]*domain.TokenMetadata, error) {
		assert.ElementsMatch(t, []domain.ChainToken{{Chain: domain.Solana, TokenID: "7ddd3rNWdx36MgnLkoUdwEPNoXt1bi9fszorkZungN2E"}}, tokens)
		return map[domain.ChainToken]*domain.TokenMetadata{
			{Chain: domain.Solana, TokenID: "7ddd3rNWdx36MgnLkoUdwEPNoXt1bi9fszorkZungN2E"}: {
				Name:          "AM",
				Symbol:        "AM",
				CoingeckoID:   "am",
				LogoUrl:       "https://am.token.logo",
				BinanceTicker: "AM",
			},
		}, nil
	})
	tokenmeta.Init(mockTokenMetaRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})
	Init(rdb.GormRepo())

	// Execute
	handleSolanaTokenTransfers(context.Background(), job)

	// Verify the additional txs from RPC
	var txDetails []model.TxDetail
	err := rdb.Get().Where("tx_hash = ?", recentTx.TransID).Find(&txDetails).Error
	assert.NoError(t, err)
	assert.Equal(t, 5, len(txDetails))

	// Count transactions by category
	categoryCount := make(map[string]int)
	for _, tx := range txDetails {
		t.Logf("tx detail: %+v", tx)
		categoryCount[tx.Category]++
	}

	// Assert counts match expected
	assert.Equal(t, 1, categoryCount["txlist"], "should have 1 txlist transaction")
	assert.Equal(t, 3, categoryCount["txlistinternal"], "should have 3 txlistinternal transaction")
	assert.Equal(t, 1, categoryCount["tokentx"], "should have 1 tokentx transactions")
}

func TestHandleSolanaSpamTransfer(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Mock solscanapi
	mockSolscan := solscanapi.NewMockISolscan(ctrl)
	solscanapi.Set(mockSolscan)
	solanaapi.InitDefault()

	// Setup test data
	now := time.Now()
	recentTx := &solscanapi.Transfer{
		BlockID:       311734597,
		TransID:       "3RHVKAnkej5EDuXUieqSUugj85otug1JBpjSHkYZYCPdi7v5LJa6uXpgrL8xfM9LsP3FeahCZMiRnqSkb6X24HKd",
		BlockTime:     now.Unix(),
		ActivityType:  solscanapi.ActivityTypeSPLTransfer,
		FromAddress:   "2Tq5W7ydAHFuHbSJ1KTcKAsRaHBAQzoCFiVuNwtagns2",
		ToAddress:     "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
		TokenAddress:  "So11111111111111111111111111111111111111112",
		TokenDecimals: 9,
		Amount:        100,
	}

	// Mock GetTransfers to return one recent transaction
	mockSolscan.EXPECT().
		GetTransfers(gomock.Any(), gomock.Any()).
		Return([]*solscanapi.Transfer{recentTx}, 1*time.Second, nil)

	// Setup test job
	job := &model.TxJob{
		ID:       1,
		ChainID:  model.ChainIDSolana,
		Address:  "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
		Category: "tokentx",
		FromNum:  0,
		ToNum:    0,
	}

	// Initialize DB
	rdb.Reset()
	ws.Init(rdb.GormRepo())
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(mockTokenMetaRepo, []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})
	Init(rdb.GormRepo())

	// Execute
	handleSolanaTokenTransfers(context.Background(), job)

	// This is dust spam so should not have tx details
	var txDetails []model.TxDetail
	err := rdb.Get().Where("tx_hash = ?", "3RHVKAnkej5EDuXUieqSUugj85otug1JBpjSHkYZYCPdi7v5LJa6uXpgrL8xfM9LsP3FeahCZMiRnqSkb6X24HKd").Find(&txDetails).Error
	assert.NoError(t, err)
	assert.Equal(t, 0, len(txDetails))
}

func TestHandleSolTransactionsSpam(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Mock Solana API
	mockSolana := solanaapi.NewMockISolana(ctrl)
	solanaapi.Set(mockSolana)
	rdb.Reset()
	Init(rdb.GormRepo())

	// Mock GetSignaturesForAddress response
	mockSignatures := &solanaapi.GetSignaturesForAddressResp{
		Jsonrpc: "2.0",
		Result: []solanaapi.Signatures{
			{
				BlockTime:          1738714356,
				ConfirmationStatus: "finalized",
				Err:                nil,
				Signature:          "MvgLMwFdWQbz7po1mJfsSqnCiwXsXuy1DU7TRCKZVsJ6WBD7gZo8ooTGzoR1wfiVxd35C3Kte1PZC2CTjxxqLst",
				Slot:               1234567,
			},
			{
				BlockTime:          1738714357,
				ConfirmationStatus: "finalized",
				Err:                nil,
				Signature:          "4wjANryrCr5hpQKvhE4YsYyqPVjRPx4VT8etDHpTPgkZbYFrcEQjnbuXk5JHARx1PiZuSghMZSEvgAQc5rw2a6KE",
				Slot:               1234568,
			},
		},
		ID: 1,
	}

	// Setup Solana API expectations
	mockSolana.EXPECT().
		GetSignaturesForAddress(gomock.Any(), "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7", gomock.Any()).
		Return(mockSignatures, &solanaapi.ResponseMeta{
			ResponseTime: 100 * time.Millisecond,
			StatusCode:   200,
		}, nil)

	mockSolana.EXPECT().
		GetTransaction(gomock.Any(), "MvgLMwFdWQbz7po1mJfsSqnCiwXsXuy1DU7TRCKZVsJ6WBD7gZo8ooTGzoR1wfiVxd35C3Kte1PZC2CTjxxqLst").
		DoAndReturn(func(ctx context.Context, txHash string) (*solanaapi.GetTransactionResponse, error) {
			return getSolanaTransaction(ctx, txHash)
		})

	mockSolana.EXPECT().
		GetTransaction(gomock.Any(), "4wjANryrCr5hpQKvhE4YsYyqPVjRPx4VT8etDHpTPgkZbYFrcEQjnbuXk5JHARx1PiZuSghMZSEvgAQc5rw2a6KE").
		DoAndReturn(func(ctx context.Context, txHash string) (*solanaapi.GetTransactionResponse, error) {
			return getSolanaTransaction(ctx, txHash)
		})

	// Create job
	job := &model.TxJob{
		ChainID:  model.ChainIDSolana,
		Address:  "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
		Category: "txlist",
	}

	// Run the handler
	handleSolTransactions(context.Background(), job)

	// Verify that the spam transaction was not stored in RDB
	var txDetail model.TxDetail
	err := rdb.Get().Where("tx_hash = ?", mockSignatures.Result[0].Signature).First(&txDetail).Error
	assert.ErrorIs(t, err, gorm.ErrRecordNotFound, "spam transaction should not be stored in RDB")
}

func getSolanaTransaction(ctx context.Context, txHash string) (*solanaapi.GetTransactionResponse, error) {
	var rpcResp solanaapi.GetTransactionResponse
	resp, err := resty.New().SetBaseURL(solanaapi.APIHost).R().
		SetContext(ctx).
		SetBody(map[string]interface{}{
			"jsonrpc": "2.0",
			"id":      1,
			"method":  "getTransaction",
			"params":  []interface{}{txHash, map[string]any{"encoding": "jsonParsed", "maxSupportedTransactionVersion": 0}},
		}).
		SetResult(&rpcResp).
		Post("/")

	if err != nil {
		return nil, fmt.Errorf("failed to get transaction: %w", err)
	}

	if resp.StatusCode() >= 400 {
		return nil, fmt.Errorf("HTTP error: %s", resp.Status())
	}

	return &rpcResp, nil
}
