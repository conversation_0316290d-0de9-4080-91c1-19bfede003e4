package tx

import (
	"context"
	"math/big"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestTitleSummaryAtReceived(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0xf41508ce07a28cd8e57a123f1ae9299223876520a388dac3a189d57d753845f0",
			Category:        "tokentx",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "USDC",
			Value:           "1000000",
			ValueDecimals:   6,
			ValueDecimal:    1.0,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0xf41508ce07a28cd8e57a123f1ae9299223876520a388dac3a189d57d753845f0",
			Category:        "tokentx",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "WETH",
			Value:           "100000000000000000",
			ValueDecimals:   18,
			ValueDecimal:    0.1,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeReceived, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0x3fE5...2e8e・Received multiple asset", title)
	assert.Equal(t, "💰You successfully got multiple asset.\n👾0.1 WETH, 1 USDC", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0x3fE5...2e8e・收到多笔 Token", title)
	assert.Equal(t, "💰您已成功收到多笔资产。\n👾0.1 WETH, 1 USDC", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0x3fE5...2e8e・收到多筆 Token", title)
	assert.Equal(t, "💰您已成功收到多筆資產。\n👾0.1 WETH, 1 USDC", summary)
}

func TestTitleSummaryAtSentOne(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:       "eth",
			Address:       "******************************************",
			TxHash:        "0x21565377b1a50d96924455b308d2d075d67e551ddb79d6e94a28677edc8d03e0",
			Category:      "txlist",
			FromAddress:   "******************************************",
			ToAddress:     "******************************************",
			Asset:         "ETH",
			Value:         "50000000000000000",
			ValueDecimals: 18,
			ValueDecimal:  0.05,
			IsError:       0,
			TxTimestamp:   time.Now(),
			MethodID:      util.Ptr("0x"),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeSentOne, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0xdda6...c36a・Sent 0.05 ETH", title)
	assert.Equal(t, "💸You successfully sent 0.05 ETH! \n👾To: 0x19ad...5c64", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0xdda6...c36a・送出 0.05 ETH", title)
	assert.Equal(t, "💸您已成功送出 0.05 ETH！\n👾发送给：0x19ad...5c64", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0xdda6...c36a・送出 0.05 ETH", title)
	assert.Equal(t, "💸您已成功送出 0.05 ETH！\n👾發送給：0x19ad...5c64", summary)
}

func TestTitleSummaryAtTraded(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:       "matic",
			Address:       "******************************************",
			TxHash:        "0xde8af3342fa0f9329a2551a2f5977303e47067d55ab8276066a51eb4ed4bcc0e",
			Category:      "txlist",
			FromAddress:   "******************************************",
			ToAddress:     "******************************************",
			Asset:         "ETH",
			Value:         "3000000000000000000",
			ValueDecimals: 18,
			ValueDecimal:  3.0,
			IsError:       0,
			TxTimestamp:   time.Now(),
		},
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0xde8af3342fa0f9329a2551a2f5977303e47067d55ab8276066a51eb4ed4bcc0e",
			Category:        "tokennfttx",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "UNKNOWN_NFT",
			TokenID:         "5477",
			Value:           "1",
			ValueDecimals:   0,
			ValueDecimal:    1,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0xde8af3342fa0f9329a2551a2f5977303e47067d55ab8276066a51eb4ed4bcc0e",
			Category:        "tokennfttx",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "UNKNOWN_NFT_2",
			TokenID:         "5478",
			Value:           "1",
			ValueDecimals:   0,
			ValueDecimal:    1.0,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeTraded, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0x4df4...20c6・Traded multiple asset", title)
	assert.Equal(t, "🔁You successfully traded multiple asset.\n👾-3 ETH, +UNKNOWN_NFT#5477, +UNKNOWN_NFT_2#5478", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0x4df4...20c6・交易多笔 Token", title)
	assert.Equal(t, "🔁您已成功交易多笔资产。\n👾-3 ETH, +UNKNOWN_NFT#5477, +UNKNOWN_NFT_2#5478", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0x4df4...20c6・交易多筆 Token", title)
	assert.Equal(t, "🔁您已成功交易多筆資產。\n👾-3 ETH, +UNKNOWN_NFT#5477, +UNKNOWN_NFT_2#5478", summary)
}

func TestTitleSummaryAtReceiveOne(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "mainnet",
			Address:         "******************************************",
			TxHash:          "0x61ee76c413ab0e29b7a0d14df60c825f227e5d10f01f46c3b5db22dc08b8e9d9",
			Category:        "tokentx",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "ETH",
			TokenID:         "",
			Value:           "1500000000000000000",
			ValueDecimals:   18,
			ValueDecimal:    1.5,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeReceivedOne, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0xdda6...c36a・Received 1.5 ETH", title)
	assert.Equal(t, "💰You successfully got 1.5 ETH! \n👾From: 0x477b...4d6b", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0xdda6...c36a・收到 1.5 ETH", title)
	assert.Equal(t, "💰您已成功收到 1.5 ETH！\n👾来自：0x477b...4d6b", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0xdda6...c36a・收到 1.5 ETH", title)
	assert.Equal(t, "💰您已成功收到 1.5 ETH！\n👾來自：0x477b...4d6b", summary)

	details = []*dbmodel.TxDetail{
		{
			ChainID:         "mainnet",
			Address:         "******************************************",
			TxHash:          "0x61ee76c413ab0e29b7a0d14df60c825f227e5d10f01f46c3b5db22dc08b8e9d9",
			Category:        "tokentx",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "ETH",
			TokenID:         "",
			Value:           "1500000000000000000",
			ValueDecimals:   18,
			ValueDecimal:    1.5,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
	}

	txStat = parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeReceivedOne, txStat.TransactionType())

	title, summary = txStat.TitleSummary("en")
	assert.Equal(t, "0xdda6...c36a・Received 1.5 ETH", title)
	assert.Equal(t, "💰You successfully got 1.5 ETH! \n👾From: 0x0000...0000", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0xdda6...c36a・收到 1.5 ETH", title)
	assert.Equal(t, "💰您已成功收到 1.5 ETH！\n👾来自：0x0000...0000", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0xdda6...c36a・收到 1.5 ETH", title)
	assert.Equal(t, "💰您已成功收到 1.5 ETH！\n👾來自：0x0000...0000", summary)
}

func TestTitleSummaryAtTradeOne(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "mainnet",
			Address:         "******************************************",
			TxHash:          "0x4cbd6a299b39b1961736bcd338dbb19b4bb461c900cba44a617b01933ea752c7",
			Category:        "tokentx",
			FromAddress:     "0x9c35f0e78fb7b5f29520c6c7f7201a72",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "UNKNOWN_NFT",
			TokenID:         "5477",
			Value:           "0",
			ValueDecimals:   0,
			ValueDecimal:    0.0,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeContractExecution, txStat.TransactionType())

	title, summary := txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0x0901...5e33・合約互動", title)
	assert.Equal(t, "💸您已成功執行合約！\n💎合約地址：", summary)
}

func TestTitleSummaryAtContract(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0x47d16acbfa86cc23c23e5f9ad98181bc99cbe47c354fb0d477c33993db40ad34",
			Category:        "txlist",
			FromAddress:     "******************************************",
			ToAddress:       "0x0437ec2a78d1b23dce3e5a233c1373e1873e1873",
			ContractAddress: "0x0437ec2a78d1b23dce3e5a233c1373e1873e1873",
			Asset:           "",
			Value:           "0",
			ValueDecimals:   18,
			ValueDecimal:    0.0,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeContractExecution, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0xdda6...c36a・Contract Execution", title)
	assert.Equal(t, "💸You successfully executed a contract! \n💎Contract Address: 0x0437...1873", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0xdda6...c36a・合约互动", title)
	assert.Equal(t, "💸您已成功执行合约！\n💎合约地址：0x0437...1873", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0xdda6...c36a・合約互動", title)
	assert.Equal(t, "💸您已成功執行合約！\n💎合約地址：0x0437...1873", summary)
}

func TestTitleSummaryAtFailed(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "mainnet",
			Address:         "******************************************",
			TxHash:          "0x5af3da3efcf41331d8ee8fd18afbead43df32ef3f9322a1e80e571f52a73256e",
			Category:        "txlist",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "",
			Asset:           "ETH",
			Value:           "0",
			ValueDecimals:   18,
			ValueDecimal:    0.0,
			IsError:         1,
			TxTimestamp:     time.Now(),
			MethodID:        util.Ptr("0x"),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeTransactionFailed, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0xdda6...c36a・Transaction Failed", title)
	assert.Equal(t, "😨 Please check the transaction details asap.", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0xdda6...c36a・交易失败", title)
	assert.Equal(t, "😨 请尽快检查交易详情。", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0xdda6...c36a・交易失敗", title)
	assert.Equal(t, "😨 請儘快檢查交易詳情。", summary)
}

func TestTitleSummaryAtDeploy(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0x71adb57375b1482cd14f80fcbcfe358d2b2b4f774b39a931f84a2278b378d812",
			Category:        "txlist",
			FromAddress:     "******************************************",
			ToAddress:       "",
			ContractAddress: "******************************************",
			Asset:           "",
			Value:           "0",
			ValueDecimals:   18,
			ValueDecimal:    0.0,
			IsError:         0,
			TxTimestamp:     time.Now(),
			MethodID:        util.Ptr("0x"),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeDeploy, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0x7e6e...3b1d・Deployment", title)
	assert.Equal(t, "🚀You successfully deployed a contract! \n💎Contract Address: ", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0x7e6e...3b1d・布署合约", title)
	assert.Equal(t, "🚀您已成功布署合约！\n💎合约地址：", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0x7e6e...3b1d・佈署合約", title)
	assert.Equal(t, "🚀您已成功部署合約！\n💎合約地址：", summary)
}

func TestTitleSummaryAtWrap(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0xb100c9b257f5fcbbcad0162c80288244d2fda06c3ed1af2e84e548b62da2f481",
			Category:        "txlist",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "WETH",
			Value:           "5000000000000000000",
			ValueDecimals:   18,
			ValueDecimal:    5.0,
			IsError:         0,
			TxTimestamp:     time.Now(),
			MethodID:        util.Ptr("0xd0e30db0"),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeWrap, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0x0901...5e33・Wrapped 5 WETH", title)
	assert.Equal(t, "📦You successfully got 5 WETH! \n💎Contract Address: 0x0d50...1270", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0x0901...5e33・打包代币 5 WETH", title)
	assert.Equal(t, "📦您已成功打包 5 WETH！\n💎合约地址：0x0d50...1270", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0x0901...5e33・打包 5 WETH", title)
	assert.Equal(t, "📦您已成功打包 5 WETH！\n💎合約地址：0x0d50...1270", summary)
}

func TestTitleSummaryAtUnwrap(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0xf8246799fd432dfe71aae21523d4375d15a94b055744d9cd9aed9c97bbddf4d8",
			Category:        "tokentx",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "WETH",
			Value:           "800000000000000000",
			ValueDecimals:   18,
			ValueDecimal:    0.8,
			IsError:         0,
			TxTimestamp:     time.Now(),
			MethodID:        util.Ptr("0x2e1a7d4d"),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeReceivedOne, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0x0901...5e33・Received 0.8 WETH", title)
	assert.Equal(t, "💰You successfully got 0.8 WETH! \n👾From: 0x0d50...1270", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0x0901...5e33・收到 0.8 WETH", title)
	assert.Equal(t, "💰您已成功收到 0.8 WETH！\n👾来自：0x0d50...1270", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0x0901...5e33・收到 0.8 WETH", title)
	assert.Equal(t, "💰您已成功收到 0.8 WETH！\n👾來自：0x0d50...1270", summary)
}

func TestTitleSummaryAtApprove(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0x8541562a4b2f9943216beffca83f03e5fe776ebcdf4768036f302204cac818ac",
			Category:        "txlist",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "",
			Asset:           "USDC",
			Value:           "0",
			ValueDecimals:   6,
			ValueDecimal:    0.0,
			IsError:         0,
			TxTimestamp:     time.Now(),
			MethodID:        util.Ptr("0x095ea7b3"),
			Approve:         1,
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeApprove, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0xdda6...c36a・Approved USDC", title)
	assert.Equal(t, "✅You successfully approved USDC!", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0xdda6...c36a・授权 USDC", title)
	assert.Equal(t, "✅您已成功授权 USDC！", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0xdda6...c36a・授權 USDC", title)
	assert.Equal(t, "✅您已成功授權 USDC！", summary)
}

func TestTitleSummaryAtRevoke(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0x2f3f493f14700f6591e6f792cb1b3ddd82b3dc864a981ad3ea7d3ca042d726e9",
			Category:        "txlist",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "",
			Asset:           "DAI",
			Value:           "0",
			ValueDecimals:   18,
			ValueDecimal:    0.0,
			IsError:         0,
			TxTimestamp:     time.Now(),
			MethodID:        util.Ptr("0x095ea7b3"),
			Approve:         -1,
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeRevoke, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0xb884...c553・Revoked DAI", title)
	assert.Equal(t, "👋You successfully revoked DAI!", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0xb884...c553・撤销授权 DAI", title)
	assert.Equal(t, "👋您已成功撤销授权 DAI！", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0xb884...c553・撤銷授權 DAI", title)
	assert.Equal(t, "👋您已成功撤銷授權 DAI！", summary)
}

func TestTitleSummaryAtRentEnergy(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "tron",
			Address:         "TXNpWV4gzAkxZWaFPJwkzqWwTvUUqzScvP",
			TxHash:          "6bacfdf8d0022265ae7b80c26ae594762b29adee59f4905d123bed869bd5fb57",
			Category:        "txlist",
			FromAddress:     "TXNpWV4gzAkxZWaFPJwkzqWwTvUUqzScvP",
			ToAddress:       "TCxUypP7WpQYsCHEj6k3eXB2mL5ToFf1R7",
			ContractAddress: "TCxUypP7WpQYsCHEj6k3eXB2mL5ToFf1R7",
			Asset:           "TRX",
			Value:           "2149177",
			ValueDecimals:   6,
			ValueDecimal:    2.149177,
			IsError:         0,
			TxTimestamp:     time.Now(),
			MethodID:        util.Ptr("sendTokenWithFee"),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeSentOne, txStat.TransactionType())

	title, summary := txStat.TitleSummary("zh_TW")
	assert.Equal(t, "TXNpWV...ScvP・送出 2.149177 TRX", title)
	assert.Equal(t, "💸您已成功送出 2.149177 TRX！\n👾發送給：TCxUyp...f1R7", summary)

	_, err := rdb.GormRepo().CreateSendWithRent(context.Background(), &domain.SendWithRent{
		OrgID:               1,
		UID:                 "uid",
		Chain:               domain.Tron,
		From:                domain.NewTronAddress("TXNpWV4gzAkxZWaFPJwkzqWwTvUUqzScvP"),
		Recipient:           domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
		TokenAddress:        domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"),
		Amount:              big.NewInt(123),
		Status:              domain.SendWithRentStatusSuccess,
		Fee:                 decimal.NewFromFloat(0.0),
		FeeTransferTxHash:   util.Ptr("6bacfdf8d0022265ae7b80c26ae594762b29adee59f4905d123bed869bd5fb57"),
		TokenTransferTxHash: util.Ptr("1234"),
		EstimatedFinishAt:   time.Now(),
	})
	assert.Nil(t, err)

	txStat = parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeRentEnergy, txStat.TransactionType())

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "TXNpWV...ScvP・租用能量", title)
	assert.Equal(t, "🔋您已成功租用能量！ \n支付: 2.149177 TRX", summary)
}

func TestFetchAsset(t *testing.T) {
	rdb.Reset()
	nft := &dbmodel.NftAsset{
		ChainID:         "polygon",
		ContractAddress: "0x24aa8abd6d7df783d1e3efde9f24a1aa231485e6",
		TokenID:         "486735",
		Name:            "Sample NFT",
		ImageURL:        "https://example.com/nft-image.png",
	}
	err := rdb.Get().Create(nft).Error
	assert.Nil(t, err)

	details := []*dbmodel.TxDetail{
		{
			ChainID:         "matic",
			Address:         "0xabcdefabcdefabcdefabcdefabcdefabcdefabcd",
			TxHash:          "******************************************90abcdef1234567890abcdef",
			Category:        "tokennfttx",
			FromAddress:     "0xabcdefabcdefabcdefabcdefabcdefabcdefabcd",
			ToAddress:       "******************************************",
			ContractAddress: "0x24aa8abd6d7df783d1e3efde9f24a1aa231485e6",
			Asset:           "1 UNKNOWN_NFT",
			TokenID:         "486735",
			Value:           "1",
			ValueDecimals:   0,
			ValueDecimal:    1.0,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.True(t, txStat.HasMetadata())

	metaJSON := txStat.MetadataJSONString()
	assert.NotNil(t, metaJSON)

	assert.Equal(t, "1 UNKNOWN_NFT#486735", txStat.Send[0])
}

func TestParseTxContractExecution(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "mainnet",
			Address:         "******************************************",
			TxHash:          "0x91e2d26d70e894cf4f3b2ec10549d3b191798459306f4279a557971e7e223902",
			Category:        "txlist",
			FromAddress:     "******************************************",
			ToAddress:       "0x4976fb03c32e5b8cfe2b6ccb31c09ba78ebaba41",
			ContractAddress: "0x4976fb03c32e5b8cfe2b6ccb31c09ba78ebaba41",
			Asset:           "",
			Value:           "0",
			ValueDecimals:   18,
			ValueDecimal:    0.0,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeContractExecution, txStat.TransactionType())

	title, summary := txStat.TitleSummary("en")
	assert.Equal(t, "0x0901...5e33・Contract Execution", title)
	assert.Equal(t, "💸You successfully executed a contract! \n💎Contract Address: 0x4976...ba41", summary)

	title, summary = txStat.TitleSummary("zh_CN")
	assert.Equal(t, "0x0901...5e33・合约互动", title)
	assert.Equal(t, "💸您已成功执行合约！\n💎合约地址：0x4976...ba41", summary)

	title, summary = txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0x0901...5e33・合約互動", title)
	assert.Equal(t, "💸您已成功執行合約！\n💎合約地址：0x4976...ba41", summary)
}

func TestParseTxSelfTransfer(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "arb",
			Address:         "******************************************",
			TxHash:          "0x91e2d26d70e894cf4f3b2ec10549d3b191798459306f4279a557971e7e223902",
			Category:        "txlist",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "",
			Asset:           "ETH",
			TokenID:         "",
			Value:           "0.02",
			ValueDecimals:   18,
			ValueDecimal:    0.02,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeTradedOne, txStat.TransactionType())

	title, summary := txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0x8f4e...f824・送出 ETH", title)
	assert.Equal(t, "💸您已成功送出 0 ETH！\n👾發送給：0x8f4e...f824", summary)
}

func TestParseTxTokenTransfer(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "matic",
			Address:         "******************************************",
			TxHash:          "0x7b3ce282b6282aeda32e214bf98bb32ad6c1dc864a981ad3ea7d3ca042d726e9",
			Category:        "tokentx",
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "USDC",
			TokenID:         "",
			Value:           "1510000",
			ValueDecimals:   6,
			ValueDecimal:    1.51,
			IsError:         0,
			TxTimestamp:     time.Now(),
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeSentOne, txStat.TransactionType())

	title, summary := txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0x0901...5e33・送出 1.51 USDC", title)
	assert.Equal(t, "💸您已成功送出 1.51 USDC！\n👾發送給：0x19ad...5c64", summary)
}

func TestParseSolTxDetails(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "sol",
			Address:         "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			TxHash:          "2hZwhKwrgheJQuFcvujujqgBUsWvBRp4TLspfwa3rf71cdzWDCcbzudTSWxHDzaAR9gCT3cD4xNegPTtJrG51RdU",
			TxTimestamp:     time.Date(2025, 1, 3, 7, 17, 29, 0, time.UTC),
			Category:        "txlist",
			BlockNum:        311566546,
			FromAddress:     "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			ToAddress:       "6m2CDdhRgxpH4WjvdzxAYbGxwdGUz5MziiL5jek2kBma",
			ContractAddress: "",
			Asset:           "SOL",
			Value:           "0",
			ValueDecimals:   9,
			ValueDecimal:    0,
			IsError:         0,
		},
		{
			ChainID:         "sol",
			Address:         "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			TxHash:          "2hZwhKwrgheJQuFcvujujqgBUsWvBRp4TLspfwa3rf71cdzWDCcbzudTSWxHDzaAR9gCT3cD4xNegPTtJrG51RdU",
			TxTimestamp:     time.Date(2025, 1, 3, 7, 17, 29, 0, time.UTC),
			Category:        "txlistinternal",
			BlockNum:        311566546,
			FromAddress:     "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			ToAddress:       "25mYnjJ2MXHZH6NvTTdA63JvjgRVcuiaj6MRiEQNs1Dq",
			ContractAddress: "",
			Asset:           "SOL",
			Value:           "8189750",
			ValueDecimals:   9,
			ValueDecimal:    0.00818975,
			IsError:         0,
		},
		{
			ChainID:         "sol",
			Address:         "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			TxHash:          "2hZwhKwrgheJQuFcvujujqgBUsWvBRp4TLspfwa3rf71cdzWDCcbzudTSWxHDzaAR9gCT3cD4xNegPTtJrG51RdU",
			TxTimestamp:     time.Date(2025, 1, 3, 7, 17, 29, 0, time.UTC),
			Category:        "tokentx",
			BlockNum:        311566546,
			FromAddress:     "2FCuCToyXnrq2Lx6DKttpi72oNx77bqtK2Adgo52pMUh",
			ToAddress:       "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			ContractAddress: "7ddd3rNWdx36MgnLkoUdwEPNoXt1bi9fszorkZungN2E",
			Asset:           "AM",
			Value:           "74814050354",
			ValueDecimals:   6,
			ValueDecimal:    74814.050354,
			IsError:         0,
		},
		{
			ChainID:         "sol",
			Address:         "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			TxHash:          "2hZwhKwrgheJQuFcvujujqgBUsWvBRp4TLspfwa3rf71cdzWDCcbzudTSWxHDzaAR9gCT3cD4xNegPTtJrG51RdU",
			TxTimestamp:     time.Date(2025, 1, 3, 7, 17, 29, 0, time.UTC),
			Category:        "tokentx",
			BlockNum:        311566546,
			FromAddress:     "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			ToAddress:       "2EEAcuqsJUpQXgNKAe1YtmHmFkPn2G5YyQbMzSZXdMPS",
			ContractAddress: "So11111111111111111111111111111111111111112",
			Asset:           "SOL",
			Value:           "1910620",
			ValueDecimals:   9,
			ValueDecimal:    0.000191062,
			IsError:         0,
		},
		{
			ChainID:         "sol",
			Address:         "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			TxHash:          "2hZwhKwrgheJQuFcvujujqgBUsWvBRp4TLspfwa3rf71cdzWDCcbzudTSWxHDzaAR9gCT3cD4xNegPTtJrG51RdU",
			TxTimestamp:     time.Date(2025, 1, 3, 7, 17, 29, 0, time.UTC),
			Category:        "tokentx",
			BlockNum:        311566546,
			FromAddress:     "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			ToAddress:       "FERjPVNEa7Udq8CEv68h6tPL46Tq7ieE49HrE2wea3XT",
			ContractAddress: "So11111111111111111111111111111111111111112",
			Asset:           "SOL",
			Value:           "953399630",
			ValueDecimals:   9,
			ValueDecimal:    0.95339963,
			IsError:         0,
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeTradedOne, txStat.TransactionType())

	title, summary := txStat.TitleSummary("zh_TW")
	assert.Equal(t, "D4SySR...MfJ7・交易", title)
	assert.Equal(t, "🔁您已成功交易 ≈ 0.96178 SOL 換取 74814.0503... AM！\n💎合約地址：6m2CDd...kBma", summary)
}

func TestParseSpecialETHTxDetails(t *testing.T) {
	rdb.Reset()
	details := []*dbmodel.TxDetail{
		{
			ChainID:         "eth",
			Address:         "******************************************",
			TxHash:          "0x1234123412341234123412341234123412341234123412341234123412341234",
			TxTimestamp:     time.Date(2025, 1, 3, 7, 17, 29, 0, time.UTC),
			Category:        "txlist",
			BlockNum:        123456,
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "",
			Asset:           "ETH",
			Value:           "1000000000000000000",
			ValueDecimals:   18,
			ValueDecimal:    1.0,
			IsError:         0,
		},
		{
			ChainID:         "eth",
			Address:         "******************************************",
			TxHash:          "0x1234123412341234123412341234123412341234123412341234123412341234",
			TxTimestamp:     time.Date(2025, 1, 3, 7, 17, 29, 0, time.UTC),
			Category:        "txlistinternal",
			BlockNum:        123456,
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "",
			Asset:           "ETH",
			Value:           "500000000000000000",
			ValueDecimals:   18,
			ValueDecimal:    0.5,
			IsError:         0,
		},
	}

	txStat := parseTxByTxDetails(context.Background(), details)
	assert.Equal(t, model.TransactionTypeSentOne, txStat.TransactionType())

	title, summary := txStat.TitleSummary("zh_TW")
	assert.Equal(t, "0x0901...5e33・送出 0.5 ETH", title)
	assert.Equal(t, "💸您已成功送出 0.5 ETH！\n👾發送給：0xDdA6...C36a", summary)
}
