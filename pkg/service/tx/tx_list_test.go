package tx

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/assert"
)

func TestLists(t *testing.T) {
	ctx := context.Background()
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))

	r := rdb.GormRepo()
	application.Init(r)

	// Setup test user with Japanese locale
	uid := "test_user_1"
	clientID := "test_client"
	clientID2 := "test_client_2"
	defaultClientID := "20991a3ae83233d6de85d62906d71fd3"
	users := map[string]domain.UserData{
		uid: {
			UserInfo: domain.UserInfo{
				UID:       uid,
				Locale:    "ja_JP",
				LocaleMap: map[string]string{clientID: "ja_JP", clientID2: "zh_TW", defaultClientID: "en_US"},
			},
			Wallets: &domain.Wallets{
				WalletGroups: []*domain.WalletGroup{
					{
						EvmWallets: []*domain.UserWallet{
							{Address: "******************************************"},
						},
						BtcWallets: []*domain.UserWallet{
							{Address: "******************************************"},
						},
						TronWallets: []*domain.UserWallet{
							{Address: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"},
						},
						SolanaWallets: []*domain.UserWallet{
							{Address: "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7"},
						},
					},
				},
			},
		},
	}
	assert.Nil(t, r.BatchSetUsers(ctx, users))

	// Setup test transaction
	txTimestamp := time.Now()
	txList := &model.TxList{
		ChainID:       "eth",
		Address:       "******************************************",
		TargetAddress: "0x456",
		TxHash:        "0xabc",
		BlockNum:      12345,
		TxType:        1, // TxTypeFailed
		TxTimestamp:   txTimestamp,
	}

	assert.Nil(t, rdb.GetWith(ctx).Create(txList).Error)

	// Initialize service

	Init(rdb.GormRepo())

	// Test Lists function
	params := &rdb.TxListParams{
		UID:      uid,
		ClientID: clientID,
		ChainIDs: []string{"eth"},
	}

	txLists, paging, code, err := Get().Lists(ctx, params)
	assert.NoError(t, err)
	assert.Equal(t, 0, code)
	assert.NotNil(t, paging)
	assert.Len(t, txLists, 1)

	// Verify Japanese localization
	assert.Equal(t, "取引失敗", txLists[0].TxTypeDesc) // "Transaction Failed" in Japanese
	assert.Equal(t, "eth", txLists[0].ChainID)
	assert.Equal(t, "******************************************", txLists[0].Address)
	assert.Equal(t, "0x456", txLists[0].TargetAddress)
	assert.Equal(t, "0xabc", txLists[0].TxHash)
	assert.Equal(t, uint32(12345), txLists[0].BlockNum)
	assert.Equal(t, int32(1), txLists[0].TxType)

	params.ClientID = clientID2
	txLists, paging, code, err = Get().Lists(ctx, params)
	assert.NoError(t, err)
	assert.Equal(t, 0, code)
	assert.NotNil(t, paging)
	assert.Len(t, txLists, 1)
	assert.Equal(t, "交易失敗", txLists[0].TxTypeDesc) // "Transaction Failed" in Chinese

	params.ClientID = ""
	txLists, paging, code, err = Get().Lists(ctx, params)
	assert.NoError(t, err)
	assert.Equal(t, 0, code)
	assert.NotNil(t, paging)
	assert.Len(t, txLists, 1)
	assert.Equal(t, "Transaction Failed", txLists[0].TxTypeDesc)
}
