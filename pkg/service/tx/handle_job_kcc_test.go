package tx

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestHandleKccTokenTx(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	rdb.Reset()

	ctx := context.Background()
	HandleTxJob(ctx, &model.TxJob{
		ChainID:  "kcc",
		Address:  "******************************************",
		Category: "tokentx",
		FromNum:  0,
		ToNum:    0,
	})
	HandleTxJob(ctx, &model.TxJob{
		ChainID:  "kcc",
		Address:  "******************************************",
		Category: "tokentx",
		FromNum:  0,
		ToNum:    0,
	})
	HandleTxJob(ctx, &model.TxJob{
		ChainID:  "kcc",
		Address:  "******************************************",
		Category: "tokentx",
		FromNum:  0,
		ToNum:    1,
	})
	HandleTxJob(ctx, &model.TxJob{
		ChainID:  "kcc",
		Address:  "******************************************",
		Category: "tokentx",
		FromNum:  0,
		ToNum:    2,
	})
	details, err := rdb.GetTxDetailsByBlockNumRange(ctx, "kcc", "******************************************", 0, 16000000)
	assert.NoError(t, err)
	assert.True(t, len(details) >= 50)
	for _, detail := range details {
		if detail.Category == "tokennfttx" || detail.Category == "token1155tx" {
			assert.NotEmpty(t, detail.TokenID)
		} else if detail.Category == "tokentx" {
			assert.NotEmpty(t, detail.Asset)
			assert.NotEmpty(t, detail.Value)
			assert.Greater(t, detail.ValueDecimal, float64(0))
			assert.Greater(t, detail.ValueDecimals, int32(0))
		} else {
			assert.Fail(t, "wrong category")
		}
	}
	t.Logf("details[0]: %+v", details[0])
}
