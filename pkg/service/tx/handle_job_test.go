package tx

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/goplus"
	tronscanapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/tronscan-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/websocket"
	"github.com/kryptogo/kg-wallet-backend/repo"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestFillNFTAssetName(t *testing.T) {
	t.Run("should fill nft asset name", func(t *testing.T) {
		testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "MORALIS_API_KEY"})

		details := []*model.TxDetail{
			{
				ChainID:         "matic",
				Address:         "******************************************",
				TxHash:          "0x468a05eaf1846a7b624de164e4cbddcfcd63eae7ad12fda2b3f0fc53e4d524d5",
				Category:        "tx1155to",
				FromAddress:     "******************************************",
				ToAddress:       "******************************************",
				ContractAddress: "******************************************",
				TokenID:         "4388",
				Value:           "1",
				ValueDecimals:   0,
				ValueDecimal:    1,
			},
			{
				ChainID:         "matic",
				Address:         "******************************************",
				TxHash:          "0x468a05eaf1846a7b624de164e4cbddcfcd63eae7ad12fda2b3f0fc53e4d524d5",
				Category:        "tx1155to",
				FromAddress:     "******************************************",
				ToAddress:       "******************************************",
				ContractAddress: "******************************************",
				TokenID:         "4433",
				Value:           "1",
				ValueDecimals:   0,
				ValueDecimal:    1,
			},
			{
				ChainID:         "matic",
				Address:         "******************************************",
				TxHash:          "0x468a05eaf1846a7b624de164e4cbddcfcd63eae7ad12fda2b3f0fc53e4d524d5",
				Category:        "tokentx",
				FromAddress:     "******************************************",
				ToAddress:       "******************************************",
				ContractAddress: "0x9e2d266d6c90f6c0d80a88159b15958f7135b8af",
				TokenID:         "",
				Value:           "123000000",
				ValueDecimals:   9,
				ValueDecimal:    0.123,
			},
		}
		fillNFTAssetName(context.Background(), details)
		assert.Equal(t, "UNKNOWN", details[0].Asset)
		assert.Equal(t, "UNKNOWN", details[1].Asset)
		assert.Equal(t, "SSX", details[2].Asset)
	})

	t.Run("Should not replace asset name with unknown token", func(t *testing.T) {
		testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "MORALIS_API_KEY"})
		details := []*model.TxDetail{
			{
				ChainID:         "sol",
				ContractAddress: "",
				Asset:           "",
			},
			{
				ChainID:         "sol",
				ContractAddress: "",
				Asset:           "SOL",
			},
		}
		fillNFTAssetName(context.Background(), details)
		assert.Equal(t, "Unknown Token", details[0].Asset)
		assert.Equal(t, "SOL", details[1].Asset)
	})
}

func TestUpdateTxlistsbyBlockNumRange(t *testing.T) {
	ctx := context.Background()
	rateLimiter := domain.NewAllPassRateLimiter()
	goplus.InitDefault(rateLimiter)
	rdb.Reset()
	tokenmeta.Init(repo.Unified(), []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	Init(rdb.GormRepo())
	kgErr := rdb.GormRepo().BatchSetUsers(context.Background(), map[string]domain.UserData{
		"user1": {
			Wallets: &domain.Wallets{
				EvmWallets: []*domain.UserWallet{
					{Address: "******************************************"},
					{Address: "******************************************"},
				},
			},
		},
	})
	if kgErr != nil {
		t.Fatal(fmt.Errorf("kgErr: %v", kgErr))
	}

	details := []*model.TxDetail{
		{
			ChainID:         "arb",
			Address:         "******************************************",
			TxHash:          "0xc2d4fa0f770da28b3fcaef05a45bc76ab3bab336cf60c4206eecb4c1e5b64473",
			Category:        "txlist",
			BlockNum:        190159132,
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "",
			Asset:           "ETH",
			Value:           "200000000000000",
			ValueDecimals:   18,
			ValueDecimal:    0.00020000,
			GasPrice:        "100000000",
			GasUsed:         "731722",
			IsError:         0,
			MethodID:        util.Ptr("0x"),
			Approve:         0,
			TxTimestamp:     time.Date(2024, 3, 14, 3, 35, 40, 0, &time.Location{}),
			ModifiedAt:      time.Now(),
		},
		{
			ChainID:         "arb",
			Address:         "******************************************",
			TxHash:          "0x11ef6be20c4dcee0d57e1a4d6f41a379e5f3ef22abc04a7eb4d378c119a9dc9d",
			Category:        "tokentx",
			BlockNum:        190159002,
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "******************************************",
			Asset:           "USDT",
			Value:           "63270000",
			ValueDecimals:   6,
			ValueDecimal:    63.27000000,
			GasPrice:        "100000000",
			GasUsed:         "1156316",
			IsError:         0,
			Approve:         0,
			TxTimestamp:     time.Date(2024, 3, 14, 3, 35, 6, 0, &time.Location{}),
			ModifiedAt:      time.Now(),
		},
	}
	assert.Nil(t, rdb.Get().Create(details).Error)
	UpdateTxlistsByBlockNumRange(ctx, "arb", "******************************************", 190159002, 190159132)
	UpdateTxlistsByBlockNumRange(ctx, "arb", "******************************************", 190159002, 190159132)

	txs, errCode, err := rdb.TxLists(ctx, &rdb.TxListParams{
		UID:      "user1",
		ChainIDs: []string{"arb"},
		Update:   false,
	})
	assert.Nil(t, err)
	assert.Equal(t, 0, errCode)
	assert.Len(t, txs, 2)

	assert.Equal(t, "arb", txs[0].ChainID)
	assert.Equal(t, "******************************************", txs[0].Address)
	assert.Equal(t, "0xc2d4fa0f770da28b3fcaef05a45bc76ab3bab336cf60c4206eecb4c1e5b64473", txs[0].TxHash)
	assert.Equal(t, time.Date(2024, 3, 14, 3, 35, 40, 0, time.UTC), txs[0].TxTimestamp)
	assert.Equal(t, "******************************************", txs[0].TargetAddress)
	assert.Equal(t, uint32(190159132), txs[0].BlockNum)
	assert.Equal(t, int32(6), txs[0].TxType)
	assert.Nil(t, txs[0].Send)
	assert.Equal(t, "0.0002 ETH", *(txs[0].Receive))

	assert.Equal(t, "arb", txs[1].ChainID)
	assert.Equal(t, "******************************************", txs[1].Address)
	assert.Equal(t, "0x11ef6be20c4dcee0d57e1a4d6f41a379e5f3ef22abc04a7eb4d378c119a9dc9d", txs[1].TxHash)
	assert.Equal(t, time.Date(2024, 3, 14, 3, 35, 6, 0, time.UTC), txs[1].TxTimestamp)
	assert.Equal(t, "******************************************", txs[1].TargetAddress)
	assert.Equal(t, uint32(190159002), txs[1].BlockNum)
	assert.Equal(t, int32(6), txs[1].TxType)
	assert.Nil(t, txs[1].Send)
	assert.Equal(t, "63.27 USDT", *(txs[1].Receive))
}

func TestUpdateTxlistsbyBlockNumRange_ShouldSkipInHistory(t *testing.T) {
	ctx := context.Background()
	rateLimiter := domain.NewAllPassRateLimiter()
	goplus.InitDefault(rateLimiter)
	rdb.Reset()
	tokenmeta.Init(repo.Unified(), []domain.TokenMetadataFetcher{}, []domain.TokenMetadataUpdater{})

	Init(rdb.GormRepo())
	kgErr := rdb.GormRepo().BatchSetUsers(context.Background(), map[string]domain.UserData{
		"user1": {
			Wallets: &domain.Wallets{
				EvmWallets: []*domain.UserWallet{
					{Address: "******************************************"},
				},
			},
		},
	})
	if kgErr != nil {
		t.Fatal(fmt.Errorf("kgErr: %v", kgErr))
	}

	// Insert a transaction detail that matches the shouldSkipInHistory condition
	details := []*model.TxDetail{
		{
			ChainID:         "arb",
			Address:         "******************************************",
			TxHash:          "0x1234",
			Category:        "txlist",
			BlockNum:        190159132,
			FromAddress:     "******************************************",
			ToAddress:       "******************************************",
			ContractAddress: "",
			Asset:           "ETH",
			Value:           "0",
			ValueDecimals:   18,
			ValueDecimal:    0.0,
			GasPrice:        "100000000",
			GasUsed:         "731722",
			IsError:         0,
			Approve:         0,
			TxTimestamp:     time.Date(2024, 3, 14, 3, 35, 40, 0, &time.Location{}),
			ModifiedAt:      time.Now(),
		},
	}
	assert.Nil(t, rdb.Get().Create(details).Error)

	// Call UpdateTxlistsbyBlockNumRange
	UpdateTxlistsByBlockNumRange(ctx, "arb", "******************************************", 190159132, 190159132)

	// Assert that the transaction list is empty for the user
	txs, errCode, err := rdb.TxLists(ctx, &rdb.TxListParams{
		UID:      "user1",
		ChainIDs: []string{"arb"},
		Update:   false,
	})
	assert.Nil(t, err)
	assert.Equal(t, 0, errCode)
	assert.Len(t, txs, 0)
}

func TestHandleJob(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ARBISCAN_API_KEY", "MORALIS_API_KEY"})

	ctx := context.Background()
	rdb.Reset()

	ctrl := gomock.NewController(t)
	r := domain.NewMockUserRepo(ctrl)
	r.EXPECT().GetUser(gomock.Any(), "user1", "", true, gomock.Any()).Return(&domain.UserData{
		EthereumAddress: util.Ptr("******************************************"),
	}, nil).AnyTimes()
	Init(r)

	txUpdates := []*model.TxUpdate{
		{
			ChainID:    "arb",
			Address:    "******************************************",
			Category:   "tokennfttx",
			FromNum:    16375499,
			ToNum:      16375499,
			CreatedAt:  time.Now(),
			ModifiedAt: time.Now(),
		},
		{
			ChainID:    "arb",
			Address:    "******************************************",
			Category:   "tokentx",
			FromNum:    95582884,
			ToNum:      190159000,
			CreatedAt:  time.Now(),
			ModifiedAt: time.Now(),
		},
		{
			ChainID:    "arb",
			Address:    "******************************************",
			Category:   "txlist",
			FromNum:    15500620,
			ToNum:      190159000,
			CreatedAt:  time.Now(),
			ModifiedAt: time.Now(),
		},
		{
			ChainID:    "arb",
			Address:    "******************************************",
			Category:   "txlistinternal",
			FromNum:    16063035,
			ToNum:      16063035,
			CreatedAt:  time.Now(),
			ModifiedAt: time.Now(),
		},
	}
	assert.Nil(t, rdb.Get().Create(txUpdates).Error)

	AddTxJobs(ctx, map[string][]string{
		"arb": {"******************************************"},
	})

	txJobs, err := rdb.GetNextBatch(ctx, "arb")
	assert.Nil(t, err)
	assert.Len(t, txJobs, 4)
	assert.Equal(t, "tokennfttx", txJobs[0].Category)
	assert.Equal(t, int32(16375500), txJobs[0].FromNum)
	assert.Equal(t, int32(0), txJobs[0].ToNum)
	assert.Equal(t, "tokentx", txJobs[1].Category)
	assert.Equal(t, int32(190159001), txJobs[1].FromNum)
	assert.Equal(t, int32(0), txJobs[1].ToNum)
	assert.Equal(t, "txlist", txJobs[2].Category)
	assert.Equal(t, int32(190159001), txJobs[2].FromNum)
	assert.Equal(t, int32(0), txJobs[2].ToNum)
	assert.Equal(t, "txlistinternal", txJobs[3].Category)
	assert.Equal(t, int32(16063036), txJobs[3].FromNum)
	assert.Equal(t, int32(0), txJobs[3].ToNum)

	for _, txJob := range txJobs {
		HandleTxJob(ctx, txJob)
	}

	txJobs, err = rdb.GetNextBatch(ctx, "arb")
	assert.Nil(t, err)
	assert.Len(t, txJobs, 0)

	txs, errCode, err := rdb.TxLists(ctx, &rdb.TxListParams{
		UID:      "user1",
		ChainIDs: []string{"arb"},
		Update:   false,
	})
	assert.Nil(t, err)
	assert.Equal(t, 0, errCode)
	assert.Len(t, txs, 10)
	t.Logf("txs[0]: %+v\n", *(txs[0]))
}

func TestHandleTronscanJob(t *testing.T) {
	ctx := context.Background()
	rdb.Reset()

	addr := "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"
	ctrl := gomock.NewController(t)
	wsMock := websocket.NewMockIService(ctrl)
	tronscanMock := tronscanapi.NewMockITronscan(ctrl)

	wsMock.EXPECT().SendEventByAddresses(gomock.Any(), []string{addr}, websocket.EventWalletTxListUpdated, gomock.Any()).Times(1).Return(nil)
	tronscanMock.EXPECT().Trc20Transfer(gomock.Any(), gomock.Any()).Return(&tronscanapi.Trc20TransferResp{
		Total:      1,
		RangeTotal: 1,
		TokenTransfers: []tronscanapi.TokenTx{
			{
				TransactionID: "test-tx-id-123",
				FromAddress:   addr,
				ToAddress:     "testtotronaddress",
				TokenInfo: tronscanapi.TokenInfo{
					TokenID:      "test-token-id-123",
					TokenAbbr:    "test-token-abbr-123",
					TokenName:    "test-token-name-123",
					TokenDecimal: 6,
				},
				Quant:   "1230000",
				BlockTs: 1718297712000,
			},
		},
	}, time.Second, nil).Times(1)

	websocket.Set(wsMock)
	tronscanapi.Set(tronscanMock)

	HandleTxJob(ctx, &model.TxJob{
		ChainID:  "tron",
		Address:  addr,
		Category: "tokentx",
	})

	txDetails := []*model.TxDetail{}
	err := rdb.Get().Where("address = ?", addr).Find(&txDetails).Error
	assert.Nil(t, err)
	assert.Len(t, txDetails, 1)
	assert.Equal(t, "test-tx-id-123", txDetails[0].TxHash)
	assert.Equal(t, addr, txDetails[0].FromAddress)
	assert.Equal(t, "testtotronaddress", txDetails[0].ToAddress)
	assert.Equal(t, "TEST-TOKEN-ABBR-123", txDetails[0].Asset)
	assert.Equal(t, "1230000", txDetails[0].Value)
	assert.Equal(t, int32(6), txDetails[0].ValueDecimals)
}
