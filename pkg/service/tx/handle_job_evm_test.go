package tx

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	etherscanapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/etherscan-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	gomock "go.uber.org/mock/gomock"
)

func TestHandleEvmTokenTx(t *testing.T) {
	// Setup
	rdb.Reset()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Mock etherscan API
	mockEtherscan := etherscanapi.NewMockIEvmscan(ctrl)
	etherscanapi.Set(mockEtherscan)

	// Mock token metadata repo
	mockTokenMetaRepo := domain.NewMockTokenMetadataRepo(ctrl)
	tokenmeta.Init(mockTokenMetaRepo, nil, nil)

	// Test data
	ctx := context.Background()
	chainID := "matic"
	address := "0x123"
	fromNum := uint32(0)
	toNum := uint32(1000000)

	// Create test job
	job := &model.TxJob{
		ChainID:   chainID,
		Address:   address,
		Category:  "tokentx",
		FromNum:   fromNum,
		ToNum:     toNum,
		Priority:  1,
		TryAfter:  time.Now(),
		CreatedAt: time.Now(),
	}
	rdb.AddTxJobs(ctx, []*model.TxJob{job})

	// Mock TokenTx response
	mockResp := &etherscanapi.TokenTxResp{
		Result: []etherscanapi.TokenTxResult{
			{
				BlockNumber:     "100",
				TimeStamp:       "1677777777",
				Hash:            "0xhash1",
				From:            "0xfrom1",
				To:              "0xto1",
				ContractAddress: "0xcontract1",
				TokenSymbol:     "TOKEN1",
				TokenDecimal:    "18",
				Value:           "1000000000000000000",
			},
			{
				BlockNumber:     "200",
				TimeStamp:       "1677777778",
				Hash:            "0xhash2",
				From:            "0xfrom2",
				To:              "0xto2",
				ContractAddress: "0xcontract2",
				TokenSymbol:     "TOKEN2",
				TokenDecimal:    "18",
				Value:           "2000000000000000000",
			},
			{
				BlockNumber:     "300",
				TimeStamp:       "1677777779",
				Hash:            "0xhash3",
				From:            "0xfrom3",
				To:              "0xto3",
				ContractAddress: "0xcontract3",
				TokenSymbol:     "TOKEN3",
				TokenDecimal:    "18",
				Value:           "3000000000000000000",
			},
			{
				BlockNumber:     "400",
				TimeStamp:       "1677777780",
				Hash:            "0xhash4",
				From:            "0xfrom4",
				To:              "0xto4",
				ContractAddress: "0xcontract4",
				TokenSymbol:     "TOKEN4",
				TokenDecimal:    "18",
				Value:           "4000000000000000000",
			},
			{
				BlockNumber:     "500",
				TimeStamp:       "1677777781",
				Hash:            "0xhash5",
				From:            "0xfrom5",
				To:              "0xto5",
				ContractAddress: "0xcontract5",
				TokenSymbol:     "TOKEN5",
				TokenDecimal:    "18",
				Value:           "5000000000000000000",
			},
		},
	}

	// Mock TokenTx call
	mockEtherscan.EXPECT().
		TokenTx(gomock.Any(), chainID, &etherscanapi.TxParams{
			Address:    address,
			Startblock: fromNum,
			Endblock:   toNum,
			Sort:       "desc",
		}).
		DoAndReturn(func(ctx context.Context, chainID string, params *etherscanapi.TxParams) (*etherscanapi.TokenTxResp, time.Duration, error) {
			return mockResp, time.Second, nil
		})

	// Mock IsSpam calls
	mockTokenMetaRepo.EXPECT().
		IsTokenSpam(gomock.Any(), domain.IDToChain(chainID), gomock.Any()).
		DoAndReturn(func(ctx context.Context, chain domain.Chain, addr string) (bool, error) {
			spamAddrs := map[string]bool{
				"0xcontract1": true,
				"0xcontract3": true,
				"0xcontract5": true,
			}
			return spamAddrs[addr], nil
		}).AnyTimes()

	// Execute
	handleEvmTokenTx(ctx, job)

	// Assert TxJobLog
	var txJobLogs []model.TxJobLog
	err := rdb.GetWith(ctx).Model(&model.TxJobLog{}).Find(&txJobLogs).Error
	assert.NoError(t, err)
	assert.Len(t, txJobLogs, 1)
	assert.Equal(t, int32(5), txJobLogs[0].DataCnt)
	assert.Equal(t, int32(1), txJobLogs[0].Status)

	// Assert TxDetails
	var txDetails []model.TxDetail
	err = rdb.GetWith(ctx).Model(&model.TxDetail{}).Find(&txDetails).Error
	assert.NoError(t, err)
	assert.Len(t, txDetails, 2) // Only non-spam tokens should be added

	// find txhash2
	txToVerify, found := lo.Find(txDetails, func(txDetail model.TxDetail) bool {
		return txDetail.TxHash == "0xhash2"
	})
	assert.True(t, found)
	assert.Equal(t, chainID, txToVerify.ChainID)
	assert.Equal(t, address, txToVerify.Address)
	assert.Equal(t, "0xhash2", txToVerify.TxHash)
	assert.Equal(t, "tokentx", txToVerify.Category)
	assert.Equal(t, uint32(200), txToVerify.BlockNum)
	assert.Equal(t, "0xfrom2", txToVerify.FromAddress)
	assert.Equal(t, "0xto2", txToVerify.ToAddress)
	assert.Equal(t, "0xcontract2", txToVerify.ContractAddress)
	assert.Equal(t, "TOKEN2", txToVerify.Asset)
	assert.Equal(t, "2000000000000000000", txToVerify.Value)
	assert.Equal(t, int32(18), txToVerify.ValueDecimals)
	assert.Equal(t, float64(2), txToVerify.ValueDecimal)

	// Assert TxUpdate
	var txUpdate model.TxUpdate
	err = rdb.GetWith(ctx).Model(&model.TxUpdate{}).First(&txUpdate).Error
	assert.NoError(t, err)
	assert.Equal(t, chainID, txUpdate.ChainID)
	assert.Equal(t, address, txUpdate.Address)
	assert.Equal(t, "tokentx", txUpdate.Category)
	assert.Equal(t, uint32(100), txUpdate.FromNum)
	assert.Equal(t, uint32(500), txUpdate.ToNum)

	// Assert TxJob is empty
	var txJobCount int64
	rdb.GetWith(ctx).Model(&model.TxJob{}).Count(&txJobCount)
	assert.Equal(t, int64(0), txJobCount)
}
