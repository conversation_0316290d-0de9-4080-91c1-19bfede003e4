// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/service/tx (interfaces: IService)
//
// Generated by this command:
//
//	mockgen -package=tx -self_package=github.com/kryptogo/kg-wallet-backend/pkg/service/tx -destination=common_mock.go . IService
//

// Package tx is a generated GoMock package.
package tx

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	model "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	model0 "github.com/kryptogo/kg-wallet-backend/pkg/model"
	rdb "github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	gomock "go.uber.org/mock/gomock"
)

// MockIService is a mock of IService interface.
type MockIService struct {
	ctrl     *gomock.Controller
	recorder *MockIServiceMockRecorder
}

// MockIServiceMockRecorder is the mock recorder for MockIService.
type MockIServiceMockRecorder struct {
	mock *MockIService
}

// NewMockIService creates a new mock instance.
func NewMockIService(ctrl *gomock.Controller) *MockIService {
	mock := &MockIService{ctrl: ctrl}
	mock.recorder = &MockIServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIService) EXPECT() *MockIServiceMockRecorder {
	return m.recorder
}

// AddByTransactionDetail mocks base method.
func (m *MockIService) AddByTransactionDetail(arg0 context.Context, arg1 domain.Chain, arg2 domain.Address, arg3 *domain.TransactionDetail, arg4 map[domain.ChainToken]*domain.TokenMetadata) (*model0.TxStat, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddByTransactionDetail", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*model0.TxStat)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// AddByTransactionDetail indicates an expected call of AddByTransactionDetail.
func (mr *MockIServiceMockRecorder) AddByTransactionDetail(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddByTransactionDetail", reflect.TypeOf((*MockIService)(nil).AddByTransactionDetail), arg0, arg1, arg2, arg3, arg4)
}

// Lists mocks base method.
func (m *MockIService) Lists(arg0 context.Context, arg1 *rdb.TxListParams) ([]*VTxList, *rdb.TokenPagingParams, int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Lists", arg0, arg1)
	ret0, _ := ret[0].([]*VTxList)
	ret1, _ := ret[1].(*rdb.TokenPagingParams)
	ret2, _ := ret[2].(int)
	ret3, _ := ret[3].(error)
	return ret0, ret1, ret2, ret3
}

// Lists indicates an expected call of Lists.
func (mr *MockIServiceMockRecorder) Lists(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lists", reflect.TypeOf((*MockIService)(nil).Lists), arg0, arg1)
}

// UpdateTxByUID mocks base method.
func (m *MockIService) UpdateTxByUID(arg0 context.Context, arg1, arg2 string, arg3 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateTxByUID", arg0, arg1, arg2, arg3)
}

// UpdateTxByUID indicates an expected call of UpdateTxByUID.
func (mr *MockIServiceMockRecorder) UpdateTxByUID(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTxByUID", reflect.TypeOf((*MockIService)(nil).UpdateTxByUID), arg0, arg1, arg2, arg3)
}

// UpdateTxByUIDAndWait mocks base method.
func (m *MockIService) UpdateTxByUIDAndWait(arg0 context.Context, arg1 string, arg2 bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateTxByUIDAndWait", arg0, arg1, arg2)
}

// UpdateTxByUIDAndWait indicates an expected call of UpdateTxByUIDAndWait.
func (mr *MockIServiceMockRecorder) UpdateTxByUIDAndWait(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTxByUIDAndWait", reflect.TypeOf((*MockIService)(nil).UpdateTxByUIDAndWait), arg0, arg1, arg2)
}

// WaitTxConfirmed mocks base method.
func (m *MockIService) WaitTxConfirmed(arg0 context.Context, arg1, arg2 string, arg3 int) model.TxStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitTxConfirmed", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(model.TxStatus)
	return ret0
}

// WaitTxConfirmed indicates an expected call of WaitTxConfirmed.
func (mr *MockIServiceMockRecorder) WaitTxConfirmed(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitTxConfirmed", reflect.TypeOf((*MockIService)(nil).WaitTxConfirmed), arg0, arg1, arg2, arg3)
}
