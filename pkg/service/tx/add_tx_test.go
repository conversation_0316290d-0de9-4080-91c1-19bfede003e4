package tx

import (
	"context"
	"math/big"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/goplus"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/repo"
	tokenmeta "github.com/kryptogo/kg-wallet-backend/service/token-meta"
	"github.com/stretchr/testify/assert"
)

func TestAddByTransactionDetail_LargeDecimals(t *testing.T) {
	ctx := context.Background()
	rdb.Reset()

	Init(rdb.GormRepo())
	uRepo := repo.Unified()
	rateLimiter := domain.NewAllPassRateLimiter()
	goplus.InitDefault(rateLimiter)
	tokenmeta.Init(uRepo, nil, nil)
	svc := Get()
	address := domain.NewEvmAddress("******************************************")
	chain := domain.Ethereum

	// Test case for SHIB-like token with large supply
	oneTrillionRaw := new(big.Int).Mul(
		big.NewInt(1000000000000),                             // 1 trillion
		new(big.Int).Exp(big.NewInt(10), big.NewInt(18), nil), // 18 decimals
	)

	tx := &domain.TransactionDetail{
		Hash:      "******************************************123456789012345678901234",
		Timestamp: time.Now(),
		BlockNum:  12345,
		From:      address,
		To:        domain.NewEvmAddress("******************************************"),
		TransactionTransfers: domain.TransactionTransfers{
			TokenTransfers: []*domain.TokenTransfer{
				{
					From:     address,
					To:       domain.NewEvmAddress("******************************************"),
					Contract: domain.NewEvmAddress("******************************************"), // SHIB contract
					Amount:   oneTrillionRaw,
				},
			},
		},
	}

	tokenMetadata := map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: chain, TokenID: "******************************************"}: {
			Symbol:   "SHIB",
			Name:     "SHIBA INU",
			Decimals: 18,
		},
	}

	txStat, kgErr := svc.AddByTransactionDetail(ctx, chain, address, tx, tokenMetadata)
	assert.Nil(t, kgErr)
	assert.NotNil(t, txStat)

	// Verify the transaction was stored correctly
	var details []*dbmodel.TxDetail
	assert.NoError(t, rdb.Get().Where("tx_hash = ?", tx.Hash).Order("address, category asc").Find(&details).Error)
	for _, detail := range details {
		t.Logf("detail: %+v", detail)
	}
	assert.Len(t, details, 2)

	detail := details[0]
	assert.Equal(t, "SHIB", detail.Asset)
	assert.Equal(t, oneTrillionRaw.String(), detail.Value)
	assert.Equal(t, int32(18), detail.ValueDecimals)
	assert.Equal(t, float64(1000000000000), detail.ValueDecimal) // 1 trillion
}

func TestSolanaSOLTransfer(t *testing.T) {
	ctx := context.Background()
	rdb.Reset()

	tx := &domain.TransactionDetail{
		Hash:      "5KWvC68Zf8jqH6t1wADh8ocdPWbhHua5SQkGr7b8pi412fVB5NVViFuJ2XBcrMkZ9GhXL2rDyqYaV1V4ZvkGgXH1",
		Timestamp: time.Unix(1731477853, 0),
		BlockNum:  301095652,
		From:      domain.NewAddressByChain(domain.Solana, "EJtaDztfm9UNKZPJyKyFhPJRuFgiA6ebEStEjr59xLx6"),
		To:        domain.NewAddressByChain(domain.Solana, "11111111111111111111111111111111"),
		TransactionTransfers: domain.TransactionTransfers{
			InternalTransfers: []*domain.NativeTokenTransfer{
				{
					From:   domain.NewAddressByChain(domain.Solana, "EJtaDztfm9UNKZPJyKyFhPJRuFgiA6ebEStEjr59xLx6"),
					To:     domain.NewAddressByChain(domain.Solana, "FWBjyjcsoQw4d1xupzkV3QaLvxwxxLzNAdkcvXopReyz"),
					Amount: big.NewInt(490000000),
				},
			},
		},
	}

	Init(rdb.GormRepo())
	uRepo := repo.Unified()
	tokenmeta.Init(uRepo, nil, nil)
	svc := Get()
	tokenMetadata := map[domain.ChainToken]*domain.TokenMetadata{}
	_, kgErr := svc.AddByTransactionDetail(ctx, domain.Solana, tx.From, tx, tokenMetadata)
	assert.Nil(t, kgErr)
	_, kgErr = svc.AddByTransactionDetail(ctx, domain.Solana, domain.NewAddressByChain(domain.Solana, "FWBjyjcsoQw4d1xupzkV3QaLvxwxxLzNAdkcvXopReyz"), tx, tokenMetadata)
	assert.Nil(t, kgErr)

	// Verify the transaction was stored correctly
	var details []*dbmodel.TxDetail
	assert.NoError(t, rdb.Get().Where("tx_hash = ?", tx.Hash).Order("address, category asc").Find(&details).Error)
	for _, detail := range details {
		t.Logf("detail: %+v", detail)
	}
	assert.Len(t, details, 3)

	assert.Equal(t, "11111111111111111111111111111111", details[0].ToAddress)
	assert.Equal(t, "SOL", details[0].Asset)
	assert.Equal(t, "0", details[0].Value)
	assert.Equal(t, int32(9), details[0].ValueDecimals)
	assert.Equal(t, float64(0), details[0].ValueDecimal)

	assert.Equal(t, "FWBjyjcsoQw4d1xupzkV3QaLvxwxxLzNAdkcvXopReyz", details[1].ToAddress)
	assert.Equal(t, "SOL", details[1].Asset)
	assert.Equal(t, "490000000", details[1].Value)
	assert.Equal(t, int32(9), details[1].ValueDecimals)
	assert.Equal(t, float64(0.49), details[1].ValueDecimal)

	assert.Equal(t, "EJtaDztfm9UNKZPJyKyFhPJRuFgiA6ebEStEjr59xLx6", details[2].FromAddress)
	assert.Equal(t, "SOL", details[2].Asset)
	assert.Equal(t, "490000000", details[2].Value)
	assert.Equal(t, int32(9), details[2].ValueDecimals)
	assert.Equal(t, float64(0.49), details[2].ValueDecimal)

	// Verify the txlist was updated correctly
	var txlists []*dbmodel.TxList
	assert.NoError(t, rdb.Get().Where("tx_hash = ?", tx.Hash).Find(&txlists).Error)
	assert.Len(t, txlists, 2)
	assert.Equal(t, tx.BlockNum, txlists[0].BlockNum)
	assert.Equal(t, tx.From.String(), txlists[0].Address)
	assert.Equal(t, tx.Hash, txlists[0].TxHash)
	assert.Equal(t, int32(7), txlists[0].TxType)
	assert.NotNil(t, txlists[0].Send)
	assert.Equal(t, "0.49 SOL", *txlists[0].Send)
	assert.Nil(t, txlists[0].Receive)

	assert.Equal(t, tx.BlockNum, txlists[1].BlockNum)
	assert.Equal(t, "FWBjyjcsoQw4d1xupzkV3QaLvxwxxLzNAdkcvXopReyz", txlists[1].Address)
	assert.Equal(t, tx.Hash, txlists[1].TxHash)
	assert.Equal(t, int32(6), txlists[1].TxType)
	assert.NotNil(t, txlists[1].Receive)
	assert.Equal(t, "0.49 SOL", *txlists[1].Receive)
	assert.Nil(t, txlists[1].Send)
}

func TestPolygonTrade(t *testing.T) {
	ctx := context.Background()
	rdb.Reset()

	tx := &domain.TransactionDetail{
		Hash:      "0x1b7c4d4f6cb2ed59675faab2bf89db1daf7a701e172c817dc7328f8d15068a16",
		Timestamp: time.Unix(1731852297, 0),
		BlockNum:  64396659,
		From:      domain.NewEvmAddress("0x0901549Bc297BCFf4221d0ECfc0f718932205e33"),
		To:        domain.NewEvmAddress("0x39A3920Ca05E238D558F0c1786314Bca647d1b39"),
		Value:     big.NewInt(0),
		TransactionTransfers: domain.TransactionTransfers{
			TokenTransfers: []*domain.TokenTransfer{
				{
					Contract: domain.NewEvmAddress("0x058d96BAa6f9D16853970b333ed993aCC0c35aDd"),
					From:     domain.NewEvmAddress("0x0000000000000000000000000000000000000000"),
					To:       domain.NewEvmAddress("0x0901549Bc297BCFf4221d0ECfc0f718932205e33"),
					Amount:   func() *big.Int { amount, _ := new(big.Int).SetString("20701690000000000000000", 10); return amount }(),
				},
				{
					Contract: domain.NewEvmAddress("0x9CA6a77C8B38159fd2dA9Bd25bc3E259C33F5E39"),
					From:     domain.NewEvmAddress("0x0901549Bc297BCFf4221d0ECfc0f718932205e33"),
					To:       domain.NewEvmAddress("0x39A3920Ca05E238D558F0c1786314Bca647d1b39"),
					Amount:   func() *big.Int { amount, _ := new(big.Int).SetString("10701690000000000000000", 10); return amount }(),
				},
			},
		},
	}

	Init(rdb.GormRepo())
	rateLimiter := domain.NewAllPassRateLimiter()
	goplus.InitDefault(rateLimiter)
	uRepo := repo.Unified()
	tokenmeta.Init(uRepo, nil, nil)
	svc := Get()

	// Setup token metadata
	tokenMetadata := map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: domain.Polygon, TokenID: "0x058d96BAa6f9D16853970b333ed993aCC0c35aDd"}: {
			Name:     "Token1",
			Symbol:   "TKN1",
			Decimals: 18,
		},
		{Chain: domain.Polygon, TokenID: "0x9CA6a77C8B38159fd2dA9Bd25bc3E259C33F5E39"}: {
			Name:     "Token2",
			Symbol:   "TKN2",
			Decimals: 18,
		},
	}

	// Test for sender
	_, kgErr := svc.AddByTransactionDetail(ctx, domain.Polygon, tx.From, tx, tokenMetadata)
	assert.Nil(t, kgErr)

	// Test for receiver
	_, kgErr = svc.AddByTransactionDetail(ctx, domain.Polygon, tx.To, tx, tokenMetadata)
	assert.Nil(t, kgErr)

	// Verify transaction details
	var details []*dbmodel.TxDetail
	assert.NoError(t, rdb.Get().Where("tx_hash = ?", tx.Hash).Find(&details).Error)
	for _, detail := range details {
		t.Logf("detail: %+v", detail)
	}
	assert.Len(t, details, 5)

	// find the one with from_address = 0x0000000000000000000000000000000000000000
	receiveFound := false
	sendFound := false
	for _, detail := range details {
		if detail.FromAddress == "0x0000000000000000000000000000000000000000" {
			receiveFound = true
			assert.Equal(t, "0x0901549Bc297BCFf4221d0ECfc0f718932205e33", detail.ToAddress)
			assert.Equal(t, "TKN1", detail.Asset)
			assert.Equal(t, "20701690000000000000000", detail.Value)
			assert.Equal(t, int32(18), detail.ValueDecimals)
			assert.Equal(t, float64(20701.69), detail.ValueDecimal)
		} else if detail.ToAddress == "0x39A3920Ca05E238D558F0c1786314Bca647d1b39" && detail.Asset == "TKN2" {
			sendFound = true
			assert.Equal(t, "0x0901549Bc297BCFf4221d0ECfc0f718932205e33", detail.FromAddress)
			assert.Equal(t, "10701690000000000000000", detail.Value)
			assert.Equal(t, int32(18), detail.ValueDecimals)
			assert.Equal(t, float64(10701.69), detail.ValueDecimal)
		}
	}
	assert.True(t, receiveFound)
	assert.True(t, sendFound)

	// Verify txlist
	var txlists []*dbmodel.TxList
	assert.NoError(t, rdb.Get().Where("tx_hash = ?", tx.Hash).Find(&txlists).Error)
	assert.Len(t, txlists, 2)
}

func TestAddByTransactionDetail_EvmSpam(t *testing.T) {
	ctx := context.Background()
	rdb.Reset()

	Init(rdb.GormRepo())
	uRepo := repo.Unified()
	rateLimiter := domain.NewAllPassRateLimiter()
	goplus.InitDefault(rateLimiter)
	tokenmeta.Init(uRepo, nil, nil)
	svc := Get()
	address := domain.NewEvmAddress("******************************************")
	chain := domain.Ethereum

	tx := &domain.TransactionDetail{
		Hash:      "******************************************1234567890123456789012ab",
		Timestamp: time.Now(),
		BlockNum:  12345,
		From:      address,
		To:        domain.NewEvmAddress("******************************************"),
		TransactionTransfers: domain.TransactionTransfers{
			TokenTransfers: []*domain.TokenTransfer{
				{
					From:     address,
					To:       domain.NewEvmAddress("******************************************"),
					Contract: domain.NewEvmAddress("******************************************"), // SHIB contract
					Amount:   big.NewInt(0),
				},
			},
		},
	}

	tokenMetadata := map[domain.ChainToken]*domain.TokenMetadata{
		{Chain: chain, TokenID: "******************************************"}: {
			Symbol:   "SHIB",
			Name:     "SHIBA INU",
			Decimals: 18,
		},
	}

	txStat, kgErr := svc.AddByTransactionDetail(ctx, chain, address, tx, tokenMetadata)
	assert.Nil(t, kgErr)
	assert.NotNil(t, txStat)

	// Verify the transaction was filtered out correctly because of spam
	var details []*dbmodel.TxDetail
	assert.NoError(t, rdb.Get().Where("tx_hash = ?", tx.Hash).Order("address, category asc").Find(&details).Error)
	assert.Len(t, details, 0)
}
