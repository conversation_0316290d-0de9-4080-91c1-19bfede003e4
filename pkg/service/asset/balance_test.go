package asset

import (
	"context"
	"fmt"
	"testing"

	tronscanapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/tronscan-api"
	zerionapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/zerion-api"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetTokenBalanceByWalletAddress(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	zerionapi.InitDefault()
	tronscanapi.InitDefault()
	ctx := context.Background()
	tests := []struct {
		name          string
		chainID       string
		walletAddress string
		tokenAddress  string
	}{
		{
			name:          "eth USDT",
			chainID:       "eth",
			walletAddress: "******************************************",
			tokenAddress:  "******************************************",
		},
		{
			name:          "eth USDC",
			chainID:       "eth",
			walletAddress: "******************************************",
			tokenAddress:  "******************************************",
		},
		{
			name:          "matic USDT",
			chainID:       "matic",
			walletAddress: "******************************************",
			tokenAddress:  "******************************************",
		},
		{
			name:          "matic USDC",
			chainID:       "matic",
			walletAddress: "******************************************",
			tokenAddress:  "******************************************",
		},
		{
			name:          "tron USDT",
			chainID:       "tron",
			walletAddress: "TDqSquXBgUCLYvYC4XZgrprLK589dkhSCf",
			tokenAddress:  "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
		},
		{
			name:          "tron USDC",
			chainID:       "tron",
			walletAddress: "TDqSquXBgUCLYvYC4XZgrprLK589dkhSCf",
			tokenAddress:  "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tokenBalance, err := GetTokenBalanceByWalletAddress(ctx, tt.chainID, tt.walletAddress, tt.tokenAddress)
			assert.Nil(t, err)
			fmt.Printf("name: %s, balance: %s\n", tt.name, tokenBalance.String())
		})
	}
}
