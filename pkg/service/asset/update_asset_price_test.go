package asset

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	coingeckoapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/coingecko-api"
	zerionapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/zerion-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/coingecko"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/eth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func getTimeFromCache(key string) (time.Time, error) {
	timestamp, err := cache.Get(key)
	if err != nil {
		return time.Time{}, err
	}
	// timestamp is RFC3339
	t, err := time.Parse(time.RFC3339, timestamp.(string))
	if err != nil {
		return time.Time{}, err
	}
	return t, nil
}

func setup(ctx context.Context, t *testing.T) {
	zerionapi.InitDefault()
	coingeckoapi.InitDefault(domain.NewAllPassRateLimiter())
	coingecko.Init()
	rdb.Reset()
	cache.FlushDb(ctx)

	assert.Nil(t, rdb.CreateSeedData())
	assert.Nil(t, dbtest.CreateTokenMetadata(rdb.Get()))
	coingecko.CacheTokenMetadataOnce = sync.Once{}
}

// TestUpdateAssetPricesNecessaryTokens makes sure
//
// 1. Necessary tokens are updated
func TestUpdateAssetPricesNecessaryTokens(t *testing.T) {
	ctx := context.Background()
	setup(ctx, t)

	err := UpdateAssetPrices(ctx, nil)
	assert.Nil(t, err)

	tokens := getNecessaryTokensToUpdate()
	chainIDs := lo.Map(tokens, func(t *rdb.TokenInfo, _ int) string {
		return t.ChainID
	})
	assetGroups := lo.Map(tokens, func(t *rdb.TokenInfo, _ int) string {
		return t.AssetGroup
	})

	prices, err := rdb.AssetPrices(ctx, chainIDs, assetGroups)
	assert.Nil(t, err)
	assert.Len(t, prices, len(tokens))

	priceHistories, err := rdb.AssetsPricesIn24H(ctx, chainIDs, assetGroups)
	assert.Nil(t, err)
	assert.Len(t, priceHistories, len(tokens))
}

// TestUpdateAssetPricesQueueMechanism makes sure
//
// 1. A new job won't be executed until ASSET_PRICE_UPDATE_JOB_BLANK_SEC is passed
func TestUpdateAssetPricesQueueMechanism(t *testing.T) {
	ctx := context.Background()
	setup(ctx, t)

	startTime := time.Now()

	addresses := []string{"******************************************"}
	err := AddUpdateAssetPricesJob(ctx, addresses)
	assert.Nil(t, err)

	lastAddedTime, err := getTimeFromCache(cache.AssetPriceUpdateJobQueueLastAddedKey)
	assert.Nil(t, err)
	assert.Less(t, lastAddedTime.Sub(startTime).Seconds(), float64(2))

	// Check the job queue. It should be length 1 at first
	len, err := cache.LLen(cache.AssetPriceUpdateJobQueueKey)
	assert.Nil(t, err)
	assert.Equal(t, int64(1), len)

	// Check queue until it's empty for 20 seconds at most
	jobPopped := false
	for i := 0; i < 10; i++ {
		err = CheckAndUpdateAssetPrices(ctx)
		assert.Nil(t, err)
		len, err = cache.LLen(cache.AssetPriceUpdateJobQueueKey)
		assert.Nil(t, err)
		if len == 0 {
			jobPopped = true
			break
		}
		time.Sleep(3 * time.Second)
	}
	assert.True(t, jobPopped)

	lastPoppedTime, err := getTimeFromCache(cache.AssetPriceUpdateJobQueueLastPoppedKey)
	assert.Nil(t, err)
	passedTime := lastPoppedTime.Sub(lastAddedTime)
	t.Logf("lastPoppedTime: %v, lastAddedTime: %v, passedTime: %v", lastPoppedTime, lastAddedTime, passedTime)
	assert.True(t, passedTime >= time.Duration(10)*time.Second, "passedTime: %v", passedTime)
}

// TestCheckAndUpdateAssetPrices makes sure
//
// 1. A job is popped from the queue and executed
// 2. Asset prices are updated correctly
func TestCheckAndUpdateAssetPrices(t *testing.T) {
	ctx := context.Background()
	setup(ctx, t)

	// Check preconditions
	// this is an asset of ******************************************
	assetPrices, err := rdb.AssetPrices(ctx, []string{"eth"}, []string{"******************************************"})
	assert.Nil(t, err)
	assert.Equal(t, 1, len(assetPrices))
	assert.Equal(t, "0.543", assetPrices[0].Price)
	// this is a necessary token to update
	assetPrices, err = rdb.AssetPrices(ctx, []string{"matic"}, []string{"******************************************"})
	assert.Nil(t, err)
	assert.Equal(t, 0, len(assetPrices))

	lastUpdatedTimeStr := cache.HGet(cache.AssetPriceLastUpdatedHKey, "the-sandbox")
	assert.Equal(t, "", lastUpdatedTimeStr)
	lastUpdatedTimeStr = cache.HGet(cache.AssetPriceLastUpdatedHKey, "revv")
	assert.Equal(t, "", lastUpdatedTimeStr)

	addresses := []string{"******************************************"}
	err = AddUpdateAssetPricesJob(ctx, addresses)
	assert.Nil(t, err)

	// Check the job queue. It should be length 1 at first
	queueLen, err := cache.LLen(cache.AssetPriceUpdateJobQueueKey)
	assert.Nil(t, err)
	assert.Equal(t, int64(1), queueLen)

	// wait until the job is popped, or 20 sec passed and fail
	jobPopped := false
	for i := 0; i < 10; i++ {
		err = CheckAndUpdateAssetPrices(ctx)
		assert.Nil(t, err)
		len, err := cache.LLen(cache.AssetPriceUpdateJobQueueKey)
		assert.Nil(t, err)
		if len == 0 {
			jobPopped = true
			break
		}
		time.Sleep(2 * time.Second)
	}
	assert.True(t, jobPopped)

	assetPrices, err = rdb.AssetPrices(ctx, []string{"eth"}, []string{"******************************************"})
	assert.Nil(t, err)
	assert.Equal(t, 1, len(assetPrices))
	assert.NotEqual(t, "0.543", assetPrices[0].Price)
}

func TestUpdateAssetPrices(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ZERION_API_KEY_V2"})

	ctx := context.Background()
	setup(ctx, t)

	eth.UpdateAssets(ctx, []string{"******************************************"}, true, dbmodel.AllAssetTypes, []string{"matic"}, true)
	assetParams := &rdb.AssetParams{
		AssetTypes:      []string{"token", "nft", "defi"},
		ChainIDs:        []string{"eth", "matic", "bsc", "arb"},
		WalletAddresses: []string{"******************************************"},
	}
	allAssets, errCode, err := rdb.Assets(context.Background(), assetParams, nil)
	assert.Nil(t, err)
	assert.True(t, errCode == 0)
	for _, asset := range allAssets {
		t.Logf("asset %s %s value: %+v", asset.ChainID, asset.Name, asset.UsdValue)
	}
	t.Logf("=================")

	UpdateAssetPrices(ctx, []string{"******************************************"})

	allAssets, errCode, err = rdb.Assets(context.Background(), assetParams, nil)
	assert.Nil(t, err)
	assert.True(t, errCode == 0)
	for _, asset := range allAssets {
		t.Logf("asset %s %s value: %+v", asset.ChainID, asset.Name, asset.UsdValue)
	}
}

func TestUpdateAssetsAndPrices(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ZERION_API_KEY_V2", "COINGECKO_API_KEY"})
	ctx := context.Background()

	rdb.Reset()
	rdb.Get().Create(&dbmodel.TokenMetadata{
		ChainID:         "arbitrum-one",
		ContractAddress: "******************************************",
		CoingeckoID:     util.Ptr("arbitrum-bridged-usdt-arbitrum"),
		Symbol:          util.Ptr("usdt"),
		Name:            util.Ptr("Arbitrum Bridged USDT (Arbitrum)"),
	})

	zerionapi.InitDefault()
	coingeckoapi.InitDefault(domain.NewAllPassRateLimiter())
	coingecko.Init()
	wallets := []string{"******************************************"}
	chainIDs := []string{"eth", "matic", "arb"}

	// force update token and defi
	assetTypes := []string{"token", "defi"}
	eth.UpdateAssets(ctx, wallets, true, assetTypes, chainIDs, true)

	// get assets
	allAssets, errCode, err := rdb.Assets(ctx, &rdb.AssetParams{
		AssetTypes:      []string{"defi"},
		ChainIDs:        []string{"arb"},
		WalletAddresses: wallets,
	}, nil)
	assert.Nil(t, err)
	assert.True(t, errCode == 0)
	t.Logf("assets (%d): %+v", len(allAssets), allAssets[0])

	// update asset price
	err = UpdateAssetPrices(ctx, wallets)
	assert.Nil(t, err)

	// get assets
	allAssets, errCode, err = rdb.Assets(ctx, &rdb.AssetParams{
		AssetTypes:      []string{"defi"},
		ChainIDs:        []string{"arb"},
		WalletAddresses: wallets,
	}, nil)
	assert.Nil(t, err)
	assert.True(t, errCode == 0)
	t.Logf("assets (%d): %+v", len(allAssets), allAssets[0])
	assert.Greater(t, allAssets[0].UsdValue, float64(0))
}

func TestUpdateAssetPricesDefiAsset(t *testing.T) {
	ctx := context.Background()
	setup(ctx, t)

	// Mock Coingecko service
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockCoingecko := coingecko.NewMockIService(ctrl)
	newUsdPrice := 0.1
	newArbPrice := 100.0
	mockCoingecko.EXPECT().QuotesInUSD(gomock.Any(), gomock.Any()).Return(map[string]float64{
		"tether-arb-usd": newUsdPrice,
		"arbitrum":       newArbPrice,
	}, nil).AnyTimes()
	coingecko.Set(mockCoingecko)

	// Create test defi asset
	defiAsset := dbtest.DefiArbitrumSimpleAsset
	assert.Equal(t, "defi", defiAsset.AssetType)
	assert.Nil(t, rdb.Get().Create(&defiAsset).Error)

	// Run UpdateAssetPrices
	err := UpdateAssetPrices(ctx, []string{defiAsset.WalletAddress})
	assert.Nil(t, err)

	// Check the updated defi asset
	updatedDefiAssets, errCode, err := rdb.Assets(ctx, &rdb.AssetParams{
		AssetTypes:      []string{defiAsset.AssetType},
		ChainIDs:        []string{defiAsset.ChainID},
		WalletAddresses: []string{defiAsset.WalletAddress},
	}, nil)
	assert.Nil(t, err)
	assert.Equal(t, 0, errCode)
	assert.GreaterOrEqual(t, len(updatedDefiAssets), 1)

	defiAssetFound := false
	for _, asset := range updatedDefiAssets {
		if asset.ChainID == defiAsset.ChainID && asset.AssetGroup == defiAsset.AssetGroup {
			defiAssetFound = true
			newUsdValue := 359.3233*newUsdPrice + 25.3096774255044*newArbPrice
			assert.InDelta(t, newUsdValue, asset.UsdValue, 0.0001)
		}
	}
	assert.True(t, defiAssetFound)
}
