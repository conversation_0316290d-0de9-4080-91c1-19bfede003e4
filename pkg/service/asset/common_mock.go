// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/service/asset (interfaces: IService)
//
// Generated by this command:
//
//	mockgen -package=asset -self_package=github.com/kryptogo/kg-wallet-backend/pkg/service/asset -destination=common_mock.go . IService
//

// Package asset is a generated GoMock package.
package asset

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	rdb "github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	gomock "go.uber.org/mock/gomock"
)

// MockIService is a mock of IService interface.
type MockIService struct {
	ctrl     *gomock.Controller
	recorder *MockIServiceMockRecorder
}

// MockIServiceMockRecorder is the mock recorder for MockIService.
type MockIServiceMockRecorder struct {
	mock *MockIService
}

// NewMockIService creates a new mock instance.
func NewMockIService(ctrl *gomock.Controller) *MockIService {
	mock := &MockIService{ctrl: ctrl}
	mock.recorder = &MockIServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIService) EXPECT() *MockIServiceMockRecorder {
	return m.recorder
}

// GetAssets mocks base method.
func (m *MockIService) GetAssets(arg0 context.Context, arg1 *GetAssetsParams) (*GetAssetsData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssets", arg0, arg1)
	ret0, _ := ret[0].(*GetAssetsData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetAssets indicates an expected call of GetAssets.
func (mr *MockIServiceMockRecorder) GetAssets(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssets", reflect.TypeOf((*MockIService)(nil).GetAssets), arg0, arg1)
}

// GetNfts mocks base method.
func (m *MockIService) GetNfts(arg0 context.Context, arg1 string, arg2 *GetNftsParams) (*GetNftsData, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNfts", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetNftsData)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// GetNfts indicates an expected call of GetNfts.
func (mr *MockIServiceMockRecorder) GetNfts(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNfts", reflect.TypeOf((*MockIService)(nil).GetNfts), arg0, arg1, arg2)
}

// SingleAsset mocks base method.
func (m *MockIService) SingleAsset(arg0 context.Context, arg1 *domain.UserData, arg2 *rdb.SingleAssetParams) (*rdb.VAsset, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SingleAsset", arg0, arg1, arg2)
	ret0, _ := ret[0].(*rdb.VAsset)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// SingleAsset indicates an expected call of SingleAsset.
func (mr *MockIServiceMockRecorder) SingleAsset(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SingleAsset", reflect.TypeOf((*MockIService)(nil).SingleAsset), arg0, arg1, arg2)
}

// TryToUpdateAssetsAndWait mocks base method.
func (m *MockIService) TryToUpdateAssetsAndWait(arg0 context.Context, arg1 *UpdateAssetsParam) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TryToUpdateAssetsAndWait", arg0, arg1)
}

// TryToUpdateAssetsAndWait indicates an expected call of TryToUpdateAssetsAndWait.
func (mr *MockIServiceMockRecorder) TryToUpdateAssetsAndWait(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryToUpdateAssetsAndWait", reflect.TypeOf((*MockIService)(nil).TryToUpdateAssetsAndWait), arg0, arg1)
}

// UpdateAssets mocks base method.
func (m *MockIService) UpdateAssets(arg0 context.Context, arg1 *UpdateAssetsParam) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateAssets", arg0, arg1)
}

// UpdateAssets indicates an expected call of UpdateAssets.
func (mr *MockIServiceMockRecorder) UpdateAssets(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAssets", reflect.TypeOf((*MockIService)(nil).UpdateAssets), arg0, arg1)
}
