package asset

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	rdb "github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/assert"
)

func TestAssetsPricesAndChangePercent(t *testing.T) {
	ctx := context.Background()

	rdb.Reset()
	err := dbtest.CreateAssetPrices(rdb.GetWith(ctx))
	assert.Nil(t, err)
	err = dbtest.CreateAssetPriceHistories(rdb.GetWith(ctx))
	assert.Nil(t, err)

	tokens := []*rdb.TokenInfo{
		{ChainID: "eth", AssetGroup: "******************************************"},
		{ChainID: "ronin", AssetGroup: "ronin"},
	}
	result, err := PricesAndChangePercent(context.Background(), tokens)
	assert.Nil(t, err)
	assert.Len(t, result, 2)
	assert.True(t, result[0].Price != 0.0)
	assert.True(t, result[0].ChangePercent != 0.0)
	assert.True(t, result[1].Price != 0.0)
	assert.True(t, result[1].ChangePercent != 0.0)

	key := cache.ComposeAssetPriceKey(tokens[0].ChainID, tokens[0].AssetGroup)
	_, err = getPriceFromCache(key)
	assert.Nil(t, err)
}
