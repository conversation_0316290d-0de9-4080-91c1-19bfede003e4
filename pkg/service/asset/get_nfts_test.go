package asset

import (
	"context"
	"net/http"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestGetNfts(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())
	users, uids := dbtest.Users()

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	data, kgErr := Get().GetNfts(context.Background(), uids[0], &GetNftsParams{
		NftParams: rdb.NftParams{
			UID:      "",
			Tagtype:  "ALL",
			ChainIDs: dbmodel.NftChainIDs,
			Path:     "",
			PagingParams: rdb.PagingParams{
				PageNumber: 1,
				PageSize:   10,
			},
			ExcludeObserver:   false,
			IncludeUnverified: true,
		},
	})

	assert.Nil(t, kgErr)
	assert.NotNil(t, data.Nfts)
	assert.Equal(t, 1, len(*data.Nfts))
	assert.Equal(t, "ethereum", (*data.Nfts)[0].ChainID)
	assert.Equal(t, "******************************************", (*data.Nfts)[0].ContractAddress)
	assert.Equal(t, "test-id", (*data.Nfts)[0].TokenID)
	assert.Equal(t, int64(1), data.Paging.TotalCount)
	assert.Equal(t, 1, data.Paging.PageNumber)
	assert.Equal(t, 10, data.Paging.PageSize)
}

func TestGetNftsInvalidPath(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())
	users, uids := dbtest.Users()

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	data, kgErr := Get().GetNfts(context.Background(), uids[0], &GetNftsParams{
		NftParams: rdb.NftParams{
			UID:      "",
			Tagtype:  "ALL",
			ChainIDs: dbmodel.NftChainIDs,
			Path:     "group-99",
			PagingParams: rdb.PagingParams{
				PageNumber: 1,
				PageSize:   10,
			},
			ExcludeObserver:   false,
			IncludeUnverified: true,
		},
	})

	assert.NotNil(t, kgErr)
	assert.Equal(t, code.ParamIncorrect, kgErr.Code)
	assert.Equal(t, http.StatusBadRequest, kgErr.HttpStatus)
	assert.Nil(t, data)
}
