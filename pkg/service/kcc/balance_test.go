package kcc

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestTokenList(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	assets, addrs := TokenList(context.Background(), []string{"******************************************"})
	assert.Len(t, addrs, 1)
	assert.NotEmpty(t, assets)
	assert.Len(t, assets, 5)
	t.Logf("assets[1]: %+v", assets[1])
}
