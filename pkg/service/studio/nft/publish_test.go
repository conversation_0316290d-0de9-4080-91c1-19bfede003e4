package nft

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/db"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/alert"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestPublishProject(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	srv := signing.SetupDeploySmartContract(t)
	defer func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}()

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateNftProjects(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	server.Init(rdb.GormRepo(), &alert.SlackAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	ctx := context.Background()
	organization := "KryptoGO"
	organizationID := 1
	chainID := "sepolia"
	randString := util.Ptr(util.RandString(10))
	projectID := 1

	signedUrl := uploadImageToGcs(t)
	params := &rdb.StudioNftProject{
		ProjectID:             projectID,
		Status:                "publish",
		CollectionImageURL:    util.Ptr(signedUrl),
		CollectionName:        randString,
		SymbolName:            randString,
		CollectionDescription: randString,
		MsgContent:            randString,
		BannerImageURL:        util.Ptr(signedUrl),
		ContractSchemaName:    util.Ptr(model.SchemaERC721),
		MaxSupply:             util.Ptr(5),
		StartTime:             util.Ptr(time.Now().Unix()),
		EndTime:               util.Ptr(time.Now().Unix()),
		Title:                 util.Ptr("title_before"),
		Subtitle:              util.Ptr("subtitle_before"),
	}

	publishProject(ctx, params, organization, chainID)
	time.Sleep(1 * time.Second)
	// check db
	project, err := rdb.GetStudioNftProjectByID(context.Background(), "KryptoGO", projectID)
	assert.Nil(t, err)
	assert.NotNil(t, project)
	assert.NotNil(t, project.PublishStatus)
	assert.Equal(t, "success", string(*project.PublishStatus))
	assert.NotNil(t, project.EventID)
	assert.Equal(t, GetEventID(*params.CollectionName), *project.EventID)

	// check the event is created
	event, errCode, err := rdb.GetAirdropEvent(context.Background(), *project.EventID, util.LocaleEnUS)
	assert.Nil(t, err)
	assert.Equal(t, 0, errCode)
	assert.Equal(t, "title_before", *event.Title)
	assert.Equal(t, "subtitle_before", *event.SubTitle)

	_, kgErr := UpsertProject(ctx, &UpsertProjectParams{
		ProjectID: projectID,
		Title:     util.Ptr("title_after"),
		Subtitle:  util.Ptr("subtitle_after"),
	}, organization, organizationID)
	assert.Nil(t, kgErr)
	event, errCode, err = rdb.GetAirdropEvent(context.Background(), *project.EventID, util.LocaleEnUS)
	assert.Nil(t, err)
	assert.Equal(t, 0, errCode)
	assert.Equal(t, "title_after", *event.Title)
	assert.Equal(t, "subtitle_after", *event.SubTitle)

}

func uploadImageToGcs(t *testing.T) string {
	filePath := "example.jpeg"
	fileName := "example.jpeg"

	object := "public/test/" + fileName

	signedUrl, err := db.GoogleStorageServiceObj.GenerateV4PutObjectSignedURL(object)
	assert.Nil(t, err)

	headers := map[string]string{
		"x-goog-acl": "public-read",
	}
	err = db.UploadFileBySignedUrl(signedUrl, filePath, fileName, headers)
	assert.Nil(t, err)

	return signedUrl
}
