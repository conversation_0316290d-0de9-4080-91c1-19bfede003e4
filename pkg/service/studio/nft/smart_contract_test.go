package nft

import (
	"context"
	"fmt"
	"math/big"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/api/signing"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/alert"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestLaunchNftErc721(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	srv := signing.SetupDeploySmartContract(t)
	defer func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}()

	projectID := 1
	organization := "KryptoGO"

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateNftProjects(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	server.Init(rdb.GormRepo(), &alert.SlackAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	ctx := context.Background()
	args := &client.KGEventArgument{
		ContractName:    "TestContract",
		Symbol:          "TCT",
		MaxSupplyErc721: big.NewInt(10000),
		BaseURI:         "https://test.com/",
		ContractURI:     "https://test.com/",
	}
	project, err := rdb.GetStudioNftProjectByID(context.Background(), organization, projectID)
	assert.Nil(t, err)

	// deploy contract
	contractAddress, txHash, kgError := DeployContract(ctx, project, args)
	assert.Nil(t, kgError)
	assert.NotEmpty(t, contractAddress)
	assert.NotEmpty(t, txHash)

	// verify contract
	time.Sleep(60 * time.Second) // wait tx to be confirmed
	project.ContractAddress = &contractAddress
	verified, kgError := VerifyContract(ctx, project, args)
	assert.Nil(t, kgError)
	assert.True(t, verified)

	assert.NotEmpty(t, contractAddress)
}

func TestLaunchNftErc1155(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	srv := signing.SetupDeploySmartContract(t)
	defer func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}()

	projectID := 1155
	organization := "KryptoGO"

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateNftProjects(rdb.Get()))

	ctx := context.Background()
	args := &client.KGEventArgument{
		ContractName:         "FAFA Watching U",
		Symbol:               "FAFAWATCH",
		MaxSupplyListErc1155: []*big.Int{big.NewInt(50)},
		MaxIDErc1155:         big.NewInt(1),
		BaseURI:              "https://wallet-static-dev.kryptogo.com/public/studio/nft/kryptogo/fafa-watching-u/meta/",
		ContractURI:          "https://wallet-static-dev.kryptogo.com/public/studio/nft/kryptogo/fafa-watching-u_contract.json",
	}
	project, err := rdb.GetStudioNftProjectByID(context.Background(), organization, projectID)
	assert.Nil(t, err)

	// deploy contract
	contractAddress, txHash, kgError := DeployContract(ctx, project, args)
	assert.Nil(t, kgError)
	assert.NotEmpty(t, contractAddress)
	assert.NotEmpty(t, txHash)

	// verify contract
	time.Sleep(60 * time.Second) // wait tx to be confirmed
	project.ContractAddress = &contractAddress
	verified, kgError := VerifyContract(ctx, project, args)
	assert.Nil(t, kgError)
	assert.True(t, verified)

	assert.NotEmpty(t, contractAddress)
}

func TestVerifyContractErc721(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	contractAddress := "******************************************"
	projectID := 1
	organization := "KryptoGO"

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateNftProjects(rdb.Get()))

	ctx := context.Background()
	args := &client.KGEventArgument{
		ContractName:    "TestContract",
		Symbol:          "TCT",
		MaxSupplyErc721: big.NewInt(10000),
		BaseURI:         "https://test.com/",
		ContractURI:     "https://test.com/",
	}
	project, err := rdb.GetStudioNftProjectByID(context.Background(), organization, projectID)
	assert.Nil(t, err)

	project.ContractAddress = &contractAddress
	verified, kgError := VerifyContract(ctx, project, args)
	assert.Nil(t, kgError)
	assert.True(t, verified)
}

func TestVerifyContractErc1155(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	contractAddress := "******************************************"
	projectID := 1155
	organization := "KryptoGO"

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateNftProjects(rdb.Get()))

	ctx := context.Background()
	args := &client.KGEventArgument{
		ContractName:         "FAFA Watching U",
		Symbol:               "FAFAWATCH",
		MaxSupplyListErc1155: []*big.Int{big.NewInt(50)},
		MaxIDErc1155:         big.NewInt(1),
		BaseURI:              "https://wallet-static-dev.kryptogo.com/public/studio/nft/kryptogo/fafa-watching-u/meta/",
		ContractURI:          "https://wallet-static-dev.kryptogo.com/public/studio/nft/kryptogo/fafa-watching-u_contract.json",
	}
	project, err := rdb.GetStudioNftProjectByID(context.Background(), organization, projectID)
	assert.Nil(t, err)
	project.ChainID = util.Ptr("matic")
	project.ContractAddress = &contractAddress
	verified, kgError := VerifyContract(ctx, project, args)
	assert.Nil(t, kgError)
	assert.True(t, verified)
}
