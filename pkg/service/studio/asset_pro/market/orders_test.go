package market

import (
	"context"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
)

func TestValidateOrderCost(t *testing.T) {
	ctx := context.Background()
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateAssetProOrdersDefault(rdb.Get()))
	Init(InitParam{
		AssetProProductRepo: rdb.GormRepo(),
	})
	product, kgErr := productRepo.GetProductByID(ctx, 1)
	assert.Nil(t, kgErr)
	params := &domain.CreateAssetProOrderParams{
		Amount:                    decimal.NewFromFloat(66.666666),
		Price:                     decimal.NewFromFloat(3),
		TotalCost:                 decimal.NewFromFloat(210),
		FeeType:                   domain.AssetProProductFeeTypeFeeIncluded,
		ProportionalFeePercentage: decimal.NewFromFloat(0.5),
		ProportionalMinimumFee:    decimal.NewFromFloat(10),
	}

	kgErr = validateOrderCost(ctx, product, params)
	assert.Nil(t, kgErr)

	params.TotalCost = decimal.NewFromFloat(3)
	kgErr = validateOrderCost(ctx, product, params)
	assert.NotNil(t, kgErr)
	assert.Equal(t, code.ParamIncorrect, kgErr.Code)
}

func TestCalculateFeeInQuoteCurrency(t *testing.T) {
	ctx := context.Background()

	// fee type is fee_included, and proportionalFeePercentage * amount is greater than proportionalMinimumFee
	{
		cost := decimal.NewFromFloat(3100)
		feeType := domain.AssetProProductFeeTypeFeeIncluded
		proportionalFeePercentage := decimal.NewFromFloat(1)
		proportionalMinimumFee := decimal.NewFromFloat(20)

		fee, err := calculateFeeInQuoteCurrency(ctx, cost, feeType, proportionalFeePercentage, proportionalMinimumFee)
		assert.True(t, decimal.NewFromFloat(31).Equal(fee))
		assert.Nil(t, err)
	}

	// fee type is fee_included, and proportionalMinimumFee  is greater than proportionalFeePercentage * amount
	{
		cost := decimal.NewFromFloat(3100)
		feeType := domain.AssetProProductFeeTypeFeeIncluded
		proportionalFeePercentage := decimal.NewFromFloat(1)
		proportionalMinimumFee := decimal.NewFromFloat(100)

		fee, err := calculateFeeInQuoteCurrency(ctx, cost, feeType, proportionalFeePercentage, proportionalMinimumFee)
		assert.True(t, decimal.NewFromFloat(100).Equal(fee))
		assert.Nil(t, err)
	}

	// fee type is no_fee
	{
		cost := decimal.NewFromFloat(3100)
		feeType := domain.AssetProProductFeeTypeNoFee
		proportionalFeePercentage := decimal.NewFromFloat(1)
		proportionalMinimumFee := decimal.NewFromFloat(100)

		fee, err := calculateFeeInQuoteCurrency(ctx, cost, feeType, proportionalFeePercentage, proportionalMinimumFee)
		assert.True(t, decimal.NewFromFloat(0).Equal(fee))
		assert.Nil(t, err)
	}
}
