package assetpro

import (
	"context"
	"testing"

	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"

	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/sendgrid/rest"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCheckAssetProAlertThreshold(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	m := sendgrid.NewMockEmailClient(ctrl)
	m.EXPECT().SendEmailWithSubject(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Times(2).Return(&rest.Response{StatusCode: 200}, nil)
	tronMock := tron.NewMockClient(ctrl)
	tronMock.EXPECT().GetTrxBalance("TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7").Return(int64(10), nil)

	rdb.Reset()
	InitFinance(InitParam{
		AssetProFinanceRepo: rdb.GormRepo(),
		StudioOrgRepo:       rdb.GormRepo(),
		EmailSender:         m,
	})
	tron.Init(tron.InitParams{
		TronClient:   tronMock,
		ShastaClient: tronMock,
	})

	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	assert.Nil(t, rdbtest.CreateAssetProLiquidities(rdb.Get()))

	// check alert threshold
	kgErr := CheckAssetProAlertThreshold(ctx)
	assert.Nil(t, kgErr)
}
