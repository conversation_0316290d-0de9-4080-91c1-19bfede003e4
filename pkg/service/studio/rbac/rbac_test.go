package rbac_test

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/rbac"
	"github.com/stretchr/testify/suite"
)

type rbacTestSuite struct {
	ctx context.Context
	suite.Suite
}

func (s *rbacTestSuite) SetupTest() {
	s.ctx = context.Background()
	s.NoError(rbac.Init(s.ctx))
}

func (s *rbacTestSuite) TestIsGrantedWithIncompleteParam() {
	// with empty request
	s.False(rbac.RBACService.IsGranted(s.ctx, rbac.IsGrantRequest{}))

	// without action and resource
	s.False(rbac.RBACService.IsGranted(s.ctx, rbac.IsGrantRequest{
		Roles: []rbac.Role{rbac.RoleOwner},
	}))

	// without resource
	s.False(rbac.RBACService.IsGranted(s.ctx, rbac.IsGrantRequest{
		Action:   rbac.ActionExport,
		Resource: rbac.ResourceAssetPoolPrivateKey,
	}))
}

func (s *rbacTestSuite) TestIsGrantedSingleRole() {
	s.False(rbac.RBACService.IsGranted(s.ctx, rbac.IsGrantRequest{
		Roles:    []rbac.Role{rbac.RoleAssetProAdmin},
		Action:   rbac.ActionExport,
		Resource: rbac.ResourceAssetPoolPrivateKey,
	}))

	s.False(rbac.RBACService.IsGranted(s.ctx, rbac.IsGrantRequest{
		Roles:    []rbac.Role{rbac.RoleNFTBoostAdmin},
		Action:   rbac.ActionExport,
		Resource: rbac.ResourceAssetPoolPrivateKey,
	}))

	s.True(rbac.RBACService.IsGranted(s.ctx, rbac.IsGrantRequest{
		Roles:    []rbac.Role{rbac.RoleOwner},
		Action:   rbac.ActionExport,
		Resource: rbac.ResourceAssetPoolPrivateKey,
	}))
}

func (s *rbacTestSuite) TestIsGrantedMultipleRole() {
	s.False(rbac.RBACService.IsGranted(s.ctx, rbac.IsGrantRequest{
		Roles: []rbac.Role{
			rbac.RoleNFTBoostAdmin,
			rbac.RoleUser360Admin,
			rbac.RoleComplianceAdmin,
		},
		Action:   rbac.ActionRead,
		Resource: rbac.ResourceAssetPool,
	}))

	s.True(rbac.RBACService.IsGranted(s.ctx, rbac.IsGrantRequest{
		Roles: []rbac.Role{
			rbac.RoleAssetProAdmin,
			rbac.RoleNFTBoostAdmin,
			rbac.RoleUser360Admin,
			rbac.RoleComplianceAdmin,
		},
		Action:   rbac.ActionRead,
		Resource: rbac.ResourceAssetPool,
	}))
}

func (s *rbacTestSuite) TestGetGrants() {
	{
		grants, err := rbac.RBACService.GetGrants(s.ctx,
			rbac.RoleComplianceAdmin,
		)
		s.NoError(err)
		s.Len(grants, 3)
	}
	{
		grants, err := rbac.RBACService.GetGrants(s.ctx,
			rbac.RoleWalletBuilderAdmin,
		)
		s.NoError(err)
		s.Len(grants, 5)
	}
	{ // case: unique grants when multiple roles
		grantsOwner, err := rbac.RBACService.GetGrants(s.ctx,
			rbac.RoleOwner,
		)
		s.NoError(err)

		grantsMultiRoles, err := rbac.RBACService.GetGrants(s.ctx,
			rbac.RoleAssetProAdmin,
			rbac.RoleOwner,
		)
		s.NoError(err)
		s.Equal(len(grantsOwner), len(grantsMultiRoles))
	}
}

func TestRbacSuite(t *testing.T) {
	suite.Run(t, new(rbacTestSuite))
}
