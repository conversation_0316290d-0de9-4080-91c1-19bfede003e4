package organization_test

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization/stub"

	"github.com/stretchr/testify/assert"
)

func TestGetEnabledModules(t *testing.T) {
	s := assert.New(t)
	c := context.Background()
	repo := stub.NewInMemoryStudioOrgRepo()

	organization.Init(organization.InitParam{StudioOrgRepo: repo})

	// no org
	_, kgErr := organization.GetOrgEnabledModules(c, 1)
	s.NotNil(kgErr)
	s.Equal(7009, kgErr.Code)
	s.Equal(404, kgErr.HttpStatus)

	// create org's module
	err := organization.SaveOrgEnabledModules(c, 1, &domain.StudioOrganizationModule{
		AssetPro: []domain.AssetPro{
			domain.AssetProTreasury,
			domain.AssetProSendToken,
		},
	})
	s.NoError(err)

	// get org
	modules, kgErr := organization.GetOrgEnabledModules(c, 1)
	s.Nil(kgErr)

	s.Equal(0, len(modules.User360))
	s.Equal(0, len(modules.WalletBuilder))
	s.Equal(2, len(modules.AssetPro))
	s.Equal(0, len(modules.NFTBoost))
	s.Equal(0, len(modules.Compliance))
}

func TestIsUserInOrganization(t *testing.T) {
	s := assert.New(t)
	c := context.Background()
	uid := "test-user"
	notExistUID := "non-exist-user"
	rdb.Reset()
	existUserCacheKey := cache.ComposeUserOrganizationsCacheKey(uid)
	notExistUserCacheKey := cache.ComposeUserOrganizationsCacheKey(notExistUID)
	cache.Del(existUserCacheKey)
	s.NoError(dbtest.CreateStudioOrganizations(rdb.Get()))
	s.NoError(dbtest.CreateStudioUsers(rdb.Get(), uid, nil))
	organization.Init(organization.InitParam{StudioOrgRepo: rdb.GormRepo()})

	val, err := cache.Get(existUserCacheKey)
	s.Equal(cache.ErrorRedisNil, err.Error())
	s.Equal("", val.(string))

	// in org, cache missed
	exists, kgErr := organization.IsUserInOrganization(c, 1, uid)
	s.Nil(kgErr)
	s.True(exists)
	val, err = cache.SMembers(existUserCacheKey)
	s.NoError(err)
	s.Equal([]string{"1", "2"}, val)

	// in org, cache hit
	exists, kgErr = organization.IsUserInOrganization(c, 1, uid)
	s.Nil(kgErr)
	s.True(exists)
	val, err = cache.SMembers(existUserCacheKey)
	s.NoError(err)
	s.Equal([]string{"1", "2"}, val)

	// not existing user
	exists, kgErr = organization.IsUserInOrganization(c, 1, notExistUID)
	s.Nil(kgErr)
	s.False(exists)
	val, err = cache.SMembers(notExistUserCacheKey)
	s.NoError(err)
	s.Equal([]string{"-1"}, val)

	// not in org
	exists, kgErr = organization.IsUserInOrganization(c, 3, uid)
	s.Nil(kgErr)
	s.False(exists)
	val, err = cache.SMembers(existUserCacheKey)
	s.NoError(err)
	s.Equal([]string{"1", "2"}, val)
}
