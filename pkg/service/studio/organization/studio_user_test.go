package organization_test

import (
	"context"
	"math/rand"
	"testing"

	"github.com/sendgrid/rest"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"go.uber.org/mock/gomock"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/application"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/service/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/firebase"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/oauth"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/studio/organization/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/kryptogo/kg-wallet-backend/repo"
	"github.com/kryptogo/kg-wallet-backend/service/user"
)

func TestInviteStudioUserWhenRoleHasDisabledModule(t *testing.T) {
	ctx := context.Background()

	repo := stub.NewInMemoryStudioOrgRepo()
	roleRepo := stub.NewInMemoryStudioRoleRepo()

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	organization.Init(organization.InitParam{
		StudioOrgRepo:  repo,
		StudioRoleRepo: roleRepo,
	})

	adminName := util.RandString(20)

	// create studio org
	orgID, kgError := repo.CreateOrganization(ctx, domain.StudioOrganizationConfig{
		OrgName:                  util.Ptr(util.RandString(6)),
		ComplianceOrganizationID: util.Ptr(rand.Intn(100000)),
	})
	assert.Nil(t, kgError)

	// create modules
	assert.NoError(t, repo.SaveOrgEnabledModules(ctx, orgID, &domain.StudioOrganizationModule{
		User360: []domain.User360{"xxx"},
	}))

	// create studio user
	adminUID, _ := createUserWithRandomEmail(t)
	adminEmail := util.RandEmail()
	kgError = repo.CreateStudioAdmin(ctx, orgID, adminUID, adminName, adminEmail)
	assert.Nil(t, kgError)

	//	create kg user
	_, inviteeEmail := createUserWithRandomEmail(t)
	inviteeName := util.RandString(6)
	inviteStudioUserParams := organization.InviteStudioUserParams{
		OrgID:      orgID,
		InviterUID: adminUID,
		Roles: []domain.StudioRole{
			{Module: "user_360", Name: "admin"},
			{Module: "asset_pro", Name: "trader"},
			{Module: "compliance", Name: "admin"},
		},
		InviteeEmail:    inviteeEmail,
		InviteeName:     inviteeName,
		InviteeMemberID: nil,
	}

	kgError = organization.InviteStudioUser(ctx, inviteStudioUserParams)
	assert.NotNil(t, kgError)
	assert.Equal(t, code.InvalidRoles, kgError.Code)
}

func TestInviteStudioUser(t *testing.T) {
	ctx := context.Background()

	repo := stub.NewInMemoryStudioOrgRepo()
	roleRepo := stub.NewInMemoryStudioRoleRepo()
	cacheRoleRepo := cache.NewRedisStudioRoleCacheRepo(cache.Client)

	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	ctrl := gomock.NewController(t)
	sendgridClient := sendgrid.NewMockEmailClient(ctrl)
	sendgridClient.EXPECT().SendEmailWithSubject(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&rest.Response{StatusCode: 200}, nil)

	organization.Init(organization.InitParam{
		StudioOrgRepo:       repo,
		StudioRoleRepo:      roleRepo,
		StudioRoleCacheRepo: cacheRoleRepo,
		SendgridClient:      sendgridClient,
	})

	adminName := util.RandString(20)

	// create studio org
	orgID, kgError := repo.CreateOrganization(ctx, domain.StudioOrganizationConfig{
		OrgName:                  util.Ptr(util.RandString(6)),
		ComplianceOrganizationID: util.Ptr(rand.Intn(100000)),
	})
	assert.Nil(t, kgError)

	assert.NoError(t, repo.SaveOrgEnabledModules(ctx, orgID, &domain.StudioOrganizationModule{
		User360:    []domain.User360{domain.User360Data},
		AssetPro:   []domain.AssetPro{domain.AssetProSendToken},
		Compliance: []domain.Compliance{domain.ComplianceAllTasks},
	}))

	// create studio user
	adminUID, _ := createUserWithRandomEmail(t)
	adminEmail := util.RandEmail()
	kgError = repo.CreateStudioAdmin(ctx, orgID, adminUID, adminName, adminEmail)
	assert.Nil(t, kgError)

	//	create kg user
	inviteeUID, inviteeEmail := createUserWithRandomEmail(t)
	inviteeName := util.RandString(6)
	inviteStudioUserParams := organization.InviteStudioUserParams{
		OrgID:      orgID,
		InviterUID: adminUID,
		Roles: []domain.StudioRole{
			{Module: "user_360", Name: "admin"},
			{Module: "asset_pro", Name: "trader"},
			{Module: "compliance", Name: "admin"},
		},
		InviteeEmail:    inviteeEmail,
		InviteeName:     inviteeName,
		InviteeMemberID: nil,
	}
	kgError = organization.InviteStudioUser(ctx, inviteStudioUserParams)
	assert.Nil(t, kgError)

	// check user status and data
	studioUser, kgError := repo.GetStudioUser(ctx, orgID, inviteeUID)
	assert.Nil(t, kgError)
	t.Log(studioUser)
	assert.Equal(t, domain.StudioUserStatusPending.String(), studioUser.Status.String())
	assert.Equal(t, inviteeName, studioUser.Name)
	assert.Nil(t, studioUser.MemberID)
	assert.Equal(t, inviteeEmail, studioUser.Email)

	// check user roles
	roleBindings, kgError := roleRepo.GetRoleBindings(ctx, domain.GetRoleBindingsRequest{
		OrgID: orgID,
		UID:   inviteeUID,
	})
	assert.Nil(t, kgError)

	assert.Contains(t, roleBindings, domain.StudioRoleBinding{
		OrganizationID: orgID,
		UID:            inviteeUID,
		StudioRole:     domain.StudioRole{Module: "user_360", Name: "admin"},
	})
	assert.Contains(t, roleBindings, domain.StudioRoleBinding{
		OrganizationID: orgID,
		UID:            inviteeUID,
		StudioRole:     domain.StudioRole{Module: "asset_pro", Name: "trader"},
	})
	assert.Contains(t, roleBindings, domain.StudioRoleBinding{
		OrganizationID: orgID,
		UID:            inviteeUID,
		StudioRole:     domain.StudioRole{Module: "compliance", Name: "admin"},
	})

	rolesInCache, kgError := cacheRoleRepo.GetRolesByUser(ctx, orgID, inviteeUID)
	assert.Nil(t, kgError)
	assert.Len(t, rolesInCache, 3)

	assert.Contains(t, rolesInCache, domain.StudioRole{Module: "user_360", Name: "admin"})
	assert.Contains(t, rolesInCache, domain.StudioRole{Module: "asset_pro", Name: "trader"})
	assert.Contains(t, rolesInCache, domain.StudioRole{Module: "compliance", Name: "admin"})
}

func TestAcceptInvitation(t *testing.T) {
	ctx := context.Background()

	rdb.Reset()

	repo := stub.NewInMemoryStudioOrgRepo()
	roleRepo := stub.NewInMemoryStudioRoleRepo()
	cacheRoleRepo := cache.NewRedisStudioRoleCacheRepo(cache.Client)
	ctrl := gomock.NewController(t)
	sendgridClient := sendgrid.NewMockEmailClient(ctrl)
	sendgridClient.EXPECT().SendEmailWithSubject(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&rest.Response{StatusCode: 200}, nil)

	organization.Init(organization.InitParam{
		StudioOrgRepo:       repo,
		StudioRoleRepo:      roleRepo,
		StudioRoleCacheRepo: cacheRoleRepo,
		SendgridClient:      sendgridClient,
	})

	assert.Nil(t, rdbtest.CreateStudioOrganizations(rdb.Get()))
	assert.Nil(t, rdbtest.CreateOAuthClientConfigs(rdb.Get()))
	assert.Nil(t, rdbtest.CreateStudioOrganizationClients(rdb.Get()))
	oauth.Init(rdb.GormRepo())
	application.Init(rdb.GormRepo())

	// create studio org
	orgID, kgError := repo.CreateOrganization(ctx, domain.StudioOrganizationConfig{
		OrgName:                  util.Ptr(util.RandString(6)),
		ComplianceOrganizationID: util.Ptr(rand.Intn(100000)),
	})
	assert.Nil(t, kgError)

	assert.NoError(t, repo.SaveOrgEnabledModules(ctx, orgID, &domain.StudioOrganizationModule{
		User360:    []domain.User360{domain.User360Data},
		AssetPro:   []domain.AssetPro{domain.AssetProSendToken},
		Compliance: []domain.Compliance{domain.ComplianceAllTasks},
	}))

	assert.NoError(t, repo.SaveOrgEnabledModules(ctx, orgID, &domain.StudioOrganizationModule{
		User360:    []domain.User360{domain.User360Data},
		AssetPro:   []domain.AssetPro{domain.AssetProSendToken},
		Compliance: []domain.Compliance{domain.ComplianceAllTasks},
	}))

	// create modules
	assert.NoError(t, repo.SaveOrgEnabledModules(ctx, orgID, &domain.StudioOrganizationModule{
		User360:    []domain.User360{"xxx"},
		AssetPro:   []domain.AssetPro{"xxx"},
		Compliance: []domain.Compliance{"xxx"},
	}))

	// create kg user
	inviteeUID, inviteeEmail := createUserWithRandomEmail(t)
	inviteeName := util.RandString(6)
	inviteStudioUserParams := organization.InviteStudioUserParams{
		OrgID:      orgID,
		InviterUID: inviteeUID,
		Roles: []domain.StudioRole{
			{Module: "user_360", Name: "admin"},
			{Module: "asset_pro", Name: "trader"},
			{Module: "compliance", Name: "admin"},
		},
		InviteeEmail:    inviteeEmail,
		InviteeName:     inviteeName,
		InviteeMemberID: nil,
	}
	kgError = organization.InviteStudioUser(ctx, inviteStudioUserParams)
	assert.Nil(t, kgError)

	data, kgError := organization.AcceptInvitation(ctx, orgID, inviteeUID)
	assert.Nil(t, kgError)
	assert.NotNil(t, data)

	// check user status and data
	studioUser, kgError := repo.GetStudioUser(ctx, orgID, inviteeUID)
	assert.Nil(t, kgError)

	assert.Equal(t, domain.StudioUserStatusActive, studioUser.Status)
	assert.Equal(t, inviteeName, studioUser.Name)
	assert.Nil(t, studioUser.MemberID)
	assert.Equal(t, inviteeEmail, studioUser.Email)

	// check user roles
	roleBindings, kgError := roleRepo.GetRoleBindings(ctx, domain.GetRoleBindingsRequest{
		OrgID: orgID,
		UID:   inviteeUID,
	})
	assert.Nil(t, kgError)

	assert.Contains(t, roleBindings, domain.StudioRoleBinding{
		OrganizationID: orgID,
		UID:            inviteeUID,
		StudioRole:     domain.StudioRole{Module: "user_360", Name: "admin"},
	})
	assert.Contains(t, roleBindings, domain.StudioRoleBinding{
		OrganizationID: orgID,
		UID:            inviteeUID,
		StudioRole:     domain.StudioRole{Module: "asset_pro", Name: "trader"},
	})
	assert.Contains(t, roleBindings, domain.StudioRoleBinding{
		OrganizationID: orgID,
		UID:            inviteeUID,
		StudioRole:     domain.StudioRole{Module: "compliance", Name: "admin"},
	})

	rolesInCache, kgError := cacheRoleRepo.GetRolesByUser(ctx, orgID, inviteeUID)
	assert.Nil(t, kgError)
	assert.Len(t, rolesInCache, 3)

	assert.Contains(t, rolesInCache, domain.StudioRole{Module: "user_360", Name: "admin"})
	assert.Contains(t, rolesInCache, domain.StudioRole{Module: "asset_pro", Name: "trader"})
	assert.Contains(t, rolesInCache, domain.StudioRole{Module: "compliance", Name: "admin"})
}

func createUserWithRandomEmail(t *testing.T) (uid string, email string) {
	users, uid, _, email := dbtest.User()

	user.Init(repo.Unified())

	_ = rdb.GormRepo().BatchSetUsers(context.Background(), users)

	_, err := firebase.BatchCreateUsersBySeed(users)
	assert.Nil(t, err)
	return
}

type studioUserSuite struct {
	suite.Suite
	ctx context.Context
}

func (s *studioUserSuite) SetupSuite() {
	s.ctx = context.Background()
}

func (s *studioUserSuite) TestUpdateStudioUserWithoutOwnerBugOrgHaveNotOwner() {
	repo := stub.NewInMemoryStudioOrgRepo()
	roleRepo := stub.NewInMemoryStudioRoleRepo()
	organization.Init(organization.InitParam{
		StudioOrgRepo:  repo,
		StudioRoleRepo: roleRepo,
	})

	var (
		orgID    int
		uid      = util.RandString(6)
		kgError  *code.KGError
		name     = util.RandString(6)
		memberID = util.RandString(6)
	)

	{ // prepare date
		orgID, kgError = repo.CreateOrganization(s.ctx, domain.StudioOrganizationConfig{
			OrgName:                  util.Ptr(util.RandString(6)),
			ComplianceOrganizationID: util.Ptr(rand.Intn(100000)),
		})
		s.Nil(kgError)
		params := domain.SaveStudioUserDataParams{
			OrganizationID: orgID,
			UID:            uid,
			Name:           util.Ptr(name),
			MemberID:       util.Ptr(memberID),
			Email:          util.Ptr(util.RandEmail()),
		}

		s.Nil(repo.CreateStudioUser(s.ctx, params))

		s.Nil(roleRepo.SaveUserRoles(s.ctx, domain.SaveUserRolesRequest{
			OrgID: orgID,
			UID:   uid,
			Roles: []domain.StudioRole{
				{
					Name: "owner",
				},
			},
		}))
	}

	kgError = organization.UpdateStudioUser(s.ctx, organization.UpdateStudioUserParams{
		OrgID:    orgID,
		UID:      uid,
		Name:     util.Ptr(name),
		MemberID: util.Ptr(memberID),
		Roles: []domain.StudioRole{
			{
				Module: "asset_pro",
				Name:   "admin",
			},
		},
	})

	s.NotNil(kgError)

	actualRoleBindings, _ := roleRepo.GetRoleBindings(s.ctx, domain.GetRoleBindingsRequest{
		OrgID: orgID,
		UID:   uid,
	})
	s.Len(actualRoleBindings, 1)

	actualStudioRole := make([]domain.StudioRole, 0, len(actualRoleBindings))
	for _, roleBinding := range actualRoleBindings {
		actualStudioRole = append(actualStudioRole, roleBinding.StudioRole)
	}

	s.Contains(actualStudioRole, domain.StudioRole{
		Module: "",
		Name:   "owner",
	})
}

func (s *studioUserSuite) TestUpdateStudioUserWithoutOwnerAndOrgHaveOwner() {
	repo := stub.NewInMemoryStudioOrgRepo()
	roleRepo := stub.NewInMemoryStudioRoleRepo()
	cacheRoleRepo := cache.NewRedisStudioRoleCacheRepo(cache.Client)
	organization.Init(organization.InitParam{
		StudioOrgRepo:       repo,
		StudioRoleRepo:      roleRepo,
		StudioRoleCacheRepo: cacheRoleRepo,
	})

	var (
		orgID    int
		uid      = util.RandString(6)
		kgError  *code.KGError
		name     = util.RandString(6)
		memberID = util.RandString(6)
	)

	{ // prepare date
		orgID, kgError = repo.CreateOrganization(s.ctx, domain.StudioOrganizationConfig{
			OrgName:                  util.Ptr(util.RandString(6)),
			ComplianceOrganizationID: util.Ptr(rand.Intn(100000)),
		})
		s.Nil(kgError)

		s.NoError(repo.SaveOrgEnabledModules(s.ctx, orgID, &domain.StudioOrganizationModule{
			AssetPro:   []domain.AssetPro{domain.AssetProSendToken},
			Compliance: []domain.Compliance{domain.ComplianceAllTasks},
		}))

		params := domain.SaveStudioUserDataParams{
			OrganizationID: orgID,
			UID:            uid,
			Name:           util.Ptr(name),
			MemberID:       util.Ptr(memberID),
			Email:          util.Ptr(util.RandEmail()),
		}

		s.Nil(repo.CreateStudioUser(s.ctx, params))

		s.Nil(roleRepo.SaveUserRoles(s.ctx, domain.SaveUserRolesRequest{
			OrgID: orgID,
			UID:   uid,
			Roles: []domain.StudioRole{
				{
					Name: "owner",
				},
			},
		}))

		uid2 := uid + "-1"

		// another user
		params = domain.SaveStudioUserDataParams{
			OrganizationID: orgID,
			UID:            uid2,
			Name:           util.Ptr(name),
			MemberID:       util.Ptr(memberID),
			Email:          util.Ptr(util.RandEmail()),
		}
		s.Nil(repo.CreateStudioUser(s.ctx, params))
		s.Nil(roleRepo.SaveUserRoles(s.ctx, domain.SaveUserRolesRequest{
			OrgID: orgID,
			UID:   uid2,
			Roles: []domain.StudioRole{
				{
					Name: "owner",
				},
			},
		}))
	}

	expectedRoles := []domain.StudioRole{
		{
			Module: "asset_pro",
			Name:   "admin",
		},
		{
			Module: "compliance",
			Name:   "admin",
		},
	}

	kgError = organization.UpdateStudioUser(s.ctx, organization.UpdateStudioUserParams{
		OrgID:    orgID,
		UID:      uid,
		Name:     util.Ptr(name),
		MemberID: util.Ptr(memberID),
		Roles:    expectedRoles,
	})
	s.Nil(kgError)

	studioUser, kgError := repo.GetStudioUser(s.ctx, orgID, uid)
	s.Nil(kgError)
	s.Equal(name, studioUser.Name)
	s.Equal(memberID, *studioUser.MemberID)

	roleBindings, kgError := roleRepo.GetRoleBindings(s.ctx, domain.GetRoleBindingsRequest{
		OrgID: orgID,
		UID:   uid,
	})
	s.Nil(kgError)
	s.Equal(2, len(roleBindings))

	actualStudioRole := make([]domain.StudioRole, 0, len(roleBindings))
	for _, roleBinding := range roleBindings {
		actualStudioRole = append(actualStudioRole, roleBinding.StudioRole)
	}

	for _, expectedRole := range expectedRoles {
		s.Contains(actualStudioRole, expectedRole)
	}
	rolesInCache, kgError := cacheRoleRepo.GetRolesByUser(s.ctx, orgID, uid)
	s.Nil(kgError)
	s.Equal(2, len(rolesInCache))

	for _, expectedRole := range expectedRoles {
		s.Contains(rolesInCache, expectedRole)
	}
}
func (s *studioUserSuite) TestUpdateStudioUserOwner() {
	repo := stub.NewInMemoryStudioOrgRepo()
	roleRepo := stub.NewInMemoryStudioRoleRepo()
	cacheRoleRepo := cache.NewRedisStudioRoleCacheRepo(cache.Client)
	organization.Init(organization.InitParam{
		StudioOrgRepo:       repo,
		StudioRoleRepo:      roleRepo,
		StudioRoleCacheRepo: cacheRoleRepo,
	})

	var (
		orgID    int
		uid      = util.RandString(6)
		kgError  *code.KGError
		name     = util.RandString(6)
		memberID = util.RandString(6)
	)

	{ // prepare date
		orgID, kgError = repo.CreateOrganization(s.ctx, domain.StudioOrganizationConfig{
			OrgName:                  util.Ptr(util.RandString(6)),
			ComplianceOrganizationID: util.Ptr(rand.Intn(100000)),
		})
		s.Nil(kgError)

		s.NoError(repo.SaveOrgEnabledModules(s.ctx, orgID, &domain.StudioOrganizationModule{
			AssetPro:   []domain.AssetPro{domain.AssetProSendToken},
			Compliance: []domain.Compliance{domain.ComplianceAllTasks},
		}))

		params := domain.SaveStudioUserDataParams{
			OrganizationID: orgID,
			UID:            uid,
			Name:           util.Ptr(name),
			MemberID:       util.Ptr(memberID),
			Email:          util.Ptr(util.RandEmail()),
		}
		s.Nil(repo.CreateStudioUser(s.ctx, params))

		s.Nil(roleRepo.SaveUserRoles(s.ctx, domain.SaveUserRolesRequest{
			OrgID: orgID,
			UID:   uid,
			Roles: []domain.StudioRole{
				{
					Name: "owner",
				},
			},
		}))

		uid2 := uid + "-1"

		// another user
		params = domain.SaveStudioUserDataParams{
			OrganizationID: orgID,
			UID:            uid2,
			Name:           util.Ptr(name),
			MemberID:       util.Ptr(memberID),
			Email:          util.Ptr(util.RandEmail()),
		}
		s.Nil(repo.CreateStudioUser(s.ctx, params))
		s.Nil(roleRepo.SaveUserRoles(s.ctx, domain.SaveUserRolesRequest{
			OrgID: orgID,
			UID:   uid2,
			Roles: []domain.StudioRole{
				{
					Name: "owner",
				},
			},
		}))
	}

	expectedRoles := []domain.StudioRole{
		{
			Module: "asset_pro",
			Name:   "admin",
		},
		{
			Module: "compliance",
			Name:   "admin",
		},
		{
			Module: "",
			Name:   "owner",
		},
	}

	kgError = organization.UpdateStudioUser(s.ctx, organization.UpdateStudioUserParams{
		OrgID:    orgID,
		UID:      uid,
		Name:     util.Ptr(name),
		MemberID: util.Ptr(memberID),
		Roles:    expectedRoles,
	})
	s.Nil(kgError)

	studioUser, kgError := repo.GetStudioUser(s.ctx, orgID, uid)
	s.Nil(kgError)
	s.Equal(name, studioUser.Name)
	s.Equal(memberID, *studioUser.MemberID)

	roleBindings, kgError := roleRepo.GetRoleBindings(s.ctx, domain.GetRoleBindingsRequest{
		OrgID: orgID,
		UID:   uid,
	})
	s.Nil(kgError)
	s.Equal(3, len(roleBindings))

	actualStudioRole := make([]domain.StudioRole, 0, len(roleBindings))
	for _, roleBinding := range roleBindings {
		actualStudioRole = append(actualStudioRole, roleBinding.StudioRole)
	}

	for _, expectedRole := range expectedRoles {
		s.Contains(actualStudioRole, expectedRole)
	}

	rolesInCache, kgError := cacheRoleRepo.GetRolesByUser(s.ctx, orgID, uid)
	s.Nil(kgError)
	s.Len(rolesInCache, 3)

	for _, expectedRole := range expectedRoles {
		s.Contains(rolesInCache, expectedRole)
	}
}

func TestStudioUserSuite(t *testing.T) {
	suite.Run(t, new(studioUserSuite))
}
