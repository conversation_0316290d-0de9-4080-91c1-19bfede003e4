package opensea

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestSaveFloorPriceBySlugs(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	rdb.Reset()

	err := SaveFloorPriceBySlugs(context.Background(), []string{"boredapeyachtclub", "azuki", "lilpudgys"})
	assert.Nil(t, err)

	time.Sleep(60 * time.Second)

	// TODO: verify floor price data in firebase storage here
}
