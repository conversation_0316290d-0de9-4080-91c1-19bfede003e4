package sol

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestTokenList(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	rdb.Reset()
	rdb.Get().Create([]*model.TokenMetadata{
		{
			ChainID:         "solana",
			ContractAddress: "6zoshtkmyX4kRFg3p152yV2bPssxeYdNvW3c6EVCE4UP",
			CoingeckoID:     util.Ptr("prick"),
			Symbol:          util.Ptr("prick"),
			Name:            util.Ptr("Prick"),
		},
	})
	address := "B6a3Zs849Ac4zKzS23NZdwSv7ZBHAHimtbGJ3RQZ2pAw"
	assets, syncAddresses := TokenList(context.Background(), []string{address})
	assert.NotEmpty(t, assets)
	for _, asset := range assets {
		t.Logf("%s: %+v", *asset.Symbol, asset)
	}
	assert.Equal(t, 1, len(syncAddresses))
	assert.Equal(t, 2, len(assets))
	assert.Equal(t, address, syncAddresses[0])
}
