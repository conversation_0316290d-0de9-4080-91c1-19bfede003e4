package gasfaucet

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	alchemyapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCreate(t *testing.T) {
	ctx := context.Background()
	repo, mockAlchemyReq := setup(t)
	uid := "user1"
	chainID := "arb"
	wallet := "******************************************"
	orgID := 1
	orgWallet := "******************************************"

	// for every post request
	mockAlchemyReq.EXPECT().Post("https://arb-mainnet.g.alchemy.com/v2//").AnyTimes().DoAndReturn(
		func(url string) (*resty.Response, error) {
			return &resty.Response{
				RawResponse: &http.Response{
					StatusCode: 200,
				},
			}, nil
		})

	mockAlchemyReq.EXPECT().SetBody(map[string]interface{}{
		"jsonrpc": "2.0",
		"method":  "eth_gasPrice",
		"params":  nil,
		"id":      1,
	}).Times(1).Return(mockAlchemyReq)
	mockAlchemyReq.EXPECT().SetResult(&alchemyapi.JsonRpcResp{}).Times(1).DoAndReturn(func(res *alchemyapi.JsonRpcResp) *resty.MockRequest {
		bodyData := `{"jsonrpc":"2.0","id":1,"result":"0x989680"}`
		err := json.Unmarshal([]byte(bodyData), res)
		assert.Nil(t, err)
		return mockAlchemyReq
	})

	mockAlchemyReq.EXPECT().SetBody(gomock.Cond(func(v any) bool {
		val, ok := v.(map[string]interface{})
		if !ok {
			return false
		}
		assert.Equal(t, "eth_estimateGas", val["method"])
		return true
	})).Times(1).Return(mockAlchemyReq)
	mockAlchemyReq.EXPECT().SetResult(&alchemyapi.JsonRpcResp{}).Times(1).DoAndReturn(func(res *alchemyapi.JsonRpcResp) *resty.MockRequest {
		bodyData := `{"jsonrpc":"2.0","id":1,"result":"0x5208"}`
		err := json.Unmarshal([]byte(bodyData), res)
		assert.Nil(t, err)
		return mockAlchemyReq
	})

	mockAlchemyReq.EXPECT().SetBody(gomock.Cond(func(v any) bool {
		val, ok := v.(map[string]interface{})
		if !ok {
			return false
		}
		assert.Equal(t, "eth_getBalance", val["method"])
		assert.ElementsMatch(t, []string{orgWallet, "latest"}, val["params"])
		return true
	})).Times(1).Return(mockAlchemyReq)
	mockAlchemyReq.EXPECT().SetResult(&alchemyapi.BalanceResp{}).Times(1).DoAndReturn(func(res *alchemyapi.BalanceResp) *resty.MockRequest {
		bodyData := `{"jsonrpc":"2.0","id":1,"result":"0xffffffffffff"}`
		err := json.Unmarshal([]byte(bodyData), res)
		assert.Nil(t, err)
		return mockAlchemyReq
	})
	mockAlchemyReq.EXPECT().SetBody(gomock.Cond(func(v any) bool {
		val, ok := v.(map[string]interface{})
		if !ok {
			return false
		}
		assert.Equal(t, "eth_getTransactionCount", val["method"])
		assert.ElementsMatch(t, []string{orgWallet, "latest"}, val["params"])
		return true
	})).Times(1).Return(mockAlchemyReq)
	mockAlchemyReq.EXPECT().SetResult(&alchemyapi.JsonRpcResp{}).Times(1).DoAndReturn(func(res *alchemyapi.JsonRpcResp) *resty.MockRequest {
		bodyData := `{"jsonrpc":"2.0","id":1,"result":"0x0"}`
		err := json.Unmarshal([]byte(bodyData), res)
		assert.Nil(t, err)
		return mockAlchemyReq
	})

	mockAlchemyReq.EXPECT().SetBody(gomock.Cond(func(v any) bool {
		val, ok := v.(map[string]interface{})
		if !ok {
			return false
		}
		assert.Equal(t, "eth_sendRawTransaction", val["method"])
		assert.ElementsMatch(t, []string{"0xf86b8083b71b008252089431d1c7751eaa6374d4138597e8c7b5a1605cc43c8601176592e0008083014985a0e4a771a4d0eb54b69dfb3ae1580124224f2f49385d441ceafa5ae9736a2ab5bfa03e30799cbdac523d8e2d17096ef948159b6491c1107acdb970d55ff3fa2752a1"}, val["params"])
		return true
	})).Times(1).Return(mockAlchemyReq)
	mockAlchemyReq.EXPECT().SetResult(&alchemyapi.JsonRpcResp{}).Times(1).DoAndReturn(func(res *alchemyapi.JsonRpcResp) *resty.MockRequest {
		bodyData := `{"jsonrpc":"2.0","id":1,"result":"0x5678"}`
		err := json.Unmarshal([]byte(bodyData), res)
		assert.Nil(t, err)
		return mockAlchemyReq
	})

	mockAlchemyReq.EXPECT().SetBody(gomock.Cond(func(v any) bool {
		val, ok := v.(map[string]interface{})
		if !ok {
			return false
		}
		assert.Equal(t, "eth_getTransactionReceipt", val["method"])
		assert.ElementsMatch(t, []string{"0x5678"}, val["params"])
		return true
	})).Times(1).Return(mockAlchemyReq)
	mockAlchemyReq.EXPECT().SetResult(&alchemyapi.GetTransactionReceiptResp{}).Times(1).DoAndReturn(func(res *alchemyapi.GetTransactionReceiptResp) *resty.MockRequest {
		bodyData := `{"jsonrpc":"2.0","id":1,"result":{"status":"0x1"}}`
		err := json.Unmarshal([]byte(bodyData), res)
		assert.Nil(t, err)
		return mockAlchemyReq
	})

	mockAlchemyReq.EXPECT().SetBody(gomock.Cond(func(v any) bool {
		val, ok := v.(map[string]interface{})
		if !ok {
			return false
		}
		assert.Equal(t, "eth_getTransactionByHash", val["method"])
		assert.ElementsMatch(t, []string{"0x5678"}, val["params"])
		return true
	})).Times(1).Return(mockAlchemyReq)
	mockAlchemyReq.EXPECT().SetResult(&alchemyapi.GetTransactionByHashResp{}).Times(1).DoAndReturn(func(res *alchemyapi.GetTransactionByHashResp) *resty.MockRequest {
		bodyData := `{"jsonrpc":"2.0","id":1,"result":{"blockHash":"0x03565828b9f0cb8f2b2711afa0c00c0701fb151fd7f9da07a187455a18d4ce31","blockNumber":"0xd4dc40c","hash":"0xa169820f0bc68b353a2bc3435b35858008d931edbf3079ea29c4d32a06843ed4","transactionIndex":"0x1","type":"0x0","nonce":"0xb","input":"0x","r":"0xdd671cfef0de096b31832b1bffd81b18c412c37e41e10fee1432c63698fc7249","s":"0x4b08f45fe3a01144c98b823d1b6d19b6fbcd26c1545865c9d5321583796679ac","chainId":"0xa4b1","v":"0x14985","gas":"0xe3b2","from":"******************************************","to":"******************************************","value":"0x1176592e000","gasPrice":"0xb71b00"}}`
		err := json.Unmarshal([]byte(bodyData), res)
		assert.Nil(t, err)
		return mockAlchemyReq
	})

	repo.EXPECT().AcquireLockWithRetry(gomock.Any(), "gas-faucet-1-arb", gomock.Any(), gomock.Any()).Return(nil)
	repo.EXPECT().GetUserWalletAddresses(gomock.Any(), uid).Return([]string{wallet}, nil)
	repo.EXPECT().SentUsdTodayByWallet(gomock.Any(), orgID, wallet).Return(0.01, nil)
	repo.EXPECT().SentUsdToday(gomock.Any(), orgID).Return(0.01, nil)
	repo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), orgID).AnyTimes().Return(&domain.OrganizationWallets{EvmAddress: orgWallet}, nil)
	repo.EXPECT().CreateGasFaucet(gomock.Any(), gomock.Cond(func(v any) bool {
		val, ok := v.(*domain.GasFaucet)
		if !ok {
			return false
		}
		assert.Equal(t, orgID, val.OrgID)
		assert.Equal(t, uid, val.UID)
		assert.Equal(t, chainID, val.ChainID)
		assert.Equal(t, wallet, val.ReceiveWallet)
		assert.Equal(t, int64(100000), val.GasAmount)
		assert.Equal(t, 0.012, *val.GasPriceGwei)
		return true
	})).Return(3, nil)
	repo.EXPECT().UpdateGasFaucet(gomock.Any(), gomock.Cond(func(v any) bool {
		val, ok := v.(*domain.GasFaucetUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, 3, val.ID)
		assert.Equal(t, domain.TxStatusUnknown, *val.Status)
		assert.Equal(t, "0x5678", *val.TxHash)
		return true
	})).Return(nil)
	repo.EXPECT().GetNativeAssetPrice(gomock.Any(), chainID).Return(2000.0, nil)
	repo.EXPECT().UpdateGasFaucet(gomock.Any(), gomock.Cond(func(v any) bool {
		val, ok := v.(*domain.GasFaucetUpdate)
		if !ok {
			return false
		}
		assert.Equal(t, 3, val.ID)
		assert.Equal(t, 0.00000189948, *val.TotalCost)
		assert.Equal(t, 0.00379896, *val.TotalCostUsd)
		return true
	})).Return(nil)
	repo.EXPECT().ReleaseLock(gomock.Any(), "gas-faucet-1-arb")

	err := Create(ctx, &domain.GasFaucet{
		OrgID:         orgID,
		UID:           "user1",
		ChainID:       "arb",
		ReceiveWallet: wallet,
		GasAmount:     100000,
	})
	t.Logf("err: %v", err)
	assert.Nil(t, err)
}

func setup(t *testing.T) (*domain.MockGasFaucetRepo, *resty.MockRequest) {
	rdb.Reset()
	assert.Nil(t, rdb.CreateSeedData())
	ctrl := gomock.NewController(t)
	repo := domain.NewMockGasFaucetRepo(ctrl)
	Init(repo)
	mockRestyClient, mockRestyRequest := getNew3rdPartyRestyMock(ctrl)
	alchemyapi.Init(mockRestyClient)
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})
	tx.Init(rdb.GormRepo())

	// signing server
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", signing.SignEvmTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
	return repo, mockRestyRequest
}

func getNew3rdPartyRestyMock(ctrl *gomock.Controller) (*resty.MockClient, *resty.MockRequest) {
	mockClient := resty.NewMockClient(ctrl)
	mockRequest := resty.NewMockRequest(ctrl)

	mockClient.EXPECT().OnBeforeRequest(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().OnAfterResponse(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().SetBaseURL(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().SetTimeout(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().SetBasicAuth(gomock.Any(), gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().SetHeader(gomock.Any(), gomock.Any()).AnyTimes().Return(mockClient)

	mockClient.EXPECT().R().AnyTimes().Return(mockRequest)
	mockRequest.EXPECT().SetContext(gomock.Any()).AnyTimes().Return(mockRequest)
	mockRequest.EXPECT().SetHeader(gomock.Any(), gomock.Any()).AnyTimes().Return(mockRequest)
	return mockClient, mockRequest
}
