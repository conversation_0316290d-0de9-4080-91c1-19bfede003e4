// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/service/coingecko (interfaces: IService)
//
// Generated by this command:
//
//	mockgen -package=coingecko -self_package=github.com/kryptogo/kg-wallet-backend/pkg/service/coingecko -destination=common_mock.go . IService
//

// Package coingecko is a generated GoMock package.
package coingecko

import (
	context "context"
	reflect "reflect"
	time "time"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockIService is a mock of IService interface.
type MockIService struct {
	ctrl     *gomock.Controller
	recorder *MockIServiceMockRecorder
}

// MockIServiceMockRecorder is the mock recorder for MockIService.
type MockIServiceMockRecorder struct {
	mock *MockIService
}

// NewMockIService creates a new mock instance.
func NewMockIService(ctrl *gomock.Controller) *MockIService {
	mock := &MockIService{ctrl: ctrl}
	mock.recorder = &MockIServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIService) EXPECT() *MockIServiceMockRecorder {
	return m.recorder
}

// GetTokenPriceByAddress mocks base method.
func (m *MockIService) GetTokenPriceByAddress(arg0 context.Context, arg1 domain.Chain, arg2 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenPriceByAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenPriceByAddress indicates an expected call of GetTokenPriceByAddress.
func (mr *MockIServiceMockRecorder) GetTokenPriceByAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenPriceByAddress", reflect.TypeOf((*MockIService)(nil).GetTokenPriceByAddress), arg0, arg1, arg2)
}

// HistoryPriceInUSD mocks base method.
func (m *MockIService) HistoryPriceInUSD(arg0 context.Context, arg1 string, arg2 time.Time) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HistoryPriceInUSD", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HistoryPriceInUSD indicates an expected call of HistoryPriceInUSD.
func (mr *MockIServiceMockRecorder) HistoryPriceInUSD(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HistoryPriceInUSD", reflect.TypeOf((*MockIService)(nil).HistoryPriceInUSD), arg0, arg1, arg2)
}

// QuoteInUSD mocks base method.
func (m *MockIService) QuoteInUSD(arg0 context.Context, arg1 string) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QuoteInUSD", arg0, arg1)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QuoteInUSD indicates an expected call of QuoteInUSD.
func (mr *MockIServiceMockRecorder) QuoteInUSD(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QuoteInUSD", reflect.TypeOf((*MockIService)(nil).QuoteInUSD), arg0, arg1)
}

// QuotesInUSD mocks base method.
func (m *MockIService) QuotesInUSD(arg0 context.Context, arg1 []string) (map[string]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QuotesInUSD", arg0, arg1)
	ret0, _ := ret[0].(map[string]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QuotesInUSD indicates an expected call of QuotesInUSD.
func (mr *MockIServiceMockRecorder) QuotesInUSD(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QuotesInUSD", reflect.TypeOf((*MockIService)(nil).QuotesInUSD), arg0, arg1)
}
