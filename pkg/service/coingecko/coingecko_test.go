package coingecko

import (
	"context"
	"net/http"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	coingeckoapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/coingecko-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func getNew3rdPartyRestyMock(ctrl *gomock.Controller) (*resty.MockClient, *resty.MockRequest) {
	mockClient := resty.NewMockClient(ctrl)
	mockRequest := resty.NewMockRequest(ctrl)

	mockClient.EXPECT().OnBeforeRequest(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().OnAfterResponse(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().SetBaseURL(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().SetTimeout(gomock.Any()).AnyTimes().Return(mockClient)

	mockClient.EXPECT().R().AnyTimes().Return(mockRequest)
	mockRequest.EXPECT().SetContext(gomock.Any()).AnyTimes().Return(mockRequest)
	return mockClient, mockRequest
}

func TestQuoteInUSD(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	rdb.Reset()
	coingeckoapi.InitDefault(domain.NewAllPassRateLimiter())
	Init()

	ctx := context.TODO()
	cache.FlushDb(ctx)

	ids := []string{"solana"}

	quote, err := Get().QuoteInUSD(ctx, ids[0])
	assert.Nil(t, err)
	assert.Greater(t, quote, float64(0))

	quotesInUSD, err := Get().QuotesInUSD(ctx, ids)
	assert.Nil(t, err)
	assert.NotNil(t, quotesInUSD[ids[0]])
	assert.Equal(t, quote, quotesInUSD[ids[0]])

	stat := Stat()
	assert.Equal(t, stat.UseCacheCnt, 1)
	assert.Equal(t, stat.RequestCnt, 1)
}

func TestMockQuoteInUSD(t *testing.T) {
	ctx := context.TODO()
	rdb.Reset()
	ctrl := gomock.NewController(t)

	mockClient, mockRequest := getNew3rdPartyRestyMock(ctrl)
	coingeckoapi.InitResty(mockClient, domain.NewAllPassRateLimiter())
	mockRequest.EXPECT().SetQueryParam("ids", "solana").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("vs_currencies", "usd").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetResult(gomock.Any()).Times(1).DoAndReturn(func(respData *coingeckoapi.SimplePriceResp) *resty.MockRequest {
		(*respData)["solana"] = map[string]float64{"usd": 100.123}
		return mockRequest
	})
	mockRequest.EXPECT().Get("/simple/price").Times(1).DoAndReturn(func(url string) (*resty.Response, error) {
		return &resty.Response{
			RawResponse: &http.Response{
				StatusCode: 200,
			},
		}, nil
	})
	Init()

	cache.FlushDb(ctx)

	ids := []string{"solana"}

	quote, err := Get().QuoteInUSD(ctx, ids[0])
	assert.Nil(t, err)
	assert.Equal(t, quote, float64(100.123))

	quotesInUSD, err := Get().QuotesInUSD(ctx, ids)
	assert.Nil(t, err)
	assert.NotNil(t, quotesInUSD[ids[0]])
	assert.Equal(t, quote, quotesInUSD[ids[0]])

	stat := Stat()
	assert.Equal(t, stat.UseCacheCnt, 1)
	assert.Equal(t, stat.RequestCnt, 1)
}
