package cloudvision

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestDetectText(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	ctx := context.Background()
	result, err := DetectText(ctx, "https://i.seadn.io/gcs/files/c0e09d400a51410c709624813bbfa2d3.png?w=500&auto=format")
	assert.Nil(t, err)
	assert.Contains(t, result, "circlusdc.com")
}
