package gaslesssend

import (
	"context"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/chain/tron"
	"github.com/kryptogo/kg-wallet-backend/domain"
	feeeapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/feee-api"
	tronscanapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/tronscan-api"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	gasswaptest "github.com/kryptogo/kg-wallet-backend/pkg/service/gas-swap/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	trongrid "github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestCreate(t *testing.T) {
	ctx := context.Background()

	const orgWallet = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const from = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	const recipient = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
	// signed using tronbox console. private key of from: df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3
	// enter following command in console to generate signed tx: tronWeb.transactionBuilder.triggerSmartContract("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs", "approve(address,uint256)",{feeLimit: 30000000,callValue: 0,shouldPollResponse: false},[{ type: 'address', value: "TYnqfxePKF2K8Fh9pviTGEVaNf4P7FTuX7" }, { type: 'uint256', value: 3000000 }], "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd").then((tx) => {tx.transaction.raw_data.expiration = Date.now()+10*365*86400*1000; tx.transaction.raw_data_hex = tronWeb.utils.transaction.txPbToRawDataHex(tronWeb.utils.transaction.txJsonToPb(tx.transaction)).toLowerCase(); tx.transaction.txID = tronWeb.utils.transaction.txPbToTxID(tronWeb.utils.transaction.txJsonToPb(tx.transaction)).replace(/^0x/, ''); return tronWeb.trx.sign(tx.transaction)}).then((signedTx)=>console.log(JSON.stringify(signedTx))).catch(console.error);
	signedTx := `{"visible":false,"txID":"4cd10ec64c7361178cd143bcd89222ef80486c6a24cb72039d3a49ef73553e5c","raw_data":{"contract":[{"parameter":{"value":{"data":"095ea7b3000000000000000000000000fa54edb9b1c6b54fbf8da133ac83abaa298de94400000000000000000000000000000000000000000000000000000000002dc6c0","owner_address":"419b0e4215f6ffb52680076108a318d66c11224e2a","contract_address":"4142a1e39aefa49290f2b3f9ed688d7cecf86cd6e0"},"type_url":"type.googleapis.com/protocol.TriggerSmartContract"},"type":"TriggerSmartContract"}],"ref_block_bytes":"5415","ref_block_hash":"d8f8ba297d7a8f16","expiration":2036230043336,"fee_limit":30000000,"timestamp":1720870043195},"raw_data_hex":"0a0254152208d8f8ba297d7a8f1640c8ed92c6a13b5aae01081f12a9010a31747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e54726967676572536d617274436f6e747261637412740a15419b0e4215f6ffb52680076108a318d66c11224e2a12154142a1e39aefa49290f2b3f9ed688d7cecf86cd6e02244095ea7b3000000000000000000000000fa54edb9b1c6b54fbf8da133ac83abaa298de94400000000000000000000000000000000000000000000000000000000002dc6c070bbfce3de8a3290018087a70e","signature":["5a7dff96b068058b86cbfaf13a55f8d214a646e4269e8d5450926958f9f37bb7078a2f36f42636c4d018a4f79eea1234d20155ace6a0d514f6f3ecebf444ddfa1B"]}`

	r, e, f := setup(t)

	r.EXPECT().AcquireLockWithRetry(gomock.Any(), "gasless-send-1-shasta", gomock.Any(), gomock.Any()).Return(nil)
	r.EXPECT().ReleaseLock(gomock.Any(), "gasless-send-1-shasta")
	r.EXPECT().GetNativeAssetPrice(gomock.Any(), "shasta").AnyTimes().Return(0.125, nil)
	r.EXPECT().GetAssetPrice(gomock.Any(), "shasta", "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs").AnyTimes().Return(1.0, nil)
	r.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).AnyTimes().Return(&domain.OrganizationWallets{TronAddress: orgWallet}, nil)
	r.EXPECT().GetProfitRate(gomock.Any(), 1, domain.ProfitRateServiceTypeSendGasless).AnyTimes().Return(
		&domain.AssetProProfitRate{
			Service:          domain.ProfitRateServiceTypeSendGasless,
			ProfitRate:       decimal.NewFromFloat(0.1),
			ProfitShareRatio: decimal.NewFromFloat(0.02),
		}, nil)
	f.EXPECT().AccountInfo(gomock.Any()).Return(&feeeapi.AccountInfo{TrxAddress: orgWallet, TrxMoney: 50.0}, nil)
	r.EXPECT().CreateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
		gaslessSend, ok := v.(*domain.GaslessSend)
		if !ok {
			return false
		}
		assert.Equal(t, 1, gaslessSend.OrgID)
		assert.Equal(t, "shasta", gaslessSend.ChainID)
		assert.Equal(t, from, gaslessSend.From)
		assert.Equal(t, recipient, gaslessSend.Recipient)
		assert.Equal(t, usdt, gaslessSend.TokenAddress)
		assert.Equal(t, "3.0", gaslessSend.Amount)
		assert.Equal(t, "2.0", gaslessSend.Fee)
		assert.Equal(t, signedTx, gaslessSend.SignedTxs[0])
		assert.Equal(t, domain.GaslessSendStatusProcessing, gaslessSend.Status)
		t.Logf("gaslessSend: %v", gaslessSend)
		return true
	})).Return(3, nil)
	f.EXPECT().CreateOrder(gomock.Any(), orgWallet, 193352).Return(7.0, nil)
	r.EXPECT().SetEnergyRentUnitCost(gomock.Any(), 7.0/193352.0).Return(nil)
	r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.UpdateGaslessSendRequest)
		if !ok {
			return false
		}
		assert.Equal(t, 3, update.ID)
		assert.NotNil(t, update.EnergyRentCost)
		assert.Equal(t, 7.0, *update.EnergyRentCost)
		assert.NotNil(t, update.GasFaucetTxHash)
		t.Logf("gaslessSend update: %v , %v", *update.EnergyRentCost, *update.GasFaucetTxHash)
		return true
	})).Return(nil)
	e.EXPECT().Execute(gomock.Any(), gomock.Any(), gomock.Cond(
		func(v any) bool {
			task, ok := v.(*domain.HttpTask)
			assert.True(t, ok)
			taskURL := config.GetString("SELF_INTERNAL_HOST") + "/_v/handle_gasless_send/3"
			assert.Equal(t, taskURL, task.URL)
			assert.Equal(t, "POST", task.Method)
			return true
		}), gomock.Cond(
		func(v any) bool {
			taskName, ok := v.(string)
			assert.True(t, ok)
			assert.Contains(t, taskName, "handle-gasless-send-")
			return true
		})).Return(nil)

	id, err := Create(ctx, &domain.GaslessSend{
		OrgID:        1,
		ChainID:      "shasta",
		From:         from,
		Recipient:    recipient,
		TokenAddress: usdt,
		Amount:       "3.0",
		Fee:          "2.0",
		SignedTxs:    []string{signedTx},
	})
	assert.Nil(t, err)
	if err != nil {
		t.Logf("err: %v\n", err.String())
	}
	assert.Equal(t, 3, id)
}

// case: user has already approve token, so no need to send user approve tx
func TestCreateWithoutSignedTxs(t *testing.T) {
	ctx := context.Background()

	const orgWallet = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const from = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	const recipient = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
	const sendContract = "TYnqfxePKF2K8Fh9pviTGEVaNf4P7FTuX7"

	r, e, f := setup(t)

	// approve token before creating gasless send
	tx := gasswaptest.SignApproveTx(t, "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3", from, sendContract, "1.6")
	client := tron.GetClient(domain.Shasta)

	txHash, err := client.BroadcastTransaction(context.Background(), tx.ToProto())
	if err != nil {
		t.Fatalf("failed to broadcast approve tx: %v", err)
	}
	assert.NotEmpty(t, txHash)

	r.EXPECT().AcquireLockWithRetry(gomock.Any(), "gasless-send-1-shasta", gomock.Any(), gomock.Any()).Return(nil)
	r.EXPECT().ReleaseLock(gomock.Any(), "gasless-send-1-shasta")
	r.EXPECT().GetNativeAssetPrice(gomock.Any(), "shasta").AnyTimes().Return(0.125, nil)
	r.EXPECT().GetAssetPrice(gomock.Any(), "shasta", "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs").AnyTimes().Return(1.0, nil)
	r.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).AnyTimes().Return(&domain.OrganizationWallets{TronAddress: orgWallet}, nil)
	r.EXPECT().GetProfitRate(gomock.Any(), 1, domain.ProfitRateServiceTypeSendGasless).AnyTimes().Return(
		&domain.AssetProProfitRate{
			Service:          domain.ProfitRateServiceTypeSendGasless,
			ProfitRate:       decimal.NewFromFloat(0.1),
			ProfitShareRatio: decimal.NewFromFloat(0.02),
		}, nil)
	f.EXPECT().AccountInfo(gomock.Any()).Return(&feeeapi.AccountInfo{TrxAddress: orgWallet, TrxMoney: 50.0}, nil)
	r.EXPECT().CreateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
		gaslessSend, ok := v.(*domain.GaslessSend)
		if !ok {
			return false
		}
		assert.Equal(t, 1, gaslessSend.OrgID)
		assert.Equal(t, "shasta", gaslessSend.ChainID)
		assert.Equal(t, from, gaslessSend.From)
		assert.Equal(t, recipient, gaslessSend.Recipient)
		assert.Equal(t, usdt, gaslessSend.TokenAddress)
		assert.Equal(t, "1.6", gaslessSend.Amount)
		assert.Equal(t, "1.5", gaslessSend.Fee)
		assert.Len(t, gaslessSend.SignedTxs, 0)
		assert.Equal(t, domain.GaslessSendStatusProcessing, gaslessSend.Status)
		t.Logf("gaslessSend: %v", gaslessSend)
		return true
	})).Return(3, nil)
	f.EXPECT().CreateOrder(gomock.Any(), orgWallet, 184285).Return(7.0, nil)
	r.EXPECT().SetEnergyRentUnitCost(gomock.Any(), 7.0/184285.0).Return(nil)
	r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
		update, ok := v.(*domain.UpdateGaslessSendRequest)
		if !ok {
			return false
		}
		assert.Equal(t, 3, update.ID)
		assert.NotNil(t, update.EnergyRentCost)
		assert.Equal(t, 7.0, *update.EnergyRentCost)
		assert.Nil(t, update.GasFaucetTxHash)
		t.Logf("gaslessSend update: %v", *update.EnergyRentCost)
		return true
	})).Return(nil)
	e.EXPECT().Execute(gomock.Any(), gomock.Any(), gomock.Cond(
		func(v any) bool {
			task, ok := v.(*domain.HttpTask)
			assert.True(t, ok)
			taskURL := config.GetString("SELF_INTERNAL_HOST") + "/_v/handle_gasless_send/3"
			assert.Equal(t, taskURL, task.URL)
			assert.Equal(t, "POST", task.Method)
			return true
		}), gomock.Cond(
		func(v any) bool {
			taskName, ok := v.(string)
			assert.True(t, ok)
			assert.Contains(t, taskName, "handle-gasless-send-")
			return true
		})).Return(nil)

	id, kgErr := Create(ctx, &domain.GaslessSend{
		OrgID:        1,
		ChainID:      "shasta",
		From:         from,
		Recipient:    recipient,
		TokenAddress: usdt,
		Amount:       "1.6",
		Fee:          "1.5",
		SignedTxs:    []string{},
	})
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Logf("err: %v\n", kgErr.String())
	}
	assert.Equal(t, 3, id)
}

func setup(t *testing.T) (*MockIRepo, *domain.MockAsyncTaskExecutor, *feeeapi.MockIFeee) {
	rdb.Reset()
	assert.Nil(t, dbtest.CreateStudioDefault(rdb.Get()))
	ctrl := gomock.NewController(t)
	r, e := NewMockIRepo(ctrl), domain.NewMockAsyncTaskExecutor(ctrl)
	Init(r, e)
	f := feeeapi.NewMockIFeee(ctrl)
	feeeapi.Init(f)
	trongrid.Init(trongrid.InitParams{
		TronClient:   trongrid.NewGrpcClient(context.Background(), domain.Tron.ID()),
		ShastaClient: trongrid.NewGrpcClient(context.Background(), domain.Shasta.ID()),
	})
	tronscanapi.InitDefault()

	// signing server
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))

	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/tron", signing.SignTronTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
	return r, e, f
}
