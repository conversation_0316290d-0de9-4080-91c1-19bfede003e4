package gaslesssend

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/chain/tron"
	"github.com/kryptogo/kg-wallet-backend/domain"
	gasswaptest "github.com/kryptogo/kg-wallet-backend/pkg/service/gas-swap/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestHandle(t *testing.T) {
	ctx := context.Background()
	const orgWallet = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const from = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	const recipient = "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"
	const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
	const sendContract = "TYnqfxePKF2K8Fh9pviTGEVaNf4P7FTuX7"
	r, _, f := setup(t)
	tx := gasswaptest.SignApproveTx(t, "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3", from, sendContract, "3.0")
	signedTxBytes, err := json.Marshal(tx)
	assert.NoError(t, err)
	signedTxStr := string(signedTxBytes)

	r.EXPECT().GetProfitRate(gomock.Any(), 1, domain.ProfitRateServiceTypeSendGasless).AnyTimes().Return(&domain.AssetProProfitRate{
		Service:          domain.ProfitRateServiceTypeSendGasless,
		ProfitRate:       decimal.NewFromFloat(0.05),
		ProfitShareRatio: decimal.NewFromFloat(0.25), // kg 0.25 : complement 0.75
	}, nil)
	r.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).AnyTimes().Return(&domain.OrganizationWallets{TronAddress: orgWallet}, nil)

	r.EXPECT().AcquireLockWithRetry(gomock.Any(), "gasless-send-handle-3", gomock.Any(), gomock.Any()).Return(nil)
	r.EXPECT().ReleaseLock(gomock.Any(), "gasless-send-handle-3")
	r.EXPECT().GetNativeAssetPrice(gomock.Any(), "shasta").AnyTimes().Return(0.125, nil)
	f.EXPECT().CreateOrder(gomock.Any(), from, 99764).Return(4.0, nil)
	gomock.InOrder(
		r.EXPECT().GetGaslessSendByID(gomock.Any(), 3).Return(&domain.GaslessSend{
			OrgID:           1,
			UID:             "user1",
			ChainID:         "shasta",
			From:            from,
			Recipient:       recipient,
			TokenAddress:    usdt,
			Amount:          "3.0",
			Fee:             "2.0",
			SignedTxs:       []string{signedTxStr},
			FeeUsd:          2.0,
			Status:          domain.GaslessSendStatusProcessing,
			EnergyRentCost:  util.Ptr(7.0),
			GasFaucetTxHash: util.Ptr("cf77fa64cc19e7fadea82d3379b637d16891b85e7bca0781304a97650c7b29ea"),
		}, nil),
		r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
			update, ok := v.(*domain.UpdateGaslessSendRequest)
			if !ok {
				return false
			}
			assert.Equal(t, 3, update.ID)
			assert.NotNil(t, update.EnergyRentCost)
			assert.Equal(t, 11.0, *update.EnergyRentCost) // 4.0 + 7.0
			assert.NotNil(t, update.UserApproveTxHash)
			assert.Nil(t, update.ProfitMargin)
			t.Logf("UserApproveTxHash: %s\n", *update.UserApproveTxHash)
			return true
		})).Return(nil),
		r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
			update, ok := v.(*domain.UpdateGaslessSendRequest)
			if !ok {
				return false
			}
			assert.Equal(t, 3, update.ID)
			assert.NotNil(t, update.GaslessSendTxHash)
			assert.Nil(t, update.ProfitMargin)
			t.Logf("GaslessSendTxHash: %s\n", *update.GaslessSendTxHash)
			return true
		})).Return(nil),
		r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
			update, ok := v.(*domain.UpdateGaslessSendRequest)
			if !ok {
				return false
			}
			assert.Equal(t, 3, update.ID)
			assert.NotNil(t, update.Status)
			assert.Nil(t, update.ProfitMargin)
			assert.Equal(t, domain.GaslessSendStatusSuccess, *update.Status)
			t.Logf("Status: %s\n", *update.Status)
			return true
		})).Return(nil),
		r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
			update, ok := v.(*domain.UpdateGaslessSendRequest)
			if !ok {
				return false
			}
			t.Logf("update: %+v\n", update)
			assert.Equal(t, 3, update.ID)
			assert.NotNil(t, update.ActualCostUsd)
			if update.ActualCostUsd != nil {
				assert.Greater(t, *update.ActualCostUsd, 0.5) // 4 * 0.125
				assert.Equal(t, decimal.NewFromFloat(2).Sub(decimal.NewFromFloat(*update.ActualCostUsd)).Sub(decimal.NewFromFloat(2/1.15*0.1*0.125)).Mul(decimal.NewFromFloat(0.75)).InexactFloat64(), update.ProfitMargin.InexactFloat64())
				t.Logf("ActualCostUsd: %f\n", *update.ActualCostUsd)
			}
			return true
		})).Return(nil),
		r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
			update, ok := v.(*domain.UpdateGaslessSendRequest)
			if !ok {
				return false
			}
			assert.Equal(t, 3, update.ID)
			assert.NotNil(t, update.RetryCount)
			assert.Equal(t, 1, *update.RetryCount)
			return true
		})).Return(nil),
	)

	kgErr := Handle(ctx, 3)
	if kgErr != nil {
		t.Logf("err: %s\n", kgErr.String())
	}
	assert.Nil(t, err)
}

// case: user has already approve token, so no need to send user approve tx
func TestHandleWithoutSignedTxs(t *testing.T) {
	ctx := context.Background()
	const orgWallet = "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"
	const from = "TQ74pTzVEEAFPrpdpmqGS5rcCdvcgHwmNd"
	const recipient = "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"
	const usdt = "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"
	const sendContract = "TYnqfxePKF2K8Fh9pviTGEVaNf4P7FTuX7"
	tx := gasswaptest.SignApproveTx(t, "df155a9c8f15320e264c3b7d468317503d978dcd57c285739ddea99750dac6b3", from, sendContract, "1.6")
	client := tron.GetClient(domain.IDToChain("shasta"))

	txHash, err := client.BroadcastTransaction(ctx, tx.ToProto())
	assert.Nil(t, err)
	assert.NotEmpty(t, txHash)

	r, _, _ := setup(t)
	r.EXPECT().GetWalletsByOrganizationId(gomock.Any(), 1).AnyTimes().Return(&domain.OrganizationWallets{TronAddress: orgWallet}, nil)
	r.EXPECT().AcquireLockWithRetry(gomock.Any(), "gasless-send-handle-3", gomock.Any(), gomock.Any()).Return(nil)
	r.EXPECT().ReleaseLock(gomock.Any(), "gasless-send-handle-3")
	r.EXPECT().GetNativeAssetPrice(gomock.Any(), "shasta").AnyTimes().Return(0.125, nil)
	gomock.InOrder(
		r.EXPECT().GetGaslessSendByID(gomock.Any(), 3).Return(&domain.GaslessSend{
			OrgID:           1,
			UID:             "user1",
			ChainID:         "shasta",
			From:            from,
			Recipient:       recipient,
			TokenAddress:    usdt,
			Amount:          "1.6",
			Fee:             "1.5",
			SignedTxs:       []string{},
			FeeUsd:          1.5,
			Status:          domain.GaslessSendStatusProcessing,
			EnergyRentCost:  util.Ptr(7.0),
			GasFaucetTxHash: nil,
		}, nil),
		r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
			update, ok := v.(*domain.UpdateGaslessSendRequest)
			if !ok {
				return false
			}
			assert.Equal(t, 3, update.ID)
			assert.NotNil(t, update.GaslessSendTxHash)
			t.Logf("GaslessSendTxHash: %s\n", *update.GaslessSendTxHash)
			return true
		})).Return(nil),
		r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
			update, ok := v.(*domain.UpdateGaslessSendRequest)
			if !ok {
				return false
			}
			assert.Equal(t, 3, update.ID)
			assert.NotNil(t, update.Status)
			assert.Equal(t, domain.GaslessSendStatusSuccess, *update.Status)
			return true
		})).Return(nil),
		r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
			update, ok := v.(*domain.UpdateGaslessSendRequest)
			if !ok {
				return false
			}
			assert.Equal(t, 3, update.ID)
			assert.NotNil(t, update.ActualCostUsd)
			if update.ActualCostUsd != nil {
				assert.Greater(t, *update.ActualCostUsd, 2.0)
				t.Logf("ActualCostUsd: %f\n", *update.ActualCostUsd)
			}
			return true
		})).Return(nil),
		r.EXPECT().UpdateGaslessSend(gomock.Any(), gomock.Cond(func(v any) bool {
			update, ok := v.(*domain.UpdateGaslessSendRequest)
			if !ok {
				return false
			}
			assert.Equal(t, 3, update.ID)
			assert.NotNil(t, update.RetryCount)
			if update.RetryCount != nil {
				assert.Equal(t, 1, *update.RetryCount)
			}
			return true
		})).Return(nil),
	)
	r.EXPECT().GetProfitRate(gomock.Any(), 1, domain.ProfitRateServiceTypeSendGasless).AnyTimes().Return(&domain.AssetProProfitRate{
		Service:          domain.ProfitRateServiceTypeSendGasless,
		ProfitRate:       decimal.NewFromFloat(0.05),
		ProfitShareRatio: decimal.NewFromFloat(0.5),
	}, nil)

	kgErr := Handle(ctx, 3)
	if kgErr != nil {
		t.Fatalf("err: %s\n", kgErr.String())
	}
	assert.Nil(t, kgErr)
}
