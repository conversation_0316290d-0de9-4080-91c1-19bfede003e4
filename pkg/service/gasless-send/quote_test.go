package gaslesssend

import (
	"context"
	"strconv"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestQuote(t *testing.T) {
	ctx := context.Background()

	ctrl := gomock.NewController(t)
	r, e := NewMockIRepo(ctrl), domain.NewMockAsyncTaskExecutor(ctrl)
	Init(r, e)
	r.EXPECT().GetAssetPrice(gomock.Any(), "shasta", "TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs").AnyTimes().Return(1.0, nil)
	r.EXPECT().GetAssetPrice(gomock.Any(), "tron", "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t").AnyTimes().Return(1.0, nil)
	r.EXPECT().GetNativeAssetPrice(gomock.Any(), "shasta").AnyTimes().Return(0.1395, nil)
	r.EXPECT().GetNativeAssetPrice(gomock.Any(), "tron").AnyTimes().Return(0.1395, nil)
	r.EXPECT().GetProfitRate(gomock.Any(), 1, domain.ProfitRateServiceTypeSendGasless).AnyTimes().Return(&domain.AssetProProfitRate{
		Service:          domain.ProfitRateServiceTypeSendGasless,
		ProfitRate:       decimal.NewFromFloat(0.5),
		ProfitShareRatio: decimal.NewFromFloat(0.05),
	}, nil)

	// test tron with cheap energy rent unit cost
	r.EXPECT().GetEnergyRentUnitCost(gomock.Any()).Return(0.000001, nil)
	quote, kgErr := GetQuote(ctx, 1, domain.Tron, domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"), domain.NewTronAddress("TEWdRAiBawsbN5iSbpiT7G1TmkHuhUz7rU"), domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"), "0")
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Errorf("GetQuote failed: %v", kgErr.String())
	}
	assert.NotNil(t, quote)
	fee, err := strconv.ParseFloat(quote.FeeAmount, 64)
	assert.Nil(t, err)
	assert.Greater(t, fee, 1.0)
	assert.Equal(t, "TDjWJXAdbs6akJE6jJdT4sfjY2Y73uGZq5", quote.ApproveSpender)
	t.Logf("tron quote 1: %+v", *quote)

	// test tron with expensive energy rent unit cost
	r.EXPECT().GetEnergyRentUnitCost(gomock.Any()).Return(0.01, nil)
	quote, kgErr = GetQuote(ctx, 1, domain.Tron, domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"), domain.NewTronAddress("TEWdRAiBawsbN5iSbpiT7G1TmkHuhUz7rU"), domain.NewTronAddress("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"), "0")
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Errorf("GetQuote failed: %v", kgErr.String())
	}
	assert.NotNil(t, quote)
	fee, err = strconv.ParseFloat(quote.FeeAmount, 64)
	assert.Nil(t, err)
	assert.Greater(t, fee, 90.0)
	t.Logf("tron quote 2: %+v", *quote)

	// test shasta
	quote, kgErr = GetQuote(ctx, 1, domain.Shasta, domain.NewTronAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn"), domain.NewTronAddress("TCj5e18a6aNgE1xy1BC5PivWTSqHwoD1HZ"), domain.NewTronAddress("TG3XXyExBkPp9nzdajDZsozEu4BkaSJozs"), "0")
	assert.Nil(t, kgErr)
	if kgErr != nil {
		t.Errorf("GetQuote failed: %v", kgErr.String())
	}
	assert.NotNil(t, quote)
	fee, err = strconv.ParseFloat(quote.FeeAmount, 64)
	assert.Nil(t, err)
	assert.Greater(t, fee, 0.5)
	assert.Equal(t, "TYnqfxePKF2K8Fh9pviTGEVaNf4P7FTuX7", quote.ApproveSpender)
	t.Logf("shasta quote: %+v", *quote)
}
