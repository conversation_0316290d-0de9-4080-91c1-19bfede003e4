package ronin

import (
	"context"
	"testing"
	"time"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetAxieBriefList(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	for i := 0; i < 20; i++ {
		go func() {
			t.Logf("Start\n")
			resp, err := GetAxieBriefList(context.Background(), "******************************************", 0)
			assert.Nil(t, err)
			assert.NotNil(t, resp)
			assert.True(t, len(resp.Data.Axies.Results) > 0)
			t.Logf("End\n")
		}()
	}
	time.Sleep(10 * time.Second)
}
