package tron

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetTransactionInfo(t *testing.T) {
	tx, err := GetTransactionInfo(context.Background(), "shasta", "e82b17d97ddeb32db41ad3ce9a2a2e7abb07977a51805210e04e9b856d7031a9")
	assert.Nil(t, err)
	// t.Logf("len: %d, %d\n", len(tx.InternalTransactions), len(tx.InternalTransactions[0].CallValueInfo))
	t.Logf("tx val: %v, %v, %v, %v", tx.UnfreezeAmount, tx.WithdrawAmount, tx.WithdrawExpireAmount, tx.Receipt.EnergyFee)
	t.Logf("tx call: %v", tx.InternalTransactions[0])
	t.Logf("tx call value: %v", tx.InternalTransactions[0].CallValueInfo[0].CallValue)
}
