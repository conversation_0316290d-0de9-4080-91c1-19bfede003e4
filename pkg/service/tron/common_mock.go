// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/service/tron (interfaces: Client)
//
// Generated by this command:
//
//	mockgen -package=tron -self_package=github.com/kryptogo/kg-wallet-backend/pkg/service/tron -destination=common_mock.go . Client
//

// Package tron is a generated GoMock package.
package tron

import (
	context "context"
	big "math/big"
	reflect "reflect"

	api "github.com/kryptogo/gotron-sdk/pkg/proto/api"
	core "github.com/kryptogo/gotron-sdk/pkg/proto/core"
	code "github.com/kryptogo/kg-wallet-backend/pkg/code"
	gomock "go.uber.org/mock/gomock"
)

// MockClient is a mock of Client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// BroadcastRawTransaction mocks base method.
func (m *MockClient) BroadcastRawTransaction(arg0 string) (string, *code.KGError) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BroadcastRawTransaction", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(*code.KGError)
	return ret0, ret1
}

// BroadcastRawTransaction indicates an expected call of BroadcastRawTransaction.
func (mr *MockClientMockRecorder) BroadcastRawTransaction(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BroadcastRawTransaction", reflect.TypeOf((*MockClient)(nil).BroadcastRawTransaction), arg0)
}

// BroadcastTransaction mocks base method.
func (m *MockClient) BroadcastTransaction(arg0 *core.Transaction) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BroadcastTransaction", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BroadcastTransaction indicates an expected call of BroadcastTransaction.
func (mr *MockClientMockRecorder) BroadcastTransaction(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BroadcastTransaction", reflect.TypeOf((*MockClient)(nil).BroadcastTransaction), arg0)
}

// GetEnergyFee mocks base method.
func (m *MockClient) GetEnergyFee(arg0 context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnergyFee", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnergyFee indicates an expected call of GetEnergyFee.
func (mr *MockClientMockRecorder) GetEnergyFee(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnergyFee", reflect.TypeOf((*MockClient)(nil).GetEnergyFee), arg0)
}

// GetOwnerAddress mocks base method.
func (m *MockClient) GetOwnerAddress(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOwnerAddress", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOwnerAddress indicates an expected call of GetOwnerAddress.
func (mr *MockClientMockRecorder) GetOwnerAddress(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOwnerAddress", reflect.TypeOf((*MockClient)(nil).GetOwnerAddress), arg0)
}

// GetRemainingEnergy mocks base method.
func (m *MockClient) GetRemainingEnergy(arg0 context.Context, arg1 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRemainingEnergy", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRemainingEnergy indicates an expected call of GetRemainingEnergy.
func (mr *MockClientMockRecorder) GetRemainingEnergy(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRemainingEnergy", reflect.TypeOf((*MockClient)(nil).GetRemainingEnergy), arg0, arg1)
}

// GetTRC20Decimals mocks base method.
func (m *MockClient) GetTRC20Decimals(arg0 string) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTRC20Decimals", arg0)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTRC20Decimals indicates an expected call of GetTRC20Decimals.
func (mr *MockClientMockRecorder) GetTRC20Decimals(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTRC20Decimals", reflect.TypeOf((*MockClient)(nil).GetTRC20Decimals), arg0)
}

// GetTRC20Symbol mocks base method.
func (m *MockClient) GetTRC20Symbol(arg0 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTRC20Symbol", arg0)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTRC20Symbol indicates an expected call of GetTRC20Symbol.
func (mr *MockClientMockRecorder) GetTRC20Symbol(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTRC20Symbol", reflect.TypeOf((*MockClient)(nil).GetTRC20Symbol), arg0)
}

// GetTransactionInfoByID mocks base method.
func (m *MockClient) GetTransactionInfoByID(arg0 context.Context, arg1 string) (*core.TransactionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionInfoByID", arg0, arg1)
	ret0, _ := ret[0].(*core.TransactionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionInfoByID indicates an expected call of GetTransactionInfoByID.
func (mr *MockClientMockRecorder) GetTransactionInfoByID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionInfoByID", reflect.TypeOf((*MockClient)(nil).GetTransactionInfoByID), arg0, arg1)
}

// GetTrc20Allowance mocks base method.
func (m *MockClient) GetTrc20Allowance(arg0, arg1, arg2 string) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTrc20Allowance", arg0, arg1, arg2)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTrc20Allowance indicates an expected call of GetTrc20Allowance.
func (mr *MockClientMockRecorder) GetTrc20Allowance(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrc20Allowance", reflect.TypeOf((*MockClient)(nil).GetTrc20Allowance), arg0, arg1, arg2)
}

// GetTrc20Balance mocks base method.
func (m *MockClient) GetTrc20Balance(arg0, arg1 string) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTrc20Balance", arg0, arg1)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTrc20Balance indicates an expected call of GetTrc20Balance.
func (mr *MockClientMockRecorder) GetTrc20Balance(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrc20Balance", reflect.TypeOf((*MockClient)(nil).GetTrc20Balance), arg0, arg1)
}

// GetTrxBalance mocks base method.
func (m *MockClient) GetTrxBalance(arg0 string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTrxBalance", arg0)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTrxBalance indicates an expected call of GetTrxBalance.
func (mr *MockClientMockRecorder) GetTrxBalance(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTrxBalance", reflect.TypeOf((*MockClient)(nil).GetTrxBalance), arg0)
}

// ReadContract mocks base method.
func (m *MockClient) ReadContract(arg0, arg1, arg2 string, arg3 []map[string]any) (*api.TransactionExtention, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadContract", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*api.TransactionExtention)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReadContract indicates an expected call of ReadContract.
func (mr *MockClientMockRecorder) ReadContract(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadContract", reflect.TypeOf((*MockClient)(nil).ReadContract), arg0, arg1, arg2, arg3)
}

// ToRawTokenAmount mocks base method.
func (m *MockClient) ToRawTokenAmount(arg0, arg1 string) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ToRawTokenAmount", arg0, arg1)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ToRawTokenAmount indicates an expected call of ToRawTokenAmount.
func (mr *MockClientMockRecorder) ToRawTokenAmount(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ToRawTokenAmount", reflect.TypeOf((*MockClient)(nil).ToRawTokenAmount), arg0, arg1)
}

// Transfer mocks base method.
func (m *MockClient) Transfer(arg0, arg1 string, arg2 int64) (*api.TransactionExtention, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transfer", arg0, arg1, arg2)
	ret0, _ := ret[0].(*api.TransactionExtention)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Transfer indicates an expected call of Transfer.
func (mr *MockClientMockRecorder) Transfer(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transfer", reflect.TypeOf((*MockClient)(nil).Transfer), arg0, arg1, arg2)
}

// TransferTrc20 mocks base method.
func (m *MockClient) TransferTrc20(arg0 context.Context, arg1, arg2, arg3 string, arg4 *big.Int, arg5 int64) (*api.TransactionExtention, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TransferTrc20", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*api.TransactionExtention)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TransferTrc20 indicates an expected call of TransferTrc20.
func (mr *MockClientMockRecorder) TransferTrc20(arg0, arg1, arg2, arg3, arg4, arg5 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TransferTrc20", reflect.TypeOf((*MockClient)(nil).TransferTrc20), arg0, arg1, arg2, arg3, arg4, arg5)
}

// TriggerConstantContract mocks base method.
func (m *MockClient) TriggerConstantContract(arg0, arg1, arg2, arg3 string, arg4 int64) (*api.TransactionExtention, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerConstantContract", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*api.TransactionExtention)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerConstantContract indicates an expected call of TriggerConstantContract.
func (mr *MockClientMockRecorder) TriggerConstantContract(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerConstantContract", reflect.TypeOf((*MockClient)(nil).TriggerConstantContract), arg0, arg1, arg2, arg3, arg4)
}

// TriggerContract mocks base method.
func (m *MockClient) TriggerContract(arg0 context.Context, arg1, arg2, arg3 string, arg4 []map[string]any, arg5, arg6 int64, arg7 string, arg8 int64) (*api.TransactionExtention, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TriggerContract", arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	ret0, _ := ret[0].(*api.TransactionExtention)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TriggerContract indicates an expected call of TriggerContract.
func (mr *MockClientMockRecorder) TriggerContract(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerContract", reflect.TypeOf((*MockClient)(nil).TriggerContract), arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
}

// WaitUntilHavingEnoughEnergy mocks base method.
func (m *MockClient) WaitUntilHavingEnoughEnergy(arg0 context.Context, arg1 string, arg2 int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WaitUntilHavingEnoughEnergy", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// WaitUntilHavingEnoughEnergy indicates an expected call of WaitUntilHavingEnoughEnergy.
func (mr *MockClientMockRecorder) WaitUntilHavingEnoughEnergy(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WaitUntilHavingEnoughEnergy", reflect.TypeOf((*MockClient)(nil).WaitUntilHavingEnoughEnergy), arg0, arg1, arg2)
}
