package tron

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/stretchr/testify/assert"
)

func TestGetTrc20Allowance(t *testing.T) {
	Init(InitParams{
		TronClient:   NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: NewGrpcClient(context.Background(), model.ChainIDShasta),
	})
	client, kgErr := GetClient("tron")
	assert.Nil(t, kgErr)
	allowance, err := client.GetTrc20Allowance("TFkka5Psz5ZiqESuKiuNsxggp3WqTcn3CX", "TDD97yguPESTpcrJMqU6h2ozZbibv4Vaqm", "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t")
	assert.Nil(t, err)
	t.Logf("allowance: %v\n", allowance.String())
}

func TestGetRemainingEnergy(t *testing.T) {
	Init(InitParams{
		TronClient:   NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: NewGrpcClient(context.Background(), model.ChainIDShasta),
	})
	client, kgErr := GetClient("tron")
	assert.Nil(t, kgErr)
	energy, err := client.GetRemainingEnergy(context.Background(), "TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb")
	assert.Nil(t, err)
	t.Logf("energy: %v\n", energy)
}

func TestGetOwnerAddress(t *testing.T) {
	Init(InitParams{
		TronClient:   NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: NewGrpcClient(context.Background(), model.ChainIDShasta),
	})
	client, kgErr := GetClient("tron")
	assert.Nil(t, kgErr)
	owner, err := client.GetOwnerAddress("TDTcR8wBLadFYRekvobSSswHaj351EDNRT")
	assert.Nil(t, err)
	t.Logf("owner: %v\n", owner)
	owner, err = client.GetOwnerAddress("TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn")
	assert.Nil(t, err)
	t.Logf("owner: %v\n", owner)

	_, err = client.GetOwnerAddress("TKjKQSDK5fEN1rYCcJZmAGTECiJyfS9Vj5")
	assert.Equal(t, ErrAccountNotActivated, err)
}
