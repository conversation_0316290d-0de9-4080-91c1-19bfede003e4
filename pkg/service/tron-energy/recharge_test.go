package tronenergy

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/gotron-sdk/pkg/proto/api"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/domain"
	feeeapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/feee-api"
	dbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func TestRecharge(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "FEEE_API_KEY"})
	ctx := context.Background()
	setupSignServer(t)

	rdb.Reset()
	assert.Nil(t, dbtest.CreateStudioDefault(rdb.Get()))
	ctrl := gomock.NewController(t)
	repo := domain.NewMockTronEnergyRepo(ctrl)
	Init(repo)
	feeeapi.InitDefault()

	repo.EXPECT().GetWalletsByOrganizationId(ctx, 1).Return(&domain.OrganizationWallets{TronAddress: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"}, nil)

	txHash, err := Recharge(ctx, 2)
	assert.Nil(t, err)
	assert.NotEmpty(t, txHash)
	t.Logf("txHash: %s", txHash)
}

func TestAutoRecharge(t *testing.T) {
	ctx := context.Background()
	setupSignServer(t)
	rdb.Reset()
	assert.Nil(t, dbtest.CreateStudioDefault(rdb.Get()))

	tests := []struct {
		name           string
		feeeBalance    float64
		walletBalance  int64
		expectRecharge bool
		expectError    bool
		setup          func(mockFeee *feeeapi.MockIFeee, mockTron *tron.MockClient, mockRepo *domain.MockTronEnergyRepo)
	}{
		{
			name:           "no recharge needed",
			feeeBalance:    30.0, // above threshold
			expectRecharge: false,
			setup: func(mockFeee *feeeapi.MockIFeee, mockTron *tron.MockClient, mockRepo *domain.MockTronEnergyRepo) {
				mockFeee.EXPECT().AccountInfo(gomock.Any()).Return(&feeeapi.AccountInfo{
					TrxMoney: 30.0,
				}, nil)
			},
		},
		{
			name:           "insufficient wallet balance",
			feeeBalance:    10.0,
			walletBalance:  50 * 1e6, // 50 TRX
			expectRecharge: false,
			expectError:    true,
			setup: func(mockFeee *feeeapi.MockIFeee, mockTron *tron.MockClient, mockRepo *domain.MockTronEnergyRepo) {
				mockFeee.EXPECT().AccountInfo(gomock.Any()).Return(&feeeapi.AccountInfo{
					TrxMoney: 10.0,
				}, nil)
				mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), gomock.Any()).
					Return(&domain.OrganizationWallets{TronAddress: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"}, nil)
				mockTron.EXPECT().GetTrxBalance(gomock.Any()).Return(int64(50*1e6), nil)
			},
		},
		{
			name:           "successful recharge",
			feeeBalance:    10.0,
			walletBalance:  200 * 1e6, // 200 TRX
			expectRecharge: true,
			setup: func(mockFeee *feeeapi.MockIFeee, mockTron *tron.MockClient, mockRepo *domain.MockTronEnergyRepo) {
				mockFeee.EXPECT().AccountInfo(gomock.Any()).Return(&feeeapi.AccountInfo{
					TrxMoney:        10.0,
					RechargeAddress: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn",
					TrxAddress:      "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7",
				}, nil).Times(1)
				mockFeee.EXPECT().AccountInfo(gomock.Any()).Return(&feeeapi.AccountInfo{
					TrxMoney:        200.0,
					RechargeAddress: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn",
					TrxAddress:      "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7",
				}, nil).AnyTimes()
				mockRepo.EXPECT().GetWalletsByOrganizationId(gomock.Any(), gomock.Any()).
					Return(&domain.OrganizationWallets{TronAddress: "TUNfXYmHh8GDRyqP4rqohsoHYGrLbv6kj7"}, nil).AnyTimes()
				mockTron.EXPECT().GetTrxBalance(gomock.Any()).Return(int64(200*1e6), nil)
				mockTron.EXPECT().Transfer(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(from, to string, amount int64) (*api.TransactionExtention, error) {
						grpcClient := tron.NewGrpcClient(ctx, "shasta")
						return grpcClient.Transfer(from, to, 1)
					})
				mockTron.EXPECT().BroadcastTransaction(gomock.Any()).Return("txHash", nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockFeee := feeeapi.NewMockIFeee(ctrl)
			mockTron := tron.NewMockClient(ctrl)
			mockRepo := domain.NewMockTronEnergyRepo(ctrl)

			feeeapi.Init(mockFeee)
			Init(mockRepo)
			tron.Init(tron.InitParams{
				TronClient: mockTron,
			})

			tt.setup(mockFeee, mockTron, mockRepo)
			err := AutoRecharge(ctx)
			if tt.expectError {
				assert.NotNil(t, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}

func setupSignServer(t *testing.T) {
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
	TEST_SIGNING_PORT := testutil.UnusedPort(t)
	client.SetSigningHost("http://localhost:" + TEST_SIGNING_PORT)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/tron", signing.SignTronTransaction)
	srv := &http.Server{
		Addr:    ":" + TEST_SIGNING_PORT,
		Handler: rSigning,
	}
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("listen: %s\n", err)
		}
	}()
}
