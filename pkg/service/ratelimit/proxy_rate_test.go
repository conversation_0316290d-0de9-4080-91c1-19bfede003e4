package ratelimit

import (
	"context"
	"testing"
	"time"

	"github.com/go-redis/redis_rate/v9"
	"github.com/kryptogo/kg-wallet-backend/pkg/cache"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func setup(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	rdb.Reset()
	cache.FlushDb(context.Background())
}

func TestGetNextProxyIndex(t *testing.T) {
	ctx := context.Background()
	setup(t)

	util.ProxyServers = []string{"proxy1", "proxy2", "proxy3"}

	tests := []struct {
		name           string
		serviceName    cache.ServiceName
		expectedResult int
	}{
		{"First call", cache.ServiceNameTronscan, 0},
		{"Second call", cache.ServiceNameTronscan, 1},
		{"Third call", cache.ServiceNameTronscan, 2},
		{"Fourth call (wrap around)", cache.ServiceNameTronscan, -1},
		{"Fifth call", cache.ServiceNameTronscan, 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getNextProxyIndex(ctx, tt.serviceName)
			assert.NoError(t, err)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestWaitAndGetNextCallParams(t *testing.T) {
	ctx := context.Background()
	setup(t)

	util.ProxyServers = []string{"proxy1", "proxy2"}

	// Set up a very low rate limit for testing
	rateMap[cache.ServiceNameTronscan] = RateMap{
		cache.APICallTypeFree:   redis_rate.Limit{Burst: 1, Rate: 1, Period: time.Second * 2},
		cache.APICallTypeAPIKey: redis_rate.Limit{Burst: 1, Rate: 1, Period: time.Second},
	}

	results := make([]struct {
		proxyUrl string
		callType cache.APICallType
	}, 10)

	for i := 0; i < 10; i++ {
		proxyUrl, callType := WaitAndGetNextCallParams(ctx, cache.ServiceNameTronscan)
		results[i] = struct {
			proxyUrl string
			callType cache.APICallType
		}{proxyUrl, callType}
		time.Sleep(100 * time.Millisecond) // Small delay to simulate real-world scenario
	}

	freeCount := 0
	apiKeyCount := 0
	proxy1Count := 0
	proxy2Count := 0
	emptyProxyCount := 0

	for _, result := range results {
		switch result.callType {
		case cache.APICallTypeFree:
			freeCount++
		case cache.APICallTypeAPIKey:
			apiKeyCount++
		}

		switch result.proxyUrl {
		case "proxy1":
			proxy1Count++
		case "proxy2":
			proxy2Count++
		case "":
			emptyProxyCount++
		}
	}

	assert.True(t, freeCount > 0, "Expected some free API calls")
	assert.True(t, apiKeyCount > 0, "Expected some API key calls")
	assert.True(t, proxy1Count > 0, "Expected some calls using proxy1")
	assert.True(t, proxy2Count > 0, "Expected some calls using proxy2")
	assert.True(t, emptyProxyCount > 0, "Expected some calls without proxy")

	assert.Equal(t, 10, freeCount+apiKeyCount, "Total calls should equal 10")
	assert.Equal(t, 10, proxy1Count+proxy2Count+emptyProxyCount, "Total proxy usage should equal 10")
}
