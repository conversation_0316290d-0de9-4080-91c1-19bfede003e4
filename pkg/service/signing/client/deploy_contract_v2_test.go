package client_test

import (
	"context"
	"fmt"
	"math/big"
	"math/rand"
	"net/http"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kryptogo/kg-wallet-backend/api/signing"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	rdbtest "github.com/kryptogo/kg-wallet-backend/pkg/db/test"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/rdb"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/client"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tx"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestDeployContractV2(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	// setup sign server service
	setupDB(t)

	// setup test signing server
	shutdownSignServer := setupSignServer(t)
	defer shutdownSignServer()

	// deploy contract
	chainID := "sepolia"
	rpcURL := "https://ethereum-sepolia.blockpi.network/v1/rpc/public"
	contractAddress, txHash, err := client.DeployContractV2(
		context.Background(),
		1,
		chainID,
		&client.KGEventArgument{
			ContractName:    "FAFA Watching U",
			Symbol:          "FAFAWATCH",
			MaxSupplyErc721: big.NewInt(50),
			BaseURI:         "https://wallet-static-dev.kryptogo.com/public/studio/nft/kryptogo/fafa-watching-u/meta/",
			ContractURI:     "https://wallet-static-dev.kryptogo.com/public/studio/nft/kryptogo/fafa-watching-u_contract.json",
		},
		"ERC721",
	)
	assert.Nil(t, err)

	// wait for tx to be confirmed
	retry := 5
	ctx := context.Background()
	txStatus := tx.Get().WaitTxConfirmed(ctx, chainID, txHash, retry)

	assert.Equal(t, model.TxStatusSuccess, txStatus)
	t.Logf("contract address: %s\n", contractAddress)
	assert.True(t, util.IsValidContract(contractAddress, rpcURL))
}

// Seed database with necessary data and init sign server.
func setupDB(t *testing.T) {
	rdb.Reset()
	assert.Nil(t, rdbtest.CreateStudioDefault(rdb.Get()))
	server.Init(rdb.GormRepo(), &stub.DefaultAlertSender{}, server.NewKMSPrivateKeyEncryptor(""))
}

func setupSignServer(t *testing.T) func() {
	testSigningPort := fmt.Sprintf("%d", 8100+rand.Intn(50000))
	client.SetSigningHost("http://localhost:" + testSigningPort)
	rSigning := gin.Default()
	rSigning.POST("/v1/sign/evm", signing.AuthorizeSigningServer, signing.SignEvmTransaction)
	srv := &http.Server{
		Addr:    ":" + testSigningPort,
		Handler: rSigning,
	}
	go func() {
		err := srv.ListenAndServe()
		if err != nil && err != http.ErrServerClosed {
			t.Logf("Signing server terminated with error: %v\n", err)
		}
	}()
	time.Sleep(1 * time.Second) // wait for server to start
	return func() {
		err := srv.Shutdown(context.Background())
		assert.Nil(t, err)
		fmt.Println("Finish Shutdown")
	}
}
