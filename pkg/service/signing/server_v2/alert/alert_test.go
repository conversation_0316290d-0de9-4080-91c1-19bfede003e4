package alert_test

import (
	"context"
	"encoding/hex"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/alert"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/stretchr/testify/assert"
)

func TestTronCheck(t *testing.T) {
	t.Skip("skip because tron rpc is very unstable now")
	
	policy := alert.NewTronAlertPolicy(100)
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})

	c, err := tron.GetClient("tron")
	assert.Nil(t, err)

	// no alert when sending 1 USDT
	tx, err := c.TransferTrc20(context.Background(), "TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb", "TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb", "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", big.NewInt(1_000_000), int64(100_000_000))
	assert.Nil(t, err)
	if err != nil {
		t.Fatalf("TransferTrc20 failed: %v", err)
	}
	msg := policy.AlertMessage(tx.Transaction)
	assert.Nil(t, err)
	assert.Nil(t, msg)

	// alert when sending 1000 USDT
	tx, err = c.TransferTrc20(context.Background(), "TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb", "TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb", "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", big.NewInt(1_000_000_000), int64(100_000_000))
	assert.Nil(t, err)
	if err != nil {
		t.Fatalf("TransferTrc20 failed: %v", err)
	}
	msg = policy.AlertMessage(tx.Transaction)
	assert.NotNil(t, msg)
}

func TestEvmCheck(t *testing.T) {
	newTx := func(to common.Address, data []byte) *types.Transaction {
		return types.NewTransaction(
			uint64(0),
			to,
			big.NewInt(0),
			uint64(21000),
			big.NewInt(1000000000),
			data,
		)
	}
	policy := alert.NewEvmAlertPolicy(100)

	// alert when sending 298 USDT
	d, err := hex.DecodeString("a9059cbb0000000000000000000000002302bb75449cb2de0d8e6e693bcbece657aff5800000000000000000000000000000000000000000000000000000000011cdb861")
	assert.Nil(t, err)
	tx := newTx(common.HexToAddress("0xdAC17F958D2ee523a2206206994597C13D831ec7"), d)
	msg := policy.AlertMessage(1, tx)
	assert.NotNil(t, msg)

	// no alert when sending 18 USDT
	d, err = hex.DecodeString("a9059cbb0000000000000000000000002302bb75449cb2de0d8e6e693bcbece657aff58000000000000000000000000000000000000000000000000000000000011cdb86")
	assert.Nil(t, err)
	tx = newTx(common.HexToAddress("0x2791Bca1f2de4661ED88A30C99A7a9449Aa84174"), d)
	msg = policy.AlertMessage(137, tx)
	assert.Nil(t, msg)
}
