package server_test

import (
	"context"
	"os"
	"strings"
	"testing"

	"github.com/ethereum/go-ethereum/crypto"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/kms"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/stretchr/testify/assert"
)

func TestKMSPrivateKeyEncryptor(t *testing.T) {
	if !strings.Contains(os.Getenv("GOOGLE_APPLICATION_CREDENTIALS"), "kms") {
		t.Skip("skipping test; GOOGLE_APPLICATION_CREDENTIALS is not set as kms")
	}

	kmsKeyName := kms.GetKmsKeyName("mnemonic", "key")
	k := server.NewKMSPrivateKeyEncryptor(kmsKeyName)

	privKey, err := crypto.GenerateKey()
	if err != nil {
		t.Fatal(err)
	}

	encryptedPrivateKey, err := k.Encrypt(context.Background(), privKey)
	if err != nil {
		t.Fatal(err)
	}
	decryptedPk, err := k.Decrypt(context.Background(), encryptedPrivateKey)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, privKey.D, decryptedPk.D)
}

func TestDecryptForOldVersion(t *testing.T) {
	if !strings.Contains(os.Getenv("GOOGLE_APPLICATION_CREDENTIALS"), "kms") {
		t.Skip("skipping test; GOOGLE_APPLICATION_CREDENTIALS is not set as kms")
	}

	kmsKeyName := kms.GetKmsKeyName("mnemonic", "key")
	k := server.NewKMSPrivateKeyEncryptor(kmsKeyName)

	decryptedPk, err := k.Decrypt(context.Background(), "cjV4WEJJK2N6TjM3S1NRTFhab0NaZ0Q3cmEzUEN5RFluV25OQnUwVkhuZ0lZU0Rsbm82M2tMS21uMk5hY3VjWFV1SUVvcmtVRWt6eGQ4eUxRays5cXFXVUNYWktiSkxFdVZZWEhUMUd4dUJMM3Z0SnZ1U2xpTjR1RE0xZHhidldWa2Z3YllvN2tyemIrd2ZlQmlPb093PT0=")
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, "******************************************", crypto.PubkeyToAddress(decryptedPk.PublicKey).Hex())
}
