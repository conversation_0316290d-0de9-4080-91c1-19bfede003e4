package server_test

import (
	"context"
	"encoding/hex"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	server "github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/signing/server_v2/stub"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	"github.com/stretchr/testify/assert"
)

func TestGetCreateWallets(t *testing.T) {
	ctx := context.Background()
	repo := stub.NewInMemoryOrganizationSignerRepo()
	sender := &stub.DefaultAlertSender{}
	encryptor := &stub.NoOpPrivateKeyEncryptor{}
	server.Init(repo, sender, encryptor)

	for i := 0; i < 5; i++ {
		orgSigner := stub.NewStubOrganizationSigner()
		err := server.GetOrgSignerRepo().SaveOrganizationSigner(context.Background(), i, orgSigner)
		assert.Nil(t, err)
	}

	// test get
	orgSigner, err := server.GetWalletsByOrganization(ctx, 1)
	assert.Nil(t, err)
	assert.NotNil(t, orgSigner)

	// test get not found
	orgSigner, err = server.GetWalletsByOrganization(ctx, 6)
	assert.NotNil(t, err)
	assert.Equal(t, code.StudioOrganizationNotFound, err.Code)
	assert.Nil(t, orgSigner)

	// test create
	orgWallets, err := server.CreateOrganizationWallets(context.Background(), 100)
	assert.Nil(t, err)
	assert.NotNil(t, orgWallets)

	// test create already exists
	orgWallets, err = server.CreateOrganizationWallets(context.Background(), 1)
	assert.NotNil(t, err)
	assert.Equal(t, code.OrganizationAlreadyHasWallet, err.Code)
	assert.Nil(t, orgWallets)
}

func TestAlertThreshold(t *testing.T) {
	ctx := context.Background()
	repo := stub.NewInMemoryOrganizationSignerRepo()
	sender := &stub.DefaultAlertSender{}
	encryptor := &stub.NoOpPrivateKeyEncryptor{}
	server.Init(repo, sender, encryptor)

	for i := 0; i < 2; i++ {
		orgSigner := stub.NewStubOrganizationSigner()
		err := server.GetOrgSignerRepo().SaveOrganizationSigner(context.Background(), i, orgSigner)
		assert.Nil(t, err)
	}

	orgSigner, err := server.GetSignerByOrganizationId(ctx, 1)
	assert.Nil(t, err)
	assert.NotNil(t, orgSigner)
	// default alert threshold is 100 USD
	assert.Equal(t, float64(100), orgSigner.SignAlertThreshold)

	// update
	err = server.UpdateAlertThreshold(context.Background(), 1, 200)
	assert.Nil(t, err)

	// alert threshold is updated
	orgSigner, err = server.GetSignerByOrganizationId(ctx, 1)
	assert.Nil(t, err)
	assert.NotNil(t, orgSigner)
	assert.Equal(t, float64(200), orgSigner.SignAlertThreshold)
}

func TestSignTx(t *testing.T) {
	repo := stub.NewInMemoryOrganizationSignerRepo()
	sender := &stub.DefaultAlertSender{}
	encryptor := &stub.NoOpPrivateKeyEncryptor{}
	server.Init(repo, sender, encryptor)

	orgSigner := stub.NewFixedOrganizationSigner()
	err := server.GetOrgSignerRepo().SaveOrganizationSigner(context.Background(), 1, orgSigner)
	assert.Nil(t, err)

	// test sign tx
	input, e := hex.DecodeString("a9059cbb0000000000000000000000002302bb75449cb2de0d8e6e693bcbece657aff5800000000000000000000000000000000000000000000000000000000011cdb861")
	assert.Nil(t, e)
	req := &server.SignEvmReq{
		OrgID:       1,
		ChainID:     1,
		Transaction: types.NewTransaction(16, common.HexToAddress("******************************************"), big.NewInt(0), 0x5330, big.NewInt(0x533000000), input),
	}
	res, err := server.SignEvmTransaction(context.Background(), req)
	assert.Nil(t, err)

	tx := res.Transaction
	v, r, s := tx.RawSignatureValues()
	assert.NotNil(t, tx.Hash())
	assert.Equal(t, uint64(0x10), tx.Nonce())
	assert.Equal(t, uint64(0x5330), tx.Gas())
	assert.Equal(t, int64(0x533000000), tx.GasPrice().Int64())
	assert.Equal(t, "******************************************", tx.To().Hex())
	assert.Equal(t, int64(0), tx.Value().Int64())
	assert.Equal(t, "a9059cbb0000000000000000000000002302bb75449cb2de0d8e6e693bcbece657aff5800000000000000000000000000000000000000000000000000000000011cdb861", hex.EncodeToString(tx.Data()))
	assert.Equal(t, "0x26", hexutil.EncodeBig(v))
	assert.Equal(t, "0xe3ad78ef954cf521637ee2b13d7227685417938889d1638faad8266f6dfa6645", hexutil.EncodeBig(r))
	assert.Equal(t, "0x480f72c944634084a7a0dd0a806996564f259383d88b5dde1bb7c16c9bdb61c", hexutil.EncodeBig(s))
}

func TestSignTron(t *testing.T) {
	t.Skip("skip because tron rpc is very unstable now")
	
	repo := stub.NewInMemoryOrganizationSignerRepo()
	sender := &stub.DefaultAlertSender{}
	encryptor := &stub.NoOpPrivateKeyEncryptor{}
	server.Init(repo, sender, encryptor)
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(context.Background(), model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(context.Background(), model.ChainIDShasta),
	})

	orgSigner := stub.NewFixedOrganizationSigner()
	kgErr := server.GetOrgSignerRepo().SaveOrganizationSigner(context.Background(), 1, orgSigner)
	assert.Nil(t, kgErr)

	// create tron tx
	c, err := tron.GetClient("tron")
	assert.Nil(t, err)
	tx, e := c.TransferTrc20(context.Background(), "TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb", "TSyvj2Z37fcrGK6MenmLeCuWG9M867wQgb", "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", big.NewInt(1_000_000), int64(100_000_000))
	assert.Nil(t, e)

	// sign tx
	req := &server.SignTronReq{
		OrgID:       1,
		Transaction: tx.Transaction,
	}

	res, kgErr := server.SignTronTransaction(context.Background(), req)
	assert.Nil(t, kgErr)
	assert.Equal(t, 130, len(hex.EncodeToString(res.Transaction.Signature[0])))
}
