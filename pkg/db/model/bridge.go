package model

import (
	"time"
)

// Table name constants
const (
	TableNameBridgeOrganization    = "bridge_organizations"
	TableNameBridgeExternalAccount = "bridge_external_accounts"
	TableNameBridgeTransfer        = "bridge_transfers"
)

// BridgeOrganization mapped from table <bridge_organizations>
type BridgeOrganization struct {
	OrganizationID    int                   `gorm:"column:organization_id;type:integer;primaryKey"`
	CustomerID        string                `gorm:"column:customer_id;type:varchar(255);not null"`
	FullName          string                `gorm:"column:full_name;type:varchar(255);not null"`
	Email             string                `gorm:"column:email;type:varchar(255);not null"`
	Type              BridgeOrganizationType `gorm:"column:type;type:enum('individual','business');not null"`
	KYCLink           *string               `gorm:"column:kyc_link;type:text"`
	TOSLink           *string               `gorm:"column:tos_link;type:text"`
	KYCStatus         BridgeKYCStatus       `gorm:"column:kyc_status;type:enum('not_started','incomplete','awaiting_ubo','under_review','approved','rejected','paused','offboarded');default:'not_started'"`
	TOSStatus         BridgeTOSStatus       `gorm:"column:tos_status;type:enum('pending','approved');default:'pending'"`
	RejectionReasons  *string               `gorm:"column:rejection_reasons;type:json"`
	CreatedAt         time.Time             `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP"`
	UpdatedAt         time.Time             `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP;autoUpdateTime"`
	
	// Foreign key relationships
	Organization *StudioOrganization `gorm:"foreignKey:OrganizationID;references:ID"`
}

// TableName BridgeOrganization's table name
func (*BridgeOrganization) TableName() string {
	return TableNameBridgeOrganization
}

// BridgeExternalAccount mapped from table <bridge_external_accounts>
type BridgeExternalAccount struct {
	BridgeExternalAccountID string `gorm:"column:bridge_external_account_id;type:varchar(255);primaryKey"`
	OrganizationID          int    `gorm:"column:organization_id;type:integer;not null"`
	BankName                string `gorm:"column:bank_name;type:varchar(255);not null"`
	AccountNumber           string `gorm:"column:account_number;type:varchar(255);not null"`
	AccountType             string `gorm:"column:account_type;type:varchar(255);not null"`
	
	// New fields for Bridge API data (will need migration to add these columns)
	CustomerID              *string   `gorm:"column:customer_id;type:varchar(255)"`
	AccountOwnerName        *string   `gorm:"column:account_owner_name;type:varchar(255)"`
	Active                  *bool     `gorm:"column:active;type:boolean"`
	Currency                *string   `gorm:"column:currency;type:varchar(10)"`
	AccountOwnerType        *string   `gorm:"column:account_owner_type;type:varchar(50)"`
	BusinessName            *string   `gorm:"column:business_name;type:varchar(255)"`
	AccountLast4            *string   `gorm:"column:account_last_4;type:varchar(10)"`
	AccountBIC              *string   `gorm:"column:account_bic;type:varchar(50)"`
	CreatedAt               time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP"`
	UpdatedAt               time.Time `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP;autoUpdateTime"`
	
	// Foreign key relationships
	Organization *StudioOrganization `gorm:"foreignKey:OrganizationID;references:ID"`
}

// TableName BridgeExternalAccount's table name
func (*BridgeExternalAccount) TableName() string {
	return TableNameBridgeExternalAccount
}

// BridgeTransfer mapped from table <bridge_transfers>
type BridgeTransfer struct {
	BridgeTransferID        string  `gorm:"column:bridge_transfer_id;type:varchar(255);primaryKey;uniqueIndex:uniq_bridge_transfer_id"`
	OrganizationID          int     `gorm:"column:organization_id;type:integer;not null;index:idx_organization_id"`
	BridgeExternalAccountID *string `gorm:"column:bridge_external_account_id;type:varchar(255);index:idx_bridge_external_account_id"`
	Chain                   string  `gorm:"column:chain;type:varchar(50);not null"`
	FromAddress             string  `gorm:"column:from_address;type:varchar(255);not null"`
	Amount                  string  `gorm:"column:amount;type:varchar(255);not null"`
	Currency                string  `gorm:"column:currency;type:varchar(10);not null"`
	Status                  BridgeTransferStatus `gorm:"column:status;type:enum('awaiting_funds','in_review','funds_received','payment_submitted','payment_processed','canceled','error','undeliverable','returned','refunded');not null"`
	DepositToAddress        string  `gorm:"column:deposit_to_address;type:varchar(255);not null"`
	CreatedAt               time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP"`
	UpdatedAt               time.Time `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP;autoUpdateTime"`
	
	// Foreign key relationships - simplified to avoid table creation order issues
	Organization *StudioOrganization `gorm:"foreignKey:OrganizationID;references:ID"`
}

// TableName BridgeTransfer's table name
func (*BridgeTransfer) TableName() string {
	return TableNameBridgeTransfer
}

// Enum types for Bridge models
type BridgeOrganizationType string

const (
	BridgeOrganizationTypeIndividual BridgeOrganizationType = "individual"
	BridgeOrganizationTypeBusiness   BridgeOrganizationType = "business"
)

type BridgeKYCStatus string

const (
	BridgeKYCStatusNotStarted   BridgeKYCStatus = "not_started"
	BridgeKYCStatusIncomplete   BridgeKYCStatus = "incomplete"
	BridgeKYCStatusAwaitingUBO  BridgeKYCStatus = "awaiting_ubo"
	BridgeKYCStatusUnderReview  BridgeKYCStatus = "under_review"
	BridgeKYCStatusApproved     BridgeKYCStatus = "approved"
	BridgeKYCStatusRejected     BridgeKYCStatus = "rejected"
	BridgeKYCStatusPaused       BridgeKYCStatus = "paused"
	BridgeKYCStatusOffboarded   BridgeKYCStatus = "offboarded"
)

type BridgeTOSStatus string

const (
	BridgeTOSStatusPending  BridgeTOSStatus = "pending"
	BridgeTOSStatusApproved BridgeTOSStatus = "approved"
)

type BridgeTransferStatus string

const (
	BridgeTransferStatusAwaitingFunds    BridgeTransferStatus = "awaiting_funds"
	BridgeTransferStatusInReview         BridgeTransferStatus = "in_review"
	BridgeTransferStatusFundsReceived    BridgeTransferStatus = "funds_received"
	BridgeTransferStatusPaymentSubmitted BridgeTransferStatus = "payment_submitted"
	BridgeTransferStatusPaymentProcessed BridgeTransferStatus = "payment_processed"
	BridgeTransferStatusCanceled         BridgeTransferStatus = "canceled"
	BridgeTransferStatusError            BridgeTransferStatus = "error"
	BridgeTransferStatusUndeliverable    BridgeTransferStatus = "undeliverable"
	BridgeTransferStatusReturned         BridgeTransferStatus = "returned"
	BridgeTransferStatusRefunded         BridgeTransferStatus = "refunded"
) 