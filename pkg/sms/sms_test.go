package sms

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
)

// TestSendByTwilio
func TestSendByTwilio(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TWILIO_ACCOUNT_SID", "TWILIO_AUTH_TOKEN"})
	err := sendByTwilio(context.Background(), "+************", "This is test content")
	if err != nil {
		t.Logf("Err: %d , %s", err.HttpStatus, err.String())
	}
}

// TestSendByTwilioVerify
func TestSendByTwilioVerify(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TWILIO_ACCOUNT_SID", "TWILIO_AUTH_TOKEN"})
	err := SendByTwilioVerify(context.Background(), "VA39b3d56dad7820b2df954f95c344ffea", "+************", "zh_CN")
	if err != nil {
		t.Logf("Err: %d , %s", err.HttpStatus, err.String())
	}
}

// TestCheckByTwilioVerify
func TestCheckByTwilioVerify(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TWILIO_ACCOUNT_SID", "TWILIO_AUTH_TOKEN"})
	err := CheckByTwilioVerify(context.Background(), "VA39b3d56dad7820b2df954f95c344ffea", "+************", "956868")
	if err != nil {
		t.Logf("Err: %d , %s", err.HttpStatus, err.String())
	}
}

// TestLocale
func TestLocale(t *testing.T) {
	locale := toTwilioVerifyLocale(context.Background(), "zh_TW")
	t.Logf("Locale: %s", locale)
}
