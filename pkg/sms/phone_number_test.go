package sms

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsTaiwanPhone(t *testing.T) {
	// true case
	res, err := IsTaiwanPhone("+886912345678")
	assert.<PERSON><PERSON>(t, err)
	assert.True(t, res)
	res, err = IsTaiwanPhone("+12025550145")
	assert.Nil(t, err)
	assert.False(t, res)

	// false case
	res, err = IsTaiwanPhone("+862041728411")
	assert.Nil(t, err)
	assert.False(t, res)

	// error case
	_, err = IsTaiwanPhone("+8869123456789")
	assert.NotNil(t, err)
	_, err = IsTaiwanPhone("+12025550")
	assert.NotNil(t, err)
}

func TestIsTaiwanOrUSPhone(t *testing.T) {
	// true case
	res, err := IsTaiwanOrUSPhone("+886912345678")
	assert.Nil(t, err)
	assert.True(t, res)
	res, err = IsTaiwanOrUSPhone("+12025550145")
	assert.Nil(t, err)
	assert.True(t, res)

	// false case
	res, err = IsTaiwanOrUSPhone("+862041728411")
	assert.Nil(t, err)
	assert.False(t, res)

	// error case
	_, err = IsTaiwanOrUSPhone("+8869123456789")
	assert.NotNil(t, err)
	_, err = IsTaiwanOrUSPhone("+12025550")
	assert.NotNil(t, err)
}

func TestIsTaiwanOrJapanPhone(t *testing.T) {
	// true case
	res, err := IsTaiwanOrJapanPhone("+886912345678")
	assert.Nil(t, err)
	assert.True(t, res)
	res, err = IsTaiwanOrJapanPhone("+819012345678")
	assert.Nil(t, err)
	assert.True(t, res)

	// false case
	res, err = IsTaiwanOrJapanPhone("+862041728411")
	assert.Nil(t, err)
	assert.False(t, res)

	// error case
	_, err = IsTaiwanOrJapanPhone("+8869123456789")
	assert.NotNil(t, err)
	_, err = IsTaiwanOrJapanPhone("+12025550")
	assert.NotNil(t, err)
}
