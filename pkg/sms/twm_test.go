package sms

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestSendShortChineseSMSByTWM(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TWM_USERNAME", "TWM_PASSWORD", "TWM_SRC_ADDR"})

	receiver := "+************"
	content := "這是來自 KryptoGO 的測試簡訊"
	err := sendSMSByTWM(context.Background(), receiver, content)
	assert.Nil(t, err)
	if err != nil {
		t.Errorf("sendSMSByTWM returned an error: %v", err.Error)
	}
}

func TestSendLongChineseSMSByTWM(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TWM_USERNAME", "TWM_PASSWORD", "TWM_SRC_ADDR"})

	receiver := "+************"
	content := "【你的NFT已經送達】歡迎進入區塊鏈的世界， \"KryptoGO Yacht Club\"  NFT  已經送到你的錢包裡了，請下載並打開 KryptoGO App 使用手機號碼：0908 590 579 登入查看。（App 下載連結：https://kryptogo.page.link/q9gD）"
	err := sendSMSByTWM(context.Background(), receiver, content)
	assert.Nil(t, err)
	if err != nil {
		t.Errorf("sendSMSByTWM returned an error: %v", err.Error)
	}
}

func TestSendShortAsciiSMSByTWM(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TWM_USERNAME", "TWM_PASSWORD", "TWM_SRC_ADDR"})

	receiver := "+************"
	content := "KryptoGO: New phone number +************ linked to your account."
	err := sendSMSByTWM(context.Background(), receiver, content)
	assert.Nil(t, err)
	if err != nil {
		t.Errorf("sendSMSByTWM returned an error: %v", err.Error)
	}
}

func TestSendLongAsciiSMSByTWM(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TWM_USERNAME", "TWM_PASSWORD", "TWM_SRC_ADDR"})

	receiver := "+************"
	content := "Your NFT has arrived. Welcome to the blockchain world. KryptoGO Yacht Club NFT is now in your wallet. Please download and open the KryptoGO App using the phone number: 0908 590 579 to view it. (App download link: https://kryptogo.page.link/q9gD)"
	err := sendSMSByTWM(context.Background(), receiver, content)
	assert.Nil(t, err)
	if err != nil {
		t.Errorf("sendSMSByTWM returned an error: %v", err.Error)
	}
}
