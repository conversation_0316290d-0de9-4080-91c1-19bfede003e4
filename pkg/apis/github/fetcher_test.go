package github

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestFetchMetadata(t *testing.T) {
	tests := []struct {
		name      string
		chain     domain.Chain
		address   string
		wantName  string
		wantError bool
		error     error
	}{
		{
			name:     "USDT on Ethereum",
			chain:    domain.Ethereum,
			address:  "******************************************",
			wantName: "Tether",
		},
		{
			name:     "USDC on Ethereum",
			chain:    domain.Ethereum,
			address:  "******************************************",
			wantName: "USD Coin",
		},
		{
			name:      "Non-existent token",
			chain:     domain.Ethereum,
			address:   "******************************************",
			wantError: true,
			error:     domain.ErrRecordNotFound,
		},
		{
			name:      "Unsupported chain",
			chain:     domain.IDToChain("unsupported-chain"),
			address:   "******************************************",
			wantError: true,
		},
	}

	fetcher := Fetcher()

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			metadata, err := fetcher.FetchMetadata(context.Background(), tt.chain, tt.address)

			if tt.wantError {
				assert.Error(t, err)
				if tt.error != nil {
					assert.ErrorIs(t, err, tt.error)
				}
				assert.Nil(t, metadata)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, metadata)
			assert.Equal(t, tt.wantName, metadata.Name)
			assert.NotEmpty(t, metadata.Symbol)
			assert.NotEmpty(t, metadata.Decimals)
			assert.NotEmpty(t, metadata.LogoUrl)
			assert.True(t, metadata.IsVerified)
		})
	}
}
