package hyperliquidapi

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
)

func TestGetClearingHouseState(t *testing.T) {
	InitDefault()

	assets, err := Get().GetAssets(context.Background(), domain.NewEvmAddress("******************************************"), []domain.Chain{domain.Hyperliquid}, []domain.AssetType{domain.AssetTypeDefi})
	if err != nil {
		t.Fatal(err)
	}

	t.Log(assets.Defi[0].UsdValue())
}
