package goplus

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestIsSpamToken(t *testing.T) {
	rateLimiter := domain.NewAllPassRateLimiter()
	InitDefault(rateLimiter)
	isSpam, err := Get().IsSpamToken(context.Background(), 137, "******************************************")
	assert.Nil(t, err)
	assert.True(t, isSpam)

	isSpam, err = Get().IsSpamToken(context.Background(), 137, "******************************************")
	assert.Nil(t, err)
	assert.False(t, isSpam)

	isSpam, err = Get().IsSpamToken(context.Background(), 42161, "******************************************")
	assert.Nil(t, err)
	assert.False(t, isSpam)
}
