package tronscanapi

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestTokens(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	address := "TPCEvgmtmgLuKzTrtm2nSjm1SCBEi5Q1wB"
	InitDefault()

	resp, err := tronscanObj.Tokens(context.Background(), address)
	assert.Nil(t, err)
	assert.Greater(t, len(resp.Data), 0)
}
