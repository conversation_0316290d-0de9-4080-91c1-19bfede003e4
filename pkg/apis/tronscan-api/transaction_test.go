package tronscanapi

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	goresty "github.com/go-resty/resty/v2"
	"github.com/kryptogo/kg-wallet-backend/pkg/code"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
)

func getNew3rdPartyRestyMock(ctrl *gomock.Controller) (*resty.MockClient, *resty.MockRequest) {
	mockClient := resty.NewMockClient(ctrl)
	mockRequest := resty.NewMockRequest(ctrl)

	mockClient.EXPECT().OnBeforeRequest(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().OnAfterResponse(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().SetBaseURL(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().SetTimeout(gomock.Any()).AnyTimes().Return(mockClient)
	mockClient.EXPECT().Clone().AnyTimes().Return(mockClient)

	mockClient.EXPECT().R().AnyTimes().Return(mockRequest)
	mockRequest.EXPECT().SetContext(gomock.Any()).AnyTimes().Return(mockRequest)
	return mockClient, mockRequest
}

func TestMockTransactionRateLimitExceeded(t *testing.T) {
	ctx := context.TODO()
	ctrl := gomock.NewController(t)

	mockClient, mockRequest := getNew3rdPartyRestyMock(ctrl)
	InitResty(mockClient)

	mockRequest.EXPECT().SetHeader("TRON-PRO-API-KEY", gomock.Any()).AnyTimes().Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("address", "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("limit", "50").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("start", "0").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("sort", "-timestamp").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("count", "true").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetResult(gomock.Any()).Return(mockRequest)
	mockRequest.EXPECT().Get("/api/transaction").Times(1).DoAndReturn(func(url string) (*resty.Response, error) {
		errorBody := []byte(`{"Error":"request rate exceeded the allowed_rps(3), and the query server is suspended for 120s"}`)

		res := &resty.Response{
			Request: &goresty.Request{
				Time: time.Now(),
			},
			RawResponse: &http.Response{
				StatusCode: 403,
			},
		}
		res.SetBody(errorBody)
		return res, nil
	})

	_, _, err := tronscanObj.Transaction(ctx, &RequestParams{
		Address: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn",
		Start:   0,
	})
	assert.NotNil(t, err)
	assert.ErrorIs(t, err, code.ErrRateLimitExceeded)
}

func TestMockTransactionSuccess(t *testing.T) {
	ctx := context.TODO()
	ctrl := gomock.NewController(t)

	mockClient, mockRequest := getNew3rdPartyRestyMock(ctrl)
	InitResty(mockClient)

	mockRequest.EXPECT().SetHeader("TRON-PRO-API-KEY", gomock.Any()).AnyTimes().Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("address", "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("limit", "50").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("start", "0").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("sort", "-timestamp").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetQueryParam("count", "true").Times(1).Return(mockRequest)
	mockRequest.EXPECT().SetResult(gomock.Any()).Times(1).DoAndReturn(func(res *TransactionResp) *resty.MockRequest {
		bodyData := testutil.ReadJsonFile("pkg/apis/tronscan-api/test/transactions.json")
		err := json.Unmarshal(bodyData, res)
		if err != nil {
			t.Fatalf("Unmarshal error: %v", err)
		}
		return mockRequest
	})
	mockRequest.EXPECT().Get("/api/transaction").Times(1).DoAndReturn(func(url string) (*resty.Response, error) {
		return &resty.Response{
			Request: &goresty.Request{
				Time: time.Now(),
			},
			RawResponse: &http.Response{
				StatusCode: 200,
			},
		}, nil
	})

	respData, _, err := tronscanObj.Transaction(ctx, &RequestParams{
		Address: "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn",
		Start:   0,
	})
	assert.Nil(t, err)
	assert.NotNil(t, respData)
	assert.Equal(t, "TBLDEi1nrckQb89QuRJ5zeyCEHRN47RTpn", respData.Data[0].OwnerAddress)
	assert.Equal(t, "100000000", respData.Data[0].Amount)
}
