// Code generated by MockGen. DO NOT EDIT.
// Source: common.go
//
// Generated by this command:
//
//	mockgen -source=common.go -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/tronscan-api -destination=common_mock.go -package=tronscanapi ITronscan
//

// Package tronscanapi is a generated GoMock package.
package tronscanapi

import (
	context "context"
	reflect "reflect"
	time "time"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockITronscan is a mock of ITronscan interface.
type MockITronscan struct {
	ctrl     *gomock.Controller
	recorder *MockITronscanMockRecorder
}

// MockITronscanMockRecorder is the mock recorder for MockITronscan.
type MockITronscanMockRecorder struct {
	mock *MockITronscan
}

// NewMockITronscan creates a new mock instance.
func NewMockITronscan(ctrl *gomock.Controller) *MockITronscan {
	mock := &MockITronscan{ctrl: ctrl}
	mock.recorder = &MockITronscanMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockITronscan) EXPECT() *MockITronscanMockRecorder {
	return m.recorder
}

// GetAssets mocks base method.
func (m *MockITronscan) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssets", ctx, address, chains, types)
	ret0, _ := ret[0].(*domain.AggregatedAssets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssets indicates an expected call of GetAssets.
func (mr *MockITronscanMockRecorder) GetAssets(ctx, address, chains, types any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssets", reflect.TypeOf((*MockITronscan)(nil).GetAssets), ctx, address, chains, types)
}

// SupportedChains mocks base method.
func (m *MockITronscan) SupportedChains() []domain.Chain {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedChains")
	ret0, _ := ret[0].([]domain.Chain)
	return ret0
}

// SupportedChains indicates an expected call of SupportedChains.
func (mr *MockITronscanMockRecorder) SupportedChains() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedChains", reflect.TypeOf((*MockITronscan)(nil).SupportedChains))
}

// SupportedTypes mocks base method.
func (m *MockITronscan) SupportedTypes() []domain.AssetType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedTypes")
	ret0, _ := ret[0].([]domain.AssetType)
	return ret0
}

// SupportedTypes indicates an expected call of SupportedTypes.
func (mr *MockITronscanMockRecorder) SupportedTypes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedTypes", reflect.TypeOf((*MockITronscan)(nil).SupportedTypes))
}

// Tokens mocks base method.
func (m *MockITronscan) Tokens(ctx context.Context, address string) (*TokensResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Tokens", ctx, address)
	ret0, _ := ret[0].(*TokensResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Tokens indicates an expected call of Tokens.
func (mr *MockITronscanMockRecorder) Tokens(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Tokens", reflect.TypeOf((*MockITronscan)(nil).Tokens), ctx, address)
}

// Transaction mocks base method.
func (m *MockITronscan) Transaction(ctx context.Context, param *RequestParams) (*TransactionResp, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transaction", ctx, param)
	ret0, _ := ret[0].(*TransactionResp)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Transaction indicates an expected call of Transaction.
func (mr *MockITronscanMockRecorder) Transaction(ctx, param any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transaction", reflect.TypeOf((*MockITronscan)(nil).Transaction), ctx, param)
}

// TransactionDetail mocks base method.
func (m *MockITronscan) TransactionDetail(ctx context.Context, txHash string) (*TransactionInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TransactionDetail", ctx, txHash)
	ret0, _ := ret[0].(*TransactionInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TransactionDetail indicates an expected call of TransactionDetail.
func (mr *MockITronscanMockRecorder) TransactionDetail(ctx, txHash any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TransactionDetail", reflect.TypeOf((*MockITronscan)(nil).TransactionDetail), ctx, txHash)
}

// Trc20Transfer mocks base method.
func (m *MockITronscan) Trc20Transfer(ctx context.Context, param *RequestParams) (*Trc20TransferResp, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Trc20Transfer", ctx, param)
	ret0, _ := ret[0].(*Trc20TransferResp)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Trc20Transfer indicates an expected call of Trc20Transfer.
func (mr *MockITronscanMockRecorder) Trc20Transfer(ctx, param any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Trc20Transfer", reflect.TypeOf((*MockITronscan)(nil).Trc20Transfer), ctx, param)
}
