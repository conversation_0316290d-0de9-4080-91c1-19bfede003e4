package tronscanapi

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTransactionDetail(t *testing.T) {
	InitDefault()

	txHash := "e3c4328be9859c50b5d59ba8c83eb1963b1edda95484b98d36ab1cbbd8d66c8e"
	info, err := tronscanObj.TransactionDetail(context.Background(), txHash)
	t.Logf("err: %v", err)
	assert.Nil(t, err)
	t.Logf("%+v", info)

	assert.Equal(t, int64(416000), info.NetFee)
	assert.Equal(t, 1, len(info.InternalTransactions))
	assert.Equal(t, "TDSEUjDdfKtEAoWL387hi433T4Fanwip9Z", info.InternalTransactions[0].From)
	assert.Equal(t, "TRzh2k4psPBbQrK4Xv3iWSF4KZcU9QLgjW", info.InternalTransactions[0].To)
	assert.Equal(t, "27151381", info.InternalTransactions[0].Value)
	assert.Equal(t, "_", info.InternalTransactions[0].ContractAddress)
}
