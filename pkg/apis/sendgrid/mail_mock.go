// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid (interfaces: EmailClient)
//
// Generated by this command:
//
//	mockgen -package=sendgrid -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/sendgrid -destination=mail_mock.go . EmailClient
//

// Package sendgrid is a generated GoMock package.
package sendgrid

import (
	context "context"
	reflect "reflect"

	rest "github.com/sendgrid/rest"
	gomock "go.uber.org/mock/gomock"
)

// MockEmailClient is a mock of EmailClient interface.
type MockEmailClient struct {
	ctrl     *gomock.Controller
	recorder *MockEmailClientMockRecorder
}

// MockEmailClientMockRecorder is the mock recorder for MockEmailClient.
type MockEmailClientMockRecorder struct {
	mock *MockEmailClient
}

// NewMockEmailClient creates a new mock instance.
func NewMockEmailClient(ctrl *gomock.Controller) *MockEmailClient {
	mock := &MockEmailClient{ctrl: ctrl}
	mock.recorder = &MockEmailClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEmailClient) EXPECT() *MockEmailClientMockRecorder {
	return m.recorder
}

// Send mocks base method.
func (m *MockEmailClient) Send(arg0 context.Context, arg1, arg2, arg3 string) (*rest.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*rest.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Send indicates an expected call of Send.
func (mr *MockEmailClientMockRecorder) Send(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockEmailClient)(nil).Send), arg0, arg1, arg2, arg3)
}

// SendEmail mocks base method.
func (m *MockEmailClient) SendEmail(arg0 context.Context, arg1, arg2 string, arg3 EmailType, arg4 any) (*rest.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEmail", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*rest.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendEmail indicates an expected call of SendEmail.
func (mr *MockEmailClientMockRecorder) SendEmail(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEmail", reflect.TypeOf((*MockEmailClient)(nil).SendEmail), arg0, arg1, arg2, arg3, arg4)
}

// SendEmailWithSubject mocks base method.
func (m *MockEmailClient) SendEmailWithSubject(arg0 context.Context, arg1, arg2 string, arg3 EmailType, arg4 any) (*rest.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEmailWithSubject", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*rest.Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendEmailWithSubject indicates an expected call of SendEmailWithSubject.
func (mr *MockEmailClientMockRecorder) SendEmailWithSubject(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEmailWithSubject", reflect.TypeOf((*MockEmailClient)(nil).SendEmailWithSubject), arg0, arg1, arg2, arg3, arg4)
}
