package sendgrid

import (
	"context"
	"net/http"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestSendVerificationCodeEmail(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	to := "<EMAIL>"
	clientID := "20991a3ae83233d6de85d62906d71fd3"
	sendgridClient := NewClient()
	ctx := context.Background()
	resp, err := sendgridClient.SendEmail(ctx, to, clientID, EmailTypeVerificationCode, map[string]string{
		"code":           "123456",
		"expires_at":     "2021-01-01 00:00:00",
		"valid_duration": "5",
	})
	assert.Nil(t, err)
	assert.Equal(t, http.StatusAccepted, resp.StatusCode)
}

func TestSendBindEmail(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	to := "<EMAIL>"
	clientID := "20991a3ae83233d6de85d62906d71fd3"
	sendgridClient := NewClient()
	ctx := context.Background()
	resp, err := sendgridClient.SendEmail(ctx, to, clientID, EmailTypeBind, map[string]string{
		"email": to,
	})
	assert.Nil(t, err)
	assert.Equal(t, http.StatusAccepted, resp.StatusCode)
}

func TestSendResetPasswordEmail(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	to := "<EMAIL>"
	clientID := "20991a3ae83233d6de85d62906d71fd3"
	sendgridClient := NewClient()
	ctx := context.Background()
	resp, err := sendgridClient.SendEmail(ctx, to, clientID, EmailTypeUnbind, map[string]string{
		"email": to,
	})
	assert.Nil(t, err)
	assert.Equal(t, http.StatusAccepted, resp.StatusCode)
}
