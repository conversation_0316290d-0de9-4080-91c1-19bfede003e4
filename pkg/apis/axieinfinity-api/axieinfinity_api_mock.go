// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/axieinfinityapi (interfaces: IAxieInfinity)
//
// Generated by this command:
//
//	mockgen -package=axieinfinityapi -destination=axieinfinity_api_mock.go . IAxieInfinity
//

// Package axieinfinityapi is a generated GoMock package.
package axieinfinityapi

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIAxieInfinity is a mock of IAxieInfinity interface.
type MockIAxieInfinity struct {
	ctrl     *gomock.Controller
	recorder *MockIAxieInfinityMockRecorder
}

// MockIAxieInfinityMockRecorder is the mock recorder for MockIAxieInfinity.
type MockIAxieInfinityMockRecorder struct {
	mock *MockIAxieInfinity
}

// NewMockIAxieInfinity creates a new mock instance.
func NewMockIAxieInfinity(ctrl *gomock.Controller) *MockIAxieInfinity {
	mock := &MockIAxieInfinity{ctrl: ctrl}
	mock.recorder = &MockIAxieInfinityMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAxieInfinity) EXPECT() *MockIAxieInfinityMockRecorder {
	return m.recorder
}

// GetAccessories mocks base method.
func (m *MockIAxieInfinity) GetAccessories(arg0 context.Context, arg1 string, arg2, arg3 int) (*GetAccessoriesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccessories", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*GetAccessoriesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccessories indicates an expected call of GetAccessories.
func (mr *MockIAxieInfinityMockRecorder) GetAccessories(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccessories", reflect.TypeOf((*MockIAxieInfinity)(nil).GetAccessories), arg0, arg1, arg2, arg3)
}

// GetAccessoriesMarketplace mocks base method.
func (m *MockIAxieInfinity) GetAccessoriesMarketplace(arg0 context.Context) (*GetAccessoriesMarketplaceResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccessoriesMarketplace", arg0)
	ret0, _ := ret[0].(*GetAccessoriesMarketplaceResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccessoriesMarketplace indicates an expected call of GetAccessoriesMarketplace.
func (mr *MockIAxieInfinityMockRecorder) GetAccessoriesMarketplace(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccessoriesMarketplace", reflect.TypeOf((*MockIAxieInfinity)(nil).GetAccessoriesMarketplace), arg0)
}

// GetAxieBriefList mocks base method.
func (m *MockIAxieInfinity) GetAxieBriefList(arg0 context.Context, arg1 string, arg2 int) (*GetAxieBriefListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAxieBriefList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetAxieBriefListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAxieBriefList indicates an expected call of GetAxieBriefList.
func (mr *MockIAxieInfinityMockRecorder) GetAxieBriefList(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAxieBriefList", reflect.TypeOf((*MockIAxieInfinity)(nil).GetAxieBriefList), arg0, arg1, arg2)
}

// GetAxieDetail mocks base method.
func (m *MockIAxieInfinity) GetAxieDetail(arg0 context.Context, arg1 string) (*GetAxieDetailResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAxieDetail", arg0, arg1)
	ret0, _ := ret[0].(*GetAxieDetailResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAxieDetail indicates an expected call of GetAxieDetail.
func (mr *MockIAxieInfinityMockRecorder) GetAxieDetail(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAxieDetail", reflect.TypeOf((*MockIAxieInfinity)(nil).GetAxieDetail), arg0, arg1)
}

// GetBundleList mocks base method.
func (m *MockIAxieInfinity) GetBundleList(arg0 context.Context) (*GetBundleListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBundleList", arg0)
	ret0, _ := ret[0].(*GetBundleListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBundleList indicates an expected call of GetBundleList.
func (mr *MockIAxieInfinityMockRecorder) GetBundleList(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBundleList", reflect.TypeOf((*MockIAxieInfinity)(nil).GetBundleList), arg0)
}

// GetCharms mocks base method.
func (m *MockIAxieInfinity) GetCharms(arg0 context.Context) (*GetCharmsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCharms", arg0)
	ret0, _ := ret[0].(*GetCharmsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCharms indicates an expected call of GetCharms.
func (mr *MockIAxieInfinityMockRecorder) GetCharms(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCharms", reflect.TypeOf((*MockIAxieInfinity)(nil).GetCharms), arg0)
}

// GetItemBriefList mocks base method.
func (m *MockIAxieInfinity) GetItemBriefList(arg0 context.Context, arg1 string, arg2 int) (*GetItemBriefListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetItemBriefList", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetItemBriefListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItemBriefList indicates an expected call of GetItemBriefList.
func (mr *MockIAxieInfinityMockRecorder) GetItemBriefList(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItemBriefList", reflect.TypeOf((*MockIAxieInfinity)(nil).GetItemBriefList), arg0, arg1, arg2)
}

// GetLandsGrid mocks base method.
func (m *MockIAxieInfinity) GetLandsGrid(arg0 context.Context, arg1 string, arg2 int) (*GetLandsGridResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLandsGrid", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetLandsGridResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLandsGrid indicates an expected call of GetLandsGrid.
func (mr *MockIAxieInfinityMockRecorder) GetLandsGrid(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLandsGrid", reflect.TypeOf((*MockIAxieInfinity)(nil).GetLandsGrid), arg0, arg1, arg2)
}

// GetRunes mocks base method.
func (m *MockIAxieInfinity) GetRunes(arg0 context.Context) (*GetRunesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRunes", arg0)
	ret0, _ := ret[0].(*GetRunesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRunes indicates an expected call of GetRunes.
func (mr *MockIAxieInfinityMockRecorder) GetRunes(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRunes", reflect.TypeOf((*MockIAxieInfinity)(nil).GetRunes), arg0)
}
