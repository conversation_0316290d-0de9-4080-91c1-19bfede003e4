package axieinfinityapi

import (
	"context"
	"fmt"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetAxieBriefList(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()
	ctx := context.Background()
	address := "******************************************"

	resp, err := axieObj.GetAxieBriefList(ctx, address, 0)
	fmt.Println(resp, err)
	assert.Nil(t, err)
	assert.NotNil(t, resp)
}
