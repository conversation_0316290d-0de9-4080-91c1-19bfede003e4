package metamaskapi_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/kryptogo/kg-wallet-backend/domain"
	metamaskapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/metamask-api"
)

func TestMetaMaskAPI_GetAssets(t *testing.T) {
	s := assert.New(t)
	metamaskapi.InitDefault()

	asset, err := metamaskapi.Get().GetAssets(context.Background(),
		domain.NewEvmAddress("******************************************"),
		[]domain.Chain{
			domain.Ethereum,
			domain.Polygon,
			domain.Arbitrum,
		}, []domain.AssetType{
			domain.AssetTypeDefi,
		})
	s.NoError(err)

	s.Len(asset.Defi, 1)
	s.Equal(domain.Polygon, asset.Defi[0].Chain())
	s.Equal("https://aave.com/", asset.Defi[0].SiteUrl)
	s.Equal("aave-v3 WPOL", asset.Defi[0].Name())

	s.Len(asset.Defi[0].SupplyTokens, 1)
	s.Equal("******************************************", asset.Defi[0].SupplyTokens[0].ID)
	s.Equal("Wrapped Polygon Ecosystem Token", asset.Defi[0].SupplyTokens[0].Name)
	s.Equal("WPOL", asset.Defi[0].SupplyTokens[0].Symbol)
	s.Equal("https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png", *asset.Defi[0].SupplyTokens[0].LogoUrl)
	s.NotEqual("0", asset.Defi[0].SupplyTokens[0].Amount.String())
	s.NotEmpty(asset.Defi[0].SupplyTokens[0].Amount.String())
	s.NotEmpty(asset.Defi[0].SupplyTokens[0].Price)

	s.Len(asset.Defi[0].RewardTokens, 0)

	s.Len(asset.Defi[0].BorrowTokens, 2)
	s.Equal("******************************************", asset.Defi[0].BorrowTokens[0].ID)
	s.Equal("(PoS) Tether USD", asset.Defi[0].BorrowTokens[0].Name)
	s.Equal("USDT", asset.Defi[0].BorrowTokens[0].Symbol)
	s.Equal("https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png", *asset.Defi[0].BorrowTokens[0].LogoUrl)
	s.NotEqual("0", asset.Defi[0].BorrowTokens[0].Amount.String())
	s.NotEmpty(asset.Defi[0].BorrowTokens[0].Amount.String())
	s.NotEmpty(asset.Defi[0].BorrowTokens[0].Price)
	s.Equal("******************************************", asset.Defi[0].BorrowTokens[1].ID)
	s.Equal("USD Coin", asset.Defi[0].BorrowTokens[1].Name)
	s.Equal("USDC", asset.Defi[0].BorrowTokens[1].Symbol)
	s.Equal("https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/assets/******************************************/logo.png", *asset.Defi[0].BorrowTokens[1].LogoUrl)
	s.NotEqual("0", asset.Defi[0].BorrowTokens[1].Amount.String())
	s.NotEmpty(asset.Defi[0].BorrowTokens[1].Amount.String())
	s.NotEmpty(asset.Defi[0].BorrowTokens[1].Price)
}
