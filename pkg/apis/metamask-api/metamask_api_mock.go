// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/metamask-api (interfaces: IMetaMask)
//
// Generated by this command:
//
//	mockgen -package=metamaskapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/metamask-api -destination=metamask_api_mock.go . IMetaMask
//

// Package metamaskapi is a generated GoMock package.
package metamaskapi

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockIMetaMask is a mock of IMetaMask interface.
type MockIMetaMask struct {
	ctrl     *gomock.Controller
	recorder *MockIMetaMaskMockRecorder
}

// MockIMetaMaskMockRecorder is the mock recorder for MockIMetaMask.
type MockIMetaMaskMockRecorder struct {
	mock *MockIMetaMask
}

// NewMockIMetaMask creates a new mock instance.
func NewMockIMetaMask(ctrl *gomock.Controller) *MockIMetaMask {
	mock := &MockIMetaMask{ctrl: ctrl}
	mock.recorder = &MockIMetaMaskMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIMetaMask) EXPECT() *MockIMetaMaskMockRecorder {
	return m.recorder
}

// GetAssets mocks base method.
func (m *MockIMetaMask) GetAssets(arg0 context.Context, arg1 domain.Address, arg2 []domain.Chain, arg3 []domain.AssetType) (*domain.AggregatedAssets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssets", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*domain.AggregatedAssets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssets indicates an expected call of GetAssets.
func (mr *MockIMetaMaskMockRecorder) GetAssets(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssets", reflect.TypeOf((*MockIMetaMask)(nil).GetAssets), arg0, arg1, arg2, arg3)
}

// Positions mocks base method.
func (m *MockIMetaMask) Positions(arg0 context.Context, arg1 domain.Address, arg2 []domain.Chain) (*PositionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Positions", arg0, arg1, arg2)
	ret0, _ := ret[0].(*PositionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Positions indicates an expected call of Positions.
func (mr *MockIMetaMaskMockRecorder) Positions(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Positions", reflect.TypeOf((*MockIMetaMask)(nil).Positions), arg0, arg1, arg2)
}

// Prices mocks base method.
func (m *MockIMetaMask) Prices(arg0 context.Context, arg1 domain.Chain, arg2 []domain.Address) (map[domain.Address]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Prices", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[domain.Address]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Prices indicates an expected call of Prices.
func (mr *MockIMetaMaskMockRecorder) Prices(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Prices", reflect.TypeOf((*MockIMetaMask)(nil).Prices), arg0, arg1, arg2)
}

// SupportedChains mocks base method.
func (m *MockIMetaMask) SupportedChains() []domain.Chain {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedChains")
	ret0, _ := ret[0].([]domain.Chain)
	return ret0
}

// SupportedChains indicates an expected call of SupportedChains.
func (mr *MockIMetaMaskMockRecorder) SupportedChains() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedChains", reflect.TypeOf((*MockIMetaMask)(nil).SupportedChains))
}

// SupportedTypes mocks base method.
func (m *MockIMetaMask) SupportedTypes() []domain.AssetType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedTypes")
	ret0, _ := ret[0].([]domain.AssetType)
	return ret0
}

// SupportedTypes indicates an expected call of SupportedTypes.
func (mr *MockIMetaMaskMockRecorder) SupportedTypes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedTypes", reflect.TypeOf((*MockIMetaMask)(nil).SupportedTypes))
}
