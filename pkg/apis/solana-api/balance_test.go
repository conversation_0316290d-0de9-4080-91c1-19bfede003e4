package solanaapi

import (
	"context"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestGetSolBalance(t *testing.T) {
	InitDefault()
	balance, err := solanaObj.GetSolBalance(context.Background(), "HEL1USMZKAL2odpNBj2oCjffnFGaYwmbGmyewGv1e2TU")
	if err != nil {
		t.Fatal(err)
	}
	assert.True(t, balance.GreaterThan(decimal.Zero))
	t.Log(balance)
}

func TestGetTokenAccountBalance(t *testing.T) {
	InitDefault()
	amount, err := solanaObj.GetTokenAccountBalance(context.Background(), "DAv5P8ueL3FW5RdZrLVpeEqQRPbS7AW1U3L3yeTtzQvZ")
	if err != nil {
		t.Fatal(err)
	}
	assert.Nil(t, err)
	assert.Equal(t, "0", amount.String())
}
