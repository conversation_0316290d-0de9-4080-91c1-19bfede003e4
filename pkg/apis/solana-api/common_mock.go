// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api (interfaces: ISolana)
//
// Generated by this command:
//
//	mockgen -package=solanaapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/solana-api -destination=common_mock.go . ISolana
//

// Package solanaapi is a generated GoMock package.
package solanaapi

import (
	context "context"
	reflect "reflect"
	"math/big"

	decimal "github.com/shopspring/decimal"
	gomock "go.uber.org/mock/gomock"
)

// MockISolana is a mock of ISolana interface.
type MockISolana struct {
	ctrl     *gomock.Controller
	recorder *MockISolanaMockRecorder
}

// MockISolanaMockRecorder is the mock recorder for MockISolana.
type MockISolanaMockRecorder struct {
	mock *MockISolana
}

// NewMockISolana creates a new mock instance.
func NewMockISolana(ctrl *gomock.Controller) *MockISolana {
	mock := &MockISolana{ctrl: ctrl}
	mock.recorder = &MockISolanaMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISolana) EXPECT() *MockISolanaMockRecorder {
	return m.recorder
}

// GetLatestBlockNumber mocks base method.
func (m *MockISolana) GetLatestBlockNumber(arg0 context.Context) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestBlockNumber", arg0)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestBlockNumber indicates an expected call of GetLatestBlockNumber.
func (mr *MockISolanaMockRecorder) GetLatestBlockNumber(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestBlockNumber", reflect.TypeOf((*MockISolana)(nil).GetLatestBlockNumber), arg0)
}

// GetSignaturesForAddress mocks base method.
func (m *MockISolana) GetSignaturesForAddress(arg0 context.Context, arg1, arg2 string) (*GetSignaturesForAddressResp, *ResponseMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSignaturesForAddress", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetSignaturesForAddressResp)
	ret1, _ := ret[1].(*ResponseMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetSignaturesForAddress indicates an expected call of GetSignaturesForAddress.
func (mr *MockISolanaMockRecorder) GetSignaturesForAddress(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignaturesForAddress", reflect.TypeOf((*MockISolana)(nil).GetSignaturesForAddress), arg0, arg1, arg2)
}

// GetSolBalance mocks base method.
func (m *MockISolana) GetSolBalance(arg0 context.Context, arg1 string) (decimal.Decimal, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSolBalance", arg0, arg1)
	ret0, _ := ret[0].(decimal.Decimal)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSolBalance indicates an expected call of GetSolBalance.
func (mr *MockISolanaMockRecorder) GetSolBalance(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSolBalance", reflect.TypeOf((*MockISolana)(nil).GetSolBalance), arg0, arg1)
}

// GetTokenAccountBalance mocks base method.
func (m *MockISolana) GetTokenAccountBalance(arg0 context.Context, arg1 string) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenAccountBalance", arg0, arg1)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenAccountBalance indicates an expected call of GetTokenAccountBalance.
func (mr *MockISolanaMockRecorder) GetTokenAccountBalance(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenAccountBalance", reflect.TypeOf((*MockISolana)(nil).GetTokenAccountBalance), arg0, arg1)
}

// GetTokenAccountsByOwner mocks base method.
func (m *MockISolana) GetTokenAccountsByOwner(arg0 context.Context, arg1 string) (*GetTokenAccountsByOwnerResp, *ResponseMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenAccountsByOwner", arg0, arg1)
	ret0, _ := ret[0].(*GetTokenAccountsByOwnerResp)
	ret1, _ := ret[1].(*ResponseMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetTokenAccountsByOwner indicates an expected call of GetTokenAccountsByOwner.
func (mr *MockISolanaMockRecorder) GetTokenAccountsByOwner(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenAccountsByOwner", reflect.TypeOf((*MockISolana)(nil).GetTokenAccountsByOwner), arg0, arg1)
}

// GetTransaction mocks base method.
func (m *MockISolana) GetTransaction(arg0 context.Context, arg1 string) (*GetTransactionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransaction", arg0, arg1)
	ret0, _ := ret[0].(*GetTransactionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransaction indicates an expected call of GetTransaction.
func (mr *MockISolanaMockRecorder) GetTransaction(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransaction", reflect.TypeOf((*MockISolana)(nil).GetTransaction), arg0, arg1)
}
