package okxapi

import (
	"context"
	"fmt"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetApproveTransaction(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	params := &ApproveTransactionParams{
		Chain:         domain.Ethereum,
		TokenID:       "******************************************",
		ApproveAmount: "1000000",
	}

	resp, err := Get().GetApproveTransaction(ctx, params)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)
	t.Logf("resp data: %+v", resp.Data)
}

func TestGetQuoteSwap(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	params := &SwapParams{
		Chain:               domain.Ethereum,
		FromTokenAddress:    domain.Ethereum.MainToken().ID(),             // ETH
		ToTokenAddress:      "******************************************", // USDC
		Amount:              "1000000000000000000",                        // 1 ETH
		UserWalletAddress:   "******************************************",
		SwapReceiverAddress: "******************************************",
	}

	resp, err := Get().GetQuoteSwap(ctx, params)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)
	t.Logf("resp data: %+v", resp.Data)
}

func TestGetBuildSwapTx(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	params := &SwapParams{
		Chain:               domain.Ethereum,
		FromTokenAddress:    domain.Ethereum.MainToken().ID(),
		ToTokenAddress:      "******************************************",
		Amount:              "1000000000000000000",
		UserWalletAddress:   "******************************************",
		SwapReceiverAddress: "******************************************",
	}

	resp, err := Get().GetBuildSwapTx(ctx, params)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)
	t.Logf("resp data: %+v", resp.Data)
}

func TestGetQuoteAndBuildBridgeTx(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	params := &BridgeParams{
		Amount:            "1000000000",
		FromTokenAddress:  "******************************************",
		FromChain:         domain.Ethereum,
		ToTokenAddress:    domain.Arbitrum.MainToken().ID(),
		ToChain:           domain.Arbitrum,
		UserWalletAddress: "******************************************",
		ReceiveAddress:    "******************************************",
	}

	resp, err := Get().GetQuoteBridge(ctx, params)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)
	t.Logf("resp data 1: %+v", resp.Data)

	resp2, err := Get().GetBuildBridgeTx(ctx, params)
	assert.NoError(t, err)
	assert.NotNil(t, resp2)
	assert.Equal(t, "0", resp2.Code)
	assert.NotEmpty(t, resp2.Data)
	t.Logf("resp data 2: %+v", resp2.Data)
}

func TestGetSupportedChains(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	// Test getting all supported chains
	chains, err := Get().GetSupportedChains(ctx, "")
	assert.NoError(t, err)
	assert.NotEmpty(t, chains)
	t.Logf("all chains: %+v", chains)

	// Test getting specific chain info
	chainId := fmt.Sprintf("%d", domain.Ethereum.Number())
	chains, err = Get().GetSupportedChains(ctx, chainId)
	assert.NoError(t, err)
	assert.NotEmpty(t, chains)
	t.Logf("first chain info: %+v", chains[0])
}

func TestGetTransactionStatus(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	// Use a known transaction hash from a previous cross-chain transfer
	hash := "0x5c5d56c6b604f4c489921486caae4c625c821d5a48ac57bfd7a8810aa493cc17"

	resp, err := Get().GetTransactionStatus(ctx, hash, "10")
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)
	t.Logf("Length of resp data: %d", len(resp.Data))

	// Verify the response contains expected fields
	txStatus := resp.Data[0]
	assert.NotEmpty(t, txStatus.FromChainId)
	assert.NotEmpty(t, txStatus.ToChainId)
	assert.NotEmpty(t, txStatus.Status)

	t.Logf("Transaction status: %+v", txStatus)
}

func TestGetTokenPriceEthNative(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	// Test getting token price for USDT on Ethereum
	requests := []TokenPriceRequest{
		{
			ChainIndex:           fmt.Sprintf("%d", domain.Ethereum.Number()),
			TokenContractAddress: okxEthNativeTokenAddress,
		},
	}

	resp, err := Get().GetTokenPrice(ctx, requests)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)

	// Verify the response contains expected fields
	priceData := resp.Data[0]
	assert.NotEmpty(t, priceData.ChainIndex)
	assert.NotEmpty(t, priceData.TokenContractAddress)
	assert.NotEmpty(t, priceData.Time)
	assert.NotEmpty(t, priceData.Price)

	t.Logf("Token price data: %+v", priceData)
}

func TestGetTokenPriceEthErc20(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	// Test getting token price for USDT on Ethereum
	requests := []TokenPriceRequest{
		{
			ChainIndex:           fmt.Sprintf("%d", domain.Ethereum.Number()),
			TokenContractAddress: domain.Ethereum.DefaultTokens()[1].ID(),
		},
	}

	resp, err := Get().GetTokenPrice(ctx, requests)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)

	// Verify the response contains expected fields
	priceData := resp.Data[0]
	assert.NotEmpty(t, priceData.ChainIndex)
	assert.NotEmpty(t, priceData.TokenContractAddress)
	assert.NotEmpty(t, priceData.Time)
	assert.NotEmpty(t, priceData.Price)

	t.Logf("Token price data: %+v", priceData)
}

// Potential improvement: loop through all evm chains and pack them into single test
func TestGetTokenPriceBaseNative(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	// Test getting token price for USDT on Ethereum
	requests := []TokenPriceRequest{
		{
			ChainIndex:           fmt.Sprintf("%d", domain.BaseChain.Number()),
			TokenContractAddress: okxEthNativeTokenAddress,
		},
	}

	resp, err := Get().GetTokenPrice(ctx, requests)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)

	// Verify the response contains expected fields
	priceData := resp.Data[0]
	assert.NotEmpty(t, priceData.ChainIndex)
	assert.NotEmpty(t, priceData.TokenContractAddress)
	assert.NotEmpty(t, priceData.Time)
	assert.NotEmpty(t, priceData.Price)

	t.Logf("Token price data: %+v", priceData)
}

func TestGetTokenPriceSolana(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	// Test getting token price for SOL on Solana
	requests := []TokenPriceRequest{
		{
			ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(domain.Solana)),
			TokenContractAddress: okxSolanaNativeToken,
		},
	}

	resp, err := Get().GetTokenPrice(ctx, requests)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)

	// Verify the response contains expected fields
	priceData := resp.Data[0]
	assert.NotEmpty(t, priceData.ChainIndex)
	assert.NotEmpty(t, priceData.TokenContractAddress)
	assert.NotEmpty(t, priceData.Time)
	assert.NotEmpty(t, priceData.Price)

	t.Logf("Token price data: %+v", priceData)
}

func TestGetTokenPriceSui(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	requests := []TokenPriceRequest{
		{
			ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(domain.Sui)),
			TokenContractAddress: "0x2::sui::SUI", // native sui address
		},
	}

	resp, err := Get().GetTokenPrice(ctx, requests)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)

	priceData := resp.Data[0]
	assert.NotEmpty(t, priceData.ChainIndex)
	assert.NotEmpty(t, priceData.TokenContractAddress)
	assert.NotEmpty(t, priceData.Time)
	assert.NotEmpty(t, priceData.Price)

	t.Logf("Token price data: %+v", priceData)
}

func TestGetTokenPriceTrx(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	requests := []TokenPriceRequest{
		{
			ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(domain.Tron)),
			TokenContractAddress: "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", // TRX USDT
		},
	}

	resp, err := Get().GetTokenPrice(ctx, requests)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)

	priceData := resp.Data[0]
	assert.NotEmpty(t, priceData.ChainIndex)
	assert.NotEmpty(t, priceData.TokenContractAddress)
	assert.NotEmpty(t, priceData.Time)
	assert.NotEmpty(t, priceData.Price)

	t.Logf("Token price data: %+v", priceData)
}

func TestGetTokenPriceTon(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	requests := []TokenPriceRequest{
		{
			ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(domain.Ton)),
			TokenContractAddress: "EQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM9c", // ton address
		},
	}

	resp, err := Get().GetTokenPrice(ctx, requests)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)

	priceData := resp.Data[0]
	assert.NotEmpty(t, priceData.ChainIndex)
	assert.NotEmpty(t, priceData.TokenContractAddress)
	assert.NotEmpty(t, priceData.Time)
	assert.NotEmpty(t, priceData.Price)

	t.Logf("Token price data: %+v", priceData)
}

func TestGetTokenPriceMultiple(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	// Define the chains we want to test
	supportedChains := []domain.Chain{
		domain.Ethereum,
		domain.Polygon,
		domain.BNBChain,
		domain.Solana,
		domain.Arbitrum,
		domain.Optimism,
		domain.BaseChain,
		domain.Sui,
		domain.Tron,
		domain.Ton,
	}

	// Collect all token requests
	var requests []TokenPriceRequest

	// For each chain, add all default tokens
	for _, chain := range supportedChains {
		// Add native token
		if chain == domain.Solana {
			requests = append(requests, TokenPriceRequest{
				ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(chain)),
				TokenContractAddress: okxSolanaNativeToken,
			})
		} else if chain == domain.Sui {
			requests = append(requests, TokenPriceRequest{
				ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(chain)),
				TokenContractAddress: "0x2::sui::SUI",
			})
		} else if chain == domain.Tron {
			requests = append(requests, TokenPriceRequest{
				ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(chain)),
				TokenContractAddress: "T9yD14Nj9j7xAB4dbGeiX9h8unkKHxuWwb",
			})
		} else if chain == domain.Ton {
			requests = append(requests, TokenPriceRequest{
				ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(chain)),
				TokenContractAddress: "EQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAM9c",
			})
		} else if chain.IsEVM() {
			// For EVM chains, use the special native token address format
			requests = append(requests, TokenPriceRequest{
				ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(chain)),
				TokenContractAddress: okxEthNativeTokenAddress,
			})
		}

		// Add other default tokens
		for _, token := range chain.DefaultTokens() {
			// Skip native token as we've already added it with special handling
			if token.IsMainToken() {
				continue
			}

			// Use the token ID directly
			requests = append(requests, TokenPriceRequest{
				ChainIndex:           fmt.Sprintf("%d", GetOKXChainIndex(chain)),
				TokenContractAddress: token.ID(),
			})
		}
	}

	t.Logf("Testing all %d tokens across %d chains in a single request", len(requests), len(supportedChains))

	// Send all tokens in a single request to test maximum capacity
	resp, err := Get().GetTokenPrice(ctx, requests)

	// Check the response
	if err != nil {
		t.Logf("Error with batch size %d: %v", len(requests), err)
		t.Logf("Maximum batch size appears to be less than %d", len(requests))
		t.FailNow()
	}

	// Verify the response
	assert.NotNil(t, resp)
	assert.Equal(t, "0", resp.Code)
	assert.NotEmpty(t, resp.Data)
	assert.Equal(t, len(requests), len(resp.Data), "Response data length should match request size")

	// Verify all responses have expected fields
	for j, priceData := range resp.Data {
		assert.NotEmpty(t, priceData.ChainIndex)
		assert.NotEmpty(t, priceData.TokenContractAddress)
		assert.NotEmpty(t, priceData.Time)
		assert.NotEmpty(t, priceData.Price, "Price should not be empty for token %s on chain %s",
			requests[j].TokenContractAddress, requests[j].ChainIndex)
	}

	t.Logf("Successfully processed all %d tokens in a single request", len(resp.Data))

	// Print some example data from different chains for verification
	chainIndices := make(map[string]int)
	for i, data := range resp.Data {
		chainIndices[data.ChainIndex] = i
	}

	t.Logf("Example token prices from different chains:")
	for chainIndex, i := range chainIndices {
		t.Logf("Chain %s: %v", chainIndex, resp.Data[i])
	}
}
