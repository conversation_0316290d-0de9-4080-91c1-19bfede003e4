package okxapi

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestOKXPriceFetcher_GetPricesByContract(t *testing.T) {
	// Skip test if OKX API credentials are not set
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "OKX_ACCESS_KEY", "OKX_SECRET_KEY", "OKX_PASSPHRASE"})

	// Initialize the OKX API
	InitDefault(domain.NewAllPassRateLimiter())

	ctx := context.Background()
	fetcher := NewOKXPriceFetcher()

	t.Run("fetch prices for ETH and USDT", func(t *testing.T) {
		// Create the tokens to test
		ethToken := domain.ChainToken{
			Chain:   domain.Ethereum,
			TokenID: domain.Ethereum.MainToken().ID(), // Native ETH token
		}
		usdtToken := domain.ChainToken{
			Chain:   domain.Ethereum,
			TokenID: "******************************************", // USDT on Ethereum
		}

		tokens := []domain.ChainToken{ethToken, usdtToken}

		// Get prices
		prices, err := fetcher.GetPricesByContract(ctx, tokens)
		require.NoError(t, err)
		require.NotNil(t, prices)

		// Assert prices exist for both tokens
		ethPrice, ethExists := prices[ethToken]
		assert.True(t, ethExists, "Price for ETH token should exist")
		assert.Greater(t, float64(ethPrice), 0.0, "ETH price should be greater than 0")

		usdtPrice, usdtExists := prices[usdtToken]
		assert.True(t, usdtExists, "Price for USDT token should exist")
		assert.Greater(t, float64(usdtPrice), 0.0, "USDT price should be greater than 0")

		// Log the prices for verification
		t.Logf("ETH price: %f", float64(ethPrice))
		t.Logf("USDT price: %f", float64(usdtPrice))
	})

	t.Run("fetch prices for Solana", func(t *testing.T) {
		solanaToken := domain.ChainToken{
			Chain:   domain.Solana,
			TokenID: domain.Solana.MainToken().ID(), // Native SOL token
		}
		usdtToken := domain.ChainToken{
			Chain:   domain.Solana,
			TokenID: "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", // USDT on Solana
		}
		tokens := []domain.ChainToken{solanaToken, usdtToken}

		// Get prices
		prices, err := fetcher.GetPricesByContract(ctx, tokens)
		require.NoError(t, err)
		require.NotNil(t, prices)

		// Assert prices exist for both tokens
		solanaPrice, solanaExists := prices[solanaToken]
		assert.True(t, solanaExists, "Price for SOL token should exist")
		assert.Greater(t, float64(solanaPrice), 0.0, "SOL price should be greater than 0")

		// Log the prices for verification
		t.Logf("SOL price: %f", float64(solanaPrice))
	})

	t.Run("fetch prices for multiple chains", func(t *testing.T) {
		// Create tokens on different chains
		bnbToken := domain.ChainToken{
			Chain:   domain.BNBChain,
			TokenID: domain.BNBChain.MainToken().ID(), // Native BNB token
		}
		maticToken := domain.ChainToken{
			Chain:   domain.Polygon,
			TokenID: domain.Polygon.MainToken().ID(), // Native MATIC token
		}

		tokens := []domain.ChainToken{bnbToken, maticToken}

		// Get prices
		prices, err := fetcher.GetPricesByContract(ctx, tokens)
		require.NoError(t, err)
		require.NotNil(t, prices)

		// Assert prices exist for both tokens
		bnbPrice, bnbExists := prices[bnbToken]
		assert.True(t, bnbExists, "Price for BNB token should exist")
		assert.Greater(t, float64(bnbPrice), 0.0, "BNB price should be greater than 0")

		maticPrice, maticExists := prices[maticToken]
		assert.True(t, maticExists, "Price for MATIC token should exist")
		assert.Greater(t, float64(maticPrice), 0.0, "MATIC price should be greater than 0")

		// Log the prices for verification
		t.Logf("BNB price: %f", float64(bnbPrice))
		t.Logf("MATIC price: %f", float64(maticPrice))
	})

	t.Run("empty tokens list", func(t *testing.T) {
		// Test with empty tokens list
		prices, err := fetcher.GetPricesByContract(ctx, []domain.ChainToken{})
		require.NoError(t, err)
		assert.Empty(t, prices, "Should return empty map for empty tokens list")
	})

	t.Run("unsupported chain", func(t *testing.T) {
		invalidChain := domain.ChainToken{
			Chain:   domain.Hyperliquid,
			TokenID: "0x1234567890123456789012345678901234567890",
		}

		// Get prices
		prices, err := fetcher.GetPricesByContract(ctx, []domain.ChainToken{invalidChain})
		require.NoError(t, err) // Should not return error, just empty results for unsupported chains

		// The price for the invalid chain should not exist in the results
		_, exists := prices[invalidChain]
		assert.False(t, exists, "Price for invalid chain should not exist")
	})

	t.Run("GetPrices not implemented test", func(t *testing.T) {
		// Test that GetPrices returns an error as expected
		_, err := fetcher.GetPrices(ctx, []domain.CoingeckoID{"ethereum"})
		assert.Error(t, err, "GetPrices should return an error as it's not implemented")
		assert.Contains(t, err.Error(), "not implemented", "Error should mention that the function is not implemented")
	})
}
