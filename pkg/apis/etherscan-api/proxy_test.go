package etherscanapi

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetTransactionReceipt(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ETHERSCAN_API_KEY"})
	InitDefault()
	ctx := context.Background()

	txHash := "0x2380f617c382bac4edd07616ef5bedbb475017aac938731bde1ca37ab688e0a8" // Replace with a real transaction hash
	resp, err := etherscanObj.GetTransactionReceipt(ctx, "arb", txHash)

	if err != nil {
		t.Logf("Error getting transaction receipt: %v", err)
		return
	}

	assert.NotNil(t, resp)
	if resp != nil {
		t.Logf("Transaction Receipt: %+v", resp)
		assert.NotEmpty(t, resp.Result.TransactionHash)
		assert.NotEmpty(t, resp.Result.BlockNumber)
		assert.NotEmpty(t, resp.Result.Status)
	}
}
