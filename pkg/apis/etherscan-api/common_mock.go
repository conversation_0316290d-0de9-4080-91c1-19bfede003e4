// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/etherscan-api (interfaces: IEvmscan)
//
// Generated by this command:
//
//	mockgen -package=etherscanapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/etherscan-api -destination=common_mock.go . IEvmscan
//

// Package etherscanapi is a generated GoMock package.
package etherscanapi

import (
	context "context"
	reflect "reflect"
	time "time"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockIEvmscan is a mock of IEvmscan interface.
type MockIEvmscan struct {
	ctrl     *gomock.Controller
	recorder *MockIEvmscanMockRecorder
}

// MockIEvmscanMockRecorder is the mock recorder for MockIEvmscan.
type MockIEvmscanMockRecorder struct {
	mock *MockIEvmscan
}

// NewMockIEvmscan creates a new mock instance.
func NewMockIEvmscan(ctrl *gomock.Controller) *MockIEvmscan {
	mock := &MockIEvmscan{ctrl: ctrl}
	mock.recorder = &MockIEvmscanMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIEvmscan) EXPECT() *MockIEvmscanMockRecorder {
	return m.recorder
}

// CheckContractStatus mocks base method.
func (m *MockIEvmscan) CheckContractStatus(arg0 context.Context, arg1, arg2 string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckContractStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckContractStatus indicates an expected call of CheckContractStatus.
func (mr *MockIEvmscanMockRecorder) CheckContractStatus(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckContractStatus", reflect.TypeOf((*MockIEvmscan)(nil).CheckContractStatus), arg0, arg1, arg2)
}

// EthCall mocks base method.
func (m *MockIEvmscan) EthCall(arg0 context.Context, arg1, arg2, arg3, arg4 string) (*EthCallResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EthCall", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*EthCallResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EthCall indicates an expected call of EthCall.
func (mr *MockIEvmscanMockRecorder) EthCall(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EthCall", reflect.TypeOf((*MockIEvmscan)(nil).EthCall), arg0, arg1, arg2, arg3, arg4)
}

// GetBlockByNumber mocks base method.
func (m *MockIEvmscan) GetBlockByNumber(arg0 context.Context, arg1 string, arg2 int) (*GetBlockByNumberResp, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockByNumber", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetBlockByNumberResp)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetBlockByNumber indicates an expected call of GetBlockByNumber.
func (mr *MockIEvmscanMockRecorder) GetBlockByNumber(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockByNumber", reflect.TypeOf((*MockIEvmscan)(nil).GetBlockByNumber), arg0, arg1, arg2)
}

// GetInternalTxByHashWithRetry mocks base method.
func (m *MockIEvmscan) GetInternalTxByHashWithRetry(arg0 context.Context, arg1, arg2 string) ([]*domain.InternalTx, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInternalTxByHashWithRetry", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*domain.InternalTx)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetInternalTxByHashWithRetry indicates an expected call of GetInternalTxByHashWithRetry.
func (mr *MockIEvmscanMockRecorder) GetInternalTxByHashWithRetry(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInternalTxByHashWithRetry", reflect.TypeOf((*MockIEvmscan)(nil).GetInternalTxByHashWithRetry), arg0, arg1, arg2)
}

// GetTransactionReceipt mocks base method.
func (m *MockIEvmscan) GetTransactionReceipt(arg0 context.Context, arg1, arg2 string) (*GetTransactionReceiptResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionReceipt", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetTransactionReceiptResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionReceipt indicates an expected call of GetTransactionReceipt.
func (mr *MockIEvmscanMockRecorder) GetTransactionReceipt(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionReceipt", reflect.TypeOf((*MockIEvmscan)(nil).GetTransactionReceipt), arg0, arg1, arg2)
}

// GetTxStatus mocks base method.
func (m *MockIEvmscan) GetTxStatus(arg0 context.Context, arg1, arg2 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTxStatus", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTxStatus indicates an expected call of GetTxStatus.
func (mr *MockIEvmscanMockRecorder) GetTxStatus(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTxStatus", reflect.TypeOf((*MockIEvmscan)(nil).GetTxStatus), arg0, arg1, arg2)
}

// Token1155Tx mocks base method.
func (m *MockIEvmscan) Token1155Tx(arg0 context.Context, arg1 string, arg2 *TxParams) (*Token1155TxResp, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Token1155Tx", arg0, arg1, arg2)
	ret0, _ := ret[0].(*Token1155TxResp)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Token1155Tx indicates an expected call of Token1155Tx.
func (mr *MockIEvmscanMockRecorder) Token1155Tx(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Token1155Tx", reflect.TypeOf((*MockIEvmscan)(nil).Token1155Tx), arg0, arg1, arg2)
}

// TokenNftTx mocks base method.
func (m *MockIEvmscan) TokenNftTx(arg0 context.Context, arg1 string, arg2 *TxParams) (*TokenNftTxResp, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TokenNftTx", arg0, arg1, arg2)
	ret0, _ := ret[0].(*TokenNftTxResp)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// TokenNftTx indicates an expected call of TokenNftTx.
func (mr *MockIEvmscanMockRecorder) TokenNftTx(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TokenNftTx", reflect.TypeOf((*MockIEvmscan)(nil).TokenNftTx), arg0, arg1, arg2)
}

// TokenTx mocks base method.
func (m *MockIEvmscan) TokenTx(arg0 context.Context, arg1 string, arg2 *TxParams) (*TokenTxResp, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TokenTx", arg0, arg1, arg2)
	ret0, _ := ret[0].(*TokenTxResp)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// TokenTx indicates an expected call of TokenTx.
func (mr *MockIEvmscanMockRecorder) TokenTx(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TokenTx", reflect.TypeOf((*MockIEvmscan)(nil).TokenTx), arg0, arg1, arg2)
}

// Txlist mocks base method.
func (m *MockIEvmscan) Txlist(arg0 context.Context, arg1 string, arg2 *TxParams) (*TxlistResp, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Txlist", arg0, arg1, arg2)
	ret0, _ := ret[0].(*TxlistResp)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Txlist indicates an expected call of Txlist.
func (mr *MockIEvmscanMockRecorder) Txlist(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Txlist", reflect.TypeOf((*MockIEvmscan)(nil).Txlist), arg0, arg1, arg2)
}

// TxlistInternal mocks base method.
func (m *MockIEvmscan) TxlistInternal(arg0 context.Context, arg1 string, arg2 *TxParams) (*TxlistInternalResp, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TxlistInternal", arg0, arg1, arg2)
	ret0, _ := ret[0].(*TxlistInternalResp)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// TxlistInternal indicates an expected call of TxlistInternal.
func (mr *MockIEvmscanMockRecorder) TxlistInternal(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TxlistInternal", reflect.TypeOf((*MockIEvmscan)(nil).TxlistInternal), arg0, arg1, arg2)
}

// VerifyContract mocks base method.
func (m *MockIEvmscan) VerifyContract(arg0 context.Context, arg1 string, arg2 *VerifyContractParams) (*VerifyContractResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyContract", arg0, arg1, arg2)
	ret0, _ := ret[0].(*VerifyContractResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyContract indicates an expected call of VerifyContract.
func (mr *MockIEvmscanMockRecorder) VerifyContract(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyContract", reflect.TypeOf((*MockIEvmscan)(nil).VerifyContract), arg0, arg1, arg2)
}
