package etherscanapi

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestTxlist(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()
	ctx := context.Background()
	data, resp, err := etherscanObj.Txlist(ctx, "matic", &TxParams{
		Address:    "******************************************",
		Startblock: 48700860,
	})
	assert.Nil(t, err)
	t.Log(resp)
	t.Log(data)
}
