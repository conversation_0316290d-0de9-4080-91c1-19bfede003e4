package binancepay

import (
	"context"
	"testing"

	"github.com/google/uuid"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestInitDeposit(t *testing.T) {
	requiredConfigs := []string{
		"BINANCE_PAY_PRIVATE_KEY",
		"BINANCE_PAY_SOURCE",
	}
	testutil.RequireConfigsOrSkip(t, requiredConfigs)

	// Initialize the client
	InitDefault()
	client := Get()

	// Generate a unique request ID
	requestID := uuid.New().String()

	t.Logf("Request ID: %s", requestID)

	// Call the InitDeposit method
	depositURLs, err := client.InitDeposit(context.Background(), requestID, "BSC", "BNB")
	require.NoError(t, err)

	// Verify the response
	assert.NotEmpty(t, depositURLs.TransactionID)
	assert.NotEmpty(t, depositURLs.UniversalURL)

	// Log the response details
	t.Logf("Transaction ID: %s", depositURLs.TransactionID)
	t.Logf("Universal URL: %s", depositURLs.UniversalURL)
}

func TestQueryDepositAddress(t *testing.T) {
	requiredConfigs := []string{
		"BINANCE_PAY_PRIVATE_KEY",
		"BINANCE_PAY_SOURCE",
	}
	testutil.RequireConfigsOrSkip(t, requiredConfigs)

	// Initialize the client
	InitDefault()
	client := Get()

	// Use the fixed id for test
	requestID := "a285dbeefb264d5883a97b8867a88215"
	transactionID := "353137938672099328"

	// Now query the deposit address using the same request ID and the transaction ID
	address, err := client.QueryDepositAddress(context.Background(), requestID, transactionID)
	require.NoError(t, err)

	// Verify the response
	assert.NotEmpty(t, address)

	// Log the response details
	t.Logf("Deposit Address: %s", address.Address)
	t.Logf("Authorized: %t", address.Authorized)
}

func TestReportDeposit(t *testing.T) {
	requiredConfigs := []string{
		"BINANCE_PAY_PRIVATE_KEY",
		"BINANCE_PAY_SOURCE",
	}
	testutil.RequireConfigsOrSkip(t, requiredConfigs)

	// Initialize the client
	InitDefault()
	client := Get()

	// Example values - these should be replaced with actual test values
	amount := "0.1"
	txID := "0x6ddab26cd98d451513e75ce3ae5f58dae38c44d8e036c42042590b18713469b2"
	transactionID := "359465931019272192"

	// Report the deposit
	err := client.ReportDeposit(context.Background(), amount, txID, transactionID)
	require.NoError(t, err)

	// Assuming the test might fail due to invalid test data,
	// but we want to verify the request is properly structured
	if err != nil {
		t.Logf("ReportDeposit failed as expected with test data: %v", err)
	} else {
		t.Logf("ReportDeposit succeeded")
	}
}
