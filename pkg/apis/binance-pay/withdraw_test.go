package binancepay

import (
	"context"
	"encoding/json"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetWithdrawNetworks(t *testing.T) {
	requiredConfigs := []string{
		"BINANCE_PAY_PRIVATE_KEY",
		"BINANCE_PAY_SOURCE",
	}
	testutil.RequireConfigsOrSkip(t, requiredConfigs)

	// Initialize the client
	InitDefault()
	client := Get()

	resp, err := client.GetWithdrawNetworks(context.Background())
	require.NoError(t, err)
	assert.Equal(t, "000000", resp.Code)
	assert.NotEmpty(t, resp.Data.Networks)
	assert.NotEmpty(t, resp.Data.CoinDetail)

	// Print response details
	t.Logf("Available networks: %v", resp.Data.Networks)
	// [ACA ADA AE AION AKT ALGO AMB APT AR ARBITRUM ARDR ARK ASTR ATOM AVAIL AVAX AVAXC AXL BAND BASE BB BCD BCH BCHA BCHSV BCX BEAM BIFROSTKUSAMA BLAST BNB BOBA BSC BSC_PARITY BTC BTG BTS CELO CFX CFXEVM CHZ CHZ2 CKB CLOAK CMT COCOS CORE CTK CTXC CYBER DASH DCR DGB DOCK DOGE DOT DYDX DYM EDG EFI EGLD ELF ENDURANCE ENJ EOS ETC ETF ETH ETHW ETH_PARITY FET FIAT_MONEY FIL FILEVM FIO FIRO FLOW FLR FRACTAL FTM GLMR GO GRS GXS HBAR HC HCC HIVE HNT HT ICP ICX INJ IOST IOTA IOTX IOTXEVM IRIS KAIA KAS KAVA KAVAEVM KDA KDA0 KDA1 KDA10 KDA11 KDA12 KDA13 KDA14 KDA15 KDA16 KDA17 KDA18 KDA19 KDA2 KDA3 KDA4 KDA5 KDA6 KDA7 KDA8 KDA9 KLAY KLY KMD KSM LAVA LIGHTNING LSK LTC LTO LUMIA LUNA LUNC MA MANTA MATIC MERLIN METIS MINA MOB MOVR MTL NANO NAS NAV NBS NEAR NEBL NEO NEO3 NTRN NULS NXS OMNI ONE ONT OPBNB OPTIMISM ORDIBTC ORDIBTCSEGWIT OSMO PARA PIVX POA POLYX QTUM REEF REI RON ROSE RSK RUNE RVN SAGA SBTC SC SCROLL SCRT SEGWITBTC SEI SEIEVM SGB SKY SLF SOL STAKING STARKNET STATEMINT STEEM STRAT STRAX STX SUI SXP SYS TAIKO TAO THETA TIA TOMO TON TRIG TRX VANA VET VIA VIC VITE WAN WAVES WAX WLD WTC XAI XDAI XEC XECBTC XEM XLM XMR XRP XTZ XVG XYM XZC YOYO ZEC ZEN ZIL ZIRCUIT ZKSYNCERA]

	// Pretty print first few coin details
	for i, coin := range resp.Data.CoinDetail {
		if i >= 3 { // Only print first 3 coins
			break
		}
		jsonBytes, err := json.MarshalIndent(coin, "", "  ")
		require.NoError(t, err)
		t.Logf("Coin detail %d:\n%s", i+1, string(jsonBytes))
	}

	// Test at least one coin detail
	if len(resp.Data.CoinDetail) > 0 {
		coin := resp.Data.CoinDetail[0]
		assert.NotEmpty(t, coin.Coin)
		assert.NotEmpty(t, coin.NetWorkDetailList)

		// Test network details
		if len(coin.NetWorkDetailList) > 0 {
			network := coin.NetWorkDetailList[0]
			assert.NotEmpty(t, network.Network)
			assert.NotEmpty(t, network.NetworkName)
		}
	}
}

func TestPreCreateWithdraw(t *testing.T) {
	requiredConfigs := []string{
		"BINANCE_PAY_PRIVATE_KEY",
		"BINANCE_PAY_SOURCE",
	}
	testutil.RequireConfigsOrSkip(t, requiredConfigs)

	// Initialize the client
	InitDefault()
	client := Get()

	withdrawURLs, err := client.PreCreateWithdraw(context.Background(), "USDT", "ETH", "******************************************")
	require.NoError(t, err)
	assert.NotEmpty(t, withdrawURLs.TransactionID)
	assert.NotEmpty(t, withdrawURLs.IOSLink)
	assert.NotEmpty(t, withdrawURLs.AndroidLink)
	t.Logf("Transaction ID: %s", withdrawURLs.TransactionID)
	t.Logf("IOS Link: %s", withdrawURLs.IOSLink)
	t.Logf("Android Link: %s", withdrawURLs.AndroidLink)
}
