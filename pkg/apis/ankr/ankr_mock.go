// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/apis/ankr/common.go
//
// Generated by this command:
//
//	mockgen -source=pkg/apis/ankr/common.go -destination=pkg/apis/ankr/ankr_mock.go -package=ankr IAnkr
//

// Package ankr is a generated GoMock package.
package ankr

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockIAnkr is a mock of IAnkr interface.
type MockIAnkr struct {
	ctrl     *gomock.Controller
	recorder *MockIAnkrMockRecorder
}

// MockIAnkrMockRecorder is the mock recorder for MockIAnkr.
type MockIAnkrMockRecorder struct {
	mock *MockIAnkr
}

// NewMockIAnkr creates a new mock instance.
func NewMockIAnkr(ctrl *gomock.Controller) *MockIAnkr {
	mock := &MockIAnkr{ctrl: ctrl}
	mock.recorder = &MockIAnkrMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAnkr) EXPECT() *MockIAnkrMockRecorder {
	return m.recorder
}

// GetBlockByNumber mocks base method.
func (m *MockIAnkr) GetBlockByNumber(ctx context.Context, chainID string, blockNumber int64) (*domain.Block, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockByNumber", ctx, chainID, blockNumber)
	ret0, _ := ret[0].(*domain.Block)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockByNumber indicates an expected call of GetBlockByNumber.
func (mr *MockIAnkrMockRecorder) GetBlockByNumber(ctx, chainID, blockNumber any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockByNumber", reflect.TypeOf((*MockIAnkr)(nil).GetBlockByNumber), ctx, chainID, blockNumber)
}

// GetBlockNumber mocks base method.
func (m *MockIAnkr) GetBlockNumber(ctx context.Context, chainID string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlockNumber", ctx, chainID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlockNumber indicates an expected call of GetBlockNumber.
func (mr *MockIAnkrMockRecorder) GetBlockNumber(ctx, chainID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlockNumber", reflect.TypeOf((*MockIAnkr)(nil).GetBlockNumber), ctx, chainID)
}

// GetLogs mocks base method.
func (m *MockIAnkr) GetLogs(ctx context.Context, chainID string, fromBlock, toBlock int64) ([]*domain.Log, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogs", ctx, chainID, fromBlock, toBlock)
	ret0, _ := ret[0].([]*domain.Log)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLogs indicates an expected call of GetLogs.
func (mr *MockIAnkrMockRecorder) GetLogs(ctx, chainID, fromBlock, toBlock any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogs", reflect.TypeOf((*MockIAnkr)(nil).GetLogs), ctx, chainID, fromBlock, toBlock)
}
