package transpose

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetNftSale(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	client := NewClient()
	salesData, _, err := client.GetNftSales(context.Background(), &domain.GetNftSalesParams{
		ChainID:         "polygon",
		ContractAddress: "******************************************",
		TokenID:         "192",
	})
	assert.NoError(t, err)
	assert.NotNil(t, salesData)
	assert.NotEmpty(t, salesData.Price)
	t.Log(salesData.Price.String())
}
