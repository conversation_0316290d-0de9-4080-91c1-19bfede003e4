// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/zerion-api (interfaces: IZerion)
//
// Generated by this command:
//
//	mockgen -package=zerionapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/zerion-api -destination=zerion_api_mock.go . IZerion
//

// Package zerionapi is a generated GoMock package.
package zerionapi

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockIZerion is a mock of IZerion interface.
type MockIZerion struct {
	ctrl     *gomock.Controller
	recorder *MockIZerionMockRecorder
}

// MockIZerionMockRecorder is the mock recorder for MockIZerion.
type MockIZerionMockRecorder struct {
	mock *MockIZerion
}

// NewMockIZerion creates a new mock instance.
func NewMockIZerion(ctrl *gomock.Controller) *MockIZerion {
	mock := &MockIZerion{ctrl: ctrl}
	mock.recorder = &MockIZerionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIZerion) EXPECT() *MockIZerionMockRecorder {
	return m.recorder
}

// GetAssets mocks base method.
func (m *MockIZerion) GetAssets(arg0 context.Context, arg1 domain.Address, arg2 []domain.Chain, arg3 []domain.AssetType) (*domain.AggregatedAssets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssets", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*domain.AggregatedAssets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssets indicates an expected call of GetAssets.
func (mr *MockIZerionMockRecorder) GetAssets(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssets", reflect.TypeOf((*MockIZerion)(nil).GetAssets), arg0, arg1, arg2, arg3)
}

// SupportedChains mocks base method.
func (m *MockIZerion) SupportedChains() []domain.Chain {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedChains")
	ret0, _ := ret[0].([]domain.Chain)
	return ret0
}

// SupportedChains indicates an expected call of SupportedChains.
func (mr *MockIZerionMockRecorder) SupportedChains() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedChains", reflect.TypeOf((*MockIZerion)(nil).SupportedChains))
}

// SupportedTypes mocks base method.
func (m *MockIZerion) SupportedTypes() []domain.AssetType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedTypes")
	ret0, _ := ret[0].([]domain.AssetType)
	return ret0
}

// SupportedTypes indicates an expected call of SupportedTypes.
func (mr *MockIZerionMockRecorder) SupportedTypes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedTypes", reflect.TypeOf((*MockIZerion)(nil).SupportedTypes))
}

// WalletPositions mocks base method.
func (m *MockIZerion) WalletPositions(arg0 context.Context, arg1 string, arg2 []domain.Chain) (*WalletPositionsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WalletPositions", arg0, arg1, arg2)
	ret0, _ := ret[0].(*WalletPositionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// WalletPositions indicates an expected call of WalletPositions.
func (mr *MockIZerionMockRecorder) WalletPositions(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WalletPositions", reflect.TypeOf((*MockIZerion)(nil).WalletPositions), arg0, arg1, arg2)
}
