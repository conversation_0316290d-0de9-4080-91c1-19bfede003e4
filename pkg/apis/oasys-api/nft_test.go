package oasysapi_test

import (
	"context"
	"testing"

	oasysapi "github.com/kryptogo/kg-wallet-backend/pkg/apis/oasys-api"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestQueryOasysNFTMetadata(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	s := assert.New(t)

	ctx := context.Background()

	tests := []struct {
		name            string
		contractAddress string
		tokenID         string
		nftMetadata     *oasysapi.Metadata
	}{
		{
			name:            "fetch metadata for OASYX token id 5121",
			contractAddress: "******************************************",
			tokenID:         "5121",
			nftMetadata: &oasysapi.Metadata{
				Name:        "OASYX-AU #5121",
				Description: "OASYX is the first-ever NFT project minted on the eco-friendly gaming blockchain, Oasys' Hub-Layer. The project aims to create NFTs for the gaming community, making fun, interoperable experiences across games and metaverses build on Oasys. \r\n\r\nOASYX–AU will serve as the basis for future Metaverse avatar NFTs.\r\n\r\nTerms:https://x.oasys.games/terms\r\n\r\nSecondaryUseGuideLines:https://x.oasys.games/guidelines",
				Image:       "https://metadata.x.oasys.games/image/5121.png",
			},
		},
		{
			name:            "fetch metadata for OASYX 2 token id 8020",
			contractAddress: "0xe5f5db2d1154e5bb48b9ee7478e2c46515cb0113",
			tokenID:         "8020",
			nftMetadata: &oasysapi.Metadata{
				Name:        "OASYX-MARYU #8020",
				Description: "OASYX will create various NFTs in conjunction with leading creators, artists and developers hailing from established traditional gaming brands.OASYX-RYUZO is the second series of the OASYX project.In this series, users can obtain “MARYU” and bridge them from the Hub Layer to the Verse Layer. In the Verse Layer, MARYU can be hatched by burning, giving birth “RYU”The aforementioned feature is currently in development and is expected to be released soon.Terms:https://x.oasys.games/terms",
				Image:       "https://metadata.x.oasys.games/series2/image/MARYU.png",
			},
		},
	}

	for _, tt := range tests {
		t.Log(tt.name)
		nftMetadata, _, err := oasysapi.QueryOasysNFTMetadata(
			ctx, tt.contractAddress, tt.tokenID)

		s.NoError(err)
		s.Equal(tt.nftMetadata.Name, nftMetadata.Name)
		s.Equal(tt.nftMetadata.Description, nftMetadata.Description)
		s.Equal(tt.nftMetadata.Image, nftMetadata.Image)
	}
}
