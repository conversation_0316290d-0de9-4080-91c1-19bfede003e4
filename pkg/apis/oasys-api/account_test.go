package oasysapi

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestTx(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	s := assert.New(t)
	ctx := context.Background()
	resp, _, err := TxList(ctx, &TxParams{
		Address:    "******************************************",
		Startblock: 1843308,
		Endblock:   1913274,
		Sort:       "desc",
	})

	s.NoError(err)

	s.NotNil(resp)

	s.<PERSON>(resp.Result, 6)

	found := false

	for _, tx := range resp.Result {
		if tx.Hash != "0x8f0d5d6d0f9baaf7118c4a4e6f3ab0d30a6ce17e3cfae568b2b2c20fd6c4d5b2" {
			continue
		}

		found = true
		break
	}

	s.True(found)
}

func TestTxListInternal(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	s := assert.New(t)
	ctx := context.Background()
	resp, _, err := TxListInternal(ctx, &TxParams{
		Address:    "******************************************",
		Startblock: 1843308,
		Endblock:   1913274,
		Sort:       "desc",
	})

	s.NoError(err)

	s.NotNil(resp)

	s.Len(resp.Result, 2)

	found := false

	for _, tx := range resp.Result {
		if tx.TransactionHash != "0x3a42e9fb4c2d01cda3fbb3c51d802fb938733ea83155c3513accfb117c94e7ac" {
			continue
		}

		found = true
		break
	}

	s.True(found)
}

func TestTokenTx(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	s := assert.New(t)
	ctx := context.Background()
	resp, _, err := TokenTx(ctx, &TxParams{
		Address:    "******************************************",
		Startblock: 1843308,
		Endblock:   1913274,
		Sort:       "desc",
	})

	s.NoError(err)

	s.NotNil(resp)

	s.Len(resp.Result, 4)

	found := false

	for _, tx := range resp.Result {
		if tx.Hash != "0x4c29ef1d3e2cb9ffddd5381b576e208901d74f3c2823b81daac27c39b1a699ca" {
			continue
		}

		found = true
		break
	}

	s.True(found)
}
