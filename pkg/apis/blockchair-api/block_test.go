package blockchairapi

import (
	"context"
	"testing"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetLatestBlockNumber(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "BLOCKCHAIR_API_KEY"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	t.Run("normal case", func(t *testing.T) {
		blockNumber, err := blockchairObj.GetLatestBlockNumber(ctx)
		assert.Nil(t, err)
		assert.NotNil(t, blockNumber)
		assert.Greater(t, blockNumber, uint64(866632))
	})
}
