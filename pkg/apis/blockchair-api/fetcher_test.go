package blockchairapi

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetAssets(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "BLOCKCHAIR_API_KEY"})

	InitDefault(domain.NewAllPassRateLimiter())
	address := domain.NewStrAddress("******************************************")
	chains := []domain.Chain{domain.Bitcoin}
	types := []domain.AssetType{domain.AssetTypeToken}
	assets, err := blockchairObj.GetAssets(context.Background(), address, chains, types)
	assert.Nil(t, err)
	assert.NotNil(t, assets)
	assert.Equal(t, 1, len(assets.Tokens))
	assert.Equal(t, domain.Bitcoin.MainToken(), assets.Tokens[0].Token)
	t.Logf("btc amount: %f\n", assets.Tokens[0].Amount.InexactFloat64())
}
