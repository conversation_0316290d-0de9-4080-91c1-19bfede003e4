package blockchairapi

import (
	"context"
	"testing"
	time "time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestAddressInfo(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "BLOCKCHAIR_API_KEY"})
	InitDefault(domain.NewAllPassRateLimiter())

	params := &AddressInfoParams{
		Addresses: "******************************************",
		Offset:    0,
	}

	// Call the AddressInfo function
	ctx := context.Background()
	respData, execTime, err := blockchairObj.AddressInfo(ctx, params)
	assert.Nil(t, err)
	assert.Greater(t, execTime, time.Duration(0))

	txHash := "2dcb08860e64be4d99c53a6a924374c840d08fa81b95b1f95d2ff0df28bee2e0"
	txFound := false
	for _, tx := range respData.Data {
		t.Logf("tx: %+v", tx)
		for _, detail := range tx.Transactions {
			t.Logf("detail: %+v", detail)
			if detail.Hash == txHash {
				txFound = true
				assert.Equal(t, 872151, detail.BlockID)
				assert.Equal(t, -5049, detail.BalanceChange)
				break
			}
		}
	}
	assert.True(t, txFound)
}

func TestGetTransaction(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "BLOCKCHAIR_API_KEY"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	t.Run("not exist", func(t *testing.T) {
		txHash := "24a2a4a4cf832a733af59f6e08ce989292510dd82063ba79b51e1993effc457d"
		_, err := blockchairObj.GetTransaction(ctx, txHash)
		assert.Equal(t, domain.ErrRecordNotFound, err)
	})

	t.Run("exist", func(t *testing.T) {
		txHash := "24a2a4a4cf832a733af59f6e08ce989292510dd82063ba79b51e1993effc457c"
		respData, err := blockchairObj.GetTransaction(ctx, txHash)
		assert.Nil(t, err)
		t.Logf("Response data: %v", respData)
		assert.Equal(t, int64(870961), respData.BlockID)
		assert.Equal(t, txHash, respData.Hash)
		assert.Equal(t, uint64(423), respData.Fee)
		assert.WithinDuration(t, time.Date(2024, 11, 19, 2, 46, 18, 0, time.UTC), respData.Time, 1*time.Second)

		assert.Equal(t, 1, len(respData.Inputs))
		assert.Equal(t, "bc1q49fvaydgvg9wefgnne2xcajgmynsszw2vx2hjh", respData.Inputs[0].Address)
		assert.Equal(t, uint64(288785), respData.Inputs[0].Value)

		assert.Equal(t, 2, len(respData.Outputs))
		assert.Equal(t, "bc1q9v9h6kz0uacvm9v5naqf94ym3qf3nn3dtattjr", respData.Outputs[0].Address)
		assert.Equal(t, uint64(5000), respData.Outputs[0].Value)
		assert.Equal(t, "bc1q49fvaydgvg9wefgnne2xcajgmynsszw2vx2hjh", respData.Outputs[1].Address)
		assert.Equal(t, uint64(283362), respData.Outputs[1].Value)
	})
}
