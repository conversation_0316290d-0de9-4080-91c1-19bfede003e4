package blockchairapi

import (
	"context"
	"testing"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestAddressBalances(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "BLOCKCHAIR_API_KEY"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	t.Run("test address balances", func(t *testing.T) {
		addresses := "******************************************"
		balances, err := blockchairObj.AddressBalances(ctx, addresses)
		assert.Nil(t, err)
		assert.NotNil(t, balances)
		assert.Equal(t, 1, len(balances))
	})
}
