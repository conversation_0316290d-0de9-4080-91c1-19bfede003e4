// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchair-api (interfaces: IBlockchair)
//
// Generated by this command:
//
//	mockgen -package=blockchairapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchair-api -destination=blockchair_api_mock.go . IBlockchair
//

// Package blockchairapi is a generated GoMock package.
package blockchairapi

import (
	context "context"
	reflect "reflect"
	time "time"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockIBlockchair is a mock of IBlockchair interface.
type MockIBlockchair struct {
	ctrl     *gomock.Controller
	recorder *MockIBlockchairMockRecorder
}

// MockIBlockchairMockRecorder is the mock recorder for MockIBlockchair.
type MockIBlockchairMockRecorder struct {
	mock *MockIBlockchair
}

// NewMockIBlockchair creates a new mock instance.
func NewMockIBlockchair(ctrl *gomock.Controller) *MockIBlockchair {
	mock := &MockIBlockchair{ctrl: ctrl}
	mock.recorder = &MockIBlockchairMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBlockchair) EXPECT() *MockIBlockchairMockRecorder {
	return m.recorder
}

// AddressBalances mocks base method.
func (m *MockIBlockchair) AddressBalances(arg0 context.Context, arg1 string) (map[string]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddressBalances", arg0, arg1)
	ret0, _ := ret[0].(map[string]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddressBalances indicates an expected call of AddressBalances.
func (mr *MockIBlockchairMockRecorder) AddressBalances(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddressBalances", reflect.TypeOf((*MockIBlockchair)(nil).AddressBalances), arg0, arg1)
}

// AddressInfo mocks base method.
func (m *MockIBlockchair) AddressInfo(arg0 context.Context, arg1 *AddressInfoParams) (*AddressInfoResp, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddressInfo", arg0, arg1)
	ret0, _ := ret[0].(*AddressInfoResp)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// AddressInfo indicates an expected call of AddressInfo.
func (mr *MockIBlockchairMockRecorder) AddressInfo(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddressInfo", reflect.TypeOf((*MockIBlockchair)(nil).AddressInfo), arg0, arg1)
}

// GetAssets mocks base method.
func (m *MockIBlockchair) GetAssets(arg0 context.Context, arg1 domain.Address, arg2 []domain.Chain, arg3 []domain.AssetType) (*domain.AggregatedAssets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssets", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*domain.AggregatedAssets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssets indicates an expected call of GetAssets.
func (mr *MockIBlockchairMockRecorder) GetAssets(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssets", reflect.TypeOf((*MockIBlockchair)(nil).GetAssets), arg0, arg1, arg2, arg3)
}

// GetBlock mocks base method.
func (m *MockIBlockchair) GetBlock(arg0 context.Context, arg1 string) (*BlockDetailResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlock", arg0, arg1)
	ret0, _ := ret[0].(*BlockDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlock indicates an expected call of GetBlock.
func (mr *MockIBlockchairMockRecorder) GetBlock(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlock", reflect.TypeOf((*MockIBlockchair)(nil).GetBlock), arg0, arg1)
}

// GetLatestBlockNumber mocks base method.
func (m *MockIBlockchair) GetLatestBlockNumber(arg0 context.Context) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestBlockNumber", arg0)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestBlockNumber indicates an expected call of GetLatestBlockNumber.
func (mr *MockIBlockchairMockRecorder) GetLatestBlockNumber(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestBlockNumber", reflect.TypeOf((*MockIBlockchair)(nil).GetLatestBlockNumber), arg0)
}

// GetTransaction mocks base method.
func (m *MockIBlockchair) GetTransaction(arg0 context.Context, arg1 string) (*domain.BitcoinTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransaction", arg0, arg1)
	ret0, _ := ret[0].(*domain.BitcoinTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransaction indicates an expected call of GetTransaction.
func (mr *MockIBlockchairMockRecorder) GetTransaction(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransaction", reflect.TypeOf((*MockIBlockchair)(nil).GetTransaction), arg0, arg1)
}

// SupportedChains mocks base method.
func (m *MockIBlockchair) SupportedChains() []domain.Chain {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedChains")
	ret0, _ := ret[0].([]domain.Chain)
	return ret0
}

// SupportedChains indicates an expected call of SupportedChains.
func (mr *MockIBlockchairMockRecorder) SupportedChains() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedChains", reflect.TypeOf((*MockIBlockchair)(nil).SupportedChains))
}

// SupportedTypes mocks base method.
func (m *MockIBlockchair) SupportedTypes() []domain.AssetType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedTypes")
	ret0, _ := ret[0].([]domain.AssetType)
	return ret0
}

// SupportedTypes indicates an expected call of SupportedTypes.
func (mr *MockIBlockchairMockRecorder) SupportedTypes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedTypes", reflect.TypeOf((*MockIBlockchair)(nil).SupportedTypes))
}
