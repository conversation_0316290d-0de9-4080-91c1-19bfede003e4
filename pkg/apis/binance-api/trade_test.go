package binanceapi

import (
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestNewOrder(t *testing.T) {
	if config.IsDev() {
		t.Skip("Skip test in dev mode")
	}

	now := time.Now()
	orderID := util.GenerateOrderSerialNo(now, "01")
	newOrderReq := &NewOrderReq{
		Symbol:           "ETHUSDT",
		Side:             "BUY",
		Type:             string(OrderTypeMarket),
		Quantity:         decimal.NewFromFloat(0.001),
		NewClientOrderID: orderID,
		Timestamp:        now.UnixMilli(),
	}
	newOrderResp, err := NewOrder(newOrderReq)
	assert.Nil(t, err)
	assert.Equal(t, newOrderReq.Symbol, newOrderResp.Symbol)
	assert.Equal(t, newOrderReq.NewClientOrderID, newOrderResp.ClientOrderID)

	queryOrderReq := &QueryOrderReq{
		Symbol:            "ETHUSDT",
		OrigClientOrderID: orderID,
		Timestamp:         time.Now().UnixMilli(),
	}
	queryOrderResp, err := QueryOrder(queryOrderReq)
	assert.Nil(t, err)
	assert.NotNil(t, queryOrderResp)
	assert.Equal(t, newOrderReq.Symbol, queryOrderResp.Symbol)
}
