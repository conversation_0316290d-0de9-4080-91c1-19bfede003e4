package binanceapi

import (
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/util"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestWithdraw(t *testing.T) {
	if config.IsDev() {
		t.<PERSON><PERSON>("Skip test in dev mode")
	}

	now := time.Now()
	orderID := util.GenerateOrderSerialNo(now, "01")
	withdrawReq := &WithdrawReq{
		Coin:            "USDT",
		WithdrawOrderID: orderID,
		Network:         "MATIC",
		Address:         "******************************************",
		Amount:          decimal.NewFromInt(10),
		Timestamp:       now.UnixMilli(),
	}

	withdrawResp, err := Withdraw(withdrawReq)
	assert.Nil(t, err)

	withdrawHistoryReq := &WithdrawHistoryReq{
		WithdrawOrderID: withdrawResp.ID,
		Timestamp:       time.Now().UnixMilli(),
	}

	withdrawHistoryResp, err := WithdrawHistory(withdrawHistoryReq)
	assert.Nil(t, err)
	assert.NotNil(t, withdrawHistoryResp)
}
