package kccapi

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestKccAPI_GetAssets(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	InitDefault()
	api := Get()
	ctx := context.Background()
	address := domain.NewEvmAddress("******************************************")
	chains := []domain.Chain{domain.Kcc}
	types := []domain.AssetType{domain.AssetTypeToken}

	assets, err := api.GetAssets(ctx, address, chains, types)
	require.NoError(t, err)
	require.NotNil(t, assets)

	// Assert that we have at least one token (KCS)
	assert.GreaterOrEqual(t, len(assets.Tokens), 1, "Expected at least one token (KCS)")

	// Print the number of tokens found
	t.Logf("Number of tokens found: %d", len(assets.Tokens))

	// Optionally, you can print details of each token
	for i, token := range assets.Tokens {
		t.Logf("Token %d: %s (%s) - Amount: %s", i+1, token.Token.Symbol(), token.Token.Name(), token.Amount.String())
	}
}
