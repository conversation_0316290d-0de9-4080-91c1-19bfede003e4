// Code generated by MockGen. DO NOT EDIT.
// Source: common.go
//
// Generated by this command:
//
//	mockgen -source=common.go -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/kcc-api -destination=common_mock.go -package=kccapi IKcc
//

// Package kccapi is a generated GoMock package.
package kccapi

import (
	context "context"
	big "math/big"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	resty "github.com/kryptogo/kg-wallet-backend/pkg/resty"
	gomock "go.uber.org/mock/gomock"
)

// MockIKcc is a mock of IKcc interface.
type MockIKcc struct {
	ctrl     *gomock.Controller
	recorder *MockIKccMockRecorder
}

// MockIKccMockRecorder is the mock recorder for MockIKcc.
type MockIKccMockRecorder struct {
	mock *MockIKcc
}

// NewMockIKcc creates a new mock instance.
func NewMockIKcc(ctrl *gomock.Controller) *MockIKcc {
	mock := &MockIKcc{ctrl: ctrl}
	mock.recorder = &MockIKccMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIKcc) EXPECT() *MockIKccMockRecorder {
	return m.recorder
}

// GetAssets mocks base method.
func (m *MockIKcc) GetAssets(ctx context.Context, address domain.Address, chains []domain.Chain, types []domain.AssetType) (*domain.AggregatedAssets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssets", ctx, address, chains, types)
	ret0, _ := ret[0].(*domain.AggregatedAssets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssets indicates an expected call of GetAssets.
func (mr *MockIKccMockRecorder) GetAssets(ctx, address, chains, types any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssets", reflect.TypeOf((*MockIKcc)(nil).GetAssets), ctx, address, chains, types)
}

// InternalTransactionList mocks base method.
func (m *MockIKcc) InternalTransactionList(ctx context.Context, params *RequestParams) (*InternalTransactionListResp, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InternalTransactionList", ctx, params)
	ret0, _ := ret[0].(*InternalTransactionListResp)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// InternalTransactionList indicates an expected call of InternalTransactionList.
func (mr *MockIKccMockRecorder) InternalTransactionList(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternalTransactionList", reflect.TypeOf((*MockIKcc)(nil).InternalTransactionList), ctx, params)
}

// KCSBalance mocks base method.
func (m *MockIKcc) KCSBalance(ctx context.Context, address string) (*string, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KCSBalance", ctx, address)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// KCSBalance indicates an expected call of KCSBalance.
func (mr *MockIKccMockRecorder) KCSBalance(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KCSBalance", reflect.TypeOf((*MockIKcc)(nil).KCSBalance), ctx, address)
}

// NativeBalance mocks base method.
func (m *MockIKcc) NativeBalance(ctx context.Context, address string) (*big.Int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NativeBalance", ctx, address)
	ret0, _ := ret[0].(*big.Int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NativeBalance indicates an expected call of NativeBalance.
func (mr *MockIKccMockRecorder) NativeBalance(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NativeBalance", reflect.TypeOf((*MockIKcc)(nil).NativeBalance), ctx, address)
}

// SupportedChains mocks base method.
func (m *MockIKcc) SupportedChains() []domain.Chain {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedChains")
	ret0, _ := ret[0].([]domain.Chain)
	return ret0
}

// SupportedChains indicates an expected call of SupportedChains.
func (mr *MockIKccMockRecorder) SupportedChains() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedChains", reflect.TypeOf((*MockIKcc)(nil).SupportedChains))
}

// SupportedTypes mocks base method.
func (m *MockIKcc) SupportedTypes() []domain.AssetType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedTypes")
	ret0, _ := ret[0].([]domain.AssetType)
	return ret0
}

// SupportedTypes indicates an expected call of SupportedTypes.
func (mr *MockIKccMockRecorder) SupportedTypes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedTypes", reflect.TypeOf((*MockIKcc)(nil).SupportedTypes))
}

// TokenList mocks base method.
func (m *MockIKcc) TokenList(ctx context.Context, address string) (*TokenListResp, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TokenList", ctx, address)
	ret0, _ := ret[0].(*TokenListResp)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// TokenList indicates an expected call of TokenList.
func (mr *MockIKccMockRecorder) TokenList(ctx, address any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TokenList", reflect.TypeOf((*MockIKcc)(nil).TokenList), ctx, address)
}

// TokenTransactionList mocks base method.
func (m *MockIKcc) TokenTransactionList(ctx context.Context, params *RequestParams) (*TokenTransactionListResp, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TokenTransactionList", ctx, params)
	ret0, _ := ret[0].(*TokenTransactionListResp)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// TokenTransactionList indicates an expected call of TokenTransactionList.
func (mr *MockIKccMockRecorder) TokenTransactionList(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TokenTransactionList", reflect.TypeOf((*MockIKcc)(nil).TokenTransactionList), ctx, params)
}

// TransactionList mocks base method.
func (m *MockIKcc) TransactionList(ctx context.Context, params *RequestParams) (*TransactionListResp, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TransactionList", ctx, params)
	ret0, _ := ret[0].(*TransactionListResp)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// TransactionList indicates an expected call of TransactionList.
func (mr *MockIKccMockRecorder) TransactionList(ctx, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TransactionList", reflect.TypeOf((*MockIKcc)(nil).TransactionList), ctx, params)
}
