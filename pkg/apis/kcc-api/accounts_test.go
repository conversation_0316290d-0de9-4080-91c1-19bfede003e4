package kccapi

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestKCSBalance(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	balance, resp, err := kccObj.KCSBalance(context.Background(), "******************************************")
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode())
	assert.True(t, len(*balance) > 1)
	t.Logf("balance: %+v", *balance)
}

func TestTransactionList(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	txList, resp, err := kccObj.TransactionList(context.Background(), &RequestParams{
		Address: "******************************************",
		Page:    1,
	})
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode())
	assert.True(t, len(txList.Result) > 1)
	t.Logf("txlist: %+v", *txList)
}

func TestInternalTransactionList(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	txList, resp, err := kccObj.InternalTransactionList(context.Background(), &RequestParams{
		Address: "******************************************",
		Page:    1,
	})
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode())
	assert.True(t, len(txList.Result) > 0)
	t.Logf("internal txlist: %+v", *txList)
}

func TestTokenTransactionList(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	txList, resp, err := kccObj.TokenTransactionList(context.Background(), &RequestParams{
		Address: "******************************************",
		Page:    1,
	})
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode())
	assert.True(t, len(txList.Result) > 0)
	t.Logf("token tx list: %+v", *txList)
}

func TestTokenList(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	txList, resp, err := kccObj.TokenList(context.Background(), "******************************************")
	assert.NoError(t, err)
	assert.Equal(t, 200, resp.StatusCode())
	assert.True(t, len(txList.Result) > 0)
	t.Logf("token list: %+v", *txList)
}
