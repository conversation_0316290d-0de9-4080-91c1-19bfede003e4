package solscanapi

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetTransfers(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "SOLSCAN_API_KEY_V2"})

	t.Run("Normal case", func(t *testing.T) {
		param := &TransferRequestParams{
			Address:   "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			PageSize:  30,
			Page:      1,
			SortOrder: "desc",
		}
		InitDefault()

		res, _, err := solscanObj.GetTransfers(context.Background(), param)
		if err != nil {
			t.Fatalf("failed to get transfers: %v", err)
		}
		assert.NotNil(t, res)
		assert.Len(t, res, 30)
		// At least one of the transfers is an SPL transfer, and one is a SOL transfer
		assert.Condition(t, func() bool {
			solFound := false
			splFound := false
			for _, tx := range res {
				if tx.TokenAddress != "So11111111111111111111111111111111111111111" {
					splFound = true
				} else {
					solFound = true
				}
			}
			return solFound && splFound
		})
		for _, tx := range res[:5] {
			assert.Greater(t, tx.BlockID, uint32(301120000))
			assert.True(t, tx.BlockTime > 1700000000)
			assert.NotEmpty(t, tx.TransID)
			assert.NotEmpty(t, tx.FromAddress)
			assert.NotEmpty(t, tx.ToAddress)
			assert.Condition(t, func() bool {
				return tx.FromAddress == param.Address || tx.ToAddress == param.Address
			})
			assert.Equal(t, tx.ActivityType, ActivityTypeSPLTransfer)
			assert.Contains(t, []string{"in", "out"}, tx.Flow)
			assert.NotNil(t, tx.Amount)
			assert.NotEmpty(t, tx.TokenAddress)
			assert.NotEmpty(t, tx.TokenDecimals)
			assert.NotEmpty(t, tx.Value)
			assert.NotEmpty(t, tx.Time)
		}

		for _, tx := range res {
			if tx.TransID == "2hZwhKwrgheJQuFcvujujqgBUsWvBRp4TLspfwa3rf71cdzWDCcbzudTSWxHDzaAR9gCT3cD4xNegPTtJrG51RdU" {
				t.Logf("Got tx: %v", tx)
			}
		}
	})

	t.Run("Error case", func(t *testing.T) {
		param := &TransferRequestParams{
			Address:  "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			PageSize: 50,
			Page:     1,
		}
		InitDefault()

		_, _, err := solscanObj.GetTransfers(context.Background(), param)
		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), "page_size must be one of [10, 20, 30, 40, 60, 100]")
	})

	t.Run("Edge case", func(t *testing.T) {
		param := &TransferRequestParams{
			Address:  "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
			PageSize: 60,
			Page:     100000000,
		}
		InitDefault()

		res, _, err := solscanObj.GetTransfers(context.Background(), param)
		if err != nil {
			t.Fatalf("failed to get transfers: %v", err)
		}
		assert.Len(t, res, 0)
	})
}
