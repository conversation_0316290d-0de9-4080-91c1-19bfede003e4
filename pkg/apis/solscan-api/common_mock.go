// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/solscan-api (interfaces: ISolscan)
//
// Generated by this command:
//
//	mockgen -package=solscanapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/solscan-api -destination=common_mock.go . ISolscan
//

// Package solscanapi is a generated GoMock package.
package solscanapi

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "go.uber.org/mock/gomock"
)

// MockISolscan is a mock of ISolscan interface.
type MockISolscan struct {
	ctrl     *gomock.Controller
	recorder *MockISolscanMockRecorder
}

// MockISolscanMockRecorder is the mock recorder for MockISolscan.
type MockISolscanMockRecorder struct {
	mock *MockISolscan
}

// NewMockISolscan creates a new mock instance.
func NewMockISolscan(ctrl *gomock.Controller) *MockISolscan {
	mock := &MockISolscan{ctrl: ctrl}
	mock.recorder = &MockISolscanMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISolscan) EXPECT() *MockISolscanMockRecorder {
	return m.recorder
}

// GetTransfers mocks base method.
func (m *MockISolscan) GetTransfers(arg0 context.Context, arg1 *TransferRequestParams) ([]*Transfer, time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransfers", arg0, arg1)
	ret0, _ := ret[0].([]*Transfer)
	ret1, _ := ret[1].(time.Duration)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetTransfers indicates an expected call of GetTransfers.
func (mr *MockISolscanMockRecorder) GetTransfers(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransfers", reflect.TypeOf((*MockISolscan)(nil).GetTransfers), arg0, arg1)
}
