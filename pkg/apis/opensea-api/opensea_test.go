package openseaapi

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetStats(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	res, err := openseaObj.GetStats(context.Background(), "boredapeyachtclub")
	assert.<PERSON><PERSON>(t, err)
	assert.NotNil(t, res)
	assert.NotZero(t, res.TotalVolume)
	assert.NotZero(t, res.FloorPrice)
	assert.Not<PERSON><PERSON>(t, res.ThirtyDayVolume)
	assert.NotZero(t, res.AveragePrice)
	assert.NotZero(t, res.OneDayAveragePrice)
	assert.NotZero(t, res.SevenDayAveragePrice)
	assert.NotZero(t, res.ThirtyDayAveragePrice)
	t.Logf("res: %+v\n", res)

	res, err = openseaObj.GetStats(context.Background(), "MV3")
	assert.Nil(t, res)
	assert.NotNil(t, err)
	assert.Equal(t, err, Err404)
}

func TestGetAsset(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	res, err := openseaObj.GetAsset(context.Background(), "eth", "******************************************", "22207")
	assert.Nil(t, err)
	assert.NotNil(t, res)
	t.Logf("res: %+v\n", res)
}

func TestGetAssets(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	res, err := openseaObj.GetAssets(context.Background(), &GetAssetsParams{
		Owner:   "******************************************",
		ChainID: "eth",
		Limit:   "50",
		Next:    nil,
	})
	assert.Nil(t, err)
	assert.NotNil(t, res)
	t.Logf("res: %+v\n", res)
}

func TestGetMetadata(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	res, err := openseaObj.GetMetadata(context.Background(), "ethereum", "******************************************", "22207")
	assert.Nil(t, err)
	assert.NotNil(t, res)
	t.Logf("res: %+v\n", res)
}

func TestGetCollection(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	res, err := openseaObj.GetCollection(context.Background(), "boredapeyachtclub")
	assert.Nil(t, err)
	assert.NotNil(t, res)
	t.Logf("res: %+v\n", res)
}

func TestRefreshMetadata(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	err := openseaObj.RefreshMetadata(context.Background(), "eth", "******************************************", "123")
	assert.Nil(t, err)
}

func TestGetMarketplaceLists(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	orders, err := openseaObj.GetMarketplaceLists(context.Background(), "eth", "******************************************", "1")
	assert.Nil(t, err)
	t.Logf("res: %+v\n", orders)

	assert.NotZero(t, len(orders))
	assert.NotEmpty(t, orders[0].Price)
	assert.NotEmpty(t, orders[0].Side)
	assert.NotEmpty(t, orders[0].StartTime)
	assert.NotEmpty(t, orders[0].EndTime)
}
