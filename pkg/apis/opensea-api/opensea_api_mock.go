// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/opensea-api (interfaces: IOpensea)
//
// Generated by this command:
//
//	mockgen -package=openseaapi -destination=opensea_api_mock.go . IOpensea
//

// Package openseaapi is a generated GoMock package.
package openseaapi

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIOpensea is a mock of IOpensea interface.
type MockIOpensea struct {
	ctrl     *gomock.Controller
	recorder *MockIOpenseaMockRecorder
}

// MockIOpenseaMockRecorder is the mock recorder for MockIOpensea.
type MockIOpenseaMockRecorder struct {
	mock *MockIOpensea
}

// NewMockIOpensea creates a new mock instance.
func NewMockIOpensea(ctrl *gomock.Controller) *MockIOpensea {
	mock := &MockIOpensea{ctrl: ctrl}
	mock.recorder = &MockIOpenseaMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIOpensea) EXPECT() *MockIOpenseaMockRecorder {
	return m.recorder
}

// GetAsset mocks base method.
func (m *MockIOpensea) GetAsset(arg0 context.Context, arg1, arg2, arg3 string) (*AssetResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAsset", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*AssetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAsset indicates an expected call of GetAsset.
func (mr *MockIOpenseaMockRecorder) GetAsset(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAsset", reflect.TypeOf((*MockIOpensea)(nil).GetAsset), arg0, arg1, arg2, arg3)
}

// GetAssets mocks base method.
func (m *MockIOpensea) GetAssets(arg0 context.Context, arg1 *GetAssetsParams) (*AssetsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssets", arg0, arg1)
	ret0, _ := ret[0].(*AssetsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssets indicates an expected call of GetAssets.
func (mr *MockIOpenseaMockRecorder) GetAssets(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssets", reflect.TypeOf((*MockIOpensea)(nil).GetAssets), arg0, arg1)
}

// GetCollection mocks base method.
func (m *MockIOpensea) GetCollection(arg0 context.Context, arg1 string) (*CollectionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCollection", arg0, arg1)
	ret0, _ := ret[0].(*CollectionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCollection indicates an expected call of GetCollection.
func (mr *MockIOpenseaMockRecorder) GetCollection(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCollection", reflect.TypeOf((*MockIOpensea)(nil).GetCollection), arg0, arg1)
}

// GetMarketplaceLists mocks base method.
func (m *MockIOpensea) GetMarketplaceLists(arg0 context.Context, arg1, arg2, arg3 string) ([]Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMarketplaceLists", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMarketplaceLists indicates an expected call of GetMarketplaceLists.
func (mr *MockIOpenseaMockRecorder) GetMarketplaceLists(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMarketplaceLists", reflect.TypeOf((*MockIOpensea)(nil).GetMarketplaceLists), arg0, arg1, arg2, arg3)
}

// GetMetadata mocks base method.
func (m *MockIOpensea) GetMetadata(arg0 context.Context, arg1, arg2, arg3 string) (*Metadata, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMetadata", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*Metadata)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMetadata indicates an expected call of GetMetadata.
func (mr *MockIOpenseaMockRecorder) GetMetadata(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMetadata", reflect.TypeOf((*MockIOpensea)(nil).GetMetadata), arg0, arg1, arg2, arg3)
}

// GetStats mocks base method.
func (m *MockIOpensea) GetStats(arg0 context.Context, arg1 string) (*Stats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStats", arg0, arg1)
	ret0, _ := ret[0].(*Stats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStats indicates an expected call of GetStats.
func (mr *MockIOpenseaMockRecorder) GetStats(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStats", reflect.TypeOf((*MockIOpensea)(nil).GetStats), arg0, arg1)
}

// RefreshMetadata mocks base method.
func (m *MockIOpensea) RefreshMetadata(arg0 context.Context, arg1, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshMetadata", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// RefreshMetadata indicates an expected call of RefreshMetadata.
func (mr *MockIOpenseaMockRecorder) RefreshMetadata(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshMetadata", reflect.TypeOf((*MockIOpensea)(nil).RefreshMetadata), arg0, arg1, arg2, arg3)
}
