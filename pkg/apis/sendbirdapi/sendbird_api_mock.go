// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi (interfaces: SendbirdClientI)
//
// Generated by this command:
//
//	mockgen -package=sendbirdapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/sendbirdapi -destination=sendbird_api_mock.go . SendbirdClientI
//

// Package sendbirdapi is a generated GoMock package.
package sendbirdapi

import (
	context "context"
	reflect "reflect"

	resty "github.com/go-resty/resty/v2"
	gomock "go.uber.org/mock/gomock"
)

// MockSendbirdClientI is a mock of SendbirdClientI interface.
type MockSendbirdClientI struct {
	ctrl     *gomock.Controller
	recorder *MockSendbirdClientIMockRecorder
}

// MockSendbirdClientIMockRecorder is the mock recorder for MockSendbirdClientI.
type MockSendbirdClientIMockRecorder struct {
	mock *MockSendbirdClientI
}

// NewMockSendbirdClientI creates a new mock instance.
func NewMockSendbirdClientI(ctrl *gomock.Controller) *MockSendbirdClientI {
	mock := &MockSendbirdClientI{ctrl: ctrl}
	mock.recorder = &MockSendbirdClientIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSendbirdClientI) EXPECT() *MockSendbirdClientIMockRecorder {
	return m.recorder
}

// CreateAUser mocks base method.
func (m *MockSendbirdClientI) CreateAUser(arg0 context.Context, arg1 *CreateAUserRequest) (*User, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAUser", arg0, arg1)
	ret0, _ := ret[0].(*User)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CreateAUser indicates an expected call of CreateAUser.
func (mr *MockSendbirdClientIMockRecorder) CreateAUser(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAUser", reflect.TypeOf((*MockSendbirdClientI)(nil).CreateAUser), arg0, arg1)
}

// IssueASessionToken mocks base method.
func (m *MockSendbirdClientI) IssueASessionToken(arg0 context.Context, arg1 string, arg2 *IssueASessionTokenRequest) (*IssueASessionTokenResponse, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IssueASessionToken", arg0, arg1, arg2)
	ret0, _ := ret[0].(*IssueASessionTokenResponse)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// IssueASessionToken indicates an expected call of IssueASessionToken.
func (mr *MockSendbirdClientIMockRecorder) IssueASessionToken(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IssueASessionToken", reflect.TypeOf((*MockSendbirdClientI)(nil).IssueASessionToken), arg0, arg1, arg2)
}

// ListGroupChannels mocks base method.
func (m *MockSendbirdClientI) ListGroupChannels(arg0 context.Context, arg1 *ListGroupChannelsRequest) (*ListGroupChannelsResponse, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListGroupChannels", arg0, arg1)
	ret0, _ := ret[0].(*ListGroupChannelsResponse)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListGroupChannels indicates an expected call of ListGroupChannels.
func (mr *MockSendbirdClientIMockRecorder) ListGroupChannels(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListGroupChannels", reflect.TypeOf((*MockSendbirdClientI)(nil).ListGroupChannels), arg0, arg1)
}

// ListOpenChannels mocks base method.
func (m *MockSendbirdClientI) ListOpenChannels(arg0 context.Context, arg1 *ListOpenChannelsRequest) (*ListOpenChannelsResponse, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOpenChannels", arg0, arg1)
	ret0, _ := ret[0].(*ListOpenChannelsResponse)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListOpenChannels indicates an expected call of ListOpenChannels.
func (mr *MockSendbirdClientIMockRecorder) ListOpenChannels(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOpenChannels", reflect.TypeOf((*MockSendbirdClientI)(nil).ListOpenChannels), arg0, arg1)
}

// UpdateAUser mocks base method.
func (m *MockSendbirdClientI) UpdateAUser(arg0 context.Context, arg1 string, arg2 *UpdateAUserRequest) (*User, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAUser", arg0, arg1, arg2)
	ret0, _ := ret[0].(*User)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// UpdateAUser indicates an expected call of UpdateAUser.
func (mr *MockSendbirdClientIMockRecorder) UpdateAUser(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAUser", reflect.TypeOf((*MockSendbirdClientI)(nil).UpdateAUser), arg0, arg1, arg2)
}
