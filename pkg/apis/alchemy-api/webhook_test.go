package alchemyapi

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestAddSingleWebhookAddresses(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	InitDefault()

	webhookID := "wh_xsrpxxi9nvw0nsp1"
	addresses := []string{"******************************************"}

	err := alchemyObj.AddSingleWebhookAddresses(context.Background(), webhookID, addresses)
	assert.NoError(t, err)
}
