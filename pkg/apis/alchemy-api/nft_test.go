package alchemyapi

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestTokens(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()
	params := &GetNFTsParams{
		Owner:        "******************************************",
		WithMetadata: true,
	}

	resp, err := alchemyObj.GetNFTs(context.Background(), "eth", params)
	assert.Nil(t, err)
	assert.Greater(t, len(resp.OwnedNfts), 0)
}
