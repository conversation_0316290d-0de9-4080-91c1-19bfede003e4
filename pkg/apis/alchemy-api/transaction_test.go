package alchemyapi

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetTransactionReceipt(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()
	ctx := context.Background()
	resp, err := Get().GetTransactionReceipt(ctx, "matic", "0x71adb57375b1482cd14f80fcbcfe358d2b2b4f774b39a931f84a2278b378d812")
	assert.Nil(t, err)
	dataB, err := json.Marshal(resp)
	assert.Nil(t, err)
	fmt.Println(string(dataB))
}

func TestGetTransactionByHash(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()
	ctx := context.Background()
	resp, err := Get().GetTransactionByHash(ctx, "matic", "0x71adb57375b1482cd14f80fcbcfe358d2b2b4f774b39a931f84a2278b378d812")
	assert.Nil(t, err)

	dataB, err := json.Marshal(resp)
	assert.Nil(t, err)
	fmt.Println(string(dataB))
}
