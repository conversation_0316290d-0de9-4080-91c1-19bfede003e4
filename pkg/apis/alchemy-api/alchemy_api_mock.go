// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api (interfaces: IAlchemy)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/alchemy-api -package=alchemyapi -destination=alchemy_api_mock.go . IAlchemy
//

// Package alchemyapi is a generated GoMock package.
package alchemyapi

import (
	context "context"
	reflect "reflect"

	resty "github.com/go-resty/resty/v2"
	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockIAlchemy is a mock of IAlchemy interface.
type MockIAlchemy struct {
	ctrl     *gomock.Controller
	recorder *MockIAlchemyMockRecorder
}

// MockIAlchemyMockRecorder is the mock recorder for MockIAlchemy.
type MockIAlchemyMockRecorder struct {
	mock *MockIAlchemy
}

// NewMockIAlchemy creates a new mock instance.
func NewMockIAlchemy(ctrl *gomock.Controller) *MockIAlchemy {
	mock := &MockIAlchemy{ctrl: ctrl}
	mock.recorder = &MockIAlchemyMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIAlchemy) EXPECT() *MockIAlchemyMockRecorder {
	return m.recorder
}

// AddSingleWebhookAddresses mocks base method.
func (m *MockIAlchemy) AddSingleWebhookAddresses(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddSingleWebhookAddresses", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddSingleWebhookAddresses indicates an expected call of AddSingleWebhookAddresses.
func (mr *MockIAlchemyMockRecorder) AddSingleWebhookAddresses(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSingleWebhookAddresses", reflect.TypeOf((*MockIAlchemy)(nil).AddSingleWebhookAddresses), arg0, arg1, arg2)
}

// BlockNumber mocks base method.
func (m *MockIAlchemy) BlockNumber(arg0 context.Context, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BlockNumber", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BlockNumber indicates an expected call of BlockNumber.
func (mr *MockIAlchemyMockRecorder) BlockNumber(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BlockNumber", reflect.TypeOf((*MockIAlchemy)(nil).BlockNumber), arg0, arg1)
}

// CreateWebhook mocks base method.
func (m *MockIAlchemy) CreateWebhook(arg0 context.Context, arg1, arg2 string, arg3 []string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWebhook", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateWebhook indicates an expected call of CreateWebhook.
func (mr *MockIAlchemyMockRecorder) CreateWebhook(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWebhook", reflect.TypeOf((*MockIAlchemy)(nil).CreateWebhook), arg0, arg1, arg2, arg3)
}

// GetAllWebhookAddresses mocks base method.
func (m *MockIAlchemy) GetAllWebhookAddresses(arg0 context.Context, arg1 string, arg2, arg3 int) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllWebhookAddresses", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllWebhookAddresses indicates an expected call of GetAllWebhookAddresses.
func (mr *MockIAlchemyMockRecorder) GetAllWebhookAddresses(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllWebhookAddresses", reflect.TypeOf((*MockIAlchemy)(nil).GetAllWebhookAddresses), arg0, arg1, arg2, arg3)
}

// GetAssetTransfers mocks base method.
func (m *MockIAlchemy) GetAssetTransfers(arg0 context.Context, arg1 string, arg2 *GetAssetTransfersParams) (*GetAssetTransfersResp, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssetTransfers", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetAssetTransfersResp)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAssetTransfers indicates an expected call of GetAssetTransfers.
func (mr *MockIAlchemyMockRecorder) GetAssetTransfers(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssetTransfers", reflect.TypeOf((*MockIAlchemy)(nil).GetAssetTransfers), arg0, arg1, arg2)
}

// GetBalance mocks base method.
func (m *MockIAlchemy) GetBalance(arg0 context.Context, arg1, arg2, arg3 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBalance", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBalance indicates an expected call of GetBalance.
func (mr *MockIAlchemyMockRecorder) GetBalance(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBalance", reflect.TypeOf((*MockIAlchemy)(nil).GetBalance), arg0, arg1, arg2, arg3)
}

// GetERC20Decimals mocks base method.
func (m *MockIAlchemy) GetERC20Decimals(arg0 context.Context, arg1, arg2 string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetERC20Decimals", arg0, arg1, arg2)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetERC20Decimals indicates an expected call of GetERC20Decimals.
func (mr *MockIAlchemyMockRecorder) GetERC20Decimals(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetERC20Decimals", reflect.TypeOf((*MockIAlchemy)(nil).GetERC20Decimals), arg0, arg1, arg2)
}

// GetEstimateGas mocks base method.
func (m *MockIAlchemy) GetEstimateGas(arg0 context.Context, arg1 string, arg2 *Transaction) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEstimateGas", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEstimateGas indicates an expected call of GetEstimateGas.
func (mr *MockIAlchemyMockRecorder) GetEstimateGas(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEstimateGas", reflect.TypeOf((*MockIAlchemy)(nil).GetEstimateGas), arg0, arg1, arg2)
}

// GetGasPrice mocks base method.
func (m *MockIAlchemy) GetGasPrice(arg0 context.Context, arg1 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGasPrice", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGasPrice indicates an expected call of GetGasPrice.
func (mr *MockIAlchemyMockRecorder) GetGasPrice(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGasPrice", reflect.TypeOf((*MockIAlchemy)(nil).GetGasPrice), arg0, arg1)
}

// GetLogs mocks base method.
func (m *MockIAlchemy) GetLogs(arg0 context.Context, arg1, arg2, arg3 string) (*GetLogsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLogs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*GetLogsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLogs indicates an expected call of GetLogs.
func (mr *MockIAlchemyMockRecorder) GetLogs(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLogs", reflect.TypeOf((*MockIAlchemy)(nil).GetLogs), arg0, arg1, arg2, arg3)
}

// GetNFTMetadata mocks base method.
func (m *MockIAlchemy) GetNFTMetadata(arg0 context.Context, arg1 string, arg2 *GetNFTMetadataParams) (*GetNFTMetadataResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNFTMetadata", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetNFTMetadataResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFTMetadata indicates an expected call of GetNFTMetadata.
func (mr *MockIAlchemyMockRecorder) GetNFTMetadata(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFTMetadata", reflect.TypeOf((*MockIAlchemy)(nil).GetNFTMetadata), arg0, arg1, arg2)
}

// GetNFTs mocks base method.
func (m *MockIAlchemy) GetNFTs(arg0 context.Context, arg1 string, arg2 *GetNFTsParams) (*GetNFTsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNFTs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetNFTsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNFTs indicates an expected call of GetNFTs.
func (mr *MockIAlchemyMockRecorder) GetNFTs(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNFTs", reflect.TypeOf((*MockIAlchemy)(nil).GetNFTs), arg0, arg1, arg2)
}

// GetNextNonce mocks base method.
func (m *MockIAlchemy) GetNextNonce(arg0 context.Context, arg1, arg2 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNextNonce", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNextNonce indicates an expected call of GetNextNonce.
func (mr *MockIAlchemyMockRecorder) GetNextNonce(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNextNonce", reflect.TypeOf((*MockIAlchemy)(nil).GetNextNonce), arg0, arg1, arg2)
}

// GetTokenMetadata mocks base method.
func (m *MockIAlchemy) GetTokenMetadata(arg0 context.Context, arg1, arg2 string) (*GetTokenMetadataResp, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenMetadata", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetTokenMetadataResp)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetTokenMetadata indicates an expected call of GetTokenMetadata.
func (mr *MockIAlchemyMockRecorder) GetTokenMetadata(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenMetadata", reflect.TypeOf((*MockIAlchemy)(nil).GetTokenMetadata), arg0, arg1, arg2)
}

// GetTransactionByHash mocks base method.
func (m *MockIAlchemy) GetTransactionByHash(arg0 context.Context, arg1, arg2 string) (*GetTransactionByHashResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionByHash", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetTransactionByHashResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionByHash indicates an expected call of GetTransactionByHash.
func (mr *MockIAlchemyMockRecorder) GetTransactionByHash(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionByHash", reflect.TypeOf((*MockIAlchemy)(nil).GetTransactionByHash), arg0, arg1, arg2)
}

// GetTransactionByHashWithRetry mocks base method.
func (m *MockIAlchemy) GetTransactionByHashWithRetry(arg0 context.Context, arg1, arg2 string) (*domain.Transaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionByHashWithRetry", arg0, arg1, arg2)
	ret0, _ := ret[0].(*domain.Transaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionByHashWithRetry indicates an expected call of GetTransactionByHashWithRetry.
func (mr *MockIAlchemyMockRecorder) GetTransactionByHashWithRetry(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionByHashWithRetry", reflect.TypeOf((*MockIAlchemy)(nil).GetTransactionByHashWithRetry), arg0, arg1, arg2)
}

// GetTransactionReceipt mocks base method.
func (m *MockIAlchemy) GetTransactionReceipt(arg0 context.Context, arg1, arg2 string) (*GetTransactionReceiptResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionReceipt", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetTransactionReceiptResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionReceipt indicates an expected call of GetTransactionReceipt.
func (mr *MockIAlchemyMockRecorder) GetTransactionReceipt(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionReceipt", reflect.TypeOf((*MockIAlchemy)(nil).GetTransactionReceipt), arg0, arg1, arg2)
}

// GetTransactionReceiptWithRetry mocks base method.
func (m *MockIAlchemy) GetTransactionReceiptWithRetry(arg0 context.Context, arg1, arg2 string) (*domain.TxReceipt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionReceiptWithRetry", arg0, arg1, arg2)
	ret0, _ := ret[0].(*domain.TxReceipt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionReceiptWithRetry indicates an expected call of GetTransactionReceiptWithRetry.
func (mr *MockIAlchemyMockRecorder) GetTransactionReceiptWithRetry(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionReceiptWithRetry", reflect.TypeOf((*MockIAlchemy)(nil).GetTransactionReceiptWithRetry), arg0, arg1, arg2)
}

// GetTransactionReceiptWithURL mocks base method.
func (m *MockIAlchemy) GetTransactionReceiptWithURL(arg0 context.Context, arg1, arg2 string) (*GetTransactionReceiptResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactionReceiptWithURL", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetTransactionReceiptResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransactionReceiptWithURL indicates an expected call of GetTransactionReceiptWithURL.
func (mr *MockIAlchemyMockRecorder) GetTransactionReceiptWithURL(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactionReceiptWithURL", reflect.TypeOf((*MockIAlchemy)(nil).GetTransactionReceiptWithURL), arg0, arg1, arg2)
}

// RemoveSingleWebhookAddresses mocks base method.
func (m *MockIAlchemy) RemoveSingleWebhookAddresses(arg0 context.Context, arg1 string, arg2 []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveSingleWebhookAddresses", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveSingleWebhookAddresses indicates an expected call of RemoveSingleWebhookAddresses.
func (mr *MockIAlchemyMockRecorder) RemoveSingleWebhookAddresses(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveSingleWebhookAddresses", reflect.TypeOf((*MockIAlchemy)(nil).RemoveSingleWebhookAddresses), arg0, arg1, arg2)
}

// SendRawTransaction mocks base method.
func (m *MockIAlchemy) SendRawTransaction(arg0 context.Context, arg1, arg2 string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendRawTransaction", arg0, arg1, arg2)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendRawTransaction indicates an expected call of SendRawTransaction.
func (mr *MockIAlchemyMockRecorder) SendRawTransaction(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendRawTransaction", reflect.TypeOf((*MockIAlchemy)(nil).SendRawTransaction), arg0, arg1, arg2)
}

// UpdateWebhookIsActive mocks base method.
func (m *MockIAlchemy) UpdateWebhookIsActive(arg0 context.Context, arg1 string, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateWebhookIsActive", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateWebhookIsActive indicates an expected call of UpdateWebhookIsActive.
func (mr *MockIAlchemyMockRecorder) UpdateWebhookIsActive(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateWebhookIsActive", reflect.TypeOf((*MockIAlchemy)(nil).UpdateWebhookIsActive), arg0, arg1, arg2)
}
