package alchemyapi

import (
	"context"
	"fmt"
	"testing"

	dbmodel "github.com/kryptogo/kg-wallet-backend/pkg/db/model"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetLogs(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()

	resp, err := alchemyObj.GetLogs(context.Background(), dbmodel.ChainIDEthereum, "latest", "latest")
	assert.Nil(t, err)
	fmt.Println(resp)
}
