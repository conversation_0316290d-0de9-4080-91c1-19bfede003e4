package covalenthq

import (
	"context"
	"strings"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetOasysTokenBalances(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()
	s := assert.New(t)

	address := "******************************************"

	resp, err := covalentObj.GetTokenBalances(context.Background(), address, model.ChainNoOasys)

	s.NoError(err)
	s.NotNil(resp)
	s.False(resp.Error)
	s.Equal(resp.Data.Address, strings.ToLower(address))
	s.Equal(resp.Data.ChainID, 248)

	var token *TokenItem
	for index, item := range resp.Data.Items {
		if item.ContractTickerSymbol != "OAS" {
			continue
		}

		token = resp.Data.Items[index]
		break
	}

	s.NotNil(token)
	s.NotEqual(token.Balance, "")
	s.NotEqual(token.Balance, "0")
}
