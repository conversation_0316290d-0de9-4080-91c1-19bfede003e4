// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/covalenthq (interfaces: ICovalent)
//
// Generated by this command:
//
//	mockgen -package=covalenthq -destination=covalent_api_mock.go . ICovalent
//

// Package covalenthq is a generated GoMock package.
package covalenthq

import (
	context "context"
	reflect "reflect"

	resty "github.com/go-resty/resty/v2"
	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockICovalent is a mock of ICovalent interface.
type MockICovalent struct {
	ctrl     *gomock.Controller
	recorder *MockICovalentMockRecorder
}

// MockICovalentMockRecorder is the mock recorder for MockICovalent.
type MockICovalentMockRecorder struct {
	mock *MockICovalent
}

// NewMockICovalent creates a new mock instance.
func NewMockICovalent(ctrl *gomock.Controller) *MockICovalent {
	mock := &MockICovalent{ctrl: ctrl}
	mock.recorder = &MockICovalentMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICovalent) EXPECT() *MockICovalentMockRecorder {
	return m.recorder
}

// AssetFetcher mocks base method.
func (m *MockICovalent) AssetFetcher(arg0 domain.Chain) domain.AssetFetcher {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssetFetcher", arg0)
	ret0, _ := ret[0].(domain.AssetFetcher)
	return ret0
}

// AssetFetcher indicates an expected call of AssetFetcher.
func (mr *MockICovalentMockRecorder) AssetFetcher(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssetFetcher", reflect.TypeOf((*MockICovalent)(nil).AssetFetcher), arg0)
}

// GetNftBalances mocks base method.
func (m *MockICovalent) GetNftBalances(arg0 context.Context, arg1 string, arg2 int) (*GetBalancesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNftBalances", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetBalancesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNftBalances indicates an expected call of GetNftBalances.
func (mr *MockICovalentMockRecorder) GetNftBalances(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNftBalances", reflect.TypeOf((*MockICovalent)(nil).GetNftBalances), arg0, arg1, arg2)
}

// GetRoninTransactions mocks base method.
func (m *MockICovalent) GetRoninTransactions(arg0 context.Context, arg1 string, arg2 int) (*GetTransactionsResp, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRoninTransactions", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetTransactionsResp)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRoninTransactions indicates an expected call of GetRoninTransactions.
func (mr *MockICovalentMockRecorder) GetRoninTransactions(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoninTransactions", reflect.TypeOf((*MockICovalent)(nil).GetRoninTransactions), arg0, arg1, arg2)
}

// GetTokenBalances mocks base method.
func (m *MockICovalent) GetTokenBalances(arg0 context.Context, arg1 string, arg2 int) (*GetBalancesResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenBalances", arg0, arg1, arg2)
	ret0, _ := ret[0].(*GetBalancesResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenBalances indicates an expected call of GetTokenBalances.
func (mr *MockICovalentMockRecorder) GetTokenBalances(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenBalances", reflect.TypeOf((*MockICovalent)(nil).GetTokenBalances), arg0, arg1, arg2)
}

// GetTransactions mocks base method.
func (m *MockICovalent) GetTransactions(arg0 context.Context, arg1 string, arg2, arg3 int) (*GetTransactionsResp, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransactions", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*GetTransactionsResp)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetTransactions indicates an expected call of GetTransactions.
func (mr *MockICovalentMockRecorder) GetTransactions(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransactions", reflect.TypeOf((*MockICovalent)(nil).GetTransactions), arg0, arg1, arg2, arg3)
}
