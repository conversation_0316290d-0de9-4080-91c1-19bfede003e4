package covalenthq

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestCovalentAssetFetcher(t *testing.T) {
	InitDefault()

	fetcher := Get().AssetFetcher(domain.Ronin)
	assert.NotNil(t, fetcher)

	t.Run("SupportedChains", func(t *testing.T) {
		chains := fetcher.SupportedChains()
		assert.Equal(t, []domain.Chain{domain.Ronin}, chains)
	})

	t.Run("SupportedTypes", func(t *testing.T) {
		types := fetcher.SupportedTypes()
		assert.Equal(t, []domain.AssetType{domain.AssetTypeToken}, types)
	})

	t.Run("GetAssets Ronin", func(t *testing.T) {
		testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "COVALENT_API_KEY"})

		ctx := context.Background()
		address := domain.NewEvmAddress("******************************************")
		chains := []domain.Chain{domain.Ronin}
		types := []domain.AssetType{domain.AssetTypeToken}

		assets, err := fetcher.GetAssets(ctx, address, chains, types)

		assert.NoError(t, err)
		assert.NotNil(t, assets)
		assert.NotEmpty(t, assets.Tokens)

		for _, token := range assets.Tokens {
			t.Logf("Token: %s, Symbol: %s, ID: %s, Amount: %s", token.Name(), token.Symbol(), token.ID(), token.Amount.String())
		}
	})

	t.Run("GetAssets Oasys", func(t *testing.T) {
		testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "COVALENT_API_KEY"})

		fetcher := Get().AssetFetcher(domain.Oasys)
		assert.NotNil(t, fetcher)

		ctx := context.Background()
		address := domain.NewEvmAddress("******************************************")
		chains := []domain.Chain{domain.Oasys}
		types := []domain.AssetType{domain.AssetTypeToken}

		assets, err := fetcher.GetAssets(ctx, address, chains, types)

		assert.NoError(t, err)
		assert.NotNil(t, assets)
		assert.NotEmpty(t, assets.Tokens)

		for _, token := range assets.Tokens {
			t.Logf("Token: %s, Symbol: %s, ID: %s, Amount: %s", token.Name(), token.Symbol(), token.ID(), token.Amount.String())
		}
	})
}
