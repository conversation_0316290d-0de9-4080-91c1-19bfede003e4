package lifiapi

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetQuote(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API"})
	InitDefault(domain.NewAllPassRateLimiter())
	ctx := context.Background()

	params := &QuoteParams{
		FromChain:      domain.BaseChain,
		FromToken:      domain.BaseChain.MainToken().ID(),
		FromAmount:     "1200000000000000", // 1 ETH
		FromAddress:    "******************************************",
		ToChain:        domain.Solana,
		ToToken:        domain.Solana.MainToken().ID(),
		ToAddress:      "D4SySRcEDwnsfDTDMbsHoHHiKnGh241d9dJvdB7gMfJ7",
		SkipSimulation: true,
	}

	resp, err := Get().GetQuote(ctx, params)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.NotEmpty(t, resp.Action)
	t.Logf("value, gas price and limit: %s, %s, %s", resp.TransactionRequest.Value, resp.TransactionRequest.GasPrice, resp.TransactionRequest.GasLimit)
	t.Logf("tx request data: %s", resp.TransactionRequest.Data)
	// t.Logf("quote response: %+v", resp)
}
