package slackapi

import (
	"fmt"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
)

func TestSendCashLevelNotification(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	if config.GetString("SLACK_WEBHOOK_URL") == "" {
		t.Skip("Slack webhook is empty")
	}
	err := SendCashLevelNotification("TESTING", "TESTING")
	if err != nil {
		fmt.Printf("Failed to send cash level notification: %s", err.<PERSON>rror())
	}
}
