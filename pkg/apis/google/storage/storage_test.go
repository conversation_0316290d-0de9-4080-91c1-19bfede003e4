package storage

import (
	"context"
	"encoding/base64"
	"fmt"
	"mime"
	"os"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
)

// To upload to real google storage bucket, remove the STORAGE_EMULATOR_HOST environment variable
// and set the PROJECT_ID=kryptogo-wallet-app-dev
func TestUploadImage(t *testing.T) {
	InitDefault(domain.NewAllPassRateLimiter())

	for _, mimeType := range []string{"image/png", "image/heic", "image/webp", "image/heif"} {
		ext, err := mime.ExtensionsByType(mimeType)
		if err != nil || len(ext) == 0 {
			t.Fatalf("failed to get extension for %s: %v", mimeType, err)
		}
		t.Run(fmt.Sprintf("upload %s image", ext[0]), func(t *testing.T) {
			testImagePath := fmt.Sprintf("test/test%s", ext[0])
			image, err := os.ReadFile(testImagePath)
			if err != nil {
				t.Fatalf("failed to read image: %v", err)
			}
			imageBase64 := base64.StdEncoding.EncodeToString(image)
			folder := "public/avatar"
			filename := "kryptogo-ci-test"
			publicURL, err := Get().UploadImage(context.Background(), folder, filename, imageBase64, mimeType)
			if err != nil {
				t.Fatalf("failed to upload image: %v", err)
			}
			t.Logf("uploaded image to %s", publicURL)
		})
	}

	t.Run("duplicate upload", func(t *testing.T) {
		t.Skip("skipping test because it requires manual check")
		// Test duplicate upload
		folder := "public/avatar"
		filename := "kryptogo-ci-test"
		testImagePath := "test/test2.png"
		image, err := os.ReadFile(testImagePath)
		if err != nil {
			t.Fatalf("failed to read image: %v", err)
		}
		imageBase64 := base64.StdEncoding.EncodeToString(image)
		_, err = Get().UploadImage(context.Background(), folder, filename, imageBase64, "image/png")
		if err != nil {
			t.Fatalf("failed to upload image: %v", err)
		}
		// Please check if it replaces the existing kryptogo-ci-test.png manually
	})
}

func TestGetPublicURL(t *testing.T) {
	// Test won't pass in emulator
	if os.Getenv("STORAGE_EMULATOR_HOST") != "" {
		t.Skip("skipping test because STORAGE_EMULATOR_HOST is set")
	}
	InitDefault(domain.NewAllPassRateLimiter())

	for _, mimeType := range []string{"image/png", "image/heic", "image/webp", "image/heif"} {
		ext, err := mime.ExtensionsByType(mimeType)
		if err != nil {
			t.Fatalf("failed to get extension: %v", err)
		}
		t.Run(fmt.Sprintf("get public URL for %s image", ext[0]), func(t *testing.T) {
			objectPath := fmt.Sprintf("public/avatar/kryptogo-ci-test%s", ext[0])
			publicURL, err := Get().GetPublicURL(context.Background(), objectPath)
			if err != nil {
				t.Fatalf("failed to get public URL: %v", err)
			}
			t.Logf("public URL: %s", publicURL)
		})
	}
}
