// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/google/oauth (interfaces: Service)
//
// Generated by this command:
//
//	mockgen -package=oauth -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/google/oauth -destination=oauth_mock.go . Service
//

// Package oauth is a generated GoMock package.
package oauth

import (
	reflect "reflect"

	resty "github.com/go-resty/resty/v2"
	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// ExchangeAccessToken mocks base method.
func (m *MockService) ExchangeAccessToken(arg0 string) (*AccessTokenResponse, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExchangeAccessToken", arg0)
	ret0, _ := ret[0].(*AccessTokenResponse)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ExchangeAccessToken indicates an expected call of ExchangeAccessToken.
func (mr *MockServiceMockRecorder) ExchangeAccessToken(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeAccessToken", reflect.TypeOf((*MockService)(nil).ExchangeAccessToken), arg0)
}

// GenerateOAuthURL mocks base method.
func (m *MockService) GenerateOAuthURL(arg0 string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateOAuthURL", arg0)
	ret0, _ := ret[0].(string)
	return ret0
}

// GenerateOAuthURL indicates an expected call of GenerateOAuthURL.
func (mr *MockServiceMockRecorder) GenerateOAuthURL(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateOAuthURL", reflect.TypeOf((*MockService)(nil).GenerateOAuthURL), arg0)
}

// GetUserInfo mocks base method.
func (m *MockService) GetUserInfo(arg0 string) (*UserInfoResponse, *resty.Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserInfo", arg0)
	ret0, _ := ret[0].(*UserInfoResponse)
	ret1, _ := ret[1].(*resty.Response)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserInfo indicates an expected call of GetUserInfo.
func (mr *MockServiceMockRecorder) GetUserInfo(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserInfo", reflect.TypeOf((*MockService)(nil).GetUserInfo), arg0)
}
