package bridgeapi

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/core"
	"github.com/kryptogo/kg-wallet-backend/pkg/kglog"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
)

const (
	defaultTimeout = 30 * time.Second
)

var (
	bridgeObj domain.BridgeAPIClient
)

type bridgeAPIClient struct {
	client  resty.Client
	apiKey  string
	baseURL string
}

// BridgeAPIErrorResponse represents error response from Bridge API
type BridgeAPIErrorResponse struct {
	ID      string                 `json:"id"`      // For duplicate errors, this contains the existing account ID
	Code    string                 `json:"code"`    // Error code like "duplicate_external_account" or "invalid_parameters"
	Message string                 `json:"message"` // Human readable error message
	Source  *BridgeErrorSource     `json:"source"`  // Additional error details for parameter validation errors
}

// BridgeErrorSource represents the source of a Bridge API error
type BridgeErrorSource struct {
	Location string                 `json:"location"` // e.g., "body"
	Key      map[string]interface{} `json:"key"`      // e.g., {"email": "invalid email format"}
}

// IsParameterValidationError checks if this is a parameter validation error
func (e *BridgeAPIErrorResponse) IsParameterValidationError() bool {
	return e.Code == "invalid_parameters"
}

// GetParameterErrors returns a map of parameter names to error messages
func (e *BridgeAPIErrorResponse) GetParameterErrors() map[string]string {
	if e.Source == nil || e.Source.Key == nil {
		return nil
	}
	
	errors := make(map[string]string)
	for key, value := range e.Source.Key {
		if strValue, ok := value.(string); ok {
			errors[key] = strValue
		}
	}
	return errors
}

// InitDefault initializes the default Bridge API client
func InitDefault() {
	apiKey := config.GetString("BRIDGE_API_KEY")
	if apiKey == "" {
		kglog.Error("Cannot get Bridge API key")
	}

	baseURL := config.GetString("BRIDGE_API_URL")
	if baseURL == "" {
		kglog.Error("Cannot get Bridge API URL")
	}

	client := resty.NewRestyClient().
		OnBeforeRequest(core.RestyReqURLInjector).
		OnAfterResponse(core.RestyRespLogger()).
		SetBaseURL(baseURL).
		SetTimeout(defaultTimeout)

	bridgeObj = &bridgeAPIClient{
		client:  client,
		apiKey:  apiKey,
		baseURL: baseURL,
	}
}

// Get returns the Bridge API client singleton
func Get() domain.BridgeAPIClient {
	return bridgeObj
}

// Set sets the Bridge API client (useful for mocking)
func Set(client domain.BridgeAPIClient) {
	bridgeObj = client
}

// generateIdempotencyKey generates a random UUID-like idempotency key
func generateIdempotencyKey() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// CreateKYCLink creates a KYC link via Bridge API
func (c *bridgeAPIClient) CreateKYCLink(ctx context.Context, req *domain.BridgeCreateKYCLinkRequest) (*domain.BridgeCreateKYCLinkResponse, error) {
	kglog.InfoWithDataCtx(ctx, "Creating Bridge KYC link", map[string]interface{}{
		"email":     req.Email,
		"full_name": req.FullName,
		"type":      req.Type,
	})

	var response domain.BridgeCreateKYCLinkResponse

	idempotencyKey := generateIdempotencyKey()

	resp, err := c.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("Api-Key", c.apiKey).
		SetHeader("Idempotency-Key", idempotencyKey).
		SetBody(req).
		SetResult(&response).
		Post("/v0/kyc_links")

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to call Bridge API", map[string]interface{}{
			"error": err.Error(),
		})
		return nil, fmt.Errorf("failed to call Bridge API: %w", err)
	}

	if resp.StatusCode() >= 400 {
		// Parse error response
		var errorResp BridgeAPIErrorResponse
		if jsonErr := json.Unmarshal(resp.Body(), &errorResp); jsonErr == nil {
			// Handle parameter validation errors
			if errorResp.IsParameterValidationError() {
				kglog.ErrorWithDataCtx(ctx, "Bridge API returned parameter validation error", map[string]interface{}{
					"email": req.Email,
					"errors": errorResp.GetParameterErrors(),
				})
				return nil, fmt.Errorf("Bridge API returned parameter validation error: %s", errorResp.Message)
			}
		}

		kglog.ErrorWithDataCtx(ctx, "Bridge API returned error", map[string]interface{}{
			"status_code": resp.StatusCode(),
			"response":    string(resp.Body()),
		})
		return nil, fmt.Errorf("Bridge API returned error: status %d, response: %s", resp.StatusCode(), string(resp.Body()))
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created Bridge KYC link", map[string]interface{}{
		"customer_id": response.CustomerID,
		"kyc_status":  response.KYCStatus,
		"tos_status":  response.TOSStatus,
	})

	return &response, nil
}

// CreateExternalAccount creates an external account via Bridge API
func (c *bridgeAPIClient) CreateExternalAccount(ctx context.Context, customerID string, req *domain.BridgeCreateExternalAccountRequest) (*domain.BridgeCreateExternalAccountResponse, error) {
	kglog.InfoWithDataCtx(ctx, "Creating Bridge external account", map[string]interface{}{
		"customer_id":        customerID,
		"currency":           req.Currency,
		"bank_name":          req.BankName,
		"account_owner_name": req.AccountOwnerName,
		"account_owner_type": req.AccountOwnerType,
		"account_type":       req.AccountType,
	})

	var response domain.BridgeCreateExternalAccountResponse

	idempotencyKey := generateIdempotencyKey()

	endpoint := fmt.Sprintf("/v0/customers/%s/external_accounts", customerID)

	resp, err := c.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("Api-Key", c.apiKey).
		SetHeader("Idempotency-Key", idempotencyKey).
		SetBody(req).
		SetResult(&response).
		Post(endpoint)

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to call Bridge API for external account", map[string]interface{}{
			"customer_id": customerID,
			"error":       err.Error(),
		})
		return nil, fmt.Errorf("failed to call Bridge API: %w", err)
	}

	if resp.StatusCode() >= 400 {
		// Parse error response
		var errorResp BridgeAPIErrorResponse
		if jsonErr := json.Unmarshal(resp.Body(), &errorResp); jsonErr == nil {
			// Handle duplicate external account error specially
			if errorResp.Code == "duplicate_external_account" {
				kglog.InfoWithDataCtx(ctx, "Duplicate external account detected, returning existing account", map[string]interface{}{
					"customer_id":          customerID,
					"existing_account_id":  errorResp.ID,
					"message":              errorResp.Message,
				})
				
				// Return the existing account as if it was successfully created
				// We'll fetch the full account details from Bridge API
				return c.getExternalAccountByID(ctx, customerID, errorResp.ID)
			}
			// Handle parameter validation errors
			if errorResp.IsParameterValidationError() {
				kglog.ErrorWithDataCtx(ctx, "Bridge API returned parameter validation error for external account", map[string]interface{}{
					"customer_id": customerID,
					"errors": errorResp.GetParameterErrors(),
				})
				return nil, fmt.Errorf("Bridge API returned parameter validation error for external account: %s", errorResp.Message)
			}
		}

		kglog.ErrorWithDataCtx(ctx, "Bridge API returned error for external account", map[string]interface{}{
			"customer_id":  customerID,
			"status_code":  resp.StatusCode(),
			"response":     string(resp.Body()),
		})
		return nil, fmt.Errorf("Bridge API returned error: status %d, response: %s", resp.StatusCode(), string(resp.Body()))
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created Bridge external account", map[string]interface{}{
		"customer_id":          customerID,
		"external_account_id":  response.ID,
		"account_owner_name":   response.AccountOwnerName,
		"currency":             response.Currency,
		"active":               response.Active,
	})

	return &response, nil
}

// getExternalAccountByID fetches an existing external account by ID
func (c *bridgeAPIClient) getExternalAccountByID(ctx context.Context, customerID, accountID string) (*domain.BridgeCreateExternalAccountResponse, error) {
	kglog.InfoWithDataCtx(ctx, "Fetching existing Bridge external account", map[string]interface{}{
		"customer_id": customerID,
		"account_id":  accountID,
	})

	var response domain.BridgeCreateExternalAccountResponse

	endpoint := fmt.Sprintf("/v0/customers/%s/external_accounts/%s", customerID, accountID)

	resp, err := c.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("Api-Key", c.apiKey).
		SetResult(&response).
		Get(endpoint)

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to fetch existing Bridge external account", map[string]interface{}{
			"customer_id": customerID,
			"account_id":  accountID,
			"error":       err.Error(),
		})
		return nil, fmt.Errorf("failed to fetch existing external account: %w", err)
	}

	if resp.StatusCode() >= 400 {
		kglog.ErrorWithDataCtx(ctx, "Bridge API returned error when fetching existing external account", map[string]interface{}{
			"customer_id": customerID,
			"account_id":  accountID,
			"status_code": resp.StatusCode(),
			"response":    string(resp.Body()),
		})
		return nil, fmt.Errorf("Bridge API returned error when fetching existing account: status %d, response: %s", resp.StatusCode(), string(resp.Body()))
	}

	kglog.InfoWithDataCtx(ctx, "Successfully fetched existing Bridge external account", map[string]interface{}{
		"customer_id":          customerID,
		"external_account_id":  response.ID,
		"account_owner_name":   response.AccountOwnerName,
		"currency":             response.Currency,
		"active":               response.Active,
	})

	return &response, nil
}

// CreateTransfer creates a transfer via Bridge API
func (c *bridgeAPIClient) CreateTransfer(ctx context.Context, req *domain.BridgeCreateTransferRequest) (*domain.BridgeCreateTransferResponse, error) {
	kglog.InfoWithDataCtx(ctx, "Creating Bridge transfer", map[string]interface{}{
		"amount":      req.Amount,
		"on_behalf_of": req.OnBehalfOf,
		"source_payment_rail": req.Source.PaymentRail,
		"source_currency": req.Source.Currency,
		"source_from_address": req.Source.FromAddress,
		"dest_payment_rail": req.Destination.PaymentRail,
		"dest_currency": req.Destination.Currency,
		"dest_external_account_id": req.Destination.ExternalAccountID,
	})

	var response domain.BridgeCreateTransferResponse

	idempotencyKey := generateIdempotencyKey()

	resp, err := c.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("Api-Key", c.apiKey).
		SetHeader("Idempotency-Key", idempotencyKey).
		SetBody(req).
		SetResult(&response).
		Post("/v0/transfers")

	if err != nil {
		kglog.ErrorWithDataCtx(ctx, "Failed to call Bridge API for transfer creation", map[string]interface{}{
			"on_behalf_of": req.OnBehalfOf,
			"amount": req.Amount,
			"error": err.Error(),
		})
		return nil, fmt.Errorf("failed to call Bridge API: %w", err)
	}

	if resp.StatusCode() >= 400 {
		kglog.ErrorWithDataCtx(ctx, "Bridge API returned error for transfer creation", map[string]interface{}{
			"on_behalf_of": req.OnBehalfOf,
			"amount": req.Amount,
			"status_code": resp.StatusCode(),
			"response": string(resp.Body()),
		})
		return nil, fmt.Errorf("Bridge API returned error: status %d, response: %s", resp.StatusCode(), string(resp.Body()))
	}

	kglog.InfoWithDataCtx(ctx, "Successfully created Bridge transfer", map[string]interface{}{
		"transfer_id": response.ID,
		"state": response.State,
		"on_behalf_of": response.OnBehalfOf,
		"amount": response.Amount,
		"currency": response.Currency,
	})

	return &response, nil
} 