package airdropclient

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/pkg/config"
	"github.com/kryptogo/kg-wallet-backend/pkg/db/model"
	"github.com/kryptogo/kg-wallet-backend/pkg/service/tron"
	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestPolygon(t *testing.T) {
	testutil.RequireConfigsOrSkip(t, []string{"TEST_3RD_PARTY_API", "ALCHEMY_API_KEY"})

	chainID := "polygon"
	walletAdddress := "******************************************"
	ctx := context.Background()
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(ctx, model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(ctx, model.ChainIDShasta),
	})

	client := NewAlchemyChainClient(config.GetString("ALCHEMY_API_KEY"), chainID)
	nonce, err := client.GetNextNonce(ctx, chainID, walletAdddress)
	assert.Nil(t, err)
	t.Log("nonce: ", nonce)

	txStatus, err := client.GetTransactionReceipt(ctx, "0x9e2bec89bf6ac672a9bf78a41e2c4d04df0dc62b668dc11619d54f986ae09595")
	assert.Nil(t, err)
	assert.Equal(t, "1", string(txStatus))
}

func TestSepolia(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	chainID := "sepolia"
	walletAdddress := "******************************************"
	ctx := context.Background()
	tron.Init(tron.InitParams{
		TronClient:   tron.NewGrpcClient(ctx, model.ChainIDTron),
		ShastaClient: tron.NewGrpcClient(ctx, model.ChainIDShasta),
	})

	client := NewAlchemyChainClient("", chainID)
	nonce, err := client.GetNextNonce(ctx, chainID, walletAdddress)
	assert.Nil(t, err)
	t.Log("nonce: ", nonce)

	txStatus, err := client.GetTransactionReceipt(ctx, "0x6516d3ab4ab165d7a3f7c69d986e5fa4b6cf0ab7cee46476a3664afee1adb335")
	assert.Nil(t, err)
	assert.Equal(t, "1", string(txStatus))
}
