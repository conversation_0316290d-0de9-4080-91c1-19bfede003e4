package airdropclient

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNonceFromHardhat(t *testing.T) {
	chainID := "holesky"
	walletAddress := "******************************************"
	ctx := context.Background()
	client := NewHoleskyChainClient()
	nonce, err := client.GetNextNonce(ctx, chainID, walletAddress)
	assert.Nil(t, err)
	t.Log("nonce: ", nonce)
	assert.Greater(t, nonce, int32(0))
}
