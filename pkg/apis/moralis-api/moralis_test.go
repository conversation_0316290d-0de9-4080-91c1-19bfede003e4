package moralisapi

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetTokenMetadata(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "MORALIS_API_KEY")
	tests := []struct {
		name            string
		chainID         string
		contractAddress string
		wantErr         bool
	}{
		{
			name:            "ETH USDT",
			chainID:         "eth",
			contractAddress: "******************************************", // USDT on Ethereum
			wantErr:         false,
		},
		{
			name:            "Invalid chain",
			chainID:         "999999",
			contractAddress: "******************************************",
			wantErr:         true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			metadata, resp, err := GetTokenMetadata(context.Background(), tt.chainID, tt.contractAddress)

			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, resp)
			assert.NotEmpty(t, metadata)

			if len(metadata) > 0 {
				token := metadata[0]
				assert.Equal(t, tt.contractAddress, token.Address)
				assert.NotEmpty(t, token.Name)
				assert.NotEmpty(t, token.Symbol)
				assert.NotEmpty(t, token.Decimals)
			}
		})
	}
}
