package thegraphapi

import (
	"context"
	"fmt"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestGetENSNamesWithHarry(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	data, err := GetENSNames(context.Background(), "******************************************")
	assert.Contains(t, data, "harryc.eth")
	assert.Contains(t, data, "a00012025.eth")
	fmt.Println(data, err)
}

func TestGetENSNamesWithDorara(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	data, err := GetENSNames(context.Background(), "******************************************")
	assert.Contains(t, data, "dorara.eth")
	assert.NotContains(t, data, "[d18738c230747cc93749315ce812ee7fb944109b51a5bfd5f68aa85aa9d417c7].addr.reverse")
	fmt.Println(data, err)
}
