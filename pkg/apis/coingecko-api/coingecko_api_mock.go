// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/apis/coingecko-api/common.go
//
// Generated by this command:
//
//	mockgen -source=pkg/apis/coingecko-api/common.go -destination=pkg/apis/coingecko-api/coingecko_api_mock.go -package=coingeckoapi ICoingecko
//

// Package coingeckoapi is a generated GoMock package.
package coingeckoapi

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockICoingecko is a mock of ICoingecko interface.
type MockICoingecko struct {
	ctrl     *gomock.Controller
	recorder *MockICoingeckoMockRecorder
}

// MockICoingeckoMockRecorder is the mock recorder for MockICoingecko.
type MockICoingeckoMockRecorder struct {
	mock *MockICoingecko
}

// NewMockICoingecko creates a new mock instance.
func NewMockICoingecko(ctrl *gomock.Controller) *MockICoingecko {
	mock := &MockICoingecko{ctrl: ctrl}
	mock.recorder = &MockICoingeckoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockICoingecko) EXPECT() *MockICoingeckoMockRecorder {
	return m.recorder
}

// CoinHistory mocks base method.
func (m *MockICoingecko) CoinHistory(ctx context.Context, id, date string) (CoinHistoryResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoinHistory", ctx, id, date)
	ret0, _ := ret[0].(CoinHistoryResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CoinHistory indicates an expected call of CoinHistory.
func (mr *MockICoingeckoMockRecorder) CoinHistory(ctx, id, date any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoinHistory", reflect.TypeOf((*MockICoingecko)(nil).CoinHistory), ctx, id, date)
}

// CoinList mocks base method.
func (m *MockICoingecko) CoinList(ctx context.Context) (*CoinListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoinList", ctx)
	ret0, _ := ret[0].(*CoinListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CoinList indicates an expected call of CoinList.
func (mr *MockICoingeckoMockRecorder) CoinList(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoinList", reflect.TypeOf((*MockICoingecko)(nil).CoinList), ctx)
}

// GetPrices mocks base method.
func (m *MockICoingecko) GetPrices(ctx context.Context, tokens []domain.CoingeckoID) (map[domain.CoingeckoID]domain.Price, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPrices", ctx, tokens)
	ret0, _ := ret[0].(map[domain.CoingeckoID]domain.Price)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrices indicates an expected call of GetPrices.
func (mr *MockICoingeckoMockRecorder) GetPrices(ctx, tokens any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrices", reflect.TypeOf((*MockICoingecko)(nil).GetPrices), ctx, tokens)
}

// SimplePrice mocks base method.
func (m *MockICoingecko) SimplePrice(ctx context.Context, ids []domain.CoingeckoID, vsCurrencies string) (map[domain.CoingeckoID]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimplePrice", ctx, ids, vsCurrencies)
	ret0, _ := ret[0].(map[domain.CoingeckoID]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimplePrice indicates an expected call of SimplePrice.
func (mr *MockICoingeckoMockRecorder) SimplePrice(ctx, ids, vsCurrencies any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimplePrice", reflect.TypeOf((*MockICoingecko)(nil).SimplePrice), ctx, ids, vsCurrencies)
}

// SimplePriceByAddress mocks base method.
func (m *MockICoingecko) SimplePriceByAddress(ctx context.Context, platform, address, vsCurrencies string) (SimplePriceByAddressResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SimplePriceByAddress", ctx, platform, address, vsCurrencies)
	ret0, _ := ret[0].(SimplePriceByAddressResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SimplePriceByAddress indicates an expected call of SimplePriceByAddress.
func (mr *MockICoingeckoMockRecorder) SimplePriceByAddress(ctx, platform, address, vsCurrencies any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SimplePriceByAddress", reflect.TypeOf((*MockICoingecko)(nil).SimplePriceByAddress), ctx, platform, address, vsCurrencies)
}
// GetPricesByContract mocks base method.
func (m *MockICoingecko) GetPricesByContract(ctx context.Context, tokens []domain.ChainToken) (map[domain.ChainToken]domain.Price, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPricesByContract", ctx, tokens)
	ret0, _ := ret[0].(map[domain.ChainToken]domain.Price)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPricesByContract indicates an expected call of GetPricesByContract.
func (mr *MockICoingeckoMockRecorder) GetPricesByContract(ctx, tokens any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPricesByContract", reflect.TypeOf((*MockICoingecko)(nil).GetPricesByContract), ctx, tokens)
}

// GetSupportedCurrencies mocks base method.
func (m *MockICoingecko) GetSupportedCurrencies(ctx context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSupportedCurrencies", ctx)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportedCurrencies indicates an expected call of GetSupportedCurrencies.
func (mr *MockICoingeckoMockRecorder) GetSupportedCurrencies(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportedCurrencies", reflect.TypeOf((*MockICoingecko)(nil).GetSupportedCurrencies), ctx)
}

// IsCurrencySupported mocks base method.
func (m *MockICoingecko) IsCurrencySupported(ctx context.Context, currency string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsCurrencySupported", ctx, currency)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsCurrencySupported indicates an expected call of IsCurrencySupported.
func (mr *MockICoingeckoMockRecorder) IsCurrencySupported(ctx, currency any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCurrencySupported", reflect.TypeOf((*MockICoingecko)(nil).IsCurrencySupported), ctx, currency)
}

// PricesByContractSupportedChains mocks base method.
func (m *MockICoingecko) PricesByContractSupportedChains() []domain.Chain {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PricesByContractSupportedChains")
	ret0, _ := ret[0].([]domain.Chain)
	return ret0
}

// PricesByContractSupportedChains indicates an expected call of PricesByContractSupportedChains.
func (mr *MockICoingeckoMockRecorder) PricesByContractSupportedChains() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PricesByContractSupportedChains", reflect.TypeOf((*MockICoingecko)(nil).PricesByContractSupportedChains))
}
