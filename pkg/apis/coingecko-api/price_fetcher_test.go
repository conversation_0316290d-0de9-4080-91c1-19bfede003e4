package coingeckoapi

import (
	"context"
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/kryptogo/kg-wallet-backend/pkg/resty"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	gomock "go.uber.org/mock/gomock"
)

func TestGetPrices(t *testing.T) {
	// Create a gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a mock resty client and request using the generated mocks
	mockClient := resty.NewMockClient(ctrl)
	mockRequest := resty.NewMockRequest(ctrl)

	// Define test data
	batchQuerySize := 500
	coingeckoIDs := make([]domain.CoingeckoID, 0, batchQuerySize+10)
	for i := 0; i < batchQuerySize+10; i++ {
		coingeckoIDs = append(coingeckoIDs, domain.CoingeckoID(fmt.Sprintf("token%d", i)))
	}

	// Set up expectations
	mockClient.EXPECT().OnBeforeRequest(gomock.Any()).Return(mockClient).AnyTimes()
	mockClient.EXPECT().OnAfterResponse(gomock.Any()).Return(mockClient).AnyTimes()
	mockClient.EXPECT().SetBaseURL(gomock.Any()).Return(mockClient).AnyTimes()
	mockClient.EXPECT().SetTimeout(gomock.Any()).Return(mockClient).AnyTimes()
	mockClient.EXPECT().R().Return(mockRequest).AnyTimes()
	mockRequest.EXPECT().SetContext(gomock.Any()).Return(mockRequest).AnyTimes()
	mockRequest.EXPECT().SetQueryParam("ids", gomock.Any()).DoAndReturn(func(key, value string) resty.Request {
		if len(value) > 1000 {
			assert.Equal(t, value, strings.Join(lo.Map(coingeckoIDs[:batchQuerySize], func(id domain.CoingeckoID, _ int) string {
				return string(id)
			}), ","))
		} else {
			assert.Equal(t, value, strings.Join(lo.Map(coingeckoIDs[batchQuerySize:], func(id domain.CoingeckoID, _ int) string {
				return string(id)
			}), ","))
		}
		return mockRequest
	}).AnyTimes()
	mockRequest.EXPECT().SetQueryParam("vs_currencies", gomock.Any()).DoAndReturn(func(key, value string) resty.Request {
		assert.Equal(t, value, "usd")
		return mockRequest
	}).AnyTimes()
	mockRequest.EXPECT().SetResult(gomock.Any()).DoAndReturn(func(result *SimplePriceResp) resty.Request {
		for _, id := range coingeckoIDs {
			(*result)[string(id)] = map[string]float64{
				"usd": 1.0,
			}
		}
		return mockRequest
	}).AnyTimes()
	mockRequest.EXPECT().Get("/simple/price").Return(&resty.Response{}, nil).AnyTimes()

	// Initialize the coingeckoAPI with the mock client
	rateLimiter := domain.NewAllPassRateLimiter()
	InitResty(mockClient, rateLimiter)

	// Call GetPrices
	prices, err := coingeckoObj.GetPrices(context.Background(), coingeckoIDs)

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, prices)
	assert.Equal(t, 510, len(prices))
}

func TestGetPricesWithRateLimiting(t *testing.T) {
	// Create a gomock controller
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create a mock resty client and request using the generated mocks
	mockClient := resty.NewMockClient(ctrl)
	mockRequest := resty.NewMockRequest(ctrl)

	// Define test data
	coingeckoIDs := []domain.CoingeckoID{"bitcoin", "ethereum"}

	// Set up expectations
	mockClient.EXPECT().OnBeforeRequest(gomock.Any()).Return(mockClient).AnyTimes()
	mockClient.EXPECT().OnAfterResponse(gomock.Any()).Return(mockClient).AnyTimes()
	mockClient.EXPECT().SetBaseURL(gomock.Any()).Return(mockClient).AnyTimes()
	mockClient.EXPECT().SetTimeout(gomock.Any()).Return(mockClient).AnyTimes()
	mockClient.EXPECT().R().Return(mockRequest).AnyTimes()
	mockRequest.EXPECT().SetContext(gomock.Any()).Return(mockRequest).AnyTimes()
	mockRequest.EXPECT().SetQueryParam(gomock.Any(), gomock.Any()).Return(mockRequest).AnyTimes()
	mockRequest.EXPECT().SetResult(gomock.Any()).Return(mockRequest).AnyTimes()
	mockRequest.EXPECT().Get("/simple/price").Return(&resty.Response{}, nil).AnyTimes()

	// Create a mock rate limiter that simulates a delay
	mockRateLimiter := domain.NewMockRateLimiter(ctrl)
	mockRateLimiter.EXPECT().Wait(gomock.Any(), "tokenQuotes", gomock.Any()).DoAndReturn(func(ctx context.Context, key string, duration time.Duration) error {
		time.Sleep(2 * time.Second) // Simulate a delay
		return nil
	}).AnyTimes()

	// Initialize the coingeckoAPI with the mock client and rate limiter
	InitResty(mockClient, mockRateLimiter)

	// Call GetPrices
	startTime := time.Now()
	_, err := coingeckoObj.GetPrices(context.Background(), coingeckoIDs)
	elapsedTime := time.Since(startTime)

	// Assertions
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, elapsedTime, 2*time.Second, "Rate limiting should introduce a delay")
}
