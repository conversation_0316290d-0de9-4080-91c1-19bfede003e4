package recaptcha

import (
	"context"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
)

func TestCheck(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")

	ctx := context.Background()
	platform := "ios"
	token := "03AFcWeA5GHa8ocPok1PC2iuQOKfTDV-MI0H5l03Vcl9XgX4zWyRtan25zyhhmSSBWa0cK7I4v3HQZd_89BLbfHHuD7icf4dCjgXapV1D7kWrWw71K0FHoIZV8hqypHgyGdU4rUK2i8HD3WLJf3pkV_ZZA6h0_-24BneAJy4ZpYyJeKgv8ByNhI6Y4wXCwTNK-a2af4d8VrahK59v4nPx_SzYj-II87Owu30Wp3EZ1nMybaK06qS0rpGwdNKR8sStdATt7ba5tMOETP8-RXxbEieNZY0Ah0hxM7wG_sOpwUJ2xAOLT1cMwMVdNkxeLEepG9MjV1T2MfMejLocvZuM80D3Qv0BfdwpD2cgcT-a_ENdk35h99iTVjo2jMgw3M5nIauoblymx4z7ttl1btRaT2n5MpZffu-4Wz7VLm4Cm1clZHsrCWYjXSYu5MhWateBTXMW90nQBfhAGI-59GUw3zhYSGVjPvLJZuB-BwL2hzoa1NgqotxPx_WD-whOvLzQ7TPcN-9phRkmMAHQhyqmN31wdbRCGmnABBbLkd0mCzTsLh7yUCupBskaGR7_1RadS5GwBzgLJqlqoVGbVOIQ2-W-ri4r1dVbx-OK_bim4FmijToeN1LGl8t4cSvLO7mzBLm8bCInmQvaJoK7z-lD8gX0ty8O7y_1dJVcreVFy8bQLopkBg8wDEBVg71WagjtXlz1gDfR_hGaIO4BNlCiBoIek6Wl7uJwEqJafZ3LA7w69qkikLJTFO12XgcT5XJWf0AchWea9aD3DGc4-hNdwh-cxw9TwzWqqKyo6qDrgDobuSDMmH9A2VJ-FRXfxW2IZbohQZwo4cCraZ9LD11_0v8-0fOFxAO-j8219-hwySIZthaq3yI7dLx6hTiRXaeXg-5XFVx5jwGZEg0AErr0w_vqAG-b_ooixrkCkC7bWDxTBau655-9M9a36P-rBmCfZa1ebKD6dODh-piWOlfLHReD2rdsydAcxxcVdrjR6XyvRH9pyqa9ulpaZ8QZG-VFnwQpF4yAqTYiGLsaGjBPUqtRk5lduf_aJJoeZGuWqvxewfRDKGLgGPy4U_7vKiZXJSwwYUwipaqpjqg_m03S7d5FgVhLR8JqN59RJvzq4uzwOMNTM8RV7LjRB3_h69CsjhyfKjuJl-k4cJZCyW5M-5Tz76RAvR-mAvQg5xq_vENkcryCfhHefJeVcU_HXxzwusxQXjJoeG1wwC_WAvEAKSGY99uZvgfCYUJUUt2LYnMCYoU00N9FefPK_A2jU6fIhKBk44nQKk5w3ZqAVTyJiii97T9DTVdXnTNPdStt3dQnqddZ8fsPvp6H7P-gEm7uDPexMBrXjOoSPffjidUTLS70vbzDt2fkmR-75O8TjN5mbuSP7W02mCQwUyYUDOgurJVloQiVf5mv4ZntoVpUBc-0BwiGWC5RjEIxk2qY4aa-iIktSVcpTkXJJVr1dSBuYZO9rOjOV_R4IykhsyzzbV1yPY61LryJ1BtfrQdBDxBvY4HOJrd-2x1ZE2ovToY9OiQ-8L_LReUweUyhnkkJPoKaU7jWFmnzzmhRXMmA58TgMYRiSWF6Mbu8rNt1yhUu-hpNOXfOt0jfnr-kHR3HWIh90hVGTRzyQyqa69fqxkPEQu5JFv4ySYsgPyO_G2kSWPyMNf4s2gz4VZHCjMUrld8ZY7DpAqbea4qaQRiUq2-8XE3lEb-iIyDieJ5BZIwu8G_nLDYX6Kd755SOgf8ALAtuUJ4ep7rGqYXmcrgUG_Y6P_WtZGF9iaD4"
	ok, kgError := Check(ctx, platform, token)
	t.Log(ok, kgError)

}
