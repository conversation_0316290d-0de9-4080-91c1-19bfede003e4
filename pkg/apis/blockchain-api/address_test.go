package blockchainapi

import (
	"context"
	"testing"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
)

func TestGetAddressInfo(t *testing.T) {
	InitDefault(domain.NewAllPassRateLimiter())
	client := Get()

	ctx := context.Background()
	// Using a known BTC address that should have balance
	addr := "******************************************"

	resp, err := client.GetAddressInfo(ctx, addr)
	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Greater(t, resp.FinalBalance, int64(0))
	assert.Equal(t, addr, resp.Address)
}

func TestGetAssets(t *testing.T) {
	InitDefault(domain.NewAllPassRateLimiter())
	client := Get()

	addr := domain.NewStrAddress("******************************************")
	assets, err := client.GetAssets(context.Background(), addr, []domain.Chain{domain.Bitcoin}, []domain.AssetType{domain.AssetTypeToken})
	assert.NoError(t, err)
	assert.NotNil(t, assets)
	assert.Equal(t, 1, len(assets.Tokens))
	t.Logf("assets: %+v", assets.Tokens[0])
	assert.True(t, assets.Tokens[0].Amount.GreaterThan(decimal.Zero))
}
