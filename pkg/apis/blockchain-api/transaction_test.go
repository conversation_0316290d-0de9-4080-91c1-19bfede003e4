package blockchainapi

import (
	"context"
	"testing"
	"time"

	"github.com/kryptogo/kg-wallet-backend/domain"
	"github.com/stretchr/testify/assert"
)

func TestGetTransaction(t *testing.T) {
	InitDefault(domain.NewAllPassRateLimiter())
	client := Get()

	ctx := context.Background()

	t.Run("Not found", func(t *testing.T) {
		txHash := "not-found"
		tx, err := client.GetTransaction(ctx, txHash)
		assert.Error(t, err)
		t.Logf("err: %v", err)
		assert.Nil(t, tx)
		assert.ErrorIs(t, domain.ErrRecordNotFound, err)
	})

	t.Run("1-to-1 transfer", func(t *testing.T) {
		txHash := "d81d645f9a8bbd6f847474458a3a82f57b86bc4c43c83150071eedb532d9c18b"
		tx, err := client.GetTransaction(ctx, txHash)
		if err != nil {
			t.Fatalf("failed to get transaction: %v", err)
		}
		assert.NotNil(t, tx)
		t.Logf("tx: %+v", tx)
		assert.Equal(t, txHash, tx.Hash)
		assert.Equal(t, int64(866632), tx.BlockID)
		assert.Equal(t, uint64(151065), tx.Inputs[0].Value)
		assert.Equal(t, "******************************************", tx.Inputs[0].Address)
		assert.Equal(t, uint64(144732), tx.Outputs[0].Value)
		assert.Equal(t, "bc1qud66veh79taucr34szltcm5wktyuhwfm7t2z3k", tx.Outputs[0].Address)
	})

	t.Run("1-to-1 transfer with 2 inputs and 2 outputs", func(t *testing.T) {
		txHash := "277bac72568fe0957b90ab71cf9f4b5dc9c4a8504f39c9b4efead58410c263aa"
		tx, err := client.GetTransaction(ctx, txHash)
		if err != nil {
			t.Fatalf("failed to get transaction: %v", err)
		}
		assert.NotNil(t, tx)
		t.Logf("tx: %+v", tx)
		assert.Equal(t, txHash, tx.Hash)
		assert.Equal(t, int64(774326), tx.BlockID)
		assert.WithinDuration(t, time.Date(2023, 1, 30, 11, 20, 59, 0, time.UTC), tx.Time, 1*time.Second)
		assert.Equal(t, uint64(1593), tx.Inputs[0].Value)
		assert.Equal(t, "bc1q7ycjhswm4emadzly8ymjaamu76eg0nj8c2t4zy", tx.Inputs[0].Address)
		assert.Equal(t, uint64(31297), tx.Inputs[1].Value)
		assert.Equal(t, "bc1q7ycjhswm4emadzly8ymjaamu76eg0nj8c2t4zy", tx.Inputs[1].Address)
		assert.Equal(t, uint64(31000), tx.Outputs[0].Value)
		assert.Equal(t, "bc1q9v9h6kz0uacvm9v5naqf94ym3qf3nn3dtattjr", tx.Outputs[0].Address)
		assert.Equal(t, uint64(1472), tx.Outputs[1].Value)
		assert.Equal(t, "bc1q7ycjhswm4emadzly8ymjaamu76eg0nj8c2t4zy", tx.Outputs[1].Address)
	})

	t.Run("Complicated transfer transaction", func(t *testing.T) {
		txHash := "99134f0541de388bec35f276827f3d70b0a2b10078eab10e451ed5826658f79d"
		tx, err := client.GetTransaction(ctx, txHash)
		if err != nil {
			t.Fatalf("failed to get transaction: %v", err)
		}
		assert.NotNil(t, tx)
		t.Logf("tx: %+v", tx)
		assert.Equal(t, txHash, tx.Hash)
		assert.Len(t, tx.Inputs, 2)
		assert.Len(t, tx.Outputs, 4)
	})
}
