// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchain-api (interfaces: IBlockchain)
//
// Generated by this command:
//
//	mockgen -package=blockchainapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/blockchain-api -destination=blockchain_api_mock.go . IBlockchain
//

// Package blockchainapi is a generated GoMock package.
package blockchainapi

import (
	context "context"
	reflect "reflect"

	domain "github.com/kryptogo/kg-wallet-backend/domain"
	gomock "go.uber.org/mock/gomock"
)

// MockIBlockchain is a mock of IBlockchain interface.
type MockIBlockchain struct {
	ctrl     *gomock.Controller
	recorder *MockIBlockchainMockRecorder
}

// MockIBlockchainMockRecorder is the mock recorder for MockIBlockchain.
type MockIBlockchainMockRecorder struct {
	mock *MockIBlockchain
}

// NewMockIBlockchain creates a new mock instance.
func NewMockIBlockchain(ctrl *gomock.Controller) *MockIBlockchain {
	mock := &MockIBlockchain{ctrl: ctrl}
	mock.recorder = &MockIBlockchainMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBlockchain) EXPECT() *MockIBlockchainMockRecorder {
	return m.recorder
}

// GetAddressInfo mocks base method.
func (m *MockIBlockchain) GetAddressInfo(arg0 context.Context, arg1 string) (*AddressResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAddressInfo", arg0, arg1)
	ret0, _ := ret[0].(*AddressResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAddressInfo indicates an expected call of GetAddressInfo.
func (mr *MockIBlockchainMockRecorder) GetAddressInfo(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAddressInfo", reflect.TypeOf((*MockIBlockchain)(nil).GetAddressInfo), arg0, arg1)
}

// GetAssets mocks base method.
func (m *MockIBlockchain) GetAssets(arg0 context.Context, arg1 domain.Address, arg2 []domain.Chain, arg3 []domain.AssetType) (*domain.AggregatedAssets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAssets", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*domain.AggregatedAssets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAssets indicates an expected call of GetAssets.
func (mr *MockIBlockchainMockRecorder) GetAssets(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAssets", reflect.TypeOf((*MockIBlockchain)(nil).GetAssets), arg0, arg1, arg2, arg3)
}

// GetTransaction mocks base method.
func (m *MockIBlockchain) GetTransaction(arg0 context.Context, arg1 string) (*domain.BitcoinTransaction, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTransaction", arg0, arg1)
	ret0, _ := ret[0].(*domain.BitcoinTransaction)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTransaction indicates an expected call of GetTransaction.
func (mr *MockIBlockchainMockRecorder) GetTransaction(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTransaction", reflect.TypeOf((*MockIBlockchain)(nil).GetTransaction), arg0, arg1)
}

// SupportedChains mocks base method.
func (m *MockIBlockchain) SupportedChains() []domain.Chain {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedChains")
	ret0, _ := ret[0].([]domain.Chain)
	return ret0
}

// SupportedChains indicates an expected call of SupportedChains.
func (mr *MockIBlockchainMockRecorder) SupportedChains() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedChains", reflect.TypeOf((*MockIBlockchain)(nil).SupportedChains))
}

// SupportedTypes mocks base method.
func (m *MockIBlockchain) SupportedTypes() []domain.AssetType {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SupportedTypes")
	ret0, _ := ret[0].([]domain.AssetType)
	return ret0
}

// SupportedTypes indicates an expected call of SupportedTypes.
func (mr *MockIBlockchainMockRecorder) SupportedTypes() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SupportedTypes", reflect.TypeOf((*MockIBlockchain)(nil).SupportedTypes))
}
