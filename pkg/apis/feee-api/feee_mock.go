// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/kryptogo/kg-wallet-backend/pkg/apis/feee-api (interfaces: IFeee)
//
// Generated by this command:
//
//	mockgen -package=feeeapi -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/feee-api -destination=feee_mock.go . IFeee
//

// Package feeeapi is a generated GoMock package.
package feeeapi

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockIFeee is a mock of IFeee interface.
type MockIFeee struct {
	ctrl     *gomock.Controller
	recorder *MockIFeeeMockRecorder
}

// MockIFeeeMockRecorder is the mock recorder for MockIFeee.
type MockIFeeeMockRecorder struct {
	mock *MockIFeee
}

// NewMockIFeee creates a new mock instance.
func NewMockIFeee(ctrl *gomock.Controller) *MockIFeee {
	mock := &MockIFeee{ctrl: ctrl}
	mock.recorder = &MockIFeeeMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIFeee) EXPECT() *MockIFeeeMockRecorder {
	return m.recorder
}

// AccountInfo mocks base method.
func (m *MockIFeee) AccountInfo(arg0 context.Context) (*AccountInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AccountInfo", arg0)
	ret0, _ := ret[0].(*AccountInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AccountInfo indicates an expected call of AccountInfo.
func (mr *MockIFeeeMockRecorder) AccountInfo(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AccountInfo", reflect.TypeOf((*MockIFeee)(nil).AccountInfo), arg0)
}

// CreateOrder mocks base method.
func (m *MockIFeee) CreateOrder(arg0 context.Context, arg1 string, arg2 int) (float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrder", arg0, arg1, arg2)
	ret0, _ := ret[0].(float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrder indicates an expected call of CreateOrder.
func (mr *MockIFeeeMockRecorder) CreateOrder(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrder", reflect.TypeOf((*MockIFeee)(nil).CreateOrder), arg0, arg1, arg2)
}
