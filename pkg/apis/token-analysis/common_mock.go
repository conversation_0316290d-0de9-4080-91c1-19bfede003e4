// Code generated by MockGen. DO NOT EDIT.
// Source: common.go
//
// Generated by this command:
//
//	mockgen -source=common.go -self_package=github.com/kryptogo/kg-wallet-backend/pkg/apis/token-analysis -destination=common_mock.go -package=tokenanalysis ITokenAnalysis
//

// Package tokenanalysis is a generated GoMock package.
package tokenanalysis

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockITokenAnalysis is a mock of ITokenAnalysis interface.
type MockITokenAnalysis struct {
	ctrl     *gomock.Controller
	recorder *MockITokenAnalysisMockRecorder
	isgomock struct{}
}

// MockITokenAnalysisMockRecorder is the mock recorder for MockITokenAnalysis.
type MockITokenAnalysisMockRecorder struct {
	mock *MockITokenAnalysis
}

// NewMockITokenAnalysis creates a new mock instance.
func NewMockITokenAnalysis(ctrl *gomock.Controller) *MockITokenAnalysis {
	mock := &MockITokenAnalysis{ctrl: ctrl}
	mock.recorder = &MockITokenAnalysisMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockITokenAnalysis) EXPECT() *MockITokenAnalysisMockRecorder {
	return m.recorder
}

// AnalyzeToken mocks base method.
func (m *MockITokenAnalysis) AnalyzeToken(ctx context.Context, tokenAddress string) (*Analysis, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnalyzeToken", ctx, tokenAddress)
	ret0, _ := ret[0].(*Analysis)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnalyzeToken indicates an expected call of AnalyzeToken.
func (mr *MockITokenAnalysisMockRecorder) AnalyzeToken(ctx, tokenAddress any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnalyzeToken", reflect.TypeOf((*MockITokenAnalysis)(nil).AnalyzeToken), ctx, tokenAddress)
}
