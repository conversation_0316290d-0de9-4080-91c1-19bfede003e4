package tokenanalysis

import (
	"context"
	"encoding/json"
	"testing"

	testutil "github.com/kryptogo/kg-wallet-backend/pkg/util/test"
	"github.com/stretchr/testify/assert"
)

func TestAnalyzeToken(t *testing.T) {
	testutil.RequireConfigOrSkip(t, "TEST_3RD_PARTY_API")
	InitDefault()
	ctx := context.Background()
	tokenAddress := "boopkpWqe68MSxLqBGogs8ZbUDN4GXaLhFwNP7mpP1i"

	analysis, err := tokenAnalysisObj.AnalyzeToken(ctx, tokenAddress)

	// Print the response for inspection
	if analysis != nil {
		responseJSON, _ := json.MarshalIndent(analysis, "", "  ")
		t.Logf("Response body: %s", string(responseJSON))
	}

	assert.Nil(t, err)
	assert.NotNil(t, analysis)
}
