-- ─────────────────────────────────────────────────────────────────────
-- 1) Load the last watermark for organizations (or epoch if missing)
DECLARE last_run TIMESTAMP DEFAULT (
  SELECT
    IFNULL(MAX(last_ts), TIMESTAMP("1970-01-01 00:00:00 UTC"))
  FROM
    `kryptogo-wallet-app-staging.MySQL.watermarks`
  WHERE
    job_id = 'organizations'
);

-- ─────────────────────────────────────────────────────────────────────
-- 2) INSERT only the new rows into organizations_raw
INSERT INTO
  `kryptogo-wallet-app-staging.MySQL.organizations_raw` (
    organization_id,
    organization_name,
    organization_created_at,
    first_user_uid,
    first_user_name,
    first_user_email
  )
SELECT
  org_data.id                           AS organization_id,
  org_data.name                         AS organization_name,
  org_data.created_at                   AS organization_created_at,
  org_data.first_user_uid,
  org_data.first_user_name,
  org_data.first_user_email
FROM
  EXTERNAL_QUERY(
    "projects/kryptogo-wallet-app-staging/locations/asia-east1/connections/MySQL",
    """
    SELECT 
      o.id,
      o.name,
      o.created_at,
      u.uid AS first_user_uid,
      u.name AS first_user_name,
      u.email AS first_user_email
    FROM studio_organizations o
    LEFT JOIN (
      SELECT 
        organization_id,
        uid,
        name,
        email,
        created_at,
        ROW_NUMBER() OVER (PARTITION BY organization_id ORDER BY created_at ASC, uid ASC) as rn
      FROM studio_users 
      WHERE deleted_at IS NULL
    ) u ON o.id = u.organization_id AND u.rn = 1
    """
  ) AS org_data
WHERE
  org_data.created_at > last_run
;

-- ─────────────────────────────────────────────────────────────────────
-- 3) Advance the watermark for next run
MERGE `kryptogo-wallet-app-staging.MySQL.watermarks` AS wm
USING (
  SELECT
    'organizations' AS job_id,
    CURRENT_TIMESTAMP() AS last_ts
) AS src
ON
  wm.job_id = src.job_id
WHEN MATCHED THEN
  UPDATE SET last_ts = src.last_ts
WHEN NOT MATCHED THEN
  INSERT (job_id, last_ts) VALUES (src.job_id, src.last_ts)
; 