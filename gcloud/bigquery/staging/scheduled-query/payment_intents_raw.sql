-- ─────────────────────────────────────────────────────────────────────
-- 1) Load the last watermark (or epoch if missing)
DECLARE last_run TIMESTAMP DEFAULT (
  SELECT
    IFNULL(MAX(last_ts), TIMESTAMP("1970-01-01 00:00:00 UTC"))
  FROM
    `kryptogo-wallet-app-staging.MySQL.watermarks`
  WHERE
    job_id = 'payment_intents'
);

-- ─────────────────────────────────────────────────────────────────────
-- 2) INSERT only the new rows into your raw table
INSERT INTO
  `kryptogo-wallet-app-staging.MySQL.payment_intents_raw` (
    org_id,
    payer_address,
    crypto_amount,
    fiat_amount,
    fiat_currency,
    pricing_mode,
    crypto_price,
    intent_timestamp,
    payment_tx_timestamp,
    aggregation_tx_timestamp,
    finalized_timestamp,
    status,
    time_to_payment_seconds,
    time_payment_to_aggregation_seconds,
    time_aggregation_to_finalized_seconds
  )
SELECT
  mi.org_id,
  mi.payer_address,
  mi.crypto_amount,
  mi.fiat_amount,
  mi.fiat_currency,
  mi.pricing_mode,
  mi.crypto_price,
  mi.created_at    AS intent_timestamp,
  mi.payment_tx_timestamp,
  mi.aggregation_tx_timestamp,
  mi.finalized_timestamp,
  mi.status,
  -- Time from created to payment made (in seconds)
  CASE 
    WHEN mi.payment_tx_timestamp IS NOT NULL 
    THEN UNIX_SECONDS(mi.payment_tx_timestamp) - UNIX_SECONDS(mi.created_at)
    ELSE NULL
  END AS time_to_payment_seconds,
  -- Time from payment made to aggregated (in seconds)
  CASE 
    WHEN mi.payment_tx_timestamp IS NOT NULL AND mi.aggregation_tx_timestamp IS NOT NULL
    THEN UNIX_SECONDS(mi.aggregation_tx_timestamp) - UNIX_SECONDS(mi.payment_tx_timestamp)
    ELSE NULL
  END AS time_payment_to_aggregation_seconds,
  -- Time from aggregated to finished (in seconds)
  CASE 
    WHEN mi.aggregation_tx_timestamp IS NOT NULL AND mi.finalized_timestamp IS NOT NULL
    THEN UNIX_SECONDS(mi.finalized_timestamp) - UNIX_SECONDS(mi.aggregation_tx_timestamp)
    ELSE NULL
  END AS time_aggregation_to_finalized_seconds
FROM
  EXTERNAL_QUERY(
    "projects/kryptogo-wallet-app-staging/locations/asia-east1/connections/MySQL",
    """
    SELECT
      org_id,
      payer_address,
      crypto_amount,
      fiat_amount,
      fiat_currency,
      pricing_mode,
      crypto_price,
      created_at,
      payment_tx_timestamp,
      aggregation_tx_timestamp,
      finalized_timestamp,
      status
    FROM payment_intents
    """
  ) AS mi
WHERE
  mi.created_at > last_run
  AND mi.status NOT IN ('expired', 'pending')
;

-- ─────────────────────────────────────────────────────────────────────
-- 3) Advance the watermark (only after the INSERT completes)
MERGE `kryptogo-wallet-app-staging.MySQL.watermarks` AS wm
USING (
  SELECT
    'payment_intents' AS job_id,
    CURRENT_TIMESTAMP() AS last_ts
) AS src
ON
  wm.job_id = src.job_id
WHEN MATCHED THEN
  UPDATE SET last_ts = src.last_ts
WHEN NOT MATCHED THEN
  INSERT (job_id, last_ts) VALUES (src.job_id, src.last_ts)
;