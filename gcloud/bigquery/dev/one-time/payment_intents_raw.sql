CREATE TABLE `kryptogo-wallet-app-dev.MySQL.payment_intents_raw` (
  org_id                               INT64,
  payer_address                        STRING,
  crypto_amount                        NUMERIC,
  fiat_amount                          NUMERIC,
  fiat_currency                        STRING,
  pricing_mode                         STRING,
  crypto_price                         NUMERIC,
  intent_timestamp                     TIMESTAMP,
  payment_tx_timestamp                 TIMESTAMP,
  aggregation_tx_timestamp             TIMESTAMP,
  finalized_timestamp                  TIMESTAMP,
  status                               STRING,
  time_to_payment_seconds              INT64,
  time_payment_to_aggregation_seconds  INT64,
  time_aggregation_to_finalized_seconds INT64
)
PARTITION BY DATE(intent_timestamp)
OPTIONS (
  description = "Append‐only dump of payment_intents from MySQL, with fiat & crypto pricing and time duration metrics"
);