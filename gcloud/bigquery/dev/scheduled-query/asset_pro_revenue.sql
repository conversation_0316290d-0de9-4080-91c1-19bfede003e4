with orders as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app-dev/locations/asia-east1/connections/MySQL",
  '''
  SELECT 
    'buy_crypto' as type, 
    orders.uid, 
    orders.organization_id, 
    orders.chain_id,
    CAST(orders.amount AS DECIMAL(22,9))* orders.price as amount, 
    orders.contract_address,
    orders.created_at as timestamp, 
    orders.profit_margin as profit
    FROM orders
    WHERE orders.status = 'done'
  ''')
), gas_swaps as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app-dev/locations/asia-east1/connections/MySQL",
  '''
  SELECT 
    'gas_swap' as type, 
    studio_organization_gas_swaps.uid, 
    studio_organization_gas_swaps.organization_id, 
    studio_organization_gas_swaps.chain_id,
    CAST(studio_organization_gas_swaps.amount AS DECIMAL(22,9)) as amount, 
    studio_organization_gas_swaps.token_address as contract_address,
    studio_organization_gas_swaps.created_at as timestamp, 
    studio_organization_gas_swaps.profit_margin as profit
    FROM studio_organization_gas_swaps
    WHERE studio_organization_gas_swaps.status = 'success'
  ''')
), gasless_sends as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app-dev/locations/asia-east1/connections/MySQL",
  '''
  SELECT 
    'gasless_send' as type, 
    studio_organization_gasless_sends.uid, 
    studio_organization_gasless_sends.organization_id, 
    studio_organization_gasless_sends.chain_id,
    CAST(studio_organization_gasless_sends.amount AS DECIMAL(22,9)) as amount, 
    studio_organization_gasless_sends.token_address as contract_address,
    studio_organization_gasless_sends.created_at as timestamp, 
    studio_organization_gasless_sends.profit_margin as profit
    FROM studio_organization_gasless_sends
    WHERE studio_organization_gasless_sends.status = 'success'
  ''')
), defi_swaps as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app-dev/locations/asia-east1/connections/MySQL",
  '''
  SELECT 
    'defi_swap' as type, 
    defi_swaps.uid, 
    defi_swaps.organization_id, 
    defi_swaps.chain_id,
    defi_swaps.amount_usd as amount, 
    defi_swaps.token_address as contract_address,
    defi_swaps.created_at as timestamp, 
    defi_swaps.profit_margin as profit
    FROM defi_swaps
  ''')
), send_with_fee as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app-dev/locations/asia-east1/connections/MySQL",
  '''
  SELECT 
    'send' as type, 
    studio_organization_send_with_fee_txs.uid, 
    studio_organization_send_with_fee_txs.organization_id, 
    studio_organization_send_with_fee_txs.chain_id,
    CAST(studio_organization_send_with_fee_txs.amount AS DECIMAL(22,9)) as amount, 
    studio_organization_send_with_fee_txs.token_address as contract_address,
    studio_organization_send_with_fee_txs.created_at as timestamp, 
    studio_organization_send_with_fee_txs.profit_margin as profit
    FROM studio_organization_send_with_fee_txs
  ''')
), send_with_rents as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app-dev/locations/asia-east1/connections/MySQL",
  '''
  SELECT 
    'send' as type, 
    studio_organization_send_with_rents.uid, 
    studio_organization_send_with_rents.organization_id, 
    studio_organization_send_with_rents.chain_id,
    CAST(studio_organization_send_with_rents.amount/1000000 AS DECIMAL(22,9)) as amount, 
    studio_organization_send_with_rents.token_address as contract_address,
    studio_organization_send_with_rents.created_at as timestamp, 
    studio_organization_send_with_rents.profit_margin as profit
    FROM studio_organization_send_with_rents
  ''')
), bridge as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app-dev/locations/asia-east1/connections/MySQL",
  '''
  SELECT 
    'bridge' as type, 
    studio_organization_bridge_records.uid, 
    studio_organization_bridge_records.organization_id, 
    studio_organization_bridge_records.from_chain_id as chain_id,
    CAST(studio_organization_bridge_records.from_amount AS DECIMAL(22,9)) as amount, 
    studio_organization_bridge_records.from_token_address as contract_address,
    studio_organization_bridge_records.created_at as timestamp, 
    studio_organization_bridge_records.profit_margin as profit
    FROM studio_organization_bridge_records
  ''')
)

SELECT orders.* FROM orders
UNION ALL
SELECT gas_swaps.* FROM gas_swaps
UNION ALL
SELECT gasless_sends.* FROM gasless_sends
UNION ALL
SELECT defi_swaps.* FROM defi_swaps
UNION ALL
SELECT send_with_fee.* FROM send_with_fee
UNION ALL
SELECT send_with_rents.* FROM send_with_rents
UNION ALL
SELECT bridge.* FROM bridge
;
