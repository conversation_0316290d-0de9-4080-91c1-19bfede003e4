-- ─────────────────────────────────────────────────────────────────────
-- 1) Load the last watermark for payment_items (or epoch if missing)
DECLARE last_run TIMESTAMP DEFAULT (
  SELECT
    IFNULL(MAX(last_ts), TIMESTAMP("1970-01-01 00:00:00 UTC"))
  FROM
    `kryptogo-wallet-app-dev.MySQL.watermarks`
  WHERE
    job_id = 'payment_items'
);

-- ─────────────────────────────────────────────────────────────────────
-- 2) INSERT only the new rows into payment_items_raw
INSERT INTO
  `kryptogo-wallet-app-dev.MySQL.payment_items_raw` (
    item_id,
    name,
    price,
    currency,
    organization_id,
    item_timestamp
  )
SELECT
  mi.id           AS item_id,
  mi.name,
  mi.price,
  mi.currency,
  mi.organization_id,
  mi.created_at   AS item_timestamp
FROM
  EXTERNAL_QUERY(
    "projects/kryptogo-wallet-app-dev/locations/asia-east1/connections/MySQL",
    """
    SELECT
      id,
      name,
      price,
      currency,
      organization_id,
      created_at
    FROM payment_items
    """
  ) AS mi
WHERE
  mi.created_at > last_run
;

-- ─────────────────────────────────────────────────────────────────────
-- 3) Advance the watermark for next run
MERGE `kryptogo-wallet-app-dev.MySQL.watermarks` AS wm
USING (
  SELECT
    'payment_items' AS job_id,
    CURRENT_TIMESTAMP() AS last_ts
) AS src
ON
  wm.job_id = src.job_id
WHEN MATCHED THEN
  UPDATE SET last_ts = src.last_ts
WHEN NOT MATCHED THEN
  INSERT (job_id, last_ts) VALUES (src.job_id, src.last_ts)
;