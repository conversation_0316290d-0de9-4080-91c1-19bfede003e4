-- ─────────────────────────────────────────────────────────────────────
-- 1) Load the last watermark for organizations (or epoch if missing)
DECLARE last_run TIMESTAMP DEFAULT (
  SELECT
    IFNULL(MAX(last_ts), TIMESTAMP("1970-01-01 00:00:00 UTC"))
  FROM
    `kryptogo-wallet-app.MySQL.watermarks`
  WHERE
    job_id = 'organizations'
);

-- ─────────────────────────────────────────────────────────────────────
-- 2) INSERT only the new rows into organizations_raw
INSERT INTO
  `kryptogo-wallet-app.MySQL.organizations_raw` (
    organization_id,
    organization_name,
    organization_created_at,
    first_user_uid,
    first_user_name,
    first_user_email
  )
SELECT
  org_data.id                           AS organization_id,
  org_data.name                         AS organization_name,
  org_data.created_at                   AS organization_created_at,
  org_data.first_user_uid,
  org_data.first_user_name,
  org_data.first_user_email
FROM
  EXTERNAL_QUERY(
    "projects/kryptogo-wallet-app/locations/asia-east1/connections/MySQL",
    """
    SELECT 
      o.id,
      o.name,
      o.created_at,
      u.uid AS first_user_uid,
      u.name AS first_user_name,
      u.email AS first_user_email
    FROM studio_organizations o
    LEFT JOIN (
      SELECT 
        organization_id,
        uid,
        name,
        email,
        created_at,
        ROW_NUMBER() OVER (PARTITION BY organization_id ORDER BY created_at ASC, uid ASC) as rn
      FROM studio_users 
      WHERE deleted_at IS NULL
    ) u ON o.id = u.organization_id AND u.rn = 1
    """
  ) AS org_data
WHERE
  org_data.created_at > last_run
  -- Exclude test data based on organization_id
  AND org_data.id NOT IN (
    1, 2, 3, 5, 7, 8, 9, 10, 11, 12, 14, 20, 21, 
    25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36,
    39, 40, 41, 42, 43, 45, 46, 47, 48, 49, 
    51, 52, 53, 54, 58, 59, 60, 61, 62, 63, 66,
    68, 69, 70, 71, 72, 74, 88, 89, 113
  )
;

-- ─────────────────────────────────────────────────────────────────────
-- 3) Advance the watermark for next run
MERGE `kryptogo-wallet-app.MySQL.watermarks` AS wm
USING (
  SELECT
    'organizations' AS job_id,
    CURRENT_TIMESTAMP() AS last_ts
) AS src
ON
  wm.job_id = src.job_id
WHEN MATCHED THEN
  UPDATE SET last_ts = src.last_ts
WHEN NOT MATCHED THEN
  INSERT (job_id, last_ts) VALUES (src.job_id, src.last_ts)
; 