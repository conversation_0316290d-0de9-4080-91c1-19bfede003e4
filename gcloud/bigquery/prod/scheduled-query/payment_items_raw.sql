-- ─────────────────────────────────────────────────────────────────────
-- 1) Load the last watermark for payment_items (or epoch if missing)
DECLARE last_run TIMESTAMP DEFAULT (
  SELECT
    IFNULL(MAX(last_ts), TIMESTAMP("1970-01-01 00:00:00 UTC"))
  FROM
    `kryptogo-wallet-app.MySQL.watermarks`
  WHERE
    job_id = 'payment_items'
);

-- ─────────────────────────────────────────────────────────────────────
-- 2) INSERT only the new rows into payment_items_raw
INSERT INTO
  `kryptogo-wallet-app.MySQL.payment_items_raw` (
    item_id,
    name,
    price,
    currency,
    organization_id,
    item_timestamp
  )
SELECT
  mi.id           AS item_id,
  mi.name,
  mi.price,
  mi.currency,
  mi.organization_id,
  mi.created_at   AS item_timestamp
FROM
  EXTERNAL_QUERY(
    "projects/kryptogo-wallet-app/locations/asia-east1/connections/MySQL",
    """
    SELECT
      id,
      name,
      price,
      currency,
      organization_id,
      created_at
    FROM payment_items
    """
  ) AS mi
WHERE
  mi.created_at > last_run
  -- Exclude test data based on organization_id
  AND mi.organization_id NOT IN (
    1, 2, 3, 5, 7, 8, 9, 10, 11, 12, 14, 20, 21, 
    25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36,
    39, 40, 41, 42, 43, 45, 46, 47, 48, 49, 
    51, 52, 53, 54, 58, 59, 60, 61, 62, 63, 66,
    68, 69, 70, 71, 72, 74, 88, 89, 113
  )
;

-- ─────────────────────────────────────────────────────────────────────
-- 3) Advance the watermark for next run
MERGE `kryptogo-wallet-app.MySQL.watermarks` AS wm
USING (
  SELECT
    'payment_items' AS job_id,
    CURRENT_TIMESTAMP() AS last_ts
) AS src
ON
  wm.job_id = src.job_id
WHEN MATCHED THEN
  UPDATE SET last_ts = src.last_ts
WHEN NOT MATCHED THEN
  INSERT (job_id, last_ts) VALUES (src.job_id, src.last_ts)
;