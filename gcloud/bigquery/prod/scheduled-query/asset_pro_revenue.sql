with orders as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app/locations/asia-east1/connections/MySQL",
  '''
  SELECT
    'buy_crypto'                                              AS type,
    o.uid,
    o.organization_id,
    o.chain_id,
    -- USD volume for this order
    CAST(o.amount AS DECIMAL(22,9)) * CAST(o.price AS DECIMAL(22,9))  
                                                              AS amount,
    o.contract_address                                        AS contract_address,
    o.created_at                                              AS timestamp,
    o.profit_margin                                           AS profit,
    -- flat 0.15% of volume as your kg_profit
    CAST(o.amount AS DECIMAL(22,9)) 
      * CAST(o.price AS DECIMAL(22,9)) 
      * 0.0015                                                 AS kg_profit
  FROM orders AS o
  WHERE
    o.status = 'done'
    AND o.amount       IS NOT NULL
    AND o.price        IS NOT NULL;
      ''')
), gas_swaps as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app/locations/asia-east1/connections/MySQL",
  '''
  SELECT 
    'gas_swap'                            AS type,
    sogs.uid,
    sogs.organization_id,
    sogs.chain_id,
    CAST(sogs.amount AS DECIMAL(22,9))   AS amount,
    sogs.token_address                   AS contract_address,
    sogs.created_at                      AS timestamp,
    sogs.profit_margin                   AS profit,
    (
      CAST(sogs.amount AS DECIMAL(22,9)) * sogs.paid_token_price
      - sogs.actual_cost                  * sogs.native_token_price
      - sogs.profit_margin
    )                                     AS kg_profit
  FROM studio_organization_gas_swaps sogs
  WHERE sogs.status = 'success'
    AND sogs.amount         IS NOT NULL
    AND sogs.actual_cost    IS NOT NULL
    AND sogs.paid_token_price   IS NOT NULL
    AND sogs.native_token_price IS NOT NULL;
  ''')
), gasless_sends as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app/locations/asia-east1/connections/MySQL",
  '''
  SELECT
    'gasless_send'                                        AS type,
    sogl.uid,
    sogl.organization_id,
    sogl.chain_id,
    CAST(sogl.amount AS DECIMAL(22,9))                    AS amount,
    sogl.token_address                                    AS contract_address,
    sogl.created_at                                       AS timestamp,
    sogl.profit_margin                                    AS profit,
    (
      CASE
        WHEN sogl.fee_usd <> 0
          THEN sogl.fee_usd
        ELSE CAST(sogl.fee AS DECIMAL(22,9)) * sogl.token_price
      END
      - sogl.profit_margin
      - sogl.actual_cost_usd
    )                                                      AS kg_profit
  FROM studio_organization_gasless_sends AS sogl
  WHERE sogl.status = 'success'
    -- need profit_margin and actual_cost_usd to compute kg_profit
    AND sogl.profit_margin    IS NOT NULL
    AND sogl.actual_cost_usd  IS NOT NULL
    -- ensure we have something to calculate the fee side
    AND (
          sogl.fee_usd <> 0
          OR (sogl.fee         IS NOT NULL
              AND sogl.token_price IS NOT NULL)
        );
  ''')
), defi_swaps as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app/locations/asia-east1/connections/MySQL",
  '''
  SELECT
    'defi_swap'                             AS type,
    ds.uid,
    ds.organization_id,
    ds.chain_id,
    ds.amount_usd                           AS amount,
    ds.token_address                        AS contract_address,
    ds.created_at                           AS timestamp,
    ds.profit_margin                        AS profit,
    (
      ds.fee_amount * ds.token_price
      - ds.profit_margin
    )                                        AS kg_profit
  FROM defi_swaps AS ds
  WHERE
      ds.fee_amount   IS NOT NULL
    AND ds.token_price IS NOT NULL
    AND ds.profit_margin IS NOT NULL;
  ''')
), send_with_fee as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app/locations/asia-east1/connections/MySQL",
  '''
  SELECT
    'send'                                             AS type,
    s.uid,
    s.organization_id,
    s.chain_id,
    CAST(s.amount AS DECIMAL(22,9))                    AS amount,
    s.token_address                                    AS contract_address,
    s.created_at                                       AS timestamp,
    s.profit_margin                                    AS profit,
    (s.fee_amount * s.token_price - s.profit_margin)   AS kg_profit
  FROM studio_organization_send_with_fee_txs AS s
  WHERE
    s.fee_amount    IS NOT NULL
    AND s.token_price  IS NOT NULL
    AND s.profit_margin IS NOT NULL;
  ''')
), send_with_rents as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app/locations/asia-east1/connections/MySQL",
  '''
  SELECT
    'send'                      AS type,
    s.uid,
    s.organization_id,
    s.chain_id,
    CAST(s.amount/1000000 AS DECIMAL(22,9))  AS amount,
    s.token_address           AS contract_address,
    s.created_at              AS timestamp,
    s.profit_margin           AS profit,
    (
      s.fee * s.actual_cost_usd 
        / NULLIF(s.energy_rent_cost, 0) 
      - s.actual_cost_usd
      - s.profit_margin
    ) AS kg_profit
  FROM studio_organization_send_with_rents AS s;
  ''')
), bridge as (
SELECT * FROM
  EXTERNAL_QUERY("projects/kryptogo-wallet-app/locations/asia-east1/connections/MySQL",
  '''
  SELECT
    'bridge' AS type,
    b.uid,
    b.organization_id,
    b.from_chain_id   AS chain_id,
    CAST(b.from_amount AS DECIMAL(22,9)) AS amount,
    b.from_token_address  AS contract_address,
    b.created_at       AS timestamp,
    b.profit_margin    AS profit,
    (
      CASE
        WHEN b.fee_usd <> 0 THEN b.fee_usd
        ELSE b.fee_amount * b.fee_token_price
      END
      - b.profit_margin
    ) AS kg_profit
  FROM studio_organization_bridge_records b;
  ''')
)

SELECT orders.* FROM orders
UNION ALL
SELECT gas_swaps.* FROM gas_swaps
UNION ALL
SELECT gasless_sends.* FROM gasless_sends
UNION ALL
SELECT defi_swaps.* FROM defi_swaps
UNION ALL
SELECT send_with_fee.* FROM send_with_fee
UNION ALL
SELECT send_with_rents.* FROM send_with_rents
UNION ALL
SELECT bridge.* FROM bridge
;