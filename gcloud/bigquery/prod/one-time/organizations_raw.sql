CREATE TABLE `kryptogo-wallet-app.MySQL.organizations_raw` (
  organization_id                INT64,
  organization_name              STRING,
  organization_created_at        TIMESTAMP,
  first_user_uid                 STRING,
  first_user_name                STRING,
  first_user_email               STRING
)
PARTITION BY DATE(organization_created_at)
OPTIONS (
  description = "Append‐only dump of organizations with first user data from MySQL"
); 