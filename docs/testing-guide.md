# Testing Guide for KryptoGO Backend

## Setting Up Tests

### Environment Setup

1. Source the environment configuration:

```bash
source config/local.sh
```

2. Set CI environment variable:

```bash
export CI=true
```

### Database Setup

When writing tests that interact with the database:

1. Call `Reset()` at the start of each test to ensure a clean state:

```go
func TestYourFunction(t *testing.T) {
    Reset()
    // ... rest of test
}
```

2. Make sure your models are included in `getTables()` in `pkg/service/rdb/database.go`. This is required for automatic table creation.

3. Follow the test data setup pattern:

```go
// Create test user
user := &model.User{
    UID: "test_user",
}
err := GetWith(ctx).Create(user).Error
require.NoError(t, err)

// Create related data
data := &model.YourModel{
    UserID: user.UID,
    // ... other fields
}
err = GetWith(ctx).Create(data).Error
require.NoError(t, err)
```

### Test Structure

1. Use table-driven tests with descriptive names:

```go
t.Run("success", func(t *testing.T) {
    // Test happy path
})

t.Run("invalid_input", func(t *testing.T) {
    // Test error cases
})
```

2. Follow the Arrange-Act-Assert pattern:

```go
// Arrange: Set up test data
user := &model.User{...}
err := GetWith(ctx).Create(user).Error
require.NoError(t, err)

// Act: Call the function being tested
result, err := repo.YourFunction(ctx, input)

// Assert: Verify results
require.NoError(t, err)
assert.Equal(t, expected, result)
```

3. Verify database state after operations:

```go
var updatedModel model.YourModel
err = GetWith(ctx).Where("id = ?", id).First(&updatedModel).Error
require.NoError(t, err)
assert.Equal(t, expectedValue, updatedModel.Value)
```

### Running Tests

1. Run all tests in a package:

```bash
go test ./pkg/service/rdb -v
```

2. Run a specific test:

```bash
go test ./pkg/service/rdb -v -run TestSpecificFunction
```

3. Run all tests (recommended during development):

```bash
make test
```

### Common Patterns

1. Error Handling:
   - Use domain errors from `domain/errors.go`
   - Never return `*code.KGError` from repository layer
   - Common errors: `domain.ErrRecordNotFound`, `domain.ErrInsufficientBalance`

2. Database Operations:
   - Use `GetWith(ctx)` for database operations
   - Use `require.NoError(t, err)` for setup operations
   - Use `assert.Equal(t, expected, actual)` for verifying results

3. Test Data:
   - Use meaningful test data that represents real-world scenarios
   - Clean up test data using `Reset()`
   - Document test data values with comments (e.g., `amount := "1000000000" // 1 SOL`)

4. Context:
   - Always pass context to functions that require it
   - Use `context.Background()` for tests unless specific context is needed

### Best Practices

1. Test Coverage:
   - Test both success and error cases
   - Test edge cases and boundary conditions
   - Verify database state before and after operations

2. Test Independence:
   - Each test should be independent
   - Don't rely on state from other tests
   - Use `Reset()` to ensure clean state

3. Test Readability:
   - Use descriptive test names
   - Comment test data values
   - Follow consistent patterns across test files

4. Error Handling:
   - Test error conditions thoroughly
   - Verify error types match expected domain errors
   - Check error messages when relevant

### Layer-Specific Testing

1. Repository Layer (`pkg/service/rdb`):
   - Focus on database operations
   - Test CRUD operations
   - Verify data consistency

2. Service Layer (`service/`):
   - Test business logic
   - Mock external dependencies
   - Test error handling

3. API Layer (`api/`):
   - Test HTTP endpoints
   - Test request/response handling
   - Test validation logic
