# Collect Service Documentation

## Overview

The collect service handles cross-chain fund transfers (bridging) between different blockchain networks. It is responsible for:

1. Moving funds to a target address on the same chain
2. Converting them to native tokens on a bridging address for cross-chain transfers

### Supported Operations

- EVM to EVM bridging
- EVM to Solana bridging
- Native token transfers
- ERC20 token transfers

### Supported Chains

- Ethereum
- Arbitrum
- Base
- Optimism
- BNB Chain
- Polygon
- Solana

## Architecture

### Processing Flow

1. **Same Chain Transfer**

   ```
   Source Chain (Native/ERC20) -> Target Address (Same Chain)
   ```

2. **Cross-Chain Transfer**
   - **Convert to Native Token**

     ```
     Source Chain (Native/ERC20) -> Bridging Address (Native Token)
     ```

   - **Bridge to Target**

     ```
     Bridging Address (Native Token) -> Target Chain (Target Token)
     ```

### Bridge Transaction Building

1. **OKX Bridge (EVM to EVM)**
   - Handles gas price and value calculations
   - Supports fee rate adjustments

2. **LiFi Bridge (EVM to Solana)**
   - Handles hex value parsing and gas estimations
   - Supports token approvals for ERC20 transfers

### Gas and Balance Management

- Dynamic gas price retrieval using client's raw connection
- Balance adjustment with 1.5x margin for gas fees
- Calculation: `total_needed = tx_value + (gas_price * gas_limit)`

## Implementation Details

### Key Components

1. **Transaction Creation**
   - Uses `types.NewTransaction` with proper nonce management
   - Supports both direct transfers and contract interactions

2. **Error Handling**
   - Consistent `KGError` types with context
   - Proper error propagation through call stack

### Recent Improvements

- Separated bridging logic into distinct functions
- Unified transaction creation patterns
- Better gas price and limit handling
- Improved nonce management
- Enhanced error messages and context

## Usage

```go
// Process EVM bridging
err := ProcessEVM(ctx, chain, salt, target, targetTokenID, feeRate)
if err != nil {
    // Handle error
}
```

## Dependencies

- ethereum/go-ethereum: Core transaction types
- LiFi API: Solana bridging
- OKX API: EVM-to-EVM bridging

## Best Practices

1. **Security**
   - Use deterministic bridging addresses
   - Verify transaction success
   - Monitor bridging address balances

2. **Gas Management**
   - Reserve sufficient gas for transactions
   - Handle gas price fluctuations
   - Account for cross-chain fee differences
