# 新增欄位到現有架構的步驟指南

本文件說明如何在現有的 kg-wallet-backend 架構中新增欄位，以 `studio_organizations` 表中新增 `business_type` 欄位為例。

跑測試之前，需要先 source ./config/local.env

## 步驟概覽

1. **資料庫 Schema 更新**
2. **Model 層更新**
3. **Domain 層更新**
4. **Repository 層更新**
5. **Service 層更新**
6. **API 層更新**
7. **測試和驗證**

## 詳細步驟

### 1. 資料庫 Schema 更新

首先，需要更新資料庫的 Liquibase changeset：

```sql
-- changeset author:ticket-number:timestamp:seq
ALTER TABLE studio_organizations
ADD COLUMN business_type ENUM('individual', 'enterprise') NULL;
-- rollback ALTER TABLE studio_organizations DROP COLUMN business_type;
```

### 2. Model 層更新

更新對應的 GORM model (`pkg/db/model/studio_organizations.go`)：

```go
type StudioOrganization struct {
    // 其他欄位...
    BusinessType *string `gorm:"column:business_type;type:enum('individual','enterprise')"`
}
```

### 3. Domain 層更新

更新 domain struct 和 config struct (`domain/studio_organization.go`)：

```go
// StudioOrganization domain model
type StudioOrganization struct {
    // 其他欄位...
    BusinessType *string
}

// StudioOrganizationConfig for updates
type StudioOrganizationConfig struct {
    // 其他欄位...
    BusinessType *string
}
```

### 4. Repository 層更新

更新 repository 的實作 (`pkg/service/rdb/studio_organization.go`)：

#### 4.1 更新 UpdateOrganization 函數
```go
if params.BusinessType != nil {
    org.BusinessType = params.BusinessType
}
```

#### 4.2 更新所有 Get* 函數的回傳值
```go
return &domain.StudioOrganization{
    // 其他欄位...
    BusinessType: org.BusinessType,
}
```

### 5. Service 層更新

更新 service 層的 request struct 和處理邏輯 (`pkg/service/studio/organization/studio_organization.go`)：

#### 5.1 更新 UpdateOrganizationReq
```go
type UpdateOrganizationReq struct {
    // 其他欄位...
    BusinessType *string `json:"business_type"`
}
```

#### 5.2 更新 UpdateOrganization 函數
```go
return repo.UpdateOrganization(ctx, orgID, domain.StudioOrganizationConfig{
    // 其他欄位...
    BusinessType: req.BusinessType,
})
```

### 6. API 層更新

API handler (`api/studio/organization/organization.go`) 通常不需要額外修改，因為它會自動處理 JSON binding。

### 7. 測試和驗證

執行以下步驟進行驗證：

1. **編譯檢查**：確保所有程式碼能正確編譯
2. **單元測試**：執行相關的單元測試
3. **整合測試**：測試 API 端點是否正常工作
4. **資料庫遷移**：確認 Liquibase changeset 能正確執行

## 注意事項

### 欄位設計考量

- **NULL 值處理**：新欄位通常設為 NULL，保持向後相容
- **預設值**：考慮是否需要預設值
- **資料類型**：選擇適當的資料類型（ENUM、VARCHAR、INT 等）
- **索引**：評估是否需要建立索引

### 向後相容性

- 新欄位應該是可選的（使用指標類型 `*string`）
- API 回應中包含新欄位不會影響現有客戶端
- 確保舊版本的請求仍能正常處理

### 代碼一致性

- 遵循現有的命名慣例
- 保持與其他欄位相同的處理模式
- 確保所有相關函數都有更新

## 常見錯誤

1. **忘記更新 Get* 函數**：導致新欄位無法正確回傳
2. **Repository 層遺漏更新**：更新操作無法儲存新欄位
3. **型別不一致**：Model 層和 Domain 層的型別不匹配
4. **JSON tag 遺漏**：API 無法正確處理 JSON 請求/回應

## 檢查清單

- [ ] 資料庫 changeset 已建立
- [ ] Model struct 已更新
- [ ] Domain struct 已更新
- [ ] Repository UpdateOrganization 已更新
- [ ] Repository Get* 函數已更新
- [ ] Service request struct 已更新
- [ ] Service 處理邏輯已更新
- [ ] 程式碼編譯通過
- [ ] 測試執行通過

通過遵循這些步驟，可以確保新欄位的添加不會破壞現有功能，並且能夠正確地整合到現有的架構中。