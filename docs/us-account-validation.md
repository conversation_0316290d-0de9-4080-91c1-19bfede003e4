# US Account Type Validation for Bridge External Accounts

This document describes the implementation of validation rules for US account types in the Bridge External Account API.

## Overview

When `account_type` is set to `"us"`, the API now validates specific fields according to Bridge API requirements. This ensures data quality and compliance with US banking standards.

## Validation Rules

### US Account Fields

When `account_type` is `"us"`, the following fields are **required**:

- `us.account.account_number` - The US bank account number
- `us.account.routing_number` - The US bank routing number  
- `address` - Complete US address information

### Address Validation Rules

For US accounts, the address fields have the following validation rules:

| Field           | Required | Validation Rule                                 |
| --------------- | -------- | ----------------------------------------------- |
| `street_line_1` | ✅ Yes    | Length between 4 and 35 characters              |
| `street_line_2` | ❌ No     | Length ≤ 35 characters                          |
| `city`          | ✅ Yes    | Length ≥ 1 character                            |
| `state`         | ❌ No     | Length between 1 and 3 characters when provided |
| `postal_code`   | ✅ Yes    | Length ≥ 1 character                            |
| `country`       | ✅ Yes    | Exactly 3 characters (e.g., "USA")              |

## API Request Examples

### Valid US Account Request

```json
{
  "account_type": "us",
  "account_owner_name": "John Doe",
  "account_owner_type": "individual",
  "bank_name": "Chase Bank",
  "currency": "usd",
  "us": {
    "account": {
      "account_number": "************",
      "routing_number": "*********"
    }
  },
  "address": {
    "street_line_1": "270 Park Ave",
    "street_line_2": "Floor 10",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "USA"
  }
}
```

### Validation Error Examples

#### Missing US Account Details
```json
{
  "account_type": "us",
  "account_owner_name": "John Doe",
  "account_owner_type": "individual",
  "bank_name": "Chase Bank",
  "currency": "usd"
  // Missing "us" field
}
```

**Error Response:**
```json
{
  "code": 4001,
  "error": "us account details are required when account_type is 'us'"
}
```

#### Invalid Street Address
```json
{
  "account_type": "us",
  "us": {
    "account": {
      "account_number": "************",
      "routing_number": "*********"
    }
  },
  "address": {
    "street_line_1": "123",  // Too short
    "city": "New York",
    "postal_code": "10001",
    "country": "USA"
  }
}
```

**Error Response:**
```json
{
  "code": 4001,
  "error": "street_line_1 must be between 4 and 35 characters"
}
```

## Implementation Details

### Domain Model Updates

1. **New Structures Added:**
   - `USDetails` - Container for US account information
   - `USAccount` - US-specific account details with `account_number` and `routing_number`
   - `State` field added to `AddressDetails` as optional pointer

2. **Validation Method:**
   - `ValidateAccountTypeSpecificFields()` - Main validation entry point
   - `validateUSAccount()` - US-specific validation logic
   - `validateIBANAccount()` - IBAN-specific validation logic

### API Integration

The validation is automatically triggered in the Bridge External Account creation API:

1. JSON request is parsed into `CreateBridgeExternalAccountRequest`
2. `ValidateAccountTypeSpecificFields()` is called
3. If validation fails, a 400 Bad Request response is returned
4. If validation passes, the request proceeds to the Bridge API

### Account Type Requirements

| Account Type | Required Fields             | Validation                            |
| ------------ | --------------------------- | ------------------------------------- |
| `us`         | `us.account.*`, `address.*` | Strict US address validation          |
| `iban`       | `swift.*`                   | SWIFT details required                |
| `unknown`    | `swift.*`                   | SWIFT details required (same as IBAN) |
| `clabe`      | Basic fields only           | No special validation                 |

### Backward Compatibility

- Existing account types (`iban`, `clabe`) continue to work unchanged
- `unknown` type now requires `swift` details (aligns with existing usage)
- No breaking changes to existing API contracts for most account types

## Testing

Comprehensive test cases cover:

- ✅ Valid US account creation
- ❌ Missing US account details
- ❌ Missing account number or routing number
- ❌ Invalid address field lengths
- ❌ Missing required address fields
- ✅ Valid IBAN accounts (unchanged)
- ✅ Valid unknown accounts (requires Swift details)
- ❌ Missing Swift details for unknown accounts
- ✅ Valid CLABE accounts (no special validation)

## Error Codes

All validation errors return HTTP 400 with error code `4001` (ParamIncorrect) and descriptive error messages to help developers identify and fix validation issues. 