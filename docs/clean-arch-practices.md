# KryptoGO Backend Development Practices

This document outlines the development practices and architectural patterns used in the KryptoGO wallet backend. It serves as a guide for new engineers joining the team and a reference for maintaining consistency in our codebase.

## Table of Contents

- [Clean Architecture](#clean-architecture)
- [Dependency Injection](#dependency-injection)
- [Testing Practices](#testing-practices)
- [Common Packages and Utilities](#common-packages-and-utilities)
- [Error Handling](#error-handling)
- [Logging](#logging)
- [Referral Service Implementation Patterns](#referral-service-implementation-patterns)

## Clean Architecture

Our codebase follows Clean Architecture principles, with clear separation between different layers:

### 1. Domain Layer (`/domain`)

- Contains business logic and interfaces
- Defines core entities and repository interfaces
- Independent of external frameworks and databases
- Example:

  ```go
  // Domain model
  type SendWithRent struct {
      ID           int
      OrgID        int
      Chain        Chain
      Status       SendWithRentStatus
      // ... other fields
  }

  // Repository interface
  type SendWithRentRepo interface {
      CreateSendWithRent(ctx context.Context, sendWithRent *SendWithRent) (int, error)
      UpdateSendWithRent(ctx context.Context, update *UpdateSendWithRentRequest) error
      GetSendWithRentByID(ctx context.Context, id int) (*SendWithRent, error)
  }
  ```

### 2. Repository Layer (`/pkg/db/model`, `/pkg/service/rdb`)

- Implements data access interfaces defined in domain layer
- Uses GORM for database operations
- Example:

  ```go
  // Database model
  type StudioOrganizationSendWithRent struct {
      ID            int                       `gorm:"column:id;primaryKey"`
      OrganizationID int                      `gorm:"column:organization_id"`
      Status        domain.SendWithRentStatus `gorm:"column:status"`
      // ... other fields
  }
  ```

### 3. Service Layer (`/service`)

- Implements business logic
- Orchestrates domain objects and repositories
- Example:

  ```go
  func Create(ctx context.Context, params *CreateParams) (int, *code.KGError) {
      // Business logic implementation
  }
  ```

### 4. API Layer (`/api`)

- Handles HTTP requests and responses
- Uses Gin framework for routing
- Example:

  ```go
  func Init(c *gin.Context) {
      // Request handling
      var req InitReq
      if err := util.ToGinContextExt(c).BindJson(&req); err != nil {
          // Error handling
      }
      // Process request
  }
  ```

## Dependency Injection

We use dependency injection to maintain loose coupling between components:

### 1. Interface Definition

```go
type IRepo interface {
    // Repository methods
}
```

### 2. Service Initialization

```go
func Init(repo IRepo, executor domain.AsyncTaskExecutor, handler string) {
    // Initialize service with dependencies
}
```

### 3. Mock Generation

We use `gomock` for generating mocks. Add the following comment to generate mocks:

```go
//go:generate mockgen -package=domain -destination=send_with_rent_repo_mock.go . SendWithRentRepo
```

## Testing Practices

### 1. Unit Testing

- Test files are placed next to the implementation files
- Use table-driven tests for multiple test cases
- Example:

  ```go
  func TestSendWithRent(t *testing.T) {
      tests := []struct {
          name          string
          input        Input
          expectedOutput Output
          expectedError error
      }{
          // Test cases
      }
      
      for _, tt := range tests {
          t.Run(tt.name, func(t *testing.T) {
              // Test implementation
          })
      }
  }
  ```

### 2. Mock Usage

```go
func TestCreateSendWithRentSuccess(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()

    mockRepo := NewMockIRepo(ctrl)
    mockExecutor := domain.NewMockAsyncTaskExecutor(ctrl)

    // Set up expectations
    mockRepo.EXPECT().
        AcquireLockWithRetry(gomock.Any(), "send-with-rent-1-shasta", gomock.Any(), gomock.Any()).
        Return(nil)

    // Run test
}
```

## Common Packages and Utilities

### 1. Error Handling (`pkg/code`)

```go
// Creating domain errors
code.NewKGError(code, httpStatus int, err error, data map[string]interface{})

// Returning errors in API layer
response.KGError(c, kgErr)
```

### 2. Logging (`pkg/kglog`)

Use context-aware logging methods:

```go
kglog.InfoCtx(ctx, message)
kglog.InfoWithDataCtx(ctx, message, data)
kglog.ErrorWithData("An error occurred", map[string]interface{}{
    "error": err.Error(), // Always use .Error() for error logging
})
```

### 3. Context Management

- Always pass context through function calls
- Use context for timeouts and cancellation
- Add tracing when needed:

  ```go
  ctx, span := tracing.Start(ctx, "sendwithrent.Create")
  defer span.End()
  ```

### 4. Configuration (`pkg/config`)

```go
config.GetString("CONFIG_KEY")
```

### 5. Validation

- Use Gin binding tags for request validation
- Implement custom validation using `AfterBinding` interface when needed

## Referral Service Implementation Patterns

The referral service implementation demonstrates several key patterns and practices used in our codebase:

### 1. Service Package Structure

- `common.go`: Central file containing:
  - Interface definitions
  - Constants
  - Global variables
  - Service implementation
  - Type definitions
- `response.go`: Response types and transformations
- No separate `service.go` file to avoid package stuttering

### 2. Response Handling

Use response package methods instead of direct JSON responses:

```go
// Instead of c.JSON():
response.OK(c, data)
response.BadRequestWithMsg(c, code.ParamIncorrect, err.Error())
response.KGError(c, err)
```

### 3. Domain Model Design

- Use `big.Int` for cryptocurrency amounts
- Use domain types for addresses (`domain.Address`)
- Keep domain models independent of database models
- Example:

```go
type ReferralReward struct {
    ReferrerUID string
    From        domain.Address
    TxHash      string
    Amount      *big.Int
}
```

### 4. Database Models

- Use `int AUTO_INCREMENT` for IDs
- Store amounts as strings to preserve precision
- Use proper GORM tags and indexes
- Example:

```go
type ReferralReward struct {
    ID          int    `gorm:"column:id;primaryKey;autoIncrement"`
    ReferrerUID string `gorm:"column:referrer_uid;index"`
    FromAddr    string `gorm:"column:from_addr"`
    TxHash      string `gorm:"column:tx_hash;uniqueIndex"`
    Amount      string `gorm:"column:amount"`
}
```

### 5. Error Handling

- Use `code.KGError` for domain errors
- Log errors with context and data
- Example:

```go
if err != nil {
    kglog.ErrorWithDataCtx(ctx, "Failed to create withdrawal", map[string]interface{}{
        "error": err.Error(),
        "withdrawal": withdrawal,
    })
    return nil, code.NewKGError(code.DBError, http.StatusInternalServerError, err, nil)
}
```

### 6. Testing

- Reset database before each test
- Use `GormRepo()` for database access
- Test both success and error cases
- Example:

```go
func TestCreateReferralReward(t *testing.T) {
    Reset()
    ctx := context.Background()
    repo := GormRepo()

    t.Run("success", func(t *testing.T) {
        // Test implementation
    })

    t.Run("duplicate tx hash", func(t *testing.T) {
        // Test error case
    })
}
```

### 7. OAuth Scopes

- Define read and write scopes for each feature
- Add scopes to whitelist
- Example:

```go
const (
    ScopeReferralRead  = "referral:read"
    ScopeReferralWrite = "referral:write"
)
```

### 8. Input Validation

- Use Gin binding tags
- Validate addresses and amounts
- Set minimum/maximum values as constants
- Example:

```go
type WithdrawRequest struct {
    Amount    string `json:"amount" binding:"required"`
    Recipient string `json:"recipient" binding:"required"`
}
```

### 9. Repository Interface

- Define interfaces in domain package
- Combine related interfaces in service
- Example:

```go
type IRepo interface {
    domain.ReferralRepo
    domain.OrganizationWalletRepo
    domain.LockManager
}
```

These patterns ensure consistency, maintainability, and reliability across our codebase.

## Best Practices

1. **Error Handling**
   - Always wrap errors with context
   - Use appropriate error codes
   - Include relevant data in error responses

2. **Logging**
   - Use appropriate log levels
   - Include context in logs
   - Add structured data when useful

3. **Security**
   - Use OAuth for authentication
   - Implement proper input validation
   - Use prepared statements for database queries

4. **Performance**
   - Implement proper caching strategies
   - Use database indexes appropriately
   - Handle concurrent operations with locks

5. **Code Organization**
   - Follow package naming conventions
   - Group related functionality
   - Keep interfaces small and focused

## Conclusion

Following these practices ensures:

- Maintainable and testable code
- Clear separation of concerns
- Consistent error handling and logging
- Proper security measures
- Scalable architecture

For more detailed information about specific components or practices, refer to the respective package documentation or reach out to the team.
