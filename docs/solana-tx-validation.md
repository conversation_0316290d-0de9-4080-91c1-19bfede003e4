# Solana Transaction Validation Documentation

## Overview

The Solana transaction validation system is a critical component of the universal swap service that ensures the security and correctness of Solana transactions before they are processed. It supports both native SOL transfers and SPL token transfers with comprehensive validation checks.

## Transaction Types

### 1. Native SOL Transfer

- Single system program instruction
- Direct transfer between wallets
- Validated through `validateSolTransfer`

### 2. SPL Token Transfer

- Supports single transfer or create ATA + transfer
- Uses Associated Token Accounts (ATAs)
- Validated through `validateSplTokenTransfer`

## Validation Process

### Main Validation Flow (`validateSourceSolanaTx`)

1. **Transaction Decoding**

   ```go
   rawTx, err := base58.Decode(tx.SignedTx)
   signedTx, err := solanago.TransactionFromBytes(rawTx)
   ```

   - Decodes base58-encoded transaction
   - Deserializes into Solana transaction object

2. **Signature Verification**
   - Ensures transaction is signed
   - Verifies first account key matches sender

3. **Amount Validation**
   - Parses and validates amount format
   - Ensures non-negative value

4. **Transaction Type Detection**
   - Determines if <PERSON><PERSON> or <PERSON>L token transfer
   - Routes to appropriate validator

5. **Fee Calculation**

   ```go
   neededNativeToken := signatures * 5000 // lamports per signature
   ```

   - Calculates required SOL for transaction fees
   - Adds transfer amount for SOL transfers

### Native SOL Transfer Validation (`validateSolTransfer`)

1. **Instruction Count**
   - Must have exactly one instruction

2. **Program Verification**
   - Must use System Program
   - Must be Transfer instruction (index 2)

3. **Account Validation**
   - Verifies recipient address
   - Checks instruction account indices

4. **Amount Verification**
   - Validates transfer amount matches expected
   - Checks for proper encoding in instruction data

### SPL Token Transfer Validation (`validateSplTokenTransfer`)

1. **Instruction Count**
   - Allows 1-2 instructions
   - Supports optional ATA creation

2. **ATA Derivation**

   ```go
   sourceATA, _, err := solanago.FindAssociatedTokenAddress(sourceOwner, tokenMint)
   destATA, _, err := solanago.FindAssociatedTokenAddress(destOwner, tokenMint)
   ```

   - Derives source and destination ATAs
   - Validates ATA addresses match transaction

3. **Create ATA Validation (if present)**
   - Verifies Associated Token Program
   - Validates account list
   - Checks destination ATA

4. **Transfer Instruction Validation**
   - Must be TransferChecked (index 12)
   - Validates token program ID
   - Verifies instruction data format
   - Checks transfer amount

5. **Account Validation**
   - Validates source ATA
   - Verifies token mint
   - Checks destination ATA

## Error Handling

The validation system provides detailed error messages for various failure cases:

1. **Transaction Format Errors**
   - Invalid base58 encoding
   - Malformed transaction bytes
   - Missing signatures

2. **Instruction Errors**
   - Invalid instruction count
   - Wrong program ID
   - Incorrect instruction format

3. **Account Errors**
   - Invalid account indices
   - Mismatched addresses
   - Wrong token accounts

4. **Amount Errors**
   - Invalid amount format
   - Amount mismatch
   - Incorrect encoding

## Usage Example

```go
// Example: Validate a Solana transaction
tx := &domain.SourceTransaction{
    Chain:    domain.Solana,
    SignedTx: "base58_encoded_transaction",
    From:     senderAddress,
    To:       recipientAddress,
    TokenID:  "token_mint_address", // Use chain.MainToken().ID() for SOL
    RawAmount: "**********",       // Amount in lamports
}

neededNative, txHash, err := validateSourceSolanaTx(tx)
if err != nil {
    // Handle validation error
}
// Transaction is valid, proceed with processing
```

## Security Considerations

1. **Signature Verification**
   - Ensures transaction is properly signed
   - Validates signer authority

2. **Amount Protection**
   - Prevents amount manipulation
   - Validates exact amount match

3. **Program Safety**
   - Enforces correct program usage
   - Prevents unauthorized program calls

4. **Account Protection**
   - Validates all account relationships
   - Ensures proper token account derivation

## Integration Points

The validation system integrates with:

1. **Universal Swap Service**
   - Pre-swap validation
   - Fee calculation
   - Transaction verification

2. **Token Management**
   - SPL token handling
   - ATA management
   - Token program interaction

3. **Fee Management**
   - Native token requirements
   - Transaction fee calculation
   - Sponsorship handling

## Best Practices

1. Always validate transaction signatures
2. Verify program IDs match expected values
3. Check all account relationships
4. Validate exact amounts
5. Handle both SOL and SPL token transfers appropriately
6. Calculate fees accurately
7. Provide detailed error messages
