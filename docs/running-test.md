# Running Tests

## Environment Setup

Before running tests, make sure to source the environment configuration and set the CI environment variable:

```bash
source config/local.sh
export CI=true
```

The recommended way to run tests is using the Makefile command:

```bash
# Run all tests (includes environment checks, custom lint, and race detection)
make test
```

Alternatively, you can use the go test command directly:

```bash
# Run a specific test
go test ./chain/solana -v -run TestComplicatedTransactionDetail

# Run all tests in a package
go test ./chain/solana -v

# Run all tests in all packages
go test ./... -v
```

Note: The `make test` command includes additional checks such as custom linting and race detection, so it's preferred for development.
