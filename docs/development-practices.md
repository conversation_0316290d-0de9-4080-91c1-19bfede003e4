# Development Practices

This document outlines the development practices and conventions used in the KryptoGO Wallet Backend.

## Database Schema

### User IDs and Foreign Keys

1. User IDs should be stored as `VARCHAR(60) NOT NULL` in database tables
2. When referencing user IDs from other tables, always use a foreign key constraint to `users(uid)`
3. In GORM models, use the following tag format for user ID fields:

   ```go
   UserID string `gorm:"column:user_id;type:varchar(60);not null;references:users(uid)"`
   ```

### Common Field Types

1. Blockchain Addresses: `VARCHAR(44)` for most chains (e.g., Solana)
2. Transaction Hashes: `VARCHAR(88)` to accommodate different chain formats
3. Token Amounts: `VARCHAR(78)` to store large numbers as strings
4. Status Fields: `VARCHAR(20)` for status enums
5. Timestamps: `TIMESTAMP` with NOT NULL constraint

### Indexes

1. Always create indexes on frequently queried fields
2. Use composite indexes when queries commonly filter on multiple columns
3. Add unique constraints where appropriate (e.g., transaction hashes)

Example:

```sql
CREATE INDEX idx_table_name_user_id ON table_name(user_id);
CREATE UNIQUE INDEX uniq_table_name_tx_hash ON table_name(tx_hash);
```

## GORM Models

### Model Structure

1. Always include proper GORM tags for each field
2. Use appropriate Go types that match database column types
3. Implement `TableName()` method to explicitly set table name
4. Include `ToDomain()` method to convert database model to domain model

Example:

```go
type Model struct {
    ID        int       `gorm:"column:id;type:int;primaryKey;autoIncrement"`
    UserID    string    `gorm:"column:user_id;type:varchar(60);not null;references:users(uid)"`
    CreatedAt time.Time `gorm:"column:created_at;type:timestamp;not null"`
    UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp;not null"`
}

func (Model) TableName() string {
    return "table_name"
}

func (m *Model) ToDomain() *domain.Model {
    return &domain.Model{
        ID:     m.ID,
        UserID: m.UserID,
    }
}
```

## Error Handling

### Domain Errors

Common domain errors are defined in `domain/errors.go`:

```go
var ErrRecordNotFound = errors.New("record not found")
var ErrInsufficientBalance = errors.New("insufficient balance")
var ErrUnsupportedChain = errors.New("unsupported chain")
var ErrAddressNotFound = errors.New("address not found")
```

### KGError

`KGError` is used in the API layer to convert domain errors to HTTP responses:

```go
// In pkg/code/error.go
type KGError struct {
    Code       int                    // Error code
    HttpStatus int                    // HTTP status code
    Error      error                  // Original error
    Data       map[string]interface{} // Additional error data
}

// Create a new KGError
err := code.NewKGError(code.InsufficientRewardBalance, http.StatusBadRequest, originalErr, map[string]interface{}{
    "message": "insufficient balance",
})
```

Common error codes are defined in `pkg/code/code.go`.

### Error Flow

1. Repository layer: Return domain errors
2. Service layer: Handle domain errors, may wrap with additional context
3. API layer: Convert to KGError with appropriate HTTP status

## Service Initialization

### Adding New Services

When creating a new service package:

1. Define an `Init` function in your service package:

   ```go
   // service/your-service/common.go
   func Init(uRepo domain.UserRepo) {
       // Initialize your service dependencies
   }
   ```

2. Register the service initialization in `router/common.go`:

   ```go
   // router/common.go
   func InitDependencies(uRepo domain.UserRepo) {
       // ... other service initializations ...
       yourservice.Init(uRepo)
   }
   ```

3. Service initialization order matters! Add your service in a logical order:
   - Core services (user, auth) should be initialized first
   - Services with dependencies should be initialized after their dependencies
   - Example order:

     ```go
     func InitDependencies(uRepo domain.UserRepo) {
         user.Init(uRepo)
         auth.Init(uRepo)
         wallet.Init(uRepo)
         referral.Init(uRepo)  // Add new services in appropriate order
     }
     ```

4. Common mistakes to avoid:
   - Forgetting to add Init() call in router/common.go
   - Circular dependencies between services
   - Not handling service initialization errors

## Common Utilities

### String Utilities (pkg/util)

```go
// Convert pointer to string value, empty string if nil
util.Val(*string) string

// Convert string to pointer
util.Ptr(string) *string

// Convert big.Int to string
util.BigIntToString(*big.Int) string

// Parse string to big.Int
util.StringToBigInt(string) *big.Int
```

### Domain Types

```go
// Create address from string
domain.NewStrAddress(string) domain.Address
```

### Database Operations

```go
// Get DB with context
GetWith(ctx context.Context) *gorm.DB

// Begin transaction
tx := GetWith(ctx).Begin()

// Rollback on error
if err != nil {
    tx.Rollback()
    return err
}

// Commit transaction
if err := tx.Commit().Error; err != nil {
    return err
}
```

## Logging

### Using kglog Package

Always use `kglog` instead of standard `log` or `fmt.Printf`:

```go
// Error logging with context and data
kglog.ErrorWithDataCtx(ctx, "Operation failed", map[string]interface{}{
    "error": err.Error(),
    "userId": userID,
})

// Info logging with context
kglog.InfoCtx(ctx, "Operation successful")

// Debug logging with data
kglog.DebugWithData("Processing request", map[string]interface{}{
    "requestId": reqID,
})
```

### Log Levels

1. Debug: Detailed information for debugging
2. Info: General operational information
3. Warning: Warning messages for potential issues
4. Error: Error conditions that should be investigated
5. Fatal: Critical errors that require immediate attention

## Service Layer

1. Use domain models for business logic
2. Keep database operations in repository layer
3. Validate inputs before processing
4. Use transactions for operations that modify multiple records

## API Layer

1. Use common response types from `api/response`
2. Validate request parameters
3. Convert domain errors to appropriate HTTP responses
4. Use proper HTTP methods and status codes

## Testing

1. Write unit tests for repository methods
2. Use test fixtures for database tests
3. Mock external dependencies
4. Test error cases and edge conditions
