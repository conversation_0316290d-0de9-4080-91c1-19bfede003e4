# Testing Best Practices

## Response Struct Testing

When testing API endpoints, it's important to verify not just the business logic but also the API contract itself. This includes ensuring that the JSON serialization/deserialization works correctly according to the OpenAPI specification.

### Response Struct Definition

1. **Don't Reuse Implementation Structs**
   - Never reuse the request/response structs from the implementation code in tests
   - Define separate request/response structs in test files that match the OpenAPI spec
   - This helps catch issues with incorrect JSON tags or missing fields

2. **Example**

   ```go
   // In implementation code (api/referral/referral.go)
   type RewardResponse struct {
       RewardID string `json:"reward_id"`
       Amount   string `json:"amount"`
   }

   // In test code (api/referral/referral_test.go)
   type expectedRewardResponse struct {
       Code int `json:"code"`
       Data struct {
           RewardID string `json:"reward_id"`
           Amount   string `json:"amount"`
       } `json:"data"`
   }
   ```

3. **Benefits**
   - Catches JSON tag mismatches
   - Verifies complete request/response structure
   - Tests serialization/deserialization
   - Ensures OpenAPI compliance

### Best Practices

1. **Reference OpenAPI Spec**
   - Always refer to the OpenAPI YAML files when defining test request/response structs
   - Match field names, types, and JSON tags exactly
   - Include all fields defined in the spec

2. **Test Structure**

   ```go
   func TestEndpoint(t *testing.T) {
       // Make request
       w := httptest.NewRecorder()
       // ... make request ...

       // Define expected response struct based on OpenAPI spec
       var resp struct {
           Code int `json:"code"`
           Data struct {
               Field string `json:"field"`
           } `json:"data"`
       }

       // Unmarshal and verify
       err := json.Unmarshal(w.Body.Bytes(), &resp)
       require.NoError(t, err)
       assert.Equal(t, expectedCode, resp.Code)
       assert.Equal(t, expectedData, resp.Data.Field)
   }
   ```

3. **Error Responses**
   - Define separate structs for error responses
   - Match error response schema from OpenAPI spec
   - Test all error cases defined in the spec

4. **Validation**
   - Test required vs optional fields
   - Verify field types match spec
   - Check enum values if specified
   - Test field constraints (min/max, pattern, etc.)

### Example Test Cases

1. **Success Response**

   ```go
   t.Run("success", func(t *testing.T) {
       // Make request
       w := httptest.NewRecorder()
       // ... make request ...

       // Define expected response (from OpenAPI spec)
       var resp struct {
           Code int `json:"code"`
           Data struct {
               ID     string `json:"id"`
               Status string `json:"status"`
           } `json:"data"`
       }

       err := json.Unmarshal(w.Body.Bytes(), &resp)
       require.NoError(t, err)
       assert.Equal(t, 0, resp.Code)
       assert.NotEmpty(t, resp.Data.ID)
   })
   ```

2. **Error Response**

   ```go
   t.Run("invalid_input", func(t *testing.T) {
       // Make invalid request
       w := httptest.NewRecorder()
       // ... make request ...

       // Define error response (from OpenAPI spec)
       var resp struct {
           Code    int    `json:"code"`
           Message string `json:"message"`
       }

       err := json.Unmarshal(w.Body.Bytes(), &resp)
       require.NoError(t, err)
       assert.Equal(t, code.ParamIncorrect, resp.Code)
   })
   ```

## API Integration Testing

### Overview

API integration tests verify the entire request/response flow through all layers of the application:

1. HTTP Layer (routing, middleware)
2. Service Layer (business logic)
3. Repository Layer (database operations)

### Test Setup

1. **Database Initialization**

   ```go
   func (suite *TestSuite) SetupTest() {
       // Reset database to clean state
       rdb.Reset()
       
       // Create seed data
       suite.Nil(rdb.CreateSeedData())
       
       // Get repository instance
       repo := rdb.GormRepo()
       
       // Initialize services with real repository
       service.Init(repo)
   }
   ```

2. **Benefits of Integration Tests**
   - Tests real database interactions
   - Verifies data persistence
   - Catches SQL/ORM issues
   - Tests transaction handling
   - Validates data consistency

3. **Test Data Management**
   - Use `rdb.Reset()` to clean database
   - Create seed data for each test
   - Isolate test data between runs
   - Use transactions when possible

4. **Example Integration Test**

   ```go
   type ReferralTestSuite struct {
       suite.Suite
       repo domain.Repository
   }

   func (s *ReferralTestSuite) SetupTest() {
       rdb.Reset()
       s.repo = rdb.GormRepo()
       referral.Init(s.repo)
   }

   func (s *ReferralTestSuite) TestWithdraw() {
       // Setup test data
       s.repo.CreateUser(...)
       s.repo.CreateReferralBalance(...)

       // Make API request
       w := httptest.NewRecorder()
       req := WithdrawRequest{...}
       
       // Verify response
       s.Equal(http.StatusOK, w.Code)
       
       // Verify database state
       balance, err := s.repo.GetReferralBalance(...)
       s.NoError(err)
       s.Equal(expectedBalance, balance)
   }
   ```

### Best Practices

1. **Use Real Dependencies**
   - Use actual database for integration tests
   - Initialize services with real repositories
   - Only mock external APIs (e.g., blockchain)

2. **Test Data Management**
   - Reset database before each test
   - Create minimal required test data
   - Clean up after tests
   - Use unique identifiers

3. **Transaction Testing**
   - Test rollback scenarios
   - Verify data consistency
   - Test concurrent operations
   - Check error conditions

4. **Performance Considerations**
   - Use transactions to speed up tests
   - Minimize database operations
   - Batch create test data
   - Clean up efficiently
