# Signing Server Documentation

## Overview

The signing server is a crucial component of the KryptoGO wallet backend that handles cryptographic operations for multiple blockchain networks. It provides secure wallet management and transaction signing capabilities for organizations across different blockchain platforms including EVM-compatible chains, Tron, and Solana.

## Architecture

### Core Components

1. **API Server (`cmd/api-signing`)**
   - Entry point for the signing service
   - Handles HTTP requests and routes
   - Implements authentication and middleware
   - Exposes RESTful endpoints for wallet operations

2. **Signing Server (`pkg/service/signing/server_v2`)**
   - Core business logic implementation
   - Manages wallet creation and signing operations
   - Handles multiple blockchain platforms
   - Implements security and alerting mechanisms

### Key Features

1. **Multi-Chain Support**
   - EVM (Ethereum Virtual Machine) compatible chains
   - Tron blockchain
   - Solana blockchain

2. **Organization Wallet Management**
   - Create organization wallets
   - Retrieve wallet addresses
   - Manage signing operations
   - Alert threshold management

3. **Security Features**
   - KMS integration for key encryption
   - Audit logging
   - Transaction threshold alerts
   - Request authentication

4. **Bridging Address System**
   - Deterministic EOA wallet generation from org+salt
   - Separate from ERC4337 deposit addresses
   - Dedicated signing endpoints for bridging operations
   - Compatible with cross-chain bridging protocols

## API Endpoints

### V1 API Routes

- **Wallet Management**
  - `GET /v1/wallets` - Get organization wallets
  - `POST /v1/wallets` - Create organization wallet

- **Transaction Signing**
  - `POST /v1/sign/evm/message` - Sign EVM messages
  - `POST /v1/sign/evm` - Sign EVM transactions
  - `POST /v1/sign/evm_by_salt` - Sign EVM transactions with bridging address
  - `POST /v1/sign/tron` - Sign Tron transactions
  - `POST /v1/sign/solana` - Sign Solana transactions

- **Bridging Address Management**
  - `GET /v1/bridging_address?org_id=<org_id>&salt=<salt>` - Get bridging addresses for org and salt. The `salt` parameter should be a hex-encoded 32-byte value.

- **Alert Management**
  - `GET /v1/alert_threshold` - Get alert threshold
  - `POST /v1/alert_threshold` - Update alert threshold

- **Deposit Address Management**
  - `GET /v1/deposit_address` - Get deposit address
  - `GET /v1/deposit_address_by_salt` - Get deposit addresses by salt

### Health Check Endpoints

- `GET /ok` - Basic health check
- `GET /health` - Detailed health status
- `GET /ip` - Server IP information

## Implementation Details

### Initialization

The signing server is initialized with three main components:

1. Organization Signer Repository
2. Alert Sender
3. Private Key Encryptor

```go
server.Init(repo domain.OrganizationSignerRepo, sender domain.AlertSender, encryptor domain.PrivateKeyEncryptor)
```

### Error Handling

The signing server uses a standardized error handling system through the `code` package:

1. **KGError Creation**

```go
// Create a new KGError with code, HTTP status, error, and optional data
kgErr := code.NewKGError(
    code.SignError,               // Error code from pkg/code/code.go
    http.StatusInternalServerError, // HTTP status code
    fmt.Errorf("error message"),    // Underlying error
    nil,                           // Optional data map
)
```

Common error codes:

- `code.Success` (0) - Operation successful
- `code.ParamIncorrect` (1004) - Invalid parameters
- `code.SigningFailed` (2001) - Signing operation failed
- `code.ExternalAPIError` (1012) - External service error

### Response Handling

The API uses a standardized response format:

1. **Success Response**

```json
{
    "code": 0,
    "data": { ... }
}
```

2. **Error Response**

```json
{
    "code": <error_code>,
    "msg": "error message",
    "data": { ... }  // Optional
}
```

All responses implement the `GetCode()` interface for consistent error handling:

```go
type Response interface {
    GetCode() int
}
```

Example response struct:

```go
type BridgingAddressResp struct {
    Code int                       `json:"code"`
    Data *server.BridgingAddresses `json:"data"`
}

func (r *BridgingAddressResp) GetCode() int {
    return r.Code
}
```

### EVM Transaction Signing Implementation

#### Request Handling

The signing server handles EVM transaction signing through two main endpoints:

1. Regular Transaction Signing (`SignEvmTransaction`)
2. Salt-based Transaction Signing (`SignEvmTransactionWithSalt`)

Both use the same request structure:

```go
type SignEvmReq struct {
    OrgID       int                `json:"organization_id" binding:"required"`
    ChainID     int                `json:"chain_id" binding:"required"`
    Transaction *types.Transaction `json:"transaction" binding:"required"`
}
```

For salt-based signing, include the `salt` parameter:

```json
{
    "organization_id": 123,
    "chain_id": 1,  // Chain ID number (e.g., 1 for Ethereum mainnet, 56 for BSC)
    "transaction": {...},  // types.Transaction object
    "salt": "hex_encoded_32_bytes"
}
```

Client usage example:

```go
// Create a salt (32 bytes)
salt := [32]byte{}
salt[31] = 1 // Example: set last byte to 1

// Get addresses for a salt
addresses, err := client.GetBridgingAddressesBySalt(ctx, orgID, salt)

// Create transaction
tx := types.NewTransaction(
    nonce,
    common.HexToAddress(to),
    value,
    gasLimit,
    gasPrice,
    data,
)

// Sign transaction using salt
signedTx, err := client.SignEvmTransactionWithSalt(
    ctx,
    orgID,
    "eth", // Chain ID string (client will convert to number)
    tx,
    salt,
)

// Verify signature
signer, err := types.Signer{}.Sender(signedTx)
assert.Equal(t, addresses.EvmAddress, signer.String())
```

### Testing

The signing server provides test utilities in `pkg/service/signing/server_v2/test` to help with integration testing:

```go
// Setup test signing server
signingservertest.Setup(t)

// Create test client
client := signingclient.New()

// Use client for testing
addresses, err := client.GetBridgingAddressesBySalt(ctx, orgID, salt)
assert.Nil(t, err)
```

### Best Practices

1. Always validate input parameters before making requests
2. Handle errors appropriately using `code.KGError`
3. Use the provided helper functions for contract deployment
4. Implement proper logging for debugging and monitoring
5. Set appropriate timeouts for network requests

### Changelog

#### v2.0.1 (2025-01-15)

- Changed `/sign/evm_by_salt` endpoint to `/v1/sign/evm_by_salt` for consistency
- Updated chain ID handling to use domain.Chain type
- Improved transaction handling in client code
- Added test utilities for integration testing
